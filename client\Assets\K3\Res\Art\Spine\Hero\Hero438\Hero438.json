{"skeleton": {"hash": "V4RnAPt3rcg", "spine": "4.2.33", "x": -343.9, "y": -63.51, "width": 754.32, "height": 1850.25, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 221.98, "y": 1127.59}, {"name": "ALL2", "parent": "ALL", "x": -181.97, "y": 22.31, "icon": "arrows"}, {"name": "body", "parent": "ALL2", "length": 96.78, "rotation": 75.96, "x": 2.27, "y": 8.96}, {"name": "body2", "parent": "body", "length": 268.84, "rotation": 110.88, "x": 96.78, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 63, "rotation": 87.57, "x": 268.84, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 220, "rotation": -7, "x": 63}, {"name": "eyebrow_R", "parent": "head", "length": 15.46, "rotation": -79.86, "x": 75.81, "y": 1.4}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 18.07, "rotation": -29.47, "x": 15.46}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 12.66, "rotation": -2.51, "x": 18.07}, {"name": "eyebrow_L", "parent": "head", "length": 12.81, "rotation": 116.53, "x": 77.26, "y": -84.45}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 8.93, "rotation": 10.54, "x": 12.81}, {"name": "eye_R", "parent": "head", "x": 54.99, "y": -11.93}, {"name": "eye_L", "parent": "head", "x": 55.55, "y": -66.6}, {"name": "earring", "parent": "head", "length": 31.26, "rotation": -92.15, "x": 8.37, "y": 44.92, "inherit": "noRotationOrReflection"}, {"name": "hair", "parent": "head", "x": 148.6, "y": -23.11}, {"name": "hair_FR", "parent": "hair", "length": 49.48, "rotation": -177.23, "x": -43.62, "y": 56.97}, {"name": "hair_FR2", "parent": "hair_FR", "length": 46.3, "rotation": 8.18, "x": 49.48, "color": "abe323ff"}, {"name": "hair_FR3", "parent": "hair_FR2", "length": 34.41, "rotation": 7.38, "x": 46.3, "color": "abe323ff"}, {"name": "hair_FR4", "parent": "hair_FR3", "length": 27.76, "rotation": 12.74, "x": 34.41, "color": "abe323ff"}, {"name": "hair_FR5", "parent": "hair_FR4", "length": 28.67, "rotation": 15.23, "x": 27.76, "color": "abe323ff"}, {"name": "hair_F", "parent": "hair", "length": 35.5, "rotation": -159.27, "x": 0.91, "y": -12.55}, {"name": "hair_F2", "parent": "hair_F", "length": 37.45, "rotation": -16.96, "x": 35.5, "color": "abe323ff"}, {"name": "hair_F3", "parent": "hair_F2", "length": 33.89, "rotation": -20.92, "x": 37.45, "color": "abe323ff"}, {"name": "hair_FL", "parent": "hair", "length": 30.41, "rotation": -145.56, "x": 5.7, "y": -51.42}, {"name": "hair_FL2", "parent": "hair_FL", "length": 32.86, "rotation": -14.71, "x": 30.41, "color": "abe323ff"}, {"name": "hair_FL3", "parent": "hair_FL2", "length": 25.48, "rotation": -16.1, "x": 32.86, "color": "abe323ff"}, {"name": "hair_FL4", "parent": "hair_FL3", "length": 28.32, "rotation": -17.11, "x": 25.48, "color": "abe323ff"}, {"name": "hair_B", "parent": "head", "x": 128.75, "y": -64.71}, {"name": "hair_B2", "parent": "hair_B", "length": 38.78, "rotation": -167.68, "x": -7.82, "y": -6.76}, {"name": "hair_B3", "parent": "hair_B2", "length": 44.13, "rotation": -4.17, "x": 38.78, "color": "abe323ff"}, {"name": "hair_B4", "parent": "hair_B3", "length": 40.67, "rotation": -11.26, "x": 44.13, "color": "abe323ff"}, {"name": "hair_B5", "parent": "hair_B4", "length": 31.41, "rotation": -10.43, "x": 40.67, "color": "abe323ff"}, {"name": "hair_B6", "parent": "hair_B5", "length": 32.9, "rotation": -10.49, "x": 31.41, "color": "abe323ff"}, {"name": "sh_R", "parent": "body2", "x": 264.41, "y": 130.25, "inherit": "noScale"}, {"name": "sh_L", "parent": "body2", "x": 210.16, "y": -110.38, "inherit": "noScale"}, {"name": "RU_R", "parent": "body2", "length": 40, "x": 101.09, "y": 21.05}, {"name": "RU_R2", "parent": "RU_R", "length": 40, "x": -10.81, "y": 6.44}, {"name": "RU_R3", "parent": "RU_R2", "length": 40, "x": -10.52, "y": 6.53}, {"name": "RU_L", "parent": "body2", "length": 40, "x": 85.44, "y": -89.1}, {"name": "RU_L2", "parent": "RU_L", "length": 40, "x": -25.63, "y": -30.34}, {"name": "RU_L3", "parent": "RU_L2", "length": 40, "x": -17.95, "y": -19.9}, {"name": "tun", "parent": "ALL2", "length": 134.47, "rotation": -111.74, "x": -4.3, "y": -10.31}, {"name": "leg_R", "parent": "tun", "x": 26.91, "y": -78.07}, {"name": "leg_L", "parent": "tun", "x": 39, "y": 63.48}, {"name": "arm_R", "parent": "sh_R", "length": 234.86, "rotation": -92.53, "x": -28.4, "y": 1.04, "inherit": "noRotationOrReflection"}, {"name": "arm_R2", "parent": "arm_R", "length": 254.79, "rotation": -15.02, "x": 234.86}, {"name": "arm_R3", "parent": "arm_R2", "length": 85.64, "rotation": -13.06, "x": 254.79}, {"name": "arm_R4", "parent": "arm_R3", "length": 37.37, "rotation": 30.61, "x": 85.64}, {"name": "arm_R5", "parent": "arm_R4", "length": 40.38, "rotation": 17.97, "x": 37.37}, {"name": "arm_R6", "parent": "arm_R3", "length": 66.23, "rotation": 28.2, "x": 44.39, "y": 26.74}, {"name": "arm_L", "parent": "sh_L", "length": 249.99, "rotation": 175.37, "x": -20.02, "y": 16.54}, {"name": "arm_L2", "parent": "arm_L", "length": 206.4, "rotation": 147.39, "x": 249.99, "inherit": "noScale"}, {"name": "arm_L4", "parent": "sh_L", "rotation": -110.88, "x": -104.87, "y": -88.21, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L3", "parent": "arm_L4", "length": 100.26, "rotation": 92.94}, {"name": "leg_R2", "parent": "leg_R", "length": 512.65, "rotation": 21.44, "x": 85.38, "y": -30.7}, {"name": "leg_R3", "parent": "leg_R2", "length": 407.78, "rotation": -4.09, "x": 512.65, "inherit": "noScale"}, {"name": "leg_R4", "parent": "leg_R3", "length": 216.33, "rotation": -79.77, "x": 407.8, "inherit": "onlyTranslation"}, {"name": "leg_L2", "parent": "leg_L", "length": 451.54, "rotation": 42.48, "x": 95.06, "y": -5.12}, {"name": "leg_L3", "parent": "leg_L2", "length": 406.89, "rotation": -11.66, "x": 451.54, "inherit": "noScale"}, {"name": "leg_L4", "parent": "leg_L3", "length": 189.68, "rotation": -60.67, "x": 406.89, "inherit": "onlyTranslation"}, {"name": "tunround", "parent": "ALL2", "x": 277.97, "y": -93.91, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 277.97, "y": -159.41, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "rotation": -110.88, "x": 49.8, "y": -346.07, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "rotation": -110.88, "x": -10.27, "y": -323.16, "icon": "warning"}, {"name": "headround3", "parent": "head", "x": 437.21, "y": -5.28, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -77.37, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 369.79, "y": -82.65, "icon": "warning"}, {"name": "leg_L5", "parent": "leg_L", "length": 463.81, "rotation": 49.86, "x": 94.82, "y": -6.07}, {"name": "leg_L6", "parent": "leg_L5", "length": 416.16, "rotation": -27.15, "x": 463.81, "color": "abe323ff"}, {"name": "leg_L7", "parent": "root", "x": 264.4, "y": 169.36, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "root", "x": 200.17, "y": 571.16, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R5", "parent": "leg_R", "length": 919.54, "rotation": 19.64, "x": 85.38, "y": -30.68}, {"name": "leg_R6", "parent": "root", "x": -140.91, "y": 156.33, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "leg_R5", "rotation": 92.12, "x": 512.22, "y": 16.22, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_L2", "parent": "sh_L", "length": 279.86, "rotation": 173.46, "x": -20.08, "y": 16.5}, {"name": "sh_L3", "parent": "sh_L2", "length": 237, "rotation": 151.33, "x": 279.86, "color": "abe323ff"}, {"name": "arm_L1", "parent": "sh_L", "rotation": -110.88, "x": -269.19, "y": 36.73, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "arm_La", "bone": "root", "attachment": "arm_La"}, {"name": "arm_Lb", "bone": "root", "attachment": "arm_Lb"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "arm_R", "bone": "root", "attachment": "arm_R"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "hair_L", "bone": "root", "attachment": "hair_L"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "nose", "bone": "root", "attachment": "nose"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}], "ik": [{"name": "arm_L", "order": 1, "bones": ["sh_L2", "sh_L3"], "target": "arm_L4"}, {"name": "arm_L1", "order": 3, "bones": ["arm_L"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 4, "bones": ["arm_L2"], "target": "arm_L4", "compress": true, "stretch": true}, {"name": "leg_L", "order": 6, "bones": ["leg_L5", "leg_L6"], "target": "leg_L7", "bendPositive": false}, {"name": "leg_L1", "order": 8, "bones": ["leg_L2"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 9, "bones": ["leg_L3"], "target": "leg_L7", "compress": true, "stretch": true}, {"name": "leg_R", "order": 11, "bones": ["leg_R5"], "target": "leg_R6", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 12, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 13, "bones": ["leg_R3"], "target": "leg_R6", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 2, "bones": ["arm_L1"], "target": "sh_L3", "rotation": -75.47, "x": 30.69, "y": 6.6, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 14, "bones": ["bodyround2"], "target": "bodyround", "y": -64.29, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "order": 15, "bones": ["sh_R"], "target": "bodyround", "rotation": 110.88, "x": -521.53, "y": 30.76, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "bones": ["sh_L"], "target": "bodyround", "rotation": 110.88, "x": -277.37, "y": 65.83, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 16, "bones": ["headround2"], "target": "headround", "x": -67.42, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 17, "bones": ["eyebrow_R"], "target": "headround", "rotation": -79.86, "x": -361.4, "y": 84.05, "mixRotate": 0, "mixX": 0.035, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 18, "bones": ["hair_B"], "target": "headround", "x": -308.46, "y": 17.94, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 19, "bones": ["earring"], "target": "headround", "rotation": -172.73, "x": -428.84, "y": 127.57, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 20, "bones": ["eyebrow_L"], "target": "headround", "rotation": 116.53, "x": -359.95, "y": -1.8, "mixRotate": 0, "mixX": 0.025, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround5", "order": 21, "bones": ["hair"], "target": "headround", "x": -288.6, "y": 59.54, "mixRotate": 0, "mixX": 0.035, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_L3", "order": 7, "bones": ["leg_L1"], "target": "leg_L6", "rotation": 89.27, "x": 13.58, "y": -59.1, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "tunround", "order": 22, "bones": ["tunround2"], "target": "tunround", "y": -65.51, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 10, "bones": ["leg_R"], "target": "tunround", "rotation": -111.74, "x": -364.76, "y": 87.51, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 5, "bones": ["leg_L"], "target": "tunround", "rotation": -111.74, "x": -237.75, "y": 23.86, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "hair_B3", "order": 28, "bone": "hair_B3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B4", "order": 29, "bone": "hair_B4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B5", "order": 30, "bone": "hair_B5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B6", "order": 31, "bone": "hair_B6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F2", "order": 23, "bone": "hair_F2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F3", "order": 24, "bone": "hair_F3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL2", "order": 25, "bone": "hair_FL2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL3", "order": 26, "bone": "hair_FL3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL4", "order": 27, "bone": "hair_FL4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR2", "order": 32, "bone": "hair_FR2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR3", "order": 33, "bone": "hair_FR3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR4", "order": 34, "bone": "hair_FR4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR5", "order": 35, "bone": "hair_FR5", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_La": {"arm_La": {"type": "mesh", "uvs": [0.34447, 0.00372, 0.41546, 0.00321, 0.49878, 0.04039, 0.53442, 0.07284, 0.57486, 0.14775, 0.61818, 0.23199, 0.65884, 0.31498, 0.71666, 0.39005, 0.84132, 0.57123, 0.90861, 0.69062, 0.99055, 0.90332, 0.9957, 0.94271, 0.94377, 0.9827, 0.89989, 0.9952, 0.84858, 0.996, 0.75957, 0.97311, 0.62878, 0.92565, 0.49374, 0.83329, 0.37291, 0.66554, 0.21836, 0.44454, 0.17045, 0.39106, 0.11266, 0.3434, 0.05419, 0.29615, 0.00515, 0.23953, 0.00608, 0.14001, 0.0495, 0.08803, 0.10993, 0.05148, 0.19659, 0.02029, 0.46267, 0.05521, 0.48405, 0.10467, 0.50543, 0.15756, 0.50802, 0.31813, 0.46983, 0.24627, 0.58487, 0.39388, 0.7306, 0.57174, 0.79605, 0.72211, 0.65057, 0.76115, 0.49189, 0.62, 0.37685, 0.43734, 0.31735, 0.36077, 0.27768, 0.28882, 0.2427, 0.21008, 0.24072, 0.10952, 0.32601, 0.05694, 0.85273, 0.90853], "triangles": [22, 41, 40, 41, 23, 24, 24, 42, 41, 24, 25, 42, 42, 30, 41, 25, 26, 42, 22, 23, 41, 13, 14, 44, 12, 13, 44, 12, 44, 11, 44, 10, 11, 10, 44, 35, 35, 9, 10, 21, 22, 40, 20, 21, 39, 44, 14, 15, 15, 16, 44, 44, 16, 36, 16, 17, 36, 44, 36, 35, 17, 18, 36, 18, 37, 36, 36, 37, 35, 37, 34, 35, 9, 34, 8, 9, 35, 34, 37, 19, 38, 37, 18, 19, 37, 38, 34, 38, 33, 34, 33, 7, 34, 34, 7, 8, 38, 19, 39, 19, 20, 39, 38, 39, 33, 39, 31, 33, 33, 6, 7, 33, 31, 6, 39, 40, 31, 40, 32, 31, 31, 5, 6, 31, 32, 5, 32, 30, 5, 30, 4, 5, 21, 40, 39, 40, 41, 32, 30, 32, 41, 30, 29, 4, 29, 42, 43, 28, 2, 3, 28, 1, 2, 29, 28, 3, 28, 43, 1, 29, 3, 4, 43, 28, 29, 43, 0, 1, 43, 27, 0, 42, 27, 43, 29, 30, 42, 26, 27, 42], "vertices": [2, 35, 11.61, 4.64, 0.92, 4, 221.77, -105.74, 0.08, 1, 35, 8.21, -4.7, 1, 2, 35, -6.4, -11.61, 0.85981, 51, -15.85, 26.96, 0.14019, 2, 35, -17.31, -12.79, 0.57754, 51, -5.07, 29.02, 0.42246, 2, 35, -40.39, -10.04, 0.12708, 51, 18.16, 28.14, 0.87292, 2, 35, -66.25, -6.68, 0.0725, 51, 44.2, 26.87, 0.9275, 1, 51, 69.77, 25.35, 1, 1, 51, 93.73, 26.8, 1, 2, 51, 150.97, 28.3, 0.98966, 52, 98.65, 29.53, 0.01034, 1, 51, 188.11, 27.29, 1, 1, 52, 8.63, -18.68, 1, 2, 51, 264.37, 17.77, 0.04521, 52, -2.54, -22.71, 0.95479, 2, 51, 273.89, 7.42, 0.28124, 52, -16.13, -19.13, 0.71876, 2, 51, 275.78, 0.47, 0.41788, 52, -21.47, -14.3, 0.58212, 2, 51, 274.01, -6.5, 0.56162, 52, -23.73, -7.47, 0.43838, 2, 51, 263.9, -16.53, 0.89591, 52, -20.63, 6.43, 0.10409, 1, 51, 245.07, -30.12, 1, 1, 51, 213.09, -40.49, 1, 1, 51, 159.88, -42.6, 1, 1, 51, 89.96, -44.76, 1, 2, 51, 72.63, -46.7, 0.90572, 4, 121.52, -41.43, 0.09428, 2, 51, 56.59, -50.45, 0.80857, 4, 137.81, -38.98, 0.19143, 2, 51, 40.65, -54.34, 0.30857, 4, 154.01, -36.4, 0.69143, 1, 4, 172.38, -36.06, 1, 2, 35, -9.84, 63.52, 0.18857, 4, 200.32, -46.86, 0.81143, 2, 35, 2.61, 52.27, 0.24571, 4, 212.77, -58.11, 0.75429, 2, 35, 9.88, 40.44, 0.39714, 4, 220.04, -69.94, 0.60286, 2, 35, 14.33, 25.76, 0.67714, 4, 224.49, -84.62, 0.32286, 3, 35, -8.77, -5.3, 0.91459, 4, 201.39, -115.68, 0.07656, 63, -269.29, 59.53, 0.00885, 3, 35, -23.74, -2.79, 0.72274, 4, 186.42, -113.17, 0.26426, 63, -266.3, 44.64, 0.013, 4, 35, -39.69, 0.09, 0.35077, 51, 18.27, 17.99, 0.40499, 4, 170.47, -110.29, 0.22864, 63, -263.31, 28.72, 0.01561, 2, 51, 64.77, 4.81, 0.98232, 63, -262.94, -19.61, 0.01768, 4, 35, -62.86, 14.26, 0.10916, 51, 42.51, 5.73, 0.76416, 4, 147.3, -96.12, 0.10866, 63, -268.29, 2.02, 0.01802, 2, 51, 89.67, 8.76, 0.98671, 63, -252.18, -42.41, 0.01329, 3, 51, 146.78, 13.37, 0.98844, 52, 94.13, 44.36, 0.00232, 63, -231.78, -95.95, 0.00924, 2, 51, 192.8, 9.51, 0.99511, 63, -222.62, -141.21, 0.00489, 2, 51, 198.38, -13.33, 0.9953, 63, -242.99, -152.96, 0.0047, 2, 51, 151.38, -22.78, 0.99067, 63, -265.2, -110.47, 0.00933, 2, 51, 94.09, -22.86, 0.98416, 63, -281.31, -55.49, 0.01584, 2, 51, 69.63, -24.41, 0.9792, 63, -289.64, -32.45, 0.0208, 4, 35, -65.24, 43.96, 0.0512, 51, 47.28, -23.68, 0.47259, 4, 144.92, -66.42, 0.46078, 63, -295.19, -10.79, 0.01544, 4, 35, -41.35, 40.09, 0.21133, 51, 23.16, -21.75, 0.18336, 4, 168.81, -70.29, 0.59265, 63, -300.09, 12.91, 0.01266, 3, 35, -12.97, 29.56, 0.49123, 4, 197.19, -80.82, 0.49688, 63, -300.36, 43.18, 0.01189, 3, 35, -2.44, 12.76, 0.67421, 4, 207.72, -97.62, 0.31313, 63, -288.42, 59.01, 0.01266, 2, 51, 248.89, 1.43, 0.09402, 52, 1.69, -0.61, 0.90598], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 44, 46, 46, 48, 52, 54, 48, 50, 50, 52, 4, 6, 6, 8, 8, 10, 10, 12, 40, 42, 42, 44, 36, 38, 38, 40, 14, 16, 16, 18, 64, 62, 62, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80], "width": 140, "height": 301}}, "arm_Lb": {"arm_Lb": {"type": "mesh", "uvs": [0.14902, 0, 0.22524, 0.04669, 0.74713, 0.39354, 0.81568, 0.44025, 0.84543, 0.43156, 0.91531, 0.46994, 0.92351, 0.50432, 0.92244, 0.5141, 1, 0.57282, 0.99285, 0.60727, 0.97346, 0.62972, 0.92268, 0.59248, 0.91412, 0.6114, 0.91029, 0.62241, 0.907, 0.63564, 0.91686, 0.64992, 0.91343, 0.66551, 0.86939, 0.86528, 0.86084, 0.90406, 0.83663, 0.94243, 0.82108, 0.97133, 0.80915, 0.99009, 0.80049, 0.99779, 0.78886, 0.99799, 0.77453, 0.98893, 0.75163, 0.93079, 0.74792, 0.90034, 0.74898, 0.87282, 0.75566, 0.84349, 0.76718, 0.82437, 0.82499, 0.63968, 0.82506, 0.625, 0.83937, 0.61719, 0.84094, 0.60718, 0.84194, 0.59718, 0.82265, 0.58192, 0.79532, 0.55223, 0.72908, 0.45155, 0.65147, 0.39917, 0.29216, 0.16367, 0.16526, 0.08393, 0.11893, 0.06297, 0.08446, 0.06278, 0.00147, 0.1, 0.00126, 0.08532, 0.03071, 0.03594, 0.07584, 0.00714, 0.11461, 0, 0.8768, 0.57891, 0.87853, 0.59742, 0.87464, 0.61451, 0.87247, 0.6297, 0.86209, 0.66483, 0.83715, 0.63262, 0.90195, 0.65079, 0.86805, 0.64707], "triangles": [42, 46, 47, 45, 46, 42, 41, 47, 0, 40, 41, 0, 42, 47, 41, 1, 40, 0, 42, 43, 44, 42, 44, 45, 2, 39, 1, 40, 1, 39, 38, 39, 2, 37, 38, 2, 37, 2, 3, 7, 5, 6, 36, 37, 3, 7, 48, 4, 7, 4, 5, 36, 3, 4, 48, 36, 4, 35, 36, 48, 11, 7, 8, 48, 7, 11, 34, 35, 48, 49, 48, 11, 34, 48, 49, 9, 11, 8, 12, 49, 11, 50, 34, 49, 13, 50, 49, 33, 34, 50, 12, 13, 49, 10, 11, 9, 26, 27, 18, 32, 33, 50, 51, 32, 50, 14, 51, 50, 53, 31, 32, 53, 32, 51, 13, 14, 50, 30, 31, 53, 55, 53, 51, 54, 51, 14, 54, 14, 15, 55, 51, 54, 52, 53, 55, 16, 54, 15, 30, 53, 52, 29, 30, 52, 52, 55, 54, 52, 54, 16, 17, 52, 16, 29, 52, 17, 18, 28, 17, 18, 19, 26, 20, 25, 19, 21, 24, 20, 21, 23, 24, 22, 23, 21, 17, 28, 29, 27, 28, 18, 19, 25, 26, 24, 25, 20], "vertices": [1, 54, 378.21, 445.02, 1, 1, 54, 348.73, 398.07, 1, 1, 54, 131.15, 77.43, 1, 1, 54, 101.9, 35.34, 1, 1, 54, 105.96, 16.22, 1, 1, 54, 81.48, -26.96, 1, 1, 54, 61.34, -31.13, 1, 1, 54, 55.72, -30.16, 1, 1, 54, 19.23, -77.6, 1, 1, 54, -0.45, -72.04, 1, 1, 54, -12.8, -59.08, 1, 1, 54, 10.39, -27.99, 1, 2, 52, 213.42, -20.84, 0.27556, 54, -0.27, -21.99, 0.72444, 2, 52, 206.63, -20.31, 0.58516, 54, -6.51, -19.24, 0.41484, 2, 52, 198.68, -20.46, 0.88041, 54, -14.05, -16.76, 0.11959, 2, 52, 192.52, -28.8, 0.99367, 54, -22.63, -22.59, 0.00633, 1, 52, 183.24, -29.25, 1, 1, 52, 64.39, -35.01, 1, 1, 52, 41.32, -36.13, 1, 1, 52, 15.68, -27.64, 1, 2, 51, 264.98, 17.57, 0.06973, 52, -3.16, -22.88, 0.93027, 2, 51, 273.29, 7.26, 0.23853, 52, -15.71, -18.67, 0.76147, 2, 51, 276.03, 0.73, 0.29406, 52, -21.54, -14.65, 0.70594, 2, 51, 274.08, -6.39, 0.35754, 52, -23.73, -7.6, 0.64246, 2, 51, 266.5, -13.66, 0.50787, 52, -21.26, 2.61, 0.49213, 1, 51, 230.11, -18.2, 1, 2, 51, 212.52, -15.53, 0.76286, 52, 23.18, 33.28, 0.23714, 2, 51, 197.42, -10.42, 0.55143, 52, 38.66, 37.12, 0.44857, 2, 51, 182.3, -1.6, 0.32427, 52, 56.15, 37.83, 0.67573, 2, 51, 173.72, 8.52, 0.20286, 52, 68.83, 33.93, 0.79714, 2, 52, 181.77, 28.85, 0.93754, 54, -13.71, 35.37, 0.06246, 2, 52, 189.94, 31.2, 0.89122, 54, -5.22, 34.89, 0.10878, 2, 52, 196.83, 23.75, 0.71935, 54, -1.18, 25.58, 0.28065, 2, 52, 202.67, 24.43, 0.47936, 54, 4.56, 24.29, 0.52064, 2, 52, 208.41, 25.45, 0.22707, 54, 10.31, 23.36, 0.77293, 2, 52, 213.43, 39.7, 0.03445, 54, 19.76, 35.14, 0.96555, 1, 54, 37.82, 51.59, 1, 1, 54, 98.2, 90.6, 1, 1, 54, 131.02, 138.26, 1, 1, 54, 278.9, 359.11, 1, 1, 54, 329.15, 437.21, 1, 1, 54, 342.78, 465.97, 1, 1, 54, 344.02, 487.82, 1, 1, 54, 325.21, 541.56, 1, 1, 54, 333.71, 541.26, 1, 1, 54, 361.29, 521.11, 1, 1, 54, 376.47, 491.64, 1, 1, 54, 379.34, 466.84, 1, 1, 54, 19.73, 0.71, 1, 2, 52, 214.82, 3.12, 0.00606, 54, 8.97, 0.16, 0.99394, 2, 52, 204.63, 2.7, 0.58362, 54, -0.78, 3.14, 0.41638, 1, 52, 195.81, 1.54, 1, 2, 52, 174.43, 2.14, 0.99921, 54, -29.46, 12.59, 0.00079, 2, 52, 187.86, 22.59, 0.88739, 54, -10.03, 27.45, 0.11261, 2, 52, 189.37, -19.86, 0.98509, 54, -22.65, -13.11, 0.01491, 2, 52, 185.37, 1.4, 0.99693, 54, -19.39, 8.28, 0.00307], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 70, 72, 22, 24, 24, 26, 26, 28, 28, 30, 40, 42, 42, 44, 36, 38, 38, 40, 34, 36, 30, 32, 32, 34, 106, 64, 108, 28], "width": 635, "height": 579}}, "arm_R": {"arm_R": {"type": "mesh", "uvs": [0.99585, 0.04261, 0.80104, 0.30595, 0.62314, 0.54643, 0.54727, 0.54203, 0.52624, 0.56693, 0.51374, 0.59433, 0.51138, 0.62449, 0.49882, 0.65695, 0.49369, 0.68941, 0.46485, 0.74123, 0.45504, 0.77404, 0.45323, 0.81366, 0.45012, 0.84295, 0.43948, 0.87662, 0.41181, 0.89245, 0.37301, 0.8937, 0.34958, 0.88483, 0.34037, 0.8412, 0.34372, 0.81163, 0.32865, 0.77613, 0.29266, 0.76356, 0.25249, 0.79573, 0.26588, 0.8205, 0.27425, 0.8486, 0.31442, 0.86708, 0.34455, 0.89333, 0.34707, 0.91145, 0.30653, 0.94095, 0.24437, 0.96958, 0.06683, 0.9975, 0.01407, 0.99732, 0.01167, 0.89803, 0.00463, 0.86102, 0.01239, 0.82056, 0.01855, 0.77904, 0.0224, 0.73504, 0.08024, 0.69802, 0.156, 0.64953, 0.22305, 0.6072, 0.25903, 0.58136, 0.2859, 0.55586, 0.3058, 0.53061, 0.32533, 0.50523, 0.28972, 0.48735, 0.40895, 0.29031, 0.52675, 1e-05, 0.29861, 0.72367, 0.33084, 0.67622, 0.36038, 0.63529, 0.38723, 0.60504, 0.40603, 0.57597, 0.42349, 0.55046, 0.43745, 0.51199], "triangles": [28, 29, 31, 29, 30, 31, 27, 28, 31, 27, 31, 23, 27, 25, 26, 27, 24, 25, 27, 23, 24, 23, 31, 32, 23, 33, 22, 22, 33, 21, 23, 32, 33, 33, 34, 21, 20, 21, 35, 21, 34, 35, 19, 20, 46, 9, 47, 8, 35, 36, 20, 20, 36, 46, 36, 37, 46, 46, 37, 47, 37, 38, 47, 47, 48, 7, 47, 38, 48, 7, 48, 6, 6, 48, 49, 38, 39, 48, 48, 39, 49, 6, 49, 5, 49, 39, 50, 49, 50, 5, 50, 39, 40, 5, 50, 4, 50, 51, 4, 50, 40, 51, 40, 41, 51, 8, 47, 7, 46, 47, 9, 46, 9, 19, 10, 19, 9, 14, 15, 16, 14, 16, 13, 13, 16, 17, 13, 17, 12, 17, 18, 12, 12, 18, 11, 11, 18, 10, 18, 19, 10, 4, 51, 3, 51, 41, 52, 51, 52, 3, 52, 41, 42, 1, 2, 52, 2, 3, 52, 52, 43, 44, 52, 44, 1, 52, 42, 43, 0, 1, 45, 1, 44, 45], "vertices": [1, 46, 46.83, 33.71, 1, 1, 46, 149.05, 32.73, 1, 1, 46, 242.4, 31.84, 1, 2, 46, 244.58, 19.56, 0.94302, 47, -14.36, 16.75, 0.05698, 2, 46, 254.38, 19.06, 0.59591, 47, -4.71, 18.47, 0.40409, 2, 46, 264.63, 20.17, 0.19669, 47, 5.03, 21.87, 0.80331, 2, 46, 275.36, 23.16, 0.02654, 47, 14.81, 27.21, 0.97346, 2, 50, -13.8, 12.85, 0.04552, 47, 26.16, 31.54, 0.95448, 2, 50, -1.8, 12.52, 0.21522, 47, 36.89, 36.92, 0.78478, 2, 50, 17.5, 8.63, 0.44398, 47, 55.74, 42.61, 0.55602, 2, 50, 29.66, 7.54, 0.77012, 47, 66.98, 47.4, 0.22988, 1, 50, 44.29, 7.86, 1, 1, 50, 55.1, 7.81, 1, 1, 50, 67.59, 6.6, 1, 1, 50, 73.62, 2.34, 1, 1, 50, 74.34, -3.96, 1, 1, 50, 71.23, -7.92, 1, 1, 50, 55.21, -10.09, 1, 2, 50, 44.29, -10.01, 0.90459, 47, 88.15, 38.84, 0.09541, 2, 50, 31.3, -13.01, 0.51395, 47, 78.13, 30.06, 0.48605, 3, 47, 77.13, 22.65, 0.69512, 48, 4.2, 23.83, 0.30468, 49, -24.2, 32.9, 0.0002, 3, 47, 90.67, 23.06, 0.08638, 48, 16.07, 17.28, 0.87655, 49, -14.93, 23.01, 0.03707, 3, 47, 97.43, 29.59, 0.00699, 48, 25.21, 19.46, 0.78322, 49, -5.57, 22.27, 0.20979, 2, 48, 35.58, 20.83, 0.39138, 49, 4.72, 20.37, 0.60862, 2, 48, 42.4, 27.38, 0.12921, 49, 13.23, 24.49, 0.87079, 2, 48, 52.09, 32.29, 0.02888, 49, 23.96, 26.17, 0.97112, 2, 48, 58.77, 32.7, 0.00952, 49, 30.44, 24.5, 0.99048, 1, 49, 38.76, 14.86, 1, 1, 49, 45.68, 1.96, 1, 1, 49, 46.55, -28.75, 1, 1, 49, 43.84, -36.91, 1, 2, 48, 53.82, -21.97, 0.12681, 49, 8.86, -25.98, 0.87319, 2, 48, 40.16, -23.12, 0.51878, 49, -4.48, -22.85, 0.48122, 2, 48, 25.23, -21.86, 0.93133, 49, -18.29, -17.04, 0.06867, 2, 47, 104.79, -12.9, 0.07133, 48, 9.91, -20.85, 0.92867, 2, 47, 90.49, -20.63, 0.54993, 48, -6.32, -20.22, 0.45007, 2, 47, 73.94, -19.47, 0.95811, 48, -19.98, -10.8, 0.04189, 1, 47, 52.25, -17.95, 1, 1, 47, 33.24, -16.49, 1, 2, 46, 272.59, -20.86, 0.00716, 47, 22.05, -16.3, 0.99284, 2, 46, 262.29, -19.52, 0.14016, 47, 11.72, -17.32, 0.85984, 2, 46, 252.43, -19.24, 0.52564, 47, 2.05, -19.27, 0.47436, 2, 46, 242.54, -19.03, 0.90059, 47, -7.63, -21.3, 0.09941, 2, 46, 238, -26.55, 0.9871, 47, -10.35, -29.66, 0.0129, 1, 46, 162.82, -29.94, 1, 1, 46, 54.89, -43.93, 1, 1, 47, 63.96, 15.99, 1, 1, 47, 46.22, 11.59, 1, 1, 47, 30.77, 8.05, 1, 1, 47, 18.93, 6.13, 1, 1, 47, 8.14, 3.31, 1, 2, 46, 253.63, 1.26, 0.64233, 47, -1.41, 0.97, 0.35767, 2, 46, 239.41, -0.85, 0.99962, 47, -14.79, -4.3, 0.00038], "hull": 46, "edges": [8, 10, 56, 58, 58, 60, 74, 76, 76, 78, 84, 86, 86, 88, 10, 12, 4, 6, 6, 8, 16, 18, 12, 14, 14, 16, 82, 84, 78, 80, 80, 82, 70, 72, 72, 74, 68, 70, 60, 62, 62, 64, 64, 66, 66, 68, 18, 20, 20, 22, 54, 56, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 26, 28, 30, 28, 22, 24, 24, 26, 90, 0, 0, 2, 2, 4, 88, 90], "width": 163, "height": 369}}, "body": {"body": {"type": "mesh", "uvs": [0.41023, 0.00393, 0.49387, 1e-05, 0.56318, 0.00085, 0.61876, 0.00621, 0.65307, 0.01433, 0.67383, 0.0237, 0.67976, 0.0308, 0.68866, 0.03657, 0.69005, 0.04074, 0.57601, 0.10019, 0.56767, 0.11076, 0.55933, 0.12132, 0.55099, 0.13189, 0.54265, 0.14245, 0.59933, 0.15298, 0.64937, 0.15471, 0.70396, 0.15568, 0.75128, 0.15608, 0.76603, 0.16446, 0.77331, 0.17255, 0.77959, 0.18121, 0.81587, 0.19141, 0.86205, 0.20011, 0.91759, 0.20773, 0.96358, 0.21702, 0.98995, 0.22808, 0.9989, 0.23813, 0.99082, 0.25336, 0.95331, 0.26764, 0.88781, 0.27441, 0.8821, 0.28638, 0.86927, 0.29795, 0.82619, 0.31637, 0.79741, 0.3347, 0.79836, 0.34255, 0.78977, 0.35558, 0.79037, 0.37368, 0.78914, 0.39003, 0.77367, 0.39424, 0.76101, 0.40794, 0.79036, 0.42705, 0.8155, 0.43672, 0.86905, 0.44827, 0.85001, 0.46572, 0.79358, 0.47756, 0.73916, 0.48719, 0.68226, 0.49095, 0.63305, 0.49158, 0.56832, 0.49132, 0.50388, 0.48768, 0.46156, 0.4839, 0.46136, 0.49598, 0.45467, 0.5146, 0.4428, 0.53605, 0.418, 0.57526, 0.39157, 0.60459, 0.36515, 0.62947, 0.35288, 0.64302, 0.34862, 0.65308, 0.33974, 0.66296, 0.31828, 0.67296, 0.29571, 0.68168, 0.28276, 0.69115, 0.27065, 0.70351, 0.26166, 0.72544, 0.24883, 0.7567, 0.23514, 0.82328, 0.24053, 0.85172, 0.24533, 0.8623, 0.25222, 0.87121, 0.26253, 0.88034, 0.26554, 0.88996, 0.27428, 0.89853, 0.2891, 0.89951, 0.34886, 0.91603, 0.33134, 0.93018, 0.33248, 0.93501, 0.35881, 0.94993, 0.36674, 0.95371, 0.42482, 0.97178, 0.43237, 0.98809, 0.43356, 0.99382, 0.38383, 0.99955, 0.31306, 0.9996, 0.242, 0.99534, 0.2026, 0.99188, 0.12149, 0.96831, 0.09593, 0.92098, 0.11542, 0.90545, 0.12548, 0.89742, 0.11998, 0.88889, 0.1289, 0.87758, 0.13013, 0.86669, 0.12441, 0.85515, 0.09382, 0.82536, 0.02095, 0.77168, 0, 0.74158, 0.00154, 0.72207, 0.01328, 0.70528, 0.04274, 0.68603, 0.06622, 0.67408, 0.08829, 0.6628, 0.10078, 0.65206, 0.11289, 0.63902, 0.11511, 0.62716, 0.11322, 0.61611, 0.09277, 0.57541, 0.07528, 0.52709, 0.07215, 0.50923, 0.07017, 0.48889, 0.05748, 0.47193, 0.04714, 0.45406, 0.04893, 0.43815, 0.06377, 0.42151, 0.03026, 0.40387, 0.06975, 0.3907, 0.11269, 0.3786, 0.16168, 0.36693, 0.2153, 0.35633, 0.26873, 0.34651, 0.26131, 0.34191, 0.33365, 0.3317, 0.40046, 0.32308, 0.42497, 0.31588, 0.44432, 0.31204, 0.43624, 0.30033, 0.39955, 0.29071, 0.35553, 0.2793, 0.3232, 0.26902, 0.29627, 0.25693, 0.29098, 0.26677, 0.27257, 0.29256, 0.2525, 0.31111, 0.25689, 0.31663, 0.23481, 0.32532, 0.24229, 0.33202, 0.23143, 0.33685, 0.22773, 0.34323, 0.2146, 0.35311, 0.15517, 0.35011, 0.09574, 0.3471, 0.03794, 0.34418, 0.04505, 0.33803, 0.04967, 0.33097, 0.06474, 0.32508, 0.0674, 0.31478, 0.08823, 0.31089, 0.09593, 0.30669, 0.09044, 0.29793, 0.1102, 0.28884, 0.10655, 0.28094, 0.11366, 0.25091, 0.11104, 0.23957, 0.11864, 0.23497, 0.10985, 0.2209, 0.10774, 0.20529, 0.1104, 0.19324, 0.11777, 0.18375, 0.13129, 0.17751, 0.17424, 0.17234, 0.15322, 0.16591, 0.1546, 0.15573, 0.17873, 0.14437, 0.21012, 0.13573, 0.22826, 0.12758, 0.24459, 0.11748, 0.24899, 0.10613, 0.25117, 0.09385, 0.24765, 0.08452, 0.24595, 0.07649, 0.20797, 0.07511, 0.17329, 0.07177, 0.16114, 0.06654, 0.16446, 0.05972, 0.21533, 0.04003, 0.27055, 0.02458, 0.34156, 0.0118, 0.07848, 0.41127, 0.36551, 0.0959, 0.36784, 0.13816, 0.36644, 0.11747, 0.36598, 0.10669, 0.3669, 0.12825, 0.34337, 0.14106, 0.3202, 0.1566, 0.25247, 0.16505, 0.36136, 0.14587, 0.37441, 0.1635, 0.53353, 0.15256, 0.54572, 0.17581, 0.58596, 0.17121, 0.48027, 0.17718, 0.40087, 0.17532, 0.42902, 0.15793, 0.42971, 0.1495, 0.42902, 0.13995, 0.42971, 0.12818, 0.43245, 0.11737, 0.43245, 0.10655, 0.43075, 0.09723, 0.50177, 0.16063, 0.50108, 0.15236, 0.50383, 0.14122, 0.53039, 0.15876, 0.43421, 0.17067, 0.55669, 0.16083, 0.72466, 0.17351, 0.64585, 0.16173, 0.29231, 0.1885, 0.30918, 0.17091, 0.20198, 0.1842, 0.244, 0.19768, 0.16118, 0.17803, 0.26405, 0.17496, 0.28457, 0.21859, 0.30713, 0.23707, 0.29947, 0.24641, 0.37281, 0.21019, 0.33726, 0.23213, 0.33299, 0.25702, 0.45752, 0.19356, 0.3846, 0.27724, 0.4729, 0.28938, 0.57268, 0.28988, 0.64877, 0.28223, 0.72874, 0.26907, 0.80071, 0.26764, 0.84356, 0.27241, 0.5525, 0.19401, 0.64795, 0.2034, 0.64466, 0.19209, 0.70044, 0.18686, 0.77738, 0.19064, 0.7161, 0.21575, 0.75743, 0.23463, 0.7605, 0.25243, 0.53914, 0.25626, 0.95563, 0.24213, 0.47732, 0.21244, 0.57374, 0.21176, 0.66639, 0.22237, 0.39942, 0.22824, 0.38199, 0.25274, 0.42228, 0.27266, 0.70148, 0.24167, 0.68296, 0.26534, 0.60971, 0.28041, 0.49841, 0.28258, 0.44483, 0.24701, 0.50046, 0.23316, 0.57068, 0.23259, 0.62397, 0.24298, 0.6303, 0.2598, 0.59152, 0.27319, 0.51061, 0.27516, 0.45382, 0.26641, 0.91187, 0.22232, 0.88219, 0.23318, 0.88667, 0.24918, 0.91736, 0.25917, 0.822, 0.26098, 0.84391, 0.20261, 0.79585, 0.20717, 0.78077, 0.24357, 0.77475, 0.2228, 0.89642, 0.26853, 0.95449, 0.22153, 0.95913, 0.25948, 0.63529, 0.17044, 0.71416, 0.16372, 0.36375, 0.18034, 0.77028, 0.28441, 0.83413, 0.28493, 0.82181, 0.29739, 0.78932, 0.31401, 0.76132, 0.33218, 0.749, 0.34802, 0.71203, 0.38738, 0.70009, 0.40412, 0.73356, 0.36737, 0.75908, 0.29722, 0.74004, 0.31332, 0.72323, 0.33175, 0.70307, 0.34785, 0.67282, 0.36758, 0.63362, 0.38627, 0.60871, 0.40635, 0.64414, 0.29657, 0.63538, 0.31116, 0.61541, 0.32527, 0.59525, 0.33906, 0.56095, 0.35493, 0.51925, 0.37205, 0.47332, 0.38831, 0.53927, 0.30284, 0.53312, 0.31506, 0.51419, 0.32839, 0.47625, 0.33869, 0.42713, 0.35109, 0.36742, 0.36723, 0.15535, 0.18812, 0.14768, 0.19625, 0.14768, 0.20895, 0.15645, 0.22547, 0.16522, 0.24147, 0.14026, 0.30703, 0.12821, 0.31547, 0.11484, 0.32343, 0.10884, 0.33162, 0.10351, 0.33985, 0.16744, 0.34274, 0.17544, 0.3345, 0.18254, 0.32627, 0.19053, 0.31804, 0.2003, 0.30939, 0.25003, 0.24406, 0.22708, 0.28613, 0.14503, 0.28341, 0.1597, 0.25182, 0.24287, 0.25556, 0.2462, 0.22863, 0.22605, 0.21333, 0.23795, 0.26685, 0.15356, 0.26334, 0.19584, 0.19943, 0.13966, 0.42493, 0.25207, 0.44737, 0.36867, 0.46945, 0.28414, 0.37921, 0.2317, 0.38995, 0.17964, 0.4062, 0.37611, 0.40162, 0.53438, 0.44699, 0.63499, 0.44698, 0.7153, 0.42832, 0.68008, 0.42042, 0.57962, 0.42624, 0.41535, 0.39357, 0.34024, 0.37022, 0.33711, 0.41309, 0.29418, 0.42903, 0.39947, 0.45003, 0.49742, 0.46195, 0.60868, 0.46869, 0.70387, 0.46475, 0.7825, 0.45268, 0.72983, 0.44072, 0.4511, 0.40764, 0.42916, 0.42704, 0.13796, 0.44364, 0.15111, 0.46117, 0.17281, 0.50087, 0.17607, 0.55354, 0.21981, 0.6214, 0.21596, 0.63809, 0.20669, 0.65349, 0.2036, 0.66924, 0.19432, 0.68285, 0.15106, 0.69539, 0.23759, 0.69825, 0.25613, 0.68536, 0.28549, 0.67247, 0.30558, 0.65778, 0.31639, 0.64059, 0.31799, 0.62208, 0.11442, 0.723, 0.11133, 0.76024, 0.18087, 0.82041, 0.21486, 0.75738, 0.22722, 0.7255, 0.36463, 0.5528, 0.37171, 0.50012, 0.37171, 0.48328, 0.16724, 0.48178, 0.26963, 0.46505, 0.27655, 0.48103, 0.32812, 0.06238, 0.32482, 0.08113, 0.32436, 0.07238, 0.33048, 0.09264, 0.32774, 0.10566, 0.3164, 0.11777, 0.30336, 0.1296, 0.28522, 0.14063, 0.25915, 0.15159, 0.29192, 0.07186, 0.38771, 0.05123, 0.43453, 0.04427, 0.48781, 0.03824, 0.54668, 0.03452, 0.60491, 0.0328, 0.65162, 0.03363, 0.27167, 0.06518, 0.30972, 0.05492, 0.36346, 0.04469, 0.41843, 0.03704, 0.46882, 0.03099, 0.53753, 0.02611, 0.59912, 0.02471, 0.64997, 0.02619, 0.50708, 0.12983, 0.50992, 0.1188, 0.51388, 0.10868, 0.51742, 0.099], "triangles": [139, 310, 138, 138, 310, 137, 140, 309, 139, 139, 309, 310, 141, 142, 140, 140, 142, 309, 310, 311, 137, 137, 311, 136, 310, 309, 311, 142, 143, 309, 309, 308, 311, 309, 143, 308, 311, 312, 136, 135, 312, 134, 135, 136, 312, 312, 311, 307, 143, 144, 308, 311, 308, 307, 308, 144, 307, 307, 306, 312, 312, 313, 134, 144, 145, 307, 145, 146, 307, 129, 319, 216, 312, 306, 313, 134, 313, 133, 133, 314, 132, 314, 133, 313, 307, 146, 306, 306, 305, 313, 313, 305, 314, 146, 147, 306, 306, 147, 305, 132, 314, 131, 314, 316, 131, 314, 305, 316, 305, 148, 149, 305, 317, 316, 305, 149, 317, 305, 147, 148, 131, 316, 130, 130, 316, 322, 149, 150, 317, 316, 317, 322, 317, 323, 322, 317, 150, 323, 150, 151, 323, 322, 319, 130, 322, 323, 319, 130, 319, 129, 323, 318, 319, 323, 151, 318, 319, 315, 216, 319, 318, 315, 318, 304, 315, 318, 151, 304, 151, 152, 304, 304, 320, 315, 315, 320, 215, 152, 153, 304, 153, 303, 304, 304, 303, 320, 153, 154, 303, 303, 321, 320, 320, 321, 214, 154, 302, 303, 303, 302, 321, 154, 155, 302, 302, 324, 321, 321, 324, 211, 155, 301, 302, 302, 301, 324, 155, 156, 301, 301, 300, 324, 324, 300, 210, 301, 156, 300, 46, 47, 343, 343, 47, 48, 48, 49, 343, 46, 344, 45, 46, 343, 344, 45, 344, 44, 344, 345, 44, 44, 345, 43, 343, 333, 344, 343, 332, 333, 43, 345, 42, 344, 346, 345, 344, 333, 346, 345, 41, 42, 345, 346, 41, 333, 334, 346, 333, 335, 334, 346, 40, 41, 346, 334, 40, 334, 335, 39, 334, 39, 40, 39, 335, 278, 39, 278, 38, 278, 277, 38, 38, 277, 37, 37, 277, 36, 277, 279, 36, 279, 35, 36, 82, 83, 80, 81, 82, 80, 84, 80, 83, 80, 84, 79, 79, 84, 78, 84, 85, 78, 85, 77, 78, 77, 86, 76, 75, 76, 87, 86, 77, 85, 73, 75, 72, 72, 75, 87, 72, 88, 89, 89, 71, 72, 71, 90, 70, 70, 90, 91, 86, 87, 76, 88, 72, 87, 75, 73, 74, 90, 71, 89, 91, 69, 70, 91, 92, 69, 92, 68, 69, 92, 93, 68, 93, 67, 68, 67, 367, 66, 67, 93, 367, 93, 94, 367, 94, 95, 367, 367, 368, 66, 66, 368, 65, 95, 366, 367, 367, 366, 368, 95, 96, 366, 368, 366, 365, 366, 96, 365, 368, 369, 65, 368, 365, 369, 65, 369, 64, 96, 97, 365, 365, 358, 369, 64, 369, 63, 369, 358, 359, 63, 369, 359, 97, 98, 365, 365, 98, 358, 98, 99, 358, 63, 359, 62, 358, 357, 359, 359, 360, 62, 359, 357, 360, 99, 100, 358, 358, 100, 357, 62, 360, 61, 61, 360, 361, 100, 101, 357, 361, 360, 356, 360, 357, 356, 357, 101, 356, 61, 361, 60, 59, 60, 362, 60, 361, 362, 361, 356, 362, 101, 102, 356, 356, 355, 362, 356, 102, 355, 59, 362, 58, 362, 355, 363, 362, 363, 58, 363, 355, 354, 102, 103, 355, 355, 103, 354, 58, 363, 57, 57, 363, 56, 363, 354, 364, 363, 364, 56, 364, 354, 353, 103, 104, 354, 354, 104, 353, 56, 364, 55, 104, 105, 353, 54, 55, 370, 353, 106, 352, 55, 364, 370, 353, 370, 364, 353, 105, 106, 353, 352, 370, 106, 107, 352, 54, 370, 53, 370, 352, 351, 352, 107, 351, 370, 351, 371, 370, 371, 53, 371, 375, 372, 375, 371, 351, 53, 371, 52, 107, 108, 351, 52, 371, 51, 108, 109, 351, 109, 373, 351, 351, 373, 375, 371, 372, 51, 51, 372, 50, 109, 110, 373, 50, 342, 49, 49, 342, 343, 342, 50, 327, 375, 327, 372, 50, 372, 327, 110, 350, 373, 373, 374, 375, 373, 350, 374, 375, 374, 327, 110, 111, 350, 327, 341, 342, 341, 327, 326, 342, 332, 343, 350, 326, 374, 327, 374, 326, 342, 341, 332, 111, 349, 350, 350, 349, 326, 111, 112, 349, 326, 340, 341, 341, 348, 332, 341, 340, 348, 340, 326, 325, 349, 112, 325, 326, 349, 325, 325, 112, 113, 325, 330, 340, 340, 339, 348, 340, 330, 339, 339, 331, 348, 113, 177, 325, 325, 177, 330, 113, 114, 177, 330, 329, 339, 339, 329, 331, 331, 329, 328, 114, 115, 177, 177, 115, 330, 115, 116, 330, 330, 116, 329, 328, 338, 331, 116, 117, 329, 329, 117, 328, 117, 118, 328, 328, 118, 338, 338, 299, 337, 299, 298, 292, 291, 298, 297, 118, 119, 338, 338, 119, 299, 119, 121, 299, 299, 121, 298, 121, 122, 298, 298, 122, 297, 119, 120, 121, 122, 123, 297, 332, 336, 333, 332, 348, 336, 333, 336, 335, 348, 347, 336, 348, 331, 347, 336, 286, 335, 336, 347, 286, 335, 286, 278, 331, 337, 347, 347, 293, 286, 347, 337, 293, 286, 293, 285, 286, 285, 278, 285, 293, 292, 278, 285, 277, 331, 338, 337, 337, 299, 293, 293, 299, 292, 285, 284, 277, 277, 284, 279, 292, 291, 285, 285, 291, 284, 292, 298, 291, 291, 290, 284, 284, 283, 279, 291, 297, 290, 284, 290, 283, 279, 276, 35, 279, 283, 276, 35, 276, 34, 290, 297, 296, 283, 282, 276, 34, 275, 33, 34, 276, 275, 276, 282, 275, 290, 289, 283, 283, 289, 282, 290, 296, 289, 297, 123, 296, 296, 123, 124, 32, 33, 274, 33, 275, 274, 282, 281, 275, 275, 281, 274, 289, 288, 282, 282, 288, 281, 296, 295, 289, 296, 124, 295, 289, 295, 288, 274, 273, 32, 32, 273, 31, 295, 294, 288, 295, 124, 294, 281, 280, 274, 274, 280, 273, 288, 287, 281, 281, 287, 280, 124, 125, 294, 288, 294, 287, 287, 294, 223, 125, 222, 294, 294, 222, 223, 273, 272, 31, 280, 271, 273, 280, 287, 224, 20, 206, 19, 206, 207, 269, 16, 269, 207, 207, 15, 16, 206, 18, 19, 206, 269, 18, 269, 17, 18, 269, 16, 17, 321, 211, 214, 125, 126, 222, 31, 272, 30, 273, 271, 272, 271, 280, 224, 287, 223, 224, 127, 221, 126, 126, 221, 222, 223, 246, 224, 222, 247, 223, 223, 247, 246, 221, 243, 222, 222, 243, 247, 30, 272, 29, 272, 227, 29, 227, 272, 226, 224, 225, 271, 272, 271, 226, 271, 225, 226, 246, 254, 253, 246, 247, 254, 243, 255, 247, 247, 255, 254, 224, 245, 225, 224, 246, 245, 245, 253, 252, 245, 246, 253, 127, 128, 221, 242, 221, 219, 221, 128, 219, 221, 242, 243, 254, 236, 253, 254, 255, 236, 29, 265, 28, 29, 227, 265, 253, 236, 252, 243, 242, 255, 226, 260, 227, 227, 260, 265, 226, 225, 235, 219, 128, 129, 265, 259, 28, 259, 265, 258, 225, 245, 235, 226, 235, 260, 28, 267, 27, 28, 259, 267, 242, 248, 255, 255, 248, 236, 245, 244, 235, 245, 252, 244, 235, 263, 260, 265, 260, 258, 260, 263, 258, 236, 251, 252, 252, 251, 244, 27, 267, 237, 267, 259, 237, 259, 258, 237, 219, 218, 242, 219, 129, 216, 248, 249, 236, 236, 250, 251, 236, 249, 250, 27, 237, 26, 218, 219, 216, 218, 216, 215, 242, 241, 248, 242, 218, 241, 263, 235, 234, 234, 235, 244, 263, 257, 258, 258, 257, 237, 248, 241, 249, 216, 315, 215, 257, 263, 264, 244, 251, 240, 257, 256, 237, 256, 266, 237, 237, 25, 26, 237, 266, 25, 251, 250, 240, 244, 240, 234, 218, 215, 214, 240, 233, 234, 263, 234, 264, 234, 233, 264, 257, 264, 256, 256, 264, 262, 256, 262, 261, 241, 238, 249, 250, 249, 239, 250, 239, 240, 239, 249, 238, 215, 320, 214, 241, 218, 217, 218, 214, 217, 241, 217, 238, 266, 24, 25, 264, 233, 262, 239, 229, 240, 240, 229, 233, 266, 256, 24, 256, 261, 23, 24, 256, 23, 214, 208, 217, 214, 211, 208, 233, 229, 262, 217, 220, 238, 238, 228, 239, 238, 220, 228, 239, 228, 229, 208, 270, 217, 220, 270, 192, 220, 217, 270, 261, 22, 23, 262, 230, 231, 262, 231, 261, 228, 230, 229, 262, 229, 230, 231, 232, 261, 261, 232, 22, 232, 21, 22, 228, 190, 230, 190, 268, 230, 220, 191, 228, 191, 189, 228, 228, 189, 190, 220, 192, 191, 230, 268, 231, 232, 20, 21, 232, 231, 20, 20, 231, 206, 208, 209, 270, 231, 268, 206, 270, 187, 192, 270, 209, 187, 192, 204, 191, 191, 200, 189, 191, 204, 200, 200, 203, 189, 189, 205, 190, 189, 203, 205, 192, 187, 204, 268, 207, 206, 207, 268, 190, 209, 184, 187, 187, 193, 204, 204, 193, 200, 190, 205, 14, 207, 190, 14, 184, 186, 187, 187, 186, 193, 207, 14, 15, 203, 188, 205, 205, 188, 14, 193, 201, 200, 200, 201, 203, 203, 201, 188, 193, 186, 194, 193, 194, 201, 188, 13, 14, 13, 188, 202, 188, 201, 202, 324, 210, 211, 211, 210, 208, 156, 157, 300, 210, 213, 208, 208, 213, 209, 157, 158, 300, 300, 212, 210, 300, 158, 212, 212, 159, 210, 210, 159, 213, 212, 158, 159, 159, 185, 213, 213, 185, 209, 159, 160, 185, 209, 185, 184, 160, 161, 185, 185, 161, 384, 185, 384, 184, 384, 161, 162, 384, 383, 184, 162, 163, 384, 384, 163, 383, 163, 164, 383, 195, 186, 179, 186, 195, 194, 184, 183, 186, 184, 383, 183, 201, 194, 202, 194, 195, 202, 186, 183, 179, 13, 202, 12, 202, 400, 12, 202, 195, 400, 383, 382, 183, 183, 382, 182, 183, 182, 179, 381, 182, 382, 383, 164, 382, 195, 196, 400, 195, 179, 196, 179, 182, 196, 12, 400, 11, 400, 196, 401, 164, 165, 382, 182, 180, 196, 182, 381, 180, 400, 401, 11, 180, 380, 181, 180, 381, 380, 382, 165, 381, 196, 197, 401, 196, 180, 197, 11, 401, 10, 401, 402, 10, 401, 197, 402, 165, 166, 381, 381, 166, 380, 180, 181, 197, 181, 198, 197, 197, 198, 402, 9, 10, 403, 10, 402, 403, 402, 198, 403, 380, 178, 181, 181, 199, 198, 181, 178, 199, 198, 199, 403, 166, 167, 380, 380, 167, 379, 380, 379, 178, 377, 379, 168, 8, 9, 389, 199, 387, 403, 387, 388, 403, 389, 9, 403, 391, 7, 8, 389, 403, 388, 391, 8, 390, 390, 8, 389, 199, 377, 386, 199, 386, 387, 377, 199, 178, 378, 377, 385, 385, 377, 168, 167, 168, 379, 178, 379, 377, 376, 386, 378, 168, 169, 385, 386, 377, 378, 169, 392, 385, 169, 170, 392, 392, 170, 173, 378, 385, 376, 385, 392, 376, 173, 170, 171, 171, 172, 173, 392, 393, 376, 393, 392, 174, 386, 393, 394, 386, 376, 393, 392, 173, 174, 394, 393, 175, 387, 386, 395, 393, 174, 175, 386, 394, 395, 395, 394, 176, 387, 396, 388, 387, 395, 396, 389, 388, 397, 394, 175, 176, 176, 0, 395, 395, 0, 396, 391, 6, 7, 388, 396, 397, 389, 398, 390, 389, 397, 398, 390, 399, 391, 391, 399, 6, 390, 398, 399, 396, 1, 397, 396, 0, 1, 399, 5, 6, 5, 399, 4, 397, 2, 398, 397, 1, 2, 399, 398, 4, 398, 3, 4, 398, 2, 3], "vertices": [3, 6, 205.92, 57.83, 0.96198, 66, -231.28, 140.48, 0.01573, 67, -163.87, 140.48, 0.02229, 3, 6, 218.95, 23.62, 0.96167, 66, -218.26, 106.27, 0.01505, 67, -150.84, 106.27, 0.02328, 3, 6, 222.27, -5.97, 0.96471, 66, -214.93, 76.68, 0.01276, 67, -147.52, 76.68, 0.02252, 3, 6, 216.4, -31.11, 0.97178, 66, -220.81, 51.54, 0.01112, 67, -153.39, 51.54, 0.0171, 3, 6, 203.97, -48.1, 0.98329, 66, -233.23, 34.55, 0.00685, 67, -165.82, 34.55, 0.00986, 3, 6, 188.32, -59.72, 0.99351, 66, -248.88, 22.93, 0.0045, 67, -181.47, 22.93, 0.00199, 2, 6, 175.79, -64.38, 0.99327, 66, -261.42, 18.27, 0.00673, 2, 6, 165.86, -69.9, 0.98746, 66, -271.34, 12.75, 0.01254, 2, 6, 158.36, -71.75, 0.98443, 66, -278.85, 10.9, 0.01557, 1, 6, 41.79, -41.5, 1, 2, 6, 21.91, -41.18, 0.98258, 5, 79.73, -43.54, 0.01742, 3, 6, 2.03, -40.85, 0.63685, 5, 60.04, -40.79, 0.36257, 4, 307.84, -61.22, 0.00058, 3, 6, -17.85, -40.52, 0.30492, 5, 40.34, -38.05, 0.63528, 4, 290.84, -50.91, 0.0598, 3, 6, -37.73, -40.2, 0.03434, 5, 20.65, -35.3, 0.53786, 4, 273.84, -40.59, 0.42779, 2, 4, 246.98, -56.37, 0.80113, 35, 36.82, 54.01, 0.19887, 2, 4, 236.33, -75.29, 0.48753, 35, 26.17, 35.09, 0.51247, 2, 4, 226.31, -96.53, 0.13767, 35, 16.15, 13.85, 0.86233, 1, 35, 8.23, -4.85, 1, 3, 4, 201.63, -115.61, 0.07656, 35, -8.54, -5.23, 0.91459, 63, -269.44, 59.72, 0.00885, 3, 4, 186.54, -113.2, 0.26426, 35, -23.63, -2.82, 0.72274, 63, -266.31, 44.76, 0.013, 3, 4, 170.59, -110, 0.50739, 35, -39.57, 0.38, 0.47701, 63, -263.62, 28.72, 0.01561, 4, 4, 147.4, -117.81, 0.71304, 35, -62.76, -7.43, 0.17258, 39, 61.96, -28.71, 0.0984, 63, -248.06, 9.84, 0.01599, 5, 4, 125.29, -130.58, 0.79292, 35, -84.87, -20.2, 0.00206, 40, 65.48, -11.15, 0.04922, 39, 39.85, -41.48, 0.14029, 63, -228.25, -6.26, 0.01551, 4, 4, 103.62, -147.82, 0.78508, 44, -331.58, -38.88, 0, 40, 43.81, -28.38, 0.19627, 63, -204.42, -20.37, 0.01865, 5, 4, 80.53, -160.12, 0.79418, 44, -322.92, -14.19, 0, 41, 38.67, -20.79, 0.09805, 40, 20.72, -40.69, 0.08824, 63, -184.69, -37.56, 0.01953, 4, 4, 57.37, -163.39, 0.78379, 44, -308.09, 3.91, 0, 41, 15.5, -24.06, 0.19595, 63, -173.38, -58.03, 0.02026, 4, 4, 38.63, -160.36, 0.78203, 44, -292.25, 14.36, 0, 41, -3.24, -21.03, 0.19551, 63, -169.54, -76.62, 0.02246, 4, 4, 13.51, -147.07, 0.78421, 44, -264.76, 21.59, 0, 41, -28.35, -7.73, 0.19605, 63, -173, -104.83, 0.01974, 5, 4, -5.44, -122.62, 0.77627, 3, 162.5, -103.66, 0.01455, 44, -234.26, 16.43, 0, 40, -65.25, -3.18, 0.19771, 63, -189.1, -131.25, 0.01147, 3, 4, -7.15, -91.89, 0.68847, 3, 143.52, -79.44, 0.11153, 39, -92.59, -2.8, 0.2, 2, 4, -26.97, -81.71, 0.64162, 3, 121.44, -82.44, 0.35838, 2, 4, -45.02, -68.93, 0.44715, 3, 99.32, -82.29, 0.55285, 3, 4, -70.3, -39.51, 0.13181, 3, 61.75, -72.63, 0.84051, 44, -130.27, -0.81, 0.02767, 4, 4, -97.58, -15.89, 0.00791, 3, 25.86, -68.88, 0.81729, 42, -55.19, 63.76, 0.00615, 44, -94.19, 0.28, 0.16865, 4, 4, -111.31, -11.09, 0.00082, 3, 11.85, -72.8, 0.69721, 42, -41.84, 69.53, 0.02395, 44, -80.83, 6.04, 0.27801, 3, 3, -12.44, -75.08, 0.43739, 42, -18.08, 75.03, 0.05952, 44, -57.07, 11.55, 0.50309, 4, 3, -44.87, -83.45, 0.10008, 42, 12.95, 87.68, 0.01789, 44, -26.05, 24.2, 0.82489, 58, -69.53, 103.41, 0.05714, 3, 3, -74.37, -90.28, 0.0063, 44, 2.26, 34.92, 0.88798, 58, -41.41, 92.2, 0.10571, 2, 44, 11.95, 31.64, 0.82, 58, -36.47, 83.23, 0.18, 3, 42, 76.52, 99.47, 0.00095, 44, 37.52, 35.99, 0.80389, 58, -14.68, 69.17, 0.19516, 3, 42, 104.71, 124.27, 0, 44, 65.71, 60.78, 0.37156, 58, 22.86, 68.42, 0.62844, 2, 44, 78.34, 77.43, 0.19101, 58, 43.41, 72.17, 0.80899, 2, 44, 89.7, 106.7, 0.03553, 58, 71.56, 86.08, 0.96447, 3, 44, 122.72, 111.07, 0.0155, 58, 98.86, 67.01, 0.96907, 61, -180.03, -131.27, 0.01543, 3, 44, 152.04, 96.7, 0.00354, 58, 110.78, 36.61, 0.97359, 61, -204.24, -153.18, 0.02288, 3, 44, 177.25, 81.61, 0.06896, 58, 119.19, 8.46, 0.90417, 61, -227.59, -171.01, 0.02687, 4, 55, 196.63, 173.94, 0.05698, 44, 192.75, 61.52, 1e-05, 58, 117.05, -16.83, 0.91485, 61, -252, -177.97, 0.02816, 5, 42, 240.67, 105.83, 0.00837, 55, 197.92, 152.84, 0.19631, 44, 201.67, 42.34, 0, 58, 110.68, -36.99, 0.77137, 61, -273.11, -179.15, 0.02396, 4, 42, 250.5, 79.86, 0.03656, 55, 197.58, 125.07, 0.42521, 58, 100.39, -62.79, 0.52263, 61, -300.88, -178.67, 0.01559, 4, 42, 254.48, 51.68, 0.08213, 55, 190.98, 97.38, 0.67574, 58, 84.29, -86.25, 0.2334, 61, -328.53, -171.92, 0.00873, 2, 42, 254.7, 32.22, 0.06, 55, 184.08, 79.19, 0.94, 3, 42, 275.51, 40.43, 0.04286, 55, 206.45, 79.22, 0.95714, 44, 236.52, -23.06, 0, 2, 55, 240.92, 76.54, 1, 44, 269.59, -12.96, 0, 2, 55, 280.66, 71.66, 1, 44, 308.36, -2.98, 0, 2, 55, 353.29, 61.4, 1, 44, 379.71, 14.02, 0, 1, 55, 407.63, 50.35, 1, 1, 55, 453.74, 39.26, 1, 2, 55, 478.85, 34.13, 0.99704, 56, -36.15, 31.63, 0.00296, 2, 55, 497.49, 32.4, 0.90127, 56, -17.44, 31.24, 0.09873, 2, 55, 515.8, 28.69, 0.57546, 56, 1.09, 28.84, 0.42454, 2, 55, 534.36, 19.58, 0.11239, 56, 20.26, 21.08, 0.88761, 1, 56, 37.08, 12.66, 1, 1, 56, 54.98, 8.47, 1, 1, 56, 78.19, 5.05, 1, 1, 56, 118.96, 4.31, 1, 1, 56, 177.07, 3.27, 1, 2, 56, 300.41, 6.86, 1, 44, 835.2, 111.2, 0, 2, 56, 352.71, 13.21, 1, 44, 883.23, 132.84, 0, 3, 56, 372.09, 16.76, 0.99556, 57, -30.32, 25.23, 0.00444, 44, 900.67, 142.01, 0, 3, 56, 388.3, 20.97, 0.91002, 57, -13.58, 25.21, 0.08998, 44, 914.89, 150.86, 0, 3, 56, 404.82, 26.68, 0.47915, 57, 3.85, 26.57, 0.52085, 44, 928.95, 161.23, 0, 3, 56, 422.46, 29.33, 0.10375, 57, 21.59, 24.67, 0.89625, 44, 945.01, 169.02, 0, 3, 56, 438, 34.29, 0.00951, 57, 37.88, 25.55, 0.99049, 44, 958.36, 178.39, 0, 3, 56, 439.32, 40.76, 0.00083, 57, 40.79, 31.48, 0.99917, 44, 957.69, 184.96, 0, 2, 57, 75.43, 51.28, 1, 44, 976.59, 220.1, 0, 2, 57, 99.88, 39.23, 1, 44, 1003.72, 222.82, 0, 2, 57, 108.77, 38.12, 1, 44, 1011.84, 226.59, 0, 2, 57, 137.95, 44.33, 1, 44, 1033.3, 247.31, 0, 2, 57, 145.44, 46.44, 1, 44, 1038.54, 253.06, 0, 2, 57, 182.78, 65.02, 1, 44, 1060.38, 288.59, 0, 2, 57, 213.06, 62.84, 1, 44, 1087.22, 302.78, 0, 2, 57, 223.58, 61.46, 1, 44, 1096.88, 307.18, 0, 2, 57, 230.24, 38.58, 1, 44, 1114.64, 291.3, 0, 2, 57, 224.94, 8.69, 1, 44, 1125.98, 263.13, 0, 1, 57, 211.76, -19.91, 1, 1, 57, 202.46, -35.41, 1, 1, 57, 153.33, -61.9, 1, 1, 57, 65.18, -57.13, 1, 1, 57, 38.37, -43.8, 1, 2, 56, 440.85, -29.52, 0.02783, 57, 24.52, -36.91, 0.97217, 2, 56, 425.29, -33.08, 0.18515, 57, 8.56, -36.43, 0.81485, 2, 56, 404.12, -30.87, 0.64527, 57, -11.36, -28.94, 0.35473, 2, 56, 383.98, -31.89, 0.96855, 57, -31.11, -24.84, 0.03145, 1, 56, 362.88, -35.98, 1, 1, 56, 308.9, -53.29, 1, 2, 56, 212.23, -92.08, 1, 44, 780.52, -9.53, 0, 2, 56, 157.36, -105.32, 1, 44, 732.09, -38.52, 0, 3, 55, 625.98, -115.81, 0.00018, 56, 121.3, -107.43, 0.99982, 44, 698.3, -51.28, 0, 3, 55, 594.87, -110.94, 0.00965, 56, 89.92, -104.79, 0.99035, 44, 667.56, -58.12, 0, 3, 55, 559.18, -98.49, 0.0732, 56, 53.44, -94.92, 0.9268, 44, 629.79, -59.57, 0, 3, 55, 537, -88.54, 0.18128, 56, 30.6, -86.57, 0.81872, 44, 605.51, -58.41, 0, 2, 55, 516.07, -79.18, 0.37302, 56, 9.06, -78.74, 0.62698, 2, 55, 496.17, -73.93, 0.60584, 56, -11.16, -74.92, 0.39416, 2, 55, 472, -68.86, 0.84263, 56, -35.63, -71.59, 0.15737, 2, 55, 450.04, -68.03, 0.95653, 56, -57.6, -72.32, 0.04347, 2, 55, 429.6, -68.94, 0.9936, 56, -77.92, -74.7, 0.0064, 1, 55, 354.31, -78.12, 1, 1, 55, 264.91, -86.1, 1, 1, 55, 231.86, -87.61, 1, 2, 55, 194.22, -88.66, 1, 44, 286.48, -183.8, 0, 2, 55, 162.84, -94.27, 1, 44, 259.33, -200.49, 0, 2, 55, 129.79, -98.89, 1, 44, 230.25, -216.86, 0, 2, 55, 100.34, -98.27, 1, 44, 202.61, -227.06, 0, 2, 55, 69.5, -92.07, 1, 44, 171.64, -232.55, 0, 2, 55, 36.93, -106.62, 1, 44, 146.64, -258, 0, 2, 55, 12.46, -89.81, 1, 44, 117.72, -251.29, 0, 3, 43, 102.18, -100.92, 0.05961, 55, -10.03, -71.51, 0.94039, 44, 90.09, -242.48, 0, 3, 43, 74.32, -89.41, 0.17709, 55, -31.75, -50.6, 0.82291, 44, 62.24, -230.96, 0, 3, 43, 47.59, -75.3, 0.4385, 55, -51.48, -27.7, 0.5615, 44, 35.5, -216.85, 0, 3, 42, 49.12, -138.81, 0.00014, 43, 22.21, -60.74, 0.73562, 55, -69.78, -4.88, 0.26424, 2, 43, 15.48, -66.85, 0.78372, 55, -78.28, -8.11, 0.21628, 5, 4, -21.49, 168.02, 0.00557, 3, -17.01, 125.48, 0.03172, 43, -13.58, -45.03, 0.91153, 55, -97.35, 22.82, 0.05011, 45, 250.9, 68.61, 0.00107, 3, 4, -16.8, 135.56, 0.06092, 3, 5.42, 101.54, 0.26755, 43, -39.01, -24.32, 0.67153, 3, 4, -8.09, 120.98, 0.12973, 3, 20.9, 94.58, 0.41693, 43, -55.29, -19.49, 0.45335, 4, 4, -4.42, 110.69, 0.20799, 3, 29.8, 88.24, 0.50048, 42, -38.04, -92.47, 0.00033, 43, -64.95, -14.4, 0.29121, 3, 4, 17.08, 106.21, 0.46237, 3, 50, 96.87, 0.46467, 43, -83.81, -25.66, 0.07296, 2, 4, 39.32, 114.57, 0.70466, 3, 63.45, 116.45, 0.29534, 3, 4, 65.78, 124.69, 0.74303, 3, 79.35, 139.89, 0.1033, 45, 153.6, 73.7, 0.15367, 2, 4, 88.51, 130.86, 0.69099, 45, 135.19, 59, 0.30901, 2, 4, 113.53, 133.69, 0.39374, 45, 113.36, 46.47, 0.60626, 2, 4, 97.32, 142.29, 0.25023, 45, 131.65, 45.01, 0.74977, 1, 45, 179.69, 39.23, 1, 2, 45, 214.37, 32.15, 0.9463, 46, -28.12, 25.74, 0.0537, 2, 45, 224.5, 34.48, 0.84809, 46, -18.94, 30.62, 0.15191, 2, 45, 240.98, 25.73, 0.45447, 46, -0.75, 26.44, 0.54553, 2, 45, 253.22, 29.48, 0.1848, 46, 10.1, 33.23, 0.8152, 2, 45, 262.36, 25.22, 0.07209, 46, 20.03, 31.49, 0.92791, 2, 45, 274.23, 24.16, 0.01194, 46, 31.77, 33.53, 0.98806, 1, 46, 50.91, 33.68, 1, 1, 46, 53.3, 7.69, 1, 1, 46, 55.69, -18.29, 1, 1, 46, 58.01, -43.57, 1, 2, 45, 268.08, -54.56, 0.01226, 46, 46.22, -44.09, 0.98774, 2, 45, 254.94, -53.16, 0.06398, 46, 33.17, -46.14, 0.93602, 2, 45, 243.77, -47.18, 0.19353, 46, 20.83, -43.26, 0.80647, 2, 45, 224.66, -46.88, 0.46825, 46, 2.3, -47.92, 0.53175, 2, 45, 217.08, -38.27, 0.64861, 46, -7.25, -41.57, 0.35139, 2, 45, 209.17, -35.32, 0.828, 46, -15.66, -40.77, 0.172, 2, 45, 193.07, -38.39, 0.96544, 46, -30.41, -47.91, 0.03456, 1, 45, 175.88, -30.66, 1, 1, 45, 161.35, -32.87, 1, 1, 45, 105.68, -32.28, 1, 1, 45, 84.76, -34.34, 1, 2, 34, -85.76, 60.15, 9e-05, 45, 76.13, -31.45, 0.99991, 2, 34, -60.08, 54.39, 0.01478, 45, 50.27, -36.37, 0.98522, 3, 34, -32.74, 44.93, 0.13812, 4, 231.67, 175.18, 0, 45, 21.42, -38.55, 0.86188, 3, 34, -12.31, 35.92, 0.40369, 4, 252.1, 166.17, 0, 45, -0.9, -38.4, 0.59631, 3, 34, 2.98, 26.7, 0.72206, 4, 267.39, 156.95, 1e-05, 45, -18.6, -36.02, 0.27793, 3, 34, 11.7, 17.16, 0.90666, 4, 276.12, 147.42, 2e-05, 45, -30.39, -30.73, 0.09332, 3, 6, -118.19, 106.66, 0.00277, 34, 14.07, -3.46, 0.98481, 4, 278.48, 126.8, 0.01242, 4, 6, -107.91, 117.51, 0.00806, 5, -29.79, 129.78, 0.05671, 34, 28.41, 0.72, 0.92177, 4, 292.83, 130.98, 0.01347, 5, 6, -89.23, 120.01, 0.01706, 5, -10.95, 129.98, 0.14237, 34, 45.8, -6.54, 0.82487, 4, 310.21, 123.71, 0.01487, 67, -459.02, 202.66, 0.00083, 5, 6, -66.79, 113.24, 0.04969, 5, 10.51, 120.53, 0.26045, 34, 61.77, -23.71, 0.66029, 4, 326.18, 106.54, 0.02635, 67, -436.57, 195.89, 0.00322, 5, 6, -48.81, 102.57, 0.12523, 5, 27.05, 107.75, 0.34825, 34, 71.91, -42, 0.4925, 4, 336.32, 88.26, 0.02994, 67, -418.6, 185.22, 0.00408, 5, 6, -32.65, 97.36, 0.25759, 5, 42.45, 100.62, 0.37387, 34, 83.22, -54.64, 0.34388, 4, 347.64, 75.61, 0.01991, 67, -402.44, 180.01, 0.00475, 5, 6, -13.07, 93.52, 0.51055, 5, 61.42, 94.41, 0.30623, 34, 98.19, -67.85, 0.17341, 4, 362.61, 62.4, 0.00464, 67, -382.86, 176.16, 0.00517, 4, 6, 7.97, 95.09, 0.81696, 5, 82.49, 93.42, 0.14436, 34, 117.15, -77.1, 0.03111, 67, -361.82, 177.74, 0.00757, 2, 6, 30.55, 97.9, 0.98949, 67, -339.24, 180.54, 0.01051, 2, 6, 47.34, 102.21, 0.98774, 67, -322.45, 184.86, 0.01226, 2, 6, 61.88, 105.36, 0.98737, 67, -307.91, 188.01, 0.01263, 2, 6, 61.74, 121.86, 0.98656, 67, -308.05, 204.51, 0.01344, 3, 6, 65.39, 137.54, 0.98542, 66, -371.81, 220.19, 0.00185, 67, -304.4, 220.19, 0.01273, 3, 6, 74.09, 144.27, 0.98368, 66, -363.12, 226.92, 0.00401, 67, -295.7, 226.92, 0.01232, 3, 6, 86.77, 144.93, 0.97993, 66, -350.44, 227.58, 0.00717, 67, -283.02, 227.58, 0.01289, 3, 6, 126.31, 129.37, 0.97557, 66, -310.9, 212.02, 0.01239, 67, -243.48, 212.02, 0.01204, 3, 6, 158.39, 110.69, 0.96849, 66, -278.82, 193.34, 0.01458, 67, -211.4, 193.34, 0.01693, 3, 6, 186.73, 84.51, 0.9648, 66, -250.48, 167.16, 0.0156, 67, -183.06, 167.16, 0.0196, 2, 55, 50.53, -85.86, 1, 44, 151.71, -233.7, 0, 1, 6, 34.83, 48.88, 1, 4, 6, -42.17, 35.09, 0.13257, 5, 25.42, 39.96, 0.66516, 34, 43.59, -103.61, 0.08641, 4, 308, 26.65, 0.11586, 3, 6, -4.48, 41.95, 0.687, 5, 63.66, 42.18, 0.31277, 4, 344, 13.55, 0.00023, 2, 6, 15.17, 45.42, 0.97404, 5, 83.59, 43.23, 0.02596, 4, 6, -24.13, 38.49, 0.35153, 5, 43.73, 41.14, 0.62641, 34, 60.87, -109.77, 0.00326, 4, 325.29, 20.48, 0.0188, 4, 6, -49.19, 44.56, 0.11627, 5, 19.6, 50.22, 0.54165, 34, 42.31, -91.89, 0.19051, 4, 306.72, 38.37, 0.15158, 4, 6, -79.19, 49.66, 0.02522, 5, -9.55, 58.94, 0.18226, 34, 18.98, -72.35, 0.39903, 4, 283.39, 57.9, 0.3935, 3, 6, -99.37, 75.76, 0.01302, 34, 14.72, -39.63, 0.78745, 4, 279.13, 90.62, 0.19953, 4, 6, -56.7, 35.49, 0.05245, 5, 11.05, 42.14, 0.52553, 34, 31.25, -95.93, 0.14581, 4, 295.66, 34.33, 0.27621, 4, 6, -87.99, 24.63, 0.0003, 34, -1.25, -89.52, 0.12126, 4, 263.16, 40.73, 0.86411, 63, -437.45, 61.5, 0.01433, 3, 6, -56.83, -39.4, 0.00044, 5, 1.79, -32.18, 0.20158, 4, 257.75, -30.27, 0.79798, 2, 4, 215.68, -19.82, 0.97078, 63, -363.95, 38.72, 0.02922, 3, 4, 217.49, -38.98, 0.86749, 35, 7.33, 71.4, 0.10566, 63, -346.69, 47.23, 0.02685, 2, 4, 223.32, 7.32, 0.97119, 63, -392.03, 36.19, 0.02881, 3, 34, -25.72, -92.34, 0.09182, 4, 238.69, 37.92, 0.88205, 63, -426.09, 39.64, 0.02612, 3, 5, -10.02, 12.19, 0.06972, 4, 264.46, 15.16, 0.90924, 63, -414.02, 71.82, 0.02104, 3, 5, 5.58, 12.56, 0.55172, 4, 278.93, 9.32, 0.43013, 63, -413.72, 87.43, 0.01815, 3, 6, -41.15, 8.65, 0.03219, 5, 23.21, 13.6, 0.9526, 63, -414.02, 105.09, 0.01522, 3, 6, -19.6, 11.93, 0.16946, 5, 45, 14.23, 0.81783, 63, -413.72, 126.88, 0.01271, 3, 6, 0.34, 14.04, 0.75474, 5, 65.05, 13.9, 0.23392, 63, -412.54, 146.9, 0.01134, 2, 6, 20.09, 17.32, 0.98929, 63, -412.54, 166.92, 0.01071, 2, 6, 36.98, 20.87, 0.98829, 63, -413.27, 184.17, 0.01171, 2, 4, 248.66, -12.21, 0.97931, 63, -382.81, 66.82, 0.02069, 3, 5, 1.58, -18.26, 0.26873, 4, 263.07, -17.4, 0.71239, 63, -383.1, 82.13, 0.01889, 4, 6, -38.21, -23.39, 0.03972, 5, 22.22, -18.56, 0.7044, 4, 281.91, -25.84, 0.23911, 63, -381.92, 102.74, 0.01677, 2, 4, 247.52, -24.92, 0.99034, 63, -370.53, 70.28, 0.00966, 2, 4, 241.63, 21.49, 0.97606, 63, -411.79, 48.24, 0.02394, 3, 4, 239.92, -34.1, 0.92615, 35, 29.76, 76.28, 0.06212, 63, -359.25, 66.45, 0.01173, 3, 4, 192.31, -93.07, 0.33894, 35, -17.85, 17.32, 0.63623, 63, -287.18, 42.99, 0.02483, 3, 4, 224.72, -69.24, 0.55009, 35, 14.56, 41.14, 0.42862, 63, -321, 64.78, 0.0213, 3, 34, -31.93, -40.12, 0.43694, 4, 232.48, 90.13, 0.53424, 63, -472.66, 15.23, 0.02882, 4, 6, -106.1, 49.99, 0.00284, 34, -4.09, -58.49, 0.45372, 4, 260.32, 71.77, 0.52838, 63, -465.43, 47.79, 0.01506, 4, 34, -10.69, -6.75, 0.83986, 4, 253.72, 123.5, 0.02944, 45, -19.35, 0.11, 0.11622, 63, -511.42, 23.18, 0.01447, 4, 34, -40.42, -14.7, 0.55796, 4, 223.99, 115.55, 0.30666, 45, 4.78, 19.22, 0.11188, 63, -493.39, -1.77, 0.0235, 3, 6, -129.49, 110.46, 0.00027, 34, 6.23, 5.53, 0.98982, 63, -528.92, 34.61, 0.0099, 4, 6, -116.67, 67.86, 0.00169, 34, -4.2, -37.73, 0.70347, 4, 260.21, 92.53, 0.27921, 63, -484.79, 40.29, 0.01564, 4, 34, -82.79, -17.17, 0.21472, 4, 181.62, 113.08, 0.55662, 45, 42.68, 38.32, 0.20039, 63, -475.98, -40.47, 0.02828, 3, 4, 146.22, 116.23, 0.695, 45, 76.42, 49.5, 0.27908, 63, -466.31, -74.67, 0.02592, 3, 4, 131.23, 125.46, 0.58315, 45, 93.84, 46.98, 0.39953, 63, -469.59, -91.96, 0.01732, 4, 34, -81.75, -58.08, 0.11411, 4, 182.66, 72.17, 0.68269, 36, 81.57, 51.12, 0.1632, 63, -438.13, -24.92, 0.04, 4, 34, -114.26, -29.36, 0.04028, 4, 150.15, 100.89, 0.72772, 36, 49.05, 79.84, 0.192, 63, -453.38, -65.54, 0.04, 3, 4, 107.77, 119.02, 0.77759, 36, 6.68, 97.97, 0.1944, 63, -455.21, -111.59, 0.02801, 4, 34, -65.93, -103.01, 0.14653, 4, 198.48, 27.24, 0.6806, 36, 97.39, 6.19, 0.13465, 63, -401.79, 5.88, 0.03822, 4, 4, 64.91, 111.68, 0.68104, 3, 86.09, 128.72, 0.10066, 36, -36.19, 90.62, 0.19542, 63, -433.07, -149.02, 0.02288, 4, 4, 30.4, 84.3, 0.48924, 3, 73.46, 86.52, 0.28895, 36, -70.69, 63.24, 0.19455, 63, -395.2, -171.5, 0.02726, 4, 4, 14.28, 44.63, 0.40063, 3, 82.95, 44.77, 0.37042, 36, -86.81, 23.58, 0.19276, 63, -352.39, -172.43, 0.03619, 4, 4, 15.88, 9.08, 0.72533, 3, 104.61, 16.54, 0.04267, 36, -85.21, -11.97, 0.192, 63, -319.75, -158.26, 0.04, 3, 4, 26.41, -31.66, 0.768, 36, -74.68, -52.71, 0.192, 63, -285.44, -133.9, 0.04, 4, 4, 17.88, -61.45, 0.72117, 3, 146.62, -40.15, 0.04888, 39, -67.56, 27.65, 0.19251, 63, -254.56, -131.26, 0.03743, 4, 4, 3.08, -75.47, 0.66572, 3, 142.5, -60.13, 0.11024, 39, -82.36, 13.62, 0.19399, 63, -236.18, -140.09, 0.03005, 4, 34, -81.23, -140.79, 0.04197, 4, 183.18, -10.53, 0.77403, 36, 82.09, -31.59, 0.144, 63, -361.04, 5.04, 0.04, 4, 4, 152.34, -42.6, 0.7776, 36, 51.25, -63.65, 0.096, 39, 66.9, 46.5, 0.0864, 63, -320.09, -12.34, 0.04, 4, 4, 172.41, -48.74, 0.75547, 35, -37.75, 61.64, 0.06062, 39, 86.97, 40.36, 0.14402, 63, -321.51, 8.59, 0.03989, 4, 4, 172.92, -74.54, 0.60354, 35, -37.24, 35.84, 0.21515, 39, 87.48, 14.55, 0.14448, 63, -297.58, 18.27, 0.03683, 4, 4, 154.63, -102.89, 0.5781, 35, -55.53, 7.49, 0.19776, 39, 69.19, -13.8, 0.19397, 63, -264.57, 11.28, 0.03017, 4, 4, 120.56, -61.76, 0.77375, 36, 19.47, -82.82, 0.09552, 39, 35.12, 27.33, 0.08597, 63, -290.86, -35.21, 0.04476, 4, 4, 81.6, -65.88, 0.77331, 36, -19.49, -86.93, 0.09547, 39, -3.84, 23.22, 0.08592, 63, -273.13, -70.15, 0.0453, 4, 4, 50.33, -55.36, 0.77432, 36, -50.76, -76.42, 0.09559, 39, -35.11, 33.73, 0.08604, 63, -271.81, -103.1, 0.04405, 3, 4, 77.55, 35.89, 0.69375, 38, -2.21, 1.87, 0.23125, 63, -366.78, -110.2, 0.075, 4, 4, 38.32, -140.37, 0.71635, 44, -278.49, -0.14, 0, 41, -3.54, -1.04, 0.23878, 63, -188.1, -84.04, 0.04487, 4, 34, -101.62, -98.49, 0.04974, 4, 162.79, 31.77, 0.70626, 37, 72.5, 4.27, 0.189, 63, -393.3, -29.09, 0.055, 4, 34, -115.19, -137.58, 0.01728, 4, 149.23, -7.33, 0.73872, 37, 58.94, -34.83, 0.189, 63, -351.93, -27.83, 0.055, 3, 4, 116.71, -37.47, 0.756, 37, 26.42, -64.97, 0.189, 63, -312.18, -47.47, 0.055, 4, 34, -117.04, -56.84, 0.02842, 4, 147.37, 73.41, 0.72758, 37, 57.09, 45.91, 0.189, 63, -426.71, -58.33, 0.055, 3, 4, 107.67, 96.56, 0.756, 37, 17.38, 69.06, 0.189, 63, -434.19, -103.68, 0.055, 4, 4, 67.06, 93.55, 0.6789, 3, 98.23, 115.1, 0.0771, 37, -23.22, 66.05, 0.189, 63, -416.91, -140.55, 0.055, 3, 4, 77.98, -38.81, 0.756, 37, -12.31, -66.3, 0.189, 63, -297.13, -83.18, 0.055, 3, 4, 39.87, -15.76, 0.756, 37, -50.42, -43.26, 0.189, 63, -305.08, -127, 0.055, 4, 4, 24.99, 23.54, 0.64522, 3, 103.81, 33.61, 0.11078, 37, -65.29, -3.96, 0.189, 63, -336.5, -154.9, 0.055, 4, 4, 38.27, 69.58, 0.54879, 3, 88.34, 78.96, 0.20721, 37, -52.02, 42.08, 0.189, 63, -384.25, -158.91, 0.055, 3, 4, 107.98, 67.59, 0.744, 38, 28.21, 33.57, 0.186, 63, -407.24, -93.07, 0.07, 3, 4, 123.42, 36.16, 0.744, 38, 43.66, 2.13, 0.186, 63, -383.37, -67.44, 0.07, 3, 4, 113.68, 7.63, 0.744, 38, 33.91, -26.39, 0.186, 63, -353.25, -66.37, 0.07, 3, 4, 87.56, -6.87, 0.744, 38, 7.8, -40.9, 0.186, 63, -330.38, -85.6, 0.07, 3, 4, 57.5, 1.69, 0.744, 38, -22.26, -32.34, 0.186, 63, -327.67, -116.74, 0.07, 4, 4, 40.27, 26.06, 0.71292, 3, 114.89, 44.42, 0.03108, 38, -39.49, -7.96, 0.186, 63, -344.31, -141.53, 0.07, 4, 4, 49.23, 59.79, 0.63324, 3, 102.93, 77.21, 0.11076, 38, -30.53, 25.77, 0.186, 63, -379.01, -145.18, 0.07, 4, 4, 73.05, 76.78, 0.71563, 3, 112.74, 104.78, 0.02837, 38, -6.71, 42.76, 0.186, 63, -403.38, -128.98, 0.07, 4, 4, 79.27, -135.9, 0.76414, 44, -305.6, -31.16, 0, 41, 37.41, 3.43, 0.19103, 63, -206.87, -47.36, 0.04483, 4, 4, 65.02, -116.84, 0.75771, 44, -282.2, -35.54, 0, 41, 23.16, 22.5, 0.18943, 63, -219.61, -67.48, 0.05286, 3, 4, 36.66, -108.08, 0.75819, 41, -5.2, 31.26, 0.18955, 63, -217.69, -97.1, 0.05226, 3, 4, 14.7, -113.79, 0.76454, 41, -27.16, 25.54, 0.19113, 63, -204.52, -115.57, 0.04433, 4, 4, 26.16, -74.38, 0.75098, 3, 160.8, -46.02, 0.01357, 40, -33.65, 45.06, 0.19114, 63, -245.43, -118.92, 0.04431, 3, 4, 123.75, -121.66, 0.77392, 40, 63.94, -2.23, 0.19348, 63, -236.03, -10.89, 0.0326, 3, 4, 123.22, -99.39, 0.76451, 40, 63.41, 20.04, 0.19113, 63, -256.65, -19.32, 0.04436, 3, 4, 62.56, -69.33, 0.7592, 40, 2.75, 50.1, 0.1898, 63, -263.12, -86.71, 0.051, 3, 4, 99.4, -80.62, 0.75967, 40, 39.6, 38.81, 0.18992, 63, -265.7, -48.25, 0.05042, 4, 4, 1.71, -99.22, 0.72264, 3, 154.97, -80.38, 0.05276, 40, -58.1, 20.21, 0.19385, 63, -213.5, -132.91, 0.03075, 4, 4, 74.12, -153.51, 0.77533, 44, -313.72, -14.72, 0, 41, 32.26, -14.17, 0.19383, 63, -188.59, -45.91, 0.03084, 4, 4, 7.77, -130.33, 0.77641, 44, -249.21, 13.15, 0, 41, -34.09, 9.01, 0.1941, 63, -186.6, -116.16, 0.02949, 3, 4, 211.28, -59.26, 0.70774, 35, 1.12, 51.12, 0.26572, 63, -325.53, 48.67, 0.02654, 3, 4, 210.85, -95.31, 0.17109, 35, 0.69, 15.07, 0.81029, 63, -291.69, 61.11, 0.01863, 3, 34, -28.74, -74.14, 0.23773, 4, 235.67, 56.11, 0.73464, 63, -442.02, 30.33, 0.02762, 4, 4, -6.47, -38.19, 0.63034, 3, 113.33, -35.02, 0.33432, 63, -267.62, -162.3, 0.03493, 61, -214.24, 204.33, 0.0004, 4, 4, -17.13, -63.44, 0.62725, 3, 119.05, -61.82, 0.35311, 63, -240.22, -163.26, 0.01926, 61, -186.85, 203.37, 0.00038, 4, 4, -36.8, -50.28, 0.38505, 3, 95.39, -62.29, 0.59286, 63, -245.51, -186.33, 0.01835, 61, -192.13, 180.3, 0.00375, 4, 4, -60.57, -26.29, 0.10959, 3, 62.17, -56.23, 0.864, 63, -259.45, -217.08, 0.01693, 61, -206.07, 149.54, 0.00948, 6, 4, -87.72, -3.08, 0.00708, 3, 26.62, -52.73, 0.91419, 42, -53.78, 47.66, 0.0044, 44, -92.78, -15.83, 0.04849, 63, -271.46, -250.72, 0.01169, 61, -218.08, 115.9, 0.01416, 5, 3, -3.1, -54.71, 0.69144, 42, -24.59, 53.6, 0.08787, 44, -63.59, -9.88, 0.19572, 63, -276.75, -280.04, 0.00704, 61, -223.37, 86.59, 0.01793, 4, 3, -77.63, -57, 0.08113, 42, 48.95, 65.86, 0.47724, 44, 9.96, 2.37, 0.42079, 61, -239.23, 13.73, 0.02084, 4, 42, 79.62, 72.57, 0.49148, 44, 40.63, 9.09, 0.43592, 58, -30.55, 47.24, 0.05348, 61, -244.35, -17.25, 0.01912, 5, 3, -39.46, -56.98, 0.3299, 42, 11.13, 60.72, 0.29008, 44, -27.87, -2.76, 0.35703, 63, -283.37, -315.86, 0.00379, 61, -229.99, 50.77, 0.0192, 4, 4, -26.91, -25.24, 0.28061, 3, 89.16, -36.1, 0.68488, 63, -272.42, -186.01, 0.03005, 61, -219.05, 180.61, 0.00445, 4, 4, -51.84, -6.99, 0.03715, 3, 58.28, -35.41, 0.92837, 63, -280.59, -215.81, 0.02385, 61, -227.21, 150.82, 0.01063, 5, 4, -81.15, 11.9, 0.00117, 3, 23.43, -36.69, 0.96369, 42, -48.47, 32.18, 0.00113, 63, -287.8, -249.93, 0.01699, 61, -234.42, 116.7, 0.01702, 4, 3, -7.58, -35.52, 0.74265, 42, -17.59, 35.19, 0.22488, 63, -296.45, -279.72, 0.01098, 61, -243.07, 86.9, 0.02149, 4, 3, -46.16, -31.79, 0.10516, 42, 21.14, 36.66, 0.86639, 63, -309.42, -316.25, 0.00352, 61, -256.05, 50.38, 0.02492, 3, 42, 59.51, 33.85, 0.96972, 63, -326.24, -350.85, 0.00135, 61, -272.87, 15.78, 0.02893, 2, 42, 97.98, 37.69, 0.97355, 61, -283.55, -21.37, 0.02645, 4, 4, -8.21, 20.4, 0.21076, 3, 78.37, 12.03, 0.75409, 63, -321.73, -184.81, 0.02803, 61, -268.36, 181.82, 0.00712, 4, 4, -32.1, 33.53, 0.03407, 3, 51.27, 9.12, 0.93091, 63, -325.49, -211.8, 0.02259, 61, -272.11, 154.82, 0.01243, 5, 4, -53.46, 50.84, 0.00783, 3, 23.84, 11.1, 0.94977, 42, -42.48, -15.22, 0.00732, 63, -334.05, -237.93, 0.01714, 61, -280.68, 128.69, 0.01794, 5, 4, -74.21, 68.02, 0.00252, 3, -3.01, 13.3, 0.77365, 42, -15.57, -13.81, 0.18863, 63, -342.7, -263.45, 0.01188, 61, -289.33, 103.18, 0.02333, 6, 4, -96.43, 92.24, 5e-05, 3, -35.09, 20.44, 0.0622, 42, 17.17, -16.59, 0.84842, 43, -9.74, 61.48, 0.05519, 63, -357.42, -292.84, 0.00573, 61, -304.04, 73.79, 0.02841, 4, 42, 53.23, -21.47, 0.88751, 43, 26.31, 56.59, 0.07969, 63, -375.31, -324.52, 0.00106, 61, -321.93, 42.11, 0.03174, 3, 42, 88.48, -28.63, 0.86985, 43, 61.56, 49.44, 0.09844, 61, -341.64, 12.02, 0.03171, 4, 4, -3.01, 66.56, 0.30338, 3, 56.21, 52.86, 0.66468, 63, -366.72, -196.4, 0.02098, 61, -313.34, 170.22, 0.01097, 6, 4, -23.22, 77.09, 0.1404, 3, 33.61, 49.93, 0.78087, 42, -46.95, -55.01, 0.01217, 43, -73.87, 23.05, 0.03389, 63, -369.36, -219.04, 0.01786, 61, -315.98, 147.59, 0.01482, 6, 4, -43.37, 93.47, 0.05319, 3, 7.72, 51.83, 0.59515, 42, -21.04, -53.42, 0.10414, 43, -47.95, 24.64, 0.21996, 63, -377.48, -243.7, 0.01304, 61, -324.1, 122.93, 0.01452, 6, 4, -55.39, 115.48, 0.02008, 3, -14.73, 62.99, 0.28349, 42, 2.7, -61.48, 0.16065, 43, -24.21, 16.59, 0.51706, 63, -393.75, -262.77, 0.00898, 61, -340.38, 103.86, 0.00973, 5, 3, -42.11, 77.86, 0.04778, 42, 31.83, -72.55, 0.13076, 43, 4.92, 5.52, 0.80512, 63, -414.83, -285.73, 0.00367, 61, -361.45, 80.9, 0.01267, 4, 42, 69.06, -85.28, 0.21091, 43, 42.15, -7.21, 0.66206, 55, -31.66, 37.66, 0.10719, 61, -387.07, 51.03, 0.01984, 3, 34, -10.34, 14.53, 0.56219, 45, -11.21, -19.55, 0.42555, 63, -531.42, 15.93, 0.01226, 3, 34, -23.23, 22.96, 0.27262, 45, 3.97, -22.18, 0.71488, 63, -534.71, 0.88, 0.01251, 3, 34, -45.2, 31.34, 0.06231, 45, 27.46, -21.14, 0.92443, 63, -534.71, -22.64, 0.01326, 3, 34, -75.1, 38.72, 0.00227, 45, 57.83, -16.03, 0.98531, 63, -530.95, -53.2, 0.01241, 2, 45, 87.26, -10.96, 0.98684, 63, -527.19, -82.83, 0.01316, 3, 45, 208.95, -16.29, 0.9306, 46, -20.8, -22.44, 0.06305, 63, -537.89, -204.17, 0.00635, 3, 45, 224.8, -20.77, 0.65666, 46, -4.34, -22.66, 0.34056, 63, -543.07, -219.8, 0.00277, 2, 45, 239.77, -25.84, 0.27984, 46, 11.44, -23.69, 0.72016, 2, 45, 255.02, -27.75, 0.0601, 46, 26.67, -21.58, 0.9399, 2, 45, 270.35, -29.36, 0.00501, 46, 41.88, -19.16, 0.99499, 1, 46, 38.7, 8.6, 1, 1, 46, 23.14, 7.27, 1, 3, 45, 243.73, 3.4, 0.17327, 46, 7.69, 5.59, 0.82613, 63, -519.76, -239.78, 0.00061, 3, 45, 228.35, 6.15, 0.81287, 46, -7.87, 4.26, 0.18349, 63, -516.33, -224.54, 0.00364, 2, 45, 212.19, 9.63, 0.99287, 63, -512.14, -208.54, 0.00713, 3, 4, 142.86, 143.73, 0.22029, 45, 90.43, 25.6, 0.76125, 63, -490.8, -87.61, 0.01846, 3, 45, 168.66, 19.21, 0.99068, 46, -68.91, 1.4, 0.00013, 63, -500.65, -165.48, 0.0092, 2, 45, 165.19, -16.18, 0.98991, 63, -535.85, -160.45, 0.01009, 2, 45, 106.5, -12.48, 0.98759, 63, -529.55, -101.98, 0.01241, 3, 4, 124.06, 154.18, 0.11472, 45, 111.83, 23.47, 0.86879, 63, -493.87, -108.9, 0.01649, 3, 4, 170.13, 135.08, 0.26852, 45, 61.97, 22.7, 0.71074, 63, -492.45, -59.05, 0.02074, 3, 4, 199.67, 133.07, 0.21603, 45, 34.06, 12.81, 0.76235, 63, -501.09, -30.73, 0.02161, 3, 4, 105.29, 163.61, 0.03927, 45, 132.8, 22.29, 0.94733, 63, -495.99, -129.8, 0.0134, 2, 45, 127.92, -14.17, 0.98805, 63, -532.19, -123.31, 0.01195, 2, 45, 8.93, -1.28, 0.98376, 63, -514.05, -5.01, 0.01624, 3, 55, 75.66, -59.48, 0.98305, 44, 165.46, -199.96, 0, 61, -484.77, -55.77, 0.01695, 2, 55, 116.94, -11.04, 0.97504, 61, -436.55, -97.3, 0.02496, 3, 42, 244.62, -14.7, 0.01969, 55, 157.55, 39.2, 0.96482, 61, -386.53, -138.18, 0.01549, 4, 42, 102.91, -110.25, 0.03064, 43, 75.99, -32.18, 0.16818, 55, -9.28, 2.05, 0.78223, 61, -422.8, 28.85, 0.01896, 2, 55, 10.71, -20.34, 0.98327, 61, -445.29, 8.97, 0.01673, 3, 55, 40.91, -42.51, 0.98237, 44, 126.92, -196.87, 0, 61, -467.63, -21.11, 0.01763, 4, 42, 126.81, -58.24, 0.56343, 43, 99.9, 19.83, 0.05133, 55, 31.98, 41.73, 0.35688, 61, -383.34, -12.62, 0.02835, 3, 42, 179.68, 35.94, 0.69251, 55, 115.61, 110.07, 0.29531, 61, -315.44, -96.61, 0.01218, 4, 42, 163.67, 76.02, 0.34891, 55, 115.35, 153.23, 0.05041, 58, 33.76, -6.98, 0.58094, 61, -272.28, -96.59, 0.01973, 3, 44, 79.82, 31.74, 0.50495, 58, 13.65, 37.48, 0.47497, 61, -237.83, -62.04, 0.02008, 4, 42, 110.83, 75.78, 0.22995, 44, 71.84, 12.29, 0.39659, 58, -5.37, 28.52, 0.3503, 61, -252.94, -47.42, 0.02316, 3, 42, 136.8, 39.74, 0.91254, 55, 77.09, 129.27, 0.06418, 61, -296.03, -58.19, 0.02328, 4, 42, 106.75, -48.12, 0.73795, 43, 79.83, 29.95, 0.11447, 55, 17, 58.48, 0.12095, 61, -366.5, 2.27, 0.02664, 4, 42, 78.53, -94.06, 0.15091, 43, 51.62, -15.99, 0.52854, 55, -26.05, 26.03, 0.30104, 61, -398.73, 45.49, 0.0195, 4, 42, 152.73, -65.92, 0.26948, 43, 125.82, 12.15, 0.00379, 55, 53.3, 25.11, 0.70224, 61, -400.07, -33.86, 0.02448, 3, 42, 186.97, -72.09, 0.05106, 55, 82.91, 6.85, 0.9247, 61, -418.49, -63.37, 0.02424, 3, 42, 206.34, -15.74, 0.14756, 55, 121.54, 52.22, 0.83633, 61, -373.32, -102.24, 0.0161, 3, 42, 211.27, 31.47, 0.31283, 55, 143.38, 94.36, 0.68042, 61, -331.3, -124.3, 0.00675, 4, 42, 205.18, 80.42, 0.07776, 55, 155.6, 142.16, 0.18669, 58, 67.35, -31.76, 0.71332, 61, -283.57, -136.78, 0.02223, 2, 58, 74.99, 9.01, 0.97252, 61, -242.73, -129.49, 0.02748, 3, 44, 111.04, 75.23, 0.09494, 58, 66.04, 48.47, 0.88798, 61, -209, -107.14, 0.01708, 3, 44, 98.84, 46.04, 0.27279, 58, 37.33, 35.18, 0.70982, 61, -231.6, -85, 0.01739, 4, 42, 125.25, -24.23, 0.84731, 43, 98.34, 53.84, 0.01324, 55, 42.96, 73.96, 0.11104, 61, -351.17, -23.77, 0.02841, 4, 42, 162.09, -19.67, 0.40677, 43, 135.18, 58.4, 0.00011, 55, 78.91, 64.73, 0.57288, 61, -360.58, -59.68, 0.02024, 3, 55, 110.3, -60.03, 0.98151, 44, 197.9, -187.81, 0, 61, -485.51, -90.4, 0.01849, 3, 55, 142.71, -54.21, 0.98114, 44, 225.95, -170.55, 0, 61, -479.86, -122.85, 0.01886, 2, 55, 216.15, -44.51, 0.98189, 61, -470.55, -196.34, 0.01811, 2, 55, 313.64, -42.6, 0.98558, 61, -469.16, -293.84, 0.01442, 2, 55, 439.14, -23.17, 0.98423, 61, -450.39, -419.43, 0.01577, 3, 55, 470.04, -24.66, 0.95804, 56, -40.74, -27.64, 0.02669, 61, -452.04, -450.33, 0.01528, 3, 55, 498.57, -28.48, 0.73358, 56, -12.02, -29.42, 0.25123, 61, -456.02, -478.83, 0.01519, 4, 55, 527.74, -29.65, 0.24516, 56, 17.16, -28.5, 0.73912, 44, 575.37, -6.98, 0, 61, -457.35, -508, 0.01572, 4, 55, 552.95, -33.5, 0.05912, 56, 42.58, -30.53, 0.92665, 44, 600.24, -1.35, 0, 61, -461.33, -533.19, 0.01423, 4, 55, 576.25, -51.93, 0.02606, 56, 67.14, -47.26, 0.96202, 44, 628.66, -9.99, 0, 61, -479.89, -556.39, 0.01192, 4, 55, 581.35, -14.79, 0.00133, 56, 69.58, -9.84, 0.98793, 44, 619.84, 26.45, 0, 61, -442.76, -561.69, 0.01075, 4, 55, 557.45, -6.96, 0.00069, 56, 45.18, -3.74, 0.98666, 44, 594.73, 25, 0, 61, -434.81, -537.83, 0.01265, 3, 55, 533.52, 5.51, 0.01173, 56, 20.42, 6.99, 0.97405, 61, -422.22, -513.96, 0.01422, 3, 55, 506.29, 13.98, 0.73787, 56, -7.34, 13.49, 0.24619, 61, -413.6, -486.79, 0.01594, 2, 55, 474.45, 18.45, 0.98385, 61, -408.96, -454.97, 0.01615, 2, 55, 440.19, 18.96, 0.98383, 61, -408.27, -420.71, 0.01617, 3, 56, 119.3, -59.01, 0.98903, 44, 681.96, -5.66, 0, 61, -495.6, -607.49, 0.01097, 3, 56, 188.14, -55.05, 0.99253, 44, 746.48, 18.64, 0, 61, -496.93, -676.43, 0.00747, 1, 56, 296.89, -16.76, 1, 3, 56, 179.44, -11.17, 0.99431, 44, 725.11, 57.93, 0, 61, -452.52, -671.13, 0.00569, 3, 56, 120.22, -10.41, 0.99201, 44, 668.34, 41, 0, 61, -447.21, -612.13, 0.00799, 3, 55, 311.83, 38.29, 0.98283, 44, 349.57, -22.65, 0, 61, -388.26, -292.46, 0.01717, 3, 55, 214.31, 40.8, 0.98158, 44, 257.87, -55.94, 0, 61, -385.23, -194.95, 0.01842, 2, 55, 183.13, 40.64, 0.98228, 61, -385.23, -163.77, 0.01772, 3, 55, 180.84, -47.09, 0.9817, 44, 258.84, -149.99, 0, 61, -472.94, -161.01, 0.0183, 2, 55, 149.63, -3.33, 0.97651, 61, -429.02, -130.04, 0.02349, 2, 55, 179.19, -0.2, 0.97741, 61, -426.05, -159.61, 0.02259, 2, 6, 93.42, 74.86, 0.99414, 66, -343.79, 157.51, 0.00586, 2, 6, 58.94, 70.58, 0.9916, 66, -378.27, 153.22, 0.0084, 2, 6, 74.89, 73.43, 0.99317, 66, -362.32, 156.07, 0.00683, 2, 6, 38.33, 64.69, 0.99363, 66, -398.88, 147.34, 0.00637, 4, 6, 14.35, 61.91, 0.93395, 5, 84.78, 59.7, 0.05872, 66, -422.86, 144.56, 0.00631, 63, -457.47, 168.56, 0.00102, 6, 6, -8.56, 63.03, 0.62694, 5, 62.18, 63.61, 0.31531, 34, 86.71, -96.44, 0.04513, 4, 351.12, 33.81, 0.00327, 66, -445.77, 145.68, 0.00553, 63, -462.33, 146.15, 0.00383, 6, 6, -31.07, 64.97, 0.30972, 5, 40.08, 68.27, 0.45229, 34, 68.25, -83.41, 0.19618, 4, 332.66, 46.84, 0.03189, 66, -468.27, 147.62, 0.00428, 63, -467.92, 124.26, 0.00563, 6, 6, -52.49, 69.3, 0.13897, 5, 19.34, 75.18, 0.37972, 34, 51.94, -68.86, 0.38465, 4, 316.35, 61.39, 0.08797, 66, -489.7, 151.95, 0.0025, 63, -475.7, 103.83, 0.0062, 6, 6, -74.33, 77.01, 0.05427, 5, -1.4, 85.5, 0.22112, 34, 36.97, -51.18, 0.59422, 4, 301.38, 79.07, 0.12494, 66, -511.54, 159.66, 0.0003, 63, -486.89, 83.55, 0.00515, 2, 6, 73.57, 87.32, 0.99662, 66, -363.64, 169.96, 0.00338, 2, 6, 117.97, 53.03, 0.98318, 66, -319.24, 135.68, 0.01682, 2, 6, 133.95, 35.32, 0.97638, 66, -303.25, 117.97, 0.02362, 2, 6, 148.72, 14.6, 0.97386, 66, -288.49, 97.25, 0.02614, 2, 6, 159.64, -9.18, 0.97431, 66, -277.56, 73.46, 0.02569, 2, 6, 166.86, -33.31, 0.97903, 66, -270.34, 49.34, 0.02097, 2, 6, 168.64, -53.33, 0.98526, 66, -268.57, 29.32, 0.01474, 2, 6, 84.33, 97.91, 0.99742, 66, -352.88, 180.56, 0.00258, 2, 6, 105.75, 84.92, 0.99321, 66, -331.46, 167.56, 0.00679, 2, 6, 128.21, 65.27, 0.98679, 66, -309, 147.92, 0.01321, 2, 6, 146.03, 44.33, 0.98207, 66, -291.18, 126.97, 0.01793, 2, 6, 160.62, 24.83, 0.98084, 66, -276.59, 107.48, 0.01916, 2, 6, 174.36, -2.77, 0.98319, 66, -262.84, 79.88, 0.01681, 2, 6, 181.24, -28.41, 0.9872, 66, -255.96, 54.24, 0.0128, 2, 6, 182.1, -50.37, 0.99247, 66, -255.11, 32.27, 0.00753, 4, 6, -17.18, -21.32, 0.25354, 5, 43.36, -19.07, 0.70182, 4, 301.12, -34.66, 0.02941, 63, -380.53, 123.83, 0.01522, 3, 6, 3.17, -19.17, 0.67559, 5, 63.81, -19.42, 0.31096, 63, -379.31, 144.25, 0.01345, 2, 6, 21.91, -17.79, 0.98709, 63, -377.61, 162.97, 0.01291, 2, 6, 39.85, -16.35, 0.98652, 63, -376.09, 180.9, 0.01348], "hull": 177, "edges": [4, 6, 6, 8, 12, 14, 26, 28, 32, 34, 40, 42, 48, 50, 50, 52, 56, 58, 66, 68, 84, 86, 90, 92, 96, 98, 98, 100, 122, 124, 124, 126, 142, 144, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 226, 228, 228, 230, 282, 284, 290, 292, 292, 294, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 326, 328, 332, 334, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 14, 16, 16, 18, 8, 10, 10, 12, 2, 4, 2, 0, 0, 352, 338, 340, 340, 342, 334, 336, 336, 338, 328, 330, 330, 332, 322, 324, 324, 326, 310, 312, 302, 304, 304, 306, 230, 232, 240, 238, 258, 260, 260, 262, 262, 264, 264, 266, 306, 308, 308, 310, 294, 296, 296, 298, 298, 300, 300, 302, 284, 286, 286, 288, 288, 290, 274, 276, 270, 272, 272, 274, 266, 268, 268, 270, 256, 258, 254, 256, 246, 248, 240, 242, 242, 244, 244, 246, 250, 252, 252, 254, 34, 36, 28, 30, 30, 32, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 58, 60, 60, 62, 62, 64, 64, 66, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 236, 238, 224, 226, 220, 222, 222, 224, 78, 80, 100, 102, 86, 88, 88, 90, 92, 94, 94, 96, 102, 104, 116, 118, 112, 114, 114, 116, 204, 206, 206, 208, 200, 202, 202, 204, 118, 120, 120, 122, 196, 198, 198, 200, 126, 128, 128, 130, 178, 180, 180, 182, 182, 184, 136, 138, 138, 140, 140, 142, 144, 146, 146, 148, 174, 176, 176, 178, 132, 134, 134, 136, 130, 132, 104, 106, 106, 108, 108, 110, 110, 112, 354, 226, 18, 20, 20, 22, 356, 362, 362, 360, 358, 364, 364, 360, 22, 24, 24, 26, 368, 370, 370, 318, 372, 374, 26, 376, 378, 380, 382, 384, 386, 388, 388, 390, 390, 392, 392, 394, 394, 396, 398, 356, 396, 398, 400, 402, 402, 404, 406, 376, 376, 410, 410, 380, 412, 414, 414, 28, 416, 418, 418, 368, 420, 422, 424, 420, 422, 428, 428, 430, 430, 432, 432, 258, 434, 436, 436, 438, 434, 440, 438, 442, 442, 444, 444, 446, 446, 448, 448, 450, 452, 454, 454, 58, 440, 456, 456, 458, 458, 460, 460, 462, 462, 464, 464, 44, 458, 466, 466, 468, 468, 470, 476, 478, 478, 480, 476, 482, 482, 484, 484, 486, 480, 488, 488, 490, 490, 492, 492, 494, 494, 486, 496, 498, 498, 500, 500, 502, 502, 504, 504, 506, 506, 508, 508, 510, 510, 496, 512, 514, 514, 516, 516, 518, 522, 524, 522, 46, 462, 522, 526, 520, 470, 450, 470, 452, 524, 460, 524, 528, 528, 526, 52, 54, 56, 530, 520, 530, 54, 56, 50, 532, 532, 512, 54, 534, 534, 518, 412, 536, 416, 540, 544, 546, 546, 548, 548, 550, 550, 552, 554, 556, 552, 558, 558, 554, 542, 560, 560, 562, 562, 564, 564, 566, 566, 568, 568, 570, 570, 572, 574, 576, 576, 578, 578, 580, 580, 582, 582, 584, 584, 586, 588, 590, 590, 592, 592, 594, 594, 596, 596, 598, 248, 250, 600, 602, 602, 604, 604, 606, 606, 608, 610, 612, 612, 614, 614, 616, 616, 618, 280, 282, 618, 280, 276, 278, 278, 280, 278, 620, 620, 622, 622, 624, 624, 626, 626, 628, 628, 632, 634, 610, 608, 636, 630, 638, 630, 640, 632, 644, 644, 638, 634, 646, 646, 636, 648, 642, 354, 650, 652, 654, 654, 100, 238, 598, 598, 586, 586, 572, 572, 556, 556, 76, 232, 234, 234, 236, 650, 652, 702, 704, 704, 706, 706, 708, 708, 710, 710, 712, 712, 714, 714, 716, 718, 720, 720, 722, 722, 724, 724, 726, 726, 728, 716, 730, 730, 732, 732, 734, 734, 736, 736, 738, 738, 718, 728, 740, 740, 742, 744, 742, 702, 746, 756, 754, 336, 770, 770, 752, 752, 772, 772, 774, 774, 776, 776, 778, 778, 780, 780, 782, 782, 14, 784, 786, 786, 788, 788, 790, 790, 792, 792, 794, 794, 796, 796, 798, 404, 800, 800, 802, 802, 804, 18, 806, 806, 398, 804, 806], "width": 429, "height": 1851}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.11184, 0.3203, 0.63246, 0.08282, 0.92815, 0.2591, 0.87002, 0.63858, 0.54147, 0.92258, 0.08151, 0.70958], "triangles": [3, 1, 2, 0, 1, 3, 4, 5, 0, 3, 4, 0], "vertices": [2, 6, 56.83, -57.62, 0.975, 66, -380.38, 25.03, 0.025, 2, 6, 66.97, -72.29, 0.98119, 66, -370.24, 10.36, 0.01881, 2, 6, 62.91, -82.26, 0.98567, 66, -374.3, 0.39, 0.01433, 2, 6, 50.63, -82.47, 0.98425, 66, -386.57, 0.18, 0.01575, 2, 6, 40, -73.91, 0.9803, 66, -397.21, 8.74, 0.0197, 2, 6, 44.39, -58.73, 0.975, 66, -392.82, 23.92, 0.025], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 31, "height": 32}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.16892, 0.07056, 0.04085, 0.57869, 0.3467, 0.94579, 0.88307, 0.91222, 0.95237, 0.58988, 0.67666, 0.14891], "triangles": [3, 5, 4, 2, 0, 5, 2, 5, 3, 1, 0, 2], "vertices": [2, 6, 65.58, 4.34, 0.96056, 66, -371.62, 86.98, 0.03944, 2, 6, 46.95, 7.99, 0.9604, 66, -390.26, 90.64, 0.0396, 2, 6, 36.88, -9.8, 0.96216, 66, -400.33, 72.85, 0.03784, 2, 6, 42.61, -37.12, 0.96884, 66, -394.6, 45.53, 0.03116, 2, 6, 54.32, -38.83, 0.96841, 66, -382.88, 43.82, 0.03159, 2, 6, 67.2, -22.16, 0.965, 66, -370, 60.49, 0.035], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 52, "height": 35}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 13, -7.44, -10.24, 0.98054, 66, -389.1, 5.81, 0.01946, 2, 13, -10.38, 7.52, 0.9759, 66, -392.04, 23.57, 0.0241, 2, 13, 7.37, 10.47, 0.97583, 66, -374.29, 26.52, 0.02417, 2, 13, 10.32, -7.29, 0.98066, 66, -371.34, 8.76, 0.01934], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.77791, 0.08453, 0.98271, 0.35992, 0.93446, 0.48426, 0.77284, 0.55279, 0.74481, 0.69012, 0.68162, 0.84322, 0.59804, 0.95497, 0.42912, 0.9543, 0.27028, 0.87358, 0.12933, 0.79101, 0.03568, 0.76381, 0.01946, 0.69598, 0.05838, 0.59951, 0.12406, 0.46003, 0.23478, 0.31438, 0.35245, 0.2108, 0.50107, 0.14881, 0.55108, 0.03133, 0.08916, 0.71942, 0.12376, 0.60006, 0.18972, 0.47925, 0.27406, 0.3759, 0.38111, 0.31914, 0.50978, 0.29293, 0.61034, 0.31476, 0.67522, 0.41374, 0.65354, 0.5428, 0.59948, 0.66797, 0.51189, 0.76695, 0.40052, 0.78151, 0.28158, 0.77423, 0.17345, 0.73639], "triangles": [24, 23, 16, 15, 16, 23, 24, 17, 0, 24, 16, 17, 22, 15, 23, 21, 14, 15, 25, 24, 0, 22, 21, 15, 1, 25, 0, 20, 13, 14, 21, 20, 14, 2, 25, 1, 26, 24, 25, 3, 25, 2, 26, 25, 3, 19, 12, 13, 19, 13, 20, 26, 27, 23, 26, 23, 24, 4, 26, 3, 27, 26, 4, 18, 12, 19, 11, 12, 18, 31, 19, 20, 18, 19, 31, 10, 11, 18, 27, 22, 23, 28, 22, 27, 29, 30, 21, 30, 20, 21, 30, 31, 20, 22, 29, 21, 28, 29, 22, 9, 18, 31, 10, 18, 9, 5, 27, 4, 28, 27, 5, 8, 31, 30, 9, 31, 8, 7, 29, 28, 8, 30, 29, 7, 8, 29, 6, 28, 5, 7, 28, 6], "vertices": [2, 6, 67.35, -84.01, 0.98619, 66, -369.86, -1.36, 0.01381, 2, 6, 61.46, -92.25, 0.99108, 66, -375.75, -9.61, 0.00892, 2, 6, 57.99, -91.12, 0.99086, 66, -379.22, -8.47, 0.00914, 2, 6, 55.31, -85.83, 0.98824, 66, -381.9, -3.18, 0.01176, 2, 6, 51.62, -85.45, 0.98784, 66, -385.58, -2.8, 0.01216, 2, 6, 47.34, -83.92, 0.98651, 66, -389.87, -1.27, 0.01349, 2, 6, 43.99, -81.51, 0.98469, 66, -393.22, 1.14, 0.01531, 2, 6, 43.04, -75.67, 0.98157, 66, -394.17, 6.98, 0.01843, 2, 6, 44.2, -69.84, 0.97866, 66, -393.01, 12.81, 0.02134, 2, 6, 45.51, -64.62, 0.97608, 66, -391.7, 18.02, 0.02392, 2, 6, 45.67, -61.28, 0.97443, 66, -391.54, 21.37, 0.02557, 2, 6, 47.32, -60.43, 0.97413, 66, -389.89, 22.22, 0.02587, 2, 6, 50.01, -61.36, 0.97474, 66, -387.19, 21.29, 0.02526, 2, 6, 53.97, -63.03, 0.97582, 66, -383.24, 19.61, 0.02418, 2, 6, 58.34, -66.24, 0.97758, 66, -378.87, 16.41, 0.02242, 2, 6, 61.67, -69.86, 0.97949, 66, -375.54, 12.79, 0.02051, 2, 6, 64.11, -74.73, 0.98199, 66, -373.1, 7.92, 0.01801, 2, 6, 67.41, -75.95, 0.98227, 66, -369.8, 6.7, 0.01773, 2, 6, 47.12, -62.93, 0.97524, 66, -390.09, 19.72, 0.02476, 2, 6, 50.38, -63.62, 0.97569, 66, -386.83, 19.03, 0.02431, 2, 6, 53.85, -65.38, 0.97653, 66, -383.36, 17.27, 0.02347, 2, 6, 56.99, -67.85, 0.97787, 66, -380.22, 14.79, 0.02213, 2, 6, 59.06, -71.31, 0.9798, 66, -378.15, 11.34, 0.0202, 2, 6, 60.46, -75.64, 0.98239, 66, -376.74, 7.01, 0.01761, 2, 6, 60.48, -79.21, 0.98466, 66, -376.73, 3.44, 0.01534, 2, 6, 58.31, -81.87, 0.98618, 66, -378.89, 0.78, 0.01382, 2, 6, 54.88, -81.67, 0.98606, 66, -382.33, 0.98, 0.01394, 2, 6, 51.36, -80.33, 0.98514, 66, -385.85, 2.31, 0.01486, 2, 6, 48.32, -77.73, 0.98346, 66, -388.89, 4.92, 0.01654, 2, 6, 47.31, -73.95, 0.98128, 66, -389.9, 8.7, 0.01872, 2, 6, 46.81, -69.81, 0.97897, 66, -390.4, 12.84, 0.02103, 2, 6, 47.16, -65.92, 0.97688, 66, -390.04, 16.73, 0.02312], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 20, 22, 22, 24, 24, 26, 32, 34, 30, 32, 26, 28, 28, 30, 18, 20, 14, 16, 16, 18, 10, 12, 12, 14, 6, 8, 8, 10, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 36], "width": 35, "height": 26}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 12, -7.83, -10.33, 0.96323, 66, -390.05, 60.39, 0.03677, 2, 12, -10.77, 7.43, 0.96221, 66, -392.99, 78.15, 0.03779, 2, 12, 6.98, 10.38, 0.9616, 66, -375.24, 81.1, 0.0384, 2, 12, 9.93, -7.38, 0.96382, 66, -372.29, 63.34, 0.03618], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.18591, 0.11319, 0.37181, 0.02164, 0.60539, 0.04019, 0.78736, 0.15051, 0.7919, 0.27208, 0.89283, 0.45661, 0.92282, 0.63944, 0.98118, 0.79674, 0.95896, 0.86632, 0.85034, 0.86399, 0.74511, 0.92622, 0.62984, 0.97232, 0.41039, 0.96744, 0.23354, 0.83411, 0.09633, 0.62784, 0.13495, 0.4619, 0, 0.20474, 0.88935, 0.75471, 0.86817, 0.63372, 0.83003, 0.50265, 0.74846, 0.38166, 0.62663, 0.30772, 0.46455, 0.29764, 0.34166, 0.32956, 0.24314, 0.42703, 0.27641, 0.56986, 0.35586, 0.7211, 0.4618, 0.79167, 0.59845, 0.80008, 0.71922, 0.80008, 0.82304, 0.76478], "triangles": [22, 1, 2, 21, 2, 3, 21, 3, 4, 22, 2, 21, 23, 0, 1, 23, 1, 22, 20, 21, 4, 24, 0, 23, 15, 16, 0, 19, 20, 4, 24, 15, 0, 5, 19, 4, 25, 24, 23, 18, 19, 5, 18, 5, 6, 26, 23, 22, 25, 23, 26, 17, 18, 6, 20, 29, 28, 30, 19, 18, 30, 18, 17, 27, 26, 22, 28, 27, 22, 17, 6, 7, 21, 28, 22, 28, 21, 20, 20, 19, 29, 30, 29, 19, 15, 25, 14, 25, 15, 24, 13, 25, 26, 13, 14, 25, 9, 30, 17, 8, 17, 7, 9, 17, 8, 10, 29, 30, 10, 30, 9, 12, 26, 27, 13, 26, 12, 11, 28, 29, 11, 29, 10, 27, 28, 11, 12, 27, 11], "vertices": [2, 6, 61.79, 3.94, 0.962, 66, -375.42, 86.59, 0.038, 2, 6, 65.8, -4.06, 0.962, 66, -371.4, 78.59, 0.038, 2, 6, 67.03, -14.75, 0.96284, 66, -370.17, 67.9, 0.03716, 2, 6, 65.25, -23.53, 0.96502, 66, -371.96, 59.12, 0.03498, 2, 6, 61.8, -24.31, 0.96532, 66, -375.4, 58.34, 0.03468, 2, 6, 57.29, -29.77, 0.96672, 66, -379.92, 52.88, 0.03328, 2, 6, 52.28, -32, 0.96726, 66, -384.93, 50.65, 0.03274, 2, 6, 48.22, -35.39, 0.96799, 66, -388.99, 47.25, 0.03201, 2, 6, 46.06, -34.72, 0.96779, 66, -391.15, 47.93, 0.03221, 2, 6, 45.31, -29.78, 0.96662, 66, -391.9, 52.87, 0.03338, 2, 6, 42.74, -25.3, 0.96558, 66, -394.47, 57.35, 0.03442, 2, 6, 40.55, -20.28, 0.96449, 66, -396.66, 62.36, 0.03551, 2, 6, 39.04, -10.3, 0.96285, 66, -398.17, 72.35, 0.03715, 2, 6, 41.52, -1.64, 0.96264, 66, -395.69, 81, 0.03736, 2, 6, 46.39, 5.56, 0.9622, 66, -390.82, 88.21, 0.0378, 2, 6, 51.43, 4.6, 0.962, 66, -385.78, 87.25, 0.038, 2, 6, 57.77, 11.94, 0.962, 66, -379.44, 94.59, 0.038, 2, 6, 48.73, -31.03, 0.96696, 66, -388.48, 51.62, 0.03304, 2, 6, 52.03, -29.49, 0.96666, 66, -385.17, 53.16, 0.03334, 2, 6, 55.5, -27.14, 0.9661, 66, -381.71, 55.51, 0.0339, 2, 6, 58.34, -22.86, 0.96504, 66, -378.87, 59.79, 0.03496, 2, 6, 59.54, -16.98, 0.96359, 66, -377.67, 65.67, 0.03641, 2, 6, 58.61, -9.58, 0.96215, 66, -378.6, 73.07, 0.03785, 2, 6, 56.77, -4.15, 0.962, 66, -380.44, 78.49, 0.038, 2, 6, 53.24, -0.15, 0.962, 66, -383.97, 82.5, 0.038, 2, 6, 49.4, -2.33, 0.962, 66, -387.81, 80.31, 0.038, 2, 6, 45.67, -6.66, 0.962, 66, -391.53, 75.99, 0.038, 2, 6, 44.45, -11.8, 0.96242, 66, -392.75, 70.85, 0.03758, 2, 6, 45.24, -18.04, 0.96374, 66, -391.97, 64.61, 0.03626, 2, 6, 46.15, -23.52, 0.96508, 66, -391.06, 59.13, 0.03492, 2, 6, 47.94, -28.07, 0.96622, 66, -389.26, 54.58, 0.03378], "hull": 17, "edges": [2, 4, 26, 28, 14, 16, 10, 12, 12, 14, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 28, 30, 30, 32, 4, 6, 6, 8, 8, 10, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 34, 2, 0, 0, 32], "width": 46, "height": 29}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.57546, 0, 0.68569, 0.00587, 0.77198, 0.02813, 0.8329, 0.07627, 0.88445, 0.14615, 0.91463, 0.2158, 0.93732, 0.3004, 0.95831, 0.3871, 0.97533, 0.44812, 1, 0.52613, 0.94238, 0.47794, 0.91576, 0.42665, 0.88648, 0.50114, 0.80486, 0.57285, 0.74858, 0.50237, 0.61341, 0.49161, 0.49876, 0.52312, 0.37198, 0.45329, 0.24408, 0.42527, 0.23775, 0.50432, 0.24813, 0.57583, 0.28123, 0.63317, 0.33169, 0.68496, 0.2713, 0.65001, 0.21987, 0.58864, 0.21987, 0.67803, 0.22896, 0.76313, 0.24529, 0.83827, 0.27919, 0.91766, 0.32487, 0.99607, 0.31256, 0.99753, 0.20961, 0.93908, 0.11483, 0.87267, 0.05766, 0.79695, 0.02743, 0.70621, 0.01299, 0.61121, 0.00667, 0.50982, 0.00592, 0.41433, 0.01381, 0.3174, 0.03177, 0.22142, 0.06389, 0.1459, 0.17217, 0.0914, 0.30108, 0.04302, 0.43546, 0.01329, 0.26308, 0.33125, 0.29513, 0.24461, 0.36375, 0.11706, 0.18729, 0.13925, 0.1544, 0.2376, 0.13861, 0.33181, 0.1294, 0.43637, 0.12414, 0.51868, 0.1294, 0.60564, 0.13335, 0.69468, 0.15045, 0.78267, 0.17808, 0.85825, 0.60347, 0.09256, 0.62191, 0.21518, 0.62005, 0.32645, 0.5874, 0.43714, 0.80771, 0.30801, 0.79358, 0.417, 0.78227, 0.18911, 0.73061, 0.08968, 0.41377, 0.33944, 0.44561, 0.23045, 0.47953, 0.10261], "triangles": [14, 61, 12, 13, 14, 12, 7, 11, 60, 61, 60, 11, 11, 7, 8, 10, 11, 8, 10, 8, 9, 12, 61, 11, 60, 62, 5, 60, 5, 6, 7, 60, 6, 17, 64, 59, 14, 15, 59, 61, 14, 59, 16, 17, 59, 16, 59, 15, 59, 64, 58, 59, 58, 61, 58, 57, 60, 58, 65, 57, 64, 65, 58, 61, 58, 60, 31, 55, 28, 30, 28, 29, 31, 28, 30, 54, 26, 27, 55, 54, 27, 32, 33, 54, 32, 54, 55, 55, 27, 28, 32, 55, 31, 53, 52, 25, 34, 35, 53, 53, 25, 26, 54, 53, 26, 33, 34, 53, 33, 53, 54, 19, 50, 18, 36, 37, 50, 51, 36, 50, 51, 50, 19, 24, 51, 19, 20, 24, 19, 52, 51, 24, 35, 36, 51, 35, 51, 52, 24, 20, 21, 23, 24, 21, 25, 52, 24, 23, 21, 22, 35, 52, 53, 49, 48, 44, 44, 45, 64, 37, 38, 49, 18, 49, 44, 50, 37, 49, 50, 49, 18, 17, 44, 64, 18, 44, 17, 49, 38, 48, 44, 48, 45, 64, 45, 65, 57, 62, 60, 57, 56, 63, 57, 63, 62, 62, 63, 3, 38, 39, 48, 48, 47, 45, 45, 46, 65, 45, 47, 46, 66, 43, 0, 46, 42, 43, 46, 43, 66, 65, 46, 66, 47, 42, 46, 47, 41, 42, 40, 41, 47, 48, 40, 47, 39, 40, 48, 66, 0, 56, 63, 1, 2, 56, 0, 1, 56, 1, 63, 57, 65, 66, 63, 2, 3, 57, 66, 56, 62, 3, 4, 62, 4, 5], "vertices": [3, 15, 17.93, -9.91, 0.96226, 66, -270.68, 49.64, 0.00484, 67, -203.26, 49.64, 0.0329, 3, 15, 19.86, -29.81, 0.95587, 66, -268.74, 29.73, 0.0048, 67, -201.32, 29.73, 0.03933, 3, 15, 17.37, -46.06, 0.94822, 66, -271.23, 13.49, 0.00476, 67, -203.81, 13.49, 0.04701, 5, 15, 8.25, -58.75, 0.82607, 24, 2.03, 7.49, 0.11981, 27, -76.42, -41.54, 0, 66, -280.35, 0.8, 0.00475, 67, -212.93, 0.8, 0.04937, 5, 15, -6.07, -70.58, 0.59149, 24, 20.54, 9.15, 0.35459, 27, -65.25, -26.69, 0, 66, -294.68, -11.04, 0.00475, 67, -227.26, -11.04, 0.04917, 6, 15, -20.98, -78.59, 0.36414, 24, 37.37, 7.32, 0.09927, 25, 4.87, 8.85, 0.48633, 27, -52.62, -15.43, 0, 66, -309.59, -19.05, 0.00477, 67, -242.17, -19.05, 0.04549, 5, 15, -39.51, -85.83, 0.09544, 25, 24.75, 9.41, 0.84601, 26, -10.4, 6.79, 0.00676, 66, -328.11, -26.29, 0.00476, 67, -260.69, -26.29, 0.04703, 5, 25, 45.05, 9.58, 0.01589, 26, 9.05, 12.58, 0.92734, 27, -19.41, 7.19, 0, 66, -347.16, -33.3, 0.00474, 67, -279.74, -33.3, 0.05203, 3, 26, 22.71, 17.06, 0.93708, 66, -360.5, -38.64, 0.00471, 67, -293.08, -38.64, 0.05821, 3, 26, 40.11, 23.32, 0.92748, 66, -377.47, -45.98, 0.00466, 67, -310.05, -45.98, 0.06786, 3, 26, 30.13, 11.82, 0.94325, 66, -368.24, -33.88, 0.00474, 67, -300.82, -33.88, 0.05201, 3, 26, 18.88, 5.84, 0.95186, 66, -357.39, -27.19, 0.00478, 67, -289.97, -27.19, 0.04336, 3, 27, 9.82, 5.42, 0.95558, 66, -375.16, -24.77, 0.0048, 67, -307.75, -24.77, 0.03962, 3, 27, 30.76, -1.76, 0.96481, 66, -393.85, -12.9, 0.00485, 67, -326.44, -12.9, 0.03034, 7, 22, 60.78, 30.68, 0.03895, 23, 10.83, 36.99, 0.22236, 25, 64.34, -32.51, 0.00327, 26, 39.26, -22.51, 0.05692, 27, 19.79, -17.46, 0.65259, 66, -379.53, -0.2, 0.00489, 67, -312.11, -0.2, 0.02101, 7, 22, 60.73, 6.09, 0.00389, 23, 19.56, 14, 0.88791, 25, 57.53, -56.14, 6e-05, 26, 39.27, -47.1, 0.015, 27, 27.04, -40.96, 0.08023, 66, -381.09, 24.35, 0.00496, 67, -313.68, 24.35, 0.00795, 5, 16, 62.87, 69.92, 5e-05, 22, 69.98, -13.84, 1e-05, 23, 35.33, -1.32, 0.99366, 66, -391.64, 43.63, 0.00499, 67, -324.22, 43.63, 0.00128, 8, 15, -90.95, 9.35, 0.0069, 16, 49.57, 45.27, 0.3105, 17, 6.53, 44.8, 0.00934, 22, 56.25, -38.26, 0.06297, 23, 31.22, -29.03, 0.60526, 26, 34.91, -91.46, 0, 27, 35.92, -84.64, 4e-05, 66, -379.55, 68.9, 0.005, 7, 15, -88.38, 33.25, 0.00367, 16, 45.86, 21.53, 0.59114, 17, -0.53, 21.83, 0.28437, 23, 35.81, -52.61, 0.11582, 26, 30.84, -115.15, 0, 27, 39, -108.47, 1e-05, 66, -376.99, 92.79, 0.005, 6, 16, 64.05, 22.5, 0.05253, 17, 17.62, 20.2, 0.93437, 23, 52.58, -45.51, 0.0081, 26, 49.04, -114.45, 0, 27, 56.19, -102.45, 0, 66, -395.11, 90.94, 0.005, 5, 17, 34.11, 21.64, 0.96985, 18, -9.31, 23.03, 0.02508, 23, 66.46, -36.47, 7e-05, 27, 70.61, -94.32, 0, 66, -411.03, 86.4, 0.005, 4, 17, 47.45, 27.28, 0.995, 23, 75.57, -25.22, 0, 27, 80.43, -83.67, 0, 66, -423.06, 78.33, 0.005, 5, 17, 59.6, 36.09, 0.99497, 18, 17.83, 34.09, 3e-05, 23, 82.14, -11.72, 0, 27, 87.85, -70.62, 0, 66, -433.32, 67.37, 0.005, 2, 17, 51.27, 25.38, 0.995, 66, -427.18, 79.47, 0.005, 4, 17, 36.92, 16.45, 0.91483, 18, -7.19, 17.52, 0.08017, 27, 75.32, -97.89, 0, 66, -414.77, 90.96, 0.005, 4, 17, 57.47, 15.9, 0.05376, 18, 13.12, 14.34, 0.94109, 66, -435.06, 87.59, 0.005, 67, -367.64, 87.59, 0.00015, 4, 18, 32.71, 12.93, 0.53958, 19, 1.2, 12.99, 0.45528, 66, -454.1, 82.76, 0.005, 67, -386.68, 82.76, 0.00014, 4, 19, 18.35, 9.36, 0.96668, 20, -6.62, 11.51, 0.02832, 26, 125.32, -105.34, 0, 66, -470.66, 77.02, 0.005, 3, 19, 37.58, 8.33, 0.01311, 20, 11.67, 5.46, 0.98189, 66, -487.67, 67.98, 0.005, 2, 20, 31.05, 1.25, 0.995, 66, -504.1, 56.87, 0.005, 2, 20, 29.99, -0.73, 0.995, 66, -504.8, 59.01, 0.005, 4, 19, 37.52, -5.19, 0.02939, 20, 8.05, -7.57, 0.96251, 66, -494.59, 79.59, 0.00498, 67, -427.17, 79.59, 0.00312, 4, 18, 54.41, -11.37, 0.00699, 19, 16.99, -15.5, 0.97862, 66, -482.33, 99.02, 0.00495, 67, -414.91, 99.02, 0.00944, 4, 18, 35.6, -18.9, 0.54667, 19, -3.01, -18.7, 0.43373, 66, -466.85, 112.08, 0.00493, 67, -399.43, 112.08, 0.01468, 5, 17, 63.02, -19.09, 0.04568, 18, 14.13, -21.08, 0.92891, 19, -24.43, -16.09, 0.00302, 66, -447.15, 120.89, 0.00491, 67, -379.74, 120.89, 0.01749, 4, 17, 41.11, -21.12, 0.70762, 18, -7.86, -20.28, 0.26871, 66, -426.03, 127.05, 0.00491, 67, -358.61, 127.05, 0.01876, 4, 16, 70.15, -18.9, 0.02624, 17, 17.77, -21.65, 0.94927, 66, -403.21, 132, 0.0049, 67, -335.79, 132, 0.01958, 5, 15, -92.96, 76.18, 0.11115, 16, 48.35, -21.58, 0.53472, 17, -4.19, -21.2, 0.32884, 66, -381.57, 135.73, 0.0049, 67, -314.15, 135.73, 0.02039, 5, 15, -70.74, 78.43, 0.42129, 16, 26.04, -22.74, 0.55049, 17, -26.44, -19.18, 0.00144, 66, -359.34, 137.97, 0.00489, 67, -291.92, 137.97, 0.02188, 4, 15, -48.43, 78.83, 0.72831, 16, 3.74, -22.07, 0.24102, 66, -337.03, 138.38, 0.00487, 67, -269.61, 138.38, 0.02579, 2, 15, -30.34, 75.94, 0.96886, 67, -251.52, 135.49, 0.03114, 3, 15, -14.76, 58.66, 0.97231, 66, -303.37, 118.21, 0.002, 67, -235.95, 118.21, 0.02569, 3, 15, 0.04, 37.47, 0.9719, 66, -288.57, 97.01, 0.00488, 67, -221.15, 97.01, 0.02322, 3, 15, 10.76, 14.59, 0.97035, 66, -277.84, 74.13, 0.00488, 67, -210.42, 74.13, 0.02478, 8, 15, -66.49, 33.4, 0.23226, 16, 23.98, 22.44, 0.65652, 17, -22.06, 25.84, 0.0062, 21, 46.77, -66.83, 0.00368, 23, 14.93, -59.21, 0.09633, 26, 8.98, -113.91, 0, 27, 17.74, -113.72, 1e-05, 66, -355.09, 92.94, 0.005, 7, 15, -45.88, 30.94, 0.49062, 16, 3.51, 25.89, 0.45341, 21, 28.36, -57.23, 0.01608, 23, -5.49, -62.93, 0.0349, 26, -11.44, -110.15, 0, 27, -2.87, -116.13, 0, 66, -334.48, 90.48, 0.005, 8, 15, -14.91, 23.49, 0.92434, 16, -27.06, 34.83, 0.0605, 21, 2.03, -39.3, 0.00033, 23, -37.28, -64.94, 4e-05, 26, -41.88, -100.75, 0, 27, -34.73, -116.11, 0, 66, -303.51, 83.03, 0.00495, 67, -236.09, 83.03, 0.00984, 8, 15, -25.17, 54.16, 0.8899, 16, -18.29, 3.7, 0.08897, 21, 0.77, -71.62, 4e-05, 23, -18.43, -91.23, 4e-05, 26, -33.58, -132.01, 0, 27, -17.6, -143.55, 0, 66, -313.77, 113.7, 0.00492, 67, -246.36, 113.7, 0.01614, 8, 15, -48.46, 56.33, 0.59658, 16, 4.86, 0.4, 0.39122, 21, 21.79, -81.89, 1e-05, 23, 4.46, -86.44, 5e-05, 26, -10.47, -135.65, 0, 27, 5.56, -140.23, 0, 66, -337.06, 115.87, 0.00496, 67, -269.65, 115.87, 0.00717, 8, 15, -70.3, 55.6, 0.34427, 16, 26.72, 0.08, 0.64393, 21, 42.47, -88.94, 0, 23, 25.12, -79.3, 5e-05, 26, 11.38, -136.31, 0, 27, 26.63, -134.42, 0, 66, -358.91, 115.14, 0.00497, 67, -291.49, 115.14, 0.00679, 8, 15, -94.3, 53.31, 0.02288, 16, 50.8, 1.21, 0.28936, 17, 1.47, 1.01, 0.67515, 23, 47.38, -70.04, 0.00095, 26, 35.47, -135.54, 0, 27, 49.43, -126.6, 0, 66, -382.91, 112.85, 0.00497, 67, -315.49, 112.85, 0.0067, 3, 17, 20.37, -0.45, 0.98809, 66, -401.74, 110.69, 0.00497, 67, -334.32, 110.69, 0.00695, 3, 17, 40.39, -0.02, 0.98843, 66, -421.31, 106.47, 0.00497, 67, -353.9, 106.47, 0.0066, 3, 18, 14.48, -1.73, 0.98839, 66, -441.4, 102.42, 0.00497, 67, -373.98, 102.42, 0.00664, 5, 18, 34.95, -1.8, 0.44992, 19, 0.13, -1.88, 0.53917, 26, 114.33, -123.71, 0, 66, -460.86, 96.05, 0.00497, 67, -393.44, 96.05, 0.00594, 3, 19, 18.13, -3.64, 0.99049, 66, -477.19, 88.27, 0.00498, 67, -409.77, 88.27, 0.00454, 5, 15, -2.24, -18.39, 0.79932, 21, 5.01, 4.35, 0.16706, 24, -12.13, -31.73, 0.00842, 66, -290.85, 41.15, 0.0049, 67, -223.43, 41.15, 0.02029, 8, 15, -29.52, -26.31, 0.33141, 21, 33.33, 2.1, 0.36327, 22, -2.69, 1.37, 0.28286, 24, 14.84, -40.63, 0.00646, 25, -4.74, -43.25, 0.00278, 26, -24.14, -51.98, 3e-05, 66, -318.12, 33.24, 0.00496, 67, -250.7, 33.24, 0.00824, 8, 15, -54.82, -30.16, 0.05217, 22, 22.81, 3.55, 0.9098, 24, 37.89, -51.76, 0.00091, 25, 20.38, -48.16, 0.01635, 26, 1.36, -49.74, 0.00718, 27, -8.42, -54.63, 0.00217, 66, -343.42, 29.38, 0.00497, 67, -276.01, 29.38, 0.00644, 6, 23, 10.46, 4.19, 0.96751, 25, 44.36, -58.53, 0.00044, 26, 27.28, -53.05, 0.00475, 27, 17.33, -50.17, 0.01818, 66, -369.51, 31.04, 0.00498, 67, -302.09, 31.04, 0.00414, 10, 15, -45.07, -62.98, 0.1064, 21, 60.86, 30.88, 0.00279, 22, 15.24, 36.94, 0.18077, 23, -33.94, 26.57, 0.00506, 24, 48.41, -19.18, 0.01225, 25, 22.28, -13.99, 0.56376, 26, -6.29, -16.37, 0.09911, 27, -25.55, -25, 0.00448, 66, -333.68, -3.43, 0.0049, 67, -266.26, -3.43, 0.02049, 7, 22, 40.44, 36.86, 0.12736, 23, -10.37, 35.49, 0.10237, 25, 46.48, -20.99, 0.06643, 26, 18.9, -16.39, 0.41404, 27, -1.47, -17.6, 0.26392, 66, -358.83, -5.02, 0.0049, 67, -291.41, -5.02, 0.02098, 7, 15, -18.85, -53.96, 0.46917, 21, 33.14, 31.73, 0.06029, 22, -11.52, 29.66, 0.04239, 24, 21.68, -11.79, 0.3255, 25, -5.45, -13.62, 0.07486, 66, -307.45, 5.59, 0.00489, 67, -240.03, 5.59, 0.02291, 5, 15, 2.18, -40.99, 0.74116, 21, 8.88, 27.05, 0.07194, 24, -3, -10.6, 0.15208, 66, -286.42, 18.56, 0.00485, 67, -219.01, 18.56, 0.02997, 9, 15, -63.88, 6.18, 0.05236, 16, 22.69, 49.75, 0.32678, 17, -19.45, 53.05, 0.00014, 21, 53.96, -40.45, 0.01744, 22, 29.45, -33.31, 0.28099, 23, 4.42, -33.97, 0.31728, 26, 8.1, -86.58, 0, 27, 8.86, -87.87, 1e-05, 66, -352.48, 65.72, 0.005, 8, 15, -38.21, 4.6, 0.48505, 16, -2.88, 52.57, 0.1427, 21, 30.51, -29.88, 0.18095, 22, 3.94, -30.04, 0.15595, 23, -20.58, -40.03, 0.03035, 26, -17.43, -83.38, 0, 27, -16.47, -92.31, 0, 66, -326.81, 64.14, 0.005, 4, 15, -8.2, 3.36, 0.89371, 21, 2.88, -18.1, 0.08993, 66, -296.8, 62.9, 0.00494, 67, -229.38, 62.9, 0.01141], "hull": 44, "edges": [2, 4, 4, 6, 26, 28, 28, 30, 30, 32, 36, 38, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 16, 18, 32, 34, 34, 36, 38, 40, 40, 42, 42, 44, 68, 70, 70, 72, 48, 46, 46, 44, 48, 50, 50, 52, 72, 74, 74, 76, 52, 54, 54, 56, 76, 78, 78, 80, 80, 82, 82, 84, 2, 0, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 24, 26, 36, 88, 88, 90, 90, 92, 84, 86, 92, 86, 86, 0], "width": 181, "height": 230}}, "hair_L": {"hair_L": {"type": "mesh", "uvs": [0.59161, 0.02605, 0.81424, 0.00143, 0.93444, 0.09548, 0.98571, 0.20433, 0.99193, 0.3293, 0.9785, 0.44926, 0.93311, 0.57411, 0.90986, 0.68442, 0.81877, 0.79427, 0.55257, 0.87748, 0.31991, 0.95208, 0.04348, 1, 0.02174, 0.92231, 0, 0.83615, 0.12957, 0.74523, 0.19035, 0.65598, 0.24364, 0.55308, 0.26357, 0.45235, 0.28961, 0.35352, 0.28478, 0.25872, 0.27318, 0.1592, 0.22717, 0.06636, 0.2589, 0.84716, 0.43532, 0.75756, 0.53993, 0.67248, 0.6296, 0.56708, 0.70432, 0.45152, 0.73516, 0.33388, 0.72673, 0.22183, 0.67348, 0.11719], "triangles": [11, 12, 10, 12, 22, 10, 10, 22, 9, 12, 13, 22, 22, 23, 9, 9, 23, 8, 13, 14, 22, 22, 14, 23, 23, 24, 8, 14, 15, 23, 8, 24, 7, 23, 15, 24, 24, 25, 7, 7, 25, 6, 15, 16, 24, 24, 16, 25, 25, 26, 6, 16, 17, 25, 6, 26, 5, 25, 17, 26, 17, 18, 26, 26, 27, 5, 26, 18, 27, 5, 27, 4, 18, 19, 27, 27, 28, 4, 20, 29, 28, 19, 28, 27, 28, 3, 4, 28, 29, 3, 29, 2, 3, 19, 20, 28, 29, 1, 2, 20, 21, 29, 21, 0, 29, 29, 0, 1], "vertices": [2, 28, 9.31, -0.05, 0.99709, 67, -231.73, 17.88, 0.00291, 1, 28, 16.76, -14.39, 1, 2, 28, -0.72, -25.7, 0.50999, 29, -2.9, 20.01, 0.49001, 3, 28, -21.94, -32.81, 0.1039, 29, 19.35, 22.43, 0.87126, 30, -21.01, 20.96, 0.02484, 2, 29, 44.7, 21.57, 0.29734, 30, 4.34, 21.95, 0.70266, 3, 29, 68.98, 19.42, 6e-05, 30, 28.71, 21.56, 0.97905, 31, -19.33, 18.14, 0.02089, 2, 30, 54.12, 19, 0.19274, 31, 6.09, 20.58, 0.80726, 2, 31, 28.3, 23.87, 0.90518, 32, -16.49, 21.24, 0.09482, 3, 31, 51.43, 22.57, 0.24163, 32, 6.49, 24.15, 0.7577, 67, -383.01, -23.12, 0.00067, 3, 32, 29.21, 13.83, 0.78476, 33, -4.68, 13.19, 0.21153, 67, -402.68, -7.76, 0.00371, 2, 33, 16.8, 8.15, 0.99393, 67, -420.25, 5.59, 0.00607, 2, 33, 35.43, -2.4, 0.98781, 67, -432.97, 22.82, 0.01219, 3, 32, 51.88, -16.35, 0.01061, 33, 23.1, -12.35, 0.97506, 67, -417.65, 26.88, 0.01433, 3, 32, 36.36, -24.55, 0.30086, 33, 9.34, -23.24, 0.6825, 67, -400.65, 31.22, 0.01664, 4, 31, 52.03, -26.01, 0.10066, 32, 15.88, -23.52, 0.77063, 33, -10.99, -25.96, 0.11403, 67, -380.97, 25.43, 0.01468, 5, 30, 71.87, -31.87, 0.00167, 31, 33.43, -25.84, 0.63668, 32, -2.44, -26.73, 0.34669, 33, -28.42, -32.45, 0.0008, 67, -362.41, 24.26, 0.01416, 4, 30, 50.91, -28.66, 0.17703, 31, 12.24, -26.79, 0.78856, 32, -23.11, -31.49, 0.02102, 67, -341.2, 24.05, 0.01338, 4, 29, 67.11, -29.88, 0.00265, 30, 30.43, -27.74, 0.72135, 31, -8.02, -29.88, 0.26268, 67, -320.81, 26.04, 0.01332, 5, 28, -59.68, 9.62, 0.08501, 29, 47.16, -27.07, 0.17466, 30, 10.34, -26.39, 0.70818, 31, -27.99, -32.48, 0.01911, 67, -300.72, 27.55, 0.01304, 4, 28, -40.75, 13.1, 0.31406, 29, 27.93, -26.43, 0.51482, 30, -8.9, -27.15, 0.15791, 67, -281.79, 31.03, 0.01321, 4, 28, -20.95, 17.2, 0.68327, 29, 7.71, -26.21, 0.30134, 30, -29.08, -28.4, 0.00242, 67, -261.99, 35.13, 0.01297, 2, 28, -2.87, 23.41, 0.98679, 67, -243.92, 41.35, 0.01321, 3, 32, 31.45, -7.23, 0.40424, 33, 1.35, -7.11, 0.5864, 67, -399.92, 13.23, 0.00936, 4, 31, 49.89, -4.87, 0.0022, 32, 9.95, -3.12, 0.99078, 33, -20.53, -6.98, 0.0008, 67, -379.99, 4.2, 0.00622, 3, 31, 31.47, -1.57, 0.98868, 32, -8.77, -3.21, 0.00614, 67, -361.77, -0.09, 0.00518, 3, 30, 53.16, -1.97, 0.00066, 31, 9.24, -0.17, 0.99531, 67, -339.65, -2.69, 0.00404, 2, 30, 29.59, 2.66, 0.99701, 67, -315.66, -3.93, 0.00299, 4, 28, -50.71, -20.06, 0.0285, 29, 44.74, 3.83, 0.05244, 30, 5.67, 4.26, 0.9165, 67, -291.75, -2.12, 0.00256, 4, 28, -28.37, -15.76, 0.20154, 29, 21.99, 4.4, 0.79418, 30, -17.06, 3.17, 0.00201, 67, -269.41, 2.18, 0.00227, 3, 28, -8.01, -8.66, 0.58726, 29, 0.59, 1.81, 0.41033, 67, -249.06, 9.28, 0.00241], "hull": 22, "edges": [14, 16, 30, 32, 32, 34, 34, 36, 36, 38, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 10, 12, 12, 14, 6, 8, 8, 10, 38, 40, 40, 42, 4, 6, 2, 4, 2, 0, 0, 42, 22, 24, 24, 26], "width": 69, "height": 203}}, "head": {"head": {"type": "mesh", "uvs": [0.31945, 1e-05, 0.42873, 0.01066, 0.53424, 0.02696, 0.63975, 0.04326, 0.76675, 0.06288, 0.86145, 0.0775, 0.95616, 0.09213, 0.97434, 0.15723, 0.9893, 0.25326, 0.99019, 0.38584, 0.98902, 0.40898, 0.96525, 0.49294, 0.95681, 0.54244, 0.94787, 0.65188, 0.90475, 0.75037, 0.76988, 0.90745, 0.72388, 0.97093, 0.68949, 0.9898, 0.618, 0.99082, 0.56159, 0.97066, 0.41457, 0.89515, 0.25774, 0.78324, 0.2207, 0.736, 0.18316, 0.6321, 0.15225, 0.63854, 0.15437, 0.64849, 0.16191, 0.65764, 0.17846, 0.67773, 0.19664, 0.73847, 0.19042, 0.79621, 0.15339, 0.82125, 0.10733, 0.8198, 0.07227, 0.78832, 0.0684, 0.72538, 0.0865, 0.68117, 0.11077, 0.65704, 0.12178, 0.6461, 0.1014, 0.62955, 0.03431, 0.53676, 0.00489, 0.46408, 0.01113, 0.39335, 0.03478, 0.35502, 0.02976, 0.26842, 0.04081, 0.17457, 0.17617, 0.06957, 0.70013, 0.41685, 0.56285, 0.34791, 0.52847, 0.33768, 0.44601, 0.33687, 0.45382, 0.31858, 0.5258, 0.30868, 0.57016, 0.31478, 0.71487, 0.3841, 0.63038, 0.38483, 0.65348, 0.35268, 0.61377, 0.333, 0.68334, 0.36863, 0.66525, 0.40157, 0.59494, 0.36711, 0.7197, 0.41125, 0.84858, 0.44022, 0.85081, 0.41917, 0.93271, 0.38345, 0.96391, 0.38003, 0.96503, 0.40841, 0.94107, 0.41379, 0.86641, 0.44022, 0.89202, 0.43288, 0.87616, 0.4067, 0.90165, 0.39361, 0.91655, 0.42334, 0.80667, 0.53432, 0.81787, 0.51262, 0.83447, 0.49397, 0.85764, 0.47972, 0.88313, 0.4726, 0.91132, 0.47124, 0.92792, 0.47735, 0.93912, 0.49566, 0.93371, 0.51635, 0.91943, 0.53365, 0.89548, 0.54619, 0.87039, 0.54552, 0.84567, 0.54179, 0.82444, 0.53636, 0.815, 0.49894, 0.82951, 0.48385, 0.855, 0.46723, 0.88241, 0.45773, 0.91009, 0.45672, 0.93635, 0.4618, 0.95025, 0.47096, 0.62743, 0.49767, 0.6255, 0.4797, 0.61199, 0.45494, 0.59036, 0.43662, 0.5614, 0.42408, 0.5251, 0.42136, 0.49306, 0.4234, 0.46409, 0.43086, 0.44015, 0.44714, 0.45444, 0.47224, 0.47645, 0.49157, 0.50503, 0.50683, 0.54287, 0.50988, 0.57916, 0.50785, 0.60774, 0.5031, 0.41335, 0.43428, 0.45169, 0.41512, 0.48801, 0.40383, 0.53054, 0.40195, 0.56863, 0.40719, 0.60327, 0.42167, 0.6274, 0.44371, 0.64073, 0.47009, 0.64904, 0.49388, 0.64653, 0.51394, 0.61169, 0.51485, 0.55994, 0.53061, 0.49268, 0.52879, 0.4368, 0.50576, 0.40851, 0.47123, 0.80234, 0.54029, 0.83582, 0.55034, 0.88549, 0.56882, 0.92275, 0.56609, 0.69278, 0.52272, 0.75238, 0.5032, 0.80273, 0.5124, 0.68656, 0.6339, 0.6623, 0.6558, 0.6623, 0.68516, 0.68134, 0.70869, 0.72384, 0.70908, 0.75232, 0.72668, 0.78464, 0.71735, 0.79634, 0.61605, 0.68967, 0.57831, 0.585, 0.78411, 0.61517, 0.78581, 0.67387, 0.77203, 0.7098, 0.76063, 0.73168, 0.75923, 0.75103, 0.76941, 0.77145, 0.76176, 0.78556, 0.76994, 0.7886, 0.78453, 0.78715, 0.79484, 0.77535, 0.81311, 0.7682, 0.83611, 0.75112, 0.85748, 0.72633, 0.86607, 0.68525, 0.8656, 0.65287, 0.8556, 0.6203, 0.83793, 0.57092, 0.79931, 0.793, 0.80112, 0.77771, 0.80353, 0.7637, 0.80084, 0.75759, 0.79427, 0.74319, 0.79685, 0.72049, 0.79321, 0.69737, 0.79252, 0.66641, 0.79459, 0.64423, 0.79583, 0.61585, 0.79631, 0.58254, 0.79527, 0.56687, 0.7877, 0.66827, 0.80648, 0.69241, 0.80986, 0.72294, 0.81162, 0.74447, 0.8081, 0.64647, 0.78032, 0.59597, 0.81925, 0.78978, 0.80756, 0.80252, 0.78767, 0.80995, 0.80104, 0.80111, 0.81036, 0.58545, 0.76529, 0.54953, 0.77524, 0.53838, 0.80107, 0.65938, 0.82403, 0.69413, 0.83161, 0.72826, 0.83204, 0.75148, 0.82442, 0.63413, 0.81503, 0.66117, 0.87883, 0.72122, 0.87947, 0.63947, 0.93284, 0.70531, 0.9303, 0.58048, 0.91165, 0.48422, 0.8453, 0.38303, 0.67717, 0.37997, 0.56668, 0.41835, 0.77008, 0.54178, 0.59744, 0.53576, 0.69044, 0.15858, 0.42766, 0.16123, 0.53662, 0.27624, 0.64759, 0.29163, 0.5353, 0.26085, 0.46147, 0.27269, 0.37933, 0.29755, 0.29304, 0.33306, 0.19842, 0.3816, 0.10252, 0.1233, 0.3757, 0.07758, 0.33919, 0.14007, 0.27613, 0.15393, 0.17371, 0.8171, 0.47138, 0.73917, 0.46309, 0.68582, 0.46481, 0.86787, 0.26328, 0.74164, 0.23917, 0.58797, 0.20061, 0.44308, 0.19193, 0.86897, 0.16492, 0.75152, 0.13792, 0.60992, 0.11575, 0.48369, 0.10707, 0.38929, 0.30527, 0.35636, 0.39301, 0.35417, 0.47735, 0.89803, 0.63257, 0.86057, 0.72591, 0.13593, 0.6817, 0.13347, 0.72971, 0.13199, 0.78247, 0.13583, 0.6599, 0.13804, 0.64711, 0.13782, 0.63664], "triangles": [10, 91, 64, 90, 65, 64, 90, 70, 65, 89, 67, 70, 68, 69, 70, 70, 69, 65, 69, 62, 65, 65, 62, 64, 10, 64, 9, 62, 63, 64, 64, 63, 9, 62, 69, 213, 63, 8, 9, 63, 62, 8, 60, 210, 61, 87, 60, 66, 59, 61, 210, 88, 66, 67, 60, 61, 66, 67, 66, 68, 66, 61, 68, 67, 68, 70, 213, 68, 61, 69, 68, 213, 52, 213, 61, 45, 59, 211, 212, 57, 45, 57, 112, 53, 59, 45, 52, 45, 57, 52, 61, 59, 52, 57, 53, 56, 57, 56, 52, 56, 53, 54, 52, 56, 214, 56, 54, 214, 112, 58, 53, 58, 111, 46, 53, 58, 54, 58, 55, 54, 58, 46, 55, 54, 55, 214, 46, 51, 55, 46, 47, 51, 55, 51, 215, 109, 48, 47, 110, 47, 46, 48, 49, 47, 49, 50, 47, 47, 50, 51, 48, 221, 49, 50, 49, 216, 51, 50, 215, 31, 228, 30, 30, 228, 29, 31, 32, 228, 29, 228, 28, 28, 228, 227, 32, 33, 228, 28, 227, 27, 227, 226, 27, 228, 33, 227, 33, 34, 227, 227, 34, 226, 226, 35, 229, 226, 34, 35, 226, 26, 27, 229, 26, 226, 18, 188, 17, 18, 19, 188, 17, 189, 16, 17, 188, 189, 16, 189, 15, 19, 20, 190, 19, 190, 188, 190, 20, 191, 188, 186, 189, 188, 190, 186, 189, 187, 15, 189, 186, 187, 190, 191, 173, 173, 191, 180, 186, 154, 153, 186, 190, 154, 190, 173, 154, 173, 180, 155, 15, 176, 14, 15, 177, 176, 177, 149, 148, 148, 174, 177, 15, 187, 150, 187, 151, 150, 150, 149, 15, 177, 15, 149, 20, 194, 191, 20, 21, 194, 186, 152, 187, 187, 152, 151, 186, 153, 152, 152, 182, 151, 151, 183, 150, 151, 182, 183, 152, 153, 182, 150, 184, 149, 150, 183, 184, 153, 154, 181, 153, 181, 182, 181, 154, 185, 191, 194, 180, 154, 173, 185, 149, 184, 148, 182, 170, 183, 183, 171, 184, 183, 170, 171, 182, 181, 169, 181, 168, 169, 182, 169, 170, 184, 158, 148, 184, 171, 158, 181, 185, 168, 155, 166, 173, 173, 165, 185, 173, 166, 165, 185, 164, 168, 185, 165, 164, 148, 157, 174, 148, 158, 157, 169, 162, 170, 162, 161, 170, 170, 160, 171, 170, 161, 160, 174, 156, 177, 177, 156, 176, 162, 169, 163, 171, 159, 158, 171, 160, 159, 174, 157, 156, 164, 163, 168, 169, 168, 163, 157, 147, 156, 157, 158, 147, 176, 156, 175, 180, 167, 155, 180, 179, 167, 180, 194, 179, 156, 147, 175, 176, 225, 14, 176, 175, 225, 158, 159, 147, 155, 167, 166, 159, 160, 143, 166, 138, 165, 138, 139, 165, 165, 139, 164, 164, 172, 163, 164, 139, 172, 166, 167, 138, 147, 146, 175, 147, 159, 146, 163, 140, 162, 163, 172, 140, 160, 161, 143, 159, 145, 146, 159, 144, 145, 159, 143, 144, 162, 141, 161, 161, 142, 143, 161, 141, 142, 162, 140, 141, 138, 167, 178, 225, 175, 145, 172, 139, 178, 175, 146, 145, 139, 138, 178, 178, 167, 179, 21, 192, 194, 21, 199, 192, 21, 22, 199, 140, 172, 132, 178, 179, 196, 141, 140, 132, 179, 194, 196, 194, 192, 196, 144, 135, 145, 145, 135, 225, 144, 143, 134, 178, 196, 131, 132, 172, 178, 178, 131, 132, 130, 131, 196, 143, 142, 134, 144, 134, 135, 141, 133, 142, 141, 132, 133, 142, 133, 134, 14, 225, 13, 199, 22, 23, 134, 133, 135, 135, 136, 225, 225, 224, 13, 225, 136, 224, 136, 124, 224, 136, 123, 124, 135, 133, 136, 133, 131, 129, 133, 129, 136, 133, 132, 131, 129, 131, 130, 130, 196, 195, 117, 130, 195, 117, 195, 118, 196, 192, 195, 137, 130, 117, 26, 229, 25, 25, 229, 230, 199, 193, 192, 195, 193, 119, 119, 193, 120, 193, 195, 192, 35, 36, 229, 229, 36, 230, 129, 130, 137, 224, 125, 13, 13, 125, 12, 230, 24, 25, 199, 200, 193, 199, 23, 200, 36, 231, 230, 230, 231, 24, 36, 37, 231, 23, 24, 198, 198, 24, 231, 129, 137, 136, 116, 137, 117, 224, 124, 125, 231, 37, 198, 23, 198, 200, 198, 38, 197, 198, 37, 38, 136, 137, 122, 127, 122, 137, 136, 122, 123, 127, 137, 126, 195, 119, 118, 137, 116, 126, 124, 123, 82, 123, 83, 82, 124, 81, 125, 124, 82, 81, 200, 223, 193, 120, 223, 121, 120, 193, 223, 81, 80, 125, 125, 80, 12, 123, 122, 84, 122, 71, 84, 123, 84, 83, 80, 81, 75, 82, 73, 74, 75, 81, 82, 80, 79, 12, 12, 79, 11, 11, 79, 78, 82, 74, 75, 80, 75, 79, 83, 72, 73, 82, 83, 73, 122, 128, 71, 122, 127, 128, 197, 39, 206, 41, 206, 40, 41, 207, 206, 39, 197, 38, 40, 206, 39, 198, 201, 200, 198, 197, 201, 71, 72, 84, 83, 84, 72, 200, 201, 223, 71, 128, 72, 78, 79, 76, 76, 79, 75, 118, 119, 104, 119, 103, 104, 117, 105, 106, 117, 118, 105, 118, 104, 105, 120, 102, 119, 119, 102, 103, 116, 115, 126, 115, 212, 126, 127, 212, 211, 127, 126, 212, 78, 76, 77, 117, 92, 116, 117, 106, 92, 116, 92, 115, 128, 85, 72, 72, 85, 73, 85, 128, 210, 103, 97, 104, 105, 104, 95, 104, 97, 96, 106, 105, 94, 95, 104, 96, 103, 102, 97, 97, 102, 98, 120, 101, 102, 120, 121, 101, 210, 128, 127, 106, 93, 92, 106, 94, 93, 94, 105, 95, 85, 86, 73, 85, 210, 86, 210, 127, 211, 92, 93, 115, 78, 91, 11, 78, 77, 91, 73, 86, 74, 93, 114, 115, 115, 114, 212, 11, 91, 10, 102, 101, 98, 98, 101, 99, 86, 87, 74, 86, 210, 87, 74, 87, 75, 93, 94, 114, 107, 121, 223, 77, 90, 91, 77, 76, 90, 87, 88, 75, 75, 89, 76, 75, 88, 89, 121, 100, 101, 101, 100, 99, 87, 210, 60, 76, 89, 90, 223, 201, 222, 121, 107, 100, 107, 223, 222, 94, 113, 114, 114, 113, 212, 87, 66, 88, 212, 45, 211, 113, 57, 212, 64, 91, 90, 89, 70, 90, 201, 202, 222, 201, 197, 202, 88, 67, 89, 94, 95, 113, 100, 108, 99, 100, 107, 108, 95, 112, 113, 57, 113, 112, 59, 210, 211, 112, 95, 111, 107, 222, 108, 98, 99, 109, 197, 206, 202, 97, 110, 96, 95, 96, 111, 96, 110, 111, 99, 108, 109, 98, 109, 97, 111, 58, 112, 97, 109, 110, 109, 108, 48, 108, 222, 48, 109, 47, 110, 111, 110, 46, 202, 203, 222, 222, 221, 48, 222, 203, 221, 52, 214, 213, 8, 62, 213, 8, 217, 7, 217, 8, 213, 206, 208, 202, 202, 208, 203, 206, 207, 208, 41, 42, 207, 207, 42, 208, 214, 55, 215, 49, 221, 216, 50, 216, 215, 203, 204, 221, 221, 204, 216, 204, 203, 209, 42, 43, 208, 203, 208, 209, 208, 43, 209, 213, 214, 217, 217, 214, 218, 218, 5, 217, 218, 4, 5, 214, 215, 218, 218, 215, 219, 216, 220, 215, 215, 220, 219, 209, 44, 204, 204, 205, 216, 204, 44, 205, 216, 205, 220, 209, 43, 44, 217, 6, 7, 217, 5, 6, 219, 3, 218, 218, 3, 4, 220, 2, 219, 219, 2, 3, 205, 1, 220, 220, 1, 2, 44, 0, 205, 205, 0, 1], "vertices": [2, 6, 133.02, 34.54, 0.97781, 66, -304.19, 117.19, 0.02219, 2, 6, 134, 16.31, 0.97371, 66, -303.21, 98.96, 0.02629, 2, 6, 133.83, -1.47, 0.97092, 66, -303.38, 81.18, 0.02908, 2, 6, 133.66, -19.25, 0.9735, 66, -303.55, 63.4, 0.0265, 2, 6, 133.45, -40.65, 0.9757, 66, -303.75, 41.99, 0.0243, 2, 6, 133.3, -56.62, 0.97906, 66, -303.91, 26.03, 0.02094, 2, 6, 133.15, -72.58, 0.98879, 66, -304.06, 10.07, 0.01121, 2, 6, 121.51, -77.57, 0.99044, 66, -315.7, 5.08, 0.00956, 2, 6, 104.01, -82.99, 0.99063, 66, -333.2, -0.34, 0.00937, 2, 10, -3.41, -0.59, 0.97686, 67, -290.48, -4.59, 0.02314, 3, 11, -13.85, 6.17, 0.01035, 10, -1.94, 3.53, 0.96667, 67, -294.82, -5.12, 0.02298, 2, 6, 58.67, -86.47, 0.98943, 66, -378.54, -3.82, 0.01057, 2, 6, 49.21, -86.62, 0.9897, 66, -388, -3.97, 0.0103, 2, 6, 28.56, -88.54, 0.98979, 66, -408.65, -5.9, 0.01021, 2, 6, 9.02, -84.53, 0.98713, 66, -428.18, -1.88, 0.01287, 2, 6, -23.93, -67.31, 0.97779, 66, -461.14, 15.34, 0.02221, 2, 6, -37.02, -61.74, 0.97371, 66, -474.23, 20.91, 0.02629, 3, 6, -41.47, -56.69, 0.971, 14, 62.3, 94.48, 0, 66, -478.68, 25.96, 0.029, 3, 6, -43.6, -45.01, 0.96848, 14, 62.93, 82.63, 0, 66, -480.81, 37.64, 0.03152, 2, 6, -41.38, -35.15, 0.96858, 66, -478.59, 47.5, 0.03142, 2, 6, -31.3, -8.74, 0.96977, 66, -468.5, 73.91, 0.03023, 3, 6, -14.69, 20.41, 0.89146, 14, 25.98, 21.4, 0.08538, 66, -451.9, 103.06, 0.02316, 3, 6, -6.89, 27.93, 0.79495, 14, 17.29, 14.92, 0.18312, 66, -444.1, 110.58, 0.02193, 2, 6, 11.46, 37.3, 0.97869, 66, -425.75, 119.95, 0.02131, 2, 6, 9.42, 42.16, 0.98278, 66, -427.79, 124.81, 0.01722, 3, 6, 7.62, 41.5, 0.78286, 14, 1.17, 3.29, 0.20214, 66, -429.59, 124.15, 0.015, 3, 6, 6.12, 39.99, 0.40228, 14, 2.85, 4.61, 0.58572, 66, -431.09, 122.64, 0.012, 3, 6, 2.82, 36.66, 0.14475, 14, 6.54, 7.5, 0.84425, 66, -434.38, 119.3, 0.011, 2, 14, 17.9, 10.94, 0.989, 66, -445.21, 114.45, 0.011, 2, 14, 28.84, 10.32, 0.99, 66, -456.15, 113.68, 0.01, 2, 14, 33.81, 4.36, 0.99119, 66, -461.82, 118.97, 0.00881, 2, 14, 33.82, -3.29, 0.99328, 66, -462.81, 126.56, 0.00672, 2, 14, 28.09, -9.33, 0.996, 66, -457.89, 133.27, 0.004, 2, 14, 16.23, -10.42, 0.997, 66, -446.26, 135.85, 0.003, 3, 6, -0.32, 51.61, 0.15989, 14, 7.77, -7.73, 0.83611, 66, -437.52, 134.26, 0.004, 3, 6, 4.84, 48.38, 0.62076, 14, 3.06, -3.88, 0.37024, 66, -432.37, 131.03, 0.009, 2, 6, 7.18, 46.92, 0.98681, 66, -430.03, 129.56, 0.01319, 2, 6, 9.71, 50.76, 0.98892, 66, -427.5, 133.41, 0.01108, 2, 6, 25.19, 64.62, 0.99403, 66, -412.02, 147.27, 0.00597, 2, 6, 37.94, 71.69, 0.99559, 66, -399.27, 154.34, 0.00441, 2, 6, 51.3, 72.86, 0.99394, 66, -385.91, 155.51, 0.00606, 2, 6, 59.09, 70.17, 0.9916, 66, -378.12, 152.82, 0.0084, 2, 6, 75.1, 73.67, 0.99317, 66, -362.11, 156.32, 0.00683, 2, 6, 92.9, 74.77, 0.99414, 66, -344.31, 157.42, 0.00586, 2, 6, 116.15, 55.85, 0.98197, 66, -321.05, 138.5, 0.01803, 4, 7, 39.66, -17.42, 0.0041, 9, 11.7, -2.75, 0.98831, 66, -371.56, 41.95, 0.0014, 67, -304.14, 41.95, 0.00619, 5, 7, 17.03, -4.11, 0.18088, 8, 3.39, -2.8, 0.8126, 9, -14.54, -3.44, 0.00235, 66, -362.44, 66.56, 0.00413, 67, -295.02, 66.56, 3e-05, 4, 7, 11.35, -2.1, 0.92165, 8, -2.54, -3.85, 0.07147, 9, -20.42, -4.75, 0.00211, 66, -361.47, 72.51, 0.00477, 4, 7, -2.34, -1.78, 0.98888, 8, -14.62, -10.3, 0.00352, 9, -32.21, -11.72, 0.00305, 66, -363.56, 86.04, 0.00455, 2, 7, -1, 1.66, 0.99547, 66, -359.94, 85.33, 0.00453, 3, 7, 10.98, 3.39, 0.97208, 8, -5.57, 0.74, 0.02315, 66, -356.13, 73.84, 0.00477, 4, 7, 18.32, 2.14, 0.23872, 8, 1.44, 3.28, 0.75647, 9, -16.76, 2.55, 0.00066, 66, -356.06, 66.39, 0.00415, 4, 8, 28.81, 3.35, 0.01798, 9, 10.58, 3.81, 0.97444, 66, -365.05, 40.55, 0.00133, 67, -297.64, 40.55, 0.00625, 5, 7, 28.15, -11.22, 0.01645, 8, 16.58, -3.52, 0.61399, 9, -1.34, -3.58, 0.36447, 66, -367.49, 54.36, 0.00262, 67, -300.07, 54.36, 0.00248, 4, 8, 17.01, 3.65, 0.60457, 9, -1.21, 3.6, 0.39019, 66, -360.87, 51.57, 0.00236, 67, -293.45, 51.57, 0.00288, 4, 8, 9.45, 3.74, 0.98786, 9, -8.78, 3.36, 0.00767, 66, -358.28, 58.69, 0.00322, 67, -290.86, 58.69, 0.00125, 4, 8, 22.81, 3.39, 0.08588, 9, 4.59, 3.59, 0.9078, 66, -363.03, 46.19, 0.00179, 67, -295.61, 46.19, 0.00453, 5, 7, 33.9, -14.46, 0.00777, 8, 23.17, -3.51, 0.03166, 9, 5.25, -3.29, 0.95434, 66, -369.66, 48.13, 0.0019, 67, -302.24, 48.13, 0.00433, 5, 7, 22.31, -7.8, 0.04231, 8, 9.81, -3.42, 0.93346, 9, -8.11, -3.77, 0.01965, 66, -365.15, 60.71, 0.0034, 67, -297.73, 60.71, 0.00118, 4, 7, 42.92, -16.4, 0.00015, 9, 13.92, -0.16, 0.99171, 66, -369.99, 38.92, 0.00117, 67, -302.57, 38.92, 0.00697, 3, 11, 9.54, 0.58, 0.99828, 10, 22.08, 2.32, 3e-05, 67, -304.47, 16.92, 0.00168, 2, 11, 7.37, -2.77, 0.99904, 67, -300.48, 17.2, 0.00096, 2, 10, 5.57, -3.83, 0.98821, 67, -291.6, 4.9, 0.01179, 2, 10, 0.43, -2.92, 0.98401, 67, -290.11, -0.11, 0.01599, 3, 11, -10.37, 4.23, 0.01187, 10, 1.83, 2.26, 0.97191, 67, -295.37, -1.17, 0.01622, 3, 11, -6.38, 3.28, 0.02032, 10, 5.94, 2.06, 0.96683, 67, -297.03, 2.59, 0.01285, 3, 11, 6.92, 1.96, 0.99072, 10, 19.25, 3.19, 0.00526, 67, -303.98, 14, 0.00402, 3, 11, 2.51, 2.7, 0.79981, 10, 14.78, 3.12, 0.19299, 67, -301.92, 10.03, 0.0072, 3, 11, 2.55, -2.9, 0.83049, 10, 15.84, -2.39, 0.1653, 67, -297.47, 13.44, 0.00421, 3, 11, -2.35, -3.13, 0.23558, 10, 11.07, -3.51, 0.75676, 67, -294.33, 9.67, 0.00766, 3, 11, -1.93, 2.99, 0.18206, 10, 10.36, 2.59, 0.80803, 67, -299.47, 6.31, 0.0099, 2, 6, 46.64, -61.78, 0.97551, 66, -390.57, 20.87, 0.02449, 2, 6, 50.99, -62.95, 0.97591, 66, -386.22, 19.7, 0.02409, 2, 6, 54.92, -65.09, 0.97661, 66, -382.29, 17.56, 0.02339, 2, 6, 58.2, -68.44, 0.9776, 66, -379, 14.21, 0.0224, 2, 6, 60.23, -72.39, 0.97936, 66, -376.98, 10.26, 0.02064, 2, 6, 61.24, -76.97, 0.98247, 66, -375.96, 5.68, 0.01753, 2, 6, 60.56, -79.88, 0.98446, 66, -376.65, 2.77, 0.01554, 2, 6, 57.45, -82.28, 0.98607, 66, -379.76, 0.37, 0.01393, 2, 6, 53.44, -82.03, 0.98601, 66, -383.76, 0.62, 0.01399, 2, 6, 49.83, -80.23, 0.98471, 66, -387.38, 2.42, 0.01529, 2, 6, 46.84, -76.69, 0.98227, 66, -390.37, 5.95, 0.01773, 2, 6, 46.28, -72.56, 0.97952, 66, -390.92, 10.09, 0.02048, 2, 6, 46.31, -68.4, 0.97766, 66, -390.9, 14.25, 0.02234, 2, 6, 46.74, -64.75, 0.97647, 66, -390.47, 17.89, 0.02353, 2, 6, 53.46, -62.05, 0.97563, 66, -383.75, 20.6, 0.02437, 2, 6, 56.67, -63.96, 0.9762, 66, -380.54, 18.69, 0.0238, 2, 6, 60.46, -67.62, 0.97736, 66, -376.75, 15.03, 0.02264, 2, 6, 62.98, -71.82, 0.97918, 66, -374.23, 10.83, 0.02082, 2, 6, 63.92, -76.32, 0.98223, 66, -373.29, 6.33, 0.01777, 2, 6, 63.68, -80.77, 0.98529, 66, -373.52, 1.88, 0.01471, 2, 6, 62.36, -83.33, 0.98704, 66, -374.85, -0.68, 0.01296, 2, 6, 48.6, -31.3, 0.96751, 66, -388.61, 51.35, 0.03249, 2, 6, 51.9, -30.42, 0.96737, 66, -385.31, 52.23, 0.03263, 2, 6, 56.15, -27.44, 0.96682, 66, -381.06, 55.2, 0.03318, 2, 6, 58.97, -23.34, 0.96603, 66, -378.23, 59.31, 0.03397, 2, 6, 60.53, -18.21, 0.9651, 66, -376.68, 64.44, 0.0349, 2, 6, 60.05, -12.18, 0.96395, 66, -377.16, 70.47, 0.03605, 2, 6, 58.8, -6.99, 0.96294, 66, -378.41, 75.66, 0.03706, 2, 6, 56.62, -2.48, 0.96219, 66, -380.59, 80.17, 0.03781, 2, 6, 52.93, 0.94, 0.9626, 66, -384.28, 83.59, 0.0374, 2, 6, 48.64, -2.18, 0.96222, 66, -388.57, 80.47, 0.03778, 2, 6, 45.63, -6.38, 0.96281, 66, -391.57, 76.27, 0.03719, 2, 6, 43.57, -11.53, 0.96377, 66, -393.64, 71.11, 0.03623, 2, 6, 44.02, -17.83, 0.96496, 66, -393.18, 64.82, 0.03504, 2, 6, 45.39, -23.71, 0.96608, 66, -391.82, 58.94, 0.03392, 2, 6, 47.05, -28.24, 0.96693, 66, -390.15, 54.41, 0.03307, 2, 6, 54.6, 5.72, 0.96321, 66, -382.61, 88.37, 0.03679, 2, 6, 59.21, 0.04, 0.96252, 66, -377.99, 82.69, 0.03748, 2, 6, 62.31, -5.56, 0.96276, 66, -374.9, 77.09, 0.03724, 2, 6, 63.81, -12.47, 0.96409, 66, -373.39, 70.18, 0.03591, 2, 6, 63.87, -18.87, 0.9653, 66, -373.34, 63.78, 0.0347, 2, 6, 62.11, -24.99, 0.96641, 66, -375.09, 57.66, 0.03359, 2, 6, 58.66, -29.62, 0.96722, 66, -378.55, 53.03, 0.03278, 2, 6, 54.1, -32.62, 0.9678, 66, -383.1, 50.03, 0.0322, 2, 6, 49.89, -34.72, 0.96817, 66, -387.31, 47.93, 0.03183, 2, 6, 46.08, -34.93, 0.9682, 66, -391.12, 47.72, 0.0318, 2, 6, 44.97, -29.25, 0.96712, 66, -392.24, 53.4, 0.03288, 2, 6, 40.62, -21.26, 0.96556, 66, -396.58, 61.39, 0.03444, 2, 6, 39.14, -10.19, 0.9634, 66, -398.07, 72.46, 0.0366, 2, 6, 41.91, -0.33, 0.96243, 66, -395.3, 82.32, 0.03757, 2, 6, 47.58, 5.37, 0.96318, 66, -389.63, 88.02, 0.03682, 2, 6, 45.41, -61.26, 0.97535, 66, -391.8, 21.39, 0.02465, 2, 6, 44.44, -67.05, 0.97723, 66, -392.76, 15.6, 0.02277, 2, 6, 42.35, -75.76, 0.98162, 66, -394.86, 6.89, 0.01838, 2, 6, 43.87, -81.77, 0.98593, 66, -393.34, 0.87, 0.01407, 2, 6, 45.71, -42.77, 0.97113, 66, -391.5, 39.88, 0.02887, 2, 6, 50.97, -51.93, 0.97232, 66, -386.24, 30.72, 0.02768, 2, 6, 50.62, -60.46, 0.97509, 66, -386.59, 22.19, 0.02491, 2, 6, 24.81, -45.2, 0.96971, 66, -412.4, 37.45, 0.03029, 2, 6, 20.06, -41.9, 0.96883, 66, -417.14, 40.75, 0.03117, 2, 6, 14.59, -42.81, 0.96863, 66, -422.62, 39.84, 0.03137, 2, 6, 10.72, -46.65, 0.96915, 66, -426.49, 35.99, 0.03085, 2, 6, 11.8, -53.63, 0.9713, 66, -425.4, 29.02, 0.0287, 2, 6, 9.3, -58.84, 0.97285, 66, -427.91, 23.81, 0.02715, 2, 6, 11.92, -63.84, 0.97469, 66, -425.29, 18.81, 0.02531, 2, 6, 31.12, -62.62, 0.97556, 66, -406.09, 20.03, 0.02444, 2, 6, 35.26, -43.98, 0.9704, 66, -401.95, 38.67, 0.0296, 2, 6, -5.96, -33.21, 0.96554, 66, -443.17, 49.44, 0.03446, 2, 6, -5.46, -38.21, 0.9667, 66, -442.66, 44.44, 0.0333, 2, 6, -1.29, -47.39, 0.96813, 66, -438.5, 35.26, 0.03187, 2, 6, 1.81, -52.92, 0.96881, 66, -435.4, 29.73, 0.03119, 2, 6, 2.67, -56.46, 0.9698, 66, -434.54, 26.19, 0.0302, 2, 6, 1.29, -59.95, 0.97095, 66, -435.91, 22.7, 0.02905, 2, 6, 3.28, -63.05, 0.97224, 66, -433.93, 19.59, 0.02776, 2, 6, 2.13, -65.62, 0.97385, 66, -435.07, 17.03, 0.02615, 2, 6, -0.5, -66.57, 0.97391, 66, -437.71, 16.08, 0.02609, 2, 6, -2.47, -66.65, 0.97427, 66, -439.67, 16, 0.02573, 2, 6, -6.19, -65.28, 0.97334, 66, -443.4, 17.37, 0.02666, 2, 6, -10.68, -64.82, 0.97301, 66, -447.88, 17.83, 0.02699, 2, 6, -15.12, -62.69, 0.97198, 66, -452.33, 19.96, 0.02802, 2, 6, -17.4, -58.89, 0.97072, 66, -454.61, 23.76, 0.02928, 2, 6, -18.43, -52.15, 0.96908, 66, -455.64, 30.5, 0.03092, 2, 6, -17.44, -46.54, 0.96756, 66, -454.65, 36.11, 0.03244, 2, 6, -15.04, -40.66, 0.96654, 66, -452.24, 41.99, 0.03346, 2, 6, -9.18, -31.38, 0.96521, 66, -446.38, 51.27, 0.03479, 2, 6, -3.48, -67.8, 0.97538, 66, -440.69, 14.85, 0.02462, 2, 6, -4.34, -65.37, 0.97427, 66, -441.55, 17.28, 0.02573, 2, 6, -4.22, -62.99, 0.97309, 66, -441.43, 19.66, 0.02691, 2, 6, -3.16, -61.79, 0.97289, 66, -440.37, 20.86, 0.02711, 2, 6, -4.04, -59.51, 0.97209, 66, -441.24, 23.14, 0.02791, 2, 6, -3.97, -55.68, 0.97081, 66, -441.18, 26.97, 0.02919, 2, 6, -4.47, -51.87, 0.96969, 66, -441.68, 30.77, 0.03031, 2, 6, -5.7, -46.87, 0.96851, 66, -442.91, 35.78, 0.03149, 2, 6, -6.53, -43.27, 0.96778, 66, -443.74, 39.37, 0.03222, 2, 6, -7.39, -38.64, 0.96673, 66, -444.6, 44.01, 0.03327, 2, 6, -8.11, -33.16, 0.96543, 66, -445.32, 49.49, 0.03457, 2, 6, -7.12, -30.35, 0.96504, 66, -444.33, 52.29, 0.03496, 2, 6, -7.87, -47.54, 0.96879, 66, -445.07, 35.11, 0.03121, 2, 6, -7.84, -51.6, 0.96968, 66, -445.05, 31.05, 0.03032, 2, 6, -7.34, -56.65, 0.9711, 66, -444.55, 26, 0.0289, 2, 6, -6.1, -60.07, 0.97233, 66, -443.31, 22.58, 0.02767, 2, 6, -3.58, -43.16, 0.96756, 66, -440.79, 39.49, 0.03244, 2, 6, -12.21, -36.1, 0.966, 66, -449.42, 46.55, 0.034, 2, 6, -4.77, -67.47, 0.97525, 66, -441.97, 15.18, 0.02475, 2, 6, -0.71, -68.94, 0.9757, 66, -437.92, 13.7, 0.0243, 2, 6, -3, -70.57, 0.97711, 66, -440.21, 12.07, 0.02289, 2, 6, -4.98, -69.41, 0.97628, 66, -442.19, 13.23, 0.02372, 2, 6, -2.44, -32.7, 0.96556, 66, -439.65, 49.95, 0.03444, 2, 6, -5.27, -27.13, 0.9646, 66, -442.48, 55.52, 0.0354, 2, 6, -10.39, -26.1, 0.9646, 66, -447.6, 56.55, 0.0354, 2, 6, -11.38, -46.63, 0.9673, 66, -448.59, 36.02, 0.0327, 2, 6, -11.85, -52.55, 0.96771, 66, -449.06, 30.1, 0.03229, 2, 6, -11, -58.16, 0.96968, 66, -448.21, 24.49, 0.03032, 2, 6, -8.95, -61.72, 0.97211, 66, -446.16, 20.93, 0.02789, 2, 6, -10.39, -42.22, 0.96714, 66, -447.6, 40.43, 0.03286, 2, 6, -21.55, -48.62, 0.96919, 66, -458.76, 34.03, 0.03081, 2, 6, -20.04, -58.47, 0.97234, 66, -457.24, 24.18, 0.02766, 2, 6, -32.21, -46.74, 0.96711, 66, -469.42, 35.91, 0.03289, 2, 6, -29.95, -57.44, 0.97063, 66, -467.15, 25.21, 0.02937, 2, 6, -29.86, -36.42, 0.96609, 66, -467.07, 46.23, 0.03391, 2, 6, -20.11, -18.6, 0.9638, 66, -457.32, 64.05, 0.0362, 2, 6, 8.49, 3.17, 0.96408, 66, -428.72, 85.82, 0.03592, 2, 6, 29.01, 7.09, 0.96295, 66, -408.2, 89.74, 0.03705, 2, 6, -7.87, -5.49, 0.96374, 66, -445.08, 77.16, 0.03626, 2, 6, 27.67, -20.36, 0.965, 66, -409.54, 62.29, 0.035, 2, 6, 10.17, -22.25, 0.96417, 66, -427.04, 60.4, 0.03583, 2, 6, 48.91, 47.65, 0.97675, 66, -388.3, 130.3, 0.02325, 2, 6, 28.66, 43.84, 0.97877, 66, -408.54, 126.49, 0.02123, 2, 6, 11.1, 21.58, 0.96971, 66, -426.11, 104.22, 0.03029, 2, 6, 32.46, 22.53, 0.96652, 66, -404.75, 105.18, 0.03348, 2, 6, 45.38, 29.86, 0.96803, 66, -391.82, 112.5, 0.03197, 2, 6, 61.02, 30.46, 0.96836, 66, -376.19, 113.11, 0.03164, 2, 6, 77.79, 29.06, 0.96842, 66, -359.42, 111.71, 0.03158, 2, 6, 96.39, 26.17, 0.96844, 66, -340.81, 108.82, 0.03156, 2, 6, 115.59, 21.19, 0.96922, 66, -321.61, 103.84, 0.03078, 2, 6, 57.64, 55.04, 0.98119, 66, -379.57, 137.68, 0.01881, 2, 6, 63.2, 63.65, 0.98677, 66, -374.01, 146.3, 0.01323, 2, 6, 76.66, 55.37, 0.97926, 66, -360.55, 138.02, 0.02074, 2, 6, 96.13, 56.27, 0.97975, 66, -341.08, 138.92, 0.02025, 2, 6, 58.66, -61.54, 0.97536, 66, -378.55, 21.11, 0.02464, 2, 6, 58.08, -48.52, 0.97115, 66, -379.12, 34.12, 0.02885, 2, 6, 56.31, -39.84, 0.97018, 66, -380.89, 42.81, 0.02982, 2, 6, 98.84, -63.42, 0.97705, 66, -338.37, 19.23, 0.02295, 2, 6, 99.9, -42, 0.97124, 66, -337.31, 40.65, 0.02876, 2, 6, 102.91, -15.64, 0.96647, 66, -334.29, 67.01, 0.03353, 2, 6, 100.59, 8.36, 0.9636, 66, -336.61, 91, 0.0364, 2, 6, 117.21, -60.55, 0.97779, 66, -320, 22.1, 0.02221, 2, 6, 119.05, -40.48, 0.97277, 66, -318.16, 42.17, 0.02723, 2, 6, 119.33, -16.61, 0.96921, 66, -317.87, 66.04, 0.03079, 2, 6, 117.52, 4.33, 0.96579, 66, -319.69, 86.98, 0.03421, 2, 6, 78, 13.66, 0.96355, 66, -359.21, 96.3, 0.03645, 2, 6, 60.75, 16.33, 0.96377, 66, -376.46, 98.98, 0.03623, 2, 6, 44.96, 14.08, 0.96351, 66, -392.25, 96.73, 0.03649, 2, 6, 30.8, -79.78, 0.98266, 66, -406.4, 2.86, 0.01734, 2, 6, 12.38, -76.54, 0.98099, 66, -424.82, 6.11, 0.01901, 3, 6, 0.93, 43.5, 0.14567, 14, 7.56, 0.47, 0.84262, 66, -436.28, 126.14, 0.01171, 2, 14, 16.64, 0.4, 0.98789, 66, -445.3, 125.06, 0.01211, 2, 14, 26.61, 0.53, 0.98841, 66, -455.18, 123.67, 0.01159, 3, 6, 4.99, 44.19, 0.61981, 14, 3.44, 0.3, 0.36654, 66, -432.22, 126.84, 0.01366, 3, 6, 7.43, 44.22, 0.87853, 14, 1.01, 0.57, 0.10712, 66, -429.77, 126.87, 0.01435, 2, 6, 9.38, 44.58, 0.98454, 66, -427.83, 127.23, 0.01546], "hull": 45, "edges": [0, 2, 26, 28, 32, 34, 34, 36, 36, 38, 56, 58, 86, 88, 28, 30, 30, 32, 20, 22, 22, 24, 24, 26, 16, 18, 18, 20, 12, 14, 14, 16, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 64, 66, 66, 68, 72, 74, 74, 76, 76, 78, 78, 80, 84, 86, 80, 82, 82, 84, 88, 0, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 110, 110, 108, 104, 112, 112, 108, 90, 114, 114, 106, 92, 116, 116, 106, 104, 118, 118, 90, 120, 122, 124, 126, 128, 130, 132, 120, 134, 132, 122, 136, 124, 138, 138, 136, 130, 140, 140, 134, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 142, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 184, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 244, 246, 246, 248, 248, 250, 170, 256, 252, 254, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 244, 252, 274, 274, 258, 276, 278, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 328, 336, 336, 338, 338, 340, 340, 342, 342, 316, 278, 344, 344, 280, 308, 346, 346, 310, 296, 348, 350, 352, 352, 354, 356, 358, 358, 360, 362, 364, 364, 366, 366, 368, 362, 370, 372, 374, 376, 378, 376, 380, 380, 382, 384, 386, 382, 388, 388, 384, 390, 392, 394, 396, 396, 46, 398, 400, 400, 402, 402, 404, 404, 406, 406, 408, 408, 410, 410, 2, 394, 412, 412, 414, 414, 82, 420, 256, 254, 422, 424, 252, 8, 10, 10, 12, 6, 8, 2, 4, 4, 6, 68, 70, 70, 72, 50, 52, 52, 54, 448, 450, 452, 454, 454, 456, 62, 64, 58, 60, 62, 60], "width": 166, "height": 189}}, "head2": {"head": {"type": "mesh", "uvs": [0.41835, 0.77008, 0.53576, 0.69044, 0.6623, 0.68516, 0.68134, 0.70869, 0.72384, 0.70908, 0.75232, 0.72668, 0.78464, 0.71735, 0.86057, 0.72591, 0.90475, 0.75037, 0.76988, 0.90745, 0.72388, 0.97093, 0.68949, 0.9898, 0.618, 0.99082, 0.56159, 0.97066, 0.41457, 0.89515, 0.585, 0.78411, 0.61517, 0.78581, 0.67387, 0.77203, 0.7098, 0.76063, 0.73168, 0.75923, 0.75103, 0.76941, 0.77145, 0.76176, 0.78556, 0.76994, 0.7886, 0.78453, 0.78715, 0.79484, 0.77535, 0.81311, 0.7682, 0.83611, 0.75112, 0.85748, 0.72633, 0.86607, 0.68525, 0.8656, 0.65287, 0.8556, 0.6203, 0.83793, 0.57092, 0.79931, 0.793, 0.80112, 0.77771, 0.80353, 0.7637, 0.80084, 0.75759, 0.79427, 0.74319, 0.79685, 0.72049, 0.79321, 0.69737, 0.79252, 0.66641, 0.79459, 0.64423, 0.79583, 0.61585, 0.79631, 0.58254, 0.79527, 0.56687, 0.7877, 0.66827, 0.80648, 0.69241, 0.80986, 0.72294, 0.81162, 0.74447, 0.8081, 0.64647, 0.78032, 0.59597, 0.81925, 0.78978, 0.80756, 0.80252, 0.78767, 0.80995, 0.80104, 0.80111, 0.81036, 0.58545, 0.76529, 0.54953, 0.77524, 0.53838, 0.80107, 0.65938, 0.82403, 0.69413, 0.83161, 0.72826, 0.83204, 0.75148, 0.82442, 0.63413, 0.81503, 0.66117, 0.87883, 0.72122, 0.87947, 0.63947, 0.93284, 0.70531, 0.9303, 0.58048, 0.91165, 0.48422, 0.8453], "triangles": [19, 4, 5, 18, 3, 4, 18, 4, 19, 21, 5, 6, 20, 19, 5, 55, 1, 2, 55, 2, 3, 3, 49, 55, 21, 20, 5, 22, 6, 7, 21, 6, 22, 56, 0, 1, 18, 17, 3, 55, 56, 1, 17, 49, 3, 55, 44, 56, 16, 15, 55, 52, 23, 22, 49, 16, 55, 7, 52, 22, 15, 44, 55, 39, 17, 18, 38, 18, 19, 38, 19, 20, 39, 18, 38, 36, 20, 21, 36, 21, 22, 36, 22, 23, 37, 38, 20, 40, 49, 17, 40, 17, 39, 24, 36, 23, 24, 23, 52, 43, 44, 15, 41, 16, 49, 41, 49, 40, 42, 16, 41, 15, 16, 42, 43, 15, 42, 36, 37, 20, 32, 44, 43, 35, 36, 24, 53, 52, 7, 53, 7, 8, 33, 24, 52, 57, 0, 56, 57, 56, 44, 57, 44, 32, 53, 33, 52, 34, 35, 24, 34, 24, 33, 46, 45, 40, 41, 40, 45, 51, 34, 33, 48, 37, 36, 48, 36, 35, 39, 46, 40, 54, 33, 53, 51, 33, 54, 47, 38, 37, 47, 37, 48, 39, 38, 47, 46, 39, 47, 25, 35, 34, 25, 34, 51, 62, 42, 41, 62, 41, 45, 50, 43, 42, 50, 42, 62, 32, 43, 50, 58, 62, 45, 61, 48, 35, 61, 35, 25, 59, 46, 47, 58, 45, 46, 59, 58, 46, 60, 47, 48, 60, 48, 61, 59, 47, 60, 26, 61, 25, 31, 50, 62, 68, 0, 57, 58, 31, 62, 30, 58, 59, 30, 31, 58, 27, 60, 61, 27, 61, 26, 29, 30, 59, 28, 59, 60, 28, 60, 27, 29, 59, 28, 63, 30, 29, 64, 29, 28, 63, 29, 64, 14, 0, 68, 54, 9, 26, 27, 26, 9, 64, 28, 27, 9, 64, 27, 25, 51, 54, 54, 26, 25, 9, 54, 53, 9, 53, 8, 50, 57, 32, 67, 50, 31, 63, 67, 31, 63, 31, 30, 50, 68, 57, 67, 68, 50, 66, 63, 64, 66, 64, 9, 65, 67, 63, 65, 63, 66, 67, 14, 68, 13, 67, 65, 13, 14, 67, 10, 66, 9, 11, 65, 66, 11, 66, 10, 12, 13, 65, 12, 65, 11], "vertices": [2, 6, -7.87, -5.49, 0.96374, 66, -445.08, 77.16, 0.03626, 2, 6, 10.17, -22.25, 0.96417, 66, -427.04, 60.4, 0.03583, 2, 6, 14.59, -42.81, 0.96863, 66, -422.62, 39.84, 0.03137, 2, 6, 10.72, -46.65, 0.96915, 66, -426.49, 35.99, 0.03085, 2, 6, 11.8, -53.63, 0.9713, 66, -425.4, 29.02, 0.0287, 2, 6, 9.3, -58.84, 0.97285, 66, -427.91, 23.81, 0.02715, 2, 6, 11.92, -63.84, 0.97469, 66, -425.29, 18.81, 0.02531, 2, 6, 12.38, -76.54, 0.98099, 66, -424.82, 6.11, 0.01901, 2, 6, 9.02, -84.53, 0.98713, 66, -428.18, -1.88, 0.01287, 2, 6, -23.93, -67.31, 0.97779, 66, -461.14, 15.34, 0.02221, 2, 6, -37.02, -61.74, 0.97371, 66, -474.23, 20.91, 0.02629, 3, 6, -41.47, -56.69, 0.971, 14, 62.3, 94.48, 0, 66, -478.68, 25.96, 0.029, 3, 6, -43.6, -45.01, 0.96848, 14, 62.93, 82.63, 0, 66, -480.81, 37.64, 0.03152, 2, 6, -41.38, -35.15, 0.96858, 66, -478.59, 47.5, 0.03142, 2, 6, -31.3, -8.74, 0.96977, 66, -468.5, 73.91, 0.03023, 2, 6, -5.96, -33.21, 0.96554, 66, -443.17, 49.44, 0.03446, 2, 6, -5.46, -38.21, 0.9667, 66, -442.66, 44.44, 0.0333, 2, 6, -1.29, -47.39, 0.96813, 66, -438.5, 35.26, 0.03187, 2, 6, 1.81, -52.92, 0.96881, 66, -435.4, 29.73, 0.03119, 2, 6, 2.67, -56.46, 0.9698, 66, -434.54, 26.19, 0.0302, 2, 6, 1.29, -59.95, 0.97095, 66, -435.91, 22.7, 0.02905, 2, 6, 3.28, -63.05, 0.97224, 66, -433.93, 19.59, 0.02776, 2, 6, 2.13, -65.62, 0.97385, 66, -435.07, 17.03, 0.02615, 2, 6, -0.5, -66.57, 0.97391, 66, -437.71, 16.08, 0.02609, 2, 6, -2.47, -66.65, 0.97427, 66, -439.67, 16, 0.02573, 2, 6, -6.19, -65.28, 0.97334, 66, -443.4, 17.37, 0.02666, 2, 6, -10.68, -64.82, 0.97301, 66, -447.88, 17.83, 0.02699, 2, 6, -15.12, -62.69, 0.97198, 66, -452.33, 19.96, 0.02802, 2, 6, -17.4, -58.89, 0.97072, 66, -454.61, 23.76, 0.02928, 2, 6, -18.43, -52.15, 0.96908, 66, -455.64, 30.5, 0.03092, 2, 6, -17.44, -46.54, 0.96756, 66, -454.65, 36.11, 0.03244, 2, 6, -15.04, -40.66, 0.96654, 66, -452.24, 41.99, 0.03346, 2, 6, -9.18, -31.38, 0.96521, 66, -446.38, 51.27, 0.03479, 2, 6, -3.48, -67.8, 0.97538, 66, -440.69, 14.85, 0.02462, 2, 6, -4.34, -65.37, 0.97427, 66, -441.55, 17.28, 0.02573, 2, 6, -4.22, -62.99, 0.97309, 66, -441.43, 19.66, 0.02691, 2, 6, -3.16, -61.79, 0.97289, 66, -440.37, 20.86, 0.02711, 2, 6, -4.04, -59.51, 0.97209, 66, -441.24, 23.14, 0.02791, 2, 6, -3.97, -55.68, 0.97081, 66, -441.18, 26.97, 0.02919, 2, 6, -4.47, -51.87, 0.96969, 66, -441.68, 30.77, 0.03031, 2, 6, -5.7, -46.87, 0.96851, 66, -442.91, 35.78, 0.03149, 2, 6, -6.53, -43.27, 0.96778, 66, -443.74, 39.37, 0.03222, 2, 6, -7.39, -38.64, 0.96673, 66, -444.6, 44.01, 0.03327, 2, 6, -8.11, -33.16, 0.96543, 66, -445.32, 49.49, 0.03457, 2, 6, -7.12, -30.35, 0.96504, 66, -444.33, 52.29, 0.03496, 2, 6, -7.87, -47.54, 0.96879, 66, -445.07, 35.11, 0.03121, 2, 6, -7.84, -51.6, 0.96968, 66, -445.05, 31.05, 0.03032, 2, 6, -7.34, -56.65, 0.9711, 66, -444.55, 26, 0.0289, 2, 6, -6.1, -60.07, 0.97233, 66, -443.31, 22.58, 0.02767, 2, 6, -3.58, -43.16, 0.96756, 66, -440.79, 39.49, 0.03244, 2, 6, -12.21, -36.1, 0.966, 66, -449.42, 46.55, 0.034, 2, 6, -4.77, -67.47, 0.97525, 66, -441.97, 15.18, 0.02475, 2, 6, -0.71, -68.94, 0.9757, 66, -437.92, 13.7, 0.0243, 2, 6, -3, -70.57, 0.97711, 66, -440.21, 12.07, 0.02289, 2, 6, -4.98, -69.41, 0.97628, 66, -442.19, 13.23, 0.02372, 2, 6, -2.44, -32.7, 0.96556, 66, -439.65, 49.95, 0.03444, 2, 6, -5.27, -27.13, 0.9646, 66, -442.48, 55.52, 0.0354, 2, 6, -10.39, -26.1, 0.9646, 66, -447.6, 56.55, 0.0354, 2, 6, -11.38, -46.63, 0.9673, 66, -448.59, 36.02, 0.0327, 2, 6, -11.85, -52.55, 0.96771, 66, -449.06, 30.1, 0.03229, 2, 6, -11, -58.16, 0.96968, 66, -448.21, 24.49, 0.03032, 2, 6, -8.95, -61.72, 0.97211, 66, -446.16, 20.93, 0.02789, 2, 6, -10.39, -42.22, 0.96714, 66, -447.6, 40.43, 0.03286, 2, 6, -21.55, -48.62, 0.96919, 66, -458.76, 34.03, 0.03081, 2, 6, -20.04, -58.47, 0.97234, 66, -457.24, 24.18, 0.02766, 2, 6, -32.21, -46.74, 0.96711, 66, -469.42, 35.91, 0.03289, 2, 6, -29.95, -57.44, 0.97063, 66, -467.15, 25.21, 0.02937, 2, 6, -29.86, -36.42, 0.96609, 66, -467.07, 46.23, 0.03391, 2, 6, -20.11, -18.6, 0.9638, 66, -457.32, 64.05, 0.0362], "hull": 15, "edges": [20, 22, 22, 24, 24, 26, 16, 18, 18, 20, 26, 28, 4, 6, 6, 8, 8, 10, 10, 12, 30, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 82, 90, 90, 92, 92, 94, 94, 96, 96, 70, 32, 98, 98, 34, 62, 100, 100, 64, 50, 102, 104, 106, 106, 108, 110, 112, 112, 114, 116, 118, 118, 120, 120, 122, 116, 124, 126, 128, 130, 132, 130, 134, 134, 136, 136, 0, 0, 28, 0, 2, 2, 4, 12, 14, 14, 16], "width": 166, "height": 189}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.16178, 0.00966, 0.26429, 0.02032, 0.36679, 0.03098, 0.35874, 0.05356, 0.34863, 0.07639, 0.35163, 0.09313, 0.36352, 0.10695, 0.37554, 0.11857, 0.4116, 0.14998, 0.45021, 0.18484, 0.50543, 0.24928, 0.53829, 0.32018, 0.56682, 0.39074, 0.57477, 0.41386, 0.58549, 0.42942, 0.60163, 0.44499, 0.61757, 0.46206, 0.6273, 0.48176, 0.62499, 0.50137, 0.61623, 0.52055, 0.61603, 0.54236, 0.61918, 0.56682, 0.6316, 0.59755, 0.65221, 0.64461, 0.7048, 0.76079, 0.71961, 0.7845, 0.73001, 0.79954, 0.74186, 0.81444, 0.75487, 0.82851, 0.77048, 0.8433, 0.79229, 0.86191, 0.80681, 0.85268, 0.87843, 0.87202, 0.88051, 0.91977, 0.88288, 0.93731, 0.98335, 0.9516, 0.99824, 0.97837, 0.99825, 0.99088, 0.95429, 0.9991, 0.84437, 0.99917, 0.7973, 0.99176, 0.77028, 0.97329, 0.70687, 0.94578, 0.70737, 0.98097, 0.65407, 0.97916, 0.61691, 0.97345, 0.59948, 0.91749, 0.60041, 0.88633, 0.62738, 0.85835, 0.63846, 0.85287, 0.63615, 0.84293, 0.63044, 0.82958, 0.62245, 0.81498, 0.609, 0.79875, 0.58515, 0.77604, 0.45616, 0.67421, 0.43, 0.64679, 0.40987, 0.60617, 0.40891, 0.56608, 0.41595, 0.54133, 0.42643, 0.52047, 0.4114, 0.50136, 0.3974, 0.48179, 0.38518, 0.46213, 0.36955, 0.44495, 0.34961, 0.42558, 0.28736, 0.37558, 0.19257, 0.30531, 0.13928, 0.25786, 0.10125, 0.22177, 0.06558, 0.18966, 0.02847, 0.162, 0.00904, 0.14014, 0.00143, 0.11438, 0.0032, 0.08333, 0.01444, 0.05502, 0.03514, 0.02867, 0.07054, 0.00017, 0.5352, 0.41376, 0.55799, 0.43307, 0.57292, 0.44772, 0.58979, 0.4653, 0.59888, 0.48464, 0.60213, 0.50486, 0.59369, 0.52274, 0.5872, 0.54501, 0.52152, 0.54776, 0.52813, 0.52734, 0.52641, 0.51084, 0.5149, 0.49549, 0.50906, 0.47642, 0.48715, 0.44198, 0.50605, 0.45879, 0.45687, 0.42176, 0.4964, 0.57097, 0.48785, 0.61388, 0.51989, 0.66691, 0.63095, 0.73923, 0.60745, 0.66113, 0.59144, 0.60905, 0.58076, 0.56952, 0.38634, 0.3612, 0.28671, 0.27133, 0.24818, 0.2391, 0.20144, 0.20021, 0.16971, 0.16594, 0.14809, 0.13895, 0.12495, 0.10885, 0.12259, 0.07816, 0.13458, 0.04644, 0.25414, 0.0532, 0.25074, 0.08133, 0.26699, 0.10325, 0.29333, 0.12358, 0.31713, 0.14456, 0.343, 0.16532, 0.38366, 0.20582, 0.42576, 0.25047, 0.48787, 0.34478], "triangles": [38, 39, 34, 39, 40, 34, 38, 36, 37, 38, 35, 36, 38, 34, 35, 40, 41, 34, 43, 44, 42, 44, 45, 42, 45, 46, 42, 34, 42, 33, 32, 33, 30, 42, 34, 41, 33, 42, 30, 30, 31, 32, 46, 47, 42, 42, 47, 30, 30, 47, 48, 30, 49, 29, 49, 30, 48, 49, 28, 29, 49, 50, 28, 50, 27, 28, 50, 51, 27, 27, 52, 26, 27, 51, 52, 52, 25, 26, 52, 53, 25, 25, 53, 24, 53, 54, 24, 54, 55, 97, 54, 97, 24, 97, 55, 96, 97, 23, 24, 96, 98, 97, 97, 98, 23, 55, 56, 96, 56, 95, 96, 96, 95, 99, 96, 99, 98, 99, 95, 94, 98, 99, 23, 94, 100, 99, 56, 57, 95, 99, 22, 23, 95, 57, 94, 22, 100, 21, 22, 99, 100, 57, 58, 94, 94, 86, 100, 86, 94, 59, 100, 85, 21, 100, 86, 85, 85, 20, 21, 94, 58, 59, 59, 60, 86, 86, 87, 85, 86, 60, 87, 85, 84, 20, 85, 87, 84, 20, 84, 19, 60, 88, 87, 87, 88, 84, 84, 83, 19, 84, 88, 83, 19, 83, 18, 60, 89, 88, 60, 61, 89, 88, 89, 83, 89, 82, 83, 83, 82, 18, 18, 82, 17, 89, 90, 82, 82, 81, 17, 61, 90, 89, 61, 62, 90, 90, 81, 82, 62, 92, 90, 92, 63, 91, 92, 62, 63, 81, 16, 17, 81, 92, 80, 81, 90, 92, 81, 15, 16, 81, 80, 15, 63, 93, 91, 63, 64, 93, 92, 79, 80, 92, 91, 79, 80, 14, 15, 80, 79, 14, 64, 65, 93, 91, 78, 79, 91, 93, 78, 79, 13, 14, 79, 78, 13, 65, 101, 93, 65, 66, 101, 78, 93, 118, 78, 12, 13, 93, 101, 118, 78, 118, 12, 118, 11, 12, 101, 66, 102, 66, 67, 102, 118, 101, 117, 101, 102, 117, 11, 117, 10, 11, 118, 117, 102, 68, 103, 102, 67, 68, 117, 102, 116, 68, 104, 103, 68, 69, 104, 102, 103, 116, 10, 117, 9, 117, 116, 9, 103, 104, 116, 69, 70, 104, 104, 115, 116, 9, 115, 8, 9, 116, 115, 70, 105, 104, 104, 114, 115, 104, 105, 114, 105, 70, 106, 70, 71, 106, 105, 113, 114, 105, 106, 113, 115, 114, 8, 114, 7, 8, 114, 113, 7, 106, 112, 113, 106, 107, 112, 113, 6, 7, 113, 5, 6, 113, 112, 5, 107, 111, 112, 5, 112, 4, 112, 111, 4, 111, 110, 4, 2, 3, 1, 3, 110, 1, 4, 110, 3, 71, 72, 106, 72, 107, 106, 72, 73, 107, 73, 74, 107, 74, 108, 107, 107, 108, 111, 74, 75, 108, 108, 109, 111, 111, 109, 110, 108, 75, 109, 75, 76, 109, 109, 0, 110, 110, 0, 1, 76, 77, 109, 109, 77, 0], "vertices": [4, 42, 51.91, -23.62, 0.97461, 44, 12.92, -87.1, 0.00572, 58, -115.95, -4.99, 6e-05, 61, -323.44, 44.12, 0.01961, 4, 42, 39.2, 27.01, 0.63188, 44, 0.2, -36.47, 0.33796, 58, -91.13, 40.93, 0.00621, 61, -271.7, 37.18, 0.02396, 1, 44, -7.78, 15.3, 1, 3, 42, 63.76, 86.7, 0.02795, 44, 24.76, 23.22, 0.72942, 58, -32.71, 68.37, 0.24263, 3, 42, 96.29, 93.52, 0.01319, 44, 57.29, 30.03, 0.29738, 58, -4.12, 51.43, 0.68943, 2, 44, 73.89, 38.26, 0.12384, 58, 13.68, 46.29, 0.87616, 2, 44, 85.86, 49.41, 0.04902, 58, 30.03, 46.43, 0.95098, 2, 44, 95.55, 59.72, 0.02093, 58, 44.15, 47.48, 0.97907, 2, 44, 121.07, 89.23, 4e-05, 58, 82.9, 52.01, 0.99996, 1, 58, 125.67, 56.38, 1, 1, 58, 201.88, 56.92, 1, 1, 58, 280.81, 44.54, 1, 1, 58, 358.62, 30.27, 1, 1, 58, 383.88, 24.94, 1, 1, 58, 401.82, 23.85, 1, 2, 58, 420.72, 25.29, 0.99779, 59, -35.3, 18.54, 0.00221, 2, 58, 441.14, 26.05, 0.84948, 59, -15.45, 23.41, 0.15052, 2, 58, 463.18, 22.88, 0.2801, 59, 6.77, 24.76, 0.7199, 2, 58, 483, 14.15, 0.00819, 59, 27.95, 20.21, 0.99181, 1, 59, 48.15, 12.56, 1, 1, 59, 71.89, 8.67, 1, 1, 59, 98.78, 5.96, 1, 1, 59, 133.23, 6.72, 1, 1, 59, 186.09, 8.66, 1, 1, 59, 316.77, 14.29, 1, 1, 59, 343.75, 17.44, 1, 1, 59, 360.96, 19.94, 1, 2, 59, 378.12, 23.17, 0.98163, 60, -18.97, 31.7, 0.01837, 2, 59, 394.46, 27.12, 0.70925, 60, -2.27, 29.74, 0.29075, 2, 59, 411.8, 32.22, 0.23179, 60, 15.76, 28.53, 0.76821, 2, 59, 433.78, 39.71, 0.00815, 60, 38.98, 27.94, 0.99185, 1, 60, 33.64, 39.23, 1, 1, 60, 69.72, 59.87, 1, 1, 60, 116.13, 34.98, 1, 1, 60, 133.57, 26.53, 1, 1, 60, 171.83, 62.42, 1, 1, 60, 201.2, 54.43, 1, 1, 60, 213.24, 47.66, 1, 1, 60, 210.42, 24.14, 1, 1, 60, 183.66, -23.62, 1, 1, 60, 165.05, -40.05, 1, 1, 60, 140.71, -41.8, 1, 1, 60, 98.79, -54.46, 1, 1, 60, 132.74, -73.26, 1, 1, 60, 118, -95.42, 1, 1, 60, 103.44, -108.47, 1, 1, 60, 45.38, -85.8, 1, 2, 59, 445.3, -58.9, 0.02874, 60, 15.65, -68.56, 0.97126, 2, 59, 416.94, -40.77, 0.26405, 60, -4.68, -41.73, 0.73595, 2, 59, 411.84, -34.37, 0.41782, 60, -7.25, -33.96, 0.58218, 2, 59, 400.84, -33.77, 0.71112, 60, -17.36, -29.59, 0.28888, 2, 59, 385.84, -34.25, 0.93486, 60, -31.6, -24.85, 0.06514, 2, 59, 369.32, -35.64, 0.99418, 60, -47.58, -20.43, 0.00582, 1, 59, 350.58, -39.43, 1, 1, 59, 323.97, -47.2, 1, 1, 59, 202.92, -92.9, 1, 1, 59, 171, -101, 1, 2, 58, 553.17, -126.97, 3e-05, 59, 125.18, -103.82, 0.99997, 2, 58, 511.64, -111.76, 0.02062, 59, 81.44, -97.32, 0.97938, 2, 58, 487.35, -98.82, 0.08475, 59, 55.03, -89.54, 0.91525, 2, 58, 467.69, -85.79, 0.24055, 59, 33.14, -80.76, 0.75945, 2, 58, 445.33, -85.33, 0.48833, 59, 11.15, -84.82, 0.51167, 2, 58, 422.67, -84.2, 0.71296, 59, -11.26, -88.3, 0.28704, 2, 58, 400.24, -82.21, 0.87504, 59, -33.64, -90.88, 0.12496, 2, 58, 379.76, -82.78, 0.95668, 59, -53.58, -95.58, 0.04332, 2, 58, 356.26, -84.5, 0.99265, 59, -76.24, -102.01, 0.00735, 1, 58, 293.7, -93.97, 1, 2, 42, 359.62, 114.85, 0.00675, 58, 204.5, -110.67, 0.99325, 2, 42, 320.83, 70.81, 0.0542, 58, 146.16, -116.95, 0.9458, 2, 42, 290.88, 38.48, 0.15919, 58, 102.23, -120.57, 0.84081, 2, 42, 264.56, 8.86, 0.3426, 58, 62.81, -124.64, 0.6574, 2, 42, 243.06, -19.61, 0.56018, 58, 27.73, -131.12, 0.43982, 2, 42, 224.25, -37.52, 0.70722, 58, 1.76, -131.63, 0.29278, 3, 42, 199.26, -51.57, 0.85367, 44, 160.26, -115.05, 0, 58, -26.16, -125.11, 0.14633, 3, 42, 167.12, -63.44, 0.97272, 44, 128.12, -126.92, 0.00014, 58, -57.88, -112.16, 0.02714, 2, 42, 136.04, -69.8, 0.99935, 44, 97.05, -133.28, 0.00065, 3, 42, 105.23, -70.99, 0.9982, 44, 66.23, -134.48, 0.00179, 58, -108.62, -75.94, 0, 3, 42, 69.5, -66.26, 0.99639, 44, 30.5, -129.74, 0.00359, 58, -131.78, -48.32, 2e-05, 2, 58, 376.79, 6.55, 0.98461, 61, -138.16, -412.6, 0.01539, 2, 58, 400.73, 9.62, 0.98595, 61, -126.81, -433.9, 0.01405, 2, 58, 418.48, 10.85, 0.98626, 61, -119.38, -450.06, 0.01374, 3, 58, 439.59, 11.84, 0.91161, 59, -14.1, 9.18, 0.07588, 61, -110.97, -469.45, 0.01251, 3, 58, 461.14, 8.52, 0.2082, 59, 7.68, 10.29, 0.78047, 61, -106.45, -490.79, 0.01133, 2, 59, 29.95, 8.36, 0.9897, 61, -104.83, -513.09, 0.0103, 2, 59, 48.76, 1.1, 0.9901, 61, -109.03, -532.8, 0.0099, 2, 59, 72.5, -5.97, 0.98998, 61, -112.27, -557.37, 0.01002, 3, 58, 512.61, -52.16, 0.01747, 59, 70.34, -38.75, 0.9694, 61, -144.97, -560.41, 0.01313, 3, 58, 492.71, -41.11, 0.04899, 59, 48.63, -31.94, 0.93593, 61, -141.68, -537.89, 0.01508, 3, 58, 475.39, -35.46, 0.12108, 59, 30.52, -29.92, 0.86283, 61, -142.54, -519.69, 0.01608, 3, 58, 457.53, -34.83, 0.34017, 59, 12.9, -32.9, 0.64468, 61, -148.27, -502.76, 0.01515, 3, 58, 436.82, -30.1, 0.72004, 59, -8.34, -32.45, 0.26468, 61, -151.18, -481.72, 0.01528, 3, 58, 397.43, -26.85, 0.96382, 59, -47.57, -37.23, 0.02089, 61, -162.09, -443.73, 0.01529, 3, 58, 418.1, -24.62, 0.91498, 59, -27.77, -30.86, 0.06969, 61, -152.68, -462.27, 0.01533, 3, 58, 371.23, -33.06, 0.98106, 59, -71.98, -48.6, 0.00418, 61, -177.17, -421.43, 0.01475, 3, 58, 532.11, -72.92, 0.00557, 59, 93.64, -55.14, 0.98293, 61, -157.48, -586, 0.01151, 2, 59, 139.71, -66.81, 0.98835, 61, -161.74, -633.33, 0.01165, 2, 59, 199.99, -60.3, 0.99142, 61, -145.79, -691.83, 0.00858, 1, 59, 287.48, -18.27, 1, 2, 59, 200.57, -16.23, 0.99065, 61, -102.18, -685.45, 0.00935, 2, 59, 142.6, -15.04, 0.98794, 61, -110.16, -628.02, 0.01206, 2, 59, 98.7, -13.41, 0.989, 61, -115.47, -584.41, 0.011, 2, 58, 296.32, -42.25, 0.98085, 61, -212.29, -354.63, 0.01915, 3, 42, 307.44, 144.51, 0.00495, 58, 186.05, -53.55, 0.97317, 61, -261.91, -255.5, 0.02188, 3, 42, 281.52, 113.52, 0.02465, 58, 146.01, -58.9, 0.95229, 61, -281.1, -219.95, 0.02306, 3, 42, 250.31, 76.02, 0.0971, 58, 97.66, -65.49, 0.88018, 61, -304.37, -177.06, 0.02272, 3, 42, 221.04, 47.34, 0.22812, 58, 56.7, -66.88, 0.74864, 61, -320.17, -139.26, 0.02324, 3, 42, 197.38, 26.31, 0.40973, 58, 25.05, -66.41, 0.56697, 61, -330.94, -109.49, 0.0233, 3, 42, 170.81, 3.31, 0.72217, 58, -10.07, -65.43, 0.25854, 61, -342.46, -76.29, 0.01928, 2, 42, 139.8, -10.32, 0.98061, 61, -343.64, -42.44, 0.01939, 2, 42, 105.09, -17.73, 0.98061, 61, -337.67, -7.45, 0.01939, 4, 42, 89.96, 40.33, 0.44222, 44, 50.96, -23.15, 0.2296, 58, -44.7, 16.48, 0.29966, 61, -278.13, -14.9, 0.02852, 4, 42, 119.42, 50.26, 0.17047, 44, 80.42, -13.23, 0.06793, 58, -16.28, 3.91, 0.73198, 61, -279.82, -45.94, 0.02962, 3, 44, 99.87, 3.24, 0.00493, 58, 9.19, 2.91, 0.96577, 61, -271.73, -70.11, 0.0293, 3, 44, 115.85, 23.73, 0.00312, 58, 34.81, 7.24, 0.96757, 61, -258.61, -92.54, 0.02931, 3, 44, 132.95, 43.31, 0.00074, 58, 60.65, 10.13, 0.9713, 61, -246.76, -115.67, 0.02797, 2, 58, 86.62, 14.07, 0.97334, 61, -233.88, -138.57, 0.02666, 2, 58, 135.57, 17.19, 0.97746, 61, -213.63, -183.25, 0.02254, 2, 58, 189.05, 19.36, 0.97943, 61, -192.66, -232.49, 0.02057, 2, 58, 297.29, 11.45, 0.98202, 61, -161.73, -336.52, 0.01798], "hull": 78, "edges": [12, 14, 18, 20, 20, 22, 26, 28, 40, 42, 42, 44, 58, 60, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 106, 108, 108, 110, 110, 112, 112, 114, 118, 120, 128, 130, 144, 146, 146, 148, 152, 154, 4, 6, 6, 8, 8, 10, 10, 12, 140, 142, 142, 144, 148, 150, 150, 152, 138, 140, 14, 16, 16, 18, 134, 136, 136, 138, 126, 128, 124, 126, 120, 122, 122, 124, 114, 116, 116, 118, 22, 24, 24, 26, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 130, 132, 132, 134, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 172, 174, 174, 176, 176, 178, 178, 180, 180, 184, 184, 182, 182, 186, 172, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 170, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 48, 50, 50, 52, 44, 46, 46, 48, 186, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 0, 154, 218, 0, 0, 2, 2, 4, 2, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 156], "width": 498, "height": 1103}}, "nose": {"nose": {"type": "mesh", "uvs": [0.51233, 0.02618, 0.78239, 0.06464, 0.79394, 0.26858, 0.8393, 0.49506, 0.94555, 0.64373, 0.95535, 0.76027, 0.87497, 0.85583, 0.68072, 0.94548, 0.50912, 0.9839, 0.36472, 0.91306, 0.12984, 0.90722, 0.03193, 0.80845, 0.0295, 0.68022, 0.16232, 0.58752, 0.17847, 0.34837, 0.19463, 0.10922, 0.51873, 0.25047, 0.52949, 0.4843, 0.44028, 0.62743, 0.43397, 0.75423, 0.59359, 0.84325, 0.7447, 0.74847, 0.74469, 0.63535, 0.29846, 0.59543, 0.23235, 0.75845, 0.32207, 0.31118, 0.70692, 0.47087, 0.67386, 0.24797], "triangles": [27, 0, 1, 16, 0, 27, 15, 0, 16, 27, 1, 2, 25, 15, 16, 14, 15, 25, 26, 27, 2, 17, 16, 27, 17, 27, 26, 25, 16, 17, 26, 2, 3, 23, 14, 25, 17, 23, 25, 13, 14, 23, 18, 23, 17, 22, 26, 3, 22, 3, 4, 21, 22, 4, 19, 23, 18, 17, 26, 22, 18, 17, 22, 24, 13, 23, 24, 23, 19, 12, 13, 24, 21, 4, 5, 11, 12, 24, 18, 21, 19, 21, 18, 22, 20, 19, 21, 6, 21, 5, 20, 21, 6, 10, 11, 24, 9, 24, 19, 9, 19, 20, 10, 24, 9, 7, 20, 6, 8, 9, 20, 8, 20, 7], "vertices": [2, 6, 50.92, -51.92, 0.97232, 66, -386.29, 30.73, 0.02768, 2, 6, 50.62, -60.46, 0.97509, 66, -386.59, 22.19, 0.02491, 2, 6, 41.82, -62.28, 0.97379, 66, -395.38, 20.37, 0.02621, 2, 6, 32.22, -65.3, 0.97188, 66, -404.98, 17.35, 0.02812, 2, 6, 26.31, -69.62, 0.97132, 66, -410.9, 13.03, 0.02868, 2, 6, 21.3, -70.76, 0.9714, 66, -415.91, 11.89, 0.0286, 2, 6, 16.75, -68.99, 0.9716, 66, -420.46, 13.66, 0.0284, 2, 6, 11.87, -63.7, 0.97469, 66, -425.34, 18.95, 0.02531, 2, 6, 9.33, -58.73, 0.97285, 66, -427.88, 23.92, 0.02715, 2, 6, 11.67, -53.8, 0.9713, 66, -425.54, 28.85, 0.0287, 2, 6, 10.73, -46.57, 0.96915, 66, -426.47, 36.07, 0.03085, 2, 6, 14.52, -42.87, 0.96863, 66, -422.68, 39.78, 0.03137, 2, 6, 20.08, -41.87, 0.96883, 66, -417.13, 40.78, 0.03117, 2, 6, 24.77, -45.26, 0.96971, 66, -412.43, 37.38, 0.03029, 2, 6, 35.24, -44.04, 0.9704, 66, -401.97, 38.61, 0.0296, 2, 6, 45.7, -42.81, 0.97113, 66, -391.51, 39.84, 0.02887, 2, 6, 41.21, -53.74, 0.9701, 66, -395.99, 28.91, 0.0299, 2, 6, 31.12, -55.75, 0.96754, 66, -406.09, 26.9, 0.03246, 2, 6, 24.45, -54.05, 0.96654, 66, -412.75, 28.6, 0.03346, 2, 6, 18.92, -54.77, 0.96656, 66, -418.29, 27.88, 0.03344, 2, 6, 15.86, -60.3, 0.96637, 66, -421.34, 22.35, 0.03363, 2, 6, 20.74, -64.23, 0.96703, 66, -416.46, 18.41, 0.03297, 2, 6, 25.65, -63.42, 0.96742, 66, -411.55, 19.23, 0.03258, 2, 6, 25.12, -49.48, 0.9683, 66, -412.08, 33.16, 0.0317, 2, 6, 17.71, -48.64, 0.96726, 66, -419.5, 34.01, 0.03274, 2, 6, 37.58, -48.16, 0.97, 66, -399.63, 34.49, 0.03, 2, 6, 32.6, -61.08, 0.96946, 66, -404.6, 21.57, 0.03054, 2, 6, 42.11, -58.46, 0.97148, 66, -395.1, 24.19, 0.02852], "hull": 16, "edges": [0, 30, 0, 2, 10, 12, 12, 14, 14, 16, 20, 22, 8, 10, 6, 8, 2, 4, 4, 6, 24, 26, 22, 24, 16, 18, 18, 20, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 40, 12, 46, 48, 26, 28, 28, 30, 44, 52, 52, 54], "width": 31, "height": 44}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": -12.91, "curve": [0.444, -12.91, 0.889, 16.69, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 16.69, "curve": [1.778, 16.7, 2.222, -12.91, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -12.91, "curve": [3.111, -12.92, 3.556, 16.69, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 16.69, "curve": [4.444, 16.7, 4.889, -12.9, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -12.91, "curve": [5.667, -12.93, 6, 28.67, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 28.68, "curve": [6.778, 28.68, 7.222, 7.89, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 7.88, "curve": [8.111, 7.88, 8.556, 28.67, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 28.68, "curve": [9.444, 28.69, 9.889, -12.91, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -12.91, "curve": [10.778, -12.92, 11.222, 16.69, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 16.69, "curve": [12.111, 16.7, 12.556, -12.91, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -12.91}]}, "ALL2": {"translate": [{"y": -17.88, "curve": [0.444, 0, 0.889, 0, 0.444, -17.88, 0.889, 23.23]}, {"time": 1.3333, "y": 23.24, "curve": [1.778, 0, 2.222, 0, 1.778, 23.25, 2.222, -17.87]}, {"time": 2.6667, "y": -17.88, "curve": [3.111, 0, 3.556, 0, 3.111, -17.89, 3.556, 23.23]}, {"time": 4, "y": 23.24, "curve": [4.444, 0, 4.889, 0, 4.444, 23.25, 4.889, -17.86]}, {"time": 5.3333, "y": -17.88, "curve": [5.667, 0, 6, 0, 5.667, -17.89, 6, 23.24]}, {"time": 6.3333, "y": 23.24, "curve": [6.778, 0, 7.222, 0, 6.778, 23.25, 7.222, 2.69]}, {"time": 7.6667, "y": 2.68, "curve": [8.111, 0, 8.556, 0, 8.111, 2.68, 8.556, 23.23]}, {"time": 9, "y": 23.24, "curve": [9.444, 0, 9.889, 0, 9.444, 23.25, 9.889, -17.87]}, {"time": 10.3333, "y": -17.88, "curve": [10.778, 0, 11.222, 0, 10.778, -17.89, 11.222, 23.23]}, {"time": 11.6667, "y": 23.24, "curve": [12.111, 0, 12.556, 0, 12.111, 23.25, 12.556, -17.88]}, {"time": 13, "y": -17.88}]}, "body": {"rotate": [{"value": -2.36, "curve": [0.444, -2.36, 0.889, 4.03]}, {"time": 1.3333, "value": 4.03, "curve": [1.778, 4.03, 2.222, -2.36]}, {"time": 2.6667, "value": -2.36, "curve": [3.111, -2.36, 3.556, 4.03]}, {"time": 4, "value": 4.03, "curve": [4.444, 4.03, 4.889, -2.35]}, {"time": 5.3333, "value": -2.36, "curve": [5.667, -2.36, 6, 9.88]}, {"time": 6.3333, "value": 9.88, "curve": [6.778, 9.88, 7.222, 3.76]}, {"time": 7.6667, "value": 3.76, "curve": [8.111, 3.76, 8.556, 9.88]}, {"time": 9, "value": 9.88, "curve": [9.444, 9.89, 9.889, -2.36]}, {"time": 10.3333, "value": -2.36, "curve": [10.778, -2.36, 11.222, 4.03]}, {"time": 11.6667, "value": 4.03, "curve": [12.111, 4.03, 12.556, -2.36]}, {"time": 13, "value": -2.36}], "translate": [{"x": 0.29, "y": -12.05, "curve": [0.057, 0.32, 0.112, 0.35, 0.057, -12.8, 0.112, -13.36]}, {"time": 0.1667, "x": 0.35, "y": -13.36, "curve": [0.611, 0.35, 1.056, -0.86, 0.611, -13.36, 1.056, 14.51]}, {"time": 1.5, "x": -0.86, "y": 14.52, "curve": [1.944, -0.86, 2.389, 0.35, 1.944, 14.52, 2.389, -13.36]}, {"time": 2.8333, "x": 0.35, "y": -13.36, "curve": [3.278, 0.35, 3.722, -0.86, 3.278, -13.37, 3.722, 14.51]}, {"time": 4.1667, "x": -0.86, "y": 14.52, "curve": [4.611, -0.86, 5.056, 0.35, 4.611, 14.52, 5.056, -13.35]}, {"time": 5.5, "x": 0.35, "y": -13.36, "curve": [5.833, 0.35, 6.167, -0.86, 5.833, -13.37, 6.167, 14.51]}, {"time": 6.5, "x": -0.86, "y": 14.52, "curve": [6.944, -0.86, 7.389, -0.26, 6.944, 14.52, 7.389, 0.58]}, {"time": 7.8333, "x": -0.26, "y": 0.58, "curve": [8.278, -0.26, 8.722, -0.86, 8.278, 0.57, 8.722, 14.51]}, {"time": 9.1667, "x": -0.86, "y": 14.52, "curve": [9.611, -0.86, 10.056, 0.35, 9.611, 14.52, 10.056, -13.36]}, {"time": 10.5, "x": 0.35, "y": -13.36, "curve": [10.944, 0.35, 11.389, -0.86, 10.944, -13.37, 11.389, 14.51]}, {"time": 11.8333, "x": -0.86, "y": 14.52, "curve": [12.223, -0.86, 12.613, 0.06, 12.223, 14.52, 12.613, -6.77]}, {"time": 13, "x": 0.29, "y": -12.05}], "scale": [{"y": 1.056, "curve": [0.057, 1, 0.112, 1, 0.057, 1.058, 0.112, 1.06]}, {"time": 0.1667, "y": 1.06, "curve": [0.611, 1, 1.056, 1, 0.611, 1.06, 1.056, 0.961]}, {"time": 1.5, "y": 0.961, "curve": [1.944, 1, 2.389, 1, 1.944, 0.961, 2.389, 1.06]}, {"time": 2.8333, "y": 1.06, "curve": [3.278, 1, 3.722, 1, 3.278, 1.06, 3.722, 0.961]}, {"time": 4.1667, "y": 0.961, "curve": [4.611, 1, 5.056, 1, 4.611, 0.961, 5.056, 1.06]}, {"time": 5.5, "y": 1.06, "curve": [5.833, 1, 6.167, 1, 5.833, 1.06, 6.167, 0.961]}, {"time": 6.5, "y": 0.961, "curve": [6.944, 1, 7.389, 1, 6.944, 0.961, 7.389, 1.011]}, {"time": 7.8333, "y": 1.011, "curve": [8.278, 1, 8.722, 1, 8.278, 1.011, 8.722, 0.961]}, {"time": 9.1667, "y": 0.961, "curve": [9.611, 1, 10.056, 1, 9.611, 0.961, 10.056, 1.06]}, {"time": 10.5, "y": 1.06, "curve": [10.944, 1, 11.389, 1, 10.944, 1.06, 11.389, 0.961]}, {"time": 11.8333, "y": 0.961, "curve": [12.223, 1, 12.613, 1, 12.223, 0.961, 12.613, 1.037]}, {"time": 13, "y": 1.056}]}, "body2": {"rotate": [{"value": 1.56, "curve": [0.057, 1.66, 0.112, 1.73]}, {"time": 0.1667, "value": 1.73, "curve": [0.611, 1.73, 1.056, -2.02]}, {"time": 1.5, "value": -2.02, "curve": [1.944, -2.02, 2.389, 1.73]}, {"time": 2.8333, "value": 1.73, "curve": [3.278, 1.74, 3.722, -2.02]}, {"time": 4.1667, "value": -2.02, "curve": [4.611, -2.02, 5.056, 1.73]}, {"time": 5.5, "value": 1.73, "curve": [5.833, 1.74, 6.167, -5.71]}, {"time": 6.5, "value": -5.71, "curve": [6.944, -5.71, 7.389, -1.99]}, {"time": 7.8333, "value": -1.99, "curve": [8.278, -1.99, 8.722, -5.71]}, {"time": 9.1667, "value": -5.71, "curve": [9.611, -5.71, 10.056, 1.73]}, {"time": 10.5, "value": 1.73, "curve": [10.944, 1.74, 11.389, -2.02]}, {"time": 11.8333, "value": -2.02, "curve": [12.223, -2.02, 12.613, 0.85]}, {"time": 13, "value": 1.56}], "translate": [{"x": -6.57, "y": -1.93, "curve": [0.114, -8.04, 0.224, -9.13, 0.114, -2.27, 0.224, -2.53]}, {"time": 0.3333, "x": -9.13, "y": -2.53, "curve": [0.778, -9.13, 1.222, 6.87, 0.778, -2.53, 1.222, 1.26]}, {"time": 1.6667, "x": 6.87, "y": 1.26, "curve": [2.111, 6.88, 2.556, -9.13, 2.111, 1.26, 2.556, -2.53]}, {"time": 3, "x": -9.13, "y": -2.53, "curve": [3.444, -9.14, 3.889, 6.87, 3.444, -2.54, 3.889, 1.26]}, {"time": 4.3333, "x": 6.87, "y": 1.26, "curve": [4.778, 6.88, 5.222, -9.13, 4.778, 1.26, 5.222, -2.53]}, {"time": 5.6667, "x": -9.13, "y": -2.53, "curve": [6, -9.14, 6.333, 6.87, 6, -2.54, 6.333, 1.26]}, {"time": 6.6667, "x": 6.87, "y": 1.26, "curve": [7.111, 6.88, 7.556, -1.13, 7.111, 1.26, 7.556, -0.64]}, {"time": 8, "x": -1.13, "y": -0.64, "curve": [8.444, -1.13, 8.889, 6.87, 8.444, -0.64, 8.889, 1.26]}, {"time": 9.3333, "x": 6.87, "y": 1.26, "curve": [9.778, 6.88, 10.222, -9.13, 9.778, 1.26, 10.222, -2.53]}, {"time": 10.6667, "x": -9.13, "y": -2.53, "curve": [11.111, -9.14, 11.556, 6.87, 11.111, -2.54, 11.556, 1.26]}, {"time": 12, "x": 6.87, "y": 1.26, "curve": [12.335, 6.88, 12.67, -2.09, 12.335, 1.26, 12.67, -0.86]}, {"time": 13, "x": -6.57, "y": -1.93}], "scale": [{"x": 1.004, "y": 1.004, "curve": [0.114, 1.002, 0.224, 1, 0.114, 1.002, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.026, 0.778, 1, 1.222, 1.026]}, {"time": 1.6667, "x": 1.026, "y": 1.026, "curve": [2.111, 1.026, 2.556, 1, 2.111, 1.026, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.026, 3.444, 1, 3.889, 1.026]}, {"time": 4.3333, "x": 1.026, "y": 1.026, "curve": [4.778, 1.026, 5.222, 1, 4.778, 1.026, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.026, 6, 1, 6.333, 1.026]}, {"time": 6.6667, "x": 1.026, "y": 1.026, "curve": [7.111, 1.026, 7.556, 1.013, 7.111, 1.026, 7.556, 1.013]}, {"time": 8, "x": 1.013, "y": 1.013, "curve": [8.444, 1.013, 8.889, 1.026, 8.444, 1.013, 8.889, 1.026]}, {"time": 9.3333, "x": 1.026, "y": 1.026, "curve": [9.778, 1.026, 10.222, 1, 9.778, 1.026, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.026, 11.111, 1, 11.556, 1.026]}, {"time": 12, "x": 1.026, "y": 1.026, "curve": [12.335, 1.026, 12.67, 1.012, 12.335, 1.026, 12.67, 1.012]}, {"time": 13, "x": 1.004, "y": 1.004}]}, "neck": {"rotate": [{"value": -0.5, "curve": [0.168, -1.56, 0.334, -2.43]}, {"time": 0.5, "value": -2.43, "curve": [0.944, -2.43, 1.389, 3.64]}, {"time": 1.8333, "value": 3.64, "curve": [2.278, 3.64, 2.722, -2.43]}, {"time": 3.1667, "value": -2.43, "curve": [3.611, -2.43, 4.056, 3.64]}, {"time": 4.5, "value": 3.64, "curve": [4.944, 3.64, 5.389, -2.43]}, {"time": 5.8333, "value": -2.43, "curve": [6.167, -2.43, 6.5, 10.38]}, {"time": 6.8333, "value": 10.38, "curve": [7.278, 10.38, 7.722, 3.98]}, {"time": 8.1667, "value": 3.98, "curve": [8.611, 3.98, 9.056, 10.38]}, {"time": 9.5, "value": 10.38, "curve": [9.944, 10.39, 10.389, -2.43]}, {"time": 10.8333, "value": -2.43, "curve": [11.278, -2.43, 11.722, 3.64]}, {"time": 12.1667, "value": 3.64, "curve": [12.445, 3.64, 12.724, 1.28]}, {"time": 13, "value": -0.5}]}, "head": {"rotate": [{"value": 0.6, "curve": [0.225, -0.9, 0.446, -2.43]}, {"time": 0.6667, "value": -2.43, "curve": [1.111, -2.43, 1.556, 3.64]}, {"time": 2, "value": 3.64, "curve": [2.444, 3.64, 2.889, -2.43]}, {"time": 3.3333, "value": -2.43, "curve": [3.778, -2.43, 4.222, 3.64]}, {"time": 4.6667, "value": 3.64, "curve": [5.111, 3.64, 5.556, -2.43]}, {"time": 6, "value": -2.43, "curve": [6.333, -2.43, 6.667, 10.38]}, {"time": 7, "value": 10.38, "curve": [7.444, 10.38, 7.889, 3.98]}, {"time": 8.3333, "value": 3.98, "curve": [8.778, 3.98, 9.222, 10.38]}, {"time": 9.6667, "value": 10.38, "curve": [10.111, 10.39, 10.556, -2.43]}, {"time": 11, "value": -2.43, "curve": [11.444, -2.43, 11.889, 3.64]}, {"time": 12.3333, "value": 3.64, "curve": [12.557, 3.64, 12.781, 2.13]}, {"time": 13, "value": 0.6}]}, "earring": {"rotate": [{"value": 10.74, "curve": [0.056, 12.81, 0.111, 14.27]}, {"time": 0.1667, "value": 14.27, "curve": [0.389, 14.28, 0.611, -7.8]}, {"time": 0.8333, "value": -7.8, "curve": [1.056, -7.8, 1.278, 14.27]}, {"time": 1.5, "value": 14.27, "curve": [1.722, 14.28, 1.944, -7.8]}, {"time": 2.1667, "value": -7.8, "curve": [2.389, -7.81, 2.611, 14.27]}, {"time": 2.8333, "value": 14.27, "curve": [3.056, 14.28, 3.278, -7.8]}, {"time": 3.5, "value": -7.8, "curve": [3.722, -7.81, 3.944, 14.27]}, {"time": 4.1667, "value": 14.27, "curve": [4.389, 14.28, 4.611, -7.8]}, {"time": 4.8333, "value": -7.8, "curve": [5.056, -7.81, 5.278, 14.27]}, {"time": 5.5, "value": 14.27, "curve": [5.722, 14.28, 5.944, -7.79]}, {"time": 6.1667, "value": -7.8, "curve": [6.333, -7.81, 6.5, 14.27]}, {"time": 6.6667, "value": 14.27, "curve": [6.833, 14.28, 7, -7.8]}, {"time": 7.1667, "value": -7.8, "curve": [7.389, -7.81, 7.611, 14.27]}, {"time": 7.8333, "value": 14.27, "curve": [8.056, 14.28, 8.278, -7.8]}, {"time": 8.5, "value": -7.8, "curve": [8.722, -7.81, 8.944, 14.27]}, {"time": 9.1667, "value": 14.27, "curve": [9.389, 14.28, 9.611, -7.8]}, {"time": 9.8333, "value": -7.8, "curve": [10.056, -7.81, 10.278, 14.27]}, {"time": 10.5, "value": 14.27, "curve": [10.722, 14.28, 10.944, -7.8]}, {"time": 11.1667, "value": -7.8, "curve": [11.389, -7.81, 11.611, 14.27]}, {"time": 11.8333, "value": 14.27, "curve": [12.056, 14.28, 12.278, -7.8]}, {"time": 12.5, "value": -7.8, "curve": [12.667, -7.81, 12.833, 4.53]}, {"time": 13, "value": 10.74}]}, "hair_FR": {"rotate": [{"value": -0.94, "curve": [0.279, 1.56, 0.556, 4.88]}, {"time": 0.8333, "value": 4.88, "curve": [1.278, 4.88, 1.722, -3.64]}, {"time": 2.1667, "value": -3.65, "curve": [2.611, -3.65, 3.056, 4.88]}, {"time": 3.5, "value": 4.88, "curve": [3.944, 4.88, 4.389, -3.64]}, {"time": 4.8333, "value": -3.65, "curve": [5.278, -3.65, 5.722, 4.88]}, {"time": 6.1667, "value": 4.88, "curve": [6.5, 4.88, 6.833, -3.64]}, {"time": 7.1667, "value": -3.65, "curve": [7.611, -3.65, 8.056, 0.62]}, {"time": 8.5, "value": 0.62, "curve": [8.944, 0.62, 9.389, -3.64]}, {"time": 9.8333, "value": -3.65, "curve": [10.278, -3.65, 10.722, 4.88]}, {"time": 11.1667, "value": 4.88, "curve": [11.611, 4.88, 12.056, -3.64]}, {"time": 12.5, "value": -3.65, "curve": [12.667, -3.65, 12.835, -2.44]}, {"time": 13, "value": -0.94}]}, "hair_F": {"rotate": [{"value": 2.17, "curve": [0.279, -0.33, 0.556, -3.65]}, {"time": 0.8333, "value": -3.65, "curve": [1.278, -3.65, 1.722, 4.88]}, {"time": 2.1667, "value": 4.88, "curve": [2.611, 4.88, 3.056, -3.64]}, {"time": 3.5, "value": -3.65, "curve": [3.944, -3.65, 4.389, 4.88]}, {"time": 4.8333, "value": 4.88, "curve": [5.278, 4.88, 5.722, -3.64]}, {"time": 6.1667, "value": -3.65, "curve": [6.5, -3.65, 6.833, 4.88]}, {"time": 7.1667, "value": 4.88, "curve": [7.611, 4.88, 8.056, 0.62]}, {"time": 8.5, "value": 0.62, "curve": [8.944, 0.62, 9.389, 4.88]}, {"time": 9.8333, "value": 4.88, "curve": [10.278, 4.88, 10.722, -3.64]}, {"time": 11.1667, "value": -3.65, "curve": [11.611, -3.65, 12.056, 4.88]}, {"time": 12.5, "value": 4.88, "curve": [12.667, 4.88, 12.835, 3.68]}, {"time": 13, "value": 2.17}]}, "hair_FL": {"rotate": [{"value": 2.17, "curve": [0.279, -0.33, 0.556, -3.65]}, {"time": 0.8333, "value": -3.65, "curve": [1.278, -3.65, 1.722, 4.88]}, {"time": 2.1667, "value": 4.88, "curve": [2.611, 4.88, 3.056, -3.64]}, {"time": 3.5, "value": -3.65, "curve": [3.944, -3.65, 4.389, 4.88]}, {"time": 4.8333, "value": 4.88, "curve": [5.278, 4.88, 5.722, -3.64]}, {"time": 6.1667, "value": -3.65, "curve": [6.5, -3.65, 6.833, 4.88]}, {"time": 7.1667, "value": 4.88, "curve": [7.611, 4.88, 8.056, 0.62]}, {"time": 8.5, "value": 0.62, "curve": [8.944, 0.62, 9.389, 4.88]}, {"time": 9.8333, "value": 4.88, "curve": [10.278, 4.88, 10.722, -3.64]}, {"time": 11.1667, "value": -3.65, "curve": [11.611, -3.65, 12.056, 4.88]}, {"time": 12.5, "value": 4.88, "curve": [12.667, 4.88, 12.835, 3.68]}, {"time": 13, "value": 2.17}]}, "hair_B2": {"rotate": [{"value": -3.65, "curve": [0.444, -3.65, 0.889, 4.88]}, {"time": 1.3333, "value": 4.88, "curve": [1.778, 4.88, 2.222, -3.64]}, {"time": 2.6667, "value": -3.65, "curve": [3.111, -3.65, 3.556, 4.88]}, {"time": 4, "value": 4.88, "curve": [4.444, 4.88, 4.889, -3.64]}, {"time": 5.3333, "value": -3.65, "curve": [5.667, -3.65, 6, 4.88]}, {"time": 6.3333, "value": 4.88, "curve": [6.778, 4.88, 7.222, 0.62]}, {"time": 7.6667, "value": 0.62, "curve": [8.111, 0.62, 8.556, 4.88]}, {"time": 9, "value": 4.88, "curve": [9.444, 4.88, 9.889, -3.64]}, {"time": 10.3333, "value": -3.65, "curve": [10.778, -3.65, 11.222, 4.88]}, {"time": 11.6667, "value": 4.88, "curve": [12.111, 4.88, 12.556, -3.65]}, {"time": 13, "value": -3.65}]}, "sh_R": {"translate": [{"x": -2.7, "y": 1.18, "curve": [0.114, -3.52, 0.224, -4.13, 0.114, 1.49, 0.224, 1.72]}, {"time": 0.3333, "x": -4.13, "y": 1.72, "curve": [0.778, -4.13, 1.222, 4.81, 0.778, 1.72, 1.222, -1.64]}, {"time": 1.6667, "x": 4.81, "y": -1.64, "curve": [2.111, 4.81, 2.556, -4.12, 2.111, -1.64, 2.556, 1.72]}, {"time": 3, "x": -4.13, "y": 1.72, "curve": [3.444, -4.13, 3.889, 4.81, 3.444, 1.72, 3.889, -1.64]}, {"time": 4.3333, "x": 4.81, "y": -1.64, "curve": [4.778, 4.81, 5.222, -4.12, 4.778, -1.64, 5.222, 1.72]}, {"time": 5.6667, "x": -4.13, "y": 1.72, "curve": [6, -4.13, 6.333, 4.81, 6, 1.72, 6.333, -1.64]}, {"time": 6.6667, "x": 4.81, "y": -1.64, "curve": [7.111, 4.81, 7.556, 0.34, 7.111, -1.64, 7.556, 0.04]}, {"time": 8, "x": 0.34, "y": 0.04, "curve": [8.444, 0.34, 8.889, 4.81, 8.444, 0.04, 8.889, -1.64]}, {"time": 9.3333, "x": 4.81, "y": -1.64, "curve": [9.778, 4.81, 10.222, -4.12, 9.778, -1.64, 10.222, 1.72]}, {"time": 10.6667, "x": -4.13, "y": 1.72, "curve": [11.111, -4.13, 11.556, 4.81, 11.111, 1.72, 11.556, -1.64]}, {"time": 12, "x": 4.81, "y": -1.64, "curve": [12.335, 4.81, 12.67, -0.19, 12.335, -1.64, 12.67, 0.24]}, {"time": 13, "x": -2.7, "y": 1.18}]}, "sh_L": {"translate": [{"x": -3.95, "y": 1.72, "curve": [0.114, -5.08, 0.224, -5.92, 0.114, 2.15, 0.224, 2.47]}, {"time": 0.3333, "x": -5.92, "y": 2.47, "curve": [0.778, -5.92, 1.222, 6.36, 0.778, 2.47, 1.222, -2.17]}, {"time": 1.6667, "x": 6.36, "y": -2.17, "curve": [2.111, 6.36, 2.556, -5.92, 2.111, -2.17, 2.556, 2.46]}, {"time": 3, "x": -5.92, "y": 2.47, "curve": [3.444, -5.92, 3.889, 6.36, 3.444, 2.47, 3.889, -2.17]}, {"time": 4.3333, "x": 6.36, "y": -2.17, "curve": [4.778, 6.36, 5.222, -5.91, 4.778, -2.17, 5.222, 2.46]}, {"time": 5.6667, "x": -5.92, "y": 2.47, "curve": [6, -5.92, 6.333, 6.36, 6, 2.47, 6.333, -2.17]}, {"time": 6.6667, "x": 6.36, "y": -2.17, "curve": [7.111, 6.36, 7.556, 0.22, 7.111, -2.17, 7.556, 0.15]}, {"time": 8, "x": 0.22, "y": 0.15, "curve": [8.444, 0.22, 8.889, 6.36, 8.444, 0.15, 8.889, -2.17]}, {"time": 9.3333, "x": 6.36, "y": -2.17, "curve": [9.778, 6.36, 10.222, -5.92, 9.778, -2.17, 10.222, 2.46]}, {"time": 10.6667, "x": -5.92, "y": 2.47, "curve": [11.111, -5.92, 11.556, 6.36, 11.111, 2.47, 11.556, -2.17]}, {"time": 12, "x": 6.36, "y": -2.17, "curve": [12.335, 6.36, 12.67, -0.52, 12.335, -2.17, 12.67, 0.42]}, {"time": 13, "x": -3.95, "y": 1.72}]}, "RU_R": {"translate": [{"x": -10.88, "curve": [0.168, -24.02, 0.334, -34.81, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -34.81, "curve": [0.944, -34.81, 1.389, 40.42, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 40.44, "curve": [2.278, 40.46, 2.722, -34.79, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -34.81, "curve": [3.611, -34.83, 4.056, 40.42, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 40.44, "curve": [4.944, 40.46, 5.389, -34.78, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -34.81, "curve": [6.167, -34.83, 6.5, 40.42, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 40.44, "curve": [7.278, 40.46, 7.722, -34.79, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -34.81, "curve": [8.611, -34.83, 9.056, 40.42, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 40.44, "curve": [9.944, 40.46, 10.389, -34.79, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -34.81, "curve": [11.278, -34.83, 11.722, 40.42, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 40.44, "curve": [12.445, 40.45, 12.724, 11.19, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -10.88}], "scale": [{"x": 1.082, "y": 0.955, "curve": [0.167, 1.038, 0.333, 0.951, 0.167, 0.988, 0.333, 1.055]}, {"time": 0.5, "x": 0.951, "y": 1.055, "curve": [0.722, 0.951, 0.944, 1.106, 0.722, 1.055, 0.944, 0.936]}, {"time": 1.1667, "x": 1.106, "y": 0.936, "curve": [1.389, 1.106, 1.611, 0.951, 1.389, 0.936, 1.611, 1.055]}, {"time": 1.8333, "x": 0.951, "y": 1.055, "curve": [2.056, 0.951, 2.278, 1.106, 2.056, 1.055, 2.278, 0.936]}, {"time": 2.5, "x": 1.106, "y": 0.936, "curve": [2.722, 1.106, 2.944, 0.951, 2.722, 0.936, 2.944, 1.055]}, {"time": 3.1667, "x": 0.951, "y": 1.055, "curve": [3.389, 0.951, 3.611, 1.106, 3.389, 1.055, 3.611, 0.936]}, {"time": 3.8333, "x": 1.106, "y": 0.936, "curve": [4.056, 1.106, 4.278, 0.951, 4.056, 0.936, 4.278, 1.055]}, {"time": 4.5, "x": 0.951, "y": 1.055, "curve": [4.722, 0.951, 4.944, 1.106, 4.722, 1.055, 4.944, 0.936]}, {"time": 5.1667, "x": 1.106, "y": 0.936, "curve": [5.389, 1.106, 5.611, 0.951, 5.389, 0.936, 5.611, 1.055]}, {"time": 5.8333, "x": 0.951, "y": 1.055, "curve": [6, 0.951, 6.167, 1.106, 6, 1.055, 6.167, 0.936]}, {"time": 6.3333, "x": 1.106, "y": 0.936, "curve": [6.5, 1.106, 6.667, 0.951, 6.5, 0.936, 6.667, 1.055]}, {"time": 6.8333, "x": 0.951, "y": 1.055, "curve": [7.056, 0.951, 7.278, 1.106, 7.056, 1.055, 7.278, 0.936]}, {"time": 7.5, "x": 1.106, "y": 0.936, "curve": [7.722, 1.106, 7.944, 0.951, 7.722, 0.936, 7.944, 1.055]}, {"time": 8.1667, "x": 0.951, "y": 1.055, "curve": [8.389, 0.951, 8.611, 1.106, 8.389, 1.055, 8.611, 0.936]}, {"time": 8.8333, "x": 1.106, "y": 0.936, "curve": [9.056, 1.106, 9.278, 0.951, 9.056, 0.936, 9.278, 1.055]}, {"time": 9.5, "x": 0.951, "y": 1.055, "curve": [9.722, 0.951, 9.944, 1.106, 9.722, 1.055, 9.944, 0.936]}, {"time": 10.1667, "x": 1.106, "y": 0.936, "curve": [10.389, 1.106, 10.611, 0.951, 10.389, 0.936, 10.611, 1.055]}, {"time": 10.8333, "x": 0.951, "y": 1.055, "curve": [11.056, 0.951, 11.278, 1.106, 11.056, 1.055, 11.278, 0.936]}, {"time": 11.5, "x": 1.106, "y": 0.936, "curve": [11.722, 1.106, 11.944, 0.951, 11.722, 0.936, 11.944, 1.055]}, {"time": 12.1667, "x": 0.951, "y": 1.055, "curve": [12.389, 0.951, 12.611, 1.106, 12.389, 1.055, 12.611, 0.936]}, {"time": 12.8333, "x": 1.106, "y": 0.936, "curve": [12.889, 1.106, 12.944, 1.096, 12.889, 0.936, 12.944, 0.944]}, {"time": 13, "x": 1.082, "y": 0.955}]}, "RU_R2": {"translate": [{"x": 2.65, "curve": [0.225, -13.3, 0.446, -29.46, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -29.46, "curve": [1.111, -29.46, 1.556, 34.73, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 34.75, "curve": [2.444, 34.76, 2.889, -29.44, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -29.46, "curve": [3.778, -29.48, 4.222, 34.73, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 34.75, "curve": [5.111, 34.76, 5.556, -29.44, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -29.46, "curve": [6.333, -29.48, 6.667, 34.74, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 34.75, "curve": [7.444, 34.76, 7.889, -29.44, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -29.46, "curve": [8.778, -29.48, 9.222, 34.73, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 34.75, "curve": [10.111, 34.76, 10.556, -29.44, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -29.46, "curve": [11.444, -29.48, 11.889, 34.73, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 34.75, "curve": [12.557, 34.76, 12.781, 18.81, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 2.65}], "scale": [{"x": 1.106, "y": 0.936, "curve": [0.222, 1.106, 0.444, 0.951, 0.222, 0.936, 0.444, 1.055]}, {"time": 0.6667, "x": 0.951, "y": 1.055, "curve": [0.889, 0.951, 1.111, 1.106, 0.889, 1.055, 1.111, 0.936]}, {"time": 1.3333, "x": 1.106, "y": 0.936, "curve": [1.556, 1.106, 1.778, 0.951, 1.556, 0.936, 1.778, 1.055]}, {"time": 2, "x": 0.951, "y": 1.055, "curve": [2.222, 0.951, 2.444, 1.106, 2.222, 1.055, 2.444, 0.936]}, {"time": 2.6667, "x": 1.106, "y": 0.936, "curve": [2.889, 1.106, 3.111, 0.951, 2.889, 0.936, 3.111, 1.055]}, {"time": 3.3333, "x": 0.951, "y": 1.055, "curve": [3.556, 0.951, 3.778, 1.106, 3.556, 1.055, 3.778, 0.936]}, {"time": 4, "x": 1.106, "y": 0.936, "curve": [4.222, 1.106, 4.444, 0.951, 4.222, 0.936, 4.444, 1.055]}, {"time": 4.6667, "x": 0.951, "y": 1.055, "curve": [4.889, 0.951, 5.111, 1.106, 4.889, 1.055, 5.111, 0.936]}, {"time": 5.3333, "x": 1.106, "y": 0.936, "curve": [5.556, 1.106, 5.778, 0.951, 5.556, 0.936, 5.778, 1.055]}, {"time": 6, "x": 0.951, "y": 1.055, "curve": [6.167, 0.951, 6.333, 1.106, 6.167, 1.055, 6.333, 0.936]}, {"time": 6.5, "x": 1.106, "y": 0.936, "curve": [6.667, 1.106, 6.833, 0.951, 6.667, 0.936, 6.833, 1.055]}, {"time": 7, "x": 0.951, "y": 1.055, "curve": [7.222, 0.951, 7.444, 1.106, 7.222, 1.055, 7.444, 0.936]}, {"time": 7.6667, "x": 1.106, "y": 0.936, "curve": [7.889, 1.106, 8.111, 0.951, 7.889, 0.936, 8.111, 1.055]}, {"time": 8.3333, "x": 0.951, "y": 1.055, "curve": [8.556, 0.951, 8.778, 1.106, 8.556, 1.055, 8.778, 0.936]}, {"time": 9, "x": 1.106, "y": 0.936, "curve": [9.222, 1.106, 9.444, 0.951, 9.222, 0.936, 9.444, 1.055]}, {"time": 9.6667, "x": 0.951, "y": 1.055, "curve": [9.889, 0.951, 10.111, 1.106, 9.889, 1.055, 10.111, 0.936]}, {"time": 10.3333, "x": 1.106, "y": 0.936, "curve": [10.556, 1.106, 10.778, 0.951, 10.556, 0.936, 10.778, 1.055]}, {"time": 11, "x": 0.951, "y": 1.055, "curve": [11.222, 0.951, 11.444, 1.106, 11.222, 1.055, 11.444, 0.936]}, {"time": 11.6667, "x": 1.106, "y": 0.936, "curve": [11.889, 1.106, 12.111, 0.951, 11.889, 0.936, 12.111, 1.055]}, {"time": 12.3333, "x": 0.951, "y": 1.055, "curve": [12.556, 0.951, 12.778, 1.106, 12.556, 1.055, 12.778, 0.936]}, {"time": 13, "x": 1.106, "y": 0.936}]}, "RU_R3": {"translate": [{"x": 13.79, "curve": [0.279, -1.68, 0.556, -22.26, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -22.26, "curve": [1.278, -22.26, 1.722, 30.57, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 30.59, "curve": [2.611, 30.6, 3.056, -22.24, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -22.26, "curve": [3.944, -22.27, 4.389, 30.57, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 30.59, "curve": [5.278, 30.6, 5.722, -22.24, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -22.26, "curve": [6.5, -22.27, 6.833, 30.58, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 30.59, "curve": [7.611, 30.6, 8.056, -22.24, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -22.26, "curve": [8.944, -22.27, 9.389, 30.57, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 30.59, "curve": [10.278, 30.6, 10.722, -22.24, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -22.26, "curve": [11.611, -22.27, 12.056, 30.57, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 30.59, "curve": [12.667, 30.59, 12.835, 23.13, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 13.79}], "scale": [{"x": 1.082, "y": 0.955, "curve": [0.056, 1.096, 0.111, 1.106, 0.056, 0.944, 0.111, 0.936]}, {"time": 0.1667, "x": 1.106, "y": 0.936, "curve": [0.389, 1.106, 0.611, 0.951, 0.389, 0.936, 0.611, 1.055]}, {"time": 0.8333, "x": 0.951, "y": 1.055, "curve": [1.056, 0.951, 1.278, 1.106, 1.056, 1.055, 1.278, 0.936]}, {"time": 1.5, "x": 1.106, "y": 0.936, "curve": [1.722, 1.106, 1.944, 0.951, 1.722, 0.936, 1.944, 1.055]}, {"time": 2.1667, "x": 0.951, "y": 1.055, "curve": [2.389, 0.951, 2.611, 1.106, 2.389, 1.055, 2.611, 0.936]}, {"time": 2.8333, "x": 1.106, "y": 0.936, "curve": [3.056, 1.106, 3.278, 0.951, 3.056, 0.936, 3.278, 1.055]}, {"time": 3.5, "x": 0.951, "y": 1.055, "curve": [3.722, 0.951, 3.944, 1.106, 3.722, 1.055, 3.944, 0.936]}, {"time": 4.1667, "x": 1.106, "y": 0.936, "curve": [4.389, 1.106, 4.611, 0.951, 4.389, 0.936, 4.611, 1.055]}, {"time": 4.8333, "x": 0.951, "y": 1.055, "curve": [5.056, 0.951, 5.278, 1.106, 5.056, 1.055, 5.278, 0.936]}, {"time": 5.5, "x": 1.106, "y": 0.936, "curve": [5.722, 1.106, 5.944, 0.951, 5.722, 0.936, 5.944, 1.055]}, {"time": 6.1667, "x": 0.951, "y": 1.055, "curve": [6.333, 0.951, 6.5, 1.106, 6.333, 1.055, 6.5, 0.936]}, {"time": 6.6667, "x": 1.106, "y": 0.936, "curve": [6.833, 1.106, 7, 0.951, 6.833, 0.936, 7, 1.055]}, {"time": 7.1667, "x": 0.951, "y": 1.055, "curve": [7.389, 0.951, 7.611, 1.106, 7.389, 1.055, 7.611, 0.936]}, {"time": 7.8333, "x": 1.106, "y": 0.936, "curve": [8.056, 1.106, 8.278, 0.951, 8.056, 0.936, 8.278, 1.055]}, {"time": 8.5, "x": 0.951, "y": 1.055, "curve": [8.722, 0.951, 8.944, 1.106, 8.722, 1.055, 8.944, 0.936]}, {"time": 9.1667, "x": 1.106, "y": 0.936, "curve": [9.389, 1.106, 9.611, 0.951, 9.389, 0.936, 9.611, 1.055]}, {"time": 9.8333, "x": 0.951, "y": 1.055, "curve": [10.056, 0.951, 10.278, 1.106, 10.056, 1.055, 10.278, 0.936]}, {"time": 10.5, "x": 1.106, "y": 0.936, "curve": [10.722, 1.106, 10.944, 0.951, 10.722, 0.936, 10.944, 1.055]}, {"time": 11.1667, "x": 0.951, "y": 1.055, "curve": [11.389, 0.951, 11.611, 1.106, 11.389, 1.055, 11.611, 0.936]}, {"time": 11.8333, "x": 1.106, "y": 0.936, "curve": [12.056, 1.106, 12.278, 0.951, 12.056, 0.936, 12.278, 1.055]}, {"time": 12.5, "x": 0.951, "y": 1.055, "curve": [12.667, 0.951, 12.833, 1.038, 12.667, 1.055, 12.833, 0.988]}, {"time": 13, "x": 1.082, "y": 0.955}]}, "RU_L": {"translate": [{"x": -10.88, "curve": [0.168, -24.02, 0.334, -34.81, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -34.81, "curve": [0.944, -34.81, 1.389, 40.42, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 40.44, "curve": [2.278, 40.46, 2.722, -34.79, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -34.81, "curve": [3.611, -34.83, 4.056, 40.42, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 40.44, "curve": [4.944, 40.46, 5.389, -34.78, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -34.81, "curve": [6.167, -34.83, 6.5, 40.42, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 40.44, "curve": [7.278, 40.46, 7.722, -34.79, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -34.81, "curve": [8.611, -34.83, 9.056, 40.42, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 40.44, "curve": [9.944, 40.46, 10.389, -34.79, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -34.81, "curve": [11.278, -34.83, 11.722, 40.42, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 40.44, "curve": [12.445, 40.45, 12.724, 11.19, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -10.88}], "scale": [{"x": 1.082, "y": 0.955, "curve": [0.167, 1.038, 0.333, 0.951, 0.167, 0.988, 0.333, 1.055]}, {"time": 0.5, "x": 0.951, "y": 1.055, "curve": [0.722, 0.951, 0.944, 1.106, 0.722, 1.055, 0.944, 0.936]}, {"time": 1.1667, "x": 1.106, "y": 0.936, "curve": [1.389, 1.106, 1.611, 0.951, 1.389, 0.936, 1.611, 1.055]}, {"time": 1.8333, "x": 0.951, "y": 1.055, "curve": [2.056, 0.951, 2.278, 1.106, 2.056, 1.055, 2.278, 0.936]}, {"time": 2.5, "x": 1.106, "y": 0.936, "curve": [2.722, 1.106, 2.944, 0.951, 2.722, 0.936, 2.944, 1.055]}, {"time": 3.1667, "x": 0.951, "y": 1.055, "curve": [3.389, 0.951, 3.611, 1.106, 3.389, 1.055, 3.611, 0.936]}, {"time": 3.8333, "x": 1.106, "y": 0.936, "curve": [4.056, 1.106, 4.278, 0.951, 4.056, 0.936, 4.278, 1.055]}, {"time": 4.5, "x": 0.951, "y": 1.055, "curve": [4.722, 0.951, 4.944, 1.106, 4.722, 1.055, 4.944, 0.936]}, {"time": 5.1667, "x": 1.106, "y": 0.936, "curve": [5.389, 1.106, 5.611, 0.951, 5.389, 0.936, 5.611, 1.055]}, {"time": 5.8333, "x": 0.951, "y": 1.055, "curve": [6, 0.951, 6.167, 1.106, 6, 1.055, 6.167, 0.936]}, {"time": 6.3333, "x": 1.106, "y": 0.936, "curve": [6.5, 1.106, 6.667, 0.951, 6.5, 0.936, 6.667, 1.055]}, {"time": 6.8333, "x": 0.951, "y": 1.055, "curve": [7.056, 0.951, 7.278, 1.106, 7.056, 1.055, 7.278, 0.936]}, {"time": 7.5, "x": 1.106, "y": 0.936, "curve": [7.722, 1.106, 7.944, 0.951, 7.722, 0.936, 7.944, 1.055]}, {"time": 8.1667, "x": 0.951, "y": 1.055, "curve": [8.389, 0.951, 8.611, 1.106, 8.389, 1.055, 8.611, 0.936]}, {"time": 8.8333, "x": 1.106, "y": 0.936, "curve": [9.056, 1.106, 9.278, 0.951, 9.056, 0.936, 9.278, 1.055]}, {"time": 9.5, "x": 0.951, "y": 1.055, "curve": [9.722, 0.951, 9.944, 1.106, 9.722, 1.055, 9.944, 0.936]}, {"time": 10.1667, "x": 1.106, "y": 0.936, "curve": [10.389, 1.106, 10.611, 0.951, 10.389, 0.936, 10.611, 1.055]}, {"time": 10.8333, "x": 0.951, "y": 1.055, "curve": [11.056, 0.951, 11.278, 1.106, 11.056, 1.055, 11.278, 0.936]}, {"time": 11.5, "x": 1.106, "y": 0.936, "curve": [11.722, 1.106, 11.944, 0.951, 11.722, 0.936, 11.944, 1.055]}, {"time": 12.1667, "x": 0.951, "y": 1.055, "curve": [12.389, 0.951, 12.611, 1.106, 12.389, 1.055, 12.611, 0.936]}, {"time": 12.8333, "x": 1.106, "y": 0.936, "curve": [12.889, 1.106, 12.944, 1.096, 12.889, 0.936, 12.944, 0.944]}, {"time": 13, "x": 1.082, "y": 0.955}]}, "RU_L2": {"translate": [{"x": 2.65, "curve": [0.225, -13.3, 0.446, -29.46, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -29.46, "curve": [1.111, -29.46, 1.556, 34.73, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 34.75, "curve": [2.444, 34.76, 2.889, -29.44, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -29.46, "curve": [3.778, -29.48, 4.222, 34.73, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 34.75, "curve": [5.111, 34.76, 5.556, -29.44, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -29.46, "curve": [6.333, -29.48, 6.667, 34.74, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 34.75, "curve": [7.444, 34.76, 7.889, -29.44, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -29.46, "curve": [8.778, -29.48, 9.222, 34.73, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 34.75, "curve": [10.111, 34.76, 10.556, -29.44, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -29.46, "curve": [11.444, -29.48, 11.889, 34.73, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 34.75, "curve": [12.557, 34.76, 12.781, 18.81, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 2.65}], "scale": [{"x": 1.106, "y": 0.936, "curve": [0.222, 1.106, 0.444, 0.951, 0.222, 0.936, 0.444, 1.055]}, {"time": 0.6667, "x": 0.951, "y": 1.055, "curve": [0.889, 0.951, 1.111, 1.106, 0.889, 1.055, 1.111, 0.936]}, {"time": 1.3333, "x": 1.106, "y": 0.936, "curve": [1.556, 1.106, 1.778, 0.951, 1.556, 0.936, 1.778, 1.055]}, {"time": 2, "x": 0.951, "y": 1.055, "curve": [2.222, 0.951, 2.444, 1.106, 2.222, 1.055, 2.444, 0.936]}, {"time": 2.6667, "x": 1.106, "y": 0.936, "curve": [2.889, 1.106, 3.111, 0.951, 2.889, 0.936, 3.111, 1.055]}, {"time": 3.3333, "x": 0.951, "y": 1.055, "curve": [3.556, 0.951, 3.778, 1.106, 3.556, 1.055, 3.778, 0.936]}, {"time": 4, "x": 1.106, "y": 0.936, "curve": [4.222, 1.106, 4.444, 0.951, 4.222, 0.936, 4.444, 1.055]}, {"time": 4.6667, "x": 0.951, "y": 1.055, "curve": [4.889, 0.951, 5.111, 1.106, 4.889, 1.055, 5.111, 0.936]}, {"time": 5.3333, "x": 1.106, "y": 0.936, "curve": [5.556, 1.106, 5.778, 0.951, 5.556, 0.936, 5.778, 1.055]}, {"time": 6, "x": 0.951, "y": 1.055, "curve": [6.167, 0.951, 6.333, 1.106, 6.167, 1.055, 6.333, 0.936]}, {"time": 6.5, "x": 1.106, "y": 0.936, "curve": [6.667, 1.106, 6.833, 0.951, 6.667, 0.936, 6.833, 1.055]}, {"time": 7, "x": 0.951, "y": 1.055, "curve": [7.222, 0.951, 7.444, 1.106, 7.222, 1.055, 7.444, 0.936]}, {"time": 7.6667, "x": 1.106, "y": 0.936, "curve": [7.889, 1.106, 8.111, 0.951, 7.889, 0.936, 8.111, 1.055]}, {"time": 8.3333, "x": 0.951, "y": 1.055, "curve": [8.556, 0.951, 8.778, 1.106, 8.556, 1.055, 8.778, 0.936]}, {"time": 9, "x": 1.106, "y": 0.936, "curve": [9.222, 1.106, 9.444, 0.951, 9.222, 0.936, 9.444, 1.055]}, {"time": 9.6667, "x": 0.951, "y": 1.055, "curve": [9.889, 0.951, 10.111, 1.106, 9.889, 1.055, 10.111, 0.936]}, {"time": 10.3333, "x": 1.106, "y": 0.936, "curve": [10.556, 1.106, 10.778, 0.951, 10.556, 0.936, 10.778, 1.055]}, {"time": 11, "x": 0.951, "y": 1.055, "curve": [11.222, 0.951, 11.444, 1.106, 11.222, 1.055, 11.444, 0.936]}, {"time": 11.6667, "x": 1.106, "y": 0.936, "curve": [11.889, 1.106, 12.111, 0.951, 11.889, 0.936, 12.111, 1.055]}, {"time": 12.3333, "x": 0.951, "y": 1.055, "curve": [12.556, 0.951, 12.778, 1.106, 12.556, 1.055, 12.778, 0.936]}, {"time": 13, "x": 1.106, "y": 0.936}]}, "RU_L3": {"translate": [{"x": 13.79, "curve": [0.279, -1.68, 0.556, -22.26, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -22.26, "curve": [1.278, -22.26, 1.722, 30.57, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 30.59, "curve": [2.611, 30.6, 3.056, -22.24, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -22.26, "curve": [3.944, -22.27, 4.389, 30.57, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 30.59, "curve": [5.278, 30.6, 5.722, -22.24, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -22.26, "curve": [6.5, -22.27, 6.833, 30.58, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 30.59, "curve": [7.611, 30.6, 8.056, -22.24, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -22.26, "curve": [8.944, -22.27, 9.389, 30.57, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 30.59, "curve": [10.278, 30.6, 10.722, -22.24, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -22.26, "curve": [11.611, -22.27, 12.056, 30.57, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 30.59, "curve": [12.667, 30.59, 12.835, 23.13, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 13.79}], "scale": [{"x": 1.082, "y": 0.955, "curve": [0.056, 1.096, 0.111, 1.106, 0.056, 0.944, 0.111, 0.936]}, {"time": 0.1667, "x": 1.106, "y": 0.936, "curve": [0.389, 1.106, 0.611, 0.951, 0.389, 0.936, 0.611, 1.055]}, {"time": 0.8333, "x": 0.951, "y": 1.055, "curve": [1.056, 0.951, 1.278, 1.106, 1.056, 1.055, 1.278, 0.936]}, {"time": 1.5, "x": 1.106, "y": 0.936, "curve": [1.722, 1.106, 1.944, 0.951, 1.722, 0.936, 1.944, 1.055]}, {"time": 2.1667, "x": 0.951, "y": 1.055, "curve": [2.389, 0.951, 2.611, 1.106, 2.389, 1.055, 2.611, 0.936]}, {"time": 2.8333, "x": 1.106, "y": 0.936, "curve": [3.056, 1.106, 3.278, 0.951, 3.056, 0.936, 3.278, 1.055]}, {"time": 3.5, "x": 0.951, "y": 1.055, "curve": [3.722, 0.951, 3.944, 1.106, 3.722, 1.055, 3.944, 0.936]}, {"time": 4.1667, "x": 1.106, "y": 0.936, "curve": [4.389, 1.106, 4.611, 0.951, 4.389, 0.936, 4.611, 1.055]}, {"time": 4.8333, "x": 0.951, "y": 1.055, "curve": [5.056, 0.951, 5.278, 1.106, 5.056, 1.055, 5.278, 0.936]}, {"time": 5.5, "x": 1.106, "y": 0.936, "curve": [5.722, 1.106, 5.944, 0.951, 5.722, 0.936, 5.944, 1.055]}, {"time": 6.1667, "x": 0.951, "y": 1.055, "curve": [6.333, 0.951, 6.5, 1.106, 6.333, 1.055, 6.5, 0.936]}, {"time": 6.6667, "x": 1.106, "y": 0.936, "curve": [6.833, 1.106, 7, 0.951, 6.833, 0.936, 7, 1.055]}, {"time": 7.1667, "x": 0.951, "y": 1.055, "curve": [7.389, 0.951, 7.611, 1.106, 7.389, 1.055, 7.611, 0.936]}, {"time": 7.8333, "x": 1.106, "y": 0.936, "curve": [8.056, 1.106, 8.278, 0.951, 8.056, 0.936, 8.278, 1.055]}, {"time": 8.5, "x": 0.951, "y": 1.055, "curve": [8.722, 0.951, 8.944, 1.106, 8.722, 1.055, 8.944, 0.936]}, {"time": 9.1667, "x": 1.106, "y": 0.936, "curve": [9.389, 1.106, 9.611, 0.951, 9.389, 0.936, 9.611, 1.055]}, {"time": 9.8333, "x": 0.951, "y": 1.055, "curve": [10.056, 0.951, 10.278, 1.106, 10.056, 1.055, 10.278, 0.936]}, {"time": 10.5, "x": 1.106, "y": 0.936, "curve": [10.722, 1.106, 10.944, 0.951, 10.722, 0.936, 10.944, 1.055]}, {"time": 11.1667, "x": 0.951, "y": 1.055, "curve": [11.389, 0.951, 11.611, 1.106, 11.389, 1.055, 11.611, 0.936]}, {"time": 11.8333, "x": 1.106, "y": 0.936, "curve": [12.056, 1.106, 12.278, 0.951, 12.056, 0.936, 12.278, 1.055]}, {"time": 12.5, "x": 0.951, "y": 1.055, "curve": [12.667, 0.951, 12.833, 1.038, 12.667, 1.055, 12.833, 0.988]}, {"time": 13, "x": 1.082, "y": 0.955}]}, "tun": {"rotate": [{"value": -3.3, "curve": [0.444, -3.3, 0.889, 4.82]}, {"time": 1.3333, "value": 4.82, "curve": [1.778, 4.83, 2.222, -3.29]}, {"time": 2.6667, "value": -3.3, "curve": [3.111, -3.3, 3.556, 4.82]}, {"time": 4, "value": 4.82, "curve": [4.444, 4.83, 4.889, -3.29]}, {"time": 5.3333, "value": -3.3, "curve": [5.667, -3.3, 6, 10.67]}, {"time": 6.3333, "value": 10.67, "curve": [6.778, 10.68, 7.222, 3.69]}, {"time": 7.6667, "value": 3.69, "curve": [8.111, 3.69, 8.556, 10.67]}, {"time": 9, "value": 10.67, "curve": [9.444, 10.68, 9.889, -3.29]}, {"time": 10.3333, "value": -3.3, "curve": [10.778, -3.3, 11.222, 4.82]}, {"time": 11.6667, "value": 4.82, "curve": [12.111, 4.83, 12.556, -3.3]}, {"time": 13, "value": -3.3}]}, "arm_R": {"rotate": [{"value": 0.5, "curve": [0.168, 1.35, 0.334, 2.04]}, {"time": 0.5, "value": 2.04, "curve": [0.944, 2.04, 1.389, -2.79]}, {"time": 1.8333, "value": -2.79, "curve": [2.278, -2.79, 2.722, 2.04]}, {"time": 3.1667, "value": 2.04, "curve": [3.611, 2.04, 4.056, -2.79]}, {"time": 4.5, "value": -2.79, "curve": [4.944, -2.79, 5.389, 2.04]}, {"time": 5.8333, "value": 2.04, "curve": [6.167, 2.04, 6.5, -2.79]}, {"time": 6.8333, "value": -2.79, "curve": [7.278, -2.79, 7.722, -0.38]}, {"time": 8.1667, "value": -0.38, "curve": [8.611, -0.37, 9.056, -2.79]}, {"time": 9.5, "value": -2.79, "curve": [9.944, -2.79, 10.389, 2.04]}, {"time": 10.8333, "value": 2.04, "curve": [11.278, 2.04, 11.722, -2.79]}, {"time": 12.1667, "value": -2.79, "curve": [12.445, -2.79, 12.724, -0.91]}, {"time": 13, "value": 0.5}]}, "arm_R2": {"rotate": [{"value": -0.38, "curve": [0.225, 0.82, 0.446, 2.04]}, {"time": 0.6667, "value": 2.04, "curve": [1.111, 2.04, 1.556, -2.79]}, {"time": 2, "value": -2.79, "curve": [2.444, -2.79, 2.889, 2.04]}, {"time": 3.3333, "value": 2.04, "curve": [3.778, 2.04, 4.222, -2.79]}, {"time": 4.6667, "value": -2.79, "curve": [5.111, -2.79, 5.556, 2.04]}, {"time": 6, "value": 2.04, "curve": [6.333, 2.04, 6.667, -2.79]}, {"time": 7, "value": -2.79, "curve": [7.444, -2.79, 7.889, -0.38]}, {"time": 8.3333, "value": -0.38, "curve": [8.778, -0.37, 9.222, -2.79]}, {"time": 9.6667, "value": -2.79, "curve": [10.111, -2.79, 10.556, 2.04]}, {"time": 11, "value": 2.04, "curve": [11.444, 2.04, 11.889, -2.79]}, {"time": 12.3333, "value": -2.79, "curve": [12.557, -2.79, 12.781, -1.59]}, {"time": 13, "value": -0.38}]}, "arm_R3": {"rotate": [{"value": -1.98, "curve": [0.279, 0.74, 0.556, 4.35]}, {"time": 0.8333, "value": 4.35, "curve": [1.278, 4.35, 1.722, -4.93]}, {"time": 2.1667, "value": -4.93, "curve": [2.611, -4.93, 3.056, 4.35]}, {"time": 3.5, "value": 4.35, "curve": [3.944, 4.36, 4.389, -4.93]}, {"time": 4.8333, "value": -4.93, "curve": [5.278, -4.93, 5.722, 4.35]}, {"time": 6.1667, "value": 4.35, "curve": [6.5, 4.36, 6.833, -4.93]}, {"time": 7.1667, "value": -4.93, "curve": [7.611, -4.93, 8.056, -0.29]}, {"time": 8.5, "value": -0.29, "curve": [8.944, -0.29, 9.389, -4.93]}, {"time": 9.8333, "value": -4.93, "curve": [10.278, -4.93, 10.722, 4.35]}, {"time": 11.1667, "value": 4.35, "curve": [11.611, 4.36, 12.056, -4.93]}, {"time": 12.5, "value": -4.93, "curve": [12.667, -4.93, 12.835, -3.62]}, {"time": 13, "value": -1.98}]}, "arm_R4": {"rotate": [{"value": -3.44, "curve": [0.336, -0.82, 0.668, 4.35]}, {"time": 1, "value": 4.35, "curve": [1.444, 4.35, 1.889, -4.93]}, {"time": 2.3333, "value": -4.93, "curve": [2.778, -4.93, 3.222, 4.35]}, {"time": 3.6667, "value": 4.35, "curve": [4.111, 4.36, 4.556, -4.93]}, {"time": 5, "value": -4.93, "curve": [5.444, -4.93, 5.889, 4.35]}, {"time": 6.3333, "value": 4.35, "curve": [6.667, 4.36, 7, -4.93]}, {"time": 7.3333, "value": -4.93, "curve": [7.778, -4.93, 8.222, -0.29]}, {"time": 8.6667, "value": -0.29, "curve": [9.111, -0.29, 9.556, -4.93]}, {"time": 10, "value": -4.93, "curve": [10.444, -4.93, 10.889, 4.35]}, {"time": 11.3333, "value": 4.35, "curve": [11.778, 4.36, 12.222, -4.93]}, {"time": 12.6667, "value": -4.93, "curve": [12.779, -4.93, 12.892, -4.33]}, {"time": 13, "value": -3.44}]}, "arm_R5": {"rotate": [{"value": -4.49, "curve": [0.39, -2.7, 0.779, 4.35]}, {"time": 1.1667, "value": 4.35, "curve": [1.611, 4.35, 2.056, -4.93]}, {"time": 2.5, "value": -4.93, "curve": [2.944, -4.93, 3.389, 4.35]}, {"time": 3.8333, "value": 4.35, "curve": [4.278, 4.36, 4.722, -4.93]}, {"time": 5.1667, "value": -4.93, "curve": [5.611, -4.93, 6.056, 4.35]}, {"time": 6.5, "value": 4.35, "curve": [6.833, 4.36, 7.167, -4.93]}, {"time": 7.5, "value": -4.93, "curve": [7.944, -4.93, 8.389, -0.29]}, {"time": 8.8333, "value": -0.29, "curve": [9.278, -0.29, 9.722, -4.93]}, {"time": 10.1667, "value": -4.93, "curve": [10.611, -4.93, 11.056, 4.35]}, {"time": 11.5, "value": 4.35, "curve": [11.944, 4.36, 12.389, -4.93]}, {"time": 12.8333, "value": -4.93, "curve": [12.89, -4.93, 12.946, -4.75]}, {"time": 13, "value": -4.49}]}, "arm_R6": {"rotate": [{"value": -3.44, "curve": [0.336, -0.82, 0.668, 4.35]}, {"time": 1, "value": 4.35, "curve": [1.444, 4.35, 1.889, -4.93]}, {"time": 2.3333, "value": -4.93, "curve": [2.778, -4.93, 3.222, 4.35]}, {"time": 3.6667, "value": 4.35, "curve": [4.111, 4.36, 4.556, -4.93]}, {"time": 5, "value": -4.93, "curve": [5.444, -4.93, 5.889, 4.35]}, {"time": 6.3333, "value": 4.35, "curve": [6.667, 4.36, 7, -4.93]}, {"time": 7.3333, "value": -4.93, "curve": [7.778, -4.93, 8.222, -0.29]}, {"time": 8.6667, "value": -0.29, "curve": [9.111, -0.29, 9.556, -4.93]}, {"time": 10, "value": -4.93, "curve": [10.444, -4.93, 10.889, 4.35]}, {"time": 11.3333, "value": 4.35, "curve": [11.778, 4.36, 12.222, -4.93]}, {"time": 12.6667, "value": -4.93, "curve": [12.779, -4.93, 12.892, -4.33]}, {"time": 13, "value": -3.44}]}, "arm_L": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 3.38]}, {"time": 7.6667, "value": 3.38, "curve": [8.111, 3.38, 8.556, 0]}, {"time": 9}]}, "arm_L2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.81]}, {"time": 7.6667, "value": 0.81, "curve": [8.111, 0.81, 8.556, 0]}, {"time": 9}]}, "leg_R2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -4.38]}, {"time": 7.6667, "value": -4.38, "curve": [8.111, -4.38, 8.556, 0]}, {"time": 9}]}, "leg_R3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0]}, {"time": 7.6667, "curve": [8.111, 0, 8.556, 0]}, {"time": 9}]}, "leg_L2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -4.85]}, {"time": 7.6667, "value": -4.85, "curve": [8.111, -4.85, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.12]}, {"time": 7.6667, "value": -0.12, "curve": [8.111, -0.12, 8.556, 0]}, {"time": 9}]}, "tunround": {"translate": [{"x": 221.95, "curve": [0.057, 235.02, 0.112, 244.98, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": 244.98, "curve": [0.611, 244.98, 1.056, -244.85, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": -244.98, "curve": [1.944, -245.1, 2.389, 244.86, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": 244.98, "curve": [3.278, 245.1, 3.722, -244.85, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": -244.98, "curve": [4.611, -245.1, 5.056, 244.79, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": 244.98, "curve": [5.833, 245.12, 6.167, -330.25, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": -330.3, "curve": [6.944, -330.38, 7.389, -42.73, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -42.66, "curve": [8.278, -42.59, 8.722, -330.16, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -330.3, "curve": [9.611, -330.45, 10.056, 244.86, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 244.98, "curve": [10.944, 245.1, 11.389, -244.85, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": -244.98, "curve": [12.223, -245.09, 12.613, 129.04, 12.223, 0, 12.613, 0]}, {"time": 13, "x": 221.95}]}, "bodyround": {"translate": [{"x": -22.66, "y": -82.13, "curve": [0.168, -52.61, 0.334, -77.17, 0.168, -161.11, 0.334, -225.92]}, {"time": 0.5, "x": -77.17, "y": -225.92, "curve": [0.944, -77.17, 1.389, 94.16, 0.944, -225.92, 1.389, 226.03]}, {"time": 1.8333, "x": 94.2, "y": 226.14, "curve": [2.278, 94.24, 2.722, -77.13, 2.278, 226.26, 2.722, -225.8]}, {"time": 3.1667, "x": -77.17, "y": -225.92, "curve": [3.611, -77.22, 4.056, 94.16, 3.611, -226.03, 4.056, 226.03]}, {"time": 4.5, "x": 94.2, "y": 226.14, "curve": [4.944, 94.24, 5.389, -77.11, 4.944, 226.26, 5.389, -225.74]}, {"time": 5.8333, "x": -77.17, "y": -225.92, "curve": [6.167, -77.22, 6.5, 115.94, 6.167, -226.05, 6.5, 306.35]}, {"time": 6.8333, "x": 115.96, "y": 306.4, "curve": [7.278, 115.98, 7.722, 19.42, 7.278, 306.46, 7.722, 40.31]}, {"time": 8.1667, "x": 19.39, "y": 40.24, "curve": [8.611, 19.37, 9.056, 115.91, 8.611, 40.17, 9.056, 306.26]}, {"time": 9.5, "x": 115.96, "y": 306.4, "curve": [9.944, 116.01, 10.389, -77.13, 9.944, 306.53, 10.389, -225.8]}, {"time": 10.8333, "x": -77.17, "y": -225.92, "curve": [11.278, -77.22, 11.722, 94.16, 11.278, -226.03, 11.722, 226.03]}, {"time": 12.1667, "x": 94.2, "y": 226.14, "curve": [12.445, 94.23, 12.724, 27.6, 12.445, 226.22, 12.724, 50.45]}, {"time": 13, "x": -22.66, "y": -82.13}]}, "headround3": {"translate": [{"x": 40.29, "curve": [0.279, -37.24, 0.556, -140.39, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -140.39, "curve": [1.278, -140.39, 1.722, 124.43, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 124.5, "curve": [2.611, 124.57, 3.056, -140.32, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -140.39, "curve": [3.944, -140.46, 4.389, 124.43, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 124.5, "curve": [5.278, 124.57, 5.722, -140.25, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -140.39, "curve": [6.5, -140.49, 6.833, 267.1, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 267.14, "curve": [7.611, 267.19, 8.056, 158.89, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 158.83, "curve": [8.944, 158.78, 9.389, 267.04, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 267.14, "curve": [10.278, 267.24, 10.722, -140.32, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -140.39, "curve": [11.611, -140.46, 12.056, 124.43, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 124.5, "curve": [12.667, 124.53, 12.835, 87.15, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 40.29}]}, "headround": {"translate": [{"y": 159.84, "curve": [0.336, 0, 0.668, 0, 0.336, 21.41, 0.668, -251.9]}, {"time": 1, "y": -251.9, "curve": [1.444, 0, 1.889, 0, 1.444, -251.9, 1.889, 238.08]}, {"time": 2.3333, "y": 238.21, "curve": [2.778, 0, 3.222, 0, 2.778, 238.33, 3.222, -251.78]}, {"time": 3.6667, "y": -251.9, "curve": [4.111, 0, 4.556, 0, 4.111, -252.03, 4.556, 238.08]}, {"time": 5, "y": 238.21, "curve": [5.444, 0, 5.889, 0, 5.444, 238.33, 5.889, -251.67]}, {"time": 6.3333, "y": -251.9, "curve": [6.667, 0, 7, 0, 6.667, -252.08, 7, 435.63]}, {"time": 7.3333, "y": 435.7, "curve": [7.778, 0, 8.222, 0, 7.778, 435.79, 8.222, 337.6]}, {"time": 8.6667, "y": 337.52, "curve": [9.111, 0, 9.556, 0, 9.111, 337.43, 9.556, 435.52]}, {"time": 10, "y": 435.7, "curve": [10.444, 0, 10.889, 0, 10.444, 435.87, 10.889, -251.78]}, {"time": 11.3333, "y": -251.9, "curve": [11.778, 0, 12.222, 0, 11.778, -252.03, 12.222, 238.08]}, {"time": 12.6667, "y": 238.21, "curve": [12.779, 0, 12.892, 0, 12.779, 238.24, 12.892, 206.81]}, {"time": 13, "y": 159.84}]}, "leg_L5": {"rotate": [{"value": 0.14, "curve": "stepped"}, {"time": 5.3333, "value": 0.14, "curve": [5.667, 0.14, 6, 0.14]}, {"time": 6.3333, "value": 0.14, "curve": [6.778, 0.14, 7.222, -4.42]}, {"time": 7.6667, "value": -4.43, "curve": [8.111, -4.43, 8.556, 0.14]}, {"time": 9, "value": 0.14}]}, "leg_L6": {"rotate": [{"value": -0.38, "curve": "stepped"}, {"time": 5.3333, "value": -0.38, "curve": [5.667, -0.38, 6, -0.38]}, {"time": 6.3333, "value": -0.38, "curve": [6.778, -0.38, 7.222, -1.14]}, {"time": 7.6667, "value": -1.14, "curve": [8.111, -1.14, 8.556, -0.38]}, {"time": 9, "value": -0.38}]}, "leg_R5": {"rotate": [{"value": -0.02, "curve": "stepped"}, {"time": 5.3333, "value": -0.02, "curve": [5.667, -0.02, 6, -0.02]}, {"time": 6.3333, "value": -0.02, "curve": [6.778, -0.02, 7.222, -4.42]}, {"time": 7.6667, "value": -4.42, "curve": [8.111, -4.42, 8.556, -0.02]}, {"time": 9, "value": -0.02}]}, "sh_L2": {"rotate": [{"value": -0.16, "curve": "stepped"}, {"time": 5.3333, "value": -0.16, "curve": [5.667, -0.16, 6, -0.15]}, {"time": 6.3333, "value": -0.16, "curve": [6.778, -0.16, 7.222, 4.16]}, {"time": 7.6667, "value": 4.16, "curve": [8.111, 4.16, 8.556, -0.16]}, {"time": 9, "value": -0.16}]}, "sh_L3": {"rotate": [{"value": -0.05, "curve": "stepped"}, {"time": 5.3333, "value": -0.05, "curve": [5.667, -0.05, 6, -0.05]}, {"time": 6.3333, "value": -0.05, "curve": [6.778, -0.05, 7.222, 0.68]}, {"time": 7.6667, "value": 0.68, "curve": [8.111, 0.68, 8.556, -0.05]}, {"time": 9, "value": -0.05}]}, "arm_L4": {"rotate": [{"value": 0.37, "curve": [0.225, -2.36, 0.446, -5.14]}, {"time": 0.6667, "value": -5.14, "curve": [1.111, -5.14, 1.556, 5.88]}, {"time": 2, "value": 5.89, "curve": [2.444, 5.89, 2.889, -5.14]}, {"time": 3.3333, "value": -5.14, "curve": [3.778, -5.14, 4.222, 5.88]}, {"time": 4.6667, "value": 5.89, "curve": [5.111, 5.89, 5.556, -5.13]}, {"time": 6, "value": -5.14, "curve": [6.333, -5.15, 6.667, 20.67]}, {"time": 7, "value": 20.67, "curve": [7.444, 20.68, 7.889, 7.77]}, {"time": 8.3333, "value": 7.77, "curve": [8.778, 7.76, 9.222, 20.67]}, {"time": 9.6667, "value": 20.67, "curve": [10.111, 20.68, 10.556, -5.14]}, {"time": 11, "value": -5.14, "curve": [11.444, -5.14, 11.889, 5.88]}, {"time": 12.3333, "value": 5.89, "curve": [12.557, 5.89, 12.781, 3.15]}, {"time": 13, "value": 0.37}], "translate": [{"x": -5.9, "y": -7.78, "curve": [0.168, -12.23, 0.334, -17.42, 0.168, -2.17, 0.334, 2.43]}, {"time": 0.5, "x": -17.42, "y": 2.43, "curve": [0.944, -17.42, 1.389, 18.78, 0.944, 2.43, 1.389, -29.68]}, {"time": 1.8333, "x": 18.79, "y": -29.68, "curve": [2.278, 18.8, 2.722, -17.41, 2.278, -29.69, 2.722, 2.42]}, {"time": 3.1667, "x": -17.42, "y": 2.43, "curve": [3.611, -17.43, 4.056, 18.78, 3.611, 2.44, 4.056, -29.68]}, {"time": 4.5, "x": 18.79, "y": -29.68, "curve": [4.944, 18.8, 5.389, -17.4, 4.944, -29.69, 5.389, 2.43]}, {"time": 5.8333, "x": -17.42, "y": 2.43, "curve": [6.167, -17.43, 6.5, 36.76, 6.167, 2.43, 6.5, -9.36]}, {"time": 6.8333, "x": 36.76, "y": -9.36, "curve": [7.278, 36.77, 7.722, 9.68, 7.278, -9.36, 7.722, -3.46]}, {"time": 8.1667, "x": 9.67, "y": -3.46, "curve": [8.611, 9.66, 9.056, 36.75, 8.611, -3.46, 9.056, -9.35]}, {"time": 9.5, "x": 36.76, "y": -9.36, "curve": [9.944, 36.78, 10.389, -17.41, 9.944, -9.36, 10.389, 2.42]}, {"time": 10.8333, "x": -17.42, "y": 2.43, "curve": [11.278, -17.43, 11.722, 18.78, 11.278, 2.44, 11.722, -29.68]}, {"time": 12.1667, "x": 18.79, "y": -29.68, "curve": [12.445, 18.8, 12.724, 4.72, 12.445, -29.69, 12.724, -17.2]}, {"time": 13, "x": -5.9, "y": -7.78}]}, "eye_R": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.87, 2.033, 0, 2.067, -1.22]}, {"time": 2.1, "x": -1.87, "y": -1.22, "curve": "stepped"}, {"time": 3.3333, "x": -1.87, "y": -1.22, "curve": [3.367, -1.87, 3.4, -0.64, 3.367, -1.22, 3.4, -1.77]}, {"time": 3.4333, "x": -0.64, "y": -1.77, "curve": "stepped"}, {"time": 3.8667, "x": -0.64, "y": -1.77, "curve": [3.9, -0.64, 3.933, 0, 3.9, -1.77, 3.933, 0]}, {"time": 3.9667, "curve": "stepped"}, {"time": 6.5, "curve": [6.533, 0, 6.567, 0, 6.533, 0, 6.567, -12.39]}, {"time": 6.6, "y": -12.39, "curve": "stepped"}, {"time": 7.3333, "y": -12.39, "curve": [7.367, 0, 7.4, -1.32, 7.367, -12.39, 7.4, -13.69]}, {"time": 7.4333, "x": -1.32, "y": -13.69, "curve": "stepped"}, {"time": 8.5667, "x": -1.32, "y": -13.69, "curve": [8.6, -1.32, 8.633, 0.33, 8.6, -13.69, 8.633, -12.75]}, {"time": 8.6667, "x": 0.33, "y": -12.75, "curve": "stepped"}, {"time": 9.1667, "x": 0.33, "y": -12.75, "curve": [9.2, 0.33, 9.233, 0, 9.2, -12.75, 9.233, -12.39]}, {"time": 9.2667, "y": -12.39, "curve": "stepped"}, {"time": 10, "y": -12.39, "curve": [10.033, 0, 10.067, 0, 10.033, -12.39, 10.067, 0]}, {"time": 10.1}]}, "eye_L": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.87, 2.033, 0, 2.067, -1.22]}, {"time": 2.1, "x": -1.87, "y": -1.22, "curve": "stepped"}, {"time": 3.3333, "x": -1.87, "y": -1.22, "curve": [3.367, -1.87, 3.4, -0.64, 3.367, -1.22, 3.4, -1.77]}, {"time": 3.4333, "x": -0.64, "y": -1.77, "curve": "stepped"}, {"time": 3.8667, "x": -0.64, "y": -1.77, "curve": [3.9, -0.64, 3.933, 0, 3.9, -1.77, 3.933, 0]}, {"time": 3.9667, "curve": "stepped"}, {"time": 6.5, "curve": [6.533, 0, 6.567, 0, 6.533, 0, 6.567, -8.78]}, {"time": 6.6, "y": -8.78, "curve": "stepped"}, {"time": 7.3333, "y": -8.78, "curve": [7.367, 0, 7.4, -1.32, 7.367, -8.78, 7.4, -10.08]}, {"time": 7.4333, "x": -1.32, "y": -10.08, "curve": "stepped"}, {"time": 8.5667, "x": -1.32, "y": -10.08, "curve": [8.6, -1.32, 8.633, 0.33, 8.6, -10.08, 8.633, -9.14]}, {"time": 8.6667, "x": 0.33, "y": -9.14, "curve": "stepped"}, {"time": 9.1667, "x": 0.33, "y": -9.14, "curve": [9.2, 0.33, 9.233, 0, 9.2, -9.14, 9.233, -8.78]}, {"time": 9.2667, "y": -8.78, "curve": "stepped"}, {"time": 10, "y": -8.78, "curve": [10.033, 0, 10.067, 0, 10.033, -8.78, 10.067, 0]}, {"time": 10.1}], "scale": [{"time": 6.5, "curve": [6.533, 1, 6.567, 0.966, 6.533, 1, 6.567, 0.846]}, {"time": 6.6, "x": 0.966, "y": 0.846, "curve": "stepped"}, {"time": 10, "x": 0.966, "y": 0.846, "curve": [10.033, 0.966, 10.067, 1, 10.033, 0.846, 10.067, 1]}, {"time": 10.1}]}, "eyebrow_L": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -10.95]}, {"time": 6.6667, "value": -10.95, "curve": "stepped"}, {"time": 9.6667, "value": -10.95, "curve": [9.889, -10.95, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_L2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -20.42]}, {"time": 6.6667, "value": -20.42, "curve": "stepped"}, {"time": 9.6667, "value": -20.42, "curve": [9.889, -20.42, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R3": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 7.2]}, {"time": 6.6667, "value": 7.2, "curve": "stepped"}, {"time": 9.6667, "value": 7.2, "curve": [9.889, 7.2, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 5.74]}, {"time": 6.6667, "value": 5.74, "curve": "stepped"}, {"time": 9.6667, "value": 5.74, "curve": [9.889, 5.74, 10.111, 0]}, {"time": 10.3333}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-9.90857, -0.89711, -9.90576, -0.89795, -4.14465, -0.48381, -4.14355, -0.48405, -3.32825, -0.28291, -3.32776, -0.28328, -3.32825, -0.28291, -3.32776, -0.28328, -1.48022, -0.16513, -1.47998, -0.16542, -0.51733, -0.04549, -0.51733, -0.04565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.60315, -0.31358, -1.60242, -0.31377, -5.0802, -1.03822, -5.07959, -1.03858, -9.20752, -1.6362, -9.20679, -1.63687, -11.69934, -2.19197, -11.69739, -2.19275, -12.62366, -1.96909, -12.62036, -1.96976, -12.79431, -1.60518, -12.79077, -1.60624, 0, 0, 0, 0, -3.36316, -0.36191, -3.36304, -0.36221, -6.8009, -0.40479, -6.80017, -0.4055, -10.18176, -0.44357, -10.18115, -0.44413, -12.04028, -0.5414, -12.03882, -0.54201, -12.70874, -0.62247, -12.70715, -0.62266, -11.1582, -1.00635, -11.15564, -1.0066, -6.91895, -0.65746, -6.91687, -0.65805, -4.31458, -0.14612, -4.31372, -0.14658, -1.74048, -0.18811, -1.74036, -0.18838], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-9.90857, -0.89711, -9.90576, -0.89795, -4.14465, -0.48381, -4.14355, -0.48405, -3.32825, -0.28291, -3.32776, -0.28328, -3.32825, -0.28291, -3.32776, -0.28328, -1.48022, -0.16513, -1.47998, -0.16542, -0.51733, -0.04549, -0.51733, -0.04565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.60315, -0.31358, -1.60242, -0.31377, -5.0802, -1.03822, -5.07959, -1.03858, -9.20752, -1.6362, -9.20679, -1.63687, -11.69934, -2.19197, -11.69739, -2.19275, -12.62366, -1.96909, -12.62036, -1.96976, -12.79431, -1.60518, -12.79077, -1.60624, 0, 0, 0, 0, -3.36316, -0.36191, -3.36304, -0.36221, -6.8009, -0.40479, -6.80017, -0.4055, -10.18176, -0.44357, -10.18115, -0.44413, -12.04028, -0.5414, -12.03882, -0.54201, -12.70874, -0.62247, -12.70715, -0.62266, -11.1582, -1.00635, -11.15564, -1.0066, -6.91895, -0.65746, -6.91687, -0.65805, -4.31458, -0.14612, -4.31372, -0.14658, -1.74048, -0.18811, -1.74036, -0.18838], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-9.90857, -0.89711, -9.90576, -0.89795, -4.14465, -0.48381, -4.14355, -0.48405, -3.32825, -0.28291, -3.32776, -0.28328, -3.32825, -0.28291, -3.32776, -0.28328, -1.48022, -0.16513, -1.47998, -0.16542, -0.51733, -0.04549, -0.51733, -0.04565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.60315, -0.31358, -1.60242, -0.31377, -5.0802, -1.03822, -5.07959, -1.03858, -9.20752, -1.6362, -9.20679, -1.63687, -11.69934, -2.19197, -11.69739, -2.19275, -12.62366, -1.96909, -12.62036, -1.96976, -12.79431, -1.60518, -12.79077, -1.60624, 0, 0, 0, 0, -3.36316, -0.36191, -3.36304, -0.36221, -6.8009, -0.40479, -6.80017, -0.4055, -10.18176, -0.44357, -10.18115, -0.44413, -12.04028, -0.5414, -12.03882, -0.54201, -12.70874, -0.62247, -12.70715, -0.62266, -11.1582, -1.00635, -11.15564, -1.0066, -6.91895, -0.65746, -6.91687, -0.65805, -4.31458, -0.14612, -4.31372, -0.14658, -1.74048, -0.18811, -1.74036, -0.18838], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-6.17468, 0.4391, -6.17322, 0.43883, -10.36169, 0.14151, -10.36011, 0.14105, -13.88354, 0.43631, -13.88013, 0.43573, -12.83801, 1.24721, -12.83362, 1.24664, -11.63416, 0.56409, -11.6322, 0.56317, -6.82471, 1.15102, -6.82349, 1.1503, -2.30969, 0.20247, -2.30933, 0.20197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.75854, 0.00888, -0.75842, 0.00876, -1.53027, 0.16797, -1.52966, 0.16779, -4.56555, 0.58136, -4.56421, 0.5811, -0.76721, 0.04968, -0.76721, 0.04963, -4.09338, 0.00055, -4.0929, 8e-05, -8.04028, 0.12286, -8.03882, 0.12253, -12.04517, -0.83691, -12.04407, -0.83731, -14.63013, -1.06563, -14.62891, -1.06588, -14.21863, -1.64854, -14.2157, -1.64908, -10.52747, -0.56511, -10.52441, -0.56555, -3.70776, 0.1412, -3.70667, 0.14098, -1.7356, -0.0773, -1.73547, -0.07749, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.20374, -0.01794, -0.20374, -0.01798], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-6.17468, 0.4391, -6.17322, 0.43883, -10.36169, 0.14151, -10.36011, 0.14105, -13.88354, 0.43631, -13.88013, 0.43573, -12.83801, 1.24721, -12.83362, 1.24664, -11.63416, 0.56409, -11.6322, 0.56317, -6.82471, 1.15102, -6.82349, 1.1503, -2.30969, 0.20247, -2.30933, 0.20197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.75854, 0.00888, -0.75842, 0.00876, -1.53027, 0.16797, -1.52966, 0.16779, -4.56555, 0.58136, -4.56421, 0.5811, -0.76721, 0.04968, -0.76721, 0.04963, -4.09338, 0.00055, -4.0929, 8e-05, -8.04028, 0.12286, -8.03882, 0.12253, -12.04517, -0.83691, -12.04407, -0.83731, -14.63013, -1.06563, -14.62891, -1.06588, -14.21863, -1.64854, -14.2157, -1.64908, -10.52747, -0.56511, -10.52441, -0.56555, -3.70776, 0.1412, -3.70667, 0.14098, -1.7356, -0.0773, -1.73547, -0.07749, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.20374, -0.01794, -0.20374, -0.01798], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-6.17468, 0.4391, -6.17322, 0.43883, -10.36169, 0.14151, -10.36011, 0.14105, -13.88354, 0.43631, -13.88013, 0.43573, -12.83801, 1.24721, -12.83362, 1.24664, -11.63416, 0.56409, -11.6322, 0.56317, -6.82471, 1.15102, -6.82349, 1.1503, -2.30969, 0.20247, -2.30933, 0.20197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.75854, 0.00888, -0.75842, 0.00876, -1.53027, 0.16797, -1.52966, 0.16779, -4.56555, 0.58136, -4.56421, 0.5811, -0.76721, 0.04968, -0.76721, 0.04963, -4.09338, 0.00055, -4.0929, 8e-05, -8.04028, 0.12286, -8.03882, 0.12253, -12.04517, -0.83691, -12.04407, -0.83731, -14.63013, -1.06563, -14.62891, -1.06588, -14.21863, -1.64854, -14.2157, -1.64908, -10.52747, -0.56511, -10.52441, -0.56555, -3.70776, 0.1412, -3.70667, 0.14098, -1.7356, -0.0773, -1.73547, -0.07749, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.20374, -0.01794, -0.20374, -0.01798], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 386, "vertices": [-3.24109, -0.28584, -3.23987, -0.28613, -6.99231, -0.95209, -6.99048, -0.95247, -10.13232, -0.93692, -10.13086, -0.93727, -11.64771, -1.32179, -11.64587, -1.3222, -11.18005, -1.40561, -11.17847, -1.40635, -8.97742, -1.21164, -8.97668, -1.21197, -5.15869, -0.37177, -5.15784, -0.37204, -3.07056, -0.31284, -3.07019, -0.31305, -1.18701, -0.31392, -1.18616, -0.31406, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.62952, -0.35588, -2.62952, -0.356, -7.30103, -0.02623, -7.29993, -0.02635, -10.84082, -0.58652, -10.83948, -0.58674, -13.67773, -1.39415, -13.67712, -1.39449, -13.58923, -1.69563, -13.58801, -1.69594, -11.89136, -1.29822, -11.89087, -1.29842, -8.2793, -0.85524, -8.27844, -0.85548, -2.26111, -0.32347, -2.26111, -0.32357, -0.77673, -0.31602, -0.77673, -0.31614], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "offset": 386, "vertices": [-3.24109, -0.28584, -3.23987, -0.28613, -6.99231, -0.95209, -6.99048, -0.95247, -10.13232, -0.93692, -10.13086, -0.93727, -11.64771, -1.32179, -11.64587, -1.3222, -11.18005, -1.40561, -11.17847, -1.40635, -8.97742, -1.21164, -8.97668, -1.21197, -5.15869, -0.37177, -5.15784, -0.37204, -3.07056, -0.31284, -3.07019, -0.31305, -1.18701, -0.31392, -1.18616, -0.31406, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.62952, -0.35588, -2.62952, -0.356, -7.30103, -0.02623, -7.29993, -0.02635, -10.84082, -0.58652, -10.83948, -0.58674, -13.67773, -1.39415, -13.67712, -1.39449, -13.58923, -1.69563, -13.58801, -1.69594, -11.89136, -1.29822, -11.89087, -1.29842, -8.2793, -0.85524, -8.27844, -0.85548, -2.26111, -0.32347, -2.26111, -0.32357, -0.77673, -0.31602, -0.77673, -0.31614], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 386, "vertices": [-3.24109, -0.28584, -3.23987, -0.28613, -6.99231, -0.95209, -6.99048, -0.95247, -10.13232, -0.93692, -10.13086, -0.93727, -11.64771, -1.32179, -11.64587, -1.3222, -11.18005, -1.40561, -11.17847, -1.40635, -8.97742, -1.21164, -8.97668, -1.21197, -5.15869, -0.37177, -5.15784, -0.37204, -3.07056, -0.31284, -3.07019, -0.31305, -1.18701, -0.31392, -1.18616, -0.31406, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.62952, -0.35588, -2.62952, -0.356, -7.30103, -0.02623, -7.29993, -0.02635, -10.84082, -0.58652, -10.83948, -0.58674, -13.67773, -1.39415, -13.67712, -1.39449, -13.58923, -1.69563, -13.58801, -1.69594, -11.89136, -1.29822, -11.89087, -1.29842, -8.2793, -0.85524, -8.27844, -0.85548, -2.26111, -0.32347, -2.26111, -0.32357, -0.77673, -0.31602, -0.77673, -0.31614], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1]}, {"time": 6.6667, "offset": 64, "vertices": [0.2522, 0.37578, 0.25488, 0.37564, -0.15979, 0.06425, -0.15796, 0.06412, -0.35791, -0.13661, -0.35571, -0.13681, -0.35339, -0.00089, -0.35205, -0.00098, -0.44568, -0.03377, -0.4436, -0.03391, -0.44568, -0.03377, -0.4436, -0.03391, -0.44568, -0.03377, -0.4436, -0.03391, -0.3479, 0.04263, -0.34692, 0.04257, -0.24487, 0.16261, -0.2439, 0.16254, -0.26831, 0.03287, -0.26611, 0.03275, 0.72534, -0.40613, 0.72656, -0.40619, 0.97668, -0.04428, 0.97803, -0.04465, 1.5238, -0.05664, 1.52563, -0.0574, 2.06677, 0.00798, 2.06909, 0.00708, 2.06677, 0.00798, 2.06909, 0.00708, 1.34045, 0.24002, 1.34204, 0.23932, 1.44971, -0.30984, 1.45166, -0.31018, 1.33362, 0.28642, 1.33691, 0.28604, 0.3573, -0.23703, 0.35864, -0.23715, 0.25378, -0.0311, 0.25464, -0.03116, -0.20593, -0.10356, -0.20581, -0.10361, -0.63049, -0.0995, -0.62769, -0.0997, -0.63049, -0.0995, -0.62769, -0.0997, -0.63049, -0.0995, -0.62769, -0.0997, -0.85327, -0.11639, -0.84937, -0.11667, -0.45618, -0.12083, -0.45435, -0.121, 0, 0, 0, 0, 0.34058, 0.1515, 0.34155, 0.15149, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 1.58032, -0.10483, 1.57959, -0.10464, 1.94592, -0.01688, 1.94458, -0.0166, 2.03845, 0.01602, 2.03687, 0.01633, 1.31836, -0.07286, 1.31714, -0.07266, -0.42017, -0.02464, -0.41748, -0.02484, 1.38635, 0.00108, 1.38867, 0.00079, 0.57507, -0.22054, 0.57617, -0.22064, 0.3573, -0.23703, 0.35864, -0.23715, 0.3573, -0.23703, 0.35864, -0.23715, 0.3573, -0.23703, 0.35864, -0.23715, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 1.32678, -0.35068, 1.32813, -0.35104, 2.12268, -0.10954, 2.12402, -0.11017, 2.12268, -0.10954, 2.12402, -0.11017, 1.34094, -0.26624, 1.34277, -0.26669, 0.79053, 0.1187, 0.79199, 0.11865, 0.63574, -0.21021, 0.6355, -0.21011, 0.63574, -0.21021, 0.6355, -0.21011], "curve": "stepped"}, {"time": 9.6667, "offset": 64, "vertices": [0.2522, 0.37578, 0.25488, 0.37564, -0.15979, 0.06425, -0.15796, 0.06412, -0.35791, -0.13661, -0.35571, -0.13681, -0.35339, -0.00089, -0.35205, -0.00098, -0.44568, -0.03377, -0.4436, -0.03391, -0.44568, -0.03377, -0.4436, -0.03391, -0.44568, -0.03377, -0.4436, -0.03391, -0.3479, 0.04263, -0.34692, 0.04257, -0.24487, 0.16261, -0.2439, 0.16254, -0.26831, 0.03287, -0.26611, 0.03275, 0.72534, -0.40613, 0.72656, -0.40619, 0.97668, -0.04428, 0.97803, -0.04465, 1.5238, -0.05664, 1.52563, -0.0574, 2.06677, 0.00798, 2.06909, 0.00708, 2.06677, 0.00798, 2.06909, 0.00708, 1.34045, 0.24002, 1.34204, 0.23932, 1.44971, -0.30984, 1.45166, -0.31018, 1.33362, 0.28642, 1.33691, 0.28604, 0.3573, -0.23703, 0.35864, -0.23715, 0.25378, -0.0311, 0.25464, -0.03116, -0.20593, -0.10356, -0.20581, -0.10361, -0.63049, -0.0995, -0.62769, -0.0997, -0.63049, -0.0995, -0.62769, -0.0997, -0.63049, -0.0995, -0.62769, -0.0997, -0.85327, -0.11639, -0.84937, -0.11667, -0.45618, -0.12083, -0.45435, -0.121, 0, 0, 0, 0, 0.34058, 0.1515, 0.34155, 0.15149, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 1.58032, -0.10483, 1.57959, -0.10464, 1.94592, -0.01688, 1.94458, -0.0166, 2.03845, 0.01602, 2.03687, 0.01633, 1.31836, -0.07286, 1.31714, -0.07266, -0.42017, -0.02464, -0.41748, -0.02484, 1.38635, 0.00108, 1.38867, 0.00079, 0.57507, -0.22054, 0.57617, -0.22064, 0.3573, -0.23703, 0.35864, -0.23715, 0.3573, -0.23703, 0.35864, -0.23715, 0.3573, -0.23703, 0.35864, -0.23715, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 1.32678, -0.35068, 1.32813, -0.35104, 2.12268, -0.10954, 2.12402, -0.11017, 2.12268, -0.10954, 2.12402, -0.11017, 1.34094, -0.26624, 1.34277, -0.26669, 0.79053, 0.1187, 0.79199, 0.11865, 0.63574, -0.21021, 0.6355, -0.21011, 0.63574, -0.21021, 0.6355, -0.21011], "curve": [9.889, 0, 10.111, 1]}, {"time": 10.3333}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": -12.91, "curve": [0.027, -12.91, 0.058, 38.27, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 38.27, "curve": [0.544, 38.27, 0.856, -12.91, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -12.91}]}, "ALL2": {"translate": [{"y": -17.88, "curve": [0.078, 0, 0.156, 0, 0.027, -17.88, 0.058, 28.61]}, {"time": 0.2333, "y": 28.61, "curve": [0.544, 0, 0.856, 0, 0.544, 28.61, 0.856, -17.88]}, {"time": 1.1667, "y": -17.88}]}, "body": {"rotate": [{"value": -2.36, "curve": [0.027, -2.36, 0.058, 11.08]}, {"time": 0.2333, "value": 11.08, "curve": [0.245, 11.08, 0.256, 10.1]}, {"time": 0.2667, "value": 10.06, "curve": [0.567, 9.15, 0.867, -2.36]}, {"time": 1.1667, "value": -2.36}], "translate": [{"x": 0.29, "y": -12.05, "curve": [0.027, 0.29, 0.058, -0.86, 0.027, -12.05, 0.058, 19.4]}, {"time": 0.2333, "x": -0.86, "y": 19.4, "curve": [0.544, -0.86, 0.856, 0.29, 0.544, 19.4, 0.856, -12.05]}, {"time": 1.1667, "x": 0.29, "y": -12.05}], "scale": [{"y": 1.056, "curve": [0.078, 1, 0.156, 1, 0.027, 1.056, 0.058, 0.968]}, {"time": 0.2333, "y": 0.968, "curve": [0.544, 1, 0.856, 1, 0.544, 0.968, 0.856, 1.056]}, {"time": 1.1667, "y": 1.056}]}, "body2": {"rotate": [{"value": 1.56, "curve": [0.031, 1.56, 0.067, -5.51]}, {"time": 0.2667, "value": -5.51, "curve": [0.567, -5.51, 0.867, 1.56]}, {"time": 1.1667, "value": 1.56}], "translate": [{"x": -6.57, "y": -1.93, "curve": [0.031, -6.57, 0.067, 11.75, 0.031, -1.93, 0.067, 1.43]}, {"time": 0.2667, "x": 11.75, "y": 1.43, "curve": [0.567, 11.75, 0.867, -6.57, 0.567, 1.43, 0.867, -1.93]}, {"time": 1.1667, "x": -6.57, "y": -1.93}], "scale": [{"x": 1.004, "y": 1.004, "curve": [0.031, 1.004, 0.067, 1.026, 0.031, 1.004, 0.067, 1.026]}, {"time": 0.2667, "x": 1.026, "y": 1.026, "curve": [0.567, 1.026, 0.867, 1.004, 0.567, 1.026, 0.867, 1.004]}, {"time": 1.1667, "x": 1.004, "y": 1.004}]}, "neck": {"rotate": [{"value": -0.5, "curve": [0.035, -0.5, 0.075, 10.38]}, {"time": 0.3, "value": 10.38, "curve": [0.589, 10.38, 0.878, -0.5]}, {"time": 1.1667, "value": -0.5}]}, "head": {"rotate": [{"value": 0.6, "curve": [0.035, 0.6, 0.075, 10.38]}, {"time": 0.3, "value": 10.38, "curve": [0.589, 10.38, 0.878, 0.6]}, {"time": 1.1667, "value": 0.6}]}, "earring": {"rotate": [{"value": 10.74, "curve": [0.024, 12.81, 0.042, 14.27]}, {"time": 0.0667, "value": 14.27, "curve": [0.165, 14.28, 0.269, -7.8]}, {"time": 0.3667, "value": -7.8, "curve": [0.465, -7.8, 0.569, 14.27]}, {"time": 0.6667, "value": 14.27, "curve": [0.765, 14.28, 0.869, -7.8]}, {"time": 0.9667, "value": -7.8, "curve": [1.04, -7.81, 1.094, 4.56]}, {"time": 1.1667, "value": 10.74}]}, "hair_FR": {"rotate": [{"value": -0.94, "curve": [0.123, 1.56, 0.245, 4.88]}, {"time": 0.3667, "value": 4.88, "curve": [0.562, 4.88, 0.771, -3.64]}, {"time": 0.9667, "value": -3.65, "curve": [1.04, -3.65, 1.094, -2.44]}, {"time": 1.1667, "value": -0.94}]}, "hair_F": {"rotate": [{"value": 2.17, "curve": [0.123, -0.33, 0.245, -3.65]}, {"time": 0.3667, "value": -3.65, "curve": [0.562, -3.65, 0.771, 4.88]}, {"time": 0.9667, "value": 4.88, "curve": [1.04, 4.88, 1.094, 3.68]}, {"time": 1.1667, "value": 2.17}]}, "hair_FL": {"rotate": [{"value": 2.17, "curve": [0.123, -0.33, 0.245, -3.65]}, {"time": 0.3667, "value": -3.65, "curve": [0.562, -3.65, 0.771, 4.88]}, {"time": 0.9667, "value": 4.88, "curve": [1.04, 4.88, 1.094, 3.68]}, {"time": 1.1667, "value": 2.17}]}, "hair_B2": {"rotate": [{"value": -3.65, "curve": [0.196, -3.65, 0.404, 4.88]}, {"time": 0.6, "value": 4.88, "curve": [0.796, 4.88, 0.971, -3.64]}, {"time": 1.1667, "value": -3.65}]}, "sh_R": {"translate": [{"x": -2.7, "y": 1.18, "curve": [0.031, -2.7, 0.067, -1.96, 0.031, 1.18, 0.067, 12.44]}, {"time": 0.2667, "x": -1.96, "y": 12.44, "curve": [0.567, -1.96, 0.867, -2.7, 0.567, 12.44, 0.867, 1.18]}, {"time": 1.1667, "x": -2.7, "y": 1.18}]}, "sh_L": {"translate": [{"x": -3.95, "y": 1.72, "curve": [0.031, -3.95, 0.067, 15.24, 0.031, 1.72, 0.067, -4.58]}, {"time": 0.2667, "x": 15.24, "y": -4.58, "curve": [0.567, 15.24, 0.867, -3.95, 0.567, -4.58, 0.867, 1.72]}, {"time": 1.1667, "x": -3.95, "y": 1.72}]}, "RU_R": {"translate": [{"x": -10.88, "curve": [0.073, -24.02, 0.161, -34.81, 0.073, 0, 0.161, 0]}, {"time": 0.2333, "x": -34.81, "curve": [0.427, -34.81, 0.607, 40.42, 0.427, 0, 0.607, 0]}, {"time": 0.8, "x": 40.44, "curve": [0.921, 40.45, 1.046, 11.2, 0.921, 0, 1.046, 0]}, {"time": 1.1667, "x": -10.87}], "scale": [{"x": 1.082, "y": 0.955, "curve": [0.073, 1.038, 0.161, 0.951, 0.073, 0.988, 0.161, 1.055]}, {"time": 0.2333, "x": 0.951, "y": 1.055, "curve": [0.33, 0.951, 0.403, 1.106, 0.33, 1.055, 0.403, 0.936]}, {"time": 0.5, "x": 1.106, "y": 0.936, "curve": [0.597, 1.106, 0.703, 0.951, 0.597, 0.936, 0.703, 1.055]}, {"time": 0.8, "x": 0.951, "y": 1.055, "curve": [0.897, 0.951, 1.003, 1.106, 0.897, 1.055, 1.003, 0.936]}, {"time": 1.1, "x": 1.106, "y": 0.936, "curve": [1.125, 1.106, 1.143, 1.096, 1.125, 0.936, 1.143, 0.944]}, {"time": 1.1667, "x": 1.082, "y": 0.955}]}, "RU_R2": {"translate": [{"x": 2.65, "curve": [0.098, -13.3, 0.204, -29.46, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -29.46, "curve": [0.493, -29.46, 0.673, 34.73, 0.493, 0, 0.673, 0]}, {"time": 0.8667, "x": 34.75, "curve": [0.964, 34.76, 1.071, 18.81, 0.964, 0, 1.071, 0]}, {"time": 1.1667, "x": 2.66}], "scale": [{"x": 1.106, "y": 0.936, "curve": [0.097, 1.106, 0.203, 0.951, 0.097, 0.936, 0.203, 1.055]}, {"time": 0.3, "x": 0.951, "y": 1.055, "curve": [0.397, 0.951, 0.47, 1.106, 0.397, 1.055, 0.47, 0.936]}, {"time": 0.5667, "x": 1.106, "y": 0.936, "curve": [0.663, 1.106, 0.77, 0.951, 0.663, 0.936, 0.77, 1.055]}, {"time": 0.8667, "x": 0.951, "y": 1.055, "curve": [0.963, 0.951, 1.07, 1.106, 0.963, 1.055, 1.07, 0.936]}, {"time": 1.1667, "x": 1.106, "y": 0.936}]}, "RU_R3": {"translate": [{"x": 13.79, "curve": [0.122, -1.68, 0.246, -22.26, 0.122, 0, 0.246, 0]}, {"time": 0.3667, "x": -22.26, "curve": [0.56, -22.26, 0.74, 30.57, 0.56, 0, 0.74, 0]}, {"time": 0.9333, "x": 30.59, "curve": [1.006, 30.59, 1.095, 23.14, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 13.79}], "scale": [{"x": 1.082, "y": 0.955, "curve": [0.024, 1.096, 0.042, 1.106, 0.024, 0.944, 0.042, 0.936]}, {"time": 0.0667, "x": 1.106, "y": 0.936, "curve": [0.163, 1.106, 0.27, 0.951, 0.163, 0.936, 0.27, 1.055]}, {"time": 0.3667, "x": 0.951, "y": 1.055, "curve": [0.463, 0.951, 0.57, 1.106, 0.463, 1.055, 0.57, 0.936]}, {"time": 0.6667, "x": 1.106, "y": 0.936, "curve": [0.763, 1.106, 0.837, 0.951, 0.763, 0.936, 0.837, 1.055]}, {"time": 0.9333, "x": 0.951, "y": 1.055, "curve": [1.006, 0.951, 1.095, 1.038, 1.006, 1.055, 1.095, 0.988]}, {"time": 1.1667, "x": 1.082, "y": 0.955}]}, "RU_L": {"translate": [{"x": -10.88, "curve": [0.073, -24.02, 0.161, -34.81, 0.073, 0, 0.161, 0]}, {"time": 0.2333, "x": -34.81, "curve": [0.427, -34.81, 0.607, 40.42, 0.427, 0, 0.607, 0]}, {"time": 0.8, "x": 40.44, "curve": [0.921, 40.45, 1.046, 11.2, 0.921, 0, 1.046, 0]}, {"time": 1.1667, "x": -10.87}], "scale": [{"x": 1.082, "y": 0.955, "curve": [0.073, 1.038, 0.161, 0.951, 0.073, 0.988, 0.161, 1.055]}, {"time": 0.2333, "x": 0.951, "y": 1.055, "curve": [0.33, 0.951, 0.403, 1.106, 0.33, 1.055, 0.403, 0.936]}, {"time": 0.5, "x": 1.106, "y": 0.936, "curve": [0.597, 1.106, 0.703, 0.951, 0.597, 0.936, 0.703, 1.055]}, {"time": 0.8, "x": 0.951, "y": 1.055, "curve": [0.897, 0.951, 1.003, 1.106, 0.897, 1.055, 1.003, 0.936]}, {"time": 1.1, "x": 1.106, "y": 0.936, "curve": [1.125, 1.106, 1.143, 1.096, 1.125, 0.936, 1.143, 0.944]}, {"time": 1.1667, "x": 1.082, "y": 0.955}]}, "RU_L2": {"translate": [{"x": 2.65, "curve": [0.098, -13.3, 0.204, -29.46, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -29.46, "curve": [0.493, -29.46, 0.673, 34.73, 0.493, 0, 0.673, 0]}, {"time": 0.8667, "x": 34.75, "curve": [0.964, 34.76, 1.071, 18.81, 0.964, 0, 1.071, 0]}, {"time": 1.1667, "x": 2.66}], "scale": [{"x": 1.106, "y": 0.936, "curve": [0.097, 1.106, 0.203, 0.951, 0.097, 0.936, 0.203, 1.055]}, {"time": 0.3, "x": 0.951, "y": 1.055, "curve": [0.397, 0.951, 0.47, 1.106, 0.397, 1.055, 0.47, 0.936]}, {"time": 0.5667, "x": 1.106, "y": 0.936, "curve": [0.663, 1.106, 0.77, 0.951, 0.663, 0.936, 0.77, 1.055]}, {"time": 0.8667, "x": 0.951, "y": 1.055, "curve": [0.963, 0.951, 1.07, 1.106, 0.963, 1.055, 1.07, 0.936]}, {"time": 1.1667, "x": 1.106, "y": 0.936}]}, "RU_L3": {"translate": [{"x": 13.79, "curve": [0.122, -1.68, 0.246, -22.26, 0.122, 0, 0.246, 0]}, {"time": 0.3667, "x": -22.26, "curve": [0.56, -22.26, 0.74, 30.57, 0.56, 0, 0.74, 0]}, {"time": 0.9333, "x": 30.59, "curve": [1.006, 30.59, 1.095, 23.14, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 13.79}], "scale": [{"x": 1.082, "y": 0.955, "curve": [0.024, 1.096, 0.042, 1.106, 0.024, 0.944, 0.042, 0.936]}, {"time": 0.0667, "x": 1.106, "y": 0.936, "curve": [0.163, 1.106, 0.27, 0.951, 0.163, 0.936, 0.27, 1.055]}, {"time": 0.3667, "x": 0.951, "y": 1.055, "curve": [0.463, 0.951, 0.57, 1.106, 0.463, 1.055, 0.57, 0.936]}, {"time": 0.6667, "x": 1.106, "y": 0.936, "curve": [0.763, 1.106, 0.837, 0.951, 0.763, 0.936, 0.837, 1.055]}, {"time": 0.9333, "x": 0.951, "y": 1.055, "curve": [1.006, 0.951, 1.095, 1.038, 1.006, 1.055, 1.095, 0.988]}, {"time": 1.1667, "x": 1.082, "y": 0.955}]}, "tun": {"rotate": [{"value": -3.3, "curve": [0.027, -3.3, 0.058, 14.42]}, {"time": 0.2333, "value": 14.42, "curve": [0.245, 14.42, 0.256, 13.41]}, {"time": 0.2667, "value": 13.36, "curve": [0.567, 12.16, 0.867, -3.3]}, {"time": 1.1667, "value": -3.3}]}, "arm_R": {"rotate": [{"value": 0.5, "curve": [0.074, 1.35, 0.16, 2.04]}, {"time": 0.2333, "value": 2.04, "curve": [0.429, 2.04, 0.604, -2.79]}, {"time": 0.8, "value": -2.79, "curve": [0.923, -2.79, 1.045, -0.91]}, {"time": 1.1667, "value": 0.5}]}, "arm_R2": {"rotate": [{"value": -0.38, "curve": [0.099, 0.82, 0.203, 2.04]}, {"time": 0.3, "value": 2.04, "curve": [0.496, 2.04, 0.671, -2.79]}, {"time": 0.8667, "value": -2.79, "curve": [0.965, -2.79, 1.07, -1.59]}, {"time": 1.1667, "value": -0.38}]}, "arm_R3": {"rotate": [{"value": -1.98, "curve": [0.123, 0.74, 0.245, 4.35]}, {"time": 0.3667, "value": 4.35, "curve": [0.562, 4.35, 0.771, -4.93]}, {"time": 0.9667, "value": -4.93, "curve": [1.04, -4.93, 1.094, -3.62]}, {"time": 1.1667, "value": -1.98}]}, "arm_R4": {"rotate": [{"value": -3.44, "curve": [0.148, -0.82, 0.287, 4.35]}, {"time": 0.4333, "value": 4.35, "curve": [0.629, 4.35, 0.838, -4.93]}, {"time": 1.0333, "value": -4.93, "curve": [1.083, -4.93, 1.119, -4.33]}, {"time": 1.1667, "value": -3.44}]}, "arm_R5": {"rotate": [{"value": -4.49, "curve": [0.172, -2.7, 0.329, 4.35]}, {"time": 0.5, "value": 4.35, "curve": [0.696, 4.35, 0.904, -4.93]}, {"time": 1.1, "value": -4.93, "curve": [1.125, -4.93, 1.143, -4.75]}, {"time": 1.1667, "value": -4.49}]}, "arm_R6": {"rotate": [{"value": -3.44, "curve": [0.148, -0.82, 0.287, 4.35]}, {"time": 0.4333, "value": 4.35, "curve": [0.629, 4.35, 0.838, -4.93]}, {"time": 1.0333, "value": -4.93, "curve": [1.083, -4.93, 1.119, -4.33]}, {"time": 1.1667, "value": -3.44}]}, "arm_L2": {"rotate": [{}]}, "leg_R3": {"rotate": [{}]}, "leg_L3": {"rotate": [{}]}, "tunround": {"translate": [{"x": 221.95, "curve": [0.027, 221.95, 0.058, -572.11, 0.027, 0, 0.058, 0]}, {"time": 0.2333, "x": -572.11, "curve": [0.544, -572.11, 0.856, 221.95, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 221.95}]}, "bodyround": {"translate": [{"x": -22.66, "y": -82.13, "curve": [0.031, -22.66, 0.067, 247.33, 0.031, -82.13, 0.067, 224.18]}, {"time": 0.2667, "x": 247.33, "y": 224.18, "curve": [0.567, 247.33, 0.867, -22.66, 0.567, 224.18, 0.867, -82.13]}, {"time": 1.1667, "x": -22.66, "y": -82.13}]}, "headround3": {"translate": [{"x": 40.29, "curve": [0.039, 40.29, 0.084, 290.75, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 290.75, "curve": [0.611, 290.75, 0.889, 40.29, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 40.29}]}, "headround": {"translate": [{"y": 159.84, "curve": [0.111, 0, 0.222, 0, 0.039, 159.84, 0.084, 482]}, {"time": 0.3333, "y": 482, "curve": [0.611, 0, 0.889, 0, 0.611, 482, 0.889, 159.84]}, {"time": 1.1667, "y": 159.84}]}, "leg_L5": {"rotate": [{"value": 0.14}]}, "leg_L6": {"rotate": [{"value": -0.38}]}, "leg_R5": {"rotate": [{"value": -0.02}]}, "sh_L2": {"rotate": [{"value": -0.16}]}, "sh_L3": {"rotate": [{"value": -0.05}]}, "arm_L4": {"rotate": [{"value": 0.37, "curve": [0.027, 0.37, 0.058, 11.4]}, {"time": 0.2333, "value": 11.4, "curve": [0.544, 11.4, 0.856, 0.37]}, {"time": 1.1667, "value": 0.37}], "translate": [{"x": -5.9, "y": -7.78, "curve": [0.027, -5.9, 0.058, 48.28, 0.027, -7.78, 0.058, -27.96]}, {"time": 0.2333, "x": 48.28, "y": -27.96, "curve": [0.544, 48.28, 0.856, -5.9, 0.544, -27.96, 0.856, -7.78]}, {"time": 1.1667, "x": -5.9, "y": -7.78}]}, "eyebrow_L": {"rotate": [{"curve": [0.027, 0, 0.058, -13.11]}, {"time": 0.2333, "value": -13.11, "curve": [0.544, -13.11, 0.856, 0]}, {"time": 1.1667}], "translate": [{"curve": [0.027, 0, 0.058, 3.31, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 3.31, "curve": [0.544, 3.31, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.027, 1, 0.058, 0.99, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.99, "curve": [0.544, 0.99, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.027, 0, 0.058, -23.68]}, {"time": 0.2333, "value": -23.68, "curve": [0.544, -23.68, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.027, 0, 0.058, 18.58]}, {"time": 0.2333, "value": 18.58, "curve": [0.544, 18.58, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.027, 0, 0.058, 4.25]}, {"time": 0.2333, "value": 4.25, "curve": [0.544, 4.25, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.027, 1, 0.058, 0.99, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.99, "curve": [0.544, 0.99, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.027, 0, 0.058, 2.63, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 2.63, "curve": [0.544, 2.63, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.027, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-1.77033, -0.16028, -1.76983, -0.16043, -0.74051, -0.08644, -0.74031, -0.08648, -0.59465, -0.05055, -0.59456, -0.05061, -0.59465, -0.05055, -0.59456, -0.05061, -0.26447, -0.0295, -0.26442, -0.02956, -0.09243, -0.00813, -0.09243, -0.00816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.28643, -0.05603, -0.2863, -0.05606, -0.90766, -0.1855, -0.90755, -0.18556, -1.64507, -0.29233, -1.64494, -0.29245, -2.09028, -0.39163, -2.08993, -0.39177, -2.25542, -0.35181, -2.25483, -0.35193, -2.28591, -0.28679, -2.28528, -0.28698, 0, 0, 0, 0, -0.60088, -0.06466, -0.60086, -0.06471, -1.21509, -0.07232, -1.21496, -0.07245, -1.81914, -0.07925, -1.81903, -0.07935, -2.15119, -0.09673, -2.15093, -0.09684, -2.27062, -0.11121, -2.27034, -0.11125, -1.9936, -0.1798, -1.99314, -0.17985, -1.23618, -0.11747, -1.23581, -0.11757, -0.77087, -0.02611, -0.77072, -0.02619, -0.31096, -0.03361, -0.31094, -0.03366], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.027, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-1.10321, 0.07845, -1.10295, 0.0784, -1.85129, 0.02528, -1.851, 0.0252, -2.48052, 0.07795, -2.47991, 0.07785, -2.29372, 0.22283, -2.29294, 0.22273, -2.07863, 0.10078, -2.07828, 0.10062, -1.21935, 0.20565, -1.21913, 0.20552, -0.41266, 0.03617, -0.4126, 0.03609, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13553, 0.00159, -0.1355, 0.00157, -0.27341, 0.03001, -0.2733, 0.02998, -0.81571, 0.10387, -0.81547, 0.10382, -0.13708, 0.00888, -0.13708, 0.00887, -0.73135, 0.0001, -0.73126, 1e-05, -1.43653, 0.02195, -1.43627, 0.02189, -2.15207, -0.14953, -2.15187, -0.1496, -2.61391, -0.19039, -2.61369, -0.19044, -2.54039, -0.29454, -2.53987, -0.29464, -1.8809, -0.10097, -1.88036, -0.10105, -0.66245, 0.02523, -0.66226, 0.02519, -0.31009, -0.01381, -0.31007, -0.01385, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.0364, -0.00321, -0.0364], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.027, 0, 0.058, 1]}, {"time": 0.2333, "offset": 386, "vertices": [-0.57907, -0.05107, -0.57886, -0.05112, -1.24929, -0.17011, -1.24896, -0.17017, -1.81031, -0.1674, -1.81004, -0.16746, -2.08105, -0.23616, -2.08073, -0.23623, -1.9975, -0.25114, -1.99722, -0.25127, -1.60396, -0.21648, -1.60383, -0.21654, -0.92168, -0.06642, -0.92153, -0.06647, -0.54861, -0.05589, -0.54854, -0.05593, -0.21208, -0.05609, -0.21193, -0.05611, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.46981, -0.06358, -0.46981, -0.06361, -1.30445, -0.00469, -1.30425, -0.00471, -1.93689, -0.10479, -1.93665, -0.10483, -2.44375, -0.24909, -2.44364, -0.24915, -2.42794, -0.30295, -2.42772, -0.30301, -2.12459, -0.23195, -2.1245, -0.23198, -1.47923, -0.1528, -1.47908, -0.15284, -0.40398, -0.05779, -0.40398, -0.05781, -0.13878, -0.05646, -0.13878, -0.05648], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.027, 0, 0.058, 1]}, {"time": 0.2333, "offset": 64, "vertices": [0.2522, 0.37578, 0.25488, 0.37564, -0.15979, 0.06425, -0.15796, 0.06412, -0.35791, -0.13661, -0.35571, -0.13681, -0.35339, -0.00089, -0.35205, -0.00098, -0.44568, -0.03377, -0.4436, -0.03391, -0.44568, -0.03377, -0.4436, -0.03391, -0.44568, -0.03377, -0.4436, -0.03391, -0.3479, 0.04263, -0.34692, 0.04257, -0.24487, 0.16261, -0.2439, 0.16254, -0.26831, 0.03287, -0.26611, 0.03275, 0.72534, -0.40613, 0.72656, -0.40619, 0.97668, -0.04428, 0.97803, -0.04465, 1.5238, -0.05664, 1.52563, -0.0574, 2.06677, 0.00798, 2.06909, 0.00708, 2.06677, 0.00798, 2.06909, 0.00708, 1.34045, 0.24002, 1.34204, 0.23932, 1.44971, -0.30984, 1.45166, -0.31018, 1.33362, 0.28642, 1.33691, 0.28604, 0.3573, -0.23703, 0.35864, -0.23715, 0.25378, -0.0311, 0.25464, -0.03116, -0.20593, -0.10356, -0.20581, -0.10361, -0.63049, -0.0995, -0.62769, -0.0997, -0.63049, -0.0995, -0.62769, -0.0997, -0.63049, -0.0995, -0.62769, -0.0997, -0.85327, -0.11639, -0.84937, -0.11667, -0.45618, -0.12083, -0.45435, -0.121, 0, 0, 0, 0, 0.34058, 0.1515, 0.34155, 0.15149, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 1.58032, -0.10483, 1.57959, -0.10464, 1.94592, -0.01688, 1.94458, -0.0166, 2.03845, 0.01602, 2.03687, 0.01633, 1.31836, -0.07286, 1.31714, -0.07266, -0.42017, -0.02464, -0.41748, -0.02484, 1.38635, 0.00108, 1.38867, 0.00079, 0.57507, -0.22054, 0.57617, -0.22064, 0.3573, -0.23703, 0.35864, -0.23715, 0.3573, -0.23703, 0.35864, -0.23715, 0.3573, -0.23703, 0.35864, -0.23715, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 0.76038, 0.42213, 0.7627, 0.42206, 1.32678, -0.35068, 1.32813, -0.35104, 2.12268, -0.10954, 2.12402, -0.11017, 2.12268, -0.10954, 2.12402, -0.11017, 1.34094, -0.26624, 1.34277, -0.26669, 0.79053, 0.1187, 0.79199, 0.11865, 0.63574, -0.21021, 0.6355, -0.21011, 0.63574, -0.21021, 0.6355, -0.21011], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}}}}}}