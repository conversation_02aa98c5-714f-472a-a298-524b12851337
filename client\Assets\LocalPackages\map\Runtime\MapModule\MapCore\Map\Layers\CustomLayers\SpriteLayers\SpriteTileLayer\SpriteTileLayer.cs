﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public class SpriteTileLayer : MapLayerBase
    {
        public SpriteTileLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.SpriteTileLayerData;

            TileData[] tileDatas = null;
            if (sourceLayer.tiles != null)
            {
                int n = sourceLayer.tiles.Length;
                int rows = sourceLayer.zTileCount;
                int cols = sourceLayer.xTileCount;
                tileDatas = new TileData[rows * cols];
                int spriteTemplateID = 0;
                for (int y = 0; y < rows; ++y)
                {
                    for (int x = 0; x < cols; ++x)
                    {
                        int idx = y * cols + x;
                        spriteTemplateID = sourceLayer.tiles[idx];
                        if (spriteTemplateID != 0)
                        {
                            var spriteTemplate = config.BaseObject.FindObject(spriteTemplateID) as config.SpriteTemplate;
                            var spriteTemplateData = map.FindObject(spriteTemplateID) as SpriteTemplate;
                            Debug.Assert(spriteTemplateData != null);
                            tileDatas[idx] = new SpriteTileData(spriteTemplateData);
                        }
                    }
                }
            }

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, sourceLayer.zTileCount, sourceLayer.xTileCount, sourceLayer.tileWidth, sourceLayer.tileHeight, sourceLayer.gridType, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));
            mLayerData = new SpriteTileLayerData(header, map, tileDatas);
            mLayerView = SpriteTileLayerView.Create(mLayerData, new Vector2(0.5f, 0.5f));
            mLayerView.active = layerData.active;
        }

        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }

        public void ChangeMaterialColor(Color32 oldColor, Color32 newColor)
        {
            mLayerView.ChangeMaterialColor(oldColor, newColor);
        }

        public override bool Contains(int objectID)
        {
            return false;
        }

        public override int GetCurrentLOD()
        {
            return 0;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            return lodChanged;
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public SpriteTileLayerData layerData { get { return mLayerData; } }
        public SpriteTileLayerView layerView { get { return mLayerView; } }
        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override int horizontalTileCount { get { return mLayerData.horizontalTileCount; } }
        public override int verticalTileCount { get { return mLayerData.verticalTileCount; } }
        public override float tileWidth { get { return mLayerData.tileWidth; } }
        public override float tileHeight { get { return mLayerData.tileHeight; } }
        public override GridType gridType { get { return mLayerData.gridType; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override int lodCount => mLayerData.lodCount;

        SpriteTileLayerData mLayerData;
        SpriteTileLayerView mLayerView;
    }
}

#endif