﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    public class MapLayerXZHexagonalCoordinateConverter : MapLayerHexagonalCoordinateConverter {
        Vector3 FromCoordinateToLocalPosition(int x, int y) {
            float px = mSize * Mathf.Sqrt(3) * (x + 0.5f * (y & 1));
            float pz = mSize * 3 / 2.0f * y;
            return new Vector3(px, 0, pz);
        }

        public override Vector2Int FromWorldPositionToCoordinate(Vector3 position) {
            float px = position.x - mTileOffsetX;
            float pz = position.z - mTileOffsetY;

            var q = (Mathf.Sqrt(3) / 3.0f * px - 1.0f / 3 * pz) / mSize;
            var r = (2.0f / 3 * pz) / mSize;
            Vector3 cube = HexagonalUtils.cube_round(q, -q - r, r);
            return HexagonalUtils.cube_to_oddr((int)cube.x, (int)cube.y, (int)cube.z);
        }

        public override Vector2Int FromWorldPositionToCoordinateUpperBounds(Vector3 position)
        {
            throw new System.NotImplementedException();
        }

        public override Vector3 FromCoordinateToWorldPosition(int x, int y) {
            float tileWidth = mLayerData.tileWidth;
            float tileHeight = mLayerData.tileHeight;
            int cols = mLayerData.horizontalTileCount;
            int rows = mLayerData.verticalTileCount;
            float totalWidth = mLayerData.GetLayerWidthInMeter(0);
            float totalHeight = mLayerData.GetLayerHeightInMeter(0);
            return FromCoordinateToLocalPosition(x, y) - new Vector3(- mTileOffsetX, 0, - mTileOffsetY);
        }
    }
}
