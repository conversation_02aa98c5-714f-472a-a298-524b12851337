﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionRemoveRegion : EditorAction
    {
        public ActionRemoveRegion(int layerID, int dataID)
        {
            mLayerID = layerID;
            mDataID = dataID;
            var data = Map.currentMap.FindObject(dataID) as RegionData;
            var navMeshObstacleOutline = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            mNavMeshObstacleOutline = new List<Vector3>(navMeshObstacleOutline.Count);
            mNavMeshObstacleOutline.AddRange(navMeshObstacleOutline);
            var objectPlacementOutline = data.GetOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle);
            mObjectPlacementOutline = new List<Vector3>(objectPlacementOutline.Count);
            mObjectPlacementOutline.AddRange(objectPlacementOutline);
            mMeshVertices = data.meshVertices;
            mMeshIndices = data.meshIndices;
            mMeshColors = data.vertexColors;
            mInnerColor = data.innerColor;
            mOuterColor = data.outerColor;
            mIsExtendable = data.IsExtendable();
            mType = data.type;
            mNumber = data.number;
            mMaterial = data.material;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RegionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RegionLayer;
            if (layer == null)
            {
                return false;
            }
            var outlineData0 = new OutlineData(mNavMeshObstacleOutline);
            var outlineData1 = new OutlineData(mObjectPlacementOutline);
            var outlineDatas = new OutlineData[2] { outlineData0, outlineData1 };
            var data = new RegionData(mDataID, Map.currentMap, outlineDatas, layer.displayVertexRadius, mIsExtendable, mMeshVertices, mMeshIndices, mMeshColors, mInnerColor, mOuterColor, mType, mNumber, mMaterial);
            layer.AddObject(data);
            return true;
        }

        int mLayerID;
        int mDataID;
        bool mIsExtendable;
        List<Vector3> mNavMeshObstacleOutline;
        List<Vector3> mObjectPlacementOutline;
        Vector3[] mMeshVertices;
        int[] mMeshIndices;
        Color[] mMeshColors;
        Color mInnerColor;
        Color mOuterColor;
        RegionType mType;
        int mNumber;
        Material mMaterial;
    }
}

#endif