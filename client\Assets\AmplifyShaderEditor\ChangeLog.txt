Release Notes:
v1.8.0 rev 00
:* Fixes:
    * Fixed SSAO issue over Universal 10 PBR template
    * Fixed issue with GPU instancing and Stereo VR on multiple Builtin Legacy templates

v1.8.7 rev 18:
* Fixes:
    * Fixed missing ASE Pragma tag over HDRP 10 Decal template

* Improvements:
    * Added new DepthNormals and <PERSON>uffer passes over Universal 10 PBR template

v1.8.7 rev 17:
* Fixes:
    * Fixed issue with debug display across multiple HDRP 10 templates
    * Fixed 'Compile and show code' button not working over ASE custom shader inspector on Unity 2020.2

* Improvements:
    * Added new strip #line directives over ASE custom shader inspector on Unity 2020.2

v1.8.7 rev 16:
* Fixes:
    * Fixed multiple _BlendMode issues across all HDRP templates
    * Fixed issue with using custom meshes on standard surface custom material inspector over 2020.1 and above

v1.8.7 rev 15:
* Fixes:
    * Automatically converting obsolete custom inspectors from HDRP v.7/8/9 when reading shaders into HDRP 10
    * Bumped internal HDRP version control to force new HDRP 10 templates to be unpacked

v1.8.7 rev 14:
* New Node:
    * Sticky Note
        * Allows user to add notes directly on the ASE canvas

* Improvements:
    * Updated HDRP templates to be compatible with SRP 10

* Fixes:
    * Fixed issue with inverted depth dir on Reconstruct World Position From Depth SF over HDRP 8 and above
    * Fixed minor issue over Tags foldout on templates node properties
    * Added missing SRP versions into ASE Package Manager
        * SRP v.8.3.1
        * SRP v.7.5.1
        * SRP v.7.5.2
        * SRP v.9.0.0 Preview 71

v1.8.7 rev 13:
* Fixes:
    * Fixed issue with capturing canvas screen shot while maximized
    * Removed multiple incorrect port restrictions over 'Triplanar Sample' node
    * Fixed issue on incorrect template behavior if on a template inline option its internal property is selected via the the inline dropdown
    * Fixed issue on template options type set to inline but with no inline property selected
    * Fixed issue on losing inline properties between canvas

* Improvements:
    * 'Dither' node now has new SS input port to be able to use custom sampler states

v1.8.7 rev 12:
* Fixes:
    * Fixed issue with removing Header attributes

v1.8.7 rev 11:
* New Sample:
    * Transparent Cutout for both URP and HDRP

v1.8.7 rev 10:
* Fixes:
    * Fixed issue on user not being able to use the dot/period character on Header attributes
    * Fixed issue on Header attributes not being correctly shown on 'Color' node

v1.8.7 rev 09:
* Improvements:
    * User can now specify multiple headers for each property

v1.8.7 rev 08:
* Improvements:
    * Added new No Interpolation option into 'Vertex To Fragment' node
    * Added new Header attribute for property type nodes
    * Added new No Keyword Toggle attribute for 'Float' node
    * Added new Remap Slider custom drawer for 'Vector4' node
        * User must specify min limit and max limit on Z and W coordinate respectively

v1.8.7 rev 07:
* Fixes:
    * Fixed issue on 'Texture Sampler' node incorrectly generating sampler states when its R,G,B,A ports were being used

v1.8.7 rev 06:
* Fixes:
    * Fixed compilation error on Built-in RP with 'Grab Screen Color' node when Auto-Register is turned on over Unity 5.6 and above

v1.8.7 rev 05:
* Fixes:
    * Fixed issue on 'Indirect Diffuse Light' node not showing correct results over Builtin templates on Unity 2019
    * Fixed issue on SubShader body not being correctly filled if main master node's pass is disabled
    * Fixed issue on incorrect clip over holes usage on URP Terrain sample

v1.8.7 rev 04:
* New Shader Function:
    * Normal From Texture

Fixes:
    * Fixed issues on Normal From Height shader function
    * Fixed issue on ASE window attempting to import templates when entering in Play mode
    * Fixed issue on 'Function Switch' node not updating correctly when on Reference mode

* Improvements:
    * Empty textfield value on 'Function Subtitle' node now removes the sub-title over its shader function node
    * Added new Clear Log on Update option into ASE preferences (on by default)
        * Clears an ASE log window each time the user hits the Update button

v1.8.7 rev 03:
* Shader Functions:
    * Removed High Quality option from Normal From Height shader function
        * This will be later added on a separate shader function

* Fixes:
    * Inline code from set by 'Custom Expression' node now placed under their own scope { (...) } to prevent duplication errors
    * Fixed issue on Procedural Texture type not having a texture to wire correspondence over 'Texture Sampler' node

v1.8.7 rev 02:
* IMPORTANT - Node renamed:
    * 'Break To Components' node renamed to 'Split' to decrease its canvas size

* New Shader Functions:
    * Bicubic Precompute
    * Bicubic Sample

* Fixes:
    * Fixed issue on 'Break To Components' node disabling connections on loading it from disk
    * Fixed issue on duplicate error thrown when multiple Normal From Height shader functions are used

v1.8.7 rev 01:
* Shader Function Removed:
    * Procedural Sample 2D Array was removed
        * Procedural Sample 2D now renamed to Procedural Sample and has internal options that allow usage with 2D Array and more

* Improvements:
    * Added Fast and High Quality options to Normal From Height shader function
    * Improved error messages thrown by 'Custom Expression' node
    * Renamed Procedural Sample 2D to Procedural Sample and added Sample Mode option
        * Planar 2D
        * Planar 2D Array
        * Triplanar 2D
        * Triplanar 2D Array

* Fixes:
    * Fixed issue on only converting CRLF to LF and not CR to LF when reading templates or using pasted code on 'Custom Expression' nodes
        * This lead to inconsistent line ending warnings being thrown
    * Fixed issue on some warnings from 'Custom Expression' node no being clickable on log to select which node they were thrown from

v1.8.7 rev 00:
* New Shader Function:
    * Procedural Sample 2D Array

* Improvements:
    * Improved Procedural Sample 2D shader function

v1.8.6 rev 02:
* Fixes:
    * Fixed division by zero warning on 'Gradient Sample' node
    * Fixed issue on 'Static Switch ' node incorrectly adding suffixes into fetched custom keywords
    * Fixed issue with attempting to register duplicate property names when reading 'Static Switch' node meta from old ASE versions

v1.8.6 rev 01:
* New Shader Function:
    * Procedural Sample 2D

* Fixes:
    * Fixed issue with duplicate generated code over 'World Reflection' node

v1.8.6 rev 00:
* Improvements:
    * Improved Plane Clip sample

v1.8.5 rev 06:
* Fixes:
    * Locking 'Decode Depth Normals' usage to Builtin pipeline and informing of that when used on SRP
        * Was currently generating compilation errors over SRP
    * Fixed issue on Auto-Register flag over property nodes not being correctly registered in certain cases
    * Fixed issue where graph sampling macro flag was incorrectly being set by non main master nodes

* Improvements:
    * Auto-register check on texture property type node also register sampler if 'Use Sampling Macros' flag is enabled on master node
    * Adding support for mask maps over 'Four Splats First Pass Terrain' shader function
        * Only available on SRP and output node must have 'Use Sampling Macros' flag turned on

v1.8.5 rev 05:
* Fixes:
    * Fixed issue with incorrect values returned from nodes using internal 'Texture Object' instances
    * Fixed duplicate uv declarations issue when being requested different sizes by 'Texture Sampler' nodes
    * Fixed duplicate directives issue caused by adding directives on both 'Outline' node generated surface and main one

v1.8.5 rev 04:
* Fixes:
    * Fixed issue on texture node types auto-sampler generator

v1.8.5 rev 03:
* New Sample:
    * Plane Clip

* Fixes:
    * Fixed issue on 'Blend Normals' node preview not taking SRP options into account
    * Fixed visual issue on node properties menu over nodes:
        * Template Parameter 
        * Template Local Var
        * Template Vertex Data
        * Template Fragment Data

* Improvements:
    * Simplified 'Template Parameter' usage
        * User no longer need to choose scope to parameters to be available
        * New Advanced View toggle added to switch to old view
    * Added widget on 'Texture Coordinates' node to change output size directly from the node

v1.8.5 rev 02:
* New Community Shader Function
    * Depth Masked Refraction by @saismirk

Fixes:
    * Fixed incorrect uv variable name being used when set is greater that 3
    * Fixed incorrect swizzling when using texture sets on vertex data over surface shaders
    * Fixed issue over texture array tool on not being able to use compressed formats

v1.8.5 rev 01:
* Fixes:
    * Fixed issue regarding uv coordinates usage with tessellation on URP PBR template

* Improvements:
    * Replaced fmod instruction usage on 'Remainder' node by faster true remainder calculation

v1.8.5 rev 00:
* Fixes:
    * Fixed desync issue between graph and master node sampling macro flag
    * Fixed unknown INSTANCEID_SEMANTICS enum  value being thrown on legacy HDRP

v1.8.4 rev 04:
* Fixes:
    * Fixed issue on 'Substance Sampler' node
        * Fixed incorrectly drawn preview on Unity 2018 and above
        * Prevented incorrect ports types being set when texture order is internally changed on Substance
    * Fixed variable redefinition on 'Gradient' node when used on shader functions
    * Fixed duplicate issue when declaring cbuffers on both outline and main surface shaders
    * Fixed URP Terrain sample layer composition

* Improvements:
    * 'FWidth' node now throws an ASE console error when connected to non-fragment ports
        * Also generates dummy value to prevent compilation error
    * Added terrain holes support on Four Splats First Pass Terrain shader function
        * Activate Holes option
    * Added new Final Color x Alpha option on URP PBR template
        * Required to correctly blend terrain layers

v1.8.4 rev 03:
* Fixes:
    * Fixed Color Mask MRT Id not being written on inline properties
        * Caused HDRP Lit shaders to become invisible when compiled in some versions

v1.8.4 rev 02:
* New Template:
    * Legacy/Lit
        * Mimics behavior of standard surface but without some of its limitations
            * Can override Baked GI
            * Removes limitation of using nodes like 'Indirect Specular Light' only on Custom Lighting

* Fixes:
    * Added missing support for per-pixel normals in terrain shaders for URP (still requires adding the keyword separately)
    * Fixed issue with alpha clip threshold not working properly in all cases when using the HDRP Lit template
    * Fixed issue with template loading that was not catching global variable in certain situations (API)
    * Fixed rare issue where changing shader templates could cause the options to fail to load properly due to not finding the new master node
    * Fixed issue on Depth Texture macro declaration being incorrectly duplicated when already present on template
        * Now depth related nodes can be correctly used on Particle Alpha Blended template
    * Fixed issue on uncaught exception when attempting to load an in-existent Assembly-CSharp-Editor assembly
        * Could happen when attempting to load invalid nodes
    * Fixed issue on not being able to connect 'Texture Object' nodes to 'Register Local Var' node
    * Fixed issue on vertex instructions generated by outline fragment ports to be correctly written on its vertex code
        * Fixes 'Camera Depth Fade' usage on 'Outline' node

* Improvements:
    * Updated the Four Splats First Pass Terrain shader function to include a new option for SRP terrain to allow per pixel normals
    * 'Static Switch' node
        * Now allows editing of certain parameters while in Fetch mode 
        * Now allow the explicit naming of the keyword created besides the automatic one with a lock button
    * Forcing vertex position w component to 1 when setting a new offset or position over Standard Surface type shaders
        * Prevent real-time shadow issues on vertex manipulation

v1.8.4 rev 01:
* Improvements:
    * Added "Open in Shader Editor" button into SRP materials (needs shader save)
    * Now SRP materials update ASE editor property values while the editor is open (needs shader save)
    * Remove dependencies for custom shader inspector (now, allows deleting the file)

* Fixes:
    * Fixed Dots instancing option in URP unlit
    * Fixed UV generation in Mesh Decals template
    * Fixed Normals and Emission Bias in Mesh Decals template

v1.8.4 rev 00:
* New HDRP Samples:
    * Added new 'Decal Muddy Ground' and 'Decal Scifi Panel' HDRP samples using various types of decals

* Improvements:
    * Added automatic 'Open and Save All' button in shader function files that tries to compiled all dependency shaders and shader functions
    * Texture Array tool now allows to create an asset file representing all options in order to save work for later
    * Texture Array no longer locks width and height at power of two for more recent versions of unity
    * Added clip shadow threshold and alpha to coverage options to URP PBR and URP Unlit shaders

* Fixes:
    * Fixed leak in the automatic template importer
    * Template importer now properly skips non-shaders files when checking for changes
    * Fixed issue with properties in fetch mode not loading correctly and making some property names hostage
    * Fixed issue with UVs generation in specific situations where it would create variables with the same name

v1.8.3 rev 03:
* Improvements:
    * Global macros sampling option has been removed in favor of another that is set by the shader (older shader load with this option OFF while new ones start with it ON)
    * Made property panel option labels flexible to the width of the panel
    * Some help text added to 'Sampler State' node

* Fixes:
    * Fixed issue with assembly definition files where nodes outside of the assembly would fail to load
    * Fixed issue of emission channel not working in HDRP decal template

v1.8.3 rev 02:
* New 'Sampler State' Node:
    * Allows using the default sampler state of a texture or the creation of a new one with specific options (versions prior to Unity 2018.1 have limited support)

* Improvements:
    * You can now use custom sampler states when sampling, however, in order for this to work you need to activate the option at Preferences > Amplify Shader Editor > Sampling Macros
        * Activating this may cause some issues in specific situations, namely, custom expressions that do sampling must be converted to use macros as well so for now this option is OFF by default
    * There are two new connection types, one for texture arrays and another for sampler states which are supported in function nodes and custom expression nodes
    * Texture Array node as been deprecated in favor of the regular 'Sampler' node which now supports texture arrays and works inside shader functions
    * 'Parallax Occlusion Mapping' node and 'Triplanar Sample' node now automatically detect if the input is a texture array and no longer use a toggle to turn ON the support

* Fixes:
    * Fixed issue with URP 2D templates accessing the wrong uv set
    * Fixed Bitangent calculation in SRP which wasn't taking tangent sign into account
    * Fixed issue with 'show and compile' button in shader inspector in unity 2020.1

v1.8.3 rev 01:
* Fixes:
    * Fixed issues over 'Substance Sample' node
        * Fixed compilation error on Unity 2018 and above
        * Fixed normal texture port detection on Unity 2018 and above

v1.8.3 rev 00:
* New HDRP Sample:
    * Added new 'TV' HDRP sample using Vector Displacement Maps (VDMs)

* New Shader Option:
    * URP PBR template now has a 'Fragment Normal Space' option that allows selecting between Object, World and Tangent space outputs for the master node

* Improvements:
    * Added normal space output option for 'Triplanar' node
    * Added help text for the PPS helper tool

* Fixes:
    * Fixed issue with world view dir calculation in URP PBR template
    * Fixed issue with normal unpacking and scaling in SRP
    * Removed HDRP dependency in ASE asmdef file
    * Fixed the mosaic PPS sample that was conflicting with assembly definition files
    * Fixed issues with focusing the editor window would sometimes select text fields or prevent nodes from being interactive
    * Fixed 'Diffusion Profile' for HDRP 6.X.X

v1.8.2 rev 01:
* Fixes:
    * Fixed Fixed issue with auto generated UVs sometimes generate the UVs twice and failing to compile

v1.8.2 rev 00:
* Fixes:
    * Fixed 'Rejection' node to support multiple length input types just like 'Projection' node does

v1.8.1 rev 04:
* New HDRP Template
    * Decal

* Improvements:
    * Added new port to 'Voronoi' node that outputs the UVs of the current voronoi cells
    * Added support for MRT for ColorMask, Blend and BlendOp in templates and template Options
    * AlphaToMask is now it's own separate module that can be activated independently  and supports inline properties
    * Changed UV creation to support pre-calculated UVs in templates (ie: decals)

* Fixes:
    * Fixed Texture Array Creator tool in order to properly allow signed and higher bit texture formats
    * Voronoi now properly creates UVs in all situations and preview was updated accordingly
    * Fixed issue with instancing macro not being detected properly and being included in the wrong place occasionally  generating errors in URP

v1.8.1 rev 03:
* Improvements:
    * Improved URP and HDRP templates in order to minimize their CBUFFER contents
    * Updated libraries for SRP 7.3.1 and 7.4.1

* Fixes:
    * Fixed issue with instance ID node not working correctly in URP due to templates not properly detecting the existing ones
    * Fixed issue with unused tessellation properties showing up in the shader inspector
    * Fixed issue with 'Reconstruct World Position From Depth' node that was preventing from compiling when used with 'Depth Fade' node
    * Fixed issue with 'Reconstruct World Position From Depth' node that wasn't working correctly in SRP 7.4.X and up

v1.8.1 rev 02:
* New Shader Functions:
    * And
    * Or
    * Projection
    * Rejection

* Improvements:
    * Added more description text to custom expression node
    * Live update doesn't kick in when editing a field
    * Shader Functions options label size resize according to the panel width

* Fixes:
    * Fixed 'Texture Sample' node and 'Texel Size' node previews so that they update when it's referenced texture changes
    * Fixed issue with certain namespaces not being available when Assembly Definitions files are present
    * Fixed samples by adding new Assembly Definition files for samples that contain their own scripts
    * Fixed issue with sampler states option not working if reference node was not compiled

v1.8.1 rev 01:
* Improvements:
    * Added AssemblyDefinition file and made it so nodes can be found in the main editor assembly besides ASE assembly
        * If you which to extend ASE with your own node and you already use your own assembly file you need to add ASE as a dependency

* Fixes:
    * Fixed issue with function nodes not saving their GUID correctly to the metadata
    * Fixed issue with function switch nodes not saving correctly when inputs were left unconnected
    * Fixed issue with reading options due to system cultural differences
    * Fixed issue where clipboard copy/pasting was not respecting system cultural differences

v1.8.1 rev 00:
* New Shader Options:
    * Added new option called 'DOTS Instancing' that provides initial support for hybrid renderer for URP and HDRP
        * Works for both latest V1 and V2 (check unity docs)
        * URP requires shader model 4.5 in order for it to work
        * Some properties nodes now have a new 'Hybrid Instanced' option when set to property mode

* Improvements:
    * CBuffer properties are now ordered by datatype for performance reasons

* Fixes:
    * Fixed issue with tessellation in HDRP where tessellation was not measuring the position correctly when relative camera was ON
    * Fixed issue with the new 'Field' option type that wasn't reading float values correctly in all situations due to system cultural differences
    * Fixed typo with the 'Trigonometry Operators' category

v1.8.0 rev 03:
* New Shader Options:
    * Added Translucency and Transmission options for URP
        * Options are based on Built-in renderer ports respectively
        * New fields are generated for both but can be manipulated as 'Inline Properties' in property panel

* New 'Compare' node:
    * Replaces all the now deprecated 'Compare XXX' nodes, this node should be easier to work with since it does ternary operations and allows for any kind of vector comparison

* Improvements:
    * 'Voronoi' node ID port now returns a vector 2 ID instead of a single float value

* Fixes:
    * Fixed issue with instanced properties marked as auto register not working inside shader functions
    * Fixed issue with 'Function Switch' node that was causing some nodes to run even if unconnected
    * Fixed issue with 'Decode Lightmap' node that was not compiling correctly in all situations
    * Other small changes and fixes to last revision

v1.8.0 rev 02:
* New Shader Options:
    * Added Tessellation options for URP and HDRP
        * Options are based on Built-in renderer options supporting Fixed, Distance-Based, Edge Length and Edge Length With Cull mode, with or without Phong displacement
        * By default Float fields are generated at the bottom of the materials inspector but can be replaced by 'Inline Properties' allowing for further field customization

* Improvements:
    * Templates API now supports new 'Field' options that create Int or Float fields that can act when values change
    * Templates API now support 'SetMaterialProperty' action that allows a 'Field' to interact with the value in the material inspector
    * Added Q as an alternative panning modifier key (for trackpad users) while Q+Alt zooms (order is important due to Alt being used for other actions)

* Fixes:
    * Fixed issue with Start Screen icon on MacOS editors
    * Fixed issue with templates not properly detecting floating values
    * Fixed issue with log window and palette window producing layout errors
    * Fixed issue with live editing mode not working properly with shader functions and producing errors
    * Fixed 'Texel Size' node issue where variable declaration was not respecting the SRP batcher and breaking it in the process
    * Fixed issue with auto spacing of shader function titles
    * Fixed/Changed refraction for both URP and Built-in due to issues with webGL version

v1.8.0 rev 01:
* Improvements:
    * Now 'Texel Size' and 'Texture Transfom' nodes also contain the 'None' option for consistency

* Fixes:
    * Fixed issue with start screen window in Unity 5.6
    * Fixed issue with 'Texture Coordinates' node generating multiple texture references in some occasions
    * Fixed 'Texture Coordinates' losing it's selected reference option on port connection

v1.8.0 rev 00:
* New Shader Functions:
    * Bacteria
    * Bricks Pattern
    * Dots Pattern
    * Grid
    * Herringbone
    * Hex Lattice
    * Houndstooth
    * Smooth Wave
    * Spiral
    * Stripes
    * Truchet
    * Whirl
    * Zig Zag

* Improvements:
    * Updated some shader functions to expose their internal data

* Fixes:
    * Fixed default value for start screen option
    * Fixed context menu showing shader templates not yet imported on first install
    * Fixed drawing issues with slider in 'Float' node in the new Unity UI

v1.7.9 rev 02:
* Improvements:
    * Added Refraction option with respective ports to URP that works like the built-in Refraction port
    * Added versioning to templates so that they can track changes and import new versions if necessary

* Fixes:
    * Fixed issue with start screen showing the wrong change log
    * Fixed issue with start screen not always respecting the selected option
    * Fixed issue with 'Fresnel' node when loading a shader from an older version of ASE
    * Fixed loop detection check for 'Get' node in certain situations
    * Fixed menu listing for templates with duplicated names
    * Fixed 'VertexID' node for HDRP causing errors due to motion vectors
    * Fixed inline properties being broken on certain template internal pass ordering

v1.7.9 rev 01:
* Improvements:
    * Start screen option is now a dropdown to allow for more control of how it behaves
    * Added more options to the preference window
        * Option to prevent automatic import of SRP templates
        * Option to force the use of Unity's macros for sampling
        * Option to allow or prevent the use of ASE define symbol

* Fixes:
    * Included missing files for 'Diffusion Profile' node from last release which prevented the use of the new default inspector option
    * Fixed issue with 'Gradient Sample' node that was not generating the component channel correctly

v1.7.9 rev 00:
* New Start Screen:
    * New window that will popup when on a new session starts and/or if a new version is released (can be dismissed)
    * Provides easy start up links for the wiki and contact channels
    * Streamlines the process of importing ASE samples for each rendering pipeline
    * Automatically tracks the release of new versions

* New Preferences Entry:
    * Currently only controls the start screen settings but in the future it will contain many more options that are currently hidden

* New Shader Function:
    * Random Range
        * Returns random value between a minimum and maximum range values

* Improvements:
    * Removed alpha to coverage alpha test check for consistency between pipeline and setups
    * Added check to custom shader inspector and updated missing features like keywords popups and other small changes that came with unity 2019.3
    * Added new option to 'Diffusion Profile' node to be able to create the unity default profile inspector for users that need to share their shaders
    * Changed the color of Int ports to white for proper differentiation between Float and Int

* Fixes:
    * Depth texture is now uses proper macros even when using global texture samplers to prevent duplication errors
    * Added infinite loop detection to 'Get Local Var' nodes to prevent the case where you can change the 'Get Local Var' node reference to itself
    * Fixed Issue with diffusion profile drawer not being able to change profiles if ASE window wasn't open
    * Fixed issue with outline in surface shaders not respecting the selected precision type

v1.7.8 rev 02:
* New Property Attributes:
    * Remap Sliders attribute for Vector2 node that creates a min-max slider in the 0-1 range
    * Single Line Texture attribute for texture properties nodes that minimizes texture object field into single line fields

* Fixes:
    * Fixed fog color and ambient color node values in HDRP
    * Fixed issue with Fabric and Hair HDRP templates not correctly writing to depth offset in all shader passes
    * Fixed custom expression precision types that didn't respect the selected choice in all cases and added a new Inherit option
    * Fixed possible issue with saving files when path is null

* Improvements:
    * Improved loading of shaders by preventing unnecessary loads of other types of assets
    * Added warning with fix button to diffusion profile property drawer for when profile isn't present in the profile list
    * Shader functions names now have automatic spacing when searched in the palette
    * Added a direct dependency check for shader function files that allows to search which shaders and shader function are currently directly using it
    * Templates context entry generation is now done by name to prevent random sorting

v1.7.8 rev 01:
* New HDRP Templates:
    * Hair
    * Fabric

* New Shader Function:
    * Inverse Lerp
        * Inverse operation of the 'Lerp' node

* Improvements:
    * Update HD templates to v.7.1.8 and above 

* Fixes:
    * Fixed issue with 'Component Mask' node preview not correctly showing alpha channel in all situations

v1.7.8 rev 00:
* Fixes:
    * Fixed issues with 'Function Switch' node incorrectly displaying reference nodes and not properly ordering when in Reference mode

v1.7.7 rev 06:
* Fixes:
    * Fixed 'Parallax Occlusion Mapping' node texture array sampling in LW/URP
    * Fixed issue with 'Light Attenuation' node not taking into account vertex offset in URP templates

* Improvements:
    * Added extra pre pass option for Lightweight and Universal RP that allows for custom outline passes
    * World position dependent nodes will now take vertex offset into account over LW templates

v1.7.7 rev 05:
* Fixes:
    * Fixed issues when reading shader over updated template
    * Fixed 'Light Attenuation' issue on URP 7.2.X which was causing rendering errors

* Improvements:
    * World position dependent nodes will now take vertex offset into account over default Unlit and URP templates
    * Improved Diffusion Profile support in HDRP 7.2.X

v1.7.7 rev 04:
* Fixes:
    * Fixed compatibility issues with HDRP v7.2.0 and above
    * Fixed issue with 'Indirect Diffuse Light' node on Normal port not properly working over Lightweight RP

v1.7.7 rev 03:
* Fixes:
    * Fixed issue on Unity ignoring Emission channel values set over Debug Port
    * Fixed issue with multiple uses of 'Voronoi' node over a graph
    * Fixed helper window colors in personal skin
    * Fixed emission contribution on baked lighting
    * Fixed GUID conflicts between Lightweight and Universal samples

v1.7.7 rev 02:
* Fixes:
    * Fixed incorrect values given by time related nodes
    * Fixed shadow compilation issue on HD Lit template
    * Added null checks on updating shader over master node to prevent saving issues

* Improvements:
    * Decode Directional Lightmap shader function now compatible with all pipelines
    * Added events that can be registered externally which indicate shader type change and shader save (API)
        * IOUtils.OnShaderSavedEvent called when shader is saved
        * IOUtils.OnShaderTypeChangedEvent called when shader 
        * Registered functions must receive three arguments
            * Shader shader: Current shader being modified 
            * bool isTemplate: Current shader type is template based
            * string type: GUID of template in use or string.empty if Standard Surface

v1.7.7 rev 01:
* Improvements:
    * Some adjustments were made to the Decode Lightmap's Instructions port
        * Port is set be only visible when on SRP
        * ASE automatically sets Unity's default instruction values if no value is connected to it

v1.7.7 rev 00:
* Fixes:
    * Fixed multiple connections on Wire node when Alt + dragging nodes from it
    * Fixed issue with normal map usage over URP PBR

* Improvements:
    * Added new previews:
        * Heightmap Texture Blend
        * Parallax Offset
        * Clip Planes
        * Fog and Ambient Colors
        * Fog Params
        * Instance ID
        * Object To Clip
        * Object To View
        * Shade Vertex Lights

v1.7.6 rev 02:
* Fixes:
    * Assured latest fix for time related nodes over SRP is only applied on latest versions
    * Fixed issue on declaring variables inside CBuffer that are already on native template

v1.7.6 rev 01:
* Fixes:
    * Fixed usage of legacy variables over SRP on time related nodes
        * Time
        * Time Parameters
        * Sin Time
        * Cos Time
    * Fixed issue on HD Unlit Vertex Offset input code not being set on all passes

v1.7.6 rev 00:
* New Shader Function:
    * SRP Additional Light
        * Returns SRP's additional lights information calculated with a selected lighting mode

* Improvements:
    * 'Light Attenuation' node on Lightweight/Universal now outputs only the attenuation of the main light
        * Before it outputted the sum of all light attenuations which didn't have a practical use

v1.7.5 rev 03:
* New Templates:
    * Universal Experimental 2D Lit
    * Universal Experimental 2D Unlit

* Fixes:
    * Fixed issue on incorrectly setting a wrong instanced block name on non-main passes when using GPU Instancing over templates
    * Added Safe Power option into both 'Power' and 'Fresnel' nodes
        * Ensures base for power operation is always greater than zero to prevent NaN over the result
    * Fixed 'Indirect Diffuse Light' and 'Indirect Specular Light' nodes preview when Normal Space option is set to World
    * Fixed 'Fresnel' node preview as it wasn't showing the correct result when scale was different than 1
    * Fixed Alpha Clip issue across multiple Unlit Lightweight and Universal template versions

* Improvements:
    * Improved mouse focus behavior when ASE is not focused
    * Migrated Lightweight samples into Universal RP

v1.7.5 rev 02:
* Fixes:
    * Fixed issue with incorrect _ST variable declaration on templates over 'Texture Array' node
    * Fixed issue on 'Grab Screen Color' node creating a default Grab Pass when using the default grab name with Custom Grab Pass activated
    * Fixed issue on 'Texture Transform' node giving incorrect values on certain connections
    * Fixed node preview update for missing shader functions
    * Fixed issue on 'Static Switch' node using in-existent keyword indices
    * Fixed 'Static Switch' reordering issues when in Reference mode

* Improvements:
    * Added missing previews to nodes
        * Ortho Param
        * Projection Param
        * Screen Param
        * ZBuffer Param
        * Clip
        * Compute Screen Pos
        * Texel Size
        * Texture Transform
    * Tweaked 'Texture Object' and 'Texture Sampler' node previews to avoid recent for AMD crashes

v1.7.5 rev 01:
* Fixes:
    * Fixed 'Color Space Double' node issue in SRP
    * Fixed 'Texture Array' node ordering issue in the material inspector when on Reference mode
    * Fixed issue with references lost over 'Texture Sample' on shader functions
    * Small fixes and optimizations to the URP unlit template
    * Fixed texture fields not being modifiable on 'Texture Sample' nodes when ASE window was not focused

* Improvements:
    * Updated 'Flow' shader function to make Strength a Vector2 input
    * Improved registered local variables usage on templates
    * Removed hard-code directives declaration on LWRP and added them as Port options on templates

v1.7.5 rev 00:
* New Nodes:
    * Inverse Projection Matrix
    * Inverse View Projection Matrix

* New Shader Functions:
    * Noise Sine Wave
        * Creates a sine wave from a given input with an added pseudo-random value
    * Sawtooth Wave
        * Creates a saw-tooth wave from a given input
    * Square Wave
        * Creates a square wave from a given input
    * Triangle Wave
        * Creates a triangle wave from a given input
    * Checkerboard
        * Created a checkerboard pattern with given colors where Frequency controls its tiling
    * Ellipse
        * Creates an ellipse based on given uv and dimensions
    * Polygon
        * Creates a polygon shape with a specified amount of sides
    * Rectangle
        * Creates a rectangle shape with a specified size
    * Rounded Rectangle
        * Creates a round rectangle shape from a given size and radius

* Improvements:
    * Added new Smooth toggle option to 'Voronoi' node
        * Only available under the Cells Method
    * Improved selection box inside the editor graph to match Unity's and not be version dependent
    * Ported terrain sample to Universal Rendering Pipeline
        * Packed inside Examples > Universal SRP Samples.unitypackage

* Fixes:
    * Fixed multiple issues regarding using instanced properties over 'Outline' node
    * Fixed hard-crash when modifying pass amount over template (API)
    * Fixed Native Directives display over Templates in Unity 2019.3
    * Fixed GPU instancing on Particles Alpha Blended template
    * Removed Clip from From option over 'Transform Position' and 'Transform Direction' nodes
        * Transform matrix was incorrect and there's no viable replacement over those nodes context

v1.7.4 rev 02:
* New Shader Functions:
    * Flow
        * Creates a flow effect which can be given from a flow map.
    * Twirl
        * Sets a twirl effect to a given input UV. Created by The C.reator @cayou66
    * Normal From Height
        * Extrapolate a normal vector from an height value

* New Sample:
    * Scale Independent Tile

* Improvements:
    * Added new Rotation Independent Scale option into 'Object Scale' node
    * Tweaked 'Remainder' node to always use the % operator on Unity 2018.1 and above

* Fixes:
    * Fixed issue on loading multipass multi visible master nodes losing their names
    * Fixed issue with point lights not working with instanced terrain shaders
    * Fixed issue on terrain getting black when drawn instanced
    * Fixed alpha test issues over deprecated HDRP Lit
    * Fixed issue where not setting pass name on port options lead to incorrect option behavior (API)
    * Fixed previews not updating correctly in shader functions

v1.7.4 rev 01:
* New Template:
    * Unlit for HD SRPv6.x.x

* New Node:
    * Luminance

* Fixes:
    * Fixed alpha-clip issues with legacy HD templates
    * Fixed issue on incorrectly initializing custom type variables on 'Custom Expression' node
    * Fixed nodes preview getting lost on loading scenes
    * Fixed preview issue on 'Noise Generator' node
    * Fixed preview issue on 'Panner' node
    * Fixed preview issue on 'Rotator' node
    * Fixed issue on ignored Passes over SubShader options appearing on both shader reload hot code reload
    * Fixed issue with 'Parallax Occlusion Mapping' node when used on shader functions 
    * Fixed issue with setting directly the property name over its textfield wouldn't trigger the duplicates prevention system

* Improvements:
    * Updated 6.9.2 Lightweight templates to includes the same options as URP templates

v1.7.4 rev 00:
* Fixes:
    * Fixed critical error where users could not create a surface shader if no ASE window was opened

v1.7.3 rev 00:
* Fixes:
    * Fixed preview issues on multiple nodes
    * Fixed incorrect template reload when multiple shaders using the same template are opened
    * Fixed Ignore Rotation issues on 'Billboard' node over Lightweight RP
    * Fixed issue on not importing latest SRP templates when an unregistered version is detected
    * HDRP templates no longer throw console errors on Unity 2019.3
    * Fixed Alpha Clip issue on deprecated HDRPLit template
    * Fixed issue on incorrectly resetting port internal data on load with matrices types

* Improvement:
    * Minor renaming on 'Baked GI' node name to 'SRP Baked GI'
    * Updated URP templates to include a Baked GI port similarly to HDRP

v1.7.2 rev 03:
* Fixes:
    * Fixed issue on templates not capturing properties with certain attributes
    * Fixed issue on templates inline properties not being properly set on modules if main master node wasn't the first pass

* Improvements:
    * Added Builtin Fog toggle option into Lightweight PBR template

v1.7.2 rev 02:
* Fixes:
    * Fixed issue on 'Toggle Switch' node
    * Fixed abnormal behavior when reading template with more interpolators than the ones allowed by its Shader Model (API)

* Improvements:
    * Template can now read Shader Model from area outside SubShader (API)

v1.7.2 rev 01:
* Fixes:
    * Fixed issues on generating incorrect precision inputs on both 'Screen Position' and 'World Position' node
    * Fixed issue on multi-pass master nodes not setting correct given shader name when created from file
    * Fixed issue on WaterSample shader
    * Fixed issue on 'Custom Expression' node not generating correct function code without a return statement

* Improvements:
    * Users can, like on Shader Functions, immediately name shader when creating it
    * Adding Instanced option into 'Texture Transform' node
        * Determines if the _ST texture transform variable is created as an instanced property

v1.7.2 rev 00:
* Improvements:
    * 'Toggle Switch' node now uses a ternary operator to prevent incorrect results on NaN inputs

v1.7.1 rev 13:
* Fixes:
    * Fixed issue duplicating certain nodes

v1.7.1 rev 12:
* Fixes:
    * Fixed issue when using more than one Octave on 'Voronoi' node
    * Fixed issue on Motion Vectors pass over HD templates
    * Fixed issue on generating incorrect keyword value on 'Static Switch' node using Reference Mode

v1.7.1 rev 11:
* Fixes:
    * Fixed issue on registering/unregistering internal template generated nodes over the reorder node event
    * Fixed multiple issues on 'Custom Expression' node
    * Fixed issue on using 'Voronoi' node over shader functions

v1.7.1 rev 09/10:
* New Node:
    * HD Emission

* Fixes:
    * Fixed issue when reading shader with missing shader function
    * Fixed issue when duplicating property nodes on Fetch mode would reset property name
    * Fixed issue on 'Parallax Occlusion Mapping' node incorrectly generating instanced _ST vars outside the CBuffer
    * Fixed alpha cutoff issues on HD Lit templates v5.7.2 and v5.16.1
    * Fixed issue on not declaring both BakedGI and BakedBackGI on Lit template
    * Fixed issue on not setting some ports with graph data over templates

* Improvements:
    * Listing on nodes is now alphabetically ordered
        * Get Local Var
        * Grab Screen Color
        * Static Switch
        * Texture Array
        * Texture Sample
    * Node search bar text now takes property name and inspector name into account

v1.7.1 rev 08:
* Fixes:
    * Fixed issue on 'Register Local Var' not allowing to reset name to loaded one
    * Fixed Console Log issues with Personal Skin
    * Fixed issue on overwriting Render Type custom values over a template(API)
    * Fixed issue on local variables generation over the 'Unpack Scale Normal' node 
    * Fixed issue with setting special tags over template options (API)

* Improvements:
    * Added more flexibility to 'Static Switch' node
        * Can have empty property names when Material Toggle is toggled off
        * It no longer modifies keywords ( setting to upper, adding _ON, etc ) if Material Toggle is toggled off
        * Tweaked visible options when on Fetch mode
    * Added hot-fix to 2019.2 Lit template in order to generate shaders without errors when used on HDRP v7.x.x

v1.7.1 rev 07:
* New Shader Functions:
    * Derive Tangent Basis
    * Height-based Blending

* Improvements:
    * Clicking on node specific messages over ASE log now jumps to which node generated it
    * Changing ports types to matrix now sets the identity matrix into its internal data
        * Relevant on nodes like 'Custom Expression'

* Fixes:
    * Fixed issue on incorrectly adding 0 to W channel when creating position vectors
    * Fixed issue on not auto-importing templates correctly over Unity 2019
    * Fixed multiple issues regarding shader function previews
    * Fixed compilation issue when using unreferenced 'Get Local Var' nodes
    * Fixed issue on changing 'Static Switch' name not being reflected on referenced list
    * Fixed issues with 'Texture Coordinates' and 'Texture Sampler' nodes with 'Relay' or 'Wire' nodes on Tex port

v1.7.1 rev 06:
* Fixes:
    * Fixed new and old shader templates for HDRP 6.9.X so the diffusion profile value is passed around correctly
    * Fixed issue on 'Texture Coordinates' node not correctly swizzling data on template vertex functions
    * Prevented Additional Directives reference lost when saving certain shader functions

v1.7.1 rev 05:
* Fixes:
    * Fixed issue on getting incorrect inline properties on templates

v1.7.1 rev 04:
* Fixes:
    * Fixed 'Diffusion Profile' node issues on SRP v6.9.X
    * Fixed multiple issues regarding shader functions Additional Directives usage

v1.7.1 rev 02/03:
* Fixes:
    * Fixed issue both on creating duplicate texture ST constants and being declared out of the cbuffer
    * Fixed possible issues on 'Texture Transform' node over SRP
    * Fixed baked lighmap issues over Lightweight/Universal PBR templates
    * Fixed SimpleTerrain sample water shader which was broken due to recent 'Screen Depth' node changes
    * Fixed compilation errors on 'Voronoi' node
    * Fixed issue on registering duplicate instanced variables which are already on template as non-instanced

v1.7.1 rev 01:
* Fixes:
    * Removed unused variable warning
    * Broadened up Shader Function versions to apply serialized fix on Origin field

v1.7.1 rev 00:
* Fixes:
    * Fixed issue with Face related nodes not working in HDRP v6.9.0 due to bad version handling
    * Fixed retro-compatibility issue with incorrectly reading Shader Function Custom Directives as Native

* Improvements:
    * Tweaked 'Get Local Var' node message thrown when no Reference is selected
    * Natives UI listing is now locked on Unity 2019.3 to prevent incorrect behaviors on new UI system

v1.7.0 rev 09:
* Improvements:
    * Changing Variable Mode from Fetch to Create no longer rewrites property name on Property Nodes
    * Improved property renaming when duplicating Property nodes
    * Added property name to items on Material Properties list 
        * Can be clicked to focus on specified node

v1.7.0 rev 08:
* Fixes:
    * Fixed issue on HD Lit Diffusion Profile port being incorrectly set as type Int
        * Multi Pass Master Nodes no longer reads/writes input port meta data as this must always be given by template

v1.7.0 rev 07:
* Fixes:
    * Fixed issue with the 'Diffusion Profile' node not updating from and to the material inspector

* Improvements:
    * Added new internal console inside shader canvas to more easily check ASE related messages

v1.7.0 rev 06:
* New Shader Function:
    * Non Stereo Screen Pos
        * Converts a VR screen position into a non-VR one

* Fixes:
    * Fixed issue with both 'Gradient' and 'Gradient Sample' nodes on Lightweight/Universal 
    * Fixed 'Voronoi' node issue over templates
    * 'Reconstruct World Position From Depth' now correctly works on VR

* Improvements
    * Tweaked Lightweight and Universal templates
        * Pragma tag now after native includes
        * Removed unused _MainTex_ST variable from Meta pass
    * Shader Properties can now be fully rewritten through template options(API)
    * Added Vulkan to Standard Surface Rendering Platforms

v1.7.0 rev 05:
* New Templates:
    * Added templates for Universal Rendering Pipeline
        * Packed inside AmplifyShaderEditor\Plugins\EditorResources\Templates\USRPTemplates.unitypackage

* Fixes:
    * Fixed multiple issues with Lit HDRP template
    * Fixed issue on not taking the 'Int' node as SRP compatible
    * Fixed both 'Transform Direction' and 'Transform Position' nodes issues regarding Tangent space
    * Fixed multiple issues regarding template options
    * Fixed issue on incorrect reading over Depth Offset values from shader
    * Minor fix on templates post-processor to prevent null pointer exceptions

* Improvements: 
    * Unity global variables used as inline properties on templates are now registered so they can be correctly maintained over the final shaders
    * Added LOD Cross Fade into Lightweight RP templates
    * Added Inherit type into precision popup and made sure most nodes can now compile with the proper precision type
        * Some nodes have a specific precision on purpose
    * Improved 'Diffusion Profile' node behavior
        * Added material property attribute 
        * Updated source code able to create profile properties in HDRP 7.X

v1.7.0 rev 04:
* Improvements:
    * When switching between shader type the connections with the same port name are kept intact
    * Custom inspector for SRP 7.X is now detected automatically and upgraded from previous versions
    * Templates master nodes now display the shader name in their title for better consistency
* Fixes:
    * Fixed issue in SRP 7.X where certain auto defines were overriding the selected options
    * Fixed issue in templates system where in certain situations defines would be set forever
    * Fixed stencil options in HDRP 7.X
    * Thickness port in HDRP 7.X is now always visible
    * Some properties in HDRP 7.X are now override-able

v1.7.0 rev 03:
* Improvements:
    * Added support for pragma options to the template system
* Fixes:
    * Fixed issue where dependent template options would be visible when it's dependency was off in some situations
    * Fixed issue with wrong connection types on creation of new nodes when dragging from ports
    * Fixed issue with detecting LOD parameter in templates

v1.7.0 rev 02:
* Fixes:
    * Fixed issue when selecting a shader that contains errors in 2019.3 would generate inspector errors

v1.7.0 rev 01:
* Fixes:
    * Fixed issue on incorrect initial node setup from previously saved options

v1.7.0 rev 00:
* New Node:
    * Voronoi

* Fixes:
    * Fixed issue on all templates not being available to choose over the Creates > Amplify Shader menu
    * Fixed issue on Unlit absolute mode
    * Fixed issue on both Lightweight templates absolute mode
    * Fixed issue on 'World Space View Dir' node preview
    * Fixed blinking window when 'Add'or 'Mul' nodes are dynamically changing their input ports
    * Fixed issue with detection loop function that was causing massive slow downs on node connection on big graphs
    * Fixed multiple issues with new Lit template
    * 'Face' and 'Switch By Face' now correctly work on the latest HDRP version
    * Fixed multiple issue on latest HD Lit template
    * Fixed issue when selecting shaders on Unity 2019.3
    * Fixed incorrect local variable generation on multiple nodes over Standard Surface's Vertex ports

* Improvements:
    * Improved editor performance by only refreshing when needed and only updating previews that are necessary to update
    * Texture array auto sorting on texture drop over Texture Array tool
    * Directives can now be written both before or after (via mouse drag ) to native directives detected on template
    * Added dropdown for custom editor in templates that contain the HD material inspector
    * 'Depth Fade' and 'Screen Depth' can now be used on vertex shader
    * Improved and optimized node search on context menu
        * Node developers can now associate tags to their nodes
    * Small improvement to shader function properties order when multiple shader functions are inside each other
    * Improved 'Noise Generator' node
        * Added new Gradient type
        * Added new option to auto set noise generator values to a 0-1 range
        * Added new scale input port to scale its Size input
    * Extended Port options versatility when declared on SubShader (API)
    * Shader Properties default values can now be set through the SetShaderProperty action (API)
    * Refactored templates paths

* Deprecated:
    * Disabled ASE custom texture array inspector on Unity 2019 and above
    * Marked old templates as deprecated
        * These will be eliminated from the package on a future build

v1.6.9 rev 02:
* New Template:
    * Re-designed a new HD Lit template for SRP v6 and above
        * Compatible with Unity's default HD material inspector

* Fixes:
    * Fixed division by zero issues on 'Blend Operations' node
    * Fixed VR stereo issue in Standard Surface custom shadow caster
    * Fixed issue with reorderable properties in shader function being stuck when two or more of the same SF was used inside another one
    * Fixed incorrect variable naming on certain operations over both 'Transform Position' and 'Transform Direction' nodes
    * Fixed issue on Input Port internal wrapper creating float3x3 with float4x4 contents
    * Minor tab fixing on Unlit template
    * Fixed issue on HD templates Auto-Importer
    * Fixed issue on not always capturing the template custom inspector correctly
    * Fixed compatibility issue with 'Diffusion Profile' node on Unity 2019.3

* Improvements:
    * Added tag system to node search and improved how nodes are searched
        * Now words don't have to be in the same order of the name to match
    * Reactivated SRP Batcher compatibility
        * Each UnityPerMaterial Cbuffer set in pass now contains all declared property variables

v1.6.9 rev 01:
* Improvements:
    * Added support for HDRP v6xx over Unity 2019.2
        * HDRP v5xx now set as legacy and moved into the SRP (Legacy) folder
        * LWRP templates remain the same between v5 and v6
    * 'Swizzle' node is now non-destructive when connecting data with less info than selected options

v1.6.9 rev 00:
* Fixes:
    * Fixed 'Component Mask' node preview 

v1.6.8 rev 07:
* New Nodes:
    * Gradient
    * Gradient Sample

* Fixes:
    * Fixed nodes incorrectly moving when interacting with popup fields in recent Unity versions
    * Fixed issue on incorrect size for vertex normal data on LWRP Unlit template

* Improvements:
    * Added Built-in Fog option to LWRP Unlit template

v1.6.8 rev 06:
* Fixes:
    * Fixed issue on incorrectly selecting Shader Name textfield when opening an ASE shader
    * Fixed issues on incorrect uv coordinates size over 'Grab Screen Color' node
    * Fixed GPU instancing and vertex offset issues on all HDRP templates
    * Fixed parse issues when capturing structs inside templates (API)
    * Temporarily disabled SRP Batcher as it was not properly working

v1.6.8 rev 05:
* Fixes:
    * 'Grab Screen Color' now prevents Unity macro inconsistent behavior between Unity versions

v1.6.8 rev 04:
* Fixes:
    * Fixed issues regarding Diffusion Profile usage on HDRP

v1.6.8 rev 03:
* Fixes:
    * 'Grab Screen Color' now prevents Unity macro inconsistent declaration ending between VR and non-VR compilation
    * Preventing compilation errors related to GPU Instancing on SRP

v1.6.8 rev 02:
* Fixes:
    * Added missing stereo config on LW Unlit template
    * Fixed Blend Mode config on HD Lit template when choosing Opaque mode

v1.6.8 rev 01:
* Fixes:
    * Fixed compilation issue with 'Grab Screen Color' node on VR
    * Fixed issue on Shadow Caster pass over HD Lit template v5.16.1
    * 'Grab Screen Color' node now takes VR into account on LWRP

* Improvements:
    * Share links now have permanent lifetime
    * Added new Depth Offset option into HD Lit template v5.16.1

v1.6.8 rev 00:
* New features:
    * Added Share button that uploads select data to the cloud for easy sharing
    * Added Screenshot button that takes a screenshot of the whole graph and saves it to disk
        * For now this is a Windows only feature

* Fixes:
    * Fixed issue on 'Toggle Switch' node registering a toggle drawer twice causing inspector warnings
    * Fixed issue on not always capturing the Fallback library on templates
    * Fixed both 'HSV to RBG' and 'RGB to HSV' node previews
    * Fixed issue on sometimes losing directives info on Shader Functions after being moved into a different path
    * Fixed issue on Shader Function being incorrectly loaded into a separate tab after being moved into a different path
    * Fixed issue on certain Pass Options only being applied to main pass Master Node

* Improvements:
    * ASE now auto-imports SRP templates if they are installed via Package Manager
        * It will also import correct templates in case of user switches between versions
            * User still need to open the shaders and hit the Update button so they are compiled on the correct version
    * Improved canvas buttons to better represent their purpose
    * Improved PPS Template under Single Pass VR
    * Mosaic Effect PPS sample now fully works across VR
    * Set Port Name action can now be registered on Port options block(API)

v1.6.7 rev 06:
* Fixes:
    * Fixed multiple issues on 'Custom Expression' node
        * Fixed layout issue when writing input names
        * Fixed issue on updating InOut/Out ports when reordering inputs
        * Fixed custom loading issue on void Output or Call mode when no inputs are declared
        * Fixed issue where user could set main output type as OBJECT type
        * Fixed incorrect text replacement on Call mode code
        * Fixed Custom Types not being correctly declared
    * Fixed compilation issues both on HD Lit and PBR when WRITE_MSAA_DEPTH directive is used
    * Possible fix on VR issue with Lightweight RP
    * Possible fix on issue with Unlit template when using Single Pass Stereo VR
    * Possible fix on issue with 'Grab Screen Color' node when using Single Pass Stereo VR

* Improvements:
    * Added Variable Mode to 'Grab Screen Color' node
    * Added new Alpha port on 'Blend Operations' node to enable a more layered like behavior
    * Added support to SRP 5.16.1

v1.6.7 rev 05:
* Fixes:
    * Fixed issue with alpha cutoff on both HD Lit 5.7.2 and 5.13 templates

* Improvements:
    * Added new template option functionality to copy properties from sub shader module (API)

v1.6.7 rev 04:
* Fixes:
    * Fixed issue on not taking variables declared on template into account when registering instanced variables

v1.6.7 rev 03:
* Fixes:
    * Fixed issue on 'Static Switch' node when setting default value on Enum type
    * Fixed issue on 'Triplanar Sampler' node incorrectly activating mid and bottom index ports
    * Fixed normal map unpacking issues with Lightweight RP over mobile

* Improvements:
    * Adding INTERNALTESSPOS into template semantics to prevent user issues

v1.6.7 rev 02:
* Fixes:
    * Fixed exposure issue on both diffuse and specular components over HDRP templates

* New Templates:
    * Adding specific templates for legacy HDRP v5.7.2

v1.6.7 rev 01:
* Fixes:
    * Changed how depth fetch is done to prevent issues on single pass vr 
    * Tweaked 'World Position' node preview shader to prevent issues over PS4
    * Fixed issue on 'World to Object' node when used on templates
    * Fixed 'Custom Expression' node issue with Auto-Register on shader functions

* Improvements:
    * Post-Processing Tool now works under a InvariantCulture to be region independent
    * 'Static Switch' node now supports both shader_feature_local and multi_compile_local
        * Only available on Unity 2019.1 and above 
        * Activate the Is Local toggle over the node properties

v1.6.7 rev 00:
* New Sample:
    * Volumetric Pixelize by Raphael Ernaelsten

* New Nodes:
    * Diffusion Profile
        * Packed inside HDSRPTemplates.unitypackage
        * To be used on Unity 2019 with HDRP 5.13
    * Baked GI

* Improvements:
    * 'Grab Screen Color' node now works on both HDRP 4.x.x and 5.x.x
    * Added new Is Variable toggle on 'Custom Expression' node

* Fixes:
    * Fixed possible null pointer access when losing focus on a ASE window
    * 'Texture Sample' and 'Texture Coordinates' nodes no longer breaks GPU Instancing/SRP Batcher

v1.6.6 rev 00:
* Improvements:
    * Added support for both Lightweight and HD SRP v5.13.0 on Unity 2019
        * Templates for SRP v4.x.x on Unity 2018.3 are still accessible through packages
            * HDSRPTemplates 4xx (Legacy)
            * LWSRPTemplates 4xx (Legacy)
    * Updated 'Triplanar Sample' node to include a new position port
        * Also updated TriplanarProjection sample to include this node usage

* Fixes:
    * Fixed issue with using vertex tangent data over SRP templates

v1.6.5 rev 00:
* Fixes:
    * Fixed issue on interpolator usage on template based shaders when current shader model max amount is reached

v1.6.4 rev 03:
* Improvements:
    * Improved Post-Processing Tool behavior
        * Removed dependencies on Unity's Post-Processing Stack libs to prevent compilation issues
        * Added new 'Allow In Scene View' option

v1.6.4 rev 02:
* Fixes:
    * Added missing ase_pragma tag over PPS template

v1.6.4 rev 01:
* Fixes:
    * Fixed issue with using 'Indirect Diffuse Light' node with Lightweight PBR template

v1.6.4 rev 00:
* Fixes:
    * 'Indirect Diffuse Light' node now works on the HD SRP templates
    * Fixed memory leak on closing ASE windows
    * Fixed port define issue with linked ports over template options (API)
    * Fixed loading issue on 'Reconstruct World Position From Depth' shader function

v1.6.3 rev 09:
* Fixes:
    * Added missing ase_frag_input tag on Lightweight Unlit SRP template
    * Fixed compiler issues when using 'Face' and 'Switch by Face' nodes on Vertex Ports

* Improvements:
    * Minor tweak on Simple GPU Instancing sample for GPU Instancing behavior to be more evident

v1.6.3 rev 08:
* Improvements:
    * Standard Surface custom ASE shadow caster can now be turned off
        * Toggle on new 'Use Default Shadowcaster' option over Rendering Options group

* Fixes:
    * Nodes 'Face' and 'Switch by Face' now properly work over the HD Unlit template
        * Fixed issue on missing ase_frag_input tags over HD Unlit
    * Template Port Options can now be properly registered over SubShader (API)

v1.6.3 rev 07:
* Fixes:
    * Removed unnecessary code being used when on Custom Lighting 
    * Fixed 'Light Color' node not being correctly reset when on subtractive lightmaps
        * Removed gi.light.color usage from Blinn Phong Light shader function as it was a temporary fix for the above

v1.6.3 rev 06:
* Fixes:
    * Fixed issues on 'Light Attenuation' node over Lightweight SRP
        * Removed incorrect N.L contribution 
        * Fixed issue on duplicate variables when using multiple nodes

v1.6.3 rev 05:
* New Sample:
    * SRP HD Omni Decal

* Fixes:
    * 'Indirect Diffuse Light', 'Indirect Specular Light' and 'Light Attenuation' now works on Lightweight SRP
    * Fixed issue on 'Screen Position' node previewer when on Screen Mode
    * Fixed 'Screen Depth' node issue over Particle Alpha Blended template

* Improvements:
    * 'Static Switch' nodes now have a Reference mode
        * Can use other nodes as reference, similar to 'Texture Sample' node
    * Minor cleanup on 'Reconstruct World Position From Depth' shader function
    * Can set shader functions as hidden so it wont show on context menu
        * Activate Hidden toggle over shader function General options

v1.6.3 rev 04:
* Fixes:
    * Minor fix on Screen Space Detail sample
    * Fixed issue on instanced surface terrains not being able to be picked/selected
    * Fixed issue with lightmaps and instanced standard surface terrains
    * Fixed issue with 'Four Splats First Pass Terrain' shader function on default templates
    * Fixed issue with duplicate local variables on 'Texture Coordinates'
    * Fixed issue on duplicate ST variable when using Particle Texture Parameter over Particles template

* Improvements:
    * 'Switch By Pipeline' node now takes default template and surface into account
    * UsePasses can be directly registered by nodes on data collector(API)
    * ASE Custom Shader Inspector now also shows SRP Batcher compatibility status

v1.6.3 rev 03:
* Fixes:
    * Fixed issues with Custom Lighting on Subtractive Lighting Mode
    * Fixed issue on Diffusion Profile Id not being correctly written when Transmission is activated on HD Lit template

v1.6.3 rev 02:
* Fixes:
    * Fixed issue with Mosaic Effect PPS sample not opening correctly on Unity 2018.1 and 2018.2
    * Fixed issue on Multi Pass Distortion sample not taking skybox into account

* Improvements:
    * User can now specify sub-shader tags 
        * Moved RenderType tag to sub-shader level

v1.6.3 rev 01:
* Fixes:
    * Fixed issue on screen position calculation across all templates
    * Fixed issue with 'Parallax Occlusion Mapping' node

* Improvements:
    * Minor tweak on Post-Processing Stack template

v1.6.3 rev 00:
* Fixes:
    * Fixed multiple issues with 'Triplanar Sample' node over SRP

v1.6.2 rev 11:
* Fixes:
    * Fixed issue on ST registry at 'Parallax Occlusion Mapping' node not taking SRP name convention into account
    * 'Reconstruct World Position From Depth' shader function now compatible with HDRP
    * Fixed crash caused by the 'Outline' node when attempting to be used on nested shader functions
    * Fixed issues with 'Screen Position', 'Depth Fade' and 'Screen Depth' nodes on PPS template

v1.6.2 rev 10:
* New Template:
    * PostProcessStack
        * Post-Processing shader based on Unity provided example

* New Sample:
    * Mosaic Effect PPS

* New Tool:
    * Post-Processing Stack Tool
        * Generates a cs script with a PPS Renderer and Settings from a given shader

* New Shader Function:
    * Decoding Directional Lightmap

* Improvements:
    * Added inline property for Alpha to Coverage option on Standard Surface Shader Type

* Fixes:
    * Fixed 'Triplanar Sample' and 'Parallax Occlusion Mapping' nodes issues with texture arrays on SRP
    * Fixed crash caused by the 'Outline' node when attempting to be used on templates
        * This node is specific to Standard Surface Shader Type
    * Fixed issue with using Lightweight PBR legacy template

v1.6.2 rev 08/09:
* Fixes:
    * Fixed issue with depth texture fetching on Lightweight SRP

v1.6.2 rev 07:
* Fixes:
    * Fixed issue on template asset post processor

v1.6.2 rev 06:
* Improvements:
    * Updated Decals code and removed warnings from HD Lit template
    * Improved behavior when loading outdated meta from shaders using modified templates
    * Added No Scale and Offset attribute to 'Texture Array' node

* Fixes:
    * Fixed instancing issues over HD Lit template

v1.6.2 rev 05:
* New Shader Function:
    * Blinn Phong Light Wrap

* Fixes:
    * Fixed issue with null pointer exception when hacking selection on dynamic ports
        * Input ports which appear on p.e. 'Add' and 'Multiply' nodes when dragging wires
    * Fixed shadow issues on HD PBR samples
    * Fixed 'Billboard' node issues on HD SRP
    * Fixed D3D11 internal compiler error on Android + VR over multiple templates
    * Fixed issue with Depth Texture declaration on particles template over Android + VR
    * Fixed issue with local var declaration of custom types on 'Custom Expression' node
    * Fixed issue with 'Object To World','World To Object' and 'Transform Position' nodes

* Improvements:
    * Added the action of (de)activating ZWrite when Surface type is selected on HD Lit template
    * Added the action of selecting correct Blend RGB when Surface Type is selected on HD Lit template
    * Improved failsafe to when template creators changes order on passes, removes or add new ones
    * Depth Texture now being declared with Unity's own macro on Unity 5.6 and above to prevent issues
    * Updated HD templates and samples to v.4.9.0
        * Users can still use v4.8.0 although template shaders will present an error
        * Final compiled shader will have error removed
    * Added Refraction's Chromatic Aberration as an Inline Property on Standard Surface
        * Located on Blend Mode menu, bellow Refraction Layer option
        * Only shows when Refraction port is connected
    * 'DecodeLightmap' now working on latest SRP templates
    * 'Fetch Lightmap Value' shader function now takes SRP into account

v1.6.2 rev 04:
* Fixes:
    * Fixed World To Object and Object To World operations on Lightweight SRP

* Improvements:
    * Added support for Terrain instancing over Unity 2018.3
    * Correctly handling when user wants to disable main output node
    * Minor tweaks over SRP templates global tags to be able to properly inject code dependent on vertex data

v1.6.2 rev 03:
* Fixes:
    * Fixed issue on HD Lit Motion Vector's pass
    * Fixed issue with Default UI template when used on Sprite Renderers

* Improvements:
    * SRP templates no longer have CBuffer block pre-written
        * Instanced Properties now correctly declare CBlocks
    * Added new Receive Shadows option into Lightweight templates to correctly handle shadows

v1.6.2 rev 02:
* New Node:
    * Instance Id

* Fixes:
    * Fixed issue on not being able to have multiple property nodes fetching the same variable
    * Fixed version issue on both HD and LW samples

* Improvements:
    * Removed hard cap on interpolator usage on templates
        * Message is thrown to the user if maximum allowed by shader model is reached

v1.6.2 rev 01:
* New Template:
    * HD Lit

* New Sample:
    * SRP HD Lit Refraction

* Improvements:
    * Can add options on template which lets users change its behavior when using it on ASE
    * Added Vertex Position option into Unlit and SRP templates
        * Specified value on Vertex input port can now be either a relative value or an absolute one
    * Passes can now be excluded from final shader through Available Passes menu over SubShader Properties

* Fixes:
    * Fixed issues with Lightweight PBR Shadow Caster on WebGL and Android

v1.6.2 rev 00 / v1.6.1 rev 05:
* New Template:
    * Unlit Lightmap

* New Sample:
    * Unlit With Lightmap

* Fixes:
    * Fixed issue on 'Depth Fade' node with custom Vertex Positions
    * Fixed issue with using the X,Y,Z,W output ports on 'Object To World' node

v1.6.1 rev 04:
* New Node:
    * Linear Depth
        * Converts values given on logarithmic space to linear

* Improvements:
    * Minor improvement on nodes loading time when initializing ASE

* Fixes:
    * Fixed local variable duplicate issue when using 'Depth Fade' and 'Screen Position' nodes
    * Fixed serialization issue with Additional Use Passes and Fallback data when going into play mode
    * Fixed HDRP issues on Motion Vectors pass
    * Fixed issue with uniform keyword usage inside cbuffers (SRP templates) over PS4
    * Fixed issue with 'Common Transform Matrices' node over SRP templates
    * Fixed object to world/world to object space transform on transform nodes over HD templates
        * 'Transform Position', 'Object To World' and 'World To Object' nodes 
        * They now take SHADEROPTIONS_CAMERA_RELATIVE_RENDERING keyword into account
    * Fixed issue with 'Pixelate UV' node when used over vertex functions
    * Fixed foldout issue between Additional Surface Options and Custom SubShader Tags on Surface node properties

v1.6.1 rev 03:
* New Shader Functions:
    * Terrain Wind Value
    * Terrain Wind Animate Vertex
        * Updated Simple Terrain sample to use this shader function

* Improvements:
    * Updated HDRP PBR template to v4.6.0
    * Added Alpha To Coverage/Alpha To Mask option into templates
        * Set under the Blend Mode area

* Fixes:
    * Fixed issue on Emission color being added over Forward Add pass when using 'Standard Surface Light' node

v1.6.1 rev 02:
* Fixes:
    * Fixed issue with reordering material properties for Translucency and Refraction port's properties
    * Fixed issue with 'Texture Sample' node when referencing an unconnected node

v1.6.1 rev 01:
* Improvements:
    * Updated HDRP templates to v4.3.0
    * 'Vertex Tangent' node now has Size option
    * Added access to all 8 UV sets on Surface shaders
    * Re-added 'Clip' node
        * First Input Port acts as a relay
        * Clip uses difference between Alpha and Threshold ( Alpha - Threshold )

* Fixes:
    * Fixed issue with late directives being injected on globals tag when functions area is available
        * Affected usage of 'World Space Light' nodes on HDRP templates
    * Fixed division by 0 issue on 'Triplanar Sample' node
    * Fixed incorrect directional light vector usage on HDRP templates

v1.6.1 rev 00:
* Fixes:
    * Excluding Primitive ID source code from MacOS 

v1.6.0 rev 00:
* Improvements:
    * Updated SRP templates to v4.2.0

* Fixes:
    * Fixed issue on GBuffer pass over HD PBR template
    * Fixed alpha clip issues on Legacy HD PBR template

v1.5.9 rev 02:
* New Nodes:
    * Vertex Id
    * Primitive ID

* New Shader Functions:
    * Perturb Normal HQ
    * Prepare Perturb Normal HQ

* Improvements:
    * Matrix type nodes can now be GPU instanced

* Fixes:
    * Fixed issue with 'Fresnel' node with Tangent Normal vectors on Surface shaders
    * Fixed issues on Motion Vector pass on both 3.x.x. and 4.x.x templates
    * Fixed issue with world pos generation on HD 4.x.x

v1.5.9 rev 01:
* Improvements:
    * Added Auto Gamma to Linear option into 'Color' nodes when set to Constant
    * Added access to all 8 uv channels on 'Texture Coordinates' and 'Vertex TexCoord' nodes
        * Only for 2018.2 and above

* Fixes:
    * Fixed 'Gamma To Linear' and 'Linear To Gamma' compiler errors on SRP v.4.x.x

v1.5.9 rev 00:
* Fixes:
    * 'Grab Screen Color' node now also working on Lightweight SRP v3.x.x
    * Fixed possible issue with Shader Function creation callback not being inside AmplifyShaderEditor namespace 

v1.5.8 rev 03:
* Fixes:
    * Fixed multiple issues in both PBR and Unlit templates for HD SRP v4.x.x
    * Fixed issue over clip space transforms on HD SRP
    * Fixed issue on sampling depth buffer on HD SRP

v1.5.8 rev 02:
* Improvements:
    * Multiple improvements over 'Global Array' node
        * Added support for jagged arrays
        * Added Auto-Register toggle
        * Can reference other nodes through new Mode dropdown ( similar to 'Grab Screen Color' node )

* Fixes:
    * Fixed memory leak over 'Template Local Var' node
    * Fixed HD templates issues over new HD 4.1.0

v1.5.8 rev 01:
* Fixes:
    * Fixed issue on Live Mode writing on non ase shaders when loaded into canvas
    * Fixed issue on MacOS with scroll bar over Texture Array Creator tool
    * Fixed issue on MacOS with 'Function Input' node generated default Sampler 2D value

* New Shader Function:
    * Substance Blend Metallic

* Improvements:
    * 'Grab Screen Color' node is now supported on LW SRP
        * Uses the new _CameraOpaqueTexture which must be requested on the pipeline asset
            * Must toggle on the Opaque Texture option over its General foldout
    * Added Clip and Tangent spaces into 'Transform Direction' node
    * Added Tangent space into 'Transform Position' node

v1.5.8 rev 00:
* New Sample:
    * Substance 2018

* Fixes:
    * Fixed canvas crash when loading shaders with missing templates
    * Fixed issue on UV Coords creation over 'Substance Sample' Node
    * Fixed issue with loosing Substance reference on 'Substance Sample' node when applying new changes on it
        * Only happened on Unity 2018 with Substance in Unity plugin
    * Fixed issue on nested instanced properties over shader functions not being taken into account

* Improvements:
    * Upgraded Lightweight and HD templates to v5.0.0
        * Maintained templates for v.3.x.x under the packages HDSRPTemplates (Legacy) and LWSRPTemplates (Legacy)

v1.5.7 rev 02:
* Fixes:
    * Fixed issues on 'Static Switch' node
        * Property Name wasn't available in all options
        * Keys on Keyword Enum over Fetch Mode were being automatically upper cased
    * Fixed issue on 'Get Local Var' node  not propagating node data correctly after being loaded

v1.5.7 rev 01:
* New Templates:
    * Custom RT Init
        * Packed inside AmplifyShaderEditor > Plugins > EditorResources > Templates > CustomRTTemplates
    * Custom RT Update
        * Packed inside AmplifyShaderEditor > Plugins > EditorResources > Templates > CustomRTTemplates

* New Sample:
    * Custom Render Texture Rain
        * Packed inside AmplifyShaderEditor > Examples > Custom RT Samples
        * Needs both Custom RT Init and Custom RT Update templates to be imported into project

* Fixes:
    * Fixed issue on Queue Offset incorrect writing over templates
    * Fixed issue on templates not recognizing uint
    * Fixed minor issue over Directives UI
    * Fixed Shadow Coord macro registry differences between Unity 5.5 and above

* Improvements:
    * Added Substance Plugin integration over ASE for Unity 2018 and above
    * Added Mirror option into 'Depth Fade' node
        * Enabled by default, which was older behavior
        * If toggled on, sets an abs operation over the final value
    * Custom Render Textures can now be used/dragged over ASE
    * Can Pan and Zoom ASE canvas camera through keyboard directional keys
        * Up/Down/Left/Right Arrow keys pan camera
        * Alt+Up/Alt+Down/Alt+Left/Alt+Right Arrow keys zoom camera

v1.5.7 rev 00:
* Fixes:
    * Fixed issue with using FACE type nodes over both Lightweight and HD Unlit templates
    * Fixed issue regarding 'Lerp' node's Alpha input port always loading as a Float type
    * Fixed minor issue on 'Function Subtitle' node tooltip
    * Fixed minor issue on 'Posterize' node
        *  Power variable should not be shown on property window if its input port is connected
    * Fixed minor issue with auto-spacing over shader function names

v1.5.6 rev 08:
* Fixes:
    * Fixed Queue Offset not being correctly set when loading certain template based shaders
    * Fixed HSV nodes previews
    * Fixed multiple issues with Directives over shaders functions
    * Fixed issue on nodes not being correctly set up over template's invisible passes with linked ports

* Improvements:
    * Directives in Include mode now fully support cgincs and hlsl files to be dragged into them
        * Similar behavior of inline properties, click on button right next to Include textfield to activate object drop UI
    * Adding GPU Instancing support to Unlit template
    * Adding new Vertex Position port into 'Depth Fade' node
    * Adding new Saturate option into 'Depth Fade' node
    * Can now set precision to each 'Custom Expression' node items individually

v1.5.6 rev 07:
* Fixes:
    * Fixed duplicated events registry when loading graphs
    * Fixed incorrect instanced variables reset over shader functions

v1.5.6 rev 06:
* Fixes:
    * Out variables on 'Custom Expression' node no longer appears as inputs
    * Fixed issue on not updating output port names correctly over 'Custom Expression' node
    * Fixed issue on properties with Header(...) not being written on shader
    * Fixed incorrect read/write on MRT Blend Mode and Op over template based shaders
    * Fixed possible infinite loops caused by 'Register/Get Local Var' nodes
    * Fixed Shader Type dropdown items not updating names correctly according to changes on template
    * Fixed issue on adding multiple ASE tabs on Unity 2018.3
    * Fixed WebGL error over 2018.3b
    * Fixed 'Parallax Offset' node issues when used on Lightweight and HD SRP templates
    * Fixed dynamically created outputs over shader function not being correctly set after hot code reload

* Improvements:
    * Property Name and Values get its label clamped when too big
        * Full original name and value is shown over its node tooltip
    * Hidden tag on template shader names no longer created a category called Hidden
    * Adding new ase_funcs tag functionality to determine where function code should be injected 
        * If not specified, functions are grouped with global variables as before
    * Improved template reloading after being manually edited

v1.5.6 rev 05:
* Fixes:
    * Fixed 'Smoothstep' node code generation issue
    * Fixed ASE item grouping issue on Create menu over Unity 2017 and above

v1.5.6 rev 04:
* Fixes:
    * Fixed shortcut manager to take 2018.3 new event behavior into account

v1.5.6 rev 03:
* Fixes:
    * Fixed several shader functions issues
        * Fixed Undo issue when using directives textfields
        * Fixed issue of null references being set on directives list when loaded
        * Fixed directives not being correctly written on shader
        * Fixed incorrect Refraction port activation issue on Standard Surface Custom Lighting
        * Fixed issue on Property nodes with Auto-Register toggled on not being registered
    * Fixed cascaded shadows issue on HD PBR template

* Improvements:
    * Minor update on 'Smoothstep' node ports behavior
    * Added preview to 'Swizzle' node

v1.5.6 rev 02
* Fixes:
    * Fixed issue on multi-pass templates not getting correct interpolator count from subshader info
    * Fixed issue on Depth Offset template option auto enabling after hot code reload

* Improvements:
    * Using current world position and world view direction when getting indirect specular light values on templates

v1.5.6 rev 01
* Fixes:
    * Fixed issue with global variables on Property type nodes being ignored by Auto-Register toggle
    * Fixed Native Directives capture issue over templates
        * Fixed issues on adding duplicate directives if they are already natively on the template
    * Fixed compilation errors from Gamma/Linear nodes on SRP templates
    * Fixed ArgumentOutOfRangeException over file path on new shader creation
    * Fixed issues over Motion Vector pass on HD PBR template
    * Fixed issue on 'Light Color' node with HD templates
    * Fixed issue on 'World Space Light Dir' node with HD templates
    * Fixed issue on 'Object Space Light Dir' node with HD templates

* Improvements:
    * Added support for Variable Mode over Texture Objects type nodes
    * Adding UI Widget helper for setting Render Type and Queue for templates
        * On SubShader Tags group, Value UI changes as soon as user writes RenderType or Queue on the Name textfield

v1.5.6 rev 00
* New Samples:
    * SRP Lightweight Terrain
    * Multi Pass Distortion

* New Node:
    * Switch by Pipeline
        * Shader function specific node to choose different paths according to current pipeline

* New Shader Function:
    * Fetch HD Color Pyramid

* Fixes:
    * Fixed world position issues over HD PBR Template
    * Fixed transparent issues over HD PBR template
    * Prevented WorldNormalVector macro to confuse lerp operations
    * Fixed issue on created shaders being placed in incorrect folders when on two column node
    * Fixed issue on property registering over multi-pass shaders

* Improvements:
    * Custom Options can now be added directly over templates
    * Adding Final Color x Alpha custom option into LW PBR
        * If toggled on multiples fragment final color by its alpha
    * Apply Fog on LW PBR takes Add Pass directive into account
    * Adding SRP support to Four Splats First Pass Terrain shader function
    * Material Global lIllumination flags for emission can now be modified over Surface Rendering Options
    * Activated Variable mode into 'Texture Sample' nodes

v1.5.5 rev 01
* Fixes:
    * Fixed issue over incorrect name registration over 'Register Local Var' on pasting into another shader
    * Fixed issue on reading shaders using old shader functions with deprecated nodes
    * Fixed 'World Pos' node issue on HD SRP template
    * Fixed issue on 'World Normal' node when used in 'Lerp' node

* Improvements:
    * 'Register/Get Local Var' nodes maintain connections even if copied to another shader

v1.5.5 rev 00
* New Node:
    * Diffuse And Specular From Metallic

* Fixes:
    * Fixed issues on graph not being updated with material changes on inspector if tab is opened but not selected
    * Preventing Culture difference issues on material copy-paste
    * Fixed issue on all modules template tag conflict with existing shader model definition
    * Fixed HD issue on 'World Position' node
    * Fixed issue on losing name when copy-pasting property nodes between shaders
    * Fixed issue on resetting custom inspector to ASE default when loading or hot code reloading shader with its textfield empty
    * Fixed issue on not setting importer correctly on 'Texture Sample' node when setting a normal map type texture from inspector

* Improvements:
    * Properties on invisible passes can now be synced with main pass over templates

v1.5.4 dev 10:
* New Sample:
    * SRP HD Material Types
        * Please notice that for SSS to work properly, a Diffusion profile must be attached to the HD pipeline asset
            * We already supply one over the sample folder ( Diffusion Profile Settings )

* Fixes:
    * Fixed UX issue with Auto-Register/Set Unique flags on 'Custom Expression' node
    * Fixed issue with cascade shadows over Lightweight template

* Improvements:
    * Added Material Types selector into HD template
        * Subsurface Scattering
        * Standard (Metallic)
        * Specular
        * Anisotropy
        * Iridescence
        * Translucent

v1.5.4 dev 09:
* Fixes:
    * Fixed Grab Pass issue with 'Outline' node
    * Fixed PreviewRenderUtility leak over shader and material inspector on 2018.2
    * Fixed issue with 'PI' node over HD SRP

* Improvements:
    * Minor improvement over shader save time

v1.5.4 dev 08:
* Fixes:
    * Fixed issue on 'Component Mask' node generating compilation errors inside shader functions with auto-cast inputs
    * Fixed issue on loop unroll error over 'Parallax Occlusion Mapping' node
    * Fixed issue on crashing ASE when loading a shader or shader function with a missing shader function reference
        * Adding error message when loading a shader or shader function with missing shader functions

* Improvements:
    * Added support to Vertex Normal modification on both Unlit and PBR HD templates
    * Adding 'Global Array' variable name info directly on node

v1.5.4 dev 07:
* New Templates:
    * HD PBR
    * HD Unlit

* Fixes:
    * Fixed issue on incorrectly capturing module tags
    * Fixed issues with 'Flipbook UV Animation' node
    * Fixed multiple issues with Directives usage under shader functions with templates
    * Fixed issue on 'Custom Expression' node loading on Call mode
    * Fixed issue on unpacking normals with scale on templates over multiple nodes
    * Changing tessellation on Procedural Wall sample shader from edge to distanced based to prevent metal issues on Mac

v1.5.4 dev 06:
* Fixes:
    * Fixed issue on 'Toggle Switch' node not taking Custom Attributes into account
    * Fixed out of bounds exception over 'Static Switch' node
    * Fixed issue on templates port linking behavior
    * Fixed issues on 'Texture Array', 'Triplanar Sample' and 'Unpack Scale Normal' nodes on Lightweight SRP
    * Fixed issue on shader function directives not being written on template based shaders
    * Fixed serialization issue on saving/loading shader function directives
    * Fixed issue with using 'Texture Array' node inside shader functions
    * Fixed shader compilation errors on'Parallax Occlusion Mapping' 
        * Now it does not generate code if no 'Texture Object' node is connected to it
    * Fixed issue on 'Grab Screen Color' node not updating reference list correctly when copy/pasted
    * Fixed issue with Undo'ing a 'Grab Screen Color' node on reference mode

* Improvements:
    * Min and Max Samples options on 'Parallax Occlusion Mapping' node are now inline options

v1.5.4 dev 05:
* Fixes:
    * Fixed issue with Vector and Matrix UI spacing on 'Custom Expression' node
    * Fixed issue on functions being generated twice over a 'Custom Expression' node when auto register and connection are both on
    * Fixed Blend Op issues with OFF state

* Improvements:
    * Can now use a custom screen position into 'Dither' node
    * Added Normalize option into 'Compute Screen Pos' node

v1.5.4 dev 04:
* Fixes:
    * Fixed issue on resetting material's Render Queue setting when compiling the shader in Material mode
    * Fixed issue on Blend Op menu not showing when loading a template based shader

* Improvements:
    * Re-adding DX11 specific Blend Ops into its respective dropdowns
    * 'Custom Expression' node items are now reorderable
    * 'Custom Expression' node can now register dependencies to other ones
         * Can now better control function register order on final shader

v1.5.4 dev 02/dev 03:
* New Shader Function:
    * Color Mask

* Fixes:
    * Fixed issue on template Unlit SRP generating an error when creating a Color or MainTex named property
    * Fixed issue on both 'Surface Depth' and 'Camera Depth Fade' nodes when inputting a custom vertex position
    * Stencil module on Pass now uses SubShader Cull mode if it's not specified on it
        * Important since Cull determines if separate stencil ops are show for each face
    * Fixed issue with Fallback shader selector not updating if its textfield is focused
    * Fixed issue on Grab Pass only being registered on first pass when on multi-pass templates

* Improvements:
    * Added auto register option into 'Grab Screen Color' node
    * Adding UsePass support
        * New Additional Use Passes section on both Surface and Template output nodes
        * Can add UsePass before or after Surface code via a Location dropdown
        * On Templates the Below location is still under development and its not available

v1.5.4 dev 01:
* New Shader Function:
    * Fetch Lightmap Value

* Improvements:
    * Simple Terrain sample now support more than four splats

* Fixes:
    * Fixed issue on Node Palette reading incorrect maximized value when opening ASE window

v1.5.3 dev 12:
* Fixes:
    * Fixed null pointer exception with texture arrays over shader functions
    * Fixed memory leak on shader functions destruction

* Improvements:
    * Improved templates loading behavior after hot code reload
    * Replaced individual lists for pragmas, defines and includes by a single reorderable list
        * Now user select what each line represents via a dropdown
    * Additional surface options can now be added via the Additional Surface Options tab on the Output node
    * Added custom type for 'Custom Expression' node data type specification

v1.5.3 dev 11:
* Fixes:
    * Fixed issue on not correctly cleaning Undo stack when changing master node
    * Fixed multiple issues with safe normalize operation over world light dir and view dir 
    * Fixed world light dir issues over Lightweight SRP
    * Fixed GPU instancing compile issues on Lightweight SRP
    * Fixed issue on Includes/Pragmas/Defines UI not showing when no other modules over a template

* Improvements:
    * Optimizing property block allocation for instanced variables to have less internal padding involved
    * Added custom attributes to 'Toggle Switch' node
    * Added toggle to 'Matrix from Vector' node use each input as column

v1.5.3 dev 10:
* Fixes:
    * Fixed issue with being able to duplicate output nodes on templates
    * Fixed issue on foldout flag values being shared between multiple ASE tabs

* Improvements:
    * 'Get Local Var' port is locked until a valid reference is set to prevent incorrect connections
    * Updated About image and added Manual link on Amplify Shader Editor menu group
    * Adding unique id duplicate prevention over shader load
    * Added support for stencil operations on Lightweight templates

v1.5.3 dev 09:
* New Shader Functions:
    * Lerp White To
    * Detail Albedo

* Fixes:
    * Fixed API library excluding tool for custom nodes

* Improvements:
    * Template creators can now register all modules
        * Simply add the /*ase_all_modules*/ either on the Pass or SubShader body
    * Templates now support Fallback definition on shader
    * Templates now support Dependencies definition on shader

v1.5.3 dev 08:
* Fixes:
    * Fixed Instance ID issue over Lightweight SRP template
 
* Improvements:
    * Added API option to remove includes on data collector
    * Stencil can now be toggled off on templates which have it

v1.5.3 dev 07:
* New Sample:
    * Projectors
        * Exemplify Unity Projectors usage under ASE

* Improvements:
    * Improved Texture Array Tool
        * Now also creates Texture3D by hitting the Texture 3D toggle
        * Can drag and drop multiple textures into tool
        * Can drag and drop a folder and it will include all its nested textures
        * Added Clear button to remove all textures from Texture List ( UI still work in progress )
    * 'Parallax Occlusion Mapping' node now also supports Texture 3D
        * Replaced the Texture Array toggle by a Texture Type dropdown
        * User must explicitly set the current 3D texture slice via the Tex3D Slice input port
    * Removed Fixed precision type since it's no longer available on Unity 2018
    * Updated Lightweight PBR template to v1.1.8
    * Re-factored Templates location and names
        * New and more flexible templates will be gradually added to replace legacy ones
                * A cleaner Unlit template shader was already added on this build

* Fixes:
    * Fixed issue on using depth operations on particle template
    * Fixed sometimes light attenuation not showing up correctly
    * Fixed allowing to open unity built-in shaders on ASE 
    * Fixed issue on using incorrect port value on 'Refract' node 
    * Fixed issue on Texture Object type nodes loosing texture type after load

v1.5.3 dev 06:
* New Sample:
    * XRay
        * Object has different drawing behavior in front and behind a wall
        * Wall pattern is procedurally generated

* Improvements:
    * Template internal properties can be used as inline properties on modules
    * Modules can now read inline properties from the template original source
        * Unreadable data message is no longer shown on node properties
    * Added more options to 'Fresnel' node
        * Now supports custom view vector
    * Preventing cast warning  with object to clip internal function usage when building shader code for Lightweight SRP
    * Added new icons for inline properties to the Unity personal skin to make them easier to spot
    * Updated Lightweight SRP templates
        * Cull Mode now shared across base, shadow and depth pass
        * Added new Normal input port

* Fixes:
    * Fixed issue over templates on not assuming inline properties when loading/saving Cull Mode module
    * Fixed incorrect cast and type assignment on connections between 'Texture Object' and 'Texture Sample' nodes
    * Fixed issue on deleting outputs on shader functions and later opening the shaders that use it
    * Fixed issues on Vertex Offset port on Lightweight PBR template 
    * Fixed issue on not correctly loading 'Rotate About Axis' node from old shaders
    * Fixed issue on header not showing with texture properties with the NoScaleOffset attribute

v1.5.3 dev 05:
* Fixes:
    * Fixed world normal issues over 'Outline' node
    * Fixed issues on reading/writing cull mode on templates meta
    * Fixed issue on capturing includes over templates
    * Fixed issue on Specular workflow over Lightweight PBR template

* Improvements:
    * Cull Mode can now be modified over outline node 

v1.5.3 dev 04:
* New nodes:
    * Transform Position
    * Transform Direction

* Improvements:
    * Added inline property support over templates modules
        * Also added inline property support over Depth Offset values in both template and standard surface
    * Minor tweak over 'Clamp' node
        * Internal max value defaulted to 1
    * Adding additional modules into Lightweight templates
    * Enabling ZTest over Particles Alpha Blended template

* Fixes:
    * Fixed issue on Lightweight PBR template over tangents on objects with non-uniform scales
    * Fixed issue on Lightweight PBR not receiving real time shadows
    * Fixed issue on Lightweight Unlit template available interpolators
    * Fixed issue on Lightweight Unlit template not showing correctly on dropdown menu
    * Fixed issue on tab titles not being correctly written on shader mode over templates
    * Fixed issue on property names incorrectly appearing over inline properties
    * Fixed issue with copy/pasting 'Append' nodes
    * Fixed issue on 'Global Array' node loading from older shaders

v1.5.3 dev 03:
* New Template:
    * Lightweight Unlit
        * Renamed already existing SRP Template to Lightweight PBR

* New Sample:
    * SRP Lightweight 2D Fractal

* Fixes:
    * Fixed issue with 'Parallax Occlusion Mapping' node over Lightweight templates
    * Fixed Safe Normalize issues with 'View Dir' node on templates

* Improvements:
    * Added Auto-Register option into 'Static Switch' node
    * Minor improvement over 'Parallax Occlusion Mapping' node generated code to avoid duplicate calculations

v1.5.3 dev 02:
* Fixes:
    * Fixed infinite loop via context menu issue

* Improvements:
    * Added support for Specular workflow into Lightweight SRP template
        * Use either the new Specular port or the already existing Metallic port to choose which workflow to use
    * Added extra ASE null pointer exception prevention test on attempting loading shader with missing shader functions

v1.5.3 dev 01:
* Improvements:
    * Conditional 'If' node also support Int as compare values
    * Added Triplanar scale port for normal scaling when using normals in 'Triplanar Sample' node
    * Improved code generation for 'Texture Coordinates' node
    * Changed way float values were being generated when their value was an integer
        * Removes warnings and makes sure some operations are done in the correct space, like divisions
    * Improved operations order on 'Panner' node
    * Added Force Disable Instancing option into Surface output node Rendering Options
    * Updated Lightweight SRP template to be compatible with Unity latest LW SRP version 1.1.5

* Fixes:
    * Fixed issue on 'Global Array' node not being correctly detected by ASE
    * Fixed texture array sampling error caused by 'Parallax Occlusion Mapping' node on Unity 2018
    * Fixed issue on local variable excluded reset method
    * Fixed issue on 'Triplanar Sample' node Material Mode not being correctly set

v1.5.2 dev 05:
* New Samples:
    * Single Channel Masking by Sarah Akers and David Marshall
    * Dithering Fade Blue Noise

* New Node:
    * Inverse View Matrix

* Fixes:
    * Fixed issue on not copying defaults to material values when changing Type from Global to Property over property type nodes
    * Fixed Stencil Comparison not being correctly written on shader when set as an inline property
    * Fixed issue on Reference mode not being taken into account when registering 'Texture Sample' properties
    * Fixed infinite loop on 'Texture Sample' nodes with Reference mode
    * Fixed custom outline alpha mask mode
    * Fixed issues on compare type nodes accessing their Wiki page
    * Fixed dropdown left title padding for nodes that have it but don't have a preview arrow

* Improvements:
    * Improved 'Unpack Scale Normal' node usage on Burn Effect sample
        * Now compatible across all Unity versions
    * Re-factored Global variables behavior over property type nodes
        * Material values can no longer be edited
        * Auto-fetching global variables values every 2 seconds 
    * Parameter type can be changed in node over texture type nodes
    * Removing automatic header creation by properties under shader functions
        * User now can manually set headers on property type nodes via its attributes
    * Turned the main Mask Clip Value an inline property so users can set it to be a specific node
    * Updated 'Screen Position' node preview

* New Previews:
    * Compare ( A > B )
    * Compare ( A >= B )
    * Compare ( A < B )
    * Compare ( A <= B )
    * Compare ( A == B )
    * Compare ( A != B )
    * Compare With Range
    * If [Community]
    * Decode Float RG
    * Encode Float RG
    * Decode Float RGBA
    * Encode Float RGBA
    * Decode View Normal Stereo
    * Encode View Normal Stereo
    * Decode Lightmap
    * ColorSpaceDouble
    * Face
    * Switch By Face
    * LOD Fade
    * Layered Blend
    * Weighted Blend
    * Summed Blend

v1.5.2 dev 04:
* Fixes:
    * Fixed issue on Post-Process sample scenes crashing on lower Unity versions
    * Fixed issue on fallback behavior when required template on loaded shader is not found
    * Fixes issue on texture type nodes moving when a texture is selected over it's picker
        * ASE window now aware when ObjectSelectorClosed command is fired to ignore incorrect mouse delta values

* Improvements:
    * View Dir vector on templates is now calculated on fragment by default
    * World Reflection vector on templates is now calculated on fragment by default
    * Native View Dir vector calculation on Lightweight SRP template is now done on fragment
    * Shader function 'Reconstruct World Position From Depth' now also works with screen shaders
    * Updated Skybox - Cubemap Extended by Cristian Pop sample

v1.5.2 dev 03:
* Fixes:
    * Fixed issue with 'Grab Screen Color' node on Lightweight SRP template
    * Fixed multiple issues with Shader Model selection over templates
    * Fixed issue on not correctly syncing module data from modified template

* Improvements:
    * Re-factored local variable creation over ports to make it more robust
    * Vertex ports are now analyzed before fragment ones
        * Vertex position dependent operations now take applied offsets into account
    * Pragmas, Includes and Defines are copied from linked nodes if Pass is set as invisible
    * Added preview to 'Vertex To Frag' node
    * Small optimization on drawing connections
    * Minor tweak over Particles Alpha Blended template
    * Assigned dynamic interpolators now take both sub shader and pass info into account
        * Modified Lightweight SRP template to share dynamic interpolators amount over hidden passes

v1.5.2 dev 02:
* Improvements:
    * Added Safe Normalize option to 'World Space Light Dir' node
        * Assures vector is not zero even if there's no lights in scene 
    * Added Safe Normalize option to 'View Dir' node
        * Assures vector is not zero even if there's no cameras on scene
    * Property names can neither be or start with numerical values nor Unity reserved names
    * Variables from 'Register Local Var' nodes cannot start or be numerical

* Fixes:
    * Fixed shader function subtitles not being shown after being set with the 'Function Subtitle' node
    * Fixed Custom Lighting using opacity and opacity mask duplicating code

v1.5.2 dev 01:
* Fixes:
    * Changed alpha generation for Custom Lighting to work correctly with alpha mask
    * Fixed issue on shader function reloading not being taken as a load operation
    * Fixed instanced properties not being correctly taken into account inside shader functions

* Improvements:
    * Improved shader function asset loading on Shader Function node

v1.5.1 dev 06:
* Improvements:
    * Added preview to 'Noise Generator' node
    * Improved node internal data viewer performance
    * Improved the shader changed/dirty flag behavior
    * Improved graph canvas position adjust on loading from a previously different window configuration
    * Renamed 'Melting' sample shader to be in accordance with other community samples

* Fixes:
    * Fixed property node auto-register issue on 'Skybox - Cubemap Extended' sample
    * Fixed issues with 'World Normal' and 'World Reflection' nodes on PS4
    * Fixed multiple cast issues across multiple nodes
    * Fixed incorrect port behavior on 'Refract' node
    * Fixed issue on undoing instanced properties assignment
    * Fixed 'Break To Components' node duplicating code and generating errors

v1.5.1 dev 05:
* Improvements:
    * Added previews to nodes
        * Desaturate
        * Grayscale
        * Posterize
        * Simple Contrast
    * Normal type input ports now show the correct default tangent vector on nodes internal value previewer
        * Indirect Diffuse Light
        * Indirect Specular Light
        * Fresnel
        * World Normal

* Fixes:
    * Fixed possible issue with 'Desaturate' node on PS4
    * Fixed issues with Undo/Redo operations over Wire nodes 
    * Fixed light color and light dir info on 'Light Color' 
        * Now takes into account Unity different behaviors between versions
    * Fixed issue on loading canvas on ASE tabs pointing to inexistent resources

v1.5.1 dev 04:
* New Node:
    * Function Subtitle
        * Allows creation for custom subtitles on Shader Functions

* New Shader Functions:
    * Perturb Normal
    * Cotangent Frame

* Improvements:
    * Can now assign property to both blend modes and operations
    * Removing carriage return when saving 'Custom Expression' node code on meta
    * Improved restrictions on Shader Functions outputting Matrix type data
    * Name is carried over Pasted/Duplicated 'Custom Expression' nodes
    * Custom Editor/Inspector option is now available over templates
    * Updated both Lightweight template and samples to new beta version
        * Template now support vertex offset and alpha test
    * Nodes internal data can be viewed directly on canvas 
        * Can be turned on/off through the 'I' key
    * Added Editable If Custom Drawer by BinaryCats
    * Dynamic interpolator cap now respecting pass choice on graph 
    * Updating current templates to be dynamically cap'ed
    * 'Light Color' and 'World Space Light Dir' nodes now behave properly when used with lightmaps 

* Fixes:
    * Fixed issue with incorrectly trying to write fragment instructions on template without specified frag code area
    * Fixed issue where reference 'Function Switch' nodes were not displaying their option in all cases
    * Fixed wire node Undo issues

v1.5.1 dev 03:
* New Samples:
    * Simple Potion Liquid
    * Melting by Gil Damoiseaux

* Improvements:
    * Extended functionalities on 'Custom Expression' node 
        * Added Auto-Register toggle to create custom functions even if not connected to output node
        * Added void to Output Type
        * Call Mode toggle is now a Mode dropdown
            * Mode Call now only allows external function calls or inline instructions
            * If a return instruction is detected on code, Mode is set to Create and return type set to void

* Fixes:
    * Fixed Undo issue with property type nodes auto-register option
    * Fixed issue on calculation view direction on tangent space over templates

v1.5.1 dev 02:
* Fixes:
    * Fixed issue with 'Standard Surface Light' not taking Normal Space option into account
    * Fixed minor issue over 'Outline' node
    * Fixed issue on Undo not resetting correctly ports internal values
    * Fixed issue on incorrectly assigning main output node status into copy-pasted 'Function Output' nodes

* Improvements:
    * Added custom attributes to 'Static Switch' node
    * Custom Material Inspector now properly copies texture scale and offset values

v1.5.1 dev 01:
* Fixes:
    * Fixed 'Static Switch' node not properly generating Enum code in the correct order
    * Fixed normal generation issue on 'Standard Surface Light' node
    * Fixed issue when attempting to load shader using inexistent template on project folder
        * Now fall-backing to Standard Surface when template is not found
    * Fixed issue capturing properties with attributes on templates
    * Fixed incorrect tool-tip on 'Template Multi-Pass Switch' node
    * Fixed issue on normal generation over 'Fresnel' node
    * Fixed initialization issue on Default UI template
    * Fixed issue with accessing uninitialized textures on 'Triplanar Sample' node
    * Fixed preview issue on 'Template Parameter' node
    * Fixed issue locking picker on texture type nodes to current type when auto-cast is on 

* Improvements:
    * Improved 'Break To Components' generated code
    * Preventing duplicates/re-definition of Pragmas, Defines and Includes over templates

v1.5.0 dev 02:
Fixes:
    * Fixed issue on template capturing commented properties
    * Fixed multiple issues over templates with multiple sub-shaders
    * Fixed issue on master nodes attempting to access uninitialized UI texture
    * Fixed issue with sometimes not extracting the correct pass name from template
    * Fixed issue on incorrectly catching shader name on fetching modules info
    * Fixed issue with correctly setting the shader name

v1.5.0 dev 01:
* New Samples:
    * SRP Lightweight GlintSparkle
    * SRP Lightweight Coverage

* Fixes:
    * Fixed issue on loading shader with missing shader functions
    * Fixed issue on duplicate pragmas over generated shader
    * Fixed issue with template post processor not correctly registering new templates
    * Fixed issue on template output nodes modules incorrectly sharing foldout value
    * Fixed Undo issue when undoing deleted property type nodes after saving shader
    * Fixed 'Static Switch' node registering duplicates in the material properties group

* Improvements:
    * Reorganized samples shader paths
    * Reorganized template menu items
    * Canvas and output node title now shows shader name without relative paths
    * 'Register Local Var' node now generates a new name when duplicated
    * 'Indirect Diffuse Light', 'Indirect Specular Light' and 'Light Attenuation' nodes now work on templates

v1.4.5 dev 04:
* New features:
    * Added support for multi-pass templates
    * Added support for Lightweight Scriptable Rendering Pipeline

* New Shader Function:
    * Create Normal : Generates a normal map from a height map

* New Template:
    * LightweightSRP

* Fixes:
    * Fixed texture coordinates generation in templates
    * Changed initialization in 'Triplanar Sample' node to prevent null pointer errors

v1.4.5 dev 03:
* Improvements:
    * Exposed Specular Color property into the Output node material list when Blinn Phong Light Model is selected

v1.4.5 dev 02:
* Fixes:
    * Fixed Auto-Register option not being saved for property type nodes
    * Fixed issues with generated normalized screen position values over templates

* Improvements:
    * Cull, Stencil, Color Mask and Depth options can now reference properties instead of standard options
    * Redone visuals of property type nodes Attributes list to be easier to use
    * Added new Enum and Custom attributes to property type nodes
        * Enum on Create Enum mode creates an Enum attribute using the specified Name/Value pairs
        * Enum on Use Engine Enum Class mode creates an Enum attribute from the specified class
        * Custom attribute allows to specify completely custom attributes
    * Outline now forces shader to be Forward only to prevent visual inconsistencies

v1.4.5 dev 01:
* New Shader Function:
    * Constant Bias Scale

* Fixes:
    * Fixed null pointer issue with removing connections with Alt key
    * Fixed issue on 'Template Local Var' node only working on fragment function

v1.4.4 dev 06:
* Fixes:
    * Fixed issue on 'Texture Sample' nodes not automatically setting the texture type on AUTO
    * Fixed connection type mismatch after CTRL + swap shortcut
    * Fixed issue on 'Dither' node

v1.4.4 dev 05:
* Fixes:
    * Fixed multiple Undo issues on shader functions
    * Fixed serialization issue with both nodes and graph when hitting Unity play button

v1.4.4 dev 04:
* Fixes:
    * Fixed issue on Specular Highlights and Reflection toggle not being read/written into shader meta
    * Fixed issue on not correctly taking templates global variables into account when building shader
    * Fixed issue where 'Triplanar Sample' node wasn't being initialized properly
    * Fixed Undo issues on create/delete nodes
    * Fixed Undo issues on preview node resizing

* Improvements:
    * Added new Enum Property Attributes for both 'Float' and 'Int' nodes
    * Added new Keyword Enums option to 'Static Switch' node Type option
        * Improved its interface to better accommodate new options
    * Added new keywords to 'Static Switch' Keyword dropdown
    * Increase max port count for 'Function Switch' node to 9
    * Added/Changed shortcuts for various nodes 
        * R - Create 'Register Local Var' node 
        * G - Create 'Get Local Var' node
        * Z - Create 'Swizzle' node 
        * X - Create 'Cross' product node
        * Period(.) - Create 'Dot' product node
        * B - Create 'Break to Components' node
        * K - Create 'Component Mask' node
        * V - Create 'Append' node

v1.4.4 dev 03:
* New Sample:
    * Skybox - Cubemap Extended by Cristian Pop

* New Shader Functions:
    * Step Antialiasing
    * Create Orthogonal Vector

* Improvements:
    * Node graph is now a ScriptableObject to better deal with automatic serialization
    * Templates can now register and use local variables
        * Use /*ase_local_var*/ before the variable declaration over the template
        * Access the local variable through the new 'Template Local Var Data' node over the graph
    * Added new Normal Space option into 'Standard Surface Light' node
    * Optimized nodes port internal data usage
    * Node tooltip no longer shown online link for shader function nodes
    * Small optimizations to node previews

* Fixes:
    * Fixed function registry on 'Custom Expression' node to correctly deal with dependencies
    * Fixed issue on linear textures over the texture creator tool
    * Fixed issue where the editor was being called on play mode but no window was present
    * Fixed issue on texture coordinates of different sizes being created with the same name

v1.4.4 dev 02:
* Fixes:
    * Fixed issue on templates where vertex normal was being declared as a float4 on vertex data
    * Fixed PS4 issue on billboards generated code
        * Updated Orientation Based Sprite sample to include fix

* Improvements:
    * Changed texture array creator to support compressed formats 
    * Added custom inspector for texture arrays to allows previewing their contents on the inspector window
    * Added new Set Unique option into 'Custom Expression' node
        * If toggled off, generated internal function doesn't use unique id on its name

v1.4.4 dev 01:
* Improvements:
    * Custom Pragmas, Includes and Defines are now also included on the Outline pass
    * Automatically removing crlf from copy-pasted code over the 'Custom Expression' node
    * Custom Render Type can now be specified on the Render Type dropdown over the Output node properties

* Fixes:
    * Fixed issues on 'Remainder' node

v1.4.3 dev 05:
* Fixes:
    * Fixed issue on excluding Add Pass independently of Debug port connection

v1.4.3 dev 04:
* New Shader Function:
    * Radial UV Distortion
        * Sample with the same name was also added to demonstrate its usage

* Fixes:
    * Fixed multiple issues related to Debug port usage on Output node
        * It now works as a custom lighting port instead of emission
    * Fixed issue on incorrectly opening standard materials into ASE
    * Fixed issue on duplicate local variable creation when using 'Parallax Mapping' node on shader functions

v1.4.3 dev 03:
* Fixes:
    * Fixed issue on 'Vertex Tangent' node internally outputting float4 data in templates instead of float3
    * Fixed shader function category typos
    * Fixed issue on ports accessing already destroyed nodes for previews
    * Fixed issue on precision type used in shader functions

* Improvements:
    * Added ZWrite and ZTest options to 'Outline' node

v1.4.3 dev 02:
* Improvements:
    * Shader tags can now be modified on templates based shaders
    * Added new Particle Additive option on Blend RGB and Blend Alpha dropdown
        * Sets Source to 'Source Alpha' and Destination to 'One'
    * Added new options to 'Shade Vertex Lights' node
        * Can now select amount of lights to take into account and if as spot or point light

* Fixes:
    * Fixed issue on incorrectly indented code generated by some nodes on template shaders

v1.4.3 dev 01:
* New Samples:
    * Orientation Based Sprite
    * UI Sprite FX

* New Shader Function:
    * Box Mask
    * UI-Sprite Effect Layer

* Fixes:
    * Fixed issue on attempting to deactivate destroyed nodes

* Improvements:
    * Updated Sprite and UI templates to better support normal maps
    * Changed way logic is updated in some nodes to be more consistent and work inside shader functions

v1.4.2 dev 06:
* Fixes:
    * Fixed incorrect wire highlighting caused by 'Register Local Var' node
    * Fixed issue with 'Texture Sample' node not generating proper UVs in specific cases when using templates
    * Minor UI and refresh fixes to 'Function Switch' node
    * Fixed issue on incorrectly loading SimpleTerrain sample
    * Fixed issue on unnecessary loading when opening a changed shader or function

* Improvements:
    * Added compatibility with Unity 2018
        * For now 'Substance Sample' node is unavailable on this version
        * Substance example is now inside a unity package to prevent sbsar importing error
    * Removed Substance and API update warnings on Unity 2017
    * Added new Vertex Position input port into 'Surface Depth' and 'Camera Depth Fade' nodes
        * Can now take custom vertex positions into account
    * Added some minor improvements into 'Register Local Var'/'Get Local Var' node usage
        * 'Register Local Var' node is now highlighted if one of its 'Get Local Var' nodes are selected
        * 'Register Local Var' node now lists and can focus each 'Get Local Var' which uses it
    * Added reference capabilities to 'Function Switch' 
        * One node can now control different paths of a shader function graph

v1.4.2 dev 05:
* Fixes:
    * Fixed Undo issues with connections created/deleted by drag + Alt
    * Fixed issue on templates vertex local variables when using 'Vertex To Fragment' node
    * Fixed bug where 'Function Switch' node options were being incorrectly saved which caused a crash on load
    * Fixed shader 'Function Switch' node options not being correctly ordered all the time 
    * Fixed connection signal detection to make 'Function Switch' nodes know when to turn on

v1.4.2 dev 04:
* New Node:
    * Function Switch
        * Node specific to shader functions which allows switching options at compile time on them

* New Shader Function:
    * Flipbook

* Improvements:
    * Changed switch type nodes port order to match 'Function Switch' and maintain consistency

* Fixes:
    * Fixed issue on duplicated functions when both outline and shadow caster passes are active
    * Fixed issue with calculating label size for nodes
    * Fixed UI issue when not being able to read depth info from template

v1.4.2 dev 03:
* Improvements:
    * Added Depth module into templates
    * Minor improvement on Four Splats First Pass Terrain shader function
    * Minor improvement on TerrainSnowCoverage and SimpleTerrain samples
    * Added preview and access to internal data into 'HSV To RGB' and 'RGB To HSV' nodes
    * Added preview to 'If' node
    * Added new Normalize option into 'World Normal' and 'World Reflection' nodes
    * 'Outline' node now supports transparency and mask operations through the new Alpha Mode option
    * An additional depth pass can be added via the new Extra Depth Pass option on the Output Node properties
    * Property nodes can now register its properties/global variables without being connected to Output node
        * Done via the new Auto-Register option

* Fixes:
    * Fixed minor issue on 'Rotate About Axis' node UI
    * Fixed issue with 'World Reflection' node on templates

v1.4.2 dev 02:
* Improvements:
    * 'Toggle Switch' node can now share properties
    *  Added lock button to property name on property type nodes
        * Allows to customize the internal variable name used
    * Added support for 'View Dir' node on templates
    * Added preview position selection to shader functions to further customize function nodes

* Fixes:
    * Fixed issue where custom shadows were ignoring vertex colors
    * Fixed issue on 'Fresnel' node using a non-normalized normal vector
    * Small fix on 'Static Switch' node getting and setting values from the material

v1.4.2 dev 01:
* Improvements:
    * Lowered Shader Model target of Custom Lighting Toon example to be compatible with MacOs
    * Improved generated code for 'Grab Screen Color' and 'Grab Screen Position' nodes
    * Changed automatic generation of 'Grab Screen Color' node to make sure normalization is happening only once 
    * Added a toggle on 'Grab Screen Color' node which chooses if it normalizes/projects the input or not (default OFF)

* Fixes:
    * Fixed issue on generating default values for shader functions
    * Fixed issue on normal generation with the following nodes:
        * 'Fresnel'
        * 'Indirect Specular Light'
        * 'World Reflection'

v1.4.1 dev 02:
* New Nodes:
    * Decode Depth Normal
    * Encode Depth Normal

* Improvements:
    * Added internal data into shader functions 
        * If inputs are unconnected is now possible to change the default value from its node property panel
    * Property type nodes can now keep material value when not connected to an Output node
    * Tweaked 'Static Switch' node properties
    * Initial opened tab on property nodes depends on material mode

* Fixes:
    * Fixed issue on 'Custom Expression' node when using out variables in Call mode
    * Preventing 'Commentary' node from generating infinite loops when copy-pasted
    * Fixed issue with incorrect refresh call order on pasted nodes
    * Fixed issue on nodes are not being correctly nested into their parent 'Commentary' node when copy-pasted
    * Fixed issue on 'Toggle Switch' node not updating port names correctly after loading
    * Fixed focus issue on pickers with Mac

v1.4.1 dev 01:
* Fixes:
    * Fixed issue on 'Texture Sampler' node getting incorrectly configured on load
        * When default texture type different from material texture type

v1.4.0 dev 06:
* New Samples:
    * Simple Terrain
    * Terrain Snow Coverage
    * Custom Outline Toon

* New Nodes:
    * Rotate About Axis
    * Outline

* New Shader Functions:
    * Four Splats First Pass Terrain

* Improvements:
    * Tweaked 'Scale And Offset' node behavior
    * Added Defines tab into Output node properties
    * Preventing possible compilation errors on Experimental .NET 4.6

* Fixes:
    * Fixed issue on 'Texture Sampler' node not previewing a connected 'Texture Object' node
    * Fixed issue on texture picker not working correctly on texture nodes
    * Fixed incorrect version reading Convert To Linear parameter at 'Screen Depth' and 'Depth Fade' node
    * Fixed issue on duplicate UV Set on 'Texture Sampler' node when connected to a 'Texture Object'
    * Fixed issue shader function inputs and outputs getting lost when re-focusing  on them by double clicking the shader function node
    * Fixed issue on 'Custom Expression' node name editing on node body

v1.4.0 dev 05:
* Fixes:
    * Fixed issue on shadow caster for legacy samples:
        * Matcap
        * Parallax Mapping Iterations
        * Reflect Refract Soap Bubble
        * Screen Space Curvature
    * Fixed typo in 'Toggle Switch' node
    * Fixed issue on incorrectly accessing port through array id instead of unique id
    * Fixed issue on deprecated internal data not being correctly read into new 'Append' node

* Improvements:
    * Up and Down arrow keys can now change the focus of the node element in the context palette search similar to the Tab key

v1.4.0 dev 04:
* New Samples:
    * Animated UV Distortion

* Fixes:
    * Fixed issue on a shader function node crashing when its corresponding asset is not found
    * Fixed issue on applying Undo on Material Inspector not being caught by ASE canvas
    * Fixed issue on 'Float' node slider not being registered on Undo stack
    * Fixed issue on generating duplicate function names at 'Noise Generator' node
    * Fixed issue on returning to from a shader function to a main graph using with that shader function selected

* Improvements:
    * Added new Variable Mode property into property nodes when their type are not Constant
        * Create Mode, this is the current option on which an uniform variable is always created when the node is analyzed
        * Fetch Mode, assumes that this variable is already declared elsewhere, p.e. an external lib and doesn't declare it
            * When Property Type is selected, the property declaration is still created
    * Added explicit call to both Thread and CultureInfo system classes to avoid conflicts with user classes without proper namespace
    * Shader function's input and output ports maintain correct connections after being internally re-ordered
    * Automatically adding spaces into camelcase'd shader function node titles

v1.4.0 dev 03:
* Improvements:
    * Added validity check on Output and Input data types
    * Improved color code request for ports

v1.4.0 dev 02:
* New Shader Functions:
    * Compute Filter Width

* Improvements:
    * Explicitly calling System.Globalization on TextInfo usage to avoid compilation errors
    * Added custom pragmas for shader functions
    * Updated Vertex Normal Reconstruction sample
    * Added Reflection and Specular Highlight toggles into the Rendering Options group that mimic Unity's Standard
    * Added ToggleOff option to 'Static Switch' node which should now allow the creation of OFF toggles

* Fixes:
    * Fixed issue of properties not being properly ordered if their material property group wasn't open
        * Blend modes now update properly even if group is collapsed

v1.4.0 dev 01:
* Fixes:
    * Fixed issue with 'World Space Light Pos', 'World Space Light Dir' and 'Object Space Light Dir' nodes
    * Fixed issue on 'Texture Coordinates' node forcing a sampler to be picked even when none was selected
    * Fixed issue on 'Indirect Diffuse Light'node ignoring the tangent space normal completely
    * Small fix to shader functions so they can refresh their custom include list properly on change

* Improvements:
    * Added node previews for 'Light Attenuation' and both Indirect Light nodes
    * Added explicit call to Unity Editor ShaderUtil on Material Inspector to prevent class issues
    * Added Dependencies List on the Output Node properties

v1.3.9 dev 03:
* New Nodes:
    * 'Projector Matrix'
    * 'Projector Clip Matrix'
    * 'Texture Transform'

* Improvements:
    * Properties can be re-ordered on Template shaders
    * Cull, Blend Mode, Blend Ops, Color Mask and Stencil Buffer data can be read and modified on Template shaders
    * Added new custom Time port into 'Flipbook UV Animation' node
    * Templates no longer need /*ase_pass*/ tag to be declared
    * Adding UnityEngine.Object redundancy on its usage to prevent issues with other plugins
    * Outline can now take fog into account

* Fixes:
    * Fixed issue on shader function headers being placed last on shader property list

v1.3.9 dev 02:
* Fixes:
    * Additional fix on custom colored categories

v1.3.9 dev 01:
* Fixes:
    * Fixed issue with 'Texture Coordinates' node not generating proper code for sizes bigger than float2 
    * Fixed issue on reading old shaders data into new ASE versions
    * Fixed issue on custom colored categories

* Improvements:
    * Updated various samples that were still compiled on older ASE versions
    * ASE window now loses text focus when losing its focus to prevent UI issues
    * Added proper Texcoord support in custom shadow caster
    * Added new toggle on 'Screen Depth' and 'Depth Fade' nodes to disable conversion from log to linear space 
        * Important for Orthographic camera projections where Depth Buffer values are already stored in linear space

v1.3.8 dev 02/03:
* Improvements:
    * Added new custom lighting port and migrated the emission connection to this port
        * Now custom lighting display both albedo and emission to be used in baking
    * 'Function Input' and 'Function Output' names can be edited through node body

* Fixes:
    * Fixed indentation issue on some templates generated code
    * Fixed issue in texture property that would forget it's auto cast type on load
    * Fixed issue on 'Triplanar Sample' node being initialized with incorrect internal tiling value
    * Fixed issue on screen position for 'Dither' node
        * Now it changes the screen position interpolator globally to make it work on the shadow caster

v1.3.8 dev 01 ( the same as v1.3.7 dev 07 but bumped version for Asset Store release ):
* Improvements:
    * Added Shader Function previews
    * Improved node list update on current focused window when renaming a shader function

* Fixes:
    * Fixed issue on tabs node list not being updated with shader function renaming
    * Fixed issue with opacity mask not working correctly in custom lighting mode

v1.3.7 dev 05/06:
* New Shader Functions:
    * Half Lambert Term
    * Blinn-Phong Light

* Fixes:
    * Fixed issue with setting the proper canvas mode when load the shader or the editor window on hotcode reload
    * Fixed issue with shader function titles not supporting hyphen characters
    * Fixed issue on not refreshing shader function include files on load
    * Fixed issue on shader function tab name not being renamed when its file is renamed from the editor
    * Fixed 'Texture Sampler' node preview when in reference mode
    * Fixed stack overflow crash with pasting 'Commentary' nodes

* Improvements:
    * Premultiplied options now multiply RGB values with Alpha when in custom lighting mode
    * Shader Functions are now loaded by guid and fallback to name search method if load fails
    * Added custom categories for shader functions
        * Recompiled existing shader functions to account for new categories
    * Improved 'Triplanar Sample' node texture array support 
        * Now allows different index for each texture when doing triplanar in cylindrical mode
    * Area from picking inputs from connections now only take the port icon into account and only include the label when dropping the connection
    * Added preview for 'Static Switch' node

v1.3.7 dev 04:
* Fixes:
    * Fixes issue on Shader Function includes

v1.3.7 dev 03:
* Fixes:
    * Fixed issue on custom lighting nodes not compiling correctly when inside a shader function
    * Fixed dithermask being declared when not in use
    * Fixed texture array support with 'Triplanar Sample' node generating index code inside the function instead of outside of it
    * Fixed issue with 'Append' node preview

* Improvements:
    * Replacing '\' with '/' instead of removing it when writing Additional Include path names
    * Added Additional Include list into shader functions

v1.3.7 dev 02:
* Fixes:
    * Fixed issue with 'Texture Coordinates' node not generating local variables correctly
    * Fixed issue with Refraction port not correctly working with Tessellation
    * Fixed issue on applying vertex offset in certain Templates
    * Fixed cast and per channel operation issues on remaining blends on 'Blend Ops' node
    * Fixed issue on Soft Light Blend Op on 'Blend Ops' node
    * Fixed issue of Shader Function nodes not propagating data when generating code
    * Fixed focus issues when adding new items on Additional Includes, Pragmas, Sub-Shaders Tags and 'Custom Expression' node tools

v1.3.7 dev 01:
* Fixes:
    * Fixed 'Fmod' node issue with Int type connections
    * Small fix to the path button in the new Texture Array Creator tool

* Improvements:
    * Adding fallback when searching template by guid fails

v1.3.6 dev 01:
* Fixes:
    * Fixed error when setting previews for texture related nodes

* Improvements:
    * Improved cubemap support into texture related nodes
    * Removing Texture 0-3 and Scale Matrix options from 'Common Transform Matrices' node
    * Major rewrite for 'Triplanar Sample' node to make it easy to extend on the future
        * Small performance increase to 'Triplanar Sample' node

* Deprecated nodes:
    * 'Texture 0 Matrix'
    * 'Texture 1 Matrix'
    * 'Texture 2 Matrix'
    * 'Texture 3 Matrix'
    * 'Scale Matrix'

v1.3.5 dev 02:
* New Tool:
    * Added Texture Array Creator tool 
        * Available at Window > Amplify Shader Editor > Texture Array Creator

* Fixes:
    * Fixed Undo not being able to recover some nodes

* Improvements:
    * Changed Custom Material Inspector to be able to set and show custom meshes in it's Preview
    * Template Data nodes now expand individual channels if data selected is from vector/color type
    * Expanded individual channels ports on:
        * 'Object Space Light Dir'
        *'World Space Light Dir'
        * 'World Space Camera Pos'
        * 'Position From Transform'
        * 'Vector From Matrix'

v1.3.5 dev 01:
* New Shader Functions:
    * Blinn-Phong Half Vector

* Fixes:
    * Fixed issue on 'Toggle Switch' node not being correctly registered when created

v1.3.4 dev 02:
* Fixes:
    * Fixed issue on text fields in nodes picking up values from other text fields in the editor
    * Fixed cast issue on 'Flipbook UV Animation' node
    * Fixed issue on creating sampler wire nodes
    * Small fix for resize buttons of 'Comment' nodes
    * Small fix to focus and select search text in the context menu not happening in specific situations
    * Fixed issue with 'Object To View Pos' local variable
    * Fixed 'Triplanar Sample' node normal mode signs in certain situations
    * Fixed issue on adding the same grab pass declaration multiple times
    * Fixed issue on incorrectly getting separate channels from transform nodes after local variables are created
    * Fixed issue on duplicate uniforms with some templates
    * Fixed issue where shader function properties were resetting  after every save

* Improvements:
    * Locking blend type nodes from sampler and matrix type connections
    * Expanded 'Object To View Pos' vector output vector into individual ports
    * Changed 'Triplanar Sample' node base UV direction to match unity terrain
    * Expanded vector ports for 'Object Space View Dir' and 'World Space View Dir'
    * 'Lerp' node now converts int types in the alpha input port to float types to prevent errors

v1.3.4 dev 01
* Fixes:
    * Fixed issue with Opacity Mask port incorrect type
    * Fixed issue with incorrectly saving/loading multilines state in editor prefs
    * Fixed issue on not being able to create relays on texture type connections

v1.3.3 dev 01:
* Fixes:
    * Fixed multiple issues with 'Append' node behavior
    * Fixed null pointer exception when Shift + Tabbing helper window
    * Fixed duplication error on UV generation code

* Improvements:
    * Added Alpha To Coverage option ( tied to Opacity port being active ) 
    * Opacity Mask now only generates code if connected
    * Blend ops defaults are now OFF instead of ADD
    * Blend ops now pick the respective refined options when a specific blend mode is selected to make it easy to switch between them and the custom option ( opaque doesn't change anything )

v1.3.2 dev 04/05:
* Fixes:
    * Fixed Asset Post Processor issue with Templates renaming
    * Fixed middle clicking on reference preview focusing on referenced node
    * Fixed graphic glitch on 'Static Switch' node button
    * Fixed issue with copy/pasting nodes taking incorrect property names
    * Fixed issue on 'Toggle Switch' incorrectly being read from older ASE versions
    * Minor fix on 'Object To Clip Pos' node
    * Fixed Undo issue with pasted property nodes
    * Fixed issue with duplicate local variables on templates when having multiple ports from the same category

* Improvements:
    * Minor visual tweak on 'Standard Surface Light' node
    * Removed dependencies on custom shader inspector over the main ASE window so it can be removed by users
    * Setting first vector port automatically invisible ( if unconnected ) on nodes representing Unity built-in parameters ( since they are never used as vectors )
    * Improved custom template reader behavior
    * Minor improvement on Undo node paste behavior
    * Added Stencil Buffer Back and Front face options ( only visible when Culling is Off )
    * Changed input ports number of connections and data type visualization to represent the data being transferred in each cast
    * Improved crooked lines when nodes are too close to each other
    * Added custom single line texture properties for when the texture is marked to not have scale and offset properties

v1.3.2 dev 03:
* Fixes:
    * Fixed issue with 'Texture Sampler' node not taking procedural textures correctly into account
    * Matrices can no longer be connected into 'Vertex To Fragment' nodes input port
    * Fixed reordering issues with shader functions when these were updated
    * Fixed rare compile issue where shader function headers were created but no property was present
    * Fixed issue where view direction vector shader code was being generated with different precision types
    * Fixed issue with Matrix nodes being able to choose its type as Property and add Attributes
    * Fixed issue with Matrix nodes initial value not corresponding to internal draw data
    * Fixed issue with Matrix3x3 not working correctly when its type was set as Global
    * Fixed issue on function nodes generating local variables for Sampler data type variables

* Improvements:
    * Activating internal data for tessellation nodes
    * Minor tweak on vertex position data across all templates
    * Preventing 'DDX' and 'DDY' nodes to generate code when in vertex function
    * Removed Sampler data types as valid 'Custom Expression' output valid type

v1.3.2 dev 02:
* New Shader Functions:
    * 'Bidirectional Parallax Mapping' 
        * Mimics iterative Parallax Mapping with reference plane
    * 'Reconstruct World Position From Depth'

* New Samples:
    * Added new Vertex Normal Reconstruction sample

* Fixes:
    * Fixed issue with shader function nodes generating the same code multiple times
    * Fixed issue with function input generating duplicated code
    * Fixed issue with 'Texture Coordinates' node generating code in the vertex function when used inside a shader function
    * Fixed issue on 'Texture Coordinates' node in templates not respecting the size of the Coord Size option
    * Fixed issue on matrix multiplication via the 'Multiply' node not taking correct output type into account
    * Minor fix on Search Bar positioning
    * Recompiled Triplanar sample to be PS4 compatible
    * Recompiled Translucency sample to be PS4 compatible
    * Recompiled Hologram sample to be PS4 compatible
    * Fixed compilation issue with Billboard generated code in PS4
    * Fixed hot code reload issue with 'Template Parameter', 'Template Vertex Data' and 'Template Fragment Data'
    * Fixed issue on 'Fresnel' node not read/writing new normal space option
    * Fixed issue on 'Screen Position' node when used on Templates

* Improvements:
    * Functions inputs now allow the use of node default graph trees supporting complex default operations
    * Particle Alpha Blend template now uses a float4 on its TEXCOORD0 semantic both for vertex and interpolator data
    * Added pragma tag into Default Sprites template
    * Added port failsafe config into 'Texture Sample' node after reading all its internal data
    * Making vector port invisible in Unity Parameters type nodes if not being used
    * 'Component Mask' node only creates local variables if needed
    * Multi-wire colored connections now active by default
    * 'Fresnel' node now have Normal Space option set to Tangent by default
    * Shader functions now display their description on its Inspector window
    * Fixed 'Grab Screen Position' and 'Grab Screen Color' nodes for VR and updated the respective samples accordingly

v1.3.2 dev 01:
* New nodes:
    * 'Camera To World Matrix'
    * 'World To Camera Matrix'

* Fixes:
    * Fixed issues with 'Toggle Switch' node
        * It now properly creates a toggle property and lets user change material in the editor
    * Fixed issue on vertex local variables not being registered correctly on custom lighting
    * Precision selection on 'Grab Screen Color' node is no longer locked when Custom Grab pass is disabled
    * Fixed issue with 'Depth Fade' node  on OpenGL platforms
    * Normalized screen position code now works properly in all platforms
    * Fixed issue with 'Texture Array' node preview
    * Fixed issue with 'Vertex To Fragment' node generating duplicated code

* Improvements:
    * Added toggle button into 'Static Switch' node
    * Improved wire auto-connection to node when its created from the context palette when dragging a wire 
    * Auto screen UVs from 'Grab Screen Color' node now also take Unity Single Pass Stereo into account
    * Improved code generation on screen position related nodes
    * Activating internal port data into 'Toggle Switch' node
    * Updated Simple Blur and Simple noise examples to be fully android compatible
    * Tweaked 'Desaturate' node to prevent issues with PS4
    * Tweaked 'Parallax Occlusion Mapping' node to prevent issues with PS4

v1.3.1 dev 11:
* Fixes:
    * Fixed incorrect UV variable name on Post-Process template
    * Fixed Perforce integration again 
    * Fixed preview on 'Fresnel' node for the new tangent mode
    * Fixed issue with 'Screen Position' subtitle
    * Fixed issue with 'Vertex to Fragment' node on templates

* Improvements:
    * Added two additional nodes to templates,'Template Vertex Data' and 'Template Fragment Data' 
        * These nodes allow direct access to vertex and interpolated fragment data from the template
    * Adding vertex code entry tag into Post-Process template
    * Improved fail-safe behavior on attempt to write vertex code on template with no vertex tag declared
    * Minor tweaks on some nodes port names and order
    * 'Dither' node now has a input port that allows the use of a custom dither pattern 
    * 'Vertex to Fragment' node no longer generates unnecessary code and now acts as a relay if connected to a vertex path

v1.3.1 dev 10:
* Fixes:
    * Fixed cast issues on 'Smoothstep' node

v1.3.1 dev 09:
* Fixes:
    * Multiple fixes on custom shadow caster
    * Fixed issue on Templates Manager being incorrectly destroyed in some situations
    * Fixed issue on Template data not being correctly synced when user changes its source code and returns to ASE
    * Fixed issue where referenced 'Texture Sampler' nodes was not respecting the original property order
    * Fixed issue on 'Grab Screen Color' node not using Unity default grab pass when selected to use it
    * Fixed small issues on multiple examples

* Improvements:
    * Added tangent space normals to 'Fresnel' node and removed the internal normal value from its properties

v1.3.1 dev 08:
* Fixes:
    * Fixed issue on 'Simple Contrast' node
    * Fixed boundaries issues on 'Dither' node

* Improvements:
    * Minor tweak on 'Smoothstep' ports order
    * Added new Color and Intensity ports into 'Light Color' node
    * Minor overall optimizations on node previews
    * Added preview for 'Substance Sample' node
    * Added preview for 'Blend Operations'
    * Added input port for automatic texture dithering into 'Dither' node

v1.3.1 dev 07:
* Fixes:
    * Fixed issue on 'Simple Contrast' node ignoring Value internal data
    * Fixed issue on nodes preview data not being written when its internal data is read from shader
    * Fixed 'Texture Sampler' node to output a Color instead of Vector type 
    * Fixed 'Swizzle' node not detecting changes on its input ports
    * Fixed issue on allowing invalid characters when typing a custom keyword on the 'Static Switch' node

* Improvements:
    * Improved code generated by 'If' node and hides unused internal data
    * Improved 'Rotator' node behavior
    * Improved 'Panner' node behavior
    * Added checkout for version control systems that need it to edit files like perforce.
    * Changed labels and port order for various nodes in the Image Effects category to improve consistency

v1.3.1 dev 06:
* Fixes:
    * Fixed issue on template output node attempting to access template data before its initialization is complete
    * Fixed issue with validate/execute commands like duplicate on Mac
    * Fixed issue on not updating correctly mouse position when doing multiple pastes/duplicates
    * Fixed disappearing titles when selecting 'Register Local Var' nodes being used by 'Get Local Var' nodes located outside the visible graph area 
    * 'Toggle Switch' node now properly casts its main port type for both input ports

v1.3.1 dev 05:
* Fixes:
    * 'Indirect Specular Light' and 'Indirect Diffuse Light' now compile properly in vertex functions but provide dynamic baking results only
    * Changed mask clip variable name to be compatible with internal unity functions
    * Fixed issue with texture arrays derivatives not being declared in 'Parallax Occlusion Mapping' nodes
    * Fixed 'Texture Sample' node not changing cast mode automatically when in reference mode
    * 'Lerp' node now works as the hlsl/cg specification and allows for component based interpolation
    * Fixed issue on template native properties getting lost when hot code reloading

* Improvements:
    * 'Get Local Var' nodes now get highlighted in green when their referenced 'Register Local Var' node are selected
        * It should be now easier to spot how many and which nodes use a determined Register node, we intend to expand this idea to other similar cases
    * 'Static Switch' now allows to use define symbols and material toggle is now optional
    * 'Keyword Switch' is now deprecated (opening the shader in newer versions should replace it by 'Static Switch')
    * 'Static Switch' and 'Grab Screen Color' nodes now show their node name in title but still allows to edit their variable by double clicking
    * Reorganized toolbar buttons for consistency
    * Showing internal value name at the node properties window when selecting a property on the 'Template Parameter' node

v1.3.1 dev 04:

* Fixes:
    * Fixed issue with 'Static Switch' node duplicatnig code
    * 'Static Switch' node now properly allows the use of the same keyword
    * Fixed issue with Int ports generating black previews
    * Fixed issue where 'Custom Standard Lighting' node was generating garbage code when connected multiple times
    * Fixed dynamic baked lightmapping for 'Indirect Diffuse Light' node
    * Default fallback is now only added if shader doesn't use it's own

* Improvements:
    * Changed 'Template Parameter' node to mimic the same look from the equivalent property nodes
    * Changed some labels and warning texts to be more clear on what's going on for texture objects
    * Int port color now uses the same color as float nodes
    * Added ASE custom inspector to the default templates
    * Added support for Texture Arrays with 'Parallax Occlusion Mapping' node

v1.3.1 dev 03:
* New Features:
    * Added custom pragmas support to the main property panel

* Fixes:
    * Fixed issue with texture arrays when in reference mode creating multiple properties
    * Fixed issue of 'Vertex To Frag' node not generating code in certain situations
    * Fixed issue with 'World Reflection' node not generating code correctly in vertex functions
    * Fixed issue of custom shadow caster not using the correct shader model
    * Fixed issue with dynamic port nodes not updating correctly in some occasions
    * Fixed issue of some nodes not properly using the selected precision type
    * Matrix 3x3 port types now display properly in the node property panel and compile correctly

* Improvements:
    * 'World Position' node now forces float precision
    * Some more changes for the nodes subtitles for consistency
    * Minor performance and GC improvements

v1.3.1 dev 02:
* Fixes:
    * Fixed issue with 'World Normal' node not generating it's components values properly in some occasions
    * Fixed issue with some parameters foldouts not displaying correctly and added a new context message for empty foldouts

* Improvements:
    * Changed some subtitles prefixes to be more consistent about what they represent
    * Changed dropdown icon to a less confusing and more intuitive one
    * More editor performance improvements and reduction of GC in various places

v1.3.1 dev 01:
* Fixes:
    * Fixed issue on copy-pasting custom lighting nodes
    * Fixed issue on null pointer reference on preview material when hitting play mode 

* Improvements:
    * Added upper left widgets into several nodes to change important properties directly on node body
    * Added secondary title into several nodes to show its current state directly from node body

v1.3.0 dev 03:
* Improvements:
    * Completely refactored and changed the graph and node rendering to use a semi-MVC model
        * Improves the overall performance in several orders of magnitude
    * Various small visual fixes and improvements
    * Various changes to prevent most memory allocations heavily reducing GC
    * Changed zoom and auto-pan to a smooth version and fixed its auto-boundaries
        * This should make the editor feel more snappy and responsive
    * New object pickers for 'Substance Sampler' and 'Triplanar Sampler'
    * New outline for selected Wire nodes
        * Now is easier to see in all situations
    * Various previews were added,improved or fixed
        * 'Texture Sampler' nodes now properly display default values
    * Tweaked Input Type labels on 'Custom Expression' nodes to match shader variable type names
    * Custom Lighting nodes now show internal data and have additional Normal options

* Fixes:
    * Fixed 'Texture Array' node issue when referencing an un-connected node
    * Fixed UI issue on 'Custom Expression' qualifiers
    * Fixed issue on shader name being overwritten when changing template
    * Fixed issue on copy/cut/paste not being correctly caught by nodes search bar

v1.3.0 dev 02:
* Fixes:
    * Fixed issue with Output Node Opacity Mask port not working with Custom Lighting 
    * Fixed errors with some nodes inside shader function

* Improvements:
    * Improved internal file reader to be more robust in case of trying to load in-existent files
    * Templates Manager can now also be initialized by its post processor in case of an ASE window is not open
    * 'Blend Operations' node show current selected Blend Op on node body
    * Locked Custom Light nodes from being used on Templates

v1.3.0 dev 01:
* New Features:
    * Templates
        * Create new shaders from already existing ones which serves as base/templates

* Fixes:
    * Fixed issue on ports internal data not showing on 'Append' node
    * Fixed infinite loop on 'Texture Coordinates' and 'Texel Size' nodes

v1.2.1 dev 02:
* Fixes:
    * Fixed issues with previews on 'Multiply' node
    * Fixed incorrect tooltip on 'Face' node
    * Fixed issue on 'Standard Surface Light' node where GI wasn't correctly picking normals
    * Fixed 'Texture Sampler' node not correctly generating code when connected to relays or shader functions
    * Fixed issue on 'Texel Size' and 'Texture Coordinate' nodes when referencing nodes not connected to Master Node

* Improvements:
    * 'Grab Screen Color' node now uses Unity default grab texture and allows overriding it like it's previous behavior

v1.2.1 dev 01:
* Improvements:
    * Major refactor on all nodes categories and colors to improve consistency
    * Added subtitle to 'Swizzle' and 'Component Mask' nodes to reflect their options
    * Added configurable background color for 'Commentary' nodes

* Fixes:
    * Fixed issue with lightmaps when using the 'Standard Surface Light' node on Custom Lighting Light Model
    * Fixed issue on overwriting default texture values on 'Texture Sample' node when loading values from material

v1.2.0 dev 02:
* Improvements:
    * Adding Call Mode into 'Custom Expression' node
        * On this mode all code written into the Code area will be directly injected into the shader code without being assigned to a local variable
        * The result written on the output port will be what is directly connected to the first input port ( named In ) which is not taken into account by the code expression. The In/Out pair will act as a simple relay.

v1.2.0 dev 01:
* Fixes:
    * Fixed issue on generating UI exception when sometimes iterating between Search Node Bar results
    * Fixed issue on Output node size increasing infinitely with shader name
    * Fixed issue on incorrect serialization on 'Texture Sampler' node

* Improvements:
    * Texture Object type nodes no longer auto-set the 'Texture Sampler' Normal Map option
        * Normal Map option was renamed to Unpack Normal Map 
        * A warning is shown if a Texture Object marked as normal map is connected to a 'Texture Sampler' node with the Unpack Normal Map options turned off

v1.1.0 dev 13:
* Fixes:
    * Fixed issue with reading incorrect legacy port info into 'Lerp' node
    * Fixed issue on cycling through deleted nodes when using the Nodes Search Bar
    * Fixed issue with incorrectly moving nodes nested into multiple 'Commentary' nodes
    * Fixed issue on Undo not registering internal node movement on 'Commentary' nodes
    * Fixed issue when using 'Virtual Texture Object' node on Vertex ports
    * Fixed issue with incorrectly moving selected nodes while resizing side menus

* Improvements:
    * 'Simple Contrast' node now always store its result on a local variable
    * Greatly improved 'Commentary' node:
        * You now can use box selection inside the node body
        * You now can create Wire nodes by double clicking on a wire inside the node body
        * You now select and drag the node via its header or by pressing anywhere on the node body having the Alt key down
        * You now need to double click the node header to be able to modify its comment directly from there

v1.1.0 dev 12:
* Fixes:
    * Fixed issue with creating legacy code for LOD Cross Fade on Unity v.2017 and above
    * Fixed issue on 'Lerp' node not adjusting correctly when disconnecting input ports
    * Fixing issue with node drag with snap 
        * Now done by having both Ctrl+Shift pressed

* Improvements:
    * 'Blend Operations' node now automatically adapts to input ports
    * Improving Search Bar focus behavior

v1.1.0 dev 11:
* Fixes:
    * Fixed incorrect behavior on creating connections through Alt + Shift
    * Fixed out of bounds exception caused by removing ports on shader functions
    * Fixed issue with 'Triplanar Sampler' node not deleting correctly in some occasions
    * Fixed ordering issues with Stencil Buffer example

* Improvements:
    * Overall improvements on nodes descriptions

v1.1.0 dev 10:
* Fixes:
    * Fixed new Billboard Ignore Rotation option incorrectly ignoring game object translation

v1.1.0 dev 09:
* Fixes:
    * Fixed issue with deprecated nodes warning message throwing an exception on recent Unity versions
    * Fixed 'Texel Size' node issues on Shader Functions

v1.1.0 dev 08:
* New Features:
    * New 'Keyword Switch' node

* Improvements:
    * Improved 'Lerp' and 'Clamp' nodes behavior
    * Added new improved dynamic 'Append' node which adapts to inputs and deprecated the old one
    * Billboards can now ignore object original rotation via its new Ignore Rotation toggle
    * New Soft Light option was added to 'Blend Operations' node

v1.1.0 dev 07:
* New Features:
    * Added support for Custom Subshader Tags on Output Node properties

* Fixes:
    * Fixed issue with having 'Custom Expression' nodes with similar port names

* Improvements
    * Small improvements on canvas zoom behavior 

v1.1.0 dev 06:
* New Features:
    * Added new nodes:
        * 'World Transform Params'
        * 'Vertex Bitangent'
        * 'Vertex Tangent Sign'

* Improvements:
    * Able to specify an HDR color on 'Color' node if the HDR attribute is set
    * Added previews to nodes:
        * Time
        * Object Scale
    * Improved how vertex data is being generated to prevent future issues

* Fixes:
    * Fixed incorrect order of instruction write on 'Texture Coordinates' node

v1.1.0 dev 05:
* New Features:
    * New 'Standard Surface Light' node ( exclusive to Custom Lighting Light Model )

* New Samples:
    * Double Layer Custom Surface

* Fixes:
    * Fixed issue with pasting nodes not refreshing external references from original ones
    * Fixed issue with generating helper local variable ids on several nodes which may lead to issues on shader functions
    * Fixed issue on 'Depth Fade' node
    * Fixed issue with not registering sampler dummies correctly when using 'Texture Coordinates' node with Tessellation
    * Fixed issue on multi-tabs with breaking all tabs except the focused one when dragging wires

* Improvements:
    * Added previews to nodes:
        *'World Space Camera Pos'
        *'Object Space Light Dir'
        *'World Space Light Dir'
        *'Light Color'
        *'Object To World'
        *'World To Object'

v1.1.0 dev 04:
* Fixes:
    * Fixed issue on 'Texture Coordinates' node generating wrong dummies on UV Sets different than 1
    * Fixed issue on 'Register Local Var' node usage with shader functions

* Improvements:
    * Setting Enable Instancing option default value to false
    * Adding Exact Conversion option into 'Gamma To Linear' and 'Linear To Gamma' nodes for more accurate results

v1.1.0 dev 03:
* Fixes:
    * Fixed issue on 'Pi' node
    * Fixed issue on 'Texture Coordinates' node not generating unique names when used on vertex body
    * Fixed issue with incorrectly counting amount of 'Virtual Texture Object' nodes on graphs
    * Fixed issue on 'Texture Array' drawers
    * Fixed issue on 'Remap' node preview preventing division by zero

* Improvements:
    * 'Texture Array' node:
        * Now work with shader functions 
        * Added derivative option to 'Texture Array' node
        * Minor tweak on tooltip text

* New Features:
    * New Community Node 'GlobalArray' submitted by Vincent van Brummen and created by Johann van Berkel
    * Added new Enable Instancing toggle into Rendering Options to be able to activate instancing without having to use Property nodes

v1.1.0 dev 02:
* Improvements:
    * 'Vertex TexCoord' and 'Swizzle' node types can be selected from node body

* Fixes:
    * Fixed issue with 'Grab Screen Color', 'Get Local Var' and 'Texture Sample' nodes loosing references inside Shader Functions
    * Fixed issue on not correctly registering all Grab Passes from multiple 'Grab Screen Color' nodes
    * Fixed small issue on 'Commentary' node not being able to focus on comment text field when created

v1.1.0 dev 01:
* Fixes:
    * Fixed issue with being able to open recently created shader multiple times

* Improvements:
    * Added preview for 'Screen Position' node
    * 'Append' output type can be selected from node body
    * Small overall optimizations

v1.0.0 dev 12:
* Fixes:
    * Fixed wrong casting issues on dynamic type nodes
    * Fixed lost reference when deleting 'Grab Screen Color' node

v1.0.0 dev 11:
* New Features:
    * Additional includes (.cginc) can now be used into an ASE shader via the Additional Includes sections on the Output node
        * Their contents can be accessed via the 'Custom Expression' node
    * Added Node Search bar to quickly find nodes on the canvas
        * Ctrl + F: Shows Search Bar
        * Enter/Return/F3: Goes to next occurrence
        * Shift + (Enter/Return/F3): Goes to previous occurrence
        * Escape: Hides Search Bar

* New samples:
    * UV Light Reveal 

* Fixes:
    * Fixed issue on creating unnecessary casts from floats
    * Fixed minor issue on GPU Instancing sample
    * Fixed minor UI issues on 'Reflect' and 'Refract' nodes
    * Fixing shader paths for Community Shaders
    * Fixed issue on incorrect cast when using Floats and Ints in certain nodes
    * Fixed issue on resetting in certain situations vertex local variables generated during Output Node fragment code generation
    * Fixed issue on property name update in 'Grab Screen Color' node

* Improvements:
    * Improved nodes local variables reset behavior to prevent future issues
    * Added previews to 'Gamma To Linear' and 'Linear To Gamma' nodes
    * Forcing 'Dot' and 'Normalize' nodes to store results in local value and prevent with power operations

v1.0.0 dev 10:
* Fixes:
    * Fixed issue with temporary variable assignment Id on 'Texture Coordinates' node

v1.0.0 dev 09:
* New Features:
    * New 'Static Switch' node which allows creation of shader variants

* Fixes:
    * Fixed minor issue on reading inputs from dynamic 'Add' and 'Multiply' nodes on older shader versions
    * Fixed issue on Parent Graph attempting to delete in-existent connections
    * Fixed issue with always disabling Light Maps when using Tessellation
    * Fixed issue with Texture Nodes reference drop down selector showing incorrect labels both on 'Texture Sampler' and 'Texture Coordinates' nodes
    * Fixed issues on incorrect loosing references with 'Texture Sampler' node on Reference mode

* Improvements:
    * Improved 'Append' node connection management
    * Added Local Var selector directly on 'Get Local Var' node body

v1.0.0 dev 08:
* New Features:
    * New Output node Rendering Options
        * Disable Batching
        * Ignore Projector
        * Force No Shadow Casting

* Fixes:
    * Fixed issue with new dynamic 'Add' and 'Multiply' nodes not registering port creation/destruction into Undo system
    * Fixed issue on 'Grab Screen Color' node duplicating code
    * Fixed issue with Opacity Mask port being incorrectly Enabled/Disabled on certain situations
    * Fixed issue on 'Get Local Var' nodes getting wrong ids on certain situations when a 'Register Local Var' node is deleted
    * Small fix to force property name update when changing type on property nodes
    * Fixed issue where 'View Dir' node was generating code in the wrong space when in vertex function for both world space and tangent space
    
v1.0.0 dev 07:
* Fixes:
    * Fixed name conflict on 'Custom Expression' node
    * Fixed issue for both normal input in indirect lighting nodes that were asking from normal in world space instead of tangent space

v1.0.0 dev 06:
* New Features:
    * Added LOD Cross Fade support for LOD groups ( located in the Output node Rendering Options tab )

* Improvements:
    * 'Add' and 'Multiply' nodes can have more than 2 input ports ( max 10 )
    * Minor improvements on several nodes
    * Refraction port use Unity's grabpass by default so it can pick other refraction materials
    * Avoiding possible compiler misunderstandings with System.Type calls
    * Ensuring variables/functions created by custom expressions have unique names
    * Auto enabling instance mode on material ( if on Unity 5.6 or above ) when detecting instance mode on ASE shader
    * Improved zoom behavior

* Fixes:
    * Fixed issue when remapping ports from very old shaders
    * Fixed swizzle issue on 'Vertex Position' node
    * Fixed matrix 'Invert' node
    * Fixed SimpleGPUInstancing sample not fully batching on Unity 5.6
    * Fixed opening a SF in more than one tab after creation
    * Fixed header click to edit name when zoomed out
    * Fixed both Commentary node side menus resize not following the mouse movement correctly

* New Shader Functions: ( AmplifyShaderEditor/Examples/Assets/ShaderFunctions )
    * Simple HUE
    * SphereMask

v1.0.0 dev 05:
* Fixes:
    * Fixed issue with conditional 'If' node
    * Fixed issue with 'Vertex Position' node swizzle in Local Vertex Ports

* Improvements:
    * Caching instanced property nodes into local variables to prevent multiple UNITY_ACCESS_INSTANCED_PROP() on them
    * Added support for samplers types into 'Custom Expression' node

v1.0.0 dev 04:
* Fixes:
    * Fixed node drag and drop issue from palette
    * Fixed issue with online reference button having a "too-large" click box
    * Palette Menus now display the correct cursor on mouse hover
    * Fixed clicking Enter on palette without selecting a node
    * Changing lighting models should now show the error messages correctly
    * Fixed issue of Custom Light nodes not loading properly

* Improvements:
    * Added Per Renderer Data tag to Properties available Attributes
    * Adding help box into 'Virtual Texture Object' with additional info

v1.0.0 dev 03:
* New Features:
    * Custom Lighting 
        * New Nodes: ( can only be used on this light model )
            * Indirect Diffuse Light
            * Indirect Specular Light
            * Light Attenuation

* New Samples:
    * Custom Lighting Toon

* Fixes:
    * Fixed issue when zooming with Alt + Right Mouse button
    * Fixed issue with window not detecting graph type on Unity load
    * Fixed issue on 'Debug Switch' node not loading properly
    * Fixed issue on assigning invalid cultures when an error/exception occurred inside ASE

* Improvements:
    * Context Palettes now allow  Tab / Shift Tab to select between nodes instead of mouse selection (confirms with Return/Enter key)
    * Added previews for 'Debug Switch' and 'Toggle Switch' nodes
    * Added link to node documentation on its tooltip
    * Small optimization on all nodes overall
    * Preventing ASE to crash if some faulty class/dll is present on the project

v1.0.0 dev 02:
* Fixes:
    * Preventing shadow caster error on using 'Vertex TexCoord' with 'Vertex Normal'

v1.0.0 dev 01:
* Fixes:
    * Fixed issue on not recognizing Tessellation port correctly when at Lambert or BlinnPhong light models
    * Fixed issue on dragging nodes via Alt mode not respecting ports unique ids when creating connections
    * Fixed minor typo on 'Switch by Face' node
    * Fixed minor issue when loading LoadPolyWater example

v0.7.2 dev 08:
* Fixes:
    * Fixed issue generating input ports instructions on 'Custom Expression' node

v0.7.2 dev 07:
* New Features:
    * Added support for multiple ASE windows opened simultaneously

* New Samples:
    * Animated Fire with Shader Functions

* Improvements:
    * Forcing InvariantCulture on ASE execution cycle to prevent issues with number parsing
    * 'Texture Sampler' node no longer shows it's sampler properties when a 'Texture Object' node  is connected to it
    * Improved redundancy awareness on 'Virtual Texture Object' and 'Texture Sampler' nodes
    * Improved 'Virtual Texture Object' tooltip
    * Removed Return button from Shader Functions since it is now useless with new multi-tab behavior

* Fixes:
    * Fixed issue on changing Normal map option in 'Texture Sampler' node not changing its output type
    * Changed 'Virtual Texture Object' node channel name to 'Layer' and fixed its default value not showing up correctly
    * Virtual textures now generate properties with their correct name (requires user changes to the virtual texture itself)
    * Fixed issue that break compiling when a missing shader function was present
    * Forcing internal data update for shader function Output nodes to prevent errors when they are disconnected
    * Fixed small issue with shader function nodes being stuck on selection when double clicking on them

v0.7.2 dev 06:
* New Features:
    * Added 'HSV To RGB' and 'RGB To HSV' nodes

* Improvements:
    * 'Custom Expression' node with a return instruction on its Code text area generates a function with the code contents thus enabling multiple instructions lines on its body
        * Added small info text on node properties to explain its behavior
        * Added new name field ( can also be edited directly on node by double clicking on it ) which is used to name the generated function/ local variable
    * Small refactoring on some classes for consistency and warning removal from Visual Studio

* Fixes:
    * Fixed issue on some changes not being correctly caught on setting Blend Render Type
    * Fixed issue with Unlit Light model doubling the value set on the Emission output port
    * Small fix on title updates when using Shader Functions
    * Removed warning from unused legacy source code on 'Register Local Var' and 'Get Local Var' nodes
    * Fixed issues on incorrect  casts on 'Texture Sampler' node
    * Fixed issues on incorrectly snapping wires into hidden ports

v0.7.2 dev 05:
* Improvements:
    * Changed tool tip display to trigger when mouse is on top of the node ( now displays below the node)
    * Shader Functions 
        * Added default values for input node in SF (these are used when there's no connection) 
        * Added port restrictions to dynamic node types
        * Changed way input node work with restrictions when changing type to prevent invalid connections

* Fixes:
    * Fixed minor typo on 'Rotator' node

v0.7.2 dev 04:
* Fixes:
    * Fixed multiple issues on save behavior when changing modes
    * Fixed issue with shader functions not assigning the main node correctly
    * Fixed issue on Project Window Change callback
    * Fixed graph count increasing on shader switch
    * Fixed version numbering in function nodes
    * Fixed nested SF issue with inputs

v0.7.2 dev 03:
* Fixes:
    * Fixed issue with 'Vertex TexCoord' not writing properties correctly into shader meta

v0.7.2 dev 02:
* Fixes:
    * Fixed cast and port activation issues on Blend Nodes
    * Fixed various issues with SF:
        * Saving no longer deselects
        * Reordering is now working properly
        * Autocast now has port restrictions into account and deletes with warning when possible
        * Sampler types no longer duplicate

* Improvements:
    * Texture Objects node family  can now be set as Globals

v0.7.2 dev 01:
* New Features:
    * Added Shader Functions
    * Added new 'Object Scale' node

* Fixes:
    * Fixed multiple issues with Copy/Paste
    * Fixed issues with nodes on Vertex Function
        * 'Fresnel'
        * 'Posterize'
        * 'Heightmap Texture Blend'
        * 'Unpack Scale Normal'
    * Fixed issue with incorrect data read from 'Texture Coordinates' nodes on versions below 0.5.0 dev 003
    * Fixed issue on inverted Receive Shadows toggle

* Improvements:
    * Can Copy/Paste between different Shaders and Shader Functions 
    * Shader properties created by Refraction, Translucency, Mask Value and Tessellation now appear on the Output node Material Properties list and can be reordered
    * Preventing UndoParentNode to generate DefaultValue conflicts caused by other plugins
    * Removed warnings generated on some situations by the 'Screen Position' node

* New Samples:
    * LowPolyWater by The Four Headed Cat
    * ForceShield by The Four Headed Cat

v0.7.1 dev 02:
* Improvements:
    * Improved 'Texture Coordinates' node and added new Tex input port into it
    * Improved local variable usage on several node generated code to improve overall shader instruction count
    * 'Vertex Position' node now has new Size property

* Fixes:
    * Fixed issues on 'Vertex to Fragment' node
    * Fixed issue on loading an ASE shader with its window already opened but tabbed and not visible during play mode
    * Fixed multiple issues with 'Grab Screen Position' node usage on Vertex function
    * Fixed issue with Forward Shadows not being correctly written when Custom Shadow Caster was active
    * Fixed issues with Blend nodes usage on Vertex function
    * Fixed issues with 'Dithering' node usage on vertex function and when Tessellation is active
    * Fixed issues with 'Screen Depth' node usage on vertex function and when Tessellation is active
    * Dithering sample now works while Tessellation is active

v0.7.1 dev 01:
* New Features:
    * Alt + Node Drag to Auto-(dis)connect node on existing wire connection

* Improvements:
    * Added new Tex Input Port into 'Texel Size' node
    * Optimized nodes list usage on palettes (API)
    * Improved retro-compatibility handling with adding new ports on already existing nodes (API)

* Fixes:
    * Fixed issue on horizontal scroll bar not appearing on Helper Window

v0.7.0 dev 03:
* New Features:
    * Added 'Face' node
    * Added 'Switch by Face' node

* Fixes:
    * Fixed issue on not setting shader version on graph when creating a new empty one which lead to copy/paste issues
    * Fixed wrong port type assignment and incorrect conditional operator usage on community 'Compare ...' nodes
    * Fixed issue with creating a material from a shader already with properties in Unity 5.6
    * Fixed multiple UI issues on Retina MacBook

* New Samples:
    * Highlight Animated by The Four Headed Cat
    * 2 Sided by The Four Headed Cat
    * Two Sided with Face

v0.7.0 dev 02:
* Improvements:
    * Improved Float to Vector auto-cast
    * Double-clicking on a 'Get Local Var' node focus on its referenced 'Register Local Var' node

* Fixes:
    * Fixed issue with keyboard shortcuts on Mac
    * Fixed renaming issues with 'Triplanar Sampler' node
    * Fixed issue on property nodes UI not refreshing on Undo
    * Fixed issues on 'Fresnel' and 'Vertex Normal' related with normal generation
    * Fixed typos on POM
    * Fixed issue with Wire node deletion
    * Fixed auto-change port types issues on all Compare nodes

v0.7.0 dev 01:
* Improvements:
    * Greatly improved Undo
    * Colored Port Mode behaves as a normal toggle and doesn't require double tap on W key

* New Samples:
    * Hologram by The Four Headed Cat

* Fixes:
    * Fixed issue on deleting nodes with Wire nodes on their connections
   
v0.6.1 dev 05:
* Fixes:
    * Fixed issue with custom Shadow Caster on Vulkan

v0.6.1 dev 04:
* Improvement:
    * Renaming 'Texture Sampler' Type property Instance to Reference and prevent confusion with GPU Instanced properties

* Fixes:
    * Fixed issue on unnecessary saves on Live mode 
        * Also increased Inactivity time from 0.5s to 1s
    * Fixed issues on some node interactions not being detected by live mode ( and thus not being flagged to save )
    * Fixed issue on 'Rotator' node not correctly generating local values according to vertex/frag
    * Fixed issue on 'Texture Coordinates' node when defining its Inputs with Tessellation active
    * Fixed issue with custom Shadow Caster on Metal IOs
    * Fixed small typo on Tessellation Shader Model warning message

v0.6.1 dev 03:
* New Features:
    * Adding Fallback shader picker on Master Node
    * Adding Shader LOD value modifier on Master Node

* Improvements:
    * Node property title changes according to selected node
    * Added Multi-Line mode to wires ( Ctrl + W ) 
    * Added ability to change 'Triplanar Sampler' node name 
    * Improved wire connections rendering while zoomed
    * Tweaked live mode to save only when user is inactive for 0.5s

* Fixes:
    * Small node resizing issues fixed
    * Fixed issues on Live mode not catching node connections and creation correctly

v0.6.1 dev 02:
* New Features:
    * Added 'Triplanar Sampler' node
    * Added Vertex Output ( can now change from Relative/Local Vertex Offset to Absolute/Local Vertex Position )
    * Added Smear Sample
    * Added Unlit Light Model
    * Added simpler 'Time' node 
    * Added 'Depth Fade' node
    * Added 'Camera Depth' Fade node

* Improvements:
    * Adding node info into Helper Window
    * Adding drag and drag valid unity assets list to helper window
    * 'Screen Position' and 'Grab Screen Position' now have a Output dropdown on its properties instead of a Toggle 
    * Improved GPU instancing example by adding a C# illustrating how to set instanced properties

* Fixes:
    * Fixed issue on preview materials not being initialized after returning from play mode
    * Fixed issue on local variables reset
    * Fixed issue with tangent and bitangent previews
    * Fixed billboard issue with non-uniform scaling
    * Fixed issue on Tex ports counting as having valid internal data on Node Properties UI
    * Fixed issue on using 'Texture Sampler' or 'Screen Color instances on Master Node Debug port

v0.6.1 dev 01:
* Improvements:
    * Activating internal data into 'Object To World' and 'World To Object' nodes and setting it to (0,0,0,1) by default
    * 'Texture Array' nodes can be created by dragging a Texture 2D Array object into ASE canvas
* Fixes:
    * Fixed issue on 'Texture Array' node when connecting it to Vertex Ports
    * Fixed issue on 'Vertex TexCoord' not generating correct source according to their properties
    * Fixed issues on MourEnvironment, SandPOM and WaterSample shaders

v0.6.0 dev 01:
* Improvements:
    * Added Texture Coordinate Coord Size parameter for 'Vertex TexCoord' node
* Fixes:
    * Fixed issue when doing custom shadow caster with translucency on deferred mode
    * Fix for texture coordinates zeroing out Z and W
    * Fixed issue with input port internal name not being set correctly
    * Fixed issue with custom shader inspector on unity 5.6
    * Fixed shadows issue on Matcap example
    * Fixed 'Virtual Texture Object' sampling the correct UVs when not connected

v0.5.1 dev 012:
* Fixed issue with LightColor node not generating the proper values
* Fixed issue when doing custom shadow caster with translucency on deferred mode
* Made the code generation compiler friendly because of unity 5.5 and up changes

v0.5.1 dev 011:
* Adding new Billboard option into Master Node
* Control key can be also used to append nodes to selection
* Fixed issue with not updating material inspector in real time ( because out of focus ) when changing properties on canvas
* Fixed cast issues on object picker with 'Texture Sample' and 'Texture Object' nodes
* Added Mask buttons on Previews
* Improved overall editor performance

v0.5.1 dev 010:
* Added 'Desaturate' node
* Fixed small visual issue with Color Mask UI 
* Improved overall UI performance

v0.5.1 dev 009:
* Linking both Up/Down keys and right mouse dragging to scroll behavior into menus
* Canvas zoom can be changed by right mouse dragging while pressing Alt key
* Fixing multiple issues with 'Swizzle' node
* Heavily optimized drawing the node lines 
* Fixed issue with loading default shaders to ports

v0.5.1 dev 008:
* Fixed issues when using line feed on 'Custom Expression' node code area
* Fixed wires and previews displaying on top of the title bar
* Fixed order issues on 'Commentary' node
* Fixed issue with BurnEffect sample
* Majorly improved Previews update speed
* Added LOD levels to previews ( sampler and texture arrays ) 
* Added many more node previews 
* Updated TriplanarProjection and ParallaxMappingIterations samples
* Optimization on drawing wires
* 'World Normal', 'World Reflection' and 'Fresnel' input ports now modify their previews
* Improved Nodes Graph internal ordering to correctly create connections on shader load

v0.5.1 dev 007:
* Fixed issues with 'Texture Coordinates' node usage with Tessellation
* Fixed swizzling issues on 'View Dir' node

v0.5.1 dev 006:
* Added new Helper Window accessible via the right most button on the graph window
* (De)Activating Tessellation and Outlines forces shader to save
* Expanded the amount of nodes with available preview
* Added fail safe to continue loading shader if in-existing community nodes are detected 
* Added Normal Map unpacking to 'Texture Array' node and updated its sample
* Fixed issues on Debug Port usage
* Fixed issues on 'Flipbook UV Animation' when property nodes are connected to rows and column input ports
* Fixed issues on Not configuring 'Texture Array' node ports after read
* Fixed issues on Major fix on register/get local var mechanics
* Fixed issues on Adding a space on the node palette search when opening it via space bar
* Fixed issue on ignoring color masks setup on certain situations
* Forcing default values on input port internal data if an exception is caught

v0.5.1 dev 005:
* Added new Curvature Parameter for 'Parallax Occlusion Mapping' Node
* Added 'World To Object' node
* Added 'Object To World' node
* Fixed issue on 'World Normal' node
* Fixed issue on 'World Tangent' node
* Fixed issue on 'World Bitangent' node
* Fixed issue on 'World Reflection' node
* Fixed issue on 'Register Local Var' node
* Fixed issue with Tessellation used with Custom Shadow Caster
* Fixed issue with Mip Level not being used with 'Texture Sampler' nodes on Vertex Ports
* Fixed issues with Master Node Debug port usage

v0.5.0 dev 005:
* Applied overall UI changes from Master Node into all other nodes
* Added Node Previews
* Added new Frame Title parameter on 'Commentary' nodes
    * Auto focus on new Frame Title textfield when node is created
* Added new Soap Bubble sample using both Reflection and Refraction
* Fixed issue with 'Custom Expression' node
* Fixed issues on 'Scale' node
* Fixed issues on 'Panner' node
* Fixed issues on 'View Dir' node
* Fixed issues on 'Substance Sample' node
* Fixed Repaint issues on ASE custom material editor
* Fixed issue with texture defaults not being correctly written on shader meta
* Fixed issue on reading alpha:fade option from older versions
* Tweaked 'Component Mask' node
* Tweaked 'Pi' node
* Improved 'Substance Sample' node previewer 
* Refraction to now have Specularity into account
* Removed warnings on importing ASE to Unity v5.6.0 

v0.5.0 dev 004:
* Added new Outline option on Master Node properties
* Tweaked Tessellation material update

v0.5.0 dev 003:
* Fixed issue with Live Mode load/save state 
* Fixed repaint issue with picking ports
* Tweaked 'Substance Sample' node preview
* Adding Toggle Attribute to 'Switch Toggle' node

v0.5.0 dev 002:
* 'Texture Coordinate' now support float3 or float4 output types 
* Changed Colored ports saving mechanism 
* Tweaked how ports are saved/loaded via shader meta to easily modify existing nodes port amount without breaking older versions (API)
* Tweaked Simple Noise example
* Tweaked Read Atlas examples
* Tweaked Translucency example
* Minor tweak on 'Texture Sample' node
* Fixed issue with local variable declaration on Master Node Debug port 
* Fixed issue with Screen Space Curvature example
* Fixed issue with reading fade parameter on master node 
* Fixed issue with Transparency shader

v0.5.0 dev 001:
* Fixed issues with 'Texture Array' node
* Fixed issues with 'Texture Coordinates' node
* Fixed issues with Tessellation example on MacOs
* Fixed issues on multiple examples with Unity beta version 6 and above
* Added new 'Substance Sample' Node
* Added example using the new 'Substance Sample' node located at Examples/Official/Substance
* Added Attributes to Property Nodes
* Added Conditional 'If' Node with Dynamic Branching option
* Tweaked 'Flipbook UV Animation' node

v0.4.1 dev 002:
* Fixed issue 'Flipbook UV Animation' node not resetting properly and added a start frame parameter

v0.4.1 dev 001:
* Fixed issue with Texture Array sample

v0.4.0 dev 003:
* Default Alpha mode set to Transparent and not Alpha Pre-Multiply
* Minor tweak on node sorting on palette windows
* Minor tweak on Master Node Property UI
* Added new Rendering Options foldout on Master Node properties
* Added check to prevent division by zero warning with 'Grab Screen Position' and 'Screen Position' nodes

v0.4.0 dev 002:
* Forcing LF on all shaders to prevent CRLF mixed with LF when upgrading them  
* Fixed issues with custom shader inspector

v0.4.0 dev 001:
* Minor fix on 'Grab Screen Position' node
* Added new Refraction port into Master Node. Only works correctly with Unity 5.5.1 p1 and above due to an internal unity issue
* Added new Refraction Example ( AmplifyShaderEditor/Official/ObjectNormalRefraction )
* Added new Vertex Normal port into Master Node
* Small update to Material and Shader mode borders
* Parameter types can now be changed from node itself on property nodes via dropdown on its upper left corner 
* Various fixes from the way the Blend Mode works to take new translucent option into account
* Fixed issue with connections from cache when changing Light Mode on Master Node not respecting port availability
* Changed Refracted Shadow demo blend mode
* Fixed Vertex Offset issue with custom shadow caster
* Small fix to auto change blend mode on rendertype and render queue changes
* Fixed some samples with wrong version or wrong connections
* Fixed UI problems in Unity Personal skin

v0.3.2 dev 003:
* Fixed issue with 'Multiply' node
* Fixed issue with 'Divide' node
* Fixed issue with 'Texture Sample' node
* Fixed issue with 'Dot' node
* Tweaked 'Fresnel' node to use Unity's interpolators and made the default values match Schlick Fresnel
* Tweaked 'World Normal' node to prevent multiple normals generation
* Added 'Texture Array' node
* Added 'Linear to Gamma' and 'Gamma to Linear' nodes
* Majorly revamped the UI for the master nodes options
* Revamped Blend Modes and added additional options
* Added pos-load test on nodes invalid connections to prevent issues with older ASE versions 

v0.3.2 dev 001:
* Added custom shadow caster
* Small fix to both emission and alpha on Fade mode
* Fixed minor issues on reading shaders from older versions( < v0.2.0 dev 002 )
* Fixed issues on 'Custom Expression' node
* Fixed issues with 'Grab Screen Position' node
* Property nodes with Parameter Type set to Global doesn't force _ on the parameter internal name

v0.3.1 dev 009:
* Fixed 'Grab Screen Color' node issues
* Minor tweaks on Context menu
* Tweaked 'Screen Position' and 'Grab Screen Position' behavior
* Added switching of input ports connections by holding the CTRL key
* Added removing of input ports connections by double clicking with the left mouse button on them
* Forcing Shader Model to at least 4.6 if Tessellation is active

v0.3.1 dev 008:
* Small fix to 'Virtual Texture Object' node
* Fixed issues on 'Texture Sample' node
* Fixed issue on not correctly unregistering nodes from 'Commentary' nodes when they were deleted 
* Fixed issue when reading old shaders created with v0.2.4 dev 004
* Fixed issue with 'Texture Coordinates' node when using Tessellation
* Fixed issues and tweaked overall normals generation
* Fixed issue on 'Vector From Matrix' node
* Fixed issue on ASE canvas camera incorrectly panning when hitting a tooltip with Middle/Right Mouse Button
* Fixed connection errors with 'Vector From Matrix' node 
* Fixed issue with 'Vertex To Fragment' node
* Deprecated 'Local Position' node 
* Added 'Grab Screen Position' node 
* Tweaks on nodes and ports names to maintain overall consistency
* Added new Scale and Offset option on 'Screen Position' node
* 'Register Local Var' node now also has system to prevent duplicate names

v0.3.1 dev 007:
* Added auto-order option into 'Register Local Var' node
* Added new 'Improved Read From Atlas Tiled' example 
* Added 'Simplified Fmod' node

v0.3.1 dev 006:
* Fixed control argument exception when deleting connection with Alt key on selected node
* Fixed issue with 'Switch Toggle' node

v0.3.1 dev 005
* Side menus are now resizable
* Tweaked 'Weighted Blend' node
* Added 'Summed Blend' node
* Added 'Toggle Switch' node
* Added new 'Scale and Offset' node
* Fixed issues on 'Vertex Binormal World' and '[VS]Vertex Tangent' nodes
* Fixed issues with 'Texture Sample' nodes created via dragging a texture to ASE
* Fixed issue with 'Scale' node 
* Fixed issues on incorrectly reading 'Receive Shadows' parameter from ASE shader previous to v0.2.5

v0.3.1 dev 004
* Fixed issues with accessing 'Texture Coordinates' node when tessellation is active

v0.3.1 dev 003
* Fixed yet another issue with accessing 'Texture Coordinates' node on vertex function
* Reverted shader update mechanism  after save to previous old one until issue reported by Seith is fixed

v0.3.1 dev 002
* Fixed issues with accessing 'Texture Coordinates' node on vertex function

v0.3.1 dev 001
* Improved overall editor UI
* Improved Live Mode
* Nodes can generate shader comments ( API )
* Each port can now have multiple restrictions ( API )
* 'Texture Object' can now only be connected to 'Texture Sample' nodes
* Added 'Switch' toggle node

v0.3.0 dev 005
* Auto adding AMPLIFY_SHADER_EDITOR symbol on current target group when initializing ASE ( API - to be used on external community nodes )
* Added keyboard shortcut [F5] to force available nodes refresh ( API )

v0.3.0 dev 004
* Fixed yet another issue with local variables generation

v0.3.0 dev 003
* Fixed crash from infinite loop generated on port 'Tex' at 'Texture Sample' node
* Fixed cast issue when using internal port data on some nodes
* Fixed issues with local variables generation  
* Tweaked Vertex Displacement port on Master Node
* Added ability to specify range of valid data types for input ports ( API )
* Locked 'Tex' port from 'Texture Sample' node to only allow connections to 'Texture Object' nodes

v0.3.0 dev 002
* Updated POM to clip edges using a tilling parameter 
* Updated the sand POM example and its height texture
* Updated Water sample
* 'Vertex NormalWorld', 'World Position' and 'View Dir' nodes now also output into vertex offset correctly
* All editor resources are loaded via their own guid
* Added Tessellation port into master node to be able to create custom Tessellation behaviors
    * Tessellation parameters ( excluding Phong ) on Master Node Properties will be deactivated if its port is being used   	
    * Created Nodes for each of the builtin Tessellation functions
        * Distance-base Tessellation
        * Edge Length Tessellation
        * Edge Length Tessellation with Cull
* Fixed issues with not creating local variable correctly if graph is shared between vertex and frag ports
* Fixed issue with local variables created on automatic casting not taking port category into account
* Fixed node width issue regarding its header title size

v0.3.0 dev 001
* Fixed issue on where deprecated nodes needed to be available to be replaced by their replacement type
* Fixed issues on all conditional nodes
* Fixed issue on local variable creations on vertex shader
* Fixed issue on 'Commentary' node
* Amplify Texture dependency is dynamically set through asset guid
* Texture Sampler will quietly ignore virtual object if AT is not found in project and will not generate a broken shader
* Deprecated nodes are automatically excluded from the palette
* Updated version in all samples
* Optimized 'Multiply' and 'Divide' nodes 
* Added Edge Length based tessellation
* Added Fixed Amount based tessellation

v0.2.6 dev 001
* Fixed issue on 'Virtual Texture Object' node
* Fixed issue on 'If' node
* Fixed issues in 'Parallax Occlusion Mapping' node
* Fixed issues on 'Texture Sampler' node
* Fixed issue for translucency on point lights
* Fixed issues on 'Texture Coordinates' node
* Fixed issue on not correctly syncing ASE when when hitting paste button on our custom inspector
* Avoiding null pointer exception when compiling a 'Texel Size' node without references 
* Re-Organized ASE folder system and added initial pop-up window to clean old/deprecated data
* Tweaked Default/Material values UI on Property nodes
* Node Properties window can now be show by double clicking a node
* Renamed Uniform parameter type to Global 
* Added Distance-Based tessellation. Can be activated/configured on Master Node properties
    * Added Tessellation sample
* Added emission baking support. Queue must be set to "Geometry" to work properly
* Added Tiled Atlas sample
* Added scenes for each sample
* Added tool tips for Master Node properties 

v0.2.5 dev 004
* Added Parallax Occlusion Mapping node (uses linear search with customizable samples in conjuntion with interval mapping for refinement of sidewalls)
* Added simple snapping behavior when moving nodes (left-control)
* Fixed background grid image sliding when zooming
* Fixed issue with 'Texcoord Vertex Data' on writing
* Fixed issues with default values of 'Virtual Texture Object'
* Fixed issues when drawing 'Matrix3x3' and 'Matrix4x4' nodes
* Fixed compilation error when creating builds

v0.2.5 dev 003
* Fixed issues on 'Texture Coordinate' node
* Fixed issues with 'Texture Sample' node not reading the correct values from older shaders versions
* Fixed issues with instanced texture samples
* Fixed issues with 'Texel Size' node
* Fixed issues with adding new categories on community created nodes 
    * Custom category colors can now also be set up via NodeAttributes(...) 
* Created simpler method ( GeneratePortInstructions(...) ) to generate input instructions. Handy for community members which are creating new custom nodes
* Fixed issue with propagating incorrect port types on master node when loading shader from older versions
* Fixed issues with parallax example
* Alpha channel/Opacity port is forced to 1 if Keep Alpha is set to true and port is not connected to prevent UI issues
* Added index property in '[VS] Vertex TexCoord' node and marked '[VS] Vertex TexCoord1' node as deprecated 
* Tweaked collision area on minimize/maximize buttons on lateral windows
* Small optimization on 'Custom Expression' node
* Added support for virtual texturing via Amplify Texture
* Community Node additions
    * Jason Booth
        * Added 'Vertex To Fragment' node

v0.2.5 dev 002
* Added 'Texture Object' node
* Tweaked 'Texture Sample' node behavior to use the new 'Texture Object' node
* Added Stencil Buffer support
* Added Depth foldout with access to ZWrite, ZTest and Offset configuration
* Added AMPLIFY_SHADER_EDITOR preprocessor directive
* Fixed issue on not resetting instance variables counter on reset/load 
* Overall fixes on node UI and its adaptation when zooming out
 

v0.2.5 dev 001
* Added Color Mask option on Master Node
* Added access to additional UV sets on "Texture Sample" and "Texture Coordinates" nodes
* Fixed issue when attempting to connect a wire to a locked port
* Fixed issue with incorrectly adding '#pragma multi_compile_instancing' on non instanced property shaders
* Minor tweak on palette foldout behavior


v0.2.4 dev 007 
* Fixed issue on wrong auto-snapping wires with invisible, locked ports
* Fixed issue with version control on Master Node
* Added Transmission input port on Master Node
* Added 'Mip Mode' in 'Texture Sample' node 
* Property names can now be changed directly on node by double clicking on it
* Properties can be reordered through drag and drop operations on the Master Node properties via the Available Properties foldout area
* Min/Max values on 'Ranged Float' nodes can be modified directly on canvas

v0.2.4 dev 006 ( for internal reasons we had to skip dev 005 )
* Fixes issues on 'Panner' node
* Fixed issues with not correctly generating local variables according to port category
* Tweaked behavior and fixed issues on the 'Texture Coordinate' node
* Fixed issues on 'Texel Size' node
* Fixed issues on 'Local Vertex Pos' node
* Fixed issues with Burn Effect Sample
* Removed positive number restriction from Master Node 'Queue Index' property
* Custom Material Inspector can be selected/changed on Master Node
    * Done through the Custom Editor property
    * You can always reset to our own by hitting the Reset button next to hit
* Updated Rim Light Sample to use the new Space option on the 'View Dir' node
* Updated Parallax Sample to use the new Space option on the 'View Dir' node
* Added 'Translucency' input port into Master Node 
* Added 'Dithering' node
* Added Matcap Sample
* Added Dithering Sample
* Added Rendering Platforms selector on Master node
* Added Water Sample on a small terrain in the Sample Scene

v0.2.4 dev 004
* Fixed issues with wire shader
* Fixed issues with 'Texture Coordinates' node
* Removed warnings occurring on Unity v5.5
* Fixed issue with 'Append' Node 
* Fixed issue with ASE Custom Material Inspector 
* Tweaked 'Local Vertex Pos' node to output differently if generating code for vertex or fragment ports

v0.2.4 dev 003
* Added Texture Reference dropdown to 'Texture Coordinates' node
* Added Render Path dropdown in Master Node
* Tweaked 'View Dir' node so you're able to choose between getting the result in tangent or world space
* Tweaked 'World Space Light Dir' to no longer use internal input port data when nothing is connected, instead worldPos is automaticaly fed into it
* Added Unity version check for wires shader and fix compatibility issues
* Added Normalize toggle into 'Screen Position' node 
* Community Node additions
    * Tobias Pott
        * Added 'Swizzle' node

v0.2.4 dev 002
* Added 'Layered Blend' node
* Added 'Weighted Blend' node
* Added 'Texel Size' node
* Merged '[VS] Vertex Color' and 'Vertex Color' nodes in order to avoid further confusions and marked the first one as deprecated
    * Node internally changes its output if it's writing to a vertex or fragment port
* Added 'Surface Depth' node
* Added 'Screen Depth' node
* Fixed issue with property nodes uniform variables not taking selected precision into account

v0.2.4 dev 001
* Added 'Parallax Mapping' node 
* Added 'Negate' node 
* Added Fake Window user sample 
* Added Parallax Mapping example with 4 iterations using the basic Parallax Mapping node 

v0.2.3 dev 002
* Added 'Custom Expression' node
* Precision used is now the least between the one selected in the node and main one selected on the master node
* 'Register Local Var' and 'Get Local Var' nodes can now be used on Master Node Vertex ports
* Improved wires visuals
* Fixed issue with local variables generation
* Fixed issue with switching port internal data from float to int
* Fixed cast issue with 'Remap' node
* Added all the supported shader models into the Master Node dropdown
    * When creating a shader the default selected is now 2.5 to match Unity default
* Community Node additions
    * The Four Headed Cat
        * Added 'Grayscale' node

v0.2.3 dev 001
* New control points can be added to wires to better manipulate its shape
    * Double click a wire to create an additional control point
    * Control points are selected, moved and deleted similar to regular nodes
* Fixed issue with box selection being active when dragging nodes with the 'Shift' key held down
* Fixed issues with wire resources not being correctly released when ASE is shut down
* Fixed issues with 'Pixel Normal World' and 'Vertex Normal World' nodes
* Fixed issue with 'Multiply' node on Matrix/Vector multiplications
* Fixed issue with 'Break To Components' node
* Fixed issue with 'Component Mask' node
* Fixed issue with wrong type propagation when replacing node connections 
* Fixed issue with instance references being lost on 'Texture Sample' and 'Screen Color' nodes
* Tweaked 'Object to World' and 'World to Object' nodes to be more compile friendly
* Added 'Model' matrix node
* Added 'Relay' node
* Added 'TriplanarObjectSpace' sample to Samples folder
* Added precision selector for variables ( known issue: precision info is not being taken into account when auto local vars are created )
* Community Node additions
    * The Four Headed Cat
        * Added 'Tau' node
    * Rea
        * Added 'Height Map Blend' node

v0.2.2 dev 001
* Fixed issue with 'Mask Clip Value' not being correctly synced between material and shader
* Added colored Debug Mode ... this is an experimental feature where each port and wire are colored according to its data type. Each color/type pair are not final
    * Hold 'P' key to enable debug mode and release it to disable it
    * Double hit 'P' key to toggle  debug mode on. Double hit 'P' key again to disable it.
* Added 'Matrix From Vectors' node
* Fixed issue with 'Vertex Binormal World' node	
* Added 'Shader Model' dropdown on Master Node properties
* Community Node additions
    * The Four Headed Cat
        * Fixed issues with 'Logical Operator' nodes
    
v0.2.1 dev 001
* Fixed multiple issues importing current version on top of v0.1.0 dev 001

v0.2.0 dev 003
* Mask Clip Value when used is registered as a Material Property

v0.2.0 dev 002
* Improved duplicate code detection system
* Slight improvement on save/update times ( important for Live Mode )
* Fixed issue with 'Texture Sample' node incorrectly outputting a float4 when no texture assigned and on Normal mode 
* Fixed issue on not opening the correct shader from a material inspector if a new one is selected from its dropdown
* Fixed issue with 'Length' node incorrectly changing its output type
* Community Node additions
    * The Four Headed Cat
        * Flipbook UV Animation

v0.2.0 dev 001
* Fixed issues with 'Get Local Var' node
    * Output port type is now correctly set to its reference type
    * Changed how references are saved so it won't be affected by order index re-ordering
* Fixed issue with 'Power' node
* Fixed issue with 'ATan2' node
* Fixed issue with 'Cross' node
* Community Node additions
    * The Four Headed Cat
        * Logical If
        * Pixelate
* Community Samples additions
    * The Four Headed Cat 
        * DissolveBurn
    * Mourelas Konstantinos
        * EnvironmentGradient
        
v0.1.5 dev 001
* Community Node additions
    * The Four Headed Cat ( moved to a separate 'Logical Operators' category )
        * Compare With Range
        * Compare Not Equal
        * Compare Lower Equal
        * Compare Greater Equal
        * Compare Lower
        * Compare Greater
    * Kebrus
        * Vertex Tangent World
        * Vertex Binormal World
* 'Register Local Var' node changes:
    * Is now independent from Master Node execution order and generates activation signals
    * Will always be executed even if not connected to Master Node 
    * Fixed issue updating name string array when loading from file		
    * Added order index to control their declaration order( lesser index declared first )
* Fixed issue on port type change not propagating in certain nodes
* Hitting Escape key will disable context palette menu if active
* Fixed issue where right mouse clicking on certain port areas would delete their wire connection
* Minor improvement on nodes performance

v0.1.4 dev 002
* Dynamic type nodes now also reacting to input port disconnections
* Updated TFHCRemap node from benderete

0.1.4 dev 001
* Fixed issue with order index not being correctly read/written
* Redone Refraction Shader according to rea suggestion 
* Register Local Var node now has a title style similar to Property Nodes with the local var name always visible 
* Added Get Local Var Node. It allows the user to use already registered local vars anywhere in the graph
* Added Custom Node Remap ("TFHC - Remap") created by user benderete
* Fixed issue on Texture Sample UV port not correctly casting to float2 when needed
* Fixed issue with Texture Sample node not adapting layout when reference is in normal mode

v0.1.3 dev 003
* Added RegisterLocalVar node. This is a node to improve shader readability in certain situations, as it forces the shader to create a local var containing its input value and always use that as output
* Added Screen Color Texture Reference feature
* Created Simple Refraction example ( Samples/SimpleRefraction ) 
* Fixed issues with sampler instance resizing
* Fixed issue with Fresnel node incident vector
* Fixed issue with attempting connection removal on inexistent nodes ( bug affecting Append node )
* Fixed issue with overwriting render type and queue values with Blend Mode default values on read from file

v0.1.3 dev 002
* Fixed multiple issues with deleting a Texture Sample node being referenced by other nodes
* Tweaked Texture Sample reference UI 

v0.1.3 dev 001
* Fixed issue when releasing mouse drag on menu areas not resetting auto*pan
* Tweaked Shader Instancing UI
* Fixed issue with material inspector crashing when updating a property with ASE window turned off
* Fixed issue with custom UI skins not being correctly initialized under some conditions
* Added Texture Reference feature
* Added 'Simple Blur' example to demonstrate how Texture referencing works
* Added small 'Made with Amplify Shader Editor' info as comment on generated shaders

v0.1.2 dev 003
* Fixed issues with opening materials via inspector with no ASE window initialized  

v0.1.2 dev 002
* Added GPU Instancing ( see Samples/SimpleGPUInstancing example )
* Added Screen Color node 
* Fixed issue on version testing
* Tweaked Master Node icon 
* Added additional icon into top-left master node node indicating if gpu instancing is being used or not

v0.1.2 dev 001
* Fixed typo on Texture Coordinate node
* Added explicit control on Render Type and Queue. They will be automatically set when a Blend Mode is selected.
* Added Fresnel node
* Fixed Trigonometry typo 
* Improved local var generation on op nodes
* Added FWidth node
* Fixed issue with not immediately updating shader when hitting the Live Shader Mode button

v0.1.1 dev 001
* Community Contribution from kebrus: Added Rotator node, rotates UV Coordinates/ Vector2 nodes 
* Fixed Vector2 Append Node bug
* Fixed Int to Float cast issues
* Added Shader title area on top canvas to forbidden node interaction area
* Adjusted Auto-Pan behavior
* Fixed issue with nodes infinite loop detection
* Tweaked mouse detection inside main canvas
* Fixed Queue Order typo when building shader
* Improved notifications when impossible cast is requested
* Added new VectorFromMatrix which gets a specific row or column from a 3x3 or 4x4 matrix
* Automatically hiding Order Index on Uniform Property Type nodes
* Simpler Searchable Node List also being called by hitting the space bar if focus is on main node canvas
