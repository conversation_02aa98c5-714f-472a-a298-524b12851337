﻿ 



 
 


using UnityEngine;
using System;

namespace TFW.Map
{
    //在一定的相机高度距离内保持game object在屏幕上大小不变
    public abstract class KeepScale : MonoBehaviour
    {
        public KeepScaleConfig config;
        //超过缩放上限距离时回调
        public event System.Action<float, bool> PassUpperHeightLimitEvent;
        //超过缩放下限距离时回调
        public event System.Action<float, bool> PassLowerHeightLimitEvent;

        protected float mLastCameraHeight = 0;
        float mUpScaleThresholdDistance = 0.0f;
        protected float mPrefabInitScale = 0;
        //don't modify this value after it's been set
        protected float mPrefabInitScaleConst = 0;

        protected abstract void SetInitScale();

        protected bool UpdateScaleImpl()
        {
            var map = Map.currentMap;
            if (!System.Object.ReferenceEquals(map, null))
            {
                var config = GetScaleConfig();
                if (!System.Object.ReferenceEquals(config, null))
                {
                    var camera = Map.currentMap.camera;
                    float cameraHeight = camera.transform.position.y;

                    float upperHeight = config.maximumCameraHeight + mUpScaleThresholdDistance;

                    if (Utils.GT(cameraHeight, upperHeight) && Utils.GT(mLastCameraHeight, upperHeight))
                    {
                        return false;
                    }

                    TriggerScaleEvent(cameraHeight, upperHeight, config);

                    mLastCameraHeight = cameraHeight;
                    cameraHeight = Mathf.Clamp(cameraHeight, config.minimumCameraHeight, config.maximumCameraHeight);

                    SetScaleAtHeight(cameraHeight);

                    return true;
                }
            }

            return false;
        }

        void TriggerScaleEvent(float cameraHeight, float upperHeight, KeepScaleConfig config)
        {
            if (!System.Object.ReferenceEquals(PassUpperHeightLimitEvent, null))
            {
                if (Utils.GE(cameraHeight, upperHeight) && Utils.LT(mLastCameraHeight, upperHeight))
                {
                    //超过了scale上限
                    PassUpperHeightLimitEvent(cameraHeight, true);
                }
                else if (Utils.LE(cameraHeight, upperHeight) && Utils.GT(mLastCameraHeight, upperHeight))
                {
                    //小于了scale下限
                    PassUpperHeightLimitEvent(cameraHeight, false);
                }
            }

            if (!System.Object.ReferenceEquals(PassLowerHeightLimitEvent, null))
            {
                //mLastCameraHeight == 0的原因是有可能建筑初始化时就在小于minimumCameraHeight的高度,这时还没有last camera height
                if (Utils.LE(cameraHeight, config.minimumCameraHeight) && (mLastCameraHeight >= config.minimumCameraHeight || mLastCameraHeight == 0))
                {
                    //小于了scale下限
                    PassLowerHeightLimitEvent(cameraHeight, false);
                }
                else if (cameraHeight >= config.minimumCameraHeight && mLastCameraHeight <= config.minimumCameraHeight)
                {
                    //超过了scale下限距离
                    PassLowerHeightLimitEvent(cameraHeight, true);
                }
            }
        }

        protected abstract void SetScaleAtHeight(float cameraHeight);

        public float GetMaxCameraHeightWhenCityIsInCenter()
        {
            var config = GetScaleConfig();
            if (!System.Object.ReferenceEquals(config, null))
            {
                return config.maximumCameraHeightWhenCityIsInCenter;
            }
            return 0;
        }

        public Vector2 GetCameraHeightRange()
        {
            var config = GetScaleConfig();
            if (!System.Object.ReferenceEquals(config, null))
            {
                return new Vector2(config.minimumCameraHeight, config.maximumCameraHeight);
            }
            return Vector2.zero;
        }

        protected abstract KeepScaleConfig GetScaleConfig();

        protected float GetScaleFactor(float cameraHeight)
        {
            var config = GetScaleConfig();
            var camera = Map.currentMap.camera;
            float oldWidth = Utils.CalculateViewportWidth(config.cameraFovWhenScaleIsOne, Map.currentMap);
            float fov = Map.currentMap.GetCameraFOVAtHeight(cameraHeight);
            float newWidth = Utils.CalculateViewportWidth(fov, Map.currentMap);
            float ratio = newWidth / oldWidth;

            //calculate object scale at camera height
            float scaleAtCameraHeight = 1.0f / config.cameraHeightWhenScaleIsBaseScale * cameraHeight * ratio;
            return scaleAtCameraHeight;
        }

        //计算在某个相机高度下物体所占某面积下的scale
        protected float CalculateObjectScaleAtHeight(float prefabInitScale, float cameraHeight)
        {
            return prefabInitScale * GetScaleFactor(cameraHeight);
        }

        public void AddPassUpperHeightLimitEvent(System.Action<float, bool> handler)
        {
            PassUpperHeightLimitEvent += handler;
        }
        public void RemovePassUpperHeightLimitEvent(System.Action<float, bool> handler)
        {
            PassUpperHeightLimitEvent -= handler;
        }
        //超过缩放下限距离时回调
        public void AddPassLowerHeightLimitEvent(System.Action<float, bool> handler)
        {
            PassLowerHeightLimitEvent += handler;
        }
        public void RemovePassLowerHeightLimitEvent(System.Action<float, bool> handler)
        {
            PassLowerHeightLimitEvent -= handler;
        }

        public float upperHeight { get { return GetScaleConfig().maximumCameraHeight + mUpScaleThresholdDistance; } }
        public float lowerHeight { get { return GetScaleConfig().minimumCameraHeight; } }
    }
}
