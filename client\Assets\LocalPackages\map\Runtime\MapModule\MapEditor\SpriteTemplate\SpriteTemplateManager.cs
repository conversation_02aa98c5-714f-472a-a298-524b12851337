﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;

namespace TFW.Map {
    [Black]
    public class SpriteTemplateManager {
        public void AddSpriteTemplate(SpriteTemplate temp) {
            if (!mSpriteTemplates.Contains(temp)) {
                mSpriteTemplates.Add(temp);

                CreateNames();
            }
        }

        public void RemoveSpriteTemplate(int id) {
            for (int i = 0; i < mSpriteTemplates.Count; ++i) {
                if (mSpriteTemplates[i].id == id) {
                    Map.currentMap.DestroyObject(id);
                    mSpriteTemplates.RemoveAt(i);
                    CreateNames();
                    break;
                }
            }
        }

        public SpriteTemplate FindSpriteTemplate(string name) {
            for (int i = 0; i < mSpriteTemplates.Count; ++i) {
                if (mSpriteTemplates[i].name == name) {
                    return mSpriteTemplates[i];
                }
            }
            return null;
        }

        public int GetSpriteTemplateIndex(int spriteTemplateID) {
            for (int i = 0; i < mSpriteTemplates.Count; ++i) {
                if (mSpriteTemplates[i].id == spriteTemplateID) {
                    return i;
                }
            }
            return -1;
        }

        public SpriteTemplate GetSpriteTemplate(int index) {
            if (index >= 0 && index < mSpriteTemplates.Count) {
                return mSpriteTemplates[index];
            }
            return null;
        }

        public void ChangeSpriteTemplateName(int index, string newName) {
            if (index >= 0 && index < mSpriteTemplates.Count) {
                mSpriteTemplates[index].name = newName;
                CreateNames();
            }
        }

        void CreateNames() {
            int n = mSpriteTemplates.Count;
            if (mSpriteTemplateNames.Length != n) {
                mSpriteTemplateNames = new string[n];
            }
            for (int i = 0; i < n; ++i) {
                mSpriteTemplateNames[i] = mSpriteTemplates[i].name;
            }   
        }

        public List<SpriteTemplate> spriteTemplates { get { return mSpriteTemplates; } }
        public string[] spriteTemplateNames { get { return mSpriteTemplateNames; } }

        List<SpriteTemplate> mSpriteTemplates = new List<SpriteTemplate>();
        string[] mSpriteTemplateNames = new string[0];
    }
}

#endif