﻿using UnityEngine;
using UnityEditor;

public class PrecomputeTextureTool : EditorWindow
{
    [MenuItem("Optimize/Precompute Textures")]
    static void Init()
    {
        PrecomputeTextureTool window = (PrecomputeTextureTool)EditorWindow.GetWindow(typeof(PrecomputeTextureTool));
        window.Show();
    }

    private Texture2D _L1Tex;
    private Texture2D _L2Tex;
    private Texture2D _L3Tex;
    private Texture2D _L4Tex;
    private Texture2D _Detail;
    private Texture2D _DetailTexture;
    private Vector2 _DetailTiling = Vector2.one;
    private Vector2 _DetailTextureTiling = Vector2.one;
    private Vector2 _DetailOffset = Vector2.zero;
    private Vector2 _DetailTextureOffset = Vector2.zero;

    void OnGUI()
    {
        _L1Tex = (Texture2D)EditorGUILayout.ObjectField("L1 Texture", _L1Tex, typeof(Texture2D), false);
        _L2Tex = (Texture2D)EditorGUILayout.ObjectField("L2 Texture", _L2Tex, typeof(Texture2D), false);
        _L3Tex = (Texture2D)EditorGUILayout.ObjectField("L3 Texture", _L3Tex, typeof(Texture2D), false);
        _L4Tex = (Texture2D)EditorGUILayout.ObjectField("L4 Texture", _L4Tex, typeof(Texture2D), false);
        _Detail = (Texture2D)EditorGUILayout.ObjectField("Detail Mask", _Detail, typeof(Texture2D), false);
        _DetailTiling = EditorGUILayout.Vector2Field("Detail Mask Tiling", _DetailTiling);
        _DetailOffset = EditorGUILayout.Vector2Field("Detail Mask Offset", _DetailOffset);

        _DetailTexture = (Texture2D)EditorGUILayout.ObjectField("Detail Texture", _DetailTexture, typeof(Texture2D), false);
        _DetailTextureTiling = EditorGUILayout.Vector2Field("Detail Texture Tiling", _DetailTextureTiling);
        _DetailTextureOffset = EditorGUILayout.Vector2Field("Detail Texture Offset", _DetailTextureOffset);
        if (GUILayout.Button("Precompute12"))
        {
            PrecomputeTextures12();
        }

        if (GUILayout.Button("Precompute34"))
        {
            PrecomputeTextures34();
        }

        if (GUILayout.Button("Precompute1234"))
        {
            PrecomputeTextures1234();
        }

        if (GUILayout.Button("Precompute142434"))
        {
            PrecomputeTextures142434();
        }

        if (GUILayout.Button("PrecomputeDetail"))
        {
            PrecomputeTexturesDetail();
        }
    }

    void PrecomputeTextures142434()
    {
        int width = _L1Tex.width;
        int height = _L1Tex.height;

        Texture2D preComputedTex1 = new Texture2D(width, height, TextureFormat.RGBA32, false);
        Texture2D preComputedTex2 = new Texture2D(width, height, TextureFormat.RGBA32, false);
        Texture2D preComputedTex3 = new Texture2D(width, height, TextureFormat.RGBA32, false);

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Color l1 = _L1Tex.GetPixel(x, y);
                Color l2 = _L2Tex.GetPixel(x, y);
                Color l3 = _L3Tex.GetPixel(x, y);
                Color l4 = _L4Tex.GetPixel(x, y);

                Color preComputedColor1 = new Color(l1.r, l1.g, l1.b, l4.r);
                Color preComputedColor2 = new Color(l2.r, l2.g, l2.b, l4.g);
                Color preComputedColor3 = new Color(l3.r, l3.g, l3.b, l4.b);

                preComputedTex1.SetPixel(x, y, preComputedColor1);
                preComputedTex2.SetPixel(x, y, preComputedColor2);
                preComputedTex3.SetPixel(x, y, preComputedColor3);
            }
        }

        preComputedTex1.Apply();
        preComputedTex2.Apply();
        preComputedTex3.Apply();

        SaveTexture(preComputedTex1, "PrecomputedTexture_14.png");
        SaveTexture(preComputedTex2, "PrecomputedTexture_24.png");
        SaveTexture(preComputedTex3, "PrecomputedTexture_34.png");
    }

    void SaveTexture(Texture2D texture, string defaultName)
    {
        byte[] bytes = texture.EncodeToPNG();
        string path = EditorUtility.SaveFilePanel("Save Precomputed Texture", "", defaultName, "png");
        if (!string.IsNullOrEmpty(path))
        {
            System.IO.File.WriteAllBytes(path, bytes);
            AssetDatabase.Refresh();

            Debug.Log("Precomputed texture saved to " + path);
        }
    }

    void PrecomputeTextures1234()
    {
        int width = _L1Tex.width;
        int height = _L1Tex.height;

        Texture2D preComputedTex = new Texture2D(width, height, TextureFormat.RGBA32, false);

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Color l1 = _L1Tex.GetPixel(x, y);
                Color l2 = _L2Tex.GetPixel(x, y);
                Color l3 = _L3Tex.GetPixel(x, y);
                Color l4 = _L4Tex.GetPixel(x, y);

                Color preComputedColor = new Color(l1.g, l2.g, l3.g, l4.g);
                preComputedTex.SetPixel(x, y, preComputedColor);
            }
        }

        preComputedTex.Apply();

        byte[] bytes = preComputedTex.EncodeToPNG();
        string path = EditorUtility.SaveFilePanel("Save Precomputed Texture", "", "PrecomputedTexture.png", "png");
        if (!string.IsNullOrEmpty(path))
        {
            System.IO.File.WriteAllBytes(path, bytes);
            AssetDatabase.Refresh();

            Debug.Log("Precomputed texture saved to " + path);
        }
    }

    void PrecomputeTextures12()
    {
        int width = _L1Tex.width;
        int height = _L1Tex.height;

        Texture2D preComputedTex = new Texture2D(width, height, TextureFormat.RGBA32, false);

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Color l1 = _L1Tex.GetPixel(x, y);
                Color l2 = _L2Tex.GetPixel(x, y);
                Color preComputedColor = new Color(l1.r, l1.g, l2.r, l2.g);
                preComputedTex.SetPixel(x, y, preComputedColor);
            }
        }

        preComputedTex.Apply();

        byte[] bytes = preComputedTex.EncodeToPNG();
        string path = EditorUtility.SaveFilePanel("Save Precomputed Texture", "", "PrecomputedTexture.png", "png");
        if (!string.IsNullOrEmpty(path))
        {
            System.IO.File.WriteAllBytes(path, bytes);
            AssetDatabase.Refresh();

            Debug.Log("Precomputed texture saved to " + path);
        }
    }

    void PrecomputeTextures34()
    {
        int width = _L3Tex.width;
        int height = _L3Tex.height;

        Texture2D preComputedTex = new Texture2D(width, height, TextureFormat.RGBA32, false);

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Color l3 = _L3Tex.GetPixel(x, y);
                Color l4 = _L4Tex.GetPixel(x, y);
                Color preComputedColor = new Color(l3.r, l3.g, l4.r, l4.g);
                preComputedTex.SetPixel(x, y, preComputedColor);
            }
        }

        preComputedTex.Apply();

        byte[] bytes = preComputedTex.EncodeToPNG();
        string path = EditorUtility.SaveFilePanel("Save Precomputed Texture", "", "PrecomputedTexture.png", "png");
        if (!string.IsNullOrEmpty(path))
        {
            System.IO.File.WriteAllBytes(path, bytes);
            AssetDatabase.Refresh();

            Debug.Log("Precomputed texture saved to " + path);
        }
    }
    void PrecomputeTexturesDetail()
    {
        Texture2D baseTex = GetLargestTexture();
        int width = baseTex.width;
        int height = baseTex.height;

        Texture2D preComputedTex = new Texture2D(width, height, TextureFormat.RGBA32, false);

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Color detail = GetPixelWithTilingAndOffset(_Detail, _DetailTiling, _DetailOffset, width, height, x, y);
                Color detailTex = GetPixelWithTilingAndOffset(_DetailTexture, _DetailTextureTiling, _DetailTextureOffset, width, height, x, y);

                Color preComputedColor = new Color(detailTex.r, detailTex.g, detailTex.b, detail.r);
                preComputedTex.SetPixel(x, y, preComputedColor);
            }
        }

        preComputedTex.Apply();

        SaveTexture(preComputedTex, "PrecomputedTexture_Detail.png");
    }
    Color GetPixelWithTilingAndOffset(Texture2D tex, Vector2 tiling, Vector2 offset, int baseWidth, int baseHeight, int x, int y)
    {
        if (tex == null)
        {
            return Color.clear;
        }

        float u = ((float)x / baseWidth * tiling.x) + offset.x;
        float v = ((float)y / baseHeight * tiling.y) + offset.y;
        return tex.GetPixelBilinear(u, v);
    }
    Texture2D GetLargestTexture()
    {
        Texture2D[] textures = { _Detail, _DetailTexture };
        Texture2D largest = textures[0];
        foreach (var tex in textures)
        {
            if (tex && tex.width * tex.height > largest.width * largest.height)
            {
                largest = tex;
            }
        }

        return largest;
    }
}