﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        void Load(string folderPath)
        {
            string filePath = $"{folderPath}/{MapCoreDef.OBJECT_PLACEMENT_EDITOR_FILE_NAME}";

            var bytes = File.ReadAllBytes(filePath);
            if (bytes != null && bytes.Length > 0)
            {
                var stream = new MemoryStream(bytes);
                BinaryReader reader = new BinaryReader(stream);

                int version = reader.ReadInt32();

                //load setting
                mShowRotationSetting = reader.ReadBoolean();
                mShowGridSetting = reader.ReadBoolean();
                mShowPrefabSetting = reader.ReadBoolean();
                mSnapToGrid = reader.ReadBoolean();
                mGridSize = reader.ReadSingle();
                mPrefabRotationSetting.rotation = Utils.ReadQuaternion(reader);
                mPrefabRotationSetting.rotationStep = reader.ReadSingle();
                mPrefabRotationSetting.randomYRotation = reader.ReadBoolean();
                if (version >= 2)
                {
                    mPlaceRandomObject = reader.ReadBoolean();
                }
                if (version >= 3)
                {
                    mMinDistance = reader.ReadSingle();
                    mFillCount = reader.ReadInt32();
                    mMode = (Mode)reader.ReadInt32();
                    SetMode(mMode);
                }
                if (version >= 4)
                {
                    mFillEdge = reader.ReadBoolean();
                    mEdgeSize = reader.ReadSingle();
                    mCircleRadius = reader.ReadSingle();
                }
                if (version >= 5)
                {
                    mUseSameDeltaDistance = reader.ReadBoolean();
                }
                if (version >= 7)
                {
                    mRemoveObjectOfSeletedPrefab = reader.ReadBoolean();
                }
                if (version >= 8)
                {
                    float worldSize = reader.ReadSingle();
                    SetWorldSize(worldSize);
                    if (version >= 9)
                    {
                        float displayGridSize = reader.ReadSingle();
                        SetDisplayGridSize(displayGridSize);
                    }

                    if (mPrefabIndicator == null)
                    {
                        mPrefabIndicator = new PrefabIndicator(false);
                    }
                    string prefabGuid = Utils.ReadString(reader);
                    var assetPath = AssetDatabase.GUIDToAssetPath(prefabGuid);
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                    mPrefabIndicator.SetPrefab(prefab, mToolObjectRoot.transform);
                }

                //load prefab manager
                InitPrefabManager();
                LoadPrefabManager(reader, version);

                if (version >= 6)
                {
                    LoadBrush(reader);
                }

                mProjectFolder = folderPath;

                reader.Close();
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "File not found!", "OK");
            }
        }

        void LoadPrefabManager(BinaryReader reader, int version)
        {
            int nGroups = reader.ReadInt32();
            for (int i = 0; i < nGroups; ++i)
            {
                LoadPrefabGroup(reader, version);
            }
        }

        void LoadPrefabGroup(BinaryReader reader, int version)
        {
            string name = Utils.ReadString(reader);
            float gridSize = 0;
            if (version >= 8)
            {
                gridSize = reader.ReadSingle();
            }
            var group = mPrefabManager.AddGroup(-1, name, Color.white, false, false, gridSize);
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                string prefabGuid = Utils.ReadString(reader);
                string prefabPath = AssetDatabase.GUIDToAssetPath(prefabGuid);
                group.AddPrefab(prefabPath, null, false, true, false, null, new Vector2Int(1, 1), 0);
            }
        }

        void LoadBrush(BinaryReader reader)
        {
            SetSelectedBrush(-1);
            mBrushies = new List<Brush>();
            int nBrushies = reader.ReadInt32();
            for (int i = 0; i < nBrushies; ++i)
            {
                Brush brush = new Brush();
                brush.name = Utils.ReadString(reader);
                int nObjects = reader.ReadInt32();
                for (int k = 0; k < nObjects; ++k)
                {
                    BrushObject bo = new BrushObject();
                    brush.objects.Add(bo);
                    bo.layer = reader.ReadInt32();
                    bo.position = Utils.ReadVector3(reader);
                    bo.scale = Utils.ReadVector3(reader);
                    bo.rotation = Utils.ReadQuaternion(reader);
                    bo.tag = Utils.ReadString(reader);
                    string guid = Utils.ReadString(reader);
                    bo.prefabPath = AssetDatabase.GUIDToAssetPath(guid);
                }
                mBrushies.Add(brush);
            }
            SetSelectedBrush(nBrushies - 1);
        }
    }
}
#endif