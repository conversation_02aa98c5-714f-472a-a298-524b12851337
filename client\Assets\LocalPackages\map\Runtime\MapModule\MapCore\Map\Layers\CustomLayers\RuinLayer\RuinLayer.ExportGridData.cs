﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;
using System.Text;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class RuinLayer : ModelLayer, IOverlapObjectAtPosition
    {
        //k1格式
        public void ExportGridData(List<RuinSetting> ruinSettings, string filePath, int startID, float startX, float startZ, float bigGridWidth, float bigGridHeight, int horizontalBigGridCount, int verticalBigGridCount)
        {
            if (ruinSettings == null || ruinSettings.Count == 0)
            {
                return;
            }

            //get all ruins
            StringBuilder builder = new StringBuilder();
            List<IMapObjectData> allRuins = new List<IMapObjectData>();
            for (int i = 0; i < ruinSettings.Count; ++i)
            {
                for (int h = 0; h < hordeCount; ++h)
                {
                    var objects = GetObjectsInsideHorde(h, ruinSettings[i].name);
                    allRuins.AddRange(objects);
                }
            }

            //create header
            PropertyDatas properties = ruinSettings[0].properties;
            List<string> headers = CreateHeaders(properties);
            for (int i = 0; i < headers.Count; ++i)
            {
                builder.Append(headers[i]);
                if (i != headers.Count - 1)
                {
                    builder.Append("\t");
                }
            }
            builder.AppendLine();

            //put ruins in grids
            for (int i = 0; i < allRuins.Count; ++i)
            {
                var ruin = allRuins[i] as RuinData;
                var pos = ruin.GetPosition();

                builder.AppendFormat("{0}\t", startID);
                ++startID;

                builder.AppendFormat("{0}\t", ruin.objectType);

                var s = string.Format("\"x\":{0},\"z\":{1}", Utils.F2I(pos.x), Utils.F2I(pos.z));
                s = "{" + s + "}\t";
                builder.Append(s);

                var rotation = ruin.GetRotation();
                builder.AppendFormat("{0}\t", rotation.eulerAngles.y);

                properties = ruin.properties;
                int propCount = properties.GetPropertyCount();

                //region id
                float localX = pos.x - startX;
                float localZ = pos.z - startZ;
                int bigGridX = Mathf.FloorToInt(localX / bigGridWidth);
                int bigGridY = Mathf.FloorToInt(localZ / bigGridHeight);
                int bigIndex = bigGridY * horizontalBigGridCount + bigGridX;
                int distanceX = Mathf.Abs(bigGridX - horizontalBigGridCount / 2);
                int distanceY = Mathf.Abs(bigGridY - verticalBigGridCount / 2);
                int minDistance = Mathf.Max(distanceX, distanceY);
                int cirlceIndex = horizontalBigGridCount / 2 - minDistance + 1;
                int gridIndex = cirlceIndex * 100 + bigIndex;
                builder.AppendFormat("{0}\t", gridIndex);

                builder.AppendFormat("{0}{1}", ruin.radius, propCount == 0 ? '\n' : '\t');

                if (mShareProperties)
                {
                    for (int p = 0; p < propCount; ++p)
                    {
                        var property = properties.GetProperty(p);
                        switch (property.type)
                        {
                            case PropertyType.kPropertyFloat:
                                builder.AppendFormat("{0}{1}", (property as PropertyData<float>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                                break;
                            case PropertyType.kPropertyInt:
                                builder.AppendFormat("{0}{1}", (property as PropertyData<int>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                                break;
                            case PropertyType.kPropertyIntArray:
                                builder.AppendFormat("{0}{1}", Utils.IntArrayToStringList((property as PropertyData<int[]>).value), p == propCount - 1 ? '\n' : '\t');
                                break;
                            case PropertyType.kPropertyString:
                                builder.AppendFormat("{0}{1}", (property as PropertyData<string>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                                break;
                            default:
                                Debug.Assert(false, "not supported now!");
                                break;
                        }
                    }
                }
                else
                {
                    var customPropertiesStr = properties.ConvertToJsonString();
                    builder.AppendFormat("{0}\n", customPropertiesStr);
                }
            }

            var str = builder.ToString();
            File.WriteAllText(filePath, str);
        }
    }
}

#endif