﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public partial class DetailSprites
    {
        class SpriteGroup
        {
            public SpriteGroup(int nSprites)
            {
                sprites = new string[nSprites];
                spriteBaseScale = new float[nSprites];
                spriteRadius = new float[nSprites];
            }
            public string[] sprites;
            public float[] spriteBaseScale;
            public float[] spriteRadius;
        }

        //调试用
        GridViewer mGridViewer;

        //sprite的类型
        List<SpriteGroup> mSpriteGroups;

        //sprite的材质,所有sprite必须使用相同的材质,即他们必须能够被打包进一张atlas
        Material mSpriteMaterial;
        //sprite alpha为0时相机的高度
        float mAlpha0Height = 18;
        //sprite alpha为1时相机的高度
        float mAlpha1Height = 17;
        //是否使用fadein/out过度
        bool mCrossfading = true;
        //相机高度变化时是否动态调整sprite大小
        bool mUpdateScale = true;
        KeepScaleUpdater mScaleUpdater;
        //缩放因子
        float mScaleFactorAtCameraHeight;

        List<SpriteObject> mSprites = new List<SpriteObject>();
        SubGridObjects[,] mSubGridObjects;

        //一共40x40x18x18个
        int[] mSubgridKeysStartOffset;
        //一共40x40x18x18个,每个subgrid使用的sprite集合索引
        byte[] mSubgridSpriteGroupIndices;
        //所有有效的key,按照格子顺序存储
        byte[] mAllKeys;

        //每个小格子多少个sprite
        int mSpriteCountPerGrid = 5;

        //tile的大小,通常为180m
        float mTileSize;
        //小格子的大小,默认为10米
        float mGridSize;
        //有多少个tile
        int mRows;
        int mCols;
        //一个tile里水平有多少个小格子,默认为18个
        int mHorizontalGridCount;

        float mLastUpdateCameraHeight;

        class SpriteObject
        {
            public GameObject obj;
            public int type;
            public int groupIndex = -1;
            public int gx;
            public int gy;
        }

        class SubGridObjects
        {
            public List<SpriteObject>[,] objs;

            //temp code
            //public List<Vector2>[,] objPositions;
        }

        //all sprites must be in one atlas texture!
        public void Init(string spritePath, string spawnPointPath, KeepScaleConfig scaleConfig, Map map)
        {
            bool suc = LoadSprites(spritePath);
            if (!suc)
            {
                return;
            }
            LoadSpawnPoints(spawnPointPath);

            mMap = map;
            mObjectPool = new GameObjectPool(CreateSpriteGameObject, (path) => { MapModuleResourceMgr.UnloadAsset(path); }, (str) => 
            {
                return MapModuleResourceMgr.Exists(str);
            });
            mLastUpdateViewport = new Rect(-10000, -10000, 0, 0);

            if (scaleConfig != null)
            {
                mScaleUpdater = new KeepScaleUpdater(new KeepScaleConfig[] { scaleConfig });
            }

            mSubGridObjects = new SubGridObjects[mRows, mCols];
            for (int y = 0; y < mRows; ++y)
            {
                for (int x = 0; x < mCols; ++x)
                {
                    mSubGridObjects[y, x] = new SubGridObjects();
                    mSubGridObjects[y, x].objs = new List<SpriteObject>[mHorizontalGridCount, mHorizontalGridCount];
                }
            }

            InitSpriteData();
        }

        bool LoadSprites(string spritePath)
        {
            var stream = MapModuleResourceMgr.LoadTextStream(spritePath, true);
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);

                int majorVersion = reader.ReadInt32();
                int minorVersion = reader.ReadInt32();

                string validPrefabPath = null;
                int groupCount = reader.ReadInt32();
                mSpriteGroups = new List<SpriteGroup>(groupCount);
                for (int i = 0; i < groupCount; ++i)
                {
                    int nSprites = reader.ReadInt32();
                    var group = new SpriteGroup(nSprites);
                    for (int k = 0; k < nSprites; ++k)
                    {
                        group.sprites[k] = Utils.ReadString(reader);
                        if (validPrefabPath == null)
                        {
                            validPrefabPath = group.sprites[k];
                        }
                    }
                    mSpriteGroups.Add(group);
                }

                mListPool = new ObjectPool<List<SpriteObject>>(40, () => { return new List<SpriteObject>(); });
                mAlpha0Height = reader.ReadSingle();
                mAlpha1Height = reader.ReadSingle();
                mCrossfading = reader.ReadBoolean();
                mUpdateScale = reader.ReadBoolean();
                var mtlPath = Utils.ReadString(reader);
                mSpriteMaterial = MapModuleResourceMgr.LoadPrefab(validPrefabPath).GetComponentInChildren<SpriteRenderer>().sharedMaterial;
                mSpriteMaterial = Object.Instantiate<Material>(mSpriteMaterial);

                mRows = reader.ReadInt32();
                mCols = reader.ReadInt32();
                mTileSize = reader.ReadSingle();
                mGridSize = reader.ReadSingle();
                mSpriteCountPerGrid = reader.ReadInt32();
                mHorizontalGridCount = Mathf.FloorToInt(mTileSize / mGridSize);
                mSubgridSpriteGroupIndices = reader.ReadBytes(mRows * mCols * mHorizontalGridCount * mHorizontalGridCount);

                reader.Close();

                return true;
            }


            return false;
        }

        void LoadSpawnPoints(string spawnPointPath)
        {
            var stream = MapModuleResourceMgr.LoadTextStream(spawnPointPath, true);
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);

                int majorVersion = reader.ReadInt32();
                int minorVersion = reader.ReadInt32();

                int n = reader.ReadInt32();
                mSubgridKeysStartOffset = new int[n];
                for (int i = 0; i < n; ++i)
                {
                    mSubgridKeysStartOffset[i] = reader.ReadInt32();
                }

                int validKeyCount = reader.ReadInt32();
                mAllKeys = new byte[validKeyCount];
                for (int i = 0; i < validKeyCount; ++i)
                {
                    mAllKeys[i] = reader.ReadByte();
                }

                mSubGridObjects = new SubGridObjects[mRows, mCols];
                List<byte> allValidKeys = new List<byte>();
                for (int y = 0; y < mRows; ++y)
                {
                    for (int x = 0; x < mCols; ++x)
                    {
                        mSubGridObjects[y, x] = new SubGridObjects();
                        mSubGridObjects[y, x].objs = new List<SpriteObject>[mHorizontalGridCount, mHorizontalGridCount];
                    }
                }

                reader.Close();
            }
        }

        public void OnDestroy()
        {
            ReleaseAllSprites();

            mObjectPool?.OnDestroy();
            mSpritePool?.OnDestroy();

            Utils.DestroyObject(mSpriteMaterial);
            mSpriteMaterial = null;

            mSprites.Clear();
        }

        void InitSpriteData()
        {
            for (int i = 0; i < mSpriteGroups.Count; ++i)
            {
                var group = mSpriteGroups[i];
                int nSprites = group.sprites.Length;
                for (int k = 0; k < nSprites; ++k)
                {
                    var spritePrefab = MapModuleResourceMgr.LoadPrefab(group.sprites[k]);
                    var boundsSize = spritePrefab.GetComponent<Renderer>().bounds.size;
                    group.spriteBaseScale[k] = spritePrefab.transform.localScale.x;
                    group.spriteRadius[k] = Mathf.Max(boundsSize.x, boundsSize.y, boundsSize.z) * 0.5f;
                }
            }
        }

        int GetSubGridIndex(int x, int y, int sx, int sy)
        {
            return (y * mHorizontalGridCount + sy) * mCols * mHorizontalGridCount + x * mHorizontalGridCount + sx;
        }

        int GetKeysStartOffset(int x, int y, int sx, int sy)
        {
            var idx = GetSubGridIndex(x, y, sx, sy);
            return mSubgridKeysStartOffset[idx];
        }

        void GenerateObjects(int x, int y, int sx, int sy, bool createSprite)
        {
            Debug.Assert(mSubGridObjects != null);
            if (mSubGridObjects[y, x] == null)
            {
                return;
            }

            int gridIdx = GetSubGridIndex(x, y, sx, sy);
            var groupIndex = mSubgridSpriteGroupIndices[gridIdx];
            var group = mSpriteGroups[groupIndex];
            int ntypes = group.sprites.Length - 1;

            var objs = mSubGridObjects[y, x].objs[sy, sx];
            if (objs == null)
            {
                objs = mListPool.Require();
                mSubGridObjects[y, x].objs[sy, sx] = objs;
            }

            int keyStartOffset = GetKeysStartOffset(x, y, sx, sy);
            int keyCount = 0;
            if (keyStartOffset >= 0)
            {
                keyCount = mAllKeys[keyStartOffset];
            }
            for (int k = 0; k < keyCount; k++) // how many element by 10*10 square ?
            {
                int key = mAllKeys[keyStartOffset + k + 1];
                float k1 = GetNum(key, x, y, sy, sx);
                float k2 = GetNum((key + 1) % 256, x, y, sy, sx);
                float k3 = GetNum((key + 2) % 256, x, y, sy, sx);
                int type = (int)(ntypes * k1);// we have a set of n sprites to use, which one could it be ?
                var pos = GenerateGridObjectPosition(k2, k3, x, y, sx, sy);

                if (createSprite)
                {
                    int gx = x * mHorizontalGridCount + sx;
                    int gy = y * mHorizontalGridCount + sy;
                    var sprite = CreateSprite(pos.x, pos.y, type, gx, gy, groupIndex);
                    if (sprite != null)
                    {
                        objs.Add(sprite);
                    }
                }
                //else
                //{
                //    if (pos != mSubGridObjects[y, x].objPositions[sy, sx][k])
                //    {
                //        Debug.Assert(false);
                //    }
                //}
            }
        }

        void RemoveObjects(int x, int y, int sx, int sy)
        {
            if (mSubGridObjects[y, x] != null)
            {
                var objs = mSubGridObjects[y, x].objs[sy, sx];
                for (int i = objs.Count - 1; i >= 0; --i)
                {
                    ReleaseSprite(objs[i]);
                }
                objs.Clear();
                mListPool.Release(objs);
                mSubGridObjects[y, x].objs[sy, sx] = null;
            }
        }

        public void Update()
        {
            if (mSubGridObjects == null)
            {
                return;
            }

            UpdateViewport(mMap.originalViewport);

            if (!Mathf.Approximately(mLastUpdateCameraHeight, MapCameraMgr.currentCameraHeight))
            {
                UpdateFading();
                UpdateScale();
                mLastUpdateCameraHeight = MapCameraMgr.currentCameraHeight;
            }

        }

        SpriteObject CreateSprite(float x, float z, int type, int gx, int gy, int groupIndex)
        {
            var group = mSpriteGroups[groupIndex];
            bool intersectedWithDynamicObjects = IsIntersectedWithAnyDynamicObject(x, z, group.spriteRadius[type]);
            if (intersectedWithDynamicObjects)
            {
                return null;
            }

            float height = mMap.GetTerrainHeightAtPos(x, z);
            var sprite = RequireSprite(x, z, type, gx, gy, groupIndex, height);
            return sprite;
        }

        Vector2 GenerateGridObjectPosition(/*ref int startGrid, ref int endGrid, */float k2, float k3, /*float k4, int step,*/ int x, int y, int sx, int sy)
        {
            //int subgrid = (int)(startGrid + (endGrid - startGrid) * k4);
            //startGrid = endGrid;
            //endGrid += step;
            //endGrid = Mathf.Min(endGrid, mSubGridCount - 1);
            //int ssx = subgrid % mSubGridRows;
            //int ssy = subgrid / mSubGridRows;
            //float p1x = x * mTileSize + sx * mGridSize + ssx * mSubGridSize + mSubGridSize * k2;
            //float p1y = y * mTileSize + sy * mGridSize + ssy * mSubGridSize + mSubGridSize * k3;
            float p1x = x * mTileSize + sx * mGridSize + k2 * mGridSize;
            float p1y = y * mTileSize + sy * mGridSize + k3 * mGridSize;
            return new Vector2(p1x, p1y);
        }

        ObjectPool<List<SpriteObject>> mListPool;
    }
}