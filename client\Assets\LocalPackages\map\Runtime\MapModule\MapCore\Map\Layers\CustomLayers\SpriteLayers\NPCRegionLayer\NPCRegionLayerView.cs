﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class NPCRegionLayerView : MapLayerView
    {
        class Layer
        {
            public GameObject planeObject;

            public void OnDestroy()
            {
                if (planeObject != null)
                {
                    var meshRenderer = planeObject.GetComponent<MeshRenderer>();
                    var texture = meshRenderer.sharedMaterial.GetTexture("_MainTex") as Texture2D;
                    Object.DestroyImmediate(texture);
                    var mtl = meshRenderer.sharedMaterial;
                    Object.DestroyImmediate(mtl);
                }
                Object.DestroyImmediate(planeObject);
            }
        }

        public NPCRegionLayerView(MapLayerData layerData) : base(layerData, false)
        {
            var npcRegionLayerData = layerData as NPCRegionLayerData;
            var layers = npcRegionLayerData.layers;
            for (int k = 0; k < layers.Count; ++k)
            {
                AddLayer(k);
            }
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            for (int i = 0; i < mLayers.Count; ++i)
            {
                mLayers[i].OnDestroy();
            }
            mLayers = null;
            Object.DestroyImmediate(mMesh);
            mMesh = null;
        }

        Mesh CreateMesh(float mapWidth, float mapHeight)
        {
            if (mMesh == null)
            {
                mMesh = new Mesh();
                mMesh.vertices = new Vector3[]{
                    new Vector3(0, 0, 0),
                    new Vector3(0, 0, mapHeight),
                    new Vector3(mapWidth, 0, mapHeight),
                    new Vector3(mapWidth, 0, 0),
                };
                mMesh.uv = new Vector2[] {
                    new Vector2(0, 0),
                    new Vector2(0, 1),
                    new Vector2(1, 1),
                    new Vector2(1, 0),
                };
                mMesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            }
            return mMesh;
        }

        Material CreateMaterial()
        {
            var mtl = new Material(Shader.Find("SLGMaker/DiffuseTransparent"));
            mtl.renderQueue = 3200;
            return mtl;
        }

        public void RefreshTexture(int layerIndex)
        {
            var npcRegionLayerData = layerData as NPCRegionLayerData;
            NPCRegionLayerData.Layer dataLayer = npcRegionLayerData.GetLayer(layerIndex);
            int h = dataLayer.overridenHeight;
            int v = dataLayer.overridenWidth;
            Color32[] colors = new Color32[v * h];
            Color32 black = new Color32(0, 0, 0, 0);
            int idx = 0;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    var template = npcRegionLayerData.GetNPCRegionTemplateByCoord(layerIndex, j, i);
                    if (template != null)
                    {
                        colors[idx] = template.color;
                    }
                    else
                    {
                        colors[idx] = black;
                    }
                    ++idx;
                }
            }

            SetTexturePixels(layerIndex, 0, 0, h, v, colors);
        }

        public void SetTexturePixels(int layerIndex, int startX, int startY, int width, int height, Color32[] pixels)
        {
            var texture = GetTexture(layerIndex);
            texture.SetPixels32(startX, startY, width, height, pixels);
            texture.Apply();
        }

        public Texture2D GetTexture(int layerIndex)
        {
            var layer = mLayers[layerIndex];
            var meshRenderer = layer.planeObject.GetComponent<MeshRenderer>();
            var texture = meshRenderer.sharedMaterial.GetTexture("_MainTex") as Texture2D;
            return texture;
        }

        public void AddLayer(int layerIndex)
        {
            var layer = CreateLayer(layerIndex);
            mLayers.Add(layer);
        }

        public void RemoveLayer(int layerIndex)
        {
            mLayers[layerIndex].OnDestroy();
            mLayers.RemoveAt(layerIndex);
        }

        Layer CreateLayer(int layerIndex)
        {
            var npcRegionLayerData = layerData as NPCRegionLayerData;
            var layer = new Layer();
            NPCRegionLayerData.Layer dataLayer = npcRegionLayerData.GetLayer(layerIndex);
            //create region texture and materials
            int h = dataLayer.overridenHeight;
            int v = dataLayer.overridenWidth;
            Texture2D texture = new Texture2D(h, v, TextureFormat.RGBA32, false, false);
            Color32[] gridColors = new Color32[h * v];
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    int regionType = npcRegionLayerData.GetTileBandID(layerIndex, j, i);
                    var template = npcRegionLayerData.GetNPCRegionTemplateByBandID(layerIndex, regionType);
                    if (template != null)
                    {
                        gridColors[i * h + j] = template.color;
                    }
                }
            }
            texture.filterMode = FilterMode.Point;
            texture.SetPixels32(gridColors);
            texture.Apply();

            //create plane
            layer.planeObject = new GameObject("region plane");
            layer.planeObject.transform.SetParent(root.transform, false);
            Utils.HideGameObject(layer.planeObject);
            layer.planeObject.SetActive(false);
            var meshRenderer = layer.planeObject.AddComponent<MeshRenderer>();
            var meshFilter = layer.planeObject.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = CreateMesh(layerData.GetLayerWidthInMeter(), layerData.GetLayerHeightInMeter());
            meshRenderer.sharedMaterial = CreateMaterial();
            meshRenderer.sharedMaterial.SetTexture("_MainTex", texture);

            return layer;
        }

        public void Show(int layerIndex, bool visible)
        {
            if (layerIndex >= 0 && layerIndex < mLayers.Count)
            {
                mLayers[layerIndex].planeObject.SetActive(visible);
            }
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        List<Layer> mLayers = new List<Layer>();
        public Mesh mMesh;
    }
}
