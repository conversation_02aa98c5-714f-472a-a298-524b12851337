﻿ 



 
 


using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

namespace TFW.Map
{
    /// <summary>
    /// 基于格子来管理地图对象,每个格子有许多子对象,格子使用的是稀疏的数据结构,根据格子的索引来管理
    /// </summary>
    public class GridModelLayer2 : MapLayerBase
    {
        public GridModelLayer2(Map map) : base(map)
        {
        }

        //清理地图层
        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }

            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override IEnumerator LoadAsync(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var sourceLayer = layerData as config.GridModelLayerData2;
            if (sourceLayer == null)
            {
                yield break;
            }

            var size = sourceLayer.realLayerBounds.size;
            var rows = Mathf.CeilToInt(size.y / sourceLayer.tileHeight);
            var cols = Mathf.CeilToInt(size.x / sourceLayer.tileWidth);

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth,
                sourceLayer.tileHeight, sourceLayer.gridType, sourceLayer.origin + setting.origin);

            // 读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                var count = layerData.config.lodConfigs.Length;
                var lods = new MapLayerLODConfig.LOD[count];
                for (var i = 0; i < count; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold,
                        srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag,
                        srcLOD.terrainLODTileCount);
                }

                config = new MapLayerLODConfig(map, lods);
            }

            KeepScaleConfig keepScaleConfig0 = null;
            yield return MapModuleResourceMgr.LoadResourceAsync<KeepScaleConfig>(
                map.name == "MobaMap" ? $"{MapModule.configResDirectory}keep_decoration_size_moba.asset" : $"{MapModule.configResDirectory}keep_decoration_size.asset", null)
                .ContinueWith(x => keepScaleConfig0 = x)
                .ToCoroutine();
            
            mLayerData = new TileGridObjectLayerData2(header, config, map, sourceLayer.enableObjectMaterialChange);
            yield return mLayerData.LoadAsync(sourceLayer.realLayerBounds,
                new[] { keepScaleConfig0 }, async);

            mLayerView = new TileGridObjectLayerView2(mLayerData, true)
            {
                active = sourceLayer.active
            };
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            map.AddMapLayer(this);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var sourceLayer = layerData as config.GridModelLayerData2;
            if (sourceLayer == null)
            {
                return;
            }

            var size = sourceLayer.realLayerBounds.size;
            var rows = Mathf.CeilToInt(size.y / sourceLayer.tileHeight);
            var cols = Mathf.CeilToInt(size.x / sourceLayer.tileWidth);

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth,
                sourceLayer.tileHeight, sourceLayer.gridType, sourceLayer.origin + setting.origin);

            // 读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                var count = layerData.config.lodConfigs.Length;
                var lods = new MapLayerLODConfig.LOD[count];
                for (var i = 0; i < count; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold,
                        srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag,
                        srcLOD.terrainLODTileCount);
                }

                config = new MapLayerLODConfig(map, lods);
            }

            var keepScaleConfig0 = MapModuleResourceMgr.LoadResource<KeepScaleConfig>(map.name == "MobaMap" ? $"{MapModule.configResDirectory}keep_decoration_size_moba.asset" : $"{MapModule.configResDirectory}keep_decoration_size.asset");

            mLayerData = new TileGridObjectLayerData2(header, config, map, sourceLayer.realLayerBounds, new[] { keepScaleConfig0 }, async, sourceLayer.enableObjectMaterialChange);
            mLayerView = new TileGridObjectLayerView2(mLayerData, true);
            active = sourceLayer.active;
           
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);
            mLayerData.SetObjectTypeChangeCallback(mLayerView.OnObjectTypeChange);
            mLayerData.SetObjectMaterialChangeCallback(mLayerView.OnObjectMaterialChange);

            map.AddMapLayer(this);
        }

        public IMapObjectData GetObjectData(int objectID)
        {
            return mLayerData.GetObjectData(objectID);
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            return lodChanged;
        }

        //内部使用,加载完地图后刷新该层中可见的地图对象
        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        //从世界坐标转换到格子坐标
        public Vector2Int FromWorldPositionToCoordinate(Vector3 position)
        {
            return mLayerData.FromWorldPositionToCoordinate(position);
        }

        //从屏幕坐标转换到格子坐标
        public Vector2Int FromScreenToCoordinate(Vector3 screenPos, UnityEngine.Camera camera)
        {
            return mLayerData.FromScreenToCoordinate(screenPos, camera);
        }

        //从格子坐标转换到世界坐标
        public Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPosition(x, y);
        }

        //从格子坐标转换到格子的中心点的世界坐标
        public Vector3 FromCoordinateToWorldPositionCenter(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPositionCenter(x, y);
        }

        public Vector3 FromCoordinateToWorldPositionCenter(int x, int y, int width, int height)
        {
            return mLayerData.FromCoordinateToWorldPositionCenter(x, y, width, height);
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }

        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void UpdateObjectScale()
        {
            mLayerData.UpdateObjectScaleAtHeight();
        }

        public void OnShowNPC(long npcID, Vector3 center, float radius)
        {
            mLayerData.OnShowNPC(npcID, center, radius);
        }

        public void OnHideNPC(long npcID)
        {
            mLayerData.OnHideNPC(npcID);
        }

        //游戏运行时动态增加装饰物
        public int AddDynamicObject(List<string> prefabLODPaths, Vector3 position)
        {
            return mLayerData.AddDynamicObject(prefabLODPaths, position);
        }

        public bool HasDynamicObject(int objectID)
        {
            return mLayerData.HasDynamicObject(objectID);
        }

        //删除tag为removable且坐标在position的object
        public void RemoveObject(Vector3 position)
        {
            mLayerData.RemoveRemovableObject(position);
        }

        //还原tag为removable且坐标在position的object
        public void AddRemovableObject(Vector3 position)
        {
            mLayerData.AddRemovableObject(position);
        }

        public virtual bool AddObject(IMapObjectData objectData)
        {
            throw new System.NotImplementedException();
        }

        public virtual bool RemoveObject(int objectID)
        {
            throw new System.NotImplementedException();
        }

        //隐藏special area地板
        public void HideSpecialAreaObject(int areaID)
        {
            mLayerData.HideSpecialAreaObject(areaID);
        }

        //显示special area地板
        public void ShowSpecialAreaObject(int areaID)
        {
            mLayerData.ShowSpecialAreaObject(areaID);
        }

        //改变special area里的物体类型
        public void ChangeObjectsTypeInSpecialArea(int areaID, int state)
        {
            mLayerData.ChangeObjectsTypeInSpecialArea(areaID, state);
        }

        public void ChangeObjectsMaterialInSpecialArea(int areaID, int state)
        {
            mLayerData.ChangeObjectsMaterialInSpecialArea(areaID, state);
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public bool asyncLoading
        {
            set { mLayerView.asyncLoading = value; }
            get { return mLayerView.asyncLoading; }
        }

        public int objectCount
        {
            get { return mLayerData.objectCount; }
        }

        public override int horizontalTileCount
        {
            get { return mLayerData.horizontalTileCount; }
        }

        public override int verticalTileCount
        {
            get { return mLayerData.verticalTileCount; }
        }

        public override float tileWidth
        {
            get { return mLayerData.tileWidth; }
        }

        public override float tileHeight
        {
            get { return mLayerData.tileHeight; }
        }

        public override GridType gridType
        {
            get { return mLayerData.gridType; }
        }

        public override string name
        {
            get { return mLayerData?.name; }
            set
            {
                mLayerData.name = value;
            }
        }

        public override int id
        {
            get { return mLayerData.id; }
        }

        public override Vector3 layerOffset
        {
            get { return mLayerData.layerOffset; }
        }

        public override GameObject gameObject
        {
            get { return mLayerView.root; }
        }

        public override bool active
        {
            get { return layerView.active; }
            set { mLayerView.active = value; }
        }

        public override int lodCount => mLayerData.lodCount;

        public TileGridObjectLayerData2 layerData
        {
            get { return mLayerData; }
        }

        public TileGridObjectLayerView2 layerView
        {
            get { return mLayerView; }
        }

        //该层数据的管理
        TileGridObjectLayerData2 mLayerData;

        //该层视图的管理
        TileGridObjectLayerView2 mLayerView;
    }
}