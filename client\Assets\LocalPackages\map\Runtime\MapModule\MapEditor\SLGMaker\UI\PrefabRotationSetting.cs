﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class PrefabRotationSetting
    {
        public PrefabRotationSetting()
        {
            System.Reflection.FieldInfo info = typeof(EditorApplication).GetField("globalEventHandler", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic);
            EditorApplication.CallbackFunction value = (EditorApplication.CallbackFunction)info.GetValue(null);
            value += OnKeyPressed;
            info.SetValue(null, value);
        }

        public void OnDestroy()
        {
            System.Reflection.FieldInfo info = typeof(EditorApplication).GetField("globalEventHandler", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic);
            EditorApplication.CallbackFunction value = (EditorApplication.CallbackFunction)info.GetValue(null);
            value -= OnKeyPressed;
            info.SetValue(null, value);
        }

        void OnKeyPressed()
        {
            if (Event.current.type == EventType.KeyDown)
            {
                if (mKeyIsDown == false)
                {
                    mKeyIsDown = true;
                    mPressedKey = Event.current.keyCode;
                }
            }
            else if (Event.current.type == EventType.KeyUp)
            {
                mKeyIsDown = false;
            }
        }

        public void AddRotation(float d)
        {
            mCurrentRotationY += d;
            mCurrentRotationY = EditorUtils.ClampRotation(mCurrentRotationY);
        }

        public bool Update()
        {
            bool processed = false;
            if (mPressedKey == KeyCode.LeftBracket)
            {
                AddRotation(-mRotationStep);
                processed = true;
            }
            else if (mPressedKey == KeyCode.RightBracket)
            {
                AddRotation(mRotationStep);
                processed = true;
            }
            mPressedKey = KeyCode.None;
            return processed;
        }

        public void Draw()
        {
            mRandomYRotation = EditorGUILayout.ToggleLeft("Random Y Rotation", mRandomYRotation);
            if (!mRandomYRotation)
            {
                mCurrentRotationY = EditorGUILayout.FloatField("Object Rotation", mCurrentRotationY);
                mCurrentRotationY = EditorUtils.ClampRotation(mCurrentRotationY);
                mRotationStep = EditorGUILayout.FloatField("Object Rotation Step", mRotationStep);
                EditorGUILayout.LabelField("Use [ and ] keys to modify object rotation");
            }
        }

        public float yRotation 
        { 
            get 
            { 
                if (mRandomYRotation)
                {
                    if (!mRotationValid)
                    {
                        mRotationValid = true;
                        mRandomRotationValue = Quaternion.Euler(0, Random.Range(0.0f, 360.0f), 0);
                    }
                    return mRandomRotationValue.eulerAngles.y;
                } 
                return mCurrentRotationY; 
            }
        }
        public Quaternion rotation
        {
            get
            {
                if (mRandomYRotation)
                {
                    if (!mRotationValid)
                    {
                        mRotationValid = true;
                        mRandomRotationValue = Quaternion.Euler(0, Random.Range(0.0f, 360.0f), 0);
                    }
                    return mRandomRotationValue;
                }
                return Quaternion.Euler(0, mCurrentRotationY, 0);
            }
            set
            {
                mCurrentRotationY = value.eulerAngles.y;
            }
        }
        public void StepToNextRandomRotation()
        {
            mRotationValid = false;
        }
        public float rotationStep { set { mRotationStep = value; } get { return mRotationStep; } }
        public bool randomYRotation { get { return mRandomYRotation; } set { mRandomYRotation = value; } }

        float mCurrentRotationY;
        float mRotationStep = 5;
        KeyCode mPressedKey;
        bool mKeyIsDown = false;
        bool mRandomYRotation = false;
        bool mRotationValid = false;
        Quaternion mRandomRotationValue;
    }
}
#endif