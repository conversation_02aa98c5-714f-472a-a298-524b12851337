﻿ 



 
 

using System.Collections.Generic;

namespace TFW.Map
{
    //管理一个BuildingLODControl可能碰撞到的建筑物
    public class BuildingLODControlCollisionManager
    {
        //keepRecordings: 一旦记录完成,列表是否就永远固定了,适用于ResourceBuildingLODControl
        public BuildingLODControlCollisionManager(bool keepRecordings)
        {
            mKeepRecordings = keepRecordings;
            mFinishedRecording = false;
            mReachHigher = false;
            mReachLower = false;
        }

        public void ReachLowerHeight()
        {
            mReachLower = true;
            if (mReachHigher)
            {
                mFinishedRecording = true;
            }
        }

        public void ReachHigherHeight()
        {
            mReachHigher = true;
            if (mReachLower)
            {
                mFinishedRecording = true;
            }
        }

        public void ClearCollisionList()
        {
            if (!mKeepRecordings)
            {
                mRecordedCollisions = null;
                mFinishedRecording = false;
                mReachHigher = false;
                mReachLower = false;
            }
        }

        public void AddCollision(IBuildingElement building)
        {
            if (!mFinishedRecording)
            {
                if (mRecordedCollisions == null)
                {
                    mRecordedCollisions = new List<IBuildingElement>();
                }
                if (mRecordedCollisions.Contains(building) == false)
                {
                    mRecordedCollisions.Add(building);
                }
            }
        }

        public List<IBuildingElement> GetCollisionList()
        {
            if (mFinishedRecording)
            {
                return mRecordedCollisions;
            }
            return null;
        }

        List<IBuildingElement> mRecordedCollisions;
        bool mKeepRecordings;
        bool mFinishedRecording;
        bool mReachHigher;
        bool mReachLower;
    }
}
