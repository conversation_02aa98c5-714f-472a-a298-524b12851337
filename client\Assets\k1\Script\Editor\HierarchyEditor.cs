﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;
using System.Linq;
using TFW.UI;

namespace K1
{
    /// <summary>
    /// Hierarchy的编辑器扩展
    /// </summary>
    public class HierarchyEditor
    {
        [InitializeOnLoadMethod]
        static void InitializeOnLoadMethod()
        {
            EditorApplication.hierarchyWindowItemOnGUI += HierarchyWindowOnGUI;
        }
        static public GenericMenu GetTypeMenu(GameObject m_Target, bool depth = false)
        {
            Component[] components = m_Target.GetComponents<Component>();
            GenericMenu menu = new GenericMenu();


            Type t = m_Target.GetType();//.Substring(type.ToString().IndexOf(".") + 1);
            var name = t.ToString();
            if (depth)
            {
                name = name.Replace('.', '/');
                menu.AddItem(new GUIContent(name, t.ToString()), true, Callback, name);
            }
            else
            {
                menu.AddItem(new GUIContent(name), true, Callback, name);
            }

            t = typeof(Transform);//.Substring(type.ToString().IndexOf(".") + 1);
            name = t.ToString();
            if (depth)
            {
                name = name.Replace('.', '/');
                menu.AddItem(new GUIContent(name, t.ToString()), false, Callback, name);
            }
            else
            {
                menu.AddItem(new GUIContent(name), false, Callback, name);
            }


            foreach (Component type in components)
            {
                name = type.ToString();//.Substring(type.ToString().IndexOf(".") + 1);
                if (depth)
                {
                    name = name.Replace('.', '/');
                    menu.AddItem(new GUIContent(name, type.ToString()), false, Callback, name);
                }
                else
                {
                    menu.AddItem(new GUIContent(name), type is TFWImage, Callback, name);
                }
            }

            return menu;
        }

        static public GenericMenu GetTypeMenu(Type p_type)
        {
            GenericMenu menu = new GenericMenu();

            Type[] types = GetAllTypes(p_type).ToArray();
            Array.Sort(types, (t1, t2) => t1.ToString().CompareTo(t2.ToString()));

            foreach (Type type in types)
            {
                string name = type.ToString();//.Substring(type.ToString().IndexOf(".") + 1);
                name = name.Replace('.', '/');
                //name = name.Substring(0, name.Length-4);


                menu.AddItem(new GUIContent(name, type.ToString()), false, Callback, name);
            }

            return menu;
        }
        public static List<Type> GetAllTypes(Type p_type)
        {
            return AppDomain.CurrentDomain.GetAssemblies().SelectMany(x => x.GetTypes())
            .Where(x => p_type.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract).ToList();
        }

        static void Callback(object obj)
        {
            Debug.Log("Callback===" + obj);
        }

        static void HierarchyWindowOnGUI(int instanceID, Rect selectionRect)
        {
            //Toggle控制Hierarchy上的对象显隐
            Rect rect = new Rect(selectionRect);
            rect.x += (selectionRect.width - 30f);
            rect.width = 15f;

            GameObject go = EditorUtility.InstanceIDToObject(instanceID) as GameObject;
            if (go == null) return;
            bool newActive = go.activeSelf;
            bool active = GUI.Toggle(rect, go.activeSelf, string.Empty);

            if (newActive != active)
            {
                newActive = active;
                go.SetActive(newActive);
                EditorUtility.SetDirty(go);
            }

            //if (Selection.activeGameObject && instanceID == Selection.activeGameObject.GetInstanceID())
            //{
            //    var uiRefRoot = Selection.activeGameObject.GetComponentInParent<UIRefRoot>();
            //    if (uiRefRoot)
            //    {
            //        float width = 15f;
            //        float height = 15f;
            //        rect.x -= (width);
            //        rect.width = width;
            //        rect.height = height;


            //        Texture icon = EditorGUIUtility.IconContent("icons/d_ViewToolZoom.png").image;
            //        if (GUI.Button(rect, icon))// AssetDatabase.LoadAssetAtPath<Texture>("Assets/unity.png")
            //        {
            //            var menu = GetTypeMenu(Selection.activeGameObject);
            //            menu.ShowAsContext();
            //        }
            //    }
            //}
        }
    }
}
