﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class SplineEditorUI : UnityEditor.Editor
    {
        public void HandleMoveSplineFunction(Event e, Vector3 worldPos)
        {
            if ((e.type == EventType.MouseDown || e.type == EventType.MouseDrag) && e.button == 0 && e.alt == false)
            {
                if (e.type == EventType.MouseDown)
                {
                    PickSpline(worldPos);
                }
                MoveSpline(worldPos);
                Repaint();
                SceneView.RepaintAll();
            }

            HandleUtility.AddDefaultControl(0);
        }

        void MoveSpline(Vector3 worldPos)
        {
            if (mSelectedSplineObjectID > 0)
            {
                mMover.Update(worldPos);

                var delta = mMover.GetDelta();
                if (delta != Vector3.zero)
                {
                    var spline = mEditor.splineObjectManager.FindSplineObject(mSelectedSplineObjectID);
                    spline.Move(delta);
                    if (mSelectedControlPointIndex >= 0)
                    {
                        spline.ShowTangent(mSelectedControlPointIndex);
                    }
                    UpdateDisplay(spline);
                }
            }
        }

        void DrawMoveSplineFunctionSceneGUI()
        {
        }

        void DrawMoveSplineFunctionInspectorGUI()
        {
        }
    }
}

#endif