﻿using System;
using System.Collections.Generic;

using Sirenix.OdinInspector;

using UnityEngine;

namespace Logic
{
    [DisallowMultipleComponent]
    [RequireComponent(typeof(Camera))]
    public class GameQualityControlledCamera : MonoBehaviour
    {
        /// <summary>
        /// 是否为UI相机
        /// </summary>
        [SerializeField]
        public bool isUiCamera;

        private void Start()
        {
            var camera = GetComponent<Camera>();

            if (camera != null)
            {
                GameQualityMgr.I.SetupCamera(camera, isUiCamera);
            }
        }
    }
}