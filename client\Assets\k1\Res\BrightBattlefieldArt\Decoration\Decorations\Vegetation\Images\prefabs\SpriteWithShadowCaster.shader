﻿Shader "Sprites/SpriteWithShadowCaster"
{
    Properties
    {
        [PerRendererData]_MainTex ("Texture", 2D) = "white" {}
		_Cutoff("Alpha cutoff", Range(0, 1)) = 0.2
    	[Toggle] DayNightToggle("Day Night Toggle", Float) = 0
        _x("x",float) = 0.2
        _y("y",float) = 0.2
        _z("z",float) = 0.2
    }
    SubShader
    {
        Tags { "Queue" = "AlphaTest"}
        LOD 200

        Pass
        {
		    ZWrite On ZTest On
			Blend SrcAlpha OneMinusSrcAlpha

            CGPROGRAM
            #pragma multi_compile _ DAYNIGHTTOGGLE_GLOBAL_ON
			#pragma multi_compile_local _ DAYNIGHTTOGGLE_ON
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"
			#include "Assets/k1/Res/Shader/CG/DayNightSystemShaderHelper.cginc"
			
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
			half _Cutoff;
            float _x;
            float _y;
            float _z;
            v2f vert (appdata v)
            {
                v2f o;
                float4 worldPos = mul(UNITY_MATRIX_M, v.vertex);
                float dif = _x * snoise(float2(_y * _SinTime.x, worldPos.z)) * step(_z, worldPos.y);
                v.vertex += float4(dif, dif / _y, 0, 0);
                o.vertex = UnityObjectToClipPos(v.vertex);

              /*  float4 worldPos = mul(UNITY_MATRIX_M, v.vertex);
                float dif = _x * snoise(float2(_y * _SinTime.x, worldPos.z)) * step(_z, worldPos.y);
                worldPos += float4(dif, dif / _y, 0, 0);
                o.vertex = mul(UNITY_MATRIX_VP, worldPos);*/
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);

				clip(col.a - _Cutoff);
				
				#ifdef DAYNIGHTTOGGLE_GLOBAL_ON
        	        #ifdef DAYNIGHTTOGGLE_ON
                        col.rgb = ApplyDayNightLut(col.rgb);
			        #endif
		        #endif
		        
                return col;
            }
            ENDCG
        }
		Pass {
			Tags { "LightMode" = "ShadowCaster" }

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag

			#pragma multi_compile_shadowcaster

			#include "UnityCG.cginc"
            #include "Assets/k1/Res/Shader/CG/DayNightSystemShaderHelper.cginc"
            sampler2D _MainTex;
            float4 _MainTex_ST;
            half _Cutoff;
            float _x;
            float _y;
            float _z;
			struct v2f {
                float2 uv : TEXCOORD1;
				V2F_SHADOW_CASTER;
			};
           
			v2f vert(appdata_base v) {
				v2f o;
                float4 worldPos = mul(UNITY_MATRIX_M, v.vertex);
                float dif = _x * snoise(float2(_y * _SinTime.x, worldPos.z)) * step(_z, worldPos.y);
                v.vertex += float4(dif, dif / _y, 0, 0);
                o.uv = TRANSFORM_TEX(v.texcoord, _MainTex);
				TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
				return o;
			}

			fixed4 frag(v2f i) : SV_Target {
				half4 col = tex2D(_MainTex, i.uv);
                clip(col.a - _Cutoff);
				SHADOW_CASTER_FRAGMENT(i)
			}
			ENDCG
		}
    }
}
