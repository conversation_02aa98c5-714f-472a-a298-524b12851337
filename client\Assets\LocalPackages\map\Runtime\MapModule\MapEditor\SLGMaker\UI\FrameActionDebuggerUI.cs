﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    [CustomEditor(typeof(FrameActionDebugger))]
    public class FrameActionDegggerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mDebugger = target as FrameActionDebugger;
            mDebugger.RefreshEvent += Repaint;
        }

        void OnDisable()
        {
            mDebugger.RefreshEvent -= Repaint;
        }

        void OnSceneGUI()
        {
        }

        public override void OnInspectorGUI()
        {
            var frameActionManager = Map.currentMap.frameActionManager;
            int queueCount = frameActionManager.actionQueueCount;
            if (mDebugger.queueFoldState == null || mDebugger.queueFoldState.Length != queueCount)
            {
                mDebugger.queueFoldState = new bool[queueCount];
            }

            EditorGUILayout.BeginVertical();
            for (int i = 0; i < queueCount; ++i)
            {
                var queue = frameActionManager.GetActionQueue(i);
                DrawActionQueue(i, queue);
            }
            EditorGUILayout.EndVertical();

            if (GUILayout.Button("Pause All"))
            {
                int n = frameActionManager.actionQueueCount;
                for (int i = 0; i < n; ++i)
                {
                    frameActionManager.PauseQueue(i, true);
                }
            }
        }

        void DrawActionQueue(int i, FrameActionQueue queue)
        {
            EditorGUILayout.BeginHorizontal();
            int n = queue.frameActions.Count;
            mDebugger.queueFoldState[i] = EditorGUILayout.Foldout(mDebugger.queueFoldState[i], string.Format("{0}, {1}", queue.debugName, n));

            bool paused = Map.currentMap.frameActionManager.IsQueuePaused(i);
            if (GUILayout.Button(paused ? "Run" : "Pause"))
            {
                Map.currentMap.frameActionManager.PauseQueue(i, !paused);
            }
            if (GUILayout.Button("Execute"))
            {
                Map.currentMap.frameActionManager.UpdateQueue(i);
            }
            EditorGUILayout.EndHorizontal();
            if (mDebugger.queueFoldState[i])
            {
                var actions = queue.frameActions;
                for (var p = actions.First; p != null; p = p.Next)
                {
                    DrawFrameAction(p.Value);
                }
            }
        }

        void DrawFrameAction(FrameAction act)
        {
            EditorGUILayout.LabelField(string.Format("{0}, {1}", act.name, act.debugInfo));
        }

        FrameActionDebugger mDebugger;
    }
}

#endif