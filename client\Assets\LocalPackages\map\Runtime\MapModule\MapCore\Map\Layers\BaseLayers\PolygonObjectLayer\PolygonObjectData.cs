﻿ 



 
 

//created by wzw at 2019/11/25

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public abstract class PolygonObjectData : BaseObject, IMapObjectData, IObstacle
    {
        public PolygonObjectData(int id, Map map, OutlineData[] outlines, float displayRadius, bool isExtendable, bool checkWindingOrder) : base(id, map)
        {
            Debug.Assert(outlines != null && outlines.Length == 2);

            if (outlines[0] == null)
            {
                outlines[0] = outlines[1].Clone();
            }
            else if (outlines[1] == null)
            {
                outlines[1] = outlines[0].Clone();
            }

            mIsExtendable = isExtendable;
            mDisplayRadius = displayRadius;
            mColor = Color.white;
            mOverridenColor = Color.white;
            mUseOverridenColor = false;
            mOutlines = new OutlineData[2];
            for (int i = 0; i < mOutlines.Length; ++i)
            {
                mOutlines[i] = outlines[i].Clone();
            }

            if (checkWindingOrder)
            {
                //make polygons cw winding order
                for (int i = 0; i < mOutlines.Length; ++i)
                {
                    if (!Utils.IsPolygonCW(mOutlines[i].outline))
                    {
                        Utils.ReverseList(mOutlines[i].outline);
                    }
                }
            }

            RecalculateShapeInfo(mOutlines[(int)PrefabOutlineType.NavMeshObstacle]);
            RecalculateShapeInfo(mOutlines[(int)PrefabOutlineType.ObjectPlacementObstacle]);
        }

        public override void OnDestroy()
        {
        }

        public bool IsDynamicEntity() { return false; }
        public int GetEntityID() { return id; }
        public ModelTemplate GetModelTemplate() { return null; }
        public int GetModelTemplateID() { return 0; }
        public void OnHide() { }
        public void OnShow() { }
        public void OnInit(GameObject obj) { }
        public void OnZoomChange(float zoom) { }
        public bool IgnoreViewport() { return false; }
        public Rect GetOutlineBounds(PrefabOutlineType type)
        {
            return mOutlines[(int)type].bounds;
        }
        public Rect GetBounds()
        {
            return GetOutlineBounds(PrefabOutlineType.NavMeshObstacle);
        }

        public void SetPosition(Vector3 pos) { }
        public void SetRotation(Quaternion rot) { }
        public void SetScale(Vector3 scale) { }

        public virtual Vector3 GetPosition() { return Vector3.zero; }
        public Vector3 GetScale() { return Vector3.one; }
        public Quaternion GetRotation() { return Quaternion.identity; }

        public string GetAssetPath(int lod) { return ""; }

        public bool IsObjActive() { return mIsActive; }
        public bool SetObjActive(bool active)
        {
            if (mIsActive != active)
            {
                mIsActive = active;
                return true;
            }
            return false;
        }

        public bool HasFlag(int flag)
        {
            return HasObjectFlag((ObjectFlag)flag);
        }

        public void SetFlag(int flag)
        {
            SetObjectFlag((ObjectFlag)flag);
        }
        public void AddFlag(int flag)
        {
            AddObjectFlag((ObjectFlag)flag);
        }
        public void RemoveFlag(int flag)
        {
            RemoveObjectFlag((ObjectFlag)flag);
        }
        public int GetFlag()
        {
            return (int)flag;
        }
        public int GetModelLODGroupID()
        {
            return 0;
        }

        public bool IsSimplePolygon(PrefabOutlineType type)
        {
            return mOutlines[(int)type].isSimplePolygon;
        }

        public Vector3 GetVertexPos(PrefabOutlineType type, int index)
        {
            return mOutlines[(int)type].GetVertexPos(index);
        }

        public int GetVertexIndex(PrefabOutlineType type, Vector3 pos)
        {
            return mOutlines[(int)type].GetVertexIndex(pos);
        }

        public List<Vector3> GetOutlineVertices(PrefabOutlineType type)
        {
            return mOutlines[(int)type].outline;
        }

        public List<Vector3> GetOutlineVerticesCopy(PrefabOutlineType type)
        {
            var copy = new List<Vector3>(mOutlines[(int)type].outline.Count);
            copy.AddRange(mOutlines[(int)type].outline);
            return copy;
        }

        public Vector3[] GetWorldSpaceOutlineVertices(PrefabOutlineType type)
        {
            return GetOutlineVertices(type).ToArray();
        }

        public GameObject gameObject { get { return null; } }
        public Vector3 offset { set; get; }

        public void AddVertex(PrefabOutlineType type, Vector3 pos)
        {
            mOutlines[(int)type].outline.Add(pos);

            RecalculateShapeInfo(mOutlines[(int)type]);
        }

        public void InsertVertex(PrefabOutlineType type, int index, Vector3 pos)
        {
            mOutlines[(int)type].outline.Insert(index, pos);

            RecalculateShapeInfo(mOutlines[(int)type]);
        }

        public void RemoveVertex(PrefabOutlineType type, int index)
        {
            mOutlines[(int)type].outline.RemoveAt(index);

            RecalculateShapeInfo(mOutlines[(int)type]);
        }

        public virtual void SetVertexPosition(PrefabOutlineType type, int idx, Vector3 pos)
        {
            int n = mOutlines[(int)type].outline.Count;
            if (idx >= 0 && idx < n)
            {
                mOutlines[(int)type].outline[idx] = pos;

                RecalculateShapeInfo(mOutlines[(int)type]);
            }
        }

        public void ExpandOutline(PrefabOutlineType type, float radius)
        {
#if UNITY_EDITOR
            if (radius > 0)
            {
                int idx = (int)type;
                var expandedVertices = PolygonAlgorithm.ExpandPolygon(radius, mOutlines[idx].outline)[0];
                List<Vector3> finalVertices = new List<Vector3>();
                for (int i = 0; i < expandedVertices.Count; ++i)
                {
                    finalVertices.Add(expandedVertices[i]);
                }

                mOutlines[idx].outline = finalVertices;
                mOutlines[idx].CalculateBounds();
            }
#endif
        }

        public void ClearOutline(PrefabOutlineType type)
        {
            int idx = (int)type;
            mOutlines[idx].Clear();
        }

        public void SetOutline(PrefabOutlineType type, List<Vector3> vertices)
        {
            int idx = (int)type;
            mOutlines[idx] = new OutlineData(vertices);
        }

        public virtual void Move(PrefabOutlineType type, Vector3 offset)
        {
            int n = mOutlines[(int)type].outline.Count;
            for (int i = 0; i < n; ++i)
            {
                mOutlines[(int)type].outline[i] += offset;
            }
            RecalculateShapeInfo(mOutlines[(int)type]);
        }

        public bool IsExtendable()
        {
            return mIsExtendable;
        }

        protected void RecalculateShapeInfo(OutlineData outline)
        {
            outline.isConvex = PolygonUtils.IsConvexPolygon(outline.outline);
            outline.CalculateBounds();
            outline.isSimplePolygon = PolygonUtils.IsSimplePolygon(outline.outline);
        }

        public bool IsConvex(PrefabOutlineType type)
        {
            return mOutlines[(int)type].isConvex;
        }

        public void Revert(PrefabOutlineType type)
        {
            Utils.ReverseList(mOutlines[(int)type].outline);
        }

        public float displayRadius { set { mDisplayRadius = value; } get { return mDisplayRadius; } }
        public bool isSelected { get { return mIsSelected; } set { mIsSelected = value; } }
        public bool hitObstacles { get { return mHitObstacles; } set { mHitObstacles = value; } }
        public OutlineData[] outlines { get { return mOutlines; } }
        public bool useOverridenColor { set { mUseOverridenColor = value; } get { return mUseOverridenColor; } }
        public Color overridenColor { set { mOverridenColor = value; } get { return mOverridenColor; } }
        public Color color { get { return mColor; } set { mColor = value; } }
        public Color activeColor
        {
            get
            {
                if (mUseOverridenColor)
                {
                    return mOverridenColor;
                }
                return mColor;
            }
        }

        protected OutlineData[] mOutlines;
        protected float mDisplayRadius;
        protected bool mIsExtendable;
        protected bool mIsSelected = false;
        protected bool mHitObstacles = false;
        protected bool mIsActive = false;
        protected Color mColor;
        Color mOverridenColor;
        bool mUseOverridenColor;
    }
}