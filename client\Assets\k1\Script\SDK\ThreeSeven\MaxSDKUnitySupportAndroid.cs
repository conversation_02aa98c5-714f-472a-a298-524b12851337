
using UnityEngine;
using System.Collections.Generic;
using Newtonsoft.Json;


/**
 *   unity调用Android中的方法
 *   通过反射拿到unity的activity对象，在activity对象类，通过反射调用对应的方法
 */
namespace maxsdk
{

    //#if UNITY_ANDROID && !UNITY_EDITOR

    public class MaxSDKUnitySupportAndroid : MaxSDKUnitySupportBase
    {

        private AndroidJavaObject unityActivity;

        public MaxSDKUnitySupportAndroid()
        {
            AndroidJavaClass ac = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            unityActivity = ac.GetStatic<AndroidJavaObject>("currentActivity");
        }

        public override int GetSDKType()
        {
            int type = unityActivity.Call<int>("getSDKType");
            return type;
        }

        public override string GetAppConfig()
        {
            return unityActivity.Call<string>("getAppConfig");
        }

        public override void SetListener(MaxSDKListener listener)
        {
            Debug.Log("gameObject is " + listener.gameObject.name);
            if (listener == null)
            {
                Debug.LogError("set SQSDKListener error, listener is null");
                return;
            }
            string gameObjectName = listener.gameObject.name;
            if (unityActivity == null)
            {
                Debug.LogError("setListener error, current activity is null");
            }
            else
            {
                unityActivity.Call("setGameObjectName", gameObjectName);
            }
        }

        public override void Init(MaxSDKInitInfo info)
        {
            unityActivity.Call("init", info == null ? "" : JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void Login(MaxSDKLoginInfo info)
        {
            unityActivity.Call("login", info == null ? "" : JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void Logout(MaxSDKLogoutInfo info)
        {
            unityActivity.Call("logout", info == null ? "" : JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void SwitchAccount(MaxSDKSwitchAccountInfo info)
        {
            if (info == null)
            {
                Debug.LogError("调用【切换帐号】失败，info为空");
                return;
            }
            unityActivity.Call("switchAccount", JsonConvert.SerializeObject(info, Formatting.Indented));

        }

        public override void Pay(MaxSDKPayInfo info)
        {
            if (info == null)
            {
                Debug.LogError("调用支付失败，订单信息为空");
                return;
            }
            unityActivity.Call("pay", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void Share(MaxSDKShareInfo info)
        {

            if (info == null)
            {
                Debug.LogError("调用分享失败，分享信息为空");
                return;
            }

            unityActivity.Call("share", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void ReportRoleInfo(MaxSDKRoleInfo info)
        {

            if (info == null)
            {
                Debug.LogError("调用【上报游戏行为】失败，info为空");
                return;
            }
            unityActivity.Call("reportRoleInfo", JsonConvert.SerializeObject(info, Formatting.Indented));

        }
      

        public override void ReportEvent(MaxSDKReportEventInfo info)
        {
            if (info == null)
            {
                Debug.LogError("调用【上报自定义埋点事件】失败，info为空");
                return;
            }
            unityActivity.Call("reportEvent", JsonConvert.SerializeObject(info, Formatting.Indented));

        }


        public override bool IsActionSupported(int type)
        {
            return unityActivity.Call<bool>("isActionSupported", (int)type);
        }


        public override void OpenActionExt(MaxSDKActionInfo info)
        {

            if (info == null || info.type <= 0)
            {
                Debug.LogError("调用【用户行为扩展接口】失败，info为空或者type未设置");
                return;
            }
            unityActivity.Call("openActionExt", JsonConvert.SerializeObject(info, Formatting.Indented));
        }


        public override void ExitGame(MaxSDKExitInfo info)
        {
            if (info == null)
            {
                Debug.LogError("调用【退出游戏】失败，info为空");
                return;
            }
            unityActivity.Call("exitGame", JsonConvert.SerializeObject(info, Formatting.Indented));

        }


        /**
        * 扩展接口（同步），支持返回值
        */
        public override string DispatchSync(MaxSDKDispatchInfo dispatchInfo)
        {
            if (dispatchInfo == null)
            {
                Debug.LogError("【同步】扩展方法接口调用失败，必要信息为空");
                return "";
            }
            return unityActivity.Call<string>("dispatchSync", JsonConvert.SerializeObject(dispatchInfo, Formatting.Indented));
        }


        /**
         * 扩展接口（异步），带统一回调方法  OnDispatchResult(MaxSDKDispatchBean bean)
         */
        public override void DispatchASync(MaxSDKDispatchInfo dispatchInfo)
        {
            if (dispatchInfo == null)
            {
                Debug.LogError("【异步】扩展方法接口调用失败，必要信息为空");
            }
            unityActivity.Call("dispatchASync", JsonConvert.SerializeObject(dispatchInfo, Formatting.Indented));
        }

    }
    //#endif
}

