﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //测试火山的生成
    public static class MapCircleBorderCreator
    {
        public static void Create(List<string> slicePrefabs, Vector3 pos, ModelLayer layer)
        {
#if UNITY_EDITOR
            var map = Map.currentMap;
            for (int i = 0; i < slicePrefabs.Count; ++i)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(slicePrefabs[i]);
                var bounds = GameObjectBoundsCalculator.CalculateRect(prefab);
                map.AddModelTemplate(slicePrefabs[i], Map.currentMap, bounds, false, null, null, null);
            }

            float deltaAngle = 360.0f / slicePrefabs.Count;

            for (int i = 0; i < slicePrefabs.Count; ++i)
            {
                var modelTemplate = map.FindModelTemplate(slicePrefabs[i]);
                var circleBorder = new CircleBorderData(map.nextCustomObjectID, map, 0, pos, Quaternion.Euler(new Vector3(0, i * deltaAngle, 0)), Vector3.one, modelTemplate, i == 0 ? true : false);
                layer.AddObject(circleBorder);
            }
#endif
        }
    }
}
