﻿ 



 
 


//create by wzw at 2019.7.24

using UnityEngine;
using System.Collections.Generic;


#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //拼接地表层使用的数据
    public partial class RenderTextureBlendTerrainLayerData : BlendTerrainLayerData
    {
        public RenderTextureBlendTerrainLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, BlendTerrainTileData[] lod0Tiles, List<ModelData[,]> otherLODTiles, TerrainRenderTextureLODSetting[] lodSettings, bool useGeneratedLOD, bool useDecorationObject, string combinedTexturePropertyName, string groundTileAtlasSettingGuid, bool generateMeshCollider, bool getGroundHeightInGame, bool optimizeMesh) : base(header, config, map, lod0Tiles, combinedTexturePropertyName, groundTileAtlasSettingGuid, generateMeshCollider, getGroundHeightInGame, optimizeMesh)
        {
            mOtherLODTiles = otherLODTiles;
            mLODSettings = lodSettings;
            this.useGeneratedLOD = useGeneratedLOD;
            this.useDecorationObject = useDecorationObject;

            //check lod settings
            if (lodSettings != null)
            {
                mDefaultLODCount = config.lodConfigs.Length - lodSettings.Length;
            }
            else
            {
                mDefaultLODCount = config.lodConfigs.Length;
            }

            Debug.Assert(mDefaultLODCount > 0, "Invalid ground layer LOD Setting!!!");

            //temp code
            //mDebugger = new GameObject("RenderTextureBlendTerrainLayerDataDebug");
            //var test = mDebugger.AddComponent<RenderTextureBlendTerrainLayerDataDebug>();
            //test.layerData = this;
        }

        public override void OnDestroy()
        {
            base.OnDestroy();

            if (mOtherLODTiles != null)
            {
                for (int i = 0; i < mOtherLODTiles.Count; ++i)
                {
                    if (mOtherLODTiles[i] != null)
                    {
                        int r = mOtherLODTiles[i].GetLength(0);
                        int c = mOtherLODTiles[i].GetLength(1);

                        for (int k = 0; k < r; ++k)
                        {
                            for (int j = 0; j < c; ++j)
                            {
                                map.DestroyObject(mOtherLODTiles[i][k, j]);
                            }
                        }
                    }
                }
                mOtherLODTiles = null;
            }
        }

        public override void RefreshObjectsInViewport()
        {
            mLastViewport = new Rect(-10000, -10000, 0, 0);
            var newViewport = map.viewport;
            var oldViewRect = GetViewRect(mLastViewport);
            var newViewRect = GetViewRect(newViewport);
            UpdateViewRect(oldViewRect, newViewRect, mCurrentLOD);
            mLastViewport = newViewport;
        }

        protected override void OnAddObjectData(IMapObjectData data)
        {
            var coord = FromWorldPositionToCoordinate(data.GetPosition());
            var idx = coord.y * mCols + coord.x;
            mTiles[idx] = data as BlendTerrainTileData;
            //这个tile是否在视野内
            bool isVisible = false;
            if (!isLoading)
            {
                isVisible = IsInViewRange(coord.x, coord.y, map.viewport);
            }
            data.SetObjActive(isVisible);
        }

        void SetObjectActive(IMapObjectData objectData, int x, int y, bool active, int lod)
        {
            if (objectData != null)
            {
                objectData.SetObjActive(active);
                mOnSetActive(objectData, x, y, lod);
            }
        }

        public void SetTerrainObjectActiveCallback(System.Action<IMapObjectData, int, int, int> onSetActive)
        {
            mOnSetActive = onSetActive;
        }

        bool UpdateViewRect(Rect2D oldViewRect, Rect2D newViewRect, int currentLOD)
        {
            if (oldViewRect != newViewRect)
            {
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (!newViewRect.Contains(j, i))
                        {
                            //物体不在新的视野中,隐藏它
                            SetObjectActive(GetTile(j, i), j, i, false, currentLOD);
                        }
                    }
                }

                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (!oldViewRect.Contains(j, i))
                        {
                            //物体不在旧的视野中,显示它
                            SetObjectActive(GetTile(j, i), j, i, true, currentLOD);
                        }
                    }
                }

                return true;
            }

            return false;
        }

        public void UpdateViewport(Rect newViewport)
        {
            if (mLastViewport != newViewport)
            {
                if (mCurrentLOD < mDefaultLODCount || mLODSettings == null)
                {
                    if (map.isEditorMode)
                    {
                        if (mLastViewport.width == 0)
                        {
                            mLastViewport = newViewport;
                        }
                    }
                    var oldViewRect = GetViewRect(mLastViewport);
                    var newViewRect = GetViewRect(newViewport);
                    UpdateViewRect(oldViewRect, newViewRect, mCurrentLOD);
                }
                else
                {
                    //使用合并后的tile
                    int localLOD = mCurrentLOD - mDefaultLODCount;
                    var oldViewRect = GetLODTileViewRect(mLastViewport, mLODSettings[localLOD].realBlockSize);
                    var newViewRect = GetLODTileViewRect(newViewport, mLODSettings[localLOD].realBlockSize);

                    UpdateLODTileViewRect(oldViewRect, newViewRect, mCurrentLOD);
                }
            }

            mLastViewport = newViewport;
        }

        IMapObjectData GetLODTile(int lod, int x, int y)
        {
            if (lod < mDefaultLODCount || mLODSettings == null)
            {
                return GetTile(x, y);
            }
            return mOtherLODTiles[lod - mDefaultLODCount][y, x];
        }

        Vector2Int GetLODDimension(int lod)
        {
            if (lod < mDefaultLODCount || mLODSettings == null)
            {
                return new Vector2Int(mCols, mRows);
            }
            return new Vector2Int(mOtherLODTiles[lod - mDefaultLODCount].GetLength(1), mOtherLODTiles[lod - mDefaultLODCount].GetLength(0));
        }

        public void OnLODChanged(int oldLOD, int newLOD, Rect newViewport)
        {
            int n = lodConfig.lodConfigs.Length;
            if (oldLOD < n && newLOD < n)
            {
                Rect2D oldViewRect = GetTerrainViewRect(oldLOD, mLastViewport);
                Rect2D newViewRect = GetTerrainViewRect(newLOD, newViewport);

                Vector2Int dimensions = GetLODDimension(oldLOD);
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (i >= 0 && i < dimensions.y && j >= 0 && j < dimensions.x)
                        {
                            var objectData = GetLODTile(oldLOD, j, i);
                            SetObjectActive(objectData, j, i, false, oldLOD);
                        }
                    }
                }

                dimensions = GetLODDimension(newLOD);
                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (i >= 0 && i < dimensions.y && j >= 0 && j < dimensions.x)
                        {
                            var objectData = GetLODTile(newLOD, j, i);
                            SetObjectActive(objectData, j, i, true, newLOD);
                        }
                    }
                }
            }

            mLastViewport = newViewport;
        }

        protected bool UpdateLODTileViewRect(Rect2D oldViewRect, Rect2D newViewRect, int currentLOD)
        {
            var newLODTiles = mOtherLODTiles[currentLOD - mDefaultLODCount];
            int rows = newLODTiles.GetLength(0);
            int cols = newLODTiles.GetLength(1);

            if (oldViewRect != newViewRect)
            {
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (!newViewRect.Contains(j, i))
                        {
                            //物体不在新的视野中,隐藏它
                            if (j >= 0 && i >= 0 && i < rows && j < cols)
                            {
                                SetObjectActive(newLODTiles[i, j], j, i, false, currentLOD);
                            }
                        }
                    }
                }

                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (!oldViewRect.Contains(j, i))
                        {
                            //物体不在旧的视野中,显示它
                            if (j >= 0 && i >= 0 && i < rows && j < cols)
                            {
                                SetObjectActive(newLODTiles[i, j], j, i, true, currentLOD);
                            }
                        }
                    }
                }

                return true;
            }

            return false;
        }

        //return: if extra lod is changed
        public bool UpdateLOD(float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }
            return lodChanged;
        }

        int CalculateExtraLODFromCameraHeight()
        {
            float cameraHeight = MapCameraMgr.currentCameraHeight;
            for (int i = mLODSettings.Length - 1; i >= 0; --i)
            {
                if (cameraHeight >= mLODSettings[i].cameraHeight)
                {
                    return i;
                }
            }
            return -1;
        }

        Vector2Int WorldPosToCoordFloor(Vector3 pos, int blockSize)
        {
            float tw = tileWidth * blockSize;
            float th = tileHeight * blockSize;
            float dx = pos.x - layerOffset.x;
            float dz = pos.z - layerOffset.z;
            return new Vector2Int(Mathf.FloorToInt(dx / tw), Mathf.FloorToInt(dz / th));
        }

        Vector2Int WorldPosToCoordCeil(Vector3 pos, int blockSize)
        {
            float tw = tileWidth * blockSize;
            float th = tileHeight * blockSize;
            float dx = pos.x - layerOffset.x;
            float dz = pos.z - layerOffset.z;
            return new Vector2Int(Mathf.CeilToInt(dx / tw), Mathf.CeilToInt(dz / th));
        }

        public Rect2D GetLODTileViewRect(Rect viewport, int blockSize)
        {
            var startCoord = WorldPosToCoordFloor(new Vector3(viewport.xMin, 0, viewport.yMin), blockSize);
            var endCoord = WorldPosToCoordCeil(new Vector3(viewport.xMax, 0, viewport.yMax), blockSize);

            return new Rect2D(startCoord.x, startCoord.y, endCoord.x, endCoord.y);
        }

        public Rect2D GetTerrainViewRect(int lod, Rect viewport)
        {
            if (lod < mDefaultLODCount)
            {
                return GetViewRect(viewport);
            }
            else
            {
                lod = lod - mDefaultLODCount;
                return GetLODTileViewRect(viewport, mLODSettings[lod].realBlockSize);
            }
        }

        public override bool IsOneTileLOD(int lod)
        {
            if (lod < mDefaultLODCount)
            {
                return false;
            }
            else
            {
                lod = lod - mDefaultLODCount;
                //return (lod == mLODSettings.Length - 1 && mLODSettings[lod].realBlockSize == horizontalTileCount);
                return lod == mLODSettings.Length - 1;
            }
        }

        public TerrainRenderTextureLODSetting[] lodSettings { get { return mLODSettings; } }
        public List<ModelData[,]> otherLODTiles { get { return mOtherLODTiles; } }
        public int defaultLODCount { get { return mDefaultLODCount; } }

        List<ModelData[,]> mOtherLODTiles;
        TerrainRenderTextureLODSetting[] mLODSettings;
        System.Action<IMapObjectData, int, int, int> mOnSetActive;
        int mDefaultLODCount;

        //temp code
        //GameObject mDebugger;
    }
}
