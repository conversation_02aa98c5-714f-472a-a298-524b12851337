﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //navmesh的地图层
    public partial class NavMeshLayer : MapLayerBase
    {
        public NavMeshLayer(Map map) : base(map)
        {
        }

        public override void OnDestroy()
        {
            if (layerView != null)
            {
                layerView.OnDestroy();
                layerView = null;
            }
            if (layerData != null)
            {
                Map.currentMap.DestroyObject(layerData);
                layerData = null;
            }
        }

        public override void Unload()
        {
            Map.currentMap.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.NavMeshLayerData;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            int n = 0;
            if (sourceLayer.navMeshDatas != null)
            {
                n = sourceLayer.navMeshDatas.Length;
            }
            NavMeshData[] navMeshDatas = null;
            for (int i = 0; i < n; ++i)
            {
                if (navMeshDatas == null)
                {
                    navMeshDatas = new NavMeshData[n];
                }

                navMeshDatas[i] = new NavMeshData(
                    sourceLayer.navMeshDatas[i].vertices,
                    sourceLayer.navMeshDatas[i].triangles,
                    sourceLayer.navMeshDatas[i].triangleTypes,
                    sourceLayer.navMeshDatas[i].triangleStates
                    );
            }

            this.layerData = new NavMeshLayerData(header, Map.currentMap, navMeshDatas, sourceLayer.oceanAreaWalkable);
            layerView = NavMeshLayerView.Create(this.layerData);
            layerView.ShowNavMesh(sourceLayer.visible);
            layerView.active = layerData.active;

            if (n > 0)
            {
                InitNavigation(navMeshDatas[0].vertices, navMeshDatas[0].indices, navMeshDatas[0].triangleTypes, navMeshDatas[0].triangleStates);
            }

            Map.currentMap.AddMapLayer(this);
        }

        public NavMeshBlock[] CreateNavMesh(int xTileCount, int yTileCount, PrefabOutlineType outlineType, float radius, float minimumAngle, float maximumArea, bool useDelaunay, NavigationCreateMode createMode)
        {
            var meshies = layerView.CreateNavMesh(xTileCount, yTileCount, outlineType, radius, minimumAngle, maximumArea, useDelaunay, createMode, layerData.oceanAreaWalkable);
            layerData.CreateNavMeshData(meshies);
            CreateDebugger();

            return meshies;
        }

        public override void RefreshObjectsInViewport() { }
        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            return false;
        }
        public override float GetTotalHeight()
        {
            return layerData.GetLayerHeightInMeter(0);
        }
        public override float GetTotalWidth()
        {
            return layerData.GetLayerWidthInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return false;
        }

        public override int GetCurrentLOD()
        {
            return 0;
        }

        public override MapLayerData GetLayerData()
        {
            return layerData;
        }

        public override MapLayerView GetLayerView()
        {
            return layerView;
        }

        public void ShowNavMesh(bool show)
        {
            layerView.ShowNavMesh(show);
        }

        public void ShowNavMeshRegions(bool show)
        {
            layerData.ShowNavMeshRegions(show);
        }

        public override string name { get { return layerData?.name; } set { layerData.name = value; } }
        public override int id { get { return layerData.id; } }
        public override Vector3 layerOffset { get { return layerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return layerView.root;
            }
        }
        public Mesh GetNavMesh(int i)
        {
            return layerView.GetNavMesh(i);
        }

        public void GetTotalVertexCount(out int vertexCount, out int indexCount)
        {
            vertexCount = 0;
            indexCount = 0;

            int n = layerData.navMeshCount;
            for (int i = 0; i < n; ++i)
            {
                var meshData = layerData.navMeshDatas[0];
                vertexCount += meshData.vertices.Length;
                indexCount += meshData.indices.Length;
            }
        }

        //寻路初始化
        public void InitNavigation(Vector3[] vertices, int[] indices, ushort[] triangleTypes, bool[] triangleStates)
        {
            layerData.InitNavigation(vertices, indices, triangleTypes, triangleStates);
            CreateDebugger();
        }

        public void CreateDebugger()
        {
            //disable debugger
            return;
#if false
            mNavigationDebugger?.OnDestroy();

            if (mLayerData.navMgr != null)
            {
                mNavigationDebugger = new Nav.NavigationDebugger();
                mNavigationDebugger.Create(mLayerData.navMgr, mLayerView.root);
            }
#endif
        }

        public List<Vector2> FindPath(Vector2 start, Vector2 end)
        {
            return layerData.FindPath(start, end);
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            layerData.Resize(newWidth, newHeight);
            return true;
        }

        public int GetNavMeshRegionID(Vector3 pos)
        {
            if (layerData.navMgr != null)
            {
                return layerData.navMgr.GetIntersectedRegionID(pos.x, pos.z);
            }
            return 0;
        }

        public bool GetNavMeshRegionState(Vector3 pos)
        {
            if (layerData.navMgr != null)
            {
                int id = GetNavMeshRegionID(pos);
                return layerData.navMgr.GetRegionState(id);
            }
            return false;
        }

        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override bool active { get { return layerView.active; } set { layerView.active = value; } }
        public override int lodCount => layerData.lodCount;
        public NavMeshLayerData layerData { get; private set; }
        public NavMeshLayerView layerView { get; private set; }
        public Nav.NavigationDebugger navigationDebugger { get; }
    }
}

#endif
