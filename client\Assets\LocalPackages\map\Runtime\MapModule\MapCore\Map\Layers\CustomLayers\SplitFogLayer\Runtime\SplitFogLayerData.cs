﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //拼接地表层使用的数据
    public partial class SplitFogLayerData : MapLayerData
    {
        public SplitFogLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, int[] tileTypes, string[] tilePrefabPaths, float height, string fogLOD1PrefabPath, string selectionMaterialPath, string maskTexPropertyName) : base(header, config, map)
        {
            if (Map.currentMap.isEditorMode)
            {
                if (tileTypes == null)
                {
                    int n = header.rows * header.cols;
                    tileTypes = new int[n];
                }
                mTileTypes = tileTypes;
            }
            else
            {
                mTileTypes = new int[mRows * mCols];
                int n = mTileTypes.Length;
                for (int i = 0; i < n; ++i)
                {
                    mTileTypes[i] = mNextMeshID;
                }
                float layerWidth = GetLayerWidthInMeter();
                float layerHeight = GetLayerHeightInMeter();
                mRectFogs.Add(mNextMeshID, new FogRect(mNextMeshID, mLayerOrigin.x, mLayerOrigin.z, mLayerOrigin.x + layerWidth, mLayerOrigin.z + layerHeight));

                ++mNextMeshID;
            }

            mFogHeight = height;
            mFogLOD1PrefabPath = fogLOD1PrefabPath;
            mSelectionMaterialPath = selectionMaterialPath;
            mFogMaskTexPropertyName = maskTexPropertyName;

            mTilePrefabPaths = tilePrefabPaths;
            if (mTilePrefabPaths != null)
            {
                Debug.Assert(mTilePrefabPaths.Length == 16);
            }
            else
            {
                mTilePrefabPaths = new string[16];
            }

            if (Map.currentMap.isEditorMode)
            {
                mLastViewport = Map.currentMap.originalViewport;
            }
            else
            {
                mLastViewport = new Rect(-10000, -10000, 0, 0);
            }

            CreateLODData();
        }

        public override void OnDestroy()
        {
        }

        //拼接一个tile
        //x: tile的x坐标
        //y: tile的y坐标
        //tileIndex: tile使用的拼接prefab的索引
        //tileType: tile使用的拼接图集
        //setAbsoluteValue: 是否直接将tileIndex和tileType当成拼接后的值,而不考虑这个tile目前的情况
        public void PushTile(int x, int y, int tileType, bool setAbsoluteValue)
        {
            if (mTilePrefabPaths == null)
            {
                return;
            }
            if (x >= 0 && x < mCols && y >= 0 && y < mRows && tileType > 0)
            {
                var idx = y * mCols + x;
                int oldTileType = mTileTypes[idx];

                if (mTileTypes[idx] == 0)
                {
                    //创建一个新tile
                    mTileTypes[idx] = tileType;
                }

                if (setAbsoluteValue)
                {
                    mTileTypes[idx] = tileType;
                }
                else
                {
                    //和当前的tile拼接
                    DoPushTile(x, y, tileType);
                }

                if (mTileDataChangeCallback != null)
                {
                    mTileDataChangeCallback(x, y, oldTileType, true);
                }
            }
        }

        //反向拼接一个tile
        public void PopTile(int x, int y, int tileIndex)
        {
            if (mTilePrefabPaths == null)
            {
                return;
            }

            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                var idx = y * mCols + x;
                int oldTileType = mTileTypes[idx];
                if (mTileTypes[idx] != 0)
                {
                    DoPopTile(x, y, tileIndex);

                    var viewRect = GetViewRect(GetViewportRect());
                    bool isVisible = viewRect.Contains(x, y);
                    mTileDataChangeCallback(x, y, oldTileType, isVisible);
                }
            }
        }

        public bool GetPushTileResult(int x, int y, int tileIndex, out int combinedTileIndex)
        {
            combinedTileIndex = tileIndex;
            if (x >= 0 && x < mCols && y >= 0 && y < mRows && tileIndex > 0)
            {
                var idx = y * mCols + x;
                if (mTileTypes[idx] != 0)
                {
                    combinedTileIndex = mTileTypes[idx];
                    if (combinedTileIndex < 15)
                    {
                        combinedTileIndex |= tileIndex;
                    }
                }
                else
                {
                    combinedTileIndex = tileIndex;
                }
                return true;
            }
            return false;
        }

        //删除tile的数据
        public void ClearTile(int x, int y)
        {
            if (mTilePrefabPaths == null)
            {
                return;
            }

            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                var idx = y * mCols + x;
                int oldTileType = mTileTypes[idx];
                mTileTypes[idx] = 0;

                mTileDataChangeCallback(x, y, oldTileType, false);
            }
        }

        //获取tile的数据
        public int GetTileType(int x, int y)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                var idx = y * mCols + x;
                return mTileTypes[idx];
            }
            return -1;
        }

        public override bool isGameLayer => true;

        //和四周的tile拼接
        void DoPushTile(int x, int y, int tileIndex)
        {
            int idx = y * mCols + x;
            if (mTileTypes[idx] < 15)
            {
                mTileTypes[idx] |= tileIndex;
            }
        }

        //反向拼接
        void DoPopTile(int x, int y, int tileIndex)
        {
            int idx = y * mCols + x;
            if (mTileTypes[idx] > 15)
            {
                mTileTypes[idx] = 15;
            }
            mTileTypes[idx] &= ~tileIndex;
        }

        public string GetAssetPath(int tileType)
        {
            if (tileType > 0 && tileType <= 15)
            {
                return mTilePrefabPaths[tileType];
            }
            Debug.Assert(false, "logic error!");
            return "";
        }

        public override bool Contains(int objectID)
        {
            throw new System.NotImplementedException();
        }

        public void SetCallbacks(System.Action<int, int, bool> tileVisibilityChangeCallback,
            System.Action<FogRect, bool> rectFogVisibilityChangeCallback,
            System.Action<int, int, int, bool> tileDataChangeCallback,
            System.Action fogHeightChangeCallback,
            System.Action<FogRect> createRectCallback,
            System.Action<FogRect> removeRectCallback,
            System.Action lodChangeCallback,
            System.Action<Color32[]> maskChangeCallback,
            System.Func<int, int, int, int, bool> selectionChangeCallback,
            System.Action hideSelectionCallback)
        {
            mTileDataChangeCallback = tileDataChangeCallback;
            mFogHeightChangeCallback = fogHeightChangeCallback;
            mCreateRectCallback = createRectCallback;
            mRemoveRectCallback = removeRectCallback;
            mFogVisibilityChangeCallback = rectFogVisibilityChangeCallback;
            mTileVisibilityChangeCallback = tileVisibilityChangeCallback;
            mLODChangeCallback = lodChangeCallback;
            mUpdateMaskCallback = maskChangeCallback;
            mSelectionChangeCallback = selectionChangeCallback;
            mHideSelectionCallback = hideSelectionCallback;
        }

        public void SetPrefabPath(int tileIndex, string path)
        {
            if (tileIndex >= 0 && tileIndex <= 15)
            {
                mTilePrefabPaths[tileIndex] = path;
            }
        }

        public void PopTiles(Vector3 worldPos)
        {
            PickTiles(worldPos);

            for (int i = 0; i < 4; ++i)
            {
                int x = mPickedTiles[i].x;
                int y = mPickedTiles[i].y;
                int tileType = GetTileType(x, y);
                if (tileType > 0)
                {
                    //合并所有相同id的格子的rectangle,为后续的分割做准备
                    if (!IsGridTile(tileType))
                    {
                        RecordSplitRect(tileType, x, y);
                    }
                    PopTile(x, y, mTileIndex[i]);

                    int px, py;
                    GetPixelCoord(x, y, i, out px, out py);
                    ClearPixel(px, py);
                }
            }

            //将记录下的rectangle和当前地图上的rect mesh做差集,得到新的rect
            SplitRecordedRects();
        }

        void GetPixelCoord(int x, int y, int i, out int px, out int py)
        {
            px = 0;
            py = 0;
            switch (i)
            {
                case 0:
                    px = x * 2 + 1;
                    py = y * 2 + 1;
                    break;
                case 1:
                    px = x * 2;
                    py = y * 2 + 1;
                    break;
                case 2:
                    px = x * 2 + 1;
                    py = y * 2;
                    break;
                case 3:
                    px = x * 2;
                    py = y * 2;
                    break;
            }
        }

        void PickTiles(Vector3 worldPos)
        {
            var coord = FromWorldPositionToCoordinate(worldPos);
            var tileStartPos = FromCoordinateToWorldPosition(coord.x, coord.y);
            var rx = (worldPos.x - tileStartPos.x) / tileWidth;
            var rz = (worldPos.z - tileStartPos.z) / tileHeight;
            if (rx <= 0.5)
            {
                if (rz <= 0.5)
                {
                    mPickedTiles[0] = new Vector2Int(coord.x - 1, coord.y - 1);
                    mPickedTiles[1] = new Vector2Int(coord.x, coord.y - 1);
                    mPickedTiles[2] = new Vector2Int(coord.x - 1, coord.y);
                    mPickedTiles[3] = new Vector2Int(coord.x, coord.y);
                }
                else
                {
                    mPickedTiles[0] = new Vector2Int(coord.x - 1, coord.y);
                    mPickedTiles[1] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[2] = new Vector2Int(coord.x - 1, coord.y + 1);
                    mPickedTiles[3] = new Vector2Int(coord.x, coord.y + 1);
                }
            }
            else
            {
                if (rz <= 0.5)
                {
                    mPickedTiles[0] = new Vector2Int(coord.x, coord.y - 1);
                    mPickedTiles[1] = new Vector2Int(coord.x + 1, coord.y - 1);
                    mPickedTiles[2] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[3] = new Vector2Int(coord.x + 1, coord.y);
                }
                else
                {
                    mPickedTiles[0] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[1] = new Vector2Int(coord.x + 1, coord.y);
                    mPickedTiles[2] = new Vector2Int(coord.x, coord.y + 1);
                    mPickedTiles[3] = new Vector2Int(coord.x + 1, coord.y + 1);
                }
            }
        }

        bool IsGridTile(int tileType)
        {
            return tileType > 0 && tileType <= 15;
        }

        public Rect GetViewportRect()
        {
            return mLastViewport;
        }

        public Rect lastViewport { get { return mLastViewport; } }
        public float fogHeight
        {
            get { return mFogHeight; }
            set
            {
                mFogHeight = value;
                if (mFogHeightChangeCallback != null)
                {
                    mFogHeightChangeCallback();
                }
            }
        }

        public int[] tileTypes { get { return mTileTypes; } }
        public string[] tilePrefabPaths { get { return mTilePrefabPaths; } }
        public string selectionMaterialPath { get { return mSelectionMaterialPath; } set { mSelectionMaterialPath = value; } }
        public string fogMaskTexPropertyName { get { return mFogMaskTexPropertyName; } set { mFogMaskTexPropertyName = value; } }

        //0表示空,1到15表示tile类型,16开始表示space id
        int[] mTileTypes;
        float mFogHeight;
        string[] mTilePrefabPaths;
        string mSelectionMaterialPath;
        string mFogMaskTexPropertyName;
        Vector2Int[] mPickedTiles = new Vector2Int[4];
        static int[] mTileIndex = new int[] { 1, 2, 4, 8 };
        System.Action<int, int, int, bool> mTileDataChangeCallback;
        System.Action mFogHeightChangeCallback;
    }
}