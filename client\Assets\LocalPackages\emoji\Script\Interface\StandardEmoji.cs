﻿








using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace TFW
{
    public struct EmojiCodePoint
    {
        public int leadSurrogates; //前导代理
        public int trailSurrogates;    //后尾代理
        public int unicodePoint;   //unicode码点，可能是单独一个char，也有可能是由前导代理和后尾代理这两个char合成的值
    }

    public struct Section
    {
        public int startIndex;
        public int endIndex;
        public string value;
    }

    public class StandardEmoji : Singleton<StandardEmoji>
    {
        private HashSet<string> emojiUnicodeSet;    //所有emoji的unicode的码点
        private HashSet<int> singleCodes;    //所有emoji的unicode的码点中包含的单个unicode码点，用于第一道检测
        private string outString;    //记录每次filter的结果
        private List<EmojiCodePoint> unicodeSequence;  //遍历过程中出现的unicode码点序列
        public const string unicodeSeparator = "_";

        public StandardEmoji()
        {
            Init();
        }

        private void Init()
        {
            outString = "";
            unicodeSequence = new List<EmojiCodePoint>();
            InitUnicodeSet();
        }

        /// <summary>
        /// 识别字符串中的emoji unicode码点，并使用自定义的Tag将每个emoji标识出来
        /// </summary>
        /// <param name="str">待识别的字符串</param>
        /// <param name="startTag">起始Tag</param>
        /// <param name="endTag">终止Tag</param>
        /// <param name="keepCodePoint">是否在Tag中保留emoji码点</param>
        /// <returns></returns>
        /// 使用示例:
        /// var emoji_smile = "\uD83D\uDE03";
        /// var stringToReplace = "hello" + emoji_smile + "world";
        /// var result1 = ReplaceEmojiInStrWithTag(stringToReplace, "[e]", "[\e]", true) (结果: "hello[e]1F603[\e]world")
        /// var result2 = ReplaceEmojiInStrWithTag(stringToReplace, "[e]", "[\e]", false) (结果: "hello[e][\e]world")
        public static string ReplaceEmojiInStrWithTag(string str, string startTag, string endTag, bool keepCodePoint = true)
        {
            if (str == null)
            {
                return null;
            }
            Debug.Assert(startTag != null);
            Debug.Assert(endTag != null);
            Instance.outString = "";
            //var emojiSet = Instance.emojiUnicodeSet; 没有引用

            char[] chars = str.ToCharArray();
            int charsLength = chars.Length;
            int currentChar;
            Instance.unicodeSequence.Clear();

            for (int i = 0; i < charsLength; i++)
            {
                EmojiCodePoint emojiCodePoint;
                currentChar = chars[i];

                //var str1 = Convert.ToString(currentChar, 16).ToUpper();
                //Debug.LogFormat("var{0}==={1}", currentChar, str1);

                if (currentChar >= 0xD800 && currentChar <= 0xDBFF)
                {
                    if ((i + 1) < charsLength)
                    {
                        int nextChar = chars[++i];
                        emojiCodePoint.leadSurrogates = currentChar;
                        emojiCodePoint.trailSurrogates = nextChar;
                        emojiCodePoint.unicodePoint = (0x10000 + (currentChar - 0xD800) * 0x400 + (nextChar - 0xDC00));
                    }
                    else
                    {
                        emojiCodePoint.leadSurrogates = currentChar;
                        emojiCodePoint.trailSurrogates = -1;
                        emojiCodePoint.unicodePoint = currentChar;
                    }
                }
                else
                {
                    emojiCodePoint.leadSurrogates = currentChar;
                    emojiCodePoint.trailSurrogates = -1;
                    emojiCodePoint.unicodePoint = currentChar;
                }

                if (Instance.singleCodes.Contains(emojiCodePoint.unicodePoint))    //如果码点包含在第一道检测集合，将其加入码点序列
                {
                    if (emojiCodePoint.trailSurrogates == -1)
                    {
                        continue;
                    }
                    Instance.unicodeSequence.Add(emojiCodePoint);
                }
                else    //如果码点不包含在第一道检测集合，将当前码点序列用于emoji检测(第二道检测)，检测以后重置码点序列
                {
                    var sequenceAfterFilter = Instance.FilterCurrentSequence(Instance.unicodeSequence, startTag, endTag, keepCodePoint);
                    Instance.outString += sequenceAfterFilter;
                    Instance.unicodeSequence.Clear();

                    //示例:🤥😀 第一个表情不在
                    //原来逻辑,即使不属于emoji编码但是也添加了头和尾。导致有2个无效char,ui显示为乱码
                    //Instance.AddCodePointToStr(ref Instance.outString, emojiCodePoint);

                    //zjw fix 20201221 增加一层处理
                    //如果不属于emoji的编码点,不要添加头和尾 多余的2个字符，导致显示乱码
                    if (emojiCodePoint.trailSurrogates == -1)
                    {
                        Instance.AddCodePointToStr(ref Instance.outString, emojiCodePoint);
                    }
                    //另外一种处理方式 把新的不存在的表情添加到AddUnicodeToSet函数里
                }
            }

            if (Instance.unicodeSequence.Count != 0)
            {
                var sequenceAfterFilter = Instance.FilterCurrentSequence(Instance.unicodeSequence, startTag, endTag, keepCodePoint);
                Instance.outString += sequenceAfterFilter;
                Instance.unicodeSequence.Clear();
            }

            return Instance.outString;
        }

        /// <summary>
        /// 从当前unicode序列里过滤出emoji，并输出过滤后的字符串
        /// </summary>
        private string FilterCurrentSequence(List<EmojiCodePoint> sequence, string startTag, string endTag, bool keepCodePoint)
        {
            var outStr = "";
            var sequenceAfterFilter = new List<Section>();
            FilterSequenceRecursively(sequence, 0, sequence.Count - 1, sequenceAfterFilter, startTag, endTag, keepCodePoint);
            sequenceAfterFilter.Sort((Section x, Section y) =>
            {
                if (x.startIndex == y.startIndex) return 0;
                else if (x.startIndex < y.startIndex) return -1;
                else return 1;
            });

            for (int i = 0; i <= sequenceAfterFilter.Count - 1; ++i)
            {
                outStr += sequenceAfterFilter[i].value;
            }

            return outStr;
        }

        private void FilterSequenceRecursively(List<EmojiCodePoint> sequence, int startIndex, int endIndex, List<Section> validSequences, string startTag = "", string endTag = "", bool keepCodePoint = true)
        {
            if (startIndex < 0 || startIndex > sequence.Count - 1
                || endIndex < 0 || endIndex > sequence.Count - 1
                || startIndex > endIndex)
            {
                return;
            }

            for (int filterLength = endIndex - startIndex + 1; filterLength >= 1; filterLength--)
            {
                for (int i = startIndex; i + filterLength <= endIndex + 1; ++i)
                {
                    var detectStr = "";
                    for (int j = i; j <= i + filterLength - 1; ++j)
                    {
                        if (j != i)
                        {
                            detectStr += unicodeSeparator;
                        }
                        detectStr += Convert.ToString(sequence[j].unicodePoint, 16).ToUpper();
                    }

                    if (emojiUnicodeSet.Contains(detectStr))
                    {
                        Section validSection;
                        validSection.startIndex = i;
                        validSection.endIndex = i + filterLength - 1;
                        validSection.value = startTag + (keepCodePoint ? detectStr : "") + endTag;

                        validSequences.Add(validSection);

                        if (filterLength != 1)
                        {
                            FilterSequenceRecursively(sequence, startIndex, i - 1, validSequences, startTag, endTag);
                            FilterSequenceRecursively(sequence, i + filterLength, endIndex, validSequences, startTag, endTag);
                            return;
                        }
                    }
                    else if (filterLength == 1)
                    {
                        Section invalidSection;
                        invalidSection.startIndex = i;
                        invalidSection.endIndex = i + filterLength - 1;
                        invalidSection.value = "";
                        AddCodePointToStr(ref invalidSection.value, sequence[i]);

                        validSequences.Add(invalidSection);
                    }
                }
            }
        }

        private void AddCodePointToStr(ref string str, EmojiCodePoint codePoint)
        {
            if (codePoint.leadSurrogates != -1)
            {
                str += (char)codePoint.leadSurrogates;
            }
            if (codePoint.trailSurrogates != -1)
            {
                str += (char)codePoint.trailSurrogates;
            }
        }

        private void AddUnicodeToSet(params int[] codes)
        {
            var str = "";
            for (int i = 0; i <= codes.Length - 1; ++i)
            {
                if (i != 0)
                {
                    str += unicodeSeparator;
                }
                str += Convert.ToString(codes[i], 16).ToUpper();

                if (!singleCodes.Contains(codes[i]))
                {
                    singleCodes.Add(codes[i]);
                }
            }
            emojiUnicodeSet.Add(str);
        }

        private void InitUnicodeSet()
        {
            emojiUnicodeSet = new HashSet<string>();
            singleCodes = new HashSet<int>();

            AddUnicodeToSet(0x1F469, 0x200D, 0x2764, 0xFE0F, 0x200D, 0x1F48B, 0x200D, 0x1F468);
            AddUnicodeToSet(0x1F468, 0x200D, 0x2764, 0xFE0F, 0x200D, 0x1F48B, 0x200D, 0x1F468);
            AddUnicodeToSet(0x1F469, 0x200D, 0x2764, 0xFE0F, 0x200D, 0x1F48B, 0x200D, 0x1F469);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F469, 0x200D, 0x1F467, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F469, 0x200D, 0x1F466, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F469, 0x200D, 0x1F467, 0x200D, 0x1F467);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F468, 0x200D, 0x1F467, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F468, 0x200D, 0x1F466, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F468, 0x200D, 0x1F467, 0x200D, 0x1F467);
            AddUnicodeToSet(0x1F469, 0x200D, 0x1F469, 0x200D, 0x1F467, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F469, 0x200D, 0x1F469, 0x200D, 0x1F466, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F469, 0x200D, 0x1F469, 0x200D, 0x1F467, 0x200D, 0x1F467);
            AddUnicodeToSet(0x1F469, 0x200D, 0x2764, 0xFE0F, 0x200D, 0x1F468);
            AddUnicodeToSet(0x1F468, 0x200D, 0x2764, 0xFE0F, 0x200D, 0x1F468);
            AddUnicodeToSet(0x1F469, 0x200D, 0x2764, 0xFE0F, 0x200D, 0x1F469);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F468, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F469, 0x200D, 0x1F469, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F469, 0x200D, 0x1F469, 0x200D, 0x1F467);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F468, 0x200D, 0x1F467);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F469, 0x200D, 0x1F467);
            AddUnicodeToSet(0x1F468, 0x200D, 0x1F469, 0x200D, 0x1F466);
            AddUnicodeToSet(0x1F441, 0x200D, 0x1F5E8);
            AddUnicodeToSet(0x1F474, 0x1F3FD);
            AddUnicodeToSet(0x1F474, 0x1F3FE);
            AddUnicodeToSet(0x1F474, 0x1F3FF);
            AddUnicodeToSet(0x1F475, 0x1F3FB);
            AddUnicodeToSet(0x1F475, 0x1F3FC);
            AddUnicodeToSet(0x1F475, 0x1F3FD);
            AddUnicodeToSet(0x1F475, 0x1F3FE);
            AddUnicodeToSet(0x1F475, 0x1F3FF);
            AddUnicodeToSet(0x1F476, 0x1F3FB);
            AddUnicodeToSet(0x1F476, 0x1F3FC);
            AddUnicodeToSet(0x1F476, 0x1F3FD);
            AddUnicodeToSet(0x1F476, 0x1F3FE);
            AddUnicodeToSet(0x1F476, 0x1F3FF);
            AddUnicodeToSet(0x1F47C, 0x1F3FB);
            AddUnicodeToSet(0x1F47C, 0x1F3FC);
            AddUnicodeToSet(0x1F47C, 0x1F3FD);
            AddUnicodeToSet(0x1F47C, 0x1F3FE);
            AddUnicodeToSet(0x1F47C, 0x1F3FF);
            AddUnicodeToSet(0x1F471, 0x1F3FB);
            AddUnicodeToSet(0x1F471, 0x1F3FC);
            AddUnicodeToSet(0x1F471, 0x1F3FD);
            AddUnicodeToSet(0x1F471, 0x1F3FE);
            AddUnicodeToSet(0x1F471, 0x1F3FF);
            AddUnicodeToSet(0x1F46E, 0x1F3FB);
            AddUnicodeToSet(0x1F46E, 0x1F3FC);
            AddUnicodeToSet(0x1F46E, 0x1F3FD);
            AddUnicodeToSet(0x1F46E, 0x1F3FE);
            AddUnicodeToSet(0x1F46E, 0x1F3FF);
            AddUnicodeToSet(0x1F472, 0x1F3FB);
            AddUnicodeToSet(0x1F472, 0x1F3FC);
            AddUnicodeToSet(0x1F472, 0x1F3FD);
            AddUnicodeToSet(0x1F472, 0x1F3FE);
            AddUnicodeToSet(0x1F472, 0x1F3FF);
            AddUnicodeToSet(0x1F473, 0x1F3FB);
            AddUnicodeToSet(0x1F473, 0x1F3FC);
            AddUnicodeToSet(0x1F473, 0x1F3FD);
            AddUnicodeToSet(0x1F473, 0x1F3FE);
            AddUnicodeToSet(0x1F473, 0x1F3FF);
            AddUnicodeToSet(0x1F477, 0x1F3FB);
            AddUnicodeToSet(0x1F477, 0x1F3FC);
            AddUnicodeToSet(0x1F477, 0x1F3FD);
            AddUnicodeToSet(0x1F477, 0x1F3FE);
            AddUnicodeToSet(0x1F477, 0x1F3FF);
            AddUnicodeToSet(0x1F478, 0x1F3FB);
            AddUnicodeToSet(0x1F478, 0x1F3FC);
            AddUnicodeToSet(0x1F478, 0x1F3FD);
            AddUnicodeToSet(0x1F478, 0x1F3FE);
            AddUnicodeToSet(0x1F478, 0x1F3FF);
            AddUnicodeToSet(0x1F482, 0x1F3FB);
            AddUnicodeToSet(0x1F482, 0x1F3FC);
            AddUnicodeToSet(0x1F482, 0x1F3FD);
            AddUnicodeToSet(0x1F482, 0x1F3FE);
            AddUnicodeToSet(0x1F482, 0x1F3FF);
            AddUnicodeToSet(0x1F575, 0x1F3FB);
            AddUnicodeToSet(0x1F575, 0x1F3FC);
            AddUnicodeToSet(0x1F575, 0x1F3FD);
            AddUnicodeToSet(0x1F575, 0x1F3FE);
            AddUnicodeToSet(0x1F575, 0x1F3FF);
            AddUnicodeToSet(0x1F385, 0x1F3FB);
            AddUnicodeToSet(0x1F385, 0x1F3FC);
            AddUnicodeToSet(0x1F385, 0x1F3FD);
            AddUnicodeToSet(0x1F385, 0x1F3FE);
            AddUnicodeToSet(0x1F385, 0x1F3FF);
            AddUnicodeToSet(0x1F470, 0x1F3FB);
            AddUnicodeToSet(0x1F470, 0x1F3FC);
            AddUnicodeToSet(0x1F470, 0x1F3FD);
            AddUnicodeToSet(0x1F470, 0x1F3FE);
            AddUnicodeToSet(0x1F470, 0x1F3FF);
            AddUnicodeToSet(0x1F486, 0x1F3FB);
            AddUnicodeToSet(0x1F486, 0x1F3FC);
            AddUnicodeToSet(0x1F486, 0x1F3FD);
            AddUnicodeToSet(0x1F486, 0x1F3FE);
            AddUnicodeToSet(0x1F486, 0x1F3FF);
            AddUnicodeToSet(0x1F487, 0x1F3FB);
            AddUnicodeToSet(0x1F487, 0x1F3FC);
            AddUnicodeToSet(0x1F487, 0x1F3FD);
            AddUnicodeToSet(0x1F487, 0x1F3FE);
            AddUnicodeToSet(0x1F487, 0x1F3FF);
            AddUnicodeToSet(0x1F64D, 0x1F3FB);
            AddUnicodeToSet(0x1F64D, 0x1F3FC);
            AddUnicodeToSet(0x1F64D, 0x1F3FD);
            AddUnicodeToSet(0x1F64D, 0x1F3FE);
            AddUnicodeToSet(0x1F64D, 0x1F3FF);
            AddUnicodeToSet(0x1F64E, 0x1F3FB);
            AddUnicodeToSet(0x1F64E, 0x1F3FC);
            AddUnicodeToSet(0x1F64E, 0x1F3FD);
            AddUnicodeToSet(0x1F64E, 0x1F3FE);
            AddUnicodeToSet(0x1F64E, 0x1F3FF);
            AddUnicodeToSet(0x1F645, 0x1F3FB);
            AddUnicodeToSet(0x1F645, 0x1F3FC);
            AddUnicodeToSet(0x1F645, 0x1F3FD);
            AddUnicodeToSet(0x1F645, 0x1F3FE);
            AddUnicodeToSet(0x1F645, 0x1F3FF);
            AddUnicodeToSet(0x1F646, 0x1F3FB);
            AddUnicodeToSet(0x1F646, 0x1F3FC);
            AddUnicodeToSet(0x1F646, 0x1F3FD);
            AddUnicodeToSet(0x1F646, 0x1F3FE);
            AddUnicodeToSet(0x1F646, 0x1F3FF);
            AddUnicodeToSet(0x1F481, 0x1F3FB);
            AddUnicodeToSet(0x1F481, 0x1F3FC);
            AddUnicodeToSet(0x1F481, 0x1F3FD);
            AddUnicodeToSet(0x1F481, 0x1F3FE);
            AddUnicodeToSet(0x1F481, 0x1F3FF);
            AddUnicodeToSet(0x1F64B, 0x1F3FB);
            AddUnicodeToSet(0x1F64B, 0x1F3FC);
            AddUnicodeToSet(0x1F64B, 0x1F3FD);
            AddUnicodeToSet(0x1F64B, 0x1F3FE);
            AddUnicodeToSet(0x1F64B, 0x1F3FF);
            AddUnicodeToSet(0x1F647, 0x1F3FB);
            AddUnicodeToSet(0x1F647, 0x1F3FC);
            AddUnicodeToSet(0x1F647, 0x1F3FD);
            AddUnicodeToSet(0x1F647, 0x1F3FE);
            AddUnicodeToSet(0x1F647, 0x1F3FF);
            AddUnicodeToSet(0x1F6B6, 0x1F3FB);
            AddUnicodeToSet(0x1F6B6, 0x1F3FC);
            AddUnicodeToSet(0x1F6B6, 0x1F3FD);
            AddUnicodeToSet(0x1F6B6, 0x1F3FE);
            AddUnicodeToSet(0x1F6B6, 0x1F3FF);
            AddUnicodeToSet(0x1F3C3, 0x1F3FB);
            AddUnicodeToSet(0x1F3C3, 0x1F3FC);
            AddUnicodeToSet(0x1F3C3, 0x1F3FD);
            AddUnicodeToSet(0x1F3C3, 0x1F3FE);
            AddUnicodeToSet(0x1F3C3, 0x1F3FF);
            AddUnicodeToSet(0x1F483, 0x1F3FB);
            AddUnicodeToSet(0x1F483, 0x1F3FC);
            AddUnicodeToSet(0x1F483, 0x1F3FD);
            AddUnicodeToSet(0x1F483, 0x1F3FE);
            AddUnicodeToSet(0x1F483, 0x1F3FF);
            AddUnicodeToSet(0x1F466, 0x1F3FB);
            AddUnicodeToSet(0x1F466, 0x1F3FC);
            AddUnicodeToSet(0x1F466, 0x1F3FD);
            AddUnicodeToSet(0x1F466, 0x1F3FE);
            AddUnicodeToSet(0x1F466, 0x1F3FF);
            AddUnicodeToSet(0x1F467, 0x1F3FB);
            AddUnicodeToSet(0x1F467, 0x1F3FC);
            AddUnicodeToSet(0x1F467, 0x1F3FD);
            AddUnicodeToSet(0x1F467, 0x1F3FE);
            AddUnicodeToSet(0x1F467, 0x1F3FF);
            AddUnicodeToSet(0x1F468, 0x1F3FB);
            AddUnicodeToSet(0x1F468, 0x1F3FC);
            AddUnicodeToSet(0x1F468, 0x1F3FD);
            AddUnicodeToSet(0x1F468, 0x1F3FE);
            AddUnicodeToSet(0x1F468, 0x1F3FF);
            AddUnicodeToSet(0x1F469, 0x1F3FB);
            AddUnicodeToSet(0x1F469, 0x1F3FC);
            AddUnicodeToSet(0x1F469, 0x1F3FD);
            AddUnicodeToSet(0x1F469, 0x1F3FE);
            AddUnicodeToSet(0x1F469, 0x1F3FF);
            AddUnicodeToSet(0x1F474, 0x1F3FB);
            AddUnicodeToSet(0x1F4AA, 0x1F3FB);
            AddUnicodeToSet(0x1F4AA, 0x1F3FC);
            AddUnicodeToSet(0x1F4AA, 0x1F3FD);
            AddUnicodeToSet(0x1F4AA, 0x1F3FE);
            AddUnicodeToSet(0x1F4AA, 0x1F3FF);
            AddUnicodeToSet(0x1F448, 0x1F3FB);
            AddUnicodeToSet(0x1F448, 0x1F3FC);
            AddUnicodeToSet(0x1F448, 0x1F3FD);
            AddUnicodeToSet(0x1F448, 0x1F3FE);
            AddUnicodeToSet(0x1F448, 0x1F3FF);
            AddUnicodeToSet(0x1F449, 0x1F3FB);
            AddUnicodeToSet(0x1F449, 0x1F3FC);
            AddUnicodeToSet(0x1F449, 0x1F3FD);
            AddUnicodeToSet(0x1F449, 0x1F3FE);
            AddUnicodeToSet(0x1F449, 0x1F3FF);
            AddUnicodeToSet(0x1F446, 0x1F3FB);
            AddUnicodeToSet(0x1F446, 0x1F3FC);
            AddUnicodeToSet(0x1F446, 0x1F3FD);
            AddUnicodeToSet(0x1F446, 0x1F3FE);
            AddUnicodeToSet(0x1F446, 0x1F3FF);
            AddUnicodeToSet(0x1F595, 0x1F3FB);
            AddUnicodeToSet(0x1F595, 0x1F3FC);
            AddUnicodeToSet(0x1F595, 0x1F3FD);
            AddUnicodeToSet(0x1F595, 0x1F3FE);
            AddUnicodeToSet(0x1F595, 0x1F3FF);
            AddUnicodeToSet(0x1F447, 0x1F3FB);
            AddUnicodeToSet(0x1F447, 0x1F3FC);
            AddUnicodeToSet(0x1F447, 0x1F3FD);
            AddUnicodeToSet(0x1F447, 0x1F3FE);
            AddUnicodeToSet(0x1F447, 0x1F3FF);
            AddUnicodeToSet(0x1F596, 0x1F3FB);
            AddUnicodeToSet(0x1F596, 0x1F3FC);
            AddUnicodeToSet(0x1F596, 0x1F3FD);
            AddUnicodeToSet(0x1F596, 0x1F3FE);
            AddUnicodeToSet(0x1F596, 0x1F3FF);
            AddUnicodeToSet(0x1F918, 0x1F3FB);
            AddUnicodeToSet(0x1F918, 0x1F3FC);
            AddUnicodeToSet(0x1F918, 0x1F3FD);
            AddUnicodeToSet(0x1F918, 0x1F3FE);
            AddUnicodeToSet(0x1F918, 0x1F3FF);
            AddUnicodeToSet(0x1F590, 0x1F3FB);
            AddUnicodeToSet(0x1F590, 0x1F3FC);
            AddUnicodeToSet(0x1F590, 0x1F3FD);
            AddUnicodeToSet(0x1F590, 0x1F3FE);
            AddUnicodeToSet(0x1F590, 0x1F3FF);
            AddUnicodeToSet(0x1F44C, 0x1F3FB);
            AddUnicodeToSet(0x1F44C, 0x1F3FC);
            AddUnicodeToSet(0x1F44C, 0x1F3FD);
            AddUnicodeToSet(0x1F44C, 0x1F3FE);
            AddUnicodeToSet(0x1F44C, 0x1F3FF);
            AddUnicodeToSet(0x1F44D, 0x1F3FB);
            AddUnicodeToSet(0x1F44D, 0x1F3FC);
            AddUnicodeToSet(0x1F44D, 0x1F3FD);
            AddUnicodeToSet(0x1F44D, 0x1F3FE);
            AddUnicodeToSet(0x1F44D, 0x1F3FF);
            AddUnicodeToSet(0x1F44E, 0x1F3FB);
            AddUnicodeToSet(0x1F44E, 0x1F3FC);
            AddUnicodeToSet(0x1F44E, 0x1F3FD);
            AddUnicodeToSet(0x1F44E, 0x1F3FE);
            AddUnicodeToSet(0x1F44E, 0x1F3FF);
            AddUnicodeToSet(0x1F44A, 0x1F3FB);
            AddUnicodeToSet(0x1F44A, 0x1F3FC);
            AddUnicodeToSet(0x1F44A, 0x1F3FD);
            AddUnicodeToSet(0x1F44A, 0x1F3FE);
            AddUnicodeToSet(0x1F44A, 0x1F3FF);
            AddUnicodeToSet(0x1F44B, 0x1F3FB);
            AddUnicodeToSet(0x1F44B, 0x1F3FC);
            AddUnicodeToSet(0x1F44B, 0x1F3FD);
            AddUnicodeToSet(0x1F44B, 0x1F3FE);
            AddUnicodeToSet(0x1F44B, 0x1F3FF);
            AddUnicodeToSet(0x1F44F, 0x1F3FB);
            AddUnicodeToSet(0x1F44F, 0x1F3FC);
            AddUnicodeToSet(0x1F44F, 0x1F3FD);
            AddUnicodeToSet(0x1F44F, 0x1F3FE);
            AddUnicodeToSet(0x1F44F, 0x1F3FF);
            AddUnicodeToSet(0x1F450, 0x1F3FB);
            AddUnicodeToSet(0x1F450, 0x1F3FC);
            AddUnicodeToSet(0x1F450, 0x1F3FD);
            AddUnicodeToSet(0x1F450, 0x1F3FE);
            AddUnicodeToSet(0x1F450, 0x1F3FF);
            AddUnicodeToSet(0x1F64C, 0x1F3FB);
            AddUnicodeToSet(0x1F64C, 0x1F3FC);
            AddUnicodeToSet(0x1F64C, 0x1F3FD);
            AddUnicodeToSet(0x1F64C, 0x1F3FE);
            AddUnicodeToSet(0x1F64C, 0x1F3FF);
            AddUnicodeToSet(0x1F64F, 0x1F3FB);
            AddUnicodeToSet(0x1F64F, 0x1F3FC);
            AddUnicodeToSet(0x1F64F, 0x1F3FD);
            AddUnicodeToSet(0x1F64F, 0x1F3FE);
            AddUnicodeToSet(0x1F64F, 0x1F3FF);
            AddUnicodeToSet(0x1F485, 0x1F3FB);
            AddUnicodeToSet(0x1F485, 0x1F3FC);
            AddUnicodeToSet(0x1F485, 0x1F3FD);
            AddUnicodeToSet(0x1F485, 0x1F3FE);
            AddUnicodeToSet(0x1F485, 0x1F3FF);
            AddUnicodeToSet(0x1F442, 0x1F3FB);
            AddUnicodeToSet(0x1F442, 0x1F3FC);
            AddUnicodeToSet(0x1F442, 0x1F3FD);
            AddUnicodeToSet(0x1F442, 0x1F3FE);
            AddUnicodeToSet(0x1F442, 0x1F3FF);
            AddUnicodeToSet(0x1F443, 0x1F3FB);
            AddUnicodeToSet(0x1F443, 0x1F3FC);
            AddUnicodeToSet(0x1F443, 0x1F3FD);
            AddUnicodeToSet(0x1F443, 0x1F3FE);
            AddUnicodeToSet(0x1F443, 0x1F3FF);
            AddUnicodeToSet(0x1F474, 0x1F3FC);
            AddUnicodeToSet(0x1F6A3, 0x1F3FB);
            AddUnicodeToSet(0x1F6A3, 0x1F3FC);
            AddUnicodeToSet(0x1F6A3, 0x1F3FD);
            AddUnicodeToSet(0x1F6A3, 0x1F3FE);
            AddUnicodeToSet(0x1F6A3, 0x1F3FF);
            AddUnicodeToSet(0x1F6C0, 0x1F3FB);
            AddUnicodeToSet(0x1F6C0, 0x1F3FC);
            AddUnicodeToSet(0x1F6C0, 0x1F3FD);
            AddUnicodeToSet(0x1F6C0, 0x1F3FE);
            AddUnicodeToSet(0x1F6C0, 0x1F3FF);
            AddUnicodeToSet(0x1F3C4, 0x1F3FB);
            AddUnicodeToSet(0x1F3C4, 0x1F3FC);
            AddUnicodeToSet(0x1F3C4, 0x1F3FD);
            AddUnicodeToSet(0x1F3C4, 0x1F3FE);
            AddUnicodeToSet(0x1F3C4, 0x1F3FF);
            AddUnicodeToSet(0x1F3CA, 0x1F3FB);
            AddUnicodeToSet(0x1F3CA, 0x1F3FC);
            AddUnicodeToSet(0x1F3CA, 0x1F3FD);
            AddUnicodeToSet(0x1F3CA, 0x1F3FE);
            AddUnicodeToSet(0x1F3CA, 0x1F3FF);
            AddUnicodeToSet(0x1F3CB, 0x1F3FB);
            AddUnicodeToSet(0x1F3CB, 0x1F3FC);
            AddUnicodeToSet(0x1F3CB, 0x1F3FD);
            AddUnicodeToSet(0x1F3CB, 0x1F3FE);
            AddUnicodeToSet(0x1F3CB, 0x1F3FF);
            AddUnicodeToSet(0x1F6B4, 0x1F3FB);
            AddUnicodeToSet(0x1F6B4, 0x1F3FC);
            AddUnicodeToSet(0x1F6B4, 0x1F3FD);
            AddUnicodeToSet(0x1F6B4, 0x1F3FE);
            AddUnicodeToSet(0x1F6B4, 0x1F3FF);
            AddUnicodeToSet(0x1F6B5, 0x1F3FB);
            AddUnicodeToSet(0x1F6B5, 0x1F3FC);
            AddUnicodeToSet(0x1F6B5, 0x1F3FD);
            AddUnicodeToSet(0x1F6B5, 0x1F3FE);
            AddUnicodeToSet(0x1F6B5, 0x1F3FF);
            AddUnicodeToSet(0x1F1E6, 0x1F1E8);
            AddUnicodeToSet(0x1F1E6, 0x1F1E9);
            AddUnicodeToSet(0x1F1E6, 0x1F1EA);
            AddUnicodeToSet(0x1F1E6, 0x1F1EB);
            AddUnicodeToSet(0x1F1E6, 0x1F1EC);
            AddUnicodeToSet(0x1F1E6, 0x1F1EE);
            AddUnicodeToSet(0x1F1E6, 0x1F1F1);
            AddUnicodeToSet(0x1F1E6, 0x1F1F2);
            AddUnicodeToSet(0x1F1E6, 0x1F1F4);
            AddUnicodeToSet(0x1F1E6, 0x1F1F6);
            AddUnicodeToSet(0x1F1E6, 0x1F1F7);
            AddUnicodeToSet(0x1F1E6, 0x1F1F8);
            AddUnicodeToSet(0x1F1E6, 0x1F1F9);
            AddUnicodeToSet(0x1F1E6, 0x1F1FA);
            AddUnicodeToSet(0x1F1E6, 0x1F1FC);
            AddUnicodeToSet(0x1F1E6, 0x1F1FD);
            AddUnicodeToSet(0x1F1E6, 0x1F1FF);
            AddUnicodeToSet(0x1F1E7, 0x1F1E6);
            AddUnicodeToSet(0x1F1E7, 0x1F1E7);
            AddUnicodeToSet(0x1F1E7, 0x1F1E9);
            AddUnicodeToSet(0x1F1E7, 0x1F1EA);
            AddUnicodeToSet(0x1F1E7, 0x1F1EB);
            AddUnicodeToSet(0x1F1E7, 0x1F1EC);
            AddUnicodeToSet(0x1F1E7, 0x1F1ED);
            AddUnicodeToSet(0x1F1E7, 0x1F1EE);
            AddUnicodeToSet(0x1F1E7, 0x1F1EF);
            AddUnicodeToSet(0x1F1E7, 0x1F1F1);
            AddUnicodeToSet(0x1F1E7, 0x1F1F2);
            AddUnicodeToSet(0x1F1E7, 0x1F1F3);
            AddUnicodeToSet(0x1F1E7, 0x1F1F4);
            AddUnicodeToSet(0x1F1E7, 0x1F1F6);
            AddUnicodeToSet(0x1F1E7, 0x1F1F7);
            AddUnicodeToSet(0x1F1E7, 0x1F1F8);
            AddUnicodeToSet(0x1F1E7, 0x1F1F9);
            AddUnicodeToSet(0x1F1E7, 0x1F1FB);
            AddUnicodeToSet(0x1F1E7, 0x1F1FC);
            AddUnicodeToSet(0x1F1E7, 0x1F1FE);
            AddUnicodeToSet(0x1F1E7, 0x1F1FF);
            AddUnicodeToSet(0x1F1E8, 0x1F1E6);
            AddUnicodeToSet(0x1F1E8, 0x1F1E8);
            AddUnicodeToSet(0x1F1E8, 0x1F1E9);
            AddUnicodeToSet(0x1F1E8, 0x1F1EB);
            AddUnicodeToSet(0x1F1E8, 0x1F1EC);
            AddUnicodeToSet(0x1F1E8, 0x1F1ED);
            AddUnicodeToSet(0x1F1E8, 0x1F1EE);
            AddUnicodeToSet(0x1F1E8, 0x1F1F0);
            AddUnicodeToSet(0x1F1E8, 0x1F1F1);
            AddUnicodeToSet(0x1F1E8, 0x1F1F2);
            AddUnicodeToSet(0x1F1E8, 0x1F1F3);
            AddUnicodeToSet(0x1F1E8, 0x1F1F4);
            AddUnicodeToSet(0x1F1E8, 0x1F1F5);
            AddUnicodeToSet(0x1F1E8, 0x1F1F7);
            AddUnicodeToSet(0x1F1E8, 0x1F1FA);
            AddUnicodeToSet(0x1F1E8, 0x1F1FB);
            AddUnicodeToSet(0x1F1E8, 0x1F1FC);
            AddUnicodeToSet(0x1F1E8, 0x1F1FD);
            AddUnicodeToSet(0x1F1E8, 0x1F1FE);
            AddUnicodeToSet(0x1F1E8, 0x1F1FF);
            AddUnicodeToSet(0x1F1E9, 0x1F1EA);
            AddUnicodeToSet(0x1F1E9, 0x1F1EC);
            AddUnicodeToSet(0x1F1E9, 0x1F1EF);
            AddUnicodeToSet(0x1F1E9, 0x1F1F0);
            AddUnicodeToSet(0x1F1E9, 0x1F1F2);
            AddUnicodeToSet(0x1F1E9, 0x1F1F4);
            AddUnicodeToSet(0x1F1E9, 0x1F1FF);
            AddUnicodeToSet(0x1F1EA, 0x1F1E6);
            AddUnicodeToSet(0x1F1EA, 0x1F1E8);
            AddUnicodeToSet(0x1F1EA, 0x1F1EA);
            AddUnicodeToSet(0x1F1EA, 0x1F1EC);
            AddUnicodeToSet(0x1F1EA, 0x1F1ED);
            AddUnicodeToSet(0x1F1EA, 0x1F1F7);
            AddUnicodeToSet(0x1F1EA, 0x1F1F8);
            AddUnicodeToSet(0x1F1EA, 0x1F1F9);
            AddUnicodeToSet(0x1F1EA, 0x1F1FA);
            AddUnicodeToSet(0x1F1EB, 0x1F1EE);
            AddUnicodeToSet(0x1F1EB, 0x1F1EF);
            AddUnicodeToSet(0x1F1EB, 0x1F1F0);
            AddUnicodeToSet(0x1F1EB, 0x1F1F2);
            AddUnicodeToSet(0x1F1EB, 0x1F1F4);
            AddUnicodeToSet(0x1F1EB, 0x1F1F7);
            AddUnicodeToSet(0x1F1EC, 0x1F1E6);
            AddUnicodeToSet(0x1F1EC, 0x1F1E7);
            AddUnicodeToSet(0x1F1EC, 0x1F1E9);
            AddUnicodeToSet(0x1F1EC, 0x1F1EA);
            AddUnicodeToSet(0x1F1EC, 0x1F1EB);
            AddUnicodeToSet(0x1F1EC, 0x1F1EC);
            AddUnicodeToSet(0x1F1EC, 0x1F1ED);
            AddUnicodeToSet(0x1F1EC, 0x1F1EE);
            AddUnicodeToSet(0x1F1EC, 0x1F1F1);
            AddUnicodeToSet(0x1F1EC, 0x1F1F2);
            AddUnicodeToSet(0x1F1EC, 0x1F1F3);
            AddUnicodeToSet(0x1F1EC, 0x1F1F5);
            AddUnicodeToSet(0x1F1EC, 0x1F1F6);
            AddUnicodeToSet(0x1F1EC, 0x1F1F7);
            AddUnicodeToSet(0x1F1EC, 0x1F1F8);
            AddUnicodeToSet(0x1F1EC, 0x1F1F9);
            AddUnicodeToSet(0x1F1EC, 0x1F1FA);
            AddUnicodeToSet(0x1F1EC, 0x1F1FC);
            AddUnicodeToSet(0x1F1EC, 0x1F1FE);
            AddUnicodeToSet(0x1F1ED, 0x1F1F0);
            AddUnicodeToSet(0x1F1ED, 0x1F1F2);
            AddUnicodeToSet(0x1F1ED, 0x1F1F3);
            AddUnicodeToSet(0x1F1ED, 0x1F1F7);
            AddUnicodeToSet(0x1F1ED, 0x1F1F9);
            AddUnicodeToSet(0x1F1ED, 0x1F1FA);
            AddUnicodeToSet(0x1F1EE, 0x1F1E8);
            AddUnicodeToSet(0x1F1EE, 0x1F1E9);
            AddUnicodeToSet(0x1F1EE, 0x1F1EA);
            AddUnicodeToSet(0x1F1EE, 0x1F1F1);
            AddUnicodeToSet(0x1F1EE, 0x1F1F2);
            AddUnicodeToSet(0x1F1EE, 0x1F1F3);
            AddUnicodeToSet(0x1F1EE, 0x1F1F4);
            AddUnicodeToSet(0x1F1EE, 0x1F1F6);
            AddUnicodeToSet(0x1F1EE, 0x1F1F7);
            AddUnicodeToSet(0x1F1EE, 0x1F1F8);
            AddUnicodeToSet(0x1F1EE, 0x1F1F9);
            AddUnicodeToSet(0x1F1EF, 0x1F1EA);
            AddUnicodeToSet(0x1F1EF, 0x1F1F2);
            AddUnicodeToSet(0x1F1EF, 0x1F1F4);
            AddUnicodeToSet(0x1F1EF, 0x1F1F5);
            AddUnicodeToSet(0x1F1F0, 0x1F1EA);
            AddUnicodeToSet(0x1F1F0, 0x1F1EC);
            AddUnicodeToSet(0x1F1F0, 0x1F1ED);
            AddUnicodeToSet(0x1F1F0, 0x1F1EE);
            AddUnicodeToSet(0x1F1F0, 0x1F1F2);
            AddUnicodeToSet(0x1F1F0, 0x1F1F3);
            AddUnicodeToSet(0x1F1F0, 0x1F1F5);
            AddUnicodeToSet(0x1F1F0, 0x1F1F7);
            AddUnicodeToSet(0x1F1F0, 0x1F1FC);
            AddUnicodeToSet(0x1F1F0, 0x1F1FE);
            AddUnicodeToSet(0x1F1F0, 0x1F1FF);
            AddUnicodeToSet(0x1F1F1, 0x1F1E6);
            AddUnicodeToSet(0x1F1F1, 0x1F1E7);
            AddUnicodeToSet(0x1F1F1, 0x1F1E8);
            AddUnicodeToSet(0x1F1F1, 0x1F1EE);
            AddUnicodeToSet(0x1F1F1, 0x1F1F0);
            AddUnicodeToSet(0x1F1F1, 0x1F1F7);
            AddUnicodeToSet(0x1F1F1, 0x1F1F8);
            AddUnicodeToSet(0x1F1F1, 0x1F1F9);
            AddUnicodeToSet(0x1F1F1, 0x1F1FA);
            AddUnicodeToSet(0x1F1F1, 0x1F1FB);
            AddUnicodeToSet(0x1F1F1, 0x1F1FE);
            AddUnicodeToSet(0x1F1F2, 0x1F1E6);
            AddUnicodeToSet(0x1F1F2, 0x1F1E8);
            AddUnicodeToSet(0x1F1F2, 0x1F1E9);
            AddUnicodeToSet(0x1F1F2, 0x1F1EA);
            AddUnicodeToSet(0x1F1F2, 0x1F1EB);
            AddUnicodeToSet(0x1F1F2, 0x1F1EC);
            AddUnicodeToSet(0x1F1F2, 0x1F1ED);
            AddUnicodeToSet(0x1F1F2, 0x1F1F0);
            AddUnicodeToSet(0x1F1F2, 0x1F1F1);
            AddUnicodeToSet(0x1F1F2, 0x1F1F2);
            AddUnicodeToSet(0x1F1F2, 0x1F1F3);
            AddUnicodeToSet(0x1F1F2, 0x1F1F4);
            AddUnicodeToSet(0x1F1F2, 0x1F1F5);
            AddUnicodeToSet(0x1F1F2, 0x1F1F6);
            AddUnicodeToSet(0x1F1F2, 0x1F1F7);
            AddUnicodeToSet(0x1F1F2, 0x1F1F8);
            AddUnicodeToSet(0x1F1F2, 0x1F1F9);
            AddUnicodeToSet(0x1F1F2, 0x1F1FA);
            AddUnicodeToSet(0x1F1F2, 0x1F1FB);
            AddUnicodeToSet(0x1F1F2, 0x1F1FC);
            AddUnicodeToSet(0x1F1F2, 0x1F1FD);
            AddUnicodeToSet(0x1F1F2, 0x1F1FE);
            AddUnicodeToSet(0x1F1F2, 0x1F1FF);
            AddUnicodeToSet(0x1F1F3, 0x1F1E6);
            AddUnicodeToSet(0x1F1F3, 0x1F1E8);
            AddUnicodeToSet(0x1F1F3, 0x1F1EA);
            AddUnicodeToSet(0x1F1F3, 0x1F1EB);
            AddUnicodeToSet(0x1F1F3, 0x1F1EC);
            AddUnicodeToSet(0x1F1F3, 0x1F1EE);
            AddUnicodeToSet(0x1F1F3, 0x1F1F1);
            AddUnicodeToSet(0x1F1F3, 0x1F1F4);
            AddUnicodeToSet(0x1F1F3, 0x1F1F5);
            AddUnicodeToSet(0x1F1F3, 0x1F1F7);
            AddUnicodeToSet(0x1F1F3, 0x1F1FA);
            AddUnicodeToSet(0x1F1F3, 0x1F1FF);
            AddUnicodeToSet(0x1F1F4, 0x1F1F2);
            AddUnicodeToSet(0x1F1F5, 0x1F1E6);
            AddUnicodeToSet(0x1F1F5, 0x1F1EA);
            AddUnicodeToSet(0x1F1F5, 0x1F1EB);
            AddUnicodeToSet(0x1F1F5, 0x1F1EC);
            AddUnicodeToSet(0x1F1F5, 0x1F1ED);
            AddUnicodeToSet(0x1F1F5, 0x1F1F0);
            AddUnicodeToSet(0x1F1F5, 0x1F1F1);
            AddUnicodeToSet(0x1F1F5, 0x1F1F2);
            AddUnicodeToSet(0x1F1F5, 0x1F1F3);
            AddUnicodeToSet(0x1F1F5, 0x1F1F7);
            AddUnicodeToSet(0x1F1F5, 0x1F1F8);
            AddUnicodeToSet(0x1F1F5, 0x1F1F9);
            AddUnicodeToSet(0x1F1F5, 0x1F1FC);
            AddUnicodeToSet(0x1F1F5, 0x1F1FE);
            AddUnicodeToSet(0x1F1F6, 0x1F1E6);
            AddUnicodeToSet(0x1F1F7, 0x1F1EA);
            AddUnicodeToSet(0x1F1F7, 0x1F1F4);
            AddUnicodeToSet(0x1F1F7, 0x1F1F8);
            AddUnicodeToSet(0x1F1F7, 0x1F1FA);
            AddUnicodeToSet(0x1F1F7, 0x1F1FC);
            AddUnicodeToSet(0x1F1F8, 0x1F1E6);
            AddUnicodeToSet(0x1F1F8, 0x1F1E7);
            AddUnicodeToSet(0x1F1F8, 0x1F1E8);
            AddUnicodeToSet(0x1F1F8, 0x1F1E9);
            AddUnicodeToSet(0x1F1F8, 0x1F1EA);
            AddUnicodeToSet(0x1F1F8, 0x1F1EC);
            AddUnicodeToSet(0x1F1F8, 0x1F1ED);
            AddUnicodeToSet(0x1F1F8, 0x1F1EE);
            AddUnicodeToSet(0x1F1F8, 0x1F1EF);
            AddUnicodeToSet(0x1F1F8, 0x1F1F0);
            AddUnicodeToSet(0x1F1F8, 0x1F1F1);
            AddUnicodeToSet(0x1F1F8, 0x1F1F2);
            AddUnicodeToSet(0x1F1F8, 0x1F1F3);
            AddUnicodeToSet(0x1F1F8, 0x1F1F4);
            AddUnicodeToSet(0x1F1F8, 0x1F1F7);
            AddUnicodeToSet(0x1F1F8, 0x1F1F8);
            AddUnicodeToSet(0x1F1F8, 0x1F1F9);
            AddUnicodeToSet(0x1F1F8, 0x1F1FB);
            AddUnicodeToSet(0x1F1F8, 0x1F1FD);
            AddUnicodeToSet(0x1F1F8, 0x1F1FE);
            AddUnicodeToSet(0x1F1F8, 0x1F1FF);
            AddUnicodeToSet(0x1F1F9, 0x1F1E6);
            AddUnicodeToSet(0x1F1F9, 0x1F1E8);
            AddUnicodeToSet(0x1F1F9, 0x1F1E9);
            AddUnicodeToSet(0x1F1F9, 0x1F1EB);
            AddUnicodeToSet(0x1F1F9, 0x1F1EC);
            AddUnicodeToSet(0x1F1F9, 0x1F1ED);
            AddUnicodeToSet(0x1F1F9, 0x1F1EF);
            AddUnicodeToSet(0x1F1F9, 0x1F1F0);
            AddUnicodeToSet(0x1F1F9, 0x1F1F1);
            AddUnicodeToSet(0x1F1F9, 0x1F1F2);
            AddUnicodeToSet(0x1F1F9, 0x1F1F3);
            AddUnicodeToSet(0x1F1F9, 0x1F1F4);
            AddUnicodeToSet(0x1F1F9, 0x1F1F7);
            AddUnicodeToSet(0x1F1F9, 0x1F1F9);
            AddUnicodeToSet(0x1F1F9, 0x1F1FB);
            AddUnicodeToSet(0x1F1F9, 0x1F1FC);
            AddUnicodeToSet(0x1F1F9, 0x1F1FF);
            AddUnicodeToSet(0x1F1FA, 0x1F1E6);
            AddUnicodeToSet(0x1F1FA, 0x1F1EC);
            AddUnicodeToSet(0x1F1FA, 0x1F1F2);
            AddUnicodeToSet(0x1F1FA, 0x1F1F8);
            AddUnicodeToSet(0x1F1FA, 0x1F1FE);
            AddUnicodeToSet(0x1F1FA, 0x1F1FF);
            AddUnicodeToSet(0x1F1FB, 0x1F1E6);
            AddUnicodeToSet(0x1F1FB, 0x1F1E8);
            AddUnicodeToSet(0x1F1FB, 0x1F1EA);
            AddUnicodeToSet(0x1F1FB, 0x1F1EC);
            AddUnicodeToSet(0x1F1FB, 0x1F1EE);
            AddUnicodeToSet(0x1F1FB, 0x1F1F3);
            AddUnicodeToSet(0x1F1FB, 0x1F1FA);
            AddUnicodeToSet(0x1F1FC, 0x1F1EB);
            AddUnicodeToSet(0x1F1FC, 0x1F1F8);
            AddUnicodeToSet(0x1F1FD, 0x1F1F0);
            AddUnicodeToSet(0x1F1FE, 0x1F1EA);
            AddUnicodeToSet(0x1F1FE, 0x1F1F9);
            AddUnicodeToSet(0x1F1FF, 0x1F1E6);
            AddUnicodeToSet(0x1F1FF, 0x1F1F2);
            AddUnicodeToSet(0x1F1FF, 0x1F1FC);
            AddUnicodeToSet(0x270B, 0x1F3FB);
            AddUnicodeToSet(0x270B, 0x1F3FC);
            AddUnicodeToSet(0x270B, 0x1F3FD);
            AddUnicodeToSet(0x270B, 0x1F3FE);
            AddUnicodeToSet(0x270B, 0x1F3FF);
            AddUnicodeToSet(0x270D, 0x1F3FB);
            AddUnicodeToSet(0x270D, 0x1F3FC);
            AddUnicodeToSet(0x270D, 0x1F3FD);
            AddUnicodeToSet(0x270D, 0x1F3FE);
            AddUnicodeToSet(0x270D, 0x1F3FF);
            AddUnicodeToSet(0x270A, 0x1F3FB);
            AddUnicodeToSet(0x26F9, 0x1F3FB);
            AddUnicodeToSet(0x26F9, 0x1F3FC);
            AddUnicodeToSet(0x26F9, 0x1F3FD);
            AddUnicodeToSet(0x26F9, 0x1F3FE);
            AddUnicodeToSet(0x26F9, 0x1F3FF);
            AddUnicodeToSet(0x270A, 0x1F3FC);
            AddUnicodeToSet(0x270A, 0x1F3FD);
            AddUnicodeToSet(0x270A, 0x1F3FE);
            AddUnicodeToSet(0x270A, 0x1F3FF);
            AddUnicodeToSet(0x270C, 0x1F3FB);
            AddUnicodeToSet(0x270C, 0x1F3FC);
            AddUnicodeToSet(0x270C, 0x1F3FD);
            AddUnicodeToSet(0x270C, 0x1F3FE);
            AddUnicodeToSet(0x270C, 0x1F3FF);
            AddUnicodeToSet(0x261D, 0x1F3FB);
            AddUnicodeToSet(0x261D, 0x1F3FC);
            AddUnicodeToSet(0x261D, 0x1F3FD);
            AddUnicodeToSet(0x261D, 0x1F3FE);
            AddUnicodeToSet(0x261D, 0x1F3FF);
            AddUnicodeToSet(0x1F6D0);
            AddUnicodeToSet(0x1F549);
            AddUnicodeToSet(0x1F54E);
            AddUnicodeToSet(0x1F32E);
            AddUnicodeToSet(0x1F32F);
            AddUnicodeToSet(0x1F37E);
            AddUnicodeToSet(0x1F3FA);
            AddUnicodeToSet(0x1F5FA);
            AddUnicodeToSet(0x1F3D4);
            AddUnicodeToSet(0x1F3D5);
            AddUnicodeToSet(0x1F3D6);
            AddUnicodeToSet(0x1F3DC);
            AddUnicodeToSet(0x1F3DD);
            AddUnicodeToSet(0x1F3DE);
            AddUnicodeToSet(0x1F3DF);
            AddUnicodeToSet(0x1F3F4);
            AddUnicodeToSet(0x1F3F3);
            AddUnicodeToSet(0x1F3DB);
            AddUnicodeToSet(0x1F3D7);
            AddUnicodeToSet(0x1F3D8);
            AddUnicodeToSet(0x1F3D9);
            AddUnicodeToSet(0x1F3DA);
            AddUnicodeToSet(0x1F54C);
            AddUnicodeToSet(0x1F54D);
            AddUnicodeToSet(0x1F54B);
            AddUnicodeToSet(0x1F5BC);
            AddUnicodeToSet(0x1F69E);
            AddUnicodeToSet(0x1F6E3);
            AddUnicodeToSet(0x1F6E4);
            AddUnicodeToSet(0x1F915);
            AddUnicodeToSet(0x1F918);
            AddUnicodeToSet(0x1F595);
            AddUnicodeToSet(0x1F916);
            AddUnicodeToSet(0x1F642);
            AddUnicodeToSet(0x1F6F3);
            AddUnicodeToSet(0x1F6E5);
            AddUnicodeToSet(0x1F6E9);
            AddUnicodeToSet(0x1F6EB);
            AddUnicodeToSet(0x1F6EC);
            AddUnicodeToSet(0x1F6F0);
            AddUnicodeToSet(0x1F6CE);
            AddUnicodeToSet(0x1F6CC);
            AddUnicodeToSet(0x1F6CF);
            AddUnicodeToSet(0x1F6CB);
            AddUnicodeToSet(0x1F574);
            AddUnicodeToSet(0x1F5E3);
            AddUnicodeToSet(0x1F917);
            AddUnicodeToSet(0x1F914);
            AddUnicodeToSet(0x1F575);
            AddUnicodeToSet(0x1F570);
            AddUnicodeToSet(0x1F321);
            AddUnicodeToSet(0x1F324);
            AddUnicodeToSet(0x1F325);
            AddUnicodeToSet(0x1F326);
            AddUnicodeToSet(0x1F327);
            AddUnicodeToSet(0x1F328);
            AddUnicodeToSet(0x1F329);
            AddUnicodeToSet(0x1F32A);
            AddUnicodeToSet(0x1F32B);
            AddUnicodeToSet(0x1F32C);
            AddUnicodeToSet(0x1F397);
            AddUnicodeToSet(0x1F39F);
            AddUnicodeToSet(0x1F396);
            AddUnicodeToSet(0x1F3C5);
            AddUnicodeToSet(0x1F3D0);
            AddUnicodeToSet(0x1F3CF);
            AddUnicodeToSet(0x1F3D1);
            AddUnicodeToSet(0x1F3D2);
            AddUnicodeToSet(0x1F3D3);
            AddUnicodeToSet(0x1F3F8);
            AddUnicodeToSet(0x1F3CC);
            AddUnicodeToSet(0x1F644);
            AddUnicodeToSet(0x1F910);
            AddUnicodeToSet(0x1F913);
            AddUnicodeToSet(0x1F3FB);
            AddUnicodeToSet(0x1F3FC);
            AddUnicodeToSet(0x1F3FD);
            AddUnicodeToSet(0x1F3FE);
            AddUnicodeToSet(0x1F3FF);
            AddUnicodeToSet(0x1F643);
            AddUnicodeToSet(0x1F911);
            AddUnicodeToSet(0x1F641);
            AddUnicodeToSet(0x1F441);
            AddUnicodeToSet(0x1F912);
            AddUnicodeToSet(0x1F5E8);
            AddUnicodeToSet(0x1F5EF);
            AddUnicodeToSet(0x1F3CB);
            AddUnicodeToSet(0x1F573);
            AddUnicodeToSet(0x1F576);
            AddUnicodeToSet(0x1F6CD);
            AddUnicodeToSet(0x1F4FF);
            AddUnicodeToSet(0x1F981);
            AddUnicodeToSet(0x1F43F);
            AddUnicodeToSet(0x1F983);
            AddUnicodeToSet(0x1F54A);
            AddUnicodeToSet(0x1F980);
            AddUnicodeToSet(0x1F577);
            AddUnicodeToSet(0x1F578);
            AddUnicodeToSet(0x1F982);
            AddUnicodeToSet(0x1F3F5);
            AddUnicodeToSet(0x1F336);
            AddUnicodeToSet(0x1F32D);
            AddUnicodeToSet(0x1F3CE);
            AddUnicodeToSet(0x1F3CD);
            AddUnicodeToSet(0x1F579);
            AddUnicodeToSet(0x1F399);
            AddUnicodeToSet(0x1F39A);
            AddUnicodeToSet(0x1F39B);
            AddUnicodeToSet(0x1F5A5);
            AddUnicodeToSet(0x1F5A8);
            AddUnicodeToSet(0x1F5B1);
            AddUnicodeToSet(0x1F5B2);
            AddUnicodeToSet(0x1F39E);
            AddUnicodeToSet(0x1F4FD);
            AddUnicodeToSet(0x1F4F8);
            AddUnicodeToSet(0x1F56F);
            AddUnicodeToSet(0x1F5DE);
            AddUnicodeToSet(0x1F3F7);
            AddUnicodeToSet(0x1F5F3);
            AddUnicodeToSet(0x1F58B);
            AddUnicodeToSet(0x1F58A);
            AddUnicodeToSet(0x1F58C);
            AddUnicodeToSet(0x1F58D);
            AddUnicodeToSet(0x1F5C2);
            AddUnicodeToSet(0x1F5D2);
            AddUnicodeToSet(0x1F5D3);
            AddUnicodeToSet(0x1F587);
            AddUnicodeToSet(0x1F5C3);
            AddUnicodeToSet(0x1F5C4);
            AddUnicodeToSet(0x1F5D1);
            AddUnicodeToSet(0x1F5DD);
            AddUnicodeToSet(0x1F6E0);
            AddUnicodeToSet(0x1F5E1);
            AddUnicodeToSet(0x1F3F9);
            AddUnicodeToSet(0x1F6E1);
            AddUnicodeToSet(0x1F5DC);
            AddUnicodeToSet(0x1F6E2);
            AddUnicodeToSet(0x2626);
            AddUnicodeToSet(0x262A);
            AddUnicodeToSet(0x262E);
            AddUnicodeToSet(0x26C8);
            AddUnicodeToSet(0x269C);
            AddUnicodeToSet(0x23ED);
            AddUnicodeToSet(0x23EF);
            AddUnicodeToSet(0x23EE);
            AddUnicodeToSet(0x23F8);
            AddUnicodeToSet(0x23F9);
            AddUnicodeToSet(0x23FA);
            AddUnicodeToSet(0x2639);
            AddUnicodeToSet(0x26F9);
            AddUnicodeToSet(0x26F4);
            AddUnicodeToSet(0x26D1);
            AddUnicodeToSet(0x2618);
            AddUnicodeToSet(0x270D);
            AddUnicodeToSet(0x2620);
            AddUnicodeToSet(0x26E9);
            AddUnicodeToSet(0x23F1);
            AddUnicodeToSet(0x26F8);
            AddUnicodeToSet(0x26F7);
            AddUnicodeToSet(0x2328);
            AddUnicodeToSet(0x26CF);
            AddUnicodeToSet(0x2692);
            AddUnicodeToSet(0x23F2);
            AddUnicodeToSet(0x2602);
            AddUnicodeToSet(0x2694);
            AddUnicodeToSet(0x26F1);
            AddUnicodeToSet(0x2603);
            AddUnicodeToSet(0x2699);
            AddUnicodeToSet(0x2604);
            AddUnicodeToSet(0x2697);
            AddUnicodeToSet(0x2696);
            AddUnicodeToSet(0x26D3);
            AddUnicodeToSet(0x26B0);
            AddUnicodeToSet(0x26B1);
            AddUnicodeToSet(0x2728);
            AddUnicodeToSet(0x2622);
            AddUnicodeToSet(0x2623);
            AddUnicodeToSet(0x26F0);
            AddUnicodeToSet(0x269B);
            AddUnicodeToSet(0x2763);
            AddUnicodeToSet(0x2721);
            AddUnicodeToSet(0x2638);
            AddUnicodeToSet(0x262F);
            AddUnicodeToSet(0x271D);
            AddUnicodeToSet(0x1F604);
            AddUnicodeToSet(0x1F603);
            AddUnicodeToSet(0x1F600);
            AddUnicodeToSet(0x1F60A);

            AddUnicodeToSet(0x263A);
            AddUnicodeToSet(0x261D);
            AddUnicodeToSet(0x270C);

            AddUnicodeToSet(0x1F609);
            AddUnicodeToSet(0x1F60D);
            AddUnicodeToSet(0x1F618);
            AddUnicodeToSet(0x1F61A);
            AddUnicodeToSet(0x1F617);
            AddUnicodeToSet(0x1F619);
            AddUnicodeToSet(0x1F61C);
            AddUnicodeToSet(0x1F61D);
            AddUnicodeToSet(0x1F61B);
            AddUnicodeToSet(0x1F633);
            AddUnicodeToSet(0x1F601);
            AddUnicodeToSet(0x1F614);
            AddUnicodeToSet(0x1F60C);
            AddUnicodeToSet(0x1F612);
            AddUnicodeToSet(0x1F61E);
            AddUnicodeToSet(0x1F623);
            AddUnicodeToSet(0x1F622);
            AddUnicodeToSet(0x1F602);
            AddUnicodeToSet(0x1F62D);
            AddUnicodeToSet(0x1F62A);
            AddUnicodeToSet(0x1F625);
            AddUnicodeToSet(0x1F630);
            AddUnicodeToSet(0x1F605);
            AddUnicodeToSet(0x1F613);
            AddUnicodeToSet(0x1F629);
            AddUnicodeToSet(0x1F62B);
            AddUnicodeToSet(0x1F628);
            AddUnicodeToSet(0x1F631);
            AddUnicodeToSet(0x1F620);
            AddUnicodeToSet(0x1F621);
            AddUnicodeToSet(0x1F624);
            AddUnicodeToSet(0x1F616);
            AddUnicodeToSet(0x1F606);
            AddUnicodeToSet(0x1F60B);
            AddUnicodeToSet(0x1F637);
            AddUnicodeToSet(0x1F60E);
            AddUnicodeToSet(0x1F634);
            AddUnicodeToSet(0x1F635);
            AddUnicodeToSet(0x1F632);
            AddUnicodeToSet(0x1F61F);
            AddUnicodeToSet(0x1F626);
            AddUnicodeToSet(0x1F627);
            AddUnicodeToSet(0x1F608);
            AddUnicodeToSet(0x1F47F);
            AddUnicodeToSet(0x1F62E);
            AddUnicodeToSet(0x1F62C);
            AddUnicodeToSet(0x1F610);
            AddUnicodeToSet(0x1F615);
            AddUnicodeToSet(0x1F62F);
            AddUnicodeToSet(0x1F636);
            AddUnicodeToSet(0x1F607);
            AddUnicodeToSet(0x1F60F);
            AddUnicodeToSet(0x1F611);
            AddUnicodeToSet(0x1F472);
            AddUnicodeToSet(0x1F473);
            AddUnicodeToSet(0x1F46E);
            AddUnicodeToSet(0x1F477);
            AddUnicodeToSet(0x1F482);
            AddUnicodeToSet(0x1F476);
            AddUnicodeToSet(0x1F466);
            AddUnicodeToSet(0x1F467);
            AddUnicodeToSet(0x1F468);
            AddUnicodeToSet(0x1F469);
            AddUnicodeToSet(0x1F474);
            AddUnicodeToSet(0x1F475);
            AddUnicodeToSet(0x1F471);
            AddUnicodeToSet(0x1F47C);
            AddUnicodeToSet(0x1F478);
            AddUnicodeToSet(0x1F63A);
            AddUnicodeToSet(0x1F638);
            AddUnicodeToSet(0x1F63B);
            AddUnicodeToSet(0x1F63D);
            AddUnicodeToSet(0x1F63C);
            AddUnicodeToSet(0x1F640);
            AddUnicodeToSet(0x1F63F);
            AddUnicodeToSet(0x1F639);
            AddUnicodeToSet(0x1F63E);
            AddUnicodeToSet(0x1F479);
            AddUnicodeToSet(0x1F47A);
            AddUnicodeToSet(0x1F648);
            AddUnicodeToSet(0x1F649);
            AddUnicodeToSet(0x1F64A);
            AddUnicodeToSet(0x1F480);
            AddUnicodeToSet(0x1F47D);
            AddUnicodeToSet(0x1F4A9);
            AddUnicodeToSet(0x1F525);
            AddUnicodeToSet(0x1F31F);
            AddUnicodeToSet(0x1F4AB);
            AddUnicodeToSet(0x1F4A5);
            AddUnicodeToSet(0x1F4A2);
            AddUnicodeToSet(0x1F4A6);
            AddUnicodeToSet(0x1F4A7);
            AddUnicodeToSet(0x1F4A4);
            AddUnicodeToSet(0x1F4A8);
            AddUnicodeToSet(0x1F442);
            AddUnicodeToSet(0x1F440);
            AddUnicodeToSet(0x1F443);
            AddUnicodeToSet(0x1F445);
            AddUnicodeToSet(0x1F444);
            AddUnicodeToSet(0x1F44D);
            AddUnicodeToSet(0x1F44E);
            AddUnicodeToSet(0x1F44C);
            AddUnicodeToSet(0x1F44A);
            AddUnicodeToSet(0x270A);
            AddUnicodeToSet(0x1F44B);
            AddUnicodeToSet(0x270B);
            AddUnicodeToSet(0x1F450);
            AddUnicodeToSet(0x1F446);
            AddUnicodeToSet(0x1F447);
            AddUnicodeToSet(0x1F449);
            AddUnicodeToSet(0x1F448);
            AddUnicodeToSet(0x1F64C);
            AddUnicodeToSet(0x1F64F);
            AddUnicodeToSet(0x1F44F);
            AddUnicodeToSet(0x1F4AA);
            AddUnicodeToSet(0x1F6B6);
            AddUnicodeToSet(0x1F3C3);
            AddUnicodeToSet(0x1F483);
            AddUnicodeToSet(0x1F46B);
            AddUnicodeToSet(0x1F46A);
            AddUnicodeToSet(0x1F46C);
            AddUnicodeToSet(0x1F46D);
            AddUnicodeToSet(0x1F48F);
            AddUnicodeToSet(0x1F491);
            AddUnicodeToSet(0x1F46F);
            AddUnicodeToSet(0x1F646);
            AddUnicodeToSet(0x1F645);
            AddUnicodeToSet(0x1F481);
            AddUnicodeToSet(0x1F64B);
            AddUnicodeToSet(0x1F486);
            AddUnicodeToSet(0x1F487);
            AddUnicodeToSet(0x1F485);
            AddUnicodeToSet(0x1F470);
            AddUnicodeToSet(0x1F64E);
            AddUnicodeToSet(0x1F64D);
            AddUnicodeToSet(0x1F647);
            AddUnicodeToSet(0x1F3A9);
            AddUnicodeToSet(0x1F451);
            AddUnicodeToSet(0x1F452);
            AddUnicodeToSet(0x1F45F);
            AddUnicodeToSet(0x1F45E);
            AddUnicodeToSet(0x1F461);
            AddUnicodeToSet(0x1F460);
            AddUnicodeToSet(0x1F462);
            AddUnicodeToSet(0x1F455);
            AddUnicodeToSet(0x1F454);
            AddUnicodeToSet(0x1F45A);
            AddUnicodeToSet(0x1F457);
            AddUnicodeToSet(0x1F3BD);
            AddUnicodeToSet(0x1F456);
            AddUnicodeToSet(0x1F458);
            AddUnicodeToSet(0x1F459);
            AddUnicodeToSet(0x1F4BC);
            AddUnicodeToSet(0x1F45C);
            AddUnicodeToSet(0x1F45D);
            AddUnicodeToSet(0x1F45B);
            AddUnicodeToSet(0x1F453);
            AddUnicodeToSet(0x1F380);
            AddUnicodeToSet(0x1F302);
            AddUnicodeToSet(0x1F484);
            AddUnicodeToSet(0x1F49B);
            AddUnicodeToSet(0x1F499);
            AddUnicodeToSet(0x1F49C);
            AddUnicodeToSet(0x1F49A);
            AddUnicodeToSet(0x2764);
            AddUnicodeToSet(0x1F494);
            AddUnicodeToSet(0x1F497);
            AddUnicodeToSet(0x1F493);
            AddUnicodeToSet(0x1F495);
            AddUnicodeToSet(0x1F496);
            AddUnicodeToSet(0x1F49E);
            AddUnicodeToSet(0x1F498);
            AddUnicodeToSet(0x1F48C);
            AddUnicodeToSet(0x1F48B);
            AddUnicodeToSet(0x1F48D);
            AddUnicodeToSet(0x1F48E);
            AddUnicodeToSet(0x1F464);
            AddUnicodeToSet(0x1F465);
            AddUnicodeToSet(0x1F4AC);
            AddUnicodeToSet(0x1F463);
            AddUnicodeToSet(0x1F4AD);
            AddUnicodeToSet(0x1F436);
            AddUnicodeToSet(0x1F43A);
            AddUnicodeToSet(0x1F42D);
            AddUnicodeToSet(0x1F431);
            AddUnicodeToSet(0x1F439);
            AddUnicodeToSet(0x1F430);
            AddUnicodeToSet(0x1F438);
            AddUnicodeToSet(0x1F42F);
            AddUnicodeToSet(0x1F428);
            AddUnicodeToSet(0x1F43B);
            AddUnicodeToSet(0x1F437);
            AddUnicodeToSet(0x1F43D);
            AddUnicodeToSet(0x1F42E);
            AddUnicodeToSet(0x1F417);
            AddUnicodeToSet(0x1F435);
            AddUnicodeToSet(0x1F412);
            AddUnicodeToSet(0x1F434);
            AddUnicodeToSet(0x1F411);
            AddUnicodeToSet(0x1F418);
            AddUnicodeToSet(0x1F43C);
            AddUnicodeToSet(0x1F427);
            AddUnicodeToSet(0x1F426);
            AddUnicodeToSet(0x1F424);
            AddUnicodeToSet(0x1F425);
            AddUnicodeToSet(0x1F423);
            AddUnicodeToSet(0x1F414);
            AddUnicodeToSet(0x1F40D);
            AddUnicodeToSet(0x1F422);
            AddUnicodeToSet(0x1F41B);
            AddUnicodeToSet(0x1F41D);
            AddUnicodeToSet(0x1F41C);
            AddUnicodeToSet(0x1F41E);
            AddUnicodeToSet(0x1F40C);
            AddUnicodeToSet(0x1F419);
            AddUnicodeToSet(0x1F41A);
            AddUnicodeToSet(0x1F420);
            AddUnicodeToSet(0x1F41F);
            AddUnicodeToSet(0x1F42C);
            AddUnicodeToSet(0x1F433);
            AddUnicodeToSet(0x1F40B);
            AddUnicodeToSet(0x1F404);
            AddUnicodeToSet(0x1F40F);
            AddUnicodeToSet(0x1F400);
            AddUnicodeToSet(0x1F403);
            AddUnicodeToSet(0x1F405);
            AddUnicodeToSet(0x1F407);
            AddUnicodeToSet(0x1F409);
            AddUnicodeToSet(0x1F40E);
            AddUnicodeToSet(0x1F410);
            AddUnicodeToSet(0x1F413);
            AddUnicodeToSet(0x1F415);
            AddUnicodeToSet(0x1F416);
            AddUnicodeToSet(0x1F401);
            AddUnicodeToSet(0x1F402);
            AddUnicodeToSet(0x1F432);
            AddUnicodeToSet(0x1F421);
            AddUnicodeToSet(0x1F40A);
            AddUnicodeToSet(0x1F42B);
            AddUnicodeToSet(0x1F42A);
            AddUnicodeToSet(0x1F406);
            AddUnicodeToSet(0x1F408);
            AddUnicodeToSet(0x1F429);
            AddUnicodeToSet(0x1F43E);
            AddUnicodeToSet(0x1F490);
            AddUnicodeToSet(0x1F338);
            AddUnicodeToSet(0x1F337);
            AddUnicodeToSet(0x1F340);
            AddUnicodeToSet(0x1F339);
            AddUnicodeToSet(0x1F33B);
            AddUnicodeToSet(0x1F33A);
            AddUnicodeToSet(0x1F341);
            AddUnicodeToSet(0x1F343);
            AddUnicodeToSet(0x1F342);
            AddUnicodeToSet(0x1F33F);
            AddUnicodeToSet(0x1F33E);
            AddUnicodeToSet(0x1F344);
            AddUnicodeToSet(0x1F335);
            AddUnicodeToSet(0x1F334);
            AddUnicodeToSet(0x1F332);
            AddUnicodeToSet(0x1F333);
            AddUnicodeToSet(0x1F330);
            AddUnicodeToSet(0x1F331);
            AddUnicodeToSet(0x1F33C);
            AddUnicodeToSet(0x1F310);
            AddUnicodeToSet(0x1F31E);
            AddUnicodeToSet(0x1F31D);
            AddUnicodeToSet(0x1F31A);
            AddUnicodeToSet(0x1F311);
            AddUnicodeToSet(0x1F312);
            AddUnicodeToSet(0x1F313);
            AddUnicodeToSet(0x1F314);
            AddUnicodeToSet(0x1F315);
            AddUnicodeToSet(0x1F316);
            AddUnicodeToSet(0x1F317);
            AddUnicodeToSet(0x1F318);
            AddUnicodeToSet(0x1F31C);
            AddUnicodeToSet(0x1F31B);
            AddUnicodeToSet(0x1F319);
            AddUnicodeToSet(0x1F30D);
            AddUnicodeToSet(0x1F30E);
            AddUnicodeToSet(0x1F30F);
            AddUnicodeToSet(0x1F30B);
            AddUnicodeToSet(0x1F30C);
            AddUnicodeToSet(0x1F320);
            AddUnicodeToSet(0x2B50);
            AddUnicodeToSet(0x2600);
            AddUnicodeToSet(0x26C5);
            AddUnicodeToSet(0x2601);
            AddUnicodeToSet(0x26A1);
            AddUnicodeToSet(0x2614);
            AddUnicodeToSet(0x2744);
            AddUnicodeToSet(0x26C4);
            AddUnicodeToSet(0x1F300);
            AddUnicodeToSet(0x1F301);
            AddUnicodeToSet(0x1F308);
            AddUnicodeToSet(0x1F30A);
            AddUnicodeToSet(0x1F38D);
            AddUnicodeToSet(0x1F49D);
            AddUnicodeToSet(0x1F38E);
            AddUnicodeToSet(0x1F392);
            AddUnicodeToSet(0x1F393);
            AddUnicodeToSet(0x1F38F);
            AddUnicodeToSet(0x1F386);
            AddUnicodeToSet(0x1F387);
            AddUnicodeToSet(0x1F390);
            AddUnicodeToSet(0x1F391);
            AddUnicodeToSet(0x1F383);
            AddUnicodeToSet(0x1F47B);
            AddUnicodeToSet(0x1F385);
            AddUnicodeToSet(0x1F384);
            AddUnicodeToSet(0x1F381);
            AddUnicodeToSet(0x1F38B);
            AddUnicodeToSet(0x1F389);
            AddUnicodeToSet(0x1F38A);
            AddUnicodeToSet(0x1F388);
            AddUnicodeToSet(0x1F38C);
            AddUnicodeToSet(0x1F52E);
            AddUnicodeToSet(0x1F3A5);
            AddUnicodeToSet(0x1F4F7);
            AddUnicodeToSet(0x1F4F9);
            AddUnicodeToSet(0x1F4FC);
            AddUnicodeToSet(0x1F4BF);
            AddUnicodeToSet(0x1F4C0);
            AddUnicodeToSet(0x1F4BD);
            AddUnicodeToSet(0x1F4BE);
            AddUnicodeToSet(0x1F4BB);
            AddUnicodeToSet(0x1F4F1);
            AddUnicodeToSet(0x260E);
            AddUnicodeToSet(0x1F4DE);
            AddUnicodeToSet(0x1F4DF);
            AddUnicodeToSet(0x1F4E0);
            AddUnicodeToSet(0x1F4E1);
            AddUnicodeToSet(0x1F4FA);
            AddUnicodeToSet(0x1F4FB);
            AddUnicodeToSet(0x1F50A);
            AddUnicodeToSet(0x1F509);
            AddUnicodeToSet(0x1F508);
            AddUnicodeToSet(0x1F507);
            AddUnicodeToSet(0x1F514);
            AddUnicodeToSet(0x1F515);
            AddUnicodeToSet(0x1F4E2);
            AddUnicodeToSet(0x1F4E3);
            AddUnicodeToSet(0x23F3);
            AddUnicodeToSet(0x231B);
            AddUnicodeToSet(0x23F0);
            AddUnicodeToSet(0x231A);
            AddUnicodeToSet(0x1F513);
            AddUnicodeToSet(0x1F512);
            AddUnicodeToSet(0x1F50F);
            AddUnicodeToSet(0x1F510);
            AddUnicodeToSet(0x1F511);
            AddUnicodeToSet(0x1F50E);
            AddUnicodeToSet(0x1F4A1);
            AddUnicodeToSet(0x1F526);
            AddUnicodeToSet(0x1F506);
            AddUnicodeToSet(0x1F505);
            AddUnicodeToSet(0x1F50C);
            AddUnicodeToSet(0x1F50B);
            AddUnicodeToSet(0x1F50D);
            AddUnicodeToSet(0x1F6C1);
            AddUnicodeToSet(0x1F6C0);
            AddUnicodeToSet(0x1F6BF);
            AddUnicodeToSet(0x1F6BD);
            AddUnicodeToSet(0x1F527);
            AddUnicodeToSet(0x1F529);
            AddUnicodeToSet(0x1F528);
            AddUnicodeToSet(0x1F6AA);
            AddUnicodeToSet(0x1F6AC);
            AddUnicodeToSet(0x1F4A3);
            AddUnicodeToSet(0x1F52B);
            AddUnicodeToSet(0x1F52A);
            AddUnicodeToSet(0x1F48A);
            AddUnicodeToSet(0x1F489);
            AddUnicodeToSet(0x1F4B0);
            AddUnicodeToSet(0x1F4B4);
            AddUnicodeToSet(0x1F4B5);
            AddUnicodeToSet(0x1F4B7);
            AddUnicodeToSet(0x1F4B6);
            AddUnicodeToSet(0x1F4B3);
            AddUnicodeToSet(0x1F4B8);
            AddUnicodeToSet(0x1F4F2);
            AddUnicodeToSet(0x1F4E7);
            AddUnicodeToSet(0x1F4E5);
            AddUnicodeToSet(0x1F4E4);
            AddUnicodeToSet(0x2709);
            AddUnicodeToSet(0x1F4E9);
            AddUnicodeToSet(0x1F4E8);
            AddUnicodeToSet(0x1F4EF);
            AddUnicodeToSet(0x1F4EB);
            AddUnicodeToSet(0x1F4EA);
            AddUnicodeToSet(0x1F4EC);
            AddUnicodeToSet(0x1F4ED);
            AddUnicodeToSet(0x1F4EE);
            AddUnicodeToSet(0x1F4E6);
            AddUnicodeToSet(0x1F4DD);
            AddUnicodeToSet(0x1F4C4);
            AddUnicodeToSet(0x1F4C3);
            AddUnicodeToSet(0x1F4D1);
            AddUnicodeToSet(0x1F4CA);
            AddUnicodeToSet(0x1F4C8);
            AddUnicodeToSet(0x1F4C9);
            AddUnicodeToSet(0x1F4DC);
            AddUnicodeToSet(0x1F4CB);
            AddUnicodeToSet(0x1F4C5);
            AddUnicodeToSet(0x1F4C6);
            AddUnicodeToSet(0x1F4C7);
            AddUnicodeToSet(0x1F4C1);
            AddUnicodeToSet(0x1F4C2);
            AddUnicodeToSet(0x2702);
            AddUnicodeToSet(0x1F4CC);
            AddUnicodeToSet(0x1F4CE);
            AddUnicodeToSet(0x2712);
            AddUnicodeToSet(0x270F);
            AddUnicodeToSet(0x1F4CF);
            AddUnicodeToSet(0x1F4D0);
            AddUnicodeToSet(0x1F4D5);
            AddUnicodeToSet(0x1F4D7);
            AddUnicodeToSet(0x1F4D8);
            AddUnicodeToSet(0x1F4D9);
            AddUnicodeToSet(0x1F4D3);
            AddUnicodeToSet(0x1F4D4);
            AddUnicodeToSet(0x1F4D2);
            AddUnicodeToSet(0x1F4DA);
            AddUnicodeToSet(0x1F4D6);
            AddUnicodeToSet(0x1F516);
            AddUnicodeToSet(0x1F4DB);
            AddUnicodeToSet(0x1F52C);
            AddUnicodeToSet(0x1F52D);
            AddUnicodeToSet(0x1F4F0);
            AddUnicodeToSet(0x1F3A8);
            AddUnicodeToSet(0x1F3AC);
            AddUnicodeToSet(0x1F3A4);
            AddUnicodeToSet(0x1F3A7);
            AddUnicodeToSet(0x1F3BC);
            AddUnicodeToSet(0x1F3B5);
            AddUnicodeToSet(0x1F3B6);
            AddUnicodeToSet(0x1F3B9);
            AddUnicodeToSet(0x1F3BB);
            AddUnicodeToSet(0x1F3BA);
            AddUnicodeToSet(0x1F3B7);
            AddUnicodeToSet(0x1F3B8);
            AddUnicodeToSet(0x1F47E);
            AddUnicodeToSet(0x1F3AE);
            AddUnicodeToSet(0x1F0CF);
            AddUnicodeToSet(0x1F3B4);
            AddUnicodeToSet(0x1F004);
            AddUnicodeToSet(0x1F3B2);
            AddUnicodeToSet(0x1F3AF);
            AddUnicodeToSet(0x1F3C8);
            AddUnicodeToSet(0x1F3C0);
            AddUnicodeToSet(0x26BD);
            AddUnicodeToSet(0x26BE);
            AddUnicodeToSet(0x1F3BE);
            AddUnicodeToSet(0x1F3B1);
            AddUnicodeToSet(0x1F3C9);
            AddUnicodeToSet(0x1F3B3);
            AddUnicodeToSet(0x26F3);
            AddUnicodeToSet(0x1F6B5);
            AddUnicodeToSet(0x1F6B4);
            AddUnicodeToSet(0x1F3C1);
            AddUnicodeToSet(0x1F3C7);
            AddUnicodeToSet(0x1F3C6);
            AddUnicodeToSet(0x1F3BF);
            AddUnicodeToSet(0x1F3C2);
            AddUnicodeToSet(0x1F3CA);
            AddUnicodeToSet(0x1F3C4);
            AddUnicodeToSet(0x1F3A3);
            AddUnicodeToSet(0x2615);
            AddUnicodeToSet(0x1F375);
            AddUnicodeToSet(0x1F376);
            AddUnicodeToSet(0x1F37C);
            AddUnicodeToSet(0x1F37A);
            AddUnicodeToSet(0x1F37B);
            AddUnicodeToSet(0x1F378);
            AddUnicodeToSet(0x1F379);
            AddUnicodeToSet(0x1F377);
            AddUnicodeToSet(0x1F374);
            AddUnicodeToSet(0x1F355);
            AddUnicodeToSet(0x1F354);
            AddUnicodeToSet(0x1F35F);
            AddUnicodeToSet(0x1F357);
            AddUnicodeToSet(0x1F356);
            AddUnicodeToSet(0x1F35D);
            AddUnicodeToSet(0x1F35B);
            AddUnicodeToSet(0x1F364);
            AddUnicodeToSet(0x1F371);
            AddUnicodeToSet(0x1F363);
            AddUnicodeToSet(0x1F365);
            AddUnicodeToSet(0x1F359);
            AddUnicodeToSet(0x1F358);
            AddUnicodeToSet(0x1F35A);
            AddUnicodeToSet(0x1F35C);
            AddUnicodeToSet(0x1F372);
            AddUnicodeToSet(0x1F362);
            AddUnicodeToSet(0x1F361);
            AddUnicodeToSet(0x1F373);
            AddUnicodeToSet(0x1F35E);
            AddUnicodeToSet(0x1F369);
            AddUnicodeToSet(0x1F36E);
            AddUnicodeToSet(0x1F366);
            AddUnicodeToSet(0x1F368);
            AddUnicodeToSet(0x1F367);
            AddUnicodeToSet(0x1F382);
            AddUnicodeToSet(0x1F370);
            AddUnicodeToSet(0x1F36A);
            AddUnicodeToSet(0x1F36B);
            AddUnicodeToSet(0x1F36C);
            AddUnicodeToSet(0x1F36D);
            AddUnicodeToSet(0x1F36F);
            AddUnicodeToSet(0x1F34E);
            AddUnicodeToSet(0x1F34F);
            AddUnicodeToSet(0x1F34A);
            AddUnicodeToSet(0x1F34B);
            AddUnicodeToSet(0x1F352);
            AddUnicodeToSet(0x1F347);
            AddUnicodeToSet(0x1F349);
            AddUnicodeToSet(0x1F353);
            AddUnicodeToSet(0x1F351);
            AddUnicodeToSet(0x1F348);
            AddUnicodeToSet(0x1F34C);
            AddUnicodeToSet(0x1F350);
            AddUnicodeToSet(0x1F34D);
            AddUnicodeToSet(0x1F360);
            AddUnicodeToSet(0x1F346);
            AddUnicodeToSet(0x1F345);
            AddUnicodeToSet(0x1F33D);
            AddUnicodeToSet(0x1F3E0);
            AddUnicodeToSet(0x1F3E1);
            AddUnicodeToSet(0x1F3EB);
            AddUnicodeToSet(0x1F3E2);
            AddUnicodeToSet(0x1F3E3);
            AddUnicodeToSet(0x1F3E5);
            AddUnicodeToSet(0x1F3E6);
            AddUnicodeToSet(0x1F3EA);
            AddUnicodeToSet(0x1F3E9);
            AddUnicodeToSet(0x1F3E8);
            AddUnicodeToSet(0x1F492);
            AddUnicodeToSet(0x26EA);
            AddUnicodeToSet(0x1F3EC);
            AddUnicodeToSet(0x1F3E4);
            AddUnicodeToSet(0x1F307);
            AddUnicodeToSet(0x1F306);
            AddUnicodeToSet(0x1F3EF);
            AddUnicodeToSet(0x1F3F0);
            AddUnicodeToSet(0x26FA);
            AddUnicodeToSet(0x1F3ED);
            AddUnicodeToSet(0x1F5FC);
            AddUnicodeToSet(0x1F5FE);
            AddUnicodeToSet(0x1F5FB);
            AddUnicodeToSet(0x1F304);
            AddUnicodeToSet(0x1F305);
            AddUnicodeToSet(0x1F303);
            AddUnicodeToSet(0x1F5FD);
            AddUnicodeToSet(0x1F309);
            AddUnicodeToSet(0x1F3A0);
            AddUnicodeToSet(0x1F3A1);
            AddUnicodeToSet(0x26F2);
            AddUnicodeToSet(0x1F3A2);
            AddUnicodeToSet(0x1F6A2);
            AddUnicodeToSet(0x26F5);
            AddUnicodeToSet(0x1F6A4);
            AddUnicodeToSet(0x1F6A3);
            AddUnicodeToSet(0x2693);
            AddUnicodeToSet(0x1F680);
            AddUnicodeToSet(0x2708);
            AddUnicodeToSet(0x1F4BA);
            AddUnicodeToSet(0x1F681);
            AddUnicodeToSet(0x1F682);
            AddUnicodeToSet(0x1F68A);
            AddUnicodeToSet(0x1F689);
            AddUnicodeToSet(0x1F68E);
            AddUnicodeToSet(0x1F686);
            AddUnicodeToSet(0x1F684);
            AddUnicodeToSet(0x1F685);
            AddUnicodeToSet(0x1F688);
            AddUnicodeToSet(0x1F687);
            AddUnicodeToSet(0x1F69D);
            AddUnicodeToSet(0x1F68B);
            AddUnicodeToSet(0x1F683);
            AddUnicodeToSet(0x1F68E);
            AddUnicodeToSet(0x1F68C);
            AddUnicodeToSet(0x1F68D);
            AddUnicodeToSet(0x1F699);
            AddUnicodeToSet(0x1F698);
            AddUnicodeToSet(0x1F697);
            AddUnicodeToSet(0x1F695);
            AddUnicodeToSet(0x1F696);
            AddUnicodeToSet(0x1F69B);
            AddUnicodeToSet(0x1F69A);
            AddUnicodeToSet(0x1F6A8);
            AddUnicodeToSet(0x1F693);
            AddUnicodeToSet(0x1F694);
            AddUnicodeToSet(0x1F692);
            AddUnicodeToSet(0x1F691);
            AddUnicodeToSet(0x1F690);
            AddUnicodeToSet(0x1F6B2);
            AddUnicodeToSet(0x1F6A1);
            AddUnicodeToSet(0x1F69F);
            AddUnicodeToSet(0x1F6A0);
            AddUnicodeToSet(0x1F69C);
            AddUnicodeToSet(0x1F488);
            AddUnicodeToSet(0x1F68F);
            AddUnicodeToSet(0x1F3AB);
            AddUnicodeToSet(0x1F6A6);
            AddUnicodeToSet(0x1F6A5);
            AddUnicodeToSet(0x26A0);
            AddUnicodeToSet(0x1F6A7);
            AddUnicodeToSet(0x1F530);
            AddUnicodeToSet(0x26FD);
            AddUnicodeToSet(0x1F3EE);
            AddUnicodeToSet(0x1F3B0);
            AddUnicodeToSet(0x2668);
            AddUnicodeToSet(0x1F5FF);
            AddUnicodeToSet(0x1F3AA);
            AddUnicodeToSet(0x1F3AD);
            AddUnicodeToSet(0x1F4CD);
            AddUnicodeToSet(0x1F6A9);
            AddUnicodeToSet(0x1F1EF);
            AddUnicodeToSet(0x1F1F0);
            AddUnicodeToSet(0x1F1F7);
            AddUnicodeToSet(0x1F1E9);
            AddUnicodeToSet(0x1F1EA);
            AddUnicodeToSet(0x1F1E8);
            AddUnicodeToSet(0x1F1FA);
            AddUnicodeToSet(0x1F1EB);
            AddUnicodeToSet(0x1F1EE);
            AddUnicodeToSet(0x1F1EC);
            AddUnicodeToSet(0x1F51F);
            AddUnicodeToSet(0x1F522);
            AddUnicodeToSet(0x1F523);
            AddUnicodeToSet(0x2B06);
            AddUnicodeToSet(0x2B07);
            AddUnicodeToSet(0x2B05);
            AddUnicodeToSet(0x27A1);
            AddUnicodeToSet(0x1F520);
            AddUnicodeToSet(0x1F521);
            AddUnicodeToSet(0x1F524);
            AddUnicodeToSet(0x2197);
            AddUnicodeToSet(0x2196);
            AddUnicodeToSet(0x2198);
            AddUnicodeToSet(0x2199);
            AddUnicodeToSet(0x2194);
            AddUnicodeToSet(0x2195);
            AddUnicodeToSet(0x1F504);
            AddUnicodeToSet(0x25C0);
            AddUnicodeToSet(0x25B6);
            AddUnicodeToSet(0x1F53C);
            AddUnicodeToSet(0x1F53D);
            AddUnicodeToSet(0x21A9);
            AddUnicodeToSet(0x21AA);
            AddUnicodeToSet(0x2139);
            AddUnicodeToSet(0x23EA);
            AddUnicodeToSet(0x23E9);
            AddUnicodeToSet(0x23EB);
            AddUnicodeToSet(0x23EC);
            AddUnicodeToSet(0x2935);
            AddUnicodeToSet(0x2934);
            AddUnicodeToSet(0x1F197);
            AddUnicodeToSet(0x1F500);
            AddUnicodeToSet(0x1F501);
            AddUnicodeToSet(0x1F502);
            AddUnicodeToSet(0x1F195);
            AddUnicodeToSet(0x1F199);
            AddUnicodeToSet(0x1F192);
            AddUnicodeToSet(0x1F193);
            AddUnicodeToSet(0x1F196);
            AddUnicodeToSet(0x1F4F6);
            AddUnicodeToSet(0x1F3A6);
            AddUnicodeToSet(0x1F201);
            AddUnicodeToSet(0x1F22F);
            AddUnicodeToSet(0x1F233);
            AddUnicodeToSet(0x1F235);
            AddUnicodeToSet(0x1F234);
            AddUnicodeToSet(0x1F232);
            AddUnicodeToSet(0x1F250);
            AddUnicodeToSet(0x1F239);
            AddUnicodeToSet(0x1F23A);
            AddUnicodeToSet(0x1F236);
            AddUnicodeToSet(0x1F21A);
            AddUnicodeToSet(0x1F6BB);
            AddUnicodeToSet(0x1F6B9);
            AddUnicodeToSet(0x1F6BA);
            AddUnicodeToSet(0x1F6BC);
            AddUnicodeToSet(0x1F6BE);
            AddUnicodeToSet(0x1F6B0);
            AddUnicodeToSet(0x1F6AE);
            AddUnicodeToSet(0x1F17F);
            AddUnicodeToSet(0x267F);
            AddUnicodeToSet(0x1F6AD);
            AddUnicodeToSet(0x1F237);
            AddUnicodeToSet(0x1F238);
            AddUnicodeToSet(0x1F202);
            AddUnicodeToSet(0x24C2);
            AddUnicodeToSet(0x1F6C2);
            AddUnicodeToSet(0x1F6C4);
            AddUnicodeToSet(0x1F6C5);
            AddUnicodeToSet(0x1F6C3);
            AddUnicodeToSet(0x1F251);
            AddUnicodeToSet(0x3299);
            AddUnicodeToSet(0x3297);
            AddUnicodeToSet(0x1F191);
            AddUnicodeToSet(0x1F198);
            AddUnicodeToSet(0x1F194);
            AddUnicodeToSet(0x1F6AB);
            AddUnicodeToSet(0x1F51E);
            AddUnicodeToSet(0x1F4F5);
            AddUnicodeToSet(0x1F6AF);
            AddUnicodeToSet(0x1F6B1);
            AddUnicodeToSet(0x1F6B3);
            AddUnicodeToSet(0x1F6B7);
            AddUnicodeToSet(0x1F6B8);
            AddUnicodeToSet(0x26D4);
            AddUnicodeToSet(0x2733);
            AddUnicodeToSet(0x2747);
            AddUnicodeToSet(0x274E);
            AddUnicodeToSet(0x2705);
            AddUnicodeToSet(0x2734);
            AddUnicodeToSet(0x1F49F);
            AddUnicodeToSet(0x1F19A);
            AddUnicodeToSet(0x1F4F3);
            AddUnicodeToSet(0x1F4F4);
            AddUnicodeToSet(0x1F170);
            AddUnicodeToSet(0x1F171);
            AddUnicodeToSet(0x1F18E);
            AddUnicodeToSet(0x1F17E);
            AddUnicodeToSet(0x1F4A0);
            AddUnicodeToSet(0x27BF);
            AddUnicodeToSet(0x267B);
            AddUnicodeToSet(0x2648);
            AddUnicodeToSet(0x2649);
            AddUnicodeToSet(0x264A);
            AddUnicodeToSet(0x264B);
            AddUnicodeToSet(0x264C);
            AddUnicodeToSet(0x264D);
            AddUnicodeToSet(0x264E);
            AddUnicodeToSet(0x264F);
            AddUnicodeToSet(0x2650);
            AddUnicodeToSet(0x2651);
            AddUnicodeToSet(0x2652);
            AddUnicodeToSet(0x2653);
            AddUnicodeToSet(0x26CE);
            AddUnicodeToSet(0x1F52F);
            AddUnicodeToSet(0x1F3E7);
            AddUnicodeToSet(0x1F4B9);
            AddUnicodeToSet(0x1F4B2);
            AddUnicodeToSet(0x1F4B1);
            AddUnicodeToSet(0x00A9);
            AddUnicodeToSet(0x00AE);
            AddUnicodeToSet(0x2122);
            AddUnicodeToSet(0x274C);
            AddUnicodeToSet(0x203C);
            AddUnicodeToSet(0x2049);
            AddUnicodeToSet(0x2757);
            AddUnicodeToSet(0x2753);
            AddUnicodeToSet(0x2755);
            AddUnicodeToSet(0x2754);
            AddUnicodeToSet(0x2B55);
            AddUnicodeToSet(0x1F51D);
            AddUnicodeToSet(0x1F51A);
            AddUnicodeToSet(0x1F519);
            AddUnicodeToSet(0x1F51B);
            AddUnicodeToSet(0x1F51C);
            AddUnicodeToSet(0x1F503);
            AddUnicodeToSet(0x1F55B);
            AddUnicodeToSet(0x1F567);
            AddUnicodeToSet(0x1F550);
            AddUnicodeToSet(0x1F55C);
            AddUnicodeToSet(0x1F551);
            AddUnicodeToSet(0x1F55D);
            AddUnicodeToSet(0x1F552);
            AddUnicodeToSet(0x1F55E);
            AddUnicodeToSet(0x1F553);
            AddUnicodeToSet(0x1F55F);
            AddUnicodeToSet(0x1F554);
            AddUnicodeToSet(0x1F560);
            AddUnicodeToSet(0x1F555);
            AddUnicodeToSet(0x1F561);
            AddUnicodeToSet(0x1F556);
            AddUnicodeToSet(0x1F562);
            AddUnicodeToSet(0x1F557);
            AddUnicodeToSet(0x1F563);
            AddUnicodeToSet(0x1F558);
            AddUnicodeToSet(0x1F564);
            AddUnicodeToSet(0x1F559);
            AddUnicodeToSet(0x1F565);
            AddUnicodeToSet(0x1F55A);
            AddUnicodeToSet(0x1F566);
            AddUnicodeToSet(0x2716);
            AddUnicodeToSet(0x2795);
            AddUnicodeToSet(0x2796);
            AddUnicodeToSet(0x2797);
            AddUnicodeToSet(0x2660);
            AddUnicodeToSet(0x2665);
            AddUnicodeToSet(0x2663);
            AddUnicodeToSet(0x2666);
            AddUnicodeToSet(0x1F4AE);
            AddUnicodeToSet(0x1F4AF);
            AddUnicodeToSet(0x2714);
            AddUnicodeToSet(0x2611);
            AddUnicodeToSet(0x1F518);
            AddUnicodeToSet(0x1F517);
            AddUnicodeToSet(0x27B0);
            AddUnicodeToSet(0x3030);
            AddUnicodeToSet(0x303D);
            AddUnicodeToSet(0x1F531);
            AddUnicodeToSet(0x25FC);
            AddUnicodeToSet(0x25FB);
            AddUnicodeToSet(0x25FE);
            AddUnicodeToSet(0x25FD);
            AddUnicodeToSet(0x25AA);
            AddUnicodeToSet(0x25AB);
            AddUnicodeToSet(0x26AB);
            AddUnicodeToSet(0x26AA);
            AddUnicodeToSet(0x1F53A);
            AddUnicodeToSet(0x1F532);
            AddUnicodeToSet(0x1F533);
            AddUnicodeToSet(0x1F534);
            AddUnicodeToSet(0x1F535);
            AddUnicodeToSet(0x1F53B);
            AddUnicodeToSet(0x2B1C);
            AddUnicodeToSet(0x2B1B);
            AddUnicodeToSet(0x1F536);
            AddUnicodeToSet(0x1F537);
            AddUnicodeToSet(0x1F538);
            AddUnicodeToSet(0x1F539);
            AddUnicodeToSet(0x1F9C0);
            AddUnicodeToSet(0x1F37D);
        }
    }
}