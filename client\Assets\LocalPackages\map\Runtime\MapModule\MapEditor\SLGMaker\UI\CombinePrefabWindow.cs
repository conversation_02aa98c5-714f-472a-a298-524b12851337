﻿ 



 
 

﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Text;

namespace TFW.Map
{
    public class CombinePrefabWindow : EditorWindow
    {
        void OnGUI()
        {
            var prefab = EditorGUILayout.ObjectField("Prefab", mPrefab, typeof(GameObject), true, null) as GameObject;
            if (prefab != null)
            {
                mPrefab = prefab;
            }

            if (mPrefab != null)
            {
                var path = AssetDatabase.GetAssetPath(mPrefab);
                if (!string.IsNullOrEmpty(path))
                {
                    path = Utils.GetFolderPath(path);
                }
                else
                {
                    var root = Utils.GetRootTransform(mPrefab.transform);
                    path = AssetDatabase.GetAssetPath(root.gameObject);
                    if (string.IsNullOrEmpty(path))
                    {
                        path = "Assets";
                    }
                }

                if (!string.IsNullOrEmpty(path))
                {
                    if (GUILayout.Button("Combine"))
                    {
                        int n = mPrefab.transform.childCount;
                        List<GameObject> objects = new List<GameObject>();
                        for (int i = 0; i < n; ++i)
                        {
                            objects.Add(mPrefab.transform.GetChild(i).gameObject);
                        }

                        PrefabCombiner.Combine(path, objects);
                    }
                }
            }
        }

        GameObject mPrefab;
    }
}


#endif