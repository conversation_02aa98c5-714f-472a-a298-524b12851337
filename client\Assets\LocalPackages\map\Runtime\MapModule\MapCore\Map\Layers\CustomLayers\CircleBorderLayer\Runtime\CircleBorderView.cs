﻿ 



 
 

using UnityEngine;
using System;

namespace TFW.Map
{
    public class CrossfadeModelLoadingTask : ModelLoadingTask
    {
        public void Init(int taskID, Action<ModelBase, bool> loadFinishedCallback, float priority, int curLOD, int newLOD, float lowerHeight, float upperHeight, bool keepOldLOD)
        {
            mTaskID = taskID;
            mLoadFinishedCallback = loadFinishedCallback;
            mPriority = priority;
            mCurLOD = curLOD;
            mNewLOD = newLOD;
            mLowerHeight = lowerHeight;
            mUpperHeight = upperHeight;
            mKeepOldLOD = keepOldLOD;
        }

        public static CrossfadeModelLoadingTask Require(int taskID, Action<ModelBase, bool> loadFinishedCallback, float priority, int curLOD, int newLOD, float lowerHeight, float upperHeight, bool keepOldLOD)
        {
            var task = mPool.Require();
            task.Init(taskID, loadFinishedCallback, priority, curLOD, newLOD, lowerHeight, upperHeight, keepOldLOD);
            return task;
        }

        public static void Release(CrossfadeModelLoadingTask task)
        {
            mPool.Release(task);
        }

        protected override ModelBase LoadImpl()
        {
            var mapObjectData = Map.currentMap.FindObject(mTaskID) as IMapObjectData;
            Debug.Assert(mapObjectData != null, "[CircleBorderVew] map object (IMapObjectData) is removed!");

            var model = CrossfadeModel.Require(
                Map.currentMap,
                mapObjectData.GetModelTemplateID(),
                mCurLOD, mNewLOD,
                mLowerHeight, mUpperHeight,
                mKeepOldLOD,
                mapObjectData.GetPosition(), mapObjectData.GetScale(), mapObjectData.GetRotation());
            return model;
        }

        public override void OnDestroy()
        {
            CrossfadeModelLoadingTask.Release(this);
        }

        int mCurLOD;
        int mNewLOD;
        float mLowerHeight;
        float mUpperHeight;
        bool mKeepOldLOD;

        static ObjectPool<CrossfadeModelLoadingTask> mPool = new ObjectPool<CrossfadeModelLoadingTask>(1000, ()=>new CrossfadeModelLoadingTask());
    }

    public class CircleBorderView : ModelView
    {
        public CircleBorderView(IMapObjectData data, MapLayerView layerView) : base(data, layerView)
        {
            mOnLoadFinish = OnLoadFinish;
            mSingleModelLOD = (layerView.layerData as CircleBorderLayerData).combineBorderLOD;
        }

        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            var map = Map.currentMap;
            if (map.isEditorMode)
            {
                return base.CreateModelInternal(data, newLOD);
            }

            var borderData = data as CircleBorderData;
            if (newLOD < mSingleModelLOD || borderData.isAlwaysVisibleAtHigherLODs)
            {
                return CreateCrossfadeModel(data, newLOD);
            }

            //在最顶层lod,只显示一个模型,其他的使用placeholder
            if (newLOD < 0)
            {
                newLOD = 0;
            }
            Debug.Assert(mModel == null);

            ModelBase model = null;
            var objectLayerData = mLayerView.layerData as MapObjectLayerData;
            if (data == null)
            {
                Debug.Assert(false);
            }

            bool active = data.IsObjActive();


            var loadingTaskManager = map.loadingTaskManager;
            loadingTaskManager.CancelTask(data.GetEntityID());

            model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
            OnModelPlaceholderReady(model);

            return model;
        }

        ModelBase CreateCrossfadeModel(IMapObjectData data, int newLOD)
        {
            if (newLOD < 0)
            {
                newLOD = 0;
            }
            Debug.Assert(mModel == null);

            var map = Map.currentMap;
            ModelBase model = null;
            var objectLayerData = mLayerView.layerData as MapObjectLayerData;
            if (data == null)
            {
                Debug.Assert(false);
            }

            bool active = data.IsObjActive();


            var template = map.GetEntityModelTemplate(data.GetEntityID());
            Debug.Assert(template != null, "invalid model template");

            var loadingTaskManager = map.loadingTaskManager;
            loadingTaskManager.CancelTask(data.GetEntityID());

            var layerView = mLayerView as CircleBorderLayerView;
            int lastLOD = objectLayerData.lastLOD;

            bool keepOldLOD = true;
            if (newLOD >= mSingleModelLOD)
            {
                keepOldLOD = false;
            }
            if (newLOD < mSingleModelLOD && lastLOD < mSingleModelLOD)
            {
                lastLOD = newLOD;
            }

            //如果对象池中有缓存的prefab,直接加载它
            //if (mUseModelPlaceholder ||
            //    layerView.asyncLoading == false ||
            //    map.HasCachedObject(template.GetLODPrefabPath(newLOD)))
            if (true)
            {
                //不使用加载队列加载,直接生成一个ReusableModel,这种模型使用对象池来管理它的game object
                if (active/* && !mUseModelPlaceholder*/)
                {
                    //向下切换lod时只使用低层的lod
                    if (newLOD < lastLOD)
                    {
                        lastLOD = newLOD;
                    }
                    model = CrossfadeModel.Require(map, data.GetModelTemplateID(), lastLOD, newLOD, layerView.crossFadeLowerHeight, layerView.crossFadeUpperHeight, keepOldLOD, data.GetPosition(), data.GetScale(), data.GetRotation());
                    model.transform.SetParent(mLayerView.root.transform, false);
                    OnModelReady(model);   
                }
                else
                {
                    model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
                    OnModelPlaceholderReady(model);
                }
            }
            //else
            //{
            //    //往加载队列里添加一个新的任务
            //    //先使用一个占位模型来显示,当实际的模型加载完毕后再替换占位模型
            //    model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
            //    OnModelPlaceholderReady(model);

            //    if (active)
            //    {
            //        //向下切换lod时只使用低层的lod
            //        if (newLOD < lastLOD)
            //        {
            //            lastLOD = newLOD;
            //        }
            //        var task = CrossfadeModelLoadingTask.Require(data.GetEntityID(), mOnLoadFinish, 0, lastLOD, newLOD, layerView.crossFadeLowerHeight, layerView.crossFadeUpperHeight, keepOldLOD);
            //        loadingTaskManager.AddTask(task);
            //    }
            //}

            return model;
        }

        System.Action<ModelBase, bool> mOnLoadFinish;
        //使用1个模型的lod层级
        int mSingleModelLOD = 2;
    }
}
