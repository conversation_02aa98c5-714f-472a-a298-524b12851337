﻿Shader "SLGMaker/BakedVertexAnimation"
{
    Properties
    {
        _Color("Color", Color) = (1, 1, 1, 1)
        _MainTex("Alpha", 2D) = "white" {}
        //mesh使用的动画数据
        _AnimationData("Animation Data", 2D) = "white" {}
        _AnimationParams("Animation Parameters", Vector) = (0, 0, 0, 0)
        _AnimTextureSize("Animation Texture Size", Vector) = (0, 0, 0, 0)
        _BlendParams("Blending Parameters", Float) = 0
    }
        SubShader
    {
        Tags { "RenderType" = "Opaque" }
        LOD 100

        Cull Off

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float3 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float3 uv : TEXCOORD0;
            };

            float4 _Color;
            sampler2D _MainTex;

            UNITY_INSTANCING_BUFFER_START(Props)
                UNITY_DEFINE_INSTANCED_PROP(float4, _AnimationParams)
                UNITY_DEFINE_INSTANCED_PROP(float4, _AnimTextureSize)
                UNITY_DEFINE_INSTANCED_PROP(float, _BlendParams)
            UNITY_INSTANCING_BUFFER_END(Props)

#include "Assets/LocalPackages/com.tfw.map/Runtime/MapModuleRes/RuntimeRes/BakedVertexAnimation.cginc"

            v2f vert(appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                float4 localPos = GetVertexPosition(v.uv.z);
                o.vertex = UnityObjectToClipPos(localPos.xyz);
                o.uv = v.uv;
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                return tex2D(_MainTex, i.uv);
            }
            ENDCG
        }
    }
}
