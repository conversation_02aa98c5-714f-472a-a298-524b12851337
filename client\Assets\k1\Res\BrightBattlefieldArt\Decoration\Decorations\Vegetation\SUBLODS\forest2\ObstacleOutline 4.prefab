%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5853086135832201853
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2508018133250409012}
  - component: {fileID: 4696814891397219128}
  m_Layer: 0
  m_Name: ObstacleOutline 4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2508018133250409012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5853086135832201853}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4696814891397219128
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5853086135832201853}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -15.911022, y: 0, z: -75.69049}
    - {x: 9.965211, y: 0, z: -57.133858}
    - {x: 19.053448, y: 0, z: -57.061184}
    - {x: 32.84549, y: 0, z: -65.26332}
    - {x: 33.85267, y: 0, z: -74.27281}
    - {x: -5.4133453, y: 0, z: -90}
    - {x: -16.703201, y: 0, z: -90}
    - {x: -20.912123, y: 0, z: -85.45061}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -7.1953, y: 0, z: -90}
    - {x: -7.1392, y: 0, z: -90}
    - {x: -4.4868, y: 0, z: -90}
    - {x: -4.416, y: 0, z: -90}
    - {x: -2.4421, y: 0, z: -90}
    - {x: -2.355, y: 0, z: -90}
    - {x: 26.2346, y: 0, z: -80.7283}
    - {x: 26.4694, y: 0, z: -80.621}
    - {x: 31.9925, y: 0, z: -77.4975}
    - {x: 32.0336, y: 0, z: -77.4734}
    - {x: 35.0326, y: 0, z: -75.6495}
    - {x: 35.7604, y: 0, z: -74.5921}
    - {x: 36.2676, y: 0, z: -71.8254}
    - {x: 36.2189, y: 0, z: -71.0828}
    - {x: 34.7675, y: 0, z: -66.5874}
    - {x: 34.6239, y: 0, z: -66.2723}
    - {x: 32.6582, y: 0, z: -63.0144}
    - {x: 32.1372, y: 0, z: -62.4949}
    - {x: 21.1777, y: 0, z: -55.928}
    - {x: 20.8527, y: 0, z: -55.7824}
    - {x: 14.0381, y: 0, z: -53.6543}
    - {x: 12.9816, y: 0, z: -53.7032}
    - {x: 9.2793, y: 0, z: -55.2477}
    - {x: 9.0663, y: 0, z: -55.3572}
    - {x: 3.6544, y: 0, z: -58.7114}
    - {x: 3.6043, y: 0, z: -58.7438}
    - {x: -5.6735, y: 0, z: -65.0175}
    - {x: -5.7563, y: 0, z: -65.0777}
    - {x: -14.4478, y: 0, z: -71.8627}
    - {x: -14.7165, y: 0, z: -72.1338}
    - {x: -16.3095, y: 0, z: -74.2124}
    - {x: -16.3526, y: 0, z: -74.2715}
    - {x: -21.88, y: 0, z: -82.2615}
    - {x: -22.131, y: 0, z: -82.8874}
    - {x: -22.5408, y: 0, z: -85.4677}
    - {x: -22.5134, y: 0, z: -86.0764}
    - {x: -22.1388, y: 0, z: -87.5596}
    - {x: -21.8931, y: 0, z: -88.0825}
    - {x: -20.5215, y: 0, z: -89.9582}
    - {x: -20.2149, y: 0, z: -90}
    - {x: -18.7866, y: 0, z: -90}
    - {x: -18.4859, y: 0, z: -90}
    - {x: -16.1937, y: 0, z: -90}
    - {x: -15.9561, y: 0, z: -90}
    - {x: -13.4283, y: 0, z: -90}
    - {x: -12.761, y: 0, z: -90}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
