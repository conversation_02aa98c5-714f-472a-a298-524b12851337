﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionAddPolygonRiver : EditorAction
    {
        public ActionAddPolygonRiver(int layerID, int dataID, List<Vector3> vertices, string materialPath, float height, bool generateRiverMaterial)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mHeight = height;
            mMaterialPath = materialPath;
            mVertices = new List<Vector3>(vertices.Count);
            mVertices.AddRange(vertices);
            mGenerateRiverMaterial = generateRiverMaterial;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            var data = new PolygonRiverData(mDataID, Map.currentMap, mVertices, layer.displayVertexRadius, 0, "", null, null, -1, mHeight, mGenerateRiverMaterial);
            layer.AddObject(data);

            var param = new RiverGenerationParameter();
            param.textureSize = 256;
            param.mtlPath = mMaterialPath;
            layer.GenerateRiverMesh(data.id, param, null);

            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        int mLayerID;
        int mDataID;
        float mHeight;
        string mMaterialPath;
        List<Vector3> mVertices;
        bool mGenerateRiverMaterial;
    }
}

#endif