namespace THelper
{
    public class Ins<T> where T : class, new()
    {
        static T s_Ins;
        public static T I
        {
            get
            {
                if (s_Ins == null)
                {
                    s_Ins = new T();
                }
                return s_Ins;
            }
        }

        protected bool m_DebugEnable = true;

        string ObjectToString(object obj)
        {
            return obj.ToString();// JsonMapper.ToJson(obj);//ObjectDumper.Dump(obj);
        }

        /// <summary>
        /// Debug
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="para"></param>
        protected void Debug(object msg, params object[] para)
        {
            if (m_DebugEnable)
            {
                if (para != null)
                {
                    for (int i = 0; i < para.Length; i++)
                    {
                        if (para[i].GetType().IsValueType) // 值类型
                        {
                            para[i] = para[i].ToString();
                        }
                        else
                        {
                            para[i] = ObjectToString(para[i]);
                        }
                    }
                }

                D.Debug?.Log(string.Format(msg.ToString(), para));
            }
        }
    }
}