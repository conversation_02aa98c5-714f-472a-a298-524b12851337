﻿ 



 
 



using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //地图上对象使用的对象池
    public class TileObjectDataPool2
    {
        public void OnDestroy()
        {
            Clear();
        }

        public TileObjectData2 Require(int id, Map map, BigTileChildPrefabData2 prefabData, string prefabPath, Rect worldBounds, Vector3 position, Vector3 scaling, Quaternion rotation, int localIndex, int tileIndex, int lod, float baseScale, PrefabInitInfo2 prefabInfo, System.Action<TileObjectData2> onObjectScaleChangeCallback, bool useCullManager, TileGridObjectLayerData2.SpecialArea area)
        {
            TileObjectData2 result = null;

            int n = mObjects.Count;
            if (n > 0)
            {
                result = mObjects[n - 1];
                mObjects.RemoveAt(n - 1);
            }
            else
            {
                BaseObject.sAddObjects = false;
                result = new TileObjectData2(id, map);
                BaseObject.sAddObjects = true;
            }

            BaseObject.RemoveObject(map, id);
            result.Set(id, prefabData.viewID, prefabData.objectType, prefabData, prefabPath, worldBounds, position, scaling, rotation, localIndex, tileIndex, lod, baseScale, prefabInfo, onObjectScaleChangeCallback, useCullManager, area);
            BaseObject.AddObject(result);

            return result;
        }

        public void Release(TileObjectData2 tileObject)
        {
            if (tileObject != null)
            {
                mObjects.Add(tileObject);
                tileObject.OnDestroy();
                BaseObject.RemoveObject(tileObject);
            }
        }

        public void Clear()
        {
            mObjects.Clear();
        }

        List<TileObjectData2> mObjects = new List<TileObjectData2>();
    }
}
