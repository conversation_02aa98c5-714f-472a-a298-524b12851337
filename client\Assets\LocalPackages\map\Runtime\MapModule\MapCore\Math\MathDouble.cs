﻿ 



 
 

using System;

namespace TFW.Map
{
    public static class MathDouble
    {
        public static bool GE(double a, double b, double esp = double.Epsilon)
        {
            if (Approximately(a, b, esp))
            {
                return true;
            }

            if (a - b >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool LE(double a, double b, double esp = double.Epsilon)
        {
            if (Approximately(a, b, esp))
            {
                return true;
            }

            if (b - a >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool LT(double a, double b, double esp = double.Epsilon)
        {
            if (b - a >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool GT(double a, double b, double esp = double.Epsilon)
        {
            if (a - b >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool GTE(double a, double b, double esp)
        {
            if (a - b >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool LTE(double a, double b, double esp)
        {
            if (b - a >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool Approximately(double a, double b, double esp = double.Epsilon)
        {
            double d = System.Math.Abs(a - b);
            if (d <= esp)
            {
                return true;
            }

            return false;
        }
    }
}
