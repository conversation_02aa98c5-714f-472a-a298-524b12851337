﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using System.Collections.Generic;
using UnityEngine;
using System.IO;

namespace TFW.Map
{
    //生成游戏运行时需要的河流资源
    public static class PolygonRiverAssetGenerator
    {
        //返回每个river使用的prefab路径
        public static Dictionary<int, string> Generate(string folderPath, PolygonRiverLayer layer, bool usUV2, bool generateOBJ)
        {
            if (string.IsNullOrEmpty(folderPath))
            {
                EditorUtility.DisplayDialog("Error", "invalid river asset folder", "OK");
                return null;
            }
            Dictionary<int, string> riverUsedPrefabs = new Dictionary<int, string>();
            int lodCount = layer.layerData.lodConfig.lodConfigs.Length;
            if (lodCount == 0)
            {
                EditorUtility.DisplayDialog("Error", "Invalid river layer lod count", "OK");
                return riverUsedPrefabs;
            }

            if (layer.onlyGenerateLOD0Assets)
            {
                lodCount = 1;
            }

            //每次生成都删除文件夹
            FileUtil.DeleteFileOrDirectory(folderPath);
            Directory.CreateDirectory(folderPath);
            if (Directory.Exists(folderPath))
            {
                List<IMapObjectData> allPolygonRivers = new List<IMapObjectData>();
                layer.GetAllObjects(allPolygonRivers);

                List<PrefabInfo> allPrefabs = new List<PrefabInfo>();
                for (int i = 0; i < allPolygonRivers.Count; ++i)
                {
                    EditorUtility.DisplayProgressBar($"Generating River Prefabs {i}/{allPolygonRivers.Count}", "Please wait...", (float)i / allPolygonRivers.Count);
                    CreatePrefab(folderPath, i, lodCount, allPolygonRivers[i] as PolygonRiverData, layer, riverUsedPrefabs, usUV2, generateOBJ, allPrefabs);
                }

                AssetDatabase.StartAssetEditing();
                try
                {
                    for (int i = 0; i < allPrefabs.Count; ++i)
                    {
                        PrefabUtility.SaveAsPrefabAssetAndConnect(allPrefabs[i].gameObject, allPrefabs[i].prefabPath, InteractionMode.AutomatedAction);
                        GameObject.DestroyImmediate(allPrefabs[i].gameObject);
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                }
                
                EditorUtility.ClearProgressBar();

                AssetDatabase.Refresh();
            }

            if (mCamera != null)
            {
                Object.DestroyImmediate(mCamera.gameObject);
                mCamera = null;
            }

            return riverUsedPrefabs;
        }

        public static string GetAssetPath(string folder, string type, int idx, int section, int lod, string assetExt)
        {
            string path = null;
            if (type.Length == 0)
            {
                path = $"{folder}/gen_river_{idx}_sec_{section}{MapCoreDef.MAP_PREFAB_LOD_PREFIX}{lod}.{assetExt}";
            }
            else
            {
                path = $"{folder}/gen_river_{type}_{idx}_sec_{section}{MapCoreDef.MAP_PREFAB_LOD_PREFIX}{lod}.{assetExt}";
            }
            return path;
        }

        class PrefabInfo
        {
            public PrefabInfo(GameObject obj, string path)
            {
                gameObject = obj;
                prefabPath = path;
            }

            public GameObject gameObject;
            public string prefabPath;
        }

        //返回每个section使用的prefab
        static void CreatePrefab(string folder, int idx, int lodCount, PolygonRiverData river, PolygonRiverLayer layer, Dictionary<int, string> riverSectionUsedPrefabs, bool useUV2, bool generateOBJ, List<PrefabInfo> allPrefabs)
        {
            for (int lod = 0; lod < lodCount; ++lod)
            {
                var sections = river.sections;
                bool hide = layer.layerData.lodConfig.lodConfigs[lod].hideObject;
                if (hide == false)
                {
                    for (int s = 0; s < sections.Count; ++s)
                    {
                        string newPrefabPath = GetAssetPath(folder, "", idx, s, lod, "prefab");

                        bool useRenderTextureAtLOD = UseRenderTextureAtLOD(layer, lod);
                        if (useRenderTextureAtLOD)
                        {
                            //使用渲染到贴图的河流
                            GameObject riverMeshObject = layer.GetRiverMeshObject(river.id, s);
                            string alphaTexturePath = GetAssetPath(folder, "texture", idx, s, 0, "png");
                            var alphaTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(alphaTexturePath);
                            Debug.Assert(alphaTexture != null, $"invalid alpha texture {alphaTexturePath}");
                            CreateRenderTexturePrefab(riverMeshObject, folder, idx, s, lod, alphaTexture, layer.bakedTextureSize, allPrefabs);
                        }
                        else
                        {
                            if (lod == 0)
                            {
                                riverSectionUsedPrefabs[sections[s].id] = newPrefabPath;
                            }

                            GameObject riverMeshObject = layer.GetRiverMeshObject(river.id, s);
                            Debug.Assert(riverMeshObject != null);
                            var newRiverMeshObject = Object.Instantiate<GameObject>(riverMeshObject);

                            //save mesh
                            //use lod0's mesh
                            string meshPath = GetAssetPath(folder, "mesh", idx, s, 0, "asset");
                            if (lod == 0)
                            {
                                var mesh = riverMeshObject.GetComponent<MeshFilter>().sharedMesh;
                                var localMesh = CreateLocalMesh(mesh, useUV2);
                                AssetDatabase.CreateAsset(localMesh, meshPath);
                                //AssetDatabase.Refresh();

                                if (generateOBJ)
                                {
                                    string objPath = GetAssetPath(folder, "mesh", idx, s, 0, "obj");
                                    OBJExporter.Export(objPath, localMesh.vertices, localMesh.uv, null, localMesh.triangles);
                                }
                                newRiverMeshObject.GetComponent<MeshFilter>().sharedMesh = localMesh;
                            }
                            else
                            {
                                var newMesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
                                Debug.Assert(newMesh != null);
                                newRiverMeshObject.GetComponent<MeshFilter>().sharedMesh = newMesh;
                            }

                            //save material
                            if (river.generateRiverMaterial)
                            {
                                //save texture
                                //注意只生成lod0的mask贴图!
                                string texturePath = GetAssetPath(folder, "texture", idx, s, 0, "png");
                                if (lod == 0)
                                {
                                    byte[] bytes = sections[s].texture.EncodeToPNG();
                                    System.IO.File.WriteAllBytes(texturePath, bytes);
                                    //AssetDatabase.Refresh();
                                }
                                var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
                                Debug.Assert(texture != null);
                                string mtlPath = GetAssetPath(folder, "mtl", idx, s, 0, "mat");
                                if (lod == 0)
                                {
                                    var mtl = riverMeshObject.GetComponent<MeshRenderer>().sharedMaterial;
                                    mtl = Object.Instantiate<Material>(mtl);
                                    mtl.SetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME, texture);
                                    AssetDatabase.CreateAsset(mtl, mtlPath);
                                }
                                var newMtl = AssetDatabase.LoadAssetAtPath<Material>(mtlPath);
                                newRiverMeshObject.GetComponent<MeshRenderer>().sharedMaterial = newMtl;
                            }
                            else
                            {
                                var mtl = AssetDatabase.LoadAssetAtPath<Material>(river.materialPath);
                                newRiverMeshObject.GetComponent<MeshRenderer>().sharedMaterial = mtl;
                            }


                            allPrefabs.Add(new PrefabInfo(newRiverMeshObject, newPrefabPath));
                            //save prefab
                            //PrefabUtility.SaveAsPrefabAssetAndConnect(newRiverMeshObject, newPrefabPath, InteractionMode.AutomatedAction);
                            //GameObject.DestroyImmediate(newRiverMeshObject);
                        }
                    }
                }
                else
                {
                    for (int s = 0; s < sections.Count; ++s)
                    {
                        //create a dummy prefab
                        var newPrefabPath = GetAssetPath(folder, "", idx, s, lod, "prefab");
                        GameObject riverMeshObject = new GameObject();
                        allPrefabs.Add(new PrefabInfo(riverMeshObject, newPrefabPath));
                        //save prefab
                        //PrefabUtility.SaveAsPrefabAssetAndConnect(riverMeshObject, newPrefabPath, InteractionMode.AutomatedAction);
                        //GameObject.DestroyImmediate(riverMeshObject);
                    }
                }
            }
        }

        //是否在lod上使用render texture
        static bool UseRenderTextureAtLOD(PolygonRiverLayer layer, int lod)
        {
            var config = layer.layerData.lodConfig.lodConfigs[lod];
            return config.useRenderTexture;
        }

        static Mesh CreateLocalMesh(Mesh globalMesh, bool useUV2)
        {
            var mesh = new Mesh();
            var globalVertices = globalMesh.vertices;
            var bounds = globalMesh.bounds;
            var center = bounds.center;
            //convert to local vertices
            for (int i = 0; i < globalVertices.Length; ++i)
            {
                globalVertices[i] = globalVertices[i] - center;
            }
            mesh.vertices = globalVertices;
            mesh.triangles = globalMesh.triangles;
            mesh.uv = globalMesh.uv;
            if (useUV2)
            {
                mesh.uv2 = globalMesh.uv2;
            }
            mesh.RecalculateNormals();
            return mesh;
        }

        //创建使用render texture的河流prefab
        static void CreateRenderTexturePrefab(GameObject riverSectionGameObject, string folder, int idx, int section, int lod, Texture2D alphaTexture, int bakedTextureSize, List<PrefabInfo> allPrefabs)
        {
            GameObject prefab = new GameObject();
            var filter = prefab.AddComponent<MeshFilter>();
            var renderer = prefab.AddComponent<MeshRenderer>();

            var rect = GameObjectBoundsCalculator.CalculateRect(riverSectionGameObject);

            //create mesh
            var mesh = CreatePlaneMesh(rect);
            var meshPath = GetAssetPath(folder, "mesh", idx, section, lod, "asset");
            filter.sharedMesh = mesh;
            AssetDatabase.CreateAsset(mesh, meshPath);

            //create texture
            if (mCamera == null)
            {
                CreateCamera();
            }
            RenderObjectToTexture rt = new RenderObjectToTexture(mCamera, null);
            float distance = 100.0f;
            var renderTexture = CreateRenderTexture(rect, bakedTextureSize);
            //注意,对于带透明度的水来说,必须disable alpha,不然水会和背景混合2次!
            EnableRiverAlpha(riverSectionGameObject, false);
            rt.Render(riverSectionGameObject, renderFront: false, cameraExtraDistance: distance, customRenderTexture: renderTexture, hasLight: false, useReplacementCamera: false, overrideBounds: new Bounds());
            EnableRiverAlpha(riverSectionGameObject, true);
            string texturePath = GetAssetPath(folder, "texture", idx, section, lod, "png");
            rt.SaveTexture(renderTexture, texturePath, hasAlpha: false);
            //AssetDatabase.Refresh();
            var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
            Debug.Assert(texture != null);
            Object.DestroyImmediate(renderTexture);

            //create material
            string materialPath = GetAssetPath(folder, "mtl", idx, section, lod, "mat");
            var mtl = new Material(Shader.Find("SLGMaker/TextureRiver"));
            mtl.mainTexture = texture;
            mtl.SetTexture("_Alpha", alphaTexture);
            renderer.sharedMaterial = mtl;
            AssetDatabase.CreateAsset(mtl, materialPath);

            string prefabpath = GetAssetPath(folder, "", idx, section, lod, "prefab");
            allPrefabs.Add(new PrefabInfo(prefab, prefabpath));
            //PrefabUtility.SaveAsPrefabAssetAndConnect(prefab, prefabpath, InteractionMode.AutomatedAction);
            //GameObject.DestroyImmediate(prefab);

            rt.OnDestroy(true);
        }

        static void EnableRiverAlpha(GameObject obj, bool enableAlpha)
        {
            var mtl = obj.GetComponent<MeshRenderer>().sharedMaterial;
            bool hasPropSrc = mtl.HasProperty(MapCoreDef.RIVER_BLEND_SRC_FACTOR_PROPERTY_NAME);
            bool hasPropDst = mtl.HasProperty(MapCoreDef.RIVER_BLEND_DST_FACTOR_PROPERTY_NAME);
            if (!hasPropSrc)
            {
                Debug.LogError($"No {MapCoreDef.RIVER_BLEND_SRC_FACTOR_PROPERTY_NAME} parameter for src Blend state!");
                return;
            }
            if (!hasPropDst)
            {
                Debug.LogError($"No {MapCoreDef.RIVER_BLEND_DST_FACTOR_PROPERTY_NAME} parameter for dst Blend state!");
                return;
            }

            if (enableAlpha)
            {
                mtl.SetInt(MapCoreDef.RIVER_BLEND_SRC_FACTOR_PROPERTY_NAME, (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                mtl.SetInt(MapCoreDef.RIVER_BLEND_DST_FACTOR_PROPERTY_NAME, (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            }
            else
            {
                mtl.SetInt(MapCoreDef.RIVER_BLEND_SRC_FACTOR_PROPERTY_NAME, (int)UnityEngine.Rendering.BlendMode.One);
                mtl.SetInt(MapCoreDef.RIVER_BLEND_DST_FACTOR_PROPERTY_NAME, (int)UnityEngine.Rendering.BlendMode.Zero);
            }
        }

        static Mesh CreatePlaneMesh(Rect r)
        {
            float halfWidth = r.width * 0.5f;
            float halfHeight = r.height * 0.5f;
            Mesh mesh = new Mesh();
            mesh.vertices = new Vector3[4]
            {
                new Vector3(-halfWidth, 0, -halfHeight),
                new Vector3(-halfWidth, 0, halfHeight),
                new Vector3(halfWidth, 0, halfHeight),
                new Vector3(halfWidth, 0, -halfHeight),
            };
            mesh.uv = new Vector2[4]
            {
                new Vector2(0, 0),
                new Vector2(0, 1),
                new Vector2(1, 1),
                new Vector2(1, 0),
            };
            mesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            mesh.RecalculateBounds();

            return mesh;
        }

        static RenderTexture CreateRenderTexture(Rect r, int textureSize)
        {
            //change texture size here!
            //添加图片生成大小数值检测处理
            if (textureSize <= 0)
                textureSize = 256;
            return new RenderTexture(textureSize, textureSize, 24, RenderTextureFormat.Default);
        }

        static void CreateCamera()
        {
            Debug.Assert(mCamera == null);
            var obj = new GameObject();
            mCamera = obj.AddComponent<Camera>();
        }

        //used for river texture creation
        static Camera mCamera;
    }
}

#endif