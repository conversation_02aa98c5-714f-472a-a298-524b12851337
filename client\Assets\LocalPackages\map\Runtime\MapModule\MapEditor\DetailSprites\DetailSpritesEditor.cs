﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace TFW.Map
{
    public partial class DetailSpritesEditor
    {
        public DetailSpritesEditor()
        {
            mGroups = new List<DetailSpriteGroupEditor>();
            var map = Map.currentMap;
            if (map != null)
            {
                var ds = (Map.currentMap.data as EditorMapData).detailSpritesSetting;
                if (ds != null)
                {
                    var groups = ds.spriteGroups;
                    if (groups != null)
                    {
                        for (int i = 0; i < groups.Count; ++i)
                        {
                            var groupEditor = new DetailSpriteGroupEditor(groups[i]);
                            mGroups.Add(groupEditor);
                        }
                        if (mGroups.Count > 0)
                        {
                            mSelectedGroup = 0;
                        }

                        CreateGroupNames();
                    }
                }
            }
        }

        public void Draw()
        {
            mDisplayUI = EditorGUILayout.Foldout(mDisplayUI, "Set Detail Sprites");
            if (mDisplayUI)
            {
                EditorGUILayout.BeginVertical("GroupBox");

                DrawSettings();
                DrawGroupOption();

                if (mSelectedGroup >= 0)
                {
                    var ds = (Map.currentMap.data as EditorMapData).detailSpritesSetting;
                    EditorGUILayout.BeginHorizontal();

                    if (GUILayout.Button("Generate All"))
                    {
                        var errorMsg = CheckValidation();
                        if (string.IsNullOrEmpty(errorMsg))
                        {
                            bool valid = CheckSpriteGroupColorMap();
                            if (valid)
                            {
                                DetailSprites d = new DetailSprites();
                                List<List<string>> prefabPaths = GetPrefabPaths();
                                d.PrecalculateValidKeysMultithread(Map.currentMap.mapWidth, Map.currentMap.mapHeight, 180, 10, prefabPaths, ds.alpha0Height, ds.alpha1Height, ds.spriteCountPerGrid, mSubgridSpriteGroupIndex, ds.updateScale, ds.crossfading);
                                string detailSpriteFolder = MapCoreDef.GetDetailSpriteObjectPath(SLGMakerEditor.instance.exportFolder);
                                string spawnPointFolder = MapCoreDef.GetDetailSpriteSpawnPointFilePath(SLGMakerEditor.instance.exportFolder);
                                d.ExportSprites1(detailSpriteFolder);
                                d.ExportSpawnPoints1(spawnPointFolder);
                            }
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", errorMsg, "OK");
                        }
                    }

                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();
            }
        }

        bool CheckSpriteGroupColorMap()
        {
            if (mSubgridSpriteGroupIndex == null)
            {
                if (EditorUtility.DisplayDialog("Warning", "No sprite group color map found! continue?", "Yes", "No"))
                {
                    return true;
                }
                return false;
            }
            return true;
        }

        List<List<string>> GetPrefabPaths()
        {
            int nGroups = mGroups.Count;
            List<List<string>> prefabPaths = new List<List<string>>(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                prefabPaths.Add(mGroups[i].GetPrefabPaths());
            }
            return prefabPaths;
        }

        string CheckValidation()
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                var errMsg = mGroups[i].CheckValidation();
                if (!string.IsNullOrEmpty(errMsg))
                {
                    return $"group {i} has error: {errMsg}";
                }
            }

            var mapData = Map.currentMap.data as EditorMapData;
            var ds = mapData.detailSpritesSetting;
            if (ds.alpha0Height < ds.alpha1Height)
            {
                return "camera height is wrong!";
            }

            return "";
        }

        void DrawSettings()
        {
            var mapData = Map.currentMap.data as EditorMapData;
            var ds = mapData.detailSpritesSetting;
            ds.spriteCountPerGrid = EditorGUILayout.IntField("Object Count Per Grid", ds.spriteCountPerGrid);
            if (ds.spriteCountPerGrid <= 0)
            {
                ds.spriteCountPerGrid = 1;
            }
            ds.alpha0Height = EditorGUILayout.FloatField("Camera Height when Sprite is invisible", ds.alpha0Height);
            ds.alpha1Height = EditorGUILayout.FloatField("Camera Height when Sprite is fully visible", ds.alpha1Height);
            ds.crossfading = EditorGUILayout.Toggle("Crossfade", ds.crossfading);
            ds.updateScale = EditorGUILayout.Toggle("Update Object Scale", ds.updateScale);
            EditorGUILayout.IntField("Horizontal Grid Count", ds.horizontalGridCount);
            EditorGUILayout.IntField("Vertical Grid Count", ds.verticalGridCount);
        }

        void DrawGroupOption()
        {
            DrawColorMap();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add Group"))
            {
                var dlg = EditorUtils.CreateInputDialog("Create New Group");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", ""),
                };
                dlg.Show(items, OnClickAddGroup);
            }
            if (GUILayout.Button("Remove Group"))
            {
                if (mSelectedGroup >= 0)
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure to delete this group?", "Yes", "No"))
                    {
                        RemoveGroup(mSelectedGroup);
                    }
                }
            }
            if (GUILayout.Button("Change Group Name"))
            {
                if (mSelectedGroup >= 0)
                {
                    var dlg = EditorUtils.CreateInputDialog("Change Group Name");
                    var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", mGroups[mSelectedGroup].group.name),
                };
                    System.Func<List<InputDialog.Item>, bool> func = (List<InputDialog.Item> parameters) =>
                    {
                        string name = (parameters[0] as InputDialog.StringItem).text;
                        if (string.IsNullOrEmpty(name))
                        {
                            return false;
                        }

                        var groupEditor = GetGroupEditor(name);
                        if (groupEditor != null)
                        {
                            return false;
                        }

                        var ds = (Map.currentMap.data as EditorMapData).detailSpritesSetting;
                        ds.spriteGroups[mSelectedGroup].name = name;
                        CreateGroupNames();

                        return true;
                    };
                    dlg.Show(items, func);
                }
            }
            EditorGUILayout.EndHorizontal();

            if (mGroupNames != null)
            {
                int newSelectedGroup = EditorGUILayout.Popup(mSelectedGroup, mGroupNames);
                if (newSelectedGroup != mSelectedGroup)
                {
                    mSelectedGroup = newSelectedGroup;
                }
            }

            if (mSelectedGroup >= 0)
            {
                mGroups[mSelectedGroup].Draw();
            }
        }

        bool OnClickAddGroup(List<InputDialog.Item> parameters)
        {
            string name = (parameters[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(name))
            {
                return false;
            }

            var groupEditor = GetGroupEditor(name);
            if (groupEditor != null)
            {
                return false;
            }

            var ds = (Map.currentMap.data as EditorMapData).detailSpritesSetting;
            var group = new DetailSpriteGroup(name, new Color32(255, 255, 255, 255), null);
            groupEditor = new DetailSpriteGroupEditor(group);
            ds.spriteGroups.Add(group);
            mGroups.Add(groupEditor);
            mSelectedGroup = mGroups.Count - 1;

            CreateGroupNames();
            return true;
        }

        void RemoveGroup(int idx)
        {
            if (idx >= 0 && idx < mGroups.Count)
            {
                var ds = (Map.currentMap.data as EditorMapData).detailSpritesSetting;
                ds.spriteGroups.RemoveAt(idx);
                mGroups.RemoveAt(idx);
                mSelectedGroup = mGroups.Count - 1;
            }

            CreateGroupNames();
        }

        void CreateGroupNames()
        {
            var ds = (Map.currentMap.data as EditorMapData).detailSpritesSetting;
            int n = ds.spriteGroups.Count;
            if (n > 0)
            {
                mGroupNames = new string[n];
                for (int i = 0; i < n; ++i)
                {
                    mGroupNames[i] = ds.spriteGroups[i].name;
                }
            }
            else
            {
                mGroupNames = null;
            }
        }

        DetailSpriteGroupEditor GetGroupEditor(string name)
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                if (mGroups[i].group.name == name)
                {
                    return mGroups[i];
                }
            }
            return null;
        }

        void DrawColorMap()
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.TextField("Color Map", mColorMap);
            if (GUILayout.Button("Select"))
            {
                mSubgridSpriteGroupIndex = ImportColorMap(out mColorMap);
            }
            EditorGUILayout.EndHorizontal();
        }

        byte[] ImportColorMap(out string openFilePath)
        {
            openFilePath = "";
            string filePath = EditorUtility.OpenFilePanelWithFilters("Color Map", "", new string[] { "tga", "tga" });
            if (!string.IsNullOrEmpty(filePath))
            {
                var bytes = File.ReadAllBytes(filePath);
                if (bytes != null)
                {
                    var tgaData = new TgaDecoderTest.TgaData(bytes);
                    int width = tgaData.Width;
                    int height = tgaData.Height;
                    var ds = (Map.currentMap.data as EditorMapData).detailSpritesSetting;
                    if (width != ds.horizontalGridCount ||
                        height != ds.verticalGridCount)
                    {
                        EditorUtility.DisplayDialog("Error", "Invalid color map, size not match!", "OK");
                        return null;
                    }
                    var editorData = Map.currentMap.data as EditorMapData;

                    byte[] subgridSpriteGroupIndex = new byte[width * height];
                    for (int i = 0; i < height; ++i)
                    {
                        for (int j = 0; j < width; ++j)
                        {
                            var idx = i * width + j;
                            Color32 pixel = tgaData.GetPixel(j, i);
                            bool found;
                            byte groupIdx = GetSpriteGroupEditorIndex(pixel, out found);
                            if (!found)
                            {
                                EditorUtility.DisplayDialog("Error", string.Format("color [{0}] is not found", pixel), "OK");
                                return null;
                            }
                            subgridSpriteGroupIndex[idx] = groupIdx;
                        }
                    }

                    openFilePath = filePath;
                    return subgridSpriteGroupIndex;
                }
            }
            return null;
        }

        byte GetSpriteGroupEditorIndex(Color32 color, out bool found)
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                var groupColor = mGroups[i].group.color;
                if (color.r == groupColor.r &&
                    color.g == groupColor.g &&
                    color.b == groupColor.b)
                {
                    found = true;
                    return (byte)i;
                }
            }
            found = false;
            return 0;
        }

        List<DetailSpriteGroupEditor> mGroups;
        int mSelectedGroup = -1;
        bool mDisplayUI = true;
        string[] mGroupNames;

        string mColorMap;
        byte[] mSubgridSpriteGroupIndex;
    }
}


#endif