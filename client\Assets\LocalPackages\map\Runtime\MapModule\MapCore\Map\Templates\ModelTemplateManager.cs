﻿ 



 
 



using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //管理地图上实体使用的prefab配置
    public class ModelTemplateManager
    {
        public ModelTemplateManager(Map map, List<ModelTemplate> modelTemplates)
        {
            mMap = map;
            for (int i = 0; i < modelTemplates.Count; ++i)
            {
                mModelTemplates[modelTemplates[i].id] = modelTemplates[i];
            }
        }

        public void OnDestroy()
        {
            foreach (var p in mModelTemplates)
            {
                mMap.DestroyObject(p.Value);
            }
            mModelTemplates = null;
        }

        public void AddModelTemplate(ModelTemplate temp)
        {
            if (!mModelTemplates.ContainsKey(temp.id))
            {
                mModelTemplates[temp.id] = temp;
                string prefabPath = temp.GetLODPrefabPath(0);
                if (!string.IsNullOrEmpty(prefabPath))
                {
                    mModelTemplatesByPath[prefabPath] = temp;
                }
            }
        }

        public void RemoveModelTemplate(string path)
        {
            ModelTemplate temp;
            mModelTemplatesByPath.TryGetValue(path, out temp);
            if (temp != null)
            {
                mModelTemplates.Remove(temp.id);
                mModelTemplatesByPath.Remove(path);
                //注意,没有删除mObjectUsedModelTemplates里保存的model template,目前可以不用删除,因为在SetObjectUsedModelTemplate里会删除
            }
        }

        public ModelTemplate FindModelTemplate(string prefabPath)
        {
            if (string.IsNullOrEmpty(prefabPath))
            {
                return null;
            }
            ModelTemplate modelTemplate = null;
            mModelTemplatesByPath.TryGetValue(prefabPath, out modelTemplate);
            return modelTemplate;
        }

        //创建或引用一个地图对象模板
        public ModelTemplate GetOrCreateModelTemplate(int objectDataID, string prefabPath, bool tileModelTemplate, bool preload, Map map)
        {
            if (string.IsNullOrEmpty(prefabPath))
            {
                return null;
            }

            ModelTemplate modelTemplate = FindModelTemplate(prefabPath);
            if (modelTemplate == null)
            {
                var gameObject = MapModuleResourceMgr.LoadPrefab(prefabPath);
                if (gameObject != null)
                {
                    var id = map.nextCustomObjectID;
                    var bounds = GameObjectBoundsCalculator.CalculateRect(gameObject);

                    modelTemplate = new ModelTemplate(id, map, prefabPath, bounds, tileModelTemplate, new List<List<ModelTemplate>>(), new List<List<ChildPrefabTransform>>(), null, preload);

                    mModelTemplates[modelTemplate.id] = modelTemplate;
                    mModelTemplatesByPath[prefabPath] = modelTemplate;

                    if (objectDataID != 0)
                    {
                        mObjectUsedModelTemplates[objectDataID] = modelTemplate;
                    }
                }
                else
                {
                    UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate gameObject null");
                }
            }
            else
            {
                if (modelTemplate.isTileModelTemplate == false && tileModelTemplate)
                {
                    modelTemplate.isTileModelTemplate = true;
                }
                if (objectDataID != 0)
                {
                    mObjectUsedModelTemplates[objectDataID] = modelTemplate;
                }
            }
            return modelTemplate;
        }

        //创建或引用一个地图对象模板
        public void GetOrCreateModelTemplateAsync(int objectDataID, string prefabPath, bool tileModelTemplate, bool preload, Map map, System.Action<ModelTemplate> action)
        {
            if (string.IsNullOrEmpty(prefabPath))
            {
                action.Invoke(null);
            }

            ModelTemplate modelTemplate = FindModelTemplate(prefabPath);
            if (modelTemplate == null)
            { 
                //MapModuleResourceMgr.LoadGameObjectAsync(prefabPath, null, (_path, result)=>
                //{
                //    var gameObject = result;
                //    if (gameObject != null)
                //    {
                //        try
                //        {
                //            var id = Map.currentMap.nextCustomObjectID;
                //            var bounds = GameObjectBoundsCalculator.CalculateRect(gameObject);

                //            Debug.LogError($"{objectDataID}:<{gameObject.name}>  Rect:{bounds}");

                //            modelTemplate = new ModelTemplate(id, map, prefabPath, bounds, tileModelTemplate, new List<List<ModelTemplate>>(), new List<List<ChildPrefabTransform>>(), null, preload);

                //            mModelTemplates[modelTemplate.id] = modelTemplate;
                //            mModelTemplatesByPath[prefabPath] = modelTemplate;

                //            if (objectDataID != 0)
                //            {
                //                mObjectUsedModelTemplates[objectDataID] = modelTemplate;
                //            }

                //            MapModuleResourceMgr.UnloadAsset(prefabPath);
                //        }
                //        catch(System.Exception e)
                //        {
                //            Debug.LogException(e);
                //        }
                //    }
                //    else
                //    {
                //        Debug.LogError($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate gameObject null");
                //    }
                //    action.Invoke(modelTemplate);
                //});
                var id = Map.currentMap.nextCustomObjectID;
                Rect bounds;
                if (prefabPath == "Assets/Res/map/MapData/Res/LOD_1_Tile_0_0.prefab")
                {
                    //最大一张
                     bounds = new Rect(-4050f,-4050f,8100,8100);
                }
                else
                {
                    //
                    bounds = new Rect(-810f, -810f, 1620, 1620);
                }
                modelTemplate = new ModelTemplate(id, map, prefabPath, bounds, tileModelTemplate, new List<List<ModelTemplate>>(), new List<List<ChildPrefabTransform>>(), null, preload);

                mModelTemplates[modelTemplate.id] = modelTemplate;
                mModelTemplatesByPath[prefabPath] = modelTemplate;

                if (objectDataID != 0)
                {
                    mObjectUsedModelTemplates[objectDataID] = modelTemplate;
                }
                 
                //preloadObj(prefabPath);
            }
            else
            {
                if (modelTemplate.isTileModelTemplate == false && tileModelTemplate)
                {
                    modelTemplate.isTileModelTemplate = true;
                }
                if (objectDataID != 0)
                {
                    mObjectUsedModelTemplates[objectDataID] = modelTemplate;
                }
            }

            action.Invoke(modelTemplate);
        }


        //private void preloadObj(string path)
        //{
        //    var gameobjPool = Map.currentMap.view.reusableGameObjectPool;
        //    gameobjPool.Require(path, (a) =>
        //    {
        //        gameobjPool.Release(path, a, Map.currentMap);
        //    });
        //}


        public void SetObjectUsedModelTemplate(int objectDataID, ModelTemplate modelTemplate)
        {
            if (modelTemplate == null)
            {
                mObjectUsedModelTemplates.Remove(objectDataID);
            }
            else
            {
                mObjectUsedModelTemplates[objectDataID] = modelTemplate;
            }
        }

        public ModelTemplate GetEntityModelTemplate(int objectDataID)
        {
            ModelTemplate modelTemplate;
            mObjectUsedModelTemplates.TryGetValue(objectDataID, out modelTemplate);
            return modelTemplate;
        }

        //返回除了front layer的所有使用了的model template
        HashSet<int> GetModelTemplatesNotUsedInFrontLayer()
        {
            HashSet<int> result = new HashSet<int>();
#if UNITY_EDITOR
            var frontLayer = mMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            foreach (var p in mObjectUsedModelTemplates)
            {
                if (frontLayer != null)
                {
                    var tileData = frontLayer.GetObjectData(p.Key);
                    if (tileData == null)
                    {
                        result.Add(p.Value.id);
                    }
                }
                else
                {
                    result.Add(p.Value.id);
                }
            }
#endif

            return result;
        }

        public List<ModelTemplate> GetUsedModelTemplates()
        {
            HashSet<int> usedModelTemplates = null;

            if (MapModule.doNotExportFrontLayerModelTemplates)
            {
                usedModelTemplates = GetModelTemplatesNotUsedInFrontLayer();
            }
            else
            {
                usedModelTemplates = new HashSet<int>();

                foreach (var p in mObjectUsedModelTemplates)
                {
                    if (usedModelTemplates.Contains(p.Value.id) == false)
                    {
                        usedModelTemplates.Add(p.Value.id);
                    }
                }
            }

            List<ModelTemplate> modelTemplatesList = new List<ModelTemplate>();
            foreach (var id in usedModelTemplates)
            {
                var modelTemplate = mMap.FindObject(id) as ModelTemplate;
                modelTemplatesList.Add(modelTemplate);
            }
            return modelTemplatesList;
        }

        public void ReplaceModelTemplatePrefab(string oldPrefabPath, string newPrefabPath)
        {
            var modelTemplate = FindModelTemplate(oldPrefabPath);
            if (modelTemplate != null)
            {
                if (!modelTemplate.isTileModelTemplate)
                {
                    modelTemplate.ReplacePrefab(newPrefabPath);
                }

                bool suc = mModelTemplatesByPath.Remove(oldPrefabPath);
                Debug.Assert(suc);
                mModelTemplatesByPath[newPrefabPath] = modelTemplate;
            }
        }

        public void FindReference(int modelTemplateID, List<int> referencedObjectIDs)
        {
            foreach (var p in mObjectUsedModelTemplates)
            {
                var modelTemplate = p.Value;
                if (modelTemplate.id == modelTemplateID)
                {
                    referencedObjectIDs.Add(p.Key);
                }
            }
        }

        public Dictionary<int, ModelTemplate> modelTemplates { get { return mModelTemplates; } }

        //地图对象的模板,例如山,水等
        Map mMap;
        protected Dictionary<int, ModelTemplate> mModelTemplates = new Dictionary<int, ModelTemplate>();
        protected Dictionary<string, ModelTemplate> mModelTemplatesByPath = new Dictionary<string, ModelTemplate>();
        protected Dictionary<int, ModelTemplate> mObjectUsedModelTemplates = new Dictionary<int, ModelTemplate>();
    }
}
