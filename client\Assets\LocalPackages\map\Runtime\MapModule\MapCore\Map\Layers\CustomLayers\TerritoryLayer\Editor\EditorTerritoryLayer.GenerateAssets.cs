﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public partial class EditorTerritoryLayer : MapLayerBase
    {
        //create curve region assets
        public void GenerateOutlineAssets(string exportFolder, float layerWidth, float layerHeight, int horizontalTileCount, int verticalTileCount)
        {
            if (string.IsNullOrEmpty(exportFolder))
            {
                Debug.LogError("Invalid export folder!");
                return;
            }

            string assetFolder = $"{exportFolder}/{MapCoreDef.NEW_TERRITORY_LAYER_RUNTIME_ASSETS_FOLDER_NAME}";
            FileUtil.DeleteFileOrDirectory(assetFolder);
            Directory.CreateDirectory(assetFolder);
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            int nLODs = lodCount;
            for (int lod = 0; lod < nLODs; ++lod)
            {
                for (int i = 0; i < mSubLayers.Count; ++i)
                {
                    AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
                    mSubLayers[i].CreateAndGenerateOutlineAssets(exportFolder, i, lod, true, layerWidth, layerHeight, horizontalTileCount, verticalTileCount);
                }
            }
        }

        public string GetAssetPathLOD0(string exportFolder, int layer, int territoryID)
        {
            string assetFolder = $"{exportFolder}/{MapCoreDef.TERRITORY_LAYER_RUNTIME_ASSETS_FOLDER_NAME}";
            return $"{assetFolder}/layer{layer}/territory_{territoryID}_lod0.prefab";
        }

        public void GenerateAssets(string folder)
        {
            if (string.IsNullOrEmpty(folder))
            {
                Debug.LogError("Invalid export folder!");
                return;
            }

            string assetFolder = $"{folder}/{MapCoreDef.TERRITORY_LAYER_RUNTIME_ASSETS_FOLDER_NAME}";
            FileUtil.DeleteFileOrDirectory(assetFolder);
            Directory.CreateDirectory(assetFolder);
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            for (int i = 0; i < mSubLayers.Count; ++i)
            {
                string subLayerAssetFolder = $"{assetFolder}/layer{i}";
                Directory.CreateDirectory(subLayerAssetFolder);
                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
                mSubLayers[i].GenerateAssets(subLayerAssetFolder);
            }
        }
    }
}

#endif