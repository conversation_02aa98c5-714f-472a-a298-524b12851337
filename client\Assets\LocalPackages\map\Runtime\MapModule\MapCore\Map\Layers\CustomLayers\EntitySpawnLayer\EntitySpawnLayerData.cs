﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TFW.Map
{
    //npc的刷新区域
    public class EntitySpawnRegion
    {
        public EntitySpawnRegion(Vector2Int coord, List<Vector2> spawnPoints, bool visible, int seed)
        {
            if (seed == 0)
            {
                seed = 9876543;
            }

            this.coord = coord;
            this.spawnPoints = spawnPoints;
            this.visible = visible;
            this.randomGenerator = new RandomWrapper(seed);
        }

        public Vector2Int coord;
        public bool visible = false;
        public List<Vector2> spawnPoints { set; get; }
        public RandomWrapper randomGenerator { set; get; }
    }

    public class EntitySpawnRegionBrush
    {
        public int id;
        public Color color = Color.white;
        public int priority = 0;
        public bool canGenerateSpawnPoints = true;
    }

    //npc的巡逻点集
    public class EntityMoveInfo
    {
        public EntityMoveInfo(List<Vector2> waypoints)
        {
            mWaypoints = waypoints;
        }

        public List<Vector2> waypoints { get { return mWaypoints; } }

        List<Vector2> mWaypoints;
    }

    //npc刷新区域的地图层
    public class EntitySpawnLayerData : MapLayerData
    {
        //regionSize: 一个刷新区域的大小
        //npcDensity: 每多少平方米一个生成一个npc刷新点
        //npcMoveRange: npc移动范围
        public EntitySpawnLayerData(MapLayerDataHeader header, Map map, Vector2 regionSize, Vector2 npcMoveRange, List<Vector2> waypoints, List<EntitySpawnRegion> regions, ISpawnPointGenerationStrategy strategy, int[,] regionBrushIDs, List<EntitySpawnRegionBrush> regionBrushes, string spawnPointTileTypeReferenceNPCRegionLayerName) : base(header, null, map)
        {
            Debug.Assert(strategy != null);

            mNPCMoveRange = npcMoveRange;
            mRegionSize = regionSize;
            mNPCMoveInfo = new EntityMoveInfo(waypoints);
            mRegions = regions;
            mGenerationStrategy = strategy;
            mRegionBrushes = regionBrushes;
            mRegionBrushIDs = regionBrushIDs;
            mSpawnPointTypeReferenceNPCRegionLayerName = spawnPointTileTypeReferenceNPCRegionLayerName;
            if (mRegionBrushes == null)
            {
                mRegionBrushes = new List<EntitySpawnRegionBrush>();
            }
            if (mRegionBrushIDs == null)
            {
                mRegionBrushIDs = new int[header.rows, header.cols];
            }

            if (regions == null || regions.Count == 0)
            {
                mRegions = new List<EntitySpawnRegion>();
                for (int i = 0; i < verticalTileCount; ++i)
                {
                    for (int j = 0; j < horizontalTileCount; ++j)
                    {
                        EntitySpawnRegion regionData = new EntitySpawnRegion(new Vector2Int(j, i), new List<Vector2>(), false, Time.frameCount + mRegions.Count);
                        mRegions.Add(regionData);
                    }
                }
            }
            else
            {
                mIsCreated = true;
            }
        }

        public override void OnDestroy()
        {
            mRegionBrushes.Clear();
        }

        //mapRadius: 是否考虑圆形的地图边界
        public void GenerateSpawnPoints(bool generateVisibleRegions, float mapRadius)
        {
            GenerateSpawnPointsMultithread(generateVisibleRegions, mapRadius);
        }

        Task CreateTask(int taskIndex, MapRegion[] regions, int minRegionIndex, int maxRegionIndex, float mapRadius, bool generateVisibleRegions)
        {
            var pool = Triangulator.GetPool(taskIndex);

            var task = Task.Run(() =>
            {
                TriangleNet.TrianglePool localPool = pool;
                for (int k = minRegionIndex; k <= maxRegionIndex; ++k)
                {
                    bool generate = CanGenerateSpawnPoints(generateVisibleRegions, k);
                    GenerateSpawnPointsInRegion(k, regions[k], mapRadius, generate, localPool);
                }
                localPool.Clear();
            });

            return task;
        } 

        //mapRadius: 是否考虑圆形的地图边界
        public void GenerateSpawnPointsMultithread(bool generateVisibleRegions, float mapRadius)
        {
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            mIsCreated = true;

            var inSpecialRegion = (Map.currentMap as EditorMap).generateNPCSpawnPointsInBorderLine;
            CreateMapRegions(inSpecialRegion);
            var regions = mNPCRegionManager.regions;

#if false
            int idx = 0;
            GenerateSpawnPointsInRegion(idx, regions[idx], mapRadius, true, null);
#else
            List<Task> tasks = new List<Task>();
            
            int threadCount = System.Environment.ProcessorCount;
            int regionCountPerThread = Mathf.CeilToInt(regions.Length / (float)threadCount);

            for (int i = 0; i < threadCount; ++i)
            {
                int min = i * regionCountPerThread;
                int max = Mathf.Min(min + regionCountPerThread - 1, regions.Length - 1);
                var task = CreateTask(i, regions, min, max, mapRadius, generateVisibleRegions);
                tasks.Add(task);
            }

            Task.WaitAll(tasks.ToArray());
#endif
            //System.GC.Collect();

            var elapsedTime = w.Stop();
            Debug.Log($"elapsedTime: {elapsedTime}");
        }

        void CreateMapRegions(bool specialRegion)
        {
            float mapWidth = GetLayerWidthInMeter();
            float mapHeight = GetLayerHeightInMeter();
            mNPCRegionManager = new MapRegionManager();
            float maxRange = Mathf.Max(mNPCMoveRange.x, mNPCMoveRange.y);
            mNPCRegionManager.CreateRegions(0, 0, mapWidth, mapHeight, mRegionSize.x, mRegionSize.y, maxRange, PrefabOutlineType.ObjectPlacementObstacle, CheckMapCollisionOperation.kGenerateMapObstacles, specialRegion, true);
        }

        public void ToggleRegionVisibility(int startX, int startY, int endX, int endY, bool visible)
        {
            for (int y = startY; y <= endY; ++y)
            {
                for (int x = startX; x <= endX; ++x)
                {
                    if (x >= 0 && x < horizontalTileCount && y >= 0 && y < verticalTileCount)
                    {
                        var idx = y * horizontalTileCount + x;
                        mRegions[idx].visible = visible;
                    }
                }
            }
        }

        public bool PickSpawnPoint(Vector2 worldPos, float pickRadius, out int spawnPointIndex, out int regionIndex, out Vector2 spawnPointPos)
        {
            spawnPointIndex = -1;
            spawnPointPos = Vector2.zero;
            regionIndex = GetRegion(worldPos.x, worldPos.y);
            if (regionIndex >= 0)
            {
                if (mRegions[regionIndex].visible)
                {
                    var spawnPoints = mRegions[regionIndex].spawnPoints;
                    int n = spawnPoints.Count;
                    float radiusSqr = pickRadius * pickRadius;
                    for (int i = 0; i < n; ++i)
                    {
                        var d = worldPos - spawnPoints[i];
                        if (d.sqrMagnitude <= radiusSqr)
                        {
                            spawnPointPos = spawnPoints[i];
                            spawnPointIndex = i;
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        int GetRegion(float x, float y)
        {
            int col = Mathf.FloorToInt(x / tileWidth);
            int row = Mathf.FloorToInt(y / tileHeight);
            if (row >= 0 && row < verticalTileCount && col >= 0 && col < horizontalTileCount)
            {
                return row * horizontalTileCount + col;
            }
            return -1;
        }

        public Vector2Int GetRegionIndex(Vector3 worldPos)
        {
            int regionIndex = GetRegion(worldPos.x, worldPos.z);
            int rows = regionIndex / horizontalTileCount;
            int cols = regionIndex % horizontalTileCount;
            return new Vector2Int(cols, rows);
        }

        public Vector3 GetRegionPosition(int regionIdx)
        {
            int rows = regionIdx / horizontalTileCount;
            int cols = regionIdx % horizontalTileCount;
            return new Vector3((cols + 0.5f) * tileWidth, 0, (rows + 0.5f) * tileHeight);
        }

        public Vector3[] GetRegionRect(int regionIdx)
        {
            int rows = regionIdx / horizontalTileCount;
            int cols = regionIdx % horizontalTileCount;
            return new Vector3[]
            {
                new Vector3(cols * tileWidth, 0, rows * tileHeight),
                new Vector3((cols + 1) * tileWidth, 0, rows * tileHeight),
                new Vector3((cols + 1) * tileWidth, 0, (rows + 1) * tileHeight),
                new Vector3(cols * tileWidth, 0, (rows + 1) * tileHeight)
            };
        }

        public float GetRegionEmptyArea(int regionIdx)
        {
            var regions = mNPCRegionManager.regions;
            if (regions != null && regionIdx < regions.Length && regions[regionIdx] != null)
            {
                return regions[regionIdx].GetValidArea();
            }
            return 0;
        }

        bool CanGenerateSpawnPoints(bool generateVisibleRegions, int regionIdx)
        {
            bool generate = true;
            if (generateVisibleRegions)
            {
                generate = generateVisibleRegions && mRegions[regionIdx].visible;
            }

            int rx = regionIdx % mCols;
            int ry = regionIdx / mCols;
            generate &= GetRegionGenerateSpawnPointToggle(rx, ry);

            return generate;
        }

        //创建每个区域的npc刷新点
        //mapRadius: 圆形地图的半径
        void GenerateSpawnPointsInRegion(int regionIdx, MapRegion region, float mapRadius, bool generate, TriangleNet.TrianglePool pool)
        {
            if (generate)
            {
                //reset seed
                mRegions[regionIdx].randomGenerator = new RandomWrapper(mRegions[regionIdx].randomGenerator.seed);
                mRegions[regionIdx].spawnPoints = mGenerationStrategy.Generate(region, mapRadius, mRegions[regionIdx].randomGenerator, pool);

#if false
                //检查point + move range是否在地图内
                var points = mRegions[regionIdx].spawnPoints;
                var wayPoints = mNPCMoveInfo.waypoints;
                int wayPointCount = wayPoints.Count;
                float mapWidth = Map.currentMap.mapWidth;
                float mapHeight = Map.currentMap.mapHeight;
                for (int i = 0; i < points.Count; ++i)
                {
                    for (int k = 0; k < wayPointCount; ++k)
                    {
                        float x = points[i].x + wayPoints[k].x;
                        float z = points[i].y + wayPoints[k].y;
                        if (x < 0 || z < 0 || x > mapWidth || z > mapHeight)
                        {
                            Debug.Assert(false, $"invalid npc point {points[i].x} {points[i].y} {wayPoints[k].x} {wayPoints[k].y}  in {regionIdx}");
                        }
                    }
                }
#endif
            }
            else
            {
                mRegions[regionIdx].spawnPoints.Clear();
            }
        }

        public void Resize(int newWidth, int newHeight, bool useLayerOffset)
        {
            mRows = newWidth;
            mCols = newHeight;
            mRegions = new List<EntitySpawnRegion>();
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    EntitySpawnRegion regionData = new EntitySpawnRegion(new Vector2Int(j, i), new List<Vector2>(), false, 0);
                    mRegions.Add(regionData);
                }
            }

            if (!useLayerOffset)
            {
                mLayerOrigin = Vector3.zero;
            }
        }

        public EntitySpawnRegionBrush FindBrush(int brushID)
        {
            for (int i = 0; i < mRegionBrushes.Count; ++i)
            {
                if (mRegionBrushes[i].id == brushID)
                {
                    return mRegionBrushes[i];
                }
            }
            return null;
        }

        public EntitySpawnRegionBrush GetBrush(int x, int y)
        {
            return FindBrush(mRegionBrushIDs[y, x]);
        }

        public int GetRegionPriority(int x, int y)
        {
            var brush = FindBrush(mRegionBrushIDs[y, x]);
            if (brush == null)
            {
                return mDefaultRegionPriority;
            }
            return brush.priority;
        }

        public bool GetRegionGenerateSpawnPointToggle(int x, int y)
        {
            var brush = FindBrush(mRegionBrushIDs[y, x]);
            if (brush == null)
            {
                return true;
            }
            return brush.canGenerateSpawnPoints;
        }

        public EntitySpawnRegionBrush AddBrush(int brushID, Color color, int priority)
        {
            var template = new EntitySpawnRegionBrush { id = brushID, color = color, priority = priority };
            mRegionBrushes.Add(template);

            return template;
        }

        public void RemoveBrush(int index)
        {
            if (index >= 0 && index < mRegionBrushes.Count)
            {
                mRegionBrushes.RemoveAt(index);
            }
        }

        public void SetGrid(Vector3 pos, int brushSize, int type)
        {
            Vector2Int coord = FromWorldPositionToCoordinate(pos);
            int startX = coord.x - brushSize / 2;
            int startY = coord.y - brushSize / 2;
            int endX = startX + brushSize - 1;
            int endY = startY + brushSize - 1;

            if (endX < 0 || endY < 0 || startX >= mCols || startY >= mRows)
            {
                return;
            }

            startX = Mathf.Clamp(startX, 0, mCols - 1);
            startY = Mathf.Clamp(startY, 0, mRows - 1);
            endX = Mathf.Clamp(endX, 0, mCols - 1);
            endY = Mathf.Clamp(endY, 0, mRows - 1);

            int width = endX - startX + 1;
            int height = endY - startY + 1;
            var pixels = mColorArrayPool.Rent(width * height);
            int idx = 0;
            var template = FindBrush(type);
            Color32 color = new Color32(0, 0, 0, 0);
            if (template != null)
            {
                color = template.color;
            }
            for (int i = startY; i <= endY; ++i)
            {
                for (int j = startX; j <= endX; ++j)
                {
                    mRegionBrushIDs[i, j] = type;
                    pixels[idx] = color;
                    ++idx;
                }
            }
            mOnSetPixels(startX, startY, width, height, pixels);
            mColorArrayPool.Return(pixels);
        }

        public void SetGridData(int x, int y, int type)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                if (mRegionBrushIDs[y, x] != type)
                {
                    mRegionBrushIDs[y, x] = type;
                }
            }
        }

        public int GetPriority(int x, int y)
        {
            var id = mRegionBrushIDs[y, x];
            var brush = FindBrush(id);
            if (brush != null)
            {
                return brush.priority;
            }
            return mDefaultRegionPriority;
        }

        public override bool isGameLayer => false;

        public List<EntitySpawnRegion> regions { get { return mRegions; } }
        public EntityMoveInfo entityMoveInfo { get { return mNPCMoveInfo; } }
        public Vector2 npcMoveRange { get { return mNPCMoveRange; } }
        public int[,] regionBrushIDs { get { return mRegionBrushIDs; } }
        public bool isCreated
        {
            get
            {
                return mIsCreated;
            }
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom) { return false; }
        public override void RefreshObjectsInViewport() { }
        public override bool Contains(int objectID) { return false; }

        public ISpawnPointGenerationStrategy strategy { get { return mGenerationStrategy; } }
        public void SetStrategy(ISpawnPointGenerationStrategy strategy)
        {
            mGenerationStrategy = strategy;
        }

        public List<EntitySpawnRegionBrush> regionBrushes { get { return mRegionBrushes; } }
        public void SetOnPixelsChangeCallback(System.Action<int, int, int, int, Color32[]> onSetPixels)
        {
            mOnSetPixels = onSetPixels;
        }

        class SortRegion : IComparer<EntitySpawnRegion>
        {
            EntitySpawnLayerData mLayerData;

            public SortRegion(EntitySpawnLayerData layerData)
            {
                mLayerData = layerData;
            }

            public int Compare(EntitySpawnRegion x, EntitySpawnRegion y)
            {
                var pa = mLayerData.GetRegionPriority(x.coord.x, x.coord.y);
                var pb = mLayerData.GetRegionPriority(y.coord.x, y.coord.y);
                return pa - pb;
            }
        }

        public List<EntitySpawnRegion> GetSortedRegions()
        {
            List<EntitySpawnRegion> sortedRegions = new List<EntitySpawnRegion>();
            sortedRegions.AddRange(mRegions);
            sortedRegions.Sort(new SortRegion(this));
            return sortedRegions;
        }

        public void ChangeSeed()
        {
            if (mRegions != null)
            {
                var r = new System.Random();
                for (int i = 0; i < mRegions.Count; ++i)
                {
                    if (mRegions[i].visible)
                    {
                        mRegions[i].randomGenerator = new RandomWrapper(r.Next());
                    }
                }
            }
        }

        public string spawnPointTypeReferenceNPCRegionLayerName { get { return mSpawnPointTypeReferenceNPCRegionLayerName; } set { mSpawnPointTypeReferenceNPCRegionLayerName = value; } }

        ISpawnPointGenerationStrategy mGenerationStrategy;
        List<EntitySpawnRegion> mRegions;
        EntityMoveInfo mNPCMoveInfo;
        MapRegionManager mNPCRegionManager;
        Vector2 mNPCMoveRange;
        Vector2 mRegionSize;
        string mSpawnPointTypeReferenceNPCRegionLayerName;
        int[,] mRegionBrushIDs;
        List<EntitySpawnRegionBrush> mRegionBrushes = new List<EntitySpawnRegionBrush>();
        bool mIsCreated = false;
        ArrayPool<Color32> mColorArrayPool = ArrayPool<Color32>.Create();
        System.Action<int, int, int, int, Color32[]> mOnSetPixels;

        static int mDefaultRegionPriority = int.MaxValue;
    }
}


#endif