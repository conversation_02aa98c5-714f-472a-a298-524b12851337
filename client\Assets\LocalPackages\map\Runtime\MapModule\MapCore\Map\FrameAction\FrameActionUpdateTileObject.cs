﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //检测tile object是否可见
    public class FrameActionUpdateTileObject : FrameAction
    {
        public static FrameActionUpdateTileObject Require(TileGridObjectLayerData layerData, int tileObjectID)
        {
            var act = mPool.Require();
            act.Init(layerData, tileObjectID);
            return act;
        }

        void Init(TileGridObjectLayerData layerData, int tileObjectID)
        {
            InitAction();
            mLayerData = layerData;
            mTileObjectID = tileObjectID;
            mKey = MakeActionKey(tileObjectID, FrameActionType.UpdateTileObject);
        }

        protected override void DoImpl()
        {
            var map = mLayerData.map;
            var tileObject = mLayerData.GetTileObject(mTileObjectID);
            //现在只管理山的视野,因为山太大了,不适合用管理小件物体的方法管理
            bool overlap = tileObject.GetBounds().Overlaps(map.viewport);
            bool isActive = tileObject.IsObjActive();
            if (overlap != isActive)
            {
                //隐藏视野外的物体,显示视野内的物体
                mLayerData.SetObjectActiveOnly(tileObject, overlap, tileObject.lod);
            }
        }

        public static long MakeActionKey(int id, FrameActionType type)
        {
            return MakeKeyHelper(id, type);
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mPool.Release(this);
        }

        public override long key { get { return mKey; } }
        public override FrameActionType type => FrameActionType.UpdateTileObject;
        public override string debugInfo => "";
        public override string name => "Update Tile Object";

        TileGridObjectLayerData mLayerData;
        int mTileObjectID;
        long mKey;

        static ObjectPool<FrameActionUpdateTileObject> mPool = new ObjectPool<FrameActionUpdateTileObject>(1000, () => new FrameActionUpdateTileObject());
    }
}
