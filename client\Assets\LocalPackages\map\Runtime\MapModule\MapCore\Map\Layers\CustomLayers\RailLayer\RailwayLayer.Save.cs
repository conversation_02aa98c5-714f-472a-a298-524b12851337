﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class RailwayLayer : ModelLayer
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.RailwayLayerEditorDataVersion);
            PrepareSaving();
            SaveSetting(writer);
            SaveLayer(writer);
            SavePathMapper(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);

            IDExporter idExporter = new IDExporter();
            IDExporter groupIDExporter = new IDExporter();

            var allObjects = layerData.objects;
            int n = allObjects.Count;
            int nGroups = layerData.lodGroupManager.groups.Count;
            writer.Write(n);
            writer.Write(nGroups);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveRailObjectData(writer, objData as RailObjectData, idExporter, groupIDExporter);
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layerData);
            SaveModelLODGroupManager(writer, layerData.lodGroupManager, idExporter, groupIDExporter);

            writer.Write(railLayerData.railCount);
            writer.Write(railLayerData.railWidth);
            writer.Write(railLayerData.railTotalLength);
            Utils.WriteVector3(writer, railLayerData.railwayCenter);
            writer.Write(railLayerData.railPrefabLength);
        }

        void SaveRailObjectData(BinaryWriter writer, RailObjectData data, IDExporter idExporter, IDExporter groupIDExporter)
        {
            var railData = data as RailObjectData;
            writer.Write(data.GetFlag());
            idExporter.Export(writer, data.id);
            Utils.WriteVector3(writer, data.GetPosition());
            Utils.WriteQuaternion(writer, data.GetRotation());
            Utils.WriteVector3(writer, data.GetScale());

            short prefabPathIndex = GetPrefabPathIndex(railData.GetModelTemplate());
            writer.Write(prefabPathIndex);
            writer.Write((int)data.type);
            writer.Write(railData.isGroupLeader);
            groupIDExporter.Export(writer, railData.GetModelLODGroupID());
            writer.Write(railData.railIndex);
            writer.Write(railData.segmentIndex);
        }

        void SaveModelLODGroupManager(BinaryWriter writer, ModelLODGroupManager groupManager, IDExporter idExporter, IDExporter groupIDExporter)
        {
            int nGroups = groupManager.groups.Count;
            writer.Write(nGroups);

            for (int i = 0; i < nGroups; ++i)
            {
                var group = groupManager.groups[i];
                groupIDExporter.Export(writer, group.id);
                writer.Write(group.combineModels);
                idExporter.Export(writer, group.leaderObjectID);
                writer.Write(group.lod);
            }
        }

        short GetPrefabPathIndex(ModelTemplate modelTemplate)
        {
            short index;
            bool found = mModelTemplateIDToPrefabPathIndex.TryGetValue(modelTemplate.id, out index);
            if (!found)
            {
                mPrefabPathStringTable.Add(mPathMapper.Map(modelTemplate.GetLODPrefabPath(0)));
                index = (short)(mPrefabPathStringTable.Count - 1);
                mModelTemplateIDToPrefabPathIndex[modelTemplate.id] = index;
            }
            return index;
        }

        void SaveSetting(BinaryWriter writer)
        {
            mPathMapperPosition = writer.BaseStream.Position;
            long pathMapperOffsetPlaceholder = 0;
            writer.Write(pathMapperOffsetPlaceholder);
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
                writer.Write((int)0);
                Utils.WriteString(writer, "");
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                    writer.Write((int)c.flag);
                    Utils.WriteString(writer, c.name);
                }
            }
        }

        void SavePathMapper(BinaryWriter writer)
        {
            long position = writer.BaseStream.Position;
            //跳转到offset placeholder的地方,写下PathMapper数据的地址
            writer.BaseStream.Position = mPathMapperPosition;
            writer.Write(position);
            writer.BaseStream.Position = position;

            int n = mPathMapper.pathToGuid.Count;
            writer.Write(n);

            foreach (var p in mPathMapper.pathToGuid)
            {
                Utils.WriteString(writer, p.Key);
                Utils.WriteString(writer, p.Value);
            }

            //save string table
            int pathCount = mPrefabPathStringTable.Count;
            writer.Write(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                Utils.WriteString(writer, mPrefabPathStringTable[i]);
            }
        }

        void PrepareSaving()
        {
            mPathMapper = new PathMapper();
            mModelTemplateIDToPrefabPathIndex = new Dictionary<int, short>();
            mPrefabPathStringTable = new List<string>();
        }

        PathMapper mPathMapper;
        long mPathMapperPosition;
        Dictionary<int, short> mModelTemplateIDToPrefabPathIndex;
        List<string> mPrefabPathStringTable;
    }
}

#endif