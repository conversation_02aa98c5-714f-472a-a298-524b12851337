﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class GroundTileMaker : MonoBehaviour
    {
        public void CreatePrefabs(bool forceCreateAll)
        {
            var maskTextureSetting = mLODMaterialSettings[0].maskTextureSetting;
            if (maskTextureSetting.Count == 1 && mPackTextures && mAtlasShader == null)
            {
                EditorUtility.DisplayDialog("Error", "Atlas Shader is Empty!", "OK");
                return;
            }

            if (!Directory.Exists(mRuntimeAssetOutputFolder))
            {
                Directory.CreateDirectory(mRuntimeAssetOutputFolder);
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            RemoveUnusedPrefabs();

            int nLODs = lodCount;
            Mesh sharedMesh = null;
            for (int lod = 0; lod < nLODs; ++lod)
            {
                CreateAssetsForLOD(lod, forceCreateAll, ref sharedMesh);
            }

            CreateVariationList();
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        //创建贴图打包信息
        List<TexturePackerItem> CreateTexturePackItems(int lod)
        {
            List<TexturePackerItem> packItems = new List<TexturePackerItem>();

            int nPrefabs = mTiles.Length;
            for (int i = 0; i < nPrefabs; ++i)
            {
                var variations = mTiles[i].variations;
                for (int v = 0; v < variations.Count; ++v)
                {
                    var variation = variations[v];
                    var tileLOD = variation.GetLOD(lod);
                    var gameObject = tileLOD.gameObject;

                    //create textures
                    packItems.Add(new TexturePackerItem(gameObject.name, tileLOD.maskTextures[0].texture, false));
                }
            }
            return packItems;
        }

        string GetTextureAtlasPath(int lod, int atlasIndex, string shaderPropertyName)
        {
            string texturePath = $"{mRuntimeAssetOutputFolder}/lod{lod}_atlas_{atlasIndex}_{shaderPropertyName}.tga";
            return texturePath;
        }

        //贴图集打包成功
        bool OnPackTextureSucceed(List<TexturePacker.PackInfo> packInfos, List<TexturePackerItem> items)
        {
            //创建贴图
            mPackInfos = packInfos;
            for (int k = 0; k < packInfos.Count; ++k)
            {
                var maskTextureSetting = mLODMaterialSettings[mPackingLOD].maskTextureSetting[0];
                string texturePath = GetTextureAtlasPath(mPackingLOD, k, maskTextureSetting.shaderPropertyName);
                byte[] bytes = packInfos[k].packedTexture.EncodeToTGA();
                System.IO.File.WriteAllBytes(texturePath, bytes);
                AssetDatabase.Refresh();
                var newTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
                newTexture.wrapMode = TextureWrapMode.Clamp;
                EditorUtility.SetDirty(newTexture);
                AssetDatabase.SaveAssets();
            }
            return false;
        }

        int mPackingLOD;
        List<TexturePacker.PackInfo> mPackInfos;

        /* 关于资源生成,有几个选择
         * 1.使用多个mask texture,则最多只生成相同的mesh,不做其他优化
         * 2.只使用一个mask texture,不打包贴图集,也不生成相同的mesh,完全依赖unity的dynamic batch
         * 3.只使用一个mask texture,并且用gpu instancing渲染,就要求所有地表使用同一个mesh.这里又有2种情况.
         *   1.不打贴图集,但是unity不能针对不同texture做instancing,所以没法使用instancing
         *   2.打包贴图集,但是又要保证同一个uv,这样就需要将每个mesh的uv信息单独保存到另外文件中,在运行时每个地表单独设置Material Property Block
         */
        void CreateAssetsForLOD(int lod, bool forceCreateAll, ref Mesh sharedMesh)
        {
            mPackingLOD = lod;
            var maskTextureSetting = mLODMaterialSettings[lod].maskTextureSetting;
            int nTextures = maskTextureSetting.Count;
            TexturePacker texturePacker = null;
            List<TexturePackerItem> texturePackItems = null;
            bool canPackTexture = (nTextures == 1 && mPackTextures);
            if (canPackTexture)
            {
                texturePackItems = CreateTexturePackItems(lod);
                texturePacker = new TexturePacker();
                texturePacker.Pack(TexturePackerStrategyType.EqualRects, OnPackTextureSucceed, texturePackItems);
            }

            //如果使用atlas,所有prefab都使用同一个material
            Material atlasMaterial = null;
            int nPrefabs = mTiles.Length;
            for (int i = 0; i < nPrefabs; ++i)
            {
                if (!mTiles[i].generateAssets)
                {
                    DeleteAssets(i);
                    continue;
                }

                var variations = mTiles[i].variations;
                for (int v = 0; v < variations.Count; ++v)
                {
                    var variation = variations[v];

                    var tileLOD = variation.GetLOD(lod);
                    if (tileLOD.assetsDirty || forceCreateAll || canPackTexture)
                    {
                        tileLOD.assetsDirty = false;
                        var gameObject = tileLOD.gameObject;
                        string prefix = mRuntimeAssetOutputFolder + "/" + gameObject.name;
                        string newPrefabPath = prefix + ".prefab";

                        bool createGameObject = false;
                        if (!File.Exists(newPrefabPath))
                        {
                            createGameObject = true;
                        }

                        GameObject tileObj = null;
                        if (createGameObject)
                        {
                            tileObj = GameObject.Instantiate<GameObject>(gameObject);
                            tileObj.name = gameObject.name;
                        }
                        else
                        {
                            tileObj = AssetDatabase.LoadAssetAtPath<GameObject>(newPrefabPath);
                        }
                        tileObj.SetActive(true);

                        //create mesh
                        Mesh mesh = null;
                        if (mShareMesh && texturePacker == null)
                        {
                            if (sharedMesh == null)
                            {
                                sharedMesh = GameObject.Instantiate<Mesh>(tileObj.GetComponent<MeshFilter>().sharedMesh);
                                string meshPath = prefix + ".asset";
                                AssetDatabase.CreateAsset(sharedMesh, meshPath);
                            }
                            mesh = sharedMesh;
                        }
                        else
                        {
                            var filter = tileObj.GetComponent<MeshFilter>();
                            mesh = GetMesh(filter.sharedMesh.name);
                            if (texturePacker != null)
                            {
                                //给每个texture atlas指定uv通道
                                int textureAtlasIndex;
                                int indexInAtlas;
                                var uvs = texturePacker.GetUV(tileObj.name, out textureAtlasIndex, out indexInAtlas);
                                switch (maskTextureSetting[0].uvChannel)
                                {
                                    case AtlasUVChannel.UV0:
                                        mesh.uv = uvs;
                                        break;
                                    case AtlasUVChannel.UV1:
                                        mesh.uv2 = uvs;
                                        break;
                                    case AtlasUVChannel.UV2:
                                        mesh.uv3 = uvs;
                                        break;
                                    case AtlasUVChannel.UV3:
                                        mesh.uv4 = uvs;
                                        break;
                                    case AtlasUVChannel.UV4:
                                        mesh.uv5 = uvs;
                                        break;
                                    case AtlasUVChannel.UV5:
                                        mesh.uv6 = uvs;
                                        break;
                                    case AtlasUVChannel.UV6:
                                        mesh.uv7 = uvs;
                                        break;
                                    case AtlasUVChannel.UV7:
                                        mesh.uv8 = uvs;
                                        break;
                                    default:
                                        Debug.Assert(false);
                                        break;
                                }
                            }

                            mesh.UploadMeshData(false);
                            string meshPath = prefix + ".asset";
                            AssetDatabase.CreateAsset(mesh, meshPath);
                        }

                        tileObj.GetComponent<MeshFilter>().sharedMesh = mesh;

                        var meshRenderer = tileObj.GetComponent<MeshRenderer>();
                        bool useAtlas = false;
                        if (texturePacker != null && maskTextureSetting[0].uvChannel != AtlasUVChannel.UV0)
                        {
                            useAtlas = true;
                        }

                        Material material = null;
                        string materialPath = null;
                        bool createAtlasMaterial = false;
                        var originalMaterial = AssetDatabase.LoadAssetAtPath<Material>(mLODMaterialSettings[lod].materialPath);
                        if (useAtlas)
                        {
                            if (atlasMaterial == null)
                            {
                                createAtlasMaterial = true;
                                atlasMaterial = Object.Instantiate<Material>(originalMaterial);
                                var renderQueue = atlasMaterial.renderQueue;
                                atlasMaterial.shader = mAtlasShader;
                                atlasMaterial.renderQueue = renderQueue;
                            }
                            //change shader
                            materialPath = mRuntimeAssetOutputFolder + "/atlas.mat";
                            material = atlasMaterial;
                        }
                        else
                        {
                            materialPath = prefix + ".mat";
                            material = Object.Instantiate<Material>(originalMaterial);
                        }

                        //create textures
                        for (int k = 0; k < nTextures; ++k)
                        {
                            if (texturePackItems == null)
                            {
                                string propName = tileLOD.maskTextures[k].shaderPropertyName;
                                string texturePath = prefix + "_" + propName + ".tga";
                                byte[] bytes = tileLOD.maskTextures[k].texture.EncodeToTGA();
                                System.IO.File.WriteAllBytes(texturePath, bytes);
                                AssetDatabase.Refresh();
                                var newTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
                                newTexture.wrapMode = TextureWrapMode.Clamp;
                                EditorUtility.SetDirty(newTexture);
                                AssetDatabase.SaveAssets();
                                material.SetTexture(tileLOD.maskTextures[k].shaderPropertyName, newTexture);
                            }
                            else
                            {
                                int atlasIndex = texturePacker.GetAtlasTextureIndex(tileObj.name, texturePackItems, mPackInfos);
                                string shaderPropertyName = tileLOD.maskTextures[k].shaderPropertyName;
                                string atlasTexturePath = GetTextureAtlasPath(lod, atlasIndex, shaderPropertyName);
                                Texture2D atlasTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(atlasTexturePath);
                                Debug.Assert(atlasTexture != null);
                                material.SetTexture(shaderPropertyName, atlasTexture);
                            }
                        }

                        //create material

                        //change shader
                        if (useAtlas && createAtlasMaterial)
                        {
                            AssetDatabase.CreateAsset(atlasMaterial, materialPath);
                        }
                        else if (!useAtlas)
                        {
                            AssetDatabase.CreateAsset(material, materialPath);
                        }
                        var newMaterial = AssetDatabase.LoadAssetAtPath<Material>(materialPath);

                        if (mExtraSubmeshMaterial != null)
                        {
                            if (mUseExtraSubmeshMaterialAsFirstMaterial)
                            {
                                meshRenderer.sharedMaterials = new Material[2] { mExtraSubmeshMaterial, material };
                            }
                            else
                            {
                                meshRenderer.sharedMaterials = new Material[2] { material, mExtraSubmeshMaterial };
                            }
                        }
                        else
                        {
                            meshRenderer.sharedMaterials = new Material[] { material };
                        }

                        //create prefab
                        if (createGameObject)
                        {
                            PrefabUtility.SaveAsPrefabAsset(tileObj, newPrefabPath);
                            GameObject.DestroyImmediate(tileObj);
                        }
                        else
                        {
                            bool suc;
                            EditorUtility.SetDirty(tileObj);
                            PrefabUtility.SavePrefabAsset(tileObj, out suc);
                            Debug.Assert(suc);
                            AssetDatabase.SaveAssets();
                        }
                    }
                }
            }
            mPackingLOD = -1;
        }

        //删除导出目录里未使用的prefab
        void RemoveUnusedPrefabs()
        {
            HashSet<string> usedPrefabPaths = new HashSet<string>();
            int nPrefabs = mTiles.Length;
            int nLODs = lodCount;

            for (int i = 0; i < nPrefabs; ++i)
            {
                var variations = mTiles[i].variations;
                for (int v = 0; v < variations.Count; ++v)
                {
                    var variation = variations[v];
                    for (int lod = 0; lod < nLODs; ++lod)
                    {
                        var gameObject = variation.GetLOD(lod).gameObject;
                        string prefix = mRuntimeAssetOutputFolder + "/" + gameObject.name;
                        string prefabPath = prefix + ".prefab";
                        usedPrefabPaths.Add(prefabPath);
                    }
                }
            }

            var enumerator = Directory.EnumerateFiles(mRuntimeAssetOutputFolder, "*.prefab", SearchOption.TopDirectoryOnly);
            foreach (var filePath in enumerator)
            {
                var validPath = filePath.Replace('\\', '/');
                validPath = Utils.ConvertToUnityAssetsPath(validPath);
                int objLOD = Utils.GetPrefabNameLOD(validPath);
                if (objLOD < nLODs && objLOD >= 0)
                {
                    if (!usedPrefabPaths.Contains(validPath))
                    {
                        //delete prefab
                        AssetDatabase.DeleteAsset(validPath);

                        string prefix = Utils.RemoveExtension(validPath);

                        //delete mesh
                        string meshPath = prefix + ".asset";
                        AssetDatabase.DeleteAsset(meshPath);

                        //delete material
                        var materialPath = prefix + ".mat";
                        AssetDatabase.DeleteAsset(materialPath);

                        //delete textures
                        for (int lod = 0; lod < nLODs; ++lod)
                        {
                            var maskTextureSetting = mLODMaterialSettings[lod].maskTextureSetting;
                            int nTextures = maskTextureSetting.Count;
                            for (int k = 0; k < nTextures; ++k)
                            {
                                string propName = maskTextureSetting[k].shaderPropertyName;
                                string texturePath = prefix + "_" + propName + ".tga";
                                AssetDatabase.DeleteAsset(texturePath);
                            }
                        }
                    }
                }
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        void CreateVariationList()
        {
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.GroundTileVariationVersion);

            for (int i = 0; i < mTiles.Length; ++i)
            {
                int variationCount = mTiles[i].variations.Count;
                writer.Write(variationCount);
                for (int v = 0; v < variationCount; ++v)
                {
                    //only save prefab name of lod 0
                    var variation = mTiles[i].variations[v];
                    string variationPrefabName = variation.GetLOD(0).gameObject.name;
                    Utils.WriteString(writer, variationPrefabName);
                }
            }

            var data = stream.ToArray();
            string filePath = mRuntimeAssetOutputFolder + "/" + MapCoreDef.GROUND_TILE_VARIATION_FILE_NAME;
            File.WriteAllBytes(filePath, data);
            writer.Close();
        }

        void DeleteAssets(int index)
        {
            List<string> paths = new List<string>();
            var enumerator = Directory.EnumerateFiles(mRuntimeAssetOutputFolder, "*.*", SearchOption.TopDirectoryOnly);
            foreach (var filePath in enumerator)
            {
                var validPath = filePath.Replace('\\', '/');
                validPath = Utils.ConvertToUnityAssetsPath(validPath);
                if (validPath.IndexOf($"_{index.ToString("D2")}_") > 0)
                {
                    paths.Add(validPath);
                }
            }

            for (int i = 0; i < paths.Count; ++i)
            {
                FileUtil.DeleteFileOrDirectory(paths[i]);
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }
    }
}

#endif