﻿ 



 
 



using System.Collections;
using UnityEngine;

namespace TFW.Map
{
    //类似maya的相机缩放,只在pc上使用
    public class CameraMayaZoom : CameraAction
    {
        public CameraMayaZoom(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
        }

        public override void OnFinishImpl()
        {
        }

        public override Vector3 GetTargetPosition()
        {
            if (!MapCameraMgr.enableCameraXAngleChange)
            {
                MapCameraMgr.UpdateRotateCenter();
            }
            return mCameraTargetPos;
        }


#if UNITY_EDITOR || UNITY_STANDALONE
        // runtime values to avoid redeclaration
        Vector3 prevMousePosition;
        float scrollRate = 0;
#endif
        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
#if UNITY_EDITOR || UNITY_STANDALONE
            if (!mOn)
            {
                return;
            }
            // Maya & Unity-like smooth zoom with keyboard and RMB.
            if (Input.GetKey(KeyCode.LeftAlt) && Input.GetMouseButtonDown(1))
            {
                prevMousePosition = Input.mousePosition;
            }
            else if (Input.GetKey(KeyCode.LeftAlt) && Input.GetMouseButton(1))
            {
                var camera = Map.currentMap.camera.firstCamera;
                int centerX = camera.pixelWidth / 2;
                int centerY = camera.pixelHeight / 2;

                Transform trCam = camera.transform;

                Vector3 mousePosition = Input.mousePosition;

                // calculation from mouse movement
                scrollRate = ((prevMousePosition.x - mousePosition.x) + (prevMousePosition.y - mousePosition.y)) * 0.5f;
                // proportionnal to camera Y position : far is fast, near is slow
                scrollRate *= currentCameraPos.y * 0.01f;
                // assignement to camera
                mCameraTargetPos = trCam.position + trCam.forward * scrollRate;

                prevMousePosition = mousePosition;

                enabled = true;
            }
            else if (Input.GetKey(KeyCode.LeftAlt) && Input.GetMouseButtonUp(1))
            {
                isFinished = true;
            }
            else
            {
                isFinished = true;
            }
#endif
        }

        public bool on
        {
            get { return mOn; }
            set
            {
                mOn = value;
                if (!value)
                {
                    isFinished = true;
                }
            }
        }

        Vector3 mCameraTargetPos = Vector3.zero;
        bool mOn = true;
    }
}