﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    [Black]
    public class SpriteTileView : TileView {
        public SpriteTileView(MapLayerView layerView, GameObject gameObject)
            : base(layerView) {
            mGameObject = gameObject;
        }
    }

    [Black]
    public class DummySpriteTileView : SpriteTileView {
        public DummySpriteTileView(MapLayerView layerView) : base(layerView, null) {
        }
    }
}

#endif