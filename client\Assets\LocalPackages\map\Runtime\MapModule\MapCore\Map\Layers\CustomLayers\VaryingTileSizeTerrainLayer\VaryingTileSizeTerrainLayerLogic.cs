﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public enum VaryingTileSizeTerrainOperationType
    {
        Create,
        Remove,
        Select,
    }

    [ExecuteInEditMode]
    public class VaryingTileSizeTerrainLayerLogic : MapLayerLogic
    {
        protected override MoveAxis moveAxis { get { return MoveAxis.Y; } }

        void Start()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
                prefabManager.addPrefabEvent += OnAddPrefab;
                prefabManager.setPrefabEvent += OnSetPrefab;
                prefabManager.removePrefabEvent += OnRemovePrefab;
            }
        }

        void OnDestroy()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
                prefabManager.addPrefabEvent -= OnAddPrefab;
                prefabManager.setPrefabEvent -= OnSetPrefab;
                prefabManager.removePrefabEvent -= OnRemovePrefab;
            }
        }

        public VaryingTileSizeTerrainLayerData layerData
        {
            get
            {
                return Map.currentMap.FindObject(layerID) as VaryingTileSizeTerrainLayerData;
            }
        }

        public VaryingTileSizeTerrainLayer layer
        {
            get
            {
                return Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND) as VaryingTileSizeTerrainLayer;
            }
        }

        void OnSetPrefab(int groupIndex, int index, GameObject prefab)
        {
            var prefabPath = AssetDatabase.GetAssetPath(prefab);
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var terrainPrefabManager = Map.currentMap.data.varyingTileSizeTerrainPrefabManager;
            terrainPrefabManager.SetGroupPrefabByID(group.groupID, index, prefabPath);
        }

        void OnAddPrefab(int groupIndex, GameObject prefab)
        {
            var prefabPath = AssetDatabase.GetAssetPath(prefab);
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var idx = group.count - 1;
            var terrainPrefabManager = Map.currentMap.data.varyingTileSizeTerrainPrefabManager;
            terrainPrefabManager.SetGroupPrefabByID(group.groupID, idx, prefabPath);
        }

        void OnRemovePrefab(int groupIndex, int index, string prefabPath)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var terrainPrefabManager = Map.currentMap.data.varyingTileSizeTerrainPrefabManager;
            terrainPrefabManager.SetGroupPrefabByID(group.groupID, index, "");
        }

        public VaryingTileSizeTerrainOperationType operationType { set; get; }
        public bool showRenderTextureOption { set; get; } = false;        
    }
}


#endif