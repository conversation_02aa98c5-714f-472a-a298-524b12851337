﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class RiverEditor
    {
        public RiverEditor(PolygonRiverLayer layer, PolygonRiverLayerLogic logic, System.Action OnRepaintGUI, System.Action OnRepaintScene)
        {
            mLayer = layer;
            mLogic = logic;
            mOnRepaintGUI = OnRepaintGUI;
            mOnRepaintScene = OnRepaintScene;

            PolygonRiverLayerEventHandlers handlers = new PolygonRiverLayerEventHandlers();
            handlers.onDeleteCollision = OnDeleteRiver;
            mLayer.SetEventHandlers(handlers);
        }

        public void OnDestroy()
        {
            for (int i = 0; i < mTools.Count; ++i)
            {
                mTools[i].OnDestroy();
            }
            mTools.Clear();

            SetSelection(null, 0);
            mLayer.SetEventHandlers(null);
        }

        public void AddTool(RiverEditorTool tool)
        {
            mTools.Add(tool);
        }

        RiverEditorTool GetTool(RiverEditorToolType type)
        {
            for (int i = 0; i < mTools.Count; ++i)
            {
                if (mTools[i].type == type)
                {
                    return mTools[i];
                }
            }
            return null;
        }

        public RiverEditorTool GetActiveTool()
        {
            return mActiveTool;
        }

        public void SetActiveTool(RiverEditorToolType type)
        {
            var tool = GetTool(type);
            if (mActiveTool != null)
            {
                mActiveTool.OnDisabled();
            }
            mActiveTool = tool;
            if (mActiveTool != null)
            {
                mActiveTool.OnEnabled();
            }
            SetSelection(null, -1);
        }

        public void Update(Event e)
        {
            if (mActiveTool != null)
            {
                mActiveTool.Update(e);
            }
        }

        public void DrawScene()
        {
            if (mActiveTool != null)
            {
                mActiveTool.DrawScene();
            }
        }

        public void DrawGUI()
        {
            if (mActiveTool != null)
            {
                mActiveTool.DrawGUI();
            }

            if (mSelectedCollisionDataID != 0)
            {
                var river = Map.currentMap.FindObject(mSelectedCollisionDataID) as PolygonRiverData;
                EditorGUILayout.IntField(new GUIContent("River Splitter Count", "所选河流Splitter的数量"), river.splitters.Count);
                EditorGUILayout.IntField(new GUIContent("River Section Count", "所选河流被分成了多少段"), river.sections.Count);
                EditorGUILayout.IntField(new GUIContent("River Texture Size", "所选河流的mask贴图分辨率大小"), river.textureSize);
                var mtl = AssetDatabase.LoadAssetAtPath<Material>(river.materialPath);
                EditorGUILayout.ObjectField(new GUIContent("River Material", "河流的材质"), mtl, typeof(Material), false);
                river.generateRiverMaterial = EditorGUILayout.Toggle(new GUIContent("Generate Material When Export Assets", "是否在生成河流资源时生成材质,如果不勾选,则直接使用设置的材质,但就不能使用河流mask贴图绘制功能"), river.generateRiverMaterial);
                float newHeight = EditorGUILayout.FloatField(new GUIContent("River Height", "海平面的高度"), river.height);
                if (!Mathf.Approximately(newHeight, river.height))
                {
                    mLayer.SetRiverHeight(mSelectedCollisionDataID, newHeight);
                }
            }
            else
            {
                mLayer.defaultRiverMaterial = EditorGUILayout.ObjectField(new GUIContent("River Material", "河流的材质"), mLayer.defaultRiverMaterial, typeof(Material), false) as Material;
            }
        }

        public void DrawMenu()
        {
            if (mActiveTool != null)
            {
                mActiveTool.DrawMenu();
            }
        }

        public void SetSelection(PolygonRiverData data, int vertexIndex)
        {
            if (mSelectedCollisionDataID != 0)
            {
                var d = Map.currentMap.FindObject(mSelectedCollisionDataID) as PolygonRiverData;
                if (d != null)
                {
                    mLayer.SetSelected(d.id, false);
                }
                mSelectedCollisionDataID = 0;
                mSelectedVertexIndex = -1;
            }

            if (data != null)
            {
                mSelectedCollisionDataID = data.GetEntityID();
                mLayer.SetSelected(data.id, true);
            }
            else
            {
                mSelectedCollisionDataID = 0;
            }
            mSelectedVertexIndex = vertexIndex;
            RepaintGUI();
        }

        public void Pick(Vector3 pos)
        {
            System.Func<IMapObjectData, bool> func = (IMapObjectData data) =>
            {
                var collisionData = data as PolygonRiverData;

                int hitVertex = -1;
                var vertices = collisionData.GetOutlineVertices(mLayer.displayType);
                for (int i = 0; i < vertices.Count; ++i)
                {
                    if (Utils.IsHitRectangle(pos, vertices[i], collisionData.displayRadius))
                    {
                        hitVertex = i;
                        break;
                    }
                }

                if (hitVertex >= 0)
                {
                    SetSelection(collisionData, hitVertex);
                }
                else
                {
                    SetSelection(null, -1);
                }

                if (mSelectedCollisionDataID != 0)
                {
                    RepaintScene();
                    return true;
                }

                return false;
            };

            mLayer.Traverse(func);
        }

        public void RepaintScene()
        {
            mOnRepaintScene();
        }

        public void RepaintGUI()
        {
            mOnRepaintGUI();
        }

        public void ClearVertexSelection()
        {
            mSelectedVertexIndex = -1;
        }

        void OnDeleteRiver(int dataID)
        {
            if (dataID == selectedObjectID)
            {
                SetSelection(null, 0);
                RepaintGUI();

                layer.ShowOutline(layer.displayType);
            }
        }

        public void DeleteRiver()
        {
            if (selectedObjectID != 0)
            {
                var act = new ActionRemovePolygonRiver(mLayer.id, selectedObjectID);
                ActionManager.instance.PushAction(act);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a river first!", "OK");
            }
        }

        public void SetVertexPosition()
        {
            if (selectedObjectID != 0 && selectedVertexIndex >= 0)
            {
                var river = mLayer.GetRiver(selectedObjectID) as PolygonRiverData;
                var pos = river.GetVertexPos(PrefabOutlineType.NavMeshObstacle, selectedVertexIndex);
                var dlg = EditorUtils.CreateInputDialog("Set Vertex Position");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("X", "", pos.x.ToString()),
                    new InputDialog.StringItem("Z", "", pos.z.ToString()),
                };
                dlg.Show(items, OnSetVertexPosition);
            }
        }

        bool OnSetVertexPosition(List<InputDialog.Item> parameters)
        {
            string xText = (parameters[0] as InputDialog.StringItem).text;
            string zText = (parameters[1] as InputDialog.StringItem).text;

            float x, z;
            bool suc = float.TryParse(xText, out x);
            suc &= float.TryParse(zText, out z);
            if (!suc)
            {
                return false;
            }

            if (selectedObjectID == 0 || selectedVertexIndex < 0)
            {
                return false;
            }

            var river = mLayer.GetRiver(selectedObjectID) as PolygonRiverData;
            var oldPos = river.GetVertexPos(PrefabOutlineType.NavMeshObstacle, selectedVertexIndex);
            var newPos = new Vector3(x, 0, z);
            if (oldPos != newPos)
            {
                var act = new ActionMovePolygonRiverVertex(mLayer.id, selectedObjectID, selectedVertexIndex, oldPos, PrefabOutlineType.NavMeshObstacle);
                act.SetEndPosition(newPos);
                ActionManager.instance.PushAction(act);
            }
            return true;
        }

        public void CopyRiver()
        {
            if (selectedObjectID != 0)
            {
                var act = new ActionCopyPolygonRiver(mLayer.id, selectedObjectID, Vector3.right * 20);
                ActionManager.instance.PushAction(act);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a river first!", "OK");
            }
        }

        public void CheckCollideWithMapObstacles()
        {
            if (selectedObjectID != 0)
            {
                var mapObstacleManager = Map.currentMap.data.localObstacleManager;
                if (mapObstacleManager != null)
                {
                    var collisionData = Map.currentMap.FindObject(selectedObjectID) as PolygonRiverData;
                    bool intersectedWithObstacles = false;
                    if (collisionData.IsConvex(layer.displayType))
                    {
                        intersectedWithObstacles = mapObstacleManager.IsIntersectedWithObstacles(collisionData.GetOutlineVertices(layer.displayType), collisionData.GetBounds());
                    }
                    layer.SetIntersectedWithObstacles(selectedObjectID, intersectedWithObstacles);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a river first!", "OK");
            }
        }

        public PolygonRiverLayer layer { get { return mLayer; } }
        public PolygonRiverLayerLogic logic { get { return mLogic; } }
        public int selectedObjectID { get { return mSelectedCollisionDataID; } }
        public int selectedVertexIndex { get { return mSelectedVertexIndex; } }

        PolygonRiverLayer mLayer;
        PolygonRiverLayerLogic mLogic;
        int mSelectedCollisionDataID;
        int mSelectedVertexIndex = -1;
        System.Action mOnRepaintGUI;
        System.Action mOnRepaintScene;
        List<RiverEditorTool> mTools = new List<RiverEditorTool>();
        RiverEditorTool mActiveTool;
    }
}

#endif