﻿using System.Security.Cryptography;

namespace GameServerConnection
{
    internal class AesDecryptor
    {
        AesManaged m_AesManager;
        ICryptoTransform m_AesDecryptor;
        public AesDecryptor(byte[] key, byte[] iv, bool isUseKeyV1)
        {
            m_AesManager = new AesManaged { Mode = CipherMode.ECB, Padding = PaddingMode.None };
            m_AesDecryptor = new CounterModeCryptoTransform(m_AesManager, key, iv, isUseKeyV1);
        }

        public byte[] Decrypt(byte[] data, int offset = 0, int len = -1)
        {
            len = len < 0 ? data.Length : len;
            return m_AesDecryptor.TransformFinalBlock(data, offset, len);
        }
    }
}