﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public abstract class RiverEditorTool
    {
        public RiverEditorTool(RiverEditor editor)
        {
            mEditor = editor;
        }

        public abstract void OnDestroy();
        public abstract void OnEnabled();
        public abstract void OnDisabled();

        public abstract void Update(Event e);
        public abstract void DrawScene();
        protected abstract void DrawGUIImpl();
        public void DrawGUI()
        {
            DrawHeader();
            DrawGUIImpl();
            DrawFooter();
        }

        void DrawHeader()
        {
        }

        void DrawFooter()
        {
            var layer = mEditor.layer;
            layer.layerData.riverMaskTextureSaveFolderPath = EditorUtils.FolderSelectionField("River Mask Texture Save Folder", layer.layerData.riverMaskTextureSaveFolderPath);
            int newTextureSize = EditorGUILayout.IntField(new GUIContent("Baked Texture Size", "烘培的贴图大小, 可在高lod时将河流烘培成一张贴图显示"), layer.bakedTextureSize);
            newTextureSize = Mathf.NextPowerOfTwo(newTextureSize);
            newTextureSize = Mathf.Clamp(newTextureSize, 1, 2048);
            layer.bakedTextureSize = newTextureSize;

            if (mEditor.selectedObjectID != 0)
            {
                if (GUILayout.Button(new GUIContent("Select River Mask Texture", "在Asset界面中选中所选河流的mask贴图")))
                {
                    string texturePath = layer.GetRiverMaskTextureFilePath(mEditor.selectedObjectID);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
                    Selection.activeObject = texture;
                }
            }

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button(new GUIContent("Refresh Mask Texture", "外部工具修改mask texture后点刷新")))
            {
                layer.RefreshMaskTexture();
            }
            if (GUILayout.Button(new GUIContent("Import Mask Texture", "导入一张河流的mask贴图")))
            {
                ImportMaskTexture();
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button(new GUIContent("Export River Assets", "生成河流的资源")))
            {
                layer.GenerateRiverAssetsForGame();
            }
            if (GUILayout.Button(new GUIContent("Select River Assets Folder", "选择河流资源文件夹")))
            {
                string riverAssetPath = MapCoreDef.GetRiverAssetsFolderPath(SLGMakerEditor.instance.exportFolder);
                EditorUtils.SelectFolder(riverAssetPath);
            }
            EditorGUILayout.EndHorizontal();
        }

        void ImportMaskTexture()
        {
            if (mEditor.selectedObjectID != 0)
            {
                var river = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;
                if (river.sections.Count != 1)
                {
                    EditorUtility.DisplayDialog("Error", "Only support one section river!", "OK");
                    return;
                }
                string filePath = EditorUtility.OpenFilePanelWithFilters("Mask Texture", "", new string[] { "tga", "tga" });
                if (!string.IsNullOrEmpty(filePath))
                {
                    var bytes = File.ReadAllBytes(filePath);
                    if (bytes != null)
                    {
                        var texture = new Texture2D(1, 1);
                        bool suc = texture.LoadImage(bytes);
                        if (suc)
                        {
                            var maskTextureSize = river.textureSize;
                            if (texture.width != maskTextureSize || texture.height != maskTextureSize)
                            {
                                EditorUtility.DisplayDialog("Error", "Imported texture size must be same as mask texture size!", "OK");
                                return;
                            }


                            var pixels = texture.GetPixels();
                            var act = new ActionUpdateRiverTextureData(river.id, 0, pixels);
                            ActionManager.instance.PushAction(act);
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", "Load texture failed!", "OK");
                        }
                    }
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a river first!", "OK");
            }
        }

        public virtual void DrawMenu()
        {
            GenericMenu menu = new GenericMenu();
            menu.AddItem(new GUIContent("Copy River"), false, mEditor.CopyRiver);
            menu.AddItem(new GUIContent("Delete River"), false, mEditor.DeleteRiver);
            menu.AddItem(new GUIContent("Set Vertex Position"), false, mEditor.SetVertexPosition);
            DrawSubMenu(menu);
            menu.ShowAsContext();
        }

        protected virtual void DrawSubMenu(GenericMenu menu) { }

        public RiverEditor editor { get { return mEditor; } }
        public abstract RiverEditorToolType type { get; }

        protected RiverEditor mEditor;
    }
}


#endif