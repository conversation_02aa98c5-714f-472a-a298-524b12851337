﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public interface IDynamicObjectData
    {
        //游戏中Entity的id,动态实体的id参考MapCoreDef的MAP_ENTITY_START_ID和MAP_ENTITY_END_ID的区间
        int GetEntityID();
        int GetModelTemplateID();
        int GetGridIndex();
        void SetGridIndex(int index);
        //Entity的prefab路径
        string GetAssetPath(int lod = 0);
        //返回bounding box
        Rect GetBounds();
        //Entity的位置
        Vector3 GetPosition();
        //Entity的缩放
        Vector3 GetScale();
        //Entity的旋转
        Quaternion GetRotation();

        void SetPosition(Vector3 pos);
        void SetScale(Vector3 scale);
        void SetRotation(Quaternion rotation);

        bool IsObjActive();
        bool SetObjActive(bool active);
    }
}
