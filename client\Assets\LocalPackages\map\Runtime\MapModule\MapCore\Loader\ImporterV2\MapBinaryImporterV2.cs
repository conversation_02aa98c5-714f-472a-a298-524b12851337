﻿ 



 
 



/*
 * created by wzw at 2020.3.11
 */

using System.IO;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    enum MapDataSectionType
    {
        BlendTerrainLayer,
        CameraCollider,
        Camera,
        CircleBorderLayer,
        GridModelLayer2,
        GlobalObstacles,
        GridModelLayer,
        GridRegionSetting,
        LocalObstacles,
        LODLayer,
        ModelLayer,
        ModelTemplate,
        PolygonRiverLayer,
        RailwayLayer,
        Setting,
        TerrainPrefabManager,
        SplitFogLayer,
        SimpleBlendTerrainLayer,
        CityTerritoryLayer,
        TileBlockTerrainLayer,
        PluginLayerList,
        RegionLayer,
        RegionColorLayer,
        DecorationBorderLayer,
        //取代CityTerritoryLayer的数据
        NewCityTerritoryLayer,
        VaryingTileSizeTerrainLayer,
        VaryingTileSizeTerrainPrefabManager,
        //新的section放在最后面

        Total,
    }

    public partial class MapBinaryImporterV2 : MapBinaryImporter
    {
        public MapBinaryImporterV2(int majorVersion) : base(majorVersion)
        {
        }

        public override config.SLGMakerData Load(int minorVersion, string mapName, BinaryReader reader)
        {
            mEditorData = new config.SLGMakerData(0, 0, 0, false, 0, 0, false, MapModule.defaultGroundTileSize, MapModule.defaultFrontTileSize, new config.BackgroundSetting(), new Version(version, minorVersion), new Bounds());

            LoadHeader(reader);

            LoadSetting(reader);
            LoadModelTemplates(reader);
            LoadTerrainPrefabManager(reader);
            LoadVaryingTileSizeTerrainPrefabManager(reader);
            LoadCamera(reader);
            LoadMapLayers(reader);
            LoadMapObstacles(reader);
            LoadGridRegionSetting(reader);
            return mEditorData;
        }

        void LoadHeader(BinaryReader reader)
        {
            //读取数据段的起始偏移
            int nSections = reader.ReadInt32();
            mEditorData.sectionDataOffset = new long[nSections];
            for (int i = 0; i < nSections; ++i)
            {
                mEditorData.sectionDataOffset[i] = reader.ReadInt64();
            }
        }

        long GetSectionDataStartPosition(MapDataSectionType type)
        {
            int idx = (int)type;
            if (idx >= mEditorData.sectionDataOffset.Length)
            {
                return -1;
            }
            return mEditorData.sectionDataOffset[idx];
        }

        void LoadMapLayers(BinaryReader reader)
        {
            List<config.MapLayerData> validLayers = new List<config.MapLayerData>();

            var gridModelLayer = LoadGridModelLayerData(reader);
            if (gridModelLayer != null)
            {
                validLayers.Add(gridModelLayer);
            }

            var modelLayer = LoadModelLayerData(reader);
            if (modelLayer != null)
            {
                validLayers.Add(modelLayer);
            }

            var circleBorderLayer = LoadCircleBorderLayerData(reader);
            if (circleBorderLayer != null)
            {
                validLayers.Add(circleBorderLayer);
            }

            var blendTerrainLayer = LoadBlendTerrainLayerData(reader);
            if (blendTerrainLayer != null)
            {
                validLayers.Add(blendTerrainLayer);
            }
            else
            {
                var simpleBlendTerrainLayer = LoadSimpleBlendTerrainLayerData(reader);
                if (simpleBlendTerrainLayer != null)
                {
                    validLayers.Add(simpleBlendTerrainLayer);
                }
                else
                {
                    var tileBlockTerrainLayer = LoadTileBlockTerrainLayerData(reader);
                    if (tileBlockTerrainLayer != null)
                    {
                        validLayers.Add(tileBlockTerrainLayer);
                    }
                }
            }

            var varyingTileSizeTerrainLayer = LoadVaryingTileSizeTerrainLayerData(reader);
            if (varyingTileSizeTerrainLayer != null)
            {
                validLayers.Add(varyingTileSizeTerrainLayer);
            }

            var railwayLayer = LoadRailwayLayerData(reader);
            if (railwayLayer != null)
            {
                validLayers.Add(railwayLayer);
            }

            var lodLayer = LoadLODLayerData(reader);
            if (lodLayer != null)
            {
                validLayers.Add(lodLayer);
            }

            var riverLayer = LoadRiverLayerData(reader);
            if (riverLayer != null)
            {
                validLayers.Add(riverLayer);
            }

            var complexGridModelLayer = LoadComplexGridModelLayerData(reader, MapDataSectionType.GridModelLayer2);
            if (complexGridModelLayer != null)
            {
                validLayers.Add(complexGridModelLayer);
            }

            var decorationBorderLayer = LoadComplexGridModelLayerData(reader, MapDataSectionType.DecorationBorderLayer);
            if (decorationBorderLayer != null)
            {
                validLayers.Add(decorationBorderLayer);
            }

            var splitFogLayer = LoadSplitFogLayerData(reader);
            if (splitFogLayer != null)
            {
                validLayers.Add(splitFogLayer);
            }

            var cityTerritoryLayer = LoadNewCityTerritoryLayer(reader);
            if (cityTerritoryLayer != null)
            {
                validLayers.Add(cityTerritoryLayer);
            }

            var regionLayer = LoadRegionLayer(reader);
            if (regionLayer != null)
            {
                validLayers.Add(regionLayer);
            }

            var regionColorLayer = LoadRegionColorLayer(reader);
            if (regionColorLayer != null)
            {
                validLayers.Add(regionColorLayer);
            }

            var list = LoadPluginLayerList(reader);
            if (list != null) {
                mEditorData.pluginList = list;
            }

            mEditorData.map.mapLayers = validLayers.ToArray();
        }

        void LoadMapObstacles(BinaryReader reader)
        {
            LoadLocalObstacles(reader);
            LoadGlobalObstacles(reader);
            LoadCameraCollider(reader);
        }

        config.SLGMakerData mEditorData;
    }
}