﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public interface IMapObjectData
    {
        //是否是动态的实体,例如服务器下发的实体单位,如果是动态的实体,在实体移动到视野框外时直接删除实体的显示数据,否则只是隐藏数据
        bool IsDynamicEntity();
        //游戏中Entity的id,动态实体的id参考MapCoreDef的MAP_ENTITY_START_ID和MAP_ENTITY_END_ID的区间
        int GetEntityID();
        ModelTemplate GetModelTemplate();
        int GetModelTemplateID();
        //当Entity被隐藏时调用
        void OnHide();
        //当Entity被显示时调用
        void OnShow();
        //当Entity的GameObject创建完成时调用
        void OnInit(GameObject obj);
        //当LOD Zoom变化时被调用
        void OnZoomChange(float zoom);
        //Entity是否忽略视野范围的管理,如果为true,则不管视野框如何变化,Entity的显示状态都不会被改变
        bool IgnoreViewport();
        //Entity的prefab路径
        string GetAssetPath(int lod = 0);
        //返回bounding box
        Rect GetBounds();
        //Entity的位置
        Vector3 GetPosition();
        //Entity的缩放
        Vector3 GetScale();
        //Entity的旋转
        Quaternion GetRotation();

        void SetPosition(Vector3 pos);
        void SetScale(Vector3 scale);
        void SetRotation(Quaternion rotation);

        bool IsObjActive();
        bool SetObjActive(bool active);

        //flag相关的接口
        bool HasFlag(int flag);
        void SetFlag(int flag);
        void AddFlag(int flag);
        void RemoveFlag(int flag);
        int GetFlag();
        //获取属于哪个model group,0表示未使用lod group
        int GetModelLODGroupID();
    }
}
