﻿using Common;
using Cysharp.Threading.Tasks;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using K1;
using K3;
using Logic;
using Logic.Alliance.Achievement;
using Public;
using TFW;
using TFW.UI;
using UI.Alliance;
using UI.Utils;
using UnityEngine;

namespace UI
{

    /// <summary>
    /// 主界面 菜单栏联盟按钮
    /// </summary>
    public class UIMainMenuAlliance : UIMainMenuFunctionBase
    {

        #region 属性字段信息

        /// <summary>
        /// 聊天频道状态
        /// </summary>
        public enum ChatState
        {
            /// <summary>
            /// 世界
            /// </summary>
            World = 0,
            /// <summary>
            /// 联盟
            /// </summary>
            Alliance,
            /// <summary>
            /// 私聊
            /// </summary>
            Private,
            /// <summary>
            /// 跨服聊天
            /// </summary>
            CrossServer,
            /// <summary>
            /// 阵营测试wjk
            /// </summary>
            Faction,

            Legion,
            Luminary
        }

        /// <summary>
        /// 当前聊天状态
        /// </summary>
        private ChatState currChatState = ChatState.Alliance;

        /// <summary>
        /// 世界图标对象
        /// </summary>
        private GameObject worldIconObj;

        /// <summary>
        /// 联盟Icon对象
        /// </summary>
        private GameObject allianceIconObj;

        /// <summary>
        /// 联盟任务点对象
        /// </summary>
        private GameObject allianceTaskPointObj;

        /// <summary>
        /// 联盟任务红点文本
        /// </summary>
        private TFWText allianceTaskPointNumText;

        /// <summary>
        /// 联盟任务进度条
        /// </summary>
        private TFWSlider allianceTaskPointSlider;

        /// <summary>
        /// 联盟默认状态时红点对象
        /// </summary>
        public RedWidget allianceDefualtRedWidget;

        /// <summary>
        /// 联盟选中状态时红点对象
        /// </summary>
        public RedWidget allianceSelectRedWidget;

        #endregion


        #region 初始化

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="type"></param>
        /// <param name="root"></param>
        /// <param name="data"></param>
        public UIMainMenuAlliance(MainMenuType type, GameObject root, DeepUI.BasePopupLayer main)
            : base(type, root, main)
        {

        }

        /// <summary>
        /// 初始化联盟菜单
        /// </summary>
        protected override void Init()
        {
            base.Init();

            //记录数据位置
            GameData.I.MainData.UpdateMainAllianceUnLockAnimPos(lockAnim.transform.position);

            worldIconObj = UIHelper.GetChild(Root, "Selected/iconWorld");
            allianceIconObj = UIHelper.GetChild(Root, "Selected/icon");
            allianceTaskPointObj = UIHelper.GetChild(Root, "Selected/Slider");
            allianceTaskPointSlider = UIHelper.GetComponent<TFWSlider>(Root, "Selected/Slider");
            allianceTaskPointNumText = UIHelper.GetComponent<TFWText>(Root, "Selected/Slider/fill/TxtNum");

            var obj = UIHelper.GetChild(Root, "Icon/redPoint");
            allianceDefualtRedWidget = new RedWidget(obj);

            obj = UIHelper.GetChild(Root, "Selected/redPoint");
            allianceSelectRedWidget = new RedWidget(obj);


            UpdateAllianceRedInfo().Forget();
        }

        int countNum = -1;

        void CheckShow() 
        {
            return;
            //if (GameData.I.MainData.CurrMenuType != MainMenuType.CITY)
            //    return;
            //if (countNum == -1) 
            //{
            //    long openserverTs = LoginMgr.I.CreateTS;
            //    if (openserverTs + MetaConfig.alliance_pop_up_time > GameTime.Time)
            //    {
            //        countNum = NTimer.CountDown(10, () =>
            //        {
            //            if (GameData.I.MainData.CurrMenuType == MainMenuType.CITY)
            //            {
            //                PopupManager.I.ShowDialog<UIAllianceWelcome>();
            //            }
            //            else 
            //            {
            //                countNum = 0;
            //            }
            //        });
            //    }

            //}
            //else
            //{
            //    if (countNum == 0)
            //    {
            //        PopupManager.I.ShowDialog<UIAllianceWelcome>();
            //    }
            //}
        }

        /// <summary>
        /// 事件信息监听
        /// </summary>
        protected override void RegisterEvents()
        {
            base.RegisterEvents();

            //刷新聊天类型
            EventMgr.RegisterEvent(TEventType.ChangeChatTypeByChatMenu, OnUpdateChatTypeByChatMenu, this);
            //注册联盟礼物信息刷新
            EventMgr.RegisterEvent(TEventType.UnionGiftInfoAck, OnUpdateAllianceRedInfo, this);

            //留言板
            EventMgr.RegisterEvent(TEventType.GetUnionMessageAck, OnUpdateAllianceRedInfo, this);
            //科技
            EventMgr.RegisterEvent(TEventType.UnionTechNtf, OnUpdateAllianceRedInfo, this);
            //圣坛
            EventMgr.RegisterEvent(TEventType.UnionAltarAck, OnUpdateAllianceRedInfo, this);
            //圣坛收集
            EventMgr.RegisterEvent(TEventType.UnionAltarCollectAck, OnUpdateAllianceRedInfo, this);

            //聊天收到新消息
            EventMgr.RegisterEvent(TEventType.ChatReceiveNewMsg, OnUpdateAllianceRedInfo, this);
            //聊天消息已读
            EventMgr.RegisterEvent(TEventType.ChatMessageUnreadChanged, OnUpdateAllianceRedInfo, this);

            //军情警报相关
            EventMgr.RegisterEvent(TEventType.AllianceMillitaryAlertRed, OnUpdateAllianceRedInfo, this);

            EventMgr.RegisterEvent(TEventType.AutoRallyRedDotRefush, OnUpdateAllianceRedInfo, this);

            //#region 军情警报相关
            //EventMgr.RegisterEvent(TEventType.InformationNtf, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.InformationDelNtf, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.AllianceRallyDataUpdate, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.AllianceRallyDelNtf, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.UnionFlagsInfo, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.UnionFortsInfo, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.ActiveShieldSucceed, OnUpdateAllianceRedInfo, this);
            //#endregion

            //联盟成就
            EventMgr.RegisterEvent(TEventType.AllianceAchievementQuestReward, OnUpdateAllianceRedInfo, this);
            EventMgr.RegisterEvent(TEventType.AllianceAchievementQuestUpdate, OnUpdateAllianceRedInfo, this);

            //联盟挑战(任务) 里程碑
            EventMgr.RegisterEvent(TEventType.ChestChangeNtf, OnUpdateAllianceRedInfo, this);
            EventMgr.RegisterEvent(TEventType.MilestoneRewardAck, OnUpdateAllianceRedInfo, this);

            // 联盟商店免费奖励
            EventMgr.RegisterEvent(TEventType.NewShopBuyAck, OnUpdateAllianceRedInfo, this);
        }

        /// <summary>
        /// 数据清理
        /// </summary>
        public override void Clear()
        {
            base.Clear();

            if (allianceDefualtRedWidget != null)
            {
                allianceDefualtRedWidget.Destroy();
                allianceDefualtRedWidget = null;
            }

            if (allianceSelectRedWidget != null)
            {
                allianceSelectRedWidget.Destroy();
                allianceSelectRedWidget = null;
            }

            countNum = -1;
        }

        #endregion


        #region 数据刷新

        /// <summary>
        /// 刷新联盟信息显示
        /// </summary>
        /// <param name="chatState"></param>
        private void UpdateAllianceInfo(int chatState = -1)
        {

            //if (PopupManager.I.FindPopupFromPool<Alliance.UIAllianceMenber>() != null)
            //{
            //    PopupManager.I.ClosePopup<Alliance.UIAllianceMenber>();
            //    //PopupManager.I.ClosePopup<UIAllianceMain>();
            //    PopupManager.I.ClosePopup<UIAllianceReview>();
            //}

            var allianceWel = PopupManager.I.FindPopupFromPool<UIAllianceWel_k1>();
            if (allianceWel != null && allianceWel.IsShow)
                return;

            var index = -1;
            if (chatState == -1)
            {
                index = (int)currChatState;
                index = index + 1 > 1 ? 0 : index + 1;
            }
            else
            {
                index = chatState;
            }

            //更新聊天状态信息
            currChatState = (ChatState)index;
            if (currChatState == ChatState.Alliance)
            {
                //刷新红点显示
                //UpdateAllianceTaskPoint(null);
                //worldIconObj.SetActive(false);
                //allianceIconObj.SetActive(true);
            }
            else if (currChatState == ChatState.World)
            {
                allianceTaskPointObj.SetActive(false);
                //allianceIconObj.SetActive(false);
                //worldIconObj.SetActive(true);
            }

            //发送事件信息
            EventMgr.FireEvent(TEventType.ChangeChatTypeByMainMenu, GetChatTab(currChatState));
        }

        /// <summary>
        /// 刷新联盟红点信息
        /// </summary>
        public async UniTask UpdateAllianceRedInfo1()
        {
            var num = 0;
            //当前是否在联盟界面显示
            var isInAlliance = GameData.I.MainData.CurrMenuType == MenuType;
            if (LPlayer.I.UnionID == 0)
            {
                num = 1;
                if (allianceDefualtRedWidget != null)
                    allianceDefualtRedWidget.SetData(isInAlliance ? 0 : num, false);

                if (allianceSelectRedWidget != null)
                    allianceSelectRedWidget.SetData(isInAlliance ? num : 0, false);
                return;
            }

            
            //有权限才添加 联盟申请的数量
            if (LAllianceMgr.I.HavePermission(AlliancePermissionEnum.UpdateJoinApply))
            {
                var unionList = LAllianceMgr.I.GetUnionApplies();
                num += unionList.Count;
            }

            //联盟礼物红点
            num += await GameData.I.AllianceGiftData.TotalRedPoint();
            

            if (allianceDefualtRedWidget != null)
                allianceDefualtRedWidget.SetData(isInAlliance ? 0 : num, false);

            if (allianceSelectRedWidget != null)
                allianceSelectRedWidget.SetData(isInAlliance ? num : 0, false);
        }

        /// <summary>
        /// 刷新联盟红点信息
        /// </summary>
        public async UniTaskVoid UpdateAllianceRedInfo()
        {
            var num = 0;
            var isInAlliance = GameData.I.MainData.CurrMenuType == MenuType;
            if (LGameRedPoint.I.OpenClientRed)
            {
                var isOpen = MainFunctionOpenUtils.ALllianceOpenState;
   
                if (!isOpen)
                {
                    allianceDefualtRedWidget?.SetData(0);
                    allianceSelectRedWidget?.SetData(0);
                    return;
                }
                if (LPlayer.I.UnionID == 0)
                {
                    CheckShow();
                    //立斗说没加入联盟的时候 不需要有红点
                    allianceSelectRedWidget?.SetData(0); 
                    allianceDefualtRedWidget?.SetData(0);
                    /*if (GameData.I.MainData.CurrMenuType != MainMenuType.ALLIANCE)
                    {
                        allianceDefualtRedWidget?.SetData(1);
                        allianceSelectRedWidget?.SetData(1);
                    }
                    else
                    {
                        allianceDefualtRedWidget?.SetData(0);
                        allianceSelectRedWidget?.SetData(1);
                    }*/
                    return;
                }

                //有权限才添加 联盟申请的数量
                if (LAllianceMgr.I.HavePermission(AlliancePermissionEnum.UpdateJoinApply))
                {
                    var unionList = LAllianceMgr.I.GetUnionApplies();
                    num += unionList.Count;
                }

                if (AllianceMobilizeMgr.I.IsCanOpenMobilize(false))
                {
                    num += await GameData.I.AllianceMobilizeData.GetMobilizeRedCount();

                }

                //联盟礼物红点
                num += await GameData.I.AllianceGiftData.TotalRedPoint();
                
                //圣坛领取
                num += (LAllianceAltar.I.CanCollect ? 1 : 0);


                //联盟留言数据
                num += AllianceGameData.I.AlliancMsgBoardData.HaveSelfUnionNewMsgCount;// ( ? 1 : 0);



                ///私聊
                ///num += LSocialRedPoint.I.GetChatRedPointsCount();
                //联盟帮助条目
                num += AllianceGameData.I.AllianceHelp.GetHelpInfoCount();


                ///联盟成就
                //num += LAllianceAchievement.I.RedCount;
                //num += LAllianceNewAchievement.I.GetRedCount(); 联盟成就先不要，change by yujiawei


                num += await GameData.I.AllianceTechData.GetTechRedCount();


                num += await GameData.I.AllianceShopData.GetShopRedCount();


                var waropen = MainFunctionOpenUtils.IsOpenState((int)MetaConfig.UnlockUnionRally, true);
                var worldclose = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.World);
                
                if (waropen && !worldclose)
                {
                    //军情警报
                    num += GameData.I.MainData.MassRedCount; //LAllianceMillitaryAlert.I.GetTotalRedPoint;
                    num += GameData.I.RallyData.IsNeedShowRallyRed ? 1 : 0;

                }
            }
            else
            {
                num = GetAllianceRedCount();
            }
            
            allianceDefualtRedWidget?.SetData(isInAlliance ? 0 : num , false);
            allianceSelectRedWidget?.SetData(!isInAlliance ? num : 0, false);
        }

        /// <summary>
        /// 获取联盟红点
        /// </summary>
        /// <returns></returns>
        public int GetAllianceRedCount()
        {
            return LGameRedPoint.I.GetRedCount(RedPointType.RedPointUnionGift, RedPointType.RedPointUnionHelp,
                RedPointType.RedPointUnionTech, RedPointType.RedPointUnionLand, RedPointType.RedPointUnionShop,
                RedPointType.RedPointUnionMobilize, RedPointType.RedPointUnionAchieve);
        }

        /// <summary>
        /// 通过聊天状态，获取聊天页签
        /// </summary>
        /// <param name="state"></param>
        /// <returns></returns>
        private ChatTabs GetChatTab(ChatState state)
        {
            switch (state)
            {
                case ChatState.World:
                    return ChatTabs.Server;
                case ChatState.Alliance:
                    return ChatTabs.Alliance;
                case ChatState.Private:
                    return ChatTabs.PrivateChat;
                case ChatState.CrossServer:
                    return ChatTabs.CrossServer;
                case ChatState.Legion:
                    //军团wjk
                    return ChatTabs.Legion;
                case ChatState.Luminary:
                    return ChatTabs.Luminary;
                default:
                    return ChatTabs.Server;
            }
        }


        /// <summary>
        /// 退出菜单栏界面
        /// </summary>
        /// <param name="nextType"></param>
        public override void ExitMenuPanel(MainMenuType nextType)
        {
            base.ExitMenuPanel(nextType);

            //PopupManager.I.ClosePopup<Alliance.UIAllianceMenber>();
            PopupManager.I.ClosePopup<UIAllianceMain>();
            PopupManager.I.ClosePopup<UIAllianceWel_k1>();
            //PopupManager.I.ClosePopup<UIAllianceSettings>();
            PopupManager.I.ClosePopup<UIAllianceList_k1>();
            PopupManager.I.ClosePopup<UIAllianceReview>();
            //PopupManager.I.ClosePopup<UITask>();
            //PopupManager.I.ClosePopup<UIAllianceInfoPop>(false);
            
            PopupManager.I.ClosePopup<UIPlayerReName>();
            PopupManager.I.ClosePopup<UIAllianceInvite>();
            PopupManager.I.ClosePopup<UIAllianceWarTerritoryList>();
            PopupManager.I.ClosePopup<UIOtherPlayerData>(false);
            //PopupManager.I.ClosePopup<UIAllianceMessageBoard>(false);
            //PopupManager.I.ClearAllDialog();
        }

        /// <summary>
        /// 刷新解锁状态
        /// </summary>
        public override void UpdateMenuOpenState()
        {
            base.UpdateMenuOpenState();

            //if (!FightManager.I.IsFighted)
            //{
                var isOpen = MainFunctionOpenUtils.ALllianceOpenState;
                // if (!PlayerPrefs.HasKey(GuideMgr.I.Guide_unlockAllianceKey))
                // {
                //     isOpen = false;
                // }
                lockAnim?.gameObject.SetActive(!isOpen);
                UnlockObj?.SetActive(isOpen);
            //}
        }

        #endregion


        #region 事件监听

        /// <summary>
        /// 通过聊天菜单，刷新聊天类型
        /// </summary>
        /// <param name="objs"></param>
        private void OnUpdateChatTypeByChatMenu(object[] objs)
        {
            if (objs == null || objs.Length == 0)
                return;

            UpdateAllianceInfo((int)objs[0]);
        }

        /// <summary>
        /// 刷新联盟礼物信息数据
        /// </summary>
        /// <param name="objs"></param>
        private void OnUpdateAllianceRedInfo(object[] objs)
        {
            UpdateAllianceRedInfo().Forget();
        }


        /// <summary>
        /// 重复点击当前菜单按钮
        /// </summary>
        public override void ClickCurrMenuBtn(UIData data = null)
        {
            base.ClickCurrMenuBtn(data);

            //点击联盟
            //ClickAlliance(data);
        }

        /// <summary>
        /// 点击造兵
        /// </summary>
        //private void ClickAlliance(UIData data = null)
        //{
        //    UI.Alliance.UIAllianceMainData chatData = null;
        //    if (data != null)
        //        chatData = data as UI.Alliance.UIAllianceMainData;

        //    if (chatData != null)
        //    {
        //        LAllianceMgr.I.ShowAllianceMain(null);

        //    }
        //    else
        //    {
        //        UpdateAllianceInfo();
        //    }
        //}


        /// <summary>
        /// 点击菜单按钮
        /// </summary>
        public override async UniTask<bool> ClickMenuBtn(UIData data = null)
        {
            await base.ClickMenuBtn(data);
            WorldSwitchMgr.I.OnlyChangeWorldType(WorldTypeEnum.CITY);
            //更新聊天状态
            currChatState = ChatState.Alliance;
            //点击联盟按钮对象
            var ok = ClickChatBtn(data);

            if (!MainFunctionOpenUtils.ALllianceOpenState && !ok)
            {
                if (lockAnim != null)
                    lockAnim.Play();

                if (Main != null)
                {
                    //(Main as UIMain1)?.UpdateTrainCoinActive(false);
                    (Main as UIMain2)?.UpdateTrainCoinActive(false);
                }

                return false;
            }

            return ok;
        }

        /// <summary>
        /// 点击联盟按钮
        /// </summary>
        /// <param name="data"></param>
        private bool ClickChatBtn(UIData data = null)
        {
            //if (!GuideMgr.I.IsGuideDone)
            //    return false;
            UI.Alliance.UIAllianceMainData alliacneData = null;
            if (data != null)
                alliacneData = data as UI.Alliance.UIAllianceMainData;

            //if (LPlayer.I.GetMainCityLevel() < MetaConfig.NewUnlockUnionBtn)
            //    return false;

            //PopupManager.I.WaitingCanvasGroup.alpha = 1;

            if (LPlayer.I.UnionID == 0)
            {
                if (GameData.I.MainData.CurrMenuType == MainMenuType.HERO)
                {
                    GameInstance.CityMainCameraEnable(true);
                }


                PopupManager.I.ShowDialog<UIAllianceWelcome>(onComplete: (s) => {
                    //PopupManager.I.WaitingCanvasGroup.DOFade(0, 0.36f);
                });
                //LAllianceMgr.I.ShowAllianceMain(null);
            }
            else 
            {
               PopupManager.I.ShowLayer<UI.Alliance.UIAllianceMain>(alliacneData);

                //PopupManager.I.WaitingCanvasGroup.DOFade(0, 0.36f);
            }

            return true;
        }

        #endregion



    }
}
