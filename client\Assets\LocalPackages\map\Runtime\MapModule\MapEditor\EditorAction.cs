﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System;

namespace TFW.Map {
    class BlackAttribute : Attribute
    {

    }

    [Black]
    public abstract class EditorAction {
        public virtual string description { get { return GetType().Name; } }
        public virtual void OnDestroy() { }
        public abstract bool Do();
        public abstract bool Undo();
        public virtual bool IsUndoable() {
            return true;
        }
    };
}

#endif