﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using System.Linq;
using TFW;
using TFW.UI;
using UI;
using UnityEngine;

namespace K3
{
    public partial class UIMerge
    {

        private bool autoMergeChanging;

        public static bool IsAutoMerging;

        public bool autoMergeing = false;
        public static bool ForceAutoMergeingGM = false;

        private Dictionary<int, float> mergeAutoTimeCD;

        private float timeCD;

        public void AutoMergeDOing()
        {
            if (mergeAutoTimeCD == null)
            {
                mergeAutoTimeCD = new Dictionary<int, float>();
            }

            //if (mergeAutoType == null)
            //    mergeAutoType = new List<Vector2Int>();
            //else
            //    mergeAutoType.Clear();
            if (!StateMerge)
                return;

            //if (autoMergeGooding != null && autoMergeGoodingTime > Time.time)
            //{
            //     D.Info?.Log("autoMergeGooding && autoMergeGoodingTimeing ");
            //    return;
            //}

            //if (mergeSoliderGuid > 0 && mergeSoliderGuid < 100)
            //    return;

            //if (PopupManager.I.AnyPopup)
            //    return;

            //foreach (var item in mGoodDic)
            //{
            //    if (item.Value.mData.goodType == 1)
            //    {
            //        var hero = HeroGameData.I.GetHeroByCfgId(item.Value.mData.id);
            //        if (hero != null)
            //        {
            //            var effect = Cfg.C.CD2Skill.I(hero.HeroCfg.ActiveSkill).Effect3;
            //            if (effect.Count > 0)
            //            {
            //                item.Value.mData.GetBuffTime(out var totalTime, out var lastTime);
            //                if (lastTime > 0)
            //                {
            //                    foreach (var effectItem in effect)
            //                    {
            //                        //CD中 （buff中） 将a(type)b(code)类物品自动合成，持续时间c秒
            //                        mergeAutoType.Add(new Vector2Int(effectItem.A, effectItem.B));
            //                    }
            //                }
            //            }
            //        }
            //    }
            //}



            if (!autoMergeing) //mergeAutoType.Count == 0 &&
                return;


            if (timeCD > Time.time) //刷新CD
                return;


            timeCD = Time.time + 0.2f;
            //if(!autoMergeChanging)
            //    return;

            //if (Using_HeroSkill)
            //{
            //    D.Info?.Log("Using_HeroSkilling ");
            //    return;
            //}


            //数据和表现不对称进行检测 只有DEV&BETA
            //#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
            //            foreach (var item in K3PlayerMgr.I.GridsData)
            //            {
            //                if (mGridDic.TryGetValue(item.Key, out var gridData))
            //                {
            //                    if (gridData.Good?.mData?.id != item.Value.goodData?.id)
            //                    {
            //                        D.Error?.Log($"【棋盘】 棋盘Grid 数据不一致 Key：{item.Key} ===>>> {gridData.Good?.mData?.id}!={item.Value.goodData?.id}");
            //                    }
            //                }
            //                else
            //                {
            //                    D.Error?.Log($"【棋盘】 棋盘Grid 不包含Key{item.Key}");
            //                }
            //            }
            //#else
            //#endif


            //List<UIMergeGrid> guideGridList = new List<UIMergeGrid>();
            Dictionary<int, List<UIMergeGrid>> gridDic = new Dictionary<int, List<UIMergeGrid>>();

            foreach (var grid in mGridDic)
            {
                if (grid.GetData() != null
                    && grid.GetData().isFull
                    && grid.NoInArea()
                    && grid.Good != null
                    && grid.Good.mData.goodType == 0
                    && !grid.GetData().goodData.locked
                    && !grid.GetData().goodData.max
                    //&& !grid.Value.GetData().goodData.Immovable
                    && !grid.Good.autoMergeing
                    && grid.Good.CanClick)
                //&& grid.Value.Good.CanLevelUpByCityTech(false))
                {

                    //var k3ItemCfg = Cfg.C.CK3Item.I(grid.Value.Good.mData.id);

                    //if (autoMergeingGM ||  mergeAutoType.Contains(new Vector2Int(k3ItemCfg.Type, k3ItemCfg.Code)))

                    //{
                    int id = grid.GetData().goodData.id;
                    if (!gridDic.ContainsKey(id))
                    {
                        gridDic.Add(id, new List<UIMergeGrid>());
                    }

                    if (!gridDic[id].Contains(grid) && !grid.Good.AutoMerge())
                    {
                        gridDic[id].Add(grid);
                    }
                    //}
                }
            }

            var keyList = gridDic.Keys.OrderBy(a => Cfg.C.CK3Item.I(a)?.Level ?? 0).ToList();
            List<int> mergeCode = new List<int>();


            foreach (var key in keyList)
            {
                if (gridDic.TryGetValue(key, out var item))
                {
                    
                    if (item.Count > 1)
                    {
                        int autoMergeCode = Cfg.C.CK3Item.I(key)?.Code ?? 0;

                        if (mergeCode.Contains(autoMergeCode) || (mergeAutoTimeCD.TryGetValue(autoMergeCode, out var lastTime) && lastTime > Time.time))
                            continue;


                        var toGood = item[1].Good;
                        var fromGood = item[0].Good;

                        //if (autoMergeGooding.MergeWindowTip(toGood))//out var curCount, out var needCount
                        //{
                        //    //if (IngoreGoodIDs.Contains(toGood.mData.id))
                        //    //{
                        //    //    autoMergeGooding = null; Debug.Log("autoMergeGooding null");
                        //    //    continue;
                        //    //}

                        //    //Debug.LogError($"道具当前数量:{autoMergeGooding.mData.id} ///{curCount} 所需数量：{needCount}");

                        //    var title = LocalizationMgr.Get("Buying_Confirm_Tips_Title");
                        //    var content = LocalizationMgr.Get("Ui_Confirm_merge&mergeskill");

                        //    UITools.OpenConfirmPop(() =>
                        //    {
                        //        if (fromGood != null && toGood != null && fromGood.CanClick && toGood.CanClick)
                        //        {
                        //            autoMergeGoodingTime = Time.time + 1.2f;
                        //            fromGood.AutoMerge(toGood, () => { autoMergeGooding = null; Debug.Log("autoMergeGooding null"); });
                        //        }
                        //        else 
                        //        {
                        //            Debug.Log($"{autoMergeGooding==null} =====>>>>  {toGood==null}");
                        //            autoMergeGooding = null; Debug.Log("autoMergeGooding null");
                        //        }
                        //    },
                        //    () =>
                        //    {
                        //       /* IngoreGoodIDs = true;*/// IngoreGoodIDs.Add(toGood.mData.id);
                        //        autoMergeGooding = null; Debug.Log("autoMergeGooding null");
                        //    }, title, content, true, UIMsgBoxInitData.OpenSecondEnum.MergeGoodMergeHelpTip);
                        //}
                        //else
                        //{
                        if (fromGood != null && toGood != null && fromGood.CanClick && toGood.CanClick)
                        {

                            if (mergeCode.Contains(autoMergeCode))
                                mergeCode.Add(autoMergeCode);

                            if (mergeAutoTimeCD.ContainsKey(autoMergeCode))
                                mergeAutoTimeCD[autoMergeCode] = Time.time + 0.6f;
                            else
                                mergeAutoTimeCD.Add(autoMergeCode, Time.time + 0.6f);

                            fromGood.AutoMerge(toGood, () => { D.Info?.Log("autoMergeGooding null"); });
                        }
                        //else
                        //{
                        //    Debug.Log("autoMergeGooding null");
                        //    autoMergeGooding = null;
                        //}
                        //}


                    }
                }
            }


        }

        public static bool IngoreGoodIDs = false;

        ///// <summary>
        ///// 使用自动合成的时间
        ///// </summary>
        //DateTime UseAutoTime;
        /// <summary>
        /// 结束自动合成的时间
        /// </summary>
        long EndAutoTime;

        ///// <summary>
        ///// CD开启时间
        ///// </summary>
        //DateTime BeginCDTime;
        ///// <summary>
        ///// CD结束时间
        ///// </summary>
        //DateTime EndCDTime;

        [PopupField("Root/btns/AutoButtonBG")]
        public GameObject AutoButtonBG;

        [PopupField("Root/btns/AutoButtonBG/AutoButton/RedDot")]
        private GameObject autoButtonRedPoint;

        [PopupField("Root/btns")]
        public GameObject AutoButton_SortBtns;

        [PopupField("Root/btns/AutoButtonBG/AutoButton")]
        public GameObject AutoButton;

        [PopupField("Root/btns/SortButton")]
        public GameObject SortMergeBtn;

        [PopupField("Root/btns/AutoButtonBG/AutoButton/Time")]
        private TFWImage AutoTime;

        [PopupField("Root/btns/AutoButtonBG/AutoButton/Time/Time0")]
        private TFWImage AutoTime0;

        [PopupField("Root/btns/AutoButtonBG/AutoButton/CD")]
        private TFWImage AutoCD;

        [PopupField("Root/InfoPanel/AutoInfoButton")]
        private GameObject AutoInfoButton;
        [PopupField("Root/InfoPanel/AutoInfoButton/Time")]
        private GameObject TimeObj;
        [PopupField("Root/InfoPanel/AutoInfoButton/Time/Time0")]
        private TFWImage TimeSlider;
        [PopupField("Root/InfoPanel/AutoInfoButton/CDTime")]
        private TFWText CDTime;
        [PopupField("Root/InfoPanel/AutoBtnGrid")]
        private GameObject AutoBtnGrid;
        [PopupField("Root/InfoPanel/AutoTextGrid")]
        private GameObject AutoTextGrid;

        [PopupField("Root/InfoPanel/AutoBtnGrid/AutoADBtn")]
        private GameObject AutoADBtn;
        [PopupField("Root/InfoPanel/AutoBtnGrid/AutoCDBtn")]
        private GameObject AutoCDBtn;
        [PopupField("Root/InfoPanel/AutoBtnGrid/AutoCDBtn/text")]
        private TFWText AutoCDBtnText;
        [PopupField("Root/InfoPanel/AutoBtnGrid/AutoYueKaBtn")]
        private GameObject AutoYueKaBtn;
        [PopupField("Root/InfoPanel/AutoBtnGrid/AutoYueKaBtn/text")]
        private TFWText AutoYueKaBtnText;

        [PopupField("Root/InfoPanel/AutoTextGrid/AutoADText")]
        private GameObject AutoADText;
        [PopupField("Root/InfoPanel/AutoTextGrid/AutoCDText")]
        private GameObject AutoCDText;
        [PopupField("Root/InfoPanel/AutoTextGrid/AutoYueKaText")]
        private GameObject AutoYueKaText;

        private bool mAutoMergeReq = false;



        void InitAuto()
        {
            //MessageMgr.RegisterMsg<MergeAutoAck>(this, OnMergeAutoAck);
            //MessageMgr.RegisterMsg<MergeAutoNtf>(this, OnMergeAutoNtf);
            AutoTime0.fillAmount = 0;

            BindClickListener(AutoButton, (x, y) =>
            {
                BtnClickAuto();
            });

            if (K3PlayerMgr.I.PlayerData != null)
            {
                autoMergeing = K3PlayerMgr.I.PlayerData.OpenVipAutoMerge;
            }
            else
            {
                autoMergeing = false; 
            }

            //BindClickListener(AutoYueKaBtn, (x, y) =>
            //{
            //    if (!MonthCardMgr.I.GetHavaCard(MonthCardEnum.Common))
            //    {
            //        //ShopMgr.I.OpenShopPanel( ActivityType.AccumulativeRecharge)
            //        //ShopMgr.I.OpenShopPanel(ShopTab.MonthCard, ShopTabConfig.I.specialDealEntranceTabs);
            //    }
            //});
            //BindClickListener(AutoCDBtn, (x, y) =>
            //{
            //    ClickContineuCd(false);
            //});
            //BindClickListener(AutoADBtn, (x, y) =>
            //{
            //    //var adEvent = new K3.ADEvent();
            //    //adEvent.EventKey = "ADEvent_Start";
            //    //adEvent.Properties.Add("AdResource", "AutoMerge");
            //    //K3.K3GameEvent.I.TaLog(adEvent);

            //    //SDKManager.instance.ShowAd("automerge", (success) =>
            //    //{

            //    //    switch (success)
            //    //    {
            //    //        case SDKManager.AdResult.VIP:
            //    //            //adEvent = new K3.ADEvent();
            //    //            //adEvent.EventKey = "ADEvent_vip";
            //    //            //adEvent.Properties.Add("AdResource", "AutoMerge");
            //    //            //K3.K3GameEvent.I.TaLog(adEvent);

            //    //            var uimerge = DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
            //    //            uimerge.UseAuto();
            //    //            FloatTips.I.FloatMsg(LocalizationMgr.Get("System_ADShop_Erro_3"));
            //    //            AutoPanelBI(success.ToString());
            //    //            break;
            //    //        case SDKManager.AdResult.Ok:
            //    //            //adEvent = new K3.ADEvent();
            //    //            //adEvent.EventKey = "ADEvent_Success";
            //    //            //adEvent.Properties.Add("AdResource", "AutoMerge");
            //    //            //K3.K3GameEvent.I.TaLog(adEvent);

            //    //            var uimerge2 = DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
            //    //            uimerge2.UseAuto();
            //    //            FloatTips.I.FloatMsg(LocalizationMgr.Get("System_ADShop_Erro_3"));
            //    //            AutoPanelBI("Ad");
            //    //            break;
            //    //        case SDKManager.AdResult.CloseVideo:
            //    //            //adEvent = new K3.ADEvent();
            //    //            //adEvent.EventKey = "ADEvent_closevideo";
            //    //            //adEvent.Properties.Add("AdResource", "AutoMerge");
            //    //            //K3.K3GameEvent.I.TaLog(adEvent);

            //    //            break;
            //    //        default:
            //    //            //adEvent = new K3.ADEvent();
            //    //            //adEvent.EventKey = "ADEvent_loadError";
            //    //            //adEvent.Properties.Add("AdResource", "AutoMerge");
            //    //            //K3.K3GameEvent.I.TaLog(adEvent);

            //    //            FloatTips.I.FloatMsg(LocalizationMgr.Get("System_ADShop_Erro_2"));
            //    //            break;
            //    //    }
            //    //    ShowAutoMergeToPinfo(true);
            //    //});
            //});

            //autoMergeChanging = true;
        }



        //void InitDataAuto()
        //{
        //    //string Str_UseAutoTime = PlayerPrefs.GetString(LPlayer.I.PlayerID + "UseAutoTime");
        //    //string Str_EndAutoTime = PlayerPrefs.GetString(LPlayer.I.PlayerID + "EndAutoTime");
        //    //string Str_BeginCDTime = PlayerPrefs.GetString(LPlayer.I.PlayerID + "BeginCDTime");
        //    //string Str_EndCDTime = PlayerPrefs.GetString(LPlayer.I.PlayerID + "EndCDTime");
        //    //if (!string.IsNullOrEmpty(Str_UseAutoTime))
        //    //{
        //    //    UseAutoTime = DateTime.Parse(Str_UseAutoTime);
        //    //}
        //    //if (!string.IsNullOrEmpty(Str_EndAutoTime))
        //    //{
        //    //    EndAutoTime = DateTime.Parse(Str_EndAutoTime);
        //    //}
        //    //if (!string.IsNullOrEmpty(Str_BeginCDTime))
        //    //{
        //    //    BeginCDTime = DateTime.Parse(Str_BeginCDTime);
        //    //}
        //    //if (!string.IsNullOrEmpty(Str_EndCDTime))
        //    //{
        //    //    EndCDTime = DateTime.Parse(Str_EndCDTime);
        //    //}
        //    //AutoTime.gameObject.SetActive(false);
 
        //    //if (GameTime.Time<EndAutoTime)
        //    //{
        //    //    if (DateTime.Compare(timenow, EndAutoTime) >= 0)
        //    //    {

        //    //        autoMergeing = false;
        //    //        UseAutoTime = new DateTime();
        //    //        EndAutoTime = new DateTime();
        //    //        //Debug.LogError("时间结束:" + UseAutoTime);
        //    //        SaveAutoTime();
        //    //    }
        //    //    else
        //    //    {
        //    //        autoMergeing = true;
        //    //        double all = EndAutoTime.Subtract(UseAutoTime).TotalSeconds;
        //    //        double less = EndAutoTime.Subtract(timenow).TotalSeconds;
        //    //        AutoTime0.fillAmount = ((float)less / (float)all);
        //    //        AutoTime.gameObject.SetActive(true);
        //    //    }
        //    //}

        //    //firstMonthAutoMerge = GamePlayerPrefs.GetBoolByRole("monthcard_automerge");

        //    //UpdateAuto();
        //}

        //public int GetCDPrice()
        //{
        //    DateTime timenow = TFW.Common.GetTimeByServerMillisecondsTime(GameTime.Time);
        //    double cdTime = EndCDTime.Subtract(timenow).TotalSeconds;
        //    int cost = (int)Math.Ceiling(cdTime * 1f / MetaConfig.AutoMergeCdCost);
        //    return cost;
        //}
      
        //void ClickAdCd(bool free)
        //{
        //    DateTime timenow = TFW.Common.GetTimeByServerMillisecondsTime(GameTime.Time);
        //    EndCDTime = timenow;
        //}

        //void ClickContineuCd(bool free)
        //{
        //    if (free)
        //    {
        //        DateTime timenow = TFW.Common.GetTimeByServerMillisecondsTime(GameTime.Time);
        //        EndCDTime = timenow;

        //        SaveAutoTime();
        //    }
        //    else
        //    {
        //        var price = GetCDPrice();
        //        if (K3PlayerMgr.I.UseMoney(MoneyType.Diamond, price, "AutoMergeCD"))
        //        {
        //            DateTime timenow = TFW.Common.GetTimeByServerMillisecondsTime(GameTime.Time);
        //            EndCDTime = timenow;

        //            SaveAutoTime();

        //            CSPlayer.I.k3ToServerData.AddAutoMergeCDStep(price);
        //            K3PlayerMgr.I.SavePlayerDataToServer();
        //        }
        //        else
        //        {
        //            FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("ui_diamond_tilte_not_enough"));
        //        }
        //    }
        //}
        /// <summary>
        /// 跳过CD
        /// </summary>
        //void PassCd(int second)
        //{
        //    EndCDTime.AddSeconds(-second);
        //}

        void BtnClickAuto()
        { 
            if (autoMergeing)
            {
                if (VipManager.I.curVipLevel >= MetaConfig.AutoMergeUnlockLv)
                {
                    K3PlayerMgr.I.PlayerData.OpenVipAutoMerge = false;

                    autoMergeing = false;

                    K3PlayerMgr.I.SavePlayerDataToServer();
                }
                return;
            }

            if (K3PlayerMgr.I.PlayerData.MonthFree == 0 && VipManager.I.curVipLevel < MetaConfig.AutoMergeUnlockLv)
            {
                TestAutoMerge();
            }
            else
            {
                if (VipManager.I.curVipLevel >= MetaConfig.AutoMergeUnlockLv)
                {
                    //VIP 2等级限制了 
                    autoMergeing = true;
                    K3PlayerMgr.I.PlayerData.OpenVipAutoMerge = true;
                    K3PlayerMgr.I.PlayerData.MonthFree = 1;
                    K3PlayerMgr.I.SavePlayerDataToServer();
                }
                else
                {
                    FloatTips.I.FloatMsg("Vip_tips_01".ToLocal());
                } 
            } 
        }

        //private bool firstMonthAutoMerge = false;

        void UpdateAuto()
        { 
            bool unlock = false;
            var hasVipAutoMerge = VipManager.I.curVipLevel >= MetaConfig.AutoMergeUnlockLv;//  MonthCardMgr.I.GetHavaCard(MonthCardEnum.Common);
            //if (hasMonthCard)
            //{
            //    unlock = true;
            //}
            //else
            {
                unlock = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Auto);
            }
            //达成指定条件或购买月卡后
            AutoButtonBG.gameObject.SetActive(unlock);
             
            autoButtonRedPoint.SetActive(K3PlayerMgr.I.PlayerData.MonthFree == 0);

            SortMergeBtn.SetActive(UnlockMgr.I.CheckUnlock(UnlockFuncEnum.MergeSort));

            //if (hasMonthCard && !firstMonthAutoMerge) //只在第一次进行更新
            //{
            //    GamePlayerPrefs.SetBoolByRole("monthcard_automerge", true);
            //    firstMonthAutoMerge = true;
            //    UseAuto();
            //}

            if (!K3PlayerMgr.I.PlayerData.UnlockMenus.Contains((int)K3UnlockData.UnlockType.AutoMerge) && unlock)
            {
                K3Unlock.UnlockDatas.Enqueue(new K3UnlockData()
                {
                    unlockType = K3UnlockData.UnlockType.AutoMerge
                });

                K3Unlock.ShowData(true);
                D.Info?.Log($"UnlockMenu :解锁AutoMerge");
            }

            if (ForceAutoMergeingGM)
            {
                autoMergeing = true;
                return;
            }

            if (autoMergeing)
            {

                if (hasVipAutoMerge)
                {
                    AutoTime0.fillAmount = 1f;
                    TimeSlider.fillAmount = 1f;
                }
                else if (EndAutoTime > GameTime.Time)
                {
                    var process = ((EndAutoTime - GameTime.Time) * 0.001f) / MetaConfig.AutoMergePerSecond;
                    AutoTime0.fillAmount = process;
                    TimeSlider.fillAmount = process;
                }
                else
                {
                    autoMergeing = false;
                }
                 
                AutoTime.gameObject.SetActive(true);
                TimeObj.SetActive(true);
                AutoCD.fillAmount = 0;
                CDTime.gameObject.SetActive(false);

                //PlayAutoMergeBtnAni(true);
            }
            else
            {
                //PlayAutoMergeBtnAni(false);

                AutoCD.fillAmount = 0;
                AutoTime.gameObject.SetActive(false);
                TimeObj.SetActive(false);
                AutoTime0.fillAmount = 0;
                //AutoIsCD = false; 
            }
            CDTime.gameObject.SetActive(false);
            AutoCDBtn.SetActive(false);
            AutoCDText.SetActive(false);
        }

   

        //public bool AutoIsCD;
        //private void OnMergeAutoAck(MergeAutoAck obj)
        //{
        //    if (obj.errCode == 0)
        //    {
        //        autoMergeing = true;
        //        UseAutoTime = TFW.Common.GetTimeByServerMillisecondsTime(obj.ts);
        //        EndAutoTime = UseAutoTime.AddSeconds(Game.Config.TableConfig.AutoMergePerSecond);
        //        BeginCDTime = EndAutoTime;
        //        EndCDTime = BeginCDTime.AddSeconds(Game.Config.TableConfig.AutoMergeCD);
        //        SaveAutoTime();
        //    }

        //    mAutoMergeReq = false;
        //}
        //private void OnMergeAutoNtf(MergeAutoNtf obj)
        //{
        //    if (obj != null)
        //    {
        //        UseAutoTime = TFW.Common.GetTimeByServerMillisecondsTime(obj.ts);
        //        EndAutoTime = UseAutoTime.AddSeconds(Game.Config.TableConfig.AutoMergePerSecond);
        //        BeginCDTime = EndAutoTime;
        //        EndCDTime = BeginCDTime.AddSeconds(Game.Config.TableConfig.AutoMergeCD);
        //        SaveAutoTime();
        //    }
        //}
        //public void UseAuto()
        //{
        //    MessageMgr.Send(new MergeAutoReq());
        //    mAutoMergeReq = true;
        //}

        private void TestAutoMerge()
        {
            autoMergeing = true;
            EndAutoTime = GameTime.Time + (long)(MetaConfig.AutoMergePerSecond * 1000);
            K3PlayerMgr.I.PlayerData.MonthFree = 1;
            K3PlayerMgr.I.SavePlayerDataToServer();

            NTimer.CountDown(TableConfig.AutoMergePerSecond, () =>
            {
                //试用结束
                FloatTips.I.FloatMsg("Trial_use_end_02".ToLocal());
                GuidManage.TriggerGuid(GuidManage.GuidTriggerType.TestMonthCard, 0); 
            });
        }

        //void SaveAutoTime()
        //{
        //    PlayerPrefs.SetString(LPlayer.I.PlayerID + "UseAutoTime", UseAutoTime.ToString());
        //    PlayerPrefs.SetString(LPlayer.I.PlayerID + "EndAutoTime", EndAutoTime.ToString());
        //    PlayerPrefs.SetString(LPlayer.I.PlayerID + "BeginCDTime", BeginCDTime.ToString());
        //    PlayerPrefs.SetString(LPlayer.I.PlayerID + "EndCDTime", EndCDTime.ToString());
        //}
        //private void AutoPanelBI(string count)
        //{

        //    //var adEvent = new K3.ADEvent();
        //    //adEvent.EventKey = "ADEvent_AutoCompound";
        //    //if (count != null)
        //    //{
        //    //    adEvent.Properties.Add("AdSource", count);
        //    //    K3.K3GameEvent.I.TaLog(adEvent);
        //    //}

        //}




        /// <summary>
        /// 仓库是否解锁
        /// </summary>
        /// <returns></returns>
        //public bool IsPrivateOpen()
        //{
        //    if (mGoodDic != null && mGoodDic.Length > 0)
        //    {
        //        var good = mGoodDic.Where(t => t.Value != null && t.Value.Info?.id == Cfg.CfgConst.K3MergeStoreID).FirstOrDefault();
        //        if (good.Value != null)
        //        {
        //            return good.Value.CurGrid?.NoInArea() ?? false;
        //        }
        //    }
        //    return false;
        //}



        public static DG.Tweening.Sequence DOJump(Transform target, Vector3 endValue, float jumpPower, int numJumps, float duration, bool snapping = false)
        {
            if (numJumps < 1) numJumps = 1;
            float startPosY = target.position.y; // Temporary fix for OnStart not being called when using Goto instead of GotoWithCallbacks
            float offsetY = -1;
            bool offsetYSet = false;

            // Separate Y Tween so we can elaborate elapsedPercentage on that instead of on the Sequence
            // (in case users add a delay or other elements to the Sequence)
            DG.Tweening.Sequence s = DOTween.Sequence();
            Tween yTween = DOTween.To(() => target.position, x => target.position = x, new Vector3(0, jumpPower, 0), duration / (numJumps * 2))
                .SetOptions(AxisConstraint.Y, snapping).SetEase(Ease.OutQuad).SetRelative()
                .SetLoops(numJumps * 2, LoopType.Yoyo)
                .OnStart(() => startPosY = target.position.y); // FIXME not called if you only use Goto (and not GotoWithCallbacks)
            s.Append(DOTween.To(() => target.position, x => target.position = x, new Vector3(endValue.x, 0, 0), duration)
                    .SetOptions(AxisConstraint.X, snapping).SetEase(Ease.Linear)
                ).Join(DOTween.To(() => target.position, x => target.position = x, new Vector3(0, 0, endValue.z), duration)
                    .SetOptions(AxisConstraint.Z, snapping).SetEase(Ease.Linear)
                ).Join(yTween)
                .SetTarget(target).SetEase(DOTween.defaultEaseType);
            yTween.OnUpdate(() =>
            {
                if (!offsetYSet)
                {
                    offsetYSet = true;
                    offsetY = endValue.y - startPosY;
                }
                Vector3 pos = target.position;
                pos.y += DOVirtual.EasedValue(0, offsetY, yTween.ElapsedPercentage(), Ease.OutQuad);
                target.position = pos;
            });
            return s;
        }

          
        [ContextMenu("Sort")]
        public async UniTask<bool> SortMerge()
        {
            // 1. 添加数据验证和错误处理
            if (mGoodDic == null || mGridDic == null)
            {
                D.Error?.Log("排序失败：数据字典为空");
                return false;
            }

            if (!CanDrag())
            {
                FloatTips.I.FloatMsg("auto_merge_tips_01".ToLocal());
                return false;
            }

            var effectPath = "Assets/K3/Res/Effect/UI/Eff_ui_qpg_zh.prefab";
            var effect = await ResourceMgr.LoadInstanceAsync(effectPath, GameObject.transform);

            try
            {
                List<Vector2Int> PosList = new List<Vector2Int>();
                foreach (var item in K3PlayerMgr.I.GridsData)
                {
                    if (K3PlayerMgr.I.AreaData.NoPoint(new Vector2(item.point.x, item.point.y)))
                    {
                        if (item.goodData == null || !item.goodData.Immovable)
                        {
                            PosList.Add(new Vector2Int(item.point.x, item.point.y));
                        }
                    }
                }

                //寻找中心点
                Vector2 center = Vector2.zero;
                foreach (var item in PosList)
                {
                    center += item;
                }
                center = new Vector2(center.x * 1f / PosList.Count, center.y * 1f / PosList.Count);

                var centerPos = PosList.OrderBy(a => Vector2.Distance(a, center)).FirstOrDefault();

                var SortPosList = PosList.OrderByDescending(a => a.y).ThenBy(a => a.x).ToList();

                await UniTask.WaitForSeconds(0.36f);

                Dictionary<int, List<UIMergeGood>> SortGoodDic = new Dictionary<int, List<UIMergeGood>>();
                foreach (var good in mGoodDic)
                {
                    if (good != null && !good.Info.Immovable &&
                        K3PlayerMgr.I.AreaData.NoPoint(good.mPoint))
                    {
                       var targetGridRoot=  mGridDic[new Point(centerPos.x, centerPos.y).ServerIndex];
                        if (targetGridRoot == null)
                            continue;

                        good.transform.SetParent(MoveGoodRoot);

                        //可移动
                        DOJump(good.transform, targetGridRoot.transform.position, 1.2f, 1, 0.36f);

                        int curKey = good.Info.ItemSource[0] - 10;

                        if (SortGoodDic.ContainsKey(curKey))
                        {
                            SortGoodDic[curKey].Add(good);
                        }
                        else
                        {
                            SortGoodDic.Add(curKey, new List<UIMergeGood>() { good });
                        }
                    }
                }

                int itemMoveCount = 0;
                foreach (var goods in SortGoodDic)
                {
                    foreach (var ii in goods.Value)
                    {
                        if (ii != null)
                        {
                            ii.GridOutGood();
                            itemMoveCount++;
                        }
                    }
                }

                await UniTask.WaitForSeconds(1f);

                var filledPositions = new Dictionary<Vector2Int, UIMergeGood>();

                int curY = -1;
                foreach (var keyValuePair in SortGoodDic.OrderBy(kvp => kvp.Key))
                {
                    var items = keyValuePair.Value;
                    int itemCount = items.Count;

                    var curItems = items.OrderBy(a => a.Info.Code)
                                       .ThenBy(a => a.Info.Level)
                                       .ToList();
                    foreach (var item in curItems)//同类型道具
                    {
                        if (SortPosList.Count == 0) break;

                        // 找到当前填充位置
                        Vector2Int position = SortPosList[0];

                        // 填充数据
                        filledPositions[position] = item;

                        SortPosList.Remove(position);

                        curY = position.y;
                        itemMoveCount--;
                    }
                }

                foreach (var item in filledPositions)
                {
                    if (item.Value == null) continue;

                    var toPoint = new Point(item.Key.x, item.Key.y);
                    var targetGridRoot = mGridDic[toPoint.ServerIndex];
                    if (targetGridRoot != null)
                    {
                        DOJump(item.Value.transform, targetGridRoot.transform.position, 1.2f, 1, 0.36f).AppendCallback(() =>
                        {
                            item.Value.SetGrid(toPoint);
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                D.Error?.Log($"排序过程中发生错误: {ex.Message}\n{ex.StackTrace}");
            }
            finally
            {
                GameObject.Destroy(effect, 0.5f);
                ResourceMgr.Release(effectPath);
            }

            return true;
        }

    }
}








