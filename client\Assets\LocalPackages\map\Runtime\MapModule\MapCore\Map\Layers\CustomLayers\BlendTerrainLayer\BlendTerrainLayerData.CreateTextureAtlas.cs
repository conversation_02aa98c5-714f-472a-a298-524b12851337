﻿ 



 
 

#if false

using UnityEngine;
using System.Collections.Generic;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //将所有用到的tile的贴图打到一张或几张atlas贴图上
    public partial class BlendTerrainLayerData : MapObjectLayerData
    {
#if UNITY_EDITOR
        class TexturePackerEntry
        {
            public TexturePacker packer;
            public List<TexturePacker.PackInfo> packInfo;
            public List<TexturePackerItem> packItems;
        }
        TexturePackerEntry mCurrentEntry;
        List<TexturePackerEntry> mPackers;
#endif
        Dictionary<string, Vector2[]> mAtlasUVMappings;

        public void PackUsedTileTextures(Material atlasMaterial, string atlasTexturePropertyName, bool packOnlyFileNotExist, int borderSize, bool rgbTextureAlphaIsOne)
        {
            var folders = GetUsedTileFolders();
            PackAllTilesToAtlas(folders, atlasTexturePropertyName, atlasMaterial, packOnlyFileNotExist, borderSize, rgbTextureAlphaIsOne);
        }

        class FolderEntry
        {
            public string path;
            public string tag;
        }

        //获取所有使用了的tile prefab的目录,准备打包
        List<FolderEntry> GetUsedTileFolders()
        {
            List<FolderEntry> folders = new List<FolderEntry>();
#if UNITY_EDITOR
            //指定的目录
            int n = mGroundTileAtlasSetting.groundTileFolders.Length;
            for (int i = 0; i < n; ++i)
            {
                string folderPath = AssetDatabase.GetAssetPath(mGroundTileAtlasSetting.groundTileFolders[i]);
                if (Directory.Exists(folderPath))
                {
                    folders.Add(new FolderEntry{ path = folderPath, tag = mGroundTileAtlasSetting.tags[i]});
                }
            }
#endif
            return folders;
        }

        //将指定目录的所有tile prefab某个贴图打包到atlas
        void PackAllTilesToAtlas(List<FolderEntry> tilePrefabFolders, string atlasTexturePropertyName, Material material, bool packOnlyFileNotExist, int borderSize, bool rgbTextureAlphaIsOne)
        {
#if UNITY_EDITOR
            if (packOnlyFileNotExist)
            {
                string path = $"{MapModule.groundTileAtlasInfoDir}/all_ground_tile_atlas_0.tga";
                if (File.Exists(path))
                {
                    return;
                }
            }

            //find all textures in folders
            Dictionary<string, List<Texture2D>> texturesSortedByTag = FindAllTexturesInFolder(tilePrefabFolders, atlasTexturePropertyName);

            mAtlasUVMappings = new Dictionary<string, Vector2[]>();
            mPackers = new List<TexturePackerEntry>();
            bool packOK = true;
            foreach (var p in texturesSortedByTag)
            {
                mCurrentEntry = new TexturePackerEntry();
                mCurrentEntry.packer = new TexturePacker();
                packOK &= mCurrentEntry.packer.Pack(TexturePackerStrategyType.EqualRects, OnPackTexturesSucceed, p.Value, false, 2048, borderSize, rgbTextureAlphaIsOne);
                mPackers.Add(mCurrentEntry);
            }
            if (packOK)
            {
                OnPackFinish();
                ExportGroundTileUVAtlasInfo();
            }
            else
            {
                EditorUtility.DisplayDialog("Erorr", "Create Texture Atlas Failed!", "OK");
            }
#endif
        }

        Dictionary<string, List<Texture2D>> FindAllTexturesInFolder(List<FolderEntry> folders, string atlasTexturePropertyName)
        {
            Dictionary<string, List<Texture2D>> allTextures = new Dictionary<string, List<Texture2D>>();
#if UNITY_EDITOR
            for (int i = 0; i < folders.Count; ++i)
            {
                var tag = folders[i].tag;
                if (string.IsNullOrEmpty(tag))
                {
                    tag = "";
                }
                List<Texture2D> texturesOfTag;
                allTextures.TryGetValue(tag, out texturesOfTag);
                if (texturesOfTag == null)
                {
                    texturesOfTag = new List<Texture2D>();
                    allTextures[tag] = texturesOfTag;
                }
                var enumerator = Directory.EnumerateFiles(folders[i].path);
                foreach (var path in enumerator)
                {
                    if (path.EndsWith("tga", true, null) && path.IndexOf("_00_") == -1)
                    {
                        string nameWithoutExt = Utils.RemoveExtension(path);
                        if (nameWithoutExt.EndsWith(atlasTexturePropertyName, true, null))
                        {
                            var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                            Debug.Assert(texture != null);
                            texturesOfTag.Add(texture);
                        }
                    }
                }
            }
#endif
            return allTextures;
        }

#if UNITY_EDITOR
        bool OnPackTexturesSucceed(List<TexturePacker.PackInfo> packInfos, List<TexturePackerItem> textures)
        {
            mCurrentEntry.packInfo = packInfos;
            mCurrentEntry.packItems = textures;
            return false;
        }

        void OnPackFinish()
        {
            int globalIdx = 0;
            List<Material> atlasMaterials = new List<Material>();
            List<Texture2D> atlasTextures = new List<Texture2D>();
            for (int p = 0; p < mPackers.Count; ++p)
            {
                var entry = mPackers[p];
                if (globalIdx == 0)
                {
                    //create atlas texture
                    var atlasTexture0 = CreateAtlasTexture(entry.packInfo[0].packedTexture, 0);
                    mCombinedTileMaterial.SetTexture(mGroundTileAtlasSetting.atlasTexturePropertyName, atlasTexture0);
                    EditorUtility.SetDirty(atlasTexture0);
                    AssetDatabase.SaveAssets();
                    atlasTextures.Add(atlasTexture0);
                    atlasMaterials.Add(mCombinedTileMaterial);

                    if (entry.packInfo.Count > 1)
                    {
                        for (int i = 1; i < entry.packInfo.Count; ++i)
                        {
                            var atlasTexture = CreateAtlasTexture(entry.packInfo[i].packedTexture, i + globalIdx);
                            var atlasMaterial = CreateAtlasMaterial(i + globalIdx, atlasTexture);
                            Debug.Assert(atlasMaterial != null);
                            atlasTextures.Add(atlasTexture);
                            atlasMaterials.Add(atlasMaterial);
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < entry.packInfo.Count; ++i)
                    {
                        var atlasTexture = CreateAtlasTexture(entry.packInfo[i].packedTexture, i + globalIdx);
                        var atlasMaterial = CreateAtlasMaterial(i + globalIdx, atlasTexture);
                        Debug.Assert(atlasMaterial != null);
                        atlasTextures.Add(atlasTexture);
                        atlasMaterials.Add(atlasMaterial);
                    }
                }

                for (int i = 0; i < entry.packItems.Count; ++i)
                {
                    EditorUtility.DisplayProgressBar("Creating Texture Atlas...",
                        $"Processing[{i + 1}/{entry.packItems.Count}]: {entry.packItems[i].texture.name}",
                        1f * (i + 1) / entry.packItems.Count) ;

                    string textureFilePath = AssetDatabase.GetAssetPath(entry.packItems[i].texture);
                    Debug.Assert(!string.IsNullOrEmpty(textureFilePath));
                    string textureFolder = Utils.GetFolderPath(textureFilePath);
                    int textureAtlasIndex;
                    Vector2[] uv = entry.packer.GetUV(textureFilePath, out textureAtlasIndex);
                    textureAtlasIndex += globalIdx;
                    string prefabPath = GetAssetPath(textureFilePath, mGroundTileAtlasSetting.atlasTexturePropertyName, "prefab");
                    mAtlasUVMappings.Add(prefabPath, uv);

                    //modify mesh uv
                    string meshPath = GetAssetPath(textureFilePath, mGroundTileAtlasSetting.atlasTexturePropertyName, "asset");
                    var mesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
                    mesh.uv = uv;
                    EditorUtility.SetDirty(mesh);

                    //modify material
                    atlasMaterials[textureAtlasIndex].SetTexture(mGroundTileAtlasSetting.atlasTexturePropertyName, atlasTextures[textureAtlasIndex]);
                    EditorUtility.SetDirty(atlasMaterials[textureAtlasIndex]);

                    //modify prefab
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    prefab.GetComponent<MeshRenderer>().sharedMaterial = atlasMaterials[textureAtlasIndex];
                    PrefabUtility.SavePrefabAsset(prefab);
                }

                AssetDatabase.SaveAssets();

                for (int i = 0; i < entry.packInfo.Count; ++i)
                {
                    Utils.DestroyObject(entry.packInfo[i].packedTexture);
                }

                globalIdx += entry.packInfo.Count;

                EditorUtility.ClearProgressBar();
            }
        }
#endif

        Material CreateAtlasMaterial(int index, Texture2D atlasTexture)
        {
#if UNITY_EDITOR
            var mtl = Object.Instantiate<Material>(mCombinedTileMaterial);
            mtl.SetTexture(mGroundTileAtlasSetting.atlasTexturePropertyName, atlasTexture);
            string assetPath = AssetDatabase.GetAssetPath(mCombinedTileMaterial);
            string mtlPath = Utils.RemoveExtension(assetPath) + $"_{index}.mat";
            AssetDatabase.CreateAsset(mtl, mtlPath);
            return AssetDatabase.LoadAssetAtPath<Material>(mtlPath);
#else
            return null;
#endif
        }

        Texture2D CreateAtlasTexture(Texture2D runtimeAtlasTexture, int index)
        {
#if UNITY_EDITOR
            string path = $"{MapModule.groundTileAtlasInfoDir}/all_ground_tile_atlas_{index}.tga";
            if (!Directory.Exists(MapModule.groundTileAtlasInfoDir))
            {
                Directory.CreateDirectory(MapModule.groundTileAtlasInfoDir);
            }

            var bytes = runtimeAtlasTexture.EncodeToTGA();
            File.WriteAllBytes(path, bytes);
            AssetDatabase.Refresh();
            var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
            return texture;
#else
            return null;
#endif
        }

        string GetAssetPath(string textureFilePath, string textureAtlasPropertyName, string ext)
        {
            int index = textureFilePath.IndexOf($"_{textureAtlasPropertyName}");
            var path = textureFilePath.Remove(index);
            path = path + $".{ext}";
            return path;
        }

        //生成tile prefab与atlas uv的对应信息
        void ExportGroundTileUVAtlasInfo()
        {
#if UNITY_EDITOR
            string filePath = $"{MapModule.groundTileAtlasInfoDir}/{MapCoreDef.MAP_GROUND_TILE_ATLAS_INFO_FILE_NAME}";

            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.GroundTileAtlasInfoVersion);

            writer.Write(mAtlasUVMappings.Count);
            foreach (var p in mAtlasUVMappings)
            {
                string prefabFilePath = p.Key;
                Vector2[] uv = p.Value;
                Utils.WriteString(writer, prefabFilePath);
                Utils.WriteVector2Array(writer, uv);
            }

            var data = stream.ToArray();
            File.WriteAllBytes(filePath, data);
            writer.Close();

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
#endif
        }

        //读取tile prefab与atlas uv的对应信息
        Dictionary<string, Vector2[]> ImportGroundTileUVAtlasInfo()
        {
            Dictionary<string, Vector2[]> result = null;

#if UNITY_EDITOR
            string filePath = $"{MapModule.groundTileAtlasInfoDir}/{MapCoreDef.MAP_GROUND_TILE_ATLAS_INFO_FILE_NAME}";

            var stream = MapModuleResourceMgr.LoadTextStream(filePath, true);
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);

                int version = reader.ReadInt32();

                int n = reader.ReadInt32();

                result = new Dictionary<string, Vector2[]>(n);
                for (int i = 0; i < n; ++i)
                {
                    string prefabPath = Utils.ReadString(reader);
                    Vector2[] uv = Utils.ReadVector2Array(reader);

                    result.Add(prefabPath, uv);
                }

                reader.Close();
            }
#endif
            return result;
        }
    }
}
#endif