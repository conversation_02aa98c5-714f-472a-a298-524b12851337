﻿using UnityEngine;
using static TFW.Map.MapManager;

namespace TFW.Map
{
    //所有客户端可调用的接口都在这
    public static partial class MapCameraMgr
    {
        public static CameraDrag.CameraState GetCameraDragState()
        {
            var drag = GetGlobalAction(typeof(CameraDrag)) as CameraDrag;
            if (drag != null)
            {
                return drag.state;
            }
            return CameraDrag.CameraState.Invalid;
        }

        public static bool IsCameraDragEnabled()
        {
            var drag = GetGlobalAction(typeof(CameraDrag)) as CameraDrag;
            if (drag != null)
            {
                return drag.on;
            }
            return false;
        }

        //是否能够拖拽相机
        public static void EnableCameraDrag(bool enable)
        {
            var drag = GetGlobalAction(typeof(CameraDrag)) as CameraDrag;
            if (drag != null)
            {
                drag.TurnOn(enable);
            }
        }

        //是否开启相机二指缩放时也可旋转相机
        public static void EnableCameraZoomRotate(bool enable)
        {
            var zoom = GetGlobalAction(typeof(CameraZoomAndPan)) as CameraZoomAndPan;
            if (zoom != null)
            {
                zoom.enableRotate = enable;
            }
        }

        public static void EnableCameraDragRotate(bool enable)
        {
            var drag = GetGlobalAction(typeof(CameraDragRotate)) as CameraDragRotate;
            if (drag != null)
            {
                drag.TurnOn(enable);
            }
        }

        public static void SetCameraDragRotateMaxAngle(float maxAngle)
        {
            var drag = GetGlobalAction(typeof(CameraDragRotate)) as CameraDragRotate;
            if (drag != null)
            {
                drag.maxAngle = maxAngle;
            }
        }

        public static void SetCameraDragRotateSpeedModifyCallback(System.Func<float, float> callback)
        {
            var drag = GetGlobalAction(typeof(CameraDragRotate)) as CameraDragRotate;
            if (drag != null)
            {
                drag.rotateSpeedModifierCallback = callback;
            }
        }

        //设置相机在二指缩放时旋转速率,根据相机高度返回一个速度值
        public static void SetCameraZoomRotateSpeedModifyCallback(System.Func<float, float> callback)
        {
            var zoom = GetGlobalAction(typeof(CameraZoomAndPan)) as CameraZoomAndPan;
            if (zoom != null)
            {
                zoom.rotateSpeedModifierCallback = callback;
            }

#if UNITY_EDITOR
            //pc上通过拖动鼠标右键模拟相机旋转
            var drag = GetGlobalAction(typeof(CameraDrag)) as CameraDrag;
            if (drag != null)
            {
                drag.rotateSpeedModifierCallback = callback;
            }
#endif
        }

        public static void ResetCameraDragRotateCurrentAngle()
        {
            var action = GetGlobalAction(typeof(CameraDragRotate)) as CameraDragRotate;
            if (action != null)
            {
                action.ResetCurrentAngle();
            }
        }

        public static float GetCameraDragRotateMaxAngle()
        {
            var drag = GetGlobalAction(typeof(CameraDragRotate)) as CameraDragRotate;
            if (drag != null)
            {
                return drag.maxAngle;
            }
            return 0;
        }

        public static bool IsCameraZoomEnabled()
        {
            var zoom = GetGlobalAction(typeof(CameraZoomAndPan)) as CameraZoomAndPan;
            if (zoom != null)
            {
                return zoom.on;
            }
            return false;
        }

        public static void EnableCameraZoom(bool enable)
        {
            var zoom = GetGlobalAction(typeof(CameraZoomAndPan)) as CameraZoomAndPan;
            if (zoom != null)
            {
                zoom.on = enable;
            }

            var scroll = GetGlobalAction(typeof(CameraScroll)) as CameraScroll;
            if (scroll != null)
            {
                scroll.on = enable;
            }

            var mayaZoom = GetGlobalAction(typeof(CameraMayaZoom)) as CameraMayaZoom;
            if (mayaZoom != null)
            {
                mayaZoom.on = enable;
            }
        }

        //立即将相机移动到某个地方
        //targetPos: 相机的最终位置
        public static void MoveCameraToTargetInstantly(Vector3 targetPos, System.Action onCameraReachTarget)
        {
            var moveAction = GetGlobalAction(typeof(CameraMoveToTargetInstantly)) as CameraMoveToTargetInstantly;
            if (moveAction != null)
            {
                SetActionCallOrder(moveAction);
                moveAction.MoveTo(targetPos, onCameraReachTarget);
            }
        }

        //立即将相机移动到某个地方
        //相机高度不变
        public static void MoveCameraToTargetInstantly(float x, float z, System.Action onCameraReachTarget)
        {
            var targetPos = new Vector3()
            {
                x = x,
                y = updatedCameraPosition.y,
                z = z
            };
            MoveCameraToTargetInstantly(targetPos, onCameraReachTarget);
        }

        //直线将相机移动到某地
        //targetPos: 相机的最终位置
        public static void MoveCameraStraightToTarget(float lookAtX, float lookAtZ, string cameraSettingName, float moveDuration, float zoomDuration, System.Action onCameraReachTarget, bool clampToBorder = false)
        {
            var moveAction = GetGlobalAction(typeof(CameraMoveStraightToTarget)) as CameraMoveStraightToTarget;
            if (moveAction != null)
            {
                float zoom = MapCameraMgr.GetCameraHeight(cameraSettingName);
                double cameraHeight = Map.currentMap.CalculateCameraHeightFromZoom(zoom);
                var cameraTargetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtX, lookAtZ, (float)cameraHeight);
                moveAction.StartMoving(cameraTargetPos, moveDuration, zoomDuration, onCameraReachTarget, clampToBorder);
                EnableCameraCollisionCheck(false);

                SetActionCallOrder(moveAction);
            }
        }

        public static void MoveCameraToTarget2D(float targetX, float targetZ, float targetOrthographicSize, float moveDuration, float zoomDuration, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false)
        {
            var moveAction = GetGlobalAction(typeof(CameraMoveToTarget2D)) as CameraMoveToTarget2D;
            if (moveAction != null)
            {
                moveAction.StartMoving(targetX, targetZ, targetOrthographicSize, moveDuration, zoomDuration, onCameraReachTarget, clampToBorder, forceMoving);

                SetActionCallOrder(moveAction);
            }
        }

        //移动相机到某个位置
        //targetPos: 相机的目标位置
        //moveDuration: xz方向移动的时间
        //zoomDuration: y方向的移动时间
        //onCameraReachTarget: 相机移动到目标点后调用
        //forceMoving:设置为false情况下如果相机在moving to target过程中会忽略本次调用,true则会移动到新目标
        //clampToBorder:是否将相机中心点限制在地图范围内
        //triggerCameraReachTargetCallbackWhenBeingInterrupted:设置为true,如果相机在moving to target过程中被其他移动命令打断,则会调用上次移动的onCameraReachTarget callback
        public static void MoveCameraToTarget(Vector3 targetPos, float moveDuration, float zoomDuration, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            var moveAction = GetGlobalAction(typeof(CameraMoveToTarget)) as CameraMoveToTarget;
            if (moveAction != null)
            {
                moveAction.StartMoving(targetPos, moveDuration, zoomDuration, onCameraReachTarget, clampToBorder, forceMoving, triggerCameraReachTargetCallbackWhenBeingInterrupted);
                EnableCameraCollisionCheck(false);

                SetActionCallOrder(moveAction);

                if (CameraMoveToTargetInvokeCallback != null)
                {
                    CameraMoveToTargetInvokeCallback();
                }
            }
        }

        //移动相机到某个位置
        //targetPos: 相机的目标位置
        //moveDuration: xz方向移动的时间
        //zoomDuration: y方向的移动时间
        //moveCurve: 调制归一化移动时间的curve,curve的x轴在0-1之间,y轴也要在0-1之间
        //onCameraReachTarget: 相机移动到目标点后调用
        //forceMoving:设置为false情况下如果相机在moving to target过程中会忽略本次调用,true则会移动到新目标
        //clampToBorder:是否将相机中心点限制在地图范围内
        //triggerCameraReachTargetCallbackWhenBeingInterrupted:设置为true,如果相机在moving to target过程中被其他移动命令打断,则会调用上次移动的onCameraReachTarget callback
        public static void MoveCameraToTargetByCurve(Vector3 targetPos, float moveDuration, float zoomDuration, AnimationCurve moveCurve, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            var moveAction = GetGlobalAction(typeof(CameraMoveToTarget)) as CameraMoveToTarget;
            if (moveAction != null)
            {
                moveAction.StartMovingByCurve(targetPos, moveDuration, zoomDuration, moveCurve, onCameraReachTarget, clampToBorder, forceMoving, triggerCameraReachTargetCallbackWhenBeingInterrupted);
                EnableCameraCollisionCheck(false);

                SetActionCallOrder(moveAction);

                if (CameraMoveToTargetInvokeCallback != null)
                {
                    CameraMoveToTargetInvokeCallback();
                }
            }
        }

        //相机移动到观察点的位置
        //lookAtPosX: 观察点的x坐标
        //lookAtPosZ: 观察点的z坐标
        //zoom: 无极缩放的LOD值
        //moveDuration: xz方向移动的时间
        //zoomDuration: y方向的移动时间
        //onCameraReachTarget: 相机移动到目标点后调用
        //forceMoving:设置为false情况下如果相机在moving to target过程中会忽略本次调用,true则会移动到新目标
        //clampToBorder:是否将相机中心点限制在地图范围内
        //triggerCameraReachTargetCallbackWhenBeingInterrupted:设置为true,如果相机在moving to target过程中被其他移动命令打断,则会调用上次移动的onCameraReachTarget callback
        public static void MoveCameraToTarget(float lookAtPosX, float lookAtPosZ, float zoom, float moveDuration, float zoomDuration, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            if (Map.currentMap == null)
                return;
            double cameraHeight = Map.currentMap.CalculateCameraHeightFromZoom(zoom);
            var cameraTargetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtPosX, lookAtPosZ, (float)cameraHeight);
            MoveCameraToTarget(cameraTargetPos, moveDuration, zoomDuration, onCameraReachTarget, forceMoving, clampToBorder, triggerCameraReachTargetCallbackWhenBeingInterrupted);
        }

        public static void MoveCameraToTargetByCurve(float lookAtPosX, float lookAtPosZ, float zoom, float moveDuration, float zoomDuration, AnimationCurve moveCurve, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            if (Map.currentMap == null)
                return;
            double cameraHeight = Map.currentMap.CalculateCameraHeightFromZoom(zoom);
            var cameraTargetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtPosX, lookAtPosZ, (float)cameraHeight);
            MoveCameraToTargetByCurve(cameraTargetPos, moveDuration, zoomDuration, moveCurve, onCameraReachTarget, forceMoving, clampToBorder, triggerCameraReachTargetCallbackWhenBeingInterrupted);
        }

        public static void MoveCameraToTarget(float lookAtPosX, float lookAtPosZ, string cameraSettingName, float moveDuration, float zoomDuration, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            if (Map.currentMap == null)
                return;

            float zoom = GetCameraZoom(cameraSettingName);

            double cameraHeight = Map.currentMap.CalculateCameraHeightFromZoom(zoom);
            var cameraTargetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtPosX, lookAtPosZ, (float)cameraHeight);
            MoveCameraToTarget(cameraTargetPos, moveDuration, zoomDuration, onCameraReachTarget, forceMoving, clampToBorder, triggerCameraReachTargetCallbackWhenBeingInterrupted);
        }

        public static void MoveCameraToTargetByCurve(float lookAtPosX, float lookAtPosZ, string cameraSettingName, float moveDuration, float zoomDuration, AnimationCurve moveCurve, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            if (Map.currentMap == null)
                return;
            float zoom = GetCameraZoom(cameraSettingName);

            double cameraHeight = Map.currentMap.CalculateCameraHeightFromZoom(zoom);
            var cameraTargetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtPosX, lookAtPosZ, (float)cameraHeight);
            MoveCameraToTargetByCurve(cameraTargetPos, moveDuration, zoomDuration, moveCurve, onCameraReachTarget, forceMoving, clampToBorder, triggerCameraReachTargetCallbackWhenBeingInterrupted);
        }

        public static void MoveCameraToTargetLookAtInstantly(float lookAtX, float lookAtZ, System.Action onCameraReachToTarget)
        {
            if (Map.currentMap == null)
                return;
            var targetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtX, lookAtZ, MapCameraMgr.updatedCameraPosition.y);
            MoveCameraToTargetInstantly(targetPos, onCameraReachToTarget);
        }

        public static void MoveCameraToTargetRevHeight(float lookAtPosX, float lookAtPosZ, float cameraHeight, float moveDuration, float zoomDuration, System.Action onCameraReachTarget)
        {
            if (Map.currentMap == null)
                return;
            var moveAction = GetGlobalAction(typeof(CameraMoveToTargetRev)) as CameraMoveToTargetRev;
            if (moveAction != null)
            {
                var cameraTargetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtPosX, lookAtPosZ, (float)cameraHeight);
                moveAction.StartMoving(cameraTargetPos, moveDuration, zoomDuration, onCameraReachTarget);

                SetActionCallOrder(moveAction);

                if (CameraMoveToTargetRevInvokeCallback != null)
                {
                    CameraMoveToTargetRevInvokeCallback();
                }
            }
        }

        //先缩放并移动到目标
        public static void MoveCameraToTargetRev(float lookAtPosX, float lookAtPosZ, string cameraSettingName, float moveDuration, float zoomDuration, System.Action onCameraReachTarget)
        {
            var moveAction = GetGlobalAction(typeof(CameraMoveToTargetRev)) as CameraMoveToTargetRev;
            if (moveAction != null)
            {
                float zoom = GetCameraZoom(cameraSettingName);
                double cameraHeight = Map.currentMap.CalculateCameraHeightFromZoom(zoom);
                var cameraTargetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtPosX, lookAtPosZ, (float)cameraHeight);
                moveAction.StartMoving(cameraTargetPos, moveDuration, zoomDuration, onCameraReachTarget);

                SetActionCallOrder(moveAction);

                if (CameraMoveToTargetRevInvokeCallback != null)
                {
                    CameraMoveToTargetRevInvokeCallback();
                }
            }
        }

        //先缩放并移动到目标
        public static void MoveCameraToTargetRev(float lookAtPosX, float lookAtPosZ, float zoom, float moveDuration, float zoomDuration, System.Action onCameraReachTarget)
        {
            var moveAction = GetGlobalAction(typeof(CameraMoveToTargetRev)) as CameraMoveToTargetRev;
            if (moveAction != null)
            {
                double cameraHeight = Map.currentMap.CalculateCameraHeightFromZoom(zoom);
                var cameraTargetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(lookAtPosX, lookAtPosZ, (float)cameraHeight);
                moveAction.StartMoving(cameraTargetPos, moveDuration, zoomDuration, onCameraReachTarget);

                SetActionCallOrder(moveAction);

                if (CameraMoveToTargetRevInvokeCallback != null)
                {
                    CameraMoveToTargetRevInvokeCallback();
                }
            }
        }

        /// <summary>
        /// 单纯XZ平移, 相机高度不变
        /// <param name="x">X位置</param>
        /// <param name="z">z位置</param>
        /// <param name="duration">移动耗时</param>
        /// </summary>
        public static void MoveCameraToTarget(float x, float z, float duration, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            if (Map.currentMap == null)
                return;
            var updateCameraPos = updatedCameraPosition;
            float zoom = Map.currentMap.CalculateCameraZoom(updateCameraPos.y);
            MoveCameraToTarget(x, z, zoom, duration, 0, onCameraReachTarget, forceMoving, clampToBorder, triggerCameraReachTargetCallbackWhenBeingInterrupted);
        }

        public static void MoveCameraToTargetByCurve(float x, float z, float duration, AnimationCurve moveCurve, System.Action onCameraReachTarget, bool forceMoving = false, bool clampToBorder = false, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            var updateCameraPos = updatedCameraPosition;
            float zoom = Map.currentMap.CalculateCameraZoom(updateCameraPos.y);
            MoveCameraToTargetByCurve(x, z, zoom, duration, 0, moveCurve, onCameraReachTarget, forceMoving, clampToBorder, triggerCameraReachTargetCallbackWhenBeingInterrupted);
        }

        //开始跟随,这里是先追赶目标,使目标在窗口居中,然后开始跟随
        //x,z:目标的x,z坐标,这里不会缩放相机高度
        //catchDuration:追赶目标的间隔时间,如果fixedCatchSpeed不为0,则使用fixedCatchSpeed作为追赶速度
        //fixedCatchSpeed:固定的相机追赶速度
        public static void StartFollowTarget(float x, float z, float catchDuration, float fixedCatchSpeed = 0)
        {
            var followAction = GetGlobalAction(typeof(CameraFollowTarget)) as CameraFollowTarget;
            if (followAction != null)
            {
                followAction.StartFollow(x, z, catchDuration, fixedCatchSpeed);
            }
        }

        //开始跟随
        //x,y,z: 目标点的坐标
        //lookAtHeight: 相机跟随目标点时与目标点的相对高度
        //catchDuration:追赶目标的间隔时间,如果fixedCatchSpeed不为0,则使用fixedCatchSpeed作为追赶速度
        //fixedCatchSpeed:固定的相机追赶速度
        public static void StartFollowTarget(float x, float y, float z, float catchDuration, float lookAtHeight, float fixedCatchSpeed = 0)
        {
            var followAction = GetGlobalAction(typeof(CameraFollowTarget)) as CameraFollowTarget;
            if (followAction != null)
            {
                followAction.StartFollow(x, y, z, catchDuration, lookAtHeight, fixedCatchSpeed);
            }
        }

        //保持跟随目标,需要在跟随期间每帧调用此函数
        public static void UpdateFollowTarget(float x, float z)
        {
            var followAction = GetGlobalAction(typeof(CameraFollowTarget)) as CameraFollowTarget;
            if (followAction != null)
            {
                followAction.UpdateTarget(x, z);
            }
        }

        //x,y,z:目标点的坐标
        public static void UpdateFollowTarget(float x, float y, float z, float lookAtHeight = 0)
        {
            var followAction = GetGlobalAction(typeof(CameraFollowTarget)) as CameraFollowTarget;
            if (followAction != null)
            {
                followAction.UpdateTarget(x, y, z, lookAtHeight);
            }
        }

        //停止跟随目标
        public static void StopFollowTarget()
        {
            var followAction = GetGlobalAction(typeof(CameraFollowTarget)) as CameraFollowTarget;
            if (followAction != null)
            {
                followAction.Cancel();
            }
        }

        public static void EnableDirMove(Vector3 dir)
        {
            var action = GetGlobalAction(typeof(CameraMoveByDir)) as CameraMoveByDir;
            if (action != null)
            {
                action.dir = dir;
                action.enabled = true;
            }
        }

        public static void StopDirMove()
        {
            var action = GetGlobalAction(typeof(CameraMoveByDir)) as CameraMoveByDir;
            if (action != null) action.enabled = false;
        }

        //是否开启相机在主城的自动升高
        public static void EnableCameraHeightAutoUpdate(bool enable)
        {
            var action = GetGlobalAction(typeof(CameraAutoUpdateHeight)) as CameraAutoUpdateHeight;
            if (action != null)
            {
                action.on = enable;
            }
        }

        //是否在跟随目标
        public static bool IsFollowing()
        {
            var followAction = GetGlobalAction(typeof(CameraFollowTarget)) as CameraFollowTarget;
            if (followAction != null)
            {
                return followAction.enabled;
            }

            return false;
        }

        //相机震动
        public static void Shake(float duration, AnimationCurve rangeCurve, AnimationCurve speedCurve)
        {
            var shakeAction = GetLocalAction(typeof(CameraShake)) as CameraShake;
            if (shakeAction != null)
            {
                shakeAction.StartShaking(duration, rangeCurve, speedCurve);
            }
        }

        //相机震动
        public static void Shake(float duration, float range, float speed)
        {
            var shakeAction = GetLocalAction(typeof(CameraShake)) as CameraShake;
            if (shakeAction != null)
            {
                shakeAction.StartShaking(duration, range, speed);
            }
        }

        public static float GetCameraZoom(string cameraSettingName)
        {
            if (mActiveCameraSetting != null)
            {
                var setting = mActiveCameraSetting?.GetCameraHeightSetting(cameraSettingName);
                if (setting != null && Map.currentMap != null)
                {
                    return Map.currentMap.CalculateCameraZoom(setting.cameraHeight);
                }
            }

            return 0;
        }

        public static float GetCameraHeight(string cameraSettingName)
        {
            if (mActiveCameraSetting != null)
            {
                var setting = mActiveCameraSetting.GetCameraHeightSetting(cameraSettingName);
                if (setting != null)
                {
                    return setting.cameraHeight;
                }
            }

            return 0;
        }

        public static void SetCameraDragBounceRange(float range)
        {
            var drag = GetGlobalAction(CameraActionType.Drag) as CameraDrag;
            if (drag != null)
            {
                drag.bounceAreaRange = range;
            }
        }

        public static void SetCameraDragBounceDuration(float duration)
        {
            var drag = GetGlobalAction(CameraActionType.Drag) as CameraDrag;
            if (drag != null)
            {
                drag.bounceDuration = duration;
            }
        }

        public static void SetCameraDragBounceMinimumSpeed(float speed)
        {
            var drag = GetGlobalAction(CameraActionType.Drag) as CameraDrag;
            if (drag != null)
            {
                drag.bounceMinimumSpeed = speed;
            }
        }

        public static MapCameraSetting GetMapCameraSetting(string name)
        {
            mCameraHeightSettings.TryGetValue(name, out MapCameraSetting setting);
            if (setting != null)
            {
                return setting;
            }
            else
            {
                if (mCameraHeightSettings.TryGetValue("camera_setting", out setting))
                {
                    //使用默认相机设置
                    UnityEngine.Debug.Log($"GetcameraSetting NUll {name} Dic: {Newtonsoft.Json.JsonConvert.SerializeObject(mCameraHeightSettings)}");
                }
            }
          
            return setting; 
        }

        //注意改变camera setting要在不同的环境下,直接切换可能会导致一些跳变问题
        public static void SetActiveCameraSetting(string name)
        {
            MapCameraSetting setting;
            mCameraHeightSettings.TryGetValue(name, out setting);
             
            if (setting != null)
            {
                SetActiveCameraSetting(setting);
            }
            else
            {
                Debug.LogError($"Invalid camera setting {name}");
            }
        }

        //是否开启相机与地编导出的碰撞体碰撞
        public static void EnableCameraCollisionCheck(bool enable)
        {
            var action = GetGlobalAction(typeof(CameraCollisionResolve)) as CameraCollisionResolve;
            if (action != null)
            {
                action.EnableCollisionCheck(enable);
            }
        }

        //设置相机可以移动的范围,lookAtMinX, lookAtMinZ, lookAtMaxX, lookAtMaxZ都是xz平面的坐标,这里是通过限制相机视野中心点的范围来限制相机的移动范围
        public static void SetCameraMoveLookAtRange(float lookAtMinX, float lookAtMinZ, float lookAtMaxX, float lookAtMaxZ)
        {
            mBorderClamp.SetLookAtRange(lookAtMinX, lookAtMinZ, lookAtMaxX, lookAtMaxZ);
        }

        public static Rect GetCameraMoveLookAtRange()
        {
            return mBorderClamp.GetLookAtRange();
        }

        //取消相机移动范围限制
        public static void ClearCameraMoveLookAtRange()
        {
            mBorderClamp.ClearLookAtRange();
        }

        //设置相机缩放的高度限制
        //cameraMinHeight:相机最低高度
        //cameraMaxHeight:相机最高高度
        public static void SetCameraZoomRange(float cameraMinHeight, float cameraMaxHeight)
        {
            mActiveCameraSetting.SetCustomCameraHeightRange(cameraMinHeight, cameraMaxHeight);
        }

        //取消相机的缩放高度限制
        public static void ClearCameraZoomRange()
        {
            mActiveCameraSetting.ClearCustomCameraHeightRange();
        }

        public static void SetUpdateCallback(System.Action<CameraAction, int> callback)
        {
            mUpdateCallback = callback;
        }

        public static void SetActive(bool active)
        {
            mActive = active;
            MapCamera.enabled = active;
        }

        public static void EnableAudioListener(bool enable)
        {
            MapCamera.enableAudioListener = enable;
        }

        public static void SetCameraZoomScrollRateModifyCallback(System.Func<float, float> callback)
        {
            var action = GetGlobalAction(CameraActionType.Zoom) as CameraZoomAndPan;
            if (action != null)
            {
                action.SetScrollRateModifyCallback(callback);
            }
        }

        //设置从哪个高度开始切换相机的缩放算法
        public static void SetCameraZoomAlgorithmSwitchHeight(float height)
        {
            mZoomAlgorithmSwitchHeight = height;
            var action = GetGlobalAction(CameraActionType.Zoom) as CameraZoomAndPan;
            if (action != null)
            {
                action.zoomAlgorithmSwitchHeight = height;
            }
        }

        public static void SetCameraScrollScrollRateModifyCallback(System.Func<float, float> callback)
        {
            var action = GetGlobalAction(CameraActionType.Scroll) as CameraScroll;
            if (action != null)
            {
                action.SetScrollRateModifyCallback(callback);
            }
        }

        //forwardDistanceCurve:相机在从maxCurveMovementHeight到最低高度时往前移动的偏移值,time = 0表示相机在最低点,time = 1表示相机在最高点
        //maxCurveMovementHeight:超过这个高度后相机的x旋转角度保持在最大值,不再改变
        public static void SetCameraXAngleChangeParameter(AnimationCurve forwardDistanceCurve, float maxCurveMovementHeight)
        {
            mCurveMovementSetting.forwardOffsetCurveBelowSplitHeight = forwardDistanceCurve;
            mCurveMovementSetting.splitHeight = maxCurveMovementHeight;
            CalculateInitCameraPose();
        }

        public static void SetCameraXAngleChangeParameter(AnimationCurve forwardDistanceCurveBelow, AnimationCurve forwardDistanceCurveAbove, float curveMovementSplitHeight, float curveMovementMaxHeight, float xRotationWhenReachCurveMovementMaxHeight)
        {
            mCurveMovementSetting.forwardOffsetCurveBelowSplitHeight = forwardDistanceCurveBelow;
            mCurveMovementSetting.forwardOffsetCurveAboveSplitHeight = forwardDistanceCurveAbove;
            mCurveMovementSetting.splitHeight = curveMovementSplitHeight;
            mCurveMovementSetting.maxHeight = curveMovementMaxHeight;
            mCurveMovementSetting.xRotationAtMaxHeight = xRotationWhenReachCurveMovementMaxHeight;
            CalculateInitCameraPose();
        }

        //如果相机在moving to target时又触发了moving to target,会调用这个callback
        public static void SetCameraMovingToTargetReentranceCallback(System.Action callback)
        {
            var action = GetGlobalAction(CameraActionType.MoveToTarget) as CameraMoveToTarget;
            if (action != null)
            {
                action.reentranceCallback = callback;
            }
        }

        public static MapCameraSetting cameraSetting { get { return mActiveCameraSetting; } }
        public static float lastCameraHeight { get { return mLastCameraHeight; } }
        public static float currentCameraHeight { get { return mCurrentCameraHeight; } }
        public static float currentMinimumHeight
        {
            get { return mCurrentMinimumHeight; }
            set
            {
                mCurrentMinimumHeight = value;
            }
        }
        public static bool clampBorder
        {
            set
            {
                mClampBorder = value;
            }
            get { return mClampBorder; }
        }
        public static CameraStatistics statistics { get { return mStatistics; } }
        public static bool enableUpdateFarClipPlane { set { mEnableUpdateFarClipPlane = value; } get { return mEnableUpdateFarClipPlane; } }
        public static bool enableUpdateNearClipPlane { set { mEnableUpdateNearClipPlane = value; } get { return mEnableUpdateNearClipPlane; } }
        public static bool isMovingToTarget { set { mIsMovingToTarget = value; } get { return mIsMovingToTarget; } }
        public static float bounceRange { set { mBorderClamp.SetBounceRange(value); } get { return mBorderClamp.GetBounceRange(); } }
        public static bool enableClampTopDownView { get { return mEnableClampTopDownView; } set { mEnableClampTopDownView = value; } }
        public static bool enableCameraXAngleChange { get { return mEnableCameraXAngleChange; } 
            set 
            { 
                mEnableCameraXAngleChange = value;
                if (mEnableCameraXAngleChange)
                {
                    if (mCameraZoomCurveSetting != null)
                    {
                        SetCameraXAngleChangeParameter(cameraZoomCurveSetting.forwardDistanceCurveBelow, cameraZoomCurveSetting.forwardDistanceCurveAbove, cameraZoomCurveSetting.curveMovementSplitHeight, cameraZoomCurveSetting.curveMovementMaxHeight, cameraZoomCurveSetting.xRotationWhenReachCurveMovementMaxHeight);
                    }
                }
                else
                {
                    SetCameraXAngleChangeParameter(null, 0);
                }
            }
        }
        // 地图相机
        public static GameObject MapCameraRoot { get; private set; }
        public static MapCameras MapCamera { get { return mMapCameras; } }
        public static Vector3 updatedCameraPosition { get { return mUpdatedCameraPosition; } }
        public static bool isDragging { get { return mIsDragging; } internal set { mIsDragging = value; } }

        //调用相机move to target时触发
        public static event System.Action CameraMoveToTargetInvokeCallback;
        //调用相机move to target rev时触发
        public static event System.Action CameraMoveToTargetRevInvokeCallback;
        //相机开始移动时回调
        public static event System.Action CameraStartMoveToTargetCallback;
        //相机停止移动时回调
        public static event System.Action CameraStopMoveToTargetCallback;
        public static event System.Action CameraStartMoveToTargetRevCallback;
        public static event System.Action CameraStopMoveToTargetRevCallback;
        //相机停止跟随目标时回调
        public static event System.Action CameraStopFollowTargetCallback;
    }
}
