%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6141504012803429193
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4002669434343944360}
  - component: {fileID: 3953799254229088368}
  m_Layer: 0
  m_Name: ObstacleOutline 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4002669434343944360
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6141504012803429193}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3953799254229088368
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6141504012803429193}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -89.33595, y: 0, z: 15.90481}
    - {x: -69.59531, y: 0, z: 17.11655}
    - {x: -64.498276, y: 0, z: 7.8549347}
    - {x: -72.05036, y: 0, z: 1.7657758}
    - {x: -88.951584, y: 0, z: 7.889525}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -73.6302, y: 0, z: -0.4704}
    - {x: -74.9279, y: 0, z: -0.2261}
    - {x: -75.1868, y: 0, z: -0.1528}
    - {x: -87.5332, y: 0, z: 4.5703}
    - {x: -87.7714, y: 0, z: 4.6864}
    - {x: -90, y: 0, z: 6.1574}
    - {x: -90, y: 0, z: 7.115}
    - {x: -90, y: 0, z: 8.895}
    - {x: -90, y: 0, z: 9.0247}
    - {x: -90, y: 0, z: 11.0853}
    - {x: -90, y: 0, z: 11.8189}
    - {x: -90, y: 0, z: 13.86}
    - {x: -90, y: 0, z: 14.0387}
    - {x: -90, y: 0, z: 14.8101}
    - {x: -90, y: 0, z: 14.9742}
    - {x: -89.4658, y: 0, z: 16.3782}
    - {x: -88.5504, y: 0, z: 17.0241}
    - {x: -79.4326, y: 0, z: 19.3068}
    - {x: -78.9906, y: 0, z: 19.3501}
    - {x: -71.4824, y: 0, z: 18.9727}
    - {x: -71.1332, y: 0, z: 18.9134}
    - {x: -68.2453, y: 0, z: 18.0639}
    - {x: -67.5848, y: 0, z: 17.6665}
    - {x: -65.7631, y: 0, z: 15.8013}
    - {x: -65.6561, y: 0, z: 15.6792}
    - {x: -64.8842, y: 0, z: 14.6956}
    - {x: -64.7397, y: 0, z: 14.4738}
    - {x: -62.2145, y: 0, z: 9.7284}
    - {x: -62.0845, y: 0, z: 9.3925}
    - {x: -61.6899, y: 0, z: 7.8422}
    - {x: -62.0208, y: 0, z: 6.4192}
    - {x: -63.8098, y: 0, z: 4.3216}
    - {x: -63.9906, y: 0, z: 4.1427}
    - {x: -65.5041, y: 0, z: 2.8804}
    - {x: -65.7595, y: 0, z: 2.7085}
    - {x: -69.0083, y: 0, z: 0.9752}
    - {x: -69.2103, y: 0, z: 0.8859}
    - {x: -72.8345, y: 0, z: -0.4079}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
