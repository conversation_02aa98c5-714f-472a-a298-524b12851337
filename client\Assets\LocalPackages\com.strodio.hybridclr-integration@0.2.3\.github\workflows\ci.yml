name: CI
on:
  push:
    branches:
    - main
jobs:
  release:
    name: release
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Action For Semantic Release
      uses: cycjimmy/semantic-release-action@v4
      with:
        extra_plugins: |
          @semantic-release/changelog
          @semantic-release/git
        branch: main
      env:
        GITHUB_TOKEN: ${{ secrets.CI_TOKEN }}