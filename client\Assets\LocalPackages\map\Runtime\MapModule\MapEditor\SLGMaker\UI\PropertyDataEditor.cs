﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class PropertyDataEditor
    {
        public PropertyDataEditor(bool canEdit)
        {
            mTypes = new string[] {
                "int",
                "float",
                "string",
                "bool",
                "Vector2",
                "Vector3",
                "Vector4",
                "Color",
                "int array",
            };
            mCanEdit = canEdit;
        }

        public void Show(PropertyDatas properties, System.Action<PropertyBase> onAddProperty, System.Action<PropertyBase> onRemoveProperty, System.Action<string, PropertyBase> onRenameProperty)
        {
            mProperties = properties;
            mOnAddProperty = onAddProperty;
            mOnRemoveProperty = onRemoveProperty;
            mOnRenameProperty = onRenameProperty;
        }

        public void Draw()
        {
            mVisible = EditorGUILayout.Foldout(mVisible, "Properties");
            if (mVisible)
            {
                DrawPropertyListUI();

                if (mCanEdit)
                {
                    EditorGUILayout.BeginVertical("GroupBox");
                    mPropertyName = EditorGUILayout.TextField("Property Name", mPropertyName);
                    mSelectedType = EditorGUILayout.Popup("Property Type", mSelectedType, mTypes);

                    EditorGUILayout.BeginHorizontal();
                    
                    if (GUILayout.Button("Add Property"))
                    {
                        AddProperty();
                    }
                    
                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.EndVertical();
                }
            }
        }

        void AddProperty()
        {
            if (mProperties == null)
            {
                return;
            }
            if (mPropertyName.Length > 0)
            {
                if (mPropertyName.Length > 0 && mProperties.FindProperty(mPropertyName) == null)
                {
                    PropertyBase prop = null;
                    switch (mTypes[mSelectedType])
                    {
                        case "int":
                            prop = new PropertyData<int>(mPropertyName, PropertyType.kPropertyInt, 0);
                            break;
                        case "int array":
                            prop = new PropertyData<int[]>(mPropertyName, PropertyType.kPropertyIntArray, new int[0]);
                            break;
                        case "float":
                            prop = new PropertyData<float>(mPropertyName, PropertyType.kPropertyFloat, 0);
                            break;
                        case "string":
                            prop = new PropertyData<string>(mPropertyName, PropertyType.kPropertyString, "");
                            break;
                        case "bool":
                            prop = new PropertyData<bool>(mPropertyName, PropertyType.kPropertyBool, false);
                            break;
                        case "Vector2":
                            prop = new PropertyData<Vector2>(mPropertyName, PropertyType.kPropertyVector2, Vector2.zero);
                            break;
                        case "Vector3":
                            prop = new PropertyData<Vector3>(mPropertyName, PropertyType.kPropertyVector3, Vector3.zero);
                            break;
                        case "Vector4":
                            prop = new PropertyData<Vector4>(mPropertyName, PropertyType.kPropertyVector4, Vector4.zero);
                            break;
                        case "Color":
                            prop = new PropertyData<Color>(mPropertyName, PropertyType.kPropertyColor, Color.white);
                            break;
                        default:
                            Debug.Assert(false, "unknown property type!");
                            break;
                    }
                    if (prop != null)
                    {
                        mProperties.AddProperty(prop);

                        if (mOnAddProperty != null)
                        {
                            mOnAddProperty(prop);
                        }
                        return;
                    }
                }
            }

            EditorUtility.DisplayDialog("Error", "Input valid property name", "OK");
        }

        void DrawPropertyListUI()
        {
            EditorGUIUtility.labelWidth = 150;
            if (mProperties != null)
            {
                var properties = mProperties.properties;
                if (properties.Count > 0)
                {
                    EditorGUILayout.BeginVertical("GroupBox");
                    for (int i = 0; i < properties.Count; ++i)
                    {
                        if (properties[i].GetType() == typeof(PropertyData<int>))
                        {
                            DrawIntProperty(properties[i].name, properties[i] as PropertyData<int>, mProperties);
                        }
                        else if (properties[i].GetType() == typeof(PropertyData<int[]>))
                        {
                            DrawIntArrayProperty(properties[i].name, properties[i] as PropertyData<int[]>, mProperties);
                        }
                        else if (properties[i].GetType() == typeof(PropertyData<float>))
                        {
                            DrawFloatProperty(properties[i].name, properties[i] as PropertyData<float>, mProperties);
                        }
                        else if (properties[i].GetType() == typeof(PropertyData<string>))
                        {
                            DrawStringProperty(properties[i].name, properties[i] as PropertyData<string>, mProperties);
                        }
                        else if (properties[i].GetType() == typeof(PropertyData<Vector2>))
                        {
                            DrawVector2Property(properties[i].name, properties[i] as PropertyData<Vector2>, mProperties);
                        }
                        else if (properties[i].GetType() == typeof(PropertyData<Vector3>))
                        {
                            DrawVector3Property(properties[i].name, properties[i] as PropertyData<Vector3>, mProperties);
                        }
                        else if (properties[i].GetType() == typeof(PropertyData<Vector4>))
                        {
                            DrawVector4Property(properties[i].name, properties[i] as PropertyData<Vector4>, mProperties);
                        }
                        else if (properties[i].GetType() == typeof(PropertyData<Color>))
                        {
                            DrawColorProperty(properties[i].name, properties[i] as PropertyData<Color>, mProperties);
                        }
                        else if (properties[i].GetType() == typeof(PropertyData<bool>))
                        {
                            DrawBoolProperty(properties[i].name, properties[i] as PropertyData<bool>, mProperties);
                        }
                        else
                        {
                            Debug.Assert(false);
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }
            EditorGUIUtility.labelWidth = 0;
        }

        void AddPropertyButtons(PropertyBase property, PropertyDatas properties)
        {
            if (mCanEdit)
            {
                if (GUILayout.Button("Rename", GUILayout.Width(80)))
                {
                    if (!string.IsNullOrEmpty(mPropertyName) && properties.FindProperty(mPropertyName) == null)
                    {
                        string oldName = property.name;
                        property.name = mPropertyName;
                        if (mOnRenameProperty != null)
                        {
                            mOnRenameProperty(oldName, property);
                        }
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Invalid property name", "OK");
                    }
                }
                if (GUILayout.Button("Remove", GUILayout.Width(80)))
                {
                    if (EditorUtility.DisplayDialog("Remove Property", "Are you sure ?", "Yes", "No"))
                    {
                        mProperties.RemoveProperty(property);
                        if (mOnRemoveProperty != null)
                        {
                            mOnRemoveProperty(property);
                        }
                    }
                }
            }
        }

        void DrawIntProperty(string name, PropertyData<int> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.IntField(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawIntArrayProperty(string name, PropertyData<int[]> property, PropertyDatas properties)
        {
            var value = property.value;
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(name);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
            EditorGUI.indentLevel++;
            int newSize = EditorGUILayout.IntField("Array Size", value.Length);
            newSize = Mathf.Max(0, newSize);
            if (newSize != value.Length)
            {
                int minSize = Mathf.Min(newSize, value.Length);
                var newValues = new int[newSize];
                for (int i = 0; i < minSize; ++i)
                {
                    newValues[i] = value[i];
                }
                property.value = newValues;
            }
            for (int i = 0; i < newSize; ++i)
            {
                EditorGUI.indentLevel++;
                property.value[i] = EditorGUILayout.IntField($"Value {i}", property.value[i]);
                EditorGUI.indentLevel--;
            }
            EditorGUI.indentLevel--;
        }

        void DrawFloatProperty(string name, PropertyData<float> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.FloatField(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawStringProperty(string name, PropertyData<string> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.TextField(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawVector2Property(string name, PropertyData<Vector2> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.Vector2Field(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawVector3Property(string name, PropertyData<Vector3> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.Vector3Field(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawVector4Property(string name, PropertyData<Vector4> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.Vector4Field(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawColorProperty(string name, PropertyData<Color> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.ColorField(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawBoolProperty(string name, PropertyData<bool> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.Toggle(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        public PropertyDatas properties { get { return mProperties; } }

        int mSelectedType = 0;
        string mPropertyName = "";
        string mPropertyNameString;
        string[] mTypes;
        PropertyDatas mProperties;
        System.Action<PropertyBase> mOnAddProperty;
        System.Action<PropertyBase> mOnRemoveProperty;
        System.Action<string, PropertyBase> mOnRenameProperty;
        bool mVisible = true;
        bool mCanEdit;
    }
}

#endif