﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;

using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Diagnostics;
using TFW.Map.Geo;

namespace TFW.Map
{
    [CustomEditor(typeof(EditorTerritoryLayerLogic))]
    public partial class EditorTerritoryLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as EditorTerritoryLayerLogic;
            if (mLogic.layer.subLayers.Count > 0 && mLogic.selectedSubLayerIndex == -1)
            {
                SetSelectedSubLayer(0);
            }
        }

        void DrawLine(int i_height = 1)
        {
            Rect rect = EditorGUILayout.GetControlRect(false, i_height);

            rect.height = i_height;

            EditorGUI.DrawRect(rect, new Color(0.5f, 0.5f, 0.5f, 1));
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                var layer = mLogic.layer;
                var subLayer = layer.GetSubLayer(mLogic.selectedSubLayerIndex);
                if (mLogic.selectedSubLayerIndex >= 0 && subLayer.selectedIndex == -1 && layer.GetTerritoryCount(mLogic.selectedSubLayerIndex) > 0)
                {
                    SetActiveTerritory(0);
                }

                //create layer names
                var subLayers = layer.subLayers;
                if (mLayerNames == null || mLayerNames.Length != subLayers.Count)
                {
                    mLayerNames = new string[subLayers.Count];
                }
                for (int i = 0; i < subLayers.Count; ++i)
                {
                    mLayerNames[i] = subLayers[i].name;
                }

                if (mLODNames == null || mLODNames.Length != layer.lodCount)
                {
                    CreateLODNames();
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add Layer"))
                {
                    AddSubLayer();
                }
                if (GUILayout.Button("Remove Layer"))
                {
                    RemoveSubLayer();
                }
                
                EditorGUILayout.EndHorizontal();
                int newSelectedLayer = EditorGUILayout.Popup("Layer", mLogic.selectedSubLayerIndex, mLayerNames);
                SetSelectedSubLayer(newSelectedLayer);
                DrawLine();

                subLayer.brushSize = EditorGUILayout.IntField("Brush Size", subLayer.brushSize);
                subLayer.brushSize = Mathf.Clamp(subLayer.brushSize, 1, 200);

                subLayer.operation = (EditorTerritoryLayerOperation)EditorGUILayout.EnumPopup("Operation", subLayer.operation);

                float oldRadius = layer.GetDisplayRadius(mLogic.selectedSubLayerIndex);
                float newRadius = EditorGUILayout.FloatField("Display Radius", oldRadius);
                if (!Mathf.Approximately(newRadius, oldRadius))
                {
                    layer.SetDisplayRadius(mLogic.selectedSubLayerIndex, newRadius);
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Export Config"))
                {
                    layer.Export(mLogic.selectedSubLayerIndex, SLGMakerEditor.instance.projectFolder);
                }

                if (GUILayout.Button("Import Config"))
                {
                    string filePath = EditorUtility.OpenFilePanel("Select config file", "", "tsv");
                    if (!string.IsNullOrEmpty(filePath))
                    {
                        layer.Import(mLogic.selectedSubLayerIndex, filePath);
                    }
                }
                EditorGUILayout.EndHorizontal();

                mLogic.showMeshGeneration = EditorGUILayout.Foldout(mLogic.showMeshGeneration, "Mesh Generation");
                if (mLogic.showMeshGeneration)
                {
                    ++EditorGUI.indentLevel;

                    subLayer.selectedLODIndex = EditorGUILayout.Popup("LOD", subLayer.selectedLODIndex, mLODNames);

                    var curveParam = subLayer.curveRegionMeshGenerationParam;
                    var param = curveParam.lodParams[subLayer.selectedLODIndex];
                    param.lineWidth = EditorGUILayout.FloatField(new GUIContent("Line Width", "边界线宽度"), param.lineWidth);
                    param.gridErrorThreshold = EditorGUILayout.FloatField(new GUIContent("Grid Error Threshold", "格子误差数,越大轮廓约平滑"), param.gridErrorThreshold);
                    param.maxPointCountInOneSegment = EditorGUILayout.IntField(new GUIContent("Max Point Count In One Segment", "一段线段的最大顶点数"), param.maxPointCountInOneSegment);
                    param.textureAspectRatio = EditorGUILayout.FloatField(new GUIContent("Edge Texture Aspect Ratio", "边界线贴图宽高比,用于生成无拉伸的uv"), param.textureAspectRatio);
                    if (subLayer.selectedLODIndex == 0)
                    {
                        param.edgeMaterial = EditorGUILayout.ObjectField(new GUIContent("Edge Material", "边界线mesh材质"), param.edgeMaterial, typeof(Material), false, null) as Material;
                    }
                    param.regionMaterial = EditorGUILayout.ObjectField(new GUIContent("Region Material", "区域mesh材质"), param.regionMaterial, typeof(Material), false, null) as Material;
                    param.shareEdge = EditorGUILayout.Toggle(new GUIContent("Split Edge", "是否根据相邻区域断开区域边界"), param.shareEdge);

                    mLogic.showAdvancedParam = EditorGUILayout.Foldout(mLogic.showAdvancedParam, "Advanced Parameter");
                    if (mLogic.showAdvancedParam)
                    {
                        param.useVertexColorForRegionMesh = EditorGUILayout.Toggle(new GUIContent("Use Vertex Color For Region Mesh", "区域mesh是否使用顶点色"), param.useVertexColorForRegionMesh);
                        param.pointDeltaDistance = EditorGUILayout.FloatField(new GUIContent("Point Delta Distance", "边缘线顶点的间隔距离, 距离越大顶点越少"), param.pointDeltaDistance);
                        param.segmentLengthRatio = EditorGUILayout.FloatField(new GUIContent("Segment Length Ratio", "切线长度占线段长度的比例,越大曲线幅度越大"), param.segmentLengthRatio);
                        param.minTangentLength = EditorGUILayout.FloatField(new GUIContent("Minimum Tangent Length", "最小切线长度, 越大曲线幅度越大"), param.minTangentLength);
                        param.maxTangentLength = EditorGUILayout.FloatField(new GUIContent("Maximum Tangent Length", "最大切线长度, 越大曲线幅度越大"), param.maxTangentLength);
                        curveParam.segmentLengthRatioRandomRange = EditorGUILayout.FloatField(new GUIContent("Segment Length Ratio Random Range", "Segment Length Ratio加一个随机值"), curveParam.segmentLengthRatioRandomRange);
                        curveParam.tangentRotationRandomRange = EditorGUILayout.FloatField(new GUIContent("Tangent Rotation Random Range", "切线随机旋转范围"), curveParam.tangentRotationRandomRange);
                        curveParam.vertexDisplayRadius = EditorGUILayout.FloatField(new GUIContent("Vertex Display Radius", "顶点显示半径"), curveParam.vertexDisplayRadius);
                        param.moreRectangular = EditorGUILayout.Toggle(new GUIContent("More Rectangular", "生成轮廓更方正的边界线"), param.moreRectangular);
                        param.mergeEdge = EditorGUILayout.Toggle(new GUIContent("Merge Edge", "是否将两个区域相邻的两条边合并为一条"), param.mergeEdge);
                        param.edgeHeight = EditorGUILayout.FloatField(new GUIContent("Edge Height", "边缘线的高度"), param.edgeHeight);
                    }

                    if (GUILayout.Button("Preview Generated Data"))
                    {
                        subLayer.CreateAndGenerateOutlineAssets(SLGMakerEditor.instance.exportFolder, mLogic.selectedSubLayerIndex, subLayer.selectedLODIndex, false, subLayer.GetTotalWidth(), subLayer.GetTotalHeight(), 3, 3);
                    }
                    if (GUILayout.Button("Create And Generate Meshies"))
                    {
                        string exportFolder = SLGMakerEditor.instance.exportFolder;
                        if (!string.IsNullOrEmpty(exportFolder))
                        {
                            int nLODs = layer.lodCount;
                            for (int i = 0; i < nLODs; ++i)
                            {
                                subLayer.CreateAndGenerateOutlineAssets(SLGMakerEditor.instance.exportFolder, mLogic.selectedSubLayerIndex, i, true, subLayer.GetTotalWidth(), subLayer.GetTotalHeight(), 3, 3);
                            }
                            SLGMakerEditor.instance.ExportMap(exportFolder, true);
                            SLGMakerEditor.instance.SaveMap();
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", "Select map export folder first!", "OK");
                        }
                    }
                    if (GUILayout.Button("Select Assets Folder"))
                    {
                        string assetFolder = $"{SLGMakerEditor.instance.exportFolder}/{MapCoreDef.NEW_TERRITORY_LAYER_RUNTIME_ASSETS_FOLDER_NAME}/layer{mLogic.selectedSubLayerIndex}";
                        var asset = AssetDatabase.LoadAssetAtPath<DefaultAsset>(assetFolder);
                        if (asset != null)
                        {
                            Selection.activeObject = asset;
                        }
                    }

#if false
                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button("Create Outline"))
                    {
                        layer.CreateOutline(mLogic.selectedSubLayerIndex, 0);
                    }

                    if (GUILayout.Button("Generate Outline Assets"))
                    {
                        layer.GenerateOutlineAssets(mLogic.selectedSubLayerIndex, SLGMakerEditor.instance.exportFolder, lod);
                    }
                    EditorGUILayout.EndHorizontal();
#endif

                    EditorGUILayout.BeginHorizontal();
#if false
                    if (GUILayout.Button("Hide Line And Mesh"))
                    {
                        layer.HideLineAndMesh(mLogic.selectedSubLayerIndex);
                    }
                    if (GUILayout.Button("Hide Line"))
                    {
                        layer.HideLine(mLogic.selectedSubLayerIndex);
                    }
#endif
                    if (GUILayout.Button("Show Mesh"))
                    {
                        for (int i = 0; i < layer.lodCount; ++i) {
                            layer.ShowMesh(mLogic.selectedSubLayerIndex, i);
                        }
                    }

                    if (GUILayout.Button("Hide Mesh"))
                    {
                        for (int i = 0; i < layer.lodCount; ++i)
                        {
                            layer.HideMesh(mLogic.selectedSubLayerIndex, i);
                        }
                    }
                    EditorGUILayout.EndHorizontal();

                    if (GUILayout.Button("Hide Region Mesh"))
                    {
                        for (int i = 0; i < layer.lodCount; ++i)
                        {
                            layer.HideRegionMesh(mLogic.selectedSubLayerIndex, i);
                        }
                    }

                    --EditorGUI.indentLevel;
                }
                //if (GUILayout.Button("Generate Assets"))
                //{
                //    layer.GenerateAssets(SLGMakerEditor.instance.exportFolder);
                //}

                if (GUILayout.Button("Clear All Grids"))
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure? this operation can't be undone!", "Yes", "No"))
                    {
                        ClearAllGrids();
                    }
                }

                bool oldGridShow = layer.IsGridVisible(mLogic.selectedSubLayerIndex);
                var newGridShow = EditorGUILayout.ToggleLeft("Show Grid", oldGridShow);
                layer.ShowGrid(mLogic.selectedSubLayerIndex, newGridShow);

#if false
                EditorGUILayout.BeginVertical("GroupBox");
                subLayer.selectedLODIndex = EditorGUILayout.Popup("LOD", subLayer.selectedLODIndex, mLODNames);
                var param = layer.GetMeshGenerationParam(mLogic.selectedSubLayerIndex, subLayer.selectedLODIndex);
                param.uvScale = EditorGUILayout.FloatField("UV Scale", param.uvScale);
                param.borderSizeRatio = EditorGUILayout.FloatField("Border Size Ratio", param.borderSizeRatio);
                param.cornerSegment = EditorGUILayout.IntField("Corner Segment", param.cornerSegment);
                param.curveCorner = EditorGUILayout.Toggle("Curve Corner", param.curveCorner);
                param.territoryMeshMaterial = EditorGUILayout.ObjectField("Territory Mesh Material", param.territoryMeshMaterial, typeof(Material), false, null) as Material;
                EditorGUILayout.EndVertical();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Preview Territory Mesh"))
                {
                    layer.CreatePreview(mLogic.selectedSubLayerIndex, subLayer.selectedLODIndex);
                }
                if (GUILayout.Button("Hide Preview"))
                {
                    layer.ClearPreview(mLogic.selectedSubLayerIndex);
                }
                EditorGUILayout.EndHorizontal();
#endif

                if (subLayer.operation == EditorTerritoryLayerOperation.SetGrid || subLayer.operation == EditorTerritoryLayerOperation.CreateBuilding)
                {
                    DrawGridTemplates();
                }

                if (subLayer.selectedIndex >= 0) {
                    mPropertyEditor.Draw();
                }

                mLogic.setting.Draw("Map Layer LOD Setting", layer.lodConfig, 0, LODCountChangeCheck, OnLODCountChanged);

                EditorGUILayout.LabelField("Grid Width", "10");
                EditorGUILayout.LabelField("Grid Height", "10");
                EditorGUILayout.TextArea("Accelerator Keys:\n 'Left Mouse Button' to paint.\n 'Ctrl + Left Mouse Button' to erase.\n 'Up Arrow' and 'Down Arrow' to change brush size.");
            }
        }

        bool LODCountChangeCheck(int newLODCount)
        {
            if (newLODCount >= 1 && newLODCount <= 2)
            {
                return true;
            }
            return false;
        }

        void OnLODCountChanged(int oldLODCount, int newLODCount)
        {
            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);
            mLogic.layer.OnLODCountChanged(oldLODCount, newLODCount);
            Repaint();
            subLayer.selectedLODIndex = 0;
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                mLeftButtonDown = false;
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);
            var layer = mLogic.layer;

#if false
            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 1 && currentEvent.alt == false)
            {
                var grid = layer.FromPositionToCoordinate(mLogic.selectedSubLayerIndex, pos);
                EditorUtility.DisplayDialog("", $"pos: {pos}, coord: {grid}", "");
            }
#endif

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 1 && currentEvent.alt == false)
            {
                int id = subLayer.GetGridData(pos);
                UnityEngine.Debug.Log($"Region ID: {id}");
            }

            if (mLeftButtonDown)
            {
                if (subLayer.operation == EditorTerritoryLayerOperation.SetGrid)
                {
                    if (subLayer.selectedIndex >= 0)
                    {
                        int type = 0;
                        if (!currentEvent.control)
                        {
                            type = layer.GetTerritories(mLogic.selectedSubLayerIndex)[subLayer.selectedIndex].id;
                        }
                        layer.SetGrid(mLogic.selectedSubLayerIndex, pos, subLayer.brushSize, type);
                    }
                }
                else if (subLayer.operation == EditorTerritoryLayerOperation.CreateBuilding)
                {
                    if (currentEvent.type == EventType.MouseDown)
                    {
                        CreateBuilding(pos);
                    }
                }
                else if (subLayer.operation == EditorTerritoryLayerOperation.ClearGrid)
                {
                    layer.SetGrid(mLogic.selectedSubLayerIndex, pos, subLayer.brushSize, 0);
                }
                else if (subLayer.operation == EditorTerritoryLayerOperation.EditOutline)
                {
                    
                }
                else
                {
                    UnityEngine.Debug.Assert(false, "todo");
                }
            }

            if (subLayer.operation == EditorTerritoryLayerOperation.EditOutline)
            {
                var regionCreator = subLayer.regionCreatorsForLODs[subLayer.selectedLODIndex];
                if (regionCreator != null) {
                    regionCreator.HandleEditControlPointFunction(currentEvent, pos);
                }
            }

            if (currentEvent.type == EventType.KeyDown)
            {
                if (currentEvent.keyCode == KeyCode.UpArrow)
                {
                    subLayer.brushSize = subLayer.brushSize + 1;
                    Repaint();
                }
                else if (currentEvent.keyCode == KeyCode.DownArrow)
                {
                    subLayer.brushSize = subLayer.brushSize - 1;
                    Repaint();
                }
            }

            var coord = layer.FromPositionToCoordinate(mLogic.selectedSubLayerIndex, pos);
            DrawBrush(coord);

            SceneView.RepaintAll();
            HandleUtility.AddDefaultControl(0);
        }

        void CreateBuilding(Vector3 pos)
        {
            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);
            if (subLayer.selectedIndex >= 0)
            {
                var type = mLogic.layer.GetTerritories(mLogic.selectedSubLayerIndex)[subLayer.selectedIndex].id;
                mLogic.layer.AddBuilding(mLogic.selectedSubLayerIndex, pos, type);
            }
        }

        void DrawBrush(Vector2Int centerCoord)
        {
            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);
            int brushSize = subLayer.brushSize;
            int startX = centerCoord.x - brushSize / 2;
            int startY = centerCoord.y - brushSize / 2;
            int endX = startX + brushSize;
            int endY = startY + brushSize;
            var layer = mLogic.layer;
            var startPos = layer.FromCoordinateToPosition(mLogic.selectedSubLayerIndex, startX, startY);
            var endPos = layer.FromCoordinateToPosition(mLogic.selectedSubLayerIndex, endX, endY);

            Handles.color = Color.white;
            Handles.DrawWireCube((startPos + endPos) * 0.5f, endPos - startPos);
        }

        void ClearAllGrids()
        {
            var layer = mLogic.layer;
            var subLayer = layer.subLayers[mLogic.selectedSubLayerIndex];
            int horizontalGridCount = subLayer.horizontalTileCount;
            int verticalGridCount = subLayer.verticalTileCount;
            for (int i = 0; i < verticalGridCount; ++i)
            {
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    layer.SetGridData(mLogic.selectedSubLayerIndex, j, i, 0);
                }
            }

            layer.RefreshTexture(mLogic.selectedSubLayerIndex);
        }

        void CreateLODNames()
        {
            int n = mLogic.layer.lodCount;
            mLODNames = new string[n];
            for (int i = 0; i < mLODNames.Length; ++i)
            {
                mLODNames[i] = $"LOD {i}";
            }
        }

        void SetSelectedSubLayer(int subLayer)
        {
            if (subLayer != mLogic.selectedSubLayerIndex)
            {
                if (mLogic.selectedSubLayerIndex >= 0)
                {
                    mLogic.layer.SetActive(mLogic.selectedSubLayerIndex, false);
                }
                mLogic.selectedSubLayerIndex = subLayer;
                if (subLayer >= 0)
                {
                    mLogic.layer.SetActive(subLayer, true);
                }
            }
        }

        void AddSubLayer()
        {
            var window = EditorWindow.GetWindow<EditorTerritorySubLayerCreationDialog>("Add Layer");
            EditorUtils.CenterWindow(window, 500, 300, 500, 300);
            window.Show(() => {
                SetSelectedSubLayer(mLogic.layer.subLayers.Count - 1);
            });   
        }

        void RemoveSubLayer()
        {
            var layer = mLogic.layer;
            if (mLogic.selectedSubLayerIndex >= 0 && layer.subLayers.Count > 1)
            {
                if (EditorUtility.DisplayDialog("Warning", "Are you sure, this operation can't be undone!", "Yes", "No"))
                {
                    layer.RemoveSubLayer(mLogic.selectedSubLayerIndex);
                    mLogic.selectedSubLayerIndex = -1;
                    SetSelectedSubLayer(0);
                }
            }
            else if (layer.subLayers.Count == 1)
            {
                EditorUtility.DisplayDialog("Error", "Must have atleast 1 sub layer!", "OK");
            }
        }

        EditorTerritoryLayerLogic mLogic;
        bool mLeftButtonDown = false;
        string[] mLODNames;
        string[] mLayerNames;
        PropertyDataEditor mPropertyEditor = new PropertyDataEditor(true);
    }
}

#endif