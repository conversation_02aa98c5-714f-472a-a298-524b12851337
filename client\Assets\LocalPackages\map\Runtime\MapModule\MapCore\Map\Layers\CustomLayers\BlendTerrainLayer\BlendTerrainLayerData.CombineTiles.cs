﻿ 



 
 

using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    //合并小tile到大mesh
    public partial class BlendTerrainLayerData : MapObjectLayerData
    {
        public string GetCombinedTileNamePrefix(string resFolder, int x, int y)
        {
            return $"{resFolder}/tile_{x}_{y}.";
        }

        public string GetCombinedTilePrefabName(string resFolder, int x, int y)
        {
            return $"{resFolder}/tile_{x}_{y}.prefab";
        }

#if false
        public void CreateGroundTileTextureAtlas()
        {
#if UNITY_EDITOR
            if (mCols % mCombinedHorizontalTileCount != 0 || mRows % mCombinedVerticalTileCount != 0 || mCombinedTileMaterial == null || mGroundTileAtlasSetting == null || string.IsNullOrEmpty(mGroundTileAtlasSetting.atlasTexturePropertyName) || mGroundTileAtlasSetting.borderSize <= 0)
            {
                return;
            }

            PackUsedTileTextures(mCombinedTileMaterial, mGroundTileAtlasSetting.atlasTexturePropertyName, false, mGroundTileAtlasSetting.borderSize, mGroundTileAtlasSetting.rgbTextureAlphaIsOne);
#endif
        }
#endif

        public void CombineSmallTiles(string outputFolder, bool forceGenerateAllTiles, bool packOnlyFileNotExist)
        {
#if false
#if UNITY_EDITOR
            if (forceGenerateAllTiles)
            {
                if (Directory.Exists(outputFolder))
                {
                    FileUtil.DeleteFileOrDirectory(outputFolder);
                }
            }

            bool generateAllTiles = forceGenerateAllTiles;
            if (!Directory.Exists(outputFolder))
            {
                generateAllTiles = true;
                Directory.CreateDirectory(outputFolder);
            }
            else
            {
                if (Utils.IsFolderEmpty(outputFolder))
                {
                    generateAllTiles = true;
                }
            }

            if (mCols % mCombinedHorizontalTileCount != 0 || mRows % mCombinedVerticalTileCount != 0 || mCombinedTileMaterial == null || mGroundTileAtlasSetting == null || string.IsNullOrEmpty(mGroundTileAtlasSetting.atlasTexturePropertyName))
            {
                return;
            }

            PackUsedTileTextures(mCombinedTileMaterial, mGroundTileAtlasSetting.atlasTexturePropertyName, packOnlyFileNotExist, 4, true);

            //读取atlas uv信息
            Dictionary<string, Vector2[]> uvMappings = ImportGroundTileUVAtlasInfo();

            mCombinedTiles = new BlendTerrainTileData[mCombinedHorizontalTileCount * mCombinedVerticalTileCount];

            int width = horizontalTileCount / mCombinedHorizontalTileCount;
            int height = verticalTileCount / mCombinedVerticalTileCount;

            for (int i = 0; i < mCombinedVerticalTileCount; ++i)
            {
                for (int j = 0; j < mCombinedHorizontalTileCount; ++j)
                {
                    int idx = i * mCombinedHorizontalTileCount + j;
                    mCombinedTiles[idx] = CombineTile(j, i, width, height, outputFolder, generateAllTiles, uvMappings);
                }
            }
#endif
#endif
        }

//        BlendTerrainTileData CombineTile(int x, int y, int width, int height, string outputFolder, bool generateAllTiles, Dictionary<string, Vector2[]> uvMappings)
//        {
//#if UNITY_EDITOR
//            int sx = x * width;
//            int sy = y * height;
//            List<Vector2> uvs = new List<Vector2>();
//            List<Vector3> positions = new List<Vector3>();
//            List<int> indices = new List<int>();
//            bool isChanged = false;
//            for (int i = 0; i < height; ++i)
//            {
//                for (int j = 0; j < width; ++j)
//                {
//                    int idx = (i + sy) * mCols + j + sx;
//                    if (mIsTileChanged[idx])
//                    {
//                        mIsTileChanged[idx] = false;
//                        isChanged = true;
//                    }

//                    var tileData = GetTile(j + sx, i + sy);
//                    if (tileData != null)
//                    {
//                        int n = positions.Count;

//                        Vector3 v0 = new Vector3(j * mTileWidth, 0, i * mTileHeight);
//                        Vector3 v1 = v0 + new Vector3(0, 0, mTileHeight);
//                        Vector3 v2 = v0 + new Vector3(mTileWidth, 0, mTileHeight);
//                        Vector3 v3 = v0 + new Vector3(mTileWidth, 0, 0);
//                        var tileUVs = GetUVInAtlas(tileData, uvMappings);
//                        uvs.AddRange(tileUVs);
//                        positions.Add(v0);
//                        positions.Add(v1);
//                        positions.Add(v2);
//                        positions.Add(v3);

//                        indices.Add(n + 0);
//                        indices.Add(n + 1);
//                        indices.Add(n + 2);
//                        indices.Add(n + 0);
//                        indices.Add(n + 2);
//                        indices.Add(n + 3);
//                    }
//                }
//            }

//            if (positions.Count == 0)
//            {
//                return null;
//            }

//            string prefix = GetCombinedTileNamePrefix(outputFolder, x, y);
//            string prefabPath = prefix + "prefab";
//            if (isChanged || generateAllTiles)
//            {
//                Mesh mesh = new Mesh();
//                mesh.SetVertices(positions);
//                mesh.SetUVs(0, uvs);
//                mesh.SetTriangles(indices, 0);
//                mesh.RecalculateBounds();

//                GameObject obj = new GameObject();
//                var filter = obj.AddComponent<MeshFilter>();
//                filter.sharedMesh = mesh;
//                var renderer = obj.AddComponent<MeshRenderer>();
//                renderer.sharedMaterial = mCombinedTileMaterial;
//                AssetDatabase.CreateAsset(mesh, prefix + "asset");
//                PrefabUtility.SaveAsPrefabAsset(obj, prefabPath);
//                GameObject.DestroyImmediate(obj);
//            }

//            float blockWidth = width * tileWidth;
//            float blockHeight = height * tileHeight;
//            var pos = new Vector3(sx * blockWidth + blockWidth * 0.5f, 0, sy * blockHeight + blockHeight * 0.5f) + mLayerOrigin;
//            int objID = Map.currentMap.nextCustomObjectID;
//            ModelTemplate modelTemplate = Map.currentMap.GetOrCreateModelTemplate(objID, prefabPath, false);
//            BlendTerrainTileData combinedTileData = new BlendTerrainTileData(objID, pos, modelTemplate, 1, 1, 0, null);

//            return combinedTileData;
//#else
//            return null;
//#endif
//        }

        Vector2[] GetUVInAtlas(BlendTerrainTileData tileData, Dictionary<string, Vector2[]> uvMappings)
        {
            var modelTemplate = GetModelTemplate(tileData.id, tileData.type, tileData.index, tileData.subTypeIndex, 0, 0);
            var prefabPath = modelTemplate.GetLODPrefabPath(0);

            Vector2[] uvs;
            uvMappings.TryGetValue(prefabPath, out uvs);
            Debug.Assert(uvs != null);
            return uvs;
        }

        //public float combinedTileWidth { get { return GetLayerWidthInMeter() / mCombinedHorizontalTileCount; } }
        //public float combinedTileHeight { get { return GetLayerHeightInMeter() / mCombinedVerticalTileCount; } }
    }
}
