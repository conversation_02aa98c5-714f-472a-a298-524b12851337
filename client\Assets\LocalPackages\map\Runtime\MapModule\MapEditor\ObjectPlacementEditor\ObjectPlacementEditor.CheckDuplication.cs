﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        //检查在相同的坐标下是否有相同类型的game object,并选中多余的game object
        void CheckDuplicatedObject(GameObject root, bool deleteObjects)
        {
            EditorUtility.DisplayProgressBar("Checking Duplication", "Checking Duplicated Objects, Please Wait...", 0.1f);
            Dictionary<Vector3, Dictionary<string, List<GameObject>>> objectsWithSamePosition = new Dictionary<Vector3, Dictionary<string, List<GameObject>>>();

            int n = root.transform.childCount;
            for (int i = 0; i < n; ++i)
            {
                var child = root.transform.GetChild(i);
                var pos = child.position;

                Dictionary<string, List<GameObject>> objects;
                objectsWithSamePosition.TryGetValue(pos, out objects);
                if (objects == null)
                {
                    objects = new Dictionary<string, List<GameObject>>();
                    objectsWithSamePosition[pos] = objects;
                }

                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(child.gameObject);
                string prefabPath = AssetDatabase.GetAssetPath(childPrefab);
                List<GameObject> objectsOfSamePrefab;
                objects.TryGetValue(prefabPath, out objectsOfSamePrefab);
                if (objectsOfSamePrefab == null)
                {
                    objectsOfSamePrefab = new List<GameObject>();
                    objects[prefabPath] = objectsOfSamePrefab;
                }

                objectsOfSamePrefab.Add(child.gameObject);
            }

            EditorUtility.DisplayProgressBar("Checking Duplication", "Selecting Duplicated Objects...", 0.7f);
            List<GameObject> selectedObjects = new List<GameObject>();
            foreach (var p in objectsWithSamePosition)
            {
                var objectsOfSameType = p.Value;
                foreach (var p1 in objectsOfSameType)
                {
                    var objects = p1.Value;
                    if (objects.Count > 1)
                    {
                        for (int i = 1; i < objects.Count; ++i)
                        {
                            selectedObjects.Add(objects[i]);
                        }
                    }
                }
            }
            if (deleteObjects)
            {
                for (int i = 0; i < selectedObjects.Count; ++i)
                {
                    Undo.DestroyObjectImmediate(selectedObjects[i]);
                }
            }
            else
            {
                Selection.objects = selectedObjects.ToArray();
            }

            EditorUtility.ClearProgressBar();
        }

        void RemoveUnusedObjects(GameObject root)
        {
            int n = root.transform.childCount;
            for (int i = n - 1; i >= 0; --i)
            {
                var child = root.transform.GetChild(i);
                if (child.name == MapCoreDef.MAP_PREFAB_INDICATOR_NAME)
                {
                    Undo.DestroyObjectImmediate(child.gameObject);
                }
            }
        }
    }
}
#endif