﻿ 



 
 

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class MapCollisionLayer : MapLayerBase
    {
        public void RemovePrefabOutlines()
        {
            List<MapCollisionData> collisions = new List<MapCollisionData>();
            GetCollisionsOfType(collisions, CollisionAttribute.IsConvertedFromPrefabOutline);
            for (int i = 0; i < collisions.Count; ++i)
            {
                RemoveObject(collisions[i].id);
            }
        }

        //将PrefabOutline加入到collision layer
        public void AddObjectFromPrefabOutlines()
        {
            EditorUtility.DisplayProgressBar("Remove Collisions Converted From Prefab Outlines", "Please wait", 0.0f);
            //先删除从prefab outline转换的collision
            RemovePrefabOutlines();

            //转换collision
            //grid model layer
            HashSet<PrefabOutline> prefabOutlines = new HashSet<PrefabOutline>();
            var obstacles = Utils.GetGridModelLayerObstacles(true, prefabOutlines);
            //complex grid model layer
            List<IObstacle> complexGridModelLayerObstacles = Utils.GetComplexGridModelLayerObstacles(prefabOutlines);
            obstacles.AddRange(complexGridModelLayerObstacles);

            //other prefab outline
            var allPrefabOutlines = UnityEngine.Object.FindObjectsOfType<PrefabOutline>();
            for (int p = 0; p < allPrefabOutlines.Length; ++p)
            {
                if (prefabOutlines.Contains(allPrefabOutlines[p]) == false)
                {
                    var proxy = new ObstacleProxy(allPrefabOutlines[p], Vector3.zero);
                    obstacles.Add(proxy);
                }
            }

            for (int i = 0; i < obstacles.Count; ++i)
            {
                EditorUtility.DisplayProgressBar($"Add PrefabOutline {i}", "Please wait", (float)(i + 1) / obstacles.Count);

                List<Vector3> v0 = new List<Vector3>();
                List<Vector3> v1 = new List<Vector3>();
                v0.AddRange(obstacles[i].GetWorldSpaceOutlineVertices(PrefabOutlineType.NavMeshObstacle));
                v1.AddRange(obstacles[i].GetWorldSpaceOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle));
                
                OutlineData[] outlineData = new OutlineData[2]
                {
                    new OutlineData(v0),
                    new OutlineData(v1),
                };

                var collision = new MapCollisionData(Map.currentMap.nextCustomObjectID, Map.currentMap, outlineData, displayVertexRadius, true, CollisionAttribute.IsAutoExpandingObstacle | CollisionAttribute.IsConvertedFromPrefabOutline, 0, true);
                AddObject(collision);
            }

            EditorUtility.ClearProgressBar();
        }
    }
}


#endif