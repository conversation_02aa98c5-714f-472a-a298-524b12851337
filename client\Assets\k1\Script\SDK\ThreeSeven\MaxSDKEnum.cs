﻿
using System;
using Newtonsoft.Json.Linq;

namespace maxsdk
{
    public static class MaxSDKType
    {

        /**
        * 未知类型SDK
        */
        public static readonly int MAX_SDK_UNKNOWN = 0;

        /**
         * 37网游 国内 SDK
         */
        public static readonly int MAX_SDK_WY = 1;

        /**
         * 37海外 SDK
         */
        public static readonly int MAX_SDK_HW = 2;

        /**
         * 37手游 国内SDK
         */
        public static readonly int MAX_SDK_SY_GN = 3;

        /**
         * 37手游 海外SDK
         */
        public static readonly int MAX_SDK_SY_HW = 4;

    }

    public enum MaxSDKShareType
    {
        /** 图片类型 */
        SHARE_TYPE_IMAGE = 1,

        /** 链接类型 */
        SHARE_TYPE_LINK = 2
    }

    public enum MaxSDKSharePlatform
    {
        /**
		 * 微信平台
		 *
		 * 【国内 SDK 独有字段】
		 */
        SHARE_PLATFORM_WECHAT = 100,

        /**
		 * 微信朋友圈
		 *
		 * 【国内 SDK 独有字段】
		 */
        SHARE_PLATFORM_WECHAT_CIRCLE = 101,

        /**
		 * QQ
		 *
		 * 【国内 SDK 独有字段】
		 */
        SHARE_PLATFORM_QQ = 102,

        /* --------- 我是一条华丽的分割线 ----------- */

        /**
		 * 脸书
		 *
		 * 【海外 SDK 独有字段】
		 */
        SHARE_PLATFORM_FACEBOOK = 200,

        /**
		 * Instagram
		 *
		 * 【海外 SDK 独有字段】
		 */
        SHARE_PLATFORM_INSTAGRAM = 201,

        /**
		 * Line
		 *
		 * 【海外 SDK 独有字段】
		 */
        SHARE_PLATFORM_LINE = 202,

        /**
		 * 推特
		 *
		 * 【海外 SDK 独有字段】
		 */
        SHARE_PLATFORM_TWITTER = 203,

        /**
		 * Facebook旗下的Meseenger
		 *
		 * 【海外 SDK 独有字段】
		 */
        SHARE_PLATFORM_MESSENGER = 204,

        /**
		 * Kakao
		 *
		 * 【海外 SDK 独有字段】
		 */
        SHARE_PLATFORM_KAKAO = 205,
    }


    public class MaxSDKActionType
    {
        /**
         * 打开URL链接
         */
        public static readonly int TYPE_OPEN_URL = 101;

        /**
         * 打开论坛
         */
        public static readonly int TYPE_OPEN_BBS = 102;

        /**
         * 打开用户中心
         */
        public static readonly int TYPE_OPEN_USER_CENTER = 103;

        /**
         * 打开客服中心
         */
        public static readonly int TYPE_OPEN_CUSTOMER_SERVICE = 104;

        /**
         * 打开应用宝V+特权（特殊需求接入，需要运营确认）
         */
        public static readonly int TYPE_OPEN_VPLAYER = 105;

        /**
         * 打开用户协议页
         */
        public static readonly int TYPE_OPEN_USER_AGREEMENT = 106;

        /**
         * 打开绑定手机页
         */
        public static readonly int TYPE_OPEN_PHONE_BIND = 107;

        /**
         * 获取适龄图标（在 OnActionSuccess 回调中获取数据）
         */
        public static readonly int TYPE_GET_AGE_APPROPRIATE_ICON = 108;

        /**
         * 打开适龄提醒提示页
         */
        public static readonly int TYPE_OPEN_AGE_APPROPRIATE_TIPS = 109;

        /**
         * 打开实名制认证弹窗（按需接入）
         */
        public static readonly int TYPE_SHOW_SQ_PERSONAL_AUTH_DIALOG = 110;

        /*
         * 扩展接口（特殊需求接入，需要运营确认）
         */
        public static readonly int TYPE_PERFORM_FEATURE = 111;


        /* --------- 海外独有行为类型 ----------- */

        /**
         * FAQ客服工单（非必接）
         */
        public static readonly int TYPE_OPEN_FAQ_VIEW = 201;
        /**
         * 离线FAQ（非必接）
         */
        public static readonly int TYPE_OPEN_LOCAL_FAQ_VIEW = 202;
        /**
         * Naver界面（非必接）
         */
        public static readonly int TYPE_OPEN_NAVER_SDK_MAIN_VIEW = 203;
        /**
         * 游戏小助手（非必接）
         */
        public static readonly int TYPE_OPEN_GAME_HELPER = 204;
        /**
         * Facebook页面（非必接）
         */
        public static readonly int TYPE_OPEN_FACEBOOK_PAGE = 205;
        /**
         * 登录界面（非必接）
         */
        public static readonly int TYPE_OPEN_LOGIN_VIEW = 206;
    }
}

