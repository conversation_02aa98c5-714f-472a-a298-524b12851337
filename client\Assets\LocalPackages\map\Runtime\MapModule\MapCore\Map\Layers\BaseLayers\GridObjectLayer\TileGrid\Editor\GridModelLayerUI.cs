﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using TFW;
using System.Collections.Generic;
using System.IO;
using System.Diagnostics;

namespace TFW.Map
{
    [CustomEditor(typeof(GridModelLayerLogic))]
    public partial class GridModelLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as GridModelLayerLogic;
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.gridModelLayerPrefabManager;
                prefabManager.selectPrefabEvent += OnSelectPrefab;
                prefabManager.fillRandomPrefabEvent += OnFillRandomPrefab;
                //prefabManager.customEditFunc = EditPrefab;

                mColorIndicator = new TileIndicator(false);
                mColorIndicator.SetScale(mLogic.layerData.tileWidth);
                mColorIndicator.SetColor(Color.yellow);

                mTextureModelIndicator = new TileIndicator(false);
                mTextureModelIndicator.SetScale(mLogic.layerData.tileWidth);
                mTextureModelIndicator.SetColor(Color.green);
                HideTextureModelIndicator();

                mPrefab = prefabManager.selectedPrefab;

                mLogic.UpdateGizmoVisibilityState();
            }
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.gridModelLayerPrefabManager;
                prefabManager.selectPrefabEvent -= OnSelectPrefab;
                prefabManager.fillRandomPrefabEvent -= OnFillRandomPrefab;
                prefabManager.customEditFunc = null;
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
                HidePrefabIndicator();
                HideColorIndicator();
                HideTextureModelIndicator();
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;
            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);
            var coord = mLogic.layerData.FromWorldPositionToCoordinate(pos);
            pos = mLogic.layerData.FromCoordinateToWorldPositionCenter(coord.x, coord.y);

            CheckOperationType(pos, currentEvent);

            if (mLogic.rotationSetting.Update())
            {
                Repaint();
            }

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                mLeftButtonDown = false;
                mLastCoord = new Vector2Int(-1, -1);
            }

            if (mLogic.operationType != GridModelOperationType.kSwitchObjectModel)
            {
                if (mLogic.operationType == GridModelOperationType.kCreateObject || mLogic.operationType == GridModelOperationType.kRemoveObject)
                {
                    HideTextureModelIndicator();

                    UpdateIndicator(pos);

                    if (mLeftButtonDown)
                    {
                        if (mLogic.operationType == GridModelOperationType.kCreateObject)
                        {
                            AddObject(screenPos);
                        }
                        else if (mLogic.operationType == GridModelOperationType.kRemoveObject)
                        {
                            RemoveObject(screenPos);
                        }
                    }

                    HandleUtility.AddDefaultControl(0);
                }
            }
            else
            {
                HidePrefabIndicator();
                HideColorIndicator();
                UpdateTextureModelIndicator(pos);

                if (mLeftButtonDown)
                {
                    var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
                    layer.SwitchModel(coord.x, coord.y, mLogic.textureModelOperation == TextureModelOperation.SwitchToTexture);
                }

                HandleUtility.AddDefaultControl(0);
            }

            if (mLogic.updateTexture)
            {
                var layer = map.GetMapLayerByID(mLogic.layerID) as EditorGridModelLayer;
                UpdateModelTexture(layer);
            }
        }

        void UpdateModelTexture(EditorGridModelLayer layer)
        {
            var map = Map.currentMap;
            float cameraHeight = map.camera.transform.position.y;
            map.view.modelTemplateRenderToTextureGameObjectManager.Update(cameraHeight);
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                mLogic.operationType = (GridModelOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operationType);

                var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Remove All Objects In This Layer", "删除该层所有物体")))
                {
                    ClearLayer();
                }
                if (GUILayout.Button(new GUIContent("Remove Invalid Objects In This Layer", "删除该层prefab资源缺失的物体")))
                {
                    RemoveInvalidObjects();
                }
                EditorGUILayout.EndHorizontal();

                DrawResizeOption();

                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.gridModelLayerPrefabManager;

                if (mLogic.operationType == GridModelOperationType.kSwitchObjectModel)
                {
                    DrawTextureModelSetting();
                }
                else
                {
                    if (mLogic.operationType == GridModelOperationType.kCreateObject)
                    {
                        EditorGUILayout.BeginVertical("GroupBox");
                        prefabManager.Draw(PrefabGroupDisplayFlag.IsGridModelLayer | PrefabGroupDisplayFlag.ShowColor | PrefabGroupDisplayFlag.ShowAddFolderWithLODConstrain | PrefabGroupDisplayFlag.CanRemovePrefab);
                        EditorGUILayout.EndVertical();
                    }
                }

                mLogic.showTextureModelSetting = EditorGUILayout.Foldout(mLogic.showTextureModelSetting, new GUIContent("Texture Model", "贴图模式设置"));
                if (mLogic.showTextureModelSetting)
                {
                    EditorGUILayout.BeginVertical("GroupBox");
                    layer.useTextureModel = EditorGUILayout.Toggle(new GUIContent("Use Texture Model", "接下来是否使用贴图来替代3d物体显示, 使用贴图模式可以将prefab上所有game object渲染成贴图,提高编辑器性能"), layer.useTextureModel);
                    //mLogic.updateTexture = EditorGUILayout.Toggle(new GUIContent("Update Texture", "编辑器相机移动时是否更新渲染后的贴图"), mLogic.updateTexture);
                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button(new GUIContent("Switch To Texture Model", "将所有game object切换成对应的渲染贴图")))
                    {
                        SwitchToTextureModel(true);
                    }
                    if (GUILayout.Button(new GUIContent("Switch To 3D Model", "将所有的渲染贴图替换成原本的game object")))
                    {
                        SwitchToTextureModel(false);
                    }
                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.EndVertical();
                }

                var layerID = mLogic.layerID;
                var layerData = map.FindObject(layerID) as SparseGridObjectLayerData;
                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, 0, null, null);

                //draw obstacle data
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Import Color Map", "导入一张tga贴图,大小和front layer的格子数相同,每个像素对应prefab group的颜色设置,在相同颜色的prefab group中随机选择一个prefab填入对应格子")))
                {
                    ImportColorMap();
                }

                var group = prefabManager.selectedGroup;
                if (group != null)
                {
                    if (GUILayout.Button(new GUIContent("Import Config", "导入一张逗号隔开的配置表,每个格子填入一个数,这个数就是prefab group中的prefab的序号,填入front layer的格子中")))
                    {
                        ImportConfig(group);
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (GUILayout.Button(new GUIContent("Create Obstacles", "创建局部和全局障碍物数据")))
                {
                    string path = EditorUtility.SaveFolderPanel("NavMesh", "", "json");
                    MapEditorUI.CreateObstacles(path);
                }

                if (GUILayout.Button(new GUIContent("Remove Prefabs Which Collides With Obstacles", "删除该layer中和collision相交的game object")))
                {
                    RemovePrefabsCollidesWithCollisions();
                    SLGMakerEditor.instance.SaveMap();
                }

                if (GUILayout.Button("Create New Prefabs For Terrain Tiles"))
                {
                    UnlinkSharedPrefabs();
                }

                if (GUILayout.Button(new GUIContent("Recover Prefabs", "是Remove Prefabs Which Collides With Obstacles的逆操作")))
                {
                    RecoverPrefabs();
                }

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Object Count", layerData.objectCount.ToString());
                EditorGUILayout.LabelField("Tile Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Tile Height", layerData.tileHeight.ToString());
                EditorGUILayout.LabelField("X Tile Count", layerData.horizontalTileCount.ToString());
                EditorGUILayout.LabelField("Z Tile Count", layerData.verticalTileCount.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void DrawTextureModelSetting()
        {
            EditorGUILayout.BeginVertical("GroupBox");
            var newOperationType = (TextureModelOperation)EditorGUILayout.EnumPopup("Operation", mLogic.textureModelOperation);
            if (newOperationType != mLogic.textureModelOperation)
            {
                HideTextureModelIndicator();
                mLogic.textureModelOperation = newOperationType;
            }

            EditorGUILayout.EndVertical();
        }

        void RemoveInvalidObjects()
        {
            if (EditorUtility.DisplayDialog("Warning", "This action can NOT be undone! are you sure?", "Yes", "No"))
            {
                var layerID = mLogic.layerID;
                var map = Map.currentMap;
                var layer = map.GetMapLayerByID(layerID) as EditorGridModelLayer;
                int hori = layer.horizontalTileCount;
                int vert = layer.verticalTileCount;
                for (int i = 0; i < vert; ++i)
                {
                    for (int j = 0; j < hori; ++j)
                    {
                        var obj = layer.GetObjectData(j, i);
                        if (obj != null)
                        {
                            var modelTemplate = map.FindObject(obj.GetModelTemplateID()) as ModelTemplate;
                            if (modelTemplate.isValid == false)
                            {
                                layer.RemoveObject(obj.GetEntityID());
                            }
                        }
                    }
                }
            }
        }

        void ClearLayer()
        {
            if (EditorUtility.DisplayDialog("Warning", "This action can NOT be undone! are you sure?", "Yes", "No"))
            {
                var layerID = mLogic.layerID;
                var map = Map.currentMap;
                var layer = map.GetMapLayerByID(layerID) as EditorGridModelLayer;
                int hori = layer.horizontalTileCount;
                int vert = layer.verticalTileCount;
                for (int i = 0; i < vert; ++i)
                {
                    for (int j = 0; j < hori; ++j)
                    {
                        var obj = layer.GetObjectData(j, i);
                        if (obj != null)
                        {
                            layer.RemoveObject(obj.GetEntityID());
                        }
                    }
                }
            }
        }

        void RemoveObject(Vector2 screenPos)
        {
            var layerID = mLogic.layerID;
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(layerID) as EditorGridModelLayer;
            var layerData = layer.layerData;
            var worldPos = map.FromScreenToWorldPosition(screenPos);
            var coord = layerData.FromWorldPositionToCoordinate(worldPos);
            var centerPos = layerData.FromCoordinateToWorldPositionCenter(coord.x, coord.y);
            var objectData = layer.GetObjectData(coord.x, coord.y);
            if (objectData != null)
            {
                var act = new ActionRemoveGridModel(objectData.GetEntityID(), layerID);
                ActionManager.instance.PushAction(act);
            }
        }

        void AddBlockObject(Vector2 screenPos)
        {
            var modelTemplate = GetModelTemplate(mPrefab);
            int blockWidth = modelTemplate.blockWidth;
            int blockHeight = modelTemplate.blockHeight;

            if (blockWidth > 1 || blockHeight > 1)
            {
                //检查block prefab是否已经创建了,如果没有则创建
                modelTemplate.CheckBlockPrefabs(mLogic.layerData.tileWidth);
            }

            var layerID = mLogic.layerID;
            var map = SLGMakerEditor.GetMap();
            var layer = map.GetMapLayerByID(layerID) as EditorGridModelLayer;
            var layerData = layer.layerData;
            var worldPos = map.FromScreenToWorldPosition(screenPos);
            var coord = layerData.FromWorldPositionToCoordinate(worldPos);

            CompoundAction compoundActions = new CompoundAction("Create Model Template");

            for (int i = 0; i < blockHeight; ++i)
            {
                for (int j = 0; j < blockWidth; ++j)
                {
                    var tile = layer.GetObjectData(coord.x, coord.y);
                    string prefabFolder = ModelTemplateBlockPrefabCreator.GetPrefabFolder();
                    string blockPrefabPath = modelTemplate.GetLODPrefabPathOfBlock(0, i, j);
                    string finalPath = $"{prefabFolder}/{Utils.GetPathName(blockPrefabPath, true)}";
                    ModelTemplate blockModelTemplate = GetModelTemplate(finalPath);
                    UnityEngine.Debug.Assert(blockModelTemplate != null, "block template is null!");
                    if (tile == null || tile.GetModelTemplateID() != blockModelTemplate.id)
                    {
                        var action = CreateAddObjectAction(coord.x, coord.y, blockModelTemplate, layer.useTextureModel);
                        if (action != null)
                        {
                            compoundActions.Add(action);
                        }
                    }
                }
            }

            ActionManager.instance.PushAction(compoundActions);
        }

        void AddObject(Vector2 screenPos)
        {
            var modelTemplate = GetModelTemplate(mPrefab);
            if (modelTemplate.blockWidth > 1 || modelTemplate.blockHeight > 1)
            {
                AddBlockObject(screenPos);
            }
            else
            {
                var layerID = mLogic.layerID;
                var map = SLGMakerEditor.GetMap();
                var layer = map.GetMapLayerByID(layerID) as EditorGridModelLayer;
                var layerData = layer.layerData;
                var worldPos = map.FromScreenToWorldPosition(screenPos);
                var coord = layerData.FromWorldPositionToCoordinate(worldPos);

                var tile = layer.GetObjectData(coord.x, coord.y);
                if (tile == null || tile.GetModelTemplateID() != modelTemplate.id)
                {
                    var action = CreateAddObjectAction(coord.x, coord.y, modelTemplate, layer.useTextureModel);
                    if (action != null)
                    {
                        ActionManager.instance.PushAction(action);
                    }
                }
            }
        }

        void OnFillRandomPrefab(List<GameObject> prefabs, TileFillMode mode)
        {
            if (prefabs.Count > 0)
            {
                var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
                var layerData = mLogic.layerData;
                int cols = layerData.horizontalTileCount;
                int rows = layerData.verticalTileCount;
                CompoundAction actions = null;
                List<Vector2Int> tiles = new List<Vector2Int>();
                if (mode == TileFillMode.kAllTiles)
                {
                    for (int i = 0; i < rows; ++i)
                    {
                        for (int j = 0; j < cols; ++j)
                        {
                            tiles.Add(new Vector2Int(j, i));
                        }
                    }
                }
                else if (mode == TileFillMode.kEmptyTiles)
                {
                    for (int i = 0; i < rows; ++i)
                    {
                        for (int j = 0; j < cols; ++j)
                        {
                            if (layerData.GetObjectData(j, i) == null)
                            {
                                tiles.Add(new Vector2Int(j, i));
                            }
                        }
                    }
                }
                else if (mode == TileFillMode.kEmptyTilesWithoutCollisionCheck)
                {
                    var collisionLayer = Map.currentMap.GetMapLayerOfType(typeof(MapCollisionLayer)) as MapCollisionLayer;
                    var railwayLayer = Map.currentMap.GetMapLayerOfType(typeof(RailwayLayer)) as RailwayLayer;

                    for (int i = 0; i < rows; ++i)
                    {
                        for (int j = 0; j < cols; ++j)
                        {
                            if (layerData.GetObjectData(j, i) == null)
                            {
                                var tilePolygon = CreateTilePolygon(j, i);
                                bool hit = false;
                                if (collisionLayer != null)
                                {
                                    //check collision layer
                                    hit |= collisionLayer.IntersectWithPolygon(PrefabOutlineType.ObjectPlacementObstacle, tilePolygon);
                                }

                                if (hit == false && railwayLayer != null)
                                {
                                    //check railway layer
                                    var colliderPolygons = railwayLayer.GetColliderPolygons();
                                    for (int k = 0; k < colliderPolygons.Count; ++k)
                                    {
                                        hit |= PolygonCollisionCheck.Overlap(colliderPolygons[k], tilePolygon);
                                        if (hit)
                                        {
                                            break;
                                        }
                                    }
                                }

                                if (!hit)
                                {
                                    tiles.Add(new Vector2Int(j, i));
                                }
                            }
                        }
                    }
                }
                else
                {
                    UnityEngine.Debug.Assert(false, "unknown fill mode!");
                }

                if (tiles.Count > 0)
                {
                    actions = new CompoundAction("Fill Random Front Layer Tiles");
                    for (int i = 0; i < tiles.Count; ++i)
                    {
                        var idx = Random.Range(0, prefabs.Count);
                        var modelTemplate = GetModelTemplate(prefabs[idx]);
                        var action = CreateAddObjectAction(tiles[i].x, tiles[i].y, modelTemplate, layer.useTextureModel);
                        if (action != null)
                        {
                            actions.Add(action);
                        }
                    }

                    ActionManager.instance.PushAction(actions);
                }
            }
        }

        EditorAction CreateAddObjectAction(int x, int y, ModelTemplate modelTemplate, bool useTextureModel)
        {
            if (modelTemplate != null)
            {
                var layerID = mLogic.layerID;
                var map = SLGMakerEditor.GetMap();
                var layer = map.GetMapLayerByID(layerID) as EditorGridModelLayer;
                var layerData = layer.layerData;
                var coord = new Vector2Int(x, y);
                if (coord != mLastCoord)
                {
                    if (coord.x >= 0 && coord.x < layer.horizontalTileCount &&
                        coord.y >= 0 && coord.y < layer.verticalTileCount)
                    {
                        mLastCoord = coord;
                        CompoundAction actions = new CompoundAction("Add Front Layer Tiles");

                        var oldObject = layer.GetObjectData(coord.x, coord.y);
                        if (oldObject != null)
                        {
                            var removeAction = new ActionRemoveGridModel(oldObject.GetEntityID(), layerID);
                            actions.Add(removeAction);
                        }

                        var pos = layerData.FromCoordinateToWorldPositionCenter(coord.x, coord.y);
                        var addAction = new ActionAddGridModel(layerID, map.nextCustomObjectID, modelTemplate.id, mLogic.rotationSetting.rotation, pos, Vector3.one, useTextureModel);
                        actions.Add(addAction);

                        return actions;
                    }
                }
            }

            return null;
        }

        ModelTemplate GetModelTemplate(GameObject prefab)
        {
            var assetPath = AssetDatabase.GetAssetPath(prefab);
            if (assetPath.Length == 0)
            {
                return null;
            }
            return MapEditor.instance.CreateModelTemplate(assetPath, prefab, true);
        }

        ModelTemplate GetModelTemplate(string assetPath)
        {
            var prefab = MapModuleResourceMgr.LoadPrefab(assetPath);
            return MapEditor.instance.CreateModelTemplate(assetPath, prefab, true);
        }

        string GetAssetGUID(GameObject gameObject)
        {
            var assetPath = AssetDatabase.GetAssetPath(gameObject);
            var guid = AssetDatabase.AssetPathToGUID(assetPath);
            return guid;
        }

        void ShowPrefabIndicator()
        {
            var indicator = SLGMakerEditor.instance.renderTextureObjectIndicator;
            indicator.SetPrefab(mPrefab);
            if (mPrefab != null)
            {
                indicator.SetActive(true);
            }
            else
            {
                indicator.SetActive(false);
            }
        }

        void ShowColorIndicator()
        {
            if (mPrefab != null)
            {
                mColorIndicator.SetActive(true);
            }
            else
            {
                mColorIndicator.SetActive(false);
            }
        }

        void ShowTextureModelIndicator()
        {
            if (mPrefab != null)
            {
                mTextureModelIndicator.SetActive(true);
                //mTextureModelIndicator.SetPosition(new Vector3(1000000, 0, 0));
            }
            else
            {
                mTextureModelIndicator.SetActive(false);
            }
        }

        void UpdateIndicator(Vector3 pos)
        {
            if (mLogic.operationType == GridModelOperationType.kCreateObject)
            {
                ShowPrefabIndicator();
                HideColorIndicator();
            }
            else
            {
                HidePrefabIndicator();
                ShowColorIndicator();
            }

            var indicator = SLGMakerEditor.instance.renderTextureObjectIndicator;
            indicator.SetPosition(pos);
            mColorIndicator.SetPosition(pos + new Vector3(0, 0.2f, 0));
            var rotation = mLogic.rotationSetting.rotation;
            indicator.SetRotation(rotation);
        }

        void UpdateTextureModelIndicator(Vector3 pos)
        {
            ShowTextureModelIndicator();
            mTextureModelIndicator.SetPosition(pos + new Vector3(0, 0.2f, 0));
        }

        void HidePrefabIndicator()
        {
            var indicator = SLGMakerEditor.instance.renderTextureObjectIndicator;
            indicator.SetPrefab(null);
        }

        void HideColorIndicator()
        {
            mColorIndicator.SetActive(false);
        }

        void HideTextureModelIndicator()
        {
            mTextureModelIndicator.SetActive(false);
        }

        void SetPrefab(GameObject prefab)
        {
            mPrefab = prefab;
            mLogic.selectedPrefabGUID = GetAssetGUID(mPrefab);
        }

        void OnSelectPrefab(GameObject prefab)
        {
            mPrefab = prefab;
            mLogic.selectedPrefabGUID = GetAssetGUID(prefab);
        }

        void CheckOperationType(Vector3 pos, Event currentEvent)
        {
            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 1)
            {
                //if (mLogic.operationType == GridModelOperationType.kCreateObject)
                //{
                //    mLogic.operationType = GridModelOperationType.kSelectObject;
                //    HidePrefabIndicator();
                //    HideColorIndicator();
                //}
                //else if (mLogic.operationType == GridModelOperationType.kSelectObject)
                //{
                //    mLogic.operationType = GridModelOperationType.kCreateObject;
                //}
                //Repaint();
                //SceneView.RepaintAll();

                var coord = mLogic.layerData.FromWorldPositionToCoordinate(pos);
                var data = mLogic.layerData.GetObjectData(coord.x, coord.y);
                if (data != null)
                {
                    UnityEngine.Debug.Log($"prefab path in grid {coord.x}_{coord.y} is {data.GetAssetPath()}");
                }
            }
        }

        void SwitchToTextureModel(bool toTexture)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            for (int i = 0; i < layer.verticalTileCount; ++i)
            {
                for (int j = 0; j < layer.horizontalTileCount; ++j)
                {
                    layer.SwitchModel(j, i, toTexture);
                }
            }
        }

        void ImportColorMap()
        {
            string filePath = EditorUtility.OpenFilePanelWithFilters("Color Map", "", new string[] { "tga", "tga" });
            if (!string.IsNullOrEmpty(filePath))
            {
                var bytes = File.ReadAllBytes(filePath);
                if (bytes != null)
                {
                    var tgaData = new TgaDecoderTest.TgaData(bytes);
                    int width = tgaData.Width;
                    int height = tgaData.Height;
                    if (width != mLogic.layerData.horizontalTileCount ||
                        height != mLogic.layerData.verticalTileCount)
                    {
                        EditorUtility.DisplayDialog("Error", "Invalid color map, size not match!", "OK");
                        return;
                    }
                    var editorData = Map.currentMap.data as EditorMapData;
                    CompoundAction actions = null;
                    var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as EditorGridModelLayer;
                    bool error = false;
                    for (int i = 0; i < height; ++i)
                    {
                        for (int j = 0; j < width; ++j)
                        {
                            var pixel = tgaData.GetPixel(j, height - 1 - i);
                            PrefabGroup group = editorData.gridModelLayerPrefabManager.FindGroup(pixel);
                            if (group != null)
                            {
                                string prefabPath = group.GetRandomPrefabPath();
                                if (!string.IsNullOrEmpty(prefabPath))
                                {
                                    var modelTemplate = Map.currentMap.data.modelTemplateManager.GetOrCreateModelTemplate(0, prefabPath, true, false, Map.currentMap);
                                    UnityEngine.Debug.Assert(modelTemplate != null);
                                    var action = CreateAddObjectAction(j, i, modelTemplate, layer.useTextureModel);
                                    if (action != null)
                                    {
                                        if (actions == null)
                                        {
                                            actions = new CompoundAction("Import color tiles");
                                        }
                                        actions.Add(action);
                                    }
                                }
                            }
                            else
                            {
                                EditorUtility.DisplayDialog("Error", string.Format("color [{0}] is not found", pixel), "OK");
                                error = true;
                                break;
                            }
                        }

                        if (error)
                        {
                            break;
                        }
                    }

                    if (actions != null)
                    {
                        ActionManager.instance.PushAction(actions);
                    }
                }
            }
        }

        void ImportConfig(PrefabGroup group)
        {
            string filePath = EditorUtility.OpenFilePanelWithFilters("Config", "", new string[] { "txt", "txt" });
            if (!string.IsNullOrEmpty(filePath))
            {
                var text = File.ReadAllText(filePath);
                if (!string.IsNullOrEmpty(text))
                {
                    text = text.Replace("\r\n", string.Empty);
                    var tokens = text.Split(new string[] { "," }, System.StringSplitOptions.RemoveEmptyEntries);
                    int width = mLogic.layerData.horizontalTileCount;
                    int height = mLogic.layerData.verticalTileCount;
                    int n = width * height;
                    if (tokens.Length != n)
                    {
                        EditorUtility.DisplayDialog("Error", "Invalid config file, size not match!", "OK");
                        return;
                    }
                    var editorData = Map.currentMap.data as EditorMapData;
                    CompoundAction actions = null;
                    var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as EditorGridModelLayer;
                    bool error = false;
                    for (int i = 0; i < height; ++i)
                    {
                        for (int j = 0; j < width; ++j)
                        {
                            //上下颠倒,更符合配置表的视角
                            var idx = (height - i - 1) * width + j;
                            int prefabIndexInGroup;
                            bool suc = int.TryParse(tokens[idx], out prefabIndexInGroup);
                            if (suc)
                            {
                                if (prefabIndexInGroup >= 0 && prefabIndexInGroup < group.count)
                                {
                                    string prefabPath = group.GetPrefabPath(prefabIndexInGroup);
                                    if (!string.IsNullOrEmpty(prefabPath))
                                    {
                                        var modelTemplate = Map.currentMap.data.modelTemplateManager.GetOrCreateModelTemplate(0, prefabPath, true, false, Map.currentMap);
                                        UnityEngine.Debug.Assert(modelTemplate != null);
                                        var action = CreateAddObjectAction(j, i, modelTemplate, layer.useTextureModel);
                                        if (action != null)
                                        {
                                            if (actions == null)
                                            {
                                                actions = new CompoundAction("Import color tiles");
                                            }
                                            actions.Add(action);
                                        }
                                    }
                                }
                                else
                                {
                                    EditorUtility.DisplayDialog("Error", string.Format("invalid index [{0}] is not found", prefabIndexInGroup), "OK");
                                    error = true;
                                    break;
                                }
                            }
                        }

                        if (error)
                        {
                            break;
                        }
                    }

                    if (actions != null)
                    {
                        ActionManager.instance.PushAction(actions);
                    }
                }
            }
        }

        void ApplyPrefabModifications()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLogic.layerID) as EditorGridModelLayer;
            int rows = layer.verticalTileCount;
            int cols = layer.horizontalTileCount;

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var tileData = layer.GetObjectData(j, i);
                    if (tileData != null)
                    {
                        var obj = layer.GetObjectGameObject(tileData.GetEntityID());
                        var modelTemplatePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(tileData.GetAssetPath());
                        CompareBigTile(obj, modelTemplatePrefab);
                    }
                }
            }
        }

        void CompareBigTile(GameObject currentGameObject, GameObject modelTemplatePrefab)
        {
            UnityEngine.Debug.Assert(false, "todo");
        }

        List<ObstacleObject> GetTestColliders()
        {
            List<ObstacleObject> polygons = new List<ObstacleObject>();

            //删除与不能放置装饰物的障碍物相交的prefab
            var collisionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            if (collisionLayer != null)
            {
                List<ObstacleObject> collisions = new List<ObstacleObject>();
                collisionLayer.GetPolygonsOfCanNotPlaceDecorationObjects(collisions);
                polygons.AddRange(collisions);
            }

            HashSet<PrefabOutline> prefabOutlines = new HashSet<PrefabOutline>();
            var obs = CreateObstacleObjects(Utils.GetGridModelLayerObstacles(true, prefabOutlines));
            polygons.AddRange(obs);
            
            obs = CreateObstacleObjects(Utils.GetComplexGridModelLayerObstacles(prefabOutlines));
            polygons.AddRange(obs);

            var allPrefabOutlines = UnityEngine.Object.FindObjectsOfType<PrefabOutline>();
            for (int p = 0; p < allPrefabOutlines.Length; ++p)
            {
                if (!prefabOutlines.Contains(allPrefabOutlines[p]))
                {
                    var obj = CreateObstacleObject(allPrefabOutlines[p]);
                    polygons.Add(obj);
                }
            }

            return polygons;
        }

        ObstacleObject CreateObstacleObject(IObstacle obstacle)
        {
            var verticesArray = obstacle.GetWorldSpaceOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle);
            var vertices = new List<Vector3>();
            vertices.AddRange(verticesArray);
            ObstacleObject obj = new ObstacleObject() { polygon = vertices, bounds = Utils.CreateBounds(vertices) };
            return obj;
        }

        List<ObstacleObject> CreateObstacleObjects(List<IObstacle> obstacles)
        {
            List<ObstacleObject> objects = new List<ObstacleObject>();
            for (int i = 0; i < obstacles.Count; ++i)
            {
                var obj = CreateObstacleObject(obstacles[i]);
                objects.Add(obj);
            }
            return objects;
        }

        class ModifiedPrefabData
        {
            public int x;
            public int y;
            public string prefabPath;
        }

        void RemovePrefabsCollidesWithCollisions()
        {
            var exportFolder = SLGMakerEditor.instance.exportFolder;
            if (string.IsNullOrEmpty(exportFolder))
            {
                EditorUtility.DisplayDialog("Error", "Set export folder first!", "OK");
                return;
            }

            EditorUtility.DisplayProgressBar("Delete Unused Generated Prefabs", "Deleting...", 0.1f);
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            //delete unused prefabs
            var prefabFolder = exportFolder + MapCoreDef.MAP_GENERATED_180M_PREFABS_SUBFOLDER;
            DeleteUnusedPrefabsInGeneratedPrefabFolder(prefabFolder, mLogic.layerData);

            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLogic.layerID) as EditorGridModelLayer;
            int horizontalTileCount = layer.horizontalTileCount;
            int verticalTileCount = layer.verticalTileCount;

            List<ObstacleObject> testColliders = GetTestColliders();

            Dictionary<int, List<BVH>> bvhTrees = layer.CreateBVHTrees(5);
            TilePrefabModifier.testCount = 0;
            TilePrefabModifier.modifiedPrefabCount = 0;

            List<ModifiedPrefabData> modifiedPrefabPath = new List<ModifiedPrefabData>();
            AssetDatabase.StartAssetEditing();
            try
            {
                int idx = 0;
                for (int i = 0; i < verticalTileCount; ++i)
                {
                    for (int j = 0; j < horizontalTileCount; ++j)
                    {
                        var objData = layer.GetObjectData(j, i);
                        if (objData != null)
                        {
                            float progress = (float)(idx + 1) / (horizontalTileCount * verticalTileCount) * 0.8f;
                            EditorUtility.DisplayProgressBar($"Remove Collided Prefabs For Tile {i}_{j}", $"Remove Collided Prefabs For Tile {i}_{j}", progress + 0.1f);
                            List<BVH> bvhTreesForLOD;
                            bvhTrees.TryGetValue(objData.GetModelTemplateID(), out bvhTreesForLOD);

                            string newPrefabPath = RemoveCollidedWithPrefab(objData, j, i, layer, testColliders, bvhTreesForLOD);
                            if (!string.IsNullOrEmpty(newPrefabPath))
                            {
                                var data = new ModifiedPrefabData();
                                data.prefabPath = newPrefabPath;
                                data.x = j;
                                data.y = i;
                                modifiedPrefabPath.Add(data);
                            }
                        }
                        ++idx;
                    }
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            EditorUtility.DisplayProgressBar($"Replace Object Model Template", "Please wait", 0.9f);
            for (int k = 0; k < modifiedPrefabPath.Count; ++k)
            {
                layer.ReplaceObjectModelTemplate(modifiedPrefabPath[k].x, modifiedPrefabPath[k].y, modifiedPrefabPath[k].prefabPath);
            }
            EditorUtility.ClearProgressBar();

            double elapsedTime = w.Stop();
            UnityEngine.Debug.Log("Modify prefabs elapsed: " + elapsedTime.ToString() + " seconds");
            UnityEngine.Debug.Log("test Count: " + TilePrefabModifier.testCount);
            UnityEngine.Debug.Log("modifiedPrefabCount Count: " + TilePrefabModifier.modifiedPrefabCount);
        }

        string RemoveCollidedWithPrefab(IMapObjectData objData, int x, int y, EditorGridModelLayer layer, List<ObstacleObject> testColliders, List<BVH> bvhTreesForLODs)
        {
            StopWatchWrapper w1 = new StopWatchWrapper();
            var modelTemplate = Map.currentMap.FindObject(objData.GetModelTemplateID()) as ModelTemplate;
            string oldPrefabPath = modelTemplate.GetLODPrefabPath(0);
            string newPrefabPath;
            bool cleared = false;

            {
#if LOG_DEBUG
                w1.Start();
#endif
                var idx = y * layer.horizontalTileCount + x;
                var modifier = new TilePrefabModifier(modelTemplate.generated, new Vector3(layer.tileWidth * x, 0, layer.tileHeight * y), new Vector2(layer.tileWidth, layer.tileHeight), oldPrefabPath, idx);
#if LOG_DEBUG
                var a1 = w1.Stop();
                UnityEngine.Debug.Log("a1: " + a1);
#endif

#if LOG_DEBUG
                w1.Start();
#endif
                //删除与地图圆形边界相交的装饰物
                var borderLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CIRCLE_BORDER) as CircleBorderLayer;
                if (borderLayer != null)
                {
                    var center = new Vector3(borderLayer.GetTotalWidth() * 0.5f, 0, borderLayer.GetTotalHeight() * 0.5f);
                    modifier.RemoveIntersectedPrefabs(center, borderLayer.GetTotalWidth() * 0.5f);
                }
                cleared = modifier.isCleared;
#if LOG_DEBUG
                var a2 = w1.Stop();
                UnityEngine.Debug.Log("a2: " + a2);
#endif

#if LOG_DEBUG
                w1.Start();
#endif
                if (cleared == false)
                {
                    modifier.RemoveIntersectedPrefabsWithAnyCollider(testColliders, bvhTreesForLODs);
                }
                cleared = modifier.isCleared;
#if LOG_DEBUG
                var a3 = w1.Stop();
                UnityEngine.Debug.Log("a3: " + a3);
#endif

                var exportFolder = SLGMakerEditor.instance.exportFolder;
                var prefabFolder = exportFolder + MapCoreDef.MAP_GENERATED_180M_PREFABS_SUBFOLDER;

                if (!Directory.Exists(prefabFolder))
                {
                    Directory.CreateDirectory(prefabFolder);
                }
                prefabFolder = Utils.ConvertToUnityAssetsPath(prefabFolder);
#if LOG_DEBUG
                w1.Start();
#endif
                newPrefabPath = modifier.SavePrefab(prefabFolder);
#if LOG_DEBUG
                var a4 = w1.Stop();
                UnityEngine.Debug.Log("a4: " + a4);
#endif
            }

#if LOG_DEBUG
            w1.Start();
#endif
            if (cleared)
            {
                var obj = layer.GetObjectData(x, y);
                //直接删除tile
                layer.RemoveObject(obj.GetEntityID());
            }
#if LOG_DEBUG
            var a5 = w1.Stop();
            UnityEngine.Debug.Log("a5: " + a5);
#endif
            return newPrefabPath;
        }

        static string GetLODPrefix(string name)
        {
            var idx = name.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX);
            var prefix = name.Substring(0, idx);
            return prefix;
        }

        static bool IgnoreLODContains(List<string> names, string prefabName)
        {
            for (int i = 0; i < names.Count; ++i)
            {
                var p1 = GetLODPrefix(names[i]);
                var p2 = GetLODPrefix(prefabName);
                if (p1 == p2)
                {
                    return true;
                }
            }
            return false;
        }

        static void DeleteUnusedPrefabsInGeneratedPrefabFolder(string folder, SparseGridObjectLayerData layerData)
        {
            if (!Directory.Exists(folder))
            {
                return;
            }

            List<string> existedPrefabFiles = new List<string>();
            List<string> fullPath = new List<string>();
            var enumerator = Directory.EnumerateFiles(folder);
            foreach (var filePath in enumerator)
            {
                string validPath = filePath.Replace('\\', '/');
                fullPath.Add(validPath);
                var prefabName = Utils.GetPathName(validPath, true);
                existedPrefabFiles.Add(prefabName);
                //UnityEngine.Debug.Log("Existed Prefab Path Name: " + prefabName);
            }
            List<IMapObjectData> allTiles = new List<IMapObjectData>();
            layerData.GetAllObjects(allTiles);
            List<string> usedTemplateNames = new List<string>();
            for (int i = 0; i < allTiles.Count; ++i)
            {
                if (allTiles[i] != null)
                {
                    var template = Map.currentMap.GetEntityModelTemplate(allTiles[i].GetEntityID());
                    var templatePathName = Utils.GetPathName(template.GetLODPrefabPath(0), true);
                    //UnityEngine.Debug.LogWarning("Used Template Path Name: " + templatePathName);
                    usedTemplateNames.Add(templatePathName);
                }
            }

            int count = 0;
            for (int i = 0; i < existedPrefabFiles.Count; ++i)
            {
                if (!IgnoreLODContains(usedTemplateNames, existedPrefabFiles[i]))
                {
                    //UnityEngine.Debug.Log("Delete " + fullPath[i]);
                    FileUtil.DeleteFileOrDirectory(fullPath[i]);
                    ++count;
                }
            }
            UnityEngine.Debug.Log("delete unsed prefab: " + count.ToString());
            AssetDatabase.Refresh();
        }

        //创建这个tile的多边形
        List<Vector3> CreateTilePolygon(int x, int y)
        {
            float minX = mLogic.layerData.tileWidth * x;
            float maxX = mLogic.layerData.tileWidth * (x + 1);
            float minZ = mLogic.layerData.tileHeight * y;
            float maxZ = mLogic.layerData.tileHeight * (y + 1);

            var polygon = new List<Vector3>()
            {
            new Vector3(minX, 0, minZ),
            new Vector3(maxX, 0, minZ),
            new Vector3(maxX, 0, maxZ),
            new Vector3(minX, 0, maxZ),
            };

            return polygon;
        }

        void DrawResizeOption()
        {
            if (GUILayout.Button(new GUIContent("Change Layer Size", "修改layer的大小")))
            {
                var dlg = EditorUtils.CreateInputDialog("Change Front Layer Size");

                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as EditorGridModelLayer;
                float layerWidth = layer.GetLayerData().GetLayerWidthInMeter();
                float totalSize = layerWidth + layer.tileWidth * 2;
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Size", "", totalSize.ToString()),
                };
                dlg.Show(items, OnClickChangeSize);
            }
        }

        bool OnClickChangeSize(List<InputDialog.Item> parameters)
        {
            string newSizeStr = (parameters[0] as InputDialog.StringItem).text;
            int newSize;
            bool suc = Utils.ParseInt(newSizeStr, out newSize);
            if (newSize > 0)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as EditorGridModelLayer;
                layer.Resize(newSize, newSize, true);
                mLogic.RecreateGrid();
                return true;
            }

            return false;
        }

        //尝试将generated prefabs替换成原来的prefab
        void RecoverPrefabs()
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            int h = layer.horizontalTileCount;
            int v = layer.verticalTileCount;

            var prefabManager = (Map.currentMap.data as EditorMapData).gridModelLayerPrefabManager;

            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    var tile = layer.GetObjectData(j, i);
                    if (tile != null)
                    {
                        var modelTemplate = tile.GetModelTemplate();
                        if (modelTemplate.generated)
                        {
                            string generatedPrefabName = Utils.GetPathName(modelTemplate.GetLODPrefabPath(0), false);
                            string originalPrefabName = MapCoreDef.GetOriginalPrefabNameFromGeneratedPrefabName(generatedPrefabName);
                            //try get full path from prefab group
                            string originalPrefabFullPath = prefabManager.FindPrefabPath(originalPrefabName + "_lod0");
                            if (!string.IsNullOrEmpty(originalPrefabFullPath))
                            {
                                UnityEngine.Debug.Log($"Recover prefab from {modelTemplate.GetLODPrefabPath(0)} to {originalPrefabFullPath}");
                                layer.RemoveObject(tile.GetEntityID());

                                int nextID = Map.currentMap.nextCustomObjectID;
                                modelTemplate = Map.currentMap.GetOrCreateModelTemplate(nextID, originalPrefabFullPath, true);
                                if (modelTemplate != null)
                                {
                                    var pos = layer.FromCoordinateToWorldPositionCenter(j, i);
                                    var modelData = new ModelData(nextID, Map.currentMap, (int)ObjectFlag.kUseRenderTextureModel, pos, Quaternion.identity, Vector3.one, modelTemplate, false);
                                    layer.AddObject(modelData);
                                }
                                else
                                {
                                    UnityEngine.Debug.LogError($"Recover prefab from {modelTemplate.GetLODPrefabPath(0)} to {originalPrefabFullPath} failed!");
                                }
                            }
                            else
                            {
                                UnityEngine.Debug.Assert(false, $"todo, not found {generatedPrefabName}");
                            }
                        }
                    }
                }
            }

            ActionManager.instance.Clear();
        }

        public static void UnlinkSharedPrefabs()
        {
            var exportFolder = SLGMakerEditor.instance.exportFolder;
            if (string.IsNullOrEmpty(exportFolder))
            {
                EditorUtility.DisplayDialog("Error", "Set export folder first!", "OK");
                return;
            }

            var map = Map.currentMap;
            var gridLayer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            if (gridLayer == null)
            {
                return;
            }

            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            //delete unused prefabs
            var prefabFolder = exportFolder + MapCoreDef.MAP_GENERATED_180M_TERRAIN_PREFABS_SUBFOLDER;

            DeleteUnusedPrefabsInGeneratedPrefabFolder(prefabFolder, gridLayer.layerData);

            var blendLayer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            if (blendLayer == null)
            {
                return;
            }

            if (gridLayer.horizontalTileCount != blendLayer.horizontalTileCount ||
                gridLayer.verticalTileCount != blendLayer.verticalTileCount)
            {
                EditorUtility.DisplayDialog("Error", "Invalid size!", "OK");
                return;
            }

            int horizontalTileCount = gridLayer.horizontalTileCount;
            int verticalTileCount = gridLayer.verticalTileCount;

            TilePrefabModifier.testCount = 0;
            TilePrefabModifier.modifiedPrefabCount = 0;

            List<ModifiedPrefabData> modifiedPrefabPath = new List<ModifiedPrefabData>();
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    var objData = gridLayer.GetObjectData(j, i);
                    if (objData != null)
                    {
                        //grid model layer和blend layer必须是一样的大小
                        var tile = blendLayer.GetTile(j, i);
                        if (tile != null && tile.heights != null)
                        {
                            string newPrefabPath = CreateNewPrefab(objData, j, i, gridLayer);
                            if (!string.IsNullOrEmpty(newPrefabPath))
                            {
                                var data = new ModifiedPrefabData();
                                data.prefabPath = newPrefabPath;
                                data.x = j;
                                data.y = i;
                                modifiedPrefabPath.Add(data);
                            }
                        }
                    }
                }
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            for (int k = 0; k < modifiedPrefabPath.Count; ++k)
            {
                gridLayer.ReplaceObjectModelTemplate(modifiedPrefabPath[k].x, modifiedPrefabPath[k].y, modifiedPrefabPath[k].prefabPath);
            }

            double elapsedTime = w.Stop();
            UnityEngine.Debug.Log("Modify prefabs elapsed: " + elapsedTime.ToString() + " seconds");
            UnityEngine.Debug.LogError("test Count: " + TilePrefabModifier.testCount);
            UnityEngine.Debug.LogError("modifiedPrefabCount Count: " + TilePrefabModifier.modifiedPrefabCount);
        }

        //如果某个装饰物地块所在的地表有高度,则需要创建一个新的装饰物180m prefab
        static string CreateNewPrefab(IMapObjectData objData, int x, int y, EditorGridModelLayer layer)
        {
            StopWatchWrapper w1 = new StopWatchWrapper();
            var modelTemplate = Map.currentMap.FindObject(objData.GetModelTemplateID()) as ModelTemplate;
            string oldPrefabPath = modelTemplate.GetLODPrefabPath(0);

            var idx = y * layer.horizontalTileCount + x;
            var modifier = new TilePrefabModifier(modelTemplate.generated, new Vector3(layer.tileWidth * x, 0, layer.tileHeight * y), new Vector2(layer.tileWidth, layer.tileHeight), oldPrefabPath, idx);

            var exportFolder = SLGMakerEditor.instance.exportFolder;
            var prefabFolder = exportFolder + MapCoreDef.MAP_GENERATED_180M_TERRAIN_PREFABS_SUBFOLDER;

            if (!Directory.Exists(prefabFolder))
            {
                Directory.CreateDirectory(prefabFolder);
            }
            prefabFolder = Utils.ConvertToUnityAssetsPath(prefabFolder);
            string newPrefabPath = modifier.SavePrefab(prefabFolder, true);
            return newPrefabPath;
        }

        void EditPrefab(PrefabGroup.Item item, int groupID, int index)
        {
            var dlg = EditorUtils.CreateInputDialog("Set Tile Size");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Width", "", item.size.x.ToString()),
                    new InputDialog.StringItem("Height", "", item.size.y.ToString()),
                };
            dlg.Show(items, (List<InputDialog.Item> parameters) => {
                string widthStr = (parameters[0] as InputDialog.StringItem).text;
                string heightStr = (parameters[1] as InputDialog.StringItem).text;
                bool ok = int.TryParse(widthStr, out int width);
                if (!ok)
                {
                    return false;
                }
                ok = int.TryParse(heightStr, out int height);
                if (!ok)
                {
                    return false;
                }

                return SetPrefabSize(item, groupID, index, width, height);
            });
        }

        bool SetPrefabSize(PrefabGroup.Item item, int groupID, int index, int width, int height)
        {
            width = Mathf.Max(1, width);
            height = Mathf.Max(1, height);
            var newSize = new Vector2Int(width, height);
            if (newSize != item.size)
            {
                if (EditorUtility.DisplayDialog("警告", "改变tile大小后会重新创建block prefab?", "继续", "取消"))
                {
                    var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(0, item.prefabPath, true);
                    modelTemplate.SetBlockSize(width, height, mLogic.layerData.tileWidth);
                    item.size = newSize;
#if false
                    var terrainPrefabManager = Map.currentMap.data.varyingTileSizeTerrainPrefabManager;
                    terrainPrefabManager.SetGroupPrefabSizeByID(groupID, index, new Vector2Int(width, height));
                    //清理使用了这个prefab的地表tile
                    var layer = mLogic.layer;
                    int h = layer.horizontalTileCount;
                    int v = layer.verticalTileCount;
                    for (int i = 0; i < v; ++i)
                    {
                        for (int j = 0; j < h; ++j)
                        {
                            var tile = layer.GetTile(j, i);
                            if (tile != null)
                            {
                                if (tile.type == groupID && tile.tileID == item.id)
                                {
                                    layer.SetTile(j, i, 0, 0, 0);
                                }
                            }
                        }
                    }
#endif
                    return true;
                }
            }
            return false;
        }

        GameObject mPrefab;

        bool mLeftButtonDown = false;
        Vector2Int mLastCoord = new Vector2Int(-1, -1);
        GridModelLayerLogic mLogic;
        TileIndicator mColorIndicator;
        TileIndicator mTextureModelIndicator;
        string[] mTextureModelOperationNames;
    }
}

#endif