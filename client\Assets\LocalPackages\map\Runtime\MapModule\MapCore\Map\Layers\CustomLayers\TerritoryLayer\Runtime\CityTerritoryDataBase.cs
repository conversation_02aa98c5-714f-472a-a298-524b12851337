﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public abstract class CityTerritoryDataBase : ModelData
    {
        public CityTerritoryDataBase(int id, Map map, int flag, Vector3 position, ModelTemplate modelTemplate, Rect bounds, int territoryID, Material material, CityTerritoryDataBlock ownerBlock, CityTerritoryLayerData.SubLayerData subLayer, int index, Texture2D maskTexture) : base(id, map, flag, position, Quaternion.identity, Vector3.one, modelTemplate, false)
        {
            mBounds = bounds;
            mTerritoryID = territoryID;
            mOwnerBlock = ownerBlock;
            mSubLayer = subLayer;
            mIndex = index;
            mMaskTexture = maskTexture;
            if (material != null)
            {
                mMaterial = Material.Instantiate(material);
            }
        }

        public override void OnDestroy()
        {
            if (mMaterial != null)
            {
                Utils.DestroyObject(mMaterial);
            }
        }

        public override bool IsObjActive()
        {
            return mSubLayer.isActive && base.IsObjActive();
        }

        public bool IsObjectSelfActive()
        {
            return base.IsObjActive();
        }

        public abstract void SetAlpha(float alpha);

        public int territoryID { get { return mTerritoryID; } }
        public int index { get { return mIndex; } }
        public CityTerritoryLayerData.SubLayerData subLayer { get { return mSubLayer; } }
        public Material material { get { return mMaterial; } }
        public CityTerritoryDataBlock ownerBlock { get { return mOwnerBlock; } }
        public Texture2D maskTexture { get { return mMaskTexture; } }
        public virtual Color color { get { return Color.black; } set { } }

        int mTerritoryID;
        //用来直接设置mask texture的像素
        int mIndex;
        Material mMaterial;
        Texture2D mMaskTexture;
        CityTerritoryDataBlock mOwnerBlock;
        CityTerritoryLayerData.SubLayerData mSubLayer;
    }
}
