﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public class MapCameras
    {
        public MapCameras(GameObject mapCameraRoot, bool sceneView)
        {
            if (sceneView)
            {
                mCameraContainerObject = mapCameraRoot;
                var mainCamera = mapCameraRoot.GetComponent<Camera>();
                Debug.Assert(mainCamera != null);
                mCameras.Add(mainCamera);
            }
            else
            {
                var oldVersionMainCamera = mapCameraRoot.transform.Find(MapCoreDef.MAP_OLD_VERSION_MAIN_CAMERA_NAME);
                if (oldVersionMainCamera != null)
                {
                    //为了兼容老版本,只支持一个相机更新
                    if (oldVersionMainCamera.gameObject.activeSelf)
                    {
                        mCameraContainerObject = oldVersionMainCamera.gameObject;
                        var mainCamera = oldVersionMainCamera.GetComponent<Camera>();
                        Debug.Assert(mainCamera != null);
                        mCameras.Add(mainCamera);
                    }
                }

                if (mCameraContainerObject == null)
                {
                    //新版本支持多个相机同时渲染
                    var cameraContainer = mapCameraRoot.transform.Find(MapCoreDef.MAP_NEW_VERSION_CAMERA_CONTAINER_NAME);
                    Debug.Assert(cameraContainer != null, $"camera container {MapCoreDef.MAP_NEW_VERSION_CAMERA_CONTAINER_NAME} not found!");
                    mCameraContainerObject = cameraContainer.gameObject;
                    var cameras = cameraContainer.GetComponentsInChildren<Camera>(true);
                    mCameras.AddRange(cameras);
                }
            }
        }

        public float fieldOfView
        {
            set
            {
                for (int i = 0; i < mCameras.Count; ++i)
                {
                    mCameras[i].fieldOfView = value;
                }
            }
            get
            {
                return mCameras[0].fieldOfView;
            }
        }

        public float orthographicSize
        {
            set
            {
                if (value == 0f)
                {
                    //当 orthographicSize 为0时会抛异常
                    //Screen position out of view frustum (screen pos 0.000000, 0.000000) (Camera rect 0 0 1080 2400)
                    value = 0.1f;
                    Debug.LogErrorFormat("orthographicSize={0}", value);
                }

                for (int i = 0; i < mCameras.Count; ++i)
                {
                    mCameras[i].orthographicSize = value;
                }
            }
            get
            {
                return mCameras[0].orthographicSize;
            }
        }

        public bool orthographic
        {
            set
            {
                for (int i = 0; i < mCameras.Count; ++i)
                {
                    mCameras[i].orthographic = value;
                }
            }
            get
            {
                return mCameras[0].orthographic;
            }
        }

        public float farClipPlane
        {
            set
            {
                if (nearClipPlane == value)
                {
                    Debug.LogErrorFormat("nearClipPlane={0},farClipPlane={1}", nearClipPlane, value);
                    //当 farClipPlane == nearClipPlane 时会抛异常
                    //Screen position out of view frustum (screen pos 0.000000, 0.000000) (Camera rect 0 0 1080 2400)
                    value = value + 0.1f;
                }

                for (int i = 0; i < mCameras.Count; ++i)
                {
                    mCameras[i].farClipPlane = value;
                }
            }
            get
            {
                return mCameras[0].farClipPlane;
            }
        }

        public float nearClipPlane
        {
            set
            {
                if (farClipPlane == value)
                {
                    Debug.LogErrorFormat("farClipPlane={0},nearClipPlane={1}", farClipPlane, value);
                    //当 farClipPlane == nearClipPlane 时会抛异常
                    //Screen position out of view frustum (screen pos 0.000000, 0.000000) (Camera rect 0 0 1080 2400)
                    value = value + 0.1f;
                }
                for (int i = 0; i < mCameras.Count; ++i)
                {
                    mCameras[i].nearClipPlane = value;
                }
            }
            get
            {
                return mCameras[0].nearClipPlane;
            }
        }

        public Vector3 ViewportToWorldPoint(Vector3 viewportPos)
        {
            if (firstCamera != null)
            {
                return firstCamera.ViewportToWorldPoint(viewportPos);
            }
            return Vector3.zero;
        }

        public Vector3 WorldToViewportPoint(Vector3 worldPos)
        {
            if (firstCamera != null)
            {
                return firstCamera.WorldToViewportPoint(worldPos);
            }
            return Vector3.zero;
        }

        public Vector3 WorldToScreenPoint(Vector3 worldPos)
        {
            if (firstCamera != null)
            {
                return firstCamera.WorldToScreenPoint(worldPos);
            }
            return Vector3.zero;
        }

        public Vector3 ScreenToWorldPoint(Vector3 screenPos)
        {
            if (firstCamera != null)
            {
                return firstCamera.ScreenToWorldPoint(screenPos);
            }
            return Vector3.zero;
        }

        public T GetComponent<T>() where T : UnityEngine.Component
        {
            for (int i = 0; i < mCameras.Count; ++i)
            {
                var comp = mCameras[i].GetComponent<T>();
                if (comp != null)
                {
                    return comp;
                }
            }
            return null;
        }

#if UNITY_EDITOR
        void RecoverSceneViewCamera()
        {
            if (mCameraContainerObject == null)
            {
                var sceneCameras = SceneView.GetAllSceneCameras();
                if (sceneCameras.Length > 0)
                {
                    mCameraContainerObject = sceneCameras[0].gameObject;
                }
                mCameras[0] = sceneCameras[0];
            }
        }
#endif

        public bool enabled
        {
            set
            {
                for (int i = 0; i < mCameras.Count; ++i)
                {
                    mCameras[i].enabled = value;
                }
            }
        }

        public bool enableAudioListener { set
            {
                for (int i = 0; i < mCameras.Count; ++i)
                {
                    var listener = mCameras[i].GetComponent<AudioListener>();
                    if (listener != null)
                    {
                        listener.enabled = value;
                    }
                }
            } 
        }

        public Transform transform { 
            get
            {
#if UNITY_EDITOR
                RecoverSceneViewCamera();
#endif
                return mCameraContainerObject.transform; 
            }
        }
        public GameObject gameObject { get { return transform.gameObject; } }
        public Camera firstCamera { 
            get {
#if UNITY_EDITOR
                RecoverSceneViewCamera();
#endif
                return mCameras[0]; 
            } 
        }
        public List<Camera> cameras { get { return mCameras; } }

        GameObject mCameraContainerObject;
        List<Camera> mCameras = new List<Camera>();
    }
}
