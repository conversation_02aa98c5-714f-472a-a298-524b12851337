{"skeleton": {"hash": "CoiwhP/+I4w", "spine": "4.2.33", "x": -375.02, "y": -223.41, "width": 809.98, "height": 805.36, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "people_b", "parent": "root", "x": 25.65, "y": -90.26}, {"name": "people_b2", "parent": "people_b", "length": 9.21, "rotation": 92.73, "x": -0.22, "y": 4.82}, {"name": "people_b3", "parent": "people_b2", "length": 15.02, "rotation": -0.22, "x": 9.21}, {"name": "people_b4", "parent": "people_b3", "length": 17.85, "rotation": -2.51, "x": 15.02}, {"name": "people_b5", "parent": "people_b3", "length": 17.38, "rotation": 147.62, "x": 10.84, "y": 10.83}, {"name": "people_a", "parent": "root", "x": -225.59, "y": 25.23}, {"name": "people_a2", "parent": "people_a", "length": 10.67, "rotation": 94.48, "x": 0.13, "y": 4.57}, {"name": "people_a3", "parent": "people_a2", "length": 18.61, "rotation": 0.01, "x": 10.67}, {"name": "people_a4", "parent": "people_a3", "length": 17.93, "rotation": -4.83, "x": 18.61}, {"name": "people_a5", "parent": "people_a3", "length": 25.52, "rotation": -166.65, "x": 8.66, "y": -9.57}, {"name": "people_a6", "parent": "people_a3", "length": 22.59, "rotation": 166.48, "x": 14.97, "y": 8.13}, {"name": "tree_Ra", "parent": "root", "x": 305.94, "y": -3.39}, {"name": "tree_Ra2", "parent": "tree_Ra", "length": 57.85, "rotation": 76.47, "x": 2.11, "y": 10.23}, {"name": "tree_Ra3", "parent": "tree_Ra2", "length": 86.44, "rotation": 4.72, "x": 57.85}, {"name": "tree_Ra4", "parent": "tree_Ra3", "length": 88.66, "rotation": -0.37, "x": 86.44}, {"name": "tree_Ra5", "parent": "tree_Ra4", "length": 91.96, "rotation": 1.47, "x": 88.66}, {"name": "tree_Rb", "parent": "root", "x": 225.9, "y": 220.25}, {"name": "tree_Rb2", "parent": "tree_Rb", "length": 56.19, "rotation": 82, "x": 2.71, "y": 10.83}, {"name": "tree_Rb3", "parent": "tree_Rb2", "length": 79.71, "rotation": -2, "x": 56.19}, {"name": "tree_Rb4", "parent": "tree_Rb3", "length": 73.26, "rotation": -1.13, "x": 79.71}, {"name": "tree_Rb5", "parent": "tree_Rb4", "length": 74.26, "rotation": -0.32, "x": 73.26}, {"name": "tree_La", "parent": "root", "x": -143.73, "y": 195.44}, {"name": "tree_La2", "parent": "tree_La", "length": 53.96, "rotation": 93.84, "x": -1.5, "y": 7.22}, {"name": "tree_La3", "parent": "tree_La2", "length": 82.78, "rotation": 4.94, "x": 53.96}, {"name": "tree_La4", "parent": "tree_La3", "length": 78.81, "rotation": -7.9, "x": 82.78}, {"name": "tree_La5", "parent": "tree_La4", "length": 84.51, "rotation": -0.87, "x": 78.81}, {"name": "tree_Lb", "parent": "root", "x": -38.06, "y": 300.53}, {"name": "tree_Lb2", "parent": "tree_Lb", "length": 76.43, "rotation": 91.8, "x": -0.3, "y": 11.43}, {"name": "tree_Lb3", "parent": "tree_Lb2", "length": 75.19, "rotation": -1.8, "x": 76.43}, {"name": "tree_Lb4", "parent": "tree_Lb3", "length": 79.4, "x": 75.19}, {"name": "tree_B", "parent": "root", "x": 171.93, "y": 244.37}, {"name": "tree_B2", "parent": "tree_B", "length": 93.96, "rotation": 81.53, "x": 1.8, "y": 18.95}, {"name": "tree_B3", "parent": "tree_B2", "length": 81.89, "rotation": -1.69, "x": 93.96}, {"name": "tree_B4", "parent": "tree_B3", "length": 72.59, "rotation": 0.61, "x": 81.89}, {"name": "tree_B5", "parent": "tree_B4", "length": 75.41, "rotation": -0.34, "x": 72.59}, {"name": "A", "parent": "root", "length": 100.05, "rotation": 86.35, "x": 192.33, "y": 142.99}, {"name": "water", "parent": "root", "x": -207.71, "y": 132.05}, {"name": "water2", "parent": "root", "x": -243.03, "y": 96.32}, {"name": "water3", "parent": "root", "x": -281.01, "y": 61.88}, {"name": "water4", "parent": "root", "x": -239.64, "y": -2.04}, {"name": "water5", "parent": "root", "x": -184.9, "y": 43.68}, {"name": "water6", "parent": "root", "x": -159.48, "y": -46.95}, {"name": "water7", "parent": "root", "x": -118.27, "y": -2.34}, {"name": "water8", "parent": "root", "x": -80.75, "y": -79.07}, {"name": "water9", "parent": "root", "x": -44.23, "y": -20.53}, {"name": "water10", "parent": "root", "x": 1.07, "y": -110.44}, {"name": "water11", "parent": "root", "x": 36.61, "y": -37.58}, {"name": "water12", "parent": "root", "x": 99.62, "y": -80.6}, {"name": "water13", "parent": "root", "x": 104.18, "y": -35.4}, {"name": "water14", "parent": "root", "x": 179.32, "y": -35.86}], "slots": [{"name": "tree_B", "bone": "tree_B", "attachment": "tree_B"}, {"name": "tree_Lb", "bone": "tree_Lb", "attachment": "tree_Lb"}, {"name": "tree_Rb", "bone": "tree_Rb", "attachment": "tree_Rb"}, {"name": "Castle", "bone": "root", "attachment": "Castle"}, {"name": "tree_La", "bone": "tree_La", "attachment": "tree_La"}, {"name": "Castle2", "bone": "root", "attachment": "Castle"}, {"name": "people_a", "bone": "people_a", "attachment": "people_a"}, {"name": "people_b", "bone": "people_b", "attachment": "people_b"}, {"name": "A", "bone": "A", "attachment": "A"}, {"name": "tree_Ra", "bone": "tree_Ra", "attachment": "tree_Ra"}, {"name": "light", "bone": "root", "color": "ffffff95", "attachment": "light", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"A": {"A": {"type": "mesh", "uvs": [0.55564, 0, 0.75796, 0.04382, 0.97628, 0.16938, 0.9922, 0.35241, 0.81339, 0.45938, 0.50867, 0.48764, 0.46877, 0.99455, 0.41559, 0.99106, 0.45727, 0.48493, 0.12691, 0.44098, 0, 0.24064, 0, 0.23862, 0.08778, 0.07409, 0.43538, 0], "triangles": [10, 11, 12, 9, 12, 13, 10, 12, 9, 4, 1, 2, 4, 2, 3, 8, 13, 0, 5, 8, 0, 1, 5, 0, 9, 13, 8, 4, 5, 1, 6, 7, 8, 5, 6, 8], "vertices": [106.08, -4.13, 102.52, -23.41, 90.17, -44.76, 70.36, -47.53, 57.65, -31.5, 52.75, -3.11, -2.63, -2.89, -2.57, 2.13, 52.74, 1.73, 55.54, 33.03, 76.57, 46.33, 76.79, 46.34, 95.22, 39.25, 105.36, 7.16], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 8, 10, 4, 6, 6, 8, 10, 12], "width": 94, "height": 109}}, "Castle": {"Castle": {"type": "mesh", "uvs": [0.57119, 0, 0.83043, 0.15697, 0.82778, 0.1879, 0.77907, 0.21433, 0.77927, 0.27333, 1, 0.55869, 1, 0.61716, 0.97664, 0.76679, 0.61018, 0.97767, 0.52191, 1, 0.45961, 1, 0.36311, 0.9828, 0.01668, 0.73818, 0, 0.52043, 0.01937, 0.44086, 0.37357, 0.23935, 0.31254, 0.21277, 0.32116, 0.15179, 0.14286, 0.46268, 0.246, 0.40331, 0.27754, 0.39628, 0.27316, 0.43013, 0.25521, 0.48221, 0.29112, 0.54028, 0.30148, 0.5658, 0.33049, 0.5934, 0.37256, 0.61544, 0.44323, 0.63471, 0.54292, 0.63445, 0.64264, 0.65974, 0.71286, 0.67041, 0.78492, 0.65948, 0.80656, 0.66755, 0.82682, 0.69072, 0.82452, 0.71051, 0.80886, 0.72952, 0.60443, 0.84696, 0.5641, 0.85906, 0.50216, 0.86781, 0.44667, 0.8613, 0.38567, 0.84515, 0.33071, 0.8113, 0.12569, 0.68155, 0.2403, 0.75836, 0.09523, 0.64613, 0.07957, 0.61306, 0.07497, 0.57635, 0.08165, 0.53442, 0.10605, 0.4925, 0.20191, 0.43094, 0.25672, 0.42263, 0.23002, 0.45092, 0.18847, 0.48305, 0.14862, 0.51374, 0.11301, 0.5449, 0.12276, 0.60676, 0.1588, 0.57559, 0.19356, 0.54394, 0.22832, 0.51565, 0.15371, 0.65279, 0.18508, 0.61875, 0.22027, 0.58518, 0.25842, 0.55641, 0.20288, 0.68923, 0.23892, 0.64895, 0.27834, 0.60916, 0.25291, 0.71553, 0.2936, 0.67285, 0.3254, 0.63641, 0.30675, 0.75005, 0.35041, 0.77642, 0.3822, 0.72272, 0.41654, 0.66998, 0.36397, 0.65607, 0.34151, 0.7045, 0.40225, 0.79844, 0.43532, 0.74426, 0.46881, 0.69775, 0.49308, 0.66137, 0.46033, 0.81954, 0.48681, 0.76941, 0.51332, 0.72508, 0.5443, 0.67709, 0.59323, 0.69344, 0.57245, 0.74776, 0.54673, 0.79336, 0.5256, 0.83288, 0.62889, 0.75531, 0.64914, 0.70369, 0.69348, 0.72029, 0.73353, 0.73178, 0.67533, 0.76842, 0.75551, 0.69288, 0.79692, 0.69192, 0.75693, 0.66372, 0.67775, 0.66507, 0.59278, 0.64709, 0.49307, 0.63458, 0.60026, 0.81052, 0.65444, 0.81823, 0.71221, 0.78504, 0.76054, 0.75728, 0.77437, 0.72799, 0.28551, 0.78483, 0.183, 0.71995], "triangles": [72, 27, 77, 72, 73, 26, 77, 27, 78, 78, 28, 82, 102, 93, 35, 98, 87, 99, 38, 79, 86, 40, 70, 75, 41, 69, 70, 43, 66, 69, 46, 54, 55, 54, 48, 53, 53, 18, 52, 52, 49, 51, 21, 51, 50, 51, 19, 50, 27, 72, 26, 27, 97, 78, 88, 29, 95, 83, 96, 29, 29, 96, 31, 89, 95, 30, 95, 29, 94, 35, 7, 101, 100, 90, 101, 38, 86, 37, 9, 38, 37, 39, 79, 38, 11, 40, 39, 41, 70, 40, 103, 41, 11, 103, 43, 69, 12, 104, 43, 66, 104, 63, 42, 59, 104, 12, 45, 44, 45, 46, 55, 52, 18, 49, 19, 49, 14, 50, 19, 20, 20, 25, 21, 20, 19, 15, 25, 20, 26, 26, 20, 27, 7, 33, 6, 6, 32, 5, 5, 31, 96, 96, 28, 4, 4, 5, 96, 2, 3, 1, 1, 3, 0, 20, 15, 27, 4, 15, 3, 3, 15, 0, 15, 19, 14, 15, 17, 0, 28, 97, 15, 15, 4, 28, 47, 13, 14, 48, 14, 18, 45, 12, 13, 10, 38, 9, 11, 39, 10, 37, 36, 8, 7, 8, 101, 9, 37, 8, 43, 11, 12, 16, 17, 15, 51, 49, 19, 18, 14, 49, 48, 47, 14, 48, 18, 53, 46, 13, 47, 47, 48, 54, 45, 13, 46, 46, 47, 54, 12, 44, 42, 44, 55, 59, 44, 45, 55, 12, 42, 104, 42, 44, 59, 43, 103, 11, 43, 104, 66, 11, 41, 40, 103, 69, 41, 10, 39, 38, 40, 75, 39, 8, 36, 99, 37, 98, 36, 8, 99, 100, 36, 98, 99, 8, 100, 101, 99, 91, 100, 91, 89, 100, 7, 35, 34, 101, 102, 35, 34, 33, 7, 35, 93, 34, 34, 93, 33, 33, 32, 6, 93, 32, 33, 5, 32, 31, 93, 31, 32, 94, 29, 31, 31, 92, 94, 30, 95, 94, 92, 30, 94, 82, 28, 96, 78, 97, 28, 15, 97, 27, 68, 25, 26, 68, 65, 25, 23, 21, 25, 65, 24, 25, 25, 24, 23, 24, 62, 23, 23, 62, 22, 62, 58, 22, 22, 21, 23, 21, 22, 51, 21, 50, 20, 58, 51, 22, 104, 59, 63, 39, 75, 79, 86, 85, 37, 37, 85, 98, 99, 87, 91, 100, 89, 90, 90, 89, 30, 101, 90, 102, 88, 95, 89, 83, 29, 88, 82, 96, 83, 73, 68, 26, 24, 65, 62, 63, 60, 64, 60, 61, 64, 60, 56, 61, 56, 57, 61, 55, 56, 60, 59, 55, 60, 86, 80, 85, 85, 84, 98, 80, 81, 85, 85, 81, 84, 90, 92, 102, 92, 90, 30, 102, 92, 93, 92, 31, 93, 87, 88, 91, 91, 88, 89, 87, 83, 88, 98, 84, 87, 84, 83, 87, 81, 82, 84, 84, 82, 83, 81, 78, 82, 79, 80, 86, 76, 77, 80, 80, 77, 81, 71, 72, 76, 76, 72, 77, 77, 78, 81, 79, 76, 80, 75, 76, 79, 70, 71, 75, 75, 71, 76, 70, 74, 71, 74, 73, 71, 71, 73, 72, 67, 68, 74, 74, 68, 73, 67, 65, 68, 69, 74, 70, 66, 67, 69, 69, 67, 74, 66, 64, 67, 64, 65, 67, 64, 61, 65, 61, 62, 65, 61, 58, 62, 63, 64, 66, 59, 60, 63, 55, 54, 56, 54, 53, 56, 61, 57, 58, 56, 53, 57, 53, 52, 57, 57, 52, 58, 58, 52, 51], "vertices": [1, 0, 48.8, 432.59, 1, 1, 0, 241.16, 329.62, 1, 1, 0, 239.19, 309.34, 1, 1, 0, 203.04, 292, 1, 1, 0, 203.19, 253.29, 1, 1, 0, 366.98, 66.1, 1, 1, 0, 366.98, 27.73, 1, 1, 0, 349.64, -70.42, 1, 1, 0, 77.73, -208.76, 1, 1, 0, 12.24, -223.41, 1, 1, 0, -34, -223.41, 1, 1, 0, -105.59, -212.12, 1, 1, 0, -362.65, -51.65, 1, 1, 0, -375.02, 91.19, 1, 1, 0, -360.65, 143.39, 1, 1, 0, -97.84, 275.58, 1, 1, 0, -143.12, 293.02, 1, 1, 0, -136.72, 333.02, 1, 1, 0, -269.02, 129.07, 1, 1, 0, -192.49, 168.02, 1, 1, 0, -169.09, 172.63, 1, 1, 0, -172.34, 150.43, 1, 1, 0, -185.66, 116.26, 1, 1, 0, -159.01, 78.17, 1, 1, 0, -151.33, 61.43, 1, 1, 0, -129.8, 43.32, 1, 1, 0, -98.59, 28.86, 1, 1, 0, -46.15, 16.22, 1, 1, 0, 27.82, 16.39, 1, 1, 0, 101.82, -0.19, 1, 1, 0, 153.92, -7.2, 1, 1, 0, 207.39, -0.02, 1, 1, 0, 223.44, -5.32, 1, 1, 0, 238.47, -20.52, 1, 1, 0, 236.77, -33.5, 1, 1, 0, 225.15, -45.97, 1, 1, 0, 73.46, -123.01, 1, 1, 0, 43.54, -130.95, 1, 1, 0, -2.42, -136.69, 1, 1, 0, -43.59, -132.42, 1, 1, 0, -88.86, -121.83, 1, 1, 0, -129.64, -99.62, 1, 1, 0, -281.76, -14.5, 1, 1, 0, -196.72, -64.89, 1, 1, 0, -304.36, 8.73, 1, 1, 0, -315.98, 30.43, 1, 1, 0, -319.4, 54.51, 1, 1, 0, -314.44, 82.01, 1, 1, 0, -296.34, 109.52, 1, 1, 0, -225.21, 149.9, 1, 9, 37, 23.18, 23.29, 0.54717, 38, 58.49, 59.02, 0.0004, 41, 0.36, 111.67, 0.03167, 43, -66.27, 157.69, 0.02334, 45, -140.3, 175.88, 0.02626, 47, -221.15, 192.93, 0.01252, 49, -288.72, 190.74, 0.01688, 50, -363.86, 191.21, 0.00312, 0, -184.54, 155.35, 0.33865, 8, 37, 3.36, 4.74, 0.76178, 41, -19.45, 93.11, 0.00138, 43, -86.08, 139.13, 0.00314, 45, -160.12, 157.32, 0.0034, 47, -240.96, 174.37, 0.00158, 49, -308.53, 172.19, 0.00214, 50, -383.68, 172.65, 0.00038, 0, -204.35, 136.79, 0.22619, 5, 37, -27.46, -16.34, 0.25303, 38, 7.85, 19.39, 0.47674, 39, 45.84, 53.84, 0.00147, 41, -50.28, 72.04, 0.00708, 0, -235.18, 115.71, 0.26168, 5, 37, -57.03, -36.47, 0.01669, 38, -21.72, -0.74, 0.47161, 39, 16.27, 33.71, 0.21773, 6, -39.16, 70.36, 0.00072, 0, -264.75, 95.58, 0.29325, 4, 37, -83.45, -56.92, 0.00203, 38, -48.14, -21.19, 0.08947, 39, -10.15, 13.26, 0.53978, 0, -291.17, 75.14, 0.36872, 4, 39, -2.92, -27.31, 0.46019, 40, -44.3, 36.6, 0.17048, 6, -58.35, 9.33, 0.05895, 0, -283.93, 34.56, 0.31038, 6, 38, -14.17, -41.32, 0.16812, 39, 23.82, -6.87, 0.42648, 40, -17.56, 57.05, 0.03559, 41, -72.3, 11.33, 0.01505, 6, -31.61, 29.78, 0.23069, 0, -257.2, 55.01, 0.12408, 6, 37, -23.69, -56.29, 0.02672, 38, 11.62, -20.56, 0.53005, 39, 49.61, 13.89, 0.06866, 41, -46.5, 32.09, 0.1286, 6, -5.82, 50.54, 0.192, 0, -231.4, 75.77, 0.05396, 10, 37, 2.1, -37.73, 0.28856, 38, 37.42, -2, 0.26466, 41, -20.71, 50.65, 0.22233, 43, -87.34, 96.67, 0.00705, 45, -161.38, 114.86, 0.0032, 47, -242.22, 131.91, 0.00063, 49, -309.79, 129.72, 0.00072, 50, -384.93, 130.19, 3e-05, 6, 19.98, 69.1, 0.04276, 0, -205.61, 94.33, 0.17007, 4, 39, 20.04, -57.51, 0.14804, 40, -21.33, 6.41, 0.51237, 6, -35.38, -20.86, 0.06504, 0, -260.97, 4.36, 0.27456, 5, 38, 5.33, -69.63, 0.02131, 39, 43.32, -35.18, 0.08485, 40, 1.94, 28.74, 0.11902, 6, -12.11, 1.47, 0.6965, 0, -237.69, 26.7, 0.07833, 7, 37, -3.87, -83.34, 0.0125, 38, 31.44, -47.61, 0.13152, 39, 69.43, -13.16, 0.01326, 41, -26.69, 5.04, 0.36705, 42, -52.1, 95.67, 0.00016, 6, 14, 23.49, 0.45822, 0, -211.59, 48.72, 0.01729, 10, 37, 24.44, -64.47, 0.11479, 38, 59.75, -28.74, 0.06106, 41, 1.62, 23.91, 0.53308, 43, -65.01, 69.93, 0.02732, 45, -139.04, 88.12, 0.00859, 47, -219.89, 105.17, 0.0018, 49, -287.46, 102.99, 0.0021, 50, -362.6, 103.45, 0.00014, 6, 42.31, 42.36, 0.02229, 0, -183.28, 67.59, 0.22883, 8, 39, 56.53, -81.42, 0.00058, 40, 15.15, -17.5, 0.54505, 41, -39.58, -63.22, 0.01899, 42, -65, 27.41, 0.15012, 43, -106.21, -17.2, 0.00156, 44, -143.73, 59.53, 0.00033, 6, 1.1, -44.77, 0.03503, 0, -224.48, -19.54, 0.24835, 6, 40, 41.89, 8.92, 0.1813, 41, -12.85, -36.8, 0.27848, 42, -38.26, 53.83, 0.15734, 43, -79.48, 9.22, 0.04787, 6, 27.84, -18.35, 0.3121, 0, -197.75, 6.88, 0.02291, 11, 37, 39.22, -99.07, 0.01233, 40, 71.14, 35.03, 0.00581, 41, 16.41, -10.69, 0.60903, 42, -9.01, 79.94, 0.03512, 43, -50.22, 35.33, 0.15777, 45, -124.26, 53.52, 0.00513, 47, -205.11, 70.57, 0.00073, 49, -272.68, 68.39, 0.00101, 50, -347.82, 68.85, 3e-05, 6, 57.09, 7.76, 0.01024, 0, -168.49, 32.99, 0.1628, 6, 40, 52.27, -34.75, 0.19032, 41, -2.47, -80.47, 0.03881, 42, -27.88, 10.16, 0.49023, 43, -69.1, -34.45, 0.01147, 6, 38.22, -62.02, 0.02636, 0, -187.37, -36.79, 0.24281, 6, 40, 82.47, -6.76, 0.03639, 41, 27.73, -52.48, 0.20607, 42, 2.31, 38.15, 0.36926, 43, -38.9, -6.46, 0.35285, 44, -76.42, 70.27, 0.0007, 6, 68.42, -34.02, 0.03473, 11, 37, 74.14, -116.95, 0.01609, 40, 106.06, 17.15, 0.00112, 41, 51.32, -28.57, 0.19853, 42, 25.9, 62.06, 0.03187, 43, -15.31, 17.45, 0.53818, 45, -89.35, 35.64, 0.01434, 47, -170.19, 52.69, 0.00158, 49, -237.76, 50.51, 0.00208, 50, -312.9, 50.97, 9e-05, 6, 92.01, -10.12, 0.00152, 0, -133.58, 15.11, 0.19461, 5, 42, 12.06, -12.49, 0.57772, 43, -29.15, -57.1, 0.0305, 44, -66.67, 19.63, 0.12419, 46, -148.49, 51, 0.00122, 0, -147.42, -59.44, 0.26636, 7, 40, 124.62, -74.7, 0.00058, 42, 44.46, -29.79, 0.23812, 43, 3.25, -74.4, 0.03956, 44, -34.27, 2.33, 0.42232, 45, -70.79, -56.21, 0.00178, 46, -116.09, 33.7, 0.00734, 0, -115.02, -76.74, 0.29031, 5, 42, 68.05, 5.44, 0.08575, 43, 26.84, -39.17, 0.28638, 44, -10.68, 37.56, 0.38258, 45, -47.2, -20.98, 0.21457, 0, -91.43, -41.51, 0.03072, 10, 37, 141.76, -138.96, 0.01011, 41, 118.95, -50.59, 0.00766, 42, 93.53, 40.04, 2e-05, 43, 52.32, -4.57, 0.2344, 44, 14.8, 72.16, 0.03029, 45, -21.72, 13.62, 0.51781, 47, -102.56, 30.67, 0.00456, 49, -170.13, 28.49, 0.0053, 50, -245.27, 28.96, 0.00024, 0, -65.95, -6.91, 0.18961, 9, 37, 102.76, -129.84, 0.00935, 41, 79.95, -41.47, 0.01501, 43, 13.32, 4.55, 0.6493, 44, -24.21, 81.28, 0.0103, 45, -60.72, 22.74, 0.11415, 47, -141.57, 39.79, 0.00151, 49, -209.14, 37.61, 0.00256, 50, -284.28, 38.08, 0.0001, 0, -104.95, 2.21, 0.19771, 5, 42, 37.86, 17.39, 0.28772, 43, -3.36, -27.22, 0.50383, 44, -40.88, 49.51, 0.14281, 45, -77.39, -9.03, 0.0318, 0, -121.63, -29.56, 0.03384, 7, 42, 82.93, -44.23, 0.01232, 44, 4.2, -12.11, 0.62823, 45, -32.32, -70.65, 9e-05, 46, -77.62, 19.25, 0.07197, 47, -113.16, -53.6, 2e-05, 1, -102.2, -0.92, 0.00024, 0, -76.55, -91.18, 0.28713, 7, 43, 66.25, -53.3, 0.01575, 44, 28.73, 23.43, 0.40264, 45, -7.78, -35.11, 0.42995, 46, -53.09, 54.8, 0.07722, 47, -88.63, -18.06, 0.01958, 1, -77.67, 34.62, 0.02487, 0, -52.02, -55.64, 0.02998, 10, 37, 180.55, -157.18, 0.00182, 41, 157.73, -68.81, 0.00064, 44, 53.58, 53.94, 0.01824, 45, 17.07, -4.6, 0.66949, 46, -28.24, 85.31, 0.01257, 47, -63.78, 12.45, 0.14091, 49, -131.35, 10.27, 0.00352, 50, -206.49, 10.74, 6e-05, 1, -52.82, 65.13, 0.02218, 0, -27.17, -25.13, 0.13058, 9, 37, 198.56, -133.32, 0.01609, 41, 175.74, -44.94, 0.00757, 43, 109.11, 1.08, 0.02252, 45, 35.08, 19.27, 0.37802, 47, -45.77, 36.32, 0.23883, 49, -113.34, 34.14, 0.03344, 50, -188.48, 34.6, 0.00197, 1, -34.81, 89, 0.00233, 0, -9.16, -1.26, 0.29924, 7, 42, 126.02, -58.07, 0.00154, 44, 47.29, -25.95, 0.2543, 45, 10.78, -84.49, 0.03075, 46, -34.53, 5.41, 0.41098, 47, -70.07, -67.44, 0.00465, 1, -59.11, -14.76, 0.00708, 0, -33.46, -105.02, 0.2907, 6, 44, 66.94, 6.93, 0.13732, 45, 30.42, -51.6, 0.19705, 46, -14.88, 38.3, 0.25595, 47, -50.43, -34.55, 0.13085, 1, -39.46, 18.13, 0.19797, 0, -13.81, -72.14, 0.08085, 8, 37, 213.57, -175.11, 0.0006, 41, 190.76, -86.74, 0.0001, 44, 86.61, 36.01, 0.0199, 45, 50.09, -22.52, 0.24404, 46, 4.79, 67.38, 0.03006, 47, -30.75, -5.48, 0.50393, 1, -19.79, 47.21, 0.1405, 0, 5.86, -43.06, 0.06086, 8, 37, 236.56, -143.63, 0.01067, 41, 213.74, -55.25, 0.00335, 43, 147.11, -9.23, 0.00689, 45, 73.08, 8.96, 0.12537, 47, -7.77, 26.01, 0.49451, 49, -75.34, 23.82, 0.07582, 50, -150.48, 24.29, 0.00265, 0, 28.85, -11.57, 0.28074, 10, 37, 272.87, -154.36, 0.00625, 41, 250.06, -65.98, 0.00135, 43, 183.43, -19.96, 0.0026, 45, 109.39, -1.77, 0.01924, 47, 28.54, 15.28, 0.4172, 48, -34.46, 58.3, 0.00868, 49, -39.03, 13.1, 0.31464, 50, -114.17, 13.56, 0.003, 1, 39.51, 67.96, 6e-05, 0, 65.16, -22.3, 0.22699, 5, 47, 13.12, -20.35, 0.5117, 48, -49.89, 22.67, 0.12171, 49, -54.45, -22.54, 0.06271, 1, 24.08, 32.33, 0.271, 0, 49.73, -57.93, 0.03287, 5, 47, -5.97, -50.27, 0.02197, 48, -68.97, -7.25, 0.02223, 49, -73.53, -52.45, 0.00036, 1, 5, 2.41, 0.758, 0, 30.65, -87.85, 0.19744, 4, 46, 13.9, -3.34, 0.42406, 48, -84.65, -33.17, 0.02997, 1, -10.68, -23.51, 0.20497, 0, 14.97, -113.78, 0.34099, 6, 47, 55, -25.31, 0.07278, 48, -8, 17.71, 0.46637, 49, -12.57, -27.49, 0.2904, 50, -87.71, -27.02, 0.00166, 1, 65.97, 27.37, 0.04117, 0, 91.62, -62.89, 0.12761, 8, 37, 314.35, -161.08, 0.00196, 41, 291.54, -72.71, 0.00028, 43, 224.91, -26.69, 0.00062, 45, 150.87, -8.49, 0.0033, 47, 70.03, 8.56, 0.01173, 49, 2.46, 6.37, 0.76544, 50, -72.69, 6.84, 0.02443, 0, 106.64, -29.03, 0.19225, 8, 37, 347.25, -171.97, 0.001, 41, 324.44, -83.59, 6e-05, 43, 257.81, -37.57, 0.00018, 45, 183.77, -19.38, 0.00088, 48, 39.92, 40.69, 0.12238, 49, 35.36, -4.52, 0.40256, 50, -39.78, -4.05, 0.36054, 0, 139.54, -39.91, 0.11239, 4, 48, 69.63, 33.15, 0.08916, 49, 65.07, -12.06, 0.0577, 50, -10.07, -11.59, 0.64394, 0, 169.25, -47.45, 0.2092, 5, 46, 125, 38.94, 0.00064, 48, 26.45, 9.11, 0.45848, 49, 21.89, -36.1, 0.14996, 50, -53.26, -35.63, 0.14208, 0, 126.07, -71.49, 0.24884, 8, 37, 393.28, -153.99, 0.00486, 41, 370.47, -65.61, 0.00026, 43, 303.84, -19.59, 0.00096, 45, 229.8, -1.4, 0.0046, 47, 148.95, 15.65, 0.00499, 49, 81.38, 13.46, 0.05174, 50, 6.24, 13.93, 0.80551, 0, 185.57, -21.93, 0.12706, 9, 37, 424, -153.36, 0.00552, 41, 401.19, -64.99, 0.00015, 43, 334.56, -18.97, 0.00084, 45, 260.52, -0.77, 0.0044, 47, 179.68, 16.28, 0.00377, 48, 116.67, 59.3, 0.00341, 49, 112.11, 14.09, 0.02091, 50, 36.96, 14.56, 0.67494, 0, 216.29, -21.31, 0.28605, 1, 0, 186.62, -2.81, 1, 1, 0, 127.87, -3.69, 1, 1, 0, 64.82, 8.1, 1, 1, 0, -9.16, 16.31, 1, 7, 46, 69.3, 11.33, 0.05002, 47, 33.76, -61.53, 0.02238, 48, -29.25, -18.51, 0.37522, 49, -33.81, -63.71, 0.0014, 50, -108.96, -63.24, 0.0001, 1, 44.72, -8.85, 0.23963, 0, 70.37, -99.11, 0.31124, 1, 0, 110.57, -104.17, 1, 1, 0, 153.44, -82.39, 1, 1, 0, 189.3, -64.18, 1, 7, 37, 407.27, -177.02, 0.00044, 43, 317.83, -42.63, 4e-05, 45, 243.79, -24.44, 0.00025, 47, 162.95, -7.39, 7e-05, 48, 99.94, 35.63, 0.03434, 50, 20.24, -9.1, 0.68153, 0, 199.56, -44.97, 0.28334, 1, 0, -163.18, -82.25, 1, 1, 0, -239.24, -39.69, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 28, 30, 26, 28, 34, 0, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 84, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 36, 36, 98, 98, 38, 60, 188, 188, 62, 58, 190, 190, 60, 56, 192, 192, 58, 54, 194, 194, 56, 198, 72, 200, 198, 70, 202, 202, 200, 82, 206, 206, 86, 84, 208, 208, 86], "width": 742, "height": 656}}, "Castle2": {"Castle": {"type": "mesh", "uvs": [0.57119, 0, 0.83043, 0.15697, 0.82778, 0.1879, 0.77907, 0.21433, 0.77927, 0.27333, 0.29258, 0.3903, 0.29297, 0.33821, 0.32093, 0.31111, 0.37499, 0.26963, 0.37357, 0.23935, 0.31254, 0.21277, 0.32116, 0.15179], "triangles": [1, 3, 0, 2, 3, 1, 9, 11, 0, 10, 11, 9, 3, 8, 9, 3, 9, 0, 8, 3, 4, 5, 6, 7, 5, 7, 8, 5, 8, 4], "vertices": [48.8, 432.59, 241.16, 329.62, 239.19, 309.34, 203.04, 292, 203.19, 253.29, -157.93, 176.56, -157.64, 210.73, -136.89, 228.5, -96.78, 255.72, -97.84, 275.58, -143.12, 293.02, -136.72, 333.02], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 18, 20, 20, 22, 22, 0, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8], "width": 742, "height": 656}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [2, 3, 4, 2, 4, 5, 1, 2, 5, 1, 5, 0], "vertices": [-140.01, 192.6, -34.73, 245.08, 8.53, 222.68, 8.63, 167.13, -20.93, 106.88, -80.47, 149.74], "hull": 6, "edges": [2, 4, 4, 6, 8, 10, 10, 0, 0, 2, 6, 8], "width": 118, "height": 97}}, "people_a": {"people_a": {"type": "mesh", "uvs": [0.09023, 0.31262, 0.21977, 0.27731, 0.15917, 0.20687, 0.28564, 0.01289, 0.58087, 0.01843, 0.64843, 0.16673, 0.60153, 0.26864, 0.54565, 0.32931, 0.68741, 0.36798, 0.76594, 0.40917, 0.81827, 0.5368, 0.89334, 0.68279, 0.98607, 0.88195, 0.89459, 0.90308, 0.78314, 0.77783, 0.67547, 0.63548, 0.67547, 0.73106, 0.7311, 0.84189, 0.75513, 0.91506, 0.50823, 0.99429, 0.18671, 0.859, 0.2579, 0.76356, 0.24614, 0.68089, 0.19731, 0.57922, 0.14261, 0.68156, 0.12702, 0.79233, 0.00825, 0.78391, 0.02638, 0.62133, 0.05157, 0.47075, 0.06062, 0.36305, 0.23025, 0.44694, 0.6286, 0.48964, 0.37918, 0.31475, 0.41783, 0.19272], "triangles": [31, 9, 10, 15, 31, 10, 15, 10, 11, 11, 14, 15, 14, 11, 12, 12, 13, 14, 28, 29, 30, 23, 28, 30, 27, 28, 23, 24, 27, 23, 26, 27, 24, 25, 26, 24, 29, 0, 30, 0, 1, 30, 33, 3, 4, 33, 4, 5, 2, 3, 33, 6, 33, 5, 1, 2, 33, 32, 1, 33, 32, 33, 6, 7, 32, 6, 30, 1, 32, 31, 15, 23, 31, 7, 8, 7, 30, 32, 7, 31, 30, 31, 8, 9, 23, 30, 31, 23, 15, 22, 22, 16, 21, 16, 22, 15, 17, 21, 16, 17, 19, 21, 19, 17, 18, 20, 21, 19], "vertices": [3, 8, 19.27, 10.18, 0.08742, 9, -0.2, 10.2, 0.73247, 11, -3.7, -3, 0.18011, 2, 8, 21.04, 5.63, 0.09804, 9, 1.94, 5.81, 0.90196, 1, 9, 6.15, 7.9, 1, 1, 9, 17.82, 3.66, 1, 1, 9, 17.54, -6.38, 1, 1, 9, 8.66, -8.72, 1, 1, 9, 2.54, -7.16, 1, 2, 8, 17.06, -5.17, 0.30155, 9, -1.12, -5.29, 0.69845, 3, 8, 14.37, -9.8, 0.88832, 9, -3.41, -10.12, 0.03238, 10, -5.5, 1.54, 0.0793, 2, 8, 11.69, -12.26, 0.52889, 10, -2.33, 3.32, 0.47111, 1, 10, 5.5, 2.67, 1, 1, 10, 14.62, 2.42, 1, 1, 10, 26.97, 1.75, 1, 1, 10, 27.22, -1.59, 1, 1, 10, 18.9, -2.9, 1, 2, 8, -1.6, -8.13, 0.41039, 10, 9.65, -3.77, 0.58961, 3, 6, 7.53, 8.5, 0.20237, 7, 3.35, -7.69, 0.79516, 8, -7.32, -7.68, 0.00247, 3, 6, 9.42, 1.85, 0.56796, 7, -3.43, -9.05, 0.42998, 10, 22.02, -5.76, 0.00206, 2, 6, 10.24, -2.54, 0.78387, 7, -7.87, -9.52, 0.21613, 1, 6, 1.84, -7.29, 1, 2, 6, -9.09, 0.83, 0.55679, 7, -3.01, 9.48, 0.44321, 2, 6, -6.67, 6.55, 0.1461, 7, 2.51, 6.62, 0.8539, 3, 6, -7.07, 11.51, 0.00597, 7, 7.49, 6.63, 0.71044, 8, -3.17, 6.63, 0.28359, 2, 8, 3.04, 7.81, 0.52263, 11, 11.52, 3.1, 0.47737, 1, 11, 17.88, 2.23, 1, 1, 11, 24.53, 2.75, 1, 1, 11, 24.66, -1.32, 1, 1, 11, 14.93, -2.24, 1, 1, 11, 5.87, -2.81, 1, 2, 9, -3.24, 11.19, 0.52238, 11, -0.56, -3.52, 0.47762, 2, 8, 10.86, 6.07, 0.8903, 11, 3.51, 2.96, 0.1097, 2, 8, 7.25, -7.23, 0.8552, 10, 0.84, -2.6, 0.1448, 2, 8, 18.37, 0.4, 0.55309, 9, -0.28, 0.38, 0.44691, 1, 9, 7.05, -0.89, 1], "hull": 30, "edges": [6, 8, 6, 4, 4, 2, 0, 58, 2, 0, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 30, 28, 24, 26, 28, 26, 30, 32, 32, 34, 36, 38, 34, 36, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 48, 50, 56, 58, 52, 54, 54, 56], "width": 34, "height": 60}}, "people_b": {"people_b": {"type": "mesh", "uvs": [0.56747, 0, 0.70188, 0.05907, 0.76212, 0.1831, 0.747, 0.31531, 0.8603, 0.38523, 0.87423, 0.54206, 0.90115, 0.70436, 0.97955, 0.8892, 0.88442, 0.90221, 0.89482, 0.94349, 0.5742, 1, 0.52444, 1, 0.30731, 0.89409, 0.40319, 0.77271, 0.3697, 0.64128, 0.31377, 0.54476, 0.23905, 0.62017, 0.14817, 0.75552, 0.01684, 0.66723, 0.16194, 0.51829, 0.22741, 0.39851, 0.29855, 0.31715, 0.42124, 0.2952, 0.35542, 0.17465, 0.38648, 0.07265, 0.51488, 0, 0.70224, 0.55046, 0.73028, 0.66453, 0.76928, 0.81981, 0.36615, 0.41389, 0.5669, 0.35893, 0.66947, 0.43975, 0.57881, 0.202], "triangles": [17, 18, 16, 18, 19, 16, 16, 19, 15, 19, 20, 15, 20, 21, 29, 29, 21, 22, 30, 32, 3, 30, 22, 32, 3, 32, 2, 25, 32, 24, 25, 0, 32, 32, 22, 23, 24, 32, 23, 32, 1, 2, 32, 0, 1, 29, 22, 30, 14, 26, 27, 27, 26, 5, 14, 15, 26, 31, 29, 30, 26, 15, 31, 26, 31, 5, 31, 4, 5, 31, 3, 4, 31, 30, 3, 31, 15, 29, 15, 20, 29, 7, 8, 6, 8, 28, 6, 13, 27, 28, 28, 27, 6, 13, 14, 27, 27, 5, 6, 10, 8, 9, 10, 28, 8, 28, 10, 13, 12, 13, 11, 10, 11, 13], "vertices": [1, 4, 18.84, -0.34, 1, 1, 4, 15.65, -5.72, 1, 1, 4, 8.95, -8.13, 1, 2, 3, 16.5, -7.59, 0.12356, 4, 1.81, -7.52, 0.87644, 2, 3, 12.53, -11.96, 0.68, 4, -1.96, -12.05, 0.32, 4, 1, 11.3, 18.59, 0.00305, 2, 13.2, -12.16, 0.13833, 3, 4.04, -12.14, 0.85247, 4, -10.43, -12.61, 0.00614, 3, 1, 12.37, 9.82, 0.13559, 2, 4.4, -12.82, 0.71386, 3, -4.76, -12.83, 0.15055, 2, 1, 15.51, -0.16, 0.47699, 2, -5.72, -15.47, 0.52301, 2, 1, 11.7, -0.86, 0.56664, 2, -6.24, -11.64, 0.43336, 2, 1, 12.12, -3.09, 0.63776, 2, -8.49, -11.95, 0.36224, 1, 1, -0.71, -6.14, 1, 2, 1, -2.7, -6.14, 0.99671, 2, -10.83, 3, 0.00329, 2, 1, -11.38, -0.42, 0.65698, 2, -4.71, 11.4, 0.34302, 3, 1, -7.55, 6.13, 0.18851, 2, 1.66, 7.26, 0.80431, 3, -7.58, 7.23, 0.00718, 2, 2, 8.81, 8.26, 0.48536, 3, -0.43, 8.25, 0.51464, 2, 3, 4.88, 10.26, 0.68694, 5, 4.73, 3.67, 0.31306, 2, 3, 0.94, 13.43, 0.19067, 5, 9.75, 3.11, 0.80933, 1, 5, 17.9, 3.6, 1, 1, 5, 16.38, -3.33, 1, 1, 5, 6.52, -2.31, 1, 2, 4, -2.68, 13.26, 0.3688, 5, -0.39, -3.26, 0.6312, 3, 3, 17.18, 10.33, 0.15548, 4, 1.71, 10.42, 0.58742, 5, -5.62, -2.98, 0.2571, 2, 3, 18.15, 5.38, 0.08941, 4, 2.9, 5.51, 0.91059, 1, 4, 9.41, 8.14, 1, 1, 4, 14.92, 6.9, 1, 1, 4, 18.84, 1.76, 1, 3, 2, 13.08, -5.26, 0.1256, 3, 3.89, -5.25, 0.87369, 4, -10.89, -5.73, 0.0007, 3, 1, 5.54, 11.97, 0.02148, 2, 6.87, -6.09, 0.74873, 3, -2.31, -6.1, 0.22979, 3, 1, 7.1, 3.59, 0.37942, 2, -1.58, -7.25, 0.61355, 3, -10.76, -7.29, 0.00704, 2, 3, 11.85, 7.86, 0.77648, 4, -3.51, 7.71, 0.22352, 2, 3, 14.46, -0.29, 0.56432, 4, -0.54, -0.32, 0.43568, 2, 3, 9.92, -4.2, 0.95384, 4, -4.91, -4.42, 0.04616, 1, 4, 7.93, -0.79, 1], "hull": 26, "edges": [0, 50, 0, 2, 20, 22, 22, 24, 24, 26, 34, 36, 48, 50, 46, 48, 42, 44, 44, 46, 40, 42, 36, 38, 38, 40, 32, 34, 30, 32, 26, 28, 28, 30, 18, 20, 14, 16, 16, 18, 8, 10, 10, 12, 12, 14, 8, 6, 2, 4, 6, 4, 52, 54, 54, 56, 56, 16], "width": 40, "height": 54}}, "tree_B": {"tree_B": {"type": "mesh", "uvs": [0.62617, 0.00255, 0.71782, 0.09105, 0.76733, 0.212, 0.80893, 0.33745, 0.86004, 0.49161, 0.8757, 0.49162, 0.9401, 0.49989, 0.99023, 0.57266, 0.99798, 0.61012, 0.97398, 0.69506, 0.94987, 0.70878, 0.91691, 0.76345, 0.95036, 0.88675, 0.8816, 0.97895, 0.78961, 0.99245, 0.6395, 0.99246, 0.434, 0.95229, 0.26584, 0.93249, 0.15591, 0.92938, 0.10279, 0.9156, 0.00347, 0.74782, 0.00353, 0.52693, 0.04096, 0.39404, 0.12562, 0.3313, 0.20553, 0.33682, 0.28056, 0.39464, 0.34928, 0.24864, 0.40528, 0.12968, 0.52946, 0.01214, 0.59186, 0.00359, 0.7524, 0.84054, 0.28517, 0.77827, 0.26321, 0.5898, 0.8978, 0.6142, 0.61912, 0.65573, 0.51311, 0.81475, 0.57066, 0.4437, 0.5805, 0.26354, 0.58959, 0.12017], "triangles": [3, 37, 2, 37, 38, 2, 38, 1, 2, 1, 38, 0, 38, 29, 0, 26, 27, 37, 37, 27, 38, 27, 28, 38, 38, 28, 29, 33, 4, 5, 32, 36, 34, 4, 34, 36, 4, 36, 3, 37, 3, 36, 33, 7, 8, 33, 6, 7, 33, 5, 6, 32, 25, 36, 25, 26, 36, 36, 26, 37, 16, 35, 15, 15, 30, 14, 15, 35, 30, 14, 30, 13, 13, 30, 12, 35, 16, 31, 16, 17, 31, 17, 18, 31, 18, 19, 31, 31, 19, 20, 32, 20, 21, 31, 20, 32, 24, 32, 23, 23, 32, 22, 21, 22, 32, 30, 11, 12, 30, 33, 11, 4, 33, 34, 35, 34, 30, 33, 30, 34, 34, 35, 32, 35, 31, 32, 11, 33, 10, 10, 33, 9, 9, 33, 8, 32, 24, 25], "vertices": [1, 35, 75.21, -3.42, 1, 2, 34, 130.04, -32.86, 0.02894, 35, 57.64, -32.52, 0.97106, 3, 33, 184.71, -50.49, 0.00431, 34, 102.28, -51.58, 0.2739, 35, 29.99, -51.41, 0.72178, 4, 32, 247.55, -72.08, 0.00051, 33, 155.64, -67.52, 0.06521, 34, 73.03, -68.31, 0.63208, 35, 0.84, -68.31, 0.30219, 4, 32, 211.23, -91.95, 0.01373, 33, 119.93, -88.46, 0.33358, 34, 37.09, -88.86, 0.61869, 35, -34.97, -89.07, 0.034, 4, 32, 211.88, -96.29, 0.01652, 33, 120.7, -92.78, 0.36791, 34, 37.82, -93.19, 0.5933, 35, -34.22, -93.39, 0.02227, 4, 32, 212.47, -114.43, 0.02202, 33, 121.83, -110.89, 0.42607, 34, 38.75, -111.31, 0.54603, 35, -33.18, -111.51, 0.00588, 4, 32, 196.4, -131.02, 0.02851, 33, 106.25, -127.94, 0.46759, 34, 22.99, -128.2, 0.50326, 35, -48.84, -128.48, 0.00064, 4, 32, 187.38, -134.55, 0.03144, 33, 97.34, -131.74, 0.48117, 34, 14.04, -131.9, 0.4873, 35, -57.77, -132.24, 8e-05, 3, 32, 165.22, -131.06, 0.0433, 33, 75.09, -128.9, 0.52275, 34, -8.18, -128.82, 0.43395, 3, 32, 160.8, -124.89, 0.04989, 33, 70.49, -122.87, 0.54142, 34, -12.71, -122.74, 0.40869, 3, 32, 145.82, -117.79, 0.08391, 33, 55.31, -116.21, 0.61781, 34, -27.82, -115.92, 0.29828, 3, 32, 116.47, -131.63, 0.13774, 33, 26.37, -130.91, 0.68903, 34, -56.91, -130.31, 0.17323, 3, 32, 90.65, -116, 0.16617, 33, 0.11, -116.05, 0.70284, 34, -83.02, -115.17, 0.13099, 3, 32, 83.49, -91.03, 0.20392, 33, -7.78, -91.3, 0.7018, 34, -90.64, -90.33, 0.09428, 3, 32, 77.3, -49.46, 0.37656, 33, -15.2, -49.93, 0.60092, 34, -97.61, -48.88, 0.02252, 3, 32, 78.84, 8.95, 0.89134, 33, -15.37, 8.5, 0.10775, 34, -97.16, 9.54, 0.00092, 3, 32, 76.84, 56.25, 0.20821, 33, -18.76, 55.72, 0.74013, 34, -100.05, 56.8, 0.05166, 3, 32, 73.09, 86.81, 0.12393, 33, -23.42, 86.16, 0.77714, 34, -104.38, 87.28, 0.09893, 3, 32, 74.33, 102.03, 0.10744, 33, -22.62, 101.41, 0.77751, 34, -103.42, 102.52, 0.11505, 3, 32, 112.06, 135.77, 0.05324, 33, 14.09, 136.24, 0.73789, 34, -66.33, 136.96, 0.20887, 3, 32, 167.11, 143.95, 0.01429, 33, 68.89, 146.04, 0.63382, 34, -11.44, 146.17, 0.35189, 3, 32, 201.78, 138.51, 0.00377, 33, 103.7, 141.63, 0.57277, 34, 23.33, 141.38, 0.42346, 4, 32, 220.91, 117.39, 0.00069, 33, 123.44, 121.08, 0.53428, 34, 42.85, 120.63, 0.46501, 35, -30.44, 120.45, 2e-05, 4, 32, 222.83, 95.06, 0, 33, 126.02, 98.81, 0.49964, 34, 45.19, 98.33, 0.49809, 35, -27.97, 98.17, 0.00227, 4, 32, 211.51, 72.13, 7e-05, 33, 115.38, 75.56, 0.36622, 34, 34.3, 75.2, 0.59913, 35, -38.73, 74.97, 0.03457, 3, 33, 154.98, 63.11, 0.04757, 34, 73.77, 62.32, 0.53654, 35, 0.82, 62.33, 0.41589, 3, 33, 187.26, 52.96, 0.00147, 34, 105.93, 51.83, 0.14541, 35, 33.04, 52.02, 0.85312, 1, 35, 68.19, 22.85, 1, 1, 35, 73.31, 6, 1, 3, 32, 119.82, -75.09, 0.14527, 33, 28.06, -74.3, 0.7228, 34, -54.62, -73.71, 0.13193, 3, 32, 116.08, 56.62, 0.0925, 33, 20.44, 57.25, 0.81485, 34, -60.83, 57.9, 0.09265, 3, 32, 162.15, 69.7, 0.01243, 33, 66.11, 71.67, 0.64817, 34, -15.01, 71.84, 0.3394, 4, 32, 182.23, -106.96, 0.03227, 33, 91.38, -104.31, 0.48075, 34, 8.38, -104.41, 0.485, 35, -63.59, -104.78, 0.00199, 3, 32, 160.39, -31.32, 0.00785, 33, 67.33, -29.35, 0.75508, 34, -14.87, -29.19, 0.23707, 1, 33, 22.65, -7.2, 1, 2, 33, 117.53, -6.57, 0.00099, 34, 35.57, -6.96, 0.99901, 1, 35, 8.22, -2.1, 1, 1, 35, 44.26, 1.59, 1], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 56, 58, 30, 32, 32, 34, 50, 52, 52, 54, 4, 6, 6, 8], "width": 280, "height": 252}}, "tree_La": {"tree_La": {"type": "mesh", "uvs": [0.62306, 0.09178, 0.71751, 0.16871, 0.81196, 0.24564, 0.86922, 0.33754, 0.92915, 0.33059, 0.99717, 0.49714, 0.94313, 0.61742, 0.6441, 0.71164, 0.54366, 0.72575, 0.56996, 0.77428, 0.58925, 0.83357, 0.59377, 0.90477, 0.5996, 0.99644, 0.54488, 0.9981, 0.54488, 0.91806, 0.53505, 0.83686, 0.51894, 0.75292, 0.48864, 0.71477, 0.21618, 0.67783, 0.04522, 0.57929, 0.11157, 0.52404, 0.07651, 0.51137, 0, 0.41774, 0, 0.39768, 0.06445, 0.31929, 0.0432, 0.28083, 0.17973, 0.19731, 0.26954, 0.16523, 0.32862, 0.08388, 0.4764, 0, 0.49161, 0, 0.428, 0.65142, 0.34508, 0.52956, 0.69132, 0.50136, 0.67282, 0.6269, 0.27438, 0.40256, 0.28069, 0.26946, 0.64915, 0.37032, 0.47394, 0.17346, 0.48905, 0.07976, 0.59931, 0.26342], "triangles": [39, 29, 30, 28, 29, 39, 39, 30, 0, 38, 28, 39, 38, 39, 0, 38, 0, 1, 27, 28, 38, 40, 38, 1, 40, 1, 2, 36, 27, 38, 26, 27, 36, 36, 38, 40, 25, 26, 36, 24, 25, 36, 37, 40, 2, 37, 2, 3, 35, 24, 36, 23, 24, 35, 22, 23, 35, 33, 37, 3, 21, 22, 35, 20, 21, 35, 35, 40, 37, 40, 35, 36, 32, 37, 33, 32, 35, 37, 20, 35, 32, 5, 3, 4, 5, 33, 3, 6, 33, 5, 33, 31, 32, 34, 33, 6, 34, 31, 33, 18, 20, 32, 18, 32, 31, 19, 20, 18, 34, 17, 31, 7, 34, 6, 34, 8, 17, 18, 31, 17, 7, 8, 34, 16, 17, 8, 16, 8, 9, 15, 16, 9, 15, 9, 10, 15, 10, 11, 14, 15, 11, 14, 11, 12, 13, 14, 12], "vertices": [2, 25, 131.5, -29.22, 0.01116, 26, 53.13, -28.41, 0.98884, 3, 24, 182.79, -61.04, 0.0031, 25, 107.46, -46.71, 0.21652, 26, 29.36, -46.27, 0.78037, 3, 24, 156.58, -75.06, 0.03828, 25, 83.42, -64.19, 0.56732, 26, 5.59, -64.12, 0.3944, 3, 24, 126.86, -81.42, 0.17542, 25, 54.86, -74.58, 0.725, 26, -22.81, -74.94, 0.09958, 3, 24, 127.25, -92.94, 0.21344, 25, 56.83, -85.94, 0.72129, 26, -20.66, -86.27, 0.06527, 3, 24, 74.43, -97.79, 0.47783, 25, 5.18, -98.01, 0.51997, 26, -72.12, -99.12, 0.0022, 2, 24, 39.26, -82.02, 0.67139, 25, -31.83, -87.23, 0.32861, 2, 24, 19.11, -21.73, 0.97864, 25, -60.07, -30.27, 0.02136, 2, 24, 17.7, -2.3, 0.99978, 25, -64.14, -11.23, 0.00022, 3, 23, 56.49, -4.72, 0.23134, 24, 2.12, -4.92, 0.76866, 25, -79.22, -15.97, 0, 1, 23, 37.97, -7.14, 1, 1, 23, 15.96, -6.52, 1, 1, 22, 5.03, -4.75, 1, 1, 22, -5.31, -5.26, 1, 1, 23, 12.48, 2.98, 1, 1, 23, 37.64, 3.15, 1, 1, 24, 10.11, 3.6, 1, 1, 24, 22.64, 7.46, 1, 2, 24, 41.78, 56.61, 0.86419, 25, -48.39, 50.43, 0.13581, 3, 24, 76.8, 83.9, 0.60636, 25, -17.45, 82.28, 0.39364, 26, -97.51, 80.8, 0, 3, 24, 91.76, 68.9, 0.42179, 25, -0.58, 69.48, 0.57342, 26, -80.44, 68.26, 0.00478, 3, 24, 96.64, 74.85, 0.31441, 25, 3.44, 76.04, 0.67452, 26, -76.52, 74.88, 0.01107, 3, 24, 127.44, 84.72, 0.11348, 25, 32.59, 90.06, 0.83327, 26, -47.59, 89.34, 0.05326, 3, 24, 133.56, 83.78, 0.10013, 25, 38.79, 89.97, 0.83825, 26, -41.39, 89.34, 0.06162, 3, 24, 155.64, 68.04, 0.0263, 25, 62.82, 77.42, 0.7912, 26, -17.17, 77.16, 0.18251, 3, 24, 168, 70.2, 0.01002, 25, 74.76, 81.25, 0.74398, 26, -5.28, 81.18, 0.24601, 2, 25, 100.18, 55.06, 0.46768, 26, 20.52, 55.38, 0.53232, 2, 25, 109.83, 37.93, 0.19508, 26, 30.44, 38.4, 0.80492, 2, 25, 134.79, 26.38, 0.00586, 26, 55.57, 27.23, 0.99414, 1, 26, 81.49, -0.7, 1, 1, 26, 81.49, -3.57, 1, 2, 24, 43.73, 15.8, 0.99741, 25, -40.85, 10.28, 0.00259, 2, 24, 83.34, 25.54, 0.53831, 25, -2.96, 25.38, 0.46169, 3, 24, 81.96, -40.46, 0.47352, 25, 4.76, -40.19, 0.52588, 26, -73.43, -41.32, 0.0006, 2, 24, 44.16, -31.09, 0.90054, 25, -33.98, -36.1, 0.09946, 3, 24, 124.16, 32.76, 0.05613, 25, 36.49, 38.14, 0.91059, 26, -42.9, 37.49, 0.03328, 3, 24, 164.62, 25.3, 0.00015, 25, 77.59, 36.32, 0.60515, 26, -1.77, 36.29, 0.3947, 3, 24, 123.2, -38.76, 0.0703, 25, 45.37, -32.84, 0.87627, 26, -32.94, -33.34, 0.05344, 1, 26, 27.89, -0.23, 1, 1, 26, 56.85, -3.09, 1, 3, 24, 157.28, -34.5, 0.00539, 25, 78.54, -23.92, 0.52699, 26, 0.1, -23.93, 0.46762], "hull": 31, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 0, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 0, 2, 2, 4], "width": 189, "height": 309}}, "tree_Lb": {"tree_Lb": {"type": "mesh", "uvs": [0.70061, 0.16794, 0.69995, 0.21059, 0.81136, 0.37308, 0.95354, 0.4207, 0.99362, 0.61025, 0.97732, 0.79847, 0.92491, 0.89835, 0.80802, 0.94196, 0.50097, 0.99156, 0.29562, 0.99782, 0.0719, 0.91147, 0.00459, 0.76515, 0.03788, 0.60914, 0.07118, 0.45314, 0.07474, 0.29332, 0.29877, 0.28225, 0.31358, 0.17089, 0.53894, 0, 0.54264, 0, 0.60987, 0.0309, 0.29853, 0.81317, 0.65579, 0.81242, 0.30878, 0.63264, 0.69265, 0.63639, 0.47791, 0.43235], "triangles": [0, 16, 19, 15, 16, 1, 24, 15, 1, 17, 18, 19, 19, 16, 17, 0, 1, 16, 24, 13, 15, 13, 14, 15, 2, 24, 1, 24, 22, 13, 12, 13, 22, 2, 23, 24, 23, 2, 3, 23, 3, 4, 22, 24, 23, 5, 23, 4, 21, 22, 23, 21, 23, 5, 22, 11, 12, 20, 22, 21, 20, 11, 22, 6, 21, 5, 10, 11, 20, 7, 21, 6, 8, 20, 21, 8, 21, 7, 9, 10, 20, 9, 20, 8], "vertices": [1, 30, 53.15, -23.84, 1, 2, 29, 120.23, -23.74, 0.00168, 30, 45.04, -23.74, 0.99832, 2, 29, 89.36, -40.45, 0.36251, 30, 14.17, -40.45, 0.63749, 3, 28, 154.76, -64.27, 0.00247, 29, 80.31, -61.78, 0.63043, 30, 5.12, -61.78, 0.36709, 3, 28, 118.57, -69.15, 0.05894, 29, 44.3, -67.79, 0.82205, 30, -30.89, -67.79, 0.11901, 3, 28, 82.91, -65.58, 0.25042, 29, 8.54, -65.34, 0.7388, 30, -66.65, -65.34, 0.01078, 3, 28, 64.19, -57.13, 0.38559, 29, -10.44, -57.48, 0.61393, 30, -85.63, -57.48, 0.00048, 2, 28, 56.46, -39.34, 0.54361, 29, -18.73, -39.95, 0.45639, 2, 28, 48.49, 6.99, 0.99369, 29, -28.15, 6.11, 0.00631, 2, 28, 48.27, 37.82, 0.75554, 29, -29.34, 36.91, 0.24446, 3, 28, 65.72, 70.84, 0.46265, 29, -12.93, 70.47, 0.52726, 30, -88.12, 70.47, 0.01009, 3, 28, 93.83, 80.06, 0.28859, 29, 14.87, 80.56, 0.65767, 30, -60.32, 80.56, 0.05374, 3, 28, 123.3, 74.13, 0.12071, 29, 44.51, 75.57, 0.68492, 30, -30.68, 75.57, 0.19436, 3, 28, 152.77, 68.21, 0.02193, 29, 74.15, 70.58, 0.47852, 30, -1.04, 70.58, 0.49955, 3, 28, 183.1, 66.72, 0.00025, 29, 104.51, 70.04, 0.29095, 30, 29.32, 70.04, 0.70879, 2, 29, 106.62, 36.44, 0.09866, 30, 31.43, 36.44, 0.90134, 2, 29, 127.78, 34.22, 0.00242, 30, 52.59, 34.22, 0.99758, 1, 30, 85.05, 0.41, 1, 1, 30, 85.05, -0.14, 1, 1, 30, 79.18, -10.23, 1, 3, 28, 83.32, 36.28, 0.38751, 29, 5.74, 36.47, 0.60325, 30, -69.45, 36.47, 0.00923, 2, 28, 81.78, -17.29, 0.30517, 29, 5.89, -17.12, 0.69483, 3, 28, 117.56, 33.66, 0.06435, 29, 40.04, 34.94, 0.83512, 30, -35.15, 34.94, 0.10053, 3, 28, 115.03, -23.87, 0.02249, 29, 39.33, -22.64, 0.95072, 30, -35.86, -22.64, 0.0268, 2, 29, 78.1, 9.57, 0.23613, 30, 2.91, 9.57, 0.76387], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 22, 24, 24, 26], "width": 150, "height": 190}}, "tree_Ra": {"tree_Ra": {"type": "mesh", "uvs": [0.58986, 0, 0.63235, 0, 0.73809, 0.07866, 0.75991, 0.16291, 0.87324, 0.19732, 0.83602, 0.28714, 0.96635, 0.29922, 0.99991, 0.41446, 0.99512, 0.55387, 0.85932, 0.65952, 0.73814, 0.68544, 0.57829, 0.68888, 0.49795, 0.70014, 0.42442, 0.78208, 0.41242, 0.88908, 0.372, 0.99789, 0.29261, 0.99803, 0.25718, 0.99176, 0.32301, 0.89596, 0.3756, 0.7797, 0.39443, 0.71177, 0.36554, 0.66377, 0.28843, 0.66858, 0.13172, 0.61394, 0.04516, 0.54782, 0.02352, 0.44218, 0.00187, 0.33653, 0.10974, 0.2703, 0.21051, 0.26233, 0.23232, 0.16473, 0.39421, 0.06227, 0.53564, 0.03385, 0.32128, 0.55306, 0.62405, 0.58963, 0.72957, 0.43229, 0.29465, 0.41844, 0.53996, 0.27721, 0.537, 0.15588], "triangles": [2, 37, 31, 30, 31, 37, 31, 0, 1, 1, 2, 31, 37, 2, 3, 29, 30, 37, 36, 37, 3, 29, 37, 36, 28, 29, 36, 5, 3, 4, 36, 3, 5, 35, 28, 36, 34, 36, 5, 35, 36, 34, 35, 25, 26, 35, 24, 25, 32, 35, 34, 6, 7, 5, 7, 34, 5, 8, 34, 7, 35, 26, 27, 35, 27, 28, 32, 24, 35, 8, 33, 34, 33, 32, 34, 32, 23, 24, 9, 33, 8, 21, 32, 33, 22, 23, 32, 21, 22, 32, 10, 33, 9, 33, 12, 21, 11, 33, 10, 11, 12, 33, 20, 21, 12, 13, 20, 12, 19, 20, 13, 14, 19, 13, 18, 19, 14, 15, 18, 14, 16, 17, 18, 15, 16, 18], "vertices": [1, 16, 94.75, 5.06, 1, 1, 16, 95.84, -2.98, 1, 1, 16, 72.05, -26.58, 1, 2, 15, 133.75, -33.4, 0.03899, 16, 44.22, -34.55, 0.96101, 2, 15, 125.65, -56.64, 0.15026, 16, 35.53, -57.57, 0.84974, 3, 14, 180.45, -55.09, 0.00789, 15, 94.37, -54.49, 0.55044, 16, 4.31, -54.62, 0.44166, 3, 14, 180.2, -80.32, 0.02561, 15, 94.28, -79.72, 0.76121, 16, 3.58, -79.84, 0.21318, 3, 14, 142.46, -92.66, 0.10743, 15, 56.62, -92.29, 0.81981, 16, -34.39, -91.44, 0.07277, 3, 14, 95.48, -99.01, 0.34105, 15, 9.68, -98.95, 0.6559, 16, -81.48, -96.89, 0.00305, 2, 14, 56.02, -78.88, 0.61622, 15, -29.91, -79.07, 0.38378, 2, 14, 43.76, -57.35, 0.7663, 15, -42.3, -57.63, 0.2337, 2, 14, 37.93, -27.36, 0.95679, 15, -48.32, -27.67, 0.04321, 2, 14, 31.8, -12.78, 0.99823, 15, -54.55, -13.13, 0.00177, 2, 13, 60.22, -2.98, 0.17611, 14, 2.12, -3.17, 0.82389, 1, 13, 24.31, -9.27, 1, 2, 12, 9.09, -5.3, 0.91413, 13, -13.46, -10.42, 0.08587, 1, 12, -6.08, -5.35, 1, 1, 12, -12.85, -3.21, 1, 1, 13, 18.04, 6.79, 1, 2, 13, 58.83, 6.27, 0.33568, 14, 1.49, 6.17, 0.66432, 1, 14, 24.87, 6.15, 1, 2, 14, 40.15, 14.1, 0.98967, 15, -46.38, 13.8, 0.01033, 2, 14, 36.28, 28.41, 0.9451, 15, -50.34, 28.08, 0.0549, 2, 14, 50.05, 60.83, 0.71871, 15, -36.77, 60.59, 0.28129, 2, 14, 69.74, 80.61, 0.51783, 15, -17.22, 80.5, 0.48217, 3, 14, 104.6, 90.19, 0.25145, 15, 17.58, 90.31, 0.73449, 16, -68.73, 92.1, 0.01405, 3, 14, 139.46, 99.78, 0.10157, 15, 52.38, 100.12, 0.83203, 16, -33.69, 101.01, 0.0664, 3, 14, 164.87, 82.87, 0.0506, 15, 77.9, 83.37, 0.79386, 16, -8.61, 83.62, 0.15554, 3, 14, 170.49, 64.26, 0.02024, 15, 83.64, 64.8, 0.64194, 16, -3.35, 64.91, 0.33781, 3, 14, 203.93, 65.22, 7e-05, 15, 117.07, 65.98, 0.20349, 16, 30.1, 65.23, 0.79644, 2, 15, 156.39, 41.01, 0.00423, 16, 68.76, 39.26, 0.99577, 1, 16, 81.96, 13.78, 1, 2, 14, 76.05, 28.22, 0.69321, 15, -10.57, 28.15, 0.30679, 2, 14, 72.62, -30.83, 0.74197, 15, -13.62, -30.92, 0.25803, 3, 14, 128.57, -42.56, 0.07682, 15, 42.41, -42.29, 0.89619, 16, -47.32, -41.09, 0.02699, 3, 14, 120.5, 40.25, 0.10695, 15, 33.81, 40.47, 0.87601, 16, -53.79, 41.86, 0.01703, 2, 15, 88.68, 1.87, 0.49086, 16, 0.07, 1.87, 0.50914, 1, 16, 40.88, 7.96, 1], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 46, 48, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 36, 38, 38, 40, 42, 44, 44, 46, 48, 50, 50, 52, 6, 8], "width": 191, "height": 340}}, "tree_Rb": {"tree_Rb": {"type": "mesh", "uvs": [0.68457, 0.00345, 0.78979, 0.11813, 0.77477, 0.18583, 0.9263, 0.19486, 0.91294, 0.30306, 0.98329, 0.38508, 0.99921, 0.55523, 0.71916, 0.71807, 0.5198, 0.73238, 0.43577, 0.77335, 0.42814, 0.83863, 0.40488, 0.91197, 0.39556, 0.99765, 0.36926, 1, 0.31061, 0.98881, 0.28544, 0.97489, 0.31558, 0.90366, 0.35856, 0.83004, 0.37868, 0.75452, 0.29089, 0.69605, 0.13631, 0.67423, 0.02698, 0.62907, 0.01554, 0.51143, 0.00175, 0.36979, 0.1585, 0.30078, 0.28158, 0.1256, 0.47583, 0.02843, 0.6042, 0.00245, 0.26081, 0.58554, 0.61419, 0.60815, 0.66719, 0.44336, 0.29521, 0.40826, 0.39751, 0.29183, 0.70718, 0.30967, 0.55188, 0.1663, 0.60675, 0.07409], "triangles": [35, 27, 0, 26, 27, 35, 35, 0, 1, 34, 26, 35, 25, 26, 34, 2, 35, 1, 34, 35, 2, 32, 25, 34, 33, 34, 2, 24, 25, 32, 4, 2, 3, 33, 2, 4, 32, 34, 33, 31, 24, 32, 30, 32, 33, 31, 32, 30, 31, 22, 23, 31, 23, 24, 30, 29, 31, 33, 4, 5, 30, 33, 5, 6, 30, 5, 28, 22, 31, 29, 28, 31, 21, 22, 28, 20, 21, 28, 19, 28, 29, 20, 28, 19, 30, 6, 29, 7, 29, 6, 8, 19, 29, 8, 29, 7, 18, 19, 8, 9, 18, 8, 17, 18, 9, 10, 17, 9, 11, 17, 10, 16, 17, 11, 14, 16, 11, 14, 15, 16, 11, 13, 14, 12, 13, 11], "vertices": [1, 21, 74.16, -11.15, 1, 2, 20, 117.79, -37.76, 0.03532, 21, 44.74, -37.51, 0.96468, 3, 19, 176.43, -40.75, 0.00049, 20, 97.51, -38.84, 0.27364, 21, 24.47, -38.7, 0.72587, 3, 19, 178.79, -69.57, 0.00444, 20, 100.44, -67.6, 0.52475, 21, 27.56, -67.45, 0.47081, 3, 19, 146.7, -72.65, 0.06041, 20, 68.41, -71.31, 0.69428, 21, -4.45, -71.34, 0.24531, 3, 19, 125.03, -90.04, 0.18412, 20, 47.09, -89.13, 0.74168, 21, -25.67, -89.27, 0.0742, 3, 19, 75.79, -101.79, 0.4442, 20, -1.91, -101.85, 0.55362, 21, -74.6, -102.27, 0.00218, 2, 19, 18.93, -57.78, 0.88763, 20, -59.63, -58.97, 0.11237, 3, 18, 63.61, -21.49, 0.01961, 19, 8.16, -21.22, 0.97359, 20, -71.11, -22.62, 0.0068, 2, 18, 49.34, -7.37, 0.89346, 19, -6.59, -7.61, 0.10654, 1, 18, 29.94, -8.63, 1, 1, 18, 7.75, -7.29, 1, 1, 17, 9.23, -7.96, 1, 1, 17, 4.24, -8.66, 1, 2, 17, -6.91, -5.33, 0.97814, 18, -17.34, 7.27, 0.02186, 2, 17, -11.69, -1.2, 0.91714, 18, -13.91, 12.58, 0.08286, 2, 17, -5.96, 19.96, 0.02707, 18, 7.83, 9.86, 0.97293, 1, 18, 30.62, 4.81, 1, 2, 18, 53.37, 4.15, 0.71136, 19, -2.97, 4.05, 0.28864, 2, 19, 11.24, 23.49, 0.99406, 20, -68.92, 22.13, 0.00594, 2, 19, 12.53, 53.54, 0.93245, 20, -68.22, 52.2, 0.06755, 2, 19, 22.13, 76.32, 0.86785, 20, -59.07, 75.17, 0.13215, 2, 19, 56.16, 84.53, 0.66776, 20, -25.21, 84.05, 0.33224, 3, 19, 97.14, 94.41, 0.38791, 20, 15.56, 94.73, 0.61162, 21, -58.23, 94.41, 0.00047, 3, 19, 122.49, 68.64, 0.16613, 20, 41.42, 69.47, 0.77914, 21, -32.23, 69.29, 0.05473, 3, 19, 177.79, 54.64, 0.00039, 20, 96.98, 56.56, 0.36425, 21, 23.41, 56.69, 0.63536, 2, 20, 132.42, 25.91, 0.01363, 21, 59.01, 26.25, 0.98637, 1, 21, 71.42, 3.87, 1, 2, 19, 42.57, 34.81, 0.91781, 20, -37.82, 34.07, 0.08219, 2, 19, 47.61, -32.47, 0.88433, 20, -31.45, -33.1, 0.11567, 3, 19, 97.56, -33.9, 0.20969, 20, 18.52, -33.54, 0.78552, 21, -54.55, -33.85, 0.00479, 3, 19, 95.56, 37.51, 0.25276, 20, 15.11, 37.82, 0.74543, 21, -58.36, 37.49, 0.00181, 3, 19, 132.99, 24.38, 0.01069, 20, 52.79, 25.42, 0.91195, 21, -20.61, 25.31, 0.07736, 3, 19, 137.98, -34.49, 0.01984, 20, 58.94, -33.33, 0.79726, 21, -14.13, -33.41, 0.18291, 2, 20, 95.03, 3.84, 0.00864, 21, 21.75, 3.96, 0.99136, 1, 21, 50.66, -0.82, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 36, 38, 38, 40, 40, 42, 48, 50, 50, 52, 52, 54, 46, 48, 34, 36, 30, 32, 32, 34, 18, 20, 20, 22, 6, 8, 8, 10, 42, 44, 44, 46], "width": 190, "height": 297}}}}], "animations": {"idle": {"slots": {"light": {"rgba": [{"color": "ffffff32", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0.2, 0.889, 0.59]}, {"time": 1.3333, "color": "ffffff96", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.59, 2.222, 0.2]}, {"time": 2.6667, "color": "ffffff32", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0.2, 3.556, 0.59]}, {"time": 4, "color": "ffffff96", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.59, 4.889, 0.2]}, {"time": 5.3333, "color": "ffffff32", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0.2, 6.222, 0.59]}, {"time": 6.6667, "color": "ffffff96", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.59, 7.556, 0.2]}, {"time": 8, "color": "ffffff32", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0.2, 8.889, 0.59]}, {"time": 9.3333, "color": "ffffff96", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.59, 10.222, 0.2]}, {"time": 10.6667, "color": "ffffff32"}]}}, "bones": {"people_b2": {"translate": [{"y": 0.77, "curve": [0.279, 0, 0.556, 0, 0.279, 0.07, 0.556, -0.84]}, {"time": 0.8333, "y": -0.84, "curve": [1.278, 0, 1.722, 0, 1.278, -0.84, 1.722, 1.52]}, {"time": 2.1667, "y": 1.52, "curve": [2.611, 0, 3.056, 0, 2.611, 1.52, 3.056, -0.84]}, {"time": 3.5, "y": -0.84, "curve": [3.944, 0, 4.389, 0, 3.944, -0.84, 4.389, 1.52]}, {"time": 4.8333, "y": 1.52, "curve": [5.278, 0, 5.722, 0, 5.278, 1.52, 5.722, -0.84]}, {"time": 6.1667, "y": -0.84, "curve": [6.611, 0, 7.056, 0, 6.611, -0.84, 7.056, 1.52]}, {"time": 7.5, "y": 1.52, "curve": [7.944, 0, 8.389, 0, 7.944, 1.52, 8.389, -0.84]}, {"time": 8.8333, "y": -0.84, "curve": [9.278, 0, 9.722, 0, 9.278, -0.84, 9.722, 1.52]}, {"time": 10.1667, "y": 1.52, "curve": [10.334, 0, 10.501, 0, 10.334, 1.52, 10.501, 1.18]}, {"time": 10.6667, "y": 0.77}]}, "people_b3": {"translate": [{"x": 1.38, "curve": [0.39, 0.65, 0.779, -2.22, 0.39, 0, 0.779, 0]}, {"time": 1.1667, "x": -2.22, "curve": [1.611, -2.22, 2.056, 1.56, 1.611, 0, 2.056, 0]}, {"time": 2.5, "x": 1.56, "curve": [2.944, 1.56, 3.389, -2.22, 2.944, 0, 3.389, 0]}, {"time": 3.8333, "x": -2.22, "curve": [4.278, -2.22, 4.722, 1.56, 4.278, 0, 4.722, 0]}, {"time": 5.1667, "x": 1.56, "curve": [5.611, 1.56, 6.056, -2.22, 5.611, 0, 6.056, 0]}, {"time": 6.5, "x": -2.22, "curve": [6.944, -2.22, 7.389, 1.56, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 1.56, "curve": [8.278, 1.56, 8.722, -2.22, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -2.22, "curve": [9.611, -2.22, 10.056, 1.56, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 1.56, "curve": [10.556, 1.56, 10.613, 1.49, 10.556, 0, 10.613, 0]}, {"time": 10.6667, "x": 1.38}]}, "people_b4": {"rotate": [{"value": -2.56, "curve": [0.057, -2.7, 0.112, -2.81]}, {"time": 0.1667, "value": -2.81, "curve": [0.611, -2.81, 1.056, 2.53]}, {"time": 1.5, "value": 2.53, "curve": [1.944, 2.53, 2.389, -2.81]}, {"time": 2.8333, "value": -2.81, "curve": [3.278, -2.81, 3.722, 2.53]}, {"time": 4.1667, "value": 2.53, "curve": [4.611, 2.53, 5.056, -2.81]}, {"time": 5.5, "value": -2.81, "curve": [5.944, -2.81, 6.389, 2.53]}, {"time": 6.8333, "value": 2.53, "curve": [7.278, 2.53, 7.722, -2.81]}, {"time": 8.1667, "value": -2.81, "curve": [8.611, -2.81, 9.056, 2.53]}, {"time": 9.5, "value": 2.53, "curve": [9.89, 2.53, 10.279, -1.54]}, {"time": 10.6667, "value": -2.56}]}, "people_b5": {"rotate": [{"value": -9.8, "curve": [0.057, -10.41, 0.112, -10.88]}, {"time": 0.1667, "value": -10.88, "curve": [0.611, -10.88, 1.056, 12.12]}, {"time": 1.5, "value": 12.12, "curve": [1.944, 12.12, 2.389, -10.88]}, {"time": 2.8333, "value": -10.88, "curve": [3.278, -10.88, 3.722, 12.12]}, {"time": 4.1667, "value": 12.12, "curve": [4.611, 12.12, 5.056, -10.88]}, {"time": 5.5, "value": -10.88, "curve": [5.944, -10.88, 6.389, 12.12]}, {"time": 6.8333, "value": 12.12, "curve": [7.278, 12.12, 7.722, -10.88]}, {"time": 8.1667, "value": -10.88, "curve": [8.611, -10.88, 9.056, 12.12]}, {"time": 9.5, "value": 12.12, "curve": [9.89, 12.12, 10.279, -5.44]}, {"time": 10.6667, "value": -9.8}]}, "people_a2": {"translate": [{"y": -1.18, "curve": [0.444, 0, 0.889, 0, 0.444, -1.18, 0.889, 0.72]}, {"time": 1.3333, "y": 0.72, "curve": [1.778, 0, 2.222, 0, 1.778, 0.72, 2.222, -1.18]}, {"time": 2.6667, "y": -1.18, "curve": [3.111, 0, 3.556, 0, 3.111, -1.18, 3.556, 0.72]}, {"time": 4, "y": 0.72, "curve": [4.444, 0, 4.889, 0, 4.444, 0.72, 4.889, -1.18]}, {"time": 5.3333, "y": -1.18, "curve": [5.778, 0, 6.222, 0, 5.778, -1.18, 6.222, 0.72]}, {"time": 6.6667, "y": 0.72, "curve": [7.111, 0, 7.556, 0, 7.111, 0.72, 7.556, -1.18]}, {"time": 8, "y": -1.18, "curve": [8.444, 0, 8.889, 0, 8.444, -1.18, 8.889, 0.72]}, {"time": 9.3333, "y": 0.72, "curve": [9.778, 0, 10.222, 0, 9.778, 0.72, 10.222, -1.18]}, {"time": 10.6667, "y": -1.18}]}, "people_a3": {"translate": [{"x": -2.17, "curve": [0.114, -2.7, 0.224, -3.09, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -3.09, "curve": [0.778, -3.09, 1.222, 2.63, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 2.63, "curve": [2.111, 2.63, 2.556, -3.09, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -3.09, "curve": [3.444, -3.09, 3.889, 2.63, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 2.63, "curve": [4.778, 2.63, 5.222, -3.09, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -3.09, "curve": [6.111, -3.09, 6.556, 2.63, 6.111, 0, 6.556, 0]}, {"time": 7, "x": 2.63, "curve": [7.444, 2.63, 7.889, -3.09, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -3.09, "curve": [8.778, -3.09, 9.222, 2.63, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 2.63, "curve": [10.001, 2.63, 10.336, -0.57, 10.001, 0, 10.336, 0]}, {"time": 10.6667, "x": -2.17}]}, "people_a4": {"rotate": [{"value": 0.4, "curve": [0.225, 1.67, 0.446, 2.97]}, {"time": 0.6667, "value": 2.97, "curve": [1.111, 2.97, 1.556, -2.18]}, {"time": 2, "value": -2.18, "curve": [2.444, -2.18, 2.889, 2.97]}, {"time": 3.3333, "value": 2.97, "curve": [3.778, 2.97, 4.222, -2.18]}, {"time": 4.6667, "value": -2.18, "curve": [5.111, -2.18, 5.556, 2.97]}, {"time": 6, "value": 2.97, "curve": [6.444, 2.97, 6.889, -2.18]}, {"time": 7.3333, "value": -2.18, "curve": [7.778, -2.18, 8.222, 2.97]}, {"time": 8.6667, "value": 2.97, "curve": [9.111, 2.97, 9.556, -2.18]}, {"time": 10, "value": -2.18, "curve": [10.224, -2.18, 10.447, -0.9]}, {"time": 10.6667, "value": 0.4}]}, "people_a5": {"rotate": [{"value": 0.21, "curve": [0.225, -3.12, 0.446, -6.5]}, {"time": 0.6667, "value": -6.5, "curve": [1.111, -6.5, 1.556, 6.91]}, {"time": 2, "value": 6.91, "curve": [2.444, 6.91, 2.889, -6.5]}, {"time": 3.3333, "value": -6.5, "curve": [3.778, -6.5, 4.222, 6.91]}, {"time": 4.6667, "value": 6.91, "curve": [5.111, 6.91, 5.556, -6.5]}, {"time": 6, "value": -6.5, "curve": [6.444, -6.5, 6.889, 6.91]}, {"time": 7.3333, "value": 6.91, "curve": [7.778, 6.91, 8.222, -6.5]}, {"time": 8.6667, "value": -6.5, "curve": [9.111, -6.5, 9.556, 6.91]}, {"time": 10, "value": 6.91, "curve": [10.224, 6.91, 10.447, 3.58]}, {"time": 10.6667, "value": 0.21}]}, "people_a6": {"rotate": [{"value": -1.99, "curve": [0.225, 1.76, 0.446, 5.57]}, {"time": 0.6667, "value": 5.57, "curve": [1.111, 5.57, 1.556, -9.56]}, {"time": 2, "value": -9.56, "curve": [2.444, -9.56, 2.889, 5.57]}, {"time": 3.3333, "value": 5.57, "curve": [3.778, 5.57, 4.222, -9.56]}, {"time": 4.6667, "value": -9.56, "curve": [5.111, -9.56, 5.556, 5.57]}, {"time": 6, "value": 5.57, "curve": [6.444, 5.57, 6.889, -9.56]}, {"time": 7.3333, "value": -9.56, "curve": [7.778, -9.56, 8.222, 5.57]}, {"time": 8.6667, "value": 5.57, "curve": [9.111, 5.57, 9.556, -9.56]}, {"time": 10, "value": -9.56, "curve": [10.224, -9.56, 10.447, -5.8]}, {"time": 10.6667, "value": -1.99}]}, "tree_Ra2": {"rotate": [{"value": 2.28, "curve": [0.444, 2.28, 0.889, -1.67]}, {"time": 1.3333, "value": -1.67, "curve": [1.778, -1.67, 2.222, 2.28]}, {"time": 2.6667, "value": 2.28, "curve": [3.111, 2.28, 3.556, -1.67]}, {"time": 4, "value": -1.67, "curve": [4.444, -1.67, 4.889, 2.28]}, {"time": 5.3333, "value": 2.28, "curve": [5.778, 2.28, 6.222, -1.67]}, {"time": 6.6667, "value": -1.67, "curve": [7.111, -1.67, 7.556, 2.28]}, {"time": 8, "value": 2.28, "curve": [8.444, 2.28, 8.889, -1.67]}, {"time": 9.3333, "value": -1.67, "curve": [9.778, -1.67, 10.222, 2.28]}, {"time": 10.6667, "value": 2.28}]}, "tree_Ra3": {"rotate": [{"value": 1.64, "curve": [0.114, 2.01, 0.224, 2.28]}, {"time": 0.3333, "value": 2.28, "curve": [0.778, 2.28, 1.222, -1.67]}, {"time": 1.6667, "value": -1.67, "curve": [2.111, -1.67, 2.556, 2.28]}, {"time": 3, "value": 2.28, "curve": [3.444, 2.28, 3.889, -1.67]}, {"time": 4.3333, "value": -1.67, "curve": [4.778, -1.67, 5.222, 2.28]}, {"time": 5.6667, "value": 2.28, "curve": [6.111, 2.28, 6.556, -1.67]}, {"time": 7, "value": -1.67, "curve": [7.444, -1.67, 7.889, 2.28]}, {"time": 8.3333, "value": 2.28, "curve": [8.778, 2.28, 9.222, -1.67]}, {"time": 9.6667, "value": -1.67, "curve": [10.001, -1.67, 10.336, 0.54]}, {"time": 10.6667, "value": 1.64}]}, "tree_Ra4": {"rotate": [{"value": 0.3, "curve": [0.225, 1.28, 0.446, 2.28]}, {"time": 0.6667, "value": 2.28, "curve": [1.111, 2.28, 1.556, -1.67]}, {"time": 2, "value": -1.67, "curve": [2.444, -1.67, 2.889, 2.28]}, {"time": 3.3333, "value": 2.28, "curve": [3.778, 2.28, 4.222, -1.67]}, {"time": 4.6667, "value": -1.67, "curve": [5.111, -1.67, 5.556, 2.28]}, {"time": 6, "value": 2.28, "curve": [6.444, 2.28, 6.889, -1.67]}, {"time": 7.3333, "value": -1.67, "curve": [7.778, -1.67, 8.222, 2.28]}, {"time": 8.6667, "value": 2.28, "curve": [9.111, 2.28, 9.556, -1.67]}, {"time": 10, "value": -1.67, "curve": [10.224, -1.67, 10.447, -0.69]}, {"time": 10.6667, "value": 0.3}]}, "tree_Ra5": {"rotate": [{"value": -1.04, "curve": [0.336, 0.08, 0.668, 2.28]}, {"time": 1, "value": 2.28, "curve": [1.444, 2.28, 1.889, -1.67]}, {"time": 2.3333, "value": -1.67, "curve": [2.778, -1.67, 3.222, 2.28]}, {"time": 3.6667, "value": 2.28, "curve": [4.111, 2.28, 4.556, -1.67]}, {"time": 5, "value": -1.67, "curve": [5.444, -1.67, 5.889, 2.28]}, {"time": 6.3333, "value": 2.28, "curve": [6.778, 2.28, 7.222, -1.67]}, {"time": 7.6667, "value": -1.67, "curve": [8.111, -1.67, 8.556, 2.28]}, {"time": 9, "value": 2.28, "curve": [9.444, 2.28, 9.889, -1.67]}, {"time": 10.3333, "value": -1.67, "curve": [10.446, -1.67, 10.559, -1.42]}, {"time": 10.6667, "value": -1.04}]}, "tree_Rb2": {"rotate": [{"value": -1.04, "curve": [0.336, 0.08, 0.668, 2.28]}, {"time": 1, "value": 2.28, "curve": [1.444, 2.28, 1.889, -1.67]}, {"time": 2.3333, "value": -1.67, "curve": [2.778, -1.67, 3.222, 2.28]}, {"time": 3.6667, "value": 2.28, "curve": [4.111, 2.28, 4.556, -1.67]}, {"time": 5, "value": -1.67, "curve": [5.444, -1.67, 5.889, 2.28]}, {"time": 6.3333, "value": 2.28, "curve": [6.778, 2.28, 7.222, -1.67]}, {"time": 7.6667, "value": -1.67, "curve": [8.111, -1.67, 8.556, 2.28]}, {"time": 9, "value": 2.28, "curve": [9.444, 2.28, 9.889, -1.67]}, {"time": 10.3333, "value": -1.67, "curve": [10.446, -1.67, 10.559, -1.42]}, {"time": 10.6667, "value": -1.04}]}, "tree_Rb3": {"rotate": [{"value": -1.67, "curve": [0.444, -1.67, 0.889, 2.28]}, {"time": 1.3333, "value": 2.28, "curve": [1.778, 2.28, 2.222, -1.67]}, {"time": 2.6667, "value": -1.67, "curve": [3.111, -1.67, 3.556, 2.28]}, {"time": 4, "value": 2.28, "curve": [4.444, 2.28, 4.889, -1.67]}, {"time": 5.3333, "value": -1.67, "curve": [5.778, -1.67, 6.222, 2.28]}, {"time": 6.6667, "value": 2.28, "curve": [7.111, 2.28, 7.556, -1.67]}, {"time": 8, "value": -1.67, "curve": [8.444, -1.67, 8.889, 2.28]}, {"time": 9.3333, "value": 2.28, "curve": [9.778, 2.28, 10.222, -1.67]}, {"time": 10.6667, "value": -1.67}]}, "tree_Rb4": {"rotate": [{"value": -1.04, "curve": [0.114, -1.4, 0.224, -1.67]}, {"time": 0.3333, "value": -1.67, "curve": [0.778, -1.67, 1.222, 2.28]}, {"time": 1.6667, "value": 2.28, "curve": [2.111, 2.28, 2.556, -1.67]}, {"time": 3, "value": -1.67, "curve": [3.444, -1.67, 3.889, 2.28]}, {"time": 4.3333, "value": 2.28, "curve": [4.778, 2.28, 5.222, -1.67]}, {"time": 5.6667, "value": -1.67, "curve": [6.111, -1.67, 6.556, 2.28]}, {"time": 7, "value": 2.28, "curve": [7.444, 2.28, 7.889, -1.67]}, {"time": 8.3333, "value": -1.67, "curve": [8.778, -1.67, 9.222, 2.28]}, {"time": 9.6667, "value": 2.28, "curve": [10.001, 2.28, 10.336, 0.07]}, {"time": 10.6667, "value": -1.04}]}, "tree_Rb5": {"rotate": [{"value": 0.3, "curve": [0.225, -0.68, 0.446, -1.67]}, {"time": 0.6667, "value": -1.67, "curve": [1.111, -1.67, 1.556, 2.28]}, {"time": 2, "value": 2.28, "curve": [2.444, 2.28, 2.889, -1.67]}, {"time": 3.3333, "value": -1.67, "curve": [3.778, -1.67, 4.222, 2.28]}, {"time": 4.6667, "value": 2.28, "curve": [5.111, 2.28, 5.556, -1.67]}, {"time": 6, "value": -1.67, "curve": [6.444, -1.67, 6.889, 2.28]}, {"time": 7.3333, "value": 2.28, "curve": [7.778, 2.28, 8.222, -1.67]}, {"time": 8.6667, "value": -1.67, "curve": [9.111, -1.67, 9.556, 2.28]}, {"time": 10, "value": 2.28, "curve": [10.224, 2.28, 10.447, 1.3]}, {"time": 10.6667, "value": 0.3}]}, "tree_La2": {"rotate": [{"value": -1.49, "curve": [0.39, -0.72, 0.779, 2.28]}, {"time": 1.1667, "value": 2.28, "curve": [1.611, 2.28, 2.056, -1.67]}, {"time": 2.5, "value": -1.67, "curve": [2.944, -1.67, 3.389, 2.28]}, {"time": 3.8333, "value": 2.28, "curve": [4.278, 2.28, 4.722, -1.67]}, {"time": 5.1667, "value": -1.67, "curve": [5.611, -1.67, 6.056, 2.28]}, {"time": 6.5, "value": 2.28, "curve": [6.944, 2.28, 7.389, -1.67]}, {"time": 7.8333, "value": -1.67, "curve": [8.278, -1.67, 8.722, 2.28]}, {"time": 9.1667, "value": 2.28, "curve": [9.611, 2.28, 10.056, -1.67]}, {"time": 10.5, "value": -1.67, "curve": [10.556, -1.67, 10.613, -1.6]}, {"time": 10.6667, "value": -1.49}]}, "tree_La3": {"rotate": [{"value": -1.49, "curve": [0.057, -1.59, 0.112, -1.67]}, {"time": 0.1667, "value": -1.67, "curve": [0.611, -1.67, 1.056, 2.28]}, {"time": 1.5, "value": 2.28, "curve": [1.944, 2.28, 2.389, -1.67]}, {"time": 2.8333, "value": -1.67, "curve": [3.278, -1.67, 3.722, 2.28]}, {"time": 4.1667, "value": 2.28, "curve": [4.611, 2.28, 5.056, -1.67]}, {"time": 5.5, "value": -1.67, "curve": [5.944, -1.67, 6.389, 2.28]}, {"time": 6.8333, "value": 2.28, "curve": [7.278, 2.28, 7.722, -1.67]}, {"time": 8.1667, "value": -1.67, "curve": [8.611, -1.67, 9.056, 2.28]}, {"time": 9.5, "value": 2.28, "curve": [9.89, 2.28, 10.279, -0.74]}, {"time": 10.6667, "value": -1.49}]}, "tree_La4": {"rotate": [{"value": -0.42, "curve": [0.168, -1.11, 0.334, -1.67]}, {"time": 0.5, "value": -1.67, "curve": [0.944, -1.67, 1.389, 2.28]}, {"time": 1.8333, "value": 2.28, "curve": [2.278, 2.28, 2.722, -1.67]}, {"time": 3.1667, "value": -1.67, "curve": [3.611, -1.67, 4.056, 2.28]}, {"time": 4.5, "value": 2.28, "curve": [4.944, 2.28, 5.389, -1.67]}, {"time": 5.8333, "value": -1.67, "curve": [6.278, -1.67, 6.722, 2.28]}, {"time": 7.1667, "value": 2.28, "curve": [7.611, 2.28, 8.056, -1.67]}, {"time": 8.5, "value": -1.67, "curve": [8.944, -1.67, 9.389, 2.28]}, {"time": 9.8333, "value": 2.28, "curve": [10.112, 2.28, 10.39, 0.74]}, {"time": 10.6667, "value": -0.42}]}, "tree_La5": {"rotate": [{"value": 1.02, "curve": [0.279, -0.13, 0.556, -1.67]}, {"time": 0.8333, "value": -1.67, "curve": [1.278, -1.67, 1.722, 2.28]}, {"time": 2.1667, "value": 2.28, "curve": [2.611, 2.28, 3.056, -1.67]}, {"time": 3.5, "value": -1.67, "curve": [3.944, -1.67, 4.389, 2.28]}, {"time": 4.8333, "value": 2.28, "curve": [5.278, 2.28, 5.722, -1.67]}, {"time": 6.1667, "value": -1.67, "curve": [6.611, -1.67, 7.056, 2.28]}, {"time": 7.5, "value": 2.28, "curve": [7.944, 2.28, 8.389, -1.67]}, {"time": 8.8333, "value": -1.67, "curve": [9.278, -1.67, 9.722, 2.28]}, {"time": 10.1667, "value": 2.28, "curve": [10.334, 2.28, 10.501, 1.72]}, {"time": 10.6667, "value": 1.02}]}, "tree_Lb2": {"rotate": [{"value": 0.3, "curve": [0.225, 1.28, 0.446, 2.28]}, {"time": 0.6667, "value": 2.28, "curve": [1.111, 2.28, 1.556, -1.67]}, {"time": 2, "value": -1.67, "curve": [2.444, -1.67, 2.889, 2.28]}, {"time": 3.3333, "value": 2.28, "curve": [3.778, 2.28, 4.222, -1.67]}, {"time": 4.6667, "value": -1.67, "curve": [5.111, -1.67, 5.556, 2.28]}, {"time": 6, "value": 2.28, "curve": [6.444, 2.28, 6.889, -1.67]}, {"time": 7.3333, "value": -1.67, "curve": [7.778, -1.67, 8.222, 2.28]}, {"time": 8.6667, "value": 2.28, "curve": [9.111, 2.28, 9.556, -1.67]}, {"time": 10, "value": -1.67, "curve": [10.224, -1.67, 10.447, -0.69]}, {"time": 10.6667, "value": 0.3}]}, "tree_Lb3": {"rotate": [{"value": -1.04, "curve": [0.336, 0.08, 0.668, 2.28]}, {"time": 1, "value": 2.28, "curve": [1.444, 2.28, 1.889, -1.67]}, {"time": 2.3333, "value": -1.67, "curve": [2.778, -1.67, 3.222, 2.28]}, {"time": 3.6667, "value": 2.28, "curve": [4.111, 2.28, 4.556, -1.67]}, {"time": 5, "value": -1.67, "curve": [5.444, -1.67, 5.889, 2.28]}, {"time": 6.3333, "value": 2.28, "curve": [6.778, 2.28, 7.222, -1.67]}, {"time": 7.6667, "value": -1.67, "curve": [8.111, -1.67, 8.556, 2.28]}, {"time": 9, "value": 2.28, "curve": [9.444, 2.28, 9.889, -1.67]}, {"time": 10.3333, "value": -1.67, "curve": [10.446, -1.67, 10.559, -1.42]}, {"time": 10.6667, "value": -1.04}]}, "tree_Lb4": {"rotate": [{"value": -1.67, "curve": [0.444, -1.67, 0.889, 2.28]}, {"time": 1.3333, "value": 2.28, "curve": [1.778, 2.28, 2.222, -1.67]}, {"time": 2.6667, "value": -1.67, "curve": [3.111, -1.67, 3.556, 2.28]}, {"time": 4, "value": 2.28, "curve": [4.444, 2.28, 4.889, -1.67]}, {"time": 5.3333, "value": -1.67, "curve": [5.778, -1.67, 6.222, 2.28]}, {"time": 6.6667, "value": 2.28, "curve": [7.111, 2.28, 7.556, -1.67]}, {"time": 8, "value": -1.67, "curve": [8.444, -1.67, 8.889, 2.28]}, {"time": 9.3333, "value": 2.28, "curve": [9.778, 2.28, 10.222, -1.67]}, {"time": 10.6667, "value": -1.67}]}, "tree_B2": {"rotate": [{"value": 0.3, "curve": [0.225, -0.68, 0.446, -1.67]}, {"time": 0.6667, "value": -1.67, "curve": [1.111, -1.67, 1.556, 2.28]}, {"time": 2, "value": 2.28, "curve": [2.444, 2.28, 2.889, -1.67]}, {"time": 3.3333, "value": -1.67, "curve": [3.778, -1.67, 4.222, 2.28]}, {"time": 4.6667, "value": 2.28, "curve": [5.111, 2.28, 5.556, -1.67]}, {"time": 6, "value": -1.67, "curve": [6.444, -1.67, 6.889, 2.28]}, {"time": 7.3333, "value": 2.28, "curve": [7.778, 2.28, 8.222, -1.67]}, {"time": 8.6667, "value": -1.67, "curve": [9.111, -1.67, 9.556, 2.28]}, {"time": 10, "value": 2.28, "curve": [10.224, 2.28, 10.447, 1.3]}, {"time": 10.6667, "value": 0.3}]}, "tree_B3": {"rotate": [{"value": 1.64, "curve": [0.336, 0.53, 0.668, -1.67]}, {"time": 1, "value": -1.67, "curve": [1.444, -1.67, 1.889, 2.28]}, {"time": 2.3333, "value": 2.28, "curve": [2.778, 2.28, 3.222, -1.67]}, {"time": 3.6667, "value": -1.67, "curve": [4.111, -1.67, 4.556, 2.28]}, {"time": 5, "value": 2.28, "curve": [5.444, 2.28, 5.889, -1.67]}, {"time": 6.3333, "value": -1.67, "curve": [6.778, -1.67, 7.222, 2.28]}, {"time": 7.6667, "value": 2.28, "curve": [8.111, 2.28, 8.556, -1.67]}, {"time": 9, "value": -1.67, "curve": [9.444, -1.67, 9.889, 2.28]}, {"time": 10.3333, "value": 2.28, "curve": [10.446, 2.28, 10.559, 2.02]}, {"time": 10.6667, "value": 1.64}]}, "tree_B4": {"rotate": [{"value": 2.28, "curve": [0.444, 2.28, 0.889, -1.67]}, {"time": 1.3333, "value": -1.67, "curve": [1.778, -1.67, 2.222, 2.28]}, {"time": 2.6667, "value": 2.28, "curve": [3.111, 2.28, 3.556, -1.67]}, {"time": 4, "value": -1.67, "curve": [4.444, -1.67, 4.889, 2.28]}, {"time": 5.3333, "value": 2.28, "curve": [5.778, 2.28, 6.222, -1.67]}, {"time": 6.6667, "value": -1.67, "curve": [7.111, -1.67, 7.556, 2.28]}, {"time": 8, "value": 2.28, "curve": [8.444, 2.28, 8.889, -1.67]}, {"time": 9.3333, "value": -1.67, "curve": [9.778, -1.67, 10.222, 2.28]}, {"time": 10.6667, "value": 2.28}]}, "tree_B5": {"rotate": [{"value": 1.64, "curve": [0.114, 2.01, 0.224, 2.28]}, {"time": 0.3333, "value": 2.28, "curve": [0.778, 2.28, 1.222, -1.67]}, {"time": 1.6667, "value": -1.67, "curve": [2.111, -1.67, 2.556, 2.28]}, {"time": 3, "value": 2.28, "curve": [3.444, 2.28, 3.889, -1.67]}, {"time": 4.3333, "value": -1.67, "curve": [4.778, -1.67, 5.222, 2.28]}, {"time": 5.6667, "value": 2.28, "curve": [6.111, 2.28, 6.556, -1.67]}, {"time": 7, "value": -1.67, "curve": [7.444, -1.67, 7.889, 2.28]}, {"time": 8.3333, "value": 2.28, "curve": [8.778, 2.28, 9.222, -1.67]}, {"time": 9.6667, "value": -1.67, "curve": [10.001, -1.67, 10.336, 0.54]}, {"time": 10.6667, "value": 1.64}]}, "A": {"scale": [{"time": 4, "curve": [4.089, 1, 4.178, 1.258, 4.089, 1, 4.178, 1.258]}, {"time": 4.2667, "x": 1.258, "y": 1.258, "curve": [4.344, 1.258, 4.422, 1.163, 4.344, 1.258, 4.422, 1.163]}, {"time": 4.5, "x": 1.163, "y": 1.163, "curve": [4.578, 1.163, 4.656, 1.231, 4.578, 1.163, 4.656, 1.231]}, {"time": 4.7333, "x": 1.231, "y": 1.231, "curve": [4.811, 1.231, 4.889, 1.211, 4.811, 1.231, 4.889, 1.211]}, {"time": 4.9667, "x": 1.211, "y": 1.211, "curve": "stepped"}, {"time": 6.6, "x": 1.211, "y": 1.211, "curve": [6.689, 1.211, 6.778, 0.914, 6.689, 1.211, 6.778, 0.914]}, {"time": 6.8667, "x": 0.914, "y": 0.914, "curve": [6.944, 0.914, 7.022, 1.041, 6.944, 0.914, 7.022, 1.041]}, {"time": 7.1, "x": 1.041, "y": 1.041, "curve": [7.178, 1.041, 7.256, 0.978, 7.178, 1.041, 7.256, 0.978]}, {"time": 7.3333, "x": 0.978, "y": 0.978, "curve": [7.411, 0.978, 7.489, 1, 7.411, 0.978, 7.489, 1]}, {"time": 7.5667}]}, "water": {"translate": [{"x": -11.77, "y": -5.67, "curve": [0.444, -11.77, 0.889, 8.06, 0.444, -5.67, 0.889, 6.86]}, {"time": 1.3333, "x": 8.06, "y": 6.86, "curve": [1.778, 8.06, 2.222, -11.77, 1.778, 6.86, 2.222, -5.67]}, {"time": 2.6667, "x": -11.77, "y": -5.67, "curve": [3.111, -11.77, 3.556, 8.06, 3.111, -5.67, 3.556, 6.86]}, {"time": 4, "x": 8.06, "y": 6.86, "curve": [4.444, 8.06, 4.889, -11.77, 4.444, 6.86, 4.889, -5.67]}, {"time": 5.3333, "x": -11.77, "y": -5.67, "curve": [5.778, -11.77, 6.222, 8.06, 5.778, -5.67, 6.222, 6.86]}, {"time": 6.6667, "x": 8.06, "y": 6.86, "curve": [7.111, 8.06, 7.556, -11.77, 7.111, 6.86, 7.556, -5.67]}, {"time": 8, "x": -11.77, "y": -5.67, "curve": [8.444, -11.77, 8.889, 8.06, 8.444, -5.67, 8.889, 6.86]}, {"time": 9.3333, "x": 8.06, "y": 6.86, "curve": [9.778, 8.06, 10.222, -11.77, 9.778, 6.86, 10.222, -5.67]}, {"time": 10.6667, "x": -11.77, "y": -5.67}]}, "water2": {"translate": [{"x": -8.6, "y": -3.66, "curve": [0.114, -10.41, 0.224, -11.77, 0.114, -4.81, 0.224, -5.67]}, {"time": 0.3333, "x": -11.77, "y": -5.67, "curve": [0.778, -11.77, 1.222, 8.06, 0.778, -5.67, 1.222, 6.86]}, {"time": 1.6667, "x": 8.06, "y": 6.86, "curve": [2.111, 8.06, 2.556, -11.77, 2.111, 6.86, 2.556, -5.67]}, {"time": 3, "x": -11.77, "y": -5.67, "curve": [3.444, -11.77, 3.889, 8.06, 3.444, -5.67, 3.889, 6.86]}, {"time": 4.3333, "x": 8.06, "y": 6.86, "curve": [4.778, 8.06, 5.222, -11.77, 4.778, 6.86, 5.222, -5.67]}, {"time": 5.6667, "x": -11.77, "y": -5.67, "curve": [6.111, -11.77, 6.556, 8.06, 6.111, -5.67, 6.556, 6.86]}, {"time": 7, "x": 8.06, "y": 6.86, "curve": [7.444, 8.06, 7.889, -11.77, 7.444, 6.86, 7.889, -5.67]}, {"time": 8.3333, "x": -11.77, "y": -5.67, "curve": [8.778, -11.77, 9.222, 8.06, 8.778, -5.67, 9.222, 6.86]}, {"time": 9.6667, "x": 8.06, "y": 6.86, "curve": [10.001, 8.06, 10.336, -3.04, 10.001, 6.86, 10.336, -0.15]}, {"time": 10.6667, "x": -8.6, "y": -3.66}]}, "water3": {"translate": [{"x": -1.85, "y": 0.6, "curve": [0.225, -6.78, 0.446, -11.77, 0.225, -2.51, 0.446, -5.67]}, {"time": 0.6667, "x": -11.77, "y": -5.67, "curve": [1.111, -11.77, 1.556, 8.06, 1.111, -5.67, 1.556, 6.86]}, {"time": 2, "x": 8.06, "y": 6.86, "curve": [2.444, 8.06, 2.889, -11.77, 2.444, 6.86, 2.889, -5.67]}, {"time": 3.3333, "x": -11.77, "y": -5.67, "curve": [3.778, -11.77, 4.222, 8.06, 3.778, -5.67, 4.222, 6.86]}, {"time": 4.6667, "x": 8.06, "y": 6.86, "curve": [5.111, 8.06, 5.556, -11.77, 5.111, 6.86, 5.556, -5.67]}, {"time": 6, "x": -11.77, "y": -5.67, "curve": [6.444, -11.77, 6.889, 8.06, 6.444, -5.67, 6.889, 6.86]}, {"time": 7.3333, "x": 8.06, "y": 6.86, "curve": [7.778, 8.06, 8.222, -11.77, 7.778, 6.86, 8.222, -5.67]}, {"time": 8.6667, "x": -11.77, "y": -5.67, "curve": [9.111, -11.77, 9.556, 8.06, 9.111, -5.67, 9.556, 6.86]}, {"time": 10, "x": 8.06, "y": 6.86, "curve": [10.224, 8.06, 10.447, 3.13, 10.224, 6.86, 10.447, 3.75]}, {"time": 10.6667, "x": -1.85, "y": 0.6}]}, "water4": {"translate": [{"x": 4.89, "y": 4.86, "curve": [0.336, -0.71, 0.668, -11.77, 0.336, 1.32, 0.668, -5.67]}, {"time": 1, "x": -11.77, "y": -5.67, "curve": [1.444, -11.77, 1.889, 8.06, 1.444, -5.67, 1.889, 6.86]}, {"time": 2.3333, "x": 8.06, "y": 6.86, "curve": [2.778, 8.06, 3.222, -11.77, 2.778, 6.86, 3.222, -5.67]}, {"time": 3.6667, "x": -11.77, "y": -5.67, "curve": [4.111, -11.77, 4.556, 8.06, 4.111, -5.67, 4.556, 6.86]}, {"time": 5, "x": 8.06, "y": 6.86, "curve": [5.444, 8.06, 5.889, -11.77, 5.444, 6.86, 5.889, -5.67]}, {"time": 6.3333, "x": -11.77, "y": -5.67, "curve": [6.778, -11.77, 7.222, 8.06, 6.778, -5.67, 7.222, 6.86]}, {"time": 7.6667, "x": 8.06, "y": 6.86, "curve": [8.111, 8.06, 8.556, -11.77, 8.111, 6.86, 8.556, -5.67]}, {"time": 9, "x": -11.77, "y": -5.67, "curve": [9.444, -11.77, 9.889, 8.06, 9.444, -5.67, 9.889, 6.86]}, {"time": 10.3333, "x": 8.06, "y": 6.86, "curve": [10.446, 8.06, 10.559, 6.79, 10.446, 6.86, 10.559, 6.06]}, {"time": 10.6667, "x": 4.89, "y": 4.86}]}, "water5": {"translate": [{"x": -1.85, "y": 0.6, "curve": [0.225, -6.78, 0.446, -11.77, 0.225, -2.51, 0.446, -5.67]}, {"time": 0.6667, "x": -11.77, "y": -5.67, "curve": [1.111, -11.77, 1.556, 8.06, 1.111, -5.67, 1.556, 6.86]}, {"time": 2, "x": 8.06, "y": 6.86, "curve": [2.444, 8.06, 2.889, -11.77, 2.444, 6.86, 2.889, -5.67]}, {"time": 3.3333, "x": -11.77, "y": -5.67, "curve": [3.778, -11.77, 4.222, 8.06, 3.778, -5.67, 4.222, 6.86]}, {"time": 4.6667, "x": 8.06, "y": 6.86, "curve": [5.111, 8.06, 5.556, -11.77, 5.111, 6.86, 5.556, -5.67]}, {"time": 6, "x": -11.77, "y": -5.67, "curve": [6.444, -11.77, 6.889, 8.06, 6.444, -5.67, 6.889, 6.86]}, {"time": 7.3333, "x": 8.06, "y": 6.86, "curve": [7.778, 8.06, 8.222, -11.77, 7.778, 6.86, 8.222, -5.67]}, {"time": 8.6667, "x": -11.77, "y": -5.67, "curve": [9.111, -11.77, 9.556, 8.06, 9.111, -5.67, 9.556, 6.86]}, {"time": 10, "x": 8.06, "y": 6.86, "curve": [10.224, 8.06, 10.447, 3.13, 10.224, 6.86, 10.447, 3.75]}, {"time": 10.6667, "x": -1.85, "y": 0.6}]}, "water6": {"translate": [{"x": 8.06, "y": 6.86, "curve": [0.444, 8.06, 0.889, -11.77, 0.444, 6.86, 0.889, -5.67]}, {"time": 1.3333, "x": -11.77, "y": -5.67, "curve": [1.778, -11.77, 2.222, 8.06, 1.778, -5.67, 2.222, 6.86]}, {"time": 2.6667, "x": 8.06, "y": 6.86, "curve": [3.111, 8.06, 3.556, -11.77, 3.111, 6.86, 3.556, -5.67]}, {"time": 4, "x": -11.77, "y": -5.67, "curve": [4.444, -11.77, 4.889, 8.06, 4.444, -5.67, 4.889, 6.86]}, {"time": 5.3333, "x": 8.06, "y": 6.86, "curve": [5.778, 8.06, 6.222, -11.77, 5.778, 6.86, 6.222, -5.67]}, {"time": 6.6667, "x": -11.77, "y": -5.67, "curve": [7.111, -11.77, 7.556, 8.06, 7.111, -5.67, 7.556, 6.86]}, {"time": 8, "x": 8.06, "y": 6.86, "curve": [8.444, 8.06, 8.889, -11.77, 8.444, 6.86, 8.889, -5.67]}, {"time": 9.3333, "x": -11.77, "y": -5.67, "curve": [9.778, -11.77, 10.222, 8.06, 9.778, -5.67, 10.222, 6.86]}, {"time": 10.6667, "x": 8.06, "y": 6.86}]}, "water7": {"translate": [{"x": 4.89, "y": 4.86, "curve": [0.336, -0.71, 0.668, -11.77, 0.336, 1.32, 0.668, -5.67]}, {"time": 1, "x": -11.77, "y": -5.67, "curve": [1.444, -11.77, 1.889, 8.06, 1.444, -5.67, 1.889, 6.86]}, {"time": 2.3333, "x": 8.06, "y": 6.86, "curve": [2.778, 8.06, 3.222, -11.77, 2.778, 6.86, 3.222, -5.67]}, {"time": 3.6667, "x": -11.77, "y": -5.67, "curve": [4.111, -11.77, 4.556, 8.06, 4.111, -5.67, 4.556, 6.86]}, {"time": 5, "x": 8.06, "y": 6.86, "curve": [5.444, 8.06, 5.889, -11.77, 5.444, 6.86, 5.889, -5.67]}, {"time": 6.3333, "x": -11.77, "y": -5.67, "curve": [6.778, -11.77, 7.222, 8.06, 6.778, -5.67, 7.222, 6.86]}, {"time": 7.6667, "x": 8.06, "y": 6.86, "curve": [8.111, 8.06, 8.556, -11.77, 8.111, 6.86, 8.556, -5.67]}, {"time": 9, "x": -11.77, "y": -5.67, "curve": [9.444, -11.77, 9.889, 8.06, 9.444, -5.67, 9.889, 6.86]}, {"time": 10.3333, "x": 8.06, "y": 6.86, "curve": [10.446, 8.06, 10.559, 6.79, 10.446, 6.86, 10.559, 6.06]}, {"time": 10.6667, "x": 4.89, "y": 4.86}]}, "water8": {"translate": [{"x": 4.89, "y": 4.86, "curve": [0.114, 6.7, 0.224, 8.06, 0.114, 6.01, 0.224, 6.86]}, {"time": 0.3333, "x": 8.06, "y": 6.86, "curve": [0.778, 8.06, 1.222, -11.77, 0.778, 6.86, 1.222, -5.67]}, {"time": 1.6667, "x": -11.77, "y": -5.67, "curve": [2.111, -11.77, 2.556, 8.06, 2.111, -5.67, 2.556, 6.86]}, {"time": 3, "x": 8.06, "y": 6.86, "curve": [3.444, 8.06, 3.889, -11.77, 3.444, 6.86, 3.889, -5.67]}, {"time": 4.3333, "x": -11.77, "y": -5.67, "curve": [4.778, -11.77, 5.222, 8.06, 4.778, -5.67, 5.222, 6.86]}, {"time": 5.6667, "x": 8.06, "y": 6.86, "curve": [6.111, 8.06, 6.556, -11.77, 6.111, 6.86, 6.556, -5.67]}, {"time": 7, "x": -11.77, "y": -5.67, "curve": [7.444, -11.77, 7.889, 8.06, 7.444, -5.67, 7.889, 6.86]}, {"time": 8.3333, "x": 8.06, "y": 6.86, "curve": [8.778, 8.06, 9.222, -11.77, 8.778, 6.86, 9.222, -5.67]}, {"time": 9.6667, "x": -11.77, "y": -5.67, "curve": [10.001, -11.77, 10.336, -0.66, 10.001, -5.67, 10.336, 1.35]}, {"time": 10.6667, "x": 4.89, "y": 4.86}]}, "water9": {"translate": [{"x": 8.06, "y": 6.86, "curve": [0.444, 8.06, 0.889, -11.77, 0.444, 6.86, 0.889, -5.67]}, {"time": 1.3333, "x": -11.77, "y": -5.67, "curve": [1.778, -11.77, 2.222, 8.06, 1.778, -5.67, 2.222, 6.86]}, {"time": 2.6667, "x": 8.06, "y": 6.86, "curve": [3.111, 8.06, 3.556, -11.77, 3.111, 6.86, 3.556, -5.67]}, {"time": 4, "x": -11.77, "y": -5.67, "curve": [4.444, -11.77, 4.889, 8.06, 4.444, -5.67, 4.889, 6.86]}, {"time": 5.3333, "x": 8.06, "y": 6.86, "curve": [5.778, 8.06, 6.222, -11.77, 5.778, 6.86, 6.222, -5.67]}, {"time": 6.6667, "x": -11.77, "y": -5.67, "curve": [7.111, -11.77, 7.556, 8.06, 7.111, -5.67, 7.556, 6.86]}, {"time": 8, "x": 8.06, "y": 6.86, "curve": [8.444, 8.06, 8.889, -11.77, 8.444, 6.86, 8.889, -5.67]}, {"time": 9.3333, "x": -11.77, "y": -5.67, "curve": [9.778, -11.77, 10.222, 8.06, 9.778, -5.67, 10.222, 6.86]}, {"time": 10.6667, "x": 8.06, "y": 6.86}]}, "water10": {"translate": [{"x": -1.85, "y": 0.6, "curve": [0.225, 3.07, 0.446, 8.06, 0.225, 3.71, 0.446, 6.86]}, {"time": 0.6667, "x": 8.06, "y": 6.86, "curve": [1.111, 8.06, 1.556, -11.77, 1.111, 6.86, 1.556, -5.67]}, {"time": 2, "x": -11.77, "y": -5.67, "curve": [2.444, -11.77, 2.889, 8.06, 2.444, -5.67, 2.889, 6.86]}, {"time": 3.3333, "x": 8.06, "y": 6.86, "curve": [3.778, 8.06, 4.222, -11.77, 3.778, 6.86, 4.222, -5.67]}, {"time": 4.6667, "x": -11.77, "y": -5.67, "curve": [5.111, -11.77, 5.556, 8.06, 5.111, -5.67, 5.556, 6.86]}, {"time": 6, "x": 8.06, "y": 6.86, "curve": [6.444, 8.06, 6.889, -11.77, 6.444, 6.86, 6.889, -5.67]}, {"time": 7.3333, "x": -11.77, "y": -5.67, "curve": [7.778, -11.77, 8.222, 8.06, 7.778, -5.67, 8.222, 6.86]}, {"time": 8.6667, "x": 8.06, "y": 6.86, "curve": [9.111, 8.06, 9.556, -11.77, 9.111, 6.86, 9.556, -5.67]}, {"time": 10, "x": -11.77, "y": -5.67, "curve": [10.224, -11.77, 10.447, -6.84, 10.224, -5.67, 10.447, -2.55]}, {"time": 10.6667, "x": -1.85, "y": 0.6}]}, "water11": {"translate": [{"x": 4.89, "y": 4.86, "curve": [0.114, 6.7, 0.224, 8.06, 0.114, 6.01, 0.224, 6.86]}, {"time": 0.3333, "x": 8.06, "y": 6.86, "curve": [0.778, 8.06, 1.222, -11.77, 0.778, 6.86, 1.222, -5.67]}, {"time": 1.6667, "x": -11.77, "y": -5.67, "curve": [2.111, -11.77, 2.556, 8.06, 2.111, -5.67, 2.556, 6.86]}, {"time": 3, "x": 8.06, "y": 6.86, "curve": [3.444, 8.06, 3.889, -11.77, 3.444, 6.86, 3.889, -5.67]}, {"time": 4.3333, "x": -11.77, "y": -5.67, "curve": [4.778, -11.77, 5.222, 8.06, 4.778, -5.67, 5.222, 6.86]}, {"time": 5.6667, "x": 8.06, "y": 6.86, "curve": [6.111, 8.06, 6.556, -11.77, 6.111, 6.86, 6.556, -5.67]}, {"time": 7, "x": -11.77, "y": -5.67, "curve": [7.444, -11.77, 7.889, 8.06, 7.444, -5.67, 7.889, 6.86]}, {"time": 8.3333, "x": 8.06, "y": 6.86, "curve": [8.778, 8.06, 9.222, -11.77, 8.778, 6.86, 9.222, -5.67]}, {"time": 9.6667, "x": -11.77, "y": -5.67, "curve": [10.001, -11.77, 10.336, -0.66, 10.001, -5.67, 10.336, 1.35]}, {"time": 10.6667, "x": 4.89, "y": 4.86}]}, "water12": {"translate": [{"x": -8.6, "y": -3.66, "curve": [0.336, -3, 0.668, 8.06, 0.336, -0.12, 0.668, 6.86]}, {"time": 1, "x": 8.06, "y": 6.86, "curve": [1.444, 8.06, 1.889, -11.77, 1.444, 6.86, 1.889, -5.67]}, {"time": 2.3333, "x": -11.77, "y": -5.67, "curve": [2.778, -11.77, 3.222, 8.06, 2.778, -5.67, 3.222, 6.86]}, {"time": 3.6667, "x": 8.06, "y": 6.86, "curve": [4.111, 8.06, 4.556, -11.77, 4.111, 6.86, 4.556, -5.67]}, {"time": 5, "x": -11.77, "y": -5.67, "curve": [5.444, -11.77, 5.889, 8.06, 5.444, -5.67, 5.889, 6.86]}, {"time": 6.3333, "x": 8.06, "y": 6.86, "curve": [6.778, 8.06, 7.222, -11.77, 6.778, 6.86, 7.222, -5.67]}, {"time": 7.6667, "x": -11.77, "y": -5.67, "curve": [8.111, -11.77, 8.556, 8.06, 8.111, -5.67, 8.556, 6.86]}, {"time": 9, "x": 8.06, "y": 6.86, "curve": [9.444, 8.06, 9.889, -11.77, 9.444, 6.86, 9.889, -5.67]}, {"time": 10.3333, "x": -11.77, "y": -5.67, "curve": [10.446, -11.77, 10.559, -10.5, 10.446, -5.67, 10.559, -4.86]}, {"time": 10.6667, "x": -8.6, "y": -3.66}]}, "water13": {"translate": [{"x": -1.85, "y": 0.6, "curve": [0.225, 3.07, 0.446, 8.06, 0.225, 3.71, 0.446, 6.86]}, {"time": 0.6667, "x": 8.06, "y": 6.86, "curve": [1.111, 8.06, 1.556, -11.77, 1.111, 6.86, 1.556, -5.67]}, {"time": 2, "x": -11.77, "y": -5.67, "curve": [2.444, -11.77, 2.889, 8.06, 2.444, -5.67, 2.889, 6.86]}, {"time": 3.3333, "x": 8.06, "y": 6.86, "curve": [3.778, 8.06, 4.222, -11.77, 3.778, 6.86, 4.222, -5.67]}, {"time": 4.6667, "x": -11.77, "y": -5.67, "curve": [5.111, -11.77, 5.556, 8.06, 5.111, -5.67, 5.556, 6.86]}, {"time": 6, "x": 8.06, "y": 6.86, "curve": [6.444, 8.06, 6.889, -11.77, 6.444, 6.86, 6.889, -5.67]}, {"time": 7.3333, "x": -11.77, "y": -5.67, "curve": [7.778, -11.77, 8.222, 8.06, 7.778, -5.67, 8.222, 6.86]}, {"time": 8.6667, "x": 8.06, "y": 6.86, "curve": [9.111, 8.06, 9.556, -11.77, 9.111, 6.86, 9.556, -5.67]}, {"time": 10, "x": -11.77, "y": -5.67, "curve": [10.224, -11.77, 10.447, -6.84, 10.224, -5.67, 10.447, -2.55]}, {"time": 10.6667, "x": -1.85, "y": 0.6}]}, "water14": {"translate": [{"x": -8.6, "y": -3.66, "curve": [0.336, -3, 0.668, 8.06, 0.336, -0.12, 0.668, 6.86]}, {"time": 1, "x": 8.06, "y": 6.86, "curve": [1.444, 8.06, 1.889, -11.77, 1.444, 6.86, 1.889, -5.67]}, {"time": 2.3333, "x": -11.77, "y": -5.67, "curve": [2.778, -11.77, 3.222, 8.06, 2.778, -5.67, 3.222, 6.86]}, {"time": 3.6667, "x": 8.06, "y": 6.86, "curve": [4.111, 8.06, 4.556, -11.77, 4.111, 6.86, 4.556, -5.67]}, {"time": 5, "x": -11.77, "y": -5.67, "curve": [5.444, -11.77, 5.889, 8.06, 5.444, -5.67, 5.889, 6.86]}, {"time": 6.3333, "x": 8.06, "y": 6.86, "curve": [6.778, 8.06, 7.222, -11.77, 6.778, 6.86, 7.222, -5.67]}, {"time": 7.6667, "x": -11.77, "y": -5.67, "curve": [8.111, -11.77, 8.556, 8.06, 8.111, -5.67, 8.556, 6.86]}, {"time": 9, "x": 8.06, "y": 6.86, "curve": [9.444, 8.06, 9.889, -11.77, 9.444, 6.86, 9.889, -5.67]}, {"time": 10.3333, "x": -11.77, "y": -5.67, "curve": [10.446, -11.77, 10.559, -10.5, 10.446, -5.67, 10.559, -4.86]}, {"time": 10.6667, "x": -8.6, "y": -3.66}]}}}}}