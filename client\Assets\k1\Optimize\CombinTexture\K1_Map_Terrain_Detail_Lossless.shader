Shader "TFW/Terrain Detail Optimize Lossless"
{
    Properties
    {
        _L1Tex("L1Tex", 2D) = "white" {}
        _L1FarColor("L1FarColor", color) = (0,0,0,0)

        [Space(20)]

        _L2Tex("L2Tex", 2D) = "white" {}
        _L2FarColor("L2FarColor", color) = (0,0,0,0)

        [Space(20)]

        _L3Tex("L3Tex", 2D) = "white" {}
        _L3FarColor("L3FarColor", color) = (0,0,0,0)

        [Space(20)]
        _L4FarColor("L4FarColor", color) = (0,0,0,0)

        [Space(20)]
        [NoScaleOffset]
        _MaskTex("MaskTex ((R)MixL1/L2(G)MixL1+L2/L3(B)MixL1+L2+L3/L4 )", 2D) = "black" {}

        [Space(20)]
        _DetailMask("Detail", 2D) = "black" {}
        _DetailFarColor("DetailColor", color) = (0,0,0,0)

        //[Toggle(DETAILMASKL1_ON)] DetailMaskL1("Detail Mask L1", Float) = 0
        //[Toggle(DETAILMASKL2_ON)] DetailMaskL2("Detail Mask L2", Float) = 0
        //[Toggle(DETAILMASKL3_ON)] DetailMaskL3("Detail Mask L3", Float) = 0
        //[Toggle(DETAILMASKL4_ON)] DetailMaskL4("Detail Mask L4", Float) = 0


        [Space(20)]

        _Sharpening("Sharpening", Range(1, 10)) = 1

        //[Space(20)]

        [Enum(Off, 0, On, 1)] _ZWrite("ZWrite", Float) = 0

        [Space(20)]

        _SwitchDistanceNear("SwitchDistanceNear", float) = 20
        _SwitchDistanceFar("SwitchDistanceFar", float) = 30
        _SwitchDetailNear("SwitchDetailNear", float) = 20
        _SwitchDetailFar("SwitchDetailFar", float) = 20

        //[TOGGLE] DAYNIGHTTOGGLE("DAY NIGHT TOGGLE", FLOAT) = 0
        //[TOGGLE] SHADOWTOGGLE("SHADOW TOGGLE", FLOAT) = 0
        //[Header(Stencil)]
        //[Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", Float) = 8
        //[IntRange]_StencilWriteMask("Stencil Write Mask", Range(0,255)) = 255
        //[IntRange]_StencilReadMask("Stencil Read Mask", Range(0,255)) = 255
        //[IntRange]_Stencil("Stencil ID", Range(0,255)) = 0
        //[Enum(UnityEngine.Rendering.StencilOp)]_StencilPass("Stencil Pass", Float) = 0
        //[Enum(UnityEngine.Rendering.StencilOp)]_StencilFail("Stencil Fail", Float) = 0
        //[Enum(UnityEngine.Rendering.StencilOp)]_StencilZFail("Stencil ZFail", Float) = 0
    }

    CGINCLUDE
    #include "UnityCG.cginc"
    //#include "Lighting.cginc"
    //#include "AutoLight.cginc"
    //#include "Assets/k1/Res/Shader/CG/DayNightSystemShaderHelper.cginc"
    #define PI 3.1415926

    struct appdata
    {
        float4 vertex : POSITION;
        float2 uv : TEXCOORD0;
        float2 uv2 : TEXCOORD1;
        //UNITY_VERTEX_INPUT_INSTANCE_ID
    };

    struct v2f
    {
        float4 pos : SV_POSITION;
        //SHADOW_COORDS(1)
        float2 uv[5] : TEXCOORD2;
    };


    float4 _L1Tex_ST;
    float4 _L2Tex_ST;
    float4 _L3Tex_ST;
    float4 _MaskTex_ST;
    float4 _DetailMask_ST;

    fixed4 _L1FarColor;
    fixed4 _L2FarColor;
    fixed4 _L3FarColor;
    fixed4 _L4FarColor;
    fixed4 _DetailFarColor;

    float _SwitchDistanceFar;
    float _SwitchDistanceNear;
    float _SwitchDetailNear;
    float _SwitchDetailFar;

    fixed4 _ShadowColor;
    fixed _Sharpening;

    fixed _SwitchDistanceModifier;

    sampler2D_half _L1Tex;
    sampler2D_half _L2Tex;
    sampler2D_half _L3Tex;
    sampler2D_half _MaskTex;
    sampler2D_half _DetailMask;

    v2f vert(appdata v)
    {
        //UNITY_SETUP_INSTANCE_ID(v);
        v2f o;

        o.pos = UnityObjectToClipPos(v.vertex);
        o.uv[0] = TRANSFORM_TEX(v.uv, _MaskTex);
        o.uv[1] = TRANSFORM_TEX(v.uv, _L1Tex);
        o.uv[2] = TRANSFORM_TEX(v.uv, _L2Tex);
        o.uv[3] = TRANSFORM_TEX(v.uv, _L3Tex);
        o.uv[4] = TRANSFORM_TEX(v.uv, _DetailMask);

        //TRANSFER_SHADOW(o);
        return o;
    }

    float InverseLerp(float A, float B, float T)
    {
        return (T - A) / (B - A);
    }

    fixed Sharpening(fixed fValue)
    {
        return saturate(_Sharpening * (-cos(fValue * PI) * 0.5 + 0.5));
    }

    fixed4 frag(v2f i) : SV_Target
    {
        fixed4 mask = tex2D(_MaskTex, i.uv[0]);
        fixed4 col = 0;
        fixed4 farCol = 0;
        float currentCameraDistance = _WorldSpaceCameraPos.y;

        #if HIGH_QUALITY	
        		fixed4 toggledDetail = float4(0, 0, 0, 0);

        		//#ifdef  DETAILMASKL1_ON
        			toggledDetail.r = mask.r;
        		//#endif

        		//#ifdef  DETAILMASKL2_ON
        			toggledDetail.g = mask.g;
        		//#endif

        		//#ifdef  DETAILMASKL3_ON
                   	toggledDetail.b = mask.b;
                //#endif

        	    //#ifdef  DETAILMASKL4_ON
                    toggledDetail.a = mask.a;
                //#endif

        		float detailAlpha = saturate(toggledDetail.r + toggledDetail.g + toggledDetail.b + toggledDetail.a);
        #endif

        if (currentCameraDistance < _SwitchDistanceFar * _SwitchDistanceModifier)
        {
            fixed4 l14 = tex2D(_L1Tex, i.uv[1]);
            fixed4 l24 = tex2D(_L2Tex, i.uv[2]);
            fixed4 l34 = tex2D(_L3Tex, i.uv[3]);

            fixed4 l1 = fixed4(l14.r, l14.g, l14.b, 1);
            fixed4 l2 = fixed4(l24.r, l24.g, l24.b, 1);
            fixed4 l3 = fixed4(l34.r, l34.g, l34.b, 1);
            fixed4 l4 = fixed4(l14.a, l24.a, l34.a, 1);

            col.rgb = lerp(lerp(lerp(l1.rgb, l2.rgb, Sharpening(mask.r)), l3.rgb, Sharpening(mask.g)), l4.rgb, Sharpening(mask.b));
            farCol.rgb = lerp(lerp(lerp(_L1FarColor.rgb, _L2FarColor.rgb, mask.r), _L3FarColor.rgb, mask.g), _L4FarColor.rgb, mask.b);

            float currentCameraScale = saturate(InverseLerp(_SwitchDistanceNear, _SwitchDistanceFar * _SwitchDistanceModifier, currentCameraDistance));

            col.rgb = lerp(col.rgb, farCol.rgb, currentCameraScale);

            #if HIGH_QUALITY
            			fixed4 dM = tex2D(_DetailMask, i.uv[4]);
            			fixed4 detailMask = fixed4(dM.a,dM.a,dM.a,1);
            			fixed4 detailTex = fixed4(dM.r,dM.g,dM.b,1);
            			detailMask = lerp(detailMask, 0, saturate(((currentCameraDistance - _SwitchDetailNear) / _SwitchDetailFar / _SwitchDistanceModifier)));
            			col  =   lerp(col, (1 - _DetailFarColor.a * detailAlpha) * col +  detailTex  * _DetailFarColor.a * detailAlpha, detailMask);
            #ifdef SHADOWTOGGLE_ON
            			float atten = SHADOW_ATTENUATION(i);
            			col.rgb = col.rgb * atten;
            #endif
            #endif
            		}
            		else
            		{
            			col.rgb = lerp(lerp(lerp(_L1FarColor.rgb, _L2FarColor.rgb, mask.r), _L3FarColor.rgb, mask.g), _L4FarColor.rgb, mask.b);

            #if HIGH_QUALITY
            			fixed4 dM = tex2D(_DetailMask, i.uv[4]);
            			fixed4 detailMask = fixed4(dM.a,dM.a,dM.a,1);
            			col  =   lerp(col, (1 - _DetailFarColor.a * detailAlpha) * col +  _DetailFarColor * _DetailFarColor.a * detailAlpha, detailMask);
            #endif
        }

        #ifdef DAYNIGHTTOGGLE_GLOBAL_ON
              	#ifdef DAYNIGHTTOGGLE_ON
                      col.rgb = ApplyDayNightLut(col.rgb);
        	#endif
        #endif

        return col;
    }
    ENDCG

    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "Queue" = "Geometry-2"
        }
        LOD 100
        ZWrite[_ZWrite]

        //Stencil
        //      {
        //          Ref[_Stencil]
        //          Comp[_StencilComp]
        //          ReadMask[_StencilReadMask]
        //          WriteMask[_StencilWriteMask]
        //          Pass[_StencilPass]
        //          Fail[_StencilFail]
        //          ZFail[_StencilZFail]
        //      }

        Pass
        {
            Tags
            {
                "LightMode" = "ForwardBase"
            }

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            //#pragma target 3.0
            //#pragma multi_compile_fog
            #pragma multi_compile_fwdbase
            //#pragma multi_compile_instancing
            #pragma multi_compile __ HIGH_QUALITY
            //#pragma multi_compile __ DAYNIGHTTOGGLE_GLOBAL_ON
            //#pragma multi_compile_local __ DAYNIGHTTOGGLE_ON
            //#pragma multi_compile_local __ SHADOWTOGGLE_ON
            //#pragma multi_compile_local __  DETAILMASKL1_ON
            //#pragma multi_compile_local __  DETAILMASKL2_ON
            //#pragma multi_compile_local __  DETAILMASKL3_ON
            //#pragma multi_compile_local __  DETAILMASKL4_ON
            ENDCG
        }
    }
    Fallback "Diffuse"
}