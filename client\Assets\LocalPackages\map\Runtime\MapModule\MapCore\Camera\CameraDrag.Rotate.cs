﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;
using System.Text;

namespace TFW.Map
{
    public partial class CameraDrag : CameraAction
    {
        void OnTouchRightButton(IMapTouch touch)
        {
            if (touch.state == MapTouchState.Touch)
            {
                mInertiaVelocity = Vector3.zero;

                mCanDrag = !touch.blockUIDrag;

                ClearTouchies();

                mCameraState = CameraState.Rotating;
            }
            else if (touch.state == MapTouchState.Touching)
            {
                if (mCanDrag)
                {
                    enabled = true;
                }
                else
                {
                    OnFinish();
                }

                mCameraState = CameraState.Rotating;
            }
            else if (touch.state == MapTouchState.Release)
            {
                OnFinish();
            }
        }

        Vector3 OnRotate(Vector3 currentCameraPos, float dragDir, float offset)
        {
            if (currentCameraPos.y >= MapCameraMgr.cameraSetting.maxHorizontalRotationHeight || Input.GetKey(KeyCode.LeftAlt))
            {
                return currentCameraPos;
            }

            //拖动相机中
            var viewCenter = Map.currentMap.viewCenter;
            var cameraToViewCenter = currentCameraPos - viewCenter;
            float viewDistance = cameraToViewCenter.magnitude;
            var cameraRoot = MapCameraMgr.MapCameraRoot.transform;
            //根据拖动距离计算应该旋转多少度
            float rotationX = cameraRoot.rotation.eulerAngles.x;
            float rotationRadius = viewDistance * Mathf.Cos(rotationX * Mathf.Deg2Rad);
            float arcLength = offset * 3;

            float speed = 1.0f;
            if (mRotateSpeedModifierCallback != null)
            {
                speed = mRotateSpeedModifierCallback(currentCameraPos.y);
            }
            float angle = arcLength / rotationRadius * Mathf.Rad2Deg * dragDir * speed;

            float maxAngle = MapCameraMgr.cameraSetting.horizontalRotationRange;
            float currentAngle = MapCameraMgr.updatedCameraYRotation;
            //clamp angles
            float newAngle = currentAngle + angle;
            float deltaAngle = Mathf.Abs(angle);
            if (newAngle < -maxAngle)
            {
                deltaAngle = Mathf.Abs(angle) - Mathf.Abs(newAngle + maxAngle);
            }
            else if (newAngle > maxAngle)
            {
                deltaAngle = Mathf.Abs(angle) - Mathf.Abs(newAngle - maxAngle);
            }
            deltaAngle *= Mathf.Sign(angle);
            currentAngle += deltaAngle;

            //Debug.LogError($"current angle is: {currentAngle}");
            Quaternion rotation = Quaternion.Euler(0, deltaAngle, 0);
            var cameraTargetPos = viewCenter + rotation * cameraToViewCenter.normalized * viewDistance;
            MapCameraMgr.updatedCameraYRotation = currentAngle;

            return cameraTargetPos;
        }

        public void ResetCurrentAngle()
        {
            //mCurrentAngle = 0;
        }

        public System.Func<float, float> rotateSpeedModifierCallback { set { mRotateSpeedModifierCallback = value; } get { return mRotateSpeedModifierCallback; } }

        //rotation parameter
        //float mCurrentAngle = 0;
        float mRotateDragDir = 0;
        float mDragMoveScreenOffset = 0;
        System.Func<float, float> mRotateSpeedModifierCallback;
    }
}