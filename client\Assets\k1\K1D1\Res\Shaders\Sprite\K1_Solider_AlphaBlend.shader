﻿// Upgrade NOTE: upgraded instancing buffer 'MyProperties' to new syntax.

Shader "K1/Solider/Alpha Blend"
{
    Properties
    {
		_MainTex("Texture", 2D) = "white" {}
		_Intensity("Intensity", Range(0.1,5)) = 1
		_Cutoff("Alpha cutoff", Range(0,1)) = 0.2
    }
    SubShader
    {
        Cull Off ZWrite Off ZTest On
		blend SrcAlpha OneMinusSrcAlpha

		Tags
		{
			"Queue" = "Transparent" "IgnoreProjector" = "True" "RenderType"="Transparent"
		}

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"
			#pragma multi_compile_instancing

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
				float4 color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
				float4 color : COLOR;
				UNITY_VERTEX_OUTPUT_STEREO
            };

			sampler2D _MainTex;
			half _Intensity;
			half _Cutoff;

            v2f vert (appdata v)
            {
                v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
				o.color = v.color;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);
                col.rgb = col.rgb * _Intensity;
                return col * i.color;
            }
            ENDCG
        }
    }
}
