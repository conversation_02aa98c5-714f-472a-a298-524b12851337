﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionAddVariation : EditorAction
    {
        public ActionAddVariation(GroundTileMaker maker, int tileIndex, string name, int instanceID, List<List<Color32[]>> maskTextureDatas)
        {
            mMaker = maker;
            mTileIndex = tileIndex;
            mInstanceID = instanceID;
            mName = name;
            mMaskTextureDatas = maskTextureDatas;
        }

        public override bool Do()
        {
            if (mMaskTextureDatas.Count != mMaker.lodCount)
            {
                //lod数据已经不同步了,不能执行这个命令了
                return false;
            }

            mMaker.AddVariation(mTileIndex, mName, mInstanceID, mMaskTextureDatas);
            return true;
        }

        public override bool Undo()
        {
            if (mMaskTextureDatas.Count != mMaker.lodCount)
            {
                //lod数据已经不同步了,不能执行这个命令了
                return false;
            }

            mMaker.RemoveVariationByID(mTileIndex, mInstanceID);
            return true;
        }

        GroundTileMaker mMaker;
        int mTileIndex;
        int mInstanceID;
        string mName;
        List<List<Color32[]>> mMaskTextureDatas;
    }
}

#endif