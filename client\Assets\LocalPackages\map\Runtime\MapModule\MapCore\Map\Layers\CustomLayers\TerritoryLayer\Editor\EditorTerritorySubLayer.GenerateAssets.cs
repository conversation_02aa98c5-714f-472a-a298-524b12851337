﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public partial class EditorTerritorySubLayer
    {
        public void GenerateAssets(string folder)
        {
            for (int i = 0; i < mMeshGenerationParamForLODs.Count; ++i)
            {
                GenerateLODAssets(folder, mMeshGenerationParamForLODs[i], i);
            }
        }

        void GenerateLODAssets(string assetFolder, EditorTerritoryMeshGenerationParam param, int lod)
        {
            if (param.territoryMeshMaterial == null)
            {
                Debug.LogError("Invalid Material");
                return;
            }

            for (int i = 0; i < mTerritories.Count; ++i)
            {
                var coord = GetTerritoryCoordinates(mTerritories[i].id);
                if (coord.Count > 0)
                {
                    var polygon = GetOutlinePolygon(mTerritories[i].id, coord);
                    if (param.curveCorner)
                    {
                        polygon = ConvertToCurveOutline(polygon, param.borderSizeRatio * mGridWidth, param.cornerSegment);
                    }
                    var mesh = CreateTerritoryMesh(mTerritories[i].id, polygon, mTerritories[i].GetColor(), param.borderSizeRatio, param.uvScale, true);

                    string prefix = $"{assetFolder}/territory_{mTerritories[i].id}_lod{lod}.";

                    AssetDatabase.CreateAsset(mesh, prefix + "asset");

                    Material mtl = Object.Instantiate<Material>(param.territoryMeshMaterial);
                    mtl.color = mTerritories[i].GetColor();
                    AssetDatabase.CreateAsset(mtl, prefix + "mat");

                    GameObject obj = new GameObject(mTerritories[i].id + " LOD1");

                    var renderer = obj.AddComponent<MeshRenderer>();
                    var filter = obj.AddComponent<MeshFilter>();
                    filter.sharedMesh = mesh;
                    renderer.sharedMaterial = mtl;

                    PrefabUtility.SaveAsPrefabAsset(obj, prefix + "prefab");
                    GameObject.DestroyImmediate(obj);
                }
            }
        }
    }
}

#endif