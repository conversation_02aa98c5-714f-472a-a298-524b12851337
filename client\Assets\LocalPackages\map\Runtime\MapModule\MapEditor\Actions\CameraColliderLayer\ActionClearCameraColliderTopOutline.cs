﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionClearCameraColliderTopOutline : EditorAction
    {
        public ActionClearCameraColliderTopOutline(int layerID, int dataID)
        {
            mDataID = dataID;
            mLayerID = layerID;
            var data = Map.currentMap.FindObject(mDataID) as CameraColliderData;
            mOldOutline = data.GetTopOutlineCopy();
            mOldHeight = data.height;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetTopOutline(mDataID, null, 0);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetTopOutline(mDataID, mOldOutline, mOldHeight);
            return true;
        }

        List<Vector3> mOldOutline;
        float mOldHeight;
        int mDataID;
        int mLayerID;
    }
}

#endif