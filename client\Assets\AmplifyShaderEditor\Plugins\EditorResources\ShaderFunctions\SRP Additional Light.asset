%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: SRP Additional Light
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17503\n385;373;1413;749;2027.142;507.6773;1.942238;True;False\nNode;AmplifyShaderEditor.FunctionInput;14;-736,368;Inherit;False;Specular
    Color;3;3;False;1;0;FLOAT3;1,1,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldNormalVector;12;-1116,127;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WorldNormalVector;4;-1312,288;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;10;-1472,128;Inherit;False;Constant;_Vector0;Vector
    0;0;0;Create;True;0;0;False;0;0,0,1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionInput;18;-736,448;Inherit;False;Smoothness;1;4;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;15;-736,272;Inherit;False;View
    Dir;3;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;22;-496,208;Inherit;False;float3
    Color = 0@$#ifdef _ADDITIONAL_LIGHTS$int numLights = GetAdditionalLightsCount()@$for(int
    i = 0@ i<numLights@i++)${$\tLight light = GetAdditionalLight(i, WorldPosition)@$\thalf3
    AttLightColor = light.color *(light.distanceAttenuation * light.shadowAttenuation)@$\tColor
    +=(dot(light.direction, WorldNormal)*0.5+0.5 )* AttLightColor@$\t$}$#endif$return
    Color@;3;False;2;True;WorldPosition;FLOAT3;0,0,0;In;;Float;False;True;WorldNormal;FLOAT3;0,0,0;In;;Float;False;AdditionalLightsHalfLambert;False;False;0;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;3;-736,0;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionSwitch;9;-848,160;Inherit;False;Normal
    Space;False;0;2;1;Tangent Space;World Space;Object;-1;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;16;-1088,384;Inherit;False;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.CustomExpressionNode;13;-496,320;Inherit;False;float3
    Color = 0@$#ifdef _ADDITIONAL_LIGHTS$Smoothness = exp2(10 * Smoothness + 1)@$int
    numLights = GetAdditionalLightsCount()@$for(int i = 0@ i<numLights@i++)${$\tLight
    light = GetAdditionalLight(i, WorldPosition)@$\thalf3 AttLightColor = light.color
    *(light.distanceAttenuation * light.shadowAttenuation)@$\tColor += LightingSpecular(AttLightColor,
    light.direction, WorldNormal, WorldView, half4(SpecColor, 0), Smoothness)@\t$}$#endif$return
    Color@;3;False;5;True;WorldPosition;FLOAT3;0,0,0;In;;Float;False;True;WorldNormal;FLOAT3;0,0,0;In;;Float;False;True;WorldView;FLOAT3;0,0,0;In;;Float;False;True;SpecColor;FLOAT3;0,0,0;In;;Float;False;True;Smoothness;FLOAT;0.5;In;;Float;False;AdditionalLightsSpecular;False;False;0;5;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT;0.5;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;23;-234.1137,81.67668;Inherit;False;Half
    Lambert;True;0;2;2;In 0;In 1;Object;-1;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;11;-1104,288;Inherit;False;World
    Normal;3;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;8;-496,0;Inherit;False;float3
    Color = 0@$#ifdef _ADDITIONAL_LIGHTS$int numLights = GetAdditionalLightsCount()@$for(int
    i = 0@ i<numLights@i++)${$\tLight light = GetAdditionalLight(i, WorldPosition)@$\tColor
    += light.color *(light.distanceAttenuation * light.shadowAttenuation)@$\t$}$#endif$return
    Color@;3;False;1;True;WorldPosition;FLOAT3;0,0,0;In;;Float;False;AdditionalLightsFlat;False;False;0;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;21;144,128;Inherit;False;Constant;_Vector1;Vector
    1;0;0;Create;True;0;0;False;0;0,0,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionInput;2;-1302,128;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;5;-496,96;Inherit;False;float3
    Color = 0@$#ifdef _ADDITIONAL_LIGHTS$int numLights = GetAdditionalLightsCount()@$for(int
    i = 0@ i<numLights@i++)${$\tLight light = GetAdditionalLight(i, WorldPosition)@$\thalf3
    AttLightColor = light.color *(light.distanceAttenuation * light.shadowAttenuation)@$\tColor
    +=LightingLambert(AttLightColor, light.direction, WorldNormal)@$\t$}$#endif$return
    Color@;3;False;2;True;WorldPosition;FLOAT3;0,0,0;In;;Float;False;True;WorldNormal;FLOAT3;0,0,0;In;;Float;False;AdditionalLightsLambert;False;False;0;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;19;368,0;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;6;64,0;Inherit;False;Lighting
    Mode;False;0;3;0;Flat;Lambert;Specular;Object;-1;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;592,0;Inherit;False;True;-1;Out;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;12;0;2;0\nWireConnection;15;0;16;0\nWireConnection;22;0;3;0\nWireConnection;22;1;9;0\nWireConnection;9;0;12;0\nWireConnection;9;1;11;0\nWireConnection;13;0;3;0\nWireConnection;13;1;9;0\nWireConnection;13;2;15;0\nWireConnection;13;3;14;0\nWireConnection;13;4;18;0\nWireConnection;23;0;5;0\nWireConnection;23;1;22;0\nWireConnection;11;0;4;0\nWireConnection;8;0;3;0\nWireConnection;2;0;10;0\nWireConnection;5;0;3;0\nWireConnection;5;1;9;0\nWireConnection;19;0;21;0\nWireConnection;19;3;21;0\nWireConnection;19;1;6;0\nWireConnection;19;2;21;0\nWireConnection;6;0;8;0\nWireConnection;6;1;23;0\nWireConnection;6;2;13;0\nWireConnection;0;0;19;0\nASEEND*/\n//CHKSM=21AD7F10AE383CB3691A52293A399724359D1F3D"
  m_functionName: 
  m_description: Returns SRP's additional lights information calculated with the selected
    lighting mode
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives:
    - {fileID: 0}
    - {fileID: 0}
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems:
    - LineType: 2
      LineValue: multi_compile _ _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS
      GUIDToggle: 0
      GUIDValue: 
      Origin: 2
    - LineType: 2
      LineValue: multi_compile _ _ADDITIONAL_LIGHT_SHADOWS
      GUIDToggle: 0
      GUIDValue: 
      Origin: 2
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
