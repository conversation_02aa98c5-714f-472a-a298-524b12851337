﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    class HideAtHeight : MonoBehaviour
    {
        public float hideHeight = 0;
        Renderer[] mRenderers;

        void OnEnable()
        {
            if (mRenderers == null)
            {
                mRenderers = gameObject.GetComponentsInChildren<Renderer>();
                if (mRenderers == null)
                {
                    mRenderers = new Renderer[0];
                }
            }
            Map.currentMap.ZoomChangeEvent += OnCameraHeightChange;
            OnCameraHeightChange(0);
        }

        void OnDisable()
        {
            Map.currentMap.ZoomChangeEvent -= OnCameraHeightChange;
        }

        void OnCameraHeightChange(float zoom)
        {
            float height = Map.currentMap.camera.transform.position.y;
            if (height >= hideHeight)
            {
                SetVisible(false);
            }
            else
            {
                SetVisible(true);
            }
        }

        void SetVisible(bool visible)
        {
            for (int i = 0; i < mRenderers.Length; ++i)
            {
                mRenderers[i].enabled = visible;
            }
        }
    }
}
