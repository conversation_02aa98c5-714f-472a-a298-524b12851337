%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &21472355118038553
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3201354552062086967}
  - component: {fileID: 1255187130487952029}
  m_Layer: 5
  m_Name: ButtomText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &3201354552062086967
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 21472355118038553}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6618874122017418050}
  - {fileID: 249259763342361585}
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -476}
  m_SizeDelta: {x: 1017.18, y: 75.385925}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1255187130487952029
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 21472355118038553}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 4
  m_Spacing: 0.91
  m_ChildForceExpandWidth: 0
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 1
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &74136770969010507
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3443585769697888541}
  - component: {fileID: 4692827325013528494}
  - component: {fileID: 4223652179410699791}
  m_Layer: 5
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3443585769697888541
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 74136770969010507}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.8, y: 0.8, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5821629600794733537}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -0.79998446, y: 5.5999756}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4692827325013528494
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 74136770969010507}
  m_CullTransparentMesh: 0
--- !u!114 &4223652179410699791
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 74136770969010507}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 30d33c150f1c7584eb5d1b6d98423310, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &107696406297606102
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2746047144053643310}
  - component: {fileID: 1076360918534301945}
  - component: {fileID: 8663734628227259719}
  - component: {fileID: 1701363288012504116}
  - component: {fileID: 6357097443862539079}
  - component: {fileID: 1554874227316405311}
  m_Layer: 5
  m_Name: UICollectDebrisMonsterPop
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2746047144053643310
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 107696406297606102}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7572080543535827430}
  - {fileID: 2818676479525928486}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!223 &1076360918534301945
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 107696406297606102}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &8663734628227259719
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 107696406297606102}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1080, y: 1920}
  m_ScreenMatchMode: 1
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!114 &1701363288012504116
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 107696406297606102}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4108320567
--- !u!114 &6357097443862539079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 107696406297606102}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1e6e899d6900df241b13b4d734f7ef2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Root: {fileID: 0}
  isForceNotFlip: 0
  IsFlipRoot: 0
  isRotationObj: 0
  isFindInActiveObj: 0
  isFindScrowViewObj: 0
--- !u!114 &1554874227316405311
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 107696406297606102}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5fe1699e5848f974c9f5c4a583129093, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _entityIcon: {fileID: 5325327377187322520}
  _npcBoxIcon: {fileID: 7743070510873594674}
  _title: {fileID: 6113897881276249863}
  _confirmButton: {fileID: 6264822645431002297}
  _confirmButtonText: {fileID: 6670648881202923459}
  _rewardListUI: {fileID: 7753722467098337578}
--- !u!1 &293671352375513079
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2507267041502592830}
  - component: {fileID: 4232664938178003044}
  m_Layer: 5
  m_Name: Reward
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &2507267041502592830
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293671352375513079}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2382913761135734794}
  - {fileID: 5821629600590140035}
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -307}
  m_SizeDelta: {x: 1080, y: 345}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4232664938178003044
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293671352375513079}
  m_CullTransparentMesh: 0
--- !u!1 &415353666856019559
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 249259763342361585}
  - component: {fileID: 353582670859021020}
  - component: {fileID: 5088501785693294952}
  - component: {fileID: 1792504637340764597}
  - component: {fileID: 2935537661313431921}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &249259763342361585
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 415353666856019559}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3201354552062086967}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 556.545, y: -37.692963}
  m_SizeDelta: {x: 63, y: 50}
  m_Pivot: {x: 0, y: 0.5}
--- !u!222 &353582670859021020
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 415353666856019559}
  m_CullTransparentMesh: 0
--- !u!114 &5088501785693294952
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 415353666856019559}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961, type: 3}
    m_FontSize: 38
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 3
    m_MaxSize: 60
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 6/10
  dontConvertLineFeed: 0
  m_Key: 
  textUpdateEvent:
    m_PersistentCalls:
      m_Calls: []
  isOnlyOnceTextUpdateEvent: 1
  isRefreshTextWhenStart: 0
  IsTranslate: 0
  m_editorText: 
  EditorMode: 0
  UseCharComposing: 0
  isFading: 0
  isUseSpecifiedAlignment: 0
  specifiedAlignment: 0
  isUseSrcText: 0
  isMailText: 0
  isFlipDelayText: 0
  m_IsForceFlip: 1
--- !u!114 &1792504637340764597
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 415353666856019559}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ee2af72b1959b7d43841216a2499281b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 2, y: -2}
  m_UseGraphicAlpha: 1
  m_bShadow: 1
  m_fShadowOffset: -2
  m_TextOffset: {x: 0, y: 0}
  m_TextScale: {x: 1, y: 1}
  m_TextScaleUV: {x: 0.5, y: 0.5}
  isFading: 0
--- !u!114 &2935537661313431921
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 415353666856019559}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfabb0440166ab443bba8876756fdfa9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 0, y: -2}
  m_UseGraphicAlpha: 1
--- !u!1 &466664475382359308
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8599581398378644585}
  - component: {fileID: 1503890833228587440}
  - component: {fileID: 1747274164387776870}
  m_Layer: 5
  m_Name: fill_rect
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8599581398378644585
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 466664475382359308}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 965813157956484743}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1503890833228587440
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 466664475382359308}
  m_CullTransparentMesh: 0
--- !u!114 &1747274164387776870
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 466664475382359308}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: ef54a4cab7a6eac45b12c8c7f82a4b4d, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &2066147952217037482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6541449271579392988}
  - component: {fileID: 1956026201724057117}
  - component: {fileID: 6390487647354463060}
  - component: {fileID: 3504307166467747764}
  - component: {fileID: 3858705049178296516}
  - component: {fileID: 1358799985608769809}
  m_Layer: 5
  m_Name: InfoBtn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &6541449271579392988
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066147952217037482}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 1006.52, y: -850.42975}
  m_SizeDelta: {x: 77, y: 77}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1956026201724057117
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066147952217037482}
  m_CullTransparentMesh: 0
--- !u!114 &6390487647354463060
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066147952217037482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 24b25ff18931fb5479b6bc38fb2a9346, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!114 &3504307166467747764
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066147952217037482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1da2a900e79078449848779d2f553a64, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  LongPressBeginTime: 0.3
  LongPressEndTime: 1
  LongPressProgress: 0
  clickCD: 0
  clickCDTime: 1.2
  handled: 1
  SRFilter: {fileID: 0}
  NeedRecoverTimeScale: 0
  scrollRect: {fileID: 0}
  IsResponseDrag: 1
--- !u!114 &3858705049178296516
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066147952217037482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5e2aea1dd1bda784e98010997c09baef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 2
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 6390487647354463060}
  normal: {x: 1, y: 1, z: 1}
  pressed: {x: 0.95, y: 0.95, z: 0.95}
  durationPress: 0.1
  unscaleTime: 0
  boundValue: -0.15
  durationBound: 0.15
  enableBound: 0
  enableSpriteSwapDisable: 0
  clickAudioAssetPath: 
--- !u!114 &1358799985608769809
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066147952217037482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec175bbb6e5387041b06488a84822b93, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  audioAsset: 
  audioID: 0
  clickType: 1
--- !u!1 &2818676479525928487
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2818676479525928486}
  - component: {fileID: 1504190125584541946}
  - component: {fileID: 1539638708212658455}
  m_Layer: 5
  m_Name: Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2818676479525928486
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2818676479525928487}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6168214734144555236}
  - {fileID: 7237607460181374245}
  - {fileID: 919882132642567797}
  - {fileID: 6541449271579392988}
  - {fileID: 6790851758366003038}
  - {fileID: 7847714723864483964}
  - {fileID: 2507267041502592830}
  - {fileID: 1980564842219493908}
  - {fileID: 7815470120914884742}
  - {fileID: 4332566865729481425}
  - {fileID: 963950857035273894}
  - {fileID: 3201354552062086967}
  m_Father: {fileID: 2746047144053643310}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 1080, y: 1920}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!95 &1504190125584541946
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2818676479525928487}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 206210e8cebd46b449bcc030747be70a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &1539638708212658455
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2818676479525928487}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3265ec2ed595e7741b4b41fc460d1187, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &3752358767887326676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1723551211780945116}
  - component: {fileID: 5765010852344063635}
  m_Layer: 5
  m_Name: Slider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1723551211780945116
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3752358767887326676}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6910543379960336938}
  - {fileID: 965813157956484743}
  - {fileID: ************7135595}
  m_Father: {fileID: 7847714723864483964}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0.011230469, y: 1}
  m_SizeDelta: {x: 815, y: 36}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &5765010852344063635
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3752358767887326676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cf18302123c942043a2a6034a2ac2aa2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.9607844, g: 0.9607844, b: 0.9607844, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.9607844, g: 0.9607844, b: 0.9607844, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Highlighted
    m_DisabledTrigger: Disabled
  m_Interactable: 0
  m_TargetGraphic: {fileID: 0}
  m_FillRect: {fileID: 8599581398378644585}
  m_HandleRect: {fileID: 0}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 1
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
  isDrag: 0
  onDrag:
    m_PersistentCalls:
      m_Calls: []
  onPointDown:
    m_PersistentCalls:
      m_Calls: []
  onPointUp:
    m_PersistentCalls:
      m_Calls: []
  onSelect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &4487878540253863576
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7572080543535827430}
  - component: {fileID: 2828164928775598321}
  - component: {fileID: 7201897496626658792}
  m_Layer: 5
  m_Name: Black
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7572080543535827430
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4487878540253863576}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 1100}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2746047144053643310}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2828164928775598321
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4487878540253863576}
  m_CullTransparentMesh: 0
--- !u!114 &7201897496626658792
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4487878540253863576}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac94dbba2d7c56d4d8200315f3439f25, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.78431374}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Texture: {fileID: 2800000, guid: 8e3d262d67beeaa4c906ceda52932db3, type: 3}
  m_UVRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  m_IsForceFlip: 0
--- !u!1 &4662676200584561372
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3326204396037075293}
  - component: {fileID: 7201215863019769606}
  - component: {fileID: 7115144550881841791}
  - component: {fileID: 2006696737938612710}
  - component: {fileID: 8129798317793468313}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3326204396037075293
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4662676200584561372}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1463342366988305792}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -0.0000076294, y: 1.4}
  m_SizeDelta: {x: 85, y: 46}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7201215863019769606
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4662676200584561372}
  m_CullTransparentMesh: 0
--- !u!114 &7115144550881841791
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4662676200584561372}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961, type: 3}
    m_FontSize: 35
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 3
    m_MaxSize: 42
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: AAAAA
  dontConvertLineFeed: 0
  m_Key: 
  textUpdateEvent:
    m_PersistentCalls:
      m_Calls: []
  isOnlyOnceTextUpdateEvent: 1
  isRefreshTextWhenStart: 0
  IsTranslate: 0
  m_editorText: 
  EditorMode: 0
  UseCharComposing: 0
  isFading: 0
  isUseSpecifiedAlignment: 0
  specifiedAlignment: 0
  isUseSrcText: 0
  isMailText: 0
  isFlipDelayText: 0
  m_IsForceFlip: 1
--- !u!114 &2006696737938612710
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4662676200584561372}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 2
  m_VerticalFit: 2
--- !u!114 &8129798317793468313
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4662676200584561372}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ee2af72b1959b7d43841216a2499281b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
  m_bShadow: 1
  m_fShadowOffset: -2
  m_TextOffset: {x: 0, y: 0}
  m_TextScale: {x: 1, y: 1}
  m_TextScaleUV: {x: 0.5, y: 0.5}
  isFading: 0
--- !u!1 &4895893551510046188
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3127943366203605455}
  - component: {fileID: 5954157830061438771}
  - component: {fileID: 5156088646436288374}
  - component: {fileID: 1020090634235219698}
  - component: {fileID: 2718796368391827228}
  m_Layer: 5
  m_Name: Count
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3127943366203605455
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4895893551510046188}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5821629600794733537}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 75.7, y: -78.8}
  m_SizeDelta: {x: 61, y: 55}
  m_Pivot: {x: 1, y: 0}
--- !u!222 &5954157830061438771
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4895893551510046188}
  m_CullTransparentMesh: 0
--- !u!114 &5156088646436288374
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4895893551510046188}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961, type: 3}
    m_FontSize: 42
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 2
    m_MaxSize: 65
    m_Alignment: 5
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: X20
  dontConvertLineFeed: 0
  m_Key: 
  textUpdateEvent:
    m_PersistentCalls:
      m_Calls: []
  isOnlyOnceTextUpdateEvent: 1
  isRefreshTextWhenStart: 0
  IsTranslate: 0
  m_editorText: 
  EditorMode: 0
  UseCharComposing: 0
  isFading: 0
  isUseSpecifiedAlignment: 0
  specifiedAlignment: 0
  isUseSrcText: 0
  isMailText: 0
  isFlipDelayText: 0
  m_IsForceFlip: 1
--- !u!114 &1020090634235219698
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4895893551510046188}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 2
  m_VerticalFit: 2
--- !u!114 &2718796368391827228
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4895893551510046188}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ee2af72b1959b7d43841216a2499281b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 2, y: -2}
  m_UseGraphicAlpha: 1
  m_bShadow: 1
  m_fShadowOffset: -2
  m_TextOffset: {x: 0, y: 0}
  m_TextScale: {x: 1, y: 1}
  m_TextScaleUV: {x: 0.5, y: 0.5}
  isFading: 0
--- !u!1 &4969587802984845150
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2382913761135734794}
  - component: {fileID: 4677877560238853582}
  - component: {fileID: 1811306415900488305}
  m_Layer: 5
  m_Name: BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2382913761135734794
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4969587802984845150}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2507267041502592830}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 11.979}
  m_SizeDelta: {x: 952.83, y: 257.04218}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4677877560238853582
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4969587802984845150}
  m_CullTransparentMesh: 0
--- !u!114 &1811306415900488305
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4969587802984845150}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.8196079, g: 0.8588236, b: 0.8980393, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: d769c3a33cd42b84a9d518678da2d9bf, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &5407352836944372812
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6618874122017418050}
  - component: {fileID: 3752470947336003731}
  - component: {fileID: 5180153859011730704}
  - component: {fileID: 5880423396660865146}
  - component: {fileID: 8713226804498835961}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6618874122017418050
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5407352836944372812}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3201354552062086967}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 476.635, y: -37.692963}
  m_SizeDelta: {x: 158, y: 60}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3752470947336003731
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5407352836944372812}
  m_CullTransparentMesh: 0
--- !u!114 &5180153859011730704
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5407352836944372812}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.40000004, g: 0.47450984, b: 0.6392157, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961, type: 3}
    m_FontSize: 32
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 3
    m_MaxSize: 60
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: " :\u91CF\u6570\u4F59\u5269"
  dontConvertLineFeed: 0
  m_Key: Anniv_collect_restnum
  textUpdateEvent:
    m_PersistentCalls:
      m_Calls: []
  isOnlyOnceTextUpdateEvent: 1
  isRefreshTextWhenStart: 0
  IsTranslate: 0
  m_editorText: 
  EditorMode: 0
  UseCharComposing: 0
  isFading: 0
  isUseSpecifiedAlignment: 0
  specifiedAlignment: 0
  isUseSrcText: 0
  isMailText: 0
  isFlipDelayText: 0
  m_IsForceFlip: 1
--- !u!114 &5880423396660865146
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5407352836944372812}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ee2af72b1959b7d43841216a2499281b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 3, y: -3}
  m_UseGraphicAlpha: 1
  m_bShadow: 0
  m_fShadowOffset: 0
  m_TextOffset: {x: 0, y: 0}
  m_TextScale: {x: 1, y: 1}
  m_TextScaleUV: {x: 0.5, y: 0.5}
  isFading: 0
--- !u!114 &8713226804498835961
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5407352836944372812}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfabb0440166ab443bba8876756fdfa9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 0, y: -2}
  m_UseGraphicAlpha: 1
--- !u!1 &5473122438184229023
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 919882132642567797}
  - component: {fileID: 1750855269509649544}
  - component: {fileID: 253509193910944214}
  m_Layer: 5
  m_Name: BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &919882132642567797
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5473122438184229023}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -177.461}
  m_SizeDelta: {x: 1040.76, y: 672.46326}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1750855269509649544
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5473122438184229023}
  m_CullTransparentMesh: 0
--- !u!114 &253509193910944214
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5473122438184229023}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 71fd6b9154fd7cc40b1a69f6c2e59c07, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &5530092963152018573
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7815470120914884742}
  - component: {fileID: 8201539632165775389}
  - component: {fileID: 2414377904544927454}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7815470120914884742
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5530092963152018573}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 10}
  m_SizeDelta: {x: 911.7, y: 59}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8201539632165775389
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5530092963152018573}
  m_CullTransparentMesh: 0
--- !u!114 &2414377904544927454
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5530092963152018573}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.27450982, g: 0.33333334, b: 0.5176471, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961, type: 3}
    m_FontSize: 45
    m_FontStyle: 0
    m_BestFit: 1
    m_MinSize: 4
    m_MaxSize: 45
    m_Alignment: 3
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: "\u5305\u542B\u4EE5\u4E0B\u5956\u52B1"
  dontConvertLineFeed: 0
  m_Key: Rally_Random_Player_Chest2
  textUpdateEvent:
    m_PersistentCalls:
      m_Calls: []
  isOnlyOnceTextUpdateEvent: 1
  isRefreshTextWhenStart: 0
  IsTranslate: 0
  m_editorText: 
  EditorMode: 0
  UseCharComposing: 0
  isFading: 0
  isUseSpecifiedAlignment: 0
  specifiedAlignment: 0
  isUseSrcText: 0
  isMailText: 0
  isFlipDelayText: 0
  m_IsForceFlip: 1
--- !u!1 &5640254271798045058
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3791212599467467922}
  - component: {fileID: 4309267020025190631}
  m_Layer: 5
  m_Name: Content
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3791212599467467922
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5640254271798045058}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5821629600794733537}
  m_Father: {fileID: 5178655452542993938}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0.0006713867, y: 3}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4309267020025190631
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5640254271798045058}
  m_CullTransparentMesh: 0
--- !u!1 &5650668475018363043
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1980564842219493908}
  - component: {fileID: 897640285389553076}
  - component: {fileID: 723900228887651095}
  m_Layer: 5
  m_Name: BG1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1980564842219493908
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5650668475018363043}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -121.020996}
  m_SizeDelta: {x: 952.83, y: 257.04218}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &897640285389553076
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5650668475018363043}
  m_CullTransparentMesh: 0
--- !u!114 &723900228887651095
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5650668475018363043}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.8196079, g: 0.8588236, b: 0.8980393, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: d769c3a33cd42b84a9d518678da2d9bf, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &5697308819220148596
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7847714723864483964}
  - component: {fileID: 3929010573527079085}
  m_Layer: 5
  m_Name: Slider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &7847714723864483964
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5697308819220148596}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4259502929453744655}
  - {fileID: 6006744020725125880}
  - {fileID: 1723551211780945116}
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -53}
  m_SizeDelta: {x: 1080, y: 159.0954}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3929010573527079085
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5697308819220148596}
  m_CullTransparentMesh: 0
--- !u!1 &5781218695257686198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7237607460181374245}
  - component: {fileID: 7476404451828914198}
  - component: {fileID: 5325327377187322520}
  m_Layer: 5
  m_Name: MonsterImg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7237607460181374245
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5781218695257686198}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -10.99, y: 152}
  m_SizeDelta: {x: 990.2136, y: 773.6045}
  m_Pivot: {x: 0.5, y: 0}
--- !u!222 &7476404451828914198
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5781218695257686198}
  m_CullTransparentMesh: 0
--- !u!114 &5325327377187322520
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5781218695257686198}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 8fd97edbb3664584c8797fee43c542cb, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &5821629600590140032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5821629600590140035}
  - component: {fileID: 5821629600590140038}
  - component: {fileID: 5821629600590140039}
  - component: {fileID: 5821629600590140036}
  - component: {fileID: 5821629600590140037}
  - component: {fileID: 5821629600590140034}
  m_Layer: 5
  m_Name: ScrollView
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5821629600590140035
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600590140032}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5178655452542993938}
  m_Father: {fileID: 2507267041502592830}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: 0, y: -1.5}
  m_SizeDelta: {x: 915.39, y: -114.91565}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5821629600590140038
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600590140032}
  m_CullTransparentMesh: 0
--- !u!114 &5821629600590140039
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600590140032}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 93998fea94671024680976c492306d00, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Content: {fileID: 3791212599467467922}
  m_Horizontal: 1
  m_Vertical: 0
  m_MovementType: 1
  m_Elasticity: 0.1
  m_Inertia: 1
  m_DecelerationRate: 0.135
  m_ScrollSensitivity: 1
  m_Viewport: {fileID: 5178655452542993938}
  m_HorizontalScrollbar: {fileID: 0}
  m_VerticalScrollbar: {fileID: 0}
  m_HorizontalScrollbarVisibility: 0
  m_VerticalScrollbarVisibility: 0
  m_HorizontalScrollbarSpacing: 0
  m_VerticalScrollbarSpacing: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
  handleDrag: 1
  isDrag: 0
--- !u!114 &5821629600590140036
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600590140032}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e190d043ef387ee4fad4acea6146931e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mItemPrefabDataList:
  - mItemPrefab: {fileID: 5821629600794733550}
    mPadding: 10
    mInitCreateCount: 0
    mStartPosOffset: 0
  mArrangeType: 2
  mSupportScrollBar: 0
  mItemSnapEnable: 0
  mAutoSort: 0
  mViewPortSnapPivot: {x: 0, y: 0}
  mItemSnapPivot: {x: 0, y: 0}
  isFlipXItem: 0
  IsRecycle: 1
  ShowAnim: 0
  parentLoopListView: {fileID: 0}
--- !u!114 &5821629600590140037
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600590140032}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ca6dfd031647fe04989f0a3636c56bcd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_kScrollRect: {fileID: 5821629600590140039}
  m_fClickableTime: 1
  m_bVertical: 0
--- !u!114 &5821629600590140034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600590140032}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3312d7739989d2b4e91e6319e9a96d76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding: {x: 0, y: 0, z: 0, w: 0}
  m_Softness: {x: 0, y: 0}
--- !u!1 &5821629600794733550
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5821629600794733537}
  - component: {fileID: 5821629600794733536}
  - component: {fileID: 640372330020459913}
  - component: {fileID: 4254773090426871297}
  - component: {fileID: 7725937646395587943}
  m_Layer: 5
  m_Name: Item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5821629600794733537
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600794733550}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3980824780020207092}
  - {fileID: 3443585769697888541}
  - {fileID: 3127943366203605455}
  - {fileID: 1463342366988305792}
  m_Father: {fileID: 3791212599467467922}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -353.57, y: 2}
  m_SizeDelta: {x: 187.31757, y: 239}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5821629600794733536
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600794733550}
  m_CullTransparentMesh: 0
--- !u!114 &640372330020459913
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600794733550}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!114 &4254773090426871297
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600794733550}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6457c9ba5ea7ade42970e4fdf48a2f75, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  esv: {fileID: 5821629600590140039}
--- !u!114 &7725937646395587943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5821629600794733550}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1da2a900e79078449848779d2f553a64, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  LongPressBeginTime: 0.3
  LongPressEndTime: 1
  LongPressProgress: 0
  clickCD: 0
  clickCDTime: 1.2
  handled: 1
  SRFilter: {fileID: 0}
  NeedRecoverTimeScale: 0
  scrollRect: {fileID: 0}
  IsResponseDrag: 1
--- !u!1 &6533513135989673324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4259502929453744655}
  - component: {fileID: 5533618052698992317}
  - component: {fileID: 5943715397099752506}
  m_Layer: 5
  m_Name: BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4259502929453744655
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6533513135989673324}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7847714723864483964}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 3.3999996}
  m_SizeDelta: {x: 952.83, y: 126.06769}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5533618052698992317
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6533513135989673324}
  m_CullTransparentMesh: 0
--- !u!114 &5943715397099752506
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6533513135989673324}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.8196079, g: 0.8588236, b: 0.8980393, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: d769c3a33cd42b84a9d518678da2d9bf, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &6553816182154378360
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3980824780020207092}
  - component: {fileID: 3998491163139267750}
  - component: {fileID: 6499362492998673458}
  m_Layer: 5
  m_Name: Quality
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3980824780020207092
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6553816182154378360}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.8, y: 0.8, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5821629600794733537}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 4.400083}
  m_SizeDelta: {x: 226, y: 227}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3998491163139267750
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6553816182154378360}
  m_CullTransparentMesh: 0
--- !u!114 &6499362492998673458
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6553816182154378360}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 87572ad5093dfdf4ca42ce40dc0ef05e, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &6594033248255810416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6910543379960336938}
  - component: {fileID: 1723224095800501605}
  - component: {fileID: 8827701021527146154}
  m_Layer: 5
  m_Name: BackGround
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6910543379960336938
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6594033248255810416}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1723551211780945116}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0.8, y: -0.9}
  m_SizeDelta: {x: 910.02856, y: 45.392944}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1723224095800501605
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6594033248255810416}
  m_CullTransparentMesh: 0
--- !u!114 &8827701021527146154
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6594033248255810416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: deb1080878698b84994568508a529322, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &6729987871444895149
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5178655452542993938}
  - component: {fileID: 4309267020232846000}
  - component: {fileID: 4309267020232846001}
  m_Layer: 5
  m_Name: ViewPort
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5178655452542993938
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6729987871444895149}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3791212599467467922}
  m_Father: {fileID: 5821629600590140035}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: -0.00018311}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4309267020232846000
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6729987871444895149}
  m_CullTransparentMesh: 0
--- !u!114 &4309267020232846001
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6729987871444895149}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.003921569}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &7120232369905095775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1463342366988305792}
  - component: {fileID: 755340607970391534}
  - component: {fileID: 6081892999237161577}
  m_Layer: 5
  m_Name: Time
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &1463342366988305792
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7120232369905095775}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3326204396037075293}
  m_Father: {fileID: 5821629600794733537}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 92.1, y: -54.55}
  m_SizeDelta: {x: 164.49084, y: 46.273067}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &755340607970391534
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7120232369905095775}
  m_CullTransparentMesh: 0
--- !u!114 &6081892999237161577
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7120232369905095775}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 5d00b9fdadf25b44e869fcc1b866e6c9, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1 &7182618899508528635
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4065338954968780116}
  - component: {fileID: 3324164300025361324}
  - component: {fileID: 6113897881276249863}
  - component: {fileID: 7143680575046751718}
  - component: {fileID: 5588004298347180931}
  - component: {fileID: 970934879389598452}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4065338954968780116
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7182618899508528635}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6790851758366003038}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -473.1, y: -6.1}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0.5}
--- !u!222 &3324164300025361324
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7182618899508528635}
  m_CullTransparentMesh: 0
--- !u!114 &6113897881276249863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7182618899508528635}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961, type: 3}
    m_FontSize: 56
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 5
    m_MaxSize: 60
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: "\u5468\u5E74\u5E86\u5B9D\u7BB1"
  dontConvertLineFeed: 0
  m_Key: Anniv_collect_chest_name
  textUpdateEvent:
    m_PersistentCalls:
      m_Calls: []
  isOnlyOnceTextUpdateEvent: 1
  isRefreshTextWhenStart: 0
  IsTranslate: 0
  m_editorText: 
  EditorMode: 0
  UseCharComposing: 0
  isFading: 0
  isUseSpecifiedAlignment: 0
  specifiedAlignment: 0
  isUseSrcText: 0
  isMailText: 0
  isFlipDelayText: 0
  m_IsForceFlip: 1
--- !u!114 &7143680575046751718
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7182618899508528635}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 2
  m_VerticalFit: 2
--- !u!114 &5588004298347180931
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7182618899508528635}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ee2af72b1959b7d43841216a2499281b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 3, y: -3}
  m_UseGraphicAlpha: 1
  m_bShadow: 0
  m_fShadowOffset: 0
  m_TextOffset: {x: 0, y: 0}
  m_TextScale: {x: 1, y: 1}
  m_TextScaleUV: {x: 0.5, y: 0.5}
  isFading: 0
--- !u!114 &970934879389598452
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7182618899508528635}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfabb0440166ab443bba8876756fdfa9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 0, y: -2}
  m_UseGraphicAlpha: 1
--- !u!1 &7659628963013449352
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 965813157956484743}
  - component: {fileID: 4682814773430330212}
  m_Layer: 5
  m_Name: fill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &965813157956484743
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7659628963013449352}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8599581398378644585}
  m_Father: {fileID: 1723551211780945116}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -859.3, y: -0.9}
  m_SizeDelta: {x: 905.2318, y: 6.8404694}
  m_Pivot: {x: -0.000000017695129, y: 0.49999997}
--- !u!114 &4682814773430330212
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7659628963013449352}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cf18302123c942043a2a6034a2ac2aa2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.9607844, g: 0.9607844, b: 0.9607844, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.9607844, g: 0.9607844, b: 0.9607844, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Highlighted
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_FillRect: {fileID: 0}
  m_HandleRect: {fileID: 0}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 0.52
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
  isDrag: 0
  onDrag:
    m_PersistentCalls:
      m_Calls: []
  onPointDown:
    m_PersistentCalls:
      m_Calls: []
  onPointUp:
    m_PersistentCalls:
      m_Calls: []
  onSelect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &7981683896344160381
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: ************7135595}
  - component: {fileID: 8065717488108594374}
  - component: {fileID: 3286219989654691698}
  - component: {fileID: 9033947283038043373}
  - component: {fileID: 8351718504033453999}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &************7135595
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7981683896344160381}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1723551211780945116}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 2}
  m_SizeDelta: {x: 114, y: 48}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8065717488108594374
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7981683896344160381}
  m_CullTransparentMesh: 0
--- !u!114 &3286219989654691698
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7981683896344160381}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961, type: 3}
    m_FontSize: 36
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 3
    m_MaxSize: 45
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 60/1000
  dontConvertLineFeed: 0
  m_Key: 
  textUpdateEvent:
    m_PersistentCalls:
      m_Calls: []
  isOnlyOnceTextUpdateEvent: 1
  isRefreshTextWhenStart: 0
  IsTranslate: 0
  m_editorText: 
  EditorMode: 0
  UseCharComposing: 0
  isFading: 0
  isUseSpecifiedAlignment: 0
  specifiedAlignment: 0
  isUseSrcText: 0
  isMailText: 0
  isFlipDelayText: 0
  m_IsForceFlip: 1
--- !u!114 &9033947283038043373
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7981683896344160381}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 2
  m_VerticalFit: 2
--- !u!114 &8351718504033453999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7981683896344160381}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ee2af72b1959b7d43841216a2499281b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 2, y: -2}
  m_UseGraphicAlpha: 1
  m_bShadow: 1
  m_fShadowOffset: -2
  m_TextOffset: {x: 0, y: 0}
  m_TextScale: {x: 1, y: 1}
  m_TextScaleUV: {x: 0.5, y: 0.5}
  isFading: 0
--- !u!1 &8264499422913968820
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6006744020725125880}
  - component: {fileID: 1969842771165634301}
  - component: {fileID: 151526258592332500}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6006744020725125880
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8264499422913968820}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7847714723864483964}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 68}
  m_SizeDelta: {x: 911.7, y: 59}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1969842771165634301
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8264499422913968820}
  m_CullTransparentMesh: 0
--- !u!114 &151526258592332500
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8264499422913968820}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.27450982, g: 0.33333334, b: 0.5176471, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961, type: 3}
    m_FontSize: 45
    m_FontStyle: 0
    m_BestFit: 1
    m_MinSize: 4
    m_MaxSize: 45
    m_Alignment: 3
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: "\u5269\u4F59\u65F6\u95F4"
  dontConvertLineFeed: 0
  m_Key: chronicle_txt_11
  textUpdateEvent:
    m_PersistentCalls:
      m_Calls: []
  isOnlyOnceTextUpdateEvent: 1
  isRefreshTextWhenStart: 0
  IsTranslate: 0
  m_editorText: 
  EditorMode: 0
  UseCharComposing: 0
  isFading: 0
  isUseSpecifiedAlignment: 0
  specifiedAlignment: 0
  isUseSrcText: 0
  isMailText: 0
  isFlipDelayText: 0
  m_IsForceFlip: 1
--- !u!1 &8976449892479153515
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6790851758366003038}
  m_Layer: 5
  m_Name: TitleBar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6790851758366003038
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8976449892479153515}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4065338954968780116}
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -8, y: 116}
  m_SizeDelta: {x: 1008, y: 115.2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &8988026557584559666
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6168214734144555236}
  - component: {fileID: 3265943613581436553}
  - component: {fileID: 7743070510873594674}
  m_Layer: 5
  m_Name: TopImg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &6168214734144555236
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8988026557584559666}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2818676479525928486}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -20.373, y: -57}
  m_SizeDelta: {x: 952.4458, y: 952.4458}
  m_Pivot: {x: 0.5, y: 0}
--- !u!222 &3265943613581436553
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8988026557584559666}
  m_CullTransparentMesh: 0
--- !u!114 &7743070510873594674
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8988026557584559666}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c81b19283ff80804a88d4f1bed8bd2ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: a29398b184559f8409c956422805d6a5, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
  usePolygonCollider2DRectTouchRect: 0
  m_IsForceFlip: 0
  m_BlendType: 0
  isFading: 0
--- !u!1001 &1926883887386430221
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2818676479525928486}
    m_Modifications:
    - target: {fileID: 910379445433571111, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_Name
      value: Button
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_RootOrder
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 393
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 151
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -384
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5057346115060534478, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_Key
      value: Anniv_collect_map
      objectReference: {fileID: 0}
    - target: {fileID: 5057346115060534478, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_Text
      value: "\u6536\u96C6"
      objectReference: {fileID: 0}
    - target: {fileID: 5057346115060534478, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_FontData.m_Font
      value: 
      objectReference: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961,
        type: 3}
    - target: {fileID: 6917848549026071228, guid: 29cbece0cb069d9499ed66cdc27db211,
        type: 3}
      propertyPath: m_FontData.m_Font
      value: 
      objectReference: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961,
        type: 3}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 29cbece0cb069d9499ed66cdc27db211, type: 3}
--- !u!224 &963950857035273894 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1719546703445679531, guid: 29cbece0cb069d9499ed66cdc27db211,
    type: 3}
  m_PrefabInstance: {fileID: 1926883887386430221}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &6264822645431002297 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5497969009790513076, guid: 29cbece0cb069d9499ed66cdc27db211,
    type: 3}
  m_PrefabInstance: {fileID: 1926883887386430221}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1da2a900e79078449848779d2f553a64, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &6670648881202923459 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5057346115060534478, guid: 29cbece0cb069d9499ed66cdc27db211,
    type: 3}
  m_PrefabInstance: {fileID: 1926883887386430221}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3501d990b4af08747a2fa646f81c04dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &4783414620619400209
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2818676479525928486}
    m_Modifications:
    - target: {fileID: 2131408766180811289, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_IsForceFlip
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3016825448332273144, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_IsForceFlip
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3016825448332273144, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_FontData.m_Font
      value: 
      objectReference: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961,
        type: 3}
    - target: {fileID: 3386243587403412814, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_Color.a
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6824999407590974795, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_FontData.m_Font
      value: 
      objectReference: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961,
        type: 3}
    - target: {fileID: 8943246614534462800, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_FontData.m_Font
      value: 
      objectReference: {fileID: 12800000, guid: 550ac7523daa23648b75f0efa991d961,
        type: 3}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_RootOrder
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 900
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 191
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -5
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -126
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9097909913186930375, guid: 129963f96aa037641a7283acdfd3dce3,
        type: 3}
      propertyPath: m_Name
      value: RewardListUI
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 7044274953926691677, guid: 129963f96aa037641a7283acdfd3dce3, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 129963f96aa037641a7283acdfd3dce3, type: 3}
--- !u!224 &4332566865729481425 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 9097909913186930368, guid: 129963f96aa037641a7283acdfd3dce3,
    type: 3}
  m_PrefabInstance: {fileID: 4783414620619400209}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7753722467098337578 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 3024408768553239867, guid: 129963f96aa037641a7283acdfd3dce3,
    type: 3}
  m_PrefabInstance: {fileID: 4783414620619400209}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33d4d594c3891cd4c97c22fb100d4573, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
