﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class CameraCollisionCheck : CameraAction
    {
        float half_field_of_view;
        float plane_aspect;
        float half_plane_height = 0;
        float half_plane_width = 0;
        Vector3 mTargetPosition;
        float m_LastDistance;
        float m_DistanceVel;
        float m_SkipDistance = 3;
        float mDeltaDistance;
        float mTotalDistance;
        int mCameraColliderLayer;

        public CameraCollisionCheck(CameraActionType type) : base(type)
        {
            ignoreAxis = true;
            mCameraColliderLayer = LayerMask.NameToLayer(MapCoreDef.CAMERA_COLLIDER_LAYER);
        }

        //相机行为结束时调用
        public override void OnFinishImpl()
        {
        }
        //计算过当前帧相机的位置
        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            return mTargetPosition;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            //为了热更的代码
            if (enabled == false)
            {
                m_LastDistance = -100000;
            }
            enabled = true;
            var camera = Map.currentMap.camera.firstCamera;
            float half_field_of_view = (camera.fieldOfView / 2) * Mathf.Deg2Rad;
            float plane_aspect = camera.aspect;
            float half_plane_height = camera.nearClipPlane * Mathf.Tan(half_field_of_view);
            float half_plane_width = half_plane_height * plane_aspect;
            UpdateDesiredPosition(camera, currentCameraPos);
        }

        void UpdateDesiredPosition(Camera camera, Vector3 currentCameraPos)
        {
            float test = camera.nearClipPlane + 2.0f + m_SkipDistance;
            var forward = camera.transform.forward;
            var pos = currentCameraPos - mTotalDistance * forward;
            var zero = pos + new Vector3(0, -pos.y, pos.y);
            var fo = zero + forward * (-500);
            var to = pos + forward * test;
            var dis = 0.0f;
            var distance = Vector3.Distance(pos, fo);
            float closestDistance;

            bool constraint = false;
            if (CheckCameraClipPlane(camera, fo, to, out closestDistance))
            {
                dis = closestDistance - distance - camera.nearClipPlane - m_SkipDistance;
                constraint = true;
            }

            if (dis > 0) dis = 0;

            if (m_LastDistance < -99990)
            {
                m_LastDistance = dis;
            }

            if (!constraint && mTotalDistance < 0)
            {
                enabled = false;
                mTotalDistance = 0;
                m_LastDistance = -100000;
                return;
            }

            if (m_LastDistance < dis || !constraint)
                dis = Mathf.SmoothDamp(m_LastDistance, dis, ref m_DistanceVel, 0.7f);

            mDeltaDistance = dis - m_LastDistance;
            if (mDeltaDistance > 0.01f)
            {
                enabled = false;
                mTotalDistance = 0;
                m_LastDistance = -100000;
                return;
            }

            m_LastDistance = dis;

            mTargetPosition = currentCameraPos + forward * mDeltaDistance;
            mTotalDistance += mDeltaDistance;
        }

        bool CheckCameraClipPlane(Camera camera, Vector3 from, Vector3 to, out float closestDistance)
        {
            closestDistance = float.MaxValue;
            bool casted = false;
            RaycastHit hitInfo;

            ClipPlaneVertexes clipPlane = GetClipPlaneAt(camera, to);
#if UNITY_EDITOR
            //Debug.DrawLine(clipPlane.UpperLeft, clipPlane.UpperRight);
            //Debug.DrawLine(clipPlane.UpperRight, clipPlane.LowerRight);
            //Debug.DrawLine(clipPlane.LowerRight, clipPlane.LowerLeft);
            //Debug.DrawLine(clipPlane.LowerLeft, clipPlane.UpperLeft);

            Debug.DrawLine(from, to, Color.red);
            Debug.DrawLine(from - camera.transform.right * half_plane_width + camera.transform.up * half_plane_height, clipPlane.UpperLeft, Color.cyan);
            Debug.DrawLine(from + camera.transform.right * half_plane_width + camera.transform.up * half_plane_height, clipPlane.UpperRight, Color.cyan);
            Debug.DrawLine(from - camera.transform.right * half_plane_width - camera.transform.up * half_plane_height, clipPlane.LowerLeft, Color.cyan);
            Debug.DrawLine(from + camera.transform.right * half_plane_width - camera.transform.up * half_plane_height, clipPlane.LowerRight, Color.cyan);
#endif
            var start = from - camera.transform.right * half_plane_width + camera.transform.up * half_plane_height;
            var end = clipPlane.UpperLeft;
            if (Physics.Linecast(start, end, out hitInfo) && hitInfo.collider.gameObject.layer == mCameraColliderLayer)
            {
                if (hitInfo.distance < closestDistance)
                {
                    casted = true;
                    closestDistance = Vector3.Distance(hitInfo.point + camera.transform.right * half_plane_width - camera.transform.up * half_plane_height, from);
                }
            }

            start = from + camera.transform.right * half_plane_width + camera.transform.up * half_plane_height;
            end = clipPlane.UpperRight;
            if (Physics.Linecast(start, end, out hitInfo) && hitInfo.collider.gameObject.layer == mCameraColliderLayer)
            {
                if (hitInfo.distance < closestDistance)
                {
                    casted = true;
                    closestDistance = Vector3.Distance(hitInfo.point - camera.transform.right * half_plane_width - camera.transform.up * half_plane_height, from);
                }
            }

            start = from - camera.transform.right * half_plane_width - camera.transform.up * half_plane_height;
            end = clipPlane.LowerLeft;
            if (Physics.Linecast(start, end, out hitInfo) && hitInfo.collider.gameObject.layer == mCameraColliderLayer)
            {
                if (hitInfo.distance < closestDistance)
                {
                    casted = true;
                    closestDistance = Vector3.Distance(hitInfo.point + camera.transform.right * half_plane_width + camera.transform.up * half_plane_height, from);
                }
            }

            start = from + camera.transform.right * half_plane_width - camera.transform.up * half_plane_height;
            end = clipPlane.LowerRight;
            if (Physics.Linecast(start, end, out hitInfo) && hitInfo.collider.gameObject.layer == mCameraColliderLayer)
            {
                if (hitInfo.distance < closestDistance)
                {
                    casted = true;
                    closestDistance = Vector3.Distance(hitInfo.point - camera.transform.right * half_plane_width + camera.transform.up * half_plane_height, from);
                }
            }

            return casted;
        }


        float ClampAngle(float angle, float min, float max)
        {
            while (angle < -360 || angle > 360)
            {
                if (angle < -360)
                    angle += 360;
                if (angle > 360)
                    angle -= 360;
            }

            return Mathf.Clamp(angle, min, max);
        }

        public struct ClipPlaneVertexes
        {
            public Vector3 UpperLeft;
            public Vector3 UpperRight;
            public Vector3 LowerLeft;
            public Vector3 LowerRight;
        }

        public ClipPlaneVertexes GetClipPlaneAt(Camera camera, Vector3 pos)
        {
            var clipPlane = new ClipPlaneVertexes();

            float offset = camera.nearClipPlane;

            clipPlane.UpperLeft = pos - camera.transform.right * half_plane_width;
            clipPlane.UpperLeft += camera.transform.up * half_plane_height;
            clipPlane.UpperLeft += camera.transform.forward * offset;

            clipPlane.UpperRight = pos + camera.transform.right * half_plane_width;
            clipPlane.UpperRight += camera.transform.up * half_plane_height;
            clipPlane.UpperRight += camera.transform.forward * offset;

            clipPlane.LowerLeft = pos - camera.transform.right * half_plane_width;
            clipPlane.LowerLeft -= camera.transform.up * half_plane_height;
            clipPlane.LowerLeft += camera.transform.forward * offset;

            clipPlane.LowerRight = pos + camera.transform.right * half_plane_width;
            clipPlane.LowerRight -= camera.transform.up * half_plane_height;
            clipPlane.LowerRight += camera.transform.forward * offset;

            return clipPlane;
        }
    }
}
