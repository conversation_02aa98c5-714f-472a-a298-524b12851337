﻿ 



 
 



//created by wzw at 2019/12/5

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class CameraColliderCreatorDialog : EditorWindow
    {
        private void OnEnable()
        {
            mHeight = 60.0f;
        }

        public void Show(System.Func<float, bool> OnClickOK)
        {
            mOnClickOK = OnClickOK;
        }

        void OnGUI()
        {
            EditorGUILayout.BeginVertical();
            mHeight = EditorGUILayout.FloatField("Collider Height", mHeight);
            EditorGUILayout.EndVertical();

            if (GUILayout.Button("OK"))
            {
                if (mOnClickOK != null)
                {
                    if (mHeight > 0)
                    {
                        if (mOnClickOK(mHeight))
                        {
                            Close();
                        }
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Invalid parameter!", "OK");
                    }
                }
                else
                {
                    Close();
                }
            }
        }

        float mHeight;
        System.Func<float, bool> mOnClickOK;
    }
}

#endif