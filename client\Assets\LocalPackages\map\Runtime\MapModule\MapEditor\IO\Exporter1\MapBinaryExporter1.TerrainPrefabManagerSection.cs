﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveTerrainPrefabManager(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.TerrainPrefab<PERSON><PERSON><PERSON>, writer);

            writer.Write(VersionSetting.TerrainPrefabManagerStructVersion);

            //-------------------version 1 start------------------------------
            var map = Map.currentMap;
            var prefabManager = map.data.terrainPrefabManager;

            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                int nPrefabs = group.prefabCount;
                writer.Write(nPrefabs);
                for (int k = 0; k < nPrefabs; ++k)
                {
                    var prefabPath = group.GetPrefabPath(k, 0);
                    Utils.WriteString(writer, prefabPath);
                }
            }
            //-------------------version 1 end------------------------------
            //-------------------version 2 start------------------------------
            //save sub group prefabs
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                var groupPrefabs = group.terrainPrefabs;
                int nPrefabs = group.prefabCount;
                writer.Write(nPrefabs);
                for (int k = 0; k < nPrefabs; ++k)
                {
                    int subgroupPrefabCount = 0;
                    if (groupPrefabs[k] != null)
                    {
                        var subgroupPrefabPaths = groupPrefabs[k].prefabPaths;
                        subgroupPrefabCount = subgroupPrefabPaths.Length;
                    }

                    writer.Write(subgroupPrefabCount);

                    if (groupPrefabs[k] != null)
                    {
                        var subgroupPrefabPaths = groupPrefabs[k].prefabPaths;
                        for (int x = 0; x < subgroupPrefabCount; ++x)
                        {
                            Utils.WriteString(writer, subgroupPrefabPaths[x]);
                        }
                    }
                }
            }
            //-------------------version 2 end------------------------------
            //-------------------version 3 start------------------------------
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                writer.Write(group.groupID);
            }
            //-------------------version 3 end------------------------------
        }
    }
}

#endif