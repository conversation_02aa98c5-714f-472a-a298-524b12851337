﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2020.2.17
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum RegionOperation
    {
        CreateRegion,
        EditRegionOutline,
        MoveRegion,
    }

    [ExecuteInEditMode]
    [Black]
    public class RegionLayerLogic : MapLayerLogic
    {
        public RegionOperation operation = RegionOperation.CreateRegion;
        public bool excludeObstacles = false;

        public RegionLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as RegionLayer;
                return layer;
            }
        }

        protected override void OnUpdate()
        {
            if (mRemovedRegionsGameObjectID.Count > 0)
            {
                var regionLayer = this.layer;
                mExistedObjectIDs.Clear();
                mAllObjects.Clear();
                regionLayer.GetAllObjects(mAllObjects);

                int n = transform.childCount;
                for (int k = n - 1; k >= 0; --k)
                {
                    var child = transform.GetChild(k).gameObject;

                    var objectID = regionLayer.GetObjectIDByGameObjectID(child.GetInstanceID());
                    if (objectID != 0)
                    {
                        mExistedObjectIDs.Add(objectID);
                    }
                }

                //find difference
                for (int j = 0; j < mAllObjects.Count; ++j)
                {
                    bool found = false;
                    for (int i = 0; i < mExistedObjectIDs.Count; ++i)
                    {
                        if (mAllObjects[j].GetEntityID() == mExistedObjectIDs[i])
                        {
                            found = true;
                            break;
                        }
                    }
                    if (!found)
                    {
                        regionLayer.RemoveObject(mAllObjects[j].GetEntityID());
                    }
                }

                mRemovedRegionsGameObjectID.Clear();
            }
        }

        public void OnRegionModelRemoved(int gameObjectID)
        {
            mRemovedRegionsGameObjectID.Add(gameObjectID);
        }

        List<int> mRemovedRegionsGameObjectID = new List<int>();
        List<int> mExistedObjectIDs = new List<int>();
        List<IMapObjectData> mAllObjects = new List<IMapObjectData>();
    }
}


#endif