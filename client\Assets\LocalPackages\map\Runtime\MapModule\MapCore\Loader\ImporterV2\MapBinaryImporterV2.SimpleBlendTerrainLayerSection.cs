﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadSimpleBlendTerrainLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.SimpleBlendTerrainLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);
            bool useGeneratedLOD = reader.ReadBoolean();

            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            int n = rows * cols;
            ushort[] tiles = new ushort[n];
            for (int i = 0; i < n; ++i)
            {
                tiles[i] = reader.ReadUInt16();
            }
            var prefabPaths = Utils.ReadStringArray(reader);

            var config = LoadSimpleBlendTerrainLayerLODConfig(reader);
            //-------------------version 1 end-----------------------
            //-------------------version 2 start-----------------------
            if (version >= 2)
            {
                LoadSimpleBlendTerrainLayerLODConfigV2(reader, config);
            }
            //-------------------version 2 end-----------------------
            //-------------------version 3 start-----------------------
            Vector3 layerPosition = Vector3.zero;
            if (version >= 3)
            {
                layerPosition = Utils.ReadVector3(reader);
            }
            //-------------------version 3 end-----------------------

            var layer = new config.SimpleBlendTerrainLayerData(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, tiles, prefabPaths, useGeneratedLOD, layerPosition);
            return layer;
        }

        void LoadSimpleBlendTerrainLayerLODConfigV2(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].terrainLODTileCount = reader.ReadInt32();
            }
        }

        config.MapLayerLODConfig LoadSimpleBlendTerrainLayerLODConfig(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                var flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, flag, 0);
            }
            return config;
        }
    }
}
