%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5922963039411106252
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2235206285472817126}
  - component: {fileID: 7177544128348687477}
  m_Layer: 0
  m_Name: Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2235206285472817126
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5922963039411106252}
  m_LocalRotation: {x: 0, y: 0.38268343, z: 0, w: 0.92387956}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2212972000016204056}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 45, z: 0}
--- !u!65 &7177544128348687477
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5922963039411106252}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 5, y: 0, z: 5}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &9210702554707220176
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2212972000016204056}
  m_Layer: 0
  m_Name: GoldOre_LV3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2212972000016204056
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9210702554707220176}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7014156066998082490}
  - {fileID: 2235206285472817126}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &9072027061825470537
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2212972000016204056}
    m_Modifications:
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2176172262606891616, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_Size.y
      value: 2.14
      objectReference: {fileID: 0}
    - target: {fileID: 2176172262606891616, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: 21300000, guid: 1c549d7c5e145904a8ea5d6e595d9977,
        type: 3}
    - target: {fileID: 2176172262606891616, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_WasSpriteAssigned
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3523538079940994779, guid: 456c41236c6240646ac56eb722a9c7a6,
        type: 3}
      propertyPath: m_Name
      value: mainBuilding_Lv3
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 456c41236c6240646ac56eb722a9c7a6, type: 3}
--- !u!4 &7014156066998082490 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2067461517212965875, guid: 456c41236c6240646ac56eb722a9c7a6,
    type: 3}
  m_PrefabInstance: {fileID: 9072027061825470537}
  m_PrefabAsset: {fileID: 0}
