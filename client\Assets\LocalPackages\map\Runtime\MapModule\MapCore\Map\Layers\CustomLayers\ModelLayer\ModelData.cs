﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public class ModelData : MapObjectData
    {
        public ModelData(int id, Map map, int flag, Vector3 position, Quaternion rotation, Vector3 scale, ModelTemplate modelTemplate, bool calculateBounds) : base(id, map, flag)
        {
            mModelTemplate = modelTemplate;
            if (calculateBounds && mModelTemplate != null)
            {
                mBounds = Utils.TransformRect(modelTemplate.bounds, position, scale, rotation);
            }

            mPosition = position;
            mRotation = rotation;
            mScale = scale;
        }

        public override void SetPosition(Vector3 pos)
        {
            mPosition = pos;
        }
        public override Vector3 GetPosition()
        {
            return mPosition;
        }
        public override void SetRotation(Quaternion rot)
        {
            mRotation = rot;
        }
        public override Quaternion GetRotation()
        {
            return mRotation;
        }
        public override void SetScale(Vector3 scale)
        {
            mScale = scale;
        }
        public override Vector3 GetScale()
        {
            return mScale;
        }

        public override bool IsObjActive()
        {
            return mIsActive;
        }
        public override bool SetObjActive(bool active)
        {
            if (mIsActive != active)
            {
                mIsActive = active;
                return true;
            }
            return false;
        }

        public override ModelTemplate GetModelTemplate() { return mModelTemplate; }
        public override int GetModelTemplateID() { return mModelTemplate == null ? 0 : mModelTemplate.id; }
        public void SetModelTemplate(ModelTemplate modelTemplate) { mModelTemplate = modelTemplate; }
        public override string GetAssetPath(int lod)
        {
            if (mModelTemplate != null)
            {
                return mModelTemplate.GetLODPrefabPath(lod);
            }
            return "";
        }

        public override Rect GetBounds()
        {
            return mBounds;
        }

        public void CalculateBounds()
        {
            if (mModelTemplate != null)
            {
                mBounds = Utils.TransformRect(mModelTemplate.bounds, mPosition, mScale, mRotation);
            }
        }

        //用于快速判断一个物体是否可见的变量
        public int visibilityValue { set; get; }
        public bool isHidden { get { return mIsHidden; } set { mIsHidden = value; } }

        protected ModelTemplate mModelTemplate;
        protected Rect mBounds;

        Vector3 mPosition;
        Vector3 mScale;
        Quaternion mRotation;

        bool mIsActive = false;
        bool mIsHidden = false;
    }
}
