﻿ 



 
 


#if UNITY_EDITOR

namespace TFW.Map
{
    public partial class EntitySpawnLayerView : MapLayerView
    {
        public EntitySpawnLayerView(MapLayerData layerData) : base(layerData, false) {
            CreateTexturePlane(layerData.GetLayerWidthInMeter(), layerData.GetLayerHeightInMeter());
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            DestroyTexturePlane();
        }

        public override void SetZoom(float zoom, bool lodChanged) { }

        public override void ReloadVisibleViews()
        {
        }
    }
}

#endif