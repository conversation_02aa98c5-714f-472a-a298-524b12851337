﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //改变相机的高度
    public class CameraHeightChange : ZoomActionBase
    {
        public CameraHeightChange(CameraActionType order) : base(order)
        {
            ignoreAxis = true;
            Init();
        }

        public void StartMoving(Vector3 center, float startDistance, Vector2 cameraHeightRange, Vector2 xzDistanceAndHeightRatioRange, Vector2 cameraRisingSpeedRange)
        {
            enabled = true;
            var cameraSetting = MapCameraMgr.cameraSetting;
            mTargetCenter = center;
            mCameraHeightRange = cameraHeightRange;
            mStartDistance = startDistance;
            mXZDistanceAndHeightRatioRange = xzDistanceAndHeightRatioRange;
            mTargetHeight = mCurrentCameraPos.y;
            mRisingSpeedRange = cameraRisingSpeedRange;
        }

        public void SetTargetHeight(float height)
        {
            mTargetHeight = height;
        }

        public void StopMoving()
        {
            isFinished = true;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mCurrentCameraPos = currentCameraPos;
            if (enabled)
            {
                Vector3 targetPos = mCurrentCameraPos;
                var newViewCenter = Map.currentMap.GetViewCenterFromCamera(MapCameraMgr.MapCamera.transform.forward, mCurrentCameraPos);
                var oldViewCenter = Map.currentMap.viewCenter;
                var curDir = newViewCenter - mTargetCenter;
                curDir.y = 0;
                var lastDir = oldViewCenter - mTargetCenter;
                lastDir.y = 0;
                float curDis = curDir.sqrMagnitude;
                float lastDis = lastDir.sqrMagnitude;

                float deltaDis = curDis - lastDis;
                if (curDis >= mStartDistance * mStartDistance && deltaDis >= Mathf.Epsilon && currentCameraPos.y < mCameraHeightRange.y)
                {
                    //move away from target
                    var realDelta = Mathf.Sqrt(curDis) - Mathf.Sqrt(lastDis);

                    float t = Mathf.Clamp01((mCameraHeightRange.y - mCurrentCameraPos.y) / (mCameraHeightRange.y - mCameraHeightRange.x));
                    float ratio = Mathf.Lerp(mXZDistanceAndHeightRatioRange.x, mXZDistanceAndHeightRatioRange.y, t);

                    float heightChange = realDelta * ratio;
                    mTargetHeight += heightChange;
                }
            }
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            var targetPos = mCurrentCameraPos;

            if (mCurrentCameraPos.y < mCameraHeightRange.y)
            {
                float t = Mathf.Clamp01((mCameraHeightRange.y - mCurrentCameraPos.y) / (mCameraHeightRange.y - mCameraHeightRange.x));
                float risingSpeed = Mathf.Lerp(mRisingSpeedRange.x, mRisingSpeedRange.y, t);

                var height = Mathf.MoveTowards(mCurrentCameraPos.y, mTargetHeight, risingSpeed * Time.deltaTime);

                if (height >= mCameraHeightRange.y)
                {
                    isFinished = true;
                    height = mCameraHeightRange.y;
                }

                var map = Map.currentMap;
                var viewCenter = map.GetViewCenterFromCamera(map.camera.transform.forward, mCurrentCameraPos);
                targetPos = map.CalculateCameraPositionFromLookAtPosition(viewCenter.x, viewCenter.z, height);

                MapCameraMgr.currentMinimumHeight = height;
            }

            return targetPos;
        }

        public override void OnFinishImpl()
        {
        }

        Vector3 mTargetCenter;
        Vector3 mCurrentCameraPos;
        //相机最低点到想要到达的高度
        Vector2 mCameraHeightRange;
        float mStartDistance;
        float mTargetHeight;
        //相机上升的最大速度,真正的上升速度根据相机高度来调整
        Vector2 mRisingSpeedRange;
        //每水平距离移动多少米就垂直距离移动多少米
        Vector2 mXZDistanceAndHeightRatioRange;
    }
}
