﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    [Black]
    //创建地图层的bounds GameObject
    public abstract class MapLayerBoundsCreator {
        public void SetMapLayer(MapLayerData layer) {
            mLayer = layer;
        }
        public abstract GameObject CreateLayerBounds(Color color);
        protected MapLayerData mLayer;
    };
}
#endif