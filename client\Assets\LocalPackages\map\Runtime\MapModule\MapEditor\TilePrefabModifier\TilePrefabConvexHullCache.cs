﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //缓存tile prefab的children prefab的convex hull
    [Black]
    public static class TilePrefabConvexHullCache
    {
        class PrefabCache
        {
            public Vector3 position;
            public Vector3 scaling;
            public Quaternion rotation;
            public List<Vector3> convexHull;
        }

        public static List<Vector3> GetConvexHullFromCache(string prefabPath, Vector3 pos, Vector3 scale, Quaternion rot)
        {
            List<PrefabCache> caches;
            mPrefabCache.TryGetValue(prefabPath, out caches);
            if (caches != null)
            {
                for (int i = 0; i < caches.Count; ++i)
                {
                    if (caches[i].position == pos && caches[i].rotation == rot && caches[i].scaling == scale)
                    {
                        return caches[i].convexHull;
                    }
                }
            }
            return null;
        }

        public static void AddConvexHullToCache(string prefabPath, Vector3 pos, Vector3 scale, Quaternion rot, List<Vector3> convexHull)
        {
            List<PrefabCache> caches;
            mPrefabCache.TryGetValue(prefabPath, out caches);
            if (caches == null)
            {
                caches = new List<PrefabCache>();
                mPrefabCache[prefabPath] = caches;
            }
            var cache = new PrefabCache();
            cache.position = pos;
            cache.rotation = rot;
            cache.scaling = scale;
            cache.convexHull = convexHull;
            caches.Add(cache);
        }

        public static void Clear()
        {
            mPrefabCache = new Dictionary<string, List<PrefabCache>>();
        }

        static Dictionary<string, List<PrefabCache>> mPrefabCache = new Dictionary<string, List<PrefabCache>>();
    }
}


#endif