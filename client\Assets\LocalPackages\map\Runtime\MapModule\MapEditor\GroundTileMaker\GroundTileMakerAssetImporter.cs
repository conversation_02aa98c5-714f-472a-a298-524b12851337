﻿ 



 
 


#if UNITY_EDITOR

using System.IO;
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class GroundTileMakerAssetImporter : AssetPostprocessor
    {
        static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets, string[] movedFromAssetPaths)
        {
            var instance = Object.FindObjectOfType<GroundTileMaker>();
            if (instance != null)
            {
                for (int i = 0; i < importedAssets.Length; ++i)
                {
                    string ext = Utils.GetExtension(importedAssets[i]);
                    if (string.Compare(ext, "tga", true) == 0)
                    {
                        string textureName = Utils.GetPathName(importedAssets[i], false);
                        instance.ReloadEditorTextureData(textureName);
                    }
                }
            }
        }

        private void OnPreprocessAsset()
        {
            var instance = Object.FindObjectOfType<GroundTileMaker>();
            if (instance == null)
            {
                return;
            }

            bool isRuntimeAsset = assetPath.StartsWith(instance.runtimeAssetOutputFolder);
            bool isEditorAsset = assetPath.StartsWith(instance.editorAssetOutputFolder);
            if (!isRuntimeAsset && !isEditorAsset)
            {
                return;
            }

            var textureImporter = assetImporter as TextureImporter;
            if (textureImporter != null)
            {
                textureImporter.wrapMode = UnityEngine.TextureWrapMode.Clamp;
                textureImporter.mipmapEnabled = false;
                if (isEditorAsset)
                {
                    textureImporter.isReadable = true;
                    textureImporter.textureCompression = TextureImporterCompression.Uncompressed;
                }
            }
        }
    }
}
#endif