﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class RegionLayer
    {
        //计算一个区域内刨除障碍物后的剩余区域
        public void CreateBorderLineMesh()
        {
            if (layerData.generateBorderLine)
            {
                var objects = layerData.objects;
                foreach (var p in objects)
                {
                    var region = p.Value as RegionData;
                    if (region.type == RegionType.Inner)
                    {
                        RegionData outerRegion = null;
                        bool valid = FindOuterRegion(region, out outerRegion);
                        if (valid)
                        {
                            var mesh = CreateLineMesh(outerRegion.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle), layerData.borderLineWidth);
                            SetBorderLineMesh(region.id, mesh);
                            SetBorderLineMeshVisible(region.id, layerData.showBorderLineMesh);
                        }
                    }
                }
            }
        }

        public void ClearBorderLineMesh()
        {
            var regions = layerData.objects;
            foreach (var p in regions)
            {
                var region = p.Value as RegionData;
                SetBorderLineMesh(region.id, null);
            }
        }

        Mesh CreateLineMesh(List<Vector3> outline, float borderSize)
        {
            Mesh mesh = new Mesh();
            List<Vector3> meshVertices = new List<Vector3>();
            List<int> meshIndices = new List<int>();
            int n = outline.Count;
            float length = 0;

            Vector3 sideVertexPos0 = Vector3.zero;

            for (int i = 0; i < n; ++i)
            {
                Vector3 cur = outline[i];
                Vector3 prev = outline[Utils.Mod(i - 1, n)];
                Vector3 next = outline[Utils.Mod(i + 1, n)];
                Vector3 curToPrev = (cur - prev).normalized;
                Vector3 nextToCur = (next - cur).normalized;
                Vector3 offsetDir = ((nextToCur + curToPrev) * 0.5f).normalized;
                Vector3 perpDir = new Vector3(offsetDir.z, 0, -offsetDir.x);
                Vector3 sideVertex = cur + perpDir * borderSize;
                meshVertices.Add(cur);
                meshVertices.Add(sideVertex);

                if (i == 0)
                {
                    sideVertexPos0 = sideVertex;
                }

                length += (next - cur).magnitude;

                if (i == n - 1)
                {
                    //由于uv连续的关系,需要在最后追加一组复制的顶点
                    meshVertices.Add(outline[0]);
                    meshVertices.Add(sideVertexPos0);
                }

                int v0 = (i * 2);
                int v1 = (i * 2 + 1);
                int v2 = (i * 2 + 2);
                int v3 = (i * 2 + 3);
                meshIndices.Add(v0);
                meshIndices.Add(v2);
                meshIndices.Add(v1);
                meshIndices.Add(v2);
                meshIndices.Add(v3);
                meshIndices.Add(v1);
            }

            mesh.SetVertices(meshVertices);
            mesh.SetIndices(meshIndices, MeshTopology.Triangles, 0);

            return mesh;
        }
    }
}

#endif