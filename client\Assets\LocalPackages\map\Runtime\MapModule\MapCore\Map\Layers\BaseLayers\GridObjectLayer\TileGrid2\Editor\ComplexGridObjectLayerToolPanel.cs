﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public partial class ComplexGridObjectLayerToolPanel : EditorWindow
    {
        public void Show(ComplexGridModelLayerLogic logic)
        {
            mLogic = logic;

            if (!mLogic.layerData.objectPlacementSetting.useMapLargeTile)
            {
                ShowUtility();
            }
        }

        void OnDisable()
        {
        }

        void OnGUI()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                if (mLogic.operationType == ComplexGridModelOperationType.kCreateObject)
                {
                    if (!mLogic.layerData.objectPlacementSetting.useMapLargeTile)
                    {
                        mLogic.panelOperation = (ComplexGridModelLayerLogic.PanelOperation)EditorGUILayout.EnumPopup(mLogic.panelOperation);

                        if (mLogic.panelOperation == ComplexGridModelLayerLogic.PanelOperation.PlaceOneObject)
                        {
                            DrawPlaceOneObjectGUI();
                        }
                        else if (mLogic.panelOperation == ComplexGridModelLayerLogic.PanelOperation.PlaceMultipleObjects)
                        {
                            DrawPlaceMultipleObjectsGUI();
                        }
                        else
                        {
                            Debug.Assert(false, $"unknown operation {mLogic.panelOperation}");
                        }
                    }
                }
            }
        }

        public void DrawScene(Vector3 worldPos)
        {
            if (mLogic.panelOperation == ComplexGridModelLayerLogic.PanelOperation.PlaceMultipleObjects)
            {
                DrawPlaceMultipleObjectsScene(worldPos);
            }
        }

        public void AddObject(int lod, Vector2 screenPos)
        {
            Vector3 worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);
            if (mLogic.layerData.objectPlacementSetting.useMapLargeTile)
            {
                PlaceMapLargeTile(worldPos);
            }
            else
            {
                if (mLogic.panelOperation == ComplexGridModelLayerLogic.PanelOperation.PlaceOneObject)
                {
                    PlaceOneObject(mLogic.selectedLOD, worldPos);
                }
                else if (mLogic.panelOperation == ComplexGridModelLayerLogic.PanelOperation.PlaceMultipleObjects)
                {
                    PlaceMultipleObjects(mLogic.selectedLOD, worldPos);
                }
                else
                {
                    Debug.Assert(false, "todo");
                }
            }
        }

        ComplexGridModelLayerLogic mLogic;
    }
}
#endif
