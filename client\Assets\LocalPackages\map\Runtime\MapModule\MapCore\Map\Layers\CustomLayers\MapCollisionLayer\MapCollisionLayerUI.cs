﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace TFW.Map
{
    [CustomEditor(typeof(MapCollisionLayerLogic))]
    public partial class MapCollisionLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as MapCollisionLayerLogic;

            mLogic.UpdateGizmoVisibilityState();

            RefreshCollisionCheck();

            mLogic.layer.ShowOutline(mLogic.layer.displayType);

            MapCollisionLayerEventHandlers handlers = new MapCollisionLayerEventHandlers();
            handlers.onDeleteCollision = OnDeleteCollision;
            handlers.onAddCollision = OnAddCollision;
            handlers.onMoveCollision = OnMoveCollision;
            handlers.onMoveVertex = OnMoveVertex;
            handlers.onInsertVertex = OnInsertVertex;
            handlers.onRemoveVertex = OnRemoveVertex;
            handlers.onOutlineChanged = OnOutlineChanged;
            mLogic.layer.SetEventHandlers(handlers);

            mLogic.layer.SetColorDirty();
            mLogic.layer.UpdateColor(mLogic.layer.displayType);
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);

                SetSelection(null, 0);

                var layer = mLogic.layer;
                layer?.SetEventHandlers(null);
            }
        }

        List<Vector3> CreateCircleVertices(Vector3 center, float radius, int segment)
        {
            List<Vector3> vertices = new List<Vector3>();
            float deltaAngle = 360.0f / segment;
            for (int i = 0; i < segment; ++i)
            {
                float z = Mathf.Cos(deltaAngle * i * Mathf.Deg2Rad) * radius;
                float x = Mathf.Sin(deltaAngle * i * Mathf.Deg2Rad) * radius;
                float y = 0;
                vertices.Add(new Vector3(x, y, z) + center);
            }

            return vertices;
        }

        List<Vector3> CreateRectangleVertices(Vector3 center, float width, float height)
        {
            float minX = center.x - width * 0.5f;
            float maxX = center.x + width * 0.5f;
            float minZ = center.z - height * 0.5f;
            float maxZ = center.z + height * 0.5f;
            List<Vector3> vertices = new List<Vector3>()
            {
                new Vector3(minX, 0, minZ),
                new Vector3(minX, 0, maxZ),
                new Vector3(maxX, 0, maxZ),
                new Vector3(maxX, 0, minZ),
            };

            return vertices;
        }

        void AddCircleCollision(object pos)
        {
            System.Func<List<InputDialog.Item>, bool> onClickAdd = (List<InputDialog.Item> texts) =>
            {
                string xStr = (texts[0] as InputDialog.StringItem).text;
                string zStr = (texts[1] as InputDialog.StringItem).text;
                string radiusStr = (texts[2] as InputDialog.StringItem).text;
                string segmentStr = (texts[3] as InputDialog.StringItem).text;
                float radius;
                bool suc = Utils.ParseFloat(radiusStr, out radius);
                if (!suc)
                {
                    return false;
                }
                float segment;
                suc = Utils.ParseFloat(segmentStr, out segment);
                if (!suc)
                {
                    return false;
                }
                if (radius <= 0 || segment <= 2)
                {
                    return false;
                }

                float x;
                suc = Utils.ParseFloat(xStr, out x);
                if (!suc)
                {
                    return false;
                }
                float z;
                suc = Utils.ParseFloat(zStr, out z);
                if (!suc)
                {
                    return false;
                }

                var vertices = CreateCircleVertices(new Vector3(x, 0, z), radius, (int)segment);

                var action = new ActionAddMapCollision(mLogic.layerID, Map.currentMap.nextCustomObjectID, vertices, true, CollisionAttribute.IsObstacle, 0, 0);
                ActionManager.instance.PushAction(action);
                return true;
            };

            var window = EditorWindow.GetWindow<InputDialog>("Input Circle Parameters");
            window.minSize = new Vector2(200, 100);
            window.maxSize = new Vector2(300, 100);
            var position = window.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            window.position = position;

            Vector3 pos3 = (Vector3)pos;
            var items = new List<InputDialog.Item> {
                new InputDialog.StringItem("X", "", pos3.x.ToString()),
                new InputDialog.StringItem("Z", "", pos3.z.ToString()),
                new InputDialog.StringItem("Radius", "", "10"),
                new InputDialog.StringItem("Segment", "", "20"),
                };
            window.Show(items, onClickAdd);
        }

        //增加一个正方形与圆的差的多边形
        void AddInverseCircleCollision()
        {
            System.Func<List<InputDialog.Item>, bool> onClickAdd = (List<InputDialog.Item> texts) =>
            {
                string radiusStr = (texts[0] as InputDialog.StringItem).text;
                string segmentStr = (texts[1] as InputDialog.StringItem).text;
                float radius;
                bool suc = Utils.ParseFloat(radiusStr, out radius);
                if (!suc)
                {
                    return false;
                }
                float segment;
                suc = Utils.ParseFloat(segmentStr, out segment);
                if (!suc)
                {
                    return false;
                }
                if (radius <= 0 || segment <= 2)
                {
                    return false;
                }

                EditorUtils.AddInverseCircleCollsion(mLogic.layer, segment, radius, mLogic.layer.displayVertexRadius);

                return true;
            };

            var window = EditorWindow.GetWindow<InputDialog>("Input Circle Parameters");
            var items = new List<InputDialog.Item> {
                new InputDialog.StringItem("Radius", "", (mLogic.layer.GetTotalWidth() * 0.5f).ToString()),
                new InputDialog.StringItem("Segment", "", "20"),
                };
            window.Show(items, onClickAdd);
            window.minSize = new Vector2(200, 100);
            window.maxSize = new Vector2(300, 100);
            var position = window.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            window.position = position;
        }

        void ShowRegionID(object pos)
        {
            Vector3 pos3 = (Vector3)pos;
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH) as NavMeshLayer;
            if (layer != null)
            {
                int regionID = layer.GetNavMeshRegionID(pos3);
                EditorUtility.DisplayDialog("Show Region ID", $"Region ID is {regionID}", "OK");
            }
        }

        void ShowRegionState(object pos)
        {
            Vector3 pos3 = (Vector3)pos;
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH) as NavMeshLayer;
            if (layer != null)
            {
                bool state = layer.GetNavMeshRegionState(pos3);
                string v = state ? "Enabled" : "Disabled";
                EditorUtility.DisplayDialog("Show Region State", $"Region is {v}", "OK");
            }
        }

        void AddRectangleCollision(object pos)
        {
            System.Func<List<InputDialog.Item>, bool> onClickAdd = (List<InputDialog.Item> texts) =>
            {
                string xStr = (texts[0] as InputDialog.StringItem).text;
                string zStr = (texts[1] as InputDialog.StringItem).text;
                string widthStr = (texts[2] as InputDialog.StringItem).text;
                string heightStr = (texts[3] as InputDialog.StringItem).text;
                float width;
                bool suc = Utils.ParseFloat(widthStr, out width);
                if (!suc)
                {
                    return false;
                }
                float height;
                suc = Utils.ParseFloat(heightStr, out height);
                if (!suc)
                {
                    return false;
                }
                if (width <= 0 || height <= 0)
                {
                    return false;
                }

                float x;
                suc = Utils.ParseFloat(xStr, out x);
                if (!suc)
                {
                    return false;
                }
                float z;
                suc = Utils.ParseFloat(zStr, out z);
                if (!suc)
                {
                    return false;
                }

                var vertices = CreateRectangleVertices(new Vector3(x, 0, z), width, height);
                var action = new ActionAddMapCollision(mLogic.layerID, Map.currentMap.nextCustomObjectID, vertices, true, CollisionAttribute.IsObstacle, 0, 0);
                ActionManager.instance.PushAction(action);

                return true;
            };

            Vector3 pos3 = (Vector3)pos;
            var window = EditorWindow.GetWindow<InputDialog>("Input Rectangle Parameters");
            var items = new List<InputDialog.Item> {
                new InputDialog.StringItem("X", "", pos3.x.ToString()),
                new InputDialog.StringItem("Z", "", pos3.z.ToString()),
                new InputDialog.StringItem("Width", "", "45"),
                new InputDialog.StringItem("Height", "", "45"),
                };
            window.Show(items, onClickAdd);
            window.minSize = new Vector2(200, 100);
            window.maxSize = new Vector2(300, 100);
            var position = window.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            window.position = position;
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                if (MapModule.showDemoFunction)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button("Save Objects"))
                    {
                        var dataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.MAP_COLLISION_LAYER_EDITOR_TEMP_DATA_FILE_NAME;
                        mLogic.layer.SaveObjectsInRange(dataPath, 0, 0, 360, 360);
                    }

                    if (GUILayout.Button("Load Objects"))
                    {
                        var dataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.MAP_COLLISION_LAYER_EDITOR_TEMP_DATA_FILE_NAME;
                        mLogic.layer.LoadObjectsInRange(dataPath);
                    }
                    EditorGUILayout.EndHorizontal();
                }

                var layerData = mLogic.layer.GetLayerData();

                var outlineType = (PrefabOutlineType)EditorGUILayout.EnumPopup(new GUIContent("Outline Type", "边框类型,每个collision对象有两种边框,NavMesh Obstacle用来生成导航网格寻路,Object Placement Obstacle用来生成地图中的障碍物数据,障碍物用来判断是否能放置游戏对象等,两者独立"), mLogic.layer.displayType);
                if (outlineType != mLogic.layer.displayType)
                {
                    SetOutlineType(outlineType);
                }

#if false
                if (GUILayout.Button("Remove Self Intersection"))
                {
                    if (mSelectedCollisionDataID != 0)
                    {
                        var collisionData = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                        var vertices = collisionData.GetOutlineVerticesCopy(PrefabOutlineType.NavMeshObstacle);
                        var result = RemoveSelfIntersection(vertices);
                        EditorUtils.CreateDrawPolygon("remove self intersection", result, 3);
                    }
                }
#endif

                var operation = (MapCollisionOperation)EditorGUILayout.EnumPopup(new GUIContent("Operation", "当前所选操作"), mLogic.operation);
                if (operation == MapCollisionOperation.EditCollisionVertex)
                {
                    EditorGUILayout.BeginHorizontal("GroupBox");
                    mLogic.snapDistance = EditorGUILayout.FloatField(new GUIContent("Snap Distance", "吸附距离设置,在移动顶点的时候按住shift可以把所选顶点吸附到附近的顶点"), mLogic.snapDistance);
                    EditorGUILayout.EndHorizontal();
                }
                if (operation != mLogic.operation)
                {
                    mLogic.operation = operation;
                    mAddedVertices.Clear();
                    mDisplayedVertices = new Vector3[0];
                    mStartVertexIndex = -1;
                    mStartVertexObjectID = 0;
                }

                if (mSelectedCollisionDataID != 0)
                {
                    var collisionData = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                    if (collisionData != null)
                    {
                        bool oldCheck = collisionData.HasAttribute(CollisionAttribute.CanPlaceDecorationObject);
                        bool check = EditorGUILayout.ToggleLeft(new GUIContent("Can Place Decoration Object", "该区域内是否可放置装饰物, 在Front Layer的Remove Prefabs Which Collides With Obstacles功能时起作用"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.CanPlaceDecorationObject, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.Walkable);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Walkable", "是否在该区域内生成导航网格"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.Walkable, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.CanPlaceRuinPoint);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Can Place Ruin", "是否可在该区域内生成ruin layer的数据, 用在Ruin Layer的Generate Random Points功能,不影响手动放置的数据,注意!该区域还是会在生成导航网格和障碍物数据时被考虑"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.CanPlaceRuinPoint, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.CanNotTeleportOnly);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Can Not Teleport Only", "勾上后可移动到该区域,但是不能传送或放置npc到该区域,即在该区域同时会生成导航网格和障碍物数据"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.CanNotTeleportOnly, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.CanPlaceNPCSpawnPoint);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Can Place NPC Spawn Point", "是否能在该区域内生成NPC刷新点"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.CanPlaceNPCSpawnPoint, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.SpecialRegion);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Special Region", "特殊区域,一般用于关卡等特殊功能, 勾上后该区域的id就会起作用"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.SpecialRegion, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.BorderLine);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Border Line", "地图边界线"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.BorderLine, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.Disabled);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Disable", "和Special Region配合使用,勾上后表示该区域默认导航网格状态是关闭的,即会生成导航网格,但是不能寻路.可在代码里控制这个区域的开关状态"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.Disabled, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.IsAutoExpandingObstacle);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Auto Expanding Obstacle", "勾上后点击下面的Expand All Auto Expanding Obstacles就会对该collision生效"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.IsAutoExpandingObstacle, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.TestCollisionWhenPlaceDecorationObject);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("As Obstacle When Placing Decoration Object", "Decoration Layer的装饰物不能放在该区域中"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.TestCollisionWhenPlaceDecorationObject, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.RiverClipper);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Is River Clipper","裁剪Polygon River Layer的河流"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.RiverClipper, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.RuinPointClipper);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Is Ruin Clipper", "在使用Ruine Layer的Generate Random Points In Special Region时,该区域内不生成Ruin点"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.RuinPointClipper, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.IsConvertedFromPrefabOutline);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Is Converted From PrefabOutline", "是否是从PrefabOutline转换而来"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.IsConvertedFromPrefabOutline, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.IsConvertedFromCollisionSegment);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Is Converted From Collision Segment", "是否是从地表的Collision Segment转换而来"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.IsConvertedFromCollisionSegment, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.CameraLookAtArea);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Camera Look At Area", "将相机的视野中心点移动范围限制到该区域中"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.CameraLookAtArea, check);
                        }

                        oldCheck = collisionData.HasAttribute(CollisionAttribute.IsFrontLayerObjectClipper);
                        check = EditorGUILayout.ToggleLeft(new GUIContent("Front Layer Object Clipper", "作为Front Layer Object的裁剪框"), oldCheck);
                        if (oldCheck != check)
                        {
                            ChangeCollisionAttribute(collisionData, CollisionAttribute.IsFrontLayerObjectClipper, check);
                        }

                        EditorGUILayout.BeginHorizontal("GroupBox");
                        int newType = EditorGUILayout.IntField(new GUIContent("Area Type", "区域id, 范围从0到2000, 0表示海岛的边框,2000表示海面区域,其他值可以自由填写"), collisionData.type);
                        if (newType != collisionData.type)
                        {
                            if (newType >= MapCoreDef.MAP_MAX_REGION_TYPE_ID || newType < 0)
                            {
                                EditorUtility.DisplayDialog("Error", $"value must be in [0, {MapCoreDef.MAP_MAX_REGION_TYPE_ID - 1}] range!", "OK");
                            }
                            else
                            {
                                SetCollisionAreaType(collisionData, newType);
                            }
                        }

                        EditorGUILayout.EndHorizontal();

                        bool isSimple = collisionData.outlines[0].isSimplePolygon;
                        EditorGUILayout.ToggleLeft(new GUIContent("Is Simple", "边框是否是simple polygon"), isSimple);

                        EditorGUILayout.BeginHorizontal();
                        if (GUILayout.Button(new GUIContent("Revert","将边框顶点顺序逆转")))
                        {
                            mLogic.layer.RevertCollision(collisionData.id, mLogic.layer.displayType);

                            SetSelection(null, -1);
                        }
                        if (GUILayout.Button(new GUIContent("Is CW", "边框polygon是否是顺时针方向排列")))
                        {
                            var outline = collisionData.GetOutlineVertices(mLogic.layer.displayType);
                            bool isCW = Utils.IsPolygonCW(outline);
                            EditorUtility.DisplayDialog("", isCW ? "CW" : "CCW", "OK");
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("All CW To CCW", "将所有顺时针排列的边框转换为逆时针排列")))
                {
                    ConvertWindingOrder(true);
                }
                if (GUILayout.Button(new GUIContent("All CCW To CW", "将所有逆时针排列的边框转换为顺时针排列")))
                {
                    ConvertWindingOrder(false);
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Add Prefab Outlines", "将prefab outline转换为Map Collision Layer的障碍物")))
                {
                    mLogic.layer.AddObjectFromPrefabOutlines();
                }
                if (GUILayout.Button(new GUIContent("Remove Prefab Outlines", "删除从prefab outline转换而来的Map Collision Layer的障碍物")))
                {
                    mLogic.layer.RemovePrefabOutlines();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Add Collision Segments", "将地表tile的Collision Segment合并再转换为Map Collision Layer的障碍物")))
                {
                    mLogic.layer.AddObjectFromCollisionSegments();
                }
                if (GUILayout.Button(new GUIContent("Remove Collision Segments", "删除从Collision Segment转换而来的Map Collision Layer的障碍物")))
                {
                    mLogic.layer.RemoveCollisionSegments();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Generate Coastline Collision", "根据地表tile生成海岸线碰撞")))
                {
                    var dlg = EditorUtils.CreateInputDialog("Set Parameters");
                    var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Distance Error", "", "30"),
                        new InputDialog.StringItem("Angle Error", "", "30"),
                    };
                    dlg.Show(items, (List<InputDialog.Item> parameters) => {
                        string distanceErrorText = (parameters[0] as InputDialog.StringItem).text;
                        bool ok = float.TryParse(distanceErrorText, out float distanceError);
                        if (!ok)
                        {
                            return false;
                        }

                        string angleErrorText = (parameters[1] as InputDialog.StringItem).text;
                        ok = float.TryParse(angleErrorText, out float angleError);
                        if (!ok)
                        {
                            return false;
                        }
                        mLogic.layer.AddObjectFromMeshOutline(distanceError, angleError);
                        return true;
                    });
                    
                }
                if (GUILayout.Button(new GUIContent("Remove Coastline Collision", "删除根据地表tile生成的海岸线碰撞")))
                {
                    mLogic.layer.RemoveMeshOutlines();
                }
                EditorGUILayout.EndHorizontal();

                float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "顶点显示半径"), mLogic.layer.displayVertexRadius);
                if (radius != mLogic.layer.displayVertexRadius)
                {
                    mLogic.layer.SetVertexDisplayRadius(radius);
                }
                float maxConnectionDistance = EditorGUILayout.FloatField(new GUIContent("Collision Segment End Point Max Distance", "两条Collision Segment端点能连接上的最大距离"), mLogic.layer.maxCollectionDistance);
                maxConnectionDistance = Mathf.Max(0.1f, maxConnectionDistance);
                mLogic.layer.maxCollectionDistance = maxConnectionDistance;

                float obstacleExpandSizeWhenCreate = EditorGUILayout.FloatField(new GUIContent("Obstacle Expansion Size When Create", "当创建collision时,object placement obstacle应该在navmesh obstacle的基础上扩大多少米,服务器的逻辑是object placement obstacle应该比navmesh obstacle的范围大"), mLogic.obstacleExpandRadius);
                if (obstacleExpandSizeWhenCreate != mLogic.obstacleExpandRadius)
                {
                    mLogic.obstacleExpandRadius = obstacleExpandSizeWhenCreate;
                }
				
                EditorGUILayout.BeginHorizontal();
                mLogic.showCursor = EditorGUILayout.ToggleLeft(new GUIContent("Show Cursor", "是否显示一个指示圆"), mLogic.showCursor);
                mLogic.cursorRadius = EditorGUILayout.FloatField(new GUIContent("Cursor Radius", "指示圆的半径"), mLogic.cursorRadius);
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (mSelectedCollisionDataID != 0)
                {
                    if (GUILayout.Button(new GUIContent("Clear Outline", "清空所选的collision当前类型的边框")))
                    {
                        ClearPrefabOutline();
                    }
                    if (GUILayout.Button(new GUIContent("Expand Outline", "扩大当前的边框大小")))
                    {
                        ExpandPrefabOutline();
                    }
                    if (GUILayout.Button(new GUIContent("Copy Outline", "把当前类型的边框数据复制到另外一个类型的边框上,复制时中可以扩大")))
                    {
                        CopyPrefabOutline();
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (MapModule.enableResizeMap)
                {
                    if (GUILayout.Button(new GUIContent("Move And Resize Layer", "修改layer大小")))
                    {
                        var dlg = EditorUtils.CreateInputDialog("Move And Resize Layer");

                        var items = new List<InputDialog.Item> {
                            new InputDialog.StringItem("Size", "", "8100"),
                            new InputDialog.EnumItem("Alignment", "", ResizeAlignment.StayAndOffsetLayer),
                        };
                        dlg.Show(items, OnClickMoveAndResizeLayer);
                    }
                }

                if (GUILayout.Button("Generate Obstacles And Navigation Mesh"))
                {
                    var collisionLayer = Map.currentMap.GetMapLayer<MapCollisionLayer>();
                    if (collisionLayer != null)
                    {
                        collisionLayer.AddObjectFromPrefabOutlines();
                    }
                    bool usePrefabOutline = MapModule.usePrefabOutline;
                    MapModule.usePrefabOutline = false;

                    //create obstacles
                    MapEditorUI.CreateObstacles(SLGMakerEditor.instance.projectFolder);

                    var editorMap = Map.currentMap as EditorMap;
                    var navMeshLayer = Map.currentMap.GetMapLayerOfType(typeof(NavMeshLayer)) as NavMeshLayer;
                    if (navMeshLayer != null)
                    {
                        //StopWatchWrapper w = new StopWatchWrapper();
                        //w.Start();
                        var meshies = navMeshLayer.CreateNavMesh(2, 2, PrefabOutlineType.NavMeshObstacle, 0, 100, 180000, false, editorMap.navMeshMode);
                        //var t = w.Stop();
                        //Debug.LogError($"CreateNavMesh: {t}");
                        if (meshies != null)
                        {
                            var layerView = navMeshLayer.layerView;
                            layerView.ShowNavMesh(true);
                        }
                        //w.Start();
                        NavMeshLayerLogic.ExportNavMesh(navMeshLayer, SLGMakerEditor.instance.projectFolder);
                        //t = w.Stop();
                        //Debug.LogError($"ExportNavMesh: {t}");
                    }

                    if (collisionLayer != null)
                    {
                        collisionLayer.RemovePrefabOutlines();
                    }
                    MapModule.usePrefabOutline = usePrefabOutline;
                }

                if (GUILayout.Button(new GUIContent("Expand Outline With Same Size", "当一个边框的两种边框数据相同时,在navmesh obstacle的基础上扩大n米作为object placement obstacle的数据"))) 
                {
                    ExpandOutlineWithSameSize(6);
                }

                if (GUILayout.Button(new GUIContent("Clear All Outlines Of Current Type", "清除所有collision的当前类型的边框数据")))
                {
                    ClearAllPrefabOutlines();
                }

                if (GUILayout.Button(new GUIContent("Expand All Auto Expanding Obstacles", "扩大所有勾选了Auto Expanding Obstacles标记的collision")))
                {
                    ExpandAllAutoExpandingObstacles();
                }

                if (GUILayout.Button(new GUIContent("Export Collision Point", "以tsv格式导出所有碰撞点的坐标，浮点转整数坐标放大10000倍")))
                {
                    ExportCollisionPoint();
                }

                mLogic.layer.regionDetector.DrawGUI(mLogic.layer.displayVertexRadius);

                if (mLogic.operation == MapCollisionOperation.EditCollisionVertex)
                {
                    EditorGUILayout.LabelField(new GUIContent("Add Vertex: Ctrl + Mouse Left Button", "按住Ctrl和鼠标左键可以增加一个顶点"));
                    EditorGUILayout.LabelField(new GUIContent("Remove Vertex: Ctrl + Shift + Mouse Left Button", "按住Ctrl和Shift和鼠标左键可以删除一个选中的顶点"));
                    EditorGUILayout.LabelField(new GUIContent("Space: Switch outline type", "按空格键可以在两种类型边框之间切换显示"));
                    EditorGUILayout.LabelField(new GUIContent("Return: Switch operation", "按回车键可以切换操作类型"));
                    EditorGUILayout.LabelField(new GUIContent("Moving Vertex With Snap: Hold Down Shift When Moving Vertex", "移动顶点时按住shift可以吸附到附近顶点,吸附距离由snap distance决定"));
                    EditorGUILayout.LabelField($"Collision Count:", mLogic.layer.objectCount.ToString());
                }

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Width", mLogic.layer.GetTotalWidth().ToString());
                EditorGUILayout.LabelField("Height", mLogic.layer.GetTotalHeight().ToString());
                EditorGUILayout.EndVertical();
            }
        }

        bool OnClickMoveAndResizeLayer(List<InputDialog.Item> parameters)
        {
            string newSizeStr = (parameters[0] as InputDialog.StringItem).text;
            System.Enum alignment = (parameters[1] as InputDialog.EnumItem).value;
            int newSize;
            Utils.ParseInt(newSizeStr, out newSize);
            if (newSize > 0)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as MapCollisionLayer;
                layer.MoveAndResize(newSize, newSize, (ResizeAlignment)alignment);
                mLogic.RecreateGrid();
                return true;
            }

            return false;
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                mLeftButtonDown = true;
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (mLogic.showCursor)
            {
                Handles.DrawWireDisc(pos, Vector3.up, mLogic.cursorRadius);
                SceneView.RepaintAll();
            }
            if (currentEvent.type == EventType.KeyDown && currentEvent.keyCode == KeyCode.Space)
            {
                if (mLogic.layer.displayType == PrefabOutlineType.NavMeshObstacle)
                {
                    SetOutlineType(PrefabOutlineType.ObjectPlacementObstacle);
                }
                else
                {
                    SetOutlineType(PrefabOutlineType.NavMeshObstacle);
                }
                Repaint();
            }
            if (currentEvent.type == EventType.KeyDown && currentEvent.keyCode == KeyCode.Return)
            {
                if (mLogic.operation == MapCollisionOperation.CreateCollision)
                {
                    mLogic.operation = MapCollisionOperation.EditCollisionVertex;
                }
                else if (mLogic.operation == MapCollisionOperation.EditCollisionVertex)
                {
                    mLogic.operation = MapCollisionOperation.CreateCollision;
                }
                mAddedVertices.Clear();
                mDisplayedVertices = new Vector3[0];
                Repaint();
            }

            if (currentEvent.type == EventType.MouseUp)
            {
                if (currentEvent.button == 0)
                {
                    //press left key
                    mLeftButtonDown = false;
                    mCanCreate = true;

                    if (mCollisionMover != null)
                    {
                        mCollisionMover.Stop(pos);
                        mCollisionMover = null;
                    }
                    if (mVertexMover != null)
                    {
                        mVertexMover.Stop(pos);
                        mVertexMover = null;
                    }
                }

                mPickWhenMovingCollision = true;
                mMover.Reset();
            }

            if (Event.current.button == 1 && Event.current.type == EventType.MouseDown)
            {
                if (mAddedVertices.Count == 0)
                {
                    GenericMenu menu = new GenericMenu();
                    menu.AddItem(new GUIContent("Create Circle Collision"), false, AddCircleCollision, pos);
                    menu.AddItem(new GUIContent("Create Circle Border Collision"), false, AddInverseCircleCollision);
                    menu.AddItem(new GUIContent("Create Rectangle Collision"), false, AddRectangleCollision, pos);
                    menu.AddItem(new GUIContent("Delete Collision"), false, DeleteCollision);
                    menu.AddItem(new GUIContent("Clone Collision"), false, CloneCollision);
                    menu.AddItem(new GUIContent("Check If Collision Collides With Map Obstacles"), false, CheckCollideWithMapObstacles);
                    menu.AddItem(new GUIContent("Show Region ID"), false, ShowRegionID, pos);
                    menu.AddItem(new GUIContent("Show Region State"), false, ShowRegionState, pos);
                    menu.ShowAsContext();
                }

                //press right key
                mCanCreate = true;
                bool created = AddCollision();
                if (created)
                {
                    mLogic.operation = MapCollisionOperation.EditCollisionVertex;
                }

                SceneView.RepaintAll();
            }

            if (mLogic.operation == MapCollisionOperation.CreateCollision)
            {
                if (mLeftButtonDown && mCanCreate)
                {
                    mCanCreate = false;

                    mAddedVertices.Add(pos);
                    if (mAddedVertices.Count == 1)
                    {
                        mAddedVertices.Add(pos);
                    }
                    mDisplayedVertices = mAddedVertices.ToArray();
                    SceneView.RepaintAll();
                }

                if (currentEvent.type == EventType.MouseMove)
                {
                    if (mAddedVertices.Count > 0)
                    {
                        mAddedVertices[mAddedVertices.Count - 1] = pos;
                        mDisplayedVertices[mDisplayedVertices.Length - 1] = pos;
                    }

                    SceneView.RepaintAll();
                }

                HandleUtility.AddDefaultControl(0);
            }
            else if (mLogic.operation == MapCollisionOperation.EditCollisionVertex)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
                {
                    if (currentEvent.control == false)
                    {
                        Pick(pos);
                    }
                    else
                    {
                        if (currentEvent.shift)
                        {
                            Pick(pos);
                            RemoveVertex();
                        }
                        else
                        {
                            AddVertex(pos);
                        }
                    }
                }

                if (mLeftButtonDown && currentEvent.control == false)
                {
                    MoveVertex(pos, currentEvent.shift);
                }

                HandleUtility.AddDefaultControl(0);
            }
            else if (mLogic.operation == MapCollisionOperation.CutCollision)
            {
                OnCutCollisionSceneGUI(Event.current, pos);
            }
            else if (mLogic.operation == MapCollisionOperation.MoveCollision)
            {
                if (mLeftButtonDown)
                {
                    if (mPickWhenMovingCollision == true)
                    {
                        mPickWhenMovingCollision = false;
                        Pick(pos);
                    }
                    MoveCollision(pos);
                }

                HandleUtility.AddDefaultControl(0);
            }

            Handles.DrawPolyLine(mDisplayedVertices);

            mLogic.layer.UpdateColor(mLogic.layer.displayType);
        }

        bool PickVertex(Vector3 pos, out int objectID, out int vertexIndex)
        {
            int hitVertex = -1;
            int hitObjectID = 0;
            System.Func<IMapObjectData, bool> func = (IMapObjectData data) =>
            {
                var collisionData = data as MapCollisionData;

                var vertices = collisionData.GetOutlineVertices(mLogic.layer.displayType);
                for (int i = 0; i < vertices.Count; ++i)
                {
                    if (Utils.IsHitRectangle(pos, vertices[i], collisionData.displayRadius))
                    {
                        hitVertex = i;
                        hitObjectID = collisionData.id;
                        break;
                    }
                }

                return hitVertex != -1;
            };

            mLogic.layer.Traverse(func);

            objectID = hitObjectID;
            vertexIndex = hitVertex;

            return hitVertex != -1;
        }

        void Pick(Vector3 pos)
        {
            SetSelection(null, -1);
            System.Func<IMapObjectData, bool> func = (IMapObjectData data) =>
            {
                var collisionData = data as MapCollisionData;

                int hitVertex = -1;
                var vertices = collisionData.GetOutlineVertices(mLogic.layer.displayType);
                for (int i = 0; i < vertices.Count; ++i)
                {
                    if (Utils.IsHitRectangle(pos, vertices[i], collisionData.displayRadius))
                    {
                        hitVertex = i;
                        break;
                    }
                }

                if (hitVertex >= 0)
                {
                    SetSelection(collisionData, hitVertex);
                }

                if (mSelectedCollisionDataID != 0)
                {
                    SceneView.RepaintAll();
                    return true;
                }

                return false;
            };

            mLogic.layer.Traverse(func);
        }

        void OnMoveCollision(int dataID)
        {
            var collisionData = Map.currentMap.FindObject(dataID) as MapCollisionData;
            if (collisionData.HasAttribute(CollisionAttribute.CollisionCheck))
            {
                CheckCollideWithMapObstacles();
            }

            SceneView.RepaintAll();

            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void MoveCollision(Vector3 pos)
        {
            if (mSelectedCollisionDataID != 0)
            {
                if (mCollisionMover == null)
                {
                    mCollisionMover = new MapCollisionMover(mLogic.layerID, mSelectedCollisionDataID, pos);
                }

                UnityEngine.Debug.Assert(mSelectedVertexIndex >= 0);

                mMover.Update(pos);

                var collisionData = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                var offset = mMover.GetDelta();

                mLogic.layer.MoveObject(PrefabOutlineType.NavMeshObstacle, mSelectedCollisionDataID, offset);
                mLogic.layer.MoveObject(PrefabOutlineType.ObjectPlacementObstacle, mSelectedCollisionDataID, offset);

                if (collisionData.HasAttribute(CollisionAttribute.CollisionCheck))
                {
                    CheckCollideWithMapObstacles();
                }
#if false
                //temp code, 测试多边形之间的碰撞
                List<IMapObjectData> objects = new List<IMapObjectData>();
                mLogic.layer.GetAllObjects(objects);
                foreach(var obj in objects)
                {
                    var o = obj as MapCollisionData;
                    mLogic.layer.SetDisplayColor(o.id, mDefaultColor);
                }

                System.Action<PolygonObjectData, PolygonObjectData> onCollide = (PolygonObjectData a, PolygonObjectData b) =>
                {
                    mLogic.layer.SetDisplayColor(a.id, Color.yellow);
                    mLogic.layer.SetDisplayColor(b.id, Color.yellow);
                };
                mLogic.layer.CheckCollision(onCollide);
#endif

                SceneView.RepaintAll();
            }
        }

        void OnMoveVertex(int dataID, int vertexIndex)
        {
            SceneView.RepaintAll();

            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void MoveVertex(Vector3 pos, bool snap)
        {
            if (mSelectedCollisionDataID != 0 && mSelectedVertexIndex >= 0)
            {
                UnityEngine.Debug.Assert(mSelectedVertexIndex >= 0);

                if (mVertexMover == null)
                {
                    mVertexMover = new MapCollisionVertexMover(mLogic.layerID, mSelectedCollisionDataID, mSelectedVertexIndex, pos, mLogic.layer.displayType);
                }

                mMover.Update(pos);

                var collisionData = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                var oldPos = collisionData.GetOutlineVertices(mLogic.layer.displayType)[mSelectedVertexIndex];
                var newPos = mMover.GetDelta() + oldPos;

                if (snap)
                {
                    Vector3 snapPos;
                    bool found = mLogic.layer.FindSnapPosition(mLogic.layer.displayType, newPos, mLogic.snapDistance, collisionData.id, out snapPos);
                    if (found)
                    {
                        newPos = snapPos;
                    }
                }

                //clamp to border
                //newPos.x = Mathf.Clamp(newPos.x, 0, Map.currentMap.mapWidth);
                //newPos.z = Mathf.Clamp(newPos.z, 0, Map.currentMap.mapHeight);
                mLogic.layer.SetVertexPosition(mLogic.layer.displayType, mSelectedCollisionDataID, mSelectedVertexIndex, newPos);

                SceneView.RepaintAll();
            }
        }

        void AddVertex(Vector3 localPos)
        {
            if (mSelectedCollisionDataID != 0)
            {
                var collisionData = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                var idx = Utils.FindNearestEdgeDistance(localPos, collisionData.GetOutlineVertices(mLogic.layer.displayType));
                InsertVertex(idx, localPos);
            }
        }

        void OnInsertVertex(int dataID, int index)
        {
            SetSelection(Map.currentMap.FindObject(dataID) as MapCollisionData, index);
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void InsertVertex(int index, Vector3 localPos)
        {
            var action = new ActionAddMapCollisionVertex(mLogic.layerID, mSelectedCollisionDataID, index, localPos, mLogic.layer.displayType);
            ActionManager.instance.PushAction(action);
        }

        void OnDeleteCollision(int dataID)
        {
            if (dataID == mSelectedCollisionDataID)
            {
                mSelectedCollisionDataID = 0;
                mSelectedVertexIndex = -1;
                Repaint();

                mLogic.layer.ShowOutline(mLogic.layer.displayType);
            }
        }

        void CloneCollision()
        {
            if (mSelectedCollisionDataID != 0)
            {
                int newID = Map.currentMap.nextCustomObjectID;
                var act = new ActionCloneMapCollision(mLogic.layerID, mSelectedCollisionDataID, newID, 50);
                ActionManager.instance.PushAction(act);

                var data = Map.currentMap.FindObject(newID) as MapCollisionData;
                SetSelection(data, 0);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a collision first!", "OK");
            }
        }

        void DeleteCollision()
        {
            if (mSelectedCollisionDataID != 0)
            {
                var act = new ActionRemoveMapCollision(mLogic.layerID, mSelectedCollisionDataID);
                ActionManager.instance.PushAction(act);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a collision first!", "OK");
            }
        }

        void OnRemoveVertex(int dataID, int index)
        {
            mSelectedVertexIndex = -1;
            Repaint();
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void RemoveVertex()
        {
            if (mSelectedVertexIndex >= 0)
            {
                var collisionData = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                var vertices = collisionData.GetOutlineVertices(mLogic.layer.displayType);
                if (vertices.Count > 3)
                {
                    var action = new ActionRemoveMapCollisionVertex(mLogic.layerID, mSelectedCollisionDataID, mSelectedVertexIndex, mLogic.layer.displayType);
                    ActionManager.instance.PushAction(action);
                }
            }
        }

        void SetSelection(MapCollisionData data, int vertexIndex)
        {
            var layer = mLogic.layer;
            layer.SetColorDirty();
            if (mSelectedCollisionDataID != 0)
            {
                var d = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                if (d != null)
                {
                    layer.SetSelected(d.id, false);
                }
                mSelectedCollisionDataID = 0;
                mSelectedVertexIndex = -1;
            }

            if (data != null)
            {
                mSelectedCollisionDataID = data.GetEntityID();
                layer.SetSelected(data.id, true);
            }
            else
            {
                mSelectedCollisionDataID = 0;
            }
            mSelectedVertexIndex = vertexIndex;
            Repaint();
        }

        void OnAddCollision(int dataID)
        {
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            SetSelection(data, 0);
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
            Repaint();
        }

        bool AddCollision()
        {
            bool created = mAddedVertices.Count > 3;
            if (created)
            {
                mAddedVertices.RemoveAt(mAddedVertices.Count - 1);

                var dataID = Map.currentMap.nextCustomObjectID;
                var action = new ActionAddMapCollision(mLogic.layerID, dataID, mAddedVertices, true, CollisionAttribute.IsObstacle | CollisionAttribute.IsAutoExpandingObstacle, 0, mLogic.obstacleExpandRadius);
                ActionManager.instance.PushAction(action);
            }

            mAddedVertices.Clear();
            mDisplayedVertices = new Vector3[0];

            return created;
        }

        void CheckCollideWithMapObstacles()
        {
            if (mSelectedCollisionDataID != 0)
            {
                var mapObstacleManager = Map.currentMap.data.localObstacleManager;
                if (mapObstacleManager != null)
                {
                    var collisionData = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                    bool intersectedWithObstacles = false;
                    if (collisionData.IsConvex(mLogic.layer.displayType))
                    {
                        intersectedWithObstacles = mapObstacleManager.IsIntersectedWithObstacles(collisionData.GetOutlineVertices(mLogic.layer.displayType), collisionData.GetBounds());
                    }
                    mLogic.layer.SetIntersectedWithObstacles(mSelectedCollisionDataID, intersectedWithObstacles);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a collision first!", "OK");
            }
        }

        void RefreshCollisionCheck()
        {
            var mapObstacleManager = Map.currentMap.data.localObstacleManager;
            if (mapObstacleManager != null)
            {
                var objects = mLogic.layer.layerData.objects;
                foreach (var p in objects)
                {
                    var collisionData = p.Value as MapCollisionData;
                    if (collisionData.HasAttribute(CollisionAttribute.CollisionCheck))
                    {
                        bool intersectedWithObstacles = false;
                        if (collisionData.IsConvex(mLogic.layer.displayType))
                        {
                            intersectedWithObstacles = mapObstacleManager.IsIntersectedWithObstacles(collisionData.GetOutlineVertices(mLogic.layer.displayType), collisionData.GetBounds());
                        }
                        mLogic.layer.SetIntersectedWithObstacles(collisionData.id, intersectedWithObstacles);
                    }
                }
            }
        }

        void SetOutlineType(PrefabOutlineType type)
        {
            mLogic.layer.displayType = type;
            mLogic.layer.ShowOutline(type);
        }

        void OnOutlineChanged(int dataID, PrefabOutlineType type)
        {
            SceneView.RepaintAll();
            Repaint();
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void ClearPrefabOutline()
        {
            var action = new ActionClearMapCollisionOutline(mLogic.layerID, mSelectedCollisionDataID, mLogic.layer.displayType);
            ActionManager.instance.PushAction(action);
        }

        void ClearAllPrefabOutlines()
        {
            List<IMapObjectData> allCollisions = new List<IMapObjectData>();
            mLogic.layer.GetAllObjects(allCollisions);
            if (allCollisions.Count > 0)
            {
                CompoundAction actions = new CompoundAction(string.Format("Clear All Outlines Of {0}", mLogic.layer.displayType.ToString()));
                for (int i = 0; i < allCollisions.Count; ++i)
                {
                    var action = new ActionClearMapCollisionOutline(mLogic.layerID, allCollisions[i].GetEntityID(), mLogic.layer.displayType);
                    actions.Add(action);
                }
                ActionManager.instance.PushAction(actions);
            }
        }

        void ExpandAllAutoExpandingObstacles()
        {
            var inputDialog = EditorUtils.CreateInputDialog("Expand All Obstacle Outline");

            var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Radius", "", "1"),
                    };
            inputDialog.Show(items, OnExpandAllOutline);
        }

        int F2I(float v)
        {
            return (int)(v * 10000);
        }

        //导出
        void ExportCollisionPoint()
        {
            string path = EditorUtility.SaveFilePanel("Export Collision Point", "", "collision_point", "tsv");
            if (!string.IsNullOrEmpty(path))
            {
                StringBuilder builder = new StringBuilder();
                var headers = new string[] {
            "C_INT_id",
            "C_INT_x",
            "C_INT_z",
            };

                for (int i = 0; i < headers.Length; ++i)
                {
                    builder.Append(headers[i]);
                    if (i != headers.Length - 1)
                    {
                        builder.Append("\t");
                    }
                }

                builder.AppendLine();

                List<MapCollisionData> collisions = new List<MapCollisionData>();
                mLogic.layer.GetAllCollisions(collisions);
                int id = 1;
                foreach (var collision in collisions)
                {
                    var vertices = collision.GetOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle);
                    foreach (var v in vertices)
                    {
                        //id
                        builder.AppendFormat("{0}\t", id);
                        ++id;
                        builder.AppendFormat("{0}\t", F2I(v.x));
                        builder.AppendFormat("{0}\n", F2I(v.z));
                    }
                }

                var str = builder.ToString();
                File.WriteAllText(path, str);
            }
        }

        void ExpandPrefabOutline()
        {
            var inputDialog = EditorUtils.CreateInputDialog("Expand Collision Outline");

            var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Radius", "", "6"),
                    };
            inputDialog.Show(items, OnExpandOutline);
        }

        void CopyPrefabOutline()
        {
            var inputDialog = EditorUtils.CreateInputDialog("Copy And Expand Collision Outline");

            var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Radius", "", "3"),
                    };
            inputDialog.Show(items, OnCopyOutline);
        }

        bool OnExpandOutline(List<InputDialog.Item> param)
        {
            var radiusStr = (param[0] as InputDialog.StringItem).text;
            if (!string.IsNullOrEmpty(radiusStr))
            {
                float radius;
                bool suc = Utils.ParseFloat(radiusStr, out radius);

                if (suc && radius > 0)
                {
                    var action = new ActionExpandMapCollisionOutline(mLogic.layerID, mSelectedCollisionDataID, mLogic.layer.displayType, radius);
                    ActionManager.instance.PushAction(action);
                    return true;
                }
            }
            return false;
        }

        bool OnCopyOutline(List<InputDialog.Item> param)
        {
            var radiusStr = (param[0] as InputDialog.StringItem).text;
            if (!string.IsNullOrEmpty(radiusStr))
            {
                float radius;
                bool suc = Utils.ParseFloat(radiusStr, out radius);

                if (suc)
                {
                    var collisionData = Map.currentMap.FindObject(mSelectedCollisionDataID) as MapCollisionData;
                    var layerType = mLogic.layer.displayType;
                    var outline = PolygonAlgorithm.ExpandPolygon(radius, collisionData.GetOutlineVertices(layerType))[0];
                    if (layerType == PrefabOutlineType.NavMeshObstacle)
                    {
                        layerType = PrefabOutlineType.ObjectPlacementObstacle;
                    }
                    else
                    {
                        layerType = PrefabOutlineType.NavMeshObstacle;
                    }
                    var action = new ActionSetMapCollisionOutline(mLogic.layerID, mSelectedCollisionDataID, layerType, outline);
                    ActionManager.instance.PushAction(action);
                    return true;
                }
            }
            return false;
        }

        bool OnExpandAllOutline(List<InputDialog.Item> param)
        {
            var radiusStr = (param[0] as InputDialog.StringItem).text;
            if (!string.IsNullOrEmpty(radiusStr))
            {
                float radius;
                bool suc = Utils.ParseFloat(radiusStr, out radius);

                if (suc && radius >= 0)
                {
                    List<MapCollisionData> collisions = new List<MapCollisionData>();
                    mLogic.layer.GetCollisionsOfType(collisions, CollisionAttribute.IsAutoExpandingObstacle);
                    if (collisions.Count > 0)
                    {
                        var actions = new CompoundAction("Expand All Auto Expanding Obstacle");
                        for (int i = 0; i < collisions.Count; ++i)
                        {
                            var navMeshOutlineData = collisions[i].GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
                            var newOutline = PolygonAlgorithm.ExpandPolygon(radius, navMeshOutlineData)[0];
                            //check outline vertices range
                            Utils.ClampToRange(newOutline, 0, 0, Map.currentMap.mapWidth, Map.currentMap.mapHeight);
                            var action = new ActionSetMapCollisionOutline(mLogic.layerID, collisions[i].id, PrefabOutlineType.ObjectPlacementObstacle, newOutline);
                            actions.Add(action);
                        }
                        ActionManager.instance.PushAction(actions);
                    }
                    return true;
                }
            }
            return false;
        }

        void ChangeCollisionAttribute(MapCollisionData data, CollisionAttribute attribute, bool check)
        {
            EditorAction action = null;
            if (check)
            {
                action = new ActionAddMapCollisionAttribute(mLogic.layerID, data, attribute);
            }
            else
            {
                action = new ActionRemoveMapCollisionAttribute(mLogic.layerID, data, attribute);
            }
            ActionManager.instance.PushAction(action);
        }

        void SetCollisionAreaType(MapCollisionData data, int type)
        {
            var action = new ActionSetCollisionAreaType(mLogic.layerID, data, type);
            ActionManager.instance.PushAction(action);
        }

        //扩展navmesh和obstacle outline相同的障碍物
        void ExpandOutlineWithSameSize(float size)
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            mLogic.layer.GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                var collision = objects[i] as MapCollisionData;
                var navMeshOutline = collision.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
                var obstacleOutline = collision.GetOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle);

                if (navMeshOutline.Count == 0 || obstacleOutline.Count == 0)
                {
                    continue;
                }
                if (OutlineEquals(navMeshOutline, obstacleOutline))
                {
                    collision.ExpandOutline(PrefabOutlineType.ObjectPlacementObstacle, size);
                }
            }

            SceneView.RepaintAll();
        }

        bool OutlineEquals(List<Vector3> a, List<Vector3> b)
        {
            if (a.Count != b.Count)
            {
                return false;
            }

            for (int i = 0; i < a.Count; ++i)
            {
                if (a[i] != b[i])
                {
                    return false;
                }
            }

            return true;
        }

        void ConvertWindingOrder(bool cwToCCW)
        {
            List<MapCollisionData> allCollisions = new List<MapCollisionData>();
            mLogic.layer.GetAllCollisions(allCollisions);
            for (int i = 0; i < allCollisions.Count; ++i)
            {
                var outline = allCollisions[i].GetOutlineVertices(mLogic.layer.displayType);
                bool isCW = Utils.IsPolygonCW(outline);
                if (cwToCCW)
                {
                    if (isCW)
                    {
                        mLogic.layer.RevertCollision(allCollisions[i].id, mLogic.layer.displayType);
                    }
                }
                else
                {
                    if (!isCW)
                    {
                        mLogic.layer.RevertCollision(allCollisions[i].id, mLogic.layer.displayType);
                    }
                }
            }
            SetSelection(null, -1);
        }

        MapCollisionLayerLogic mLogic;
        List<Vector3> mAddedVertices = new List<Vector3>();
        Vector3[] mDisplayedVertices = new Vector3[0];
        MouseMover mMover = new MouseMover();
        bool mLeftButtonDown;
        bool mCanCreate = true;
        bool mPickWhenMovingCollision = true;
        int mSelectedCollisionDataID;
        int mSelectedVertexIndex = -1;
        MapCollisionMover mCollisionMover;
        MapCollisionVertexMover mVertexMover;
    }
}

#endif