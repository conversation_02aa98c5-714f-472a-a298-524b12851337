﻿using System;
using UnityEngine;

 
    public class LaunchTimeLogger: IDisposable
    {
        private readonly string _operationname;
        private readonly float _startSceonds;

        public LaunchTimeLogger(string operationname)
        {
            this._operationname = operationname;
            this._startSceonds = Time.realtimeSinceStartup;
        }

        public void Dispose()
        {

#if UNITY_EDITOR
            Debug.LogWarning($"<color=green>[启动耗时]（{this._operationname}）{Time.realtimeSinceStartup - this._startSceonds}s</color>");
#endif
            //GC.SuppressFinalize(this);
        }
    }
 