﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static partial class NavMeshCreator
    {
        static void CreateIslandAndOceanNavigationMesh(PrefabOutlineType type, Vector3 min, Vector3 max, float agentRadius, bool oceanAreaEnabled, bool removeSameHoles, out Vector3[] meshVertices, out int[] meshIndices, out ushort[] triangleTypes, out bool[] triangleStates)
        {
            var combineMeshies = new List<MeshItem>();
            //海岛游戏,先创建岛的navmesh,再创建海面的navmesh,然后将海面的navmesh三角形属性设置为不可走
            //必须将海岛的collision属性设置为SpecialRegion.region type大的会被小的当作障碍物,可用来创建岛上的桥的区域的navmesh等
            Vector3[] specialRegionVertices;
            int[] specialRegionIndices;
            List<ushort> triangleTypeList;
            List<bool> triangleStateList;
            CreateSpecialRegionNavMesh(type, agentRadius, false, out specialRegionVertices, out specialRegionIndices, out triangleTypeList, out triangleStateList);
            if (specialRegionVertices != null)
            {
                combineMeshies.Add(new MeshItem(specialRegionVertices, specialRegionIndices));
            }

            Vector3[] waterRegionVertices;
            int[] waterRegionIndices;
            CreateWaterRegionNavMesh(type, min, max, agentRadius, triangleTypeList, triangleStateList, oceanAreaEnabled, removeSameHoles, out waterRegionVertices, out waterRegionIndices);
            if (waterRegionVertices != null)
            {
                combineMeshies.Add(new MeshItem(waterRegionVertices, waterRegionIndices));
            }
            //合并两种navmesh为一个navmesh
            CombineMesh(combineMeshies, out meshVertices, out meshIndices);
            triangleTypes = triangleTypeList.ToArray();
            triangleStates = triangleStateList.ToArray();
        }
    }
}


#endif