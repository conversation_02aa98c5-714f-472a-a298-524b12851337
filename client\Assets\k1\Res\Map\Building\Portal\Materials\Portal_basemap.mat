%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Portal_basemap
  m_Shader: {fileID: 4800000, guid: 31fb72ed5c0dcce4b9109fb7b9e46345, type: 3}
  m_ShaderKeywords: _ALPHAPREMULTIPLY_ON _USE8NEIGHBOURHOOD_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    AlphaDepth: true
    IGNOREPROJECTOR: true
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 9cf32dfd6dfb3e248982cf55cd011c7d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 2800000, guid: c2ba5ef49f58b724fb81dc2007a4ff3a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - DayNightToggle: 0
    - PixelSnap: 0
    - _BlendAmount: 0
    - _Brightness: 1
    - _BumpScale: 1
    - _Cull: 0
    - _CustomRenderQueue: 0
    - _Cutoff: 0.61
    - _DetailNormalMapScale: 1
    - _DstBlend: 10
    - _EnableExternalAlpha: 0
    - _GlitterSpeed: 5
    - _GlitterStrength: 0.236
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Height: -0.75
    - _Hue: 0
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OutlineMipLevel: 0
    - _OutlineReferenceTexWidth: 1024
    - _OutlineSmoothness: 1
    - _OutlineWidth: 3
    - _Parallax: 0.02
    - _RenderQueue: 0
    - _Saturation: 1
    - _ShadowAlphaCutoff: 0.1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _StencilComp: 8
    - _StencilRef: 1
    - _ThresholdEnd: 0.25
    - _Transparent: 3.11
    - _UVSec: 0
    - _Use8Neighbourhood: 1
    - _ZTest: 4
    - _ZWrite: 0
    m_Colors:
    - _Color: {r: 0, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MaskColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 1, g: 1, b: 0, a: 1}
    - _OverlayColor: {r: 0, g: 0, b: 0, a: 0}
