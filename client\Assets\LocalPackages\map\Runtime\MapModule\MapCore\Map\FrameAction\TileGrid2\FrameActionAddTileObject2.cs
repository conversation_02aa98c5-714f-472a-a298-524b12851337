﻿ 



 
 


//#define USE_CULL
using UnityEngine;

namespace TFW.Map
{
    //当新地块显示时加入管理其子节点
    public class FrameActionAddTileObject2 : FrameAction
    {
        public static FrameActionAddTileObject2 Require(TileGridObjectLayerData2 layerData, Vector3 bigTilePos, BigTileChildPrefabData2 prefabData, int objectDataID, int localIndex, int lod, System.Action<TileObjectData2> onObjectScaleChangeCallback)
        {
            var act = mPool.Require();
            act.Init(layerData, bigTilePos, prefabData, objectDataID, localIndex, lod, onObjectScaleChangeCallback);
            return act;
        }

        void Init(TileGridObjectLayerData2 layerData, Vector3 bigTilePos, BigTileChildPrefabData2 prefabData, int objectDataID, int localIndex, int lod, System.Action<TileObjectData2> onObjectScaleChangeCallback)
        {
            InitAction();
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;
            mLayerData = layerData;
            mPrefabData = prefabData;
            mObjectDataID = objectDataID;
            mLocalIndex = localIndex;
            mLOD = lod;
            mBigTilePos = bigTilePos;

            mKey = MakeActionKey(objectDataID, type);
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mPool.Release(this);
        }

        protected override void DoImpl()
        {
            if (MapCoreDef.IsRemovableObject(mPrefabData.objectType) && mLayerData.IsObjectRemoved(mPrefabData.viewID))
            {
                return;
            }

            var map = mLayerData.map;
            var viewport = map.viewport;

            var coord = mLayerData.FromWorldPositionToCoordinate(mBigTilePos);
            int tileIndex = coord.y * mLayerData.horizontalTileCount + coord.x;
            var bigTileData = mLayerData.GetBigTileData(coord.x, coord.y);

            var offset = new Vector2(mBigTilePos.x, mBigTilePos.z);

            var bounds = mPrefabData.GetLocalBoundsInPrefab(mLayerData.prefabInfos);
            var rectMin = bounds.min + offset;
            var rectMax = bounds.max + offset;

            var pos = mPrefabData.GetPosition(mLayerData.prefabInfos) + mBigTilePos;

            bool alwaysVisible = mPrefabData.objectType == TileObjectType.AlwaysVisible;

            bool collidesWithNPC = false;
            bool isVisible = false;
            if (alwaysVisible) 
            {
                isVisible = true;
            }
            else 
            {
                if (rectMin.x > viewport.xMax || rectMin.y > viewport.yMax ||
                    viewport.xMin > rectMax.x || viewport.yMin > rectMax.y)
                {
                    isVisible = false;
                }
                else
                {        
#if USE_CULL
                    float centerX = (rectMin.x + rectMax.x) * 0.5f;
                    float centerZ = (rectMin.y + rectMax.y) * 0.5f;
                    float radius = Mathf.Max(rectMax.x - rectMin.x, rectMax.y - rectMin.y);
                    isVisible = mLayerData.IsDecorationObjectVisible(centerX, centerZ, radius);
#else
                    isVisible = mLayerData.IsIDTaken(mPrefabData.viewID, mPrefabData.objectType) ? false : true;
#endif
                    collidesWithNPC = !isVisible;
                }
            }

            var worldBounds = new Rect();
            worldBounds.Set(rectMin.x, rectMin.y, rectMax.x - rectMin.x, rectMax.y - rectMin.y);

            Vector3 baseScale = mPrefabData.GetScale(mLayerData.prefabInfos);
            Vector3 scale = baseScale;
            if (mPrefabData.objectType == TileObjectType.ScaleDecorationObject)
            {
                mLayerData.hasScaleObject = true;
                scale = mLayerData.objectScale * baseScale;
            }

            string prefabPath = null;
            int state = 0;
            var prefabInfo = mLayerData.prefabInfos[mPrefabData.prefabInitInfoIndex];
            TileGridObjectLayerData2.SpecialArea specialArea = null;
            //找到该装饰物是否在special area中,如果是,根据special area的状态选择对应的装饰物资源
            if (mLayerData.enableSpecialArea)
            {
                specialArea = mLayerData.GetSpecialAreaByViewID(mPrefabData.viewID);
                if (specialArea != null)
                {
                    state = specialArea.state;
                }
            }

            //根据state选取prefab path
            prefabPath = prefabInfo.GetPrefabPath(state);

            bool useCullManager = worldBounds.width <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && worldBounds.height <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && !alwaysVisible;
            
            var tile = mLayerData.pool.Require(mObjectDataID, mLayerData.map, mPrefabData, prefabPath, worldBounds, pos, scale, mPrefabData.GetRotation(mLayerData.prefabInfos), mLocalIndex, tileIndex, mLOD, baseScale.x, prefabInfo, mOnObjectScaleChangeCallback, useCullManager, specialArea);

            //设置tile与npc碰撞的结果
            tile.collidesWithNPC = collidesWithNPC;

            mLayerData.SetObjectActiveOnly(tile, isVisible, mLOD);
            mLayerData.AddTileObject(tile);
        }

        public static long MakeActionKey(int id, FrameActionType type)
        {
            return FrameAction.MakeKeyHelper(id, type);
        }

        public override long key => mKey;
        public override FrameActionType type => FrameActionType.AddTileObject2;
        public override string debugInfo
        {
            get
            {
                return "";
            }
        }
        public override string name => "Add Tile Object 2";

        static ObjectPool<FrameActionAddTileObject2> mPool = new ObjectPool<FrameActionAddTileObject2>(30000, () => new FrameActionAddTileObject2());

        TileGridObjectLayerData2 mLayerData;
        BigTileChildPrefabData2 mPrefabData;
        int mObjectDataID;
        int mLocalIndex;
        int mLOD;
        long mKey;
        Vector3 mBigTilePos;
        System.Action<TileObjectData2> mOnObjectScaleChangeCallback;
    }
}
