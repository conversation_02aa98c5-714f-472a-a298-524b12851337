﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public static class SkeletonAnimationBakerUtility
    {
        public static GameObject BakeToFolder(GameObject gameObject, string outputFolder, Shader defaultSkinAnimShader, Shader defaultRigidAnimShader, bool useAnimationBlending = false, AnimationBlendType blendType = AnimationBlendType.Fast, float sampleFPS = 60, bool useRGBA32Texture = true, bool deleteBipBones = true, bool useSRPBatcher = false, bool generatePrefab = false, bool clearOutputFolder = false, bool separateCPUDrivenBoneTransformData = false)
        {
            if (EditorApplication.isPlaying)
            {
                EditorUtility.DisplayDialog("Error", "Can only be called in editor mode!", "OK");
                return null;
            }

            var baker = new SkeletonAnimationBaker();
            var errorMsg = baker.Check(gameObject);
            if (!string.IsNullOrEmpty(errorMsg))
            {
                Debug.LogError(errorMsg);
                return null;
            }

            var bakeOption = new SkeletonAnimationBaker.BakeSkeletonOption();
            bakeOption.prefab = gameObject;
            bakeOption.sampleFPS = sampleFPS;
            bakeOption.outputFolder = outputFolder;
            bakeOption.useRGBA32Texture = useRGBA32Texture;
            bakeOption.blendType = blendType;
            bakeOption.useAnimationBlending = useAnimationBlending;
            bakeOption.deleteBipBones = deleteBipBones;
            bakeOption.generatePrefab = generatePrefab;
            bakeOption.clearOutputFolder = clearOutputFolder;
            bakeOption.defaultRigidAnimShader = defaultRigidAnimShader;
            bakeOption.defaultSkinAnimShader = defaultSkinAnimShader;
            bakeOption.useSRPBatcher = useSRPBatcher;
            bakeOption.useSeparateCPUDrivenBoneTransformData = separateCPUDrivenBoneTransformData;
            return baker.Bake(bakeOption);
        }
    }
}

#endif