﻿ 



 
 

using UnityEngine;

//created by wzw at 2019/8/24

namespace TFW.Map
{
    public class ScaleInfo
    {
        public float cameraHeightWhenScaleIsBaseScale = 18;
        public float cameraBaseScale = 1.0f;
        public float cameraFOVWhenScaleIsBaseScale = 30;
        public float minimumCameraHeight = 0f;
        public float maximumCameraHeight = 18;
        //在basescale基础上增加的scale
        public float increasedScale = 0;
    }

    public class KeepScaleUpdater
    {
        public KeepScaleUpdater(KeepScaleConfig[] scaleConfigs)
        {
            mScaleInfos = new ScaleInfo[scaleConfigs.Length];
            for (int i = 0; i < mScaleInfos.Length; ++i)
            {
                if (scaleConfigs[i] != null)
                {
                    mScaleInfos[i] = new ScaleInfo();
                    mScaleInfos[i].cameraHeightWhenScaleIsBaseScale = scaleConfigs[i].cameraHeightWhenScaleIsBaseScale;
                    mScaleInfos[i].cameraBaseScale = scaleConfigs[i].cameraBaseScale;
                    mScaleInfos[i].cameraFOVWhenScaleIsBaseScale = scaleConfigs[i].cameraFovWhenScaleIsOne;
                    mScaleInfos[i].minimumCameraHeight = scaleConfigs[i].minimumCameraHeight;
                    mScaleInfos[i].maximumCameraHeight = scaleConfigs[i].maximumCameraHeight;
                }
            }
        }

        public virtual float UpdateObjectScaleAtHeight()
        {
            var camera = Map.currentMap.camera;
            float cameraHeight = camera.transform.position.y;
            var lastCameraHeight = MapCameraMgr.lastCameraHeight;
            for (int i = 0; i < mScaleInfos.Length; ++i)
            {
                if (mScaleInfos[i] != null)
                {
                    if (!((lastCameraHeight > mScaleInfos[i].maximumCameraHeight && cameraHeight > mScaleInfos[i].maximumCameraHeight) ||
                        (lastCameraHeight < mScaleInfos[i].minimumCameraHeight && cameraHeight < mScaleInfos[i].minimumCameraHeight)))
                    {
                        cameraHeight = Mathf.Clamp(cameraHeight, mScaleInfos[i].minimumCameraHeight, mScaleInfos[i].maximumCameraHeight);

                        mCurrentScaleFactor = CalculateObjectScale(i, cameraHeight);
                        return mCurrentScaleFactor;
                    }
                }
            }
            return 0;
        }

        protected float CalculateObjectScale(int idx, float cameraHeight)
        {
            float scale = GetScaleAtHeight(idx, cameraHeight);
            return scale;
        }

        float GetScaleAtHeight(int idx, float cameraHeight)
        {
            var camera = Map.currentMap.camera;
            float oldWidth = CalculateViewportWidth(mScaleInfos[idx].cameraFOVWhenScaleIsBaseScale);
            float fov = Map.currentMap.GetCameraFOVAtHeight(cameraHeight);
            float newWidth = CalculateViewportWidth(fov);
            float ratio = newWidth / oldWidth;

            float scaleFactor = mScaleInfos[idx].cameraBaseScale / mScaleInfos[idx].cameraHeightWhenScaleIsBaseScale;

            float t = (cameraHeight - mScaleInfos[idx].minimumCameraHeight) / (mScaleInfos[idx].maximumCameraHeight - mScaleInfos[idx].minimumCameraHeight);
            t = Utils.EaseInQuint(0, 1, t);
            float increasedScale = Mathf.Clamp01(t) * mScaleInfos[idx].increasedScale;
            //Debug.LogError($"t: {t}, increasedScale: {increasedScale}");

            return scaleFactor * cameraHeight * ratio * (1 + increasedScale);
        }

        float CalculateViewportWidth(float fov)
        {
            var camera = Map.currentMap.camera.firstCamera;
            float height = Mathf.Tan(fov * 0.5f * Mathf.Deg2Rad) * camera.nearClipPlane * 2.0f;
            float width = height * camera.aspect;
            return width;
        }

        public float currentScaleFactor { get { return mCurrentScaleFactor; } }

        ScaleInfo[] mScaleInfos;
        protected float mCurrentScaleFactor;
    }
}
