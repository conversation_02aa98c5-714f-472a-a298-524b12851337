﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveGlobalObstacles(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.GlobalObstacles, writer);

            writer.Write(VersionSetting.GlobalObstacleStructVersion);

            //-------------------version 1 start------------------------------
            var obstacleManager = Map.currentMap.data.globalObstacleManager;
            var obj = obstacleManager.GetObstacleView();
            Vector3[] vertices = new Vector3[0];
            int[] indices = new int[0];
            if (obj != null)
            {
                var filter = obj.GetComponent<MeshFilter>();
                var sharedMesh = filter.sharedMesh;
                vertices = sharedMesh.vertices;
                indices = sharedMesh.triangles;
            }

            Utils.WriteString(writer, obstacleManager.obstacleMaterialPath);
            int n = vertices.Length;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteVector3(writer, vertices[i]);
            }
            n = indices.Length;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                writer.Write(indices[i]);
            }
            //-------------------version 1 end------------------------------

            //-------------------version 2 start------------------------------
            int gridObstacleCount = 0;
            if (obstacleManager.obstacleGrids != null)
            {
                gridObstacleCount = obstacleManager.obstacleGrids.Length;
            }
            writer.Write(gridObstacleCount);
            if (obstacleManager.obstacleGrids != null)
            {
                writer.Write(obstacleManager.obstacleGrids);
            }
            writer.Write(obstacleManager.gridSize);
            //-------------------version 2 end------------------------------
        }
    }
}

#endif