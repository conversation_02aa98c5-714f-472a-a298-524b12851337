﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    class VaryingTileSizeTerrainLayerLODCreatorDialog : EditorWindow
    {
        class LODSetting
        {
            public LODSetting(float height, int tileCount, Material material)
            {
                this.height = height;
                this.tileCount = tileCount;
                this.material = material;
            }
            public float height;
            public int tileCount;
            public Material material;
        }

        void OnEnable()
        {
            var map = Map.currentMap;
            if (map == null)
            {
                return;
            }

            var layer = map.varyingTileSizeTerrainLayer;
            if (layer == null)
            {
                return;
            }
            var lodConfig = layer.lodConfig;
            int nLODs = lodConfig.lodConfigs.Length;
            if (nLODs == 1)
            {
                return;
            }

            mLODSettings = new LODSetting[nLODs - 1];
            for (int i = 0; i < mLODSettings.Length; ++i)
            {
                var lod = lodConfig.lodConfigs[i + 1];
                float cameraHeight = map.data.lodManager.GetCameraHeight(lod.changeZoom);
                mLODSettings[i] = new LODSetting(cameraHeight, lod.terrainLODTileCount, null);
            }

            mDefaultShader = MapModuleResourceMgr.LoadResource<Shader>(MapModule.defaultGroundBakingShader);
            mTexturePropertyName = MapModule.defaultGroundBakingShaderTexturePropertyName;
            mWrongLODOrientation = MapModule.wrongGroundLODOrientation;

            //检查prefab是否使用了顶点数量不为4的mesh,如果由,则默认Generate OBJ Mesh
            mGenerateTileMeshOBJ = false;
            var prefabs = layer.GetUsedTilePrefabs();
            for (int i = 0; i < prefabs.Count; ++i)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabs[i]);
                if (prefab != null)
                {
                    var filter = prefab.GetComponent<MeshFilter>();
                    if (filter != null)
                    {
                        var mesh = filter.sharedMesh;
                        if (mesh != null)
                        {
                            if (mesh.vertexCount != 4)
                            {
                                mGenerateTileMeshOBJ = true;
                                break;
                            }
                        }
                    }
                }
            }
        }

        void OnGUI()
        {
            if (Map.currentMap == null)
            {
                return;
            }

            if (mLODSettings == null)
            {
                EditorGUILayout.LabelField("Only 1 LOD in ground layer!");
                return;
            }

            EditorGUILayout.LabelField("Please remember these lod heights must match lod height in map editor!");

            mEnableFrontLayer = EditorGUILayout.ToggleLeft("Front Layer", mEnableFrontLayer);
            mEnableRiverLayer = EditorGUILayout.ToggleLeft("River Layer", mEnableRiverLayer);
            mEnableDecorationLayer = EditorGUILayout.ToggleLeft("Decoration Layer", mEnableDecorationLayer);
            mEnableDepthTexture = EditorGUILayout.ToggleLeft("Use Camera Depth Texture", mEnableDepthTexture);
            mGenerateTileMeshOBJ = EditorGUILayout.ToggleLeft(new GUIContent("Generate Tile Mesh OBJ File"), mGenerateTileMeshOBJ);
            mWrongLODOrientation = EditorGUILayout.ToggleLeft(new GUIContent("Wrong Ground LOD Orientation"), mWrongLODOrientation);

            mDefaultShader = EditorGUILayout.ObjectField("Ground LOD Baking Shader", mDefaultShader, typeof(Shader), false, null) as Shader;
            mTexturePropertyName = EditorGUILayout.TextField("Ground LOD Baking Shader Texture Property Name", mTexturePropertyName);
            mTextureSize = EditorGUILayout.IntField("Texture Size", mTextureSize);
            int lodCount = EditorGUILayout.IntField("Generated LOD Count", mLODSettings.Length);
            if (lodCount != mLODSettings.Length)
            {
                SetLODCount(lodCount);
            }

            for (int i = 0; i < lodCount; ++i)
            {
                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.BeginHorizontal("GroupBox");
                mLODSettings[i].height = EditorGUILayout.FloatField("Camera Height", mLODSettings[i].height);
                mLODSettings[i].tileCount = EditorGUILayout.IntField("Horizontal Resolution", mLODSettings[i].tileCount);
                EditorGUILayout.EndHorizontal();
                if (i == lodCount - 1 && mLODSettings[i].tileCount == 1)
                {
                    mLODSettings[i].material = EditorGUILayout.ObjectField("Material", mLODSettings[i].material, typeof(Material), false, null) as Material;
                }
                else
                {
                    mLODSettings[i].material = null;
                }
                EditorGUILayout.EndVertical();
            }

            if (GUILayout.Button("Generate"))
            {
                GenerateLODs();
            }
        }

        bool CheckValidation()
        {
            if (mDefaultShader == null)
            {
                EditorUtility.DisplayDialog("Error", "Invalid shader", "OK");
                return false;
            }

            if (!Utils.IsPOT(mTextureSize) || mTextureSize <= 0)
            {
                EditorUtility.DisplayDialog("Error", "Invalid texture size", "OK");
                return false;
            }

            if (mLODSettings.Length == 0)
            {
                return false;
            }

            var terrainLayer = Map.currentMap.varyingTileSizeTerrainLayer;
            if (terrainLayer.horizontalTileCount != terrainLayer.verticalTileCount)
            {
                return false;
            }

            int totalTileCount = terrainLayer.horizontalTileCount;
            for (int i = 0; i < mLODSettings.Length; ++i)
            {
                if (mLODSettings[i].tileCount <= 0)
                {
                    EditorUtility.DisplayDialog("Error", "Invalid lod tile count", "OK");
                    return false;
                }

                if (i != mLODSettings.Length - 1)
                {
                    //高一级lod的tile数必须比低级lod的tile数少或相等
                    if (mLODSettings[i].tileCount < mLODSettings[i + 1].tileCount)
                    {
                        EditorUtility.DisplayDialog("Error", "Invalid lod tile count", "OK");
                        return false;
                    }
                }
            }

            return true;
        }

        void GenerateLODs()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                bool isValid = CheckValidation();
                if (!map.isEditorMode && isValid)
                {
                    var r = new VaryingTileSizeRenderTextureTerrainLODDataCreator();
                    var layer = map.varyingTileSizeTerrainLayer;
                    VaryingTileSizeTerrainRenderTextureLODSetting[] lods = CreateLODSettings();

                    if (layer != null)
                    {
                        var mapCamera = map.camera.transform.parent.Find("MapCameraWithoutPostEffect");
                        if (mapCamera == null)
                        {
                            mapCamera = map.camera.transform;
                        }
                        var camera = mapCamera.GetComponent<Camera>();
                        var originalMode = camera.depthTextureMode;
                        if (mEnableDepthTexture)
                        {
                            camera.depthTextureMode = DepthTextureMode.Depth;
                        }
                        Debug.Assert(camera != null, "LOD Camera Not Found!");
                        string exportFolder = Map.currentMap.dataFolder;

                        TextureMapLayerMask mask = TextureMapLayerMask.None;
                        if (mEnableDecorationLayer)
                        {
                            mask |= TextureMapLayerMask.DecorationLayer;
                        }
                        if (mEnableFrontLayer)
                        {
                            mask |= TextureMapLayerMask.FrontLayer;
                        }
                        if (mEnableRiverLayer)
                        {
                            mask |= TextureMapLayerMask.RiverLayer;
                        }

                        StopWatchWrapper w = new StopWatchWrapper();
                        w.Start();

                        r.CreateLODDatas(layer, lods, exportFolder + "/Res", camera, mTextureSize, mask, mDefaultShader, mTexturePropertyName, mGenerateTileMeshOBJ, mWrongLODOrientation);

                        double d = w.Stop();
                        Debug.Log($"Create Terrain LOD Cost {d}");

                        camera.depthTextureMode = originalMode;
                        Close();
                    }
                }
            }
        }

        VaryingTileSizeTerrainRenderTextureLODSetting[] CreateLODSettings()
        {
            var terrainLayer = Map.currentMap.varyingTileSizeTerrainLayer;
            int mapTileCount = terrainLayer.horizontalTileCount;
            VaryingTileSizeTerrainRenderTextureLODSetting[] results = new VaryingTileSizeTerrainRenderTextureLODSetting[mLODSettings.Length];
            for (int i = 0; i < mLODSettings.Length; ++i)
            {
                results[i] = new VaryingTileSizeTerrainRenderTextureLODSetting(mLODSettings[i].height, mapTileCount / mLODSettings[i].tileCount, mLODSettings[i].material);
                mapTileCount = mLODSettings[i].tileCount;
            }
            return results;
        }

        void SetLODCount(int count)
        {
            int minCount = Mathf.Min(count, mLODSettings.Length);
            LODSetting[] newSettings = new LODSetting[count];
            for (int i = 0; i < minCount; ++i)
            {
                newSettings[i] = mLODSettings[i];
            }

            for (int i = minCount; i < count; ++i)
            {
                newSettings[i] = new LODSetting(0, 0, null);
            }

            mLODSettings = newSettings;
        }

        int mTextureSize = 1024;
        LODSetting[] mLODSettings;
        bool mEnableFrontLayer = false;
        bool mEnableDecorationLayer = false;
        bool mEnableRiverLayer = false;
        bool mEnableDepthTexture = false;
        bool mGenerateTileMeshOBJ = false;
        bool mWrongLODOrientation = false;
        Shader mDefaultShader;
        string mTexturePropertyName;
    }
}
#endif