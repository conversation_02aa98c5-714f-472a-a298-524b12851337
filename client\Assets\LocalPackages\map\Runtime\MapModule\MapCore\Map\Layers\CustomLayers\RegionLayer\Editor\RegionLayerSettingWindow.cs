﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class RegionLayerSettingWindow : EditorWindow
    {
        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Layer Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Layer Height", mLayerHeight);
            GUILayout.EndHorizontal();

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Create"))
            {
                var map = Map.currentMap as EditorMap;
                bool valid = CheckParameter();
                if (valid)
                {
                    var layer = map.CreateRegionLayer(MapCoreDef.MAP_LAYER_NODE_REGION, mLayerWidth, mLayerHeight);

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0)
            {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
    }
}

#endif