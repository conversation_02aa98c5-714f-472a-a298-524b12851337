﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class BlendTerrainLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.BlendTerrainLayerEditorDataVersion);

            var editorMapData = Map.currentMap.data as EditorMapData;
            //save prefab manager
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            PrepareSaving();
            SaveSetting(writer);
            SavePrefabManager(writer, prefabManager);
            SaveLayerData(writer);
            SavePathMapper(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();

            SaveRenderTextures();
        }

        void SaveSetting(BinaryWriter writer)
        {
            mPathMapperPosition = writer.BaseStream.Position;
            long pathMapperOffsetPlaceholder = 0;
            writer.Write(pathMapperOffsetPlaceholder);
        }

        void SaveLayerData(BinaryWriter writer)
        {
            int cols = horizontalTileCount;
            int rows = verticalTileCount;

            var terrainLayerData = layerData as BlendTerrainLayerData;
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(terrainLayerData.useGeneratedLOD);
            writer.Write(terrainLayerData.useDecorationObject);
            writer.Write(false/*terrainLayerData.useCombinedTiles*/);
            writer.Write(1/*terrainLayerData.combinedHorizontalTileCount*/);
            writer.Write(1/*terrainLayerData.combinedVerticalTileCount*/);
            Utils.WriteString(writer, ""/*materialGuid*/);
            Utils.WriteString(writer, terrainLayerData.combinedTexturePropertyName);
            string groundTileAtlasSettingGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(terrainLayerData.groundTileAtlasSetting));
            Utils.WriteString(writer, groundTileAtlasSettingGuid);
            //Utils.WriteString(writer, AssetDatabase.GetAssetPath(terrainLayerData.combineTileShader));
            //writer.Write(terrainLayerData.minimumTileCountInRectangle);
            writer.Write(terrainLayerData.generateMeshCollider);
            writer.Write(terrainLayerData.getGroundHeightInGame);
            writer.Write(terrainLayerData.optimizeMesh);
            Utils.WriteVector3(writer, mLayerView.root.transform.position);

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var data = GetTile(j, i);
                    bool validTile = data != null;
                    writer.Write(validTile);
                    if (validTile)
                    {
                        SaveBlendTerrainTileData(writer, data);
                    }
                }
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layerData);
        }

        void SaveBlendTerrainTileData(BinaryWriter writer, BlendTerrainTileData tileData)
        {
            short prefabPathIndex = GetPrefabPathIndex(tileData.GetModelTemplate());
            writer.Write(prefabPathIndex);
            writer.Write(tileData.type);
            writer.Write(tileData.index);
            writer.Write(tileData.subTypeIndex);
            Utils.WriteFloatArray(writer, tileData.heights);
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
                int flag = 0;
                writer.Write(flag);
                Utils.WriteString(writer, "");
                writer.Write(0);
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                    writer.Write((int)c.flag);
                    Utils.WriteString(writer, c.name);
                    writer.Write(c.terrainLODTileCount);
                }
            }
        }

        void SavePrefabManager(BinaryWriter writer, PrefabManager prefabManager)
        {
            writer.Write(prefabManager.nextGroupID);
            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                SavePrefabGroup(writer, group);
            }
        }

        void SavePrefabGroup(BinaryWriter writer, PrefabGroup group)
        {
            writer.Write(group.groupID);
            Utils.WriteString(writer, group.name);
            Utils.WriteColor32(writer, group.color);
            writer.Write(group.addPrefabSet);
            int n = group.count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteString(writer, mPathMapper.Map(group.GetPrefabPath(i)));
                //save subgroup prefab paths
                var subgroupPrefabs = group.GetSubGroupPrefabPaths(i);
                int subGroupPrefabCount = subgroupPrefabs == null ? 0 : subgroupPrefabs.Length;
                writer.Write(subGroupPrefabCount);
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    Utils.WriteString(writer, mPathMapper.Map(subgroupPrefabs[k]));
                }

                //save decoration prefab path
                var decorationPrefabInfo = group.GetDecorationPrefabInfo(i);
                int prefabCount = 0;
                if (decorationPrefabInfo != null)
                {
                    prefabCount = decorationPrefabInfo.prefabGUIDs.Count;
                }
                writer.Write(prefabCount);
                for (int k = 0; k < prefabCount; ++k)
                {
                    Utils.WriteString(writer, decorationPrefabInfo.prefabGUIDs[k]);
                }
            }
        }

        short GetPrefabPathIndex(ModelTemplate modelTemplate)
        {
            if (modelTemplate == null)
            {
                return -1;
            }
            short index;
            bool found = mModelTemplateIDToPrefabPathIndex.TryGetValue(modelTemplate.id, out index);
            if (!found)
            {
                mPrefabPathStringTable.Add(mPathMapper.Map(modelTemplate.GetLODPrefabPath(0)));
                index = (short)(mPrefabPathStringTable.Count - 1);
                mModelTemplateIDToPrefabPathIndex[modelTemplate.id] = index;
            }
            return index;
        }

        void SavePathMapper(BinaryWriter writer)
        {
            long position = writer.BaseStream.Position;
            //跳转到offset placeholder的地方,写下PathMapper数据的地址
            writer.BaseStream.Position = mPathMapperPosition;
            writer.Write(position);
            writer.BaseStream.Position = position;

            int n = mPathMapper.pathToGuid.Count;
            writer.Write(n);

            foreach (var p in mPathMapper.pathToGuid)
            {
                Utils.WriteString(writer, p.Key);
                Utils.WriteString(writer, p.Value);
            }

            //save string table
            int pathCount = mPrefabPathStringTable.Count;
            writer.Write(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                Utils.WriteString(writer, mPrefabPathStringTable[i]);
            }
        }

        void PrepareSaving()
        {
            mPathMapper = new PathMapper();
            mModelTemplateIDToPrefabPathIndex = new Dictionary<int, short>();
            mPrefabPathStringTable = new List<string>();
        }

        public bool CheckIfUseTerrainHeight()
        {
            int h = horizontalTileCount;
            int v = verticalTileCount;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    var tile = GetTile(j, i);
                    if (tile != null && tile.heights != null)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public void ProcessBeforeExport()
        {
            GenerateHeightMeshies();
        }

        void GenerateHeightMeshies()
        {
            string folder = MapCoreDef.GetFullTerrainMeshFolderPath(SLGMakerEditor.instance.exportFolder);
            FileUtil.DeleteFileOrDirectory(folder);
            Directory.CreateDirectory(folder);

            int h = horizontalTileCount;
            int v = verticalTileCount;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    var tile = GetTile(j, i);
                    if (tile != null && tile.heights != null)
                    {
                        var mesh = mLayerView.GetTerrainTileMesh(j, i);
                        CreateTileHeightMesh(j, i, mesh, !mLayerData.getGroundHeightInGame && mLayerData.optimizeMesh);
                    }
                }
            }

            AssetDatabase.Refresh();
        }

        void CreateTileHeightMesh(int x, int y, Mesh mesh, bool optimizeMesh)
        {
#if UNITY_EDITOR_WIN
            var f = new FixTerrainEdgeNormal();
            f.Fix(this);
#endif

            var localMesh = Object.Instantiate<Mesh>(mesh);
            if (optimizeMesh)
            {
                OptimizeMesh(localMesh, x, y);
            }
            var path = MapCoreDef.GetTerrainMeshPath(SLGMakerEditor.instance.exportFolder, x, y);
            AssetDatabase.CreateAsset(localMesh, path);
        }

        void OptimizeMesh(Mesh mesh, int x, int y)
        {
            var tile = mLayerData.GetTile(x, y);
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            int resolution = tile.resolution;
            float blockSize = mLayerData.tileWidth / resolution;
            var connectedTiles = MergeRects.ProcessTiles(resolution, resolution, blockSize,
                                (int bx, int by) =>
                                {
                                    int v0 = by * (resolution + 1) + bx;
                                    int v1 = v0 + 1;
                                    int v2 = v0 + resolution + 1;
                                    int v3 = v2 + 1;
                                    bool valid = vertices[v0].y == 0 &&
                                    vertices[v1].y == 0 &&
                                    vertices[v2].y == 0 &&
                                    vertices[v3].y == 0;

                                    return valid;
                                },
                                (int bx, int by) => { return new Vector3(bx * blockSize, 0, by * blockSize); },
                                (int bx, int by) => { return new Vector3((bx + 0.5f) * blockSize, 0, (by + 0.5f) * blockSize); }
                                );

            if (connectedTiles.Count > 0)
            {
                List<Vector3> optimizedVertices = new List<Vector3>();
                List<Vector3> optimizedNormals = new List<Vector3>();
                List<int> optimizedIndices = new List<int>();

                //计算哪些tile是有高度的
                bool[,] removedTiles = new bool[resolution, resolution];
                for (int k = 0; k < connectedTiles.Count; ++k)
                {
                    var tiles = connectedTiles[k].tiles;
                    int rows = tiles.GetLength(0);
                    int cols = tiles.GetLength(1);
                    var rects = connectedTiles[k].rectangles;
                    var bounds = connectedTiles[k].bounds;
                    for (int i = 0; i < rows; ++i)
                    {
                        for (int j = 0; j < cols; ++j)
                        {
                            if (tiles[i, j] == 1)
                            {
                                int gx = bounds.minX + j;
                                int gy = bounds.minY + i;
                                removedTiles[gy, gx] = true;
                            }
                        }
                    }

                }

                //add not optimized vertices
                for (int i = 0; i < resolution; ++i)
                {
                    for (int j = 0; j < resolution; ++j)
                    {
                        if (!removedTiles[i, j])
                        {
                            AddQuad(j, i, blockSize, resolution, vertices, normals, optimizedVertices, optimizedNormals, optimizedIndices);
                        }
                    }
                }
                for (int k = 0; k < connectedTiles.Count; ++k)
                {
                    var rects = connectedTiles[k].rectangles;
                    var bounds = connectedTiles[k].bounds;
                    //add optimized vertices
                    foreach (var r in rects)
                    {
                        int gMinX = r.minX + bounds.minX;
                        int gMinY = r.minY + bounds.minY;
                        int gMaxX = r.maxX + bounds.minX;
                        int gMaxY = r.maxY + bounds.minY;
                        AddOptimizedQuad(gMinX, gMinY, gMaxX, gMaxY, blockSize, optimizedVertices, optimizedNormals, optimizedIndices);
                    }
                }


                mesh.Clear();
                mesh.SetVertices(optimizedVertices);
                mesh.uv = CalculateOptimizedUVs(optimizedVertices, mLayerData.tileWidth, mLayerData.tileHeight, tile.uvs);
                mesh.SetIndices(optimizedIndices, MeshTopology.Triangles, 0);
                //mesh.RecalculateNormals();
                mesh.RecalculateBounds();

                //fix edge vertex normals
#if true
                Vector2Int[] neighbourOffset = new Vector2Int[4]
                {
                    new Vector2Int(-1, 0),
                    new Vector2Int(0, 1),
                    new Vector2Int(1, 0),
                    new Vector2Int(0, -1),
                };
                Vector2Int[] vertexOffset = new Vector2Int[4]
                {
                    new Vector2Int(0, 0),
                    new Vector2Int(0, 1),
                    new Vector2Int(1, 1),
                    new Vector2Int(1, 0),
                };
                for (int i = 0; i < resolution; ++i)
                {
                    for (int j = 0; j < resolution; ++j)
                    {
                        if (!removedTiles[i, j])
                        {
                            //check if tile is edge tile
                            bool isEdgeTile = false;
                            for (int p = 0; p < 4; ++p)
                            {
                                int nx = j + neighbourOffset[p].x;
                                int ny = i + neighbourOffset[p].y;
                                if (nx >= 0 && nx < resolution && ny >= 0 && ny < resolution)
                                {
                                    if (removedTiles[ny, nx])
                                    {
                                        isEdgeTile = true;
                                        break;
                                    }
                                }
                            }

                            if (isEdgeTile)
                            {
                                for (int p = 0; p < 4; ++p)
                                {
                                    Vector3 v = new Vector3((j + vertexOffset[p].x) * blockSize, 0, (i + vertexOffset[p].y) * blockSize);
                                    int idx = Utils.FindVertexXZ(optimizedVertices, v, 0.001f);
                                    if (optimizedVertices[idx].y == 0 && !Utils.Approximately(optimizedNormals[idx], Vector3.up, 0.001f))
                                    {
                                        //只有edge tile并且顶点高度为0的顶点normal才设置为0
                                        optimizedNormals[idx] = Vector3.up;
                                    }
                                }
                            }
                        }
                    }
                }
#endif
                mesh.SetNormals(optimizedNormals);
            }
#if false
            var obj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            obj.GetComponent<MeshFilter>().sharedMesh = mesh;
            for (int i = 0; i < connectedTiles.Count; ++i)
            {
                foreach (var r in connectedTiles[i].rectangles) {
                    int gMinX = r.minX + connectedTiles[i].bounds.minX;
                    int gMinY = r.minY + connectedTiles[i].bounds.mGetinY;
                    int gMaxX = r.maxX + connectedTiles[i].bounds.minX;
                    int gMaxY = r.maxY + connectedTiles[i].bounds.minY;
                    Vector3 minPos = new Vector3(gMinX * blockSize, 0, gMinY * blockSize);
                    Vector3 maxPos = new Vector3((gMaxX + 1) * blockSize, 0, (gMaxY + 1) * blockSize);
                    var dpObj = new GameObject(i.ToString());
                    var dp = dpObj.AddComponent<DrawBounds>();
                    dp.bounds.SetMinMax(minPos, maxPos);
                }
            }
#endif
        }

        void AddQuad(int x, int y, float gridSize, int resolution, Vector3[] originalVertices, Vector3[] originalNormals, List<Vector3> vertices, List<Vector3> newNormals, List<int> indices)
        {
            int oi0 = y * (resolution + 1) + x;
            int oi1 = oi0 + 1;
            int oi2 = oi0 + resolution + 1;
            int oi3 = oi2 + 1;

            Vector3 v0 = new Vector3(x * gridSize, originalVertices[oi0].y, y * gridSize);
            Vector3 v1 = new Vector3((x + 1) * gridSize, originalVertices[oi1].y, y * gridSize);
            Vector3 v2 = new Vector3(x * gridSize, originalVertices[oi2].y, (y + 1) * gridSize);
            Vector3 v3 = new Vector3((x + 1) * gridSize, originalVertices[oi3].y, (y + 1) * gridSize);
            float esp = 0.001f;
            bool added;
            var i0 = Utils.FindOrAddVertex(vertices, v0, esp, out added);
            if (added)
            {
                newNormals.Add(originalNormals[oi0]);
            }
            else
            {
                newNormals[i0] = originalNormals[oi0];
            }
            var i1 = Utils.FindOrAddVertex(vertices, v1, esp, out added);
            if (added)
            {
                newNormals.Add(originalNormals[oi1]);
            }
            else
            {
                newNormals[i1] = originalNormals[oi1];
            }
            var i2 = Utils.FindOrAddVertex(vertices, v2, esp, out added);
            if (added)
            {
                newNormals.Add(originalNormals[oi2]);
            }
            else
            {
                newNormals[i2] = originalNormals[oi2];
            }
            var i3 = Utils.FindOrAddVertex(vertices, v3, esp, out added);
            if (added)
            {
                newNormals.Add(originalNormals[oi3]);
            }
            else
            {
                newNormals[i3] = originalNormals[oi3];
            }

            indices.Add(i0);
            indices.Add(i2);
            indices.Add(i3);
            indices.Add(i0);
            indices.Add(i3);
            indices.Add(i1);
        }

        void AddOptimizedQuad(int minX, int minY, int maxX, int maxY, float gridSize, List<Vector3> vertices, List<Vector3> normals, List<int> indices)
        {
            Vector3 v0 = new Vector3(minX * gridSize, 0, minY * gridSize);
            Vector3 v1 = new Vector3((maxX + 1) * gridSize, 0, minY * gridSize);
            Vector3 v2 = new Vector3(minX * gridSize, 0, (maxY + 1) * gridSize);
            Vector3 v3 = new Vector3((maxX + 1) * gridSize, 0, (maxY + 1) * gridSize);
            float esp = 0.001f;
            bool added;
            var i0 = Utils.FindOrAddVertex(vertices, v0, esp, out added);
            if (added)
            {
                normals.Add(Vector3.up);
            }
            else
            {
                normals[i0] = Vector3.up;
            }
            var i1 = Utils.FindOrAddVertex(vertices, v1, esp, out added);
            if (added)
            {
                normals.Add(Vector3.up);
            }
            else
            {
                normals[i1] = Vector3.up;
            }
            var i2 = Utils.FindOrAddVertex(vertices, v2, esp, out added);
            if (added)
            {
                normals.Add(Vector3.up);
            }
            else
            {
                normals[i2] = Vector3.up;
            }
            var i3 = Utils.FindOrAddVertex(vertices, v3, esp, out added);
            if (added)
            {
                normals.Add(Vector3.up);
            }
            else
            {
                normals[i3] = Vector3.up;
            }
            indices.Add(i0);
            indices.Add(i2);
            indices.Add(i3);
            indices.Add(i0);
            indices.Add(i3);
            indices.Add(i1);
        }

        Vector2 InterpolateUV(Vector2[] cornerUVs, float rx, float ry)
        {
            Vector2 lBottom = Vector2.Lerp(cornerUVs[0], cornerUVs[1], rx);
            Vector2 lTop = Vector2.Lerp(cornerUVs[2], cornerUVs[3], rx);
            Vector2 final = Vector2.Lerp(lBottom, lTop, ry);
            return final;
        }

        Vector2[] CalculateOptimizedUVs(List<Vector3> vertices, float tileWidth, float tileHeight, Vector2[] tileUVs)
        {
            Vector2[] uvs = new Vector2[vertices.Count];
            for (int i = 0; i < uvs.Length; ++i)
            {
                float rx = vertices[i].x / tileWidth;
                float ry = vertices[i].z / tileHeight;
                uvs[i] = InterpolateUV(tileUVs, rx, ry);
            }
            return uvs;
        }

        PathMapper mPathMapper;
        long mPathMapperPosition;
        Dictionary<int, short> mModelTemplateIDToPrefabPathIndex;
        List<string> mPrefabPathStringTable;
    }
}
#endif