﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadSetting(BinaryReader reader)
        {
            //跳转到section数据起始位置
            reader.BaseStream.Position = GetSectionDataStartPosition(MapDataSectionType.Setting);
            var version = reader.ReadInt32();

            //------------version 1 start------------------
            mEditorData.setting.cameraMoveRange = Utils.ReadVector2(reader);
            mEditorData.setting.dataFolder = Utils.ReadString(reader);

            mEditorData.map.width = reader.ReadSingle();
            mEditorData.map.height = reader.ReadSingle();
            mEditorData.map.borderHeight = reader.ReadSingle();
            mEditorData.map.isCircle = reader.ReadBoolean();

            LoadMapLODConfigV1(reader);
            //------------version 1 end------------------
            //------------version 2 start------------------
            if (version >= 2)
            {
                mEditorData.map.backExtendedSize = reader.ReadSingle();
            }
            //------------version 2 end------------------
            //------------version 3 start------------------
            if (version >= 3)
            {
                mEditorData.map.farClipOffset = reader.ReadSingle();
            }
            //------------version 3 end------------------
            //------------version 4 start------------------
            if (version >= 4)
            {
                mEditorData.map.useTerrainHeight = reader.ReadBoolean();
            }
            //------------version 4 end------------------
            //------------version 5 start------------------
            if (version >= 5)
            {
                mEditorData.map.groundTileSize = reader.ReadSingle();
                mEditorData.map.frontTileSize = reader.ReadSingle();
            }
            //------------version 5 end------------------
            //------------version 6 start------------------
            if (version >= 6)
            {
                mEditorData.map.maxCameraColliderHeight = reader.ReadSingle();
            }
            //------------version 6 end------------------
        }

        //only for version 1
        void LoadMapLODConfigV1(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.map.lodConfig.lods = new config.MapLOD[n];
            for (int i = 0; i < n; ++i)
            {
                mEditorData.map.lodConfig.lods[i] = new config.MapLOD();
                mEditorData.map.lodConfig.lods[i].cameraHeight = reader.ReadSingle();
            }
        }
    }
}
