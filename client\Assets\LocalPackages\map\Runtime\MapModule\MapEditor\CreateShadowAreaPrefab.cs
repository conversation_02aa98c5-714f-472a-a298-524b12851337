﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class CreateShadowAreaPrefab
    {
        class ChildInfo
        {
            public int id;
            public Vector3 position;
            public string prefabPath;
            public float size;
        }

        public static void Create(string configFilePath, string outputPrefabPath, int mapWidth, int mapHeight)
        {
            bool suc = TSVReader.Load(configFilePath);
            if (suc)
            {
                List<ChildInfo> children = new List<ChildInfo>();

                int divider = 1000;
                var rows = TSVReader.rows;
                for (int i = 0; i < rows.Count; ++i)
                {
                    ChildInfo info = new ChildInfo();

                    info.id = (int)TSVReader.GetInt(i, "id", out _);
                    info.size = (int)TSVReader.GetInt(i, "land_border", out _) / divider;
                    Vector2Int coord = TSVReader.GetCoord(i, "Coord_position", out _);
                    coord.x /= divider;
                    coord.y /= divider;
                    info.position = new Vector3(coord.x, 0, coord.y) - new Vector3(mapWidth * 0.5f, 0, mapHeight * 0.5f);
                    info.prefabPath = TSVReader.GetString(i, "res", out _);
                    children.Add(info);
                }

                CreatePrefab(children, outputPrefabPath);
            }
        }

        static void CreatePrefab(List<ChildInfo> children, string outputPrefabPath)
        {
            string name = "AllShadowGrounds_lod0";
            var root = new GameObject(name);
            for (int i = 0; i < children.Count; ++i)
            {
                string path = $"{children[i].prefabPath}.prefab";
                var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                var prefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(childPrefab, root.transform);
                prefabInstance.tag = MapCoreDef.MAP_SPECIAL_AREA_TAG;
                prefabInstance.transform.position = children[i].position;
                var config = prefabInstance.AddComponent<SpecialAreaConfig>();
                config.id = children[i].id;
                config.width = children[i].size;
                config.height = children[i].size;
                config.shape = SpecialAreaShape.Rectangle;
            }

            PrefabUtility.SaveAsPrefabAsset(root, $"{outputPrefabPath}/{name}.prefab");
            Utils.DestroyObject(root);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }
    }
}


#endif