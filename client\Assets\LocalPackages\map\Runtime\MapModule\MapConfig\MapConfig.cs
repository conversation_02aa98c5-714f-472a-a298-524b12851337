﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

namespace TFW.Map
{
    public class MapConfig
    {
        //clampBorder:是否相机在高度改变时使用曲线来限制相机的可移动范围
        public MapConfig(string mapResDir, string configResDir, string intuitiveZoomConfigFilePath, IMapResourceMgr resMgr, IMapTouchManager touchManager, NavigationCreateMode navMeshCreateMode, bool useOnly15Prefab, float defaultFrontTileSize, float defaultGroundTileSize, float defaultNPCRegionSize, bool usePrefabOutline, bool displayRailwayLayer, bool displayCircleBorderLayer, bool cameraClampBorder, bool cameraCheckCircleBorderCollision, float cameraBorderColliderRadius, bool showHorde, float obstacleGridSize, NavigationCreateMode defaultGlobalObstacleCreationMode, bool showDemoFunction, List<string> cameraSettingFilePaths, bool enableCameraCollision, string defaultGroundBakingShader, string defaultObstacleMaterial, string brushFolderPath, bool useNewCameraHeightAutoUpdateAlgorithm, bool useFakePrefab, string intuitiveConfigUnitName, string groundTileMakerDefaultRuntimeAssetFolder, string groundTileMakerDefaultEditorAssetFolder, int frameActionMaxExecuteTime, bool doNotExportFrontLayerModelTemplates, string mapListDir, float defaultSplitFogTileSize, string bakedAnimDataPath, string groundTileAtlasInfoDir, string defaultTerritoryMaterialPath, bool enableUpdateFarClipPlane, bool is2DGame, string projectName, int groundTileMakerDefaultTileSize, int groundTileMakerDefaultMaskTextureSize, string groundTileMakerDefaultMaskTextureName, bool groundTileMakerDefaultInitChannelData, bool groundTileMakerDefaultNormalizeColor, string groundTileMakerDefaultMaterialPath, bool useMapLargeTileForDecorationObject, string defaultGroundBakingShaderTexturePropertyName, bool wrongGroundLODOrientation, bool preloadGroundLayerTile, string pluginList, bool useNewConfigFormat, bool hideWallWhenMainBuildingAtFinalPosition, bool generateRiverMaterial, string rotatedUVMeshPath, bool supportGroundHeightChange, bool useCameraShakeByDistance, bool enableResizeMap, bool enableUpdateNearClipPlane, string mapGlobalSettingPath)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapResDir), $"Invalid runtime map data directory {mapResDir}");
            Debug.Assert(resMgr != null, "invalid map resource manager");
            Debug.Assert(touchManager != null, "invalid map touch manager");
            Debug.Assert(cameraSettingFilePaths.Count > 0, "invalid camera setting list");

            this.groundTileMakerDefaultInitChannelData = groundTileMakerDefaultInitChannelData;
            this.groundTileMakerDefaultMaskTextureName = groundTileMakerDefaultMaskTextureName;
            this.groundTileMakerDefaultMaskTextureSize = groundTileMakerDefaultMaskTextureSize;
            this.groundTileMakerDefaultNormalizeColor = groundTileMakerDefaultNormalizeColor;
            this.groundTileMakerDefaultTileSize = groundTileMakerDefaultTileSize;
            this.runtimeMapResDirectory = mapResDir;
            this.resourceManagerImpl = resMgr;
            this.touchManagerImpl = touchManager;
            this.defaultNavmeshCreationMode = navMeshCreateMode;
            this.defaultGlobalObstacleCreationMode = defaultGlobalObstacleCreationMode;
            this.useOnly15Prefab = useOnly15Prefab;
            this.defaultFrontTileSize = defaultFrontTileSize;
            this.defaultGroundTileSize = defaultGroundTileSize;
            this.defaultNPCRegionSize = defaultNPCRegionSize;
            this.configResDir = configResDir;
            this.intuitiveZoomConfigFilePath = intuitiveZoomConfigFilePath;
            this.usePrefabOutline = usePrefabOutline;
            this.defaultSplitFogTileSize = defaultSplitFogTileSize;
            this.displayCircleBorderLayer = displayCircleBorderLayer;
            this.displayRailwayLayer = displayRailwayLayer;
            this.cameraClampBorder = cameraClampBorder;
            this.cameraCheckCircleBorderCollision = cameraCheckCircleBorderCollision;
            this.showHorde = showHorde;
            this.obstacleGridSize = obstacleGridSize;
            this.showDemoFunction = showDemoFunction;
            this.cameraSettingFilePaths = cameraSettingFilePaths;
            this.cameraBorderColliderRadius = cameraBorderColliderRadius;
            this.enableCameraCollision = enableCameraCollision;
            this.defaultGroundBakingShader = defaultGroundBakingShader;
            this.defaultObstacleMaterial = defaultObstacleMaterial;
            this.brushFolderPath = brushFolderPath;
            this.useNewCameraHeightAutoUpdateAlgorithm = useNewCameraHeightAutoUpdateAlgorithm;
            this.intuitiveConfigUnitName = intuitiveConfigUnitName;
            this.groundTileMakerDefaultRuntimeAssetFolder = groundTileMakerDefaultRuntimeAssetFolder;
            this.groundTileMakerDefaultEditorAssetFolder = groundTileMakerDefaultEditorAssetFolder;
            this.useFakePrefab = useFakePrefab;
            this.frameActionMaxExecuteTime = frameActionMaxExecuteTime;
            this.doNotExportFrontLayerModelTemplates = doNotExportFrontLayerModelTemplates;
            this.mapListDir = mapListDir;
            this.bakedAnimDataPath = bakedAnimDataPath;
            this.groundTileAtlasInfoDir = groundTileAtlasInfoDir;
            this.defaultTerritoryMaterialPath = defaultTerritoryMaterialPath;
            this.enableUpdateFarClipPlane = enableUpdateFarClipPlane;
            this.is2DGame = is2DGame;
			this.projectName = projectName;
            this.groundTileMakerDefaultMaterialPath = groundTileMakerDefaultMaterialPath;
            this.useMapLargeTileForDecorationLayer = useMapLargeTileForDecorationObject;
            this.defaultGroundBakingShaderTexturePropertyName = defaultGroundBakingShaderTexturePropertyName;
            this.wrongGroundLODOrientation = wrongGroundLODOrientation;
            this.preloadGroundLayerTile = preloadGroundLayerTile;
            this.pluginList = pluginList;
            this.useNewConfigFormat = useNewConfigFormat;
            this.hideWallWhenMainBuildingAtFinalPosition = hideWallWhenMainBuildingAtFinalPosition;
            this.generateRiverMaterial = generateRiverMaterial;
            this.rotatedUVMeshPath = rotatedUVMeshPath;
            this.supportGroundHeightChange = supportGroundHeightChange;
            this.useCameraShakeByDistance = useCameraShakeByDistance;
            this.enableResizeMap = enableResizeMap;
            this.enableUpdateNearClipPlane = enableUpdateNearClipPlane;
            if (!string.IsNullOrEmpty(mapGlobalSettingPath))
            {
                mapGlobalSetting = resMgr.LoadResource<MapGlobalSetting>(mapGlobalSettingPath);
            }
        }

        public static async UniTask<MapConfig> CreateFromFileAsync(string configFilePath, IMapResourceMgr resMgr, IMapTouchManager touchManager)
        {
            MapConfig config = null;
            var textContent = await resMgr.LoadTextStringAsync(configFilePath);

            if (!string.IsNullOrEmpty(textContent))
            {
                string configData = textContent;
                var configDict = JSONParser.Deserialize(configData) as Dictionary<string, object>;
                string mapResDir = configDict[MapCoreDef.MAP_DIR_NAME] as string;
                string configResDir = configDict[MapCoreDef.MAP_CONFIG_DIR_NAME] as string;
                string intuitiveZoomFilePath = configDict[MapCoreDef.MAP_INTUITIVE_ZOOM_FILE_NAME] as string;
                NavigationCreateMode navMode = MapCoreDef.GetNavigationMode(configDict[MapCoreDef.MAP_NAVIGATION_MODE_NAME] as string);
                NavigationCreateMode globalObstacleMode = MapCoreDef.GetObstacleMode(configDict[MapCoreDef.MAP_GLOBAL_OBSTACLE_MODE_NAME] as string);
                bool useOnly15TerrainPrefab = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_15_GROUND_PREFAB_NAME, true);
                bool usePrefabOutline = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_PREFAB_OUTLINE_NAME, false);
                float frontTileSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_FRONT_TILE_SIZE_NAME, 180);
                float groundTileSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_GROUND_TILE_SIZE_NAME, 180);
                float npcRegionSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_NPC_REGION_SIZE_NAME, 180);
                float splitFogTileSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_SPLIT_FOG_TILE_SIZE_NAME, 180);
                bool displayRailwayLayer = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_DISPLAY_RAILWAY_LAYER_NAME, false);
                bool displayCircleBorderLayer = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_DISPLAY_CIRCLE_BORDER_LAYER_NAME, false);
                bool cameraClampBorder = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_CAMERA_CLAMP_BORDER_NAME, false);
                bool cameraCheckCircleBorderCollision = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_CAMERA_CHECK_CIRCLE_BORDER_COLLISION_NAME, false);
                float cameraBorderColliderRadius = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_CAMERA_BORDER_COLLIDER_RADIUS_NAME, 3670);
                bool showHorde = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_SHOW_HORDE_NAME, false);
                bool useNewCameraHeightAutoUpdateAlgorithm = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_NEW_CAMERA_HEIGHT_AUTO_UPDATE_ALGORITHM, false);
                float obstacleGridSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_OBSTACLE_GRID_SIZE_NAME, 0);
                bool showDemoFunction = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_SHOW_DEMO_FUNCTION_NAME, false);
                List<string> cameraSettingList = JsonHelper.ReadStringList(configDict, MapCoreDef.MAP_CAMERA_SETTINGS_NAME);
                bool enableCameraCollision = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_ENABLE_CAMERA_COLLISION_NAME, true);
                string defaultGroundBakingShader = configDict[MapCoreDef.MAP_GROUND_BAKING_SHADER_NAME] as string;
                string defaultObstacleMaterial = configDict[MapCoreDef.MAP_OBSTACLE_MATERIAL_NAME] as string;
                string brushFolderPath = configDict[MapCoreDef.MAP_BRUSH_FOLDER_NAME] as string;
                bool useFakePrefab = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_FAKE_PREFAB_NAME, true);
                string unitName = JsonHelper.ReadString(configDict, MapCoreDef.MAP_INTUITIVE_ZOOM_UNIT_NAME, "unit");
                string groundTileMakerRuntimeAssetFolder = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_RUNTIME_ASSET_FOLDER_NAME, "");
                string groundTileMakerEditorAssetFolder = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_EDITOR_ASSET_FOLDER, "");
                int groundTileMakerDefaultTileSize = JsonHelper.ReadInt(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_TILE_SIZE, 180);
                int groundTileMakerDefaultMaskTextureSize = JsonHelper.ReadInt(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_MASK_TEXTURE_SIZE, 512);
                string groundTileMakerDefaultMaskTextureName = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_MASK_TEXTURE_NAME, "");
                bool groundTileMakerDefaultInitChannelData = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_INIT_CHANNEL_DATA, false);
                bool groundTileMakerDefaultNormalizeColor = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_NORMALIZE_COLOR, false);
                string groundTileMakerDefaultMaterialPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_MATERIAL_PATH, "");
                int frameActionMaxExecuteTime = JsonHelper.ReadInt(configDict, MapCoreDef.MAP_FRAME_ACTION_MAX_EXECUTE_TIME, 3);
                bool doNotExportFrontLayerModelTemplates = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_DONT_EXPORT_FRONT_LAYER_MODEL_TEMPLATES_NAME, false);
                string mapListDir = JsonHelper.ReadString(configDict, MapCoreDef.MAP_LIST_DIR_NAME, "");
                string bakedAnimDataPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_BAKED_ANIM_DATA_PATH, "");
                string groundTileAtlasDir = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_ATLAS_INFO_PATH, "");
                string defaultTerritoryMaterialPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_DEFAULT_TERRITORY_MATERIAL_PATH, "");
                bool enableUpdateFarClipPlane = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_ENABLE_UPDATE_CLIP_PLANE, true);
                bool is2DGame = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_IS_2D_GAME, false);
                string projectName = JsonHelper.ReadString(configDict, MapCoreDef.MAP_PROJECT_NAME, "");
                bool useMapLargeTileForDecorationLayer = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_MAP_LARGE_TILE_FOR_DECORATION_LAYER, true);
                bool wrongGroundLODOrientation = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_WRONG_GROUND_LOD_ORIENTATION, true);
                bool preloadGroundLayerTile = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_PRELOAD_GROUND_LAYER_TILE, false);
                string defaultGroundBakingShaderTexturePropertyName = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_BAKING_SHADER_TEXTURE_PROPERTY_NAME, "_MainTex");
                string pluginList = JsonHelper.ReadString(configDict, MapCoreDef.MAP_PLUGIN_LIST_NAME, "");
                bool useNewConfigFormat = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_NEW_CONFIG_FORMAT, false);
                bool hideWallWhenMainBuildingAtFinalPosition = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_HIDE_WALL_WHEN_MAIN_BUILDING_AT_FINAL_POSITION, true);
                bool generateRiverMaterial = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_GENERATE_RIVER_MATERIAL, true);
                string rotatedUVMeshPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_ROTATED_UV_MESH_PATH, "");
                bool supportGroundHeightChange = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_SUPPORT_GROUND_HEIGHT_CHANGE, true);
                bool useCameraShakeByDistance = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_CAMERA_SHAKE_BY_DISTANCE, false);
                bool enableResizeMap = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_ENABLE_RESIZE_MAP, false);
                bool enableUpdateNearClipPlane = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_ENABLE_UPDATE_NEAR_CLIP_PLANE, false);
                string globalSettingPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GLOBAL_SETTING_PATH_NAME, "");

                config = new MapConfig(mapResDir, configResDir, intuitiveZoomFilePath, resMgr, touchManager, navMode, useOnly15TerrainPrefab, frontTileSize, groundTileSize, npcRegionSize, usePrefabOutline, displayRailwayLayer, displayCircleBorderLayer, cameraClampBorder, cameraCheckCircleBorderCollision, cameraBorderColliderRadius, showHorde, obstacleGridSize, globalObstacleMode, showDemoFunction, cameraSettingList, enableCameraCollision, defaultGroundBakingShader, defaultObstacleMaterial, brushFolderPath, useNewCameraHeightAutoUpdateAlgorithm, useFakePrefab, unitName, groundTileMakerRuntimeAssetFolder, groundTileMakerEditorAssetFolder, frameActionMaxExecuteTime, doNotExportFrontLayerModelTemplates, mapListDir, splitFogTileSize, bakedAnimDataPath, groundTileAtlasDir, defaultTerritoryMaterialPath, enableUpdateFarClipPlane, is2DGame, projectName, groundTileMakerDefaultTileSize, groundTileMakerDefaultMaskTextureSize, groundTileMakerDefaultMaskTextureName, groundTileMakerDefaultInitChannelData, groundTileMakerDefaultNormalizeColor, groundTileMakerDefaultMaterialPath, useMapLargeTileForDecorationLayer, defaultGroundBakingShaderTexturePropertyName, wrongGroundLODOrientation, preloadGroundLayerTile, pluginList, useNewConfigFormat, hideWallWhenMainBuildingAtFinalPosition, generateRiverMaterial, rotatedUVMeshPath, supportGroundHeightChange, useCameraShakeByDistance, enableResizeMap, enableUpdateNearClipPlane, globalSettingPath);
            }

            return config;
        }

        public static MapConfig CreateFromFile(string configFilePath, IMapResourceMgr resMgr, IMapTouchManager touchManager)
        {
            MapConfig config = null;
            var bytes = resMgr.LoadTextBytes(configFilePath, true);
            if (bytes != null)
            {
                string configData = System.Text.Encoding.UTF8.GetString(bytes);
                var configDict = JSONParser.Deserialize(configData) as Dictionary<string, object>;
                string mapResDir = configDict[MapCoreDef.MAP_DIR_NAME] as string;
                string configResDir = configDict[MapCoreDef.MAP_CONFIG_DIR_NAME] as string;
                string intuitiveZoomFilePath = configDict[MapCoreDef.MAP_INTUITIVE_ZOOM_FILE_NAME] as string;
                NavigationCreateMode navMode = MapCoreDef.GetNavigationMode(configDict[MapCoreDef.MAP_NAVIGATION_MODE_NAME] as string);
                NavigationCreateMode globalObstacleMode = MapCoreDef.GetObstacleMode(configDict[MapCoreDef.MAP_GLOBAL_OBSTACLE_MODE_NAME] as string);
                bool useOnly15TerrainPrefab = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_15_GROUND_PREFAB_NAME, true);
                bool usePrefabOutline = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_PREFAB_OUTLINE_NAME, false);
                float frontTileSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_FRONT_TILE_SIZE_NAME, 180);
                float groundTileSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_GROUND_TILE_SIZE_NAME, 180);
                float npcRegionSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_NPC_REGION_SIZE_NAME, 180);
                float splitFogTileSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_SPLIT_FOG_TILE_SIZE_NAME, 180);
                bool displayRailwayLayer = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_DISPLAY_RAILWAY_LAYER_NAME, false);
                bool displayCircleBorderLayer = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_DISPLAY_CIRCLE_BORDER_LAYER_NAME, false);
                bool cameraClampBorder = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_CAMERA_CLAMP_BORDER_NAME, false);
                bool cameraCheckCircleBorderCollision = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_CAMERA_CHECK_CIRCLE_BORDER_COLLISION_NAME, false);
                float cameraBorderColliderRadius = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_CAMERA_BORDER_COLLIDER_RADIUS_NAME, 3670);
                bool showHorde = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_SHOW_HORDE_NAME, false);
                bool useNewCameraHeightAutoUpdateAlgorithm = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_NEW_CAMERA_HEIGHT_AUTO_UPDATE_ALGORITHM, false);
                float obstacleGridSize = JsonHelper.ReadFloat(configDict, MapCoreDef.MAP_OBSTACLE_GRID_SIZE_NAME, 0);
                bool showDemoFunction = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_SHOW_DEMO_FUNCTION_NAME, false);
                List<string> cameraSettingList = JsonHelper.ReadStringList(configDict, MapCoreDef.MAP_CAMERA_SETTINGS_NAME);
                bool enableCameraCollision = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_ENABLE_CAMERA_COLLISION_NAME, true);
                string defaultGroundBakingShader = configDict[MapCoreDef.MAP_GROUND_BAKING_SHADER_NAME] as string;
                string defaultObstacleMaterial = configDict[MapCoreDef.MAP_OBSTACLE_MATERIAL_NAME] as string;
                string brushFolderPath = configDict[MapCoreDef.MAP_BRUSH_FOLDER_NAME] as string;
                bool useFakePrefab = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_FAKE_PREFAB_NAME, true);
                string unitName = JsonHelper.ReadString(configDict, MapCoreDef.MAP_INTUITIVE_ZOOM_UNIT_NAME, "unit");
                string groundTileMakerRuntimeAssetFolder = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_RUNTIME_ASSET_FOLDER_NAME, "");
                string groundTileMakerEditorAssetFolder = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_EDITOR_ASSET_FOLDER, "");
                int groundTileMakerDefaultTileSize = JsonHelper.ReadInt(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_TILE_SIZE, 180);
                int groundTileMakerDefaultMaskTextureSize = JsonHelper.ReadInt(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_MASK_TEXTURE_SIZE, 512);
                string groundTileMakerDefaultMaskTextureName = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_MASK_TEXTURE_NAME, "");
                bool groundTileMakerDefaultInitChannelData = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_INIT_CHANNEL_DATA, false);
                bool groundTileMakerDefaultNormalizeColor = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_NORMALIZE_COLOR, false);
                string groundTileMakerDefaultMaterialPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_MAKER_DEFAULT_MATERIAL_PATH, "");
                int frameActionMaxExecuteTime = JsonHelper.ReadInt(configDict, MapCoreDef.MAP_FRAME_ACTION_MAX_EXECUTE_TIME, 3);
                bool doNotExportFrontLayerModelTemplates = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_DONT_EXPORT_FRONT_LAYER_MODEL_TEMPLATES_NAME, false);
                string mapListDir = JsonHelper.ReadString(configDict, MapCoreDef.MAP_LIST_DIR_NAME, "");
                string bakedAnimDataPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_BAKED_ANIM_DATA_PATH, "");
                string groundTileAtlasDir = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_TILE_ATLAS_INFO_PATH, "");
                string defaultTerritoryMaterialPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_DEFAULT_TERRITORY_MATERIAL_PATH, "");
                bool enableUpdateFarClipPlane = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_ENABLE_UPDATE_CLIP_PLANE, true);
                bool is2DGame = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_IS_2D_GAME, false);
				string projectName = JsonHelper.ReadString(configDict, MapCoreDef.MAP_PROJECT_NAME, "");
                bool useMapLargeTileForDecorationLayer = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_MAP_LARGE_TILE_FOR_DECORATION_LAYER, true);
                bool wrongGroundLODOrientation = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_WRONG_GROUND_LOD_ORIENTATION, true);
                bool preloadGroundLayerTile = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_PRELOAD_GROUND_LAYER_TILE, false);
                string defaultGroundBakingShaderTexturePropertyName = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GROUND_BAKING_SHADER_TEXTURE_PROPERTY_NAME, "_MainTex");
                string pluginList = JsonHelper.ReadString(configDict, MapCoreDef.MAP_PLUGIN_LIST_NAME, "");
                bool useNewConfigFormat = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_NEW_CONFIG_FORMAT, false);
                bool hideWallWhenMainBuildingAtFinalPosition = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_HIDE_WALL_WHEN_MAIN_BUILDING_AT_FINAL_POSITION, true);
                bool generateRiverMaterial = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_GENERATE_RIVER_MATERIAL, true);
                string rotatedUVMeshPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_ROTATED_UV_MESH_PATH, "");
                bool supportGroundHeightChange = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_SUPPORT_GROUND_HEIGHT_CHANGE, true);
                bool useCameraShakeByDistance = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_USE_CAMERA_SHAKE_BY_DISTANCE, false);
                bool enableResizeMap = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_ENABLE_RESIZE_MAP, false);
                bool enableUpdateNearClipPlane = JsonHelper.ReadBoolean(configDict, MapCoreDef.MAP_ENABLE_UPDATE_NEAR_CLIP_PLANE, false);
                string globalSettingPath = JsonHelper.ReadString(configDict, MapCoreDef.MAP_GLOBAL_SETTING_PATH_NAME, "");

                config = new MapConfig(mapResDir, configResDir, intuitiveZoomFilePath, resMgr, touchManager, navMode, useOnly15TerrainPrefab, frontTileSize, groundTileSize, npcRegionSize, usePrefabOutline, displayRailwayLayer, displayCircleBorderLayer, cameraClampBorder, cameraCheckCircleBorderCollision, cameraBorderColliderRadius, showHorde, obstacleGridSize, globalObstacleMode, showDemoFunction, cameraSettingList, enableCameraCollision, defaultGroundBakingShader, defaultObstacleMaterial, brushFolderPath, useNewCameraHeightAutoUpdateAlgorithm, useFakePrefab, unitName, groundTileMakerRuntimeAssetFolder, groundTileMakerEditorAssetFolder, frameActionMaxExecuteTime, doNotExportFrontLayerModelTemplates, mapListDir, splitFogTileSize, bakedAnimDataPath, groundTileAtlasDir, defaultTerritoryMaterialPath, enableUpdateFarClipPlane, is2DGame, projectName, groundTileMakerDefaultTileSize, groundTileMakerDefaultMaskTextureSize, groundTileMakerDefaultMaskTextureName, groundTileMakerDefaultInitChannelData, groundTileMakerDefaultNormalizeColor, groundTileMakerDefaultMaterialPath, useMapLargeTileForDecorationLayer, defaultGroundBakingShaderTexturePropertyName, wrongGroundLODOrientation, preloadGroundLayerTile, pluginList, useNewConfigFormat, hideWallWhenMainBuildingAtFinalPosition, generateRiverMaterial, rotatedUVMeshPath, supportGroundHeightChange, useCameraShakeByDistance, enableResizeMap, enableUpdateNearClipPlane, globalSettingPath);
            }

            return config;
        }

        //资源管理实现
        public IMapResourceMgr resourceManagerImpl { get; private set; }
        //touch管理的实现
        public IMapTouchManager touchManagerImpl { get; private set; }
        //运行时地图数据目录,即MapData文件夹路径
        public string runtimeMapResDirectory { get; private set; }
        //地图配置数据文件夹
        public string configResDir { get; private set; }
        //地图lod缩放配置表路径
        public string intuitiveZoomConfigFilePath { get; private set; }
        //导出地图数据的时候默认使用哪种方式生成地图导航网格
        public NavigationCreateMode defaultNavmeshCreationMode { get; private set; }
        //导出地图数据的时候默认使用哪种方式生成global obstacle数据
        public NavigationCreateMode defaultGlobalObstacleCreationMode { get; private set; }
        //是否只使用0-15的prefab,包括随机prefab,新项目都为true,p2为false
        public bool useOnly15Prefab { get; private set; }
        public float defaultGroundTileSize { get; private set; }
        public float defaultNPCRegionSize { get; private set; }
        public float defaultFrontTileSize { get; private set; }
        //是否使用绑定到模型上的障碍物边框
        public bool usePrefabOutline { get; private set; }
        public bool displayRailwayLayer { get; private set; }
        public bool displayCircleBorderLayer { get; private set; }
        public bool cameraClampBorder { get; private set; }
        public bool cameraCheckCircleBorderCollision { get; private set; }
        //相机外围圆形碰撞的半径
        public float cameraBorderColliderRadius { get; private set; }
        //地图编辑器里是否显示ruin layer的horde
        public bool showHorde { get; private set; }
        //将地图划分为格子大小,分辨判断这些格子是否被障碍物占据,不为0表示需要创建这个数据
        public float obstacleGridSize { get; private set; }
        //是否显示一些临时的功能
        public bool showDemoFunction { get; private set; }
        //相机配置的文件名,路径需要在configResDir下
        public List<string> cameraSettingFilePaths { get; private set; }
        public bool enableCameraCollision { get; private set; }
        public string defaultGroundBakingShader { get; private set; }
        public string defaultObstacleMaterial { get; private set; }
        //编辑器用的笔刷贴图的文件夹
        public string brushFolderPath { get; private set; }
        public bool useNewCameraHeightAutoUpdateAlgorithm { get; private set; }
        //intuitivezoom.tsv里的unit列的名字,默认叫unit
        public string intuitiveConfigUnitName { get; private set; }
        public string groundTileMakerDefaultRuntimeAssetFolder { get; private set; }
        public string groundTileMakerDefaultEditorAssetFolder { get; private set; }
        public int groundTileMakerDefaultMaskTextureSize { get; private set; }
        public int groundTileMakerDefaultTileSize { get; private set; }
        public string groundTileMakerDefaultMaskTextureName { get; private set; }
        public bool groundTileMakerDefaultInitChannelData { get; private set; }
        public bool groundTileMakerDefaultNormalizeColor { get; private set; }
        public string groundTileMakerDefaultMaterialPath { get; private set; }
        public bool useFakePrefab { get; private set; }
        public int frameActionMaxExecuteTime { get; private set; }
        //是否不导出front layer使用的model template到mapdata1.bytes中,如果使用了更优化的版本,就需要兼容老数据了
        public bool doNotExportFrontLayerModelTemplates { get; private set; }
        public string mapListDir { get; private set; }
        public float defaultSplitFogTileSize { get; private set; }
        public string bakedAnimDataPath { get; private set; }
        public string groundTileAtlasInfoDir { get; private set; }
        public string defaultTerritoryMaterialPath { get; private set; }
        public bool enableUpdateFarClipPlane { get; private set; }
        public bool enableUpdateNearClipPlane { get; private set; }
        public bool is2DGame { get; private set; }
        public string projectName { get; private set; }
        public bool useMapLargeTileForDecorationLayer { get; private set; }
        public string defaultGroundBakingShaderTexturePropertyName { get; private set; }
        public bool wrongGroundLODOrientation { get; private set; }
        public bool preloadGroundLayerTile { get; private set; }
        public string pluginList { get; private set; }
        //使用新的tsv配置表header名字
        public bool useNewConfigFormat { get; private set; }
        public bool hideWallWhenMainBuildingAtFinalPosition { get; private set; }
        //是否生成河流材质
        public bool generateRiverMaterial { get; private set; }
        public string rotatedUVMeshPath { get; private set; }
        public bool supportGroundHeightChange { get; private set; }
        public bool useCameraShakeByDistance { get; private set; }
        public bool enableResizeMap { get; private set; }
        public MapGlobalSetting mapGlobalSetting { get; private set; }
    }
}