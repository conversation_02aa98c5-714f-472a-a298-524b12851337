﻿Shader "MatCapWithEdgeLight"
{
    Properties
    {
        _MainColor("Main Color", Color) = (1.0, 1.0, 1.0, 1.0)
        _DetailColor("Detail Color", Color) = (1.0, 1.0, 1.0, 1.0)
        _DetailTex("Detail Texture", 2D) = "white" {}
        _DetailTexDepthOffset("Detail Texture Depth Offset", Float) = 1.0
        _DiffuseColor("Diffuse Color", Color) = (0.0, 0.0, 0.0, 0.0)
        _DiffuseTex("Diffuse Texture", 2D) = "white" {}
        _MatCap("MatCap", 2D) = "white" {}
        _ReflectionColor("Reflection Color", Color) = (0.2, 0.2, 0.2, 1.0)
        _ReflectionMap("Reflection Cube Map", Cube) = "" {}
        _ReflectionStrength("Reflection Strength", Range(0.0, 1.0)) = 0.5
        [Toggle(USE_EDGE_LIGHT)] _UseEdgeLight("Use Edge Light", Float) = 0
        _EdgeColor("Edge Light Color", Color) = (1, 1, 1, 1)
        _EdgeStrength("Edge Light Strength", Range(0, 5)) = 0.0 // 增大上限到5
        _Threshold("Threshold", Range(0, 1)) = 0.5
        _SmoothStep("SmoothStep",Float) = 0.05
    }

        SubShader
        {
            Tags
            {
                "Queue" = "Geometry"
                "RenderType" = "Opaque"
            }

            Pass
            {
                Blend Off
                Cull Back
                ZWrite On

                CGPROGRAM
                #include "UnityCG.cginc"
                #pragma fragment frag
                #pragma vertex vert

                float4 _MainColor;
                float4 _DetailColor;
                sampler2D _DetailTex;
                float4 _DetailTex_ST;
                float _DetailTexDepthOffset;
                float4 _DiffuseColor;
                sampler2D _DiffuseTex;
                float4 _DiffuseTex_ST;
                sampler2D _MatCap;
                float4 _ReflectionColor;
                samplerCUBE _ReflectionMap;
                float _ReflectionStrength;
                uniform half4 _EdgeColor;
                uniform half _EdgeStrength;
                uniform float _UseEdgeLight; // 确保在这里声明
                float _Threshold;
                float _SmoothStep;

                // 顶点输入结构
                struct VertexInput
                {
                    float3 normal : NORMAL;
                    float4 position : POSITION;
                    float2 UVCoordsChannel1 : TEXCOORD0;
                };

                // 顶点输出(片元输入)结构
                struct VertexToFragment
                {
                    float3 detailUVCoordsAndDepth : TEXCOORD0;
                    float4 diffuseUVAndMatCapCoords : TEXCOORD1;
                    float4 position : SV_POSITION;
                    float3 worldSpaceReflectionVector : TEXCOORD2;
                    float3 worldSpaceNormal : TEXCOORD3; // 添加法线用于边缘光计算
                    float3 worldPosition : TEXCOORD4; // 保存世界空间位置
                };

                struct appdata_t
                {
                    float4 vertex : POSITION;
                    float2 uv : TEXCOORD0;
                };

                struct v2f
                {
                    float2 uv : TEXCOORD0;
                    float4 vertex : SV_POSITION;
                };

                v2f vert(appdata_t v)
                {
                    v2f o;
                    o.vertex = UnityObjectToClipPos(v.vertex);
                    o.uv = v.uv; // 将纹理坐标传递给片元着色器
                    return o;
                }

                //------------------------------------------------------------
                // 顶点着色器
                //------------------------------------------------------------
                VertexToFragment vert(VertexInput input)
                {
                    VertexToFragment output;

                    // 漫反射UV坐标准备
                    output.diffuseUVAndMatCapCoords.xy = TRANSFORM_TEX(input.UVCoordsChannel1, _DiffuseTex);

                    // MatCap坐标准备
                    output.diffuseUVAndMatCapCoords.z = dot(normalize(UNITY_MATRIX_IT_MV[0].xyz), normalize(input.normal));
                    output.diffuseUVAndMatCapCoords.w = dot(normalize(UNITY_MATRIX_IT_MV[1].xyz), normalize(input.normal));
                    output.diffuseUVAndMatCapCoords.zw = output.diffuseUVAndMatCapCoords.zw * 0.5 + 0.5;

                    // 坐标变换
                    output.position = UnityObjectToClipPos(input.position);

                    // 细节纹理准备
                    output.detailUVCoordsAndDepth.xy = TRANSFORM_TEX(input.UVCoordsChannel1, _DetailTex);
                    output.detailUVCoordsAndDepth.z = output.position.z;

                    // 世界空间法线
                    output.worldSpaceNormal = normalize(mul((float3x3)unity_ObjectToWorld, input.normal));

                    // 世界空间位置
                    output.worldPosition = mul(unity_ObjectToWorld, input.position).xyz;

                    // 世界空间反射向量
                    float3 viewDir = _WorldSpaceCameraPos.xyz - output.worldPosition;
                    output.worldSpaceReflectionVector = reflect(viewDir, output.worldSpaceNormal);

                    return output;
                }

                //------------------------------------------------------------
                // 片元着色器
                //------------------------------------------------------------
                float4 frag(VertexToFragment input) : SV_Target
                {
                    // 镜面反射颜色
                    float3 reflectionColor = texCUBE(_ReflectionMap, input.worldSpaceReflectionVector).rgb * _ReflectionColor.rgb;

                    // 漫反射颜色
                    float4 diffuseColor = tex2D(_DiffuseTex, input.diffuseUVAndMatCapCoords.xy) * _DiffuseColor;

                    // 主颜色
                    float3 mainColor = lerp(diffuseColor.rgb, reflectionColor, _ReflectionStrength);

                    // 细节纹理
                    float3 detailMask = tex2D(_DetailTex, input.detailUVCoordsAndDepth.xy).rgb;

                    // 细节颜色
                    float3 detailColor = lerp(_DetailColor.rgb, mainColor, detailMask);

                    // 细节颜色和主颜色进行插值，成为新的主颜色
                    mainColor = lerp(detailColor, mainColor, saturate(input.detailUVCoordsAndDepth.z * _DetailTexDepthOffset));

                    // 从提供的MatCap纹理中提取光照信息
                    float3 matCapColor = tex2D(_MatCap, input.diffuseUVAndMatCapCoords.zw).rgb;

                    // 计算边缘光效果
                    if (_UseEdgeLight > 0.5)
                    {
                        // 计算视线方向
                        float3 viewDir = normalize(_WorldSpaceCameraPos - input.worldPosition); // 使用世界空间位置
                        // 计算边缘光强度（反转区域）
                        float edgeLightFactor = 1.0 - saturate(dot(viewDir, input.worldSpaceNormal)); // 反转计算
                        edgeLightFactor = pow(edgeLightFactor * (_EdgeStrength), 2.0); // 使用幂函数增强边缘光效果
                        edgeLightFactor = saturate(edgeLightFactor); // 确保值在0到1之间
                        edgeLightFactor *= _EdgeStrength; // 乘以边缘光强度，确保在0时没有效果
                        edgeLightFactor = clamp(edgeLightFactor, 0.0, 5.0); // 确保最大值为5
                        mainColor = lerp(mainColor, _EdgeColor.rgb, edgeLightFactor); // 混合边缘光颜色
                    }

                    // 计算 BinaryValue
                    float BinaryValue = smoothstep(_Threshold - _SmoothStep, _Threshold + _SmoothStep, matCapColor.r); // 假设使用 matCapColor 的红色通道
                    float4 NB = float4(BinaryValue, BinaryValue, BinaryValue, 1);
                    return float4(lerp(mainColor * matCapColor * 2.0 ,_MainColor.rgb, NB), _MainColor.a);
                    
                }

                ENDCG
            }
        }

            Fallback "VertexLit"
}
