﻿ 



 
 



using UnityEngine;
using System;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum FrameActionType
    {
        AddTileObject,
        AddTileObject2,
        RemoveTileObject,
        RemoveTileObject2,
        UpdateTileObject,
        UpdateTileObject2,
        UpdateTileObjects,
        UpdateTileObjects2,
        UpdateTileObjectCull,
        UpdateTileObjectCull2,
        CheckDecorationObjectOverlap,
        CheckDecorationObjectOverlap2,
        SetGridObjectVisibility,
        ShowFrontTile,
        ShowFrontTile2,
        HideFrontTile,
        HideFrontTile2,
        UpdateFrontLayerViewport,
        UpdateFrontLayerViewport2,
        SetObjectActive,
    }

    //每帧执行的地图事件
    public abstract class FrameAction
    {
        protected static long MakeKeyHelper(int id, FrameActionType type)
        {
            long id64 = (long)id;
            long type64 = (long)type;
            id64 <<= 32;
            return type64 | id64;
        }

        public void InitAction()
        {
            this.executeTimeStamp = -1;
        }

        //action执行完毕后返回true
        public bool Do()
        {
            if (childActions.Count == 0)
            {
                //没有子命令,直接执行自己的行为
                DoImpl();
                //DoImpl以后有子命令了,需要标记该命令为未执行完状态
                if (childActions.Count > 0)
                {
                    nextExecutingChildActionIndex = 0;
                    return false;
                }
                return true;
            }
            else if (nextExecutingChildActionIndex < 0)
            {
                //先执行自己,再执行子命令
                DoImpl();
                nextExecutingChildActionIndex = 0;
                return false;
            }
            else if (nextExecutingChildActionIndex < childActions.Count)
            {
                if (isEnabled)
                {
                    //只有在enable状态下才执行子命令
                    bool childFinished = childActions[nextExecutingChildActionIndex].Do();
                    if (childFinished)
                    {
                        ++nextExecutingChildActionIndex;
                    }
                }
            }
            else
            {
                //循环执行,这里会出现这种情况是因为frame action是keep alive的,所以会一直在队列中,直到手动删除
                //对于这种action来说,执行完后下次从头开始循环执行
                OnFinish();
                nextExecutingChildActionIndex = 0;
                DoImpl();
            }

            bool finished = (nextExecutingChildActionIndex >= childActions.Count || isEnabled == false);
            return finished;
        }

        public bool IsRunning
        {
            get { return nextExecutingChildActionIndex >= 0 && nextExecutingChildActionIndex < childActions.Count; }
        }

        public void OnDestroy()
        {
            for (int i = 0; i < childActions.Count; ++i)
            {
                childActions[i].OnDestroy();
            }
            childActions.Clear();
            nextExecutingChildActionIndex = -1;
            OnDestroyImpl();
        }

        public void AddChildAction(FrameAction action)
        {
            childActions.Add(action);
        }

        public void AddChildActions(List<FrameAction> actions)
        {
            childActions.AddRange(actions);
        }

        protected abstract void OnDestroyImpl();
        protected abstract void DoImpl();
        //keep alive的action执行完一个循环后调用
        protected virtual void OnFinish() { }

        public abstract string debugInfo { get; }
        public abstract string name { get; }
        //是否允许执行
        public virtual bool isEnabled { get { return true; } }
        //是否在执行后从队列中删除
        public virtual bool keepAlive { get { return false; } }
        public long executeTimeStamp { set; get; }

        //每个action都可以包含多个子命令
        public List<FrameAction> childActions = new List<FrameAction>();
        public int nextExecutingChildActionIndex = -1;
        public abstract long key { get; }
        public abstract FrameActionType type { get; }
    }
}
