﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class BuildingGridLayerSettingWindow : EditorWindow
    {
        public void Show(string layerName)
        {
            mLayerName = layerName;
        }

        void OnEnable()
        {
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mHorizontalGridCount = EditorGUILayout.IntField("Horizontal Grid Count", mHorizontalGridCount);
            mVerticalGridCount = EditorGUILayout.IntField("Vertical Grid Count", mVerticalGridCount);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            mTileWidth = EditorGUILayout.FloatField("Tile Width", mTileWidth);
            mTileHeight = EditorGUILayout.FloatField("Tile Height", mTileHeight);
            GUILayout.EndHorizontal();

            if (GUILayout.Button("Create"))
            {
                var map = Map.currentMap as EditorMap;
                bool valid = CheckParameter();
                if (valid)
                {
                    var layer = map.CreateBuildingGridLayer(mLayerName, mTileWidth * mHorizontalGridCount, mTileHeight * mVerticalGridCount, mTileWidth, mTileHeight);

                    Selection.activeObject = layer.layerView.root;

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter()
        {
            if (mHorizontalGridCount <= 0 || mVerticalGridCount <= 0 ||
                mTileWidth <= 0 || mTileHeight <= 0)
            {
                return false;
            }
            return true;
        }

        public int mHorizontalGridCount = 60;
        public int mVerticalGridCount = 60;
        public float mTileWidth = 1;
        public float mTileHeight = 1;
        public string mLayerName;
        public GridType mGridType = GridType.Rectangle;
    }
}

#endif