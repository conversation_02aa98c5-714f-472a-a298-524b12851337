﻿//骨骼下绑定的挂接点的MeshRenderer要使用这个shader,即刚体动画
Shader "SLGMaker/TextureRigidAnim"
{
	Properties
	{
		_MainTex("Texture", 2D) = "white" {}
	//mesh使用的动画数据
	_AnimationData("Animation Data", 2D) = "white" {}
	//y:动画起始偏移
	//z:动画帧
	_AnimationParams("Animation Parameters", Vector) = (0, 0, 0, 0)
		//动画贴图大小,z是正在的动画数据开始的偏移
		_AnimTextureSize("Animation Texture Size", Vector) = (1, 1, 1, 1)
		_BlendParams("Blending Parameters", Float) = 0
		_BoneCount("Bone Count", Float) = 0
	}
		SubShader
	{
		Tags { "RenderType" = "Opaque" }
		LOD 100

		Pass
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"

			UNITY_INSTANCING_BUFFER_START(Props)
				UNITY_DEFINE_INSTANCED_PROP(float4, _AnimationParams)
				UNITY_DEFINE_INSTANCED_PROP(float, _BlendParams)
			UNITY_INSTANCING_BUFFER_END(Props)
			#include "TextureSkinTemplate.cginc"

			struct appdata
			{
				float4 vertex : POSITION;
				//uv的w记录挂接点的index
				float4 uv : TEXCOORD0;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f
			{
				float2 uv : TEXCOORD0;
				float4 vertex : SV_POSITION;
			};

			sampler2D _MainTex;
			float4 _MainTex_ST;

			v2f vert(appdata v)
			{
				v2f o;

				UNITY_SETUP_INSTANCE_ID(v);

				half3 localPos = RigidTransform(v.vertex, v.uv.w);
				o.vertex = UnityObjectToClipPos(localPos);
				o.uv = TRANSFORM_TEX(v.uv.xy, _MainTex);
				return o;
			}

			fixed4 frag(v2f i) : SV_Target
			{
				fixed4 col = tex2D(_MainTex, i.uv);
				return col;
			}
			ENDCG
		}
	}
}
