﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //update一个tile下所有的tile object
    public class FrameActionUpdateTileObjects : FrameAction
    {
        public static FrameActionUpdateTileObjects Require(TileGridObjectLayerData layerData, OptimizedTileData tileData, int lod, int x, int y)
        {
            var act = mPool.Require();
            act.Init(layerData, tileData, lod, x, y);
            return act;
        }

        void Init(TileGridObjectLayerData layerData, OptimizedTileData tileData, int lod, int x, int y)
        {
            InitAction();
            mLayerData = layerData;
            mTileData = tileData;
            mLOD = lod;
            int tileIndex = y * layerData.horizontalTileCount + x;
            mKey = MakeActionKey(tileIndex, lod, type);

            var childrenModelTemplates = tileData.objectsOfEachLOD[lod].childPrefabs;
            int n = childrenModelTemplates.Length;
            mTileIndex = y * layerData.horizontalTileCount + x;

            var map = mLayerData.map;
            float mapWidth = map.mapWidth;
            float mapHeight = map.mapHeight;
            Vector3 bigTilePos = layerData.FromCoordinateToWorldPosition(x, y);

            //bool needUpdate = false;
            for (int i = 0; i < n; ++i)
            {
                var bounds = childrenModelTemplates[i].GetLocalBoundsInPrefab(mLayerData.prefabInitInfo);
                var pos = childrenModelTemplates[i].GetPosition(mLayerData.prefabInitInfo) + bigTilePos;
                bool isInWorld = pos.x >= 0 && pos.x < mapWidth && pos.z >= 0 && pos.z < mapHeight;
                bool useCullManager = bounds.width <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && bounds.height <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && isInWorld;
                //现在只管理山的视野,因为山太大了,不适合用管理小件物体的方法管理
                if (!useCullManager && childrenModelTemplates[i].objectType != TileObjectType.AlwaysVisible)
                {
                    //needUpdate = true;
                    var objectID = mLayerData.GetObjectID(lod, tileData, mTileIndex, i);
                    FrameAction updateAction = FrameActionUpdateTileObject.Require(layerData, objectID);
                    AddChildAction(updateAction);
                }
            }
            //if (!needUpdate && lod == 0)
            //{
            //    mLayerData.ClearBigTileCullingTestFlag(x, y);
            //}
        }

        protected override void DoImpl()
        {
        }

        public override bool isEnabled
        {
            get
            {
                //return mLayerData.IsBoundsTile(mTileCoord.x, mTileCoord.y);
                //temp code
                return true;
            }
        }

        public static long MakeActionKey(int id, int lod, FrameActionType type)
        {
            long id64 = (long)id;
            long lod64 = (long)lod;
            long type64 = (long)type;
            long typeAndLOD = (lod64 << 16) | type64;
            id64 <<= 32;
            return typeAndLOD | id64;
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mTileData = null;
            mPool.Release(this);
        }

        public override long key { get { return mKey; } }
        public override FrameActionType type => FrameActionType.UpdateTileObjects;
        public override string debugInfo => string.Format("Tile Index: {0}, LOD: {1}, Enabled: {2}", mTileIndex, mLOD, isEnabled);
        public override string name => "Update Tile Objects";
        public override bool keepAlive { get { return true; } }

        TileGridObjectLayerData mLayerData;
        OptimizedTileData mTileData;
        int mLOD;
        int mTileIndex;
        Vector2Int mTileCoord;
        long mKey;

        //temp code
        //todo 1.相机没动不更新
        //2.如果内部tile更新完后,则只更新边界的tile

        static ObjectPool<FrameActionUpdateTileObjects> mPool = new ObjectPool<FrameActionUpdateTileObjects>(1000, () => new FrameActionUpdateTileObjects());
    }
}
