﻿ 



 
 

﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class ConvexPolygonCollisionCheck
    {
        //在屏幕空间中测试obb是否相交,使用SAT算法
        public static bool ConvexPolygonHit2D(List<Vector3> obb0, List<Vector3> obb1)
        {
            if (obb0 != null && obb1 != null)
            {
                //check obb0 axis
                for (int i = 0; i < obb0.Count; ++i)
                {
                    int next = (i + 1) % obb0.Count;
                    var n = obb0[next] - obb0[i];
                    n.Normalize();

                    bool intersect = IsObbIntersectedOnAxis(obb0, obb1, obb0[i], n);
                    if (!intersect)
                    {
                        return false;
                    }
                }

                //check obb1 axis
                for (int i = 0; i < obb1.Count; ++i)
                {
                    int next = (i + 1) % obb1.Count;
                    var n = obb1[next] - obb1[i];
                    n.Normalize();

                    bool intersect = IsObbIntersectedOnAxis(obb0, obb1, obb1[i], n);
                    if (!intersect)
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        //计算obb在某个轴上的投影范围
        static Vector3 GetProjectRange(List<Vector3> obb0, Vector3 axisOrigin, Vector3 axisDir)
        {
            float obb0MinProjDistance = float.MaxValue;
            float obb0MaxProjDistance = float.MinValue;
            for (int i = 0; i < obb0.Count; ++i)
            {
                var dir = obb0[i] - axisOrigin;
                var projPos = Vector3.Dot(dir, axisDir);
                if (projPos < obb0MinProjDistance)
                {
                    obb0MinProjDistance = projPos;
                }
                if (projPos > obb0MaxProjDistance)
                {
                    obb0MaxProjDistance = projPos;
                }
            }

            if (obb0MaxProjDistance < obb0MinProjDistance)
            {
                Debug.Assert(false);
            }
            return new Vector3(obb0MinProjDistance, 0, obb0MaxProjDistance);
        }

        //判断2个obb在某个轴上的投影是否相交
        static bool IsObbIntersectedOnAxis(List<Vector3> obb0, List<Vector3> obb1, Vector3 axisOrigin, Vector3 axisDir)
        {
            var range0 = GetProjectRange(obb0, axisOrigin, axisDir);
            var range1 = GetProjectRange(obb1, axisOrigin, axisDir);
            if (range0.x > range1.z || range1.x > range0.z)
            {
                return false;
            }
            return true;
        }
    }
}