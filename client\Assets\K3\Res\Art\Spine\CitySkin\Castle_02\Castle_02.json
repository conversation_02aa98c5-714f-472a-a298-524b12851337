{"skeleton": {"hash": "bp922zp+278", "spine": "4.2.33", "x": -441.75, "y": -258.52, "width": 833, "height": 879.71, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -394.74, "y": 152.47, "color": "ff0000ff"}, {"name": "bone2", "parent": "root", "x": -402.63, "y": 87.51, "color": "ff0000ff"}, {"name": "bone3", "parent": "root", "x": -391.5, "y": 10.96, "color": "ff0000ff"}, {"name": "bone4", "parent": "root", "x": -368.3, "y": -57.71, "color": "ff0000ff"}, {"name": "bone5", "parent": "root", "x": -298.54, "y": -117.73, "color": "ff0000ff"}, {"name": "bone6", "parent": "root", "x": -206.21, "y": -146.49, "color": "ff0000ff"}, {"name": "bone7", "parent": "root", "x": -102.28, "y": -173.4, "color": "ff0000ff"}, {"name": "bone8", "parent": "root", "x": -15.25, "y": -200.43, "color": "ff0000ff"}, {"name": "bone9", "parent": "root", "x": 66.4, "y": -193.47, "color": "ff0000ff"}, {"name": "bone10", "parent": "root", "x": 148.06, "y": -160.99, "color": "ff0000ff"}, {"name": "bone11", "parent": "root", "x": 208.84, "y": -118.31, "color": "ff0000ff"}, {"name": "bone12", "parent": "root", "x": 281.68, "y": -73.77, "color": "ff0000ff"}, {"name": "bone13", "parent": "root", "x": 310.45, "y": -16.24, "color": "ff0000ff"}, {"name": "bone14", "parent": "root", "x": 347.45, "y": 38.97, "color": "ff0000ff"}, {"name": "bone15", "parent": "root", "x": 351.17, "y": 102.53, "color": "ff0000ff"}, {"name": "bone16", "parent": "root", "x": -151.32, "y": 71.23, "color": "0600ffff"}, {"name": "bone17", "parent": "root", "x": -177.14, "y": 54.87, "color": "0600ffff"}, {"name": "bone18", "parent": "root", "x": -118.86, "y": 60.49, "color": "0600ffff"}, {"name": "bone19", "parent": "root", "x": -133.94, "y": 42.09, "color": "0600ffff"}, {"name": "bone20", "parent": "root", "x": -78.47, "y": 53.33, "color": "0600ffff"}, {"name": "bone21", "parent": "root", "x": -84.6, "y": 36.46, "color": "0600ffff"}, {"name": "bone22", "parent": "root", "x": -37.82, "y": 46.43, "color": "0600ffff"}, {"name": "tree_Ra", "parent": "root", "x": 282.96, "y": 203.52}, {"name": "tree_Ra2", "parent": "tree_Ra", "length": 37.44, "rotation": 56.96, "x": 3.27, "y": 3.89}, {"name": "tree_Ra3", "parent": "tree_Ra2", "length": 32.69, "rotation": 16.02, "x": 37.44}, {"name": "tree_Ra7", "parent": "tree_Ra3", "length": 27.88, "rotation": -44.71, "x": 37.67, "y": -3.81}, {"name": "tree_Ra8", "parent": "tree_Ra7", "length": 35.01, "rotation": -18.94, "x": 27.88}, {"name": "tree_Ra4", "parent": "tree_Ra3", "length": 30.96, "rotation": 43.79, "x": 39.93, "y": 4.28}, {"name": "tree_Ra5", "parent": "tree_Ra4", "length": 29.66, "rotation": 24.31, "x": 30.96}, {"name": "tree_Rb", "parent": "root", "x": 245.69, "y": 233.6}, {"name": "tree_Rb2", "parent": "tree_Rb", "length": 29.22, "rotation": 85.2, "x": 0.41, "y": 9.16}, {"name": "tree_Rb3", "parent": "tree_Rb2", "length": 28.2, "rotation": -0.17, "x": 29.22}, {"name": "tree_Rb4", "parent": "tree_Rb3", "length": 28.32, "rotation": 2.91, "x": 28.2}, {"name": "tree_Rb5", "parent": "tree_Rb4", "length": 34.04, "rotation": 4.8, "x": 28.32}, {"name": "tree_Rc", "parent": "root", "x": 152.45, "y": 319.1}, {"name": "tree_Rc2", "parent": "tree_Rc", "length": 45.4, "rotation": 64.68, "x": 1.6, "y": 5.01}, {"name": "tree_Rc3", "parent": "tree_Rc2", "length": 42.2, "rotation": 10.76, "x": 45.4}, {"name": "tree_Rc4", "parent": "tree_Rc3", "length": 26.13, "rotation": 11.36, "x": 42.2}, {"name": "tree_Rc5", "parent": "tree_Rc4", "length": 31.89, "rotation": 83.45, "x": 28.4, "y": 8.41}, {"name": "tree_Rc6", "parent": "tree_Rc5", "length": 52.69, "rotation": 7.58, "x": 31.89}, {"name": "tree_Rc7", "parent": "tree_Rc4", "length": 30.1, "rotation": -94.44, "x": 24.33, "y": -11.87}, {"name": "tree_Rc8", "parent": "tree_Rc7", "length": 34.58, "rotation": -20.7, "x": 30.1}, {"name": "tree_Lb", "parent": "root", "x": -215.92, "y": 264.23}, {"name": "tree_Lb2", "parent": "tree_Lb", "length": 47.89, "rotation": 87.21, "x": -0.2, "y": 5.33}, {"name": "tree_Lb3", "parent": "tree_Lb2", "length": 48.62, "rotation": -4.1, "x": 47.89}, {"name": "tree_Lb4", "parent": "tree_Lb3", "length": 25.42, "rotation": -4.35, "x": 48.62}, {"name": "tree_Lb5", "parent": "tree_Lb4", "length": 25.16, "rotation": -45.34, "x": 25.43}, {"name": "tree_Lb6", "parent": "tree_Lb5", "length": 28.03, "rotation": -22.92, "x": 25.16}, {"name": "tree_Lb7", "parent": "tree_Lb4", "length": 17.21, "rotation": 47.63, "x": 28.06, "y": 3.89}, {"name": "tree_Lb8", "parent": "tree_Lb7", "length": 29.54, "rotation": -9.69, "x": 17.21}, {"name": "tree_La", "parent": "root", "x": -284.51, "y": 261.82}, {"name": "tree_La2", "parent": "tree_La", "length": 40.27, "rotation": 100.86, "x": -1.84, "y": 7.59}, {"name": "tree_La3", "parent": "tree_La2", "length": 38.93, "rotation": -3.74, "x": 40.27}, {"name": "tree_La4", "parent": "tree_La3", "length": 30.17, "rotation": 0.76, "x": 38.93}, {"name": "tree_La5", "parent": "tree_La4", "length": 47.74, "rotation": -107.03, "x": 30.59, "y": -7.02}, {"name": "tree_La6", "parent": "tree_La4", "length": 39.36, "rotation": 114.23, "x": 25.72, "y": 14.78}, {"name": "sign", "parent": "root", "length": 104.31, "rotation": 91.77, "x": -350.31, "y": 182.25}, {"name": "bone23", "parent": "root", "x": 153.91, "y": 129.15}, {"name": "bone24", "parent": "root", "x": -196.94, "y": 194.63}, {"name": "bone25", "parent": "root", "x": -3.29, "y": 547.89, "scaleY": 0.3762}, {"name": "plane", "parent": "bone25", "length": 202.29, "x": -0.01, "y": -0.03, "scaleX": 1.0715, "scaleY": 2.0783}, {"name": "bone26", "parent": "root", "x": 121.31, "y": 110.75}, {"name": "bone27", "parent": "root", "x": 192.44, "y": 151.72}, {"name": "bone28", "parent": "root", "length": 64.69, "rotation": 87.45, "x": 192.75, "y": 285.31}, {"name": "bone29", "parent": "root", "x": -3.29, "y": 547.89, "scaleY": 0.3762}, {"name": "plane2", "parent": "bone29", "length": 202.29, "rotation": -60, "x": -0.01, "y": -0.03, "scaleX": 1.0715, "scaleY": 2.0783}, {"name": "bone30", "parent": "root", "x": -3.29, "y": 547.89, "scaleY": 0.3762}, {"name": "plane3", "parent": "bone30", "length": 202.29, "rotation": -120, "x": -0.01, "y": -0.03, "scaleX": 1.0715, "scaleY": 2.0783}, {"name": "bone31", "parent": "root", "x": -3.29, "y": 547.89, "scaleY": 0.3762}, {"name": "plane4", "parent": "bone31", "length": 202.29, "rotation": -180, "x": -0.01, "y": -0.03, "scaleX": 1.0715, "scaleY": 2.0783}, {"name": "bone32", "parent": "root", "x": -3.29, "y": 547.89, "scaleY": 0.3762}, {"name": "plane5", "parent": "bone32", "length": 202.29, "rotation": -240, "x": -0.01, "y": -0.03, "scaleX": 1.0715, "scaleY": 2.0783}, {"name": "bone33", "parent": "root", "x": -3.29, "y": 547.89, "scaleY": 0.3762}, {"name": "plane6", "parent": "bone33", "length": 202.29, "rotation": -300, "x": -0.01, "y": -0.03, "scaleX": 1.0715, "scaleY": 2.0783}], "slots": [{"name": "room", "bone": "root", "attachment": "room"}, {"name": "tree_Rc", "bone": "tree_Rc", "attachment": "tree_Rc"}, {"name": "grass_B", "bone": "root", "attachment": "grass_B"}, {"name": "tree_Rb", "bone": "tree_Rb", "attachment": "tree_Rb"}, {"name": "tree_Ra", "bone": "tree_Ra", "attachment": "tree_Ra"}, {"name": "tree_Lb", "bone": "tree_Lb", "attachment": "tree_Lb"}, {"name": "tree_La", "bone": "tree_La", "attachment": "tree_La"}, {"name": "sign", "bone": "sign", "attachment": "sign"}, {"name": "grass_L", "bone": "root", "attachment": "grass_L"}, {"name": "grass_R", "bone": "root", "attachment": "grass_R"}, {"name": "room2", "bone": "root", "attachment": "room"}, {"name": "glass_L", "bone": "root", "attachment": "glass_L"}, {"name": "glass_R", "bone": "root", "attachment": "glass_R"}, {"name": "plane", "bone": "plane", "attachment": "plane"}, {"name": "plane2", "bone": "plane2", "attachment": "plane"}, {"name": "plane3", "bone": "plane3", "attachment": "plane"}, {"name": "plane4", "bone": "plane4", "attachment": "plane"}, {"name": "plane5", "bone": "plane5", "attachment": "plane"}, {"name": "plane6", "bone": "plane6", "attachment": "plane"}, {"name": "room3", "bone": "root", "attachment": "room"}], "skins": [{"name": "default", "attachments": {"glass_L": {"glass_L": {"type": "mesh", "uvs": [0.92403, 0.21041, 0.92627, 0.3068, 0.14335, 0.94172, 0.07687, 0.91909, 0.04592, 0.71975, 0.13535, 0.52946, 0.48994, 0.21568, 0.73425, 0.00695], "triangles": [6, 7, 0, 6, 0, 1, 2, 3, 4, 1, 2, 5, 2, 4, 5, 1, 5, 6], "vertices": [-329.71, 221.67, -329.62, 216.37, -360.16, 181.45, -362.75, 182.69, -363.96, 193.65, -360.47, 204.12, -346.64, 221.38, -337.11, 232.86], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 39, "height": 55}}, "glass_R": {"glass_R": {"type": "mesh", "uvs": [0.56516, 0.24782, 0.81881, 0.50464, 0.96619, 0.78448, 0.97514, 0.85598, 0.8548, 0.98259, 0.75161, 0.83489, 0.47669, 0.51719, 0.2958, 0.4033, 0.02745, 0.29506, 0.03242, 0.19924, 0.04982, 0.04212, 0.13232, 0.04232], "triangles": [9, 10, 11, 7, 11, 0, 9, 11, 7, 8, 9, 7, 6, 7, 0, 6, 0, 1, 5, 6, 1, 5, 1, 2, 4, 5, 2, 4, 2, 3], "vertices": [254.2, 246.89, 279.32, 228.92, 293.91, 209.33, 294.79, 204.32, 282.88, 195.46, 272.66, 205.8, 245.45, 228.04, 227.54, 236.01, 200.97, 243.59, 201.46, 250.29, 203.19, 261.29, 211.35, 261.28], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 99, "height": 70}}, "grass_B": {"grass_B": {"type": "mesh", "uvs": [0.65292, 0.01383, 0.79259, 0.12843, 0.91311, 0.27479, 0.9888, 0.55025, 0.98899, 0.79368, 0.9139, 0.9138, 0.62689, 1, 0.54552, 1, 0.19404, 0.93971, 0.05456, 0.77062, 0.00801, 0.58303, 0.02324, 0.33745, 0.23584, 0.08023, 0.40943, 0.01318, 0.22512, 0.73139, 0.22244, 0.44293, 0.41108, 0.22741, 0.68668, 0.27764, 0.75625, 0.55313, 0.71478, 0.83834, 0.43516, 0.79297, 0.49403, 0.5272], "triangles": [20, 14, 21, 19, 21, 18, 19, 18, 4, 20, 21, 19, 5, 19, 4, 8, 9, 14, 8, 14, 20, 7, 20, 19, 8, 20, 7, 6, 7, 19, 6, 19, 5, 14, 15, 21, 16, 13, 0, 12, 13, 16, 17, 0, 1, 17, 1, 2, 16, 0, 17, 15, 11, 12, 15, 12, 16, 10, 11, 15, 18, 2, 3, 21, 16, 17, 15, 16, 21, 21, 17, 18, 10, 15, 14, 9, 10, 14, 18, 3, 4, 18, 17, 2], "vertices": [2, 64, 66.4, -8.71, 0.97338, 0, 204.4, 351.26, 0.02662, 2, 64, 58.81, -21.07, 0.90835, 0, 216.42, 343.12, 0.09165, 2, 64, 48.89, -31.89, 0.8459, 0, 226.78, 332.73, 0.1541, 2, 64, 29.64, -39.26, 0.70307, 0, 233.29, 313.17, 0.29693, 2, 64, 12.37, -40.05, 0.58408, 0, 233.31, 295.89, 0.41592, 2, 64, 3.57, -33.98, 0.46641, 0, 226.85, 287.36, 0.53359, 2, 64, -3.65, -9.59, 0.117, 0, 202.17, 281.24, 0.883, 2, 64, -3.96, -2.6, 0.02014, 0, 195.17, 281.24, 0.97986, 2, 64, -1.03, 27.79, 0.36275, 0, 164.94, 285.52, 0.63725, 2, 64, 10.43, 40.3, 0.56871, 0, 152.95, 297.53, 0.43129, 2, 64, 23.56, 44.9, 0.70663, 0, 148.94, 310.85, 0.29337, 2, 64, 41.04, 44.36, 0.85526, 0, 150.25, 328.28, 0.14474, 2, 64, 60.1, 26.91, 0.94827, 0, 168.54, 346.54, 0.05173, 2, 64, 65.52, 12.21, 0.96657, 0, 183.46, 351.31, 0.03343, 2, 64, 13.87, 25.78, 0.41162, 0, 167.61, 300.31, 0.58838, 2, 64, 34.32, 26.92, 0.6306, 0, 167.38, 320.79, 0.3694, 2, 64, 50.33, 11.39, 0.75713, 0, 183.61, 336.09, 0.24287, 2, 64, 47.82, -12.45, 0.72871, 0, 207.31, 332.53, 0.27129, 2, 64, 28.54, -19.29, 0.51441, 0, 213.29, 312.97, 0.48559, 2, 64, 8.16, -16.63, 0.27136, 0, 209.72, 292.72, 0.72864, 2, 64, 10.3, 7.53, 0.20787, 0, 185.68, 295.94, 0.79213, 2, 64, 29.38, 3.32, 0.45471, 0, 190.74, 314.81, 0.54529], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 86, "height": 71}}, "grass_L": {"grass_L": {"type": "mesh", "uvs": [0.37696, 0.00795, 0.52289, 0.03723, 0.66881, 0.06651, 0.80327, 0.18813, 0.92549, 0.29766, 0.99541, 0.42521, 0.96418, 0.61229, 0.89919, 0.74977, 0.78571, 0.8923, 0.62388, 0.99174, 0.47557, 0.96247, 0.32727, 0.9332, 0.215, 0.82961, 0.0893, 0.72042, 0.00181, 0.57818, 0.03386, 0.39781, 0.07487, 0.29859, 0.2025, 0.11177, 0.18496, 0.56066, 0.26702, 0.34948, 0.37167, 0.20208, 0.31816, 0.66129, 0.48941, 0.78035, 0.66661, 0.79594, 0.80457, 0.55499, 0.67256, 0.34806, 0.54174, 0.24176, 0.41687, 0.43594, 0.58812, 0.58476], "triangles": [24, 4, 5, 7, 24, 6, 23, 24, 7, 10, 22, 9, 11, 21, 22, 12, 18, 21, 13, 18, 12, 13, 14, 18, 14, 15, 18, 12, 21, 11, 10, 11, 22, 9, 22, 23, 9, 23, 8, 8, 23, 7, 6, 24, 5, 22, 28, 23, 23, 28, 24, 22, 21, 28, 21, 27, 28, 21, 18, 27, 28, 27, 25, 28, 25, 24, 25, 27, 26, 18, 15, 19, 18, 19, 27, 19, 15, 16, 24, 25, 4, 4, 25, 3, 19, 20, 27, 27, 20, 26, 16, 17, 19, 19, 17, 20, 26, 2, 25, 25, 2, 3, 20, 1, 26, 26, 1, 2, 20, 0, 1, 20, 17, 0], "vertices": [2, 59, -11.01, 31.03, 0.85455, 0, -207.95, 225.66, 0.14545, 2, 59, 1.68, 28.89, 0.88626, 0, -195.26, 223.52, 0.11374, 2, 59, 14.38, 26.75, 0.83708, 0, -182.56, 221.39, 0.16292, 2, 59, 26.08, 17.87, 0.73283, 0, -170.86, 212.51, 0.26717, 2, 59, 36.71, 9.88, 0.34893, 0, -160.23, 204.51, 0.65107, 2, 59, 42.79, 0.57, 0.05055, 0, -154.15, 195.2, 0.94945, 2, 59, 40.07, -13.09, 0.01046, 0, -156.86, 181.54, 0.98954, 2, 59, 34.42, -23.13, 0.01917, 0, -162.52, 171.51, 0.98083, 2, 59, 24.55, -33.53, 0.00353, 0, -172.39, 161.1, 0.99647, 2, 59, 10.47, -40.79, 0.00782, 0, -186.47, 153.84, 0.99218, 2, 59, -2.43, -38.65, 0.02757, 0, -199.37, 155.98, 0.97243, 2, 59, -15.34, -36.52, 0.03671, 0, -212.27, 158.12, 0.96329, 2, 59, -25.1, -28.95, 0.01194, 0, -222.04, 165.68, 0.98806, 2, 59, -36.04, -20.98, 0.00201, 0, -232.98, 173.65, 0.99799, 2, 59, -43.65, -10.6, 0.00695, 0, -240.59, 184.03, 0.99305, 2, 59, -40.86, 2.57, 0.31898, 0, -237.8, 197.2, 0.68102, 2, 59, -37.29, 9.81, 0.5539, 0, -234.23, 204.44, 0.4461, 2, 59, -26.19, 23.45, 0.79134, 0, -223.13, 218.08, 0.20866, 2, 59, -27.72, -9.32, 0.415, 0, -224.66, 185.31, 0.585, 2, 59, -20.58, 6.1, 0.8245, 0, -217.52, 200.73, 0.1755, 1, 59, -11.47, 16.86, 1, 2, 59, -16.13, -16.67, 0.45478, 0, -213.07, 177.97, 0.54522, 2, 59, -1.23, -25.36, 0.41573, 0, -198.17, 169.28, 0.58427, 2, 59, 14.19, -26.5, 0.30955, 0, -182.75, 168.14, 0.69045, 2, 59, 26.19, -8.91, 0.40576, 0, -170.75, 185.73, 0.59424, 2, 59, 14.7, 6.2, 0.88373, 0, -182.23, 200.83, 0.11627, 1, 59, 3.32, 13.96, 1, 2, 59, -7.54, -0.22, 0.9916, 0, -204.48, 194.42, 0.0084, 2, 59, 7.36, -11.08, 0.78663, 0, -189.58, 183.55, 0.21337], "hull": 18, "edges": [0, 34, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 28, 30, 30, 32, 32, 34, 22, 24, 24, 26, 18, 20, 20, 22, 0, 2, 2, 4, 26, 28], "width": 87, "height": 73}}, "grass_R": {"grass_R": {"type": "mesh", "uvs": [0.97643, 0.09917, 0.99961, 0.17651, 0.99241, 0.34059, 0.95069, 0.45028, 0.8454, 0.49679, 0.77225, 0.55573, 0.69494, 0.5982, 0.60616, 0.68281, 0.54705, 0.7587, 0.45729, 0.84587, 0.34261, 0.92909, 0.25256, 0.98734, 0.16978, 0.96098, 0.09622, 0.94634, 0.0226, 0.88717, 0.0063, 0.76737, 0.02928, 0.64521, 0.09611, 0.51608, 0.17872, 0.45927, 0.26132, 0.40246, 0.30758, 0.38255, 0.36834, 0.32207, 0.42529, 0.27818, 0.48077, 0.23465, 0.52175, 0.15033, 0.60003, 0.08581, 0.67831, 0.02129, 0.78207, 0.01618, 0.83543, 0.01356, 0.91152, 0.00982, 0.26059, 0.78519, 0.13757, 0.784, 0.17504, 0.61607, 0.28584, 0.61961, 0.35347, 0.73906, 0.48219, 0.65628, 0.57588, 0.55693, 0.66224, 0.46705, 0.78445, 0.35943, 0.89118, 0.26009, 0.83496, 0.11581, 0.7209, 0.16666, 0.63699, 0.2731, 0.54004, 0.36653, 0.4496, 0.45286, 0.37383, 0.53446], "triangles": [39, 0, 1, 3, 4, 39, 37, 42, 38, 5, 38, 4, 35, 44, 36, 7, 36, 6, 35, 36, 7, 12, 31, 30, 15, 16, 31, 14, 15, 31, 13, 14, 31, 13, 31, 12, 11, 12, 30, 10, 11, 30, 10, 30, 34, 10, 34, 9, 9, 34, 35, 9, 35, 8, 8, 35, 7, 34, 45, 35, 36, 37, 6, 6, 37, 5, 5, 37, 38, 36, 43, 37, 4, 38, 39, 3, 39, 2, 2, 39, 1, 40, 27, 28, 41, 26, 27, 41, 27, 40, 25, 26, 41, 29, 40, 28, 0, 40, 29, 0, 39, 40, 42, 25, 41, 38, 41, 40, 38, 40, 39, 42, 41, 38, 24, 25, 42, 23, 24, 42, 43, 23, 42, 43, 44, 22, 43, 22, 23, 21, 22, 44, 43, 42, 37, 45, 21, 44, 44, 43, 36, 45, 44, 35, 20, 21, 45, 32, 17, 18, 45, 33, 19, 45, 19, 20, 18, 19, 33, 32, 18, 33, 16, 17, 32, 34, 33, 45, 31, 16, 32, 30, 32, 33, 30, 33, 34, 31, 32, 30], "vertices": [2, 63, 24.63, 4.3, 0.47803, 0, 217.07, 156.02, 0.52197, 3, 58, 66.29, 19.67, 0.0002, 63, 27.76, -2.89, 0.25166, 0, 220.2, 148.82, 0.74814, 3, 58, 65.32, 4.41, 9e-05, 63, 26.79, -18.15, 0.00262, 0, 219.23, 133.57, 0.99729, 3, 58, 59.69, -5.79, 0.00369, 63, 21.15, -28.36, 0.04051, 0, 213.6, 123.36, 0.9558, 3, 58, 45.47, -10.11, 0.00571, 63, 6.94, -32.68, 0.01638, 0, 199.38, 119.04, 0.97792, 4, 62, 68.2, 2.8, 9e-05, 58, 35.6, -15.59, 0.02358, 63, -2.94, -38.16, 0.02525, 0, 189.51, 113.56, 0.95108, 4, 62, 57.76, -1.15, 7e-05, 58, 25.16, -19.54, 0.00169, 63, -13.37, -42.11, 0.0007, 0, 179.07, 109.61, 0.99754, 4, 62, 45.78, -9.01, 0.00281, 58, 13.18, -27.41, 0.01135, 63, -25.36, -49.98, 0.00104, 0, 167.09, 101.74, 0.9848, 4, 62, 37.8, -16.07, 0.01869, 58, 5.19, -34.47, 0.02917, 63, -33.34, -57.04, 0.00059, 0, 159.1, 94.68, 0.95155, 4, 62, 25.68, -24.18, 0.0222, 58, -6.92, -42.58, 0.01087, 63, -45.45, -65.15, 0, 0, 146.99, 86.57, 0.96693, 3, 62, 10.2, -31.92, 0.04574, 58, -22.4, -50.32, 0.00419, 0, 131.51, 78.84, 0.95007, 3, 62, -1.96, -37.34, 0.01147, 58, -34.56, -55.73, 0.00017, 0, 119.35, 73.42, 0.98836, 3, 62, -13.13, -34.88, 0.03114, 58, -45.74, -53.28, 3e-05, 0, 108.17, 75.87, 0.96883, 2, 62, -23.07, -33.52, 0.00303, 0, 98.24, 77.23, 0.99697, 2, 62, -33, -28.02, 0.02141, 0, 88.3, 82.73, 0.97859, 2, 62, -35.2, -16.88, 0.28757, 0, 86.1, 93.88, 0.71243, 2, 62, -32.1, -5.52, 0.63037, 0, 89.21, 105.24, 0.36963, 1, 62, -23.08, 6.49, 1, 2, 62, -11.93, 11.77, 0.99138, 58, -44.53, -6.62, 0.00862, 2, 62, -0.78, 17.06, 0.8392, 58, -33.38, -1.34, 0.1608, 2, 62, 5.47, 18.91, 0.64342, 58, -27.13, 0.51, 0.35658, 2, 62, 13.67, 24.53, 0.30852, 58, -18.93, 6.14, 0.69148, 3, 62, 21.36, 28.62, 0.10292, 58, -11.24, 10.22, 0.88891, 63, -49.78, -12.35, 0.00818, 3, 62, 28.85, 32.66, 0.00954, 58, -3.75, 14.27, 0.89971, 63, -42.28, -8.3, 0.09075, 2, 58, 1.78, 22.11, 0.74009, 63, -36.75, -0.46, 0.25991, 2, 58, 12.35, 28.11, 0.47396, 63, -26.18, 5.54, 0.52604, 2, 58, 22.92, 34.11, 0.20583, 63, -15.62, 11.54, 0.79417, 2, 58, 36.92, 34.58, 0.01889, 63, -1.61, 12.02, 0.98111, 2, 63, 5.59, 12.26, 0.98077, 0, 198.04, 163.98, 0.01923, 2, 63, 15.87, 12.61, 0.8042, 0, 208.31, 164.33, 0.1958, 3, 62, -0.88, -18.54, 0.5221, 58, -33.48, -36.93, 0.01371, 0, 120.43, 92.22, 0.46419, 2, 62, -17.48, -18.43, 0.46767, 0, 103.82, 92.33, 0.53233, 2, 62, -12.42, -2.81, 0.94489, 0, 108.88, 107.95, 0.05511, 3, 62, 2.53, -3.14, 0.86093, 58, -30.07, -21.54, 0.02805, 0, 123.84, 107.62, 0.11102, 3, 62, 11.66, -14.25, 0.40239, 58, -20.94, -32.64, 0.07997, 0, 132.97, 96.51, 0.51765, 4, 62, 29.04, -6.55, 0.17661, 58, -3.56, -24.95, 0.24526, 63, -42.09, -47.51, 0.00186, 0, 150.35, 104.21, 0.57626, 4, 62, 41.69, 2.69, 0.03833, 58, 9.09, -15.71, 0.28401, 63, -29.45, -38.27, 0.02709, 0, 163, 113.45, 0.65057, 4, 62, 53.35, 11.05, 0.00345, 58, 20.75, -7.35, 0.25317, 63, -17.79, -29.92, 0.11076, 0, 174.66, 121.8, 0.63262, 3, 58, 37.24, 2.66, 0.10077, 63, -1.29, -19.91, 0.27808, 0, 191.15, 131.81, 0.62115, 3, 58, 51.65, 11.9, 0.01502, 63, 13.12, -10.67, 0.43488, 0, 205.56, 141.05, 0.55009, 2, 63, 5.53, 2.75, 0.80759, 0, 197.97, 154.47, 0.19241, 3, 58, 28.67, 20.59, 0.18166, 63, -9.87, -1.98, 0.77234, 0, 182.57, 149.74, 0.04601, 3, 58, 17.34, 10.69, 0.50906, 63, -21.2, -11.88, 0.36008, 0, 171.25, 139.84, 0.13087, 3, 58, 4.25, 2, 0.80556, 63, -34.28, -20.57, 0.051, 0, 158.16, 131.15, 0.14344, 3, 62, 24.64, 12.37, 0.18852, 58, -7.96, -6.03, 0.70127, 0, 145.95, 123.12, 0.1102, 3, 62, 14.41, 4.78, 0.56831, 58, -18.19, -13.62, 0.36514, 0, 135.72, 115.54, 0.06655], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 10, 12, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 44, 46, 46, 48, 16, 18, 6, 8, 8, 10, 12, 14, 14, 16, 18, 20, 20, 22, 34, 36, 36, 38, 40, 42, 42, 44, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 135, "height": 93}}, "plane": {"plane": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [201.56, -7.47, 1.56, -7.62, 1.55, 7.38, 201.55, 7.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 15}}, "plane2": {"plane": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [201.56, -7.47, 1.56, -7.62, 1.55, 7.38, 201.55, 7.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 15}}, "plane3": {"plane": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [201.56, -7.47, 1.56, -7.62, 1.55, 7.38, 201.55, 7.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 15}}, "plane4": {"plane": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [201.56, -7.47, 1.56, -7.62, 1.55, 7.38, 201.55, 7.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 15}}, "plane5": {"plane": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [201.56, -7.47, 1.56, -7.62, 1.55, 7.38, 201.55, 7.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 15}}, "plane6": {"plane": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [201.56, -7.47, 1.56, -7.62, 1.55, 7.38, 201.55, 7.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 15}}, "room": {"room": {"type": "mesh", "uvs": [0.76898, 0, 0.78801, 0.0744, 0.73499, 0.31925, 0.72086, 0.34, 0.82387, 0.35909, 1, 0.50651, 1, 0.61015, 0.95928, 0.8204, 0.64616, 0.99884, 0.51361, 0.99973, 0.12504, 0.91214, 0.00331, 0.79996, 0, 0.5654, 0.09941, 0.42204, 0.29219, 0.34977, 0.30045, 0.34888, 0.3343, 0.24271, 0.52011, 0.06242, 0.64303, 0.10676, 0.71702, 0.08254, 0.75032, 0, 0.08733, 0.4683, 0.06875, 0.48033, 0.05079, 0.51173, 0.05048, 0.52464, 0.02477, 0.5534, 0.02415, 0.57454, 0.03623, 0.57805, 0.02199, 0.60594, 0.03159, 0.63587, 0.01115, 0.68165, 0.0127, 0.69545, 0.02694, 0.72303, 0.01031, 0.7396, 0.0166, 0.77821, 0.03945, 0.79698, 0.06019, 0.83366, 0.10589, 0.86216, 0.1403, 0.88461, 0.16151, 0.90163, 0.20208, 0.91135, 0.24194, 0.91621, 0.29043, 0.90273, 0.33006, 0.90317, 0.36892, 0.91412, 0.40506, 0.93621, 0.43816, 0.95432, 0.48409, 0.97421, 0.53261, 0.98594, 0.57924, 0.98903, 0.62027, 0.98329, 0.66083, 0.96782, 0.69254, 0.9391, 0.71832, 0.91957, 0.75422, 0.90101, 0.79712, 0.86898, 0.84957, 0.85594, 0.90855, 0.82767, 0.93955, 0.79216, 0.94538, 0.76123, 0.92463, 0.73582, 0.95214, 0.72433, 0.97172, 0.70025, 0.97941, 0.67529, 0.98951, 0.63419, 0.99324, 0.59686, 0.98345, 0.58802, 0.98765, 0.56703, 0.9725, 0.55201, 0.969, 0.53544, 0.90933, 0.4986, 0.92285, 0.55163, 0.92168, 0.61294, 0.91515, 0.66596, 0.87296, 0.72053, 0.84941, 0.74771, 0.77831, 0.80515, 0.7534, 0.81256, 0.68999, 0.84658, 0.63824, 0.86669, 0.59232, 0.88083, 0.53497, 0.88635, 0.41248, 0.87945, 0.36662, 0.86988, 0.30624, 0.84956, 0.21299, 0.82216, 0.20573, 0.8073, 0.17822, 0.79228, 0.13137, 0.75516, 0.11784, 0.7302, 0.11161, 0.68943, 0.0792, 0.65479, 0.06032, 0.6159, 0.06242, 0.54521, 0.07057, 0.50732, 0.2679, 0.64298, 0.30448, 0.628, 0.3316, 0.61414, 0.35555, 0.61089, 0.37639, 0.615, 0.40907, 0.62759, 0.39315, 0.62151, 0.42821, 0.63238, 0.44991, 0.63477, 0.46998, 0.63526, 0.5042, 0.63717, 0.51732, 0.64648, 0.50482, 0.66176, 0.49395, 0.66791, 0.46508, 0.67336, 0.42809, 0.6749, 0.39365, 0.67327, 0.36328, 0.67031, 0.33191, 0.66558, 0.30604, 0.65906, 0.28327, 0.6514, 0.28811, 0.63517, 0.31864, 0.62087, 0.34088, 0.61129, 0.49053, 0.63532, 0.30441, 0.64633, 0.32266, 0.63816, 0.34238, 0.62848, 0.35684, 0.62349, 0.37244, 0.63026, 0.35155, 0.64106, 0.32716, 0.65241, 0.35436, 0.65681, 0.37744, 0.64712, 0.3957, 0.6348, 0.37554, 0.6611, 0.4158, 0.64048, 0.40103, 0.65074, 0.425, 0.66235, 0.43041, 0.64422, 0.45276, 0.64699, 0.45305, 0.66262, 0.47481, 0.64657, 0.47612, 0.65985, 0.49409, 0.64643, 0.49862, 0.65543, 0.3987, 0.66246, 0.05303, 0.54062, 0.06383, 0.50349, 0.05885, 0.52337, 0.04655, 0.55877, 0.05059, 0.57669, 0.04445, 0.59353, 0.05993, 0.57471, 0.05932, 0.5953, 0.04396, 0.61557, 0.05034, 0.63483, 0.03516, 0.6639, 0.05848, 0.66061, 0.03967, 0.69235, 0.07349, 0.68249, 0.0504, 0.72029, 0.08838, 0.70944, 0.06489, 0.74539, 0.09585, 0.73282, 0.04199, 0.75575, 0.07243, 0.77695, 0.10443, 0.76142, 0.08374, 0.81347, 0.12199, 0.78685, 0.1147, 0.83394, 0.14956, 0.81052, 0.15479, 0.77372, 0.14462, 0.85834, 0.1787, 0.83098, 0.17357, 0.87379, 0.20167, 0.8484, 0.20973, 0.88735, 0.22795, 0.85703, 0.24824, 0.88858, 0.26385, 0.86097, 0.29116, 0.87576, 0.32697, 0.87912, 0.33643, 0.85972, 0.36183, 0.88996, 0.39811, 0.89721, 0.43089, 0.90116, 0.42855, 0.92359, 0.45795, 0.88196, 0.46214, 0.93537, 0.4715, 0.90554, 0.4914, 0.88387, 0.51269, 0.9111, 0.50437, 0.94561, 0.54974, 0.91243, 0.5448, 0.95558, 0.58044, 0.95706, 0.58434, 0.91465, 0.61504, 0.94794, 0.61322, 0.90553, 0.64105, 0.89296, 0.65562, 0.92871, 0.69308, 0.91194, 0.67851, 0.88137, 0.7103, 0.86872, 0.72487, 0.89387, 0.74048, 0.85146, 0.75219, 0.87636, 0.76858, 0.83396, 0.78693, 0.85295, 0.7217, 0.82957, 0.79981, 0.81844, 0.8175, 0.83964, 0.87906, 0.84181, 0.86068, 0.82189, 0.83597, 0.79896, 0.81386, 0.77643, 0.86596, 0.77946, 0.88938, 0.8046, 0.88756, 0.75776, 0.91331, 0.77329, 0.89903, 0.72877, 0.89406, 0.69325, 0.92502, 0.70608, 0.94401, 0.68364, 0.95256, 0.65413, 0.91842, 0.63945, 0.95698, 0.62183, 0.95405, 0.5884, 0.92227, 0.58228, 0.94937, 0.5529, 0.94957, 0.52344, 0.93887, 0.53854, 0.26304, 0.63603, 0.26304, 0.6239, 0.329, 0.59844, 0.35015, 0.59234, 0.37996, 0.5958, 0.41737, 0.60766, 0.45366, 0.61566, 0.48844, 0.61513, 0.51081, 0.62006, 0.5267, 0.63419], "triangles": [115, 116, 120, 122, 117, 97, 97, 118, 122, 100, 101, 233, 101, 99, 232, 98, 118, 231, 117, 230, 97, 104, 234, 235, 119, 235, 236, 118, 230, 231, 230, 96, 229, 228, 229, 116, 20, 0, 1, 19, 20, 1, 2, 19, 1, 18, 19, 2, 3, 18, 2, 5, 70, 4, 21, 22, 13, 22, 12, 13, 94, 22, 21, 143, 22, 94, 143, 23, 22, 144, 23, 143, 144, 143, 94, 226, 70, 5, 22, 23, 25, 24, 23, 144, 69, 226, 5, 227, 70, 226, 93, 144, 94, 71, 70, 227, 68, 69, 5, 69, 225, 227, 69, 227, 226, 225, 69, 68, 71, 227, 225, 24, 25, 23, 25, 24, 142, 22, 25, 12, 67, 68, 5, 26, 12, 25, 26, 25, 145, 148, 145, 93, 146, 145, 148, 27, 26, 145, 146, 27, 145, 224, 70, 71, 224, 71, 225, 67, 223, 68, 66, 223, 67, 15, 16, 3, 18, 16, 17, 3, 16, 18, 232, 231, 15, 149, 146, 148, 15, 233, 232, 6, 65, 67, 66, 67, 65, 231, 14, 15, 21, 13, 14, 14, 229, 21, 229, 94, 21, 229, 93, 94, 93, 229, 148, 148, 229, 149, 28, 12, 26, 28, 26, 27, 28, 27, 147, 15, 235, 233, 6, 67, 5, 74, 70, 224, 72, 224, 223, 3, 235, 15, 92, 149, 91, 150, 149, 92, 236, 235, 3, 230, 229, 14, 222, 66, 65, 64, 222, 65, 237, 236, 3, 6, 64, 65, 29, 28, 150, 105, 236, 237, 224, 72, 217, 221, 72, 222, 95, 228, 116, 106, 105, 237, 115, 95, 116, 229, 91, 149, 90, 91, 229, 151, 92, 91, 115, 120, 114, 107, 140, 106, 113, 126, 127, 114, 126, 113, 217, 72, 221, 73, 221, 220, 108, 140, 107, 113, 127, 112, 112, 130, 111, 109, 138, 108, 110, 136, 109, 111, 141, 110, 63, 220, 64, 30, 12, 28, 30, 28, 29, 30, 29, 152, 228, 90, 229, 155, 91, 90, 70, 3, 4, 73, 217, 221, 31, 30, 154, 62, 219, 63, 217, 73, 218, 224, 217, 74, 32, 31, 154, 61, 219, 62, 74, 217, 216, 95, 89, 90, 95, 90, 228, 157, 90, 89, 60, 218, 61, 30, 11, 12, 33, 30, 31, 33, 31, 32, 70, 74, 211, 167, 88, 89, 33, 32, 160, 75, 74, 214, 59, 60, 61, 162, 89, 88, 215, 60, 59, 95, 167, 89, 3, 70, 237, 74, 75, 211, 211, 237, 70, 34, 33, 160, 211, 75, 212, 164, 88, 167, 58, 215, 59, 87, 167, 95, 87, 95, 115, 35, 34, 160, 33, 11, 30, 11, 33, 34, 11, 34, 35, 211, 77, 237, 86, 87, 115, 166, 167, 87, 76, 77, 211, 205, 106, 237, 206, 76, 211, 62, 59, 61, 7, 59, 62, 58, 59, 7, 7, 62, 63, 6, 7, 63, 6, 63, 64, 114, 85, 86, 57, 213, 58, 57, 58, 7, 77, 205, 237, 78, 107, 106, 169, 87, 86, 169, 86, 85, 36, 35, 163, 203, 77, 76, 208, 213, 57, 205, 78, 106, 84, 114, 113, 84, 113, 112, 178, 84, 112, 114, 86, 115, 84, 85, 114, 201, 205, 77, 56, 209, 208, 111, 178, 112, 111, 83, 178, 84, 175, 85, 37, 36, 165, 79, 107, 78, 199, 78, 205, 55, 207, 56, 110, 83, 111, 177, 84, 178, 82, 83, 110, 80, 108, 107, 79, 80, 107, 198, 79, 78, 183, 110, 109, 82, 110, 183, 186, 183, 109, 109, 108, 186, 108, 81, 186, 38, 37, 168, 80, 81, 108, 83, 177, 178, 194, 80, 79, 180, 83, 82, 54, 202, 55, 181, 82, 183, 39, 38, 170, 42, 177, 43, 185, 183, 186, 187, 186, 81, 39, 170, 40, 10, 37, 38, 10, 38, 39, 36, 37, 10, 11, 35, 36, 11, 36, 10, 189, 81, 80, 43, 179, 44, 41, 174, 42, 40, 172, 41, 53, 200, 54, 44, 180, 45, 52, 197, 53, 45, 182, 46, 51, 196, 52, 46, 184, 47, 50, 193, 51, 47, 188, 48, 49, 191, 50, 48, 190, 49, 8, 50, 51, 8, 51, 52, 54, 8, 52, 54, 52, 53, 56, 8, 54, 56, 54, 55, 7, 8, 56, 208, 7, 56, 7, 208, 57, 9, 47, 48, 9, 48, 49, 8, 9, 49, 8, 49, 50, 46, 47, 9, 46, 41, 45, 42, 43, 44, 45, 42, 44, 41, 42, 45, 46, 40, 41, 9, 40, 46, 10, 39, 40, 9, 10, 40, 230, 14, 231, 98, 231, 232, 233, 101, 232, 102, 233, 234, 234, 233, 235, 116, 229, 96, 117, 96, 230, 97, 230, 118, 99, 98, 232, 100, 233, 102, 103, 102, 234, 103, 234, 104, 104, 235, 119, 105, 119, 236, 96, 117, 121, 123, 118, 98, 120, 116, 96, 120, 96, 121, 125, 126, 121, 120, 121, 126, 114, 120, 126, 123, 98, 99, 123, 122, 118, 124, 123, 99, 122, 121, 117, 125, 122, 123, 125, 123, 124, 125, 121, 122, 127, 125, 128, 126, 125, 127, 130, 127, 128, 141, 130, 128, 112, 127, 130, 111, 130, 141, 124, 99, 101, 129, 101, 100, 124, 101, 129, 129, 100, 131, 128, 124, 129, 125, 124, 128, 132, 129, 131, 128, 129, 132, 132, 141, 128, 131, 100, 102, 134, 102, 103, 131, 102, 134, 135, 103, 104, 134, 103, 135, 133, 131, 134, 135, 136, 134, 132, 131, 133, 141, 132, 133, 133, 136, 110, 134, 136, 133, 141, 133, 110, 139, 119, 105, 137, 104, 119, 137, 119, 139, 135, 104, 137, 140, 139, 105, 140, 105, 106, 138, 137, 139, 138, 139, 140, 138, 136, 135, 138, 135, 137, 108, 138, 140, 109, 136, 138, 223, 225, 68, 224, 225, 223, 222, 223, 66, 72, 223, 222, 220, 221, 222, 220, 222, 64, 219, 73, 220, 219, 220, 63, 218, 73, 219, 218, 219, 61, 216, 217, 218, 60, 216, 218, 214, 74, 216, 60, 214, 216, 215, 214, 60, 212, 75, 214, 210, 211, 212, 215, 212, 214, 213, 215, 58, 213, 212, 215, 209, 210, 212, 209, 212, 213, 207, 210, 209, 208, 209, 213, 56, 207, 209, 206, 211, 210, 203, 76, 206, 207, 206, 210, 201, 77, 203, 204, 203, 206, 204, 206, 207, 55, 204, 207, 204, 202, 201, 204, 201, 203, 202, 204, 55, 199, 205, 201, 199, 201, 202, 198, 78, 199, 195, 79, 198, 200, 199, 202, 200, 202, 54, 200, 197, 198, 200, 198, 199, 53, 197, 200, 196, 195, 198, 196, 198, 197, 52, 196, 197, 195, 194, 79, 192, 189, 80, 192, 80, 194, 194, 195, 196, 193, 194, 196, 192, 194, 193, 191, 189, 192, 191, 192, 193, 193, 196, 51, 191, 193, 50, 185, 186, 187, 187, 81, 189, 188, 185, 187, 184, 185, 188, 190, 187, 189, 188, 187, 190, 190, 189, 191, 47, 184, 188, 48, 188, 190, 49, 190, 191, 179, 83, 180, 180, 82, 181, 181, 183, 185, 44, 179, 180, 182, 180, 181, 182, 181, 185, 184, 182, 185, 45, 180, 182, 46, 182, 184, 175, 173, 85, 176, 175, 84, 176, 84, 177, 174, 173, 175, 174, 175, 176, 172, 173, 174, 179, 177, 83, 42, 174, 176, 42, 176, 177, 43, 177, 179, 41, 172, 174, 166, 87, 169, 165, 164, 166, 171, 169, 85, 171, 85, 173, 168, 165, 166, 168, 166, 169, 37, 165, 168, 170, 168, 169, 170, 169, 171, 38, 168, 170, 172, 171, 173, 170, 171, 172, 40, 170, 172, 160, 156, 158, 162, 159, 89, 158, 159, 162, 161, 158, 162, 160, 158, 161, 164, 162, 88, 35, 160, 161, 166, 164, 167, 164, 163, 161, 164, 161, 162, 35, 161, 163, 165, 163, 164, 165, 36, 163, 152, 151, 153, 155, 153, 91, 154, 152, 153, 154, 153, 155, 30, 152, 154, 157, 155, 90, 156, 154, 155, 156, 155, 157, 32, 154, 156, 159, 157, 89, 158, 156, 157, 158, 157, 159, 160, 32, 156, 147, 27, 146, 147, 146, 149, 150, 28, 147, 150, 147, 149, 151, 150, 92, 29, 150, 151, 153, 151, 91, 152, 29, 151, 142, 24, 144, 142, 144, 93, 145, 25, 142, 145, 142, 93], "vertices": [1, 0, 198.81, 620.24, 1, 1, 0, 214.66, 554.84, 1, 1, 0, 170.5, 339.62, 1, 1, 0, 158.73, 321.38, 1, 1, 0, 244.53, 304.6, 1, 1, 0, 391.25, 175.02, 1, 1, 0, 391.25, 83.92, 1, 1, 0, 357.33, -100.89, 1, 1, 0, 96.51, -257.74, 1, 1, 0, -13.91, -258.52, 1, 1, 0, -337.59, -181.53, 1, 1, 0, -438.99, -82.92, 1, 1, 0, -441.75, 123.26, 1, 1, 0, -358.94, 249.27, 1, 1, 0, -198.36, 312.79, 1, 1, 0, -191.47, 313.57, 1, 1, 0, -163.27, 406.9, 1, 1, 0, -8.5, 565.37, 1, 1, 0, 93.89, 526.4, 1, 1, 0, 155.53, 547.69, 1, 1, 0, 183.27, 620.24, 1, 1, 0, -369, 208.61, 1, 1, 0, -384.48, 198.03, 1, 1, 0, -399.44, 170.43, 1, 1, 0, -399.7, 159.08, 1, 1, 0, -421.11, 133.8, 1, 1, 0, -421.63, 115.22, 1, 1, 0, -411.57, 112.13, 1, 1, 0, -423.43, 87.62, 1, 1, 0, -415.44, 61.31, 1, 1, 0, -432.46, 21.07, 1, 1, 0, -431.17, 8.94, 1, 1, 0, -419.31, -15.31, 1, 1, 0, -433.16, -29.87, 1, 1, 0, -427.92, -63.8, 1, 1, 0, -408.89, -80.31, 1, 1, 0, -391.6, -112.54, 1, 1, 0, -353.54, -137.59, 1, 1, 0, -324.88, -157.34, 1, 1, 0, -307.21, -172.29, 1, 1, 0, -273.42, -180.83, 1, 1, 0, -240.21, -185.1, 1, 1, 0, -199.82, -173.26, 1, 1, 0, -166.8, -173.65, 1, 1, 0, -134.43, -183.27, 1, 1, 0, -104.33, -202.69, 1, 1, 0, -76.76, -218.61, 1, 1, 0, -38.5, -236.09, 1, 1, 0, 1.92, -246.4, 1, 1, 0, 40.76, -249.12, 1, 1, 0, 74.94, -244.07, 1, 1, 0, 108.73, -230.47, 1, 1, 0, 135.14, -205.23, 1, 1, 0, 156.61, -188.06, 1, 1, 0, 186.52, -171.75, 1, 1, 0, 222.25, -143.59, 1, 1, 0, 265.94, -132.13, 1, 1, 0, 315.07, -107.28, 1, 1, 0, 340.9, -76.07, 1, 1, 0, 345.75, -48.88, 1, 1, 0, 328.47, -26.55, 1, 1, 0, 351.38, -16.45, 1, 1, 0, 367.69, 4.72, 1, 1, 0, 374.1, 26.66, 1, 1, 0, 382.52, 62.78, 1, 1, 0, 385.63, 95.6, 1, 1, 0, 377.47, 103.37, 1, 1, 0, 380.97, 121.82, 1, 1, 0, 368.34, 135.02, 1, 1, 0, 365.43, 149.59, 1, 1, 0, 315.72, 181.97, 1, 1, 0, 326.99, 135.36, 1, 1, 0, 326.01, 81.46, 1, 1, 0, 320.58, 34.86, 1, 1, 0, 285.43, -13.11, 1, 1, 0, 265.81, -36.99, 1, 1, 0, 206.59, -87.48, 1, 1, 0, 185.84, -94, 1, 1, 0, 133.02, -123.91, 1, 1, 0, 89.91, -141.58, 1, 1, 0, 51.65, -154.01, 1, 1, 0, 3.88, -158.86, 1, 1, 0, -98.15, -152.8, 1, 1, 0, -136.35, -144.39, 1, 1, 0, -186.65, -126.52, 1, 1, 0, -264.33, -102.44, 1, 1, 0, -270.37, -89.37, 1, 1, 0, -293.29, -76.17, 1, 1, 0, -332.32, -43.55, 1, 1, 0, -343.58, -21.6, 1, 1, 0, -348.78, 14.23, 1, 1, 0, -375.77, 44.68, 1, 1, 0, -391.5, 78.86, 1, 1, 0, -389.75, 141, 1, 1, 0, -382.96, 174.31, 1, 1, 0, -218.59, 55.06, 1, 5, 22, -150.29, 21.79, 4e-05, 20, -109.65, 14.89, 0.00177, 16, -36.79, -3, 0.07491, 17, -10.97, 13.36, 0.2982, 0, -188.11, 68.23, 0.62508, 1, 0, -165.52, 80.41, 1, 6, 22, -107.76, 36.84, 0.00191, 21, -60.98, 46.81, 0, 20, -67.11, 29.93, 0.01274, 18, -26.72, 22.78, 0.06525, 16, 5.74, 12.04, 0.32851, 0, -145.58, 83.27, 0.59159, 5, 22, -90.39, 33.23, 0.00478, 20, -49.75, 26.32, 0.04154, 18, -9.36, 19.17, 0.34145, 16, 23.11, 8.43, 0.27821, 0, -128.21, 79.66, 0.33402, 6, 22, -63.17, 22.16, 0.00457, 21, -16.39, 32.13, 0.00567, 20, -22.52, 15.26, 0.24938, 18, 17.87, 8.1, 0.38352, 16, 50.33, -2.64, 0.02283, 0, -100.99, 68.59, 0.33403, 5, 22, -76.43, 27.5, 0.00614, 20, -35.79, 20.6, 0.1073, 18, 4.6, 13.44, 0.49116, 16, 37.07, 2.7, 0.10006, 0, -114.25, 73.93, 0.29534, 5, 22, -47.22, 17.94, 0.00868, 20, -6.58, 11.04, 0.51406, 18, 33.81, 3.88, 0.14544, 16, 66.28, -6.85, 0.01109, 0, -85.05, 64.38, 0.32073, 5, 22, -29.15, 15.85, 0.12285, 20, 11.49, 8.95, 0.51343, 18, 51.88, 1.8, 0.02641, 16, 84.35, -8.94, 0.01019, 0, -66.97, 62.29, 0.32712, 5, 22, -12.43, 15.41, 0.3584, 20, 28.21, 8.51, 0.29747, 18, 68.6, 1.35, 0.01874, 16, 101.07, -9.38, 0.01136, 0, -50.25, 61.84, 0.31403, 5, 22, 16.07, 13.73, 0.55019, 20, 56.72, 6.83, 0.05366, 18, 97.11, -0.33, 0.01743, 16, 129.57, -11.06, 0.01264, 0, -21.75, 60.17, 0.36608, 1, 0, -10.82, 51.99, 1, 1, 0, -21.23, 38.56, 1, 1, 0, -30.29, 33.14, 1, 1, 0, -54.33, 28.35, 1, 1, 0, -85.15, 27, 1, 1, 0, -113.84, 28.43, 1, 1, 0, -139.14, 31.04, 1, 1, 0, -165.27, 35.2, 1, 1, 0, -186.82, 40.93, 1, 1, 0, -205.78, 47.66, 1, 6, 21, -117.15, 25.47, 0.00182, 20, -123.29, 8.59, 0.00176, 19, -67.81, 19.84, 0.00449, 16, -50.43, -9.3, 0.04681, 17, -24.61, 7.06, 0.48643, 0, -201.75, 61.93, 0.45868, 5, 22, -138.5, 28.06, 0.00011, 20, -97.85, 21.16, 0.00201, 16, -25, 3.27, 0.13129, 17, 0.82, 19.63, 0.15056, 0, -176.32, 74.49, 0.71602, 7, 22, -119.97, 36.49, 0.00054, 21, -73.19, 46.46, 0, 20, -79.33, 29.59, 0.0039, 18, -38.94, 22.43, 0.0058, 16, -6.47, 11.69, 0.16134, 17, 19.35, 28.05, 0.01923, 0, -157.79, 82.92, 0.8092, 5, 22, 4.68, 15.36, 0.59556, 20, 45.33, 8.46, 0.12773, 18, 85.72, 1.3, 0.02274, 16, 118.18, -9.43, 0.01479, 0, -33.14, 61.8, 0.23918, 5, 21, -103.58, 15.65, 0.00605, 20, -109.71, -1.22, 0.00023, 19, -54.24, 10.03, 0.03255, 16, -36.86, -19.11, 0.00337, 17, -11.04, -2.75, 0.9578, 3, 19, -39.03, 17.21, 0.00961, 16, -21.65, -11.93, 0.16371, 17, 4.17, 4.43, 0.82668, 3, 19, -22.61, 25.72, 0.02959, 16, -5.22, -3.42, 0.85835, 17, 20.59, 12.94, 0.11206, 6, 22, -106.68, 25.76, 0.00078, 21, -59.9, 35.73, 0, 20, -66.03, 18.86, 0.00594, 18, -25.64, 11.7, 0.14208, 19, -10.56, 30.1, 0.03179, 16, 6.82, 0.96, 0.81942, 5, 22, -93.69, 19.81, 0.00065, 20, -53.04, 12.91, 0.00567, 18, -12.65, 5.75, 0.53628, 19, 2.43, 24.15, 0.09775, 16, 19.81, -4.99, 0.35965, 4, 18, -30.05, -3.74, 0.05406, 19, -14.97, 14.66, 0.31359, 16, 2.42, -14.48, 0.49893, 17, 28.24, 1.88, 0.13343, 4, 21, -84.62, 10.31, 0.00546, 19, -35.28, 4.69, 0.20305, 16, -17.9, -24.45, 0.05763, 17, 7.92, -8.09, 0.73386, 4, 18, -27.71, -17.59, 0.00152, 19, -12.62, 0.82, 0.69784, 16, 4.76, -28.32, 0.1216, 17, 30.58, -11.96, 0.17904, 4, 21, -42.74, 14.96, 0.02344, 18, -8.48, -9.07, 0.45325, 19, 6.6, 9.34, 0.46567, 16, 23.98, -19.81, 0.05764, 5, 22, -74.31, 15.82, 0.00058, 21, -27.53, 25.79, 0.02558, 20, -33.67, 8.92, 0.12446, 18, 6.72, 1.76, 0.84695, 16, 39.19, -8.98, 0.00243, 3, 21, -44.32, 2.67, 0.08303, 18, -10.07, -21.36, 0.0645, 19, 5.02, -2.95, 0.85247, 5, 21, -10.78, 20.8, 0.12117, 20, -16.92, 3.93, 0.48758, 18, 23.47, -3.23, 0.37302, 19, 38.56, 15.17, 0.01617, 16, 55.94, -13.97, 0.00207, 4, 21, -23.09, 11.77, 0.25511, 20, -29.22, -5.1, 0.11283, 18, 11.17, -12.26, 0.45852, 19, 26.25, 6.15, 0.17355, 3, 21, -3.12, 1.58, 0.95406, 18, 31.14, -22.45, 0.03125, 19, 46.22, -4.05, 0.01469, 4, 21, 1.38, 17.51, 0.04422, 20, -4.75, 0.64, 0.8904, 18, 35.64, -6.52, 0.06367, 19, 50.72, 11.89, 0.00171, 4, 22, -26.78, 5.11, 0.25072, 21, 20, 15.08, 0.06102, 20, 13.86, -1.79, 0.68611, 16, 86.72, -19.69, 0.00215, 3, 22, -26.54, -8.64, 0.29806, 21, 20.24, 1.33, 0.39924, 20, 14.11, -15.54, 0.3027, 5, 22, -8.41, 5.48, 0.72881, 21, 38.37, 15.45, 0.01954, 20, 32.23, -1.43, 0.24191, 18, 72.62, -8.58, 0.0054, 16, 105.09, -19.32, 0.00434, 5, 22, -7.32, -6.2, 0.78463, 21, 39.46, 3.77, 0.11848, 20, 33.33, -13.11, 0.09397, 19, 88.8, -1.86, 0.0029, 17, 132, -14.64, 2e-05, 5, 22, 7.65, 5.6, 0.93302, 20, 48.29, -1.3, 0.04842, 18, 88.68, -8.46, 0.01057, 19, 103.77, 9.94, 0.00045, 16, 121.15, -19.2, 0.00754, 6, 22, 11.42, -2.31, 0.96521, 21, 58.2, 7.66, 0.01933, 20, 52.07, -9.21, 0.00486, 18, 92.46, -16.37, 0.00375, 19, 107.54, 2.04, 0.00388, 16, 124.92, -27.11, 0.00298, 5, 21, -25.03, 1.48, 0.40618, 20, -31.17, -15.39, 0.01382, 18, 9.22, -22.55, 0.23993, 19, 24.31, -4.15, 0.33899, 17, 67.51, -16.93, 0.00108, 3, 1, -2.83, -7.44, 0.88331, 2, 5.06, 57.52, 0.08262, 0, -397.58, 145.03, 0.03407, 9, 1, 6.16, 25.21, 0.41467, 3, 2.92, 166.72, 1e-05, 5, -90.04, 295.4, 0, 6, -182.37, 324.17, 0.00075, 7, -286.29, 351.08, 0.00066, 11, -597.42, 295.98, 0.00037, 13, -699.03, 193.91, 0.00039, 15, -739.74, 75.14, 0.00301, 0, -388.58, 177.68, 0.58014, 9, 1, 2.02, 7.73, 0.75983, 3, -1.22, 149.24, 2e-05, 5, -94.18, 277.92, 1e-05, 6, -186.51, 306.69, 0.00032, 7, -290.44, 333.6, 0.00026, 11, -601.56, 278.51, 0.00014, 13, -703.17, 176.43, 0.00015, 15, -743.89, 57.67, 0.00113, 0, -392.72, 160.2, 0.23813, 9, 1, -8.23, -23.39, 0.60206, 2, -0.34, 41.57, 0.3693, 5, -104.43, 246.81, 1e-05, 6, -196.76, 275.57, 0.0002, 7, -300.69, 302.48, 7e-05, 11, -611.81, 247.39, 2e-05, 13, -713.42, 145.32, 2e-05, 15, -754.14, 26.55, 0.00012, 0, -402.97, 129.08, 0.0282, 10, 1, -4.87, -39.14, 0.31264, 2, 3.02, 25.82, 0.56593, 3, -8.11, 102.37, 0.0001, 5, -101.07, 231.06, 6e-05, 6, -193.4, 259.82, 0.00051, 7, -297.32, 286.73, 0.00017, 11, -608.45, 231.64, 6e-05, 13, -710.06, 129.57, 6e-05, 15, -750.77, 10.8, 0.00031, 0, -399.61, 113.33, 0.12016, 7, 1, -9.98, -53.94, 0.11181, 2, -2.09, 11.01, 0.88798, 6, -198.51, 245.02, 0.0001, 7, -302.44, 271.93, 3e-05, 11, -613.56, 216.84, 1e-05, 13, -715.17, 114.76, 1e-05, 15, -755.89, -4, 6e-05, 1, 0, -391.83, 115.07, 1, 1, 0, -392.34, 96.97, 1, 8, 2, -2.5, -8.36, 0.92725, 3, -13.63, 68.19, 0.07258, 5, -106.58, 196.88, 2e-05, 6, -198.91, 225.64, 9e-05, 7, -302.84, 252.55, 2e-05, 11, -613.97, 197.46, 1e-05, 13, -715.58, 95.39, 1e-05, 15, -756.29, -23.38, 3e-05, 8, 2, 2.82, -25.29, 0.70625, 3, -8.32, 51.27, 0.29234, 5, -101.27, 179.95, 0.0002, 6, -193.6, 208.72, 0.00074, 7, -297.53, 235.63, 0.00014, 11, -608.65, 180.54, 5e-05, 13, -710.26, 78.46, 5e-05, 15, -750.98, -40.3, 0.00023, 2, 2, -9.83, -50.84, 0.36394, 3, -20.96, 25.71, 0.63606, 9, 1, 1.71, -112.91, 0.00127, 2, 9.6, -47.95, 0.35749, 3, -1.54, 28.6, 0.63861, 5, -94.49, 157.29, 0.00059, 6, -186.82, 186.06, 0.00129, 7, -290.75, 212.97, 0.00022, 11, -601.88, 157.87, 8e-05, 13, -703.48, 55.8, 8e-05, 15, -744.2, -62.97, 0.00037, 3, 2, -6.07, -75.85, 0.07897, 3, -17.2, 0.7, 0.89076, 4, -40.4, 69.37, 0.03027, 10, 1, 14.22, -132.14, 0.00505, 2, 22.11, -67.18, 0.08845, 3, 10.97, 9.37, 0.89019, 4, -12.23, 78.04, 0.00877, 5, -81.98, 138.06, 0.00303, 6, -174.31, 166.82, 0.00302, 7, -278.24, 193.73, 0.00043, 11, -589.37, 138.64, 0.00016, 13, -690.98, 36.57, 0.00016, 15, -731.69, -82.2, 0.00072, 5, 3, -8.27, -23.85, 0.7398, 4, -31.47, 44.82, 0.26018, 6, -193.55, 133.6, 2e-05, 11, -608.6, 105.42, 0, 13, -710.21, 3.34, 0, 10, 1, 26.62, -155.83, 0.00731, 2, 34.51, -90.87, 0.01892, 3, 23.37, -14.32, 0.7008, 4, 0.17, 54.35, 0.25251, 5, -69.58, 114.37, 0.01171, 6, -161.91, 143.14, 0.00645, 7, -265.84, 170.05, 0.00061, 11, -576.97, 114.95, 0.00027, 13, -678.57, 12.88, 0.00027, 15, -719.29, -105.89, 0.00117, 6, 3, 3.8, -45.92, 0.38705, 4, -19.4, 22.75, 0.61292, 6, -181.48, 111.53, 1e-05, 11, -596.54, 83.35, 0, 13, -698.15, -18.72, 0, 15, -738.86, -137.49, 1e-05, 10, 1, 32.84, -176.38, 0.00551, 2, 40.72, -111.42, 0.00643, 3, 29.59, -34.87, 0.39924, 4, 6.39, 33.8, 0.55934, 5, -63.37, 93.82, 0.0212, 6, -155.69, 122.58, 0.00643, 7, -259.62, 149.49, 0.0004, 11, -570.75, 94.4, 0.00023, 13, -672.36, -7.67, 0.00023, 15, -713.07, -126.44, 0.00099, 2, 3, -15.27, -55.02, 0.37173, 4, -38.47, 13.65, 0.62827, 3, 3, 10.09, -73.66, 0.03996, 4, -13.11, -4.99, 0.95856, 5, -82.87, 55.03, 0.00148, 10, 1, 39.99, -201.51, 0.00291, 2, 47.88, -136.56, 0.00117, 3, 36.74, -60.01, 0.07142, 4, 13.54, 8.66, 0.85811, 5, -56.21, 68.68, 0.06077, 6, -148.54, 97.45, 0.00465, 7, -252.47, 124.36, 0.00011, 11, -563.6, 69.26, 0.00014, 13, -665.21, -32.81, 0.00014, 15, -705.92, -151.58, 0.00059, 3, 3, 19.51, -105.76, 0.00044, 4, -3.69, -37.1, 0.75343, 5, -73.45, 22.92, 0.24613, 9, 1, 54.61, -223.87, 0.00239, 2, 62.5, -158.91, 0.00034, 3, 51.36, -82.36, 0.01091, 4, 28.17, -13.69, 0.68488, 5, -41.59, 46.33, 0.29639, 6, -133.92, 75.09, 0.00431, 11, -548.97, 46.91, 0.00013, 13, -650.58, -55.16, 0.00013, 15, -691.3, -173.93, 0.00052, 2, 4, 22.1, -55.08, 0.44195, 5, -47.66, 4.94, 0.55805, 9, 1, 77.58, -244.67, 0.00242, 2, 85.47, -179.72, 0.00035, 3, 74.33, -103.16, 0.00401, 4, 51.14, -34.5, 0.31923, 5, -18.62, 25.52, 0.66528, 6, -110.95, 54.29, 0.00787, 11, -526, 26.11, 0.00014, 13, -627.61, -75.97, 0.00014, 15, -668.33, -194.73, 0.00056, 1, 0, -312.8, -59.86, 1, 3, 4, 47.02, -76.54, 0.1402, 5, -22.74, -16.52, 0.84833, 6, -115.07, 12.25, 0.01147, 9, 1, 101.85, -262.66, 0.00187, 2, 109.74, -197.7, 0.00034, 3, 98.61, -121.15, 0.00235, 4, 75.41, -52.48, 0.02087, 5, 5.65, 7.54, 0.93298, 6, -86.68, 36.3, 0.04087, 11, -501.73, 8.12, 0.00012, 13, -603.34, -93.95, 0.00012, 15, -644.06, -212.72, 0.00047, 3, 4, 71.14, -90.11, 0.02424, 5, 1.38, -30.09, 0.83822, 6, -90.95, -1.33, 0.13753, 8, 1, 120.99, -277.97, 0.00147, 2, 128.88, -213.02, 0.00018, 3, 117.74, -136.46, 0.0011, 5, 24.79, -7.77, 0.75293, 6, -67.54, 20.99, 0.24374, 11, -482.6, -7.19, 0.0001, 13, -584.21, -109.27, 0.0001, 15, -624.92, -228.03, 0.00039, 2, 5, 31.5, -42.01, 0.57147, 6, -60.82, -13.25, 0.42853, 8, 1, 142.88, -285.55, 0.00181, 2, 150.76, -220.6, 0.00021, 3, 139.63, -144.05, 0.00119, 5, 46.67, -15.36, 0.48705, 6, -45.66, 13.41, 0.50897, 11, -460.71, -14.78, 0.00013, 13, -562.32, -116.85, 0.00014, 15, -603.03, -235.62, 0.0005, 3, 5, 63.58, -43.1, 0.24653, 6, -28.75, -14.33, 0.75051, 7, -132.68, 12.58, 0.00296, 8, 1, 172.78, -289.02, 0.0021, 2, 180.67, -224.07, 0.00025, 3, 169.53, -147.51, 0.00116, 5, 76.58, -18.83, 0.15144, 6, -15.75, 9.94, 0.84397, 11, -430.8, -18.24, 0.00021, 13, -532.41, -120.32, 0.00019, 15, -573.13, -239.08, 0.00068, 6, 1, 195.54, -302.02, 0.00024, 6, 7, -3.06, 0.94905, 7, -96.92, 23.85, 0.0505, 11, -408.05, -31.25, 5e-05, 13, -509.66, -133.32, 4e-05, 15, -550.37, -252.09, 0.00013, 8, 1, 225.36, -304.97, 0.00238, 2, 233.25, -240.02, 8e-05, 3, 222.11, -163.46, 0.00038, 6, 36.83, -6.01, 0.66484, 7, -67.1, 20.9, 0.33034, 11, -378.23, -34.19, 0.00044, 13, -479.83, -136.27, 0.00039, 15, -520.55, -255.03, 0.00114, 1, 0, -161.5, -135.45, 1, 9, 1, 254.4, -314.51, 0.00203, 2, 262.29, -249.55, 4e-05, 3, 251.15, -173, 0.00024, 6, 65.87, -15.54, 0.35166, 7, -38.06, 11.37, 0.64418, 10, -288.41, -1.04, 2e-05, 11, -349.19, -43.73, 0.00042, 13, -450.8, -145.8, 0.00037, 15, -491.51, -264.57, 0.00104, 9, 1, 284.62, -320.88, 0.00078, 2, 292.51, -255.92, 1e-05, 3, 281.37, -179.37, 8e-05, 6, 96.09, -21.92, 0.06269, 7, -7.84, 4.99, 0.93562, 10, -258.18, -7.42, 2e-05, 11, -318.96, -50.1, 0.0002, 13, -420.57, -152.17, 0.00017, 15, -461.29, -270.94, 0.00044, 8, 1, 311.93, -324.35, 0.0017, 3, 308.68, -182.84, 5e-05, 7, 19.47, 1.53, 0.82758, 8, -67.56, 28.55, 0.16763, 10, -230.88, -10.88, 0.00025, 11, -291.66, -53.57, 0.00077, 13, -393.27, -155.64, 0.00065, 15, -433.98, -274.41, 0.00137, 6, 1, 309.98, -344.07, 7e-05, 7, 17.52, -18.2, 0.77932, 8, -69.51, 8.83, 0.22048, 11, -293.61, -73.29, 1e-05, 13, -395.22, -175.36, 4e-05, 15, -435.93, -294.13, 8e-05, 1, 0, -60.27, -155, 1, 6, 1, 337.96, -354.42, 0.00015, 7, 45.5, -28.55, 0.42396, 8, -41.53, -1.52, 0.57566, 11, -265.63, -83.64, 2e-05, 13, -367.24, -185.71, 8e-05, 15, -407.95, -304.48, 0.00015, 9, 1, 345.76, -328.2, 0.00265, 3, 342.51, -186.69, 3e-05, 7, 53.3, -2.32, 0.43484, 8, -33.73, 24.7, 0.55245, 9, -115.39, 17.74, 0.00433, 10, -197.05, -14.73, 0.00066, 11, -257.83, -57.42, 0.00142, 13, -359.44, -159.49, 0.00123, 15, -400.15, -278.26, 0.00237, 1, 0, -32.41, -156.68, 1, 9, 1, 380.07, -333.08, 0.00233, 3, 376.82, -191.57, 1e-05, 7, 87.61, -7.21, 0.0901, 8, 0.58, 19.82, 0.83338, 9, -81.08, 12.86, 0.06804, 10, -162.74, -19.62, 0.00099, 11, -223.52, -62.31, 0.00147, 13, -325.13, -164.38, 0.00134, 15, -365.84, -283.15, 0.00234, 3, 7, 80.67, -37.55, 0.04989, 8, -6.36, -10.52, 0.94856, 9, -88.01, -17.48, 0.00155, 8, 1, 410.93, -334.26, 0.00224, 7, 118.47, -8.39, 0.01634, 8, 31.44, 18.64, 0.60049, 9, -50.22, 11.68, 0.37417, 10, -131.88, -20.8, 0.00136, 11, -192.66, -63.48, 0.00132, 13, -294.26, -165.55, 0.00157, 15, -334.98, -284.32, 0.00251, 3, 6, 218.28, -73.22, 0, 8, 27.32, -19.28, 0.68421, 9, -54.34, -26.24, 0.31579, 4, 6, 247.97, -74.52, 0, 8, 57.01, -20.58, 0.33524, 9, -24.65, -27.54, 0.66304, 10, -106.31, -60.02, 0.00172, 7, 1, 439.75, -336.21, 0.00152, 7, 147.29, -10.34, 0.00614, 8, 60.26, 16.69, 0.23601, 9, -21.4, 9.73, 0.75273, 11, -163.83, -65.43, 0.00058, 13, -265.44, -167.5, 0.00121, 15, -306.16, -286.27, 0.00181, 3, 8, 85.83, -12.56, 0.0565, 9, 4.17, -19.52, 0.88197, 10, -77.48, -52, 0.06152, 8, 1, 463.81, -328.19, 0.00183, 7, 171.35, -2.32, 0.00586, 8, 84.32, 24.71, 0.02596, 9, 2.66, 17.75, 0.85287, 10, -79, -14.73, 0.10801, 11, -139.78, -57.41, 0.00124, 13, -241.39, -159.49, 0.0018, 15, -282.1, -278.25, 0.00243, 9, 1, 486.99, -317.14, 0.00249, 6, 298.46, -18.18, 0, 7, 194.53, 8.73, 0.00643, 8, 107.5, 35.76, 0.00313, 9, 25.85, 28.8, 0.58285, 10, -55.81, -3.68, 0.39663, 11, -116.59, -46.36, 0.00197, 13, -218.2, -148.43, 0.0029, 15, -258.92, -267.2, 0.00359, 5, 1, 499.13, -348.56, 4e-05, 9, 37.98, -2.62, 0.60263, 10, -43.68, -35.1, 0.39726, 13, -206.07, -179.86, 2e-05, 15, -246.78, -298.62, 5e-05, 2, 9, 69.19, 12.12, 0.20796, 10, -12.47, -20.36, 0.79204, 7, 1, 518.2, -306.95, 0.00188, 6, 329.67, -7.99, 2e-05, 7, 225.74, 18.92, 0.00389, 9, 57.05, 38.99, 0.19933, 10, -24.61, 6.51, 0.78906, 13, -187, -138.25, 0.00278, 15, -227.71, -257.01, 0.00303, 8, 1, 544.68, -295.83, 0.00145, 6, 356.15, 3.13, 6e-05, 7, 252.22, 30.04, 0.00235, 9, 83.53, 50.1, 0.00558, 10, 1.87, 17.63, 0.8406, 11, -58.9, -25.06, 0.14401, 13, -160.51, -127.13, 0.0032, 15, -201.23, -245.9, 0.00275, 2, 10, 14.01, -4.48, 0.88214, 11, -46.77, -47.16, 0.11786, 7, 1, 569.82, -280.67, 0.00152, 6, 381.29, 18.3, 8e-05, 7, 277.36, 45.21, 0.00199, 10, 27.01, 32.8, 0.43416, 11, -33.77, -9.89, 0.55428, 13, -135.38, -111.96, 0.00468, 15, -176.09, -230.73, 0.00329, 6, 1, 579.57, -302.55, 6e-05, 7, 287.11, 23.32, 5e-05, 10, 36.76, 10.91, 0.50822, 11, -24.02, -31.78, 0.49156, 13, -125.62, -133.85, 1e-05, 15, -166.34, -252.61, 9e-05, 7, 1, 593.22, -265.28, 0.00098, 6, 404.69, 33.68, 8e-05, 7, 300.76, 60.59, 0.0011, 10, 50.42, 48.18, 0.05326, 11, -10.36, 5.5, 0.93679, 13, -111.97, -96.57, 0.00525, 15, -152.69, -215.34, 0.00254, 4, 10, 65.7, 31.49, 0.04163, 11, 4.92, -11.2, 0.93041, 12, -67.92, -55.74, 0.02796, 14, -133.69, -168.47, 0, 1, 0, 159.43, -108.95, 1, 7, 1, 619.24, -251.64, 0.00094, 6, 430.71, 47.33, 7e-05, 7, 326.78, 74.24, 0.00073, 11, 15.65, 19.14, 0.75296, 12, -57.19, -25.4, 0.22953, 13, -85.96, -82.93, 0.0125, 15, -126.67, -201.7, 0.00328, 3, 11, 30.39, 0.5, 0.7059, 12, -42.45, -44.04, 0.2941, 14, -108.22, -156.77, 0, 1, 0, 290.51, -119.71, 1, 2, 11, 66.36, 16.11, 0.24489, 12, -6.48, -28.43, 0.75511, 7, 1, 649.36, -234.52, 0.00078, 6, 460.83, 64.44, 5e-05, 7, 356.9, 91.35, 0.00053, 11, 45.77, 36.26, 0.31071, 12, -27.07, -8.28, 0.66508, 13, -55.83, -65.81, 0.01979, 15, -96.55, -184.58, 0.00306, 1, 0, 236.2, -62.24, 1, 6, 1, 674.34, -217.37, 0.00037, 6, 485.81, 81.59, 2e-05, 7, 381.88, 108.5, 0.00023, 12, -2.08, 8.87, 0.9204, 13, -30.85, -48.67, 0.07729, 15, -71.57, -167.43, 0.00169, 4, 10, 151.04, 73.99, 0, 11, 90.26, 31.3, 0.03332, 12, 17.42, -13.24, 0.89492, 13, -11.35, -70.77, 0.07176, 6, 1, 692.33, -198.3, 0.0003, 6, 503.8, 100.66, 2e-05, 7, 399.87, 127.57, 0.00018, 12, 15.9, 27.94, 0.5037, 13, -12.86, -29.6, 0.4946, 15, -53.58, -148.36, 0.00121, 4, 10, 170.98, 101.51, 0, 12, 37.36, 14.28, 0.55653, 13, 8.59, -43.25, 0.43216, 14, -28.41, -98.45, 0.01131, 7, 1, 701.89, -172.82, 0.00012, 6, 513.36, 126.14, 1e-05, 7, 409.43, 153.05, 7e-05, 11, 98.3, 97.96, 0.0015, 12, 25.46, 53.42, 0.04918, 13, -3.31, -4.12, 0.94886, 15, -44.02, -122.88, 0.00026, 1, 0, 303, 10.88, 1, 8, 1, 723.54, -152.87, 0.00018, 6, 535.01, 146.09, 1e-05, 7, 431.08, 173, 8e-05, 10, 180.73, 160.59, 0, 11, 119.96, 117.9, 0.00045, 13, 18.35, 15.83, 0.65562, 14, -18.66, -39.37, 0.34214, 15, -22.37, -102.93, 0.00152, 6, 1, 739.36, -133.15, 2e-05, 7, 446.9, 192.72, 1e-05, 10, 196.55, 180.31, 0, 12, 62.93, 93.08, 4e-05, 13, 34.17, 35.55, 0.24381, 14, -2.84, -19.65, 0.75613, 2, 14, 4.28, 6.29, 0.93732, 15, 0.57, -57.27, 0.06268, 1, 0, 323.3, 58.16, 1, 3, 11, 146.58, 191.96, 0.0001, 14, 7.96, 34.68, 0.48441, 15, 4.25, -28.88, 0.5155, 2, 10, 204.92, 264.03, 0, 15, 1.81, 0.51, 1, 1, 0, 326.5, 108.41, 1, 8, 1, 743.82, -18.22, 0.0054, 6, 555.29, 280.74, 0.00019, 7, 451.36, 307.65, 0.00083, 10, 201.02, 295.24, 0, 11, 140.24, 252.55, 0.00141, 13, 38.63, 150.48, 0.00268, 15, -2.09, 31.71, 0.55103, 0, 349.08, 134.25, 0.43846, 1, 0, 349.24, 160.13, 1, 7, 1, 735.08, -5.6, 0.00454, 6, 546.55, 293.36, 0.00018, 7, 442.62, 320.27, 0.00074, 11, 131.49, 265.17, 0.00132, 13, 29.88, 163.1, 0.00255, 15, -10.83, 44.33, 0.24746, 0, 340.33, 146.87, 0.74322, 1, 0, -222.63, 61.17, 1, 1, 0, -222.63, 71.83, 1, 1, 0, -167.69, 94.21, 1, 1, 0, -150.07, 99.58, 1, 1, 0, -125.24, 96.53, 1, 1, 0, -94.08, 86.1, 1, 1, 0, -63.85, 79.08, 1, 1, 0, -34.87, 79.54, 1, 1, 0, -16.25, 75.21, 1, 1, 0, -3.01, 62.79, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 22, 24, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 148, 150, 152, 154, 156, 158, 158, 160, 160, 162, 164, 166, 168, 170, 170, 172, 172, 174, 176, 178, 178, 180, 180, 182, 182, 184, 186, 188, 188, 42, 196, 198, 198, 202, 202, 200, 200, 204, 204, 206, 206, 208, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 190, 230, 230, 228, 190, 232, 232, 192, 192, 234, 234, 194, 194, 236, 236, 196, 208, 238, 238, 210, 296, 186, 184, 298, 298, 296, 174, 334, 334, 176, 166, 356, 356, 168, 366, 164, 162, 372, 372, 366, 154, 410, 410, 156, 112, 416, 416, 114, 150, 422, 422, 152, 146, 434, 434, 148, 144, 442, 442, 146, 142, 448, 448, 144, 138, 452, 452, 140, 190, 456, 456, 458, 458, 460, 460, 462, 462, 464, 464, 466, 466, 468, 468, 470, 470, 472, 472, 474, 474, 212], "width": 833, "height": 879}}, "room2": {"room": {"type": "mesh", "uvs": [0.53876, 0.40756, 0.69161, 0.41811, 0.77251, 0.42811, 0.78631, 0.43098, 0.80291, 0.4366, 0.82277, 0.44429, 0.8367, 0.45279, 0.8533, 0.46611, 0.86917, 0.48149, 0.88261, 0.4723, 0.86856, 0.45153, 0.85608, 0.44131, 0.83816, 0.42983, 0.81708, 0.41961, 0.79493, 0.4138, 0.7745, 0.40834, 0.60036, 0.3879, 0.64979, 0.18471, 0.71004, 0.22218, 0.7393, 0.29933, 0.73499, 0.31925, 0.72086, 0.34, 0.73532, 0.34757, 0.75147, 0.35567, 0.76272, 0.36421, 0.76874, 0.37186, 0.77437, 0.38748, 0.7751, 0.40563, 0.79373, 0.41079, 0.81652, 0.41663, 0.83787, 0.42628, 0.85616, 0.43766, 0.87209, 0.45083, 0.87892, 0.46174, 0.89204, 0.47657, 0.9031, 0.49548, 0.30224, 0.54263, 0.30237, 0.46625, 0.29116, 0.45382, 0.28074, 0.43665, 0.22678, 0.46128, 0.13459, 0.52553, 0.09488, 0.51779, 0.10318, 0.48848, 0.1222, 0.47056, 0.13632, 0.45428, 0.12848, 0.4392, 0.11445, 0.45793, 0.10318, 0.46934, 0.09381, 0.48456, 0.08418, 0.5157, 0.07306, 0.51354, 0.08644, 0.4805, 0.10141, 0.45996, 0.11436, 0.44945, 0.12988, 0.43282, 0.14454, 0.41747, 0.15545, 0.41569, 0.16942, 0.40245, 0.1861, 0.38954, 0.25298, 0.36447, 0.2482, 0.43328, 0.2774, 0.42598, 0.27479, 0.39913, 0.2774, 0.38453, 0.28995, 0.36534, 0.30045, 0.34888, 0.3343, 0.24271, 0.40454, 0.17455, 0.14736, 0.42194], "triangles": [19, 21, 18, 20, 21, 19, 16, 17, 18, 16, 18, 21, 0, 67, 68, 0, 66, 67, 15, 26, 27, 24, 25, 26, 26, 23, 24, 15, 23, 26, 22, 23, 15, 16, 21, 22, 15, 16, 22, 15, 27, 28, 29, 14, 28, 15, 28, 14, 30, 13, 29, 14, 29, 13, 69, 56, 57, 65, 62, 63, 31, 12, 30, 13, 30, 12, 55, 56, 69, 61, 59, 60, 64, 65, 63, 38, 39, 62, 11, 12, 31, 46, 54, 55, 11, 31, 32, 10, 11, 32, 37, 38, 65, 66, 37, 65, 38, 62, 65, 69, 46, 55, 45, 46, 69, 47, 54, 46, 53, 54, 47, 40, 59, 61, 58, 59, 40, 57, 58, 40, 39, 40, 61, 39, 61, 62, 0, 37, 66, 48, 53, 47, 34, 9, 33, 10, 32, 33, 9, 10, 33, 52, 53, 48, 49, 52, 48, 51, 52, 49, 50, 51, 49, 41, 44, 45, 43, 44, 41, 42, 43, 41, 45, 69, 57, 40, 45, 57, 41, 45, 40, 36, 37, 0, 36, 0, 1, 1, 8, 36, 1, 2, 5, 4, 2, 3, 4, 5, 2, 5, 7, 1, 6, 7, 5, 8, 1, 7, 8, 9, 34, 8, 34, 35, 36, 8, 35], "vertices": [7.04, 261.99, 134.36, 252.72, 201.75, 243.93, 213.25, 241.41, 227.07, 236.47, 243.62, 229.71, 255.23, 222.24, 269.05, 210.53, 282.27, 197.01, 293.47, 205.08, 281.76, 223.35, 271.37, 232.33, 256.44, 242.42, 238.88, 251.4, 220.43, 256.51, 203.41, 261.31, 58.35, 279.27, 99.53, 457.88, 149.72, 424.94, 174.09, 357.13, 170.5, 339.62, 158.73, 321.38, 170.77, 314.73, 184.23, 307.61, 193.59, 300.1, 198.61, 293.38, 203.3, 279.65, 203.91, 263.69, 219.43, 259.15, 238.41, 254.02, 256.2, 245.54, 271.43, 235.54, 284.71, 223.96, 290.4, 214.37, 301.32, 201.34, 310.54, 184.71, -189.98, 143.27, -189.87, 210.41, -199.21, 221.33, -207.89, 236.43, -252.84, 214.77, -329.64, 158.3, -362.71, 165.11, -355.8, 190.87, -339.95, 206.62, -328.19, 220.93, -334.72, 234.18, -346.41, 217.72, -355.8, 207.69, -363.6, 194.32, -371.62, 166.94, -380.89, 168.84, -369.74, 197.89, -357.27, 215.94, -346.48, 225.17, -333.56, 239.8, -321.35, 253.29, -312.26, 254.85, -300.62, 266.49, -286.72, 277.83, -231.02, 299.87, -235, 239.38, -210.67, 245.8, -212.85, 269.4, -210.67, 282.24, -200.22, 299.11, -191.47, 313.57, -163.27, 406.9, -104.76, 466.81, -318.99, 249.35], "hull": 69, "edges": [40, 42, 132, 134, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 38, 40, 38, 36, 36, 34, 34, 32, 32, 30, 2, 0, 134, 136, 136, 0, 100, 102, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 82, 84, 86, 84, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132], "width": 833, "height": 879}}, "room3": {"room": {"type": "mesh", "uvs": [0.54087, 0.08081, 0.52513, 0.08472, 0.51113, 0.08067, 0.51347, 0.06887, 0.52666, 0.06537, 0.53838, 0.06901], "triangles": [1, 3, 4, 1, 4, 5, 1, 5, 0, 2, 3, 1], "vertices": [1, 0, 8.8, 549.21, 1, 1, 0, -4.31, 545.77, 1, 1, 0, -15.98, 549.33, 1, 1, 0, -14.03, 559.71, 1, 1, 0, -3.04, 562.78, 1, 1, 0, 6.72, 559.58, 1], "hull": 6, "edges": [6, 8, 8, 10, 10, 0, 0, 2, 2, 4, 4, 6], "width": 833, "height": 879}}, "sign": {"sign": {"type": "mesh", "uvs": [0.40446, 0.06332, 0.60655, 0.04247, 0.98408, 0.00351, 0.98236, 0.33236, 0.59431, 0.38321, 0.61613, 0.95413, 0.42, 0.99556, 0.41901, 0.39575, 0.00522, 0.43817, 0.00476, 0.10457], "triangles": [3, 1, 2, 4, 0, 1, 4, 1, 3, 7, 0, 4, 9, 0, 7, 8, 9, 7, 7, 4, 5, 6, 7, 5], "vertices": [104.66, 7.03, 106.62, -10.62, 110.28, -43.6, 70.84, -42.23, 65.78, -8.3, -2.75, -8.08, -7.2, 9.13, 64.75, 6.99, 60.77, 43.13, 100.78, 41.94], "hull": 10, "edges": [6, 8, 10, 12, 16, 18, 14, 16, 4, 6, 8, 10, 12, 14, 0, 18, 0, 2, 2, 4], "width": 87, "height": 120}}, "tree_La": {"tree_La": {"type": "mesh", "uvs": [0.8569, 0.04578, 0.78962, 0.0825, 0.92196, 0.19061, 0.99622, 0.30237, 0.84572, 0.31253, 0.74152, 0.44588, 0.84441, 0.55494, 0.70968, 0.5471, 0.5861, 0.46036, 0.57884, 0.52584, 0.59126, 0.60778, 0.61628, 0.7029, 0.63779, 0.79205, 0.69263, 0.90561, 0.6892, 0.98585, 0.65505, 0.99788, 0.59827, 0.9938, 0.57187, 0.91188, 0.55549, 0.80395, 0.5391, 0.69602, 0.52475, 0.60149, 0.51259, 0.52137, 0.35488, 0.49556, 0.28484, 0.54897, 0.08393, 0.50358, 0.00051, 0.3716, 0.04874, 0.27396, 0.09697, 0.17632, 0.01602, 0.17704, 0.21763, 0.09783, 0.42761, 0.10973, 0.45455, 0.0479, 0.58743, 0.00409, 0.81417, 0.01012, 0.51478, 0.35648, 0.49436, 0.24954, 0.27901, 0.24801, 0.31065, 0.39085, 0.1892, 0.34273, 0.67399, 0.1617, 0.74441, 0.29461], "triangles": [5, 40, 4, 3, 4, 2, 4, 40, 2, 35, 39, 40, 2, 39, 1, 2, 40, 39, 35, 30, 39, 30, 31, 39, 31, 32, 39, 39, 32, 1, 1, 33, 0, 1, 32, 33, 22, 23, 37, 23, 24, 37, 24, 38, 37, 24, 25, 38, 22, 37, 34, 38, 36, 37, 37, 36, 35, 25, 26, 38, 38, 26, 36, 26, 27, 36, 27, 29, 36, 36, 29, 30, 27, 28, 29, 5, 34, 40, 37, 35, 34, 34, 35, 40, 36, 30, 35, 19, 20, 10, 20, 9, 10, 20, 21, 9, 7, 5, 6, 7, 8, 5, 9, 21, 8, 8, 21, 34, 21, 22, 34, 8, 34, 5, 17, 13, 15, 17, 12, 13, 17, 18, 12, 18, 11, 12, 18, 19, 11, 19, 10, 11, 14, 15, 13, 17, 15, 16], "vertices": [1, 55, 31.3, 33.52, 1, 2, 54, 48.79, -38.03, 0.00039, 55, 24.32, 26.48, 0.99961, 1, 55, 42.6, 12.01, 1, 2, 53, 50.48, -57.44, 0, 55, 54.15, -4.13, 1, 3, 53, 51.1, -39.47, 0.03448, 54, 11.65, -39.63, 0.01783, 55, 36.73, -8.57, 0.9477, 3, 53, 31.6, -24.54, 0.71534, 54, -7.65, -24.44, 0.07788, 55, 27.85, -31.47, 0.20677, 3, 53, 12.87, -34.53, 0.95848, 54, -26.51, -34.19, 0, 55, 42.7, -46.65, 0.04152, 2, 53, 16.1, -18.78, 0.93739, 55, 26.67, -47.96, 0.06261, 3, 53, 31.61, -5.9, 0.92812, 54, -7.4, -5.8, 0.03689, 55, 9.96, -36.69, 0.03499, 1, 53, 21.38, -3.75, 1, 1, 53, 8.27, -3.6, 1, 1, 52, 32.87, -4.21, 1, 1, 52, 18.47, -4.05, 1, 2, 51, 5.18, 8.43, 0.16905, 52, -0.5, -7.06, 0.83095, 1, 51, 4.78, -4.33, 1, 1, 51, 0.71, -6.24, 1, 2, 51, -6.05, -5.59, 0.97159, 52, -12.15, 6.61, 0.02841, 2, 51, -9.19, 7.43, 0.09095, 52, 1.23, 7.24, 0.90905, 1, 52, 18.45, 5.93, 1, 1, 52, 35.68, 4.61, 1, 1, 53, 10.24, 4.13, 1, 2, 53, 23.06, 3.99, 0.98974, 56, 7.39, 42.21, 0.01026, 3, 53, 29.46, 22.1, 0.39947, 54, -9.17, 22.22, 0.06084, 56, 21.11, 28.76, 0.53969, 3, 53, 22.07, 31.42, 0.21448, 54, -16.44, 31.64, 0.01004, 56, 32.68, 31.52, 0.77548, 2, 53, 32.2, 54.25, 0.03211, 56, 49.1, 12.7, 0.96789, 1, 56, 46.35, -10.35, 1, 1, 56, 33.24, -20.45, 1, 2, 54, 45.32, 45.66, 0.00194, 56, 20.12, -30.55, 0.99806, 1, 56, 28.34, -35.57, 1, 3, 54, 55.71, 29.73, 0.05958, 56, 1.33, -33.48, 0.94008, 55, -42.49, 13.25, 0.00034, 3, 54, 50.41, 5.23, 0.34067, 56, -18.83, -18.6, 0.29598, 55, -17.52, 15.36, 0.36335, 3, 54, 59.71, 0.71, 0.23554, 56, -26.77, -25.22, 0.077, 55, -15.92, 25.57, 0.68746, 3, 54, 64.44, -15.91, 0.08605, 56, -43.87, -22.72, 0.00363, 55, -1.41, 34.96, 0.91032, 2, 54, 59.79, -42.5, 0.00032, 55, 25.38, 38.31, 0.99968, 2, 54, 10.13, 0.34, 0.99625, 56, -6.76, 20.15, 0.00375, 2, 54, 27.3, 0.41, 0.98772, 56, -13.74, 4.45, 0.01228, 2, 54, 31.06, 25.77, 0.00377, 56, 7.83, -9.38, 0.99623, 3, 53, 46.64, 25.26, 0.11234, 54, 8.04, 25.15, 0.09932, 56, 16.72, 11.86, 0.78834, 1, 56, 24.89, -2.3, 1, 2, 54, 38.21, -22.67, 0.00203, 55, 12.74, 11.86, 0.99797, 3, 53, 55.42, -27.86, 0.05664, 54, 16.12, -28.08, 0.06397, 55, 24.37, -7.67, 0.87939], "hull": 34, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 26, 28, 28, 30, 30, 32, 32, 34, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 56, 58, 40, 42, 38, 40, 34, 36, 36, 38, 20, 22, 22, 24, 24, 26, 16, 18, 18, 20, 62, 64, 58, 60, 60, 62, 64, 66, 2, 0, 66, 0, 50, 52, 52, 54], "width": 119, "height": 159}}, "tree_Lb": {"tree_Lb": {"type": "mesh", "uvs": [0.17567, 0, 0.19627, 0, 0.36691, 0.03873, 0.4464, 0.00182, 0.56096, 0.0887, 0.56058, 0.1635, 0.79581, 0.08232, 1, 0.07339, 1, 0.08337, 0.95714, 0.22177, 0.78017, 0.35031, 0.63738, 0.35981, 0.63877, 0.51974, 0.50033, 0.40497, 0.4752, 0.48178, 0.44402, 0.57707, 0.43492, 0.67493, 0.42583, 0.7728, 0.41571, 0.88159, 0.40524, 0.99422, 0.31276, 0.99634, 0.29662, 0.87747, 0.31641, 0.76737, 0.3363, 0.65673, 0.35661, 0.56598, 0.36927, 0.4733, 0.38242, 0.40012, 0.2094, 0.41645, 0.19701, 0.29462, 0, 0.29461, 0, 0.28371, 0.24336, 0.20874, 0.17568, 0.13978, 0.4791, 0.31478, 0.48207, 0.22918, 0.41375, 0.14058], "triangles": [31, 32, 35, 5, 35, 4, 32, 2, 35, 35, 2, 4, 32, 1, 2, 32, 0, 1, 2, 3, 4, 34, 28, 31, 31, 28, 30, 28, 29, 30, 31, 35, 34, 34, 35, 5, 10, 5, 9, 5, 6, 9, 9, 6, 8, 6, 7, 8, 34, 5, 10, 13, 11, 12, 27, 28, 26, 26, 33, 13, 13, 33, 11, 26, 28, 33, 10, 11, 34, 11, 33, 34, 33, 28, 34, 16, 23, 15, 23, 24, 15, 15, 24, 14, 24, 25, 14, 13, 14, 26, 14, 25, 26, 18, 20, 21, 18, 21, 17, 21, 22, 17, 17, 22, 16, 22, 23, 16, 19, 20, 18], "vertices": [1, 50, 34.5, 0.84, 1, 1, 50, 33.66, -0.84, 1, 3, 47, 12.08, 40.75, 0.01123, 49, 35.42, -14.86, 0.00302, 50, 20.45, -11.58, 0.98575, 3, 47, 21.78, 42.31, 0.02091, 49, 36.48, -24.62, 0.00647, 50, 23.14, -21.03, 0.97262, 3, 47, 21.87, 23.52, 0.11849, 49, 17.7, -23.74, 0.17574, 50, 4.49, -23.32, 0.70577, 4, 47, 14.43, 12.3, 0.54675, 48, -14.67, 7.15, 0.00723, 49, 6.88, -15.72, 0.29958, 50, -7.53, -17.24, 0.14644, 2, 47, 40.34, 12.71, 0.02009, 48, 9.04, 17.62, 0.97991, 1, 48, 27.6, 15.82, 1, 1, 48, 27.27, 14.05, 1, 3, 46, 41.06, -39.06, 0.00846, 47, 38.77, -16.33, 0.04974, 48, 18.9, -9.74, 0.94179, 3, 46, 15.23, -27.77, 0.29943, 47, 12.59, -26.77, 0.52189, 48, -1.15, -29.55, 0.17869, 3, 46, 11.02, -15.36, 0.70177, 47, 0.8, -21.05, 0.26375, 48, -14.24, -28.87, 0.03448, 2, 46, -17.19, -21.1, 0.99999, 47, -14.95, -45.14, 1e-05, 4, 45, 48.88, -4.75, 0.19979, 46, 0.61, -4.72, 0.79176, 47, -14.08, -20.96, 0.00829, 48, -27.98, -34.59, 0.00016, 1, 45, 34.88, -4.14, 1, 1, 45, 17.51, -3.38, 1, 2, 44, 47.47, -4.65, 0.52453, 45, -0.08, -4.67, 0.47547, 1, 44, 29.84, -4.68, 1, 1, 44, 10.24, -4.72, 1, 1, 43, 4.05, -4.94, 1, 1, 43, -4.36, -5.33, 1, 1, 44, 10.45, 6.14, 1, 1, 44, 30.33, 5.31, 1, 2, 44, 50.31, 4.47, 0.19705, 45, 2.1, 4.63, 0.80295, 1, 45, 18.53, 4.76, 1, 1, 45, 35.23, 5.62, 1, 3, 45, 48.46, 6.01, 0.37438, 46, -0.62, 5.98, 0.57152, 49, -17.79, 22.59, 0.0541, 3, 45, 43.65, 21.29, 0.01816, 46, -6.57, 20.85, 0.76724, 49, -10.81, 37.01, 0.2146, 3, 46, 14.71, 26.23, 0.40352, 49, 7.51, 24.91, 0.5937, 50, -13.75, 22.92, 0.00277, 3, 46, 11.22, 43.81, 0.29801, 49, 18.15, 39.34, 0.70199, 50, -5.7, 38.94, 0, 3, 46, 13.14, 44.2, 0.29804, 49, 19.73, 38.18, 0.70195, 50, -3.95, 38.06, 0, 3, 46, 30.7, 25.11, 0.13287, 49, 17.45, 12.35, 0.61257, 50, -1.84, 12.21, 0.25457, 3, 46, 41.67, 33.57, 0.00114, 49, 31.1, 9.94, 0.01172, 50, 12.02, 12.14, 0.98714, 2, 46, 16.16, 0.35, 0.99674, 49, -10.64, 6.4, 0.00326, 3, 47, 1.95, 6.37, 0.206, 49, 1.61, -2.96, 0.79166, 50, -14.88, -5.54, 0.00234, 3, 47, 5.55, 23.1, 0.06157, 49, 18.13, -7.41, 0.28626, 50, 2.16, -7.15, 0.65217], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 38, 40, 40, 42, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 36, 38, 34, 36, 42, 44, 44, 46, 30, 32, 32, 34, 26, 28, 28, 30, 50, 52, 46, 48, 48, 50], "width": 91, "height": 180}}, "tree_Ra": {"tree_Ra": {"type": "mesh", "uvs": [0.53607, 0.05738, 0.58193, 0.10313, 0.58109, 0.27458, 0.65648, 0.19356, 0.72306, 0.17822, 0.74611, 0.24953, 0.70961, 0.30388, 0.78387, 0.29877, 0.90356, 0.30737, 0.97968, 0.36779, 1, 0.42594, 1, 0.4283, 0.86651, 0.43096, 0.73224, 0.45547, 0.64891, 0.48947, 0.73842, 0.59476, 0.82794, 0.70006, 0.85715, 0.80881, 0.80425, 0.81618, 0.67856, 0.72246, 0.59438, 0.6496, 0.49445, 0.58518, 0.46843, 0.65153, 0.43304, 0.74176, 0.36197, 0.82716, 0.30354, 0.90387, 0.23401, 0.99196, 0.16829, 0.99546, 0.16658, 0.93751, 0.22246, 0.86999, 0.27835, 0.80248, 0.3393, 0.72153, 0.39462, 0.62477, 0.39833, 0.53269, 0.34738, 0.44593, 0.31916, 0.53673, 0.14705, 0.54484, 0, 0.55195, 0, 0.51875, 0.11782, 0.41908, 0.19725, 0.39254, 0.11581, 0.34744, 0.06963, 0.28046, 0.12835, 0.15923, 0.27825, 0.14848, 0.37642, 0.17803, 0.30496, 0.09801, 0.27306, 0, 0.5134, 0.48231, 0.48806, 0.40102, 0.43306, 0.27312, 0.56895, 0.3509, 0.28543, 0.26967, 0.19591, 0.24201], "triangles": [3, 4, 5, 6, 2, 3, 5, 6, 3, 12, 9, 10, 12, 7, 8, 12, 8, 9, 11, 12, 10, 13, 6, 7, 13, 7, 12, 51, 6, 13, 51, 2, 6, 48, 49, 51, 14, 48, 51, 13, 14, 51, 14, 21, 48, 20, 14, 15, 20, 21, 14, 19, 20, 15, 19, 15, 16, 18, 19, 16, 18, 16, 17, 46, 47, 0, 45, 46, 0, 45, 0, 1, 53, 43, 44, 52, 44, 45, 53, 44, 52, 50, 45, 1, 2, 50, 1, 52, 45, 50, 42, 43, 53, 41, 42, 53, 40, 53, 52, 41, 53, 40, 51, 50, 2, 49, 50, 51, 34, 52, 50, 34, 50, 49, 40, 52, 34, 34, 49, 48, 33, 34, 48, 35, 40, 34, 36, 39, 40, 36, 40, 35, 38, 39, 36, 37, 38, 36, 21, 33, 48, 32, 33, 21, 22, 32, 21, 23, 32, 22, 31, 32, 23, 24, 31, 23, 30, 31, 24, 25, 30, 24, 29, 30, 25, 25, 28, 29, 26, 28, 25, 27, 28, 26], "vertices": [4, 28, 54.02, -39.59, 0.18205, 29, 4.72, -45.58, 0.7909, 26, 36.91, 60.3, 0.02662, 27, -11.03, 59.97, 0.00042, 4, 28, 45.07, -41.5, 0.22418, 29, -4.22, -43.63, 0.732, 26, 38.58, 51.3, 0.04053, 27, -6.53, 52, 0.00329, 4, 28, 21.23, -29.36, 0.39298, 29, -20.95, -22.75, 0.13429, 26, 25.82, 27.8, 0.29047, 27, -10.97, 25.62, 0.18226, 4, 28, 28.27, -43.46, 0.23523, 29, -20.34, -38.51, 0.00516, 26, 40.11, 34.46, 0.31201, 27, 0.38, 36.57, 0.4476, 3, 28, 26.66, -51.97, 0.22569, 26, 48.57, 32.63, 0.30858, 27, 8.98, 37.58, 0.46572, 4, 28, 15.43, -49.53, 0.21351, 29, -34.54, -38.75, 0.00115, 26, 45.84, 21.47, 0.30107, 27, 10.02, 26.14, 0.48427, 4, 28, 9.92, -41.64, 0.15023, 29, -36.32, -29.29, 0.00508, 26, 37.8, 16.16, 0.25143, 27, 4.14, 18.51, 0.59326, 3, 28, 6.45, -50.29, 0.03008, 26, 46.36, 12.47, 0.03621, 27, 13.43, 17.79, 0.93371, 2, 28, -1.49, -63.04, 0.00024, 27, 27.98, 14.04, 0.99976, 1, 27, 35.84, 3.2, 1, 1, 27, 36.88, -6.17, 1, 1, 27, 36.82, -6.53, 1, 1, 27, 20.28, -4.23, 1, 2, 26, 29.1, -6.01, 0.24064, 27, 3.1, -5.29, 0.75936, 1, 26, 17.41, -5.74, 1, 2, 25, 33.57, -35.64, 0.00332, 26, 19.49, -25.51, 0.99668, 1, 26, 21.56, -45.28, 1, 1, 26, 16.75, -61.95, 1, 1, 26, 10.38, -59.83, 1, 2, 25, 12.33, -34.32, 0.00487, 26, 3.46, -39.51, 0.99513, 2, 25, 20.12, -20.93, 0.07981, 26, -0.42, -24.52, 0.92019, 2, 25, 26.07, -6.05, 0.6844, 26, -6.67, -9.75, 0.3156, 2, 25, 15.22, -5.96, 0.99996, 26, -14.43, -17.33, 4e-05, 2, 24, 39.5, -5.5, 0.32097, 25, 0.47, -5.86, 0.67903, 1, 24, 23.5, -5.31, 1, 1, 24, 9.49, -5.72, 1, 2, 23, 4.54, -5.03, 0.81097, 24, -6.78, -5.92, 0.18903, 1, 23, -3.67, -5.57, 1, 2, 23, -3.89, 3.47, 0.63748, 24, -4.26, 5.77, 0.36252, 1, 24, 8.38, 5.66, 1, 1, 24, 21.02, 5.55, 1, 2, 24, 35.76, 6.04, 0.47655, 25, 0.06, 6.27, 0.52345, 1, 25, 16.51, 4.08, 1, 2, 25, 30.38, 7.84, 0.6639, 28, -4.43, 9.17, 0.3361, 3, 25, 41.46, 17.89, 0.00214, 28, 10.53, 8.76, 0.97651, 29, -15.01, 16.4, 0.02135, 2, 28, -0.53, 18.29, 0.98982, 29, -21.16, 29.64, 0.01018, 2, 28, 8.03, 38.07, 0.98286, 29, -5.22, 44.13, 0.01714, 1, 28, 15.32, 54.98, 1, 2, 28, 19.94, 52.65, 0.99924, 29, 11.64, 52.52, 0.00076, 2, 28, 27.19, 32.5, 0.90743, 29, 9.95, 31.17, 0.09257, 2, 28, 26.41, 21.77, 0.6314, 29, 4.82, 21.71, 0.3686, 2, 28, 37.28, 27.69, 0.13692, 29, 17.16, 22.63, 0.86308, 2, 28, 49.21, 28.14, 0.02089, 29, 28.22, 18.13, 0.97911, 1, 29, 34.39, -1.2, 1, 3, 28, 55.85, -4.42, 0.00026, 29, 20.86, -14.28, 0.99934, 26, 1.79, 63.05, 0.0004, 4, 28, 46.2, -13.3, 0.12962, 29, 8.42, -18.4, 0.85188, 26, 10.42, 53.18, 0.0175, 27, -33.77, 44.63, 0.001, 3, 28, 61.37, -10.95, 0.07622, 29, 23.21, -22.5, 0.92092, 26, 8.46, 68.4, 0.00286, 2, 28, 76.82, -14.27, 0.05408, 29, 35.92, -31.89, 0.94592, 2, 28, -3.89, -7.21, 0.20856, 26, 3.02, 3.26, 0.79144, 4, 28, 8.86, -10.09, 0.70473, 29, -24.29, -0.1, 0.00053, 26, 6.24, 15.93, 0.29123, 27, -25.64, 8.04, 0.00351, 4, 28, 29.77, -12.94, 0.57688, 29, -6.41, -11.3, 0.34013, 26, 9.63, 36.76, 0.07157, 27, -29.19, 28.85, 0.01142, 4, 28, 11.29, -22.64, 0.40566, 29, -27.24, -12.54, 0.02709, 26, 18.84, 18.03, 0.46942, 27, -14.39, 14.12, 0.09782, 2, 29, 8.29, -0.13, 0.99996, 26, -6.37, 45.97, 4e-05, 2, 28, 47.46, 11.34, 0.00445, 29, 19.71, 3.54, 0.99555], "hull": 48, "edges": [0, 94, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 50, 52, 52, 54, 54, 56, 64, 66, 74, 76, 46, 48, 48, 50, 56, 58, 58, 60, 60, 62, 62, 64, 42, 44, 44, 46, 40, 42, 38, 40, 34, 36, 36, 38, 28, 30, 30, 32, 24, 26, 26, 28, 12, 14, 14, 16, 8, 10, 10, 12, 0, 2, 2, 4, 4, 6, 6, 8, 90, 92, 92, 94, 86, 88, 88, 90, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 72, 74, 70, 72, 66, 68, 68, 70], "width": 125, "height": 156}}, "tree_Rb": {"tree_Rb": {"type": "mesh", "uvs": [0.52175, 0.00457, 0.79033, 0.16368, 0.9493, 0.16428, 0.98952, 0.18638, 0.96416, 0.35097, 0.91174, 0.49229, 0.6841, 0.62871, 0.63856, 0.61894, 0.55909, 0.48912, 0.54002, 0.59216, 0.53699, 0.67851, 0.53396, 0.76486, 0.53036, 0.86767, 0.52578, 0.99827, 0.34051, 0.93403, 0.36205, 0.84335, 0.3836, 0.75268, 0.40515, 0.66201, 0.42669, 0.57134, 0.45078, 0.49414, 0.36914, 0.54839, 0.11403, 0.50979, 0.00524, 0.3701, 0.07596, 0.26339, 0.14668, 0.15667, 0.30077, 0.02316, 0.41522, 0.03176, 0.52549, 0.37015, 0.57026, 0.2597, 0.58464, 0.15103], "triangles": [1, 29, 0, 26, 0, 29, 29, 24, 26, 28, 29, 1, 29, 28, 24, 24, 25, 26, 3, 1, 2, 27, 5, 8, 4, 28, 1, 3, 4, 1, 4, 27, 28, 4, 5, 27, 28, 27, 24, 23, 27, 22, 21, 22, 27, 24, 27, 23, 19, 27, 8, 19, 21, 27, 20, 21, 19, 9, 19, 8, 18, 19, 9, 6, 7, 8, 5, 6, 8, 17, 18, 9, 10, 17, 9, 16, 17, 10, 11, 16, 10, 15, 16, 11, 12, 15, 11, 14, 15, 12, 13, 14, 12], "vertices": [1, 34, 34.29, -1.59, 1, 2, 33, 41.12, -20.45, 0.37359, 34, 11.04, -21.45, 0.62641, 3, 32, 71.29, -30.69, 0.00131, 33, 41.48, -32.84, 0.56358, 34, 10.36, -33.83, 0.43511, 3, 32, 68.48, -34.09, 0.00343, 33, 38.5, -36.09, 0.5783, 34, 7.12, -36.81, 0.41827, 3, 32, 45.36, -34.11, 0.15369, 33, 15.4, -34.94, 0.70543, 34, -15.8, -33.74, 0.14088, 3, 32, 25.29, -31.75, 0.55434, 33, -4.52, -31.56, 0.43682, 34, -35.37, -28.7, 0.00884, 2, 32, 4.73, -15.72, 0.99941, 33, -24.24, -14.51, 0.00059, 1, 32, 5.78, -12.06, 1, 2, 32, 23.35, -4.31, 0.92795, 33, -5.06, -4.06, 0.07205, 1, 32, 8.85, -4.08, 1, 2, 31, 25.99, -4.88, 0.92811, 32, -3.21, -4.89, 0.07189, 1, 31, 13.92, -5.66, 1, 2, 30, 6.93, 8.17, 0.14778, 31, -0.45, -6.58, 0.85222, 2, 30, 6.57, -10.12, 0.99992, 31, -18.7, -7.75, 8e-05, 2, 30, -7.88, -1.12, 0.88226, 31, -10.94, 7.4, 0.11774, 2, 30, -6.2, 11.57, 0.04187, 31, 1.85, 6.79, 0.95813, 1, 31, 14.64, 6.17, 1, 2, 31, 27.43, 5.56, 0.75813, 32, -1.8, 5.55, 0.24187, 1, 32, 10.99, 4.98, 1, 2, 32, 21.92, 4.04, 0.93419, 33, -6.07, 4.36, 0.06581, 2, 32, 13.8, 9.73, 0.83433, 33, -13.89, 10.45, 0.16567, 3, 32, 17.46, 30.02, 0.56837, 33, -9.2, 30.53, 0.42082, 34, -34.84, 33.56, 0.0108, 3, 32, 36.21, 40.17, 0.29765, 33, 10.03, 39.71, 0.58648, 34, -14.9, 41.1, 0.11587, 3, 32, 51.57, 35.97, 0.1264, 33, 25.16, 34.74, 0.52688, 34, -0.24, 34.88, 0.34673, 3, 32, 66.93, 31.77, 0.02369, 33, 40.29, 29.76, 0.23377, 34, 14.42, 28.65, 0.74253, 2, 33, 59.4, 18.42, 0.00346, 34, 32.52, 15.75, 0.99654, 1, 34, 30.89, 6.89, 1, 2, 33, 11.49, -0.84, 0.99997, 34, -16.84, 0.57, 3e-05, 2, 33, 27.06, -3.78, 0.7026, 34, -1.57, -3.66, 0.2974, 2, 33, 42.31, -4.35, 0.03044, 34, 13.58, -5.51, 0.96956], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 26, 28, 42, 44, 48, 50, 50, 52, 12, 14, 14, 16, 16, 18, 40, 42, 40, 38, 38, 36, 32, 34, 34, 36, 28, 30, 30, 32, 18, 20, 20, 22, 22, 24, 24, 26, 44, 46, 46, 48], "width": 78, "height": 140}}, "tree_Rc": {"tree_Rc": {"type": "mesh", "uvs": [0.56494, 0.06919, 0.56575, 0.13914, 0.62728, 0.05755, 0.71364, 0.08693, 0.79999, 0.11631, 0.84509, 0.22211, 0.92836, 0.22439, 0.9984, 0.27208, 0.98461, 0.34995, 0.9452, 0.49756, 0.88502, 0.47397, 0.83427, 0.41664, 0.71748, 0.46117, 0.61382, 0.40642, 0.56113, 0.49552, 0.52473, 0.47126, 0.51514, 0.53372, 0.4975, 0.60623, 0.47658, 0.68089, 0.44519, 0.75585, 0.41037, 0.84071, 0.37597, 0.91593, 0.33689, 0.99313, 0.30303, 0.98415, 0.34767, 0.88473, 0.38084, 0.8171, 0.4119, 0.73936, 0.4429, 0.66172, 0.46697, 0.58612, 0.48679, 0.51117, 0.50184, 0.43902, 0.45708, 0.4939, 0.39703, 0.48961, 0.33934, 0.38435, 0.34538, 0.30637, 0.24591, 0.30369, 0.12569, 0.33154, 0.12528, 0.27614, 0.14427, 0.2367, 0, 0.15063, 0, 0.12497, 0.12006, 0.09253, 0.24971, 0.10541, 0.3412, 0.12126, 0.42731, 0.13403, 0.34781, 0.02852, 0.36554, 0.01281, 0.48942, 0.00876, 0.84163, 0.31159, 0.91635, 0.32841, 0.74516, 0.28982, 0.66099, 0.25618, 0.57776, 0.26608, 0.46805, 0.28389, 0.42455, 0.40458], "triangles": [9, 10, 8, 10, 49, 8, 10, 11, 49, 11, 48, 49, 11, 50, 48, 8, 49, 7, 49, 48, 6, 49, 6, 7, 6, 48, 5, 48, 50, 5, 11, 12, 50, 12, 13, 50, 13, 51, 50, 13, 52, 51, 50, 51, 4, 50, 4, 5, 4, 51, 3, 52, 1, 51, 51, 1, 3, 3, 1, 2, 36, 37, 35, 37, 38, 35, 35, 42, 43, 35, 38, 42, 39, 41, 38, 38, 41, 42, 39, 40, 41, 54, 34, 53, 35, 43, 34, 34, 44, 53, 34, 43, 44, 53, 44, 1, 44, 0, 1, 45, 46, 44, 44, 47, 0, 44, 46, 47, 30, 53, 52, 53, 1, 52, 18, 27, 17, 27, 28, 17, 17, 28, 16, 28, 29, 16, 16, 29, 15, 15, 29, 30, 14, 15, 13, 30, 31, 54, 31, 32, 54, 32, 33, 54, 15, 30, 13, 54, 53, 30, 30, 52, 13, 33, 34, 54, 23, 24, 21, 21, 24, 20, 24, 25, 20, 20, 25, 19, 25, 26, 19, 19, 26, 18, 26, 27, 18, 22, 23, 21], "vertices": [4, 36, 134.38, 23.73, 0, 38, 50, -3.2, 0.09198, 39, -9.07, -22.78, 0.83568, 41, -10.63, 24.92, 0.07235, 4, 36, 124.83, 19.07, 0, 38, 39.39, -3.93, 0.2427, 39, -11, -12.32, 0.36957, 41, -9.09, 14.4, 0.38772, 4, 36, 140.22, 15.53, 0, 38, 52.32, -13, 0.0682, 39, -18.54, -26.2, 0.00869, 41, -1.04, 27.99, 0.92311, 3, 36, 142.06, 1.21, 0, 38, 48.62, -26.96, 0.01284, 41, 13.16, 25.39, 0.98716, 2, 41, 27.37, 22.8, 0.95114, 42, -10.61, 20.36, 0.04886, 2, 41, 36.61, 7.81, 0.21376, 42, 3.33, 9.61, 0.78624, 1, 42, 15.15, 15.59, 1, 1, 42, 28.39, 14.5, 1, 1, 42, 32.08, 3.04, 1, 1, 42, 37.22, -19.68, 1, 3, 38, -8.59, -57.46, 0.00139, 41, 48, -29.29, 0.00569, 42, 27.1, -21.07, 0.99292, 3, 38, -0.34, -48.91, 0.02272, 41, 38.84, -21.72, 0.13275, 42, 15.86, -17.23, 0.84453, 4, 37, 40.28, -31.75, 0.01044, 38, -8.14, -30.75, 0.16336, 41, 21.34, -30.9, 0.54034, 42, 2.73, -32, 0.28586, 4, 37, 44.19, -13.71, 0.23792, 38, -0.75, -13.83, 0.41887, 41, 3.89, -24.85, 0.30786, 42, -15.73, -32.51, 0.03536, 3, 37, 28.97, -9, 0.99298, 41, -2.61, -39.38, 0.00701, 42, -16.67, -48.4, 2e-05, 3, 36, 76.41, 3.37, 0, 37, 31.09, -2.47, 0.99881, 41, -8.83, -36.5, 0.00119, 2, 36, 67.17, 0.69, 0, 37, 21.51, -3.39, 1, 2, 36, 56.01, -1.49, 0.00093, 37, 10.14, -3.44, 0.99907, 3, 36, 44.33, -3.34, 0.74247, 37, -1.68, -3.08, 0.25753, 39, -10.99, 71.24, 0, 2, 36, 31.89, -3.7, 0.99981, 37, -13.96, -1.11, 0.00019, 1, 36, 17.87, -4.21, 1, 2, 36, 5.19, -4.16, 1, 39, -1.27, 109.16, 0, 2, 35, 1.37, -3.81, 0.99709, 36, -8.07, -3.56, 0.00291, 2, 35, -4.02, -2.45, 1, 39, 8.4, 121.34, 0, 1, 36, 7.56, 1.94, 1, 2, 36, 19.1, 1.57, 1, 39, 0.51, 94.22, 0, 1, 36, 31.9, 2.16, 1, 2, 36, 44.67, 2.75, 0.60379, 37, -0.21, 2.84, 0.39621, 2, 37, 11.88, 2.02, 1, 39, -7.04, 57.3, 0, 2, 37, 23.7, 1.84, 1, 39, -8.21, 45.54, 0, 3, 37, 34.91, 2.28, 0.97416, 38, -6.69, 3.67, 0.01084, 39, -8.71, 34.32, 0.015, 2, 37, 25.05, 7.07, 0.92407, 39, -3.11, 43.75, 0.07593, 3, 37, 23.28, 16.48, 0.85483, 38, -15.3, 19.88, 0.02805, 39, 6.41, 44.73, 0.11712, 4, 37, 36.46, 29.38, 0.46469, 38, 0.16, 29.93, 0.18684, 39, 18.16, 30.51, 0.34625, 40, -9.59, 32.06, 0.00222, 4, 37, 48.17, 31.43, 0.14654, 38, 12.05, 29.63, 0.13767, 39, 19.22, 18.67, 0.63541, 40, -10.1, 20.18, 0.08038, 4, 37, 44.59, 46.84, 0.00941, 38, 11.57, 45.45, 0.00699, 39, 34.88, 20.95, 0.26193, 40, 5.72, 20.37, 0.72167, 1, 40, 24.66, 25.33, 1, 1, 40, 25.05, 16.92, 1, 2, 39, 52.53, 13.65, 0.00082, 40, 22.26, 10.81, 0.99918, 2, 36, 84.77, 99.63, 0, 40, 45.68, -1.39, 1, 2, 36, 88.3, 101.3, 0, 40, 45.83, -5.29, 1, 2, 36, 100.92, 86.15, 0, 40, 26.94, -10.94, 1, 2, 39, 39.39, -8.86, 0.05044, 40, 6.26, -9.77, 0.94956, 2, 39, 24.65, -8.95, 0.97574, 40, -8.36, -7.91, 0.02426, 2, 36, 116.11, 39.3, 0, 39, 10.82, -9.35, 1, 2, 36, 125.2, 57.58, 0, 39, 26, -23.02, 1, 2, 36, 128.57, 56.05, 0, 39, 23.63, -25.85, 1, 4, 36, 137.55, 38.51, 0, 38, 58.5, 9.3, 0.0128, 39, 4.32, -29.79, 0.98398, 41, -23.75, 32.43, 0.00323, 3, 38, 15.67, -49.19, 0.0023, 41, 37.88, -5.74, 0.02143, 42, 9.31, -2.62, 0.97628, 1, 42, 20.98, 0.77, 1, 3, 38, 18.11, -33.69, 0.01638, 41, 22.23, -4.5, 0.91572, 42, -5.76, -6.99, 0.06791, 3, 38, 22.47, -20.04, 0.01222, 41, 8.29, -1.22, 0.98708, 42, -19.97, -8.85, 0.0007, 2, 38, 20.23, -6.91, 0.57672, 41, -4.63, -4.47, 0.42328, 3, 37, 56.39, 13.41, 0.04242, 38, 16.55, 10.35, 0.53771, 39, 0.58, 12, 0.41987, 3, 37, 36.89, 15.49, 0.61903, 38, -2.15, 16.23, 0.20331, 39, 4.29, 31.25, 0.17766], "hull": 48, "edges": [2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 42, 44, 44, 46, 46, 48, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 88, 90, 90, 92, 92, 94, 36, 38, 52, 54, 54, 56, 64, 62, 62, 60, 60, 58, 58, 56, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 48, 50, 50, 52, 38, 40, 40, 42, 64, 66, 66, 68, 82, 84, 84, 86, 86, 88, 2, 0, 0, 94, 4, 6, 6, 8], "width": 159, "height": 152}}}}], "animations": {"idle": {"bones": {"bone": {"translate": [{"x": 1.51, "y": 0.13, "curve": [0.168, 3.25, 0.334, 4.68, 0.168, 0.25, 0.334, 0.35]}, {"time": 0.5, "x": 4.68, "y": 0.35, "curve": [0.944, 4.68, 1.389, -5.3, 0.944, 0.35, 1.389, -0.35]}, {"time": 1.8333, "x": -5.3, "y": -0.35, "curve": [2.112, -5.3, 2.389, -1.37, 2.112, -0.35, 2.389, -0.08]}, {"time": 2.6667, "x": 1.51, "y": 0.13, "curve": [2.835, 3.25, 3.001, 4.68, 2.835, 0.25, 3.001, 0.35]}, {"time": 3.1667, "x": 4.68, "y": 0.35, "curve": [3.611, 4.68, 4.056, -5.3, 3.611, 0.35, 4.056, -0.35]}, {"time": 4.5, "x": -5.3, "y": -0.35, "curve": [4.779, -5.3, 5.056, -1.37, 4.779, -0.35, 5.056, -0.08]}, {"time": 5.3333, "x": 1.51, "y": 0.13, "curve": [5.501, 3.25, 5.667, 4.68, 5.501, 0.25, 5.667, 0.35]}, {"time": 5.8333, "x": 4.68, "y": 0.35, "curve": [6.278, 4.68, 6.722, -5.3, 6.278, 0.35, 6.722, -0.35]}, {"time": 7.1667, "x": -5.3, "y": -0.35, "curve": [7.445, -5.3, 7.722, -1.37, 7.445, -0.35, 7.722, -0.08]}, {"time": 8, "x": 1.51, "y": 0.13, "curve": [8.168, 3.25, 8.334, 4.68, 8.168, 0.25, 8.334, 0.35]}, {"time": 8.5, "x": 4.68, "y": 0.35, "curve": [8.944, 4.68, 9.389, -5.3, 8.944, 0.35, 9.389, -0.35]}, {"time": 9.8333, "x": -5.3, "y": -0.35, "curve": [10.112, -5.3, 10.39, -1.42, 10.112, -0.35, 10.39, -0.08]}, {"time": 10.6667, "x": 1.51, "y": 0.13}]}, "bone2": {"translate": [{"x": 4.53, "y": 0.31, "curve": [0.114, 5.86, 0.224, 6.85, 0.114, 0.52, 0.224, 0.68]}, {"time": 0.3333, "x": 6.85, "y": 0.68, "curve": [0.778, 6.85, 1.222, -7.64, 0.778, 0.68, 1.222, -1.64]}, {"time": 1.6667, "x": -7.64, "y": -1.64, "curve": [2.001, -7.64, 2.333, 0.65, 2.001, -1.64, 2.333, -0.31]}, {"time": 2.6667, "x": 4.53, "y": 0.31, "curve": [2.781, 5.86, 2.89, 6.85, 2.781, 0.52, 2.89, 0.68]}, {"time": 3, "x": 6.85, "y": 0.68, "curve": [3.444, 6.85, 3.889, -7.64, 3.444, 0.68, 3.889, -1.64]}, {"time": 4.3333, "x": -7.64, "y": -1.64, "curve": [4.668, -7.64, 5, 0.65, 4.668, -1.64, 5, -0.31]}, {"time": 5.3333, "x": 4.53, "y": 0.31, "curve": [5.447, 5.86, 5.557, 6.85, 5.447, 0.52, 5.557, 0.68]}, {"time": 5.6667, "x": 6.85, "y": 0.68, "curve": [6.111, 6.85, 6.556, -7.64, 6.111, 0.68, 6.556, -1.64]}, {"time": 7, "x": -7.64, "y": -1.64, "curve": [7.335, -7.64, 7.667, 0.65, 7.335, -1.64, 7.667, -0.31]}, {"time": 8, "x": 4.53, "y": 0.31, "curve": [8.114, 5.86, 8.224, 6.85, 8.114, 0.52, 8.224, 0.68]}, {"time": 8.3333, "x": 6.85, "y": 0.68, "curve": [8.778, 6.85, 9.222, -7.64, 8.778, 0.68, 9.222, -1.64]}, {"time": 9.6667, "x": -7.64, "y": -1.64, "curve": [10.001, -7.64, 10.336, 0.47, 10.001, -1.64, 10.336, -0.34]}, {"time": 10.6667, "x": 4.53, "y": 0.31}]}, "bone3": {"translate": [{"x": 12.45, "y": 3.37, "curve": [0.057, 13.08, 0.112, 13.56, 0.057, 3.65, 0.112, 3.85]}, {"time": 0.1667, "x": 13.56, "y": 3.85, "curve": [0.611, 13.56, 1.056, -10.03, 0.611, 3.85, 1.056, -6.31]}, {"time": 1.5, "x": -10.03, "y": -6.31, "curve": [1.89, -10.03, 2.278, 8.16, 1.89, -6.31, 2.278, 1.53]}, {"time": 2.6667, "x": 12.45, "y": 3.37, "curve": [2.724, 13.08, 2.779, 13.56, 2.724, 3.65, 2.779, 3.85]}, {"time": 2.8333, "x": 13.56, "y": 3.85, "curve": [3.278, 13.56, 3.722, -10.03, 3.278, 3.85, 3.722, -6.31]}, {"time": 4.1667, "x": -10.03, "y": -6.31, "curve": [4.556, -10.03, 4.944, 8.16, 4.556, -6.31, 4.944, 1.53]}, {"time": 5.3333, "x": 12.45, "y": 3.37, "curve": [5.39, 13.08, 5.445, 13.56, 5.39, 3.65, 5.445, 3.85]}, {"time": 5.5, "x": 13.56, "y": 3.85, "curve": [5.944, 13.56, 6.389, -10.03, 5.944, 3.85, 6.389, -6.31]}, {"time": 6.8333, "x": -10.03, "y": -6.31, "curve": [7.223, -10.03, 7.611, 8.16, 7.223, -6.31, 7.611, 1.53]}, {"time": 8, "x": 12.45, "y": 3.37, "curve": [8.057, 13.08, 8.112, 13.56, 8.057, 3.65, 8.112, 3.85]}, {"time": 8.1667, "x": 13.56, "y": 3.85, "curve": [8.611, 13.56, 9.056, -10.03, 8.611, 3.85, 9.056, -6.31]}, {"time": 9.5, "x": -10.03, "y": -6.31, "curve": [9.89, -10.03, 10.279, 7.98, 9.89, -6.31, 10.279, 1.45]}, {"time": 10.6667, "x": 12.45, "y": 3.37}]}, "bone4": {"translate": [{"x": 13.56, "y": 3.85, "curve": [0.444, 13.56, 0.889, -14.6, 0.444, 3.85, 0.889, -8.27]}, {"time": 1.3333, "x": -14.6, "y": -8.27, "curve": [1.778, -14.6, 2.222, 13.56, 1.778, -8.27, 2.222, 3.85]}, {"time": 2.6667, "x": 13.56, "y": 3.85, "curve": [3.111, 13.56, 3.556, -14.6, 3.111, 3.85, 3.556, -8.27]}, {"time": 4, "x": -14.6, "y": -8.27, "curve": [4.444, -14.6, 4.889, 13.56, 4.444, -8.27, 4.889, 3.85]}, {"time": 5.3333, "x": 13.56, "y": 3.85, "curve": [5.778, 13.56, 6.222, -14.6, 5.778, 3.85, 6.222, -8.27]}, {"time": 6.6667, "x": -14.6, "y": -8.27, "curve": [7.111, -14.6, 7.556, 13.56, 7.111, -8.27, 7.556, 3.85]}, {"time": 8, "x": 13.56, "y": 3.85, "curve": [8.444, 13.56, 8.889, -14.6, 8.444, 3.85, 8.889, -8.27]}, {"time": 9.3333, "x": -14.6, "y": -8.27, "curve": [9.778, -14.6, 10.222, 13.56, 9.778, -8.27, 10.222, 3.85]}, {"time": 10.6667, "x": 13.56, "y": 3.85}]}, "bone5": {"translate": [{"x": 8.44, "y": 9.71, "curve": [0.39, 5.56, 0.779, -5.76, 0.39, 4.91, 0.779, -13.97]}, {"time": 1.1667, "x": -5.76, "y": -13.97, "curve": [1.611, -5.76, 2.056, 9.14, 1.611, -13.97, 2.056, 10.88]}, {"time": 2.5, "x": 9.14, "y": 10.88, "curve": [2.556, 9.14, 2.611, 8.85, 2.556, 10.88, 2.611, 10.39]}, {"time": 2.6667, "x": 8.44, "y": 9.71, "curve": [3.057, 5.56, 3.445, -5.76, 3.057, 4.91, 3.445, -13.97]}, {"time": 3.8333, "x": -5.76, "y": -13.97, "curve": [4.278, -5.76, 4.722, 9.14, 4.278, -13.97, 4.722, 10.88]}, {"time": 5.1667, "x": 9.14, "y": 10.88, "curve": [5.223, 9.14, 5.278, 8.85, 5.223, 10.88, 5.278, 10.39]}, {"time": 5.3333, "x": 8.44, "y": 9.71, "curve": [5.724, 5.56, 6.112, -5.76, 5.724, 4.91, 6.112, -13.97]}, {"time": 6.5, "x": -5.76, "y": -13.97, "curve": [6.944, -5.76, 7.389, 9.14, 6.944, -13.97, 7.389, 10.88]}, {"time": 7.8333, "x": 9.14, "y": 10.88, "curve": [7.89, 9.14, 7.944, 8.85, 7.89, 10.88, 7.944, 10.39]}, {"time": 8, "x": 8.44, "y": 9.71, "curve": [8.39, 5.56, 8.779, -5.76, 8.39, 4.91, 8.779, -13.97]}, {"time": 9.1667, "x": -5.76, "y": -13.97, "curve": [9.611, -5.76, 10.056, 9.14, 9.611, -13.97, 10.056, 10.88]}, {"time": 10.5, "x": 9.14, "y": 10.88, "curve": [10.556, 9.14, 10.613, 8.86, 10.556, 10.88, 10.613, 10.41]}, {"time": 10.6667, "x": 8.44, "y": 9.71}]}, "bone6": {"translate": [{"x": 2.21, "y": 4.6, "curve": [0.336, 0.63, 0.668, -2.5, 0.336, -0.22, 0.668, -9.75]}, {"time": 1, "x": -2.5, "y": -9.75, "curve": [1.444, -2.5, 1.889, 3.11, 1.444, -9.75, 1.889, 7.33]}, {"time": 2.3333, "x": 3.11, "y": 7.33, "curve": [2.446, 3.11, 2.556, 2.74, 2.446, 7.33, 2.556, 6.2]}, {"time": 2.6667, "x": 2.21, "y": 4.6, "curve": [3.003, 0.63, 3.335, -2.5, 3.003, -0.22, 3.335, -9.75]}, {"time": 3.6667, "x": -2.5, "y": -9.75, "curve": [4.111, -2.5, 4.556, 3.11, 4.111, -9.75, 4.556, 7.33]}, {"time": 5, "x": 3.11, "y": 7.33, "curve": [5.113, 3.11, 5.222, 2.74, 5.113, 7.33, 5.222, 6.2]}, {"time": 5.3333, "x": 2.21, "y": 4.6, "curve": [5.67, 0.63, 6.001, -2.5, 5.67, -0.22, 6.001, -9.75]}, {"time": 6.3333, "x": -2.5, "y": -9.75, "curve": [6.778, -2.5, 7.222, 3.11, 6.778, -9.75, 7.222, 7.33]}, {"time": 7.6667, "x": 3.11, "y": 7.33, "curve": [7.779, 3.11, 7.889, 2.74, 7.779, 7.33, 7.889, 6.2]}, {"time": 8, "x": 2.21, "y": 4.6, "curve": [8.336, 0.63, 8.668, -2.5, 8.336, -0.22, 8.668, -9.75]}, {"time": 9, "x": -2.5, "y": -9.75, "curve": [9.444, -2.5, 9.889, 3.11, 9.444, -9.75, 9.889, 7.33]}, {"time": 10.3333, "x": 3.11, "y": 7.33, "curve": [10.446, 3.11, 10.559, 2.75, 10.446, 7.33, 10.559, 6.24]}, {"time": 10.6667, "x": 2.21, "y": 4.6}]}, "bone7": {"translate": [{"x": 1.81, "y": 3.82, "curve": [0.279, 0.32, 0.556, -1.67, 0.279, -2.17, 0.556, -10.12]}, {"time": 0.8333, "x": -1.67, "y": -10.12, "curve": [1.278, -1.67, 1.722, 3.44, 1.278, -10.12, 1.722, 10.31]}, {"time": 2.1667, "x": 3.44, "y": 10.31, "curve": [2.334, 3.44, 2.5, 2.71, 2.334, 10.31, 2.5, 7.38]}, {"time": 2.6667, "x": 1.81, "y": 3.82, "curve": [2.946, 0.32, 3.223, -1.67, 2.946, -2.17, 3.223, -10.12]}, {"time": 3.5, "x": -1.67, "y": -10.12, "curve": [3.944, -1.67, 4.389, 3.44, 3.944, -10.12, 4.389, 10.31]}, {"time": 4.8333, "x": 3.44, "y": 10.31, "curve": [5.001, 3.44, 5.167, 2.71, 5.001, 10.31, 5.167, 7.38]}, {"time": 5.3333, "x": 1.81, "y": 3.82, "curve": [5.613, 0.32, 5.89, -1.67, 5.613, -2.17, 5.89, -10.12]}, {"time": 6.1667, "x": -1.67, "y": -10.12, "curve": [6.611, -1.67, 7.056, 3.44, 6.611, -10.12, 7.056, 10.31]}, {"time": 7.5, "x": 3.44, "y": 10.31, "curve": [7.667, 3.44, 7.833, 2.71, 7.667, 10.31, 7.833, 7.38]}, {"time": 8, "x": 1.81, "y": 3.82, "curve": [8.279, 0.32, 8.556, -1.67, 8.279, -2.17, 8.556, -10.12]}, {"time": 8.8333, "x": -1.67, "y": -10.12, "curve": [9.278, -1.67, 9.722, 3.44, 9.278, -10.12, 9.722, 10.31]}, {"time": 10.1667, "x": 3.44, "y": 10.31, "curve": [10.334, 3.44, 10.501, 2.72, 10.334, 10.31, 10.501, 7.43]}, {"time": 10.6667, "x": 1.81, "y": 3.82}]}, "bone8": {"translate": [{"x": -0.56, "y": 2.47, "curve": [0.225, -0.1, 0.446, 0.37, 0.225, -4.2, 0.446, -10.95]}, {"time": 0.6667, "x": 0.37, "y": -10.95, "curve": [1.111, 0.37, 1.556, -1.48, 1.111, -10.95, 1.556, 15.88]}, {"time": 2, "x": -1.48, "y": 15.88, "curve": [2.224, -1.48, 2.444, -1.01, 2.224, 15.88, 2.444, 9.04]}, {"time": 2.6667, "x": -0.56, "y": 2.47, "curve": [2.892, -0.1, 3.113, 0.37, 2.892, -4.2, 3.113, -10.95]}, {"time": 3.3333, "x": 0.37, "y": -10.95, "curve": [3.778, 0.37, 4.222, -1.48, 3.778, -10.95, 4.222, 15.88]}, {"time": 4.6667, "x": -1.48, "y": 15.88, "curve": [4.89, -1.48, 5.111, -1.01, 4.89, 15.88, 5.111, 9.04]}, {"time": 5.3333, "x": -0.56, "y": 2.47, "curve": [5.559, -0.1, 5.779, 0.37, 5.559, -4.2, 5.779, -10.95]}, {"time": 6, "x": 0.37, "y": -10.95, "curve": [6.444, 0.37, 6.889, -1.48, 6.444, -10.95, 6.889, 15.88]}, {"time": 7.3333, "x": -1.48, "y": 15.88, "curve": [7.557, -1.48, 7.778, -1.01, 7.557, 15.88, 7.778, 9.04]}, {"time": 8, "x": -0.56, "y": 2.47, "curve": [8.225, -0.1, 8.446, 0.37, 8.225, -4.2, 8.446, -10.95]}, {"time": 8.6667, "x": 0.37, "y": -10.95, "curve": [9.111, 0.37, 9.556, -1.48, 9.111, -10.95, 9.556, 15.88]}, {"time": 10, "x": -1.48, "y": 15.88, "curve": [10.224, -1.48, 10.447, -1.02, 10.224, 15.88, 10.447, 9.22]}, {"time": 10.6667, "x": -0.56, "y": 2.47}]}, "bone9": {"translate": [{"x": 0.23, "y": -4.64, "curve": [0.168, 0.66, 0.334, 1.02, 0.168, -9.9, 0.334, -14.21]}, {"time": 0.5, "x": 1.02, "y": -14.21, "curve": [0.944, 1.02, 1.389, -1.48, 0.944, -14.21, 1.389, 15.88]}, {"time": 1.8333, "x": -1.48, "y": 15.88, "curve": [2.112, -1.48, 2.389, -0.5, 2.112, 15.88, 2.389, 4.04]}, {"time": 2.6667, "x": 0.23, "y": -4.64, "curve": [2.835, 0.66, 3.001, 1.02, 2.835, -9.9, 3.001, -14.21]}, {"time": 3.1667, "x": 1.02, "y": -14.21, "curve": [3.611, 1.02, 4.056, -1.48, 3.611, -14.21, 4.056, 15.88]}, {"time": 4.5, "x": -1.48, "y": 15.88, "curve": [4.779, -1.48, 5.056, -0.5, 4.779, 15.88, 5.056, 4.04]}, {"time": 5.3333, "x": 0.23, "y": -4.64, "curve": [5.501, 0.66, 5.667, 1.02, 5.501, -9.9, 5.667, -14.21]}, {"time": 5.8333, "x": 1.02, "y": -14.21, "curve": [6.278, 1.02, 6.722, -1.48, 6.278, -14.21, 6.722, 15.88]}, {"time": 7.1667, "x": -1.48, "y": 15.88, "curve": [7.445, -1.48, 7.722, -0.5, 7.445, 15.88, 7.722, 4.04]}, {"time": 8, "x": 0.23, "y": -4.64, "curve": [8.168, 0.66, 8.334, 1.02, 8.168, -9.9, 8.334, -14.21]}, {"time": 8.5, "x": 1.02, "y": -14.21, "curve": [8.944, 1.02, 9.389, -1.48, 8.944, -14.21, 9.389, 15.88]}, {"time": 9.8333, "x": -1.48, "y": 15.88, "curve": [10.112, -1.48, 10.39, -0.51, 10.112, 15.88, 10.39, 4.18]}, {"time": 10.6667, "x": 0.23, "y": -4.64}]}, "bone10": {"translate": [{"x": 2.64, "y": -3.73, "curve": [0.114, 3.78, 0.224, 4.64, 0.114, -5.37, 0.224, -6.59]}, {"time": 0.3333, "x": 4.64, "y": -6.59, "curve": [0.778, 4.64, 1.222, -7.85, 0.778, -6.59, 1.222, 11.34]}, {"time": 1.6667, "x": -7.85, "y": 11.34, "curve": [2.001, -7.85, 2.333, -0.71, 2.001, 11.34, 2.333, 1.08]}, {"time": 2.6667, "x": 2.64, "y": -3.73, "curve": [2.781, 3.78, 2.89, 4.64, 2.781, -5.37, 2.89, -6.59]}, {"time": 3, "x": 4.64, "y": -6.59, "curve": [3.444, 4.64, 3.889, -7.85, 3.444, -6.59, 3.889, 11.34]}, {"time": 4.3333, "x": -7.85, "y": 11.34, "curve": [4.668, -7.85, 5, -0.71, 4.668, 11.34, 5, 1.08]}, {"time": 5.3333, "x": 2.64, "y": -3.73, "curve": [5.447, 3.78, 5.557, 4.64, 5.447, -5.37, 5.557, -6.59]}, {"time": 5.6667, "x": 4.64, "y": -6.59, "curve": [6.111, 4.64, 6.556, -7.85, 6.111, -6.59, 6.556, 11.34]}, {"time": 7, "x": -7.85, "y": 11.34, "curve": [7.335, -7.85, 7.667, -0.71, 7.335, 11.34, 7.667, 1.08]}, {"time": 8, "x": 2.64, "y": -3.73, "curve": [8.114, 3.78, 8.224, 4.64, 8.114, -5.37, 8.224, -6.59]}, {"time": 8.3333, "x": 4.64, "y": -6.59, "curve": [8.778, 4.64, 9.222, -7.85, 8.778, -6.59, 9.222, 11.34]}, {"time": 9.6667, "x": -7.85, "y": 11.34, "curve": [10.001, -7.85, 10.336, -0.86, 10.001, 11.34, 10.336, 1.29]}, {"time": 10.6667, "x": 2.64, "y": -3.73}]}, "bone11": {"translate": [{"x": 5.6, "y": -6.81, "curve": [0.057, 5.98, 0.112, 6.27, 0.057, -7.24, 0.112, -7.57]}, {"time": 0.1667, "x": 6.27, "y": -7.57, "curve": [0.611, 6.27, 1.056, -7.85, 0.611, -7.57, 1.056, 8.73]}, {"time": 1.5, "x": -7.85, "y": 8.73, "curve": [1.89, -7.85, 2.278, 3.04, 1.89, 8.73, 2.278, -3.84]}, {"time": 2.6667, "x": 5.6, "y": -6.81, "curve": [2.724, 5.98, 2.779, 6.27, 2.724, -7.24, 2.779, -7.57]}, {"time": 2.8333, "x": 6.27, "y": -7.57, "curve": [3.278, 6.27, 3.722, -7.85, 3.278, -7.57, 3.722, 8.73]}, {"time": 4.1667, "x": -7.85, "y": 8.73, "curve": [4.556, -7.85, 4.944, 3.04, 4.556, 8.73, 4.944, -3.84]}, {"time": 5.3333, "x": 5.6, "y": -6.81, "curve": [5.39, 5.98, 5.445, 6.27, 5.39, -7.24, 5.445, -7.57]}, {"time": 5.5, "x": 6.27, "y": -7.57, "curve": [5.944, 6.27, 6.389, -7.85, 5.944, -7.57, 6.389, 8.73]}, {"time": 6.8333, "x": -7.85, "y": 8.73, "curve": [7.223, -7.85, 7.611, 3.04, 7.223, 8.73, 7.611, -3.84]}, {"time": 8, "x": 5.6, "y": -6.81, "curve": [8.057, 5.98, 8.112, 6.27, 8.057, -7.24, 8.112, -7.57]}, {"time": 8.1667, "x": 6.27, "y": -7.57, "curve": [8.611, 6.27, 9.056, -7.85, 8.611, -7.57, 9.056, 8.73]}, {"time": 9.5, "x": -7.85, "y": 8.73, "curve": [9.89, -7.85, 10.279, 2.93, 9.89, 8.73, 10.279, -3.72]}, {"time": 10.6667, "x": 5.6, "y": -6.81}]}, "bone12": {"translate": [{"x": 13.1, "y": -8.88, "curve": [0.444, 13.1, 0.889, -7.47, 0.444, -8.88, 0.889, 3.99]}, {"time": 1.3333, "x": -7.47, "y": 3.99, "curve": [1.778, -7.47, 2.222, 13.1, 1.778, 3.99, 2.222, -8.88]}, {"time": 2.6667, "x": 13.1, "y": -8.88, "curve": [3.111, 13.1, 3.556, -7.47, 3.111, -8.88, 3.556, 3.99]}, {"time": 4, "x": -7.47, "y": 3.99, "curve": [4.444, -7.47, 4.889, 13.1, 4.444, 3.99, 4.889, -8.88]}, {"time": 5.3333, "x": 13.1, "y": -8.88, "curve": [5.778, 13.1, 6.222, -7.47, 5.778, -8.88, 6.222, 3.99]}, {"time": 6.6667, "x": -7.47, "y": 3.99, "curve": [7.111, -7.47, 7.556, 13.1, 7.111, 3.99, 7.556, -8.88]}, {"time": 8, "x": 13.1, "y": -8.88, "curve": [8.444, 13.1, 8.889, -7.47, 8.444, -8.88, 8.889, 3.99]}, {"time": 9.3333, "x": -7.47, "y": 3.99, "curve": [9.778, -7.47, 10.222, 13.1, 9.778, 3.99, 10.222, -8.88]}, {"time": 10.6667, "x": 13.1, "y": -8.88}]}, "bone13": {"translate": [{"x": 7.12, "y": -2.42, "curve": [0.39, 4.09, 0.779, -7.81, 0.39, -1.11, 0.779, 4.05]}, {"time": 1.1667, "x": -7.81, "y": 4.05, "curve": [1.611, -7.81, 2.056, 7.85, 1.611, 4.05, 2.056, -2.74]}, {"time": 2.5, "x": 7.85, "y": -2.74, "curve": [2.556, 7.85, 2.611, 7.55, 2.556, -2.74, 2.611, -2.61]}, {"time": 2.6667, "x": 7.12, "y": -2.42, "curve": [3.057, 4.09, 3.445, -7.81, 3.057, -1.11, 3.445, 4.05]}, {"time": 3.8333, "x": -7.81, "y": 4.05, "curve": [4.278, -7.81, 4.722, 7.85, 4.278, 4.05, 4.722, -2.74]}, {"time": 5.1667, "x": 7.85, "y": -2.74, "curve": [5.223, 7.85, 5.278, 7.55, 5.223, -2.74, 5.278, -2.61]}, {"time": 5.3333, "x": 7.12, "y": -2.42, "curve": [5.724, 4.09, 6.112, -7.81, 5.724, -1.11, 6.112, 4.05]}, {"time": 6.5, "x": -7.81, "y": 4.05, "curve": [6.944, -7.81, 7.389, 7.85, 6.944, 4.05, 7.389, -2.74]}, {"time": 7.8333, "x": 7.85, "y": -2.74, "curve": [7.89, 7.85, 7.944, 7.55, 7.89, -2.74, 7.944, -2.61]}, {"time": 8, "x": 7.12, "y": -2.42, "curve": [8.39, 4.09, 8.779, -7.81, 8.39, -1.11, 8.779, 4.05]}, {"time": 9.1667, "x": -7.81, "y": 4.05, "curve": [9.611, -7.81, 10.056, 7.85, 9.611, 4.05, 10.056, -2.74]}, {"time": 10.5, "x": 7.85, "y": -2.74, "curve": [10.556, 7.85, 10.613, 7.56, 10.556, -2.74, 10.613, -2.61]}, {"time": 10.6667, "x": 7.12, "y": -2.42}]}, "bone14": {"translate": [{"x": 8.77, "y": -2.81, "curve": [0.336, 1.79, 0.668, -11.99, 0.336, -1.77, 0.668, 0.28]}, {"time": 1, "x": -11.99, "y": 0.28, "curve": [1.444, -11.99, 1.889, 12.73, 1.444, 0.28, 1.889, -3.39]}, {"time": 2.3333, "x": 12.73, "y": -3.39, "curve": [2.446, 12.73, 2.556, 11.08, 2.446, -3.39, 2.556, -3.15]}, {"time": 2.6667, "x": 8.77, "y": -2.81, "curve": [3.003, 1.79, 3.335, -11.99, 3.003, -1.77, 3.335, 0.28]}, {"time": 3.6667, "x": -11.99, "y": 0.28, "curve": [4.111, -11.99, 4.556, 12.73, 4.111, 0.28, 4.556, -3.39]}, {"time": 5, "x": 12.73, "y": -3.39, "curve": [5.113, 12.73, 5.222, 11.08, 5.113, -3.39, 5.222, -3.15]}, {"time": 5.3333, "x": 8.77, "y": -2.81, "curve": [5.67, 1.79, 6.001, -11.99, 5.67, -1.77, 6.001, 0.28]}, {"time": 6.3333, "x": -11.99, "y": 0.28, "curve": [6.778, -11.99, 7.222, 12.73, 6.778, 0.28, 7.222, -3.39]}, {"time": 7.6667, "x": 12.73, "y": -3.39, "curve": [7.779, 12.73, 7.889, 11.08, 7.779, -3.39, 7.889, -3.15]}, {"time": 8, "x": 8.77, "y": -2.81, "curve": [8.336, 1.79, 8.668, -11.99, 8.336, -1.77, 8.668, 0.28]}, {"time": 9, "x": -11.99, "y": 0.28, "curve": [9.444, -11.99, 9.889, 12.73, 9.444, 0.28, 9.889, -3.39]}, {"time": 10.3333, "x": 12.73, "y": -3.39, "curve": [10.446, 12.73, 10.559, 11.14, 10.446, -3.39, 10.559, -3.16]}, {"time": 10.6667, "x": 8.77, "y": -2.81}]}, "bone15": {"translate": [{"x": 2.16, "curve": [0.225, -3.53, 0.446, -9.29, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -9.29, "curve": [1.111, -9.29, 1.556, 13.6, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 13.6, "curve": [2.224, 13.6, 2.444, 7.77, 2.224, 0, 2.444, 0]}, {"time": 2.6667, "x": 2.16, "curve": [2.892, -3.53, 3.113, -9.29, 2.892, 0, 3.113, 0]}, {"time": 3.3333, "x": -9.29, "curve": [3.778, -9.29, 4.222, 13.6, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 13.6, "curve": [4.89, 13.6, 5.111, 7.77, 4.89, 0, 5.111, 0]}, {"time": 5.3333, "x": 2.16, "curve": [5.559, -3.53, 5.779, -9.29, 5.559, 0, 5.779, 0]}, {"time": 6, "x": -9.29, "curve": [6.444, -9.29, 6.889, 13.6, 6.444, 0, 6.889, 0]}, {"time": 7.3333, "x": 13.6, "curve": [7.557, 13.6, 7.778, 7.77, 7.557, 0, 7.778, 0]}, {"time": 8, "x": 2.16, "curve": [8.225, -3.53, 8.446, -9.29, 8.225, 0, 8.446, 0]}, {"time": 8.6667, "x": -9.29, "curve": [9.111, -9.29, 9.556, 13.6, 9.111, 0, 9.556, 0]}, {"time": 10, "x": 13.6, "curve": [10.224, 13.6, 10.447, 7.92, 10.224, 0, 10.447, 0]}, {"time": 10.6667, "x": 2.16}]}, "bone16": {"translate": [{"x": -6.91, "y": -2.6, "curve": [0.057, -7.29, 0.112, -7.58, 0.057, -2.7, 0.112, -2.77]}, {"time": 0.1667, "x": -7.58, "y": -2.77, "curve": [0.611, -7.58, 1.056, 6.5, 0.611, -2.77, 1.056, 0.93]}, {"time": 1.5, "x": 6.5, "y": 0.93, "curve": [1.89, 6.5, 2.278, -4.35, 1.89, 0.93, 2.278, -1.92]}, {"time": 2.6667, "x": -6.91, "y": -2.6, "curve": [2.724, -7.29, 2.779, -7.58, 2.724, -2.7, 2.779, -2.77]}, {"time": 2.8333, "x": -7.58, "y": -2.77, "curve": [3.278, -7.58, 3.722, 6.5, 3.278, -2.77, 3.722, 0.93]}, {"time": 4.1667, "x": 6.5, "y": 0.93, "curve": [4.556, 6.5, 4.944, -4.35, 4.556, 0.93, 4.944, -1.92]}, {"time": 5.3333, "x": -6.91, "y": -2.6, "curve": [5.39, -7.29, 5.445, -7.58, 5.39, -2.7, 5.445, -2.77]}, {"time": 5.5, "x": -7.58, "y": -2.77, "curve": [5.944, -7.58, 6.389, 6.5, 5.944, -2.77, 6.389, 0.93]}, {"time": 6.8333, "x": 6.5, "y": 0.93, "curve": [7.223, 6.5, 7.611, -4.35, 7.223, 0.93, 7.611, -1.92]}, {"time": 8, "x": -6.91, "y": -2.6, "curve": [8.057, -7.29, 8.112, -7.58, 8.057, -2.7, 8.112, -2.77]}, {"time": 8.1667, "x": -7.58, "y": -2.77, "curve": [8.611, -7.58, 9.056, 6.5, 8.611, -2.77, 9.056, 0.93]}, {"time": 9.5, "x": 6.5, "y": 0.93, "curve": [9.89, 6.5, 10.279, -4.25, 9.89, 0.93, 10.279, -1.9]}, {"time": 10.6667, "x": -6.91, "y": -2.6}]}, "bone17": {"translate": [{"x": -7.58, "y": -2.77, "curve": [0.444, -7.58, 0.889, 6.5, 0.444, -2.77, 0.889, 0.93]}, {"time": 1.3333, "x": 6.5, "y": 0.93, "curve": [1.778, 6.5, 2.222, -7.58, 1.778, 0.93, 2.222, -2.77]}, {"time": 2.6667, "x": -7.58, "y": -2.77, "curve": [3.111, -7.58, 3.556, 6.5, 3.111, -2.77, 3.556, 0.93]}, {"time": 4, "x": 6.5, "y": 0.93, "curve": [4.444, 6.5, 4.889, -7.58, 4.444, 0.93, 4.889, -2.77]}, {"time": 5.3333, "x": -7.58, "y": -2.77, "curve": [5.778, -7.58, 6.222, 6.5, 5.778, -2.77, 6.222, 0.93]}, {"time": 6.6667, "x": 6.5, "y": 0.93, "curve": [7.111, 6.5, 7.556, -7.58, 7.111, 0.93, 7.556, -2.77]}, {"time": 8, "x": -7.58, "y": -2.77, "curve": [8.444, -7.58, 8.889, 6.5, 8.444, -2.77, 8.889, 0.93]}, {"time": 9.3333, "x": 6.5, "y": 0.93, "curve": [9.778, 6.5, 10.222, -7.58, 9.778, 0.93, 10.222, -2.77]}, {"time": 10.6667, "x": -7.58, "y": -2.77}]}, "bone18": {"translate": [{"x": -3.1, "y": -1.59, "curve": [0.168, -5.56, 0.334, -7.58, 0.168, -2.24, 0.334, -2.77]}, {"time": 0.5, "x": -7.58, "y": -2.77, "curve": [0.944, -7.58, 1.389, 6.5, 0.944, -2.77, 1.389, 0.93]}, {"time": 1.8333, "x": 6.5, "y": 0.93, "curve": [2.112, 6.5, 2.389, 0.96, 2.112, 0.93, 2.389, -0.53]}, {"time": 2.6667, "x": -3.1, "y": -1.59, "curve": [2.835, -5.56, 3.001, -7.58, 2.835, -2.24, 3.001, -2.77]}, {"time": 3.1667, "x": -7.58, "y": -2.77, "curve": [3.611, -7.58, 4.056, 6.5, 3.611, -2.77, 4.056, 0.93]}, {"time": 4.5, "x": 6.5, "y": 0.93, "curve": [4.779, 6.5, 5.056, 0.96, 4.779, 0.93, 5.056, -0.53]}, {"time": 5.3333, "x": -3.1, "y": -1.59, "curve": [5.501, -5.56, 5.667, -7.58, 5.501, -2.24, 5.667, -2.77]}, {"time": 5.8333, "x": -7.58, "y": -2.77, "curve": [6.278, -7.58, 6.722, 6.5, 6.278, -2.77, 6.722, 0.93]}, {"time": 7.1667, "x": 6.5, "y": 0.93, "curve": [7.445, 6.5, 7.722, 0.96, 7.445, 0.93, 7.722, -0.53]}, {"time": 8, "x": -3.1, "y": -1.59, "curve": [8.168, -5.56, 8.334, -7.58, 8.168, -2.24, 8.334, -2.77]}, {"time": 8.5, "x": -7.58, "y": -2.77, "curve": [8.944, -7.58, 9.389, 6.5, 8.944, -2.77, 9.389, 0.93]}, {"time": 9.8333, "x": 6.5, "y": 0.93, "curve": [10.112, 6.5, 10.39, 1.03, 10.112, 0.93, 10.39, -0.51]}, {"time": 10.6667, "x": -3.1, "y": -1.59}]}, "bone19": {"translate": [{"x": -5.32, "y": -2.18, "curve": [0.114, -6.61, 0.224, -7.58, 0.114, -2.52, 0.224, -2.77]}, {"time": 0.3333, "x": -7.58, "y": -2.77, "curve": [0.778, -7.58, 1.222, 6.5, 0.778, -2.77, 1.222, 0.93]}, {"time": 1.6667, "x": 6.5, "y": 0.93, "curve": [2.001, 6.5, 2.333, -1.55, 2.001, 0.93, 2.333, -1.19]}, {"time": 2.6667, "x": -5.32, "y": -2.18, "curve": [2.781, -6.61, 2.89, -7.58, 2.781, -2.52, 2.89, -2.77]}, {"time": 3, "x": -7.58, "y": -2.77, "curve": [3.444, -7.58, 3.889, 6.5, 3.444, -2.77, 3.889, 0.93]}, {"time": 4.3333, "x": 6.5, "y": 0.93, "curve": [4.668, 6.5, 5, -1.55, 4.668, 0.93, 5, -1.19]}, {"time": 5.3333, "x": -5.32, "y": -2.18, "curve": [5.447, -6.61, 5.557, -7.58, 5.447, -2.52, 5.557, -2.77]}, {"time": 5.6667, "x": -7.58, "y": -2.77, "curve": [6.111, -7.58, 6.556, 6.5, 6.111, -2.77, 6.556, 0.93]}, {"time": 7, "x": 6.5, "y": 0.93, "curve": [7.335, 6.5, 7.667, -1.55, 7.335, 0.93, 7.667, -1.19]}, {"time": 8, "x": -5.32, "y": -2.18, "curve": [8.114, -6.61, 8.224, -7.58, 8.114, -2.52, 8.224, -2.77]}, {"time": 8.3333, "x": -7.58, "y": -2.77, "curve": [8.778, -7.58, 9.222, 6.5, 8.778, -2.77, 9.222, 0.93]}, {"time": 9.6667, "x": 6.5, "y": 0.93, "curve": [10.001, 6.5, 10.336, -1.38, 10.001, 0.93, 10.336, -1.14]}, {"time": 10.6667, "x": -5.32, "y": -2.18}]}, "bone20": {"translate": [{"x": 2.03, "y": -0.25, "curve": [0.279, -2.1, 0.556, -7.58, 0.279, -1.33, 0.556, -2.77]}, {"time": 0.8333, "x": -7.58, "y": -2.77, "curve": [1.278, -7.58, 1.722, 6.5, 1.278, -2.77, 1.722, 0.93]}, {"time": 2.1667, "x": 6.5, "y": 0.93, "curve": [2.334, 6.5, 2.5, 4.48, 2.334, 0.93, 2.5, 0.4]}, {"time": 2.6667, "x": 2.03, "y": -0.25, "curve": [2.946, -2.1, 3.223, -7.58, 2.946, -1.33, 3.223, -2.77]}, {"time": 3.5, "x": -7.58, "y": -2.77, "curve": [3.944, -7.58, 4.389, 6.5, 3.944, -2.77, 4.389, 0.93]}, {"time": 4.8333, "x": 6.5, "y": 0.93, "curve": [5.001, 6.5, 5.167, 4.48, 5.001, 0.93, 5.167, 0.4]}, {"time": 5.3333, "x": 2.03, "y": -0.25, "curve": [5.613, -2.1, 5.89, -7.58, 5.613, -1.33, 5.89, -2.77]}, {"time": 6.1667, "x": -7.58, "y": -2.77, "curve": [6.611, -7.58, 7.056, 6.5, 6.611, -2.77, 7.056, 0.93]}, {"time": 7.5, "x": 6.5, "y": 0.93, "curve": [7.667, 6.5, 7.833, 4.48, 7.667, 0.93, 7.833, 0.4]}, {"time": 8, "x": 2.03, "y": -0.25, "curve": [8.279, -2.1, 8.556, -7.58, 8.279, -1.33, 8.556, -2.77]}, {"time": 8.8333, "x": -7.58, "y": -2.77, "curve": [9.278, -7.58, 9.722, 6.5, 9.278, -2.77, 9.722, 0.93]}, {"time": 10.1667, "x": 6.5, "y": 0.93, "curve": [10.334, 6.5, 10.501, 4.52, 10.334, 0.93, 10.501, 0.41]}, {"time": 10.6667, "x": 2.03, "y": -0.25}]}, "bone21": {"translate": [{"x": -0.54, "y": -0.92, "curve": [0.225, -4.03, 0.446, -7.58, 0.225, -1.84, 0.446, -2.77]}, {"time": 0.6667, "x": -7.58, "y": -2.77, "curve": [1.111, -7.58, 1.556, 6.5, 1.111, -2.77, 1.556, 0.93]}, {"time": 2, "x": 6.5, "y": 0.93, "curve": [2.224, 6.5, 2.444, 2.91, 2.224, 0.93, 2.444, -0.01]}, {"time": 2.6667, "x": -0.54, "y": -0.92, "curve": [2.892, -4.03, 3.113, -7.58, 2.892, -1.84, 3.113, -2.77]}, {"time": 3.3333, "x": -7.58, "y": -2.77, "curve": [3.778, -7.58, 4.222, 6.5, 3.778, -2.77, 4.222, 0.93]}, {"time": 4.6667, "x": 6.5, "y": 0.93, "curve": [4.89, 6.5, 5.111, 2.91, 4.89, 0.93, 5.111, -0.01]}, {"time": 5.3333, "x": -0.54, "y": -0.92, "curve": [5.559, -4.03, 5.779, -7.58, 5.559, -1.84, 5.779, -2.77]}, {"time": 6, "x": -7.58, "y": -2.77, "curve": [6.444, -7.58, 6.889, 6.5, 6.444, -2.77, 6.889, 0.93]}, {"time": 7.3333, "x": 6.5, "y": 0.93, "curve": [7.557, 6.5, 7.778, 2.91, 7.557, 0.93, 7.778, -0.01]}, {"time": 8, "x": -0.54, "y": -0.92, "curve": [8.225, -4.03, 8.446, -7.58, 8.225, -1.84, 8.446, -2.77]}, {"time": 8.6667, "x": -7.58, "y": -2.77, "curve": [9.111, -7.58, 9.556, 6.5, 9.111, -2.77, 9.556, 0.93]}, {"time": 10, "x": 6.5, "y": 0.93, "curve": [10.224, 6.5, 10.447, 3.01, 10.224, 0.93, 10.447, 0.01]}, {"time": 10.6667, "x": -0.54, "y": -0.92}]}, "bone22": {"translate": [{"x": 5.84, "y": 0.75, "curve": [0.39, 3.12, 0.779, -7.58, 0.39, 0.04, 0.779, -2.77]}, {"time": 1.1667, "x": -7.58, "y": -2.77, "curve": [1.611, -7.58, 2.056, 6.5, 1.611, -2.77, 2.056, 0.93]}, {"time": 2.5, "x": 6.5, "y": 0.93, "curve": [2.556, 6.5, 2.611, 6.23, 2.556, 0.93, 2.611, 0.86]}, {"time": 2.6667, "x": 5.84, "y": 0.75, "curve": [3.057, 3.12, 3.445, -7.58, 3.057, 0.04, 3.445, -2.77]}, {"time": 3.8333, "x": -7.58, "y": -2.77, "curve": [4.278, -7.58, 4.722, 6.5, 4.278, -2.77, 4.722, 0.93]}, {"time": 5.1667, "x": 6.5, "y": 0.93, "curve": [5.223, 6.5, 5.278, 6.23, 5.223, 0.93, 5.278, 0.86]}, {"time": 5.3333, "x": 5.84, "y": 0.75, "curve": [5.724, 3.12, 6.112, -7.58, 5.724, 0.04, 6.112, -2.77]}, {"time": 6.5, "x": -7.58, "y": -2.77, "curve": [6.944, -7.58, 7.389, 6.5, 6.944, -2.77, 7.389, 0.93]}, {"time": 7.8333, "x": 6.5, "y": 0.93, "curve": [7.89, 6.5, 7.944, 6.23, 7.89, 0.93, 7.944, 0.86]}, {"time": 8, "x": 5.84, "y": 0.75, "curve": [8.39, 3.12, 8.779, -7.58, 8.39, 0.04, 8.779, -2.77]}, {"time": 9.1667, "x": -7.58, "y": -2.77, "curve": [9.611, -7.58, 10.056, 6.5, 9.611, -2.77, 10.056, 0.93]}, {"time": 10.5, "x": 6.5, "y": 0.93, "curve": [10.556, 6.5, 10.613, 6.24, 10.556, 0.93, 10.613, 0.86]}, {"time": 10.6667, "x": 5.84, "y": 0.75}]}, "tree_Ra2": {"rotate": [{"value": 0.17, "curve": [0.225, 0.92, 0.446, 1.68]}, {"time": 0.6667, "value": 1.68, "curve": [1.111, 1.68, 1.556, -1.33]}, {"time": 2, "value": -1.33, "curve": [2.224, -1.33, 2.444, -0.56]}, {"time": 2.6667, "value": 0.17, "curve": [2.892, 0.92, 3.113, 1.68]}, {"time": 3.3333, "value": 1.68, "curve": [3.778, 1.68, 4.222, -1.33]}, {"time": 4.6667, "value": -1.33, "curve": [4.89, -1.33, 5.111, -0.56]}, {"time": 5.3333, "value": 0.17, "curve": [5.559, 0.92, 5.779, 1.68]}, {"time": 6, "value": 1.68, "curve": [6.444, 1.68, 6.889, -1.33]}, {"time": 7.3333, "value": -1.33, "curve": [7.557, -1.33, 7.778, -0.56]}, {"time": 8, "value": 0.17, "curve": [8.225, 0.92, 8.446, 1.68]}, {"time": 8.6667, "value": 1.68, "curve": [9.111, 1.68, 9.556, -1.33]}, {"time": 10, "value": -1.33, "curve": [10.224, -1.33, 10.447, -0.58]}, {"time": 10.6667, "value": 0.17}]}, "tree_Ra3": {"rotate": [{"value": -1.93, "curve": [0.336, -0.44, 0.668, 2.48]}, {"time": 1, "value": 2.48, "curve": [1.444, 2.48, 1.889, -2.77]}, {"time": 2.3333, "value": -2.77, "curve": [2.446, -2.77, 2.556, -2.42]}, {"time": 2.6667, "value": -1.93, "curve": [3.003, -0.44, 3.335, 2.48]}, {"time": 3.6667, "value": 2.48, "curve": [4.111, 2.48, 4.556, -2.77]}, {"time": 5, "value": -2.77, "curve": [5.113, -2.77, 5.222, -2.42]}, {"time": 5.3333, "value": -1.93, "curve": [5.67, -0.44, 6.001, 2.48]}, {"time": 6.3333, "value": 2.48, "curve": [6.778, 2.48, 7.222, -2.77]}, {"time": 7.6667, "value": -2.77, "curve": [7.779, -2.77, 7.889, -2.42]}, {"time": 8, "value": -1.93, "curve": [8.336, -0.44, 8.668, 2.48]}, {"time": 9, "value": 2.48, "curve": [9.444, 2.48, 9.889, -2.77]}, {"time": 10.3333, "value": -2.77, "curve": [10.446, -2.77, 10.559, -2.43]}, {"time": 10.6667, "value": -1.93}]}, "tree_Ra7": {"rotate": [{"value": -5.47, "curve": [0.39, -3.41, 0.779, 4.7]}, {"time": 1.1667, "value": 4.7, "curve": [1.611, 4.7, 2.056, -5.98]}, {"time": 2.5, "value": -5.98, "curve": [2.556, -5.98, 2.611, -5.77]}, {"time": 2.6667, "value": -5.47, "curve": [3.057, -3.41, 3.445, 4.7]}, {"time": 3.8333, "value": 4.7, "curve": [4.278, 4.7, 4.722, -5.98]}, {"time": 5.1667, "value": -5.98, "curve": [5.223, -5.98, 5.278, -5.77]}, {"time": 5.3333, "value": -5.47, "curve": [5.724, -3.41, 6.112, 4.7]}, {"time": 6.5, "value": 4.7, "curve": [6.944, 4.7, 7.389, -5.98]}, {"time": 7.8333, "value": -5.98, "curve": [7.89, -5.98, 7.944, -5.77]}, {"time": 8, "value": -5.47, "curve": [8.39, -3.41, 8.779, 4.7]}, {"time": 9.1667, "value": 4.7, "curve": [9.611, 4.7, 10.056, -5.98]}, {"time": 10.5, "value": -5.98, "curve": [10.556, -5.98, 10.613, -5.77]}, {"time": 10.6667, "value": -5.47}]}, "tree_Ra8": {"rotate": [{"value": -5.47, "curve": [0.057, -5.76, 0.112, -5.98]}, {"time": 0.1667, "value": -5.98, "curve": [0.611, -5.98, 1.056, 4.7]}, {"time": 1.5, "value": 4.7, "curve": [1.89, 4.7, 2.278, -3.53]}, {"time": 2.6667, "value": -5.47, "curve": [2.724, -5.76, 2.779, -5.98]}, {"time": 2.8333, "value": -5.98, "curve": [3.278, -5.98, 3.722, 4.7]}, {"time": 4.1667, "value": 4.7, "curve": [4.556, 4.7, 4.944, -3.53]}, {"time": 5.3333, "value": -5.47, "curve": [5.39, -5.76, 5.445, -5.98]}, {"time": 5.5, "value": -5.98, "curve": [5.944, -5.98, 6.389, 4.7]}, {"time": 6.8333, "value": 4.7, "curve": [7.223, 4.7, 7.611, -3.53]}, {"time": 8, "value": -5.47, "curve": [8.057, -5.76, 8.112, -5.98]}, {"time": 8.1667, "value": -5.98, "curve": [8.611, -5.98, 9.056, 4.7]}, {"time": 9.5, "value": 4.7, "curve": [9.89, 4.7, 10.279, -3.45]}, {"time": 10.6667, "value": -5.47}]}, "tree_Ra4": {"rotate": [{"value": -5.98, "curve": [0.444, -5.98, 0.889, 4.7]}, {"time": 1.3333, "value": 4.7, "curve": [1.778, 4.7, 2.222, -5.98]}, {"time": 2.6667, "value": -5.98, "curve": [3.111, -5.98, 3.556, 4.7]}, {"time": 4, "value": 4.7, "curve": [4.444, 4.7, 4.889, -5.98]}, {"time": 5.3333, "value": -5.98, "curve": [5.778, -5.98, 6.222, 4.7]}, {"time": 6.6667, "value": 4.7, "curve": [7.111, 4.7, 7.556, -5.98]}, {"time": 8, "value": -5.98, "curve": [8.444, -5.98, 8.889, 4.7]}, {"time": 9.3333, "value": 4.7, "curve": [9.778, 4.7, 10.222, -5.98]}, {"time": 10.6667, "value": -5.98}]}, "tree_Ra5": {"rotate": [{"value": -4.27, "curve": [0.114, -5.25, 0.224, -5.98]}, {"time": 0.3333, "value": -5.98, "curve": [0.778, -5.98, 1.222, 4.7]}, {"time": 1.6667, "value": 4.7, "curve": [2.001, 4.7, 2.333, -1.41]}, {"time": 2.6667, "value": -4.27, "curve": [2.781, -5.25, 2.89, -5.98]}, {"time": 3, "value": -5.98, "curve": [3.444, -5.98, 3.889, 4.7]}, {"time": 4.3333, "value": 4.7, "curve": [4.668, 4.7, 5, -1.41]}, {"time": 5.3333, "value": -4.27, "curve": [5.447, -5.25, 5.557, -5.98]}, {"time": 5.6667, "value": -5.98, "curve": [6.111, -5.98, 6.556, 4.7]}, {"time": 7, "value": 4.7, "curve": [7.335, 4.7, 7.667, -1.41]}, {"time": 8, "value": -4.27, "curve": [8.114, -5.25, 8.224, -5.98]}, {"time": 8.3333, "value": -5.98, "curve": [8.778, -5.98, 9.222, 4.7]}, {"time": 9.6667, "value": 4.7, "curve": [10.001, 4.7, 10.336, -1.28]}, {"time": 10.6667, "value": -4.27}]}, "tree_Rb2": {"rotate": [{"value": 1.68, "curve": [0.114, 2.14, 0.224, 2.48]}, {"time": 0.3333, "value": 2.48, "curve": [0.778, 2.48, 1.222, -2.53]}, {"time": 1.6667, "value": -2.53, "curve": [2.001, -2.53, 2.333, 0.33]}, {"time": 2.6667, "value": 1.68, "curve": [2.781, 2.14, 2.89, 2.48]}, {"time": 3, "value": 2.48, "curve": [3.444, 2.48, 3.889, -2.53]}, {"time": 4.3333, "value": -2.53, "curve": [4.668, -2.53, 5, 0.33]}, {"time": 5.3333, "value": 1.68, "curve": [5.447, 2.14, 5.557, 2.48]}, {"time": 5.6667, "value": 2.48, "curve": [6.111, 2.48, 6.556, -2.53]}, {"time": 7, "value": -2.53, "curve": [7.335, -2.53, 7.667, 0.33]}, {"time": 8, "value": 1.68, "curve": [8.114, 2.14, 8.224, 2.48]}, {"time": 8.3333, "value": 2.48, "curve": [8.778, 2.48, 9.222, -2.53]}, {"time": 9.6667, "value": -2.53, "curve": [10.001, -2.53, 10.336, 0.27]}, {"time": 10.6667, "value": 1.68}]}, "tree_Rb3": {"rotate": [{"value": -0.03, "curve": [0.225, 1.22, 0.446, 2.48]}, {"time": 0.6667, "value": 2.48, "curve": [1.111, 2.48, 1.556, -2.53]}, {"time": 2, "value": -2.53, "curve": [2.224, -2.53, 2.444, -1.26]}, {"time": 2.6667, "value": -0.03, "curve": [2.892, 1.22, 3.113, 2.48]}, {"time": 3.3333, "value": 2.48, "curve": [3.778, 2.48, 4.222, -2.53]}, {"time": 4.6667, "value": -2.53, "curve": [4.89, -2.53, 5.111, -1.26]}, {"time": 5.3333, "value": -0.03, "curve": [5.559, 1.22, 5.779, 2.48]}, {"time": 6, "value": 2.48, "curve": [6.444, 2.48, 6.889, -2.53]}, {"time": 7.3333, "value": -2.53, "curve": [7.557, -2.53, 7.778, -1.26]}, {"time": 8, "value": -0.03, "curve": [8.225, 1.22, 8.446, 2.48]}, {"time": 8.6667, "value": 2.48, "curve": [9.111, 2.48, 9.556, -2.53]}, {"time": 10, "value": -2.53, "curve": [10.224, -2.53, 10.447, -1.29]}, {"time": 10.6667, "value": -0.03}]}, "tree_Rb4": {"rotate": [{"value": -1.73, "curve": [0.336, -0.32, 0.668, 2.48]}, {"time": 1, "value": 2.48, "curve": [1.444, 2.48, 1.889, -2.53]}, {"time": 2.3333, "value": -2.53, "curve": [2.446, -2.53, 2.556, -2.2]}, {"time": 2.6667, "value": -1.73, "curve": [3.003, -0.32, 3.335, 2.48]}, {"time": 3.6667, "value": 2.48, "curve": [4.111, 2.48, 4.556, -2.53]}, {"time": 5, "value": -2.53, "curve": [5.113, -2.53, 5.222, -2.2]}, {"time": 5.3333, "value": -1.73, "curve": [5.67, -0.32, 6.001, 2.48]}, {"time": 6.3333, "value": 2.48, "curve": [6.778, 2.48, 7.222, -2.53]}, {"time": 7.6667, "value": -2.53, "curve": [7.779, -2.53, 7.889, -2.2]}, {"time": 8, "value": -1.73, "curve": [8.336, -0.32, 8.668, 2.48]}, {"time": 9, "value": 2.48, "curve": [9.444, 2.48, 9.889, -2.53]}, {"time": 10.3333, "value": -2.53, "curve": [10.446, -2.53, 10.559, -2.21]}, {"time": 10.6667, "value": -1.73}]}, "tree_Rb5": {"rotate": [{"value": -2.53, "curve": [0.444, -2.53, 0.889, 2.48]}, {"time": 1.3333, "value": 2.48, "curve": [1.778, 2.48, 2.222, -2.53]}, {"time": 2.6667, "value": -2.53, "curve": [3.111, -2.53, 3.556, 2.48]}, {"time": 4, "value": 2.48, "curve": [4.444, 2.48, 4.889, -2.53]}, {"time": 5.3333, "value": -2.53, "curve": [5.778, -2.53, 6.222, 2.48]}, {"time": 6.6667, "value": 2.48, "curve": [7.111, 2.48, 7.556, -2.53]}, {"time": 8, "value": -2.53, "curve": [8.444, -2.53, 8.889, 2.48]}, {"time": 9.3333, "value": 2.48, "curve": [9.778, 2.48, 10.222, -2.53]}, {"time": 10.6667, "value": -2.53}]}, "tree_Rc2": {"rotate": [{"value": 2.21, "curve": [0.444, 2.21, 0.889, -2.04]}, {"time": 1.3333, "value": -2.04, "curve": [1.778, -2.04, 2.222, 2.21]}, {"time": 2.6667, "value": 2.21, "curve": [3.111, 2.21, 3.556, -2.04]}, {"time": 4, "value": -2.04, "curve": [4.444, -2.04, 4.889, 2.21]}, {"time": 5.3333, "value": 2.21, "curve": [5.778, 2.21, 6.222, -2.04]}, {"time": 6.6667, "value": -2.04, "curve": [7.111, -2.04, 7.556, 2.21]}, {"time": 8, "value": 2.21, "curve": [8.444, 2.21, 8.889, -2.04]}, {"time": 9.3333, "value": -2.04, "curve": [9.778, -2.04, 10.222, 2.21]}, {"time": 10.6667, "value": 2.21}]}, "tree_Rc3": {"rotate": [{"value": 2.51, "curve": [0.114, 3.17, 0.224, 3.67]}, {"time": 0.3333, "value": 3.67, "curve": [0.778, 3.67, 1.222, -3.56]}, {"time": 1.6667, "value": -3.56, "curve": [2.001, -3.56, 2.333, 0.57]}, {"time": 2.6667, "value": 2.51, "curve": [2.781, 3.17, 2.89, 3.67]}, {"time": 3, "value": 3.67, "curve": [3.444, 3.67, 3.889, -3.56]}, {"time": 4.3333, "value": -3.56, "curve": [4.668, -3.56, 5, 0.57]}, {"time": 5.3333, "value": 2.51, "curve": [5.447, 3.17, 5.557, 3.67]}, {"time": 5.6667, "value": 3.67, "curve": [6.111, 3.67, 6.556, -3.56]}, {"time": 7, "value": -3.56, "curve": [7.335, -3.56, 7.667, 0.57]}, {"time": 8, "value": 2.51, "curve": [8.114, 3.17, 8.224, 3.67]}, {"time": 8.3333, "value": 3.67, "curve": [8.778, 3.67, 9.222, -3.56]}, {"time": 9.6667, "value": -3.56, "curve": [10.001, -3.56, 10.336, 0.49]}, {"time": 10.6667, "value": 2.51}]}, "tree_Rc4": {"rotate": [{"value": -0.34, "curve": [0.225, 2.3, 0.446, 4.98]}, {"time": 0.6667, "value": 4.98, "curve": [1.111, 4.98, 1.556, -5.67]}, {"time": 2, "value": -5.67, "curve": [2.224, -5.67, 2.444, -2.95]}, {"time": 2.6667, "value": -0.34, "curve": [2.892, 2.3, 3.113, 4.98]}, {"time": 3.3333, "value": 4.98, "curve": [3.778, 4.98, 4.222, -5.67]}, {"time": 4.6667, "value": -5.67, "curve": [4.89, -5.67, 5.111, -2.95]}, {"time": 5.3333, "value": -0.34, "curve": [5.559, 2.3, 5.779, 4.98]}, {"time": 6, "value": 4.98, "curve": [6.444, 4.98, 6.889, -5.67]}, {"time": 7.3333, "value": -5.67, "curve": [7.557, -5.67, 7.778, -2.95]}, {"time": 8, "value": -0.34, "curve": [8.225, 2.3, 8.446, 4.98]}, {"time": 8.6667, "value": 4.98, "curve": [9.111, 4.98, 9.556, -5.67]}, {"time": 10, "value": -5.67, "curve": [10.224, -5.67, 10.447, -3.02]}, {"time": 10.6667, "value": -0.34}]}, "tree_Rc5": {"rotate": [{"value": -3.96, "curve": [0.336, -0.96, 0.668, 4.98]}, {"time": 1, "value": 4.98, "curve": [1.444, 4.98, 1.889, -5.67]}, {"time": 2.3333, "value": -5.67, "curve": [2.446, -5.67, 2.556, -4.96]}, {"time": 2.6667, "value": -3.96, "curve": [3.003, -0.96, 3.335, 4.98]}, {"time": 3.6667, "value": 4.98, "curve": [4.111, 4.98, 4.556, -5.67]}, {"time": 5, "value": -5.67, "curve": [5.113, -5.67, 5.222, -4.96]}, {"time": 5.3333, "value": -3.96, "curve": [5.67, -0.96, 6.001, 4.98]}, {"time": 6.3333, "value": 4.98, "curve": [6.778, 4.98, 7.222, -5.67]}, {"time": 7.6667, "value": -5.67, "curve": [7.779, -5.67, 7.889, -4.96]}, {"time": 8, "value": -3.96, "curve": [8.336, -0.96, 8.668, 4.98]}, {"time": 9, "value": 4.98, "curve": [9.444, 4.98, 9.889, -5.67]}, {"time": 10.3333, "value": -5.67, "curve": [10.446, -5.67, 10.559, -4.98]}, {"time": 10.6667, "value": -3.96}]}, "tree_Rc6": {"rotate": [{"value": -5.67, "curve": [0.444, -5.67, 0.889, 4.98]}, {"time": 1.3333, "value": 4.98, "curve": [1.778, 4.98, 2.222, -5.67]}, {"time": 2.6667, "value": -5.67, "curve": [3.111, -5.67, 3.556, 4.98]}, {"time": 4, "value": 4.98, "curve": [4.444, 4.98, 4.889, -5.67]}, {"time": 5.3333, "value": -5.67, "curve": [5.778, -5.67, 6.222, 4.98]}, {"time": 6.6667, "value": 4.98, "curve": [7.111, 4.98, 7.556, -5.67]}, {"time": 8, "value": -5.67, "curve": [8.444, -5.67, 8.889, 4.98]}, {"time": 9.3333, "value": 4.98, "curve": [9.778, 4.98, 10.222, -5.67]}, {"time": 10.6667, "value": -5.67}]}, "tree_Rc7": {"rotate": [{"value": -5.67, "curve": [0.444, -5.67, 0.889, 4.98]}, {"time": 1.3333, "value": 4.98, "curve": [1.778, 4.98, 2.222, -5.67]}, {"time": 2.6667, "value": -5.67, "curve": [3.111, -5.67, 3.556, 4.98]}, {"time": 4, "value": 4.98, "curve": [4.444, 4.98, 4.889, -5.67]}, {"time": 5.3333, "value": -5.67, "curve": [5.778, -5.67, 6.222, 4.98]}, {"time": 6.6667, "value": 4.98, "curve": [7.111, 4.98, 7.556, -5.67]}, {"time": 8, "value": -5.67, "curve": [8.444, -5.67, 8.889, 4.98]}, {"time": 9.3333, "value": 4.98, "curve": [9.778, 4.98, 10.222, -5.67]}, {"time": 10.6667, "value": -5.67}]}, "tree_Rc8": {"rotate": [{"value": -3.96, "curve": [0.114, -4.94, 0.224, -5.67]}, {"time": 0.3333, "value": -5.67, "curve": [0.778, -5.67, 1.222, 4.98]}, {"time": 1.6667, "value": 4.98, "curve": [2.001, 4.98, 2.333, -1.11]}, {"time": 2.6667, "value": -3.96, "curve": [2.781, -4.94, 2.89, -5.67]}, {"time": 3, "value": -5.67, "curve": [3.444, -5.67, 3.889, 4.98]}, {"time": 4.3333, "value": 4.98, "curve": [4.668, 4.98, 5, -1.11]}, {"time": 5.3333, "value": -3.96, "curve": [5.447, -4.94, 5.557, -5.67]}, {"time": 5.6667, "value": -5.67, "curve": [6.111, -5.67, 6.556, 4.98]}, {"time": 7, "value": 4.98, "curve": [7.335, 4.98, 7.667, -1.11]}, {"time": 8, "value": -3.96, "curve": [8.114, -4.94, 8.224, -5.67]}, {"time": 8.3333, "value": -5.67, "curve": [8.778, -5.67, 9.222, 4.98]}, {"time": 9.6667, "value": 4.98, "curve": [10.001, 4.98, 10.336, -0.98]}, {"time": 10.6667, "value": -3.96}]}, "tree_Lb2": {"rotate": [{"value": -1.61, "curve": [0.114, -2.05, 0.224, -2.37]}, {"time": 0.3333, "value": -2.37, "curve": [0.778, -2.37, 1.222, 2.42]}, {"time": 1.6667, "value": 2.42, "curve": [2.001, 2.42, 2.333, -0.32]}, {"time": 2.6667, "value": -1.61, "curve": [2.781, -2.05, 2.89, -2.37]}, {"time": 3, "value": -2.37, "curve": [3.444, -2.37, 3.889, 2.42]}, {"time": 4.3333, "value": 2.42, "curve": [4.668, 2.42, 5, -0.32]}, {"time": 5.3333, "value": -1.61, "curve": [5.447, -2.05, 5.557, -2.37]}, {"time": 5.6667, "value": -2.37, "curve": [6.111, -2.37, 6.556, 2.42]}, {"time": 7, "value": 2.42, "curve": [7.335, 2.42, 7.667, -0.32]}, {"time": 8, "value": -1.61, "curve": [8.114, -2.05, 8.224, -2.37]}, {"time": 8.3333, "value": -2.37, "curve": [8.778, -2.37, 9.222, 2.42]}, {"time": 9.6667, "value": 2.42, "curve": [10.001, 2.42, 10.336, -0.26]}, {"time": 10.6667, "value": -1.61}]}, "tree_Lb3": {"rotate": [{"value": 0.21, "curve": [0.225, -1.58, 0.446, -3.4]}, {"time": 0.6667, "value": -3.4, "curve": [1.111, -3.4, 1.556, 3.83]}, {"time": 2, "value": 3.83, "curve": [2.224, 3.83, 2.444, 1.99]}, {"time": 2.6667, "value": 0.21, "curve": [2.892, -1.58, 3.113, -3.4]}, {"time": 3.3333, "value": -3.4, "curve": [3.778, -3.4, 4.222, 3.83]}, {"time": 4.6667, "value": 3.83, "curve": [4.89, 3.83, 5.111, 1.99]}, {"time": 5.3333, "value": 0.21, "curve": [5.559, -1.58, 5.779, -3.4]}, {"time": 6, "value": -3.4, "curve": [6.444, -3.4, 6.889, 3.83]}, {"time": 7.3333, "value": 3.83, "curve": [7.557, 3.83, 7.778, 1.99]}, {"time": 8, "value": 0.21, "curve": [8.225, -1.58, 8.446, -3.4]}, {"time": 8.6667, "value": -3.4, "curve": [9.111, -3.4, 9.556, 3.83]}, {"time": 10, "value": 3.83, "curve": [10.224, 3.83, 10.447, 2.04]}, {"time": 10.6667, "value": 0.21}]}, "tree_Lb4": {"rotate": [{"value": 2.14, "curve": [0.279, -1.22, 0.556, -5.68]}, {"time": 0.8333, "value": -5.68, "curve": [1.278, -5.68, 1.722, 5.79]}, {"time": 2.1667, "value": 5.79, "curve": [2.334, 5.79, 2.5, 4.15]}, {"time": 2.6667, "value": 2.14, "curve": [2.946, -1.22, 3.223, -5.68]}, {"time": 3.5, "value": -5.68, "curve": [3.944, -5.68, 4.389, 5.79]}, {"time": 4.8333, "value": 5.79, "curve": [5.001, 5.79, 5.167, 4.15]}, {"time": 5.3333, "value": 2.14, "curve": [5.613, -1.22, 5.89, -5.68]}, {"time": 6.1667, "value": -5.68, "curve": [6.611, -5.68, 7.056, 5.79]}, {"time": 7.5, "value": 5.79, "curve": [7.667, 5.79, 7.833, 4.15]}, {"time": 8, "value": 2.14, "curve": [8.279, -1.22, 8.556, -5.68]}, {"time": 8.8333, "value": -5.68, "curve": [9.278, -5.68, 9.722, 5.79]}, {"time": 10.1667, "value": 5.79, "curve": [10.334, 5.79, 10.501, 4.17]}, {"time": 10.6667, "value": 2.14}]}, "tree_Lb5": {"rotate": [{"value": 5.26, "curve": [0.39, 3.04, 0.779, -5.68]}, {"time": 1.1667, "value": -5.68, "curve": [1.611, -5.68, 2.056, 5.79]}, {"time": 2.5, "value": 5.79, "curve": [2.556, 5.79, 2.611, 5.57]}, {"time": 2.6667, "value": 5.26, "curve": [3.057, 3.04, 3.445, -5.68]}, {"time": 3.8333, "value": -5.68, "curve": [4.278, -5.68, 4.722, 5.79]}, {"time": 5.1667, "value": 5.79, "curve": [5.223, 5.79, 5.278, 5.57]}, {"time": 5.3333, "value": 5.26, "curve": [5.724, 3.04, 6.112, -5.68]}, {"time": 6.5, "value": -5.68, "curve": [6.944, -5.68, 7.389, 5.79]}, {"time": 7.8333, "value": 5.79, "curve": [7.89, 5.79, 7.944, 5.57]}, {"time": 8, "value": 5.26, "curve": [8.39, 3.04, 8.779, -5.68]}, {"time": 9.1667, "value": -5.68, "curve": [9.611, -5.68, 10.056, 5.79]}, {"time": 10.5, "value": 5.79, "curve": [10.556, 5.79, 10.613, 5.58]}, {"time": 10.6667, "value": 5.26}]}, "tree_Lb6": {"rotate": [{"value": 5.79, "curve": [0.444, 5.79, 0.889, -5.68]}, {"time": 1.3333, "value": -5.68, "curve": [1.778, -5.68, 2.222, 5.79]}, {"time": 2.6667, "value": 5.79, "curve": [3.111, 5.79, 3.556, -5.68]}, {"time": 4, "value": -5.68, "curve": [4.444, -5.68, 4.889, 5.79]}, {"time": 5.3333, "value": 5.79, "curve": [5.778, 5.79, 6.222, -5.68]}, {"time": 6.6667, "value": -5.68, "curve": [7.111, -5.68, 7.556, 5.79]}, {"time": 8, "value": 5.79, "curve": [8.444, 5.79, 8.889, -5.68]}, {"time": 9.3333, "value": -5.68, "curve": [9.778, -5.68, 10.222, 5.79]}, {"time": 10.6667, "value": 5.79}]}, "tree_Lb7": {"rotate": [{"value": 3.96, "curve": [0.114, 5.01, 0.224, 5.79]}, {"time": 0.3333, "value": 5.79, "curve": [0.778, 5.79, 1.222, -5.68]}, {"time": 1.6667, "value": -5.68, "curve": [2.001, -5.68, 2.333, 0.88]}, {"time": 2.6667, "value": 3.96, "curve": [2.781, 5.01, 2.89, 5.79]}, {"time": 3, "value": 5.79, "curve": [3.444, 5.79, 3.889, -5.68]}, {"time": 4.3333, "value": -5.68, "curve": [4.668, -5.68, 5, 0.88]}, {"time": 5.3333, "value": 3.96, "curve": [5.447, 5.01, 5.557, 5.79]}, {"time": 5.6667, "value": 5.79, "curve": [6.111, 5.79, 6.556, -5.68]}, {"time": 7, "value": -5.68, "curve": [7.335, -5.68, 7.667, 0.88]}, {"time": 8, "value": 3.96, "curve": [8.114, 5.01, 8.224, 5.79]}, {"time": 8.3333, "value": 5.79, "curve": [8.778, 5.79, 9.222, -5.68]}, {"time": 9.6667, "value": -5.68, "curve": [10.001, -5.68, 10.336, 0.74]}, {"time": 10.6667, "value": 3.96}]}, "tree_Lb8": {"rotate": [{"value": 2.14, "curve": [0.168, 4.15, 0.334, 5.79]}, {"time": 0.5, "value": 5.79, "curve": [0.944, 5.79, 1.389, -5.68]}, {"time": 1.8333, "value": -5.68, "curve": [2.112, -5.68, 2.389, -1.17]}, {"time": 2.6667, "value": 2.14, "curve": [2.835, 4.15, 3.001, 5.79]}, {"time": 3.1667, "value": 5.79, "curve": [3.611, 5.79, 4.056, -5.68]}, {"time": 4.5, "value": -5.68, "curve": [4.779, -5.68, 5.056, -1.17]}, {"time": 5.3333, "value": 2.14, "curve": [5.501, 4.15, 5.667, 5.79]}, {"time": 5.8333, "value": 5.79, "curve": [6.278, 5.79, 6.722, -5.68]}, {"time": 7.1667, "value": -5.68, "curve": [7.445, -5.68, 7.722, -1.17]}, {"time": 8, "value": 2.14, "curve": [8.168, 4.15, 8.334, 5.79]}, {"time": 8.5, "value": 5.79, "curve": [8.944, 5.79, 9.389, -5.68]}, {"time": 9.8333, "value": -5.68, "curve": [10.112, -5.68, 10.39, -1.22]}, {"time": 10.6667, "value": 2.14}]}, "tree_La2": {"rotate": [{"value": 2.03, "curve": [0.057, 2.17, 0.112, 2.27]}, {"time": 0.1667, "value": 2.27, "curve": [0.611, 2.27, 1.056, -2.91]}, {"time": 1.5, "value": -2.91, "curve": [1.89, -2.91, 2.278, 1.09]}, {"time": 2.6667, "value": 2.03, "curve": [2.724, 2.17, 2.779, 2.27]}, {"time": 2.8333, "value": 2.27, "curve": [3.278, 2.27, 3.722, -2.91]}, {"time": 4.1667, "value": -2.91, "curve": [4.556, -2.91, 4.944, 1.09]}, {"time": 5.3333, "value": 2.03, "curve": [5.39, 2.17, 5.445, 2.27]}, {"time": 5.5, "value": 2.27, "curve": [5.944, 2.27, 6.389, -2.91]}, {"time": 6.8333, "value": -2.91, "curve": [7.223, -2.91, 7.611, 1.09]}, {"time": 8, "value": 2.03, "curve": [8.057, 2.17, 8.112, 2.27]}, {"time": 8.1667, "value": 2.27, "curve": [8.611, 2.27, 9.056, -2.91]}, {"time": 9.5, "value": -2.91, "curve": [9.89, -2.91, 10.279, 1.05]}, {"time": 10.6667, "value": 2.03}]}, "tree_La3": {"rotate": [{"value": 1.03, "curve": [0.168, 2.5, 0.334, 3.7]}, {"time": 0.5, "value": 3.7, "curve": [0.944, 3.7, 1.389, -4.68]}, {"time": 1.8333, "value": -4.68, "curve": [2.112, -4.68, 2.389, -1.39]}, {"time": 2.6667, "value": 1.03, "curve": [2.835, 2.5, 3.001, 3.7]}, {"time": 3.1667, "value": 3.7, "curve": [3.611, 3.7, 4.056, -4.68]}, {"time": 4.5, "value": -4.68, "curve": [4.779, -4.68, 5.056, -1.39]}, {"time": 5.3333, "value": 1.03, "curve": [5.501, 2.5, 5.667, 3.7]}, {"time": 5.8333, "value": 3.7, "curve": [6.278, 3.7, 6.722, -4.68]}, {"time": 7.1667, "value": -4.68, "curve": [7.445, -4.68, 7.722, -1.39]}, {"time": 8, "value": 1.03, "curve": [8.168, 2.5, 8.334, 3.7]}, {"time": 8.5, "value": 3.7, "curve": [8.944, 3.7, 9.389, -4.68]}, {"time": 9.8333, "value": -4.68, "curve": [10.112, -4.68, 10.39, -1.43]}, {"time": 10.6667, "value": 1.03}]}, "tree_La4": {"rotate": [{"value": -0.64, "curve": [0.225, 2.33, 0.446, 5.33]}, {"time": 0.6667, "value": 5.33, "curve": [1.111, 5.33, 1.556, -6.6]}, {"time": 2, "value": -6.6, "curve": [2.224, -6.6, 2.444, -3.56]}, {"time": 2.6667, "value": -0.64, "curve": [2.892, 2.33, 3.113, 5.33]}, {"time": 3.3333, "value": 5.33, "curve": [3.778, 5.33, 4.222, -6.6]}, {"time": 4.6667, "value": -6.6, "curve": [4.89, -6.6, 5.111, -3.56]}, {"time": 5.3333, "value": -0.64, "curve": [5.559, 2.33, 5.779, 5.33]}, {"time": 6, "value": 5.33, "curve": [6.444, 5.33, 6.889, -6.6]}, {"time": 7.3333, "value": -6.6, "curve": [7.557, -6.6, 7.778, -3.56]}, {"time": 8, "value": -0.64, "curve": [8.225, 2.33, 8.446, 5.33]}, {"time": 8.6667, "value": 5.33, "curve": [9.111, 5.33, 9.556, -6.6]}, {"time": 10, "value": -6.6, "curve": [10.224, -6.6, 10.447, -3.64]}, {"time": 10.6667, "value": -0.64}]}, "tree_La5": {"rotate": [{"value": -6.04, "curve": [0.39, -3.74, 0.779, 5.33]}, {"time": 1.1667, "value": 5.33, "curve": [1.611, 5.33, 2.056, -6.6]}, {"time": 2.5, "value": -6.6, "curve": [2.556, -6.6, 2.611, -6.37]}, {"time": 2.6667, "value": -6.04, "curve": [3.057, -3.74, 3.445, 5.33]}, {"time": 3.8333, "value": 5.33, "curve": [4.278, 5.33, 4.722, -6.6]}, {"time": 5.1667, "value": -6.6, "curve": [5.223, -6.6, 5.278, -6.37]}, {"time": 5.3333, "value": -6.04, "curve": [5.724, -3.74, 6.112, 5.33]}, {"time": 6.5, "value": 5.33, "curve": [6.944, 5.33, 7.389, -6.6]}, {"time": 7.8333, "value": -6.6, "curve": [7.89, -6.6, 7.944, -6.37]}, {"time": 8, "value": -6.04, "curve": [8.39, -3.74, 8.779, 5.33]}, {"time": 9.1667, "value": 5.33, "curve": [9.611, 5.33, 10.056, -6.6]}, {"time": 10.5, "value": -6.6, "curve": [10.556, -6.6, 10.613, -6.37]}, {"time": 10.6667, "value": -6.04}]}, "tree_La6": {"rotate": [{"value": -4.69, "curve": [0.336, -1.32, 0.668, 5.33]}, {"time": 1, "value": 5.33, "curve": [1.444, 5.33, 1.889, -6.6]}, {"time": 2.3333, "value": -6.6, "curve": [2.446, -6.6, 2.556, -5.81]}, {"time": 2.6667, "value": -4.69, "curve": [3.003, -1.32, 3.335, 5.33]}, {"time": 3.6667, "value": 5.33, "curve": [4.111, 5.33, 4.556, -6.6]}, {"time": 5, "value": -6.6, "curve": [5.113, -6.6, 5.222, -5.81]}, {"time": 5.3333, "value": -4.69, "curve": [5.67, -1.32, 6.001, 5.33]}, {"time": 6.3333, "value": 5.33, "curve": [6.778, 5.33, 7.222, -6.6]}, {"time": 7.6667, "value": -6.6, "curve": [7.779, -6.6, 7.889, -5.81]}, {"time": 8, "value": -4.69, "curve": [8.336, -1.32, 8.668, 5.33]}, {"time": 9, "value": 5.33, "curve": [9.444, 5.33, 9.889, -6.6]}, {"time": 10.3333, "value": -6.6, "curve": [10.446, -6.6, 10.559, -5.84]}, {"time": 10.6667, "value": -4.69}]}, "plane": {"rotate": [{}, {"time": 10.6667, "value": -5760}]}, "bone28": {"rotate": [{"value": 7.02, "curve": [0.444, 7.02, 0.889, -7.22]}, {"time": 1.3333, "value": -7.22, "curve": [1.778, -7.22, 2.222, 7.02]}, {"time": 2.6667, "value": 7.02, "curve": [3.111, 7.02, 3.556, -7.22]}, {"time": 4, "value": -7.22, "curve": [4.444, -7.22, 4.889, 7.02]}, {"time": 5.3333, "value": 7.02, "curve": [5.778, 7.02, 6.222, -7.22]}, {"time": 6.6667, "value": -7.22, "curve": [7.111, -7.22, 7.556, 7.02]}, {"time": 8, "value": 7.02, "curve": [8.444, 7.02, 8.889, -7.22]}, {"time": 9.3333, "value": -7.22, "curve": [9.778, -7.22, 10.222, 7.02]}, {"time": 10.6667, "value": 7.02}]}, "plane2": {"rotate": [{}, {"time": 10.6667, "value": -5760}]}, "plane3": {"rotate": [{}, {"time": 10.6667, "value": -5760}]}, "plane4": {"rotate": [{}, {"time": 10.6667, "value": -5760}]}, "plane5": {"rotate": [{}, {"time": 10.6667, "value": -5760}]}, "plane6": {"rotate": [{}, {"time": 10.6667, "value": -5760}]}, "sign": {"scale": [{"time": 5, "curve": [5.018, 1, 5.111, 1.287, 5.018, 1, 5.111, 1.287]}, {"time": 5.1667, "x": 1.287, "y": 1.287, "curve": [5.222, 1.287, 5.278, 1.191, 5.222, 1.287, 5.278, 1.191]}, {"time": 5.3333, "x": 1.191, "y": 1.191, "curve": [5.389, 1.191, 5.444, 1.287, 5.389, 1.191, 5.444, 1.287]}, {"time": 5.5, "x": 1.287, "y": 1.287, "curve": [5.556, 1.287, 5.611, 1.251, 5.556, 1.287, 5.611, 1.251]}, {"time": 5.6667, "x": 1.251, "y": 1.251, "curve": "stepped"}, {"time": 6.6667, "x": 1.251, "y": 1.251, "curve": [6.708, 1.251, 6.792, 1.287, 6.708, 1.251, 6.792, 1.287]}, {"time": 6.8333, "x": 1.287, "y": 1.287, "curve": [6.889, 1.287, 6.944, 0.927, 6.889, 1.287, 6.944, 0.927]}, {"time": 7, "x": 0.927, "y": 0.927, "curve": [7.056, 0.927, 7.111, 1.039, 7.056, 0.927, 7.111, 1.039]}, {"time": 7.1667, "x": 1.039, "y": 1.039, "curve": [7.222, 1.039, 7.278, 1, 7.222, 1.039, 7.278, 1]}, {"time": 7.3333}]}, "bone24": {"translate": [{"time": 5.5, "curve": [5.544, 0, 5.589, -4.39, 5.544, 0, 5.589, 2.44]}, {"time": 5.6333, "x": -4.39, "y": 2.44, "curve": [5.678, -4.39, 5.722, 3.41, 5.678, 2.44, 5.722, -2.92]}, {"time": 5.7667, "x": 3.41, "y": -2.92, "curve": [5.811, 3.41, 5.856, -4.39, 5.811, -2.92, 5.856, 2.44]}, {"time": 5.9, "x": -4.39, "y": 2.44, "curve": [5.944, -4.39, 5.989, 3.41, 5.944, 2.44, 5.989, -2.92]}, {"time": 6.0333, "x": 3.41, "y": -2.92, "curve": [6.078, 3.41, 6.122, 0, 6.078, -2.92, 6.122, 0]}, {"time": 6.1667}]}, "bone26": {"translate": [{"time": 5.6667, "curve": [5.711, 0, 5.756, -2.44, 5.711, 0, 5.756, 1.74]}, {"time": 5.8, "x": -2.44, "y": 1.74, "curve": [5.844, -2.44, 5.889, 2.09, 5.844, 1.74, 5.889, -2.09]}, {"time": 5.9333, "x": 2.09, "y": -2.09, "curve": [5.978, 2.09, 6.022, -2.44, 5.978, -2.09, 6.022, 1.74]}, {"time": 6.0667, "x": -2.44, "y": 1.74, "curve": [6.111, -2.44, 6.156, 2.09, 6.111, 1.74, 6.156, -2.09]}, {"time": 6.2, "x": 2.09, "y": -2.09, "curve": [6.244, 2.09, 6.289, -2.44, 6.244, -2.09, 6.289, 1.74]}, {"time": 6.3333, "x": -2.44, "y": 1.74, "curve": [6.378, -2.44, 6.422, 0, 6.378, 1.74, 6.422, 0]}, {"time": 6.4667}]}, "bone23": {"translate": [{"time": 5.7, "curve": [5.744, 0, 5.789, -2.44, 5.744, 0, 5.789, 1.74]}, {"time": 5.8333, "x": -2.44, "y": 1.74, "curve": [5.878, -2.44, 5.922, 2.09, 5.878, 1.74, 5.922, -2.09]}, {"time": 5.9667, "x": 2.09, "y": -2.09, "curve": [6.011, 2.09, 6.056, -2.44, 6.011, -2.09, 6.056, 1.74]}, {"time": 6.1, "x": -2.44, "y": 1.74, "curve": [6.144, -2.44, 6.189, 2.09, 6.144, 1.74, 6.189, -2.09]}, {"time": 6.2333, "x": 2.09, "y": -2.09, "curve": [6.278, 2.09, 6.322, -2.44, 6.278, -2.09, 6.322, 1.74]}, {"time": 6.3667, "x": -2.44, "y": 1.74, "curve": [6.411, -2.44, 6.456, 0, 6.411, 1.74, 6.456, 0]}, {"time": 6.5}]}, "bone27": {"translate": [{"time": 5.7333, "curve": [5.778, 0, 5.822, -2.44, 5.778, 0, 5.822, 1.74]}, {"time": 5.8667, "x": -2.44, "y": 1.74, "curve": [5.911, -2.44, 5.956, 2.09, 5.911, 1.74, 5.956, -2.09]}, {"time": 6, "x": 2.09, "y": -2.09, "curve": [6.044, 2.09, 6.089, -2.44, 6.044, -2.09, 6.089, 1.74]}, {"time": 6.1333, "x": -2.44, "y": 1.74, "curve": [6.178, -2.44, 6.222, 2.09, 6.178, 1.74, 6.222, -2.09]}, {"time": 6.2667, "x": 2.09, "y": -2.09, "curve": [6.311, 2.09, 6.356, -2.44, 6.311, -2.09, 6.356, 1.74]}, {"time": 6.4, "x": -2.44, "y": 1.74, "curve": [6.444, -2.44, 6.489, 0, 6.444, 1.74, 6.489, 0]}, {"time": 6.5333}]}}}}}