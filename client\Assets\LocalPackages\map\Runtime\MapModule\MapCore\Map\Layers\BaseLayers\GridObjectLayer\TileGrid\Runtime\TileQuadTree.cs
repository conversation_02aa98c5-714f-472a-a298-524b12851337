﻿ 



 
 



using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class TileObjectInfo
    {
        public TileObjectInfo(int lod, int index, Rect bounds)
        {
            this.lod = (byte)lod;
            this.index = (short)index;
            this.bounds = bounds;
        }

        public Rect bounds;
        public short index;
        public byte lod;
    }

    public struct TileObjectInfo2
    {
        public TileObjectInfo2(int lod, int index, int viewID)
        {
            this.lod = lod;
            this.index = index;
            this.viewID = viewID;
        }
        public int lod;
        public int index;
        public int viewID;
    }

    public class TileQuadTreeNode
    {
        public TileQuadTreeNode(Rect bounds, int depth)
        {
            mDepth = depth;
            mBounds = bounds;
        }

        public bool IsFullInside(Rect objectBounds)
        {
            return Utils.FullContains(mBounds, objectBounds);
        }

        public bool IsIntersected(Rect worldBounds)
        {
            var min = worldBounds.min;
            var max = worldBounds.max;
            var regionMin = mBounds.min;
            var regionMax = mBounds.max;
            if (min.x > regionMax.x || min.y > regionMax.y || regionMin.x > max.x || regionMin.y > max.y)
            {
                return false;
            }
            return true;
        }

        public void Clear()
        {
            children = new TileQuadTreeNode[4];
            objects = new List<TileObjectInfo>();
        }

        public int depth { get { return mDepth; } }
        public Rect bounds { get { return mBounds; } }

        public TileQuadTreeNode[] children = new TileQuadTreeNode[4];
        public List<TileObjectInfo> objects = new List<TileObjectInfo>();
        Rect mBounds;
        int mDepth = 0;
    };

    //一个地块tile的四叉树
    public class TileQuadTree
    {
        public TileQuadTree(ModelTemplate tileModelTemplate, float mapMinX, float mapMinZ, float mapMaxX, float mapMaxZ, int maxDepth)
        {
            mTileModelTemplate = tileModelTemplate;
            mMapMinX = mapMinX;
            mMapMinZ = mapMinZ;
            mMapMaxX = mapMaxX;
            mMapMaxZ = mapMaxZ;
            mMaxDepth = maxDepth;
        }

        public void Build()
        {
            Debug.Assert(mRoot == null);
            mRoot = new TileQuadTreeNode(new Rect(mMapMinX, mMapMinZ, mMapMaxX - mMapMinX, mMapMaxZ - mMapMinZ), 0);

            int lods = mTileModelTemplate.lodCount;
            for (int k = 0; k < lods; ++k)
            {
                var children = mTileModelTemplate.GetChildPrefabTransform(k);

                for (int i = 0; i < children.Count; ++i)
                {
                    if (children[i].objectType == TileObjectType.NoneScaleDecorationObject ||
                        children[i].objectType == TileObjectType.ScaleDecorationObject)
                    {
                        var obj = new TileObjectInfo(k, i, children[i].localBoundsInPrefab);
                        AddObject(obj);
                    }
                }
            }
        }

        void AddObject(TileObjectInfo model)
        {
            AddRecursively(mRoot, model.bounds, model);
        }

        void AddRecursively(TileQuadTreeNode node, Rect objectBounds, TileObjectInfo obj)
        {
            if (node.depth == mMaxDepth)
            {
                AddToNode(node, obj);
            }
            else
            {
                Rect regionBounds;
                int regionIdx = GetRegionIndex(node.bounds, objectBounds, out regionBounds);
                if (regionIdx == -1)
                {
                    //物体不完全包含在任何子区域中,直接将物体加入到node中
                    AddToNode(node, obj);
                }
                else
                {
                    if (node.children[regionIdx] == null)
                    {
                        //分割node
                        node.children[regionIdx] = new TileQuadTreeNode(regionBounds, node.depth + 1);
                    }
                    AddRecursively(node.children[regionIdx], objectBounds, obj);
                }
            }
        }

        void AddToNode(TileQuadTreeNode node, TileObjectInfo obj)
        {
            node.objects.Add(obj);
        }

        int GetRegionIndex(Rect nodeBounds, Rect objectWorldBounds, out Rect regionBounds)
        {
            var min = nodeBounds.min;
            var max = nodeBounds.max;
            var size = max - min;
            float width = size.x * 0.5f;
            float height = size.y * 0.5f;

            Rect r0 = new Rect(min.x, min.y, width, height);
            if (Utils.FullContains(r0, objectWorldBounds))
            {
                regionBounds = r0;
                return 0;
            }

            Rect r1 = new Rect(min.x + width, min.y, width, height);
            if (Utils.FullContains(r1, objectWorldBounds))
            {
                regionBounds = r1;
                return 1;
            }

            Rect r2 = new Rect(min.x, min.y + height, width, height);
            if (Utils.FullContains(r2, objectWorldBounds))
            {
                regionBounds = r2;
                return 2;
            }

            Rect r3 = new Rect(min.x + width, min.y + height, width, height);
            if (Utils.FullContains(r3, objectWorldBounds))
            {
                regionBounds = r3;
                return 3;
            }

            regionBounds = new Rect();
            return -1;
        }

        public void GetIntersection(Vector3 center, float radius, List<TileObjectInfo> intersections)
        {
            Rect bounds = new Rect(new Vector2(center.x - radius, center.z - radius), new Vector2(radius * 2, radius * 2));
            GetIntersection(bounds, intersections);
        }

        public void GetIntersection(Rect worldBounds, List<TileObjectInfo> intersections)
        {
            //check if bounds is outside of root bounds
            var max = worldBounds.max;
            var min = worldBounds.min;
            if (min.x > mMapMaxX || mMapMinX > max.x ||
                min.y > mMapMaxZ || mMapMinZ > max.y)
            {
                GetIntersectionWithObjects(mRoot, worldBounds, intersections);
            }
            else
            {
                GetIntersection(mRoot, worldBounds, intersections);
            }
        }

        void GetIntersection(TileQuadTreeNode node, Rect bounds, List<TileObjectInfo> intersections)
        {
            if (node.IsIntersected(bounds))
            {
                for (int i = 0; i < 4; ++i)
                {
                    var childNode = node.children[i];
                    if (childNode != null)
                    {
                        GetIntersection(childNode, bounds, intersections);
                    }
                }

                GetIntersectionWithObjects(node, bounds, intersections);
            }
        }

        void GetIntersectionWithObjects(TileQuadTreeNode node, Rect bounds, List<TileObjectInfo> intersections)
        {
            foreach (var obj in node.objects)
            {
                if (obj.bounds.Overlaps(bounds))
                {
                    intersections.Add(obj);
                }
            }
        }

        float mMapMinX = 0;
        float mMapMinZ = 0;
        float mMapMaxX = 0;
        float mMapMaxZ = 0;
        int mMaxDepth;
        TileQuadTreeNode mRoot;
        ModelTemplate mTileModelTemplate;
    };
}