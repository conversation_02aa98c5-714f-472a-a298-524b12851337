﻿ 



 
 

//created by wzw at 2019/11/25

using UnityEngine;

namespace TFW.Map
{
    public abstract class PolygonObjectLayerView : MapObjectLayerView
    {
        public PolygonObjectLayerView(MapLayerData layerData, bool asyncLoading) : base(layerData, asyncLoading)
        {
            mShadowRoot = new GameObject("Shadow Root");
            Utils.HideGameObject(mShadowRoot);
        }

        public override void OnDestroy()
        {
            Object.DestroyImmediate(mShadowRoot);
            base.OnDestroy();
        }

        public void UpdateVertex(PrefabOutlineType type, int objectDataID, int vertexIndex, Vector3 pos)
        {
            var view = GetObjectView(objectDataID) as PolygonObjectView;
            if (view != null)
            {
                view.UpdateVertex(type, vertexIndex);
            }
        }

        public void UpdateView(int objectDataID)
        {
            var view = GetObjectView(objectDataID) as PolygonObjectView;
            if (view != null)
            {
                view.Update();
            }
        }

        public void InsertVertex(PrefabOutlineType type, PolygonObjectData data, int vertexIndex, Vector3 pos)
        {
            var view = GetObjectView(data.id) as PolygonObjectView;
            if (view != null)
            {
                view.InsertVertex(type, data, vertexIndex, pos);
            }
        }

        public void RemoveVertex(PrefabOutlineType type, PolygonObjectData data, int vertexIndex)
        {
            var view = GetObjectView(data.id) as PolygonObjectView;
            if (view != null)
            {
                view.RemoveVertex(type, data, vertexIndex);
            }
        }

        public void ShowOutline(PrefabOutlineType type)
        {
            foreach (var p in mViews)
            {
                var view = p.Value as PolygonObjectView;
                view.ShowOutline(type);
            }
        }

        public void HideOutline()
        {
            foreach (var p in mViews)
            {
                var view = p.Value as PolygonObjectView;
                view.HideOutline();
            }
        }

        public GameObject shadowRoot { get { return mShadowRoot; } }

        GameObject mShadowRoot;
    }
}