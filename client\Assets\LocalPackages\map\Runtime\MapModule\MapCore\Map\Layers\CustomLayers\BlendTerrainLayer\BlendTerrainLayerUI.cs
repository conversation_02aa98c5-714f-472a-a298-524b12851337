﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace TFW.Map
{
    [CustomEditor(typeof(BlendTerrainLayerLogic))]
    public partial class BlendTerrainLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as BlendTerrainLayerLogic;
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            prefabManager.selectPrefabEvent += OnSelectPrefab;

            mPrefab = prefabManager.selectedPrefab;

            mLogic.UpdateGizmoVisibilityState();

            mIndicator = new TerrainTileIndicator();
            if (!mLogic.paintOneTile)
            {
                for (int i = 0; i < 4; ++i)
                {
                    mIndicator.ShowPart(i, mLogic.drawMasks[i]);
                }
            }
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorTerrainPrefabManager;
                prefabManager.selectPrefabEvent -= OnSelectPrefab;
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
                HideIndicator();

                mIndicator.OnDestroy();
                mIndicator = null;

                mStampIndicator.OnDestroy();
                mStampIndicator = null;
                mStamp = null;
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;
            if (mLogic.operationType == TerrainOperationType.Create || mLogic.operationType == TerrainOperationType.Remove)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
                {
                    mLeftButtonDown = true;
                }

                if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
                {
                    mLeftButtonDown = false;
                    mLastSubCoord = new Vector3Int(-1, -1, -1);
                }

                if (currentEvent.type == EventType.KeyDown)
                {
                    if (mLogic.paintOneTile == false && currentEvent.shift == false && currentEvent.control == false && currentEvent.alt == false)
                    {
                        if (currentEvent.keyCode == KeyCode.Z)
                        {
                            mLogic.drawMasks[0] = !mLogic.drawMasks[0];
                            mIndicator.ShowPart(0, mLogic.drawMasks[0]);
                        }
                        if (currentEvent.keyCode == KeyCode.X)
                        {
                            mLogic.drawMasks[1] = !mLogic.drawMasks[1];
                            mIndicator.ShowPart(1, mLogic.drawMasks[1]);
                        }
                        if (currentEvent.keyCode == KeyCode.A)
                        {
                            mLogic.drawMasks[2] = !mLogic.drawMasks[2];
                            mIndicator.ShowPart(2, mLogic.drawMasks[2]);
                        }
                        if (currentEvent.keyCode == KeyCode.S)
                        {
                            mLogic.drawMasks[3] = !mLogic.drawMasks[3];
                            mIndicator.ShowPart(3, mLogic.drawMasks[3]);
                        }
                        if (currentEvent.keyCode == KeyCode.LeftBracket)
                        {
                            mLogic.brushSize = Mathf.Max(mLogic.brushSize - 1, 0);
                        }
                        if (currentEvent.keyCode == KeyCode.RightBracket)
                        {
                            mLogic.brushSize = mLogic.brushSize + 1;
                        }
                        Repaint();
                    }

                    float offset = 50;
                    if (currentEvent.keyCode == KeyCode.UpArrow)
                    {
                        MoveViewport(new Vector3(0, 0, offset));
                    }
                    else if (currentEvent.keyCode == KeyCode.DownArrow)
                    {
                        MoveViewport(new Vector3(0, 0, -offset));
                    }
                    else if (currentEvent.keyCode == KeyCode.LeftArrow)
                    {
                        MoveViewport(new Vector3(-offset, 0, 0));
                    }
                    else if (currentEvent.keyCode == KeyCode.RightArrow)
                    {
                        MoveViewport(new Vector3(offset, 0, 0));
                    }
                }

                var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                var curSubCoord = PickTiles(screenPos);

                UpdateIndicatorState();

                if (mLeftButtonDown)
                {
                    if (mLogic.paintOneTile == false)
                    {
                        if (curSubCoord != mLastSubCoord)
                        {
                            mLastSubCoord = curSubCoord;
                            SetTiles(mLogic.operationType == TerrainOperationType.Remove);
                        }
                    }
                    else
                    {
                        if (curSubCoord != mLastSubCoord)
                        {
                            mLastSubCoord = curSubCoord;
                            SetOneTile(mLogic.operationType == TerrainOperationType.Remove);
                        }
                    }
                }

                if (mLogic.brushSize > 1 && mLogic.paintOneTile == false)
                {
                    DrawBrush();
                }

                HandleUtility.AddDefaultControl(0);
            }
            else if (mLogic.operationType == TerrainOperationType.PaintHeight)
            {
                var layerData = mLogic.layer.layerData;
                if (layerData.tileWidth > 160 &&
                    layerData.tileHeight > 160)
                {
                    DrawHeightEditorSceneGUI(currentEvent);
                }
            }
            else if (mLogic.operationType == TerrainOperationType.Fill)
            {
                DrawFillToolSceneGUI(currentEvent);
            }
            else if (mLogic.operationType == TerrainOperationType.Stamp)
            {
                DrawStampToolSceneGUI(currentEvent);
            }
            else
            {
                HideIndicator();
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                DrawResizeOption();

                if (GUILayout.Button(new GUIContent("Clear Tiles", "清除所有tile")))
                {
                    mLogic.layer.ClearAllTiles();
                }

                if (GUILayout.Button(new GUIContent("Create Full Map Ground LOD0 Prefab", "将lod0的所有地块合成一个大的prefab,可以在制作装饰物prefab的时候作为地表参考")))
                {
                    var folderName = EditorUtility.SaveFolderPanel("Select output folder", "", "");
                    if (!string.IsNullOrEmpty(folderName))
                    {
                        CreateLOD0Prefab(folderName, true);
                    }
                }

                DrawTilingTile();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Load Tile Config", "加载一张H X W的tsv表,H和W分别是地图的高和宽,以格子数为单位")))
                {
                    string filePath = EditorUtility.OpenFilePanel("Select Config File", "", "txt,csv");
                    if (!string.IsNullOrEmpty(filePath))
                    {
                        var editorMapData = Map.currentMap.data as EditorMapData;
                        var prefabManager = editorMapData.editorTerrainPrefabManager;
                        LoadTileConfig(filePath, prefabManager.selectedGroup);
                    }
                }
                if (GUILayout.Button(new GUIContent("Save Tile Config", "导出当前prefab group中的tile和地表使用的tile之间对应关系的tsv")))
                {
                    string filePath = EditorUtility.SaveFilePanel("Select Config File", "", "ground_tile_config", "txt");
                    if (!string.IsNullOrEmpty(filePath))
                    {
                        var editorMapData = Map.currentMap.data as EditorMapData;
                        var prefabManager = editorMapData.editorTerrainPrefabManager;
                        SaveTileConfig(filePath, prefabManager.selectedGroup);
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                bool paintOneTile = EditorGUILayout.ToggleLeft(new GUIContent("Paint One Tile", "一次只绘制一个地块,如果使用拼接规则,一次是绘制4个tile.如果想修改某个tile,可以勾上这个选项"), mLogic.paintOneTile);
                if (paintOneTile != mLogic.paintOneTile)
                {
                    mLogic.paintOneTile = paintOneTile;
                    if (paintOneTile)
                    {
                        for (int i = 0; i < 4; ++i)
                        {
                            mIndicator.ShowPart(i, true);
                        }
                    }
                    else
                    {
                        for (int i = 0; i < 4; ++i)
                        {
                            mIndicator.ShowPart(i, mLogic.drawMasks[i]);
                        }
                    }
                }
                var layerData = mLogic.layerData;

                layerData.useGeneratedLOD = EditorGUILayout.ToggleLeft(new GUIContent("Use Generated LOD", "使用程序烘培生成的lod"), layerData.useGeneratedLOD);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                layerData.useDecorationObject = EditorGUILayout.ToggleLeft(new GUIContent("Use Decoration Object", "在绘制地表tile的时候同时生成一个配套这个类型地块的Front Layer装饰物prefab"), layerData.useDecorationObject);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                layerData.getGroundHeightInGame = EditorGUILayout.ToggleLeft(new GUIContent("Get Ground Height In Game", "是否在运行游戏时能计算地表高度数据,勾上会增大内存占用"), layerData.getGroundHeightInGame);
                layerData.generateMeshCollider = EditorGUILayout.ToggleLeft(new GUIContent("Generate Mesh Collider", "是否在地表高度不为0的tile上生成mesh collider"), layerData.generateMeshCollider);
                EditorGUILayout.EndHorizontal();
                layerData.optimizeMesh = EditorGUILayout.ToggleLeft(new GUIContent("Optimize Mesh", "是否导出时优化mesh,优化后边缘顶点法线可能会不准确"), layerData.optimizeMesh);

                DrawRenderBackgroundTextureOption();

                mShowCombineTileSetting = EditorGUILayout.Foldout(mShowCombineTileSetting, new GUIContent("Ground Tile Atlas Setting", "设置地块贴图打包配置, 如果使用小地块,可以将地块贴图打包成atlas"));
                if (mShowCombineTileSetting)
                {
                    EditorGUILayout.BeginVertical("GroupBox");
                    layerData.groundTileAtlasSetting = EditorGUILayout.ObjectField(new GUIContent("Ground Tile Atlas Setting", "设置地块贴图打包配置, 如果使用小地块,可以将地块贴图打包成atlas"), layerData.groundTileAtlasSetting, typeof(GroundTileAtlasSetting), false, null) as GroundTileAtlasSetting;
                    EditorGUILayout.EndVertical();
                }

                DrawMaskUI();

                DrawAtlasUI();

                if (mLogic.operationType == TerrainOperationType.Create)
                {
                    var operationType = (TerrainOperationType)EditorGUILayout.Popup(new GUIContent("Operation", "当前操作类型"), (int)mLogic.operationType, mOperationTexts);
                    if (operationType != mLogic.operationType)
                    {
                        SetOperation(operationType);
                    }

                    if (mLogic.paintOneTile == false)
                    {
                        mLogic.brushSize = EditorGUILayout.IntField("Brush Size", mLogic.brushSize);
                        mLogic.brushSize = Mathf.Max(1, mLogic.brushSize);
                        mLogic.randomPattern = EditorGUILayout.Toggle(new GUIContent("Random Pattern", "随机填充还是填充15号地块"), mLogic.randomPattern);
                    }
                    
                    var editorMapData = Map.currentMap.data as EditorMapData;
                    var prefabManager = editorMapData.editorTerrainPrefabManager;
                    prefabManager.Draw(PrefabGroupDisplayFlag.ShowColor | PrefabGroupDisplayFlag.ShowDecoration | PrefabGroupDisplayFlag.ShowRegularTiles | PrefabGroupDisplayFlag.ShowAddFolderWithLODConstrain);
                }
                else
                {
                    mLogic.operationType = (TerrainOperationType)EditorGUILayout.Popup(new GUIContent("Operation", "当前操作类型"), (int)mLogic.operationType, mOperationTexts);
                }

                if (mLogic.operationType == TerrainOperationType.PaintHeight)
                {
                    if (layerData.tileWidth > 160 &&
                        layerData.tileHeight > 160)
                    {
                        DrawHeightEditorInspector();
                    }
                    else
                    {
                        EditorGUILayout.LabelField("Can't paint height for small tiles");
                    }
                }
                else if (mLogic.operationType == TerrainOperationType.Fill)
                {
                    DrawFillInspector();
                }
                else if (mLogic.operationType == TerrainOperationType.Stamp)
                {
                    DrawStampInspector();
                }

                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, LODDisplayFlag.SpecialBufferSetting | LODDisplayFlag.TerrainLODTile, null, null);

                EditorGUILayout.TextArea("Accelerator Keys:\n 'Left Right Up Down' to move viewport.\n 'Z' 'X' 'A' 'S' to set brush mask.\n 箭头上下左右键可以移动viewport.\n Z X A S键设置brush的mask.\n R键Fix Edge Normal.\n B键切换Smooth Brush.");

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Tile Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Tile Height", layerData.tileHeight.ToString());
                EditorGUILayout.LabelField("X Tile Count", layerData.horizontalTileCount.ToString());
                EditorGUILayout.LabelField("Z Tile Count", layerData.verticalTileCount.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void MoveViewport(Vector3 offset)
        {
            SLGMakerEditor.instance.viewportControl.transform.position += offset;
        }

        void SetNewMask(int idx, bool mask)
        {
            if (mLogic.drawMasks[idx] != mask)
            {
                mLogic.drawMasks[idx] = mask;
                mIndicator.ShowPart(idx, mask);
            }
        }

        void CalculateTileDatas(out ActionSetBlendTerrainLayerTiles.TileData[] oldTileDatas, out ActionSetBlendTerrainLayerTiles.TileData[] newTileDatas)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
            oldTileDatas = new ActionSetBlendTerrainLayerTiles.TileData[4];
            newTileDatas = new ActionSetBlendTerrainLayerTiles.TileData[4];
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            int prefabGroupIndex = prefabManager.selectedGroupIndex;
            var group = prefabManager.GetGroupByIndex(prefabGroupIndex);

            var validFullIndices = Map.currentMap.data.terrainPrefabManager.GetPrefabValidFullTileIndicesByID(group.groupID);

            for (int i = 0; i < mPickedTiles.Length; ++i)
            {
                var tile = layer.GetTile(mPickedTiles[i].x, mPickedTiles[i].y);
                if (tile != null)
                {
                    oldTileDatas[i] = new ActionSetBlendTerrainLayerTiles.TileData(tile.index, tile.type, tile.subTypeIndex);
                }
                else
                {
                    oldTileDatas[i] = new ActionSetBlendTerrainLayerTiles.TileData(0, -1, 0);
                }
            }

            //try push tile
            List<int> excludedTileIndices = new List<int>();
            if (prefabGroupIndex >= 0)
            {
                if (group != null && group.isValid)
                {
                    //不能使用的tile index,避免4个tile中有相同的tile index
                    for (int i = 0; i < 4; ++i)
                    {
                        int tileIndex;
                        int subTypeIndex;
                        bool valid = layer.GetPushTileResult(mPickedTiles[i].x, mPickedTiles[i].y, mTileIndex[i], group.groupID, true, excludedTileIndices, out tileIndex, out subTypeIndex);
                        if (valid)
                        {
                            newTileDatas[i] = new ActionSetBlendTerrainLayerTiles.TileData(tileIndex, group.groupID, subTypeIndex);
                            excludedTileIndices.Add(tileIndex);
                        }
                        else
                        {
                            newTileDatas[i] = new ActionSetBlendTerrainLayerTiles.TileData(0, -1, 0);
                        }
                    }
                }
            }
        }

        void SetTiles(bool clearTile)
        {            
            if (mLogic.paintOneTile == false)
            {
                if (mLogic.brushSize == 1)
                {
                    if (clearTile)
                    {
                        var act = new ActionClearBlendTerrainLayerTiles(mLogic.layerID, mPickedTiles, mLogic.drawMasks);
                        ActionManager.instance.PushAction(act);
                    }
                    else
                    {
                        var editorMapData = Map.currentMap.data as EditorMapData;
                        var prefabManager = editorMapData.editorTerrainPrefabManager;
                        if (prefabManager.selectedGroupIndex >= 0)
                        {
                            var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                            if (group.isValid)
                            {
                                ActionSetBlendTerrainLayerTiles.TileData[] oldTileDatas;
                                ActionSetBlendTerrainLayerTiles.TileData[] newTileDatas;
                                CalculateTileDatas(out oldTileDatas, out newTileDatas);
                                var act = new ActionSetBlendTerrainLayerTiles(mLogic.layerID, prefabManager.selectedGroupIndex, mPickedTiles, oldTileDatas, newTileDatas, mLogic.drawMasks);

                                CompoundAction actions = new CompoundAction("Set Ground Tiles");
                                actions.Add(act);

                                var frontLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;

                                if (mLogic.layerData.useDecorationObject && frontLayer != null)
                                {
                                    for (int i = 0; i < 4; ++i)
                                    {
                                        int x = mPickedTiles[i].x;
                                        int y = mPickedTiles[i].y;

                                        if (x >= 0 && x < frontLayer.horizontalTileCount && y >= 0 && y < frontLayer.verticalTileCount)
                                        {
                                            var oldObject = frontLayer.GetObjectData(x, y);
                                            if (oldObject != null)
                                            {
                                                var removeAction = new ActionRemoveGridModel(oldObject.GetEntityID(), frontLayer.id);
                                                actions.Add(removeAction);
                                            }

                                            var tileGroup = editorMapData.editorTerrainPrefabManager.GetGroupByIndex(newTileDatas[i].tileType);
                                            if (tileGroup != null)
                                            {
                                                string decorationObjPath = tileGroup.GetRandomDecorationPrefabPath(newTileDatas[i].tileIndex);
                                                if (!string.IsNullOrEmpty(decorationObjPath))
                                                {
                                                    int dataID = Map.currentMap.nextCustomObjectID;
                                                    var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(dataID, decorationObjPath, true);
                                                    var pos = frontLayer.FromCoordinateToWorldPositionCenter(x, y);
                                                    var addAct = new ActionAddGridModel(frontLayer.id, dataID, modelTemplate.id, Quaternion.identity, pos, Vector3.one, frontLayer.useTextureModel);
                                                    actions.Add(addAct);
                                                }
                                            }
                                        }
                                    }
                                }

                                ActionManager.instance.PushAction(actions);
                            }
                        }
                    }
                }
                else
                {
                    SetMultipleTiles(clearTile, mLogic.randomPattern);
                }
            }
        }

        void DrawBrush()
        {
            int brushSize = mLogic.brushSize;
            int minX = mPickedTiles[0].x - brushSize + 2;
            int minY = mPickedTiles[0].y - brushSize + 2;
            int maxX = mPickedTiles[3].x + brushSize - 1;
            int maxY = mPickedTiles[3].y + brushSize - 1;

            var layer = mLogic.layer;
            float tileWidth = layer.tileWidth;
            float tileHeight = layer.tileHeight;
            Vector3 min = mLogic.layer.FromCoordinateToWorldPosition(minX, minY) - new Vector3(tileWidth * 0.5f, 0, tileHeight * 0.5f);
            Vector3 max = mLogic.layer.FromCoordinateToWorldPosition(maxX, maxY) + new Vector3(tileWidth * 0.5f, 0, tileHeight * 0.5f);
            Handles.DrawWireCube((min + max) * 0.5f, max - min);
        }

        void SetMultipleTiles(bool clearTiles, bool fillRandomPattern) {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            if (prefabManager.selectedGroupIndex >= 0)
            {
                var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                if (group.isValid)
                {
                    var layer = mLogic.layer;
                    var layerData = layer.layerData;
                    int brushSize = mLogic.brushSize;
                    int minX = mPickedTiles[0].x - brushSize + 2;
                    int minY = mPickedTiles[0].y - brushSize + 2;
                    int maxX = mPickedTiles[3].x + brushSize - 1;
                    int maxY = mPickedTiles[3].y + brushSize - 1;

                    var act = new ActionChangeBlendTerrainLayerTiles(layer.id, Mathf.Clamp(minX - 1, 0, layerData.horizontalTileCount - 1), Mathf.Clamp(minY - 1, 0, layerData.verticalTileCount - 1), Mathf.Clamp(maxX, 0, layerData.horizontalTileCount - 1), Mathf.Clamp(maxY, 0, layerData.verticalTileCount - 1));
                    act.Begin();

                    for (int y = minY; y <= maxY; ++y)
                    {
                        for (int x = minX; x <= maxX; ++x)
                        {
                            var pos = layerData.FromCoordinateToWorldPosition(x, y);

                            if (fillRandomPattern && !clearTiles)
                            {
                                if (Random.value < 0.5f)
                                {
                                    layer.PushTile(pos, group.groupID);
                                }
                            }
                            else
                            {
                                if (clearTiles)
                                {
                                    layer.PopTile(pos);
                                }
                                else
                                {
                                    layer.PushTile(pos, group.groupID);
                                }
                            }

                        }
                    }

                    if (fillRandomPattern && !clearTiles)
                    {
                        for (int y = minY; y <= maxY; ++y)
                        {
                            for (int x = minX; x <= maxX; ++x)
                            {
                                var tile = layer.GetTile(x, y);
                                if (tile == null)
                                {
                                    var pos = layerData.FromCoordinateToWorldPosition(x, y);
                                    layer.PushTile(pos, group.groupID);
                                }
                            }
                        }
                    }

                    act.End();
                    ActionManager.instance.PushAction(act, true, false);
                }
            }
        }

        void DrawMaskUI()
        {
            mLogic.showMaskUI = EditorGUILayout.Foldout(mLogic.showMaskUI, new GUIContent("Tile Mask Setting", "当不勾选paint one tile选项时,默认一次会绘制4个tile,这里可以单独控制4个tile中哪些能被绘制"));
            if (mLogic.showMaskUI)
            {
                EditorGUILayout.BeginHorizontal();
                bool newMask = EditorGUILayout.Toggle(new GUIContent("4", "能否绘制4号地块"), mLogic.drawMasks[2]);
                SetNewMask(2, newMask);
                newMask = EditorGUILayout.Toggle(new GUIContent("8", "能否绘制8号地块"), mLogic.drawMasks[3]);
                SetNewMask(3, newMask);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                newMask = EditorGUILayout.Toggle(new GUIContent("1", "能否绘制1号地块"), mLogic.drawMasks[0]);
                SetNewMask(0, newMask);
                newMask = EditorGUILayout.Toggle(new GUIContent("2", "能否绘制2号地块"), mLogic.drawMasks[1]);
                SetNewMask(1, newMask);
                EditorGUILayout.EndHorizontal();
            }
        }

        void DrawAtlasUI()
        {
#if false
            mLogic.showAtlasUI = EditorGUILayout.Foldout(mLogic.showAtlasUI, "Texture Atlas Setting");
            if (mLogic.showAtlasUI)
            {
                EditorGUILayout.BeginHorizontal();
                var layerData = mLogic.layerData;
                EditorGUILayout.BeginVertical("GroupBox");

                layerData.groundTileAtlasSetting = EditorGUILayout.ObjectField("Ground Tile Atlas Setting", layerData.groundTileAtlasSetting, typeof(GroundTileAtlasSetting), false, null) as GroundTileAtlasSetting;
                layerData.combinedTileMaterial = EditorGUILayout.ObjectField("Atlas Material", layerData.combinedTileMaterial, typeof(Material), false, null) as Material;
                if (GUILayout.Button("Create Ground Tile Atlas"))
                {
                    layerData.CreateGroundTileTextureAtlas();
                }
                EditorGUILayout.EndVertical();

                EditorGUILayout.EndHorizontal();
            }
#endif
        }

        void DrawTilingTile()
        {
            var layerData = mLogic.layerData;

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button(new GUIContent("Tiling Prefab", "用15号地块或其变体平铺整个地图")))
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
                TilePrefab(layer);
            }
            if (GUILayout.Button(new GUIContent("Tiling Prefab With Random Pattern", "用随机的地块填充整个地图")))
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
                TilePrefabWithRandomPattern(layer);
            }
            EditorGUILayout.EndHorizontal();
        }

        //创建这个tile的多边形
        List<Vector3> CreateTilePolygon(int x, int y)
        {
            float minX = mLogic.layerData.tileWidth * x;
            float maxX = mLogic.layerData.tileWidth * (x + 1);
            float minZ = mLogic.layerData.tileHeight * y;
            float maxZ = mLogic.layerData.tileHeight * (y + 1);

            var polygon = new List<Vector3>()
            {
            new Vector3(minX, 0, minZ),
            new Vector3(maxX, 0, minZ),
            new Vector3(maxX, 0, maxZ),
            new Vector3(minX, 0, maxZ),
            };

            return polygon;
        }

        List<Vector2Int> GetValidTiles()
        {
            List<Vector2Int> tiles = new List<Vector2Int>();

            int rows = mLogic.layerData.verticalTileCount;
            int cols = mLogic.layerData.horizontalTileCount;

            bool isCircleMap = Map.currentMap.data.isCircleMap;
            var center = new Vector3(Map.currentMap.mapWidth * 0.5f, 0, Map.currentMap.mapHeight * 0.5f);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    if (isCircleMap)
                    {
                        var tilePolygon = CreateTilePolygon(j, i);

                        if (!Utils.IsConvexHullFullOutsideOfCircle(tilePolygon, center, Map.currentMap.mapWidth * 0.5f))
                        {
                            tiles.Add(new Vector2Int(j, i));
                        }
                    }
                    else
                    {
                        tiles.Add(new Vector2Int(j, i));
                    }
                }
            }

            return tiles;
        }

        bool CheckViewport(BlendTerrainLayer layer)
        {
            var viewport = Map.currentMap.viewport;
            if (viewport.width >= 1000 && viewport.height >= 1000 && layer.horizontalTileCount > 100 && layer.verticalTileCount > 100)
            {
                if (!EditorUtility.DisplayDialog("Warning", "Viewport is too large, this operation may cost long time! continue?", "Yes", "No"))
                {
                    return false;
                }
            }
            return true;
        }

        void TilePrefab(BlendTerrainLayer layer)
        {
            if (!CheckViewport(layer))
            {
                return;
            }

            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            if (prefabManager.selectedGroupIndex >= 0)
            {
                var validTiles = GetValidTiles();
                if (validTiles.Count > 0)
                {
                    var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                    //获取所有有效的full prefab
                    var validPrefabIndices = group.GetValidPrefabIndices();
                    if (validPrefabIndices.Count > 0)
                    {
                        var act = new ActionTilingBlendTerrainLayer(layer.id, prefabManager.selectedGroupIndex, validTiles, validPrefabIndices);
                        ActionManager.instance.PushAction(act, false, true);
                        ActionManager.instance.Clear();
                        Repaint();
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Can't tile prefab!", "OK");
                    }
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Can't tile prefab!", "OK");
                }
            }
        }

        void TilePrefabWithRandomPattern(BlendTerrainLayer layer)
        {
            if (!CheckViewport(layer))
            {
                return;
            }

            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            if (prefabManager.selectedGroupIndex >= 0)
            {
                var validTiles = GetValidTiles();
                if (validTiles.Count > 0)
                {
                    var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                    if (group.isValid)
                    {
                        int rows = layer.verticalTileCount;
                        int cols = layer.horizontalTileCount;
                        for (int i = 0; i <= rows; ++i)
                        {
                            for (int j = 0; j <= cols; ++j)
                            {
                                if (Random.value < 0.5f)
                                {
                                    var pos = layer.layerData.FromCoordinateToWorldPosition(j, i);
                                    layer.PushTile(pos, group.groupID);
                                }
                            }
                        }

                        //fill empty tiles
                        for (int i = 0; i < rows; ++i)
                        {
                            for (int j = 0; j < cols; ++j)
                            {
                                var tile = layer.GetTile(j, i);
                                if (tile == null)
                                {
                                    var pos = layer.layerData.FromCoordinateToWorldPosition(j, i);
                                    layer.PushTile(pos, group.groupID);
                                }
                            }
                        }

                        ActionManager.instance.Clear();
                    }
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Can't tile prefab!", "OK");
                }
            }
        }

        void ClearAllTiles(BlendTerrainLayer layer)
        {
            layer.ClearAllTiles();
            ActionManager.instance.Clear();
        }

        void ShowIndicator()
        {
            mIndicator.SetActive(true);
            mIndicator.SetColor(new Color(0, 1, 0, 0.5f));
        }

        void UpdateIndicatorState()
        {
            Vector3 indicatorPos = Vector3.zero;
            if (mLogic.paintOneTile)
            {
                indicatorPos = mLogic.layerData.FromCoordinateToWorldPositionCenter(mPickedTiles[0].x, mPickedTiles[0].y);
            }
            else
            {
                for (int i = 0; i < 4; ++i)
                {
                    var pos = mLogic.layerData.FromCoordinateToWorldPositionCenter(mPickedTiles[i].x, mPickedTiles[i].y);
                    indicatorPos += pos;
                }
                indicatorPos /= 4;
            }

            UpdateIndicator(indicatorPos);
        }

        void UpdateIndicator(Vector3 pos)
        {
            if (mLogic.operationType == TerrainOperationType.Create || mLogic.operationType == TerrainOperationType.Remove)
            {
                if (mLogic.brushSize == 1)
                {
                    ShowIndicator();
                }
                else
                {
                    HideIndicator();
                }
            }
            else
            {
                HideIndicator();
            }

            pos.y += 0.5f;
            mIndicator.SetPosition(pos);
            mIndicator.SetScale(mLogic.layerData.tileWidth);
        }

        void HideIndicator()
        {
            mIndicator.SetActive(false);
        }

        void SetPrefab(GameObject prefab)
        {
            mPrefab = prefab;
        }

        void OnSelectPrefab(GameObject prefab)
        {
            mPrefab = prefab;
        }

        Vector3Int PickTiles(Vector2 screenPos)
        {
            Vector3Int curSubCoord = Vector3Int.zero;
            var layerData = mLogic.layerData;
            if (mLogic.paintOneTile == false)
            {
                var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);
                var coord = layerData.FromWorldPositionToCoordinate(worldPos);
                curSubCoord.x = coord.x;
                curSubCoord.y = coord.y;
                var tileStartPos = layerData.FromCoordinateToWorldPosition(coord.x, coord.y);
                var rx = (worldPos.x - tileStartPos.x) / layerData.tileWidth;
                var rz = (worldPos.z - tileStartPos.z) / layerData.tileHeight;
                if (rx <= 0.5)
                {
                    if (rz <= 0.5)
                    {
                        mPickedTiles[0] = new Vector2Int(coord.x - 1, coord.y - 1);
                        mPickedTiles[1] = new Vector2Int(coord.x, coord.y - 1);
                        mPickedTiles[2] = new Vector2Int(coord.x - 1, coord.y);
                        mPickedTiles[3] = new Vector2Int(coord.x, coord.y);
                        curSubCoord.z = 0;
                    }
                    else
                    {
                        mPickedTiles[0] = new Vector2Int(coord.x - 1, coord.y);
                        mPickedTiles[1] = new Vector2Int(coord.x, coord.y);
                        mPickedTiles[2] = new Vector2Int(coord.x - 1, coord.y + 1);
                        mPickedTiles[3] = new Vector2Int(coord.x, coord.y + 1);
                        curSubCoord.z = 3;
                    }
                }
                else
                {
                    if (rz <= 0.5)
                    {
                        mPickedTiles[0] = new Vector2Int(coord.x, coord.y - 1);
                        mPickedTiles[1] = new Vector2Int(coord.x + 1, coord.y - 1);
                        mPickedTiles[2] = new Vector2Int(coord.x, coord.y);
                        mPickedTiles[3] = new Vector2Int(coord.x + 1, coord.y);
                        curSubCoord.z = 2;
                    }
                    else
                    {
                        mPickedTiles[0] = new Vector2Int(coord.x, coord.y);
                        mPickedTiles[1] = new Vector2Int(coord.x + 1, coord.y);
                        mPickedTiles[2] = new Vector2Int(coord.x, coord.y + 1);
                        mPickedTiles[3] = new Vector2Int(coord.x + 1, coord.y + 1);
                        curSubCoord.z = 4;
                    }
                }
            }
            else
            {
                var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);
                var coord = layerData.FromWorldPositionToCoordinate(worldPos);
                curSubCoord = new Vector3Int(coord.x, 0, coord.y);
                mPickedTiles[0] = coord;
            }

            return curSubCoord;
        }

        bool IsValidTile(int index)
        {
            var layerData = mLogic.layerData;
            var tile = mPickedTiles[index];
            return tile.x >= 0 && tile.x < layerData.horizontalTileCount &&
                tile.y >= 0 && tile.y < layerData.verticalTileCount;
        }

        void SetOperation(TerrainOperationType operationType)
        {
            mLogic.operationType = operationType;
            //mLastCoord = new Vector2Int(-1, -1);
        }

        void DrawResizeOption()
        {
            if (MapModule.enableResizeMap)
            {
                if (GUILayout.Button(new GUIContent("Move And Resize Layer", "修改layer大小")))
                {
                    var dlg = EditorUtils.CreateInputDialog("Move And Resize Layer");

                    var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Size", "", "8100"),
                    new InputDialog.EnumItem("Alignment", "", ResizeAlignment.Move),
                };
                    dlg.Show(items, OnClickMoveAndResizeLayer);
                }
            }
        }

        void DrawRenderBackgroundTextureOption()
        {
            mLogic.showRenderTextureOption = EditorGUILayout.Foldout(mLogic.showRenderTextureOption, new GUIContent("Render Ground Textures Setting", "将整个地表渲染成若干贴图,作为编辑器的背景参考物,主要用地图tile很小的情况下,因为tile太多,无法一次渲染所有地表的tile,所以将这些tile渲染成大块的贴图."));
            if (mLogic.showRenderTextureOption)
            {
                EditorGUILayout.BeginVertical("GroupBox");
                mLogic.layerData.combinedTexturePropertyName = EditorGUILayout.TextField(new GUIContent("Texture Property Name", "地表烘培shader里main texture的名字,使用生成lod的烘培shader"), mLogic.layerData.combinedTexturePropertyName);
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Create All Textures", "重新烘培所有的贴图")))
                {
                    var dlg = EditorUtils.CreateInputDialog("Set Render Texture Count");

                    var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Count", "", "8"),
                    };
                    dlg.Show(items, OnClickCreateRenderTextures);
                }

                if (GUILayout.Button(new GUIContent("Update Dirty Textures", "只重新烘培修改过的区域内的贴图")))
                {
                    var shader = MapModuleResourceMgr.LoadResource<Shader>(MapModule.defaultGroundBakingShader);
                    bool suc = mLogic.layer.UpdateRenderTextures(mLogic.layerData.combinedTexturePropertyName);
                    if (!suc)
                    {
                        EditorUtility.DisplayDialog("Error", "Can't update texture, create textures first!", "OK");
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (GUILayout.Button("Clear Render Textures"))
                {
                    mLogic.layer.ClearRenderTextures(SLGMakerEditor.instance.projectFolder);
                }

                EditorGUILayout.EndVertical();
            }
        }

        bool OnClickCreateRenderTextures(List<InputDialog.Item> parameters)
        {
            int count;
            Utils.ParseInt((parameters[0] as InputDialog.StringItem).text, out count);

            bool valid = BlendTerrainLayerRenderTextures.CheckValidation(mLogic.layer.layerData as BlendTerrainLayerData, count);
            if (valid)
            {
                if (EditorUtility.DisplayDialog("Warning", "This operation may take long time! continue?", "Yes", "No"))
                {
                    var shader = MapModuleResourceMgr.LoadResource<Shader>(MapModule.defaultGroundBakingShader);
                    mLogic.layer.ClearRenderTextures(SLGMakerEditor.instance.projectFolder);
                    mLogic.layer.CreateRenderTextures(count, 2048, shader, mLogic.layerData.combinedTexturePropertyName);
                    return true;
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Invalid Count", "OK");
            }
            return false;
        }

        bool OnClickChangeSize(List<InputDialog.Item> parameters)
        {
            string newSizeStr = (parameters[0] as InputDialog.StringItem).text;
            int newSize;
            Utils.ParseInt(newSizeStr, out newSize);
            if (newSize > 0)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
                layer.Resize(newSize, newSize, true);
                mLogic.RecreateGrid();
                return true;
            }

            return false;
        }

        bool OnClickMoveAndResizeLayer(List<InputDialog.Item> parameters)
        {
            string newSizeStr = (parameters[0] as InputDialog.StringItem).text;
            System.Enum alignment = (parameters[1] as InputDialog.EnumItem).value;
            int newSize;
            Utils.ParseInt(newSizeStr, out newSize);
            if (newSize > 0)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
                layer.MoveAndResize(newSize, newSize, (ResizeAlignment)alignment);
                mLogic.RecreateGrid();
                return true;
            }

            return false;
        }

        void CreateLOD0Prefab(string outputFolder, bool middleAlignment)
        {
            outputFolder = Utils.ConvertToUnityAssetsPath(outputFolder);
            if (!string.IsNullOrEmpty(outputFolder))
            {
                var layerData = mLogic.layerData;
                int h = layerData.horizontalTileCount;
                int v = layerData.verticalTileCount;
                GameObject rootObj = new GameObject("ground_lod0");
                Vector3 alignmentOffset = Vector3.zero;
                if (middleAlignment)
                {
                    alignmentOffset = new Vector3(Map.currentMap.mapWidth, 0, Map.currentMap.mapHeight) * 0.5f;
                }
                for (int i = 0; i < v; ++i)
                {
                    for (int j = 0; j < h; ++j)
                    {
                        var tileData = layerData.GetTile(j, i);
                        if (tileData != null)
                        {
                            var modelTemplate = tileData.GetModelTemplate();
                            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(modelTemplate.GetLODPrefabPath(0));
                            var obj = GameObject.Instantiate<GameObject>(prefab);
                            obj.transform.SetParent(rootObj.transform);
                            obj.transform.position = tileData.GetPosition() - alignmentOffset;
                        }
                    }
                }

                string prefabPath = outputFolder + "/" + rootObj.name + ".prefab";
                PrefabUtility.SaveAsPrefabAsset(rootObj, prefabPath);

                GameObject.DestroyImmediate(rootObj);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", $"Invalid folder {outputFolder}", "OK");
            }
        }

        void SaveTileConfig(string filePath, PrefabGroup group)
        {
            if (group == null)
            {
                return;
            }
            var layerData = mLogic.layerData;
            int width = layerData.horizontalTileCount;
            int height = layerData.verticalTileCount;

            int[] indices = new int[width * height];
            for (int i = 0; i < height; ++i)
            {
                for (int j = 0; j < width; ++j)
                {
                    var tile = layerData.GetTile(j, i);
                    int idx = 0;
                    if (tile != null)
                    {
                        idx = group.GetPrefabIndex(tile.GetAssetPath(0));
                    }
                    idx = Mathf.Max(0, idx);

                    //上下颠倒,更符合配置表的视角
                    var tileIdx = (height - i - 1) * width + j;
                    indices[tileIdx] = idx;
                }
            }

            StringBuilder builder = new StringBuilder();
            int k = 0;
            for (int i = 0; i < height; ++i)
            {
                for (int j = 0; j < width; ++j)
                {
                    builder.Append(indices[k]);
                    builder.Append(",");
                    ++k;
                }
                builder.Append("\r\n");
            }

            File.WriteAllText(filePath, builder.ToString());
        }

        void LoadTileConfig(string filePath, PrefabGroup group)
        {
            if (group == null)
            {
                return;
            }

            var text = File.ReadAllText(filePath);
            if (!string.IsNullOrEmpty(text))
            {
                text = text.Replace("\r\n", ",");
                var tokens = text.Split(new string[] { "," }, System.StringSplitOptions.RemoveEmptyEntries);
                int width = mLogic.layerData.horizontalTileCount;
                int height = mLogic.layerData.verticalTileCount;
                int n = width * height;
                if (tokens.Length != n)
                {
                    EditorUtility.DisplayDialog("Error", "Invalid config file, size not match!", "OK");
                    return;
                }
                var editorData = Map.currentMap.data as EditorMapData;

                var layerData = mLogic.layerData;
                var layer = mLogic.layer;
                bool error = false;

                var act = new ActionChangeBlendTerrainLayerTiles(layerData.id, 0, 0, width - 1, height - 1);
                act.Begin();

                for (int i = 0; i < height; ++i)
                {
                    for (int j = 0; j < width; ++j)
                    {
                        //上下颠倒,更符合配置表的视角
                        var idx = (height - i - 1) * width + j;
                        int prefabIndexInGroup;
                        bool suc = int.TryParse(tokens[idx], out prefabIndexInGroup);
                        if (suc)
                        {
                            if (prefabIndexInGroup >= 0 && prefabIndexInGroup < group.count)
                            {
                                string prefabPath = group.GetPrefabPath(prefabIndexInGroup);
                                if (!string.IsNullOrEmpty(prefabPath))
                                {
                                    layer.SetTile(j, i, prefabIndexInGroup, group.groupID, 0);
                                }
                            }
                            else
                            {
                                EditorUtility.DisplayDialog("Error", string.Format("invalid index [{0}] is not found", prefabIndexInGroup), "OK");
                                error = true;
                                break;
                            }
                        }
                    }

                    if (error)
                    {
                        break;
                    }
                }

                if (!error)
                {
                    act.End();
                    ActionManager.instance.PushAction(act, true, false);
                }
            }   
        }

        GameObject mPrefab;
        TerrainTileIndicator mIndicator;

        bool mLeftButtonDown = false;
        bool mShowCombineTileSetting = false;

        /* 3 4
         * 1 2
         */
        //当前鼠标拾取的4个tile
        Vector2Int[] mPickedTiles = new Vector2Int[4];
        static int[] mTileIndex = new int[] { 1, 2, 4, 8 };
        BlendTerrainLayerLogic mLogic;
        Vector3Int mLastSubCoord;
        string[] mOperationTexts = new string[]
        {
            "Create",
            "Remove",
            "Select",
            "Fill",
            "Stamp",
            "PaintHeight",
        };
    }
}

#endif