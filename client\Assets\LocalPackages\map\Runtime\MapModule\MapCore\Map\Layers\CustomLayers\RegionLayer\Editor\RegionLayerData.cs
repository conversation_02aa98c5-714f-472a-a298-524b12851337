﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

//created by wzw at 2020/2/17

namespace TFW.Map
{
    public class RegionLayerData : PolygonCollisionLayerData
    {
        public RegionLayerData(MapLayerDataHeader header, Map map, Material defaultRegionMaterial, float borderMinX, float borderMinZ, float borderMaxX, float borderMaxZ, Vector3[] borderMeshVertices, int[] borderMeshIndices, Material borderMaterial, bool generateBorderLineMesh, Material borderLineMaterial, float borderLineWidth, bool showBorderLineMesh, bool showRegionMesh) : base(header, null, map)
        {
            mBorderMinX = borderMinX;
            mBorderMinZ = borderMinZ;
            mBorderMaxX = borderMaxX;
            mBorderMaxZ = borderMaxZ;
            mBorderMaterial = borderMaterial;
            mBorderMeshVertices = borderMeshVertices;
            mBorderMeshIndices = borderMeshIndices;
            this.borderLineMaterial = borderLineMaterial;
            this.borderLineWidth = borderLineWidth;
            this.generateBorderLine = generateBorderLineMesh;
            this.showBorderLineMesh = showBorderLineMesh;
            this.showRegionMesh = showRegionMesh;
            this.defaultRegionMaterial = defaultRegionMaterial;
        }

        public List<RegionData> GetRegionsWithNumber(int number)
        {
            List<RegionData> regions = new List<RegionData>();
            foreach (var obj in mObjects)
            {
                var regionData = obj.Value as RegionData;
                if (regionData.number == number)
                {
                    regions.Add(regionData);
                }
            }
            return regions;
        }

        public List<RegionData> GetRegionsWithMesh()
        {
            List<RegionData> regions = new List<RegionData>();
            foreach (var obj in mObjects)
            {
                var regionData = obj.Value as RegionData;
                if (regionData.meshVertices != null && regionData.meshVertices.Length > 0)
                {
                    regions.Add(regionData);
                }
            }
            return regions;
        }

        public void SetBorderMesh(Vector3[] vertices, int[] indices)
        {
            mBorderMeshVertices = vertices;
            mBorderMeshIndices = indices;
        }

        public Material defaultRegionMaterial { get; set; }
        public Material borderMaterial { get { return mBorderMaterial; } set { mBorderMaterial = value; } }
        public Vector3[] borderMeshVertices { get { return mBorderMeshVertices; } }
        public int[] borderMeshIndices { get { return mBorderMeshIndices; } }
        public float borderMinX { get { return mBorderMinX; } set { mBorderMinX = value; } }
        public float borderMinZ { get { return mBorderMinZ; } set { mBorderMinZ = value; } }
        public float borderMaxX { get { return mBorderMaxX; } set { mBorderMaxX = value; } }
        public float borderMaxZ { get { return mBorderMaxZ; } set { mBorderMaxZ = value; } }
        public bool generateBorderLine { get; set; } = true;
        public Material borderLineMaterial { set; get; }
        public float borderLineWidth { get; set; } = 1.0f;
        public bool showBorderLineMesh { get; set; } = true;
        public bool showRegionMesh { get; set; } = true;

        Vector3[] mBorderMeshVertices;
        int[] mBorderMeshIndices;
        Material mBorderMaterial;
        public float mBorderMinX;
        public float mBorderMinZ;
        public float mBorderMaxX;
        public float mBorderMaxZ;
    }
}

#endif