﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public class NavMeshRegionDetectorEditor
    {
        public class Item
        {
            public GameObject gameObject;
            public int type;
        }

        public void OnDestroy()
        {
            for (int i = 0; i < mItems.Length; ++i)
            {
                GameObject.DestroyImmediate(mItems[i].gameObject);
            }
        }

        public void Load(config.Detector[] detectors)
        {
            SetRegionCount(detectors.Length);
            for (int i = 0; i < detectors.Length; ++i)
            {
                mItems[i].gameObject.transform.position = detectors[i].position;
                mItems[i].type = detectors[i].type;
            }
        }

        public void DrawGUI(float radius)
        {
            CheckGameObjects();

            int n = EditorGUILayout.IntField(new GUIContent("Closed Region Count", "封闭区域的数量,将各个探测器丢到空地上,可以生成该区域的mesh"), mItems.Length);
            n = Mathf.Max(0, n);
            if (n != mItems.Length)
            {
                SetRegionCount(n);
            }

            for (int i = 0; i < mItems.Length; ++i)
            {
                mItems[i].type = EditorGUILayout.IntField(new GUIContent($"Region {i} ID", "封闭区域id"), mItems[i].type);

                Handles.DrawWireDisc(mItems[i].gameObject.transform.position, Vector3.up, radius);
            }
        }

        void SetRegionCount(int n)
        {
            Item[] newItems = new Item[n];
            int k = Mathf.Min(n, mItems.Length);
            for (int i = 0; i < k; ++i)
            {
                newItems[i] = mItems[i];
            }

            if (mItems.Length > n)
            {
                for (int i = k; i < mItems.Length; ++i)
                {
                    GameObject.DestroyImmediate(mItems[i].gameObject);
                }
            }
            else
            {
                for (int i = k; i < n; ++i)
                {
                    newItems[i] = new Item();
                    newItems[i].gameObject = CreateGameObject(i);
                }
            }

            mItems = newItems;
        }

#if false
        public void Save()
        {
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.NavMeshRegionDetectorVersion);
            int n = mItems.Length;
            writer.Write(n);

            for (int i = 0; i < n; ++i)
            {
                Utils.WriteVector3(writer, mItems[i].gameObject.transform.position);
                writer.Write(mItems[i].type);
            }

            var data = stream.ToArray();

            var filePath = SLGMakerEditor.instance.projectFolder + "/region_detector.config";
            File.WriteAllBytes(filePath, data);
            writer.Close();
        }

        public void Load()
        {
            var filePath = SLGMakerEditor.instance.projectFolder + "/region_detector.config";
            if (!File.Exists(filePath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(filePath);
            if (bytes != null)
            {
                MemoryStream stream = new MemoryStream(bytes);
                BinaryReader reader = new BinaryReader(stream);

                int version = reader.ReadInt32();

                int itemsCount = reader.ReadInt32();
                mItems = new Item[itemsCount];
                for (int i = 0; i < itemsCount; ++i)
                {
                    mItems[i] = new Item();
                    mItems[i].gameObject = CreateGameObject(i);
                    mItems[i].gameObject.transform.position = Utils.ReadVector3(reader);
                    mItems[i].type = reader.ReadInt32();
                }

                reader.Close();
            }
        }

        public void Export()
        {
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.GateRegionVersion);

            var regions = CalculateRegions();
            int n = regions.Count;
            writer.Write(n);

            for (int i = 0; i < n; ++i)
            {
                writer.Write(regions[i].type);
                var bounds = Utils.CalculateRect(regions[i].meshVertices);
                Utils.WriteRect(writer, bounds);
                Utils.WriteVector3Array(writer, regions[i].meshVertices);
                Utils.WriteIntArray(writer, regions[i].meshIndices);
            }

            var data = stream.ToArray();
            File.WriteAllBytes(MapCoreDef.MAP_NAV_MESH_REGION_DATA_PATH, data);
            writer.Close();
        }
#endif

        public List<ClosedAreaDetector.Area> CalculateRegions(Vector3[] meshVertices, int[] meshIndices)
        {
            if (meshVertices != null && meshVertices.Length > 0)
            {
                var indicators = new List<AreaIndicator>(mItems.Length);
                for (int i = 0; i < mItems.Length; ++i)
                {
                    if (mItems[i].gameObject != null)
                    {
                        indicators.Add(new AreaIndicator() { position = mItems[i].gameObject.transform.position, type = mItems[i].type });
                    }
                }

                return ClosedAreaDetector.Process(indicators, meshVertices, meshIndices);
            }

            return new List<ClosedAreaDetector.Area>();
        }

        void CheckGameObjects()
        {
            for (int i = 0; i < mItems.Length; ++i)
            {
                if (mItems[i].gameObject == null)
                {
                    mItems[i].gameObject = CreateGameObject(i);
                }
            }
        }

        GameObject CreateGameObject(int i)
        {
            var gameObject = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            gameObject.name = $"region detector {i}";
            var collisionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            gameObject.transform.parent = collisionLayer.layerView.root.transform;
            return gameObject;
        }

        public Item[] items { get { return mItems; } }

        Item[] mItems = new Item[0];
    }
}

#endif