﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public class RuntimeRegionView : ModelView
    {
        public RuntimeRegionView(IMapObjectData data, MapLayerView layerView)
            : base(data, layerView)
        {
        }

        public void SetColor(Color color)
        {
            mModel.gameObject.GetComponent<MeshRenderer>().sharedMaterial.color = color;
        }

        public void ShowRegionMesh(bool show)
        {
            if (mModel != null)
            {
                var model = mModel as RuntimeRegionModel;
                model.ShowRegionMesh(show);
            }
        }

        public void ShowBorderLineMesh(bool show)
        {
            if (mModel != null)
            {
                var model = mModel as RuntimeRegionModel;
                model.ShowBorderLineMesh(show);
            }
        }

        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            Debug.Assert(mModel == null);
            Debug.Assert(data != null);

            ModelBase model = null;
            bool active = data.IsObjActive();
            if (active)
            {
                model = RuntimeRegionModel.Require(data as RuntimeRegionData);
                model.transform.SetParent(mLayerView.root.transform, false);
            }
            else
            {
                model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
                OnModelPlaceholderReady(model);
            }
            return model;
        }
    }
}
