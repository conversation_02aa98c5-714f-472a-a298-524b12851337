﻿#if !UNITY_WEBGL
using Net.Common;
using System;
using System.Collections.Concurrent;
using System.Net.Sockets;
using UnityEngine;

namespace Net.Tcp.Common
{
    class IOCPConn
    {
        public static void Start(IIOCP iOCP)
        {
            var e = Pop();
            e.UserToken = iOCP;
            iOCP.IOCPInitialize(e);
            if (!(iOCP.Socket.ReceiveAsync(e)))
            {
                ProcessReceive(e);
            }
        }
        static void ProcessReceive(SocketAsyncEventArgs e)
        {
            try
            {
                if (e != null && e.SocketError == SocketError.Success)
                {
                    // 当socket状态为成功时，不处理收到包长度为0的情况，继续执行。
                    if (e.BytesTransferred > 0)
                    {
                        var token = e.UserToken as IIOCP;
                        if (token != null && !token.IOCPReceived(e))
                        {
                            return;
                        }
                        ProcessSend(e);
                    }
                    else
                    {
                        //e.BytesTransferred == 0，但当前SocketError.Success，所以先不close试试
                        Close(e);
                    }
                }
                else
                {
                    Close(e);
                }
            }
            catch (System.Exception ex)
            {
                Close(e);
                Debug.LogError($" socket ex {ex.ToString()}");
            }
        }
        static void ProcessSend(SocketAsyncEventArgs e)
        {
            if (e.SocketError == SocketError.Success)
            {
                var token = e.UserToken as IIOCP;

                if (token != null && token.Socket != null
                    && !(token.Socket.ReceiveAsync(e)))
                {
                    ProcessReceive(e);
                }
            }
            else
            {
                Close(e);
            }
        }
        public static void Close(SocketAsyncEventArgs e)
        {
            if (e == null || e.UserToken == null)
                return;

            var token = e.UserToken as IIOCP;
            Push(e);
            Close(token);
        }
        public static void Close(IIOCP token)
        {
            try
            {
                if (token == null)
                    return;

                token.IOCPClose();
            }
            catch (Exception) { }
        }
        public static void Close(Socket sock)
        {
            if (sock == null) return;
            try
            {
                sock?.Shutdown(SocketShutdown.Both);
            }
            catch (Exception) { }
            try
            {
                sock?.Close();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }
        static SocketAsyncEventArgs Pop()
        {
            SocketAsyncEventArgs e;
            if (!s_SocketAsyncEventArgsStack.TryPop(out e))
            {
                e = new SocketAsyncEventArgs();
                e.Completed += new EventHandler<SocketAsyncEventArgs>(Completed);
            }
            BufferManager.Alloc(e);
            e.UserToken = null;
            return e;
        }
        static void Push(SocketAsyncEventArgs e)
        {
            e.UserToken = null;
            BufferManager.Free(e);
            s_SocketAsyncEventArgsStack.Push(e);
        }
        static ConcurrentStack<SocketAsyncEventArgs> s_SocketAsyncEventArgsStack = new ConcurrentStack<SocketAsyncEventArgs>();

        static void Completed(object sender, SocketAsyncEventArgs e)
        {
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.Receive:
                    ProcessReceive(e);
                    break;
                case SocketAsyncOperation.Send:
                    ProcessSend(e);
                    break;
                default:
                    throw new ArgumentException("The last operation completed on the socket was not a receive or send");
            }
        }
    }
}

#endif