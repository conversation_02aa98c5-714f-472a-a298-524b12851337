﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //相机移动并缩放到目标点
    public class CameraMoveToTarget2D : ZoomActionBase2D
    {
        enum State
        {
            Idle,
            PrepareMoving,
            Moving,
        }

        public CameraMoveToTarget2D(CameraActionType updateOrder) : base(updateOrder)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
            mState = State.Idle;

            Init();
        }

        public void StartMoving(float targetX, float targetZ, float targetOrthoGraphicSize, float moveDuration, float zoomDuration, System.Action onCameraReachTarget, bool clampBorder, bool forceMoving)
        {
            if (MapCameraMgr.isMovingToTarget)
            {
                MapCameraMgr.EnableCameraDrag(true);
                MapCameraMgr.EnableCameraZoom(true);
            }

            if (enabled == false || forceMoving)
            {
                //save state
                mMoveState.targetX = targetX;
                mMoveState.targetZ = targetZ;
                mMoveState.targetOrthographicSize = targetOrthoGraphicSize;
                mMoveState.moveDuration = moveDuration;
                mMoveState.zoomDuration = zoomDuration;
                mMoveState.cameraReachTargetCallback = onCameraReachTarget;
                mMoveState.clampBorder = clampBorder;
                mMoveState.forceMoving = forceMoving;

                MapCameraMgr.isMovingToTarget = true;
                enabled = true;
                mState = State.PrepareMoving;
                mMoveDuration = moveDuration;
                mStartPos = MapCameraMgr.updatedCameraPosition;
                mTargetOrthographicSize = targetOrthoGraphicSize;
                mEndPos = new Vector3(targetX, mStartPos.y, targetZ);
                mZoomDuration = zoomDuration;
                mCameraReachTarget = onCameraReachTarget;
                mClampBorder = clampBorder;
                if (NeedZoomCamera())
                {
                    mMoveEndPos = CalculateMoveEndPos(mEndPos, MapCameraMgr.MapCamera.transform.forward, mStartPos.y);
                    mZoomDuration = zoomDuration;
                }
                else
                {
                    mMoveEndPos = mEndPos;
                    mZoomDuration = 0;
                }
            }
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            if (mState == State.PrepareMoving)
            {
                MapCameraMgr.InvokeStartMovingCallback();

                mState = State.Moving;

                Init();

                mOriginalClampBorderValue = MapCameraMgr.clampBorder;
                MapCameraMgr.clampBorder = mClampBorder;

                mElapsedTime = 0;
                mIsZooming = false;
            }
            else if (mState == State.Idle)
            {
                enabled = false;
            }
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            mElapsedTime += Time.deltaTime;

            if (mIsZooming)
            {
                bool finished = UpdateZooming();
                if (finished)
                {
                    isFinished = true;
                }

                return MapCameraMgr.updatedCameraPosition;
            }
            else
            {
                //平移相机
                float t = 1.0f;
                if (!Mathf.Approximately(mMoveDuration, 0))
                {
                    t = Mathf.Clamp(mElapsedTime, 0, mMoveDuration) / mMoveDuration;
                }

                float x = Mathf.SmoothStep(mStartPos.x, mMoveEndPos.x, t);
                float y = Mathf.SmoothStep(mStartPos.y, mMoveEndPos.y, t);
                float z = Mathf.SmoothStep(mStartPos.z, mMoveEndPos.z, t);

                if (mElapsedTime >= mMoveDuration)
                {
                    //看看是否需要zoom
                    if (NeedZoomCamera())
                    {
                        //下一帧开始缩放相机
                        mIsZooming = true;
                        mElapsedTime = 0;
                        t = 0;
                        mStartPos = mMoveEndPos;
                        mMoveEndPos = mEndPos;
                        mMoveDuration = mZoomDuration;

                        AutoZoomToOrthoSize(mTargetOrthographicSize, mZoomDuration * 1000);
                    }
                    else
                    {
                        //不用zoom
                        isFinished = true;
                    }
                }

                return new Vector3(x, y, z);
            }
        }

        bool NeedZoomCamera()
        {
            if (mZoomDuration == 0)
            {
                return false;
            }

            if (Mathf.Abs(MapCameraMgr.updatedOrthographicSize - mTargetOrthographicSize) <= MapCameraMgr.minDelta)
            {
                return false;
            }

            return true;
        }

        Vector3 CalculateMoveEndPos(Vector3 endPos, Vector3 cameraDir, float startY)
        {
            float t = (startY - endPos.y) / -cameraDir.y;
            return endPos - t * cameraDir;
        }

        public override void OnFinishImpl()
        {
            MapCameraMgr.isMovingToTarget = false;

            if (mCameraReachTarget != null)
            {
                mCameraReachTarget();
            }

            MapCameraMgr.clampBorder = mOriginalClampBorderValue;

            mState = State.Idle;
        }

        class MoveState
        {
            public float targetX;
            public float targetZ;
            public float targetOrthographicSize;
            public float moveDuration;
            public float zoomDuration;
            public System.Action cameraReachTargetCallback;
            public bool clampBorder;
            public bool forceMoving;
        }

        public void SaveState(bool keepOriginalDuration)
        {
            if (!keepOriginalDuration)
            {
                mMoveState.zoomDuration = Mathf.Max(0, mMoveState.zoomDuration - mElapsedTime);
            }
        }

        //从保存的状态继续移动
        public void RestoreState()
        {
            isFinished = true;
            OnFinish();

            StartMoving(mMoveState.targetX, mMoveState.targetZ, mMoveState.targetOrthographicSize, 0, mMoveState.zoomDuration, mMoveState.cameraReachTargetCallback, mMoveState.clampBorder, true);
        }

        //相机的起始位置
        Vector3 mStartPos;
        //相机最终的目标位置
        Vector3 mEndPos;

        //相机平移的目标位置
        Vector3 mMoveEndPos;
        float mElapsedTime;
        float mMoveDuration;
        float mZoomDuration;
        float mTargetOrthographicSize;
        bool mOriginalClampBorderValue;
        bool mIsZooming = false;
        System.Action mCameraReachTarget;
        State mState;
        MoveState mMoveState = new MoveState();

        bool mClampBorder;
    }
}