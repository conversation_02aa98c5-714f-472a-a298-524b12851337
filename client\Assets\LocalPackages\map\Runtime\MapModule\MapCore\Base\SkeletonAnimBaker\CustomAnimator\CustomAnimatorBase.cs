﻿ 



 
 

using UnityEngine;
using UnityEngine.Playables;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum AnimationBlendType
    {
        Fast,
        Slerp,
    }

    public abstract class CustomAnimatorBase : AnimatorBase
    {
        public CustomAnimatorBase(GameObject obj) : base(obj)
        {
        }

        public override void RegisterAnimationEventCallback(System.Action<string, string> stringEventCallback, System.Action<string, int> intEventCallback, System.Action<string, float> floatEventCallback)
        {
            mStateMachine?.RegisterAnimationEventCallback(stringEventCallback, intEventCallback, floatEventCallback);
        }

        public override void UnregisterAnimationEventCallback()
        {
            mStateMachine?.UnregisterAnimationEventCallback();
        }
        public override void UnregisterAnimationIntEventCallback()
        {
            mStateMachine?.UnregisterAnimationIntEventCallback();
        }
        public override void UnregisterAnimationFloatEventCallback()
        {
            mStateMachine?.UnregisterAnimationFloatEventCallback();
        }
        public override void UnregisterAnimationStringEventCallback()
        {
            mStateMachine?.UnregisterAnimationStringEventCallback();
        }

        public override void Play(string stateName)
        {
            mStateMachine?.Play(stateName);
        }

        public override void Play(int stateHashName)
        {
            mStateMachine?.Play(stateHashName);
        }

        void Play(string stateName, float normalizedStartOffset, float normalizedTransitionDuration)
        {
            mStateMachine?.Play(stateName, normalizedStartOffset, normalizedTransitionDuration);
        }

        void Play(int stateHashName, float normalizedStartOffset, float normalizedTransitionDuration)
        {
            mStateMachine?.Play(stateHashName, normalizedStartOffset, normalizedTransitionDuration);
        }

        public override void Play(string stateName, int layer) { Play(stateName); }
        public override void Play(int stateNameHash, int layer, float normalizedTime) { Play(stateNameHash, normalizedTime, 0); }
        public override void Play(string stateName, int layer, float normalizedTime) { Play(stateName, normalizedTime, 0); }
        public override void Play(int stateNameHash, int layer) { Play(stateNameHash, 0, 0); }
        public override void PlayInFixedTime(int stateNameHash, int layer, float fixedTime)
        {
            mStateMachine?.PlayInFixedTime(stateNameHash, fixedTime, 0);
        }
        public override void PlayInFixedTime(string stateName, int layer, float fixedTime)
        {
            mStateMachine?.PlayInFixedTime(stateName, fixedTime, 0);
        }
        public override void PlayInFixedTime(int stateNameHash)
        {
            mStateMachine?.PlayInFixedTime(stateNameHash, 0, 0);
        }
        public override void PlayInFixedTime(int stateNameHash, int layer)
        {
            mStateMachine?.PlayInFixedTime(stateNameHash, 0, 0);
        }
        public override void PlayInFixedTime(string stateName, int layer)
        {
            mStateMachine?.PlayInFixedTime(stateName, 0, 0);
        }
        public override void PlayInFixedTime(string stateName)
        {
            mStateMachine?.PlayInFixedTime(stateName, 0, 0);
        }

        public override void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset, float normalizedTransitionTime)
        {
            Play(stateHashName, normalizedTimeOffset, normalizedTransitionDuration);
        }
        public override void CrossFade(string stateName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset)
        {
            Play(stateName, normalizedTimeOffset, normalizedTransitionDuration);
        }
        public override void CrossFade(string stateName, float normalizedTransitionDuration)
        {
            Play(stateName, 0, normalizedTransitionDuration);
        }
        public override void CrossFade(string stateName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset, float normalizedTransitionTime)
        {
            Play(stateName, normalizedTimeOffset, normalizedTransitionDuration);
        }
        public override void CrossFade(string stateName, float normalizedTransitionDuration, int layer)
        {
            Play(stateName, 0, normalizedTransitionDuration);
        }
        public override void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer)
        {
            Play(stateHashName, 0, normalizedTransitionDuration);
        }
        public override void CrossFade(int stateHashName, float normalizedTransitionDuration)
        {
            Play(stateHashName, 0, normalizedTransitionDuration);
        }
        public override void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset)
        {
            Play(stateHashName, normalizedTimeOffset, normalizedTransitionDuration);
        }

        public void Pause()
        {
            mStateMachine.Pause();
        }

        public void Resume()
        {
            mStateMachine.Resume();
        }
        public override bool GetBool(string name)
        {
            return mStateMachine.GetBool(name);
        }
        public override bool GetBool(int id)
        {
            return mStateMachine.GetBool(id);
        }
        public override float GetFloat(int id)
        {
            return mStateMachine.GetFloat(id);
        }
        public override float GetFloat(string name)
        {
            return mStateMachine.GetFloat(name);
        }
        public override int GetInteger(string name)
        {
            return mStateMachine.GetInteger(name);
        }
        public override int GetInteger(int id)
        {
            return mStateMachine.GetInteger(id);
        }
        public override void ResetTrigger(string name)
        {
            mStateMachine.ResetTrigger(name);
        }
        public override void ResetTrigger(int id)
        {
            mStateMachine.ResetTrigger(id);
        }
        public override void SetBool(string name, bool value)
        {
            mStateMachine.SetBool(name, value);
        }
        public override void SetBool(int id, bool value)
        {
            mStateMachine.SetBool(id, value);
        }
        public override void SetTrigger(int id)
        {
            mStateMachine.SetTrigger(id);
        }
        public override void SetTrigger(string name)
        {
            mStateMachine.SetTrigger(name);
        }
        public override void SetFloat(int id, float value)
        {
            mStateMachine.SetFloat(id, value);
        }
        public override void SetFloat(string name, float value, float dampTime, float deltaTime)
        {
            mStateMachine.SetFloat(name, value);
        }
        public override void SetFloat(string name, float value)
        {
            mStateMachine.SetFloat(name, value);
        }
        public override void SetFloat(int id, float value, float dampTime, float deltaTime)
        {
            mStateMachine.SetFloat(id, value);
        }
        public override void SetInteger(int id, int value)
        {
            mStateMachine.SetInteger(id, value);
        }
        public override void SetInteger(string name, int value)
        {
            mStateMachine.SetInteger(name, value);
        }

        public override AnimationParameterInfo[] parameters { get { return mStateMachine.parameters; } }

        public override float speed { set { mStateMachine.speed = value; } get { return mStateMachine.speed; } }

        public override bool HasState(int layerIndex, int stateID)
        {
            return mStateMachine.HasState(stateID);
        }

        public override string GetCurrentStateName()
        {
            var state = mStateMachine.currentState;
            return state == null ? string.Empty : state.animStateInfo.stateName;
        }

        public override string GetNextStateName()
        {
            var state = mStateMachine.nextState;
            return state == null ? string.Empty : state.animStateInfo.stateName;
        }

        public override float GetCurrentNormalizedTime()
        {
            var state = mStateMachine.currentState;
            if (state == null)
            {
                return 0;
            }
            return state.GetNormalizedCurrentTime();
        }

        public override bool IsPlayingAnimation(string animStateName)
        {
            if (mStateMachine == null)
            {
                return false;
            }
            return mStateMachine.IsPlayingAnimation(animStateName);
        }

        public override string[] GetAnimationNames()
        {
            return mStateMachine.GetAnimationNames();
        }

        //animator interfaces
        public override bool stabilizeFeet { get { Debug.LogError("Not implemented"); return false; } set { Debug.LogError("Not implemented"); } }
        public override Quaternion bodyRotation { get { Debug.LogError("Not implemented"); return Quaternion.identity; } set { Debug.LogError("Not implemented"); } }
        public override Vector3 bodyPosition { get { Debug.LogError("Not implemented"); return Vector3.zero; } set { Debug.LogError("Not implemented"); } }
        public override float gravityWeight { get { Debug.LogError("Not implemented"); return 0; } }
        public override bool hasTransformHierarchy { get { Debug.LogError("Not implemented"); return false; } }
        public override AnimatorUpdateMode updateMode { get { Debug.LogError("Not implemented"); return AnimatorUpdateMode.Normal; } set { Debug.LogError("Not implemented"); } }
        public override bool applyRootMotion { get { Debug.LogError("Not implemented"); return false; } set { Debug.LogError("Not implemented"); } }
        public override Quaternion rootRotation { get { Debug.LogError("Not implemented"); return Quaternion.identity; } set { Debug.LogError("Not implemented"); } }
        public override Vector3 rootPosition { get { Debug.LogError("Not implemented"); return Vector3.zero; } set { Debug.LogError("Not implemented"); } }
        public override Vector3 angularVelocity { get { Debug.LogError("Not implemented"); return Vector3.zero; } }
        public override Vector3 velocity { get { Debug.LogError("Not implemented"); return Vector3.zero; } }
        public override Quaternion deltaRotation { get { Debug.LogError("Not implemented"); return Quaternion.identity; } }
        public override Vector3 deltaPosition { get { Debug.LogError("Not implemented"); return Vector3.zero; } }
        public override bool isInitialized { get { Debug.LogError("Not implemented"); return mWrapper != null; } }
        public override float humanScale { get { Debug.LogError("Not implemented"); return 1.0f; } }
        public override bool hasRootMotion { get { Debug.LogError("Not implemented"); return false; } }
        public override bool isHuman { get { Debug.LogError("Not implemented"); return false; } }
        public override int layerCount { get { return 1; } }
        public override bool isOptimizable { get { Debug.LogError("Not implemented"); return false; } }
        public override float feetPivotActive { get { Debug.LogError("Not implemented"); return 0; } set { Debug.LogError("Not implemented"); } }
        public override bool logWarnings { get { return false; } set { Debug.LogError("Not implemented"); } }
        public override float rightFeetBottomHeight { get { Debug.LogError("Not implemented"); return 0; } }
        public override float leftFeetBottomHeight { get { Debug.LogError("Not implemented"); return 0; } }
        public override bool layersAffectMassCenter { get { Debug.LogError("Not implemented"); return false; } set { Debug.LogError("Not implemented"); } }
        public override PlayableGraph playableGraph { get { Debug.LogError("Not implemented"); return PlayableGraph.Create(); } }
        public override Avatar avatar { get { Debug.LogError("Not implemented"); return null; } set { Debug.LogError("Not implemented"); } }
        public override bool hasBoundPlayables { get { Debug.LogError("Not implemented"); return false; } }
        public override RuntimeAnimatorController runtimeAnimatorController { get { Debug.LogError("Not implemented"); return null; } set { Debug.LogError("Not implemented"); } }
        public override AnimatorRecorderMode recorderMode { get { Debug.LogError("Not implemented"); return AnimatorRecorderMode.Offline; } }
        public override float recorderStopTime { get { Debug.LogError("Not implemented"); return 0; } set { Debug.LogError("Not implemented"); } }
        public override float recorderStartTime { get { Debug.LogError("Not implemented"); return 0; } set { Debug.LogError("Not implemented"); } }
        public override float playbackTime { get { Debug.LogError("Not implemented"); return 0; } set { Debug.LogError("Not implemented"); } }
        public override AnimatorCullingMode cullingMode { get { Debug.LogError("Not implemented"); return AnimatorCullingMode.AlwaysAnimate; } set { Debug.LogError("Not implemented"); } }
        public override Quaternion targetRotation { get { Debug.LogError("Not implemented"); return Quaternion.identity; } }
        public override Vector3 targetPosition { get { Debug.LogError("Not implemented"); return Vector3.zero; } }
        public override bool isMatchingTarget { get { Debug.LogError("Not implemented"); return false; } }
        public override Vector3 pivotPosition { get { Debug.LogError("Not implemented"); return Vector3.zero; } }
        public override float pivotWeight { get { Debug.LogError("Not implemented"); return 1.0f; } }
        public override int parameterCount { get { return parameters.Length; } }
        public override bool fireEvents { get { Debug.LogError("Not implemented"); return false; } set { Debug.LogError("Not implemented"); } }
        public override bool keepAnimatorControllerStateOnDisable { get { Debug.LogError("Not implemented"); return false; } set { Debug.LogError("Not implemented"); } }
        public override void ApplyBuiltinRootMotion() { Debug.LogError("Not implemented"); }
        public override void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer, float fixedTimeOffset)
        {
            mStateMachine?.PlayInFixedTime(stateName, fixedTimeOffset, fixedTransitionDuration);
        }
        public override void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer, float fixedTimeOffset, float normalizedTransitionTime)
        {
            mStateMachine?.PlayInFixedTime(stateHashName, fixedTimeOffset, fixedTransitionDuration);
        }
        public override void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration)
        {
            mStateMachine?.PlayInFixedTime(stateHashName, 0, fixedTransitionDuration);
        }
        public override void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer)
        {
            mStateMachine?.PlayInFixedTime(stateHashName, 0, fixedTransitionDuration);
        }
        public override void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer, float fixedTimeOffset)
        {
            mStateMachine?.PlayInFixedTime(stateHashName, fixedTimeOffset, fixedTransitionDuration);
        }
        public override void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer, float fixedTimeOffset, float normalizedTransitionTime)
        {
            mStateMachine?.PlayInFixedTime(stateName, fixedTimeOffset, fixedTransitionDuration);
        }
        public override void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer)
        {
            mStateMachine?.PlayInFixedTime(stateName, 0, fixedTransitionDuration);
        }
        public override void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration)
        {
            mStateMachine?.PlayInFixedTime(stateName, 0, fixedTransitionDuration);
        }
        public override AnimatorTransitionInfo GetAnimatorTransitionInfo(int layerIndex) { Debug.LogError("not implemented"); return new AnimatorTransitionInfo(); }
        public override T GetBehaviour<T>() { Debug.LogError("not implemented"); return null; }
        public override StateMachineBehaviour[] GetBehaviours(int fullPathHash, int layerIndex) { Debug.LogError("not implemented"); return null; }
        public override T[] GetBehaviours<T>() { Debug.LogError("not implemented"); return null; }
        public override Transform GetBoneTransform(HumanBodyBones humanBoneId) { Debug.LogError("not implemented"); return null; }
        public override AnimatorClipInfo[] GetCurrentAnimatorClipInfo(int layerIndex) { Debug.LogError("not implemented"); return null; }
        public override void GetCurrentAnimatorClipInfo(int layerIndex, List<AnimatorClipInfo> clips) { Debug.LogError("not implemented"); }
        public override int GetCurrentAnimatorClipInfoCount(int layerIndex) { Debug.LogError("not implemented"); return 0; }
        public override AnimatorStateInfo GetCurrentAnimatorStateInfo(int layerIndex) { Debug.LogError("not implemented"); return new AnimatorStateInfo(); }

        public override Vector3 GetIKHintPosition(AvatarIKHint hint) { Debug.LogError("not implemented"); return Vector3.zero; }
        public override float GetIKHintPositionWeight(AvatarIKHint hint) { Debug.LogError("not implemented"); return 0; }
        public override Vector3 GetIKPosition(AvatarIKGoal goal) { Debug.LogError("not implemented"); return Vector3.zero; }
        public override float GetIKPositionWeight(AvatarIKGoal goal) { Debug.LogError("not implemented"); return 0; }
        public override Quaternion GetIKRotation(AvatarIKGoal goal) { Debug.LogError("not implemented"); return Quaternion.identity; }
        public override float GetIKRotationWeight(AvatarIKGoal goal) { Debug.LogError("not implemented"); return 0; }
        public override int GetLayerIndex(string layerName) { Debug.LogError("not implemented"); return 0; }
        public override string GetLayerName(int layerIndex) { Debug.LogError("not implemented"); return ""; }
        public override float GetLayerWeight(int layerIndex) { Debug.LogError("not implemented"); return 0; }
        public override AnimatorClipInfo[] GetNextAnimatorClipInfo(int layerIndex) { Debug.LogError("not implemented"); return null; }
        public override void GetNextAnimatorClipInfo(int layerIndex, List<AnimatorClipInfo> clips) { Debug.LogError("not implemented"); }
        public override int GetNextAnimatorClipInfoCount(int layerIndex) { Debug.LogError("not implemented"); return 0; }
        public override AnimatorStateInfo GetNextAnimatorStateInfo(int layerIndex) { Debug.LogError("not implemented"); return new AnimatorStateInfo(); }
        public override AnimationParameterInfo GetParameter(int index) { return mStateMachine.GetParameter(index); }
        public override void InterruptMatchTarget() { Debug.LogError("not implemented"); }
        public override void InterruptMatchTarget(bool completeMatch) { Debug.LogError("not implemented"); }
        public override bool IsInTransition(int layerIndex) { return mStateMachine.IsInTransitionDuration; }
        public override bool IsParameterControlledByCurve(int id) { Debug.LogError("not implemented"); return false; }
        public override bool IsParameterControlledByCurve(string name) { Debug.LogError("not implemented"); return false; }
        public override void MatchTarget(Vector3 matchPosition, Quaternion matchRotation, AvatarTarget targetBodyPart, MatchTargetWeightMask weightMask, float startNormalizedTime) { Debug.LogError("not implemented"); }
        public override void MatchTarget(Vector3 matchPosition, Quaternion matchRotation, AvatarTarget targetBodyPart, MatchTargetWeightMask weightMask, float startNormalizedTime, float targetNormalizedTime) { Debug.LogError("not implemented"); }

        public override void Rebind() { Debug.LogError("not implemented"); }
        public override void SetBoneLocalRotation(HumanBodyBones humanBoneId, Quaternion rotation) { Debug.LogError("not implemented"); }
        public override void SetIKHintPosition(AvatarIKHint hint, Vector3 hintPosition) { Debug.LogError("not implemented"); }
        public override void SetIKHintPositionWeight(AvatarIKHint hint, float value) { Debug.LogError("not implemented"); }
        public override void SetIKPosition(AvatarIKGoal goal, Vector3 goalPosition) { Debug.LogError("not implemented"); }
        public override void SetIKPositionWeight(AvatarIKGoal goal, float value) { Debug.LogError("not implemented"); }
        public override void SetIKRotation(AvatarIKGoal goal, Quaternion goalRotation) { Debug.LogError("not implemented"); }
        public override void SetIKRotationWeight(AvatarIKGoal goal, float value) { Debug.LogError("not implemented"); }
        public override void SetLayerWeight(int layerIndex, float weight) { Debug.LogError("not implemented"); }
        public override void SetLookAtPosition(Vector3 lookAtPosition) { Debug.LogError("not implemented"); }
        public override void SetLookAtWeight(float weight) { Debug.LogError("not implemented"); }
        public override void SetLookAtWeight(float weight, float bodyWeight) { Debug.LogError("not implemented"); }
        public override void SetLookAtWeight(float weight, float bodyWeight, float headWeight) { Debug.LogError("not implemented"); }
        public override void SetLookAtWeight(float weight, float bodyWeight, float headWeight, float eyesWeight) { Debug.LogError("not implemented"); }
        public override void SetLookAtWeight(float weight, float bodyWeight, float headWeight, float eyesWeight, float clampWeight) { Debug.LogError("not implemented"); }
        public override void SetTarget(AvatarTarget targetIndex, float targetNormalizedTime) { Debug.LogError("not implemented"); }
        public override void StartPlayback() { Debug.LogError("not implemented"); }
        public override void StartRecording(int frameCount) { Debug.LogError("not implemented"); }
        public override void StopPlayback() { Debug.LogError("not implemented"); }
        public override void StopRecording() { Debug.LogError("not implemented"); }
        public override void UpdateAnimator(float deltaTime) { }
        public override void WriteDefaultValues() { Debug.LogError("not implemented"); }

        public CustomAnimationStateMachine statemachine { get { return mStateMachine; } }
        public override bool enableAnimationBlending { get { return mStateMachine.enableAnimationBlendingNow; } }
        public override InterruptionType interruptionType { get { return mStateMachine.interruptionType; } set { mStateMachine.interruptionType = value; } }

        protected CustomAnimationStateMachine mStateMachine;
        protected AnimatorWrapper mWrapper;
    }
}
