﻿ 



 
 



#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public partial class NPCRegionLayer : MapLayerBase
    {
        public NPCRegionLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerData != null)
            {
                Map.currentMap.DestroyObject(mLayerData);
                mLayerData = null;
            }
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
        }

        public override void Unload()
        {
            Map.currentMap.RemoveMapLayerByID(id);
        }

        List<NPCRegionTemplate> CreateTemplateList(config.NPCRegionTemplate[] templates)
        {
            List<NPCRegionTemplate> templateList = new List<NPCRegionTemplate>(templates.Length);
            for (int i = 0; i < templates.Length; ++i)
            {
                templateList.Add(new NPCRegionTemplate(templates[i].name, templates[i].bandID, templates[i].color, templates[i].level, templates[i].tileType));
            }
            return templateList;
        }

        List<NPCRegionLayerData.Layer> CreateLayers(config.NPCRegionLayerData layerData, List<config.NPCRegionLayerData.Layer> sourceLayers)
        {
            List<NPCRegionLayerData.Layer> layers = new List<NPCRegionLayerData.Layer>(sourceLayers.Count);
            for (int i = 0; i < sourceLayers.Count; ++i)
            {
                NPCRegionLayerData.Layer layer = new NPCRegionLayerData.Layer();
                layer.templates = CreateTemplateList(sourceLayers[i].regionTemplates);
                layer.gridData = sourceLayers[i].tiles;
                int layerWidth = sourceLayers[i].overridenWidth;
                int layerHeight = sourceLayers[i].overridenHeight;
                float tileWidth = sourceLayers[i].overridenTileWidth;
                float tileHeight = sourceLayers[i].overridenTileHeight;
                if (layerWidth == 0)
                {
                    layerWidth = layerData.xTileCount;
                    tileWidth = layerData.tileWidth;
                }
                if (layerHeight == 0)
                {
                    layerHeight = layerData.zTileCount;
                    tileHeight = layerData.tileHeight;
                }

                layer.overridenWidth = layerWidth;
                layer.overridenHeight = layerHeight;
                layer.overrdenTileHeight = tileHeight;
                layer.overrdenTileWidth = tileWidth;
                layer.name = sourceLayers[i].name;
                layer.export = sourceLayers[i].export;
                layers.Add(layer);
            }
            return layers;
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.NPCRegionLayerData;

            var layers = CreateLayers(sourceLayer, sourceLayer.layers);
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, sourceLayer.zTileCount, sourceLayer.xTileCount, sourceLayer.tileWidth, sourceLayer.tileHeight, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            mLayerData = new NPCRegionLayerData(header, map, layers);
            mLayerView = new NPCRegionLayerView(mLayerData);
            mLayerView.active = layerData.active;

            Map.currentMap.AddMapLayer(this);
        }

        public void Clear(int layerIndex)
        {
            mLayerData.Clear(layerIndex);
            mLayerView.RefreshTexture(layerIndex);
        }

        public override void RefreshObjectsInViewport()
        {
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            return false;
        }

        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return false;
        }

        public override int GetCurrentLOD()
        {
            return 0;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            return false;
        }

        public void ExportCSV(int layerIndex, string path, bool exportZoneIDs)
        {
            float mapWidth = GetTotalWidth();
            float mapHeight = GetTotalHeight();
            int verticalRegionCount = layerData.verticalTileCount;
            int horizontalRegionCount = layerData.horizontalTileCount;
            int regionWidth = (int)layerData.GetLayerWidthInMeter(0) / horizontalRegionCount;
            int regionHeight = (int)layerData.GetLayerHeightInMeter(0) / verticalRegionCount;
            var editorMap = Map.currentMap as EditorMap;
            mRegions = new MapRegionManager();
            mRegions.CreateRegions(0, 0, mapWidth, mapHeight, regionWidth, regionHeight, 0, PrefabOutlineType.ObjectPlacementObstacle, CheckMapCollisionOperation.kCalculateValidArea, editorMap.generateNPCSpawnPointsInBorderLine, true);

            StringBuilder builder = new StringBuilder();
            var name = mLayerData.GetLayerName(layerIndex);

            string[] headers = null;
            if (exportZoneIDs)
            {
                if (MapModule.projectName == "k2")
                {
                    headers = new string[] {
                    "A_INT_id",
                    "A_MAP_RectangleZone_rectangle_zone",
                    "S_LNG_available_space",
                    "A_INT_lvl",
                    "A_INT_band_id",
                    "S_ARR_BandIDList_band_id_list",
                    };
                }
                else {
                    headers = new string[] {
                    "A_INT_id",
                    "A_MAP_rectangle_zone",
                    "S_INT_available_space",
                    "A_INT_lvl",
                    "A_INT_band_id",
                    "S_ARR_band_id_list",
                    };
                }
            }
            else
            {
                headers = new string[] {
                "A_INT_id",
                "A_MAP_rectangle_zone",
                "S_INT_available_space",
                "A_INT_lvl",
                $"A_INT_{name.Replace(' ', '_')}",
                };
            }

            for (int i = 0; i < headers.Length; ++i)
            {
                builder.Append(headers[i]);
                if (i != headers.Length - 1)
                {
                    builder.Append("\t");
                }
            }

            builder.AppendLine();

            int startID = 13131001;
            int scale = 1000;

            for (int i = 0; i < verticalRegionCount; ++i)
            {
                for (int j = 0; j < horizontalRegionCount; ++j)
                {
                    int idx = j * horizontalRegionCount + i;
                    //id
                    builder.AppendFormat("{0}\t", startID);
                    ++startID;

                    //rectangle zone
                    long startX = j * regionWidth;
                    long startY = i * regionHeight;
                    long endX = startX + regionWidth;
                    long endY = startY + regionHeight;

                    Dictionary<string, object> zone = new Dictionary<string, object>();
                    zone["x1"] = startX * scale;
                    zone["x2"] = endX * scale;
                    zone["y1"] = startY * scale;
                    zone["y2"] = endY * scale;
                    var zoneStr = JSONParser.Serialize(zone);
                    builder.AppendFormat("{0}\t", zoneStr);

                    //available space
                    long availableSpace = (long)(mRegions.regions[idx].GetValidArea() * scale * scale);
                    builder.AppendFormat("{0}\t", availableSpace);

                    //lvl
                    int lvl = GetLevel(layerIndex, j, i);
                    if (lvl <= 0)
                    {
                        lvl = 1;
                    }
                    builder.AppendFormat("{0}\t", lvl);

                    //band id
                    int bandID = GetBandID(layerIndex, j, i);
                    //特殊处理band id??不清楚band id用来干嘛
                    if (bandID == 0)
                    {
                        bandID = 13141000 + lvl;
                    }

                    //band_id list
                    if (exportZoneIDs)
                    {
                        builder.AppendFormat("{0}\t", bandID);
                        var bandIDListStr = CreateBandIDListStr(j, i);
                        builder.AppendLine(bandIDListStr);
                    }
                    else
                    {
                        builder.AppendLine(bandID.ToString());
                    }
                }
            }

            var str = builder.ToString();
            File.WriteAllText(path, str);
        }

        string CreateBandIDListStr(int x, int y)
        {
            var layers = layerData.layers;
            List<object> tileList = new List<object>();
            for (int i = 0; i < layers.Count; ++i)
            {
                if (layers[i].export)
                {
                    Dictionary<string, object> tileSetting = new Dictionary<string, object>();
                    var tileType = layerData.GetTileBandID(i, x, y);
                    if (tileType < 0)
                    {
                        tileType = 0;
                    }
                    tileSetting["zone_id"] = tileType;
                    tileList.Add(tileSetting);
                }
            }
            var ret = JSONParser.Serialize(tileList);
            return ret;
        }

        int GetBandID(int layerIndex, int x, int y)
        {
            NPCRegionTemplate temp = layerData.GetNPCRegionTemplateByCoord(layerIndex, x, y);
            if (temp != null)
            {
                return temp.bandID;
            }
            return 0;
        }

        int GetLevel(int layerIndex, int x, int y)
        {
            NPCRegionTemplate temp = layerData.GetNPCRegionTemplateByCoord(layerIndex, x, y);
            if (temp != null)
            {
                return temp.level;
            }
            return 0;
        }

        string GetSpriteTemplateName(int level)
        {
            return "region_sprite_" + level.ToString();
        }

        public void SetTileBandID(int layerIndex, int x, int y, int bandID)
        {
            mLayerData.SetTileBandID(layerIndex, x, y, bandID);
        }

        public void SetTileBandID(int layerIndex, Vector3 pos, int brushSize, int bandID)
        {
            Vector2Int coord = layerData.FromWorldPositionToCoordinate(layerIndex, pos);
            int startX = coord.x - brushSize / 2;
            int startY = coord.y - brushSize / 2;
            int endX = startX + brushSize - 1;
            int endY = startY + brushSize - 1;

            int h = layerData.GetLayerHeight(layerIndex);
            int v = layerData.GetLayerWidth(layerIndex);
            if (endX < 0 || endY < 0 || startX >= h || startY >= v)
            {
                return;
            }

            startX = Mathf.Clamp(startX, 0, h - 1);
            startY = Mathf.Clamp(startY, 0, v - 1);
            endX = Mathf.Clamp(endX, 0, h - 1);
            endY = Mathf.Clamp(endY, 0, v - 1);

            int width = endX - startX + 1;
            int height = endY - startY + 1;
            var pixels = mColorArrayPool.Rent(width * height);
            int idx = 0;
            var template = layerData.GetNPCRegionTemplateByBandID(layerIndex, bandID);
            Color32 color = new Color32(0, 0, 0, 0);
            if (template != null)
            {
                color = template.color;
            }
            for (int i = startY; i <= endY; ++i)
            {
                for (int j = startX; j <= endX; ++j)
                {
                    layerData.SetTileBandID(layerIndex, j, i, bandID);
                    pixels[idx] = color;
                    ++idx;
                }
            }
            mLayerView.SetTexturePixels(layerIndex, startX, startY, width, height, pixels);
            mColorArrayPool.Return(pixels);
        }

        public void InitTiles()
        {
            var color = new Color32(255, 255, 255, 255);
            mColorToIndex[color] = 1;
            mIndexToColor[1] = color;
            NPCRegionTemplate template = mLayerData.AddNPCRegionTemplate(0, "dummy", 0, color, 1, 0);

            int rows = verticalTileCount;
            int cols = horizontalTileCount;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    SetTileBandID(0, j, i, template.bandID);
                }
            }

            mLayerView.RefreshTexture(0);
        }

        public void ImportColorMap(int layerIndex)
        {
            string texturePath = EditorUtility.OpenFilePanelWithFilters("Color Map", "", new string[] { "tga", "tga" });
            string colorIndexMapPath = EditorUtility.OpenFilePanelWithFilters("Color Index", "", new string[] { "tsv", "tsv" });
            if (!string.IsNullOrEmpty(texturePath) && !string.IsNullOrEmpty(colorIndexMapPath))
            {
                //先清理
                Clear(layerIndex);

                LoadColorIndex(layerIndex, colorIndexMapPath);

                var bytes = File.ReadAllBytes(texturePath);
                if (bytes != null)
                {
                    var tgaData = new TgaDecoderTest.TgaData(bytes);
                    int width = tgaData.Width;
                    int height = tgaData.Height;
                    if (width != layerData.GetLayerWidth(layerIndex) ||
                        height != layerData.GetLayerHeight(layerIndex))
                    {
                        EditorUtility.DisplayDialog("Error", "Invalid color map, size not match!", "OK");
                        return;
                    }

                    bool error = false;
                    for (int i = 0; i < height; ++i)
                    {
                        for (int j = 0; j < width; ++j)
                        {
                            var idx = i * width + j;
                            //flip texture
                            var pixel = tgaData.GetPixel(j, height - 1 - i);

                            var index = GetColorIndex(pixel);
                            if (index > 0)
                            {
                                SetTileBandID(layerIndex, j, i, index);
                            }
                        }

                        if (error)
                        {
                            break;
                        }
                    }

                    mLayerView.RefreshTexture(layerIndex);
                }
            }
        }

        public void ExportColorMap(int layerIndex)
        {
            string colorTexturePath = EditorUtility.SaveFilePanel("Color Map", "", "color_map", "tga");
            if (!string.IsNullOrEmpty(colorTexturePath))
            {
                string colorIndexFilePath = Utils.RemoveExtension(colorTexturePath) + "_index.tsv";

                SaveColorIndex(layerIndex, colorIndexFilePath);

                Texture2D texture = mLayerView.GetTexture(layerIndex);
                byte[] bytes = texture.EncodeToTGA();
                File.WriteAllBytes(colorTexturePath, bytes);
            }
        }

        void SaveColorIndex(int layerIndex, string filePath)
        {
            StringBuilder builder = new StringBuilder();
            string[] headers = new string[] {
                "A_STR_COLOR",
                "A_INT_ID",
                "A_STR_NAME",
                "A_INT_LEVEL",
            };

            for (int i = 0; i < headers.Length; ++i)
            {
                builder.Append(headers[i]);
                if (i != headers.Length - 1)
                {
                    builder.Append("\t");
                }
            }

            builder.AppendLine();

            var templates = mLayerData.GetTemplates(layerIndex);

            for (int i = 0; i < templates.Count; ++i)
            {
                var colorStr = Utils.FormatColorRGBStr(templates[i].color);
                //color
                builder.AppendFormat("{0}\t", colorStr);

                //id
                builder.AppendFormat("{0}\t", templates[i].bandID);

                //name
                builder.AppendFormat("{0}\t", templates[i].name);

                builder.AppendLine(templates[i].level.ToString());
            }

            var str = builder.ToString();
            File.WriteAllText(filePath, str);
        }

        int LoadColorIndex(int layerIndex, string colorIndexMapPath)
        {
            int colorCount = 0;
            mColorToIndex = new Dictionary<Color32, int>();
            mIndexToColor = new Dictionary<int, Color32>();
            bool suc = TSVReader.Load(colorIndexMapPath);
            if (suc)
            {
                var rows = TSVReader.rows;
                colorCount = rows.Count;
                for (int i = 0; i < rows.Count; ++i)
                {
                    var colorStr = TSVReader.GetString(i, "COLOR", out suc);
                    var type = (int)TSVReader.GetInt(i, "ID", out suc);
                    var name = TSVReader.GetString(i, "NAME", out suc);
                    var level = (int)TSVReader.GetInt(i, "LEVEL", out suc);
                    var color = Utils.ConvertToColor32(colorStr);
                    mColorToIndex[color] = type;
                    mIndexToColor[type] = color;

                    if (string.IsNullOrEmpty(name))
                    {
                        name = $"type_{i}";
                    }
                    mLayerData.AddNPCRegionTemplate(layerIndex, name, type, color, level <= 0 ? level : 1, 0);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Can't find color index config file!", "OK");
            }
            return colorCount;
        }

        int GetColorIndex(Color32 color)
        {
            int index;
            bool found = mColorToIndex.TryGetValue(color, out index);
            if (!found)
            {
                if (!(color.r == 0 && color.g == 0 && color.b == 0 && color.a == 0))
                {
                    Debug.Assert(false, "invalid color " + color.ToString());
                }
            }

            return index;
        }

        Color32 GetIndexColor(int index)
        {
            Color32 color;
            bool found = mIndexToColor.TryGetValue(index, out color);
            if (!found)
            {
                Debug.Assert(false, "invalid index!");
            }
            return color;
        }

        public void AddLayer(string name, int overridenWidth, int overridenHeight, bool export)
        {
            var layerData = mLayerData.AddLayer(name, overridenWidth, overridenHeight, export);
            if (layerData != null)
            {
                mLayerView.AddLayer(mLayerData.layers.Count - 1);
            }
        }

        public void RemoveLayer(int layerIndex)
        {
            bool suc = mLayerData.RemoveLayer(layerIndex);
            if (suc)
            {
                mLayerView.RemoveLayer(layerIndex);
            }
        }

        public void ShowLayer(int layerIndex, bool visible)
        {
            mLayerView.Show(layerIndex, visible);
        }

        public void SetLayerName(int layerIndex, string name)
        {
            mLayerData.SetLayerName(layerIndex, name);
        }

        public NPCRegionLayerData layerData { get { return mLayerData; } }
        public NPCRegionLayerView layerView { get { return mLayerView; } }
        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override int horizontalTileCount { get { return mLayerData.horizontalTileCount; } }
        public override int verticalTileCount { get { return mLayerData.verticalTileCount; } }
        public override float tileWidth { get { return mLayerData.tileWidth; } }
        public override float tileHeight { get { return mLayerData.tileHeight; } }
        public override GridType gridType { get { return mLayerData.gridType; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override int lodCount => mLayerData.lodCount;
        public int brushSize = 1;
        public int selectedIndex = -1;
        public int selectedLayerIndex = -1;
        public bool exportZoneIDs = MapModule.projectName == "k2";

        NPCRegionLayerData mLayerData;
        NPCRegionLayerView mLayerView;
        MapRegionManager mRegions;
        ArrayPool<Color32> mColorArrayPool = ArrayPool<Color32>.Create();
        Dictionary<Color32, int> mColorToIndex = new Dictionary<Color32, int>();
        Dictionary<int, Color32> mIndexToColor = new Dictionary<int, Color32>();
    }
}


#endif