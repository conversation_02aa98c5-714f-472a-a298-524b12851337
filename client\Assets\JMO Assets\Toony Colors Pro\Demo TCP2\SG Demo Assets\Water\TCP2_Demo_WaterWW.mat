%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_WaterWW
  m_Shader: {fileID: 4800000, guid: 0fc6fe3cbeb58fb46822b6e42cbede4c, type: 3}
  m_ShaderKeywords: _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _FoamTex
      second:
        m_Texture: {fileID: 2800000, guid: 532c9e2e0905df744860e5e5c36ada26, type: 3}
        m_Scale: {x: 8, y: 8}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: 1d2d37743b4a417489980511e11c0fd8, type: 3}
        m_Scale: {x: 8, y: 8}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Mask1
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _BumpScale
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DepthDistance
      second: 0.6
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _FoamSmooth
      second: 0.02
    - first:
        name: _FoamSpread
      second: 2
    - first:
        name: _FoamStrength
      second: 0.8
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.5
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Parallax
      second: 0.02
    - first:
        name: _RampSmooth
      second: 0.1
    - first:
        name: _RampThreshold
      second: 0.5
    - first:
        name: _RimMax
      second: 1
    - first:
        name: _RimMin
      second: 0.4
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _UVWaveAmplitude
      second: 0.05
    - first:
        name: _UVWaveFrequency
      second: 1
    - first:
        name: _UVWaveSpeed
      second: 1
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: __dummy__
      second: 0
    m_Colors:
    - first:
        name: _Color
      second: {r: 0.07315097, g: 0.7210942, b: 0.9044118, a: 1}
    - first:
        name: _DepthColor
      second: {r: 0.017355122, g: 0.38781166, b: 0.78676474, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _FoamColor
      second: {r: 0.9, g: 0.9, b: 0.9, a: 1}
    - first:
        name: _FoamSpeed
      second: {r: 1, g: 1, b: -1, a: -1}
    - first:
        name: _HColor
      second: {r: 0.9264706, g: 0.9264706, b: 0.9264706, a: 1}
    - first:
        name: _RimColor
      second: {r: 0.28239998, g: 0.6072385, b: 0.8, a: 0.7058824}
    - first:
        name: _SColor
      second: {r: 0.22794116, g: 0.22794116, b: 0.22794116, a: 1}
