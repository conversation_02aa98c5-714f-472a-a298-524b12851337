﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public static class QuadTreeRectIntersectionTest
    {
        //左移视野时Bounding box的相交性判断
        public static bool LeftCondition(Rect modelWorldBounds, Rect bounds)
        {
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;

            var max = bounds.max;
            var min = bounds.min;
            if (Utils.GE(objMax.x, min.x) && Utils.LE(objMax.x, max.x) &&
                !(Utils.GT(objMin.y, max.y) || Utils.GT(min.y, objMax.y)))
            {
                return true;
            }
            return false;
        }

        //右移视野时Bounding box的相交性判断
        public static bool RightCondition(Rect modelWorldBounds, Rect bounds)
        {
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;

            var max = bounds.max;
            var min = bounds.min;
            if (Utils.GE(objMin.x, min.x) && Utils.LE(objMin.x, max.x) &&
                !(Utils.GT(objMin.y, max.y) || Utils.GT(min.y, objMax.y)))
            {
                return true;
            }
            return false;
        }

        //上移视野时Bounding box的相交性判断
        public static bool TopCondition(Rect modelWorldBounds, Rect bounds)
        {
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;
            var max = bounds.max;
            var min = bounds.min;
            if (Utils.GE(objMax.y, min.y) && Utils.LE(objMax.y, max.y) &&
                !(Utils.GT(objMin.x, max.x) || Utils.GT(min.x, objMax.x)))
            {
                return true;
            }
            return false;
        }

        //下移视野时Bounding box的相交性判断
        public static bool BottomCondition(Rect modelWorldBounds, Rect bounds)
        {
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;
            var max = bounds.max;
            var min = bounds.min;
            if (Utils.GE(objMin.y, min.y) && Utils.LE(objMin.y, max.y) &&
                !(Utils.GT(objMin.x, max.x) || Utils.GT(min.x, objMax.x)))
            {
                return true;
            }
            return false;
        }

        public static bool IntersectCondition(Rect modelWorldBounds, Rect bounds)
        {
            var min = bounds.min;
            var max = bounds.max;
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;
            if (Utils.GT(objMin.x, max.x) || Utils.GT(min.x, objMax.x) ||
                Utils.GT(min.y, objMax.y) || Utils.GT(objMin.y, max.y))
            {
                return false;
            }
            return true;
        }
    }
}
