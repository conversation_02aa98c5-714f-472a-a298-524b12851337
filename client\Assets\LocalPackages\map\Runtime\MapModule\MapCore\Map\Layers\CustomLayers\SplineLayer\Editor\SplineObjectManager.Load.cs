﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public partial class SplineObjectManager
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var objectManager = LoadSplineObjectManager(reader, version);

            reader.Close();

            var editorMap = (Map.currentMap as EditorMap);
            editorMap.SetSplineObjectManager(objectManager);
        }

        static MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader, Version version)
        {
            int n = reader.ReadInt32();
            MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[n];
            for (int i = 0; i < n; ++i)
            {
                float changeZoom = reader.ReadSingle();
                var changeZoomThreshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                MapLayerLODConfigFlag flag = MapLayerLODConfigFlag.None;
                if (version.minorVersion >= 3)
                {
                    flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                }
                string name = "";
                if (version.minorVersion >= 3)
                {
                    name = Utils.ReadString(reader);
                }
                else
                {
                    name = Map.currentMap.data.lodManager.ConvertZoomToName(changeZoom);
                }
                lods[i] = new MapLayerLODConfig.LOD(name, changeZoom, changeZoomThreshold, hideObject, shaderLOD, useRenderTexture, flag, 0);
            }
            var config = new MapLayerLODConfig(Map.currentMap, lods);
            return config;
        }

        static SplineObjectManager LoadSplineObjectManager(BinaryReader reader, Version version)
        {
            var lodConfig = LoadMapLayerLODConfig(reader, version);
            float displayRadius = 0.5f;
            if (version.minorVersion >= 5)
            {
                displayRadius = reader.ReadSingle();
            }
            SplineObjectManager manager = new SplineObjectManager(lodConfig, displayRadius);
            manager.maxExportSegmentLength = reader.ReadSingle();

            if (version.minorVersion >= 2)
            {
                manager.visible = reader.ReadBoolean();
            }
            if (version.minorVersion >= 9)
            {
                manager.riverPrefabFolder = Utils.ReadString(reader);
                manager.generateObjFile = reader.ReadBoolean();
            }

            int objectCount = reader.ReadInt32();
            for (int i = 0; i < objectCount; ++i)
            {
                float width = reader.ReadSingle();
                int tileCount = 1;
                if (version.minorVersion >= 8)
                {
                    tileCount = reader.ReadInt32();
                }
                string materialPath = AssetDatabase.GUIDToAssetPath(Utils.ReadString(reader));
                int nControlPoints = reader.ReadInt32();
                List<SplineObject.ControlPoint> controlPoints = new List<SplineObject.ControlPoint>(nControlPoints);
                for (int k = 0; k < nControlPoints; ++k)
                {
                    var cp = new SplineObject.ControlPoint();
                    cp.pos = Utils.ReadVector3(reader);
                    cp.tangents[0] = Utils.ReadVector3(reader);
                    cp.tangents[1] = Utils.ReadVector3(reader);
                    if (version.minorVersion >= 6)
                    {
                        cp.pointCountInSegment = reader.ReadInt32();
                    }
                    if (version.minorVersion >= 7)
                    {
                        cp.width = reader.ReadSingle();
                        cp.color = Utils.ReadColor(reader);
                    }
                    if (version.minorVersion >= 8)
                    {
                        cp.tileIndex = reader.ReadInt32();
                    }
                    controlPoints.Add(cp);
                }

                var attributes = reader.ReadInt32();
                float ratio = reader.ReadSingle();

                //load segments info
                int segmentCount = reader.ReadInt32();
                List<SplineObject.ExportSplineSegmentInfo> segmentsInfo = null;
                for (int s = 0; s < segmentCount; ++s)
                {
                    if (segmentsInfo == null)
                    {
                        segmentsInfo = new List<SplineObject.ExportSplineSegmentInfo>(segmentCount);
                    }
                    SplineObject.ExportSplineSegmentInfo si = new SplineObject.ExportSplineSegmentInfo();
                    si.splineIndex = reader.ReadInt32();
                    si.startControlPointIndex = reader.ReadInt32();
                    si.endControlPointIndex = reader.ReadInt32();
                    si.prefabPath = Utils.ReadString(reader);
                    si.position = Utils.ReadVector3(reader);
                    segmentsInfo.Add(si);
                }

                string name = "";
                if (version.minorVersion >= 9)
                {
                    name = Utils.ReadString(reader);
                }
                SplineObject.RiverData riverData = new SplineObject.RiverData();
                if (version.minorVersion >= 9)
                {
                    riverData = LoadRiverData(reader);
                }
                manager.CreateSplineObject(controlPoints, materialPath, ratio, (SplineObject.Attribute)attributes, segmentsInfo, tileCount, name, riverData);
            }
            return manager;
        }

        static SplineObject.RiverData LoadRiverData(BinaryReader reader)
        {
            SplineObject.RiverData data = new SplineObject.RiverData();
            data.stencilMaskMaterialPath = Utils.ReadString(reader);
            data.waterMaterialPath = Utils.ReadString(reader);
            data.isRiverObject = reader.ReadBoolean();
            data.stripeCount = reader.ReadInt32();
            data.stripeWidth = Utils.ReadFloatList(reader);
            data.stripeDepth = Utils.ReadFloatList(reader);
            return data;
        }

        //load objects in range
        public void LoadObjectsInRange(string dataPath)
        {
            UnityEngine.Debug.Assert(false);
        }
    }
}

#endif