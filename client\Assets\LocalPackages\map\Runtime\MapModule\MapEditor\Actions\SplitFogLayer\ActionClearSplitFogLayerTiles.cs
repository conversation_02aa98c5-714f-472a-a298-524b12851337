﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionClearSplitFogLayerTiles : EditorAction
    {
        public ActionClearSplitFogLayerTiles(int layerID, Vector2Int[] pickedTiles)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as SplitFogLayer;
            mPickedTiles = (Vector2Int[])pickedTiles.Clone();
            mOldTileDatas = new int[4];
            mLayerID = layerID;
            for (int i = 0; i < pickedTiles.Length; ++i)
            {
                var tile = layer.GetTile(pickedTiles[i].x, pickedTiles[i].y);
                if (tile != 0)
                {
                    mOldTileDatas[i] = tile;
                }
                else
                {
                    mOldTileDatas[i] = 0;
                }
            }

            mDescription = string.Format("clear split fog layer {0} tiles", layer.name);
        }

        public override bool Do()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as SplitFogLayer;
            if (layer != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                for (int i = 0; i < 4; ++i)
                {
                    layer.PopTile(mPickedTiles[i].x, mPickedTiles[i].y, mTileIndex[i]);
                }
                return true;

            }
            return false;
        }

        public override bool Undo()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as SplitFogLayer;
            if (layer != null)
            {
                for (int i = 0; i < 4; ++i)
                {
                    if (mOldTileDatas[i] != 0)
                    {
                        layer.SetTile(mPickedTiles[i].x, mPickedTiles[i].y, mOldTileDatas[i]);
                    }
                }
                return true;
            }
            return false;
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        static int[] mTileIndex = new int[] { 1, 2, 4, 8 };

        int[] mOldTileDatas;
        Vector2Int[] mPickedTiles;
        int mLayerID;
        string mDescription;
    }
}

#endif