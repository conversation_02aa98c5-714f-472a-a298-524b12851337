﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    public enum SpriteTemplateType {
        kColorSpriteTemplate,
    }

    public class SpriteTemplate : BaseObject {
        public SpriteTemplate(int id, Map map, string name, int propertySetID, int width, int height, Color32 color)
            : base(id, map) {
            mName = name;
            mPropertySetID = propertySetID;
            mWidth = width;
            mHeight = height;
            mColor = color;
        }

        public override void OnDestroy() {
        }

        public int width { get { return mWidth; } }
        public int height { get { return mHeight; } }
        public Color32 color { get { return mColor; } set { mColor = value; } }
        public override int propertySetID { get { return mPropertySetID; } set { mPropertySetID = value; } }
        public string name { get { return mName; } set { mName = value; } }
        public bool isDefault { set { mIsDefault = value; } get { return mIsDefault; } }

        int mWidth;
        int mHeight;
        Color32 mColor = new Color32(255, 255, 255, 255);
        int mPropertySetID;
        string mName;
        bool mIsDefault = false;
    }
}

#endif