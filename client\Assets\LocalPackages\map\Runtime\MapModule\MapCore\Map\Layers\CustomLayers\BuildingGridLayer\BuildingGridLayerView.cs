﻿#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class BuildingGridLayerView : TextureGridLayerView
    {
        public BuildingGridLayerView(MapLayerData layerData) : base(layerData)
        {
            mGridObject = new GameObject("City Grid");
            var behaviour = mGridObject.AddComponent<BuildingGridBehaviour>();
            behaviour.plane = root.transform.Find("region plane").gameObject;
            mGridObject.transform.SetParent(root.transform);
            var renderer = mGridObject.AddComponent<MeshRenderer>();
            var filter = mGridObject.AddComponent<MeshFilter>();
            renderer.sharedMaterial = new Material(Shader.Find("SLGMaker/DrawGrid"));
            Mesh mesh = new Mesh();
            mesh.vertices = new Vector3[]
            {
                new Vector3(0, 0, 0),
                new Vector3(0, 0, 1),
                new Vector3(1, 0, 1),
                new Vector3(1, 0, 0),
            };
            mesh.uv = new Vector2[]
            {
                Vector2.zero,
                new Vector2(0, 1),
                new Vector2(1, 1),
                new Vector2(1, 0),
            };
            mesh.triangles = new int[]
            {
                0,1,2,0,2,3,
            };
            UpdateGrid(mesh);
            filter.sharedMesh = mesh;
        }

        public override void OnDestroy()
        {
            if (mGridObject != null)
            {
                Utils.DestroyObject(mGridObject.GetComponent<MeshRenderer>().sharedMaterial);
                Utils.DestroyObject(mGridObject.GetComponent<MeshFilter>().sharedMesh);
                Utils.DestroyObject(mGridObject);
            }

            base.OnDestroy();
        }

        public void Show()
        {
            mGridObject.SetActive(true);
        }

        public void Hide()
        {
            mGridObject.SetActive(false);
        }

        public void SetColor(Color color)
        {
            mGridObject.GetComponent<MeshRenderer>().sharedMaterial.SetColor("_GridColour", color);
        }

        public void UpdateGrid()
        {
            var mesh = mGridObject.GetComponent<MeshFilter>().sharedMesh;
            UpdateGrid(mesh);
        }

        void UpdateGrid(Mesh mesh)
        {
            var layerData = mLayerData as BuildingGridLayerData;
            var subLayer = layerData.GetLayer(0);
            int horizontalGridCount = subLayer.horizontalTileCount;
            int verticalGridCount = subLayer.verticalTileCount;
            float totalWidth = subLayer.tileWidth * subLayer.horizontalTileCount;
            float totalHeight = subLayer.tileHeight * subLayer.verticalTileCount;

            var mtl = mGridObject.GetComponent<MeshRenderer>().sharedMaterial;
            mtl.SetFloat("_HorizontalCellNumber", horizontalGridCount);
            mtl.SetFloat("_VerticalCellNumber", verticalGridCount);

            mesh.vertices = new Vector3[]
            {
                new Vector3(0, 0, 0),
                new Vector3(0, 0, totalHeight),
                new Vector3(totalWidth, 0, totalHeight),
                new Vector3(totalWidth, 0, 0),
            };

            mesh.RecalculateBounds();
            var pos = gridPosition;
            mGridObject.transform.position = new Vector3(pos.x, 0.2f, pos.z);
        }

        public Vector3 gridPosition { get { return mGridObject.transform.position; } set { mGridObject.transform.position = value; } }

        GameObject mGridObject;
    }
}


#endif