﻿ 



 
 

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class RuinLayer : ModelLayer, IOverlapObjectAtPosition
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.RuinLayerEditorDataVersion);

            SaveLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveRuinObjectTypes(BinaryWriter writer)
        {
            int n = mRuinObjectTypes.Count;
            writer.Write(n);

            for (int i = 0; i < n; ++i)
            {
                Utils.WriteString(writer, mRuinObjectTypes[i].name);
                Utils.WriteColor(writer, mRuinObjectTypes[i].color);
                Utils.WriteProperties(writer, mRuinObjectTypes[i].properties);
                writer.Write(mRuinObjectTypes[i].colliderRadius);
                string prefabGUID = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(mRuinObjectTypes[i].prefab));
                Utils.WriteString(writer, prefabGUID);

                writer.Write(mRuinObjectTypes[i].bigGridWidth);
                writer.Write(mRuinObjectTypes[i].bigGridHeight);
                writer.Write(mRuinObjectTypes[i].startX);
                writer.Write(mRuinObjectTypes[i].startZ);
                writer.Write(mRuinObjectTypes[i].pointCount);
                writer.Write(mRuinObjectTypes[i].horizontalBigGridCount);
                writer.Write(mRuinObjectTypes[i].verticalBigGridCount);
                Utils.WriteIntList(writer, mRuinObjectTypes[i].invalidCircles);
            }
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Vector3 offset = layerOffset;
            offset.y = layerView.root.transform.position.y;
            Utils.WriteVector3(writer, offset);
            writer.Write(active);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(0.0f/*mBigGridWidth*/);
            writer.Write(0.0f/*mBigGridHeight*/);
            writer.Write(0.0f/*mStartX*/);
            writer.Write(0.0f/*mStartZ*/);
            writer.Write(0);//small grid count
            writer.Write(0/*mPointCount*/);
            writer.Write(0/*mHorizontalBigGridCount*/);
            writer.Write(0);
            Utils.WriteIntList(writer, new List<int>());
            writer.Write(mExportInHierarchyOrder);

            SaveRuinObjectTypes(writer);

            var allObjects = layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveRuinData(writer, objData as ModelData);
            }

            writer.Write(hordeCount);
            writer.Write(cellWidth);
            writer.Write(ruinDisplayRadius);
            writer.Write(IsTextVisible());
            writer.Write(mShareProperties);
        }

        void SaveRuinData(BinaryWriter writer, ModelData data)
        {
            var ruinData = data as RuinData;

            writer.Write(data.GetFlag());
            Utils.WriteVector3(writer, data.GetPosition());
            Utils.WriteVector3(writer, data.GetScale());
            Utils.WriteQuaternion(writer, data.GetRotation());

            writer.Write((int)ruinData.type);
            writer.Write(ruinData.level);
            Utils.WriteString(writer, ruinData.objectType);
            writer.Write(ruinData.radius);

            bool hasObjectProperties = !mShareProperties;
            writer.Write(hasObjectProperties);
            if (hasObjectProperties)
            {
                Utils.WriteProperties(writer, ruinData.properties);
            }
        }
    }
}

#endif