using System;
using System.Security.Cryptography;
using System.Xml;
using UnityEngine;

namespace GameServerConnection
{
    internal static class EncryptionHelper
    {
        /// <summary>
        /// 新版本加密长度
        /// </summary>
        const int KeyIVLen_V1 = 17;

        /// <summary>
        /// 通讯协议版本 v1
        /// </summary>
        const byte PROTOCOL_V1 = 1;

        internal static byte[] GenKeyIV()
        {
            var bytes = new byte[KeyIVLen_V1];
            var rand = new System.Random(DateTime.Now.Millisecond);
            for (var i = 0; i < bytes.Length; ++i)
            {
                bytes[i] = (byte)rand.Next(0, 10);
            }

            //设置版本号

            bytes[0] = PROTOCOL_V1;

            return bytes;
        }

        /// <summary>
        /// RSA的加密函数 
        /// </summary>
        /// <param name="xmlPublicKey">公钥</param>
        /// <param name="EncryptString">待加密的字节数组</param>
        /// <returns></returns>
        internal static bool RsaEncrypt(string xmlPublicKey, byte[] EncryptString, out byte[] result)
        {
            try
            {
                var rsa = new RSACryptoServiceProvider();
                RsaImportParameters(rsa, xmlPublicKey);
                result = rsa.Encrypt(EncryptString, true);
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            result = null;
            return false;
        }

        static void RsaImportParameters(RSACryptoServiceProvider rsa, string xmlString)
        {
            RSAParameters parameters = new RSAParameters();
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(xmlString);
            if (xmlDoc.DocumentElement.Name.Equals("RSAKeyValue"))
            {
                foreach (XmlNode node in xmlDoc.DocumentElement.ChildNodes)
                {
                    switch (node.Name)
                    {
                        case "Modulus": parameters.Modulus = Convert.FromBase64String(node.InnerText); break;
                        case "Exponent": parameters.Exponent = Convert.FromBase64String(node.InnerText); break;
                        case "P": parameters.P = Convert.FromBase64String(node.InnerText); break;
                        case "Q": parameters.Q = Convert.FromBase64String(node.InnerText); break;
                        case "DP": parameters.DP = Convert.FromBase64String(node.InnerText); break;
                        case "DQ": parameters.DQ = Convert.FromBase64String(node.InnerText); break;
                        case "InverseQ": parameters.InverseQ = Convert.FromBase64String(node.InnerText); break;
                        case "D": parameters.D = Convert.FromBase64String(node.InnerText); break;
                    }
                }
            }
            else
            {
                throw new Exception("Invalid XML RSA key.");
            }

            rsa.ImportParameters(parameters);
        }
    }
}