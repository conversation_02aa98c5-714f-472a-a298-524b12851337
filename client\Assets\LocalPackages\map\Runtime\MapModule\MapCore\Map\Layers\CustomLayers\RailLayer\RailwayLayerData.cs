﻿ 



 
 



/*
 * created by wzw at 2019.11.4
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    class LineSegment
    {
        public Vector2 start;
        public Vector2 end;
        //dir = (end - start).normalized
        public Vector2 dir;
    }

    public class RailInfo
    {
        public float railWidth;
        public int railCount;
        public float railPrefabLength;
        public float railTotalLength;
        public Vector3 railwayCenter;
        public bool railAlignCenter = false;
    }

    //使用viewport的外接圆来判断物体的可见性,可能会把不在视野内的物体也包含进来,但是数量可以忽略不记
    public class RailwayLayerData : MapObjectLayerData
    {
        public RailwayLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, ModelLODGroupManager groupManager, KeepScaleConfig scaleConfig, RailInfo railInfo) : base(header, config, map, groupManager)
        {
            if (scaleConfig != null)
            {
                mScaleUpdater = new KeepScaleUpdater(new KeepScaleConfig[] { scaleConfig });
            }
            mRailWidth = railInfo.railWidth;
            mRailCount = railInfo.railCount;
            mRailTotalLength = railInfo.railTotalLength;
            mRailPrefabLength = railInfo.railPrefabLength;
            mRailwayCenter = railInfo.railwayCenter;
            mRailAlignCenter = railInfo.railAlignCenter;

            mRailwayObjects = new List<RailObjectData[]>(mRailCount);
            mRailSegments = new List<LineSegment>(mRailCount);
            for (int i = 0; i < mRailCount; ++i)
            {
                int railPrefabCount = Mathf.CeilToInt(mRailTotalLength / mRailPrefabLength);
                mRailwayObjects.Add(new RailObjectData[railPrefabCount]);

                var rot = GetRailRotation(i);
                var lineSegment = new LineSegment();
                lineSegment.start = Utils.ToVector2(mRailwayCenter);
                lineSegment.end = Utils.ToVector2(mRailwayCenter + rot * Vector3.right * mRailTotalLength);
                lineSegment.dir = lineSegment.end - lineSegment.start;
                lineSegment.dir.Normalize();
                mRailSegments.Add(lineSegment);
            }

            //mDebugger = new GameObject("Railwaylayer Viewport");
            //mDebugger.AddComponent<DrawPolygon>();
        }

        protected override bool IsAbleToAdd(IMapObjectData objectData)
        {
            return true;
        }

        //add一个地图对象的数据
        protected override void OnAddObjectData(IMapObjectData data)
        {
            Debug.Assert(data is RailObjectData);

            bool isVisible = false;

            var ro = data as RailObjectData;

            if (!isLoading)
            {
                isVisible = IsInViewRange(data, Map.currentMap.viewport);
            }

            if (ro.type == RailObjectType.Rail)
            {
                Debug.Assert(ro.segmentIndex >= 0);
                InsertRail(ro);
            }
            else if (ro.type == RailObjectType.Tunnel)
            {
                mTunnels.Add(ro);
            }
            else
            {
                Debug.Assert(false, "Unknown rail object " + ro.type.ToString());
            }

            SetObjectActiveImpl(data, isVisible);
            SetObjectScale(data);
        }

        void SetObjectScale(IMapObjectData data)
        {
            if (mScaleUpdater != null)
            {
                float scale = mScaleUpdater.currentScaleFactor;
                if (mScaleUpdater.currentScaleFactor == 0)
                {
                    scale = mScaleUpdater.UpdateObjectScaleAtHeight();
                }
                if (scale != 0)
                {
                    SetScale(data, scale);
                }
            }
        }

        //remove一个地图对象的数据
        protected override void OnRemoveObjectData(IMapObjectData data)
        {
            var ro = data as RailObjectData;
            if (ro.type == RailObjectType.Rail)
            {
                RemoveRail(data as RailObjectData);
            }
            else if (ro.type == RailObjectType.Tunnel)
            {
                mTunnels.Remove(ro);
            }
        }

        void InsertRail(RailObjectData data)
        {
            if (data.segmentIndex >= 0 && data.segmentIndex < mRailwayObjects[data.railIndex].Length)
            {
                mRailwayObjects[data.railIndex][data.segmentIndex] = data;
            }
        }

        void RemoveRail(RailObjectData data)
        {
            if (data.segmentIndex >= 0 && data.segmentIndex < mRailwayObjects[data.railIndex].Length)
            {
                mRailwayObjects[data.railIndex][data.segmentIndex] = null;
            }
        }

        public override void RefreshObjectsInViewport()
        {
            UpdateViewport(Map.currentMap.viewport, 0);
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            if (mShowTunnels == false)
            {
                //所有tunnel都是一直可见的
                mShowTunnels = true;
                for (int i = 0; i < mTunnels.Count; ++i)
                {
                    SetObjectActive(mTunnels[i], true, currentLOD);
                }
            }

            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }

            if (mLastViewport != newViewport)
            {
                var size = newViewport.size;
                float hw = size.x * 0.5f;
                float hh = size.y * 0.5f;
                float outerRadius = Mathf.Sqrt(hw * hw + hh * hh);
                var center = newViewport.center;

                //var dp = mDebugger.GetComponent<DrawPolygon>();
                //dp.SetCircle(Utils.ToVector3(center), outerRadius);

                float startDistance;
                float endDistance;
                for (int i = 0; i < mRailCount; ++i)
                {
                    bool visible = GetCircleLineSegmentIntersection(center, outerRadius, i, out startDistance, out endDistance);
                    if (visible)
                    {
                        CalculateObjectActiveStateInDistance(i, startDistance, endDistance);
                    }
                    ShowFirstRailSegment(i);
                }

                foreach (var p in mVisibleObjects)
                {
                    var obj = p.Value as RailObjectData;
                    if (obj.visibilityValue != 1 && obj.type == RailObjectType.Rail)
                    {
                        mNextInvisibleObjects.Add(obj);
                    }
                    obj.visibilityValue = 0;
                }

                for (int i = 0; i < mNextInvisibleObjects.Count; ++i)
                {
                    SetObjectActive(mNextInvisibleObjects[i], false, currentLOD);
                }
                mNextInvisibleObjects.Clear();

                for (int i = 0; i < mNextVisibleObjects.Count; ++i)
                {
                    SetObjectActive(mNextVisibleObjects[i], true, currentLOD);
                }
                mNextVisibleObjects.Clear();

                mLastViewport = newViewport;
            }

            return lodChanged;
        }

        bool GetCircleLineSegmentIntersection(Vector2 center, float radius, int railIndex, out float startDistance, out float endDistance)
        {
            startDistance = 0;
            endDistance = 0;
            var segment = mRailSegments[railIndex];
            var d = center - segment.start;
            var perp = d - Vector2.Dot(d, segment.dir) * segment.dir;
            float extendedRadius = radius + mRailWidth * 0.5f;
            if (perp.sqrMagnitude > extendedRadius * extendedRadius)
            {
                //not intersected
                return false;
            }

            var k = segment.start - center;
            var a = Vector2.Dot(segment.dir, segment.dir);
            var b = 2 * Vector2.Dot(k, segment.dir);
            var c = Vector2.Dot(k, k) - radius * radius;
            float delta = b * b - 4 * a * c;
            if (delta < 0)
            {
                //not intersected
                return false;
            }
            delta = Mathf.Sqrt(delta);
            float t0 = (-b - delta) / (2 * a);
            float t1 = (-b + delta) / (2 * a);
            if (t0 >= 0 && t0 <= mRailTotalLength ||
                t1 >= 0 && t1 <= mRailTotalLength ||
                (t0 <= 0 && t1 >= mRailTotalLength))
            {
                startDistance = Mathf.Clamp(t0, 0, mRailTotalLength);
                endDistance = Mathf.Clamp(t1, 0, mRailTotalLength);

                return true;
            }

            return false;
        }

        void CalculateObjectActiveStateInDistance(int railIndex, float startDistance, float endDistance)
        {
            var railObjects = mRailwayObjects[railIndex];
            int startIndex = Mathf.Clamp(Mathf.FloorToInt(startDistance / mRailPrefabLength), 0, railObjects.Length - 1);
            int endIndex = Mathf.Clamp(Mathf.CeilToInt(endDistance / mRailPrefabLength), 0, railObjects.Length - 1);
            for (int i = startIndex; i <= endIndex; ++i)
            {
                if (railObjects[i] != null)
                {
                    if (!mVisibleObjects.ContainsKey(railObjects[i].GetEntityID()))
                    {
                        mNextVisibleObjects.Add(railObjects[i]);
                    }
                    else
                    {
                        railObjects[i].visibilityValue = 1;
                    }
                }
            }
        }

        void ShowFirstRailSegment(int railIndex)
        {
            var railObjects = mRailwayObjects[railIndex];
            if (railObjects[0] != null)
            {
                if (!mVisibleObjects.ContainsKey(railObjects[0].GetEntityID()))
                {
                    mNextVisibleObjects.Add(railObjects[0]);
                }
                else
                {
                    railObjects[0].visibilityValue = 1;
                }
            }
        }

        //对象是否在视野中
        public bool IsInViewRange(IMapObjectData data, Rect viewport)
        {
            var bounds = data.GetBounds();
            var dataMin = bounds.min;
            var dataMax = bounds.max;

            return !(dataMax.x < viewport.xMin || dataMax.y < viewport.yMin || viewport.xMax < dataMin.x || viewport.yMax < dataMin.y);
        }

        public override IMapObjectData FindObjectAtPosition(Vector3 pos, float radius)
        {
            foreach (var p in mObjects)
            {
                if (p.Value.GetPosition() == pos)
                {
                    return p.Value;
                }
            }
            return null;
        }

        public void UpdateScale()
        {
            if (mScaleUpdater != null)
            {
                float scale = mScaleUpdater.UpdateObjectScaleAtHeight();
                if (scale != 0)
                {
                    foreach (var p in mVisibleObjects)
                    {
                        SetScale(p.Value, scale);
                    }
                }
            }
        }

        public void SetScale(IMapObjectData data, float scale)
        {
            //temp code, only set z scale now
            data.SetScale(new Vector3(1, 1, scale));
            if (mOnObjectScaleChangeCallback != null)
            {
                mOnObjectScaleChangeCallback(data);
            }
        }

        public override bool isGameLayer => true;

        public float GetRailAngle(int railIndex)
        {
            float deltaAngle = 360.0f / mRailCount;
            float railAngle = railIndex * deltaAngle + 180.0f;
            return railAngle;
        }

        public Quaternion GetRailRotation(int railIndex)
        {
            float angle = GetRailAngle(railIndex);
            return Quaternion.Euler(0, angle, 0);
        }

        public int GetRailSegmentIndex(Vector3 worldPos, float railPrefabLength)
        {
            double dx = worldPos.x - mRailwayCenter.x;
            double dz = worldPos.z - mRailwayCenter.z;
            double distance = System.Math.Sqrt(dx * dx + dz * dz);
            //var distancef = (worldPos - mRailwayCenter).magnitude;
            //int railSegmentIdx = Mathf.FloorToInt(distance / railPrefabLength);
            int railSegmentIndex = (int)System.Math.Floor(distance / (double)railPrefabLength);
            return railSegmentIndex;
        }

        public void GetRotationAndPosition(int railIndex, Vector3 worldPos, float railPrefabLength, out Vector3 pos, out Quaternion rot)
        {
            int railSegmentIdx = GetRailSegmentIndex(worldPos, railPrefabLength);
            GetRotationAndPosition(railIndex, railSegmentIdx, railPrefabLength, out pos, out rot);
        }

        public void GetRotationAndPosition(int railIndex, int railSegmentIdx, float railPrefabLength, out Vector3 pos, out Quaternion rot)
        {
            float railAngle = GetRailAngle(railIndex);
            rot = Quaternion.Euler(0, railAngle, 0);
            float offset = 0;
            if (mRailAlignCenter)
            {
                offset = railPrefabLength * 0.5f;
            }
            pos = mRailwayCenter + rot * new Vector3(railSegmentIdx * railPrefabLength + offset, 0, 0);
        }

        public int CalculateRailSegmentIndex(Vector3 pos)
        {
            return GetRailSegmentIndex(pos, mRailPrefabLength);
        }

        public RailObjectData[] GetRailObjects(int railIndex)
        {
            if (railIndex >= 0 && railIndex < mRailwayObjects.Count)
            {
                return mRailwayObjects[railIndex];
            }
            return null;
        }

        public override void GetObjectInBounds(Bounds bounds, List<IMapObjectData> objects)
        {
            Debug.Assert(false, "todo");
        }

        public float railWidth { get { return mRailWidth; } set { mRailWidth = value; } }
        public int railCount { get { return mRailCount; } set { mRailCount = value; } }
        public float railTotalLength
        {
            get
            {
                return mRailTotalLength;
            }
            set
            {
                mRailTotalLength = value;
            }
        }
        public float railPrefabLength { get { return mRailPrefabLength; } set { mRailPrefabLength = value; } }
        public Vector3 railwayCenter { get { return mRailwayCenter; } set { mRailwayCenter = value; } }

        //缩放物体
        bool mShowTunnels = false;
        KeepScaleUpdater mScaleUpdater;
        float mLastZoom;
        Rect mLastViewport;

        float mRailWidth;
        int mRailCount;
        float mRailTotalLength;
        float mRailPrefabLength;
        Vector3 mRailwayCenter;
        bool mRailAlignCenter = false;

        List<LineSegment> mRailSegments = new List<LineSegment>();
        //只包含铁轨,从地图中心点开始
        List<RailObjectData[]> mRailwayObjects = new List<RailObjectData[]>();
        List<RailObjectData> mTunnels = new List<RailObjectData>();
        //中间计算变量
        List<RailObjectData> mNextVisibleObjects = new List<RailObjectData>(100);
        List<RailObjectData> mNextInvisibleObjects = new List<RailObjectData>(100);

        //temp code
        //GameObject mDebugger;
    };
}