﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum TexturePackerStrategyType
    {
        MaxRects,
        EqualRects,
    }

    class MyTexturePacker
    {
        public MyTexturePacker(TexturePackerStrategyType strategy)
        {
            if (strategy == TexturePackerStrategyType.MaxRects)
            {
                mStrategy = new MaxRects();
            }
            else if (strategy == TexturePackerStrategyType.EqualRects)
            {
                mStrategy = new EqualRects();
            }
            else
            {
                Debug.Assert(false, "Unknown strategy!");
            }
        }

        public void OnDestroy()
        {
            mStrategy.OnDestroy();
        }

        public int GetMaxPackCountInOneTexture(int textureSize, int borderSize, int padding, int maxTextureSize)
        {
            return mStrategy.GetMaxPackCountInOneTexture(textureSize, borderSize, padding, maxTextureSize);
        }

        public bool CanPackInOneTexture(TexturePackerSetting setting, List<TexturePackerItem> textures, bool sortInput, out TexturePackResult output)
        {
            return mStrategy.CanPackInOneTexture(setting, textures, sortInput, out output);
        }

        public bool CanPackInOneTexture(int maxTextureSize, int borderSize, int padding, TexturePackerItem[] textures, bool sortInput, out TexturePackResult output)
        {
            var setting = new TexturePackerSetting();
            setting.borderSize = borderSize;
            setting.enableRotate = false;
            setting.onlyPackOneTexture = true;
            setting.padding = padding;
            setting.inputTextureHeight = textures[0].texture.height;
            setting.inputTextureWidth = textures[0].texture.width;
            setting.atlasTextureMaxHeight = maxTextureSize;
            setting.atlasTextureMaxWidth = maxTextureSize;
            List<TexturePackerItem> textureList = new List<TexturePackerItem>();
            textureList.AddRange(textures);
            return mStrategy.CanPackInOneTexture(setting, textureList, sortInput, out output);
        }

        public bool Pack(int maxTextureSize, int borderSize, int padding, bool rgbTextureAlphaIsOne, TexturePackerItem[] textures, bool sortInput, out TexturePackResult output)
        {
            var setting = new TexturePackerSetting();
            setting.borderSize = borderSize;
            setting.enableRotate = false;
            setting.onlyPackOneTexture = false;
            setting.padding = padding;
            setting.atlasTextureMaxHeight = maxTextureSize;
            setting.atlasTextureMaxWidth = maxTextureSize;
            setting.inputTextureHeight = textures[0].texture.height;
            setting.inputTextureWidth = textures[0].texture.width;
            setting.backgroundColor = new Color(0, 0, 0, 0);
            setting.rgbTextureAlphaIsOne = rgbTextureAlphaIsOne;
            List<TexturePackerItem> textureList = new List<TexturePackerItem>();
            textureList.AddRange(textures);
            return mStrategy.Pack(setting, textureList, sortInput, false, out output);
        }

        public bool Pack(TexturePackerSetting setting, List<TexturePackerItem> textures, bool sortInput, out TexturePackResult output)
        {
            return mStrategy.Pack(setting, textures, sortInput, false, out output);
        }

        TexturePackStrategy mStrategy;
    }
}


#endif