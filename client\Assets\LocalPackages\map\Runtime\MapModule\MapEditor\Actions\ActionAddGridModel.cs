﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map {
    [Black]
    public class ActionAddGridModel : EditorAction {
        public ActionAddGridModel(int layerID, int objectID, int modelTemplateID, Quaternion rotation, Vector3 position, Vector3 scale, bool useTextureModel) {
            mLayerID = layerID;
            mObjectID = objectID;
            mModelTemplateID = modelTemplateID;
            mRotation = rotation;
            mPosition = position;
            mScale = scale;
            mUseTextureModel = useTextureModel;
            var modelTemplate = Map.currentMap.FindObject(modelTemplateID) as ModelTemplate;
            var prefabName = Path.GetFileName(modelTemplate.GetLODPrefabPath(0));
            mDescription = string.Format("add {0}", prefabName);
        }

        public override bool Do() {
            var map = Map.currentMap;
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null) {
                var layer = map.GetMapLayerByID(mLayerID) as EditorGridModelLayer;
                if (layer != null) {
                    layer.AddObject(modelTemplate.GetLODPrefabPath(0), mPosition, mRotation, mScale, mObjectID, mUseTextureModel);
                    return true;
                }
            }
            return false;
        }

        public override bool Undo() {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as EditorGridModelLayer;
            if (layer != null) {
                layer.RemoveObject(mObjectID);
                return true;
            }
            
            return false;
        }

        public override string description { get { return mDescription; } }

        int mLayerID;
        int mObjectID;
        int mModelTemplateID;
        Quaternion mRotation;
        Vector3 mPosition;
        Vector3 mScale;
        string mDescription;
        bool mUseTextureModel;
    }
}

#endif