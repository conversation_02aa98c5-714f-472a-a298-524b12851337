﻿ 



 
 


using System.Collections;
using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEditor;


namespace TFW.Map
{
    public interface IBlendTerrainLayer
    {
        bool HasTile(int x, int y);
        bool UpdateViewport(Rect viewport, float zoom);
        void UpdateLODTransition();
        int GetCurrentLOD();
        int GetLODFromCameraHeight(float cameraHeight);
        void SetLODSwitchHandler(IGroundLayerLODSwitchTransitionHandler handler);
        void SetLODSwitchHeight(float cameraHeight);
        void UpdateTerrainGeneratedLODHeight(int lod, float height);
        int verticalTileCount { get; }
        int horizontalTileCount { get; }
        float tileWidth { get; }
        float tileHeight { get; }
        MapLayerData layerData { get; }
        MapLayerView layerView { get; }
        Vector3 FromCoordinateToWorldPosition(int x, int y);
        string GetTilePrefabPath(int x, int y, int lod);
        List<string> GetUsedTilePrefabs();
        void GetTerrainTileMeshAndGameObject(int x, int y, out Mesh mesh, out GameObject gameObject);
        Vector3 layerOffset { get; }
        MapLayerLODConfig lodConfig { get; }
        bool supportGeneratingLOD { get; }
        bool active { set; get; }
    }

    internal enum TerrainTileEdgeCorner
    {
        LeftTopCorner,
        TopEdge,
        RightTopCorner,
        RightEdge,
        RightBottomCorner,
        BottomEdge,
        LeftBottomCorner,
        LeftEdge,
    }

    /// <summary>
    /// 使用拼接规则的地表层,lod使用render texture来优化game object数量,适用于相机可升到超高的超大地图
    /// </summary>
    public partial class BlendTerrainLayer : MapLayerBase, IBlendTerrainLayer
    {
        public BlendTerrainLayer(Map map) : base(map)
        {
#if UNITY_EDITOR
            if (map != null && map.isEditorMode)
            {
                (map as EditorMap).exportMapEvent -= ProcessBeforeExport;
                (map as EditorMap).exportMapEvent += ProcessBeforeExport;
            }
#endif
        }

        /// <summary>
        /// 加载地图层
        /// </summary>
        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            //UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load 01");
            var sourceLayer = layerData as config.BlendTerrainLayerData;
            if (sourceLayer == null)
            {
                //UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load 02");
                return;
            }
            //UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load 03");
            int rows = sourceLayer.rows;
            int cols = sourceLayer.cols;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth,
                sourceLayer.tileHeight, GridType.Rectangle,
                sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            int lodCount = 1;
            if (layerData.config != null)
            {
                lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold,
                        srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag,
                        srcLOD.terrainLODTileCount);
                }

                config = new MapLayerLODConfig(map, lods);
            }
            //UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load 04");
            List<ModelData[,]> lodTiles = null;
            TerrainRenderTextureLODSetting[] lodSetting = null;
            if (map.isEditorMode == false && sourceLayer.useGeneratedLOD && lodCount > 1)
            {
                var assetPath = map.dataFolder + "/Res/terrain_lod_data_ex.bytes";
                if (MapModuleResourceMgr.Exists(assetPath))
                {
                    LoadLODDataNew(assetPath, out lodTiles, out lodSetting);
                }
                else
                {
                    assetPath = map.dataFolder + "/Res/terrain_lod_data.bytes";
                    LoadLODData(assetPath, out lodTiles, out lodSetting);
                }
            }

            mLayerData = new RenderTextureBlendTerrainLayerData(header, config, map, null, lodTiles, lodSetting,
                sourceLayer.useGeneratedLOD, sourceLayer.useDecorationObject, sourceLayer.texturePropertyName,
                sourceLayer.groundTileAtlasSettingGuid, sourceLayer.generateMeshCollider,
                sourceLayer.getGroundHeightInGame, sourceLayer.optimizeMesh);

            mLayerData.isLoading = true;

            bool useHeight = sourceLayer.getGroundHeightInGame;
            if (map.isEditorMode)
            {
                useHeight = true;
            }

            if (sourceLayer.tiles != null)
            {
                for (int y = 0; y < rows; ++y)
                {
                    for (int x = 0; x < cols; ++x)
                    {
                        int idx = y * cols + x;
                        if (sourceLayer.tiles[idx] is config.BlendTerrainTileData tile)
                        {
                            PushTile(x, y, tile.index, tile.type, tile.subTypeIndex, false, false,
                                useHeight ? tile.heights : null, tile.heights != null);
                        }
                    }
                }
            }

            mLayerData.ResetIsChangedFlags();

            var layerView = new BlendTerrainLayerView(mLayerData, false);
            mLayerView = layerView;
            mLayerView.active = layerData.active;
            mLayerData.SetTerrainObjectActiveCallback(mLayerView.OnObjectActiveStateChange);

            var pos = mLayerView.root.transform.position;
            pos.y = sourceLayer.layerPosition.y;
            mLayerView.root.transform.position = pos;

            if (sourceLayer.useTileHeight)
            {
                layerView.PreprocessHeightMeshies(sourceLayer.usedTilePrefabPaths);
            }

            mLayerData.isLoading = false;

            map.AddMapLayer(this);
        }

        /// <summary>
        /// 异步加载地图
        /// </summary>
        public override IEnumerator LoadAsync(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var sourceLayer = layerData as config.BlendTerrainLayerData;
            if (sourceLayer == null)
            {
                yield break;
            }

            var rows = sourceLayer.rows;
            var cols = sourceLayer.cols;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth,
                sourceLayer.tileHeight, GridType.Rectangle,
                sourceLayer.origin + (setting?.origin ?? Vector3.zero));

            // 读取LOD配置信息
            MapLayerLODConfig config = null;
            var lodCount = 1;
            if (layerData.config != null)
            {
                lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold,
                        srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag,
                        srcLOD.terrainLODTileCount);
                }

                config = new MapLayerLODConfig(map, lods);
            }

            List<ModelData[,]> lodTiles = null;
            TerrainRenderTextureLODSetting[] lodSetting = null;

            if (map.isEditorMode == false && sourceLayer.useGeneratedLOD && lodCount > 1)
            { 
                var assetPath = map.dataFolder + "/Res/terrain_lod_data_ex.bytes";
                if (MapModuleResourceMgr.Exists(assetPath))
                {
                    LoadLODDataNew(assetPath, out lodTiles, out lodSetting);
                }
                else
                {
                    assetPath = map.dataFolder + "/Res/terrain_lod_data.bytes";

                    if (MapModuleResourceMgr.Exists(assetPath))
                    {
                        yield return LoadLODDataIE(assetPath, lodTiles, lodSetting);
                    }
                    else
                    {
                        //当前地图无资源 使用Main
                        assetPath = "Assets/Res/map/Main/Res/terrain_lod_data.bytes";
                        yield return LoadLODDataIE(assetPath, lodTiles, lodSetting);
                    }
                }
            }

            mLayerData = new RenderTextureBlendTerrainLayerData(header, config, map, null, lodTiles, lodSetting,
                sourceLayer.useGeneratedLOD, sourceLayer.useDecorationObject, sourceLayer.texturePropertyName,
                sourceLayer.groundTileAtlasSettingGuid, sourceLayer.generateMeshCollider,
                sourceLayer.getGroundHeightInGame, sourceLayer.optimizeMesh)
            {
                isLoading = true
            };

            // 异步加载Tiles 
            PushTiles(sourceLayer); 
 
            mLayerData.ResetIsChangedFlags();

            var layerView = new BlendTerrainLayerView(mLayerData, false);
            mLayerView = layerView;
            mLayerView.active = layerData.active;
            mLayerData.SetTerrainObjectActiveCallback(mLayerView.OnObjectActiveStateChange);

            var pos = mLayerView.root.transform.position;
            pos.y = sourceLayer.layerPosition.y;
            mLayerView.root.transform.position = pos;

            if (sourceLayer.useTileHeight)
            {
                layerView.PreprocessHeightMeshies(sourceLayer.usedTilePrefabPaths);
            }

            mLayerData.isLoading = false;

            map.AddMapLayer(this);
        }

        private void PushTiles(config.BlendTerrainLayerData sourceLayer)
        {
            var useHeight = sourceLayer.getGroundHeightInGame || map.isEditorMode;
            if (sourceLayer.tiles != null)
            {
                for (var y = 0; y < sourceLayer.rows; ++y)
                {
                    for (var x = 0; x < sourceLayer.cols; ++x)
                    {
                        var idx = y * sourceLayer.cols + x;
                        if (sourceLayer.tiles[idx] is config.BlendTerrainTileData tile)
                        {
                            PushTile(x, y, tile.index, tile.type, tile.subTypeIndex, false, false,
                                useHeight ? tile.heights : null, tile.heights != null);
                        }

                        
                    }
                }
            }
        }

        /// <summary>
        /// <inheritdoc />
        /// </summary>
        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }

            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }

            if (mTransition != null)
            {
                mTransition.OnDestroy();
            }

#if UNITY_EDITOR
            if (map.isEditorMode)
            {
                (map as EditorMap).exportMapEvent -= ProcessBeforeExport;
            }

            mRenderTextures?.OnDestroy();
#endif
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        void LoadLODData(string path, out List<ModelData[,]> lodTiles, out TerrainRenderTextureLODSetting[] lodSettings)
        {
            lodSettings = null;
            lodTiles = null;

            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream == null)
            {
                return;
            }

            using BinaryReader reader = new BinaryReader(stream);

            //load prefabs
            List<string> prefabPaths = new List<string>();
            int prefabCount = reader.ReadInt32();
            for (int i = 0; i < prefabCount; ++i)
            {
                prefabPaths.Add(Utils.ReadString(reader));
            }

            //load lod tile data
            lodTiles = new List<ModelData[,]>();
            int lodCount = reader.ReadInt32();
            for (int i = 0; i < lodCount; ++i)
            {
                int rows = reader.ReadInt32();
                int cols = reader.ReadInt32();
                ModelData[,] tiles = new ModelData[rows, cols];
                lodTiles.Add(tiles);

                for (int r = 0; r < rows; ++r)
                {
                    for (int c = 0; c < cols; ++c)
                    {
                        var position = Utils.ReadVector3(reader);
                        var prefabIndex = reader.ReadInt32();
                        int objectDataID = map.nextCustomObjectID;
                        var modelTemplate =
                            map.GetOrCreateModelTemplate(objectDataID, prefabPaths[prefabIndex], false, true);
                        var tile = new ModelData(objectDataID, map, 0, position,
                            MapModule.wrongGroundLODOrientation ? Quaternion.Euler(0, 180, 0) : Quaternion.identity,
                            Vector3.one, modelTemplate, false);
                        tiles[r, c] = tile;
                    }
                }
            }

            //load lod settings
            int lodSettingCount = reader.ReadInt32();
            lodSettings = new TerrainRenderTextureLODSetting[lodSettingCount];
            for (int i = 0; i < lodSettingCount; ++i)
            {
                float cameraHeight = reader.ReadSingle();
                int blockSize = reader.ReadInt32();
                int realBlockSize = reader.ReadInt32();
                var setting = new TerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
                setting.realBlockSize = realBlockSize;
                lodSettings[i] = setting;
            }

            reader.Close();

            //preload prefabs
            //for (int i = 0; i < prefabPaths.Count; ++i)
            //{
            //    var prefab = MapModuleResourceMgr.LoadPrefab(prefabPaths[i]);
            //    Debug.Assert(prefab != null);
            //}
        }


        IEnumerator LoadLODDataIE(string path, List<ModelData[,]> lodTiles, TerrainRenderTextureLODSetting[] lodSettings)
        {
            lodSettings = null;
            lodTiles = null;

            Stream stream = null;
            bool loaded = false;
             MapModuleResourceMgr.LoadTextStreamAsync(path, (str, _stream) => {
                 stream = _stream;
                 loaded= true;
             });
           
            while (!loaded)
            {
                yield return null;
            }

            using BinaryReader reader = new BinaryReader(stream);

            //load prefabs
            List<string> prefabPaths = new List<string>();
            int prefabCount = reader.ReadInt32();
            for (int i = 0; i < prefabCount; ++i)
            {
                prefabPaths.Add(Utils.ReadString(reader));
            }

            //load lod tile data
            lodTiles = new List<ModelData[,]>();
            int lodCount = reader.ReadInt32();
            for (int i = 0; i < lodCount; ++i)
            {
                int rows = reader.ReadInt32();
                int cols = reader.ReadInt32();
                ModelData[,] tiles = new ModelData[rows, cols];
                lodTiles.Add(tiles);

                for (int r = 0; r < rows; ++r)
                {
                    for (int c = 0; c < cols; ++c)
                    {
                        var position = Utils.ReadVector3(reader);
                        var prefabIndex = reader.ReadInt32();
                        int objectDataID = map.nextCustomObjectID;
                        int x = r; int y=c;

                        loaded = false;

                        map.GetOrCreateModelTemplateAsync(objectDataID, prefabPaths[prefabIndex], false, true, (modelTemplate) =>
                        {
                            try
                            {
                                var tile = new ModelData(objectDataID, map, 0, position,
                                MapModule.wrongGroundLODOrientation ? Quaternion.Euler(0, 180, 0) : Quaternion.identity,
                                Vector3.one, modelTemplate, false);
                                tiles[x, y] = tile;

                                loaded = true;
                            }
                            catch(System.Exception ex) { Debug.LogException(ex); }
                        });


                        while (!loaded)
                        {
                            yield return null;
                        }
                    }
                }
            }
        
            //load lod settings
            int lodSettingCount = reader.ReadInt32();
            lodSettings = new TerrainRenderTextureLODSetting[lodSettingCount];
            for (int i = 0; i < lodSettingCount; ++i)
            {
                float cameraHeight = reader.ReadSingle();
                int blockSize = reader.ReadInt32();
                int realBlockSize = reader.ReadInt32();
                var setting = new TerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
                setting.realBlockSize = realBlockSize;
                lodSettings[i] = setting;
            }

            reader.Close();
            stream.Close();
            stream.Dispose();
            reader.Dispose();
            //preload prefabs
            //for (int i = 0; i < prefabPaths.Count; ++i)
            //{
            //    MapModuleResourceMgr.LoadGameObjectAsync(prefabPaths[i],null, (path, prefab) => {
            //        Debug.Assert(prefab != null);
            //    }); 
            //}
        }


        void LoadLODDataNew(string path, out List<ModelData[,]> lodTiles,
            out TerrainRenderTextureLODSetting[] lodSettings)
        {
            lodSettings = null;
            lodTiles = null;

            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream == null)
            {
                return;
            }

            using BinaryReader reader = new BinaryReader(stream);

            int version = reader.ReadInt32();
            bool wrongLODOrientation = reader.ReadBoolean();

            //load prefabs
            List<string> prefabPaths = new List<string>();
            int prefabCount = reader.ReadInt32();
            for (int i = 0; i < prefabCount; ++i)
            {
                prefabPaths.Add(Utils.ReadString(reader));
            }

            //load lod tile data
            lodTiles = new List<ModelData[,]>();
            int lodCount = reader.ReadInt32();
            for (int i = 0; i < lodCount; ++i)
            {
                int rows = reader.ReadInt32();
                int cols = reader.ReadInt32();
                ModelData[,] tiles = new ModelData[rows, cols];
                lodTiles.Add(tiles);

                for (int r = 0; r < rows; ++r)
                {
                    for (int c = 0; c < cols; ++c)
                    {
                        var position = Utils.ReadVector3(reader);
                        var prefabIndex = reader.ReadInt32();
                        if (prefabIndex >= 0)
                        {
                            int objectDataID = map.nextCustomObjectID;
                            var modelTemplate =
                                map.GetOrCreateModelTemplate(objectDataID, prefabPaths[prefabIndex], false, true);
                            var tile = new ModelData(objectDataID, map, 0, position,
                                wrongLODOrientation ? Quaternion.Euler(0, 180, 0) : Quaternion.identity, Vector3.one,
                                modelTemplate, false);
                            tiles[r, c] = tile;
                        }
                    }
                }
            }

            //load lod settings
            int lodSettingCount = reader.ReadInt32();
            lodSettings = new TerrainRenderTextureLODSetting[lodSettingCount];
            for (int i = 0; i < lodSettingCount; ++i)
            {
                float cameraHeight = reader.ReadSingle();
                int blockSize = reader.ReadInt32();
                int realBlockSize = reader.ReadInt32();
                var setting = new TerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
                setting.realBlockSize = realBlockSize;
                lodSettings[i] = setting;
            }

            reader.Close();

            //preload prefabs
            //for (int i = 0; i < prefabPaths.Count; ++i)
            //{
            //    var prefab = MapModuleResourceMgr.LoadPrefab(prefabPaths[i]);
            //    Debug.Assert(prefab != null);
            //}
        }

        //void LoadLODDataAsync(string path, System.Action<List<ModelData[,]>, TerrainRenderTextureLODSetting[]> action)
        //{
        //    TerrainRenderTextureLODSetting[]  lodSettings = null;
        //    List<ModelData[,]> lodTiles = null;

        //    var map = Map.currentMap;

        //    List<string> prefabPaths = null;

        //    MapModuleResourceMgr.LoadTextStreamAsync(path,(result)=>
        //    {
        //        var stream = result;
        //        if (stream != null)
        //        {
        //            BinaryReader reader = new BinaryReader(stream);

        //            //load prefabs
        //            prefabPaths = new List<string>();
        //            int prefabCount = reader.ReadInt32();
        //            for (int i = 0; i < prefabCount; ++i)
        //            {
        //                prefabPaths.Add(Utils.ReadString(reader));
        //            }

        //            //load lod tile data
        //            lodTiles = new List<ModelData[,]>();
        //            int lodCount = reader.ReadInt32();
        //            for (int i = 0; i < lodCount; ++i)
        //            {
        //                int rows = reader.ReadInt32();
        //                int cols = reader.ReadInt32();
        //                ModelData[,] tiles = new ModelData[rows, cols];
        //                lodTiles.Add(tiles);

        //                for (int r = 0; r < rows; ++r)
        //                {
        //                    for (int c = 0; c < cols; ++c)
        //                    {
        //                        var position = Utils.ReadVector3(reader);
        //                        var prefabIndex = reader.ReadInt32();
        //                        int objectDataID = map.nextCustomObjectID;
        //                        var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(objectDataID, prefabPaths[prefabIndex], false, true);
        //                        var tile = new ModelData(objectDataID, map, 0, position,
        //                    MapModule.wrongGroundLODOrientation ? Quaternion.Euler(0, 180, 0) : Quaternion.identity,
        //                    Vector3.one, modelTemplate, false);
        //                        tiles[r, c] = tile;
        //                    }
        //                }
        //            }

        //            //load lod settings
        //            int lodSettingCount = reader.ReadInt32();
        //            lodSettings = new TerrainRenderTextureLODSetting[lodSettingCount];
        //            for (int i = 0; i < lodSettingCount; ++i)
        //            {
        //                float cameraHeight = reader.ReadSingle();
        //                int blockSize = reader.ReadInt32();
        //                int realBlockSize = reader.ReadInt32();
        //                var setting = new TerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
        //                setting.realBlockSize = realBlockSize;
        //                lodSettings[i] = setting;
        //            }

        //            reader.Close();
        //        }
        //        MapModuleResourceMgr.UnloadAsset(path);

        //        //preload prefabs
        //        if (prefabPaths != null)
        //        {
        //            for (int i = 0; i < prefabPaths.Count; ++i)
        //            {
        //                var prefab = MapModuleResourceMgr.LoadPrefab(prefabPaths[i]);
        //                if(prefab == null)
        //                    UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate prefab null");
        //                Debug.Assert(prefab != null);
        //            }
        //        }
        //        action.Invoke(lodTiles, lodSettings);
        //    });
            
        //}

        //直接设置tile的信息,不使用拼接的规则
        public void SetTile(int x, int y, int tileIndex, int tileType, int subTypeIndex)
        {
            if (tileIndex == 0)
            {
                //清理tile
                var tile = mLayerData.GetTile(x, y);
                if (tile != null)
                {
                    bool isVisible = tile.IsObjActive();
                    if (isVisible)
                    {
                        mLayerView.HideObject(tile, x, y, 0);
                    }

                    mLayerData.ClearTile(x, y);
                }
            }
            else
            {
                //直接设置这个tile使用的拼接后的数据
                var tile = mLayerData.GetTile(x, y);
                if (tile != null && tile.IsObjActive())
                {
                    mLayerView.HideObject(tile, x, y, 0);
                }

                tile = mLayerData.PushTile(x, y, tileIndex, tileType, subTypeIndex, true, true, null, false);
                if (tile != null)
                {
#if UNITY_EDITOR
                    CalculateTileUV(tile);
#endif
                    if (tile.IsObjActive())
                    {
                        mLayerView.ShowObject(tile, x, y, 0);
                    }
                }
            }

#if UNITY_EDITOR
            if (x >= 0 && x < horizontalTileCount && y >= 0 && y < verticalTileCount)
            {
                mRenderTextures?.OnSmallTileChange(x, y);
            }
#endif
        }

        public bool GetPushTileResult(int x, int y, int tileIndex, int tileType, bool changeRandomTile,
            List<int> excludedTileIndices, out int combindTileIndex, out int subTypeIndex)
        {
            return mLayerData.GetPushTileResult(x, y, tileIndex, tileType, changeRandomTile, excludedTileIndices,
                out combindTileIndex, out subTypeIndex);
        }

        //拼接tile,让该tile可以和四周的tile无缝衔接
        public void PushTile(int x, int y, int tileIndex, int tileType, int subTypeIndex, bool changeRandomTile,
            bool setAbsValue, float[] heights, bool hasHeightData)
        {
            var tile = mLayerData.GetTile(x, y);
            if (tile != null && tile.IsObjActive())
            {
                if (mLayerView != null)
                {
                    mLayerView.HideObject(tile, x, y, 0);
                }
            }

            tile = mLayerData.PushTile(x, y, tileIndex, tileType, subTypeIndex, setAbsValue, changeRandomTile, heights,
                hasHeightData);
            if (tile != null)
            {
#if UNITY_EDITOR
                //计算
                CalculateTileUV(tile);
#endif
                //要用新的game object替换,所以删除旧的prefab
                if (mLayerView != null && tile.IsObjActive())
                {
                    mLayerView.ShowObject(tile, x, y, 0);
                }
            }

#if UNITY_EDITOR
            if (x >= 0 && x < horizontalTileCount && y >= 0 && y < verticalTileCount)
            {
                mRenderTextures?.OnSmallTileChange(x, y);
            }
#endif
        }

        public void PushTile(Vector3 worldPos, int tileType)
        {
            PickTiles(worldPos);

            for (int i = 0; i < 4; ++i)
            {
                int x = mPickedTiles[i].x;
                int y = mPickedTiles[i].y;
                PushTile(x, y, mTileIndex[i], tileType, 0, true, false, null, false);
            }
        }

        public void PopTile(Vector3 worldPos)
        {
            PickTiles(worldPos);

            for (int i = 0; i < 4; ++i)
            {
                int x = mPickedTiles[i].x;
                int y = mPickedTiles[i].y;
                PopTile(x, y, mTileIndex[i]);
            }
        }

        //撤销拼接操作,该操作过后地表的tile还是衔接好的
        public void PopTile(int x, int y, int tileIndex)
        {
            var tile = mLayerData.GetTile(x, y);
            if (tile != null)
            {
                //删除旧的tile模型
                if (tile.IsObjActive())
                {
                    mLayerView.HideObject(tile, x, y, 0);
                }

                int curIndex = mLayerData.PopTile(x, y, tileIndex);
                if (curIndex > 0)
                {
                    if (tile.IsObjActive())
                    {
                        //显示更新后的tile模型
                        mLayerView.ShowObject(tile, x, y, 0);
                    }
                }

#if UNITY_EDITOR
                if (x >= 0 && x < horizontalTileCount && y >= 0 && y < verticalTileCount)
                {
                    mRenderTextures?.OnSmallTileChange(x, y);
                }
#endif
            }
        }

        //获取tile数据
        public BlendTerrainTileData GetTile(int x, int y)
        {
            return mLayerData.GetTile(x, y);
        }

        //返回地图的根game object
        public override GameObject gameObject
        {
            get { return mLayerView.root; }
        }

        public override int id
        {
            get { return mLayerData.id; }
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateLOD(newCameraZoom);

            if (!lodChanged)
            {
                mLayerData.UpdateViewport(newViewport);
            }
            else
            {
                mLayerData.OnLODChanged(mLayerData.lastLOD, mLayerData.currentLOD, newViewport);
                OnLODChange(mLayerData.lastLOD, mLayerData.currentLOD, map.viewCenter);
            }

            return lodChanged;
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            //use MoveAndResize
#if UNITY_EDITOR
            return MoveAndResize(newWidth, newHeight, ResizeAlignment.StayAndOffsetLayer);
#else
            return false;
#endif
        }

        //加载完地图时刷新初始视野中的对象时使用
        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void SetTileResolution(int tileX, int tileY, int resolution, bool updateMesh)
        {
            mLayerData.SetTileResolution(tileX, tileY, resolution);
            if (updateMesh)
            {
                mLayerView.UpdateMesh(tileX, tileY);
            }
        }

        public void SetHeights(int tileX, int tileY, int minX, int minY, int maxX, int maxY, int resolution,
            List<float> heights, bool updateMesh, bool dontChangeEdgeVertexHeight)
        {
            mLayerData.SetHeight(tileX, tileY, minX, minY, maxX, maxY, resolution, heights, dontChangeEdgeVertexHeight);
            if (updateMesh)
            {
                mLayerView.UpdateMesh(tileX, tileY);
            }
        }

        public void SetHeight(int tileX, int tileY, int minX, int minY, int maxX, int maxY, int resolution,
            float height, bool updateMesh, bool dontChangeEdgeVertexHeight)
        {
            mLayerData.SetHeight(tileX, tileY, minX, minY, maxX, maxY, resolution, height, dontChangeEdgeVertexHeight);
            if (updateMesh)
            {
                mLayerView.UpdateMesh(tileX, tileY);
            }
        }

        public void UpdateMesh(int tileX, int tileY)
        {
            mLayerView.UpdateMesh(tileX, tileY);
        }

        public float GetHeightAtPos(float x, float z)
        {
            var coord = mLayerData.FromWorldPositionToCoordinate(new Vector3(x, 0, z));
            var tile = GetTile(coord.x, coord.y);
            if (tile != null)
            {
                if (tile.heights == null)
                {
                    return 0;
                }

                float localX = coord.x * mLayerData.tileWidth + mLayerData.layerOffset.x;
                float localZ = coord.y * mLayerData.tileHeight + mLayerData.layerOffset.z;
                float gridSize = mLayerData.tileWidth / tile.resolution;
                return tile.GetHeightAtPos(x - localX, z - localZ, gridSize);
            }

            return 0;
        }

        public bool IsInValidState()
        {
            return true;
        }

        public void PickTiles(Vector3 worldPos)
        {
            var coord = layerData.FromWorldPositionToCoordinate(worldPos);
            var tileStartPos = layerData.FromCoordinateToWorldPosition(coord.x, coord.y);
            var rx = (worldPos.x - tileStartPos.x) / tileWidth;
            var rz = (worldPos.z - tileStartPos.z) / tileHeight;
            if (rx <= 0.5)
            {
                if (rz <= 0.5)
                {
                    mPickedTiles[0] = new Vector2Int(coord.x - 1, coord.y - 1);
                    mPickedTiles[1] = new Vector2Int(coord.x, coord.y - 1);
                    mPickedTiles[2] = new Vector2Int(coord.x - 1, coord.y);
                    mPickedTiles[3] = new Vector2Int(coord.x, coord.y);
                }
                else
                {
                    mPickedTiles[0] = new Vector2Int(coord.x - 1, coord.y);
                    mPickedTiles[1] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[2] = new Vector2Int(coord.x - 1, coord.y + 1);
                    mPickedTiles[3] = new Vector2Int(coord.x, coord.y + 1);
                }
            }
            else
            {
                if (rz <= 0.5)
                {
                    mPickedTiles[0] = new Vector2Int(coord.x, coord.y - 1);
                    mPickedTiles[1] = new Vector2Int(coord.x + 1, coord.y - 1);
                    mPickedTiles[2] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[3] = new Vector2Int(coord.x + 1, coord.y);
                }
                else
                {
                    mPickedTiles[0] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[1] = new Vector2Int(coord.x + 1, coord.y);
                    mPickedTiles[2] = new Vector2Int(coord.x, coord.y + 1);
                    mPickedTiles[3] = new Vector2Int(coord.x + 1, coord.y + 1);
                }
            }
        }

        public void ClearAllTiles()
        {
            mLayerData.ClearAllTiles();
            mLayerView.Clear();
#if UNITY_EDITOR
            ActionManager.instance.Clear();
#endif
        }

#if UNITY_EDITOR
        public void CreateRenderTextures(int bigTileCount, int textureSize, Shader bigTileShader,
            string texturePropertyName)
        {
            if (mRenderTextures == null)
            {
                mRenderTextures = new BlendTerrainLayerRenderTextures();
            }

            mRenderTextures.Create(mLayerView.root.transform, mLayerData, bigTileCount, textureSize, bigTileShader,
                true, texturePropertyName);
        }

        public bool UpdateRenderTextures(string texturePropertyName)
        {
            if (mRenderTextures != null)
            {
                return mRenderTextures.Update(mLayerView.root.transform, mLayerData, texturePropertyName);
            }

            return false;
        }

        public void ClearRenderTextures(string projectFolder)
        {
            if (mRenderTextures != null)
            {
                mRenderTextures.Clear(projectFolder);
                mRenderTextures = null;
            }
        }

        public void LoadRenderTextures(string texturePropertyName)
        {
            if (mRenderTextures == null)
            {
                mRenderTextures = new BlendTerrainLayerRenderTextures();
            }

            bool suc = mRenderTextures.Load(SLGMakerEditor.instance.projectFolder, texturePropertyName);
            if (!suc)
            {
                mRenderTextures.OnDestroy();
                mRenderTextures = null;
            }
        }

        public void SaveRenderTextures()
        {
            if (mRenderTextures != null)
            {
                mRenderTextures.Save(SLGMakerEditor.instance.projectFolder);
            }
        }
#endif

        public int GetLODFromCameraHeight(float cameraHeight)
        {
            return mLayerData.GetLODFromCameraHeight(cameraHeight);
        }

        public Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPosition(x, y);
        }

        public string GetTilePrefabPath(int x, int y, int lod)
        {
            var tile = mLayerData.GetTile(x, y);
            if (tile != null)
            {
                return tile.GetAssetPath(lod);
            }

            return null;
        }

        public bool HasTile(int x, int y)
        {
            return mLayerData.GetTile(x, y) != null;
        }

        public void UpdateTerrainGeneratedLODHeight(int lod, float newHeight)
        {
            Debug.Assert(false, "todo");
        }

        //获取使用了的prefab path
        public List<string> GetUsedTilePrefabs()
        {
            List<string> prefabPaths = new List<string>();
            int v = verticalTileCount;
            int h = horizontalTileCount;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    var tile = GetTile(j, i);
                    if (tile != null)
                    {
                        string path = tile.GetAssetPath(0);
                        if (!prefabPaths.Contains(path))
                        {
                            prefabPaths.Add(path);
                        }
                    }
                }
            }

            return prefabPaths;
        }

#if UNITY_EDITOR
        //计算这个tile的uv
        void CalculateTileUV(BlendTerrainTileData tile)
        {
            if (map.isEditorMode && MapModule.supportGroundHeightChange)
            {
                var prefabPath = tile.GetAssetPath(0);
                Vector2[] tileUVs;
                mCachedTileUVs.TryGetValue(prefabPath, out tileUVs);
                if (tileUVs == null)
                {
                    var obj = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    if (obj != null)
                    {
                        var meshFilter = obj.GetComponentInChildren<MeshFilter>();
                        if (meshFilter != null)
                        {
                            int vertexCount = meshFilter.sharedMesh.vertexCount;
                            if (vertexCount != 4)
                            {
                                return;
                            }

                            var uv0 = meshFilter.sharedMesh.uv;
                            var positions = meshFilter.sharedMesh.vertices;
                            Debug.Assert(uv0.Length == 4);
                            float minX = Mathf.Min(positions[0].x, positions[1].x, positions[2].x, positions[3].x);
                            float minZ = Mathf.Min(positions[0].z, positions[1].z, positions[2].z, positions[3].z);
                            float maxX = Mathf.Max(positions[0].x, positions[1].x, positions[2].x, positions[3].x);
                            float maxZ = Mathf.Max(positions[0].z, positions[1].z, positions[2].z, positions[3].z);

                            tileUVs = new Vector2[4];

                            for (int i = 0; i < 4; ++i)
                            {
                                if (Utils.Approximately(positions[i].x, minX, 0.001f) &&
                                    Utils.Approximately(positions[i].z, minZ, 0.001f))
                                {
                                    tileUVs[0] = uv0[i];
                                }

                                if (Utils.Approximately(positions[i].x, maxX, 0.001f) &&
                                    Utils.Approximately(positions[i].z, minZ, 0.001f))
                                {
                                    tileUVs[1] = uv0[i];
                                }

                                if (Utils.Approximately(positions[i].x, minX, 0.001f) &&
                                    Utils.Approximately(positions[i].z, maxZ, 0.001f))
                                {
                                    tileUVs[2] = uv0[i];
                                }

                                if (Utils.Approximately(positions[i].x, maxX, 0.001f) &&
                                    Utils.Approximately(positions[i].z, maxZ, 0.001f))
                                {
                                    tileUVs[3] = uv0[i];
                                }
                            }

                            mCachedTileUVs[prefabPath] = tileUVs;
                        }
                    }
                }

                tile.uvs = tileUVs;
            }
        }

        public List<CollisionSegmentInfo> GetCollisionSegments()
        {
            return mLayerView.GetCollisionSegments();
        }

        public CollisionSegment GetCollisionSegment(int x, int y)
        {
            return mLayerView.GetCollisionSegment(x, y);
        }

        public void ApplySegmentChangeToPrefab()
        {
            mLayerView.ApplySegmentChangeToPrefab();
        }
#endif

        public void GetTerrainTileMeshAndGameObject(int x, int y, out Mesh mesh, out GameObject gameObject)
        {
            mLayerView.GetTerrainTileMeshAndGameObject(x, y, out mesh, out gameObject);
        }

        //返回地图层的总宽度
        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter();
        }

        //返回地图层的总高度
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter();
        }

        //地图层的名称
        public override string name {
            get
            {
                return mLayerData?.name;
            }
            set
            {
                mLayerData.name = value;
            }
        }

        //地图层的格子类型
        public override GridType gridType => GridType.Rectangle;

        //x方向上格子的数量
        public override int horizontalTileCount => mLayerData.horizontalTileCount;

        //z方向上格子的数量
        public override int verticalTileCount => mLayerData.verticalTileCount;

        //格子的宽
        public override float tileWidth => mLayerData.tileWidth;

        //格子的高
        public override float tileHeight => mLayerData.tileHeight;

        //地图层的偏移值
        public override Vector3 layerOffset => mLayerData.layerOffset;

        public bool asyncLoading
        {
            get => mLayerView.asyncLoading;
            set => mLayerView.asyncLoading = value;
        }

        public override bool active
        {
            get => mLayerView.active;
            set => mLayerView.active = value;
        }

        public override int lodCount => mLayerData.lodCount;

        public MapLayerLODConfig lodConfig => mLayerData.lodConfig;

        public bool supportGeneratingLOD => true;

        public MapLayerData layerData => mLayerData;

        public MapLayerView layerView => mLayerView;

        //拼接地表层的tile数据
        RenderTextureBlendTerrainLayerData mLayerData;

        //地表层的模型管理
        BlendTerrainLayerView mLayerView;

        Vector2Int[] mPickedTiles = new Vector2Int[4];

        int[] mTileIndex = { 1, 2, 4, 8 };

#if UNITY_EDITOR
        BlendTerrainLayerRenderTextures mRenderTextures;

        Dictionary<string, Vector2[]> mCachedTileUVs = new Dictionary<string, Vector2[]>();
#endif
    }
}
