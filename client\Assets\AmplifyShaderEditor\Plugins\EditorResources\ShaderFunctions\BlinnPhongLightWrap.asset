%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: BlinnPhongLightWrap
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=16205\n234;92;1063;705;1563.922;447.6829;2.241337;True;False\nNode;AmplifyShaderEditor.CommentaryNode;82;-868.7166,1513.25;Float;False;615.7619;256.2104;;4;0;73;72;71;Diffuse
    + Specular;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;62;-874.5117,944.6415;Float;False;2334.29;521.7126;Comment;20;60;1;69;66;68;67;61;56;55;47;50;53;51;52;49;48;45;46;44;70;Diffuse
    Color;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;43;-873.949,406.1891;Float;False;1347.446;439.3573;;11;41;40;42;2;33;34;36;37;39;38;32;Specular
    Color;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;31;-871.1387,-356.8812;Float;False;1192.721;289.2242;;7;21;20;18;23;19;4;17;NDotL;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;30;-875.0961,-9.355102;Float;False;977.2441;332.3028;;6;26;29;28;27;77;76;Half
    Dir;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;15;-856.0029,-727.2549;Float;False;924;294;;7;3;10;12;13;11;9;14;Gloss;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;16;-866.0001,-1064.568;Float;False;748.5;272;;4;8;7;5;6;Light
    Attenuation;1,1,1,1;0;0\nNode;AmplifyShaderEditor.Exp2OpNode;9;-310.0023,-612.2548;Float;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LightColorNode;6;-768.0001,-1014.568;Float;False;0;3;COLOR;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;61;351.8147,1114.297;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;40;22.70839,527.0504;Float;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;36;-412.9491,536.5466;Float;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;55;193.8148,1072.297;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;46;-819.0866,1078.04;Float;False;Constant;_Float3;Float
    3;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;34;-823.949,563.5466;Float;False;23;CurrentNormal;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;48;-461.0868,1017.04;Float;False;FLOAT3;0;0;0;0;1;0;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;45;-614.0866,1015.04;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;33;-809.7029,474.5466;Float;False;29;HalfDirection;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;44;-824.5117,994.6415;Float;False;Light
    Wrapping;1;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;41;-314.718,456.1892;Float;False;8;AttenuationColor;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;32;-562.3635,499.8815;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;51;-189.0866,1179.04;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;47;-292.0868,1021.04;Float;False;LightWrapVector;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;50;-605.0866,1170.04;Float;False;47;LightWrapVector;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;49;-358.0868,1176.04;Float;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FogAndAmbientColorsNode;67;339.0064,1220.623;Float;False;UNITY_LIGHTMODEL_AMBIENT;0;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;73;-560,1600;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;72;-832,1664;Float;False;42;specularFinalColor;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;71;-832,1568;Float;False;70;DiffuseColor;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;37;-566.9489,632.5466;Float;False;Constant;_Float2;Float
    2;0;0;Create;True;0;0;False;0;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;66;790.0066,1122.623;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;56;-16.18523,1193.297;Float;False;Constant;_Vector1;Vector
    1;0;0;Create;True;0;0;False;0;0,0,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.GetLocalVarNode;60;9.172014,1340.574;Float;False;8;AttenuationColor;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;10;-614.0029,-660.2548;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;12;-806.0029,-564.2547;Float;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;False;0;10;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;53;-20.54932,1092.818;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;70;1146.801,1151.155;Float;False;DiffuseColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;42;200.4974,524.5294;Float;False;specularFinalColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;13;-630.0029,-548.2548;Float;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;False;0;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;14;-182.0024,-628.2548;Float;False;SpecularPower;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;68;609.0066,1209.623;Float;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;7;-576.0001,-950.5679;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;1;736.0968,1302.828;Float;False;Diffuse;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;52;-398.0868,1262.04;Float;False;21;NDotL;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;69;976.0066,1143.623;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;11;-454.0023,-612.2548;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;38;-239.3654,550.192;Float;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;2;-234.7179,664.1893;Float;False;Specular;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;39;-516.949,730.5465;Float;False;14;SpecularPower;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldSpaceLightPos;76;-854.467,61.70709;Float;False;0;3;FLOAT4;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.NormalizeNode;77;-598.467,61.70709;Float;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldNormalVector;19;-521.2433,-309.2914;Float;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RegisterLocalVarNode;23;-329.2433,-309.2914;Float;False;CurrentNormal;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;17;-857.2433,-309.2914;Float;False;Constant;_Vector0;Vector
    0;0;0;Create;True;0;0;False;0;0,0,1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionInput;4;-681.2433,-309.2914;Float;False;Normal;3;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;3;-785.0029,-677.2548;Float;False;Gloss;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LightAttenuation;5;-816.0001,-886.5679;Float;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;8;-400,-966.5679;Float;False;AttenuationColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;18;-329.2433,-213.2914;Float;False;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.DotProductOpNode;20;-57.24332,-277.2914;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;21;86.75671,-261.2914;Float;False;NDotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;26;-800,160;Float;False;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SimpleAddOpNode;27;-422.467,77.70709;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NormalizeNode;28;-294.467,77.70709;Float;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;29;-134.467,77.70709;Float;False;HalfDirection;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-416,1616;Float;False;True;Output;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;9;0;11;0\nWireConnection;61;0;55;0\nWireConnection;61;1;60;0\nWireConnection;40;0;41;0\nWireConnection;40;1;38;0\nWireConnection;40;2;2;0\nWireConnection;36;0;32;0\nWireConnection;36;1;37;0\nWireConnection;55;0;53;0\nWireConnection;55;1;56;0\nWireConnection;48;0;45;0\nWireConnection;45;0;44;0\nWireConnection;45;1;46;0\nWireConnection;32;0;33;0\nWireConnection;32;1;34;0\nWireConnection;51;0;49;0\nWireConnection;51;1;52;0\nWireConnection;47;0;48;0\nWireConnection;49;0;50;0\nWireConnection;73;0;71;0\nWireConnection;73;1;72;0\nWireConnection;66;0;61;0\nWireConnection;66;1;68;0\nWireConnection;10;0;3;0\nWireConnection;10;1;12;0\nWireConnection;53;0;47;0\nWireConnection;53;1;51;0\nWireConnection;70;0;69;0\nWireConnection;42;0;40;0\nWireConnection;14;0;9;0\nWireConnection;68;0;67;0\nWireConnection;7;0;6;1\nWireConnection;7;1;5;0\nWireConnection;69;0;66;0\nWireConnection;69;1;1;0\nWireConnection;11;0;10;0\nWireConnection;11;1;13;0\nWireConnection;38;0;36;0\nWireConnection;38;1;39;0\nWireConnection;77;0;76;1\nWireConnection;19;0;4;0\nWireConnection;23;0;19;0\nWireConnection;4;0;17;0\nWireConnection;8;0;7;0\nWireConnection;20;0;23;0\nWireConnection;20;1;18;0\nWireConnection;21;0;20;0\nWireConnection;27;0;77;0\nWireConnection;27;1;26;0\nWireConnection;28;0;27;0\nWireConnection;29;0;28;0\nWireConnection;0;0;73;0\nASEEND*/\n//CHKSM=2875532B381E030A9F10046AE6BB0271C00931BB"
  m_functionName: 
  m_description: 
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
