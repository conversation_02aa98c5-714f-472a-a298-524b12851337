﻿ 



 
 



#if UNITY_EDITOR

namespace TFW.Map
{
    public class ActionChangeBlendTerrainLayerTiles : EditorAction
    {
        class TileData
        {
            public TileData(int index, int type, int subTypeIndex)
            {
                tileIndex = index;
                tileType = type;
                this.subTypeIndex = subTypeIndex;
            }

            public int tileIndex;
            public int tileType;
            public int subTypeIndex;
        }

        public ActionChangeBlendTerrainLayerTiles(int layerID, int minX, int minY, int maxX, int maxY)
        {
            mLayerID = layerID;
            mMinX = minX;
            mMaxX = maxX;
            mMinY = minY;
            mMaxY = maxY;
            mDescription = string.Format("change terrain layer");
        }

        void RecordTiles(ref TileData[] tiles)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            int horizontalTileCount = mMaxX - mMinX + 1;
            int verticalTileCount = mMaxY - mMinY + 1;
            tiles = new TileData[verticalTileCount * horizontalTileCount];

            int idx = 0;
            for (int i = mMinY; i <= mMaxY; ++i)
            {
                for (int j = mMinX; j <= mMaxX; ++j)
                {
                    var tile = layer.GetTile(j, i);
                    if (tile != null)
                    {
                        tiles[idx] = new TileData(tile.index, tile.type, tile.subTypeIndex);
                    }
                    else
                    {
                        tiles[idx] = new TileData(0, -1, 0);
                    }
                    ++idx;
                }
            }
        }

        public void Begin()
        {
            RecordTiles(ref mOldTileDatas);
        }

        public void End()
        {
            RecordTiles(ref mNewTileDatas);
        }

        bool SetTiles(TileData[] tiles)
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                int idx = 0;
                for (int i = mMinY; i <= mMaxY; ++i)
                {
                    for (int j = mMinX; j <= mMaxX; ++j)
                    {
                        var tile = tiles[idx];
                        layer.SetTile(j, i, tile.tileIndex, tile.tileType, tile.subTypeIndex);
                        ++idx;
                    }
                }
                return true;
            }
            return false;
        }

        public override bool Do()
        {
            return SetTiles(mNewTileDatas);
        }

        public override bool Undo()
        {
            return SetTiles(mOldTileDatas);
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        TileData[] mOldTileDatas;
        TileData[] mNewTileDatas;
        int mMinX;
        int mMinY;
        int mMaxX;
        int mMaxY;
        int mLayerID;
        string mDescription;
    }
}

#endif