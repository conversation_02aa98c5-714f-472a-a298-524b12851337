%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Atlas_Calendar_1
  m_EditorData:
    serializedVersion: 2
    textureSettings:
      serializedVersion: 2
      anisoLevel: 0
      compressionQuality: 0
      maxTextureSize: 0
      textureCompression: 0
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 1
    platformSettings:
    - serializedVersion: 3
      m_BuildTarget: Android
      m_MaxTextureSize: 2048
      m_ResizeAlgorithm: 0
      m_TextureFormat: 50
      m_TextureCompression: 0
      m_CompressionQuality: 50
      m_CrunchedCompression: 0
      m_AllowsAlphaSplitting: 0
      m_Overridden: 1
      m_IgnorePlatformSupport: 0
      m_AndroidETC2FallbackOverride: 0
      m_ForceMaximumCompressionQuality_BC6H_BC7: 0
    packingSettings:
      serializedVersion: 2
      padding: 2
      blockOffset: 2
      allowAlphaSplitting: 0
      enableRotation: 0
      enableTightPacking: 0
      enableAlphaDilation: 0
    secondaryTextureSettings: {}
    variantMultiplier: 1
    packables:
    - {fileID: 2800000, guid: 8310149c27b1ae44cbbfb0eeba3753b2, type: 3}
    - {fileID: 2800000, guid: b5d2b882b7ebe4445b43489661d6ef2d, type: 3}
    - {fileID: 2800000, guid: 87ab4bdc7ae6a5844b901ea97caf1091, type: 3}
    - {fileID: 2800000, guid: 77fe64a03bebc12498bb6b9e55a48fd8, type: 3}
    - {fileID: 2800000, guid: f72b5de0cb951b8408d8457013605205, type: 3}
    - {fileID: 2800000, guid: 43c433262bf0b47468fbae11611ab0b3, type: 3}
    - {fileID: 2800000, guid: 0379f346d769c6f4eaa9389a1fc71a64, type: 3}
    - {fileID: 2800000, guid: a5d3bb9eac9c6d8438e3e3be0d6c60cc, type: 3}
    bindAsDefault: 1
    isAtlasV2: 0
    cachedData: {fileID: 0}
    packedSpriteRenderDataKeys: []
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites: []
  m_PackedSpriteNamesToIndex: []
  m_RenderDataMap: {}
  m_Tag: Atlas_Calendar_1
  m_IsVariant: 0
