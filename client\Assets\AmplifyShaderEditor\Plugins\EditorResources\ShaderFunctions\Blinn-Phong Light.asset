%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Blinn-Phong Light
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=16306\n716;346;1066;687;501.1261;294.544;1;False;False\nNode;AmplifyShaderEditor.ComponentMaskNode;29;48,-496;Float;False;True;True;True;False;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;22;-352,-224;Float;False;Property;_Shininess;Shininess;2;0;Create;True;0;0;False;0;0.1;0;0.01;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;37;609.1547,-34.90417;Float;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;59;-336,-320;Float;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;51;-832,-288;Float;False;Blinn-Phong
    Half Vector;-1;;2;91a149ac9d615be429126c95e20753ce;0;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.ComponentMaskNode;44;48,-416;Float;False;False;False;False;True;1;0;COLOR;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;24;-368,-496;Float;False;Property;_SpecularColor;Specular
    Color;1;0;Create;True;0;0;False;0;0.3921569,0.3921569,0.3921569,1;0,0,0,0;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.PowerNode;21;112,-320;Float;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.IndirectDiffuseLighting;34;337.1547,77.09583;Float;False;World;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;36;417.1547,-34.90417;Float;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.LightColorNode;17;14.82559,-84.1315;Float;False;0;3;COLOR;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.LightAttenuation;10;-27.48053,45.50479;Float;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;38;1009.155,-34.90417;Float;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.ComponentMaskNode;47;769.1547,93.09583;Float;False;True;True;True;False;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ComponentMaskNode;50;769.1547,189.0958;Float;False;False;False;False;True;1;0;COLOR;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;25;801.1547,-274.9042;Float;False;4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.DotProductOpNode;19;-512,-240;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldNormalVector;12;-937.117,-98.03006;Float;False;False;1;0;FLOAT3;0,0,0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WireNode;67;395.671,-128.7589;Float;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;55;-518.842,331.2534;Float;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NormalizeNode;64;-729.1198,-98.03006;Float;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;26;353.1547,189.0958;Float;False;Property;_MainColor;Main
    Color;0;0;Create;True;0;0;False;0;0.3921569,0.3921569,0.3921569,1;0,0,0,0;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMaxOpNode;15;-65.18457,141.2409;Float;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;40;232.9505,-46.9381;Float;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;39;1185.155,-162.9042;Float;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;42;625.1547,189.0958;Float;False;Diffuse;5;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;52;-1065.115,-98.03006;Float;False;Normal;3;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;43;-128,-496;Float;False;Specular;5;2;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;60;-48,-256;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;128;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;56;165.1989,329.8424;Float;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;13;-487.8197,165.9312;Float;False;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;53;-1273.115,-98.03006;Float;False;Constant;_DefaultNormal;DefaultNormal;3;0;Create;True;0;0;False;0;0,0,1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.DotProductOpNode;14;-210.7447,112.9567;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1329.155,-162.9042;Float;False;True;RGB;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;57;1329.155,-82.90417;Float;False;True;Alpha;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;29;0;43;0\nWireConnection;37;0;36;0\nWireConnection;37;1;34;0\nWireConnection;59;0;19;0\nWireConnection;44;0;43;0\nWireConnection;21;0;59;0\nWireConnection;21;1;60;0\nWireConnection;34;0;56;0\nWireConnection;36;0;40;0\nWireConnection;36;1;15;0\nWireConnection;38;0;37;0\nWireConnection;38;1;47;0\nWireConnection;47;0;42;0\nWireConnection;50;0;42;0\nWireConnection;25;0;29;0\nWireConnection;25;1;44;0\nWireConnection;25;2;21;0\nWireConnection;25;3;67;0\nWireConnection;19;0;51;0\nWireConnection;19;1;64;0\nWireConnection;12;0;52;0\nWireConnection;67;0;40;0\nWireConnection;55;0;64;0\nWireConnection;64;0;12;0\nWireConnection;15;0;14;0\nWireConnection;40;0;17;0\nWireConnection;40;1;10;0\nWireConnection;39;0;25;0\nWireConnection;39;1;38;0\nWireConnection;42;0;26;0\nWireConnection;52;0;53;0\nWireConnection;43;0;24;0\nWireConnection;60;0;22;0\nWireConnection;56;0;55;0\nWireConnection;14;0;64;0\nWireConnection;14;1;13;0\nWireConnection;0;0;39;0\nWireConnection;57;0;50;0\nASEEND*/\n//CHKSM=57C0C1F3D628A2FE0988ED3E128E1C80E9BEE859"
  m_functionName: 
  m_description: Generates a lighting model using Blinn-Phong reflectance model and
    closely resembles Unity legacy shaders.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 5
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
