﻿using UnityEngine;

namespace TFW.Map
{
    public static class ClampTopDownViewCameraToRange
    {
        public static Vector3 Clamp(Vector3 currentCameraPos, float fov, float aspect, float rangeMinX, float rangeMinZ, float rangeMaxX, float rangeMaxZ)
        {
            float cameraHeight = currentCameraPos.y;
            float viewHeightHalf = Mathf.Abs(cameraHeight * Mathf.Tan(fov*0.5f * Mathf.Deg2Rad));
            float viewWidthHalf = viewHeightHalf * aspect;
            float minX = currentCameraPos.x - viewWidthHalf;
            float minZ = currentCameraPos.z - viewHeightHalf;
            float maxX = currentCameraPos.x + viewWidthHalf;
            float maxZ = currentCameraPos.z + viewHeightHalf;

            if (minX < rangeMinX)
            {
                currentCameraPos.x += -minX;
            }
            if (minZ < rangeMinZ)
            {
                currentCameraPos.z += -minZ;
            }
            if (maxX > rangeMaxX)
            {
                currentCameraPos.x -= (maxX - rangeMaxX);
            }
            if (maxZ > rangeMaxZ)
            {
                currentCameraPos.z -= (maxZ - rangeMaxZ);
            }
            return currentCameraPos;
        }
    }
}