﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class MeshCombiner
    {
        class MeshData
        {
            public MeshData(Vector3[] vertices, int[] indices, Matrix4x4 transform)
            {
                this.vertices = vertices;
                this.indices = indices;
                this.transform = transform;
            }
            public Vector3[] vertices;
            public int[] indices;
            public Matrix4x4 transform;
        }

        public void AddMesh(Vector3[] vertices, int[] indices, Matrix4x4 transform)
        {
            mMeshies.Add(new MeshData(vertices, indices, transform));
        }

        public void Combine(float distanceError, out Vector3[] vertices, out int[] indices)
        {
            int n = mMeshies.Count;
            int totalIndexCount = GetTotalIndexCount();
            indices = new int[totalIndexCount];
            int offset = 0;
            for (int i = 0; i < n; ++i)
            {
                if (mMeshies[i].indices != null)
                {
                    var mi = mMeshies[i].indices;
                    var mv = mMeshies[i].vertices;
                    var transform = mMeshies[i].transform;
                    for (int j = 0; j < mi.Length; ++j)
                    {
                        var pos = transform.MultiplyPoint(mv[mi[j]]);

                        var combinedIndex = GetVertexIndex(pos, distanceError);
                        indices[offset] = combinedIndex;
                        ++offset;
                    }
                }
            }

            vertices = mCombinedVertices.ToArray();
        }

        int GetVertexIndex(Vector3 pos, float distanceError)
        {
            for (int i = 0; i < mCombinedVertices.Count; ++i)
            {
                if (Utils.Approximately(pos.x, mCombinedVertices[i].x, distanceError) &&
                    Utils.Approximately(pos.z, mCombinedVertices[i].z, distanceError))
                {
                    return i;
                }
            }

            mCombinedVertices.Add(pos);
            return mCombinedVertices.Count - 1;
        }

        int GetTotalIndexCount()
        {
            int n = 0;
            for (int i = 0; i < mMeshies.Count; ++i)
            {
                if (mMeshies[i].indices != null)
                {
                    n += mMeshies[i].indices.Length;
                }
            }
            return n;
        }

        List<Vector3> mCombinedVertices = new List<Vector3>();
        List<MeshData> mMeshies = new List<MeshData>();
    }
}

#endif