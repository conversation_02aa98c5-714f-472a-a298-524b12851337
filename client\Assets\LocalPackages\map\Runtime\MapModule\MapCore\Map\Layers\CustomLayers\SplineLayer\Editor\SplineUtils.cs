﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public static class SplineUtils
    {
        public static float Bezier(float x0, float x1, float x2, float x3, float t)
        {
            return (((x3 - 3 * x2 + 3 * x1 - x0) * t + (3 * x2 - 6 * x1 + 3 * x0)) * t + (3 * x1 - 3 * x0)) * t + x0;
        }

        public static Vector3 Bezier(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
        {
            float x = Bezier(p0.x, p1.x, p2.x, p3.x, t);
            float y = Bezier(p0.y, p1.y, p2.y, p3.y, t);
            float z = Bezier(p0.z, p1.z, p2.z, p3.z, t);

            return new Vector3(x, y, z);
        }
    }
}

#endif