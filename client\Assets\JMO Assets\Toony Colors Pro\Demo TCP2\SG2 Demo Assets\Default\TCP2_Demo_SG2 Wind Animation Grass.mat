%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_SG2 Wind Animation Grass
  m_Shader: {fileID: 4800000, guid: 3f17d46eadd0bb049ac072eb8db526a6, type: 3}
  m_ShaderKeywords: _EMISSION
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 445856b3cdfbf354c8b550fd4e644003, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _WindTexture:
        m_Texture: {fileID: 2800000, guid: 13dfe2b3da7c8d1429b8f318ff4b2337, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _Cutoff: 0.5
    - _Parallax: 0.02
    - _RampSmoothing: 1
    - _RampThreshold: 0.5
    - _WindDirectionOffset: 0
    - _WindFrequency: 1.5
    - _WindSpeed: 2
    - _WindStrength: 0.03
    - _WindTimeOffset: 0.5
    - __dummy__: 0
    m_Colors:
    - _Color: {r: 0.4224952, g: 0.541, b: 0.12880951, a: 1}
    - _ColorBack: {r: 0.48760083, g: 0.56, b: 0.13391301, a: 1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _SColor: {r: 0.7352941, g: 0.7352941, b: 0.7352941, a: 1}
    - _WindDirection: {r: 1, g: 0.5, b: 1, a: 0}
    - _WindTexTilingSpeed: {r: 0.2, g: 0.2, b: 0.1, a: 0.1}
