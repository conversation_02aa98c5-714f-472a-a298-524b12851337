{"skeleton": {"hash": "JjiC+StW324", "spine": "4.2.33", "x": -312.53, "y": -24.79, "width": 562, "height": 1760, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 310.45, "y": 1050.77}, {"name": "ALL2", "parent": "ALL", "x": -316.47, "y": -16.82, "icon": "arrows"}, {"name": "body", "parent": "ALL2", "length": 105.4, "rotation": 77.74, "x": 2.04, "y": 7.74}, {"name": "body2", "parent": "body", "length": 298.05, "rotation": 92.56, "x": 105.4, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 66.19, "rotation": 98.33, "x": 298.05, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 134.6, "rotation": -0.88, "x": 66.19}, {"name": "sh_L", "parent": "body2", "x": 279.04, "y": -178.61, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "x": 249.47, "y": 164.58, "inherit": "noScale"}, {"name": "tun", "parent": "ALL2", "length": 192.31, "rotation": -98.92, "x": -1.43, "y": -8.39}, {"name": "leg_R", "parent": "tun", "x": 26.63, "y": -95.52}, {"name": "leg_L", "parent": "tun", "x": 22.86, "y": 89.92}, {"name": "arm_L", "parent": "sh_L", "length": 337.19, "rotation": -84.98, "x": -21.68, "y": -1.06, "inherit": "noRotationOrReflection"}, {"name": "arm_L2", "parent": "arm_L", "length": 129.96, "rotation": -163.71, "x": 337.19}, {"name": "hand_L3", "parent": "body2", "rotation": -92.56, "x": 43.53, "y": -152.39, "color": "ff3f00ff", "icon": "ik"}, {"name": "hand_L", "parent": "hand_L3", "length": 57.63, "rotation": 92.2, "inherit": "noScaleOrReflection"}, {"name": "hand_L2", "parent": "hand_L", "length": 46.61, "rotation": 27.2, "x": 57.63}, {"name": "arm_R", "parent": "sh_R", "length": 287.99, "rotation": -92.89, "x": -22.42, "y": -1.04, "inherit": "noRotationOrReflection"}, {"name": "arm_R2", "parent": "arm_R", "length": 260.19, "rotation": -16.59, "x": 287.99}, {"name": "arm_R3", "parent": "arm_R2", "length": 127.18, "rotation": 2.78, "x": 260.19}, {"name": "RU_L", "parent": "body2", "length": 40, "x": 116, "y": -86.54}, {"name": "RU_L2", "parent": "RU_L", "length": 40, "x": -10.01, "y": -10.36, "color": "abe323ff"}, {"name": "RU_L3", "parent": "RU_L2", "length": 40, "x": -10.81, "y": -8.21}, {"name": "RU_R", "parent": "body2", "length": 40, "x": 119.27, "y": 89.65}, {"name": "RU_R2", "parent": "RU_R", "length": 40, "x": -10.88, "y": 16.3}, {"name": "RU_R3", "parent": "RU_R2", "length": 40, "x": -9.01, "y": 11.83}, {"name": "eye_L", "parent": "head", "x": 57.62, "y": -30.52}, {"name": "eye_R", "parent": "head", "x": 59.54, "y": 37.72}, {"name": "earring_L", "parent": "head", "length": 27.41, "rotation": -88.34, "x": 25.7, "y": -62.81, "inherit": "noRotationOrReflection"}, {"name": "earring_R", "parent": "head", "length": 24.82, "rotation": -90.92, "x": 30.59, "y": 66.04, "inherit": "noRotationOrReflection"}, {"name": "eyebrow_L", "parent": "head", "length": 13.17, "rotation": 74.42, "x": 74.82, "y": -54.63}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 23.07, "rotation": 35.52, "x": 13.17}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 14.91, "rotation": -1.47, "x": 23.07}, {"name": "eyebrow_R", "parent": "head", "length": 11.52, "rotation": -83.41, "x": 77.51, "y": 65.08}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 24.03, "rotation": -26.56, "x": 11.52}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 15.05, "rotation": 0.38, "x": 24.03}, {"name": "leg_L2", "parent": "leg_L", "length": 514.75, "rotation": 17.18, "x": 92.03, "y": 17.73}, {"name": "leg_L3", "parent": "leg_L2", "length": 531.56, "rotation": -3.08, "x": 514.75}, {"name": "leg_R2", "parent": "leg_R", "length": 567.89, "rotation": 17.62, "x": 88.54, "y": -30.5}, {"name": "leg_R3", "parent": "leg_R2", "length": 540.01, "rotation": -11.46, "x": 567.89}, {"name": "headround3", "parent": "head", "x": 385.65, "y": 4.74, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -87.28, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 312.58, "y": -82.53, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "x": 209.32, "y": -435.59, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "x": 134.18, "y": -435.59, "icon": "warning"}, {"name": "tunround", "parent": "ALL2", "x": 391.9, "y": -107.83, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 391.9, "y": -179.84, "icon": "warning"}, {"name": "leg_L4", "parent": "leg_L", "length": 1043.85, "rotation": 15.65, "x": 92.02, "y": 17.71}, {"name": "leg_L5", "parent": "root", "x": 202.95, "y": -143.42, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "leg_L4", "rotation": 83.31, "x": 513.55, "y": 14.07, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R4", "parent": "leg_R", "length": 1100.42, "rotation": 12.07, "x": 88.55, "y": -30.49}, {"name": "leg_R5", "parent": "root", "x": -89.85, "y": -169.4, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "leg_R4", "rotation": 86.88, "x": 565.18, "y": 55.25, "color": "ff3f00ff", "icon": "ik"}, {"name": "neck2", "parent": "neck", "length": 92.26, "rotation": 136.26, "x": 89.28, "y": 79.3}, {"name": "neck3", "parent": "neck", "x": 53.27, "y": -109.07}], "slots": [{"name": "arm", "bone": "root", "attachment": "arm"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "arm_Lb", "bone": "root", "attachment": "arm_Lb"}, {"name": "ear_R", "bone": "root", "attachment": "ear_R"}, {"name": "ear_L", "bone": "root", "attachment": "ear_L"}, {"name": "earring_R", "bone": "root", "attachment": "earring_R"}, {"name": "earring_L", "bone": "root", "attachment": "earring_L"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "body2", "bone": "root", "attachment": "body"}, {"name": "hat", "bone": "root", "attachment": "hat"}], "ik": [{"name": "hand_L", "order": 2, "bones": ["arm_L2"], "target": "hand_L3", "compress": true, "stretch": true}, {"name": "leg_L", "order": 4, "bones": ["leg_L4"], "target": "leg_L5", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 5, "bones": ["leg_L2"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 6, "bones": ["leg_L3"], "target": "leg_L5", "compress": true, "stretch": true}, {"name": "leg_R", "order": 8, "bones": ["leg_R4"], "target": "leg_R5", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 9, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 10, "bones": ["leg_R3"], "target": "leg_R5", "compress": true, "stretch": true}], "transform": [{"name": "bodyround", "order": 11, "bones": ["bodyround2"], "target": "bodyround", "x": -75.14, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "bones": ["hand_L3"], "target": "bodyround", "rotation": -92.56, "x": -165.79, "y": 283.21, "mixRotate": 0, "mixX": 0.05, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 12, "bones": ["headround2"], "target": "headround", "x": -73.06, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 13, "bones": ["eyebrow_L"], "target": "headround", "rotation": 74.42, "x": -310.83, "y": 27.9, "mixRotate": 0, "mixX": 0.043, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 14, "bones": ["eyebrow_R"], "target": "headround", "rotation": -83.41, "x": -308.14, "y": 147.61, "mixRotate": 0, "mixX": 0.043, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 15, "bones": ["earring_L"], "target": "headround", "rotation": 174.21, "x": -359.95, "y": 19.72, "mixRotate": 0, "mixX": -0.005, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 16, "bones": ["earring_R"], "target": "headround", "rotation": 171.64, "x": -355.06, "y": 152.07, "mixRotate": 0, "mixX": -0.005, "mixScaleX": 0, "mixShearY": 0}, {"name": "RU_L2", "order": 1, "bones": ["hand_L3"], "target": "RU_L2", "rotation": -92.56, "x": -62.46, "y": -55.48, "mixRotate": 0, "mixX": 0.2, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround", "order": 17, "bones": ["tunround2"], "target": "tunround", "y": -72.01, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 7, "bones": ["leg_R"], "target": "tunround", "rotation": -98.92, "x": -491.82, "y": 87.95, "mixRotate": 0, "mixX": 0.006, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 3, "bones": ["leg_L"], "target": "tunround", "rotation": -98.92, "x": -308.04, "y": 62.91, "mixRotate": 0, "mixX": -0.005, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"arm": {"arm": {"type": "mesh", "uvs": [0.88069, 0, 0.90932, 0.0032, 0.93034, 0.01933, 0.94289, 0.05192, 0.95544, 0.08452, 0.97616, 0.1121, 0.99988, 0.13941, 1, 0.14489, 0.96708, 0.1722, 0.97196, 0.22856, 0.97552, 0.2865, 0.97866, 0.33172, 0.98272, 0.36033, 0.99901, 0.41464, 1, 0.44243, 0.98119, 0.49462, 0.97398, 0.5057, 0.95205, 0.52036, 0.93242, 0.52534, 0.91697, 0.52268, 0.8992, 0.5056, 0.87959, 0.48639, 0.86273, 0.45585, 0.85128, 0.42083, 0.84555, 0.37932, 0.8423, 0.35136, 0.83083, 0.30571, 0.81763, 0.25349, 0.80865, 0.2533, 0.79776, 0.21254, 0.78422, 0.17202, 0.77465, 0.14999, 0.72529, 0.15266, 0.40771, 0.16975, 0.40039, 0.19556, 0.38972, 0.25103, 0.38245, 0.29276, 0.37027, 0.29302, 0.35979, 0.3463, 0.33555, 0.42523, 0.3272, 0.44996, 0.32062, 0.471, 0.31488, 0.49675, 0.30813, 0.52607, 0.29777, 0.55702, 0.27696, 0.59914, 0.22363, 0.68592, 0.18474, 0.74667, 0.1632, 0.78056, 0.17334, 0.78851, 0.15722, 0.80759, 0.1411, 0.82668, 0.13187, 0.83089, 0.12849, 0.84599, 0.12617, 0.8662, 0.12099, 0.88269, 0.10566, 0.9037, 0.09309, 0.91956, 0.10621, 0.9207, 0.11956, 0.93688, 0.13015, 0.96323, 0.0713, 0.9757, 0.07451, 0.99679, 0.06476, 0.99968, 0.0452, 0.99256, 0.01159, 0.9675, 0.00253, 0.92109, 0, 0.90339, 0.01093, 0.88197, 0.03382, 0.84823, 0.04981, 0.83017, 0.05865, 0.81392, 0.05483, 0.80094, 0.05857, 0.78681, 0.06176, 0.76521, 0.08525, 0.76439, 0.09934, 0.72655, 0.118, 0.65927, 0.13955, 0.55366, 0.1528, 0.52309, 0.17013, 0.49591, 0.18658, 0.47222, 0.19814, 0.45074, 0.20559, 0.43273, 0.2108, 0.40838, 0.22031, 0.31356, 0.22558, 0.23782, 0.17907, 0.21231, 0.20357, 0.17038, 0.22048, 0.1335, 0.22074, 0.11398, 0.23405, 0.08608, 0.25251, 0.06816, 0.28511, 0.05847, 0.31811, 0.05942, 0.38489, 0.06782, 0.42687, 0.06615, 0.69154, 0.03753, 0.72724, 0.03319, 0.83461, 0.00381, 0.10948, 0.7992, 0.0848, 0.77001, 0.15488, 0.78665, 0.11965, 0.78143, 0.10065, 0.81527, 0.09565, 0.82482, 0.09065, 0.83791, 0.0832, 0.85614, 0.22608, 0.21716, 0.33234, 0.26364, 0.3488, 0.18448, 0.37119, 0.2854, 0.26242, 0.09128, 0.27437, 0.13047, 0.2853, 0.16117, 0.29472, 0.18775, 0.89306, 0.03529, 0.87196, 0.09622, 0.86112, 0.13271, 0.8558, 0.16223, 0.27188, 0.23567, 0.2512, 0.1768, 0.24712, 0.11711, 0.24846, 0.14623, 0.34151, 0.21584, 0.3539, 0.15589, 0.24884, 0.41444, 0.25482, 0.32644, 0.32177, 0.34467, 0.29685, 0.41997, 0.2424, 0.44273, 0.23496, 0.46601, 0.22008, 0.48827, 0.20791, 0.51508, 0.26538, 0.52824, 0.27485, 0.4994, 0.27958, 0.4741, 0.28702, 0.44678, 0.19514, 0.54649, 0.25047, 0.55712, 0.86055, 0.21573, 0.89035, 0.38245, 0.89362, 0.41535, 0.90919, 0.45335, 0.93492, 0.48629, 0.95592, 0.4498, 0.95388, 0.40825, 0.94576, 0.37075, 0.91588, 0.18553, 0.96697, 0.15577, 0.81606, 0.24497, 0.92278, 0.23239, 0.86559, 0.24391, 0.9145, 0.14683, 0.91483, 0.12071, 0.91437, 0.08878, 0.9074, 0.05435, 0.8574, 0.17772], "triangles": [105, 71, 104, 52, 104, 51, 105, 104, 52, 106, 71, 105, 70, 71, 106, 52, 106, 105, 53, 106, 52, 107, 70, 106, 69, 70, 107, 53, 107, 106, 54, 107, 53, 55, 107, 54, 56, 107, 55, 56, 68, 107, 57, 66, 67, 57, 67, 68, 107, 68, 69, 56, 57, 68, 65, 66, 57, 61, 65, 57, 59, 61, 57, 59, 57, 58, 61, 59, 60, 64, 65, 61, 63, 64, 61, 62, 63, 61, 80, 81, 132, 133, 80, 132, 79, 80, 133, 43, 135, 42, 134, 132, 135, 134, 135, 43, 133, 132, 134, 138, 79, 133, 139, 138, 133, 78, 79, 138, 44, 134, 43, 134, 139, 133, 139, 134, 44, 45, 139, 44, 78, 138, 77, 45, 138, 139, 138, 45, 77, 46, 77, 45, 47, 76, 77, 46, 47, 77, 76, 103, 75, 101, 74, 75, 76, 47, 103, 103, 101, 75, 103, 47, 48, 102, 103, 48, 73, 74, 101, 102, 48, 49, 100, 101, 103, 73, 101, 100, 72, 73, 100, 50, 102, 49, 104, 71, 72, 100, 104, 72, 100, 102, 50, 102, 100, 103, 51, 100, 50, 104, 100, 51, 90, 91, 122, 89, 90, 122, 123, 122, 113, 89, 122, 123, 88, 89, 123, 121, 123, 114, 88, 123, 121, 108, 88, 121, 87, 88, 108, 115, 108, 121, 120, 115, 124, 120, 108, 115, 86, 87, 108, 86, 108, 120, 109, 120, 124, 109, 124, 35, 111, 109, 35, 36, 111, 35, 37, 109, 111, 37, 111, 36, 120, 85, 86, 127, 120, 109, 128, 127, 109, 127, 85, 120, 37, 128, 109, 38, 128, 37, 84, 85, 127, 126, 84, 127, 126, 127, 128, 129, 126, 128, 39, 129, 128, 38, 39, 128, 83, 84, 126, 130, 83, 126, 137, 126, 129, 130, 126, 137, 40, 129, 39, 137, 129, 40, 82, 83, 130, 131, 82, 130, 41, 137, 40, 81, 82, 131, 136, 130, 137, 136, 137, 41, 131, 130, 136, 132, 81, 131, 42, 136, 41, 135, 131, 136, 135, 136, 42, 132, 131, 135, 152, 141, 26, 26, 141, 25, 141, 24, 25, 142, 141, 146, 23, 24, 141, 23, 141, 142, 143, 142, 146, 22, 23, 142, 22, 142, 143, 144, 143, 145, 21, 22, 143, 21, 143, 144, 20, 21, 144, 19, 20, 144, 144, 18, 19, 17, 18, 144, 3, 156, 2, 155, 156, 3, 155, 3, 4, 117, 156, 155, 154, 155, 4, 154, 4, 5, 117, 155, 154, 153, 118, 154, 149, 154, 5, 149, 5, 6, 149, 6, 7, 153, 154, 149, 8, 149, 7, 157, 119, 153, 8, 148, 153, 8, 153, 149, 157, 153, 148, 29, 30, 157, 140, 157, 148, 29, 157, 140, 148, 8, 9, 151, 148, 9, 140, 148, 151, 152, 140, 151, 150, 29, 140, 150, 140, 152, 28, 29, 150, 27, 150, 152, 28, 150, 27, 151, 9, 10, 26, 27, 152, 11, 147, 10, 152, 147, 141, 147, 151, 10, 147, 11, 12, 147, 152, 151, 146, 147, 12, 141, 147, 146, 146, 12, 13, 145, 146, 13, 145, 13, 14, 145, 143, 146, 15, 145, 14, 144, 145, 15, 16, 144, 15, 17, 144, 16, 116, 0, 1, 116, 1, 2, 116, 99, 0, 116, 117, 99, 156, 116, 2, 98, 99, 117, 117, 116, 156, 118, 117, 154, 112, 92, 93, 91, 92, 112, 93, 94, 112, 125, 94, 95, 94, 113, 112, 122, 91, 112, 122, 112, 113, 125, 113, 94, 123, 113, 114, 124, 115, 110, 125, 114, 113, 110, 125, 33, 121, 114, 115, 119, 118, 153, 118, 31, 117, 119, 31, 118, 30, 119, 157, 30, 31, 119, 117, 31, 98, 32, 98, 31, 32, 97, 98, 32, 33, 96, 32, 96, 97, 33, 95, 96, 34, 110, 33, 125, 95, 33, 110, 114, 125, 115, 114, 110, 124, 110, 34, 35, 124, 34], "vertices": [4, 4, 291.92, -177.2, 0.00055, 8, 42.45, -341.78, 0, 7, 12.88, 1.41, 0.99945, 17, -96.89, 333.06, 0, 2, 7, 9.77, -14.56, 0.96398, 12, -30.84, 14.84, 0.03602, 2, 7, -2.86, -25.82, 0.60751, 12, -17.74, 25.55, 0.39249, 2, 7, -27.63, -31.77, 0.10489, 12, 7.27, 30.43, 0.89511, 1, 12, 32.27, 35.31, 1, 1, 12, 53.92, 45.1, 1, 1, 12, 75.53, 56.58, 1, 1, 12, 79.63, 56.29, 1, 1, 12, 98.44, 36.07, 1, 1, 12, 140.84, 35.09, 1, 1, 12, 184.36, 33.27, 1, 1, 12, 218.35, 32.05, 1, 1, 12, 239.95, 32.45, 1, 1, 12, 281.38, 37.99, 1, 1, 12, 302.22, 36.72, 1, 1, 12, 340.34, 22.76, 1, 1, 12, 348.28, 17.99, 1, 1, 12, 358.16, 4.75, 1, 2, 12, 360.92, -6.57, 0.85, 13, -20.94, 12.96, 0.15, 2, 12, 358.17, -15.04, 0.7, 13, -15.91, 20.32, 0.3, 1, 12, 344.52, -23.86, 1, 1, 12, 329.18, -33.58, 1, 1, 12, 305.51, -41.01, 1, 1, 12, 278.74, -45.12, 1, 1, 12, 247.4, -45.6, 1, 1, 12, 226.33, -45.58, 1, 2, 12, 191.61, -49, 1, 17, 133.81, 316.63, 0, 4, 4, 103.32, -133.31, 0.00377, 8, -146.15, -297.89, 0, 12, 151.9, -52.95, 0.99622, 17, 95.02, 307.25, 1e-05, 4, 4, 103.69, -128.27, 0.00424, 8, -145.78, -292.85, 0, 12, 151.31, -57.97, 0.99575, 17, 95.13, 302.2, 1e-05, 4, 4, 134.54, -123.52, 0.34288, 8, -114.93, -288.1, 1e-05, 12, 120.28, -61.38, 0.65708, 17, 64.87, 294.54, 4e-05, 2, 4, 165.28, -117.27, 0.73429, 12, 89.3, -66.3, 0.26571, 1, 4, 182.05, -112.64, 1, 1, 4, 181.28, -84.84, 1, 1, 4, 176.41, 94.04, 1, 2, 4, 157.24, 99.01, 0.84571, 17, 63.38, 70.87, 0.15429, 4, 4, 115.88, 106.86, 0.38242, 7, -163.16, 285.46, 0, 12, 129, -292.35, 9e-05, 17, 105.29, 66.97, 0.61749, 4, 4, 84.76, 112.34, 0.0029, 7, -194.28, 290.94, 0, 12, 159.86, -299.16, 4e-05, 17, 136.79, 64.47, 0.99705, 4, 4, 84.87, 119.18, 0.00244, 7, -194.17, 297.79, 0, 12, 159.45, -306, 4e-05, 17, 137.33, 57.64, 0.99752, 2, 12, 198.8, -315.37, 0, 17, 177.6, 53.78, 1, 1, 17, 237.48, 43.16, 1, 2, 17, 256.26, 39.4, 0.98207, 18, -41.66, 28.71, 0.01793, 2, 17, 272.23, 36.51, 0.88993, 18, -25.53, 30.49, 0.11007, 2, 17, 291.71, 34.26, 0.52779, 18, -6.22, 33.89, 0.47221, 2, 17, 313.89, 31.58, 0.1102, 18, 15.81, 37.66, 0.8898, 2, 17, 337.39, 26.94, 0.0011, 18, 39.66, 39.92, 0.9989, 1, 18, 73.38, 39.44, 1, 1, 18, 144.81, 32.91, 1, 1, 18, 195.12, 27.52, 1, 2, 18, 223.14, 24.59, 0.99297, 19, -35.81, 26.36, 0.00703, 2, 18, 226.88, 31.96, 0.99218, 19, -31.73, 33.54, 0.00782, 2, 18, 243.41, 28.19, 0.87234, 19, -15.39, 28.97, 0.12766, 2, 18, 259.94, 24.43, 0.5203, 19, 0.94, 24.41, 0.4797, 2, 18, 264.65, 20.59, 0.3394, 19, 5.45, 20.35, 0.6606, 2, 18, 275.98, 22.58, 0.04268, 19, 16.87, 21.79, 0.95732, 1, 19, 31.77, 24.9, 1, 1, 19, 44.47, 25.67, 1, 1, 19, 62.07, 21.95, 1, 1, 19, 75.5, 18.6, 1, 1, 19, 74.2, 25.92, 1, 1, 19, 83.69, 36.59, 1, 1, 19, 100.93, 47.97, 1, 1, 19, 119.4, 18.99, 1, 1, 19, 134.05, 25.26, 1, 1, 19, 137.71, 20.64, 1, 1, 19, 135.74, 8.57, 1, 1, 19, 123.14, -14.93, 1, 1, 19, 91.22, -29.81, 1, 1, 19, 78.9, -34.99, 1, 1, 19, 61.73, -33.73, 1, 2, 7, -602.29, 505.28, 0, 19, 33.76, -28.69, 1, 3, 7, -589.14, 495.69, 0, 18, 279.52, -23.07, 0.06084, 19, 18.18, -23.98, 0.93916, 3, 7, -577.17, 490.19, 0, 18, 266.36, -22.45, 0.37658, 19, 5.07, -22.73, 0.62342, 3, 7, -567.34, 491.9, 0, 18, 257.89, -27.73, 0.65775, 19, -3.65, -27.58, 0.34225, 3, 7, -556.83, 489.32, 0, 18, 247.18, -29.28, 0.8681, 19, -14.42, -28.61, 0.1319, 3, 7, -540.7, 486.81, 0, 18, 231.29, -33, 0.98581, 19, -30.47, -31.56, 0.01419, 1, 18, 226.31, -20.76, 1, 1, 18, 196.88, -22.77, 1, 1, 18, 145.74, -29.73, 1, 2, 7, -383.94, 436.05, 0, 18, 66.93, -44.75, 1, 3, 7, -361.33, 427.59, 0, 17, 316.05, -55.72, 0.02572, 18, 42.8, -45.39, 0.97428, 3, 7, -341.38, 416.95, 0, 17, 295.18, -47.02, 0.19079, 18, 20.31, -43.01, 0.80921, 3, 7, -324.01, 406.92, 0, 17, 276.94, -38.68, 0.55776, 18, 0.45, -40.23, 0.44224, 2, 17, 260.5, -33, 0.8686, 18, -16.92, -39.48, 0.1314, 2, 17, 246.78, -29.5, 0.98009, 18, -31.07, -40.04, 0.01991, 1, 17, 228.37, -27.5, 1, 1, 17, 156.98, -25.75, 1, 1, 17, 100.02, -25.66, 1, 1, 17, 82.21, -52.72, 1, 1, 17, 50.07, -40.56, 1, 2, 8, -41.17, 33.36, 0.00975, 17, 21.92, -32.46, 0.99025, 2, 8, -26.53, 32.56, 0.10854, 17, 7.28, -33.05, 0.89146, 2, 8, -5.93, 24.15, 0.52788, 17, -14.03, -26.64, 0.47212, 2, 8, 7.05, 13.19, 0.9214, 17, -27.99, -16.95, 0.0786, 4, 4, 262.98, 159.14, 0.00826, 8, 13.5, -5.44, 0.99171, 7, -16.06, 337.75, 0, 12, -20.21, -338.25, 2e-05, 4, 4, 261.43, 140.65, 0.06812, 8, 11.96, -23.93, 0.93169, 7, -17.61, 319.25, 1e-05, 12, -17.87, -319.84, 0.00018, 4, 4, 253.46, 103.44, 0.49267, 8, 3.99, -61.15, 0.5062, 7, -25.58, 282.04, 7e-05, 12, -8.31, -283, 0.00106, 4, 4, 253.66, 79.81, 0.76058, 8, 4.19, -84.77, 0.23811, 7, -25.38, 258.42, 8e-05, 12, -7.49, -259.39, 0.00123, 4, 4, 268.5, -69.74, 0.87582, 8, 19.03, -234.33, 0.00026, 7, -10.53, 108.86, 0.12314, 17, -63.39, 228.31, 0.00078, 4, 4, 270.86, -89.93, 0.73826, 8, 21.39, -254.51, 0.00025, 7, -8.18, 88.67, 0.26072, 17, -67.65, 248.18, 0.00077, 4, 4, 290.22, -151.2, 0.05872, 8, 40.75, -315.78, 2e-05, 7, 11.18, 27.41, 0.94117, 17, -92.73, 307.33, 8e-05, 1, 18, 246.41, 0.79, 1, 2, 18, 230.37, -19.59, 0.99475, 19, -30.74, -18.12, 0.00525, 2, 18, 229.02, 21.71, 0.98117, 19, -30.08, 23.2, 0.01883, 2, 18, 231.93, 1.74, 0.99803, 19, -28.15, 3.11, 0.00197, 2, 18, 259.44, 0.14, 0.5312, 19, -0.74, 0.17, 0.4688, 3, 7, -586.28, 469.78, 0, 18, 267.14, -0.12, 0.18675, 19, 6.94, -0.45, 0.81325, 1, 19, 17.16, -0.32, 1, 2, 7, -609.46, 477.81, 0, 19, 31.48, -0.4, 1, 1, 17, 84.51, -26.16, 1, 5, 4, 107.87, 139.5, 0.00478, 7, -171.17, 318.1, 0, 12, 135.6, -325.3, 5e-05, 17, 116.37, 35.24, 0.98034, 43, -101.46, 575.09, 0.01483, 4, 4, 166.84, 127.6, 0.74408, 8, -82.63, -36.98, 0.08181, 17, 56.53, 41.49, 0.16281, 43, -42.48, 563.2, 0.0113, 4, 4, 90.57, 118.41, 0.00359, 7, -188.47, 297.02, 0, 12, 153.79, -304.98, 5e-05, 17, 131.59, 57.87, 0.99636, 3, 4, 238.93, 172.98, 0.05551, 8, -10.54, 8.4, 0.93641, 43, 29.61, 608.58, 0.00808, 3, 4, 209.23, 167.59, 0.30563, 8, -40.24, 3, 0.68045, 43, -0.1, 603.18, 0.01392, 3, 4, 185.92, 162.48, 0.5426, 8, -63.55, -2.1, 0.43904, 43, -23.4, 598.07, 0.01836, 4, 4, 165.74, 158.08, 0.64932, 8, -83.73, -6.5, 0.18459, 17, 60.51, 11.26, 0.14552, 43, -43.58, 593.67, 0.02057, 3, 4, 265.14, -182.96, 0.02633, 7, -13.9, -4.36, 0.96832, 43, 55.81, 252.63, 0.00534, 3, 4, 219.95, -169.07, 0.31212, 7, -59.09, 9.53, 0.67118, 43, 10.62, 266.52, 0.0167, 3, 4, 192.85, -161.77, 0.57284, 7, -86.19, 16.84, 0.40863, 43, -16.47, 273.83, 0.01853, 4, 4, 170.83, -157.79, 0.67081, 7, -108.21, 20.81, 0.18285, 12, 85.5, -25.58, 0.12595, 43, -38.49, 277.8, 0.02039, 5, 4, 130.36, 172.5, 2e-05, 7, -148.68, 351.11, 0, 12, 111.7, -357.31, 0, 17, 97.1, 0.25, 0.98199, 43, -78.96, 608.1, 0.01799, 3, 8, -74.42, 17.56, 0.09548, 17, 53.53, -13.58, 0.88739, 43, -34.28, 617.74, 0.01713, 3, 8, -29.54, 17.86, 0.34683, 17, 8.88, -18.13, 0.64262, 43, 10.61, 618.03, 0.01055, 3, 8, -51.42, 18.08, 0.24791, 17, 30.68, -16.28, 0.7381, 43, -11.27, 618.25, 0.01399, 5, 4, 143.49, 132.74, 0.51648, 7, -135.55, 311.35, 0, 12, 100.3, -317.02, 9e-05, 17, 80.26, 38.59, 0.47109, 43, -65.83, 568.34, 0.01234, 3, 4, 188.16, 123.79, 0.74196, 8, -61.31, -40.8, 0.24544, 43, -21.16, 559.38, 0.0126, 2, 17, 231.84, -5.92, 0.99297, 43, -212.5, 627.02, 0.00703, 2, 17, 165.66, -5.89, 0.98606, 43, -146.63, 620.71, 0.01394, 3, 12, 195.7, -336.55, 0, 17, 177.44, 32.38, 0.98686, 43, -161.98, 583.74, 0.01314, 2, 17, 234.63, 21.23, 0.99225, 43, -217.85, 600.25, 0.00775, 3, 17, 253.24, -8.47, 0.99093, 18, -30.88, -18.04, 0.0063, 43, -233.57, 631.58, 0.00277, 3, 7, -320.56, 379.55, 0, 17, 270.91, -11.76, 0.89257, 18, -13.01, -16.15, 0.10743, 3, 7, -336.89, 388.65, 0, 17, 288.03, -19.27, 0.43021, 18, 5.54, -18.46, 0.56979, 3, 7, -356.71, 396.38, 0, 17, 308.49, -25.09, 0.04629, 18, 26.81, -18.19, 0.95371, 2, 17, 316.73, 7.67, 0.0004, 18, 25.35, 15.55, 0.9996, 2, 17, 294.83, 11.89, 0.33228, 18, 3.16, 13.35, 0.66772, 2, 17, 275.72, 13.59, 0.89485, 18, -15.64, 9.52, 0.10515, 2, 17, 255.01, 16.73, 0.99642, 43, -237.72, 606.67, 0.00358, 2, 7, -379.95, 404.6, 0, 18, 51.43, -17.1, 1, 1, 18, 48.59, 14.88, 1, 5, 4, 130.58, -158.67, 0.00664, 8, -118.89, -323.25, 0, 12, 125.76, -26.44, 0.97497, 17, 65.48, 329.91, 1e-05, 43, -78.75, 276.93, 0.01838, 2, 12, 251.95, -20.72, 0.99837, 43, -204.58, 265.78, 0.00163, 1, 12, 276.72, -21.06, 1, 1, 12, 305.92, -14.84, 1, 1, 12, 331.83, -2.6, 1, 1, 12, 305.57, 11.56, 1, 1, 12, 274.38, 13.15, 1, 2, 12, 245.93, 11.07, 0.99773, 43, -197.19, 234.28, 0.00227, 2, 12, 105.89, 6.52, 0.98245, 43, -57.48, 244.85, 0.01756, 1, 12, 86.14, 37.08, 1, 4, 4, 109.75, -132.71, 0.00593, 8, -139.72, -297.29, 0, 12, 145.45, -53.27, 0.99405, 17, 88.68, 306.04, 1e-05, 2, 12, 141.29, 7.3, 0.98279, 43, -92.81, 242.55, 0.01721, 5, 4, 109.3, -160.55, 0.00142, 8, -140.17, -325.13, 0, 12, 147.09, -25.47, 0.9811, 17, 86.48, 333.8, 0, 43, -100.02, 275.04, 0.01748, 2, 12, 76.87, 8.29, 0.98376, 43, -28.41, 244.33, 0.01624, 3, 7, -78.53, -13.72, 0.0225, 12, 57.34, 10.2, 0.96208, 43, -8.82, 243.27, 0.01542, 3, 7, -54.57, -14.53, 0.14386, 12, 33.44, 12.04, 0.84344, 43, 15.15, 242.46, 0.01269, 3, 7, -28.56, -11.77, 0.3446, 12, 7.33, 10.4, 0.64545, 43, 41.15, 245.22, 0.00995, 5, 4, 159.17, -158.17, 0.33595, 8, -90.3, -322.75, 1e-05, 12, 97.17, -25.7, 0.64451, 17, 37.07, 326.71, 3e-05, 43, -50.16, 277.42, 0.01951], "hull": 100, "edges": [0, 198, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 36, 38, 42, 44, 54, 56, 56, 58, 62, 64, 64, 66, 66, 68, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 140, 142, 148, 150, 150, 152, 152, 154, 156, 158, 164, 166, 166, 168, 172, 174, 178, 180, 180, 182, 182, 184, 190, 192, 192, 194, 194, 196, 196, 198, 72, 74, 74, 76, 68, 70, 70, 72, 184, 186, 186, 188, 188, 190, 176, 178, 174, 176, 170, 172, 158, 160, 160, 162, 162, 164, 168, 170, 86, 88, 88, 90, 84, 86, 82, 84, 80, 82, 76, 78, 78, 80, 154, 156, 90, 92, 142, 144, 138, 140, 102, 104, 108, 110, 104, 106, 106, 108, 96, 98, 92, 94, 94, 96, 144, 146, 146, 148, 98, 100, 100, 102, 202, 206, 206, 204, 118, 120, 114, 116, 116, 118, 110, 112, 112, 114, 134, 136, 136, 138, 216, 172, 222, 74, 242, 246, 246, 244, 218, 248, 248, 220, 240, 254, 254, 252, 218, 256, 256, 258, 252, 260, 260, 262, 262, 264, 264, 266, 268, 270, 270, 272, 272, 274, 274, 258, 266, 276, 268, 278, 4, 6, 6, 8, 58, 60, 60, 62, 52, 54, 50, 52, 44, 46, 22, 24, 24, 26, 46, 48, 48, 50, 34, 36, 38, 40, 40, 42, 30, 32, 32, 34, 298, 16, 300, 54, 294, 302, 302, 296, 280, 304, 304, 282, 306, 308, 308, 310, 310, 312, 306, 314], "width": 562, "height": 751}}, "arm_Lb": {"arm_Lb": {"type": "mesh", "uvs": [0.2528, 1e-05, 0.41523, 0.0132, 0.52996, 0.09846, 0.61123, 0.15284, 0.59981, 0.20639, 0.57339, 0.26265, 0.52602, 0.3635, 0.55734, 0.37881, 0.60414, 0.42572, 0.58935, 0.44707, 0.66399, 0.51044, 0.74375, 0.55778, 0.8486, 0.60095, 0.93549, 0.6392, 0.98994, 0.68747, 0.99403, 0.75327, 0.97766, 0.82309, 0.94363, 0.89438, 0.89723, 0.94311, 0.79523, 0.98587, 0.71456, 0.99854, 0.65183, 0.99445, 0.57521, 0.9487, 0.46246, 0.91251, 0.38844, 0.88039, 0.31394, 0.82209, 0.27738, 0.74836, 0.26684, 0.63212, 0.25612, 0.5782, 0.23355, 0.52905, 0.18738, 0.51846, 0.14689, 0.47395, 0.14549, 0.45608, 0.09879, 0.42671, 0.05718, 0.37674, 0.07288, 0.32528, 0.08858, 0.27382, 0.06788, 0.24742, 0.00547, 0.19473, 0.01519, 0.16483, 0.20346, 0.01844, 0.25593, 0.42497, 0.30266, 0.46782, 0.35393, 0.53191, 0.41529, 0.61528, 0.44135, 0.38623, 0.44873, 0.42662, 0.50521, 0.49782, 0.58221, 0.5829, 0.76798, 0.71475, 0.82532, 0.84221, 0.48817, 0.77518, 0.6212, 0.87407, 0.7588, 0.92791, 0.28669, 0.17223, 0.36221, 0.22008, 0.36708, 0.28543], "triangles": [15, 50, 49, 53, 50, 17, 18, 53, 17, 19, 53, 18, 19, 20, 53, 20, 21, 53, 21, 22, 53, 53, 52, 50, 17, 50, 16, 16, 50, 15, 22, 52, 53, 22, 23, 52, 50, 52, 49, 49, 13, 14, 31, 32, 42, 47, 46, 9, 42, 46, 47, 47, 9, 10, 30, 31, 42, 29, 30, 42, 43, 42, 47, 29, 42, 43, 28, 29, 43, 48, 47, 10, 48, 10, 11, 43, 47, 48, 44, 43, 48, 28, 43, 44, 27, 28, 44, 12, 48, 11, 49, 12, 13, 49, 48, 12, 26, 27, 44, 51, 44, 48, 51, 48, 49, 26, 44, 51, 25, 26, 51, 52, 51, 49, 24, 25, 51, 24, 51, 52, 23, 24, 52, 49, 14, 15, 56, 55, 5, 36, 55, 56, 6, 56, 5, 45, 56, 6, 56, 35, 36, 41, 56, 45, 41, 35, 56, 34, 35, 41, 7, 46, 45, 7, 45, 6, 46, 7, 8, 41, 45, 46, 33, 34, 41, 9, 46, 8, 32, 33, 41, 42, 41, 46, 32, 41, 42, 1, 40, 0, 54, 1, 2, 54, 40, 1, 39, 40, 54, 2, 55, 54, 2, 3, 55, 4, 55, 3, 54, 38, 39, 37, 38, 54, 5, 55, 4, 55, 36, 37, 55, 37, 54], "vertices": [1, 16, 58.44, -17.82, 1, 1, 16, 45.37, -33.95, 1, 2, 16, 18.74, -35.53, 0.96457, 15, 90.53, -23.04, 0.03543, 2, 16, 1.25, -37.43, 0.81015, 15, 75.85, -32.72, 0.18985, 2, 16, -10.31, -29.26, 0.56889, 15, 61.83, -30.74, 0.43111, 2, 16, -21.57, -19.1, 0.1894, 15, 47.17, -26.84, 0.8106, 2, 15, 20.9, -19.86, 0.91647, 13, 143.21, -25.61, 0.08353, 2, 15, 16.72, -23.65, 0.80581, 13, 138.02, -27.82, 0.19419, 2, 15, 4.17, -29.07, 0.50643, 13, 124.38, -28.83, 0.49357, 2, 15, -1.37, -26.99, 0.34576, 13, 119.83, -25.05, 0.65424, 2, 15, -18.38, -35.75, 0.02305, 13, 100.89, -27.76, 0.97695, 2, 15, -31.21, -45.31, 1e-05, 13, 85.63, -32.59, 0.99999, 2, 13, 70.25, -40.77, 0.85172, 12, 258.32, 19.42, 0.14828, 2, 13, 56.9, -47.31, 0.66352, 12, 269.3, 29.45, 0.33648, 2, 13, 42.58, -49.09, 0.46194, 12, 282.55, 35.17, 0.53806, 2, 13, 26.27, -43.28, 0.21714, 12, 299.83, 34.17, 0.78286, 2, 13, 9.92, -34.68, 0.00987, 12, 317.95, 30.51, 0.99013, 1, 12, 336.25, 24.59, 1, 1, 12, 348.5, 17.65, 1, 1, 12, 358.58, 3.86, 1, 2, 13, -21.02, 12.98, 0.15, 12, 361.01, -6.56, 0.85, 2, 13, -17.14, 19.95, 0.3, 12, 359.25, -14.34, 0.7, 2, 13, -2.42, 24.57, 0.7, 12, 346.41, -22.9, 0.3, 1, 13, 11.61, 34.34, 1, 1, 13, 22.87, 39.96, 1, 1, 13, 40.56, 43.13, 1, 1, 13, 60.3, 40.37, 1, 1, 13, 89.26, 30.5, 1, 2, 15, -34.22, 16.29, 0.00342, 13, 102.97, 26.6, 0.99658, 2, 15, -21.19, 18.64, 0.11366, 13, 116.04, 24.55, 0.88634, 2, 15, -18.19, 24.34, 0.23152, 13, 120.75, 28.96, 0.76848, 2, 15, -6.29, 28.99, 0.51415, 13, 133.51, 29.45, 0.48585, 2, 15, -1.59, 28.99, 0.65126, 13, 137.95, 27.91, 0.34874, 3, 16, -29.79, 54.18, 0.00173, 15, 6.36, 34.57, 0.86455, 13, 147.29, 30.58, 0.13372, 3, 16, -15.77, 52.3, 0.02285, 15, 19.69, 39.3, 0.95282, 13, 161.44, 30.69, 0.02432, 3, 16, -4.95, 43.93, 0.10228, 15, 33.14, 36.81, 0.89699, 13, 173.33, 23.92, 0.00073, 2, 16, 5.87, 35.56, 0.36523, 15, 46.59, 34.31, 0.63477, 2, 16, 13.2, 34.42, 0.59094, 15, 53.63, 36.65, 0.40906, 2, 16, 29.13, 34.47, 0.8587, 15, 67.77, 43.97, 0.1413, 2, 16, 35.38, 29.54, 0.90996, 15, 75.58, 42.45, 0.09004, 1, 16, 57.27, -10.03, 1, 2, 15, 6.05, 14.77, 0.82657, 13, 140.52, 11.97, 0.17343, 2, 15, -5.44, 9.32, 0.29989, 13, 127.88, 10.58, 0.70011, 2, 15, -22.53, 3.51, 0.00359, 13, 109.83, 10.69, 0.99641, 1, 13, 86.59, 11.46, 1, 2, 15, 15.34, -8.97, 0.94055, 13, 141.52, -13.5, 0.05945, 2, 15, 4.69, -9.49, 0.68607, 13, 131.28, -10.5, 0.31393, 2, 15, -14.3, -15.88, 0.0503, 13, 111.25, -10.32, 0.9497, 1, 13, 86.88, -11.23, 1, 1, 13, 46.07, -20.43, 1, 2, 13, 12.21, -14.97, 0.18041, 12, 321.27, 10.94, 0.81959, 1, 13, 44.08, 18.19, 1, 2, 13, 13.75, 12.04, 0.89143, 12, 327.37, -15.41, 0.10857, 2, 13, -5.74, 1.03, 0.09695, 12, 342.99, 0.62, 0.90305, 2, 16, 16.89, 0.7, 0.99808, 15, 72.33, 8.34, 0.00192, 2, 16, 1.25, -1.41, 0.79215, 15, 59.39, -0.69, 0.20785, 1, 15, 42.19, -0.64, 1], "hull": 41, "edges": [0, 80, 0, 2, 6, 8, 12, 14, 34, 36, 40, 42, 50, 52, 56, 58, 72, 74, 74, 76, 76, 78, 78, 80, 2, 4, 4, 6, 14, 16, 66, 68, 64, 66, 62, 64, 58, 60, 60, 62, 68, 70, 70, 72, 16, 18, 18, 20, 42, 44, 44, 46, 36, 38, 38, 40, 30, 32, 32, 34, 46, 48, 48, 50, 26, 28, 28, 30, 24, 26, 20, 22, 22, 24, 52, 54, 54, 56, 82, 84, 84, 86, 86, 88, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 88, 102, 102, 104, 8, 10, 10, 12], "width": 126, "height": 263}}, "body": {"body": {"type": "mesh", "uvs": [0.33135, 0.01567, 0.3559, 0.01442, 0.53944, 0.00505, 0.58364, 0.0028, 0.62783, 0.00055, 0.65708, 0.01445, 0.67933, 0.0278, 0.69223, 0.04292, 0.7314, 0.0546, 0.75387, 0.0678, 0.75481, 0.08269, 0.7305, 0.08997, 0.73929, 0.09781, 0.76422, 0.10436, 0.77546, 0.11188, 0.77368, 0.11819, 0.85375, 0.11974, 0.93122, 0.12185, 0.93102, 0.1304, 0.91131, 0.13026, 0.90405, 0.14206, 0.8799, 0.16874, 0.86708, 0.18541, 0.86098, 0.1983, 0.89169, 0.21273, 0.90844, 0.23015, 0.90667, 0.25359, 0.88696, 0.27159, 0.84555, 0.285, 0.79784, 0.32274, 0.76468, 0.33819, 0.75232, 0.35089, 0.7528, 0.36388, 0.75658, 0.37477, 0.78594, 0.38288, 0.78711, 0.40062, 0.80361, 0.42618, 0.80617, 0.44307, 0.81999, 0.46298, 0.83328, 0.47504, 0.84725, 0.49336, 0.87436, 0.52549, 0.87938, 0.54849, 0.89225, 0.58161, 0.90555, 0.61977, 0.91285, 0.6578, 0.9166, 0.69602, 0.91868, 0.71246, 0.92439, 0.72873, 0.93059, 0.74826, 0.93926, 0.76244, 0.95065, 0.77686, 0.96626, 0.79365, 0.98313, 0.81125, 0.99846, 0.83496, 1, 0.85533, 0.97045, 0.9525, 0.96624, 0.99597, 0.96855, 0.99945, 0.83623, 0.99992, 0.74547, 0.85301, 0.75093, 0.81654, 0.74718, 0.80413, 0.71892, 0.78377, 0.70296, 0.76897, 0.69086, 0.74929, 0.68124, 0.72984, 0.66666, 0.71024, 0.65003, 0.69269, 0.60224, 0.65521, 0.55546, 0.62817, 0.50908, 0.59754, 0.4781, 0.5665, 0.47016, 0.54428, 0.44049, 0.54555, 0.44313, 0.56761, 0.43815, 0.59218, 0.43676, 0.62796, 0.44691, 0.66504, 0.45929, 0.7034, 0.46532, 0.7236, 0.46948, 0.7415, 0.46762, 0.75809, 0.4598, 0.77463, 0.45811, 0.7895, 0.46092, 0.8073, 0.4694, 0.8396, 0.4674, 0.8672, 0.42869, 0.93344, 0.42223, 0.9457, 0.40604, 1, 0.39228, 0.99973, 0.27253, 1, 0.24348, 0.9227, 0.23721, 0.90895, 0.20825, 0.85417, 0.20746, 0.83064, 0.21871, 0.81032, 0.23787, 0.7904, 0.24648, 0.77766, 0.24907, 0.76322, 0.24393, 0.74838, 0.23149, 0.73015, 0.21345, 0.71192, 0.17996, 0.68009, 0.14125, 0.6486, 0.09732, 0.61577, 0.00542, 0.53571, 1e-05, 0.50172, 0.00673, 0.47737, 0.0339, 0.44939, 0.09067, 0.41251, 0.11989, 0.39685, 0.17808, 0.37731, 0.17534, 0.37341, 0.19356, 0.35957, 0.22776, 0.35282, 0.23911, 0.35276, 0.27387, 0.3417, 0.30053, 0.33217, 0.30492, 0.32447, 0.29526, 0.31702, 0.28097, 0.29768, 0.22609, 0.29186, 0.18355, 0.28166, 0.15236, 0.26619, 0.14263, 0.24589, 0.16622, 0.22474, 0.21132, 0.20971, 0.2013, 0.19817, 0.18798, 0.18411, 0.17386, 0.16675, 0.17278, 0.15779, 0.15905, 0.15959, 0.14974, 0.15238, 0.20103, 0.14662, 0.26285, 0.14074, 0.25992, 0.13229, 0.27839, 0.12051, 0.23162, 0.11961, 0.21492, 0.11405, 0.20965, 0.10234, 0.22215, 0.09309, 0.25173, 0.08071, 0.26834, 0.07019, 0.28608, 0.05369, 0.29094, 0.0392, 0.29887, 0.02806, 0.30679, 0.01692, 0.83862, 0.1286, 0.22054, 0.15176, 0.26614, 0.14683, 0.39097, 0.06996, 0.58642, 0.06153, 0.58867, 0.07751, 0.5922, 0.09393, 0.59673, 0.10687, 0.40399, 0.08453, 0.4181, 0.10008, 0.42767, 0.11171, 0.43271, 0.12879, 0.60626, 0.11642, 0.62691, 0.12238, 0.43296, 0.12032, 0.28964, 0.13855, 0.33078, 0.12969, 0.74969, 0.14639, 0.65651, 0.11673, 0.71397, 0.131, 0.38384, 0.11934, 0.48685, 0.15588, 0.57164, 0.15474, 0.639, 0.14902, 0.38225, 0.15245, 0.63117, 0.13596, 0.40349, 0.13939, 0.49131, 0.14253, 0.47784, 0.1279, 0.46912, 0.11236, 0.46278, 0.09841, 0.45248, 0.08263, 0.44329, 0.0677, 0.55233, 0.14178, 0.5555, 0.12578, 0.54916, 0.10955, 0.54123, 0.09446, 0.5349, 0.07937, 0.52846, 0.06403, 0.20741, 0.16414, 0.29162, 0.187, 0.368, 0.17481, 0.48203, 0.18134, 0.52642, 0.2067, 0.56384, 0.18059, 0.67264, 0.1718, 0.77274, 0.17782, 0.35523, 0.29836, 0.47208, 0.28817, 0.58952, 0.28863, 0.68022, 0.29652, 0.78008, 0.29552, 0.52769, 0.25285, 0.52321, 0.2302, 0.287, 0.25068, 0.77217, 0.24875, 0.71135, 0.19366, 0.81138, 0.202, 0.62582, 0.2033, 0.59197, 0.2269, 0.59396, 0.25591, 0.64252, 0.27996, 0.74153, 0.28732, 0.83045, 0.2773, 0.86962, 0.22451, 0.87478, 0.2552, 0.74109, 0.2149, 0.67666, 0.22726, 0.66812, 0.24836, 0.68967, 0.26733, 0.82867, 0.2637, 0.84072, 0.24269, 0.81873, 0.22205, 0.75757, 0.27505, 0.33068, 0.19748, 0.42186, 0.20503, 0.45986, 0.2285, 0.46108, 0.25584, 0.40961, 0.28073, 0.3177, 0.28903, 0.22862, 0.28157, 0.18212, 0.25793, 0.19514, 0.23133, 0.24959, 0.21043, 0.30692, 0.21959, 0.37307, 0.22978, 0.23806, 0.2282, 0.2181, 0.24701, 0.2326, 0.26663, 0.29603, 0.27726, 0.36683, 0.26926, 0.387, 0.25073, 0.26887, 0.15683, 0.33131, 0.14427, 0.35905, 0.16449, 0.26109, 0.17329, 0.47086, 0.16693, 0.58438, 0.16712, 0.6765, 0.15991, 0.78647, 0.1604, 0.65271, 0.13444, 0.69533, 0.14391, 0.82402, 0.14637, 0.36679, 0.31577, 0.46585, 0.31482, 0.59522, 0.31767, 0.71292, 0.32363, 0.37832, 0.333, 0.36753, 0.35247, 0.35487, 0.37318, 0.42049, 0.37888, 0.48883, 0.37733, 0.54549, 0.38381, 0.61556, 0.38332, 0.2766, 0.36328, 0.70445, 0.38247, 0.67581, 0.34393, 0.64408, 0.36224, 0.57974, 0.33681, 0.56994, 0.36018, 0.46163, 0.33351, 0.43984, 0.35682, 0.2284, 0.38732, 0.31984, 0.4025, 0.40203, 0.41017, 0.46908, 0.41918, 0.54648, 0.41478, 0.63557, 0.4137, 0.72269, 0.40723, 0.43606, 0.53586, 0.46748, 0.53521, 0.64204, 0.53027, 0.80236, 0.52336, 0.24723, 0.54115, 0.12534, 0.54272, 0.37383, 0.43061, 0.34699, 0.45948, 0.33508, 0.51345, 0.54865, 0.48431, 0.54132, 0.43682, 0.54279, 0.4607, 0.68566, 0.43294, 0.69118, 0.45271, 0.33474, 0.48409, 0.55697, 0.5101, 0.55885, 0.53275, 0.71203, 0.49306, 0.34567, 0.53836, 0.70359, 0.47679, 0.2327, 0.41643, 0.19236, 0.43709, 0.15707, 0.46036, 0.13287, 0.48684, 0.12883, 0.51622, 0.17425, 0.60924, 0.31247, 0.72548, 0.31977, 0.74433, 0.32531, 0.76379, 0.33513, 0.78069, 0.32717, 0.79936, 0.35985, 0.59616, 0.41973, 0.72297, 0.42869, 0.74106, 0.4257, 0.76145, 0.41575, 0.77983, 0.41774, 0.79993, 0.43603, 0.84398, 0.36835, 0.9551, 0.30565, 0.84139, 0.13644, 0.57464, 0.25644, 0.56846, 0.35358, 0.56599, 0.27322, 0.60143, 0.57501, 0.55964, 0.64301, 0.62324, 0.73627, 0.70396, 0.75091, 0.72259, 0.7659, 0.74374, 0.79421, 0.76632, 0.8142, 0.78554, 0.81759, 0.805, 0.90976, 0.80082, 0.88634, 0.78053, 0.87469, 0.75939, 0.86636, 0.73873, 0.85803, 0.71662, 0.84803, 0.69548, 0.83283, 0.61348, 0.81386, 0.55225, 0.71337, 0.55345, 0.74018, 0.61575, 0.72231, 0.52724, 0.59858, 0.59036, 0.82558, 0.58286, 0.72529, 0.58589, 0.81436, 0.85111, 0.89933, 0.94954, 0.93937, 0.84337, 0.55849, 0.01369, 0.56919, 0.02248, 0.57532, 0.03244, 0.58321, 0.04212, 0.59776, 0.04987, 0.62007, 0.05668, 0.65431, 0.06493, 0.67867, 0.07094, 0.69279, 0.07675, 0.69985, 0.08632, 0.63513, 0.07629, 0.62977, 0.09638, 0.69565, 0.10194, 0.71707, 0.11646, 0.34228, 0.11254, 0.34425, 0.09509, 0.34876, 0.0755, 0.26432, 0.08355, 0.29103, 0.07641, 0.31892, 0.0653, 0.33621, 0.05454, 0.34642, 0.04526, 0.34838, 0.03472, 0.35192, 0.0243, 0.2423, 0.09086, 0.23206, 0.10134, 0.24946, 0.11552, 0.28232, 0.10136, 0.40124, 0.05516, 0.53528, 0.04743, 0.46082, 0.43685, 0.44966, 0.46299, 0.44469, 0.48519, 0.44345, 0.51275, 0.72178, 0.07292, 0.70974, 0.06348, 0.6845, 0.05769, 0.65525, 0.04925, 0.6369, 0.03899, 0.62084, 0.02725, 0.61052, 0.01666, 0.59561, 0.00772, 0.28008, 0.07182, 0.30155, 0.06171, 0.3089, 0.05405, 0.31794, 0.04443, 0.32133, 0.03448, 0.32585, 0.02503], "triangles": [139, 140, 373, 140, 372, 373, 140, 141, 372, 139, 373, 138, 10, 11, 381, 59, 57, 58, 59, 60, 345, 59, 345, 57, 345, 60, 344, 57, 345, 56, 56, 345, 55, 346, 345, 344, 346, 55, 345, 346, 54, 55, 60, 61, 344, 344, 329, 346, 329, 330, 346, 344, 61, 329, 346, 53, 54, 346, 330, 53, 61, 62, 329, 330, 52, 53, 62, 328, 329, 330, 328, 331, 330, 329, 328, 62, 63, 328, 52, 331, 51, 52, 330, 331, 63, 327, 328, 328, 327, 331, 63, 64, 327, 327, 332, 331, 331, 50, 51, 331, 332, 50, 64, 326, 327, 64, 65, 326, 332, 327, 333, 332, 49, 50, 327, 326, 333, 332, 333, 49, 89, 90, 316, 92, 316, 91, 92, 93, 316, 90, 91, 316, 89, 316, 88, 88, 315, 87, 315, 88, 316, 316, 94, 317, 93, 94, 316, 316, 317, 315, 94, 95, 317, 87, 315, 86, 317, 314, 315, 317, 308, 314, 95, 96, 317, 315, 85, 86, 315, 314, 85, 96, 97, 317, 317, 97, 308, 308, 97, 98, 308, 98, 99, 307, 308, 100, 100, 308, 99, 314, 84, 85, 308, 307, 314, 307, 313, 314, 314, 313, 84, 307, 100, 306, 84, 313, 83, 307, 306, 313, 313, 312, 83, 313, 306, 312, 65, 66, 326, 333, 48, 49, 66, 325, 326, 326, 325, 333, 325, 334, 333, 333, 334, 48, 66, 67, 325, 334, 47, 48, 67, 324, 325, 325, 324, 334, 324, 335, 334, 47, 335, 46, 47, 334, 335, 67, 68, 324, 339, 335, 324, 335, 45, 46, 45, 336, 44, 336, 45, 335, 339, 336, 335, 324, 323, 339, 69, 324, 68, 323, 324, 69, 69, 70, 323, 70, 341, 323, 70, 71, 341, 323, 343, 339, 323, 341, 343, 336, 43, 44, 336, 339, 342, 339, 343, 342, 336, 342, 43, 71, 72, 341, 72, 322, 341, 343, 341, 338, 341, 322, 338, 343, 337, 342, 343, 338, 337, 342, 337, 43, 337, 42, 43, 72, 73, 322, 73, 294, 322, 322, 280, 338, 322, 294, 280, 338, 340, 337, 338, 280, 340, 340, 281, 337, 337, 41, 42, 337, 281, 41, 73, 279, 294, 279, 380, 293, 279, 293, 294, 294, 293, 280, 340, 280, 295, 280, 293, 295, 340, 295, 281, 281, 40, 41, 281, 295, 40, 293, 287, 295, 295, 39, 40, 287, 297, 295, 295, 297, 39, 297, 289, 291, 290, 291, 288, 289, 297, 287, 297, 38, 39, 297, 291, 38, 291, 37, 38, 291, 290, 37, 290, 36, 37, 83, 312, 82, 100, 305, 306, 306, 305, 312, 100, 101, 305, 312, 311, 82, 312, 305, 311, 82, 311, 81, 101, 102, 305, 102, 304, 305, 305, 310, 311, 305, 304, 310, 311, 80, 81, 311, 310, 80, 102, 103, 304, 321, 310, 304, 310, 79, 80, 79, 310, 78, 78, 309, 77, 309, 78, 310, 103, 104, 304, 304, 104, 303, 310, 321, 309, 304, 303, 321, 105, 303, 104, 105, 106, 303, 77, 309, 76, 106, 318, 303, 318, 107, 283, 318, 106, 107, 321, 303, 319, 303, 318, 319, 321, 319, 309, 319, 320, 309, 309, 320, 76, 76, 320, 75, 319, 283, 282, 319, 318, 283, 319, 282, 320, 75, 320, 74, 282, 296, 320, 74, 296, 278, 74, 320, 296, 73, 278, 279, 73, 74, 278, 283, 302, 282, 283, 107, 302, 282, 286, 296, 282, 302, 286, 296, 286, 278, 278, 380, 279, 278, 286, 380, 107, 108, 302, 302, 301, 286, 302, 108, 301, 301, 292, 286, 379, 286, 292, 108, 109, 301, 301, 300, 292, 301, 109, 300, 292, 300, 285, 109, 110, 300, 300, 299, 285, 300, 110, 299, 285, 299, 284, 284, 299, 298, 284, 298, 272, 110, 111, 299, 299, 111, 298, 298, 111, 112, 112, 271, 298, 290, 277, 36, 290, 276, 277, 277, 35, 36, 276, 264, 277, 276, 262, 264, 35, 264, 34, 34, 264, 33, 264, 35, 277, 262, 266, 264, 264, 266, 32, 264, 32, 33, 271, 112, 113, 298, 271, 272, 272, 271, 258, 258, 271, 263, 263, 271, 114, 263, 114, 115, 263, 116, 117, 116, 263, 115, 271, 113, 114, 258, 263, 257, 117, 118, 263, 263, 118, 257, 118, 119, 257, 195, 248, 21, 248, 251, 21, 21, 251, 20, 248, 166, 251, 166, 149, 251, 166, 168, 15, 251, 149, 20, 149, 166, 15, 20, 149, 19, 168, 360, 15, 18, 19, 17, 149, 16, 19, 19, 16, 17, 149, 15, 16, 15, 360, 14, 360, 13, 14, 360, 12, 13, 241, 242, 243, 189, 130, 244, 189, 129, 130, 130, 188, 244, 130, 131, 188, 244, 241, 243, 244, 188, 241, 131, 132, 188, 241, 188, 151, 188, 132, 150, 133, 134, 132, 132, 135, 150, 132, 134, 135, 188, 150, 151, 242, 151, 164, 242, 241, 151, 151, 150, 136, 150, 135, 136, 151, 136, 164, 164, 165, 242, 136, 137, 164, 165, 164, 138, 164, 137, 138, 165, 138, 361, 293, 379, 287, 379, 293, 380, 380, 286, 379, 379, 378, 287, 378, 289, 287, 378, 377, 289, 377, 288, 289, 292, 285, 379, 379, 285, 378, 285, 284, 378, 378, 284, 377, 276, 290, 288, 288, 275, 276, 288, 291, 289, 377, 274, 288, 377, 284, 274, 288, 274, 275, 284, 273, 274, 284, 272, 273, 274, 273, 260, 274, 261, 275, 274, 260, 261, 260, 273, 259, 275, 262, 276, 275, 261, 262, 273, 272, 259, 259, 272, 258, 262, 261, 268, 261, 260, 268, 259, 270, 260, 259, 258, 270, 262, 268, 266, 32, 265, 31, 265, 32, 266, 260, 270, 268, 258, 257, 270, 265, 266, 267, 270, 269, 268, 266, 268, 267, 268, 269, 267, 257, 256, 270, 270, 256, 269, 257, 119, 256, 31, 265, 30, 267, 254, 265, 265, 255, 30, 265, 254, 255, 30, 255, 29, 254, 267, 253, 267, 269, 253, 269, 256, 253, 119, 120, 256, 120, 252, 256, 256, 252, 253, 254, 199, 255, 254, 253, 198, 360, 359, 12, 120, 121, 252, 255, 200, 29, 255, 199, 200, 29, 200, 28, 253, 197, 198, 254, 198, 199, 121, 196, 252, 121, 122, 196, 252, 196, 253, 253, 196, 197, 196, 227, 197, 122, 228, 196, 196, 228, 227, 228, 122, 229, 199, 211, 200, 198, 210, 199, 199, 210, 211, 200, 212, 28, 200, 211, 212, 122, 123, 229, 123, 124, 229, 229, 238, 228, 228, 239, 227, 228, 238, 239, 197, 201, 198, 198, 209, 210, 198, 201, 209, 227, 226, 197, 197, 226, 201, 211, 222, 212, 210, 218, 211, 211, 218, 222, 28, 212, 27, 229, 124, 230, 237, 229, 230, 229, 237, 238, 125, 230, 124, 227, 239, 226, 226, 239, 240, 210, 209, 218, 222, 219, 212, 27, 212, 214, 237, 203, 238, 238, 203, 239, 222, 204, 219, 222, 218, 204, 212, 219, 214, 27, 214, 26, 239, 203, 240, 209, 217, 218, 218, 217, 204, 230, 236, 237, 237, 236, 203, 125, 126, 230, 219, 220, 214, 219, 204, 220, 236, 230, 231, 217, 209, 208, 240, 225, 226, 226, 202, 201, 226, 225, 202, 26, 214, 25, 213, 25, 214, 209, 201, 208, 201, 202, 208, 203, 234, 240, 240, 234, 225, 236, 235, 203, 203, 233, 234, 203, 235, 233, 217, 216, 204, 216, 215, 204, 204, 221, 220, 204, 215, 221, 217, 208, 216, 230, 126, 231, 236, 231, 235, 126, 127, 231, 214, 220, 213, 220, 221, 213, 232, 235, 231, 208, 202, 192, 213, 24, 25, 233, 223, 234, 234, 224, 225, 234, 223, 224, 202, 225, 192, 225, 224, 192, 231, 127, 128, 235, 232, 233, 232, 231, 128, 208, 207, 216, 215, 207, 205, 215, 216, 207, 208, 192, 207, 24, 213, 206, 215, 206, 221, 24, 206, 23, 206, 213, 221, 233, 232, 223, 215, 205, 206, 232, 189, 223, 232, 128, 189, 128, 129, 189, 224, 191, 192, 192, 193, 207, 192, 191, 193, 223, 190, 224, 224, 190, 191, 207, 194, 205, 207, 193, 194, 205, 195, 206, 206, 195, 23, 23, 195, 22, 223, 189, 190, 205, 194, 195, 190, 189, 243, 246, 193, 245, 170, 246, 245, 182, 171, 170, 190, 245, 191, 193, 191, 245, 193, 246, 194, 246, 170, 171, 182, 170, 176, 194, 247, 195, 248, 195, 247, 247, 250, 166, 248, 247, 166, 189, 244, 243, 190, 243, 245, 194, 246, 247, 246, 172, 247, 246, 171, 172, 243, 173, 245, 245, 173, 170, 243, 242, 173, 247, 172, 250, 173, 175, 170, 170, 175, 176, 176, 175, 160, 176, 160, 177, 171, 182, 174, 171, 174, 172, 174, 183, 162, 183, 174, 182, 173, 242, 175, 172, 249, 250, 172, 174, 249, 166, 250, 168, 242, 165, 175, 250, 249, 168, 182, 176, 183, 183, 161, 162, 183, 176, 177, 165, 169, 175, 175, 169, 160, 174, 162, 249, 249, 167, 168, 249, 162, 167, 168, 167, 360, 160, 163, 177, 160, 169, 163, 183, 177, 184, 162, 161, 167, 167, 359, 360, 22, 195, 21, 138, 374, 361, 165, 361, 169, 163, 178, 177, 177, 178, 184, 161, 184, 156, 161, 183, 184, 169, 159, 163, 163, 159, 178, 159, 169, 158, 167, 156, 358, 167, 358, 359, 167, 161, 156, 169, 361, 158, 361, 362, 158, 159, 179, 178, 178, 179, 184, 159, 158, 179, 179, 185, 184, 184, 155, 156, 184, 185, 155, 359, 358, 356, 356, 358, 357, 156, 155, 358, 359, 356, 11, 158, 180, 179, 179, 180, 185, 358, 155, 357, 155, 185, 154, 155, 154, 357, 359, 11, 12, 372, 371, 374, 357, 354, 355, 355, 356, 357, 371, 364, 374, 374, 364, 362, 362, 157, 158, 158, 157, 180, 364, 365, 362, 362, 363, 157, 362, 365, 363, 180, 186, 185, 185, 186, 154, 143, 371, 142, 371, 143, 364, 11, 356, 381, 356, 355, 381, 363, 152, 157, 157, 181, 180, 157, 152, 181, 364, 143, 365, 186, 180, 187, 143, 389, 365, 143, 144, 389, 180, 181, 187, 186, 187, 154, 187, 153, 154, 154, 153, 357, 355, 354, 381, 365, 366, 363, 365, 389, 366, 153, 352, 357, 357, 353, 354, 357, 352, 353, 363, 366, 152, 354, 382, 381, 381, 382, 9, 389, 390, 366, 389, 144, 390, 354, 383, 382, 354, 353, 383, 144, 145, 390, 366, 367, 152, 152, 375, 181, 152, 367, 375, 382, 8, 9, 187, 181, 376, 390, 391, 366, 366, 391, 367, 353, 384, 383, 353, 352, 384, 187, 376, 153, 376, 181, 375, 382, 383, 8, 390, 145, 391, 153, 376, 351, 153, 351, 352, 351, 376, 350, 383, 7, 8, 383, 384, 7, 352, 351, 384, 367, 368, 375, 370, 376, 375, 391, 392, 367, 367, 392, 368, 391, 145, 392, 145, 146, 392, 351, 385, 384, 351, 350, 385, 384, 385, 7, 375, 368, 369, 375, 369, 370, 376, 370, 2, 376, 349, 350, 376, 348, 349, 376, 347, 348, 376, 2, 347, 2, 370, 1, 369, 368, 393, 368, 392, 393, 392, 146, 393, 385, 6, 7, 385, 350, 386, 146, 147, 393, 350, 349, 386, 385, 386, 6, 393, 394, 369, 369, 394, 370, 393, 147, 394, 349, 348, 386, 147, 148, 394, 386, 5, 6, 348, 387, 386, 386, 387, 5, 394, 0, 370, 394, 148, 0, 370, 0, 1, 348, 347, 387, 347, 388, 387, 5, 387, 4, 387, 388, 4, 347, 3, 388, 347, 2, 3, 388, 3, 4, 361, 374, 362, 372, 142, 371, 141, 142, 372, 138, 373, 374, 373, 372, 374, 381, 9, 10], "vertices": [2, 6, 131.85, 67.85, 0.9838, 41, -253.8, 150.38, 0.0162, 2, 6, 132.39, 55.77, 0.9825, 41, -253.26, 138.3, 0.0175, 2, 6, 136.46, -34.54, 0.96946, 41, -249.19, 47.99, 0.03054, 3, 6, 137.44, -56.28, 0.97451, 7, 226.61, 140.9, 0.00067, 41, -248.21, 26.25, 0.02483, 2, 6, 138.42, -78.03, 0.99863, 7, 229.44, 119.32, 0.00137, 1, 6, 113.41, -89.06, 1, 2, 6, 89.75, -96.85, 0.99901, 42, -222.84, -14.32, 0.00099, 4, 6, 63.75, -99.77, 0.84425, 5, 128.4, -100.73, 0.10187, 42, -248.84, -17.24, 0.00082, 54, 75.13, 8.34, 0.05306, 6, 6, 41.81, -116.06, 0.59039, 5, 106.22, -116.69, 0.19843, 4, 415.46, -105.42, 0.00099, 7, 136.42, 73.19, 0.00702, 42, -270.77, -33.53, 0.0018, 54, 52.94, -7.62, 0.20138, 6, 6, 18.4, -123.99, 0.40455, 5, 82.69, -124.26, 0.22086, 4, 392.81, -115.31, 0.00643, 7, 113.77, 63.29, 0.02813, 42, -294.18, -41.46, 0.00162, 54, 29.41, -15.19, 0.3384, 6, 6, -6.49, -121.19, 0.31502, 5, 57.84, -121.08, 0.1962, 4, 367.78, -114.65, 0.02164, 7, 88.74, 63.95, 0.04811, 42, -319.07, -38.66, 0.0007, 54, 4.57, -12.01, 0.41832, 5, 6, -17.09, -107.92, 0.27744, 5, 47.45, -107.64, 0.25038, 4, 356.08, -102.33, 0.05952, 7, 77.04, 76.28, 0.07528, 54, -5.82, 1.43, 0.33739, 5, 6, -30.71, -110.43, 0.16576, 5, 33.78, -109.95, 0.22616, 4, 342.72, -106, 0.13474, 7, 63.68, 72.61, 0.18684, 54, -19.49, -0.88, 0.2865, 5, 6, -43.19, -121, 0.0868, 5, 21.15, -120.32, 0.14536, 4, 331.19, -117.59, 0.20401, 7, 52.15, 61.02, 0.35105, 54, -32.13, -11.25, 0.21278, 5, 6, -56.44, -124.77, 0.03577, 5, 7.84, -123.89, 0.06237, 4, 318.31, -122.47, 0.27496, 7, 39.27, 56.14, 0.53116, 54, -45.43, -14.82, 0.09574, 2, 4, 307.75, -121.14, 0.31679, 7, 28.71, 57.47, 0.68321, 2, 4, 303.42, -159.81, 0.14761, 7, 24.38, 18.79, 0.85239, 1, 7, 19.17, -18.58, 1, 2, 4, 283.85, -196.45, 0.0004, 7, 4.81, -17.85, 0.9996, 1, 7, 5.48, -8.31, 1, 3, 4, 264.86, -182.51, 0.02633, 7, -14.18, -3.91, 0.96832, 43, 55.54, 253.08, 0.00534, 3, 4, 220.57, -168.81, 0.31212, 7, -58.47, 9.79, 0.67118, 43, 11.24, 266.78, 0.0167, 4, 4, 192.86, -161.35, 0.5442, 7, -86.18, 17.25, 0.3882, 20, 76.86, -74.81, 0.04907, 43, -16.47, 274.24, 0.01853, 4, 4, 171.33, -157.43, 0.61582, 7, -107.71, 21.18, 0.16787, 20, 55.34, -70.89, 0.19592, 43, -37.99, 278.16, 0.02039, 4, 4, 146.45, -171.23, 0.7483, 7, -132.59, 7.38, 0.0373, 20, 30.45, -84.68, 0.1964, 43, -62.87, 264.37, 0.018, 3, 4, 116.82, -178.04, 0.786, 20, 0.83, -91.49, 0.1965, 43, -92.5, 257.56, 0.0175, 3, 4, 77.5, -175.42, 0.7872, 20, -38.5, -88.88, 0.1968, 43, -131.82, 260.17, 0.016, 4, 4, 47.71, -164.52, 0.78927, 3, 193.6, -146.85, 0.00218, 20, -68.29, -77.98, 0.19786, 43, -161.62, 271.07, 0.01069, 4, 4, 26.08, -143.45, 0.72569, 3, 167.3, -132.01, 0.06925, 20, -89.92, -56.91, 0.19873, 43, -183.25, 292.14, 0.00633, 3, 4, -36.28, -117.51, 0.44769, 3, 100.38, -122.88, 0.45584, 11, -132.37, 39.53, 0.09647, 3, 4, -61.49, -100.29, 0.21957, 3, 71.6, -112.68, 0.56323, 11, -104.24, 27.66, 0.2172, 3, 4, -82.56, -93.35, 0.09189, 3, 49.46, -111.36, 0.53311, 11, -82.21, 25.05, 0.375, 4, 4, -104.38, -92.61, 0.03248, 3, 28.18, -116.22, 0.38056, 11, -60.68, 28.67, 0.58694, 36, -142.67, 55.55, 2e-05, 4, 4, -122.76, -93.62, 0.00838, 3, 10.67, -121.91, 0.21441, 11, -42.87, 33.32, 0.76684, 36, -124.27, 54.73, 0.01036, 5, 4, -137, -107.24, 0.00183, 3, 0.38, -138.71, 0.10067, 8, -386.47, -271.82, 0, 11, -31.62, 49.5, 0.85458, 36, -108.75, 66.87, 0.04293, 3, 3, -28.64, -145.6, 0.02221, 11, -2.24, 54.68, 0.81703, 36, -79.15, 63.15, 0.16075, 2, 11, 38.97, 69.26, 0.46512, 36, -35.47, 64.9, 0.53488, 2, 11, 66.82, 74.89, 0.22386, 36, -7.2, 62.05, 0.77614, 2, 11, 98.85, 86.7, 0.05686, 36, 26.89, 63.88, 0.94314, 2, 11, 117.88, 96.21, 0.01841, 36, 47.88, 67.35, 0.98159, 2, 11, 147.24, 107.68, 0.00081, 36, 79.31, 69.63, 0.99919, 1, 36, 134.65, 74.88, 1, 1, 36, 173.26, 71.74, 1, 1, 36, 229.27, 69.92, 1, 1, 36, 293.68, 67.1, 1, 1, 36, 357.44, 61.42, 1, 1, 36, 421.29, 53.99, 1, 2, 36, 448.79, 51.02, 0.99796, 37, -68.61, 47.4, 0.00204, 2, 36, 476.24, 49.84, 0.94477, 37, -41.13, 47.7, 0.05523, 2, 36, 509.17, 48.1, 0.6021, 37, -8.15, 47.73, 0.3979, 2, 36, 533.36, 48.84, 0.22338, 37, 15.96, 49.77, 0.77662, 2, 36, 558.14, 50.83, 0.03658, 37, 40.6, 53.08, 0.96342, 1, 37, 69.39, 58.08, 1, 1, 37, 99.59, 63.56, 1, 1, 37, 139.97, 67.37, 1, 1, 37, 174.13, 65.02, 1, 1, 37, 335.52, 36.02, 1, 1, 37, 408.11, 27.39, 1, 1, 37, 414.04, 27.98, 1, 2, 8, -1423.78, -249.94, 0, 37, 409.05, -36.01, 1, 2, 8, -1175.11, -216.98, 0, 37, 159.12, -57.57, 1, 2, 8, -1113.98, -222.36, 0, 37, 98.3, -49.4, 1, 3, 8, -1093.05, -221.47, 0, 36, 589.33, -53.42, 0.00147, 37, 77.35, -49.33, 0.99853, 3, 8, -1058.26, -209.3, 0, 36, 553.51, -62.07, 0.09569, 37, 42.04, -59.9, 0.90431, 3, 8, -1033.06, -202.68, 0, 36, 527.77, -66.16, 0.31377, 37, 16.56, -65.36, 0.68623, 3, 8, -999.75, -198.29, 0, 36, 494.19, -67.21, 0.71011, 37, -16.92, -68.22, 0.28989, 3, 8, -966.88, -195.09, 0, 36, 461.16, -67.14, 0.94496, 37, -49.9, -69.92, 0.05504, 3, 8, -933.64, -189.49, 0, 36, 427.53, -69.4, 0.99855, 37, -83.36, -73.99, 0.00145, 2, 8, -903.81, -182.75, 0, 36, 397.18, -73.14, 1, 2, 8, -839.84, -162.41, 0, 36, 331.51, -87.04, 1, 2, 8, -793.41, -141.76, 0, 36, 283.26, -102.96, 1, 3, 8, -740.97, -121.59, 0, 38, 298.09, 103.24, 0.05932, 36, 229.07, -117.83, 0.94068, 4, 9, 319.33, 39.79, 0.10039, 8, -688.18, -108.9, 0, 38, 244.34, 96.27, 0.20246, 36, 175.28, -125.21, 0.69715, 4, 9, 283.02, 30.19, 0.17062, 8, -650.69, -106.72, 0, 38, 206.89, 98.1, 0.42132, 36, 137.76, -123.66, 0.40805, 4, 9, 287.36, 16.31, 0.17165, 8, -652.18, -92.25, 0, 38, 206.83, 83.56, 0.46066, 36, 137.81, -138.2, 0.36769, 4, 9, 323.81, 23.32, 0.06958, 8, -689.29, -91.88, 0, 38, 243.63, 79.22, 0.75952, 36, 174.7, -142.26, 0.1709, 2, 8, -730.44, -87.62, 0, 38, 284.01, 70.59, 1, 2, 8, -790.5, -84.27, 0.01084, 38, 343.26, 60.84, 0.98916, 2, 8, -852.98, -86.41, 0, 38, 405.51, 56.28, 1, 2, 8, -917.68, -89.53, 0, 38, 470.06, 52.48, 1, 2, 8, -951.72, -90.94, 0, 38, 504, 50.24, 1, 3, 8, -981.88, -91.61, 0, 38, 534.01, 47.68, 0.96675, 39, -42.84, 39.99, 0.03325, 3, 8, -1009.7, -89.47, 0, 38, 561.39, 42.58, 0.70361, 39, -14.89, 40.43, 0.29639, 3, 8, -1037.31, -84.44, 0, 38, 588.26, 34.63, 0.20543, 39, 13.12, 37.99, 0.79457, 3, 8, -1062.24, -82.51, 0, 38, 612.79, 30.04, 0.01307, 39, 38.16, 38.38, 0.98693, 2, 8, -1092.19, -82.54, 0, 39, 68.03, 41.18, 1, 2, 8, -1146.62, -84.22, 0, 39, 122.17, 47.91, 1, 2, 8, -1192.92, -81.19, 0, 39, 168.64, 49.18, 1, 1, 39, 280.98, 35.8, 1, 1, 39, 301.77, 33.67, 1, 1, 39, 393.48, 30.23, 1, 1, 39, 393.35, 23.54, 1, 2, 8, -1411.73, 23.19, 0, 39, 396.65, -34.44, 1, 2, 8, -1281.29, 31.47, 0, 39, 267.33, -54.78, 1, 2, 8, -1258.06, 33.47, 0, 39, 244.34, -58.94, 1, 1, 39, 152.88, -77.41, 1, 1, 39, 113.32, -79.7, 1, 2, 38, 629.82, -90.02, 0.00566, 39, 78.88, -75.9, 0.99434, 2, 38, 598.18, -75.78, 0.09368, 39, 44.92, -68.24, 0.90632, 2, 38, 577.67, -68.41, 0.28595, 39, 23.27, -65.1, 0.71405, 2, 38, 553.9, -63.5, 0.61811, 39, -1.08, -65.02, 0.38189, 2, 38, 528.91, -62.2, 0.88454, 39, -25.91, -68.71, 0.11546, 2, 38, 497.76, -63.53, 0.99119, 39, -56.28, -76.22, 0.00881, 1, 38, 466.2, -67.55, 1, 1, 38, 410.95, -75.52, 1, 1, 38, 355.88, -86.08, 1, 1, 38, 298.21, -98.8, 1, 1, 38, 158.68, -122.53, 1, 1, 38, 101.91, -116.49, 1, 2, 8, -528.3, 112.8, 0, 38, 62, -107.09, 1, 2, 10, 131.63, -108.05, 0.00153, 38, 17.58, -86.95, 0.99847, 2, 10, 66.12, -90.46, 0.1553, 38, -39.44, -50.37, 0.8447, 2, 10, 37.91, -80.54, 0.34875, 38, -63.29, -32.38, 0.65125, 4, 4, -114.51, 186.87, 0.00057, 3, -53.1, 151.36, 0.00356, 10, 1.09, -57.76, 0.74552, 38, -91.44, 0.48, 0.25035, 4, 4, -107.91, 187.9, 0.00138, 3, -46.98, 154.05, 0.00743, 10, -5.18, -60.08, 0.77571, 38, -98.11, 0.16, 0.21548, 4, 4, -85.05, 178.03, 0.00934, 3, -22.36, 150.35, 0.04091, 10, -29.54, -54.96, 0.82029, 38, -119.74, 12.41, 0.12946, 5, 4, -74.46, 160.96, 0.03163, 3, -7.76, 136.56, 0.12117, 9, -16.69, -135.86, 0.00303, 10, -43.32, -40.34, 0.78165, 38, -128.43, 30.52, 0.06252, 5, 4, -74.61, 155.45, 0.03907, 3, -6.49, 131.19, 0.1463, 9, -17.63, -130.43, 0.00674, 10, -44.26, -34.91, 0.76779, 38, -127.69, 35.98, 0.0401, 5, 4, -56.78, 137.78, 0.10547, 3, 15.26, 118.67, 0.30685, 9, -38.62, -116.66, 0.04309, 10, -65.25, -21.14, 0.54196, 38, -143.51, 55.46, 0.00262, 4, 4, -41.36, 124.15, 0.22089, 3, 33.66, 109.44, 0.41984, 9, -56.45, -106.37, 0.04147, 10, -83.08, -10.85, 0.31779, 4, 4, -28.51, 121.47, 0.36027, 3, 46.77, 110.13, 0.44135, 9, -69.58, -106.3, 0.02395, 10, -96.21, -10.78, 0.17443, 4, 4, -15.79, 125.57, 0.50859, 3, 58.01, 117.35, 0.39637, 9, -81.22, -112.85, 0.01131, 10, -107.85, -17.33, 0.08373, 5, 4, 16.98, 131.04, 0.65163, 3, 88.3, 131.03, 0.14326, 9, -112.25, -124.74, 0.00016, 23, -102.28, 41.4, 0.19876, 43, -192.34, 566.64, 0.00619, 4, 4, 27.95, 157.2, 0.73162, 3, 92.21, 159.12, 0.05687, 23, -91.32, 67.55, 0.19712, 43, -181.37, 592.79, 0.01438, 4, 4, 46, 177.05, 0.76352, 3, 104.58, 182.92, 0.01977, 23, -73.27, 87.4, 0.19582, 43, -163.32, 612.64, 0.02088, 3, 4, 72.65, 191, 0.78124, 23, -46.62, 101.35, 0.19531, 43, -136.67, 626.59, 0.02344, 3, 4, 106.95, 194.19, 0.78211, 23, -12.32, 104.54, 0.19553, 43, -102.37, 629.78, 0.02237, 4, 4, 141.96, 181.18, 0.7473, 8, -107.51, 16.59, 0.03564, 23, 22.69, 91.53, 0.19573, 43, -67.37, 616.77, 0.02133, 4, 4, 166.22, 158.2, 0.6101, 8, -83.25, -6.38, 0.17344, 23, 46.95, 68.55, 0.19589, 43, -43.1, 593.79, 0.02057, 4, 4, 185.83, 162.19, 0.51547, 8, -63.64, -2.39, 0.41709, 23, 66.56, 72.54, 0.04908, 43, -23.49, 597.78, 0.01836, 3, 4, 209.71, 167.59, 0.30563, 8, -39.76, 3.01, 0.68045, 43, 0.39, 603.18, 0.01392, 3, 4, 239.18, 173.13, 0.05551, 8, -10.29, 8.55, 0.93641, 43, 29.86, 608.72, 0.00808, 1, 8, 4.77, 8.4, 1, 1, 8, 2.06, 15.19, 1, 1, 8, 14.36, 19.15, 1, 2, 4, 272.4, 158.46, 0.07552, 8, 22.93, -6.13, 0.92448, 2, 4, 280.94, 128.06, 0.22618, 8, 31.47, -36.52, 0.77382, 5, 6, -58.04, 127.61, 0.05261, 5, 10.11, 128.48, 0.03878, 4, 295.19, 128.85, 0.19577, 8, 45.72, -35.74, 0.56264, 53, 91.2, 19.21, 0.1502, 5, 6, -39.56, 116.16, 0.19384, 5, 28.41, 116.75, 0.17471, 4, 314.58, 119.01, 0.11414, 8, 65.11, -45.57, 0.29854, 53, 69.87, 15.03, 0.21877, 6, 6, -35.12, 138.46, 0.25719, 5, 33.19, 138.98, 0.16022, 4, 317.1, 141.61, 0.03742, 8, 67.63, -22.97, 0.23582, 42, -347.71, 220.99, 0.00091, 53, 81.79, -4.33, 0.30843, 6, 6, -24.81, 145.28, 0.27, 5, 43.61, 145.64, 0.17869, 4, 326.8, 149.29, 0.03022, 8, 77.33, -15.3, 0.2121, 42, -337.39, 227.81, 0.00194, 53, 78.87, -16.35, 0.30705, 6, 6, -4.96, 145.26, 0.29911, 5, 63.45, 145.32, 0.21468, 4, 346.57, 150.96, 0.01872, 8, 97.1, -13.62, 0.15621, 42, -317.55, 227.79, 0.00268, 53, 64.31, -29.84, 0.3086, 6, 6, 9.66, 137.23, 0.34209, 5, 77.95, 137.07, 0.23924, 4, 361.83, 144.21, 0.01315, 8, 112.36, -20.37, 0.12145, 42, -302.92, 219.76, 0.0022, 53, 48.13, -33.9, 0.28187, 6, 6, 28.44, 120.31, 0.51704, 5, 96.47, 119.86, 0.22153, 4, 381.98, 128.95, 0.00541, 8, 132.51, -35.63, 0.0615, 42, -284.15, 202.84, 0.00111, 53, 22.86, -34.27, 0.1934, 6, 6, 44.93, 110.03, 0.71583, 5, 112.8, 109.33, 0.1589, 4, 399.29, 120.11, 0.0006, 8, 149.82, -44.47, 0.00826, 42, -267.65, 192.56, 0.00119, 53, 3.77, -37.95, 0.11522, 3, 6, 71.33, 97.9, 0.97788, 5, 139.01, 96.8, 0.02091, 42, -241.25, 180.43, 0.00121, 2, 6, 95.17, 92.41, 0.99912, 42, -217.42, 174.94, 0.00088, 1, 6, 113.23, 86.17, 1, 1, 6, 131.3, 79.93, 1, 2, 4, 288.88, -151.82, 0.14267, 7, 9.84, 26.78, 0.85733, 2, 4, 263.34, 149.39, 0.09212, 8, 13.87, -15.19, 0.90788, 3, 4, 270.64, 126.93, 0.19628, 8, 21.17, -37.66, 0.7853, 43, 61.32, 562.52, 0.01841, 2, 6, 37.62, 51, 0.89154, 5, 104.58, 50.42, 0.10846, 2, 6, 39.38, -44.83, 0.85194, 5, 104.88, -45.43, 0.14806, 3, 6, 12.6, -42.43, 0.60135, 5, 78.13, -42.61, 0.39578, 4, 380.07, -34.54, 0.00287, 3, 6, -15, -40.54, 0.28401, 5, 50.57, -40.31, 0.66772, 4, 352.42, -35.02, 0.04827, 2, 5, 28.73, -39.33, 0.72322, 4, 330.59, -36.25, 0.27678, 3, 6, 12.51, 47.92, 0.64785, 5, 79.43, 47.72, 0.34626, 4, 372.28, 55.46, 0.00589, 3, 6, -14.3, 44.52, 0.32593, 5, 52.57, 44.74, 0.61435, 4, 345.86, 49.79, 0.05972, 3, 6, -34.29, 42.45, 0.04839, 5, 32.56, 42.97, 0.693, 4, 326.12, 46.03, 0.25862, 2, 5, 3.79, 44.71, 0.28408, 4, 297.33, 44.87, 0.71592, 2, 5, 12.18, -41.58, 0.48255, 4, 314.35, -40.15, 0.51745, 2, 5, 0.82, -50.04, 0.28166, 4, 303.9, -49.71, 0.71834, 2, 5, 17.86, 42.53, 0.5246, 4, 311.55, 44.11, 0.4754, 2, 4, 284.03, 114.92, 0.33684, 8, 34.56, -49.66, 0.66316, 4, 6, -58.16, 92.96, 0.04183, 5, 9.46, 93.84, 0.17937, 4, 298.03, 94.32, 0.406, 8, 48.56, -70.26, 0.37281, 3, 4, 260.93, -107.4, 0.47294, 7, -18.11, 71.21, 0.50103, 43, 51.6, 328.2, 0.02603, 3, 5, 8.14, -65.62, 0.34816, 4, 312.75, -64.47, 0.56757, 7, 33.71, 114.13, 0.08427, 2, 4, 287.54, -91.24, 0.59248, 7, 8.5, 87.36, 0.40752, 4, 6, -44.24, 65.19, 0.05773, 5, 22.95, 65.86, 0.47557, 4, 314.26, 67.84, 0.39005, 8, 64.79, -96.74, 0.07665, 2, 4, 250.66, 20.66, 0.97338, 43, 41.34, 456.26, 0.02662, 2, 4, 250.75, -20.5, 0.97274, 43, 41.43, 415.09, 0.02726, 3, 4, 258.89, -53.57, 0.90232, 7, -20.15, 125.04, 0.07181, 43, 49.57, 382.03, 0.02587, 3, 4, 258.68, 71.09, 0.86531, 8, 9.21, -93.49, 0.11026, 43, 49.36, 506.68, 0.02443, 3, 4, 281, -50.75, 0.92936, 7, 1.96, 127.85, 0.05018, 43, 71.68, 384.84, 0.02045, 3, 4, 280.17, 59.82, 0.97691, 8, 30.7, -104.76, 0.00219, 43, 70.85, 495.41, 0.0209, 2, 4, 272.98, 17.5, 0.97892, 43, 63.66, 453.1, 0.02108, 3, 5, 2.1, 22.84, 0.26994, 4, 297.85, 22.93, 0.70999, 43, 88.52, 458.53, 0.02007, 3, 5, 28.57, 23.24, 0.74863, 4, 324.14, 25.99, 0.23113, 43, 114.82, 461.58, 0.02024, 4, 6, -14.32, 22.67, 0.2512, 5, 52.21, 22.88, 0.70834, 4, 347.7, 28.02, 0.01995, 43, 138.38, 463.61, 0.02051, 3, 6, 12.62, 24.18, 0.69039, 5, 79.18, 23.99, 0.28871, 43, 165.09, 467.42, 0.0209, 2, 6, 38.09, 25.35, 0.97993, 43, 190.37, 470.75, 0.02007, 2, 4, 272.93, -12.12, 0.97844, 43, 63.6, 423.48, 0.02156, 3, 5, 0.18, -14.95, 0.27899, 4, 299.73, -14.85, 0.70063, 43, 90.41, 420.74, 0.02038, 3, 5, 27.62, -15.86, 0.81214, 4, 327.13, -13, 0.16769, 43, 117.81, 422.6, 0.02017, 4, 6, -12.67, -15.92, 0.15961, 5, 53.28, -15.73, 0.81974, 4, 352.64, -10.29, 0.0001, 43, 143.32, 425.3, 0.02055, 3, 6, 12.88, -16.16, 0.73098, 5, 78.82, -16.36, 0.24882, 43, 168.8, 427.24, 0.0202, 2, 6, 38.86, -16.41, 0.97986, 43, 194.7, 429.21, 0.02014, 3, 4, 242.84, 156.68, 0.06731, 8, -6.63, -7.9, 0.91767, 43, 33.51, 592.27, 0.01503, 4, 4, 202.62, 117.59, 0.48198, 8, -46.85, -46.99, 0.29099, 23, 83.35, 27.94, 0.19324, 43, -6.7, 553.18, 0.03379, 4, 4, 221.44, 79.67, 0.67644, 8, -28.03, -84.92, 0.09665, 23, 102.18, -9.98, 0.19327, 43, 12.12, 515.26, 0.03364, 3, 4, 208.01, 24.91, 0.77022, 23, 88.75, -64.74, 0.19256, 43, -1.31, 460.5, 0.03722, 4, 4, 164.46, 5.3, 0.7776, 23, 45.19, -84.34, 0.0864, 20, 48.46, 91.85, 0.096, 43, -44.86, 440.9, 0.04, 3, 4, 207.51, -14.79, 0.76968, 20, 91.51, 71.76, 0.19242, 43, -1.81, 420.8, 0.0379, 4, 4, 219.92, -68.16, 0.72276, 7, -59.12, 110.44, 0.04854, 20, 103.92, 18.38, 0.19283, 43, 10.6, 367.43, 0.03587, 4, 4, 207.63, -116.21, 0.48846, 7, -71.41, 62.4, 0.28254, 20, 91.64, -29.67, 0.19275, 43, -1.69, 319.38, 0.03625, 4, 4, 14.24, 95.11, 0.57641, 3, 94.83, 95.59, 0.19996, 23, -105.03, 5.47, 0.19409, 43, -195.09, 530.71, 0.02954, 4, 4, 28.83, 37.73, 0.73536, 3, 123.62, 43.85, 0.03264, 23, -90.44, -51.91, 0.192, 43, -180.49, 473.33, 0.04, 3, 4, 25.52, -19.13, 0.768, 20, -90.48, 67.41, 0.192, 43, -183.8, 416.46, 0.04, 4, 4, 10.3, -62.49, 0.58816, 3, 131.34, -57.78, 0.17984, 20, -105.7, 24.06, 0.192, 43, -199.02, 373.11, 0.04, 4, 4, 9.83, -110.94, 0.61768, 3, 143.28, -104.74, 0.16468, 20, -106.17, -24.4, 0.19559, 43, -199.5, 324.65, 0.02205, 4, 4, 86.93, 8.14, 0.7776, 23, -32.33, -81.5, 0.0864, 20, -29.06, 94.69, 0.096, 43, -122.39, 443.74, 0.04, 4, 4, 125.07, 8.62, 0.7776, 23, 5.8, -81.03, 0.0864, 20, 9.07, 95.16, 0.096, 43, -84.26, 444.21, 0.04, 3, 4, 95.79, 124.6, 0.69573, 25, -3.59, 6.82, 0.23191, 43, -113.53, 560.19, 0.07236, 3, 4, 88.54, -110.62, 0.69708, 22, -6.64, -5.5, 0.23236, 43, -120.78, 324.98, 0.07056, 4, 4, 182.36, -85.28, 0.71137, 7, -96.68, 93.33, 0.04463, 21, 76.37, 11.63, 0.189, 43, -26.96, 350.31, 0.055, 4, 4, 166.2, -133.12, 0.65699, 7, -112.84, 45.49, 0.10253, 21, 60.21, -36.21, 0.18988, 43, -43.13, 302.48, 0.05059, 3, 4, 168.03, -43.11, 0.756, 21, 62.04, 53.79, 0.189, 43, -41.29, 392.48, 0.055, 3, 4, 129.13, -24.94, 0.756, 21, 23.14, 71.96, 0.189, 43, -80.2, 410.65, 0.055, 3, 4, 80.37, -23.73, 0.756, 21, -25.62, 73.17, 0.189, 43, -128.95, 411.86, 0.055, 3, 4, 38.93, -45.46, 0.756, 21, -67.06, 51.44, 0.189, 43, -170.39, 390.13, 0.055, 4, 4, 24.43, -92.88, 0.66215, 3, 152.77, -83.54, 0.09385, 21, -81.56, 4.03, 0.189, 43, -184.89, 342.71, 0.055, 4, 4, 39.33, -136.72, 0.73141, 3, 178.39, -122.11, 0.03009, 21, -66.66, -39.81, 0.19037, 43, -169.99, 298.87, 0.04813, 3, 4, 127.14, -159.65, 0.76749, 21, 21.15, -62.75, 0.19187, 43, -82.18, 275.94, 0.04064, 3, 4, 75.48, -159.85, 0.76869, 21, -30.51, -62.94, 0.19217, 43, -133.84, 275.74, 0.03914, 3, 4, 146.06, -98.1, 0.74638, 22, 50.88, 7.02, 0.1866, 43, -63.26, 337.5, 0.06702, 3, 4, 126.68, -65.95, 0.744, 22, 31.5, 39.17, 0.186, 43, -82.64, 369.64, 0.07, 3, 4, 91.45, -60.24, 0.744, 22, -3.74, 44.88, 0.186, 43, -117.88, 375.36, 0.07, 3, 4, 59.11, -69.25, 0.744, 22, -36.07, 35.86, 0.186, 43, -150.21, 366.34, 0.07, 3, 4, 62.21, -136.87, 0.75534, 22, -32.97, -31.76, 0.18883, 43, -147.11, 298.72, 0.05583, 3, 4, 97.23, -144.29, 0.75636, 22, 2.04, -39.17, 0.18909, 43, -112.1, 291.31, 0.05454, 3, 4, 132.37, -135.18, 0.75406, 22, 37.19, -30.06, 0.18852, 43, -76.95, 300.41, 0.05742, 4, 4, 44.69, -101.57, 0.73603, 3, 174.58, -86.76, 0.01111, 22, -50.5, 3.54, 0.18679, 43, -164.64, 334.02, 0.06607, 4, 4, 184.19, 99.45, 0.71241, 8, -65.29, -65.13, 0.04359, 24, 75.79, -6.5, 0.189, 43, -25.14, 535.04, 0.055, 3, 4, 169.53, 55.84, 0.756, 24, 61.14, -50.11, 0.189, 43, -39.79, 491.43, 0.055, 3, 4, 129.3, 39.18, 0.756, 24, 20.9, -66.76, 0.189, 43, -80.02, 474.78, 0.055, 3, 4, 83.36, 40.64, 0.756, 24, -25.03, -65.31, 0.189, 43, -125.96, 476.24, 0.055, 4, 4, 42.67, 67.44, 0.73512, 3, 129.4, 76.12, 0.02088, 24, -65.72, -38.5, 0.189, 43, -166.65, 503.04, 0.055, 4, 4, 30.73, 112.6, 0.65874, 3, 106.3, 116.71, 0.09726, 24, -77.67, 6.65, 0.189, 43, -178.59, 548.19, 0.055, 4, 4, 45.17, 155.2, 0.73649, 3, 109.37, 161.59, 0.02406, 24, -63.22, 49.25, 0.19014, 43, -164.15, 590.79, 0.04932, 3, 4, 85.88, 175.96, 0.76695, 24, -22.52, 70.01, 0.19174, 43, -123.45, 611.55, 0.04132, 3, 4, 130.28, 167.66, 0.76367, 24, 21.88, 61.71, 0.19092, 43, -79.05, 603.25, 0.04541, 4, 4, 164.19, 139.71, 0.65928, 8, -85.28, -24.87, 0.09918, 24, 55.8, 33.76, 0.18961, 43, -45.13, 575.3, 0.05193, 3, 4, 147.56, 112.62, 0.74576, 25, 48.18, -5.16, 0.18644, 43, -61.76, 548.21, 0.0678, 3, 4, 129.02, 81.33, 0.744, 25, 29.64, -36.45, 0.186, 43, -80.3, 516.92, 0.07, 3, 4, 134.59, 146.63, 0.75073, 25, 35.21, 28.85, 0.18768, 43, -74.73, 582.22, 0.06159, 3, 4, 103.44, 157.71, 0.7525, 25, 4.06, 39.93, 0.18813, 43, -105.88, 593.3, 0.05937, 3, 4, 70.18, 152.15, 0.75199, 25, -29.2, 34.37, 0.188, 43, -139.14, 587.74, 0.06002, 4, 4, 50.95, 122.22, 0.72691, 3, 123.4, 131.18, 0.01932, 25, -48.43, 4.44, 0.18656, 43, -158.37, 557.81, 0.06721, 3, 4, 62.87, 87.31, 0.744, 25, -36.52, -30.47, 0.186, 43, -146.46, 522.9, 0.07, 3, 4, 93.55, 76.15, 0.744, 25, -5.83, -41.63, 0.186, 43, -115.78, 511.74, 0.07, 3, 4, 253.79, 126.35, 0.18463, 8, 4.32, -38.23, 0.79365, 43, 44.47, 561.94, 0.02172, 3, 4, 273.52, 95.16, 0.53642, 8, 24.05, -69.42, 0.44306, 43, 64.2, 530.75, 0.02052, 4, 4, 238.97, 83.23, 0.66899, 8, -10.5, -81.35, 0.20474, 23, 119.7, -6.42, 0.09708, 43, 29.64, 518.82, 0.02919, 4, 4, 226.3, 131.36, 0.29399, 8, -23.17, -33.23, 0.62961, 23, 107.04, 41.71, 0.04861, 43, 16.98, 566.95, 0.02779, 3, 4, 232.45, 29.24, 0.87206, 23, 113.18, -60.41, 0.0969, 43, 23.13, 464.83, 0.03104, 3, 4, 229.69, -25.75, 0.87065, 20, 113.69, 60.79, 0.09674, 43, 20.37, 409.84, 0.03262, 4, 4, 239.8, -70.92, 0.74258, 7, -39.24, 107.68, 0.12953, 20, 123.8, 15.62, 0.0969, 43, 30.48, 364.67, 0.03099, 4, 4, 236.6, -124.17, 0.37392, 7, -42.44, 54.44, 0.54852, 20, 120.6, -37.62, 0.04855, 43, 27.28, 311.43, 0.02901, 3, 4, 283.08, -61.3, 0.88067, 7, 4.04, 117.3, 0.09915, 43, 73.76, 374.29, 0.02018, 3, 4, 266.25, -81.25, 0.7048, 7, -12.79, 97.36, 0.27061, 43, 56.93, 354.35, 0.02459, 3, 4, 259.35, -143.41, 0.19297, 7, -19.69, 35.19, 0.78975, 43, 50.03, 292.18, 0.01728, 5, 4, -15.25, 90.83, 0.46902, 3, 67.43, 83.91, 0.48965, 9, -88.67, -78.91, 0.0107, 43, -224.57, 526.42, 0.02942, 45, -457.53, 199.28, 0.00121, 5, 4, -15.79, 42.76, 0.3111, 3, 79.2, 37.3, 0.64883, 9, -97.7, -31.7, 0.00111, 43, -225.11, 478.35, 0.03698, 45, -409.49, 200.88, 0.00198, 4, 4, -23.38, -19.71, 0.12954, 3, 87.84, -25.04, 0.83216, 43, -232.7, 415.88, 0.03665, 45, -346.73, 196.08, 0.00164, 5, 4, -35.93, -76.3, 0.33755, 3, 90.18, -82.95, 0.57939, 11, -124.51, -0.93, 0.05497, 43, -245.26, 359.29, 0.02657, 45, -289.65, 186.06, 0.00153, 6, 4, -44.43, 86.53, 0.20133, 3, 40.32, 72.28, 0.6035, 9, -60.93, -68.89, 0.05617, 10, -87.56, 26.63, 0.10465, 43, -253.75, 522.12, 0.02728, 45, -451.93, 170.32, 0.00707, 6, 4, -76.9, 93.21, 0.06282, 3, 7.22, 70.44, 0.40861, 9, -27.78, -68.98, 0.18682, 10, -54.41, 26.54, 0.3072, 43, -286.22, 528.8, 0.02099, 45, -457.16, 137.58, 0.01356, 6, 4, -111.41, 100.9, 0.00915, 3, -28.11, 69.05, 0.12438, 9, 7.57, -69.65, 0.31072, 10, -19.06, 25.87, 0.52093, 43, -320.73, 536.49, 0.01422, 45, -463.3, 102.77, 0.0206, 6, 4, -122.39, 69.53, 0.00087, 3, -30.71, 35.91, 0.08634, 9, 12.1, -36.72, 0.72416, 10, -14.53, 58.8, 0.15066, 43, -331.72, 505.12, 0.01504, 45, -431.48, 93.19, 0.02293, 5, 4, -121.26, 36.3, 0.00026, 3, -21.11, 4.08, 0.01734, 9, 4.37, -4.38, 0.94272, 43, -330.58, 471.89, 0.01665, 45, -398.33, 95.8, 0.02303, 6, 3, -25.92, -25.09, 0.11151, 9, 10.88, 24.45, 0.76303, 11, -11.99, -65.46, 0.08225, 36, -123.94, -48.77, 0.00422, 43, -342.69, 444.93, 0.0145, 45, -370.85, 84.91, 0.02449, 5, 3, -17.91, -58.12, 0.21134, 9, 4.8, 57.9, 0.28039, 11, -18.06, -32.02, 0.47063, 43, -343.39, 410.94, 0.01426, 45, -336.87, 85.72, 0.02339, 7, 4, -93.07, 138.08, 0.03056, 3, -19.9, 109.68, 0.13924, 9, -3, -109.73, 0.02159, 10, -29.63, -14.21, 0.77898, 38, -107.52, 51.28, 0.00726, 43, -302.4, 573.67, 0.01311, 45, -501.26, 119.42, 0.00925, 5, 4, -134.55, -67.79, 0.00224, 3, -7.34, -99.95, 0.17889, 11, -26.17, 10.35, 0.79194, 43, -343.88, 367.81, 0.01271, 45, -293.75, 87.16, 0.01422, 6, 4, -69.22, -56.79, 0.07952, 3, 53.01, -72.61, 0.70042, 9, -65.14, 76.5, 0.00245, 11, -88.01, -13.42, 0.18595, 43, -278.54, 378.8, 0.0225, 45, -307.65, 151.94, 0.00916, 6, 4, -99.28, -40.05, 0.00977, 3, 19.66, -64.11, 0.57569, 9, -32.35, 66.07, 0.05807, 11, -55.21, -23.85, 0.31963, 43, -308.61, 395.55, 0.02096, 45, -323.04, 121.16, 0.01588, 3, 3, 54.8, -24.54, 0.96143, 43, -264.51, 424.81, 0.03027, 45, -354.24, 163.91, 0.0083, 5, 3, 15.4, -28.24, 0.82744, 9, -30.19, 30.01, 0.11878, 11, -53.06, -59.91, 0.01513, 43, -303.55, 431.31, 0.02237, 45, -358.99, 124.62, 0.01627, 5, 4, -47.08, 46.2, 0.07802, 3, 48.07, 32.62, 0.86261, 9, -66.35, -28.84, 0.02015, 43, -256.4, 481.79, 0.03095, 45, -411.53, 169.46, 0.00828, 6, 4, -85.77, 58.51, 0.01842, 3, 7.52, 34.62, 0.63198, 9, -25.99, -33.2, 0.303, 10, -52.62, 62.32, 0.00789, 43, -295.09, 494.1, 0.0227, 45, -422.1, 130.27, 0.01601, 4, 10, 13.93, -31.04, 0.731, 38, -71.16, 22.06, 0.24904, 43, -341.73, 598.83, 0.00384, 45, -524.64, 79, 0.01612, 5, 9, 58.89, -78.79, 0.25505, 10, 32.26, 16.73, 0.52765, 38, -39.28, 62.05, 0.18282, 43, -369.21, 555.66, 0.00399, 45, -480.29, 53.48, 0.03048, 5, 9, 65.44, -37.41, 0.74743, 10, 38.81, 58.11, 0.1624, 38, -20.56, 99.5, 0.05302, 43, -383.85, 516.41, 0.00462, 45, -440.43, 40.6, 0.03254, 3, 9, 75.36, -2.94, 0.96004, 43, -400.44, 484.6, 0.00286, 45, -407.91, 25.45, 0.0371, 5, 9, 62.24, 33, 0.75239, 11, 39.38, -56.92, 0.17013, 36, -72.34, -55.77, 0.03827, 43, -394.73, 446.77, 0.00429, 45, -370.37, 32.84, 0.03491, 5, 9, 53.75, 75.41, 0.23813, 11, 30.88, -14.51, 0.52023, 36, -67.94, -12.75, 0.20468, 43, -394.84, 403.52, 0.00409, 45, -327.16, 34.65, 0.03287, 4, 11, 13.58, 25.55, 0.79133, 36, -72.64, 30.63, 0.18286, 43, -385.86, 360.82, 0.005, 45, -284.91, 45.53, 0.02081, 4, 9, 271.61, 11.66, 0.23087, 8, -635.81, -90.83, 0, 38, 190.43, 83.89, 0.49854, 36, 121.38, -137.99, 0.27058, 4, 9, 268.17, 26.55, 0.22829, 8, -635.41, -106.11, 0, 38, 191.66, 99.12, 0.42157, 36, 122.49, -122.75, 0.35014, 4, 9, 246.83, 108.9, 0.0619, 8, -630.88, -191.05, 0, 36, 126.42, -37.77, 0.9097, 45, -324.02, -161.29, 0.0284, 2, 36, 126.1, 40.84, 0.98161, 45, -246.27, -149.68, 0.01839, 2, 38, 185.4, -7.98, 0.98033, 45, -515.51, -179.59, 0.01967, 2, 38, 179.08, -66.82, 0.98239, 45, -574.62, -182.22, 0.01761, 4, 9, 101.5, -45.6, 0.65109, 10, 74.87, 49.93, 0.10578, 38, 11.29, 80.79, 0.20587, 45, -454.11, 6.24, 0.03727, 3, 9, 151.47, -50.93, 0.54148, 38, 57.22, 60.59, 0.42552, 45, -467.12, -42.3, 0.033, 5, 9, 241.99, -42.56, 0.1609, 8, -596, -43.59, 0, 38, 145.87, 41.17, 0.72671, 36, 77.07, -181.05, 0.0924, 45, -472.9, -133.02, 0.01999, 5, 9, 177.55, 52.17, 0.48416, 8, -551.69, -149.25, 0, 38, 113.17, 150.96, 0.06673, 36, 43.48, -71.51, 0.42226, 45, -369.32, -84.05, 0.02685, 4, 9, 99.23, 36.28, 0.76793, 11, 76.36, -53.64, 0.06775, 36, -36.04, -63.56, 0.12651, 45, -372.87, -4.21, 0.03781, 5, 9, 138.78, 43.2, 0.69111, 8, -511.91, -148.17, 0, 11, 115.91, -46.72, 0.00265, 36, 3.79, -68.62, 0.27301, 45, -372.16, -44.35, 0.03323, 4, 9, 81.92, 104.42, 0.05694, 11, 59.06, 14.5, 0.30435, 36, -32.45, 6.66, 0.60495, 45, -302.87, 2.32, 0.03376, 3, 11, 91.48, 22.3, 0.00833, 36, 0.83, 4.53, 0.96106, 45, -300.19, -30.92, 0.03061, 4, 9, 193.26, -50.38, 0.32977, 38, 97.15, 48.46, 0.57863, 36, 28.21, -174.13, 0.06688, 45, -473.07, -83.67, 0.02473, 5, 9, 219.73, 62.88, 0.27622, 8, -595.17, -151.35, 0, 38, 156.54, 148.4, 0.13123, 36, 86.95, -73.74, 0.56978, 45, -365.28, -127.38, 0.02277, 5, 9, 257.21, 69.68, 0.14819, 8, -633.25, -150.56, 0, 38, 194.26, 143.55, 0.17335, 36, 124.76, -78.3, 0.65706, 45, -364.37, -165.46, 0.0214, 2, 36, 69.41, 4.8, 0.9737, 45, -290.08, -98.75, 0.0263, 5, 9, 282.56, -31, 0.08142, 8, -638.06, -46.85, 0, 38, 187.97, 39.92, 0.78645, 36, 119.24, -181.98, 0.11515, 45, -467.76, -174.89, 0.01698, 3, 11, 130.53, 34.52, 0.00077, 36, 41.74, 4.67, 0.97188, 45, -294.17, -71.39, 0.02735, 4, 9, 88.58, -116.91, 0.04626, 10, 61.95, -21.39, 0.25201, 38, -22.55, 16.73, 0.67466, 45, -522.56, 30.07, 0.02707, 2, 38, 8.76, -7.86, 0.97464, 45, -542.12, -4.66, 0.02536, 2, 38, 44.79, -30.69, 0.97648, 45, -559.24, -43.78, 0.02352, 2, 38, 86.93, -49.02, 0.97929, 45, -570.97, -88.28, 0.02071, 2, 38, 135.38, -58.41, 0.98007, 45, -572.93, -137.68, 0.01993, 2, 38, 293.02, -60.27, 0.98424, 45, -550.9, -294.05, 0.01576, 2, 38, 495.94, -23.52, 0.98408, 45, -483.87, -489.44, 0.01592, 3, 38, 527.75, -24.81, 0.96063, 39, -34.52, -32.3, 0.02359, 45, -480.33, -521.14, 0.01578, 3, 38, 560.42, -27.1, 0.63078, 39, -1.93, -28.04, 0.35354, 45, -477.64, -553.84, 0.01567, 3, 38, 589.18, -26.68, 0.08878, 39, 26.27, -21.91, 0.8962, 45, -472.87, -582.25, 0.01502, 3, 38, 619.56, -35.24, 0.00321, 39, 57.85, -24.25, 0.98443, 45, -476.74, -613.63, 0.01237, 3, 8, -735.44, -49.39, 0, 38, 284.89, 32.04, 0.98433, 45, -460.89, -272.06, 0.01567, 3, 8, -949.69, -68.9, 0, 38, 499.63, 28.54, 0.98603, 45, -431.85, -485.23, 0.01397, 4, 8, -980.26, -71.88, 0, 38, 530.29, 28.24, 0.97789, 39, -42.61, 20.19, 0.0082, 45, -427.5, -515.64, 0.01391, 4, 8, -1014.44, -68.91, 0, 38, 563.89, 21.63, 0.61364, 39, -8.25, 20.4, 0.37189, 45, -428.95, -549.91, 0.01447, 4, 8, -1045.08, -62.71, 0, 38, 593.65, 12.19, 0.0448, 39, 22.9, 17.07, 0.94076, 45, -433.78, -580.8, 0.01444, 3, 8, -1078.88, -62.16, 0, 39, 56.67, 19.67, 0.98718, 45, -432.81, -614.59, 0.01282, 3, 8, -1153.24, -67.73, 0, 39, 130.32, 32.1, 0.99486, 45, -423.94, -688.63, 0.00514, 1, 39, 318.86, 8.33, 1, 2, 39, 129.07, -31.27, 0.99474, 45, -487.17, -684.29, 0.00526, 2, 38, 232.85, -69.6, 0.98453, 45, -569.24, -235.89, 0.01547, 2, 38, 231.37, -10.5, 0.9811, 45, -511.04, -225.49, 0.0189, 5, 9, 327.85, -20, 0.01522, 8, -684.63, -48.61, 0, 38, 234.38, 36.7, 0.94951, 36, 165.76, -184.85, 0.02001, 45, -463.93, -221.34, 0.01526, 2, 38, 287.29, -10.83, 0.98078, 45, -502.9, -280.91, 0.01922, 5, 9, 300.65, 84.43, 0.01804, 8, -678.75, -156.37, 0, 38, 240.04, 144.47, 0.03532, 36, 170.62, -77.04, 0.92563, 45, -356.54, -210.66, 0.02101, 3, 8, -787.03, -184.55, 0, 36, 281.16, -59.75, 0.9858, 45, -323.56, -317.57, 0.0142, 4, 8, -951.05, -225.66, 0, 36, 448.45, -35.14, 0.97844, 37, -64.32, -38.65, 0.00752, 45, -275.17, -479.6, 0.01403, 4, 8, -989.61, -231.82, 0, 36, 487.43, -32.84, 0.84817, 37, -25.52, -34.26, 0.13758, 45, -267.3, -517.84, 0.01425, 4, 8, -1021.25, -237.48, 0, 36, 519.48, -30.35, 0.3456, 37, 6.35, -30.05, 0.63979, 45, -260.23, -549.2, 0.01461, 4, 8, -1057.05, -248.29, 0, 36, 556.18, -23.15, 0.00211, 37, 42.61, -20.89, 0.98256, 45, -247.84, -584.49, 0.01533, 3, 8, -1090.89, -256.83, 0, 37, 76.81, -13.91, 0.98706, 45, -237.81, -617.92, 0.01294, 3, 8, -1117.69, -258.62, 0, 37, 103.66, -13.35, 0.99042, 45, -234.82, -644.6, 0.00958, 2, 37, 98.99, 27.93, 0.98877, 45, -194.14, -636.23, 0.01123, 2, 37, 72.91, 21.57, 0.98618, 45, -202.82, -610.84, 0.01382, 3, 36, 552.34, 17.52, 0.01922, 37, 36.59, 19.51, 0.96622, 45, -208.14, -574.85, 0.01456, 3, 36, 519.86, 20.56, 0.39422, 37, 4, 20.81, 0.59045, 45, -209.8, -542.27, 0.01532, 3, 36, 487.75, 21.44, 0.91547, 37, -28.12, 19.96, 0.0694, 45, -213.53, -510.37, 0.01513, 2, 36, 442.12, 20.55, 0.98484, 45, -220.97, -465.34, 0.01516, 2, 36, 278.14, 33.71, 0.98575, 45, -231.49, -301.17, 0.01425, 2, 36, 174.96, 39.39, 0.98294, 45, -240.69, -198.25, 0.01706, 4, 9, 279.96, 149.12, 0.00347, 8, -671.35, -223.88, 0, 36, 169.96, -9.13, 0.96857, 45, -289.43, -200.26, 0.02796, 3, 8, -776.55, -232.2, 0, 36, 275.47, -11.31, 0.98226, 45, -276.43, -304.99, 0.01774, 2, 36, 126.98, 1.48, 0.9723, 45, -285.09, -156.2, 0.0277, 3, 8, -730.86, -165.49, 0, 36, 223.37, -73.14, 0.98099, 45, -345.1, -262.31, 0.01901, 2, 36, 226.71, 37.62, 0.98497, 45, -235.01, -249.71, 0.01503, 3, 8, -726.08, -227.22, 0, 36, 224.75, -11.25, 0.97735, 45, -283.65, -254.79, 0.02265, 3, 8, -1198.28, -251.16, 0, 37, 183.83, -24.48, 0.99545, 45, -238.68, -725.45, 0.00455, 1, 37, 327.46, 2.11, 1, 2, 37, 176.34, 37.08, 0.99513, 45, -178.05, -712.44, 0.00487, 2, 6, 120.87, -41.82, 0.96022, 41, -264.78, 40.71, 0.03978, 2, 6, 105.54, -45.05, 0.95504, 41, -280.11, 37.48, 0.04496, 2, 6, 88.56, -45.83, 0.95465, 41, -297.09, 36.7, 0.04535, 2, 6, 71.93, -47.51, 0.95773, 41, -313.71, 35.02, 0.04227, 3, 6, 58.1, -52.82, 0.90141, 5, 123.47, -53.7, 0.0607, 41, -327.55, 29.71, 0.03789, 3, 6, 45.34, -62.06, 0.81686, 5, 110.57, -62.75, 0.14896, 41, -340.31, 20.47, 0.03418, 5, 6, 29.44, -76.73, 0.6164, 5, 94.45, -77.18, 0.24544, 4, 399.78, -67.29, 0.002, 41, -356.21, 5.8, 0.02521, 54, 41.17, 31.89, 0.11096, 5, 6, 17.89, -87.14, 0.48899, 5, 82.74, -87.4, 0.29052, 4, 389.16, -78.64, 0.00826, 41, -367.76, -4.61, 0.01892, 54, 29.47, 21.67, 0.19331, 5, 6, 7.33, -92.67, 0.41291, 5, 72.09, -92.77, 0.30994, 4, 379.11, -85.05, 0.01816, 41, -378.32, -10.13, 0.01363, 54, 18.82, 16.3, 0.24535, 6, 6, -9.08, -93.97, 0.32679, 5, 55.67, -93.82, 0.32688, 4, 362.87, -87.75, 0.04885, 7, 83.84, 90.85, 0.02393, 41, -394.73, -11.44, 0.00595, 54, 2.4, 15.24, 0.26759, 5, 6, 11.72, -65.03, 0.5115, 5, 76.91, -65.21, 0.37324, 4, 381.12, -57.14, 0.00972, 42, -300.87, 17.5, 0.01392, 54, 23.63, 43.86, 0.09161, 5, 6, -21.43, -58.08, 0.25902, 5, 43.87, -57.75, 0.58841, 4, 347.5, -53.04, 0.114, 42, -334.02, 24.45, 0.00672, 54, -9.41, 51.32, 0.03185, 6, 6, -34.85, -88.55, 0.16374, 5, 29.99, -88.01, 0.34589, 4, 336.74, -84.54, 0.18505, 7, 57.7, 94.06, 0.13524, 41, -420.49, -6.02, 0.00217, 54, -23.29, 21.06, 0.16791, 3, 5, 4.32, -94.75, 0.17529, 4, 311.88, -93.84, 0.43099, 7, 32.84, 84.77, 0.39372, 6, 6, -30.3, 83.7, 0.24993, 5, 37.17, 84.15, 0.38149, 4, 326.57, 87.46, 0.16925, 8, 77.1, -77.12, 0.13117, 42, -342.89, 166.23, 0.00359, 53, 41, 32.52, 0.06457, 5, 6, -1.34, 78.95, 0.45391, 5, 66.06, 78.96, 0.37161, 4, 355.84, 85.2, 0.03615, 42, -313.92, 161.48, 0.00964, 53, 16.55, 16.3, 0.12868, 5, 6, 31.02, 72.51, 0.71796, 5, 98.32, 72.03, 0.18536, 4, 388.63, 81.55, 0.00174, 42, -281.56, 155.04, 0.01696, 53, -11.55, -0.99, 0.07798, 7, 6, 22.92, 114.87, 0.49563, 5, 90.86, 114.51, 0.23168, 4, 376.94, 123.06, 0.0066, 8, 127.47, -41.52, 0.05254, 41, -362.73, 197.4, 0.00494, 42, -289.66, 197.4, 0.00019, 53, 23.2, -26.53, 0.20843, 5, 6, 33.14, 100.47, 0.64498, 5, 100.86, 99.95, 0.19637, 4, 388.36, 109.59, 0.00196, 41, -352.51, 183, 0.01155, 53, 5.91, -22.93, 0.14514, 4, 6, 49.9, 84.64, 0.82788, 5, 117.38, 83.86, 0.10054, 41, -335.75, 167.17, 0.01949, 53, -17.14, -22.73, 0.0521, 3, 6, 66.76, 73.98, 0.97106, 5, 134.07, 72.95, 0.00621, 41, -318.89, 156.51, 0.02274, 2, 6, 81.58, 67.05, 0.97599, 41, -304.07, 149.58, 0.02401, 2, 6, 99.02, 63.81, 0.9749, 41, -286.63, 146.34, 0.0251, 2, 6, 116.18, 59.84, 0.97711, 41, -269.47, 142.37, 0.02289, 7, 6, 12.12, 127.06, 0.37887, 5, 80.25, 126.86, 0.24289, 4, 365.14, 134.28, 0.01161, 8, 115.67, -30.3, 0.09623, 41, -373.53, 209.59, 0.00297, 42, -300.46, 209.59, 0.00128, 53, 39.41, -28.12, 0.26615, 7, 6, -4.71, 134.26, 0.30987, 5, 63.53, 134.32, 0.22273, 4, 347.76, 140.03, 0.01998, 8, 98.29, -24.56, 0.1378, 41, -390.36, 216.79, 0.00119, 42, -317.3, 216.79, 0.00174, 53, 56.65, -21.95, 0.30669, 6, 6, -29.44, 128.98, 0.24615, 5, 38.73, 129.42, 0.17597, 4, 323.57, 132.66, 0.04822, 8, 74.1, -31.92, 0.23714, 42, -342.02, 211.51, 0.00052, 53, 71.18, -1.26, 0.292, 6, 6, -7.9, 110.1, 0.34373, 5, 59.98, 110.21, 0.26186, 4, 346.64, 115.68, 0.03524, 8, 97.17, -48.9, 0.10391, 41, -393.55, 192.63, 0.00211, 53, 42.54, -2.07, 0.25314, 1, 6, 61.63, 42.84, 1, 1, 6, 66.09, -23.3, 1, 5, 9, 105.33, -2.28, 0.9215, 8, -470.09, -110.25, 0, 10, 78.7, 93.24, 0.03196, 11, 82.47, -92.2, 0.00857, 45, -411.91, -4.26, 0.03797, 5, 9, 149.58, -0.82, 0.90155, 8, -513.74, -102.88, 0, 38, 70.55, 108.92, 0.03033, 36, 1.1, -113.88, 0.0363, 45, -417.33, -48.2, 0.03181, 5, 9, 186.81, 2.59, 0.68738, 8, -550.91, -98.81, 0, 38, 107.01, 100.9, 0.1693, 36, 37.68, -121.62, 0.11896, 45, -419.74, -85.51, 0.02436, 5, 9, 232.68, 9.18, 0.50238, 8, -597.17, -96.15, 0, 38, 152.65, 93.31, 0.30969, 36, 83.46, -128.87, 0.16921, 45, -420.34, -131.85, 0.01871, 7, 6, 11.89, -107.44, 0.40014, 5, 76.43, -107.61, 0.2605, 4, 384.91, -99.38, 0.01257, 7, 105.87, 79.22, 0.01467, 41, -373.76, -24.91, 0.00506, 42, -300.7, -24.91, 6e-05, 54, 23.15, 1.46, 0.307, 7, 6, 28.36, -103.7, 0.51145, 5, 92.96, -104.13, 0.23857, 4, 401.01, -94.25, 0.00549, 7, 121.97, 84.35, 0.00547, 41, -357.28, -21.17, 0.00921, 42, -284.22, -21.17, 2e-05, 54, 39.69, 4.94, 0.22979, 6, 6, 39.6, -92.83, 0.63228, 5, 104.36, -93.43, 0.20682, 4, 411.28, -82.46, 0.00244, 7, 132.24, 96.15, 0.00147, 41, -346.04, -10.3, 0.014, 54, 51.09, 15.64, 0.14298, 4, 6, 55.51, -80.6, 0.82276, 5, 120.45, -81.44, 0.12586, 41, -330.14, 1.93, 0.01959, 54, 67.18, 27.62, 0.03178, 3, 6, 73.76, -74.01, 0.95212, 5, 138.81, -75.13, 0.02215, 41, -311.89, 8.52, 0.02573, 3, 6, 94.35, -68.85, 0.96483, 5, 159.48, -70.29, 0.00573, 41, -291.29, 13.68, 0.02945, 2, 6, 112.66, -66.19, 0.97074, 41, -272.99, 16.34, 0.02926, 3, 6, 128.49, -60.97, 0.97234, 7, 218.08, 135.47, 0.00059, 41, -257.16, 21.56, 0.02707, 7, 6, 41.49, 104.74, 0.70041, 5, 109.28, 104.09, 0.16688, 4, 396.31, 114.55, 0.00101, 8, 146.84, -50.03, 0.00479, 41, -344.16, 187.27, 0.00564, 42, -271.09, 187.27, 0.00029, 53, 2.7, -31.73, 0.12097, 6, 6, 56.99, 92.21, 0.86637, 5, 124.58, 91.32, 0.08109, 4, 412.82, 103.39, 0.00014, 8, 163.35, -61.2, 0.00066, 41, -328.66, 174.74, 0.01096, 53, -17.19, -33.09, 0.04079, 3, 6, 69.3, 87.01, 0.97232, 5, 136.81, 85.93, 0.01409, 41, -316.35, 169.54, 0.01359, 3, 6, 84.76, 80.56, 0.98091, 5, 152.17, 79.25, 0.00248, 41, -300.89, 163.09, 0.01661, 2, 6, 101.12, 76.76, 0.98238, 41, -284.52, 159.3, 0.01762, 2, 6, 116.6, 72.53, 0.98247, 41, -269.05, 155.06, 0.01753], "hull": 149, "edges": [10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 44, 46, 50, 52, 52, 54, 56, 58, 64, 66, 66, 68, 68, 70, 70, 72, 76, 78, 98, 100, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 134, 136, 152, 154, 168, 170, 170, 172, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 196, 198, 198, 200, 200, 202, 206, 208, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 244, 246, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 266, 268, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 54, 56, 58, 60, 60, 62, 62, 64, 242, 244, 246, 248, 268, 270, 270, 272, 262, 264, 264, 266, 258, 260, 260, 262, 236, 238, 232, 234, 234, 236, 238, 240, 240, 242, 72, 74, 74, 76, 78, 80, 80, 82, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 136, 138, 132, 134, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 120, 122, 100, 102, 102, 104, 104, 106, 106, 108, 96, 98, 92, 94, 94, 96, 88, 90, 90, 92, 82, 84, 84, 86, 86, 88, 138, 140, 210, 212, 212, 214, 208, 210, 158, 160, 160, 162, 202, 204, 204, 206, 162, 164, 164, 166, 166, 168, 192, 194, 194, 196, 174, 176, 172, 174, 154, 156, 156, 158, 282, 284, 284, 286, 290, 292, 8, 10, 14, 16, 16, 18, 24, 26, 26, 28, 30, 32, 32, 34, 298, 38, 264, 300, 300, 302, 292, 294, 294, 296, 286, 288, 288, 290, 306, 308, 308, 310, 310, 312, 304, 314, 314, 316, 316, 318, 312, 322, 322, 324, 318, 326, 326, 320, 342, 344, 340, 346, 352, 354, 354, 356, 356, 358, 358, 360, 304, 362, 360, 362, 364, 366, 366, 368, 368, 370, 370, 372, 306, 374, 374, 362, 372, 374, 256, 378, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 388, 390, 390, 46, 244, 392, 392, 394, 394, 396, 396, 398, 398, 400, 400, 56, 384, 404, 404, 402, 410, 412, 410, 414, 414, 416, 416, 418, 418, 420, 420, 422, 422, 424, 412, 426, 426, 428, 428, 424, 430, 432, 432, 434, 434, 436, 438, 440, 440, 442, 442, 430, 436, 444, 444, 438, 446, 448, 448, 450, 450, 452, 452, 454, 454, 456, 456, 458, 458, 460, 460, 462, 462, 464, 464, 446, 466, 468, 466, 470, 470, 472, 472, 474, 474, 476, 476, 478, 478, 480, 480, 468, 376, 302, 376, 482, 482, 484, 332, 500, 500, 498, 506, 508, 516, 518, 520, 518, 520, 522, 522, 524, 516, 526, 524, 528, 542, 544, 544, 546, 546, 548, 548, 550, 550, 552, 552, 554, 148, 556, 556, 558, 558, 146, 588, 558, 588, 560, 560, 590, 590, 562, 556, 592, 592, 564, 564, 566, 606, 608, 608, 610, 610, 612, 612, 614, 614, 616, 618, 620, 620, 622, 622, 624, 624, 626, 626, 628, 628, 630, 630, 632, 632, 634, 634, 616, 642, 638, 606, 636, 618, 640, 646, 648, 648, 650, 650, 652, 652, 654, 654, 656, 656, 658, 660, 662, 662, 664, 664, 666, 666, 668, 668, 670, 670, 672, 676, 680, 644, 682, 682, 646, 672, 684, 684, 674, 676, 686, 686, 678, 658, 688, 688, 690, 690, 692, 692, 660, 4, 694, 694, 696, 696, 698, 698, 700, 700, 702, 702, 704, 704, 706, 706, 708, 708, 710, 710, 712, 728, 730, 730, 732, 732, 734, 734, 736, 736, 738, 738, 740, 4, 2, 740, 2, 728, 742, 742, 744, 744, 746, 746, 276, 4, 6, 6, 8, 2, 0, 0, 296], "width": 485, "height": 1681}}, "body2": {"body": {"type": "mesh", "uvs": [0.33135, 0.01567, 0.3559, 0.01442, 0.53944, 0.00505, 0.58364, 0.0028, 0.62783, 0.00055, 0.65708, 0.01445, 0.67933, 0.0278, 0.69223, 0.04292, 0.7314, 0.0546, 0.75387, 0.0678, 0.75481, 0.08269, 0.7305, 0.08997, 0.69985, 0.08632, 0.69279, 0.07675, 0.67867, 0.07094, 0.65431, 0.06493, 0.62007, 0.05668, 0.59776, 0.04987, 0.58321, 0.04212, 0.57532, 0.03244, 0.56919, 0.02248, 0.55849, 0.01369, 0.35192, 0.0243, 0.34838, 0.03472, 0.34642, 0.04526, 0.33621, 0.05454, 0.31892, 0.0653, 0.29103, 0.07641, 0.26432, 0.08355, 0.2423, 0.09086, 0.23206, 0.10134, 0.24946, 0.11552, 0.27839, 0.12051, 0.23162, 0.11961, 0.21492, 0.11405, 0.20965, 0.10234, 0.22215, 0.09309, 0.25173, 0.08071, 0.26834, 0.07019, 0.28608, 0.05369, 0.29094, 0.0392, 0.29887, 0.02806, 0.30679, 0.01692, 0.72178, 0.07292, 0.70974, 0.06348, 0.6845, 0.05769, 0.65525, 0.04925, 0.6369, 0.03899, 0.62084, 0.02725, 0.61052, 0.01666, 0.59561, 0.00772, 0.28008, 0.07182, 0.30155, 0.06171, 0.3089, 0.05405, 0.31794, 0.04443, 0.32133, 0.03448, 0.32585, 0.02503], "triangles": [33, 31, 32, 33, 34, 31, 34, 30, 31, 34, 35, 30, 10, 11, 43, 35, 36, 30, 30, 36, 29, 29, 36, 37, 29, 37, 28, 11, 12, 43, 12, 13, 43, 28, 37, 27, 43, 9, 10, 37, 51, 27, 37, 38, 51, 13, 14, 43, 27, 51, 26, 14, 44, 43, 43, 44, 9, 51, 52, 26, 51, 38, 52, 14, 45, 44, 14, 15, 45, 38, 39, 52, 44, 8, 9, 52, 53, 26, 26, 53, 25, 15, 46, 45, 15, 16, 46, 44, 45, 8, 52, 39, 53, 45, 7, 8, 45, 46, 7, 16, 17, 46, 53, 54, 25, 25, 54, 24, 53, 39, 54, 39, 40, 54, 17, 47, 46, 17, 18, 47, 46, 47, 7, 23, 24, 55, 24, 54, 55, 54, 40, 55, 47, 6, 7, 47, 18, 48, 40, 41, 55, 18, 19, 48, 47, 48, 6, 55, 56, 23, 23, 56, 22, 55, 41, 56, 19, 20, 48, 41, 42, 56, 48, 5, 6, 20, 49, 48, 48, 49, 5, 56, 0, 22, 56, 42, 0, 22, 2, 21, 22, 1, 2, 22, 0, 1, 20, 21, 49, 21, 50, 49, 5, 49, 4, 49, 50, 4, 21, 3, 50, 21, 2, 3, 50, 3, 4], "vertices": [2, 6, 131.85, 67.85, 0.9838, 41, -253.8, 150.38, 0.0162, 2, 6, 132.39, 55.77, 0.9825, 41, -253.26, 138.3, 0.0175, 2, 6, 136.46, -34.54, 0.96946, 41, -249.19, 47.99, 0.03054, 3, 6, 137.44, -56.28, 0.97451, 7, 226.61, 140.9, 0.00067, 41, -248.21, 26.25, 0.02483, 2, 6, 138.42, -78.03, 0.99863, 7, 229.44, 119.32, 0.00137, 1, 6, 113.41, -89.06, 1, 2, 6, 89.75, -96.85, 0.99901, 42, -222.84, -14.32, 0.00099, 4, 6, 63.75, -99.77, 0.84425, 5, 128.4, -100.73, 0.10187, 42, -248.84, -17.24, 0.00082, 54, 75.13, 8.34, 0.05306, 6, 6, 41.81, -116.06, 0.59039, 5, 106.22, -116.69, 0.19843, 4, 415.46, -105.42, 0.00099, 7, 136.42, 73.19, 0.00702, 42, -270.77, -33.53, 0.0018, 54, 52.94, -7.62, 0.20138, 6, 6, 18.4, -123.99, 0.40455, 5, 82.69, -124.26, 0.22086, 4, 392.81, -115.31, 0.00643, 7, 113.77, 63.29, 0.02813, 42, -294.18, -41.46, 0.00162, 54, 29.41, -15.19, 0.3384, 6, 6, -6.49, -121.19, 0.31502, 5, 57.84, -121.08, 0.1962, 4, 367.78, -114.65, 0.02164, 7, 88.74, 63.95, 0.04811, 42, -319.07, -38.66, 0.0007, 54, 4.57, -12.01, 0.41832, 5, 6, -17.09, -107.92, 0.27744, 5, 47.45, -107.64, 0.25038, 4, 356.08, -102.33, 0.05952, 7, 77.04, 76.28, 0.07528, 54, -5.82, 1.43, 0.33739, 6, 6, -9.08, -93.97, 0.32679, 5, 55.67, -93.82, 0.32688, 4, 362.87, -87.75, 0.04885, 7, 83.84, 90.85, 0.02393, 41, -394.73, -11.44, 0.00595, 54, 2.4, 15.24, 0.26759, 5, 6, 7.33, -92.67, 0.41291, 5, 72.09, -92.77, 0.30994, 4, 379.11, -85.05, 0.01816, 41, -378.32, -10.13, 0.01363, 54, 18.82, 16.3, 0.24535, 5, 6, 17.89, -87.14, 0.48899, 5, 82.74, -87.4, 0.29052, 4, 389.16, -78.64, 0.00826, 41, -367.76, -4.61, 0.01892, 54, 29.47, 21.67, 0.19331, 5, 6, 29.44, -76.73, 0.6164, 5, 94.45, -77.18, 0.24544, 4, 399.78, -67.29, 0.002, 41, -356.21, 5.8, 0.02521, 54, 41.17, 31.89, 0.11096, 3, 6, 45.34, -62.06, 0.81686, 5, 110.57, -62.75, 0.14896, 41, -340.31, 20.47, 0.03418, 3, 6, 58.1, -52.82, 0.90141, 5, 123.47, -53.7, 0.0607, 41, -327.55, 29.71, 0.03789, 2, 6, 71.93, -47.51, 0.95773, 41, -313.71, 35.02, 0.04227, 2, 6, 88.56, -45.83, 0.95465, 41, -297.09, 36.7, 0.04535, 2, 6, 105.54, -45.05, 0.95504, 41, -280.11, 37.48, 0.04496, 2, 6, 120.87, -41.82, 0.96022, 41, -264.78, 40.71, 0.03978, 2, 6, 116.18, 59.84, 0.97711, 41, -269.47, 142.37, 0.02289, 2, 6, 99.02, 63.81, 0.9749, 41, -286.63, 146.34, 0.0251, 2, 6, 81.58, 67.05, 0.97599, 41, -304.07, 149.58, 0.02401, 3, 6, 66.76, 73.98, 0.97106, 5, 134.07, 72.95, 0.00621, 41, -318.89, 156.51, 0.02274, 4, 6, 49.9, 84.64, 0.82788, 5, 117.38, 83.86, 0.10054, 41, -335.75, 167.17, 0.01949, 53, -17.14, -22.73, 0.0521, 5, 6, 33.14, 100.47, 0.64498, 5, 100.86, 99.95, 0.19637, 4, 388.36, 109.59, 0.00196, 41, -352.51, 183, 0.01155, 53, 5.91, -22.93, 0.14514, 7, 6, 22.92, 114.87, 0.49563, 5, 90.86, 114.51, 0.23168, 4, 376.94, 123.06, 0.0066, 8, 127.47, -41.52, 0.05254, 41, -362.73, 197.4, 0.00494, 42, -289.66, 197.4, 0.00019, 53, 23.2, -26.53, 0.20843, 7, 6, 12.12, 127.06, 0.37887, 5, 80.25, 126.86, 0.24289, 4, 365.14, 134.28, 0.01161, 8, 115.67, -30.3, 0.09623, 41, -373.53, 209.59, 0.00297, 42, -300.46, 209.59, 0.00128, 53, 39.41, -28.12, 0.26615, 7, 6, -4.71, 134.26, 0.30987, 5, 63.53, 134.32, 0.22273, 4, 347.76, 140.03, 0.01998, 8, 98.29, -24.56, 0.1378, 41, -390.36, 216.79, 0.00119, 42, -317.3, 216.79, 0.00174, 53, 56.65, -21.95, 0.30669, 6, 6, -29.44, 128.98, 0.24615, 5, 38.73, 129.42, 0.17597, 4, 323.57, 132.66, 0.04822, 8, 74.1, -31.92, 0.23714, 42, -342.02, 211.51, 0.00052, 53, 71.18, -1.26, 0.292, 5, 6, -39.56, 116.16, 0.19384, 5, 28.41, 116.75, 0.17471, 4, 314.58, 119.01, 0.11414, 8, 65.11, -45.57, 0.29854, 53, 69.87, 15.03, 0.21877, 6, 6, -35.12, 138.46, 0.25719, 5, 33.19, 138.98, 0.16022, 4, 317.1, 141.61, 0.03742, 8, 67.63, -22.97, 0.23582, 42, -347.71, 220.99, 0.00091, 53, 81.79, -4.33, 0.30843, 6, 6, -24.81, 145.28, 0.27, 5, 43.61, 145.64, 0.17869, 4, 326.8, 149.29, 0.03022, 8, 77.33, -15.3, 0.2121, 42, -337.39, 227.81, 0.00194, 53, 78.87, -16.35, 0.30705, 6, 6, -4.96, 145.26, 0.29911, 5, 63.45, 145.32, 0.21468, 4, 346.57, 150.96, 0.01872, 8, 97.1, -13.62, 0.15621, 42, -317.55, 227.79, 0.00268, 53, 64.31, -29.84, 0.3086, 6, 6, 9.66, 137.23, 0.34209, 5, 77.95, 137.07, 0.23924, 4, 361.83, 144.21, 0.01315, 8, 112.36, -20.37, 0.12145, 42, -302.92, 219.76, 0.0022, 53, 48.13, -33.9, 0.28187, 6, 6, 28.44, 120.31, 0.51704, 5, 96.47, 119.86, 0.22153, 4, 381.98, 128.95, 0.00541, 8, 132.51, -35.63, 0.0615, 42, -284.15, 202.84, 0.00111, 53, 22.86, -34.27, 0.1934, 6, 6, 44.93, 110.03, 0.71583, 5, 112.8, 109.33, 0.1589, 4, 399.29, 120.11, 0.0006, 8, 149.82, -44.47, 0.00826, 42, -267.65, 192.56, 0.00119, 53, 3.77, -37.95, 0.11522, 3, 6, 71.33, 97.9, 0.97788, 5, 139.01, 96.8, 0.02091, 42, -241.25, 180.43, 0.00121, 2, 6, 95.17, 92.41, 0.99912, 42, -217.42, 174.94, 0.00088, 1, 6, 113.23, 86.17, 1, 1, 6, 131.3, 79.93, 1, 7, 6, 11.89, -107.44, 0.40014, 5, 76.43, -107.61, 0.2605, 4, 384.91, -99.38, 0.01257, 7, 105.87, 79.22, 0.01467, 41, -373.76, -24.91, 0.00506, 42, -300.7, -24.91, 6e-05, 54, 23.15, 1.46, 0.307, 7, 6, 28.36, -103.7, 0.51145, 5, 92.96, -104.13, 0.23857, 4, 401.01, -94.25, 0.00549, 7, 121.97, 84.35, 0.00547, 41, -357.28, -21.17, 0.00921, 42, -284.22, -21.17, 2e-05, 54, 39.69, 4.94, 0.22979, 6, 6, 39.6, -92.83, 0.63228, 5, 104.36, -93.43, 0.20682, 4, 411.28, -82.46, 0.00244, 7, 132.24, 96.15, 0.00147, 41, -346.04, -10.3, 0.014, 54, 51.09, 15.64, 0.14298, 4, 6, 55.51, -80.6, 0.82276, 5, 120.45, -81.44, 0.12586, 41, -330.14, 1.93, 0.01959, 54, 67.18, 27.62, 0.03178, 3, 6, 73.76, -74.01, 0.95212, 5, 138.81, -75.13, 0.02215, 41, -311.89, 8.52, 0.02573, 3, 6, 94.35, -68.85, 0.96483, 5, 159.48, -70.29, 0.00573, 41, -291.29, 13.68, 0.02945, 2, 6, 112.66, -66.19, 0.97074, 41, -272.99, 16.34, 0.02926, 3, 6, 128.49, -60.97, 0.97234, 7, 218.08, 135.47, 0.00059, 41, -257.16, 21.56, 0.02707, 7, 6, 41.49, 104.74, 0.70041, 5, 109.28, 104.09, 0.16688, 4, 396.31, 114.55, 0.00101, 8, 146.84, -50.03, 0.00479, 41, -344.16, 187.27, 0.00564, 42, -271.09, 187.27, 0.00029, 53, 2.7, -31.73, 0.12097, 6, 6, 56.99, 92.21, 0.86637, 5, 124.58, 91.32, 0.08109, 4, 412.82, 103.39, 0.00014, 8, 163.35, -61.2, 0.00066, 41, -328.66, 174.74, 0.01096, 53, -17.19, -33.09, 0.04079, 3, 6, 69.3, 87.01, 0.97232, 5, 136.81, 85.93, 0.01409, 41, -316.35, 169.54, 0.01359, 3, 6, 84.76, 80.56, 0.98091, 5, 152.17, 79.25, 0.00248, 41, -300.89, 163.09, 0.01661, 2, 6, 101.12, 76.76, 0.98238, 41, -284.52, 159.3, 0.01762, 2, 6, 116.6, 72.53, 0.98247, 41, -269.05, 155.06, 0.01753], "hull": 43, "edges": [10, 12, 12, 14, 18, 20, 20, 22, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 78, 80, 8, 10, 14, 16, 16, 18, 80, 82, 82, 84, 74, 76, 76, 78, 4, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 4, 2, 44, 2, 56, 58, 58, 60, 60, 62, 62, 64, 4, 6, 6, 8, 2, 0, 0, 84, 24, 22, 44, 42], "width": 485, "height": 1681}}, "earring_L": {"earring_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 28, 30.73, 13.1, 1, 1, 28, 29.92, -14.89, 1, 1, 28, -4.06, -13.9, 1, 1, 28, -3.25, 14.08, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 34}}, "earring_R": {"earring_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 29, 27.82, 13.3, 1, 1, 29, 28.27, -14.7, 1, 1, 29, -2.73, -15.19, 1, 1, 29, -3.17, 12.8, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 31}}, "ear_L": {"ear_L": {"type": "mesh", "uvs": [0.95506, 0.22665, 0.95687, 0.70755, 0.68155, 0.98031, 0.33064, 0.9599, 0.03062, 0.86361, 0.03073, 0.66079, 0.03091, 0.31587, 0.03101, 0.11251, 0.4463, 0, 0.79031, 0.02814], "triangles": [2, 3, 1, 4, 5, 3, 3, 5, 1, 5, 6, 1, 6, 0, 1, 0, 6, 8, 0, 8, 9, 8, 6, 7], "vertices": [2, 6, 62.23, -82.25, 0.995, 42, -250.35, 0.28, 0.005, 2, 6, 35.04, -78.76, 0.995, 42, -277.54, 3.77, 0.005, 2, 6, 20.77, -68.01, 0.995, 42, -291.82, 14.53, 0.005, 2, 6, 23.38, -57.02, 0.995, 42, -289.21, 25.51, 0.005, 2, 6, 30.06, -48.21, 0.995, 42, -282.52, 34.32, 0.005, 2, 6, 41.53, -49.72, 0.995, 42, -271.06, 32.82, 0.005, 2, 6, 61.02, -52.27, 0.995, 42, -251.56, 30.26, 0.005, 2, 6, 72.51, -53.78, 0.995, 42, -240.07, 28.76, 0.005, 2, 6, 77.15, -67.78, 0.995, 42, -235.43, 14.75, 0.005, 2, 6, 74.13, -78.49, 0.995, 42, -238.45, 4.04, 0.005], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 6, 8, 14, 16, 16, 18, 4, 6, 12, 14, 8, 10, 10, 12], "width": 32, "height": 57}}, "ear_R": {"ear_R": {"type": "mesh", "uvs": [0.68277, 0.07703, 0.77482, 0.31093, 0.90446, 0.66877, 0.98912, 0.90249, 0.61106, 0.99007, 0.40996, 0.97784, 0.12534, 0.68151, 0, 0.38907, 0.02797, 0.05686, 0.28051, 0.00854], "triangles": [3, 4, 2, 4, 5, 2, 5, 6, 2, 6, 7, 1, 6, 1, 2, 0, 1, 9, 9, 1, 8, 7, 8, 1], "vertices": [2, 6, 76.22, 57.51, 0.995, 42, -236.36, 140.04, 0.005, 2, 6, 63.33, 56.32, 0.995, 42, -249.26, 138.85, 0.005, 2, 6, 43.65, 54.84, 0.995, 42, -268.94, 137.37, 0.005, 2, 6, 30.79, 53.87, 0.995, 42, -281.79, 136.4, 0.005, 2, 6, 27.62, 66.1, 0.995, 42, -284.96, 148.63, 0.005, 2, 6, 29.09, 72.2, 0.995, 42, -283.5, 154.73, 0.005, 2, 6, 46.1, 78.87, 0.995, 42, -266.49, 161.41, 0.005, 2, 6, 62.26, 80.68, 0.995, 42, -250.33, 163.21, 0.005, 2, 6, 79.93, 77.5, 0.995, 42, -232.65, 160.03, 0.005, 2, 6, 81.51, 69.39, 0.995, 42, -231.08, 151.93, 0.005], "hull": 10, "edges": [8, 10, 10, 12, 16, 18, 6, 8, 12, 14, 14, 16, 0, 2, 2, 4, 4, 6, 18, 0], "width": 31, "height": 54}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.03404, 0.52847, 0.1118, 0.92674, 0.63389, 0.92674, 0.94271, 0.57711, 0.85384, 0.0694, 0.38062, 0.14845], "triangles": [1, 0, 5, 3, 2, 5, 3, 5, 4, 1, 5, 2], "vertices": [2, 6, 57.75, -7.25, 0.96, 41, -327.9, 75.28, 0.04, 2, 6, 42.22, -9.29, 0.96, 41, -343.43, 73.24, 0.04, 2, 6, 38.7, -36.21, 0.96251, 41, -346.95, 46.32, 0.03749, 2, 6, 49.79, -53.86, 0.97018, 41, -335.86, 28.67, 0.02982, 2, 6, 69.52, -51.78, 0.96925, 41, -316.13, 30.75, 0.03075, 2, 6, 69.73, -26.99, 0.96, 41, -315.91, 55.54, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 52, "height": 38}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.06018, 0.199, 0.09253, 0.65121, 0.45759, 0.93838, 0.90584, 0.77333, 0.94974, 0.38384, 0.51305, 0.05376], "triangles": [1, 0, 5, 3, 5, 4, 2, 1, 5, 3, 2, 5], "vertices": [2, 6, 69.62, 59.54, 0.96896, 41, -316.02, 142.07, 0.03104, 2, 6, 53.72, 59.99, 0.96843, 41, -331.93, 142.52, 0.03157, 2, 6, 41.39, 43.19, 0.96, 41, -344.26, 125.72, 0.04, 2, 6, 44.21, 20.22, 0.96, 41, -341.44, 102.75, 0.04, 2, 6, 57.44, 16.27, 0.96, 41, -328.2, 98.8, 0.04, 2, 6, 71.73, 36.43, 0.96, 41, -313.92, 118.96, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 50, "height": 35}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 26, -9.79, -6.93, 0.96343, 41, -337.81, 45.08, 0.03657, 2, 26, -7.59, 9.93, 0.96, 41, -335.61, 61.94, 0.04, 2, 26, 9.27, 7.72, 0.96, 41, -318.75, 59.74, 0.04, 2, 26, 7.07, -9.13, 0.96343, 41, -320.96, 42.88, 0.03657], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 17}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.7935, 0.04991, 0.97955, 0.17579, 0.94141, 0.31017, 0.84116, 0.42019, 0.86565, 0.50569, 0.80114, 0.70939, 0.69943, 0.90188, 0.54375, 0.96741, 0.37425, 0.96421, 0.2363, 0.89209, 0.12902, 0.79053, 0.02352, 0.76173, 0.01636, 0.70065, 0.08112, 0.59836, 0.16917, 0.42831, 0.27051, 0.27298, 0.39849, 0.19035, 0.56803, 0.18582, 0.60647, 0.08871, 0.09733, 0.69729, 0.14845, 0.57951, 0.22718, 0.45191, 0.3284, 0.35212, 0.43984, 0.3145, 0.55333, 0.31613, 0.64841, 0.34067, 0.72305, 0.40611, 0.68624, 0.53207, 0.60854, 0.65312, 0.50016, 0.73982, 0.38667, 0.74473, 0.27932, 0.73001, 0.18321, 0.70546], "triangles": [2, 0, 1, 23, 16, 17, 24, 23, 17, 25, 18, 0, 26, 25, 0, 17, 18, 25, 24, 17, 25, 22, 15, 16, 22, 16, 23, 0, 3, 26, 2, 3, 0, 21, 14, 15, 21, 15, 22, 27, 25, 26, 20, 14, 21, 20, 13, 14, 27, 28, 24, 27, 24, 25, 19, 13, 20, 12, 13, 19, 32, 20, 21, 19, 20, 32, 26, 4, 27, 4, 26, 3, 5, 27, 4, 31, 21, 22, 32, 21, 31, 29, 23, 24, 29, 24, 28, 30, 22, 23, 30, 23, 29, 31, 22, 30, 11, 12, 19, 10, 19, 32, 11, 19, 10, 9, 32, 31, 10, 32, 9, 5, 28, 27, 6, 28, 5, 8, 31, 30, 9, 31, 8, 7, 29, 28, 7, 28, 6, 8, 30, 29, 7, 8, 29], "vertices": [2, 6, 65.87, -49.98, 0.96789, 41, -319.78, 32.55, 0.03211, 2, 6, 60.96, -58.34, 0.97198, 41, -324.69, 24.19, 0.02802, 2, 6, 57.2, -56, 0.97114, 41, -328.45, 26.53, 0.02886, 2, 6, 54.55, -50.81, 0.9689, 41, -331.09, 31.72, 0.0311, 2, 6, 51.86, -51.64, 0.9693, 41, -333.79, 30.89, 0.0307, 2, 6, 46.2, -47.78, 0.96737, 41, -339.45, 34.75, 0.03263, 2, 6, 41.11, -42.19, 0.96473, 41, -344.54, 40.34, 0.03527, 2, 6, 40.13, -34.52, 0.96122, 41, -345.52, 48.01, 0.03878, 2, 6, 41.28, -26.47, 0.96, 41, -344.37, 56.06, 0.04, 2, 6, 44.28, -20.18, 0.96, 41, -341.37, 62.35, 0.04, 2, 6, 47.97, -15.47, 0.96, 41, -337.68, 67.06, 0.04, 2, 6, 49.48, -10.56, 0.96, 41, -336.17, 71.97, 0.04, 2, 6, 51.34, -10.46, 0.96, 41, -334.31, 72.07, 0.04, 2, 6, 53.98, -13.94, 0.96, 41, -331.67, 68.59, 0.04, 2, 6, 58.49, -18.79, 0.96, 41, -327.16, 63.74, 0.04, 2, 6, 62.48, -24.22, 0.96, 41, -323.17, 58.31, 0.04, 2, 6, 64.15, -30.63, 0.96, 41, -321.5, 51.9, 0.04, 2, 6, 63.22, -38.72, 0.96294, 41, -322.42, 43.81, 0.03706, 2, 6, 65.87, -40.92, 0.96378, 41, -319.77, 41.61, 0.03622, 2, 6, 50.94, -14.33, 0.96, 41, -334.71, 68.21, 0.04, 2, 6, 54.12, -17.22, 0.96, 41, -331.52, 65.31, 0.04, 2, 6, 57.43, -21.46, 0.96, 41, -328.22, 61.07, 0.04, 2, 6, 59.77, -26.67, 0.96, 41, -325.88, 55.87, 0.04, 2, 6, 60.19, -32.12, 0.96012, 41, -325.45, 50.41, 0.03988, 2, 6, 59.44, -37.51, 0.96261, 41, -326.21, 45.02, 0.03739, 2, 6, 58.12, -41.94, 0.9647, 41, -327.53, 40.59, 0.0353, 2, 6, 55.71, -45.24, 0.96632, 41, -329.94, 37.29, 0.03368, 2, 6, 52.19, -43, 0.96534, 41, -333.46, 39.53, 0.03466, 2, 6, 49.07, -38.83, 0.96342, 41, -336.58, 43.7, 0.03658, 2, 6, 47.17, -33.33, 0.96085, 41, -338.48, 49.2, 0.03915, 2, 6, 47.73, -27.91, 0.96, 41, -337.92, 54.62, 0.04, 2, 6, 48.83, -22.86, 0.96, 41, -336.82, 59.67, 0.04, 2, 6, 50.16, -18.38, 0.96, 41, -335.49, 64.15, 0.04], "hull": 19, "edges": [10, 12, 22, 24, 24, 26, 20, 22, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 12, 14, 14, 16, 8, 10, 4, 6, 6, 8, 2, 4, 2, 0, 36, 0, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 38], "width": 48, "height": 30}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 27, -9.83, -6.83, 0.96, 41, -335.94, 113.42, 0.04, 2, 27, -7.62, 10.03, 0.96275, 41, -333.74, 130.27, 0.03725, 2, 27, 9.23, 7.82, 0.96276, 41, -316.88, 128.07, 0.03724, 2, 27, 7.03, -9.03, 0.96, 41, -319.08, 111.21, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 17}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.14694, 0.12424, 0.27117, 0.05182, 0.42392, 0.05144, 0.4803, 0.13108, 0.63119, 0.13561, 0.74599, 0.20497, 0.83522, 0.333, 0.91048, 0.459, 0.96292, 0.55402, 0.96299, 0.63283, 0.90036, 0.67574, 0.78712, 0.82191, 0.63307, 0.92287, 0.42259, 0.94621, 0.24147, 0.87361, 0.13385, 0.61631, 0.14444, 0.45761, 0.06779, 0.40657, 0, 0.15186, 0.25074, 0.40564, 0.34268, 0.28813, 0.4614, 0.23951, 0.60455, 0.24559, 0.72792, 0.30029, 0.81404, 0.40766, 0.88736, 0.5353, 0.81288, 0.58189, 0.73141, 0.64065, 0.61968, 0.69332, 0.49398, 0.70548, 0.38807, 0.65483, 0.29846, 0.54745], "triangles": [21, 2, 3, 22, 3, 4, 21, 3, 22, 20, 1, 2, 20, 2, 21, 23, 4, 5, 22, 4, 23, 23, 5, 6, 19, 0, 1, 19, 1, 20, 17, 18, 0, 16, 17, 0, 24, 23, 6, 19, 16, 0, 24, 6, 7, 25, 24, 7, 31, 19, 20, 25, 7, 8, 24, 27, 23, 26, 24, 25, 26, 27, 24, 30, 20, 21, 31, 20, 30, 9, 10, 25, 9, 25, 8, 26, 25, 10, 28, 22, 23, 28, 23, 27, 29, 21, 22, 29, 22, 28, 30, 21, 29, 11, 27, 26, 11, 26, 10, 16, 31, 15, 31, 16, 19, 14, 31, 30, 14, 15, 31, 11, 12, 28, 11, 28, 27, 13, 30, 29, 14, 30, 13, 12, 13, 29, 12, 29, 28], "vertices": [2, 6, 68, 57.84, 0.96662, 41, -317.65, 140.37, 0.03338, 2, 6, 69.18, 51.79, 0.96415, 41, -316.47, 134.33, 0.03585, 2, 6, 68.26, 44.67, 0.96138, 41, -317.39, 127.21, 0.03862, 2, 6, 65.79, 42.33, 0.96054, 41, -319.86, 124.86, 0.03946, 2, 6, 64.75, 35.31, 0.96, 41, -320.9, 117.84, 0.04, 2, 6, 62.19, 30.2, 0.96, 41, -323.46, 112.73, 0.04, 2, 6, 58.22, 26.49, 0.96, 41, -327.43, 109.02, 0.04, 2, 6, 54.39, 23.43, 0.96, 41, -331.26, 105.96, 0.04, 2, 6, 51.52, 21.31, 0.96, 41, -334.12, 103.84, 0.04, 2, 6, 49.41, 21.59, 0.96, 41, -336.24, 104.12, 0.04, 2, 6, 48.65, 24.66, 0.96, 41, -337, 107.19, 0.04, 2, 6, 45.42, 30.44, 0.96, 41, -340.23, 112.97, 0.04, 2, 6, 43.66, 37.98, 0.96, 41, -341.99, 120.51, 0.04, 2, 6, 44.32, 47.87, 0.96141, 41, -341.33, 130.4, 0.03859, 2, 6, 47.36, 56.05, 0.96491, 41, -338.29, 138.59, 0.03509, 2, 6, 54.91, 60.17, 0.96741, 41, -330.74, 142.7, 0.03259, 2, 6, 59.09, 59.12, 0.96726, 41, -326.56, 141.65, 0.03274, 2, 6, 60.92, 62.51, 0.96868, 41, -324.72, 145.04, 0.03132, 2, 6, 68.16, 64.78, 0.96934, 41, -317.49, 147.31, 0.03066, 2, 6, 59.83, 53.98, 0.96523, 41, -325.81, 136.52, 0.03477, 2, 6, 62.42, 49.29, 0.96338, 41, -323.23, 131.82, 0.03662, 2, 6, 63, 43.59, 0.96109, 41, -322.65, 126.12, 0.03891, 2, 6, 61.96, 36.94, 0.96, 41, -323.68, 119.47, 0.04, 2, 6, 59.75, 31.38, 0.96, 41, -325.9, 113.91, 0.04, 2, 6, 56.35, 27.74, 0.96, 41, -329.3, 110.27, 0.04, 2, 6, 52.49, 24.77, 0.96, 41, -333.16, 107.3, 0.04, 2, 6, 51.69, 28.4, 0.96, 41, -333.96, 110.93, 0.04, 2, 6, 50.61, 32.41, 0.96, 41, -335.03, 114.94, 0.04, 2, 6, 49.89, 37.8, 0.96, 41, -335.76, 120.33, 0.04, 2, 6, 50.33, 43.7, 0.96056, 41, -335.32, 126.23, 0.03944, 2, 6, 52.33, 48.46, 0.9626, 41, -333.32, 130.99, 0.0374, 2, 6, 55.75, 52.26, 0.96436, 41, -329.9, 134.79, 0.03564], "hull": 19, "edges": [24, 26, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 36, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 38, 34, 36], "width": 47, "height": 27}}, "hat": {"hat": {"type": "mesh", "uvs": [0.65209, 0.06694, 0.92934, 0.19753, 0.98703, 0.26133, 0.99836, 0.32357, 0.9903, 0.38848, 0.89533, 0.51139, 0.90738, 0.63478, 0.85043, 0.65488, 0.82629, 0.74647, 0.77386, 0.83459, 0.67425, 0.9204, 0.53396, 0.98001, 0.38504, 1, 0.2849, 0.977, 0.22904, 0.91058, 0.20178, 0.81528, 0.15787, 0.83116, 0.15606, 0.68952, 0.0497, 0.63227, 0.00543, 0.55761, 0.007, 0.46828, 0.05458, 0.37255, 0.28081, 0.1059, 0.43098, 0.00195, 0.48639, 0, 0.15221, 0.63218, 0.23983, 0.56053, 0.41015, 0.49033, 0.50945, 0.581, 0.57996, 0.46048, 0.78014, 0.44705, 0.88981, 0.45962, 0.26201, 0.78589, 0.36504, 0.74347, 0.49985, 0.70253, 0.63466, 0.67181, 0.75792, 0.65719, 0.35711, 0.37651, 0.38292, 0.20662, 0.44316, 0.08713, 0.53904, 0.17114, 0.6005, 0.31863, 0.29645, 0.20102, 0.09362, 0.43625, 0.03585, 0.5352, 0.61081, 0.13764, 0.8456, 0.25153, 0.95869, 0.29073], "triangles": [34, 11, 12, 12, 13, 33, 12, 33, 34, 11, 35, 10, 11, 34, 35, 14, 32, 13, 13, 32, 33, 9, 35, 36, 9, 10, 35, 14, 15, 32, 9, 36, 8, 16, 17, 15, 15, 17, 32, 17, 26, 32, 32, 26, 33, 8, 36, 7, 33, 27, 34, 33, 26, 27, 34, 28, 35, 34, 27, 28, 18, 25, 17, 17, 25, 26, 28, 29, 35, 36, 35, 30, 36, 30, 7, 30, 35, 29, 7, 5, 6, 5, 30, 31, 5, 7, 30, 19, 44, 18, 18, 44, 25, 44, 43, 25, 25, 43, 26, 28, 27, 29, 26, 37, 27, 37, 26, 42, 19, 20, 44, 44, 20, 43, 5, 31, 4, 27, 37, 40, 29, 27, 41, 40, 38, 39, 38, 40, 37, 20, 21, 43, 29, 41, 30, 41, 27, 40, 30, 46, 31, 31, 47, 4, 31, 46, 47, 30, 41, 46, 26, 43, 42, 43, 21, 42, 4, 47, 3, 37, 42, 38, 21, 22, 42, 47, 2, 3, 41, 45, 46, 41, 40, 45, 46, 1, 47, 47, 1, 2, 45, 0, 46, 46, 0, 1, 38, 42, 39, 42, 22, 39, 40, 39, 45, 39, 24, 45, 45, 24, 0, 22, 23, 39, 39, 23, 24], "vertices": [2, 6, 212.88, -42.34, 0.96151, 41, -172.77, 40.19, 0.03849, 2, 6, 183.43, -106.43, 0.98091, 41, -202.22, -23.9, 0.01909, 2, 6, 171.49, -119.01, 0.9914, 41, -214.16, -36.48, 0.0086, 2, 6, 161.26, -120.45, 0.99607, 41, -224.39, -37.92, 0.00393, 1, 6, 151.22, -117.16, 1, 1, 6, 134.71, -91.73, 1, 1, 6, 114.75, -92.07, 1, 2, 6, 113.36, -77.93, 0.98303, 41, -272.29, 4.6, 0.01697, 2, 6, 99.59, -70.22, 0.97529, 41, -286.06, 12.31, 0.02471, 2, 6, 87.26, -55.76, 0.96509, 41, -298.39, 26.77, 0.03491, 2, 6, 76.78, -29.98, 0.94844, 41, -308.87, 52.55, 0.05156, 2, 6, 71.74, 5.06, 0.9409, 41, -313.9, 87.59, 0.0591, 2, 6, 73.26, 41.36, 0.94933, 41, -312.38, 123.89, 0.05067, 2, 6, 80.07, 65.01, 0.96629, 41, -305.58, 147.54, 0.03371, 2, 6, 92.36, 77.09, 0.97577, 41, -293.29, 159.62, 0.02423, 2, 6, 108.34, 81.68, 0.98278, 41, -277.31, 164.21, 0.01722, 1, 6, 107.21, 92.59, 1, 1, 6, 129.73, 90.09, 1, 1, 6, 142.17, 114.53, 1, 2, 6, 155.41, 123.65, 0.99644, 41, -230.24, 206.18, 0.00356, 2, 6, 169.53, 121.42, 0.99289, 41, -216.12, 203.95, 0.00711, 2, 6, 183.22, 107.97, 0.98192, 41, -202.43, 190.5, 0.01808, 2, 6, 218.4, 47.93, 0.9609, 41, -167.25, 130.46, 0.0391, 2, 6, 230.16, 9.59, 0.96, 41, -155.49, 92.12, 0.04, 2, 6, 228.72, -3.8, 0.96, 41, -156.93, 78.73, 0.04, 1, 6, 138.95, 89.83, 1, 2, 6, 147.56, 67.23, 0.97557, 41, -238.09, 149.76, 0.02443, 2, 6, 153.33, 24.74, 0.96, 41, -232.32, 107.27, 0.04, 2, 6, 135.82, 2.69, 0.96, 41, -249.83, 85.22, 0.04, 2, 6, 152.72, -16.8, 0.96019, 41, -232.93, 65.73, 0.03981, 2, 6, 148.54, -65.31, 0.97958, 41, -237.1, 17.22, 0.02042, 1, 6, 143.1, -91.47, 1, 2, 6, 111.11, 66.56, 0.97592, 41, -274.54, 149.09, 0.02408, 2, 6, 114.59, 40.86, 0.96431, 41, -271.06, 123.39, 0.03569, 2, 6, 116.84, 7.52, 0.96, 41, -268.81, 90.05, 0.04, 2, 6, 117.47, -25.6, 0.96194, 41, -268.18, 56.94, 0.03806, 2, 6, 115.91, -55.6, 0.97411, 41, -269.74, 26.93, 0.02589, 2, 6, 173.06, 35.16, 0.96, 41, -212.59, 117.69, 0.04, 2, 6, 199.2, 25.41, 0.96, 41, -186.45, 107.94, 0.04, 2, 6, 216.26, 8.42, 0.96, 41, -169.39, 90.95, 0.04, 2, 6, 199.91, -12.94, 0.96, 41, -185.74, 69.59, 0.04, 2, 6, 174.58, -24.69, 0.96, 41, -211.07, 57.84, 0.04, 2, 6, 202.81, 46.13, 0.96082, 41, -182.84, 128.66, 0.03918, 2, 6, 171.88, 99.88, 0.97764, 41, -213.77, 182.41, 0.02236, 2, 6, 158, 115.86, 0.98856, 41, -227.64, 198.39, 0.01144, 2, 6, 202.97, -30.93, 0.9602, 41, -182.68, 51.6, 0.0398, 2, 6, 177.5, -85.14, 0.96927, 41, -208.15, -2.61, 0.03073, 2, 6, 167.72, -111.57, 0.98451, 41, -217.93, -29.04, 0.01549], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 10, 12, 14, 16, 16, 18, 12, 14, 36, 38, 38, 40, 44, 46, 46, 48, 26, 28, 28, 30, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 30, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 14, 54, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 58, 78, 84, 84, 86, 86, 88, 88, 36, 78, 90, 90, 92, 92, 94, 94, 8], "width": 243, "height": 160}}, "head": {"head": {"type": "mesh", "uvs": [0.17362, 0.07479, 0.32273, 0.05838, 0.51457, 0.03727, 0.68034, 0.01903, 0.8461, 0.00079, 0.89532, 0.11297, 0.94617, 0.23391, 0.984, 0.32727, 0.99462, 0.4317, 0.98437, 0.52852, 0.94532, 0.7042, 0.90755, 0.77139, 0.80604, 0.87187, 0.67554, 0.97446, 0.56893, 0.99376, 0.47926, 0.98658, 0.34384, 0.91291, 0.21492, 0.83034, 0.15742, 0.7678, 0.08481, 0.61897, 0.03519, 0.5026, 0.00983, 0.4112, 0.00328, 0.3197, 0.00834, 0.20636, 0.02451, 0.0912, 0.14111, 0.43716, 0.17336, 0.41659, 0.21348, 0.40817, 0.26422, 0.40724, 0.30553, 0.41534, 0.3425, 0.43498, 0.36453, 0.45804, 0.33778, 0.46521, 0.30081, 0.47736, 0.25596, 0.48609, 0.20758, 0.48516, 0.16707, 0.46521, 0.11427, 0.41667, 0.16108, 0.39703, 0.21182, 0.3883, 0.2665, 0.38955, 0.31253, 0.4017, 0.34871, 0.42415, 0.38095, 0.45127, 0.61803, 0.44143, 0.63416, 0.41775, 0.66405, 0.39094, 0.70063, 0.37411, 0.74115, 0.3685, 0.78009, 0.36819, 0.81628, 0.37474, 0.83988, 0.3872, 0.82623, 0.41111, 0.80184, 0.43262, 0.76526, 0.44789, 0.71806, 0.44914, 0.6799, 0.44446, 0.6445, 0.44384, 0.59589, 0.44043, 0.62081, 0.40746, 0.65436, 0.38013, 0.69409, 0.36038, 0.73786, 0.3482, 0.78562, 0.34673, 0.833, 0.35377, 0.87964, 0.3743, 0.60045, 0.44751, 0.6384, 0.45425, 0.69941, 0.47843, 0.75687, 0.48348, 0.8193, 0.47309, 0.8583, 0.44099, 0.87915, 0.40229, 0.38101, 0.47145, 0.35029, 0.48205, 0.30471, 0.50678, 0.24674, 0.51542, 0.17787, 0.51581, 0.12981, 0.49343, 0.10504, 0.45653, 0.56678, 0.38159, 0.79251, 0.28746, 0.84191, 0.28696, 0.89116, 0.28808, 0.88991, 0.26975, 0.83614, 0.25439, 0.78361, 0.25043, 0.55976, 0.3297, 0.55101, 0.36289, 0.67106, 0.28957, 0.68308, 0.33725, 0.73717, 0.3131, 0.62399, 0.36289, 0.61572, 0.30988, 0.72515, 0.26925, 0.04936, 0.36287, 0.10375, 0.34751, 0.39764, 0.40003, 0.04623, 0.34454, 0.09, 0.32224, 0.14065, 0.31481, 0.38701, 0.35098, 0.40827, 0.38467, 0.26383, 0.3329, 0.15204, 0.35283, 0.2739, 0.37693, 0.21329, 0.36537, 0.20224, 0.32385, 0.32542, 0.34194, 0.33702, 0.39095, 0.14308, 0.20075, 0.34647, 0.19091, 0.53403, 0.17031, 0.74645, 0.13092, 0.45746, 0.45529, 0.46522, 0.56335, 0.45008, 0.61501, 0.48595, 0.65128, 0.54245, 0.64952, 0.57495, 0.60947, 0.54616, 0.55831, 0.53397, 0.4509, 0.41886, 0.59223, 0.39477, 0.62687, 0.39656, 0.66574, 0.44883, 0.69218, 0.51878, 0.68624, 0.59175, 0.68101, 0.62869, 0.65161, 0.62673, 0.61061, 0.60175, 0.58233, 0.40658, 0.52172, 0.59824, 0.51095, 0.49586, 0.44229, 0.50752, 0.55307, 0.51281, 0.60931, 0.37679, 0.78561, 0.41385, 0.76741, 0.45352, 0.74301, 0.48588, 0.73887, 0.52764, 0.74797, 0.56157, 0.73432, 0.59341, 0.73308, 0.65501, 0.74715, 0.70773, 0.75749, 0.37782, 0.80655, 0.4179, 0.83993, 0.47298, 0.86446, 0.52286, 0.86865, 0.63741, 0.84829, 0.6788, 0.81319, 0.71383, 0.77398, 0.76362, 0.78784, 0.7702, 0.75138, 0.72368, 0.73095, 0.36499, 0.7674, 0.3387, 0.79184, 0.35943, 0.82149, 0.37091, 0.79492, 0.42943, 0.79738, 0.47622, 0.78984, 0.53386, 0.79505, 0.58864, 0.78051, 0.64325, 0.77756, 0.72548, 0.7642, 0.69801, 0.77115, 0.39332, 0.79827, 0.58421, 0.86382, 0.49995, 0.8839, 0.60675, 0.87542, 0.46804, 0.81918, 0.53913, 0.823, 0.61444, 0.8082, 0.73284, 0.66937, 0.74738, 0.56196, 0.89862, 0.52499, 0.88291, 0.64345, 0.83438, 0.75455, 0.782, 0.83401, 0.69054, 0.91034, 0.61238, 0.92613, 0.51067, 0.93479, 0.44103, 0.92325, 0.34498, 0.86387, 0.27423, 0.80551, 0.19666, 0.69896, 0.14925, 0.58375, 0.28592, 0.58792, 0.31945, 0.69476], "triangles": [65, 7, 8, 65, 83, 7, 83, 6, 7, 5, 85, 113, 113, 3, 4, 99, 22, 23, 23, 24, 110, 95, 21, 22, 20, 21, 79, 25, 37, 38, 39, 38, 106, 28, 39, 40, 40, 105, 41, 79, 37, 25, 29, 28, 41, 27, 28, 34, 28, 40, 41, 20, 79, 78, 36, 79, 25, 35, 36, 27, 36, 78, 79, 186, 20, 78, 186, 19, 20, 33, 28, 29, 33, 34, 28, 76, 34, 75, 34, 33, 75, 114, 43, 97, 73, 43, 114, 61, 60, 90, 56, 45, 46, 56, 46, 47, 44, 45, 57, 56, 57, 45, 45, 44, 59, 45, 59, 46, 59, 60, 46, 46, 60, 47, 56, 47, 55, 55, 48, 54, 55, 47, 48, 54, 49, 53, 54, 48, 49, 52, 49, 50, 52, 53, 49, 60, 61, 47, 49, 63, 50, 47, 61, 48, 61, 62, 48, 48, 62, 49, 49, 62, 63, 63, 81, 64, 62, 91, 63, 9, 175, 8, 70, 71, 175, 175, 71, 8, 8, 71, 72, 54, 53, 70, 70, 53, 71, 53, 52, 71, 71, 52, 72, 72, 65, 8, 52, 51, 72, 52, 50, 51, 72, 51, 65, 50, 64, 51, 51, 64, 65, 50, 63, 64, 65, 64, 83, 15, 181, 14, 14, 180, 13, 14, 181, 180, 16, 182, 15, 15, 182, 181, 13, 179, 12, 179, 178, 12, 13, 180, 179, 182, 168, 181, 180, 181, 169, 181, 168, 169, 180, 169, 179, 16, 183, 182, 183, 146, 182, 182, 147, 168, 182, 146, 147, 16, 17, 183, 183, 17, 184, 178, 150, 152, 152, 150, 151, 151, 164, 152, 169, 149, 179, 149, 150, 179, 150, 178, 179, 169, 148, 167, 169, 168, 148, 168, 147, 148, 169, 167, 149, 11, 178, 177, 11, 12, 178, 148, 171, 167, 171, 148, 170, 146, 170, 147, 148, 147, 170, 183, 184, 157, 183, 157, 146, 157, 184, 156, 167, 172, 149, 167, 171, 172, 149, 172, 150, 157, 145, 146, 145, 166, 146, 146, 159, 170, 146, 166, 159, 178, 152, 177, 177, 152, 153, 17, 18, 184, 171, 170, 161, 170, 160, 161, 171, 162, 172, 171, 161, 162, 157, 158, 145, 157, 156, 158, 170, 159, 160, 150, 165, 151, 172, 163, 150, 150, 163, 165, 172, 162, 163, 145, 158, 166, 18, 185, 184, 184, 188, 156, 184, 185, 188, 158, 136, 166, 159, 166, 137, 166, 136, 137, 159, 137, 160, 160, 140, 161, 161, 140, 162, 158, 156, 136, 156, 155, 136, 156, 188, 155, 137, 138, 160, 160, 139, 140, 160, 138, 139, 152, 164, 153, 136, 155, 137, 140, 141, 162, 162, 142, 163, 162, 141, 142, 163, 143, 165, 163, 142, 143, 151, 165, 164, 11, 177, 10, 165, 144, 164, 165, 143, 144, 18, 19, 185, 138, 137, 125, 125, 137, 124, 137, 155, 124, 155, 188, 124, 153, 164, 154, 164, 144, 154, 144, 143, 154, 10, 177, 176, 154, 173, 153, 176, 177, 173, 177, 153, 173, 141, 140, 126, 143, 173, 154, 138, 125, 139, 140, 139, 126, 139, 125, 126, 142, 141, 127, 141, 126, 127, 143, 142, 127, 143, 127, 128, 173, 143, 128, 10, 176, 9, 19, 186, 185, 185, 186, 187, 185, 187, 188, 186, 76, 187, 188, 123, 124, 188, 187, 123, 125, 117, 126, 125, 124, 117, 126, 118, 127, 126, 117, 118, 127, 118, 128, 128, 119, 129, 119, 128, 118, 173, 174, 176, 174, 173, 129, 124, 116, 117, 124, 123, 116, 176, 174, 175, 70, 175, 174, 173, 128, 129, 117, 135, 118, 117, 116, 135, 118, 135, 119, 176, 175, 9, 70, 174, 69, 123, 187, 122, 187, 131, 122, 123, 122, 116, 131, 187, 75, 131, 75, 74, 116, 115, 135, 116, 122, 115, 119, 130, 129, 129, 130, 174, 130, 119, 120, 115, 134, 135, 119, 135, 120, 135, 134, 120, 122, 131, 115, 187, 76, 75, 120, 132, 130, 174, 132, 68, 68, 132, 67, 132, 174, 130, 134, 115, 114, 174, 68, 69, 132, 120, 121, 115, 131, 114, 121, 134, 133, 120, 134, 121, 133, 134, 114, 131, 73, 114, 132, 121, 66, 132, 66, 67, 66, 121, 58, 68, 55, 69, 69, 54, 70, 69, 55, 54, 68, 67, 56, 67, 57, 56, 68, 56, 55, 102, 133, 114, 66, 44, 67, 67, 44, 57, 58, 121, 80, 66, 58, 44, 121, 133, 80, 44, 58, 59, 101, 111, 112, 110, 0, 111, 0, 1, 111, 111, 1, 112, 1, 2, 112, 112, 3, 113, 112, 2, 3, 61, 90, 62, 58, 80, 59, 59, 92, 60, 74, 73, 131, 74, 31, 73, 31, 43, 73, 74, 33, 32, 74, 75, 33, 74, 32, 31, 31, 30, 43, 32, 33, 30, 32, 30, 31, 76, 186, 77, 77, 35, 76, 76, 35, 34, 30, 33, 29, 30, 42, 43, 43, 42, 97, 30, 29, 42, 77, 36, 35, 34, 35, 27, 186, 78, 77, 78, 36, 77, 29, 41, 42, 41, 109, 42, 27, 36, 26, 27, 39, 28, 36, 25, 26, 26, 25, 38, 27, 26, 39, 26, 38, 39, 40, 39, 106, 79, 21, 37, 38, 37, 96, 21, 95, 37, 110, 24, 0, 110, 99, 23, 107, 110, 111, 94, 112, 113, 113, 4, 5, 6, 85, 5, 83, 64, 82, 64, 81, 82, 82, 84, 83, 83, 84, 6, 81, 85, 82, 81, 86, 85, 82, 85, 84, 84, 85, 6, 85, 86, 113, 90, 91, 62, 63, 91, 81, 93, 89, 90, 90, 89, 91, 89, 94, 91, 91, 94, 81, 89, 112, 94, 94, 86, 81, 94, 113, 86, 93, 112, 89, 133, 102, 88, 133, 88, 80, 59, 80, 92, 80, 88, 92, 88, 87, 92, 88, 101, 87, 87, 93, 92, 60, 92, 90, 92, 93, 90, 87, 112, 93, 38, 96, 104, 95, 96, 37, 95, 22, 98, 95, 98, 96, 96, 100, 104, 98, 99, 96, 96, 99, 100, 98, 22, 99, 110, 100, 99, 108, 103, 111, 105, 40, 106, 106, 38, 104, 106, 103, 105, 105, 103, 108, 104, 107, 106, 106, 107, 103, 104, 100, 107, 103, 107, 111, 100, 110, 107, 114, 97, 102, 42, 109, 97, 41, 105, 109, 97, 109, 102, 105, 108, 109, 109, 101, 102, 109, 108, 101, 102, 101, 88, 111, 101, 108, 112, 87, 101], "vertices": [2, 6, 125.1, 42.03, 0.97027, 41, -260.55, 124.56, 0.02973, 2, 6, 125.28, 20.21, 0.9658, 41, -260.37, 102.74, 0.0342, 2, 6, 125.5, -7.88, 0.96539, 41, -260.15, 74.65, 0.03461, 2, 6, 125.7, -32.14, 0.97036, 41, -259.95, 50.39, 0.02964, 2, 6, 125.89, -56.41, 0.98769, 41, -259.76, 26.12, 0.01231, 2, 6, 104.61, -60.82, 0.98873, 41, -281.04, 21.71, 0.01127, 2, 6, 81.71, -65.27, 0.99054, 41, -303.94, 17.26, 0.00946, 2, 6, 64.06, -68.49, 0.99308, 41, -321.59, 14.04, 0.00692, 2, 6, 44.91, -67.54, 0.99286, 41, -340.74, 14.99, 0.00714, 2, 6, 27.53, -63.77, 0.99032, 41, -358.12, 18.76, 0.00968, 2, 6, -3.61, -53.99, 0.99064, 41, -389.26, 28.54, 0.00936, 2, 6, -15.1, -46.96, 0.98782, 41, -400.74, 35.57, 0.01218, 2, 6, -31.42, -29.99, 0.97618, 41, -417.07, 52.54, 0.02382, 2, 6, -47.58, -8.79, 0.96582, 41, -433.23, 73.74, 0.03418, 2, 6, -49.08, 7, 0.96003, 41, -434.73, 89.53, 0.03997, 2, 6, -46.09, 19.72, 0.96408, 41, -431.74, 102.25, 0.03592, 2, 6, -30.18, 37.44, 0.97687, 41, -415.83, 119.97, 0.02313, 2, 6, -12.77, 54.02, 0.98788, 41, -398.42, 136.55, 0.01212, 2, 6, -0.34, 60.8, 0.99149, 41, -385.99, 143.33, 0.00851, 2, 6, 28.03, 67.71, 0.99094, 41, -357.62, 150.24, 0.00906, 2, 6, 50.08, 72.09, 0.99009, 41, -335.57, 154.62, 0.00991, 2, 6, 67.14, 73.56, 0.99142, 41, -318.51, 156.09, 0.00858, 2, 6, 83.86, 72.33, 0.99245, 41, -301.79, 154.86, 0.00755, 2, 6, 104.33, 68.92, 0.9904, 41, -281.31, 151.45, 0.0096, 2, 6, 124.93, 63.86, 0.98813, 41, -260.72, 146.39, 0.01187, 2, 6, 59.96, 55.3, 0.96459, 41, -325.69, 137.83, 0.03541, 2, 6, 63.09, 50.18, 0.96348, 41, -322.56, 132.71, 0.03652, 2, 6, 63.86, 44.21, 0.96211, 41, -321.79, 126.74, 0.03789, 2, 6, 63.07, 36.89, 0.96039, 41, -322.57, 119.42, 0.03961, 2, 6, 60.83, 31.15, 0.96, 41, -324.82, 113.68, 0.04, 2, 6, 56.57, 26.3, 0.96, 41, -329.08, 108.83, 0.04, 2, 6, 51.97, 23.68, 0.96, 41, -333.68, 106.21, 0.04, 2, 6, 51.17, 27.69, 0.96, 41, -334.48, 110.22, 0.04, 2, 6, 49.66, 33.3, 0.96, 41, -335.99, 115.83, 0.04, 2, 6, 48.92, 39.95, 0.96087, 41, -336.73, 122.48, 0.03913, 2, 6, 50, 46.89, 0.96249, 41, -335.65, 129.42, 0.03751, 2, 6, 54.38, 52.24, 0.96381, 41, -331.27, 134.77, 0.03619, 2, 6, 64.18, 58.68, 0.96803, 41, -321.47, 141.21, 0.03197, 2, 6, 66.86, 51.48, 0.96386, 41, -318.78, 134.01, 0.03614, 2, 6, 67.49, 43.98, 0.96213, 41, -318.15, 126.51, 0.03787, 2, 6, 66.24, 36.15, 0.96028, 41, -319.41, 118.68, 0.03972, 2, 6, 63.17, 29.82, 0.96, 41, -322.48, 112.35, 0.04, 2, 6, 58.42, 25.15, 0.96, 41, -327.23, 107.68, 0.04, 2, 6, 52.89, 21.15, 0.96, 41, -332.76, 103.68, 0.04, 2, 6, 50.22, -13.17, 0.96, 41, -335.43, 69.37, 0.04, 2, 6, 54.21, -16.05, 0.96, 41, -331.43, 66.48, 0.04, 2, 6, 58.52, -20.98, 0.96, 41, -327.13, 61.55, 0.04, 2, 6, 60.88, -26.64, 0.96093, 41, -324.77, 55.89, 0.03907, 2, 6, 61.14, -32.6, 0.96232, 41, -324.51, 49.93, 0.03768, 2, 6, 60.46, -38.2, 0.96364, 41, -325.18, 44.33, 0.03636, 2, 6, 58.6, -43.25, 0.96484, 41, -327.05, 39.28, 0.03516, 2, 6, 55.89, -46.35, 0.96564, 41, -329.76, 36.18, 0.03436, 2, 6, 51.81, -43.82, 0.96517, 41, -333.84, 38.71, 0.03483, 2, 6, 48.36, -39.8, 0.96433, 41, -337.28, 42.73, 0.03567, 2, 6, 46.28, -34.18, 0.96304, 41, -339.37, 48.35, 0.03696, 2, 6, 46.94, -27.36, 0.96144, 41, -338.71, 55.17, 0.03856, 2, 6, 48.51, -21.99, 0.96016, 41, -337.14, 60.54, 0.03984, 2, 6, 49.29, -16.91, 0.96, 41, -336.36, 65.62, 0.04, 2, 6, 50.82, -10.01, 0.96, 41, -334.83, 72.52, 0.04, 2, 6, 56.33, -14.37, 0.96, 41, -329.32, 68.16, 0.04, 2, 6, 60.66, -19.84, 0.96, 41, -324.99, 62.69, 0.04, 2, 6, 63.5, -26.02, 0.96076, 41, -322.15, 56.51, 0.03924, 2, 6, 64.89, -32.61, 0.96231, 41, -320.76, 49.93, 0.03769, 2, 6, 64.25, -39.51, 0.96392, 41, -321.39, 43.02, 0.03608, 2, 6, 62.09, -46.15, 0.96548, 41, -323.56, 36.38, 0.03452, 2, 6, 57.48, -52.37, 0.97159, 41, -328.16, 30.16, 0.02841, 2, 6, 49.45, -10.49, 0.96, 41, -336.2, 72.04, 0.04, 2, 6, 47.51, -15.79, 0.96, 41, -338.14, 66.74, 0.04, 2, 6, 41.98, -23.99, 0.96068, 41, -343.67, 58.54, 0.03932, 2, 6, 39.98, -32.13, 0.96263, 41, -345.67, 50.4, 0.03737, 2, 6, 40.69, -41.35, 0.96475, 41, -344.96, 41.18, 0.03525, 2, 6, 45.78, -47.72, 0.96642, 41, -339.87, 34.81, 0.03358, 2, 6, 52.41, -51.64, 0.97087, 41, -333.23, 30.89, 0.02913, 2, 6, 49.23, 21.63, 0.96, 41, -336.42, 104.16, 0.04, 2, 6, 47.88, 26.29, 0.96, 41, -337.77, 108.82, 0.04, 2, 6, 44.25, 33.43, 0.96, 41, -341.4, 115.96, 0.04, 2, 6, 43.77, 41.97, 0.96134, 41, -341.88, 124.5, 0.03866, 2, 6, 45, 51.88, 0.96366, 41, -340.65, 134.41, 0.03634, 2, 6, 49.96, 58.26, 0.96893, 41, -335.69, 140.79, 0.03107, 2, 6, 57.12, 60.95, 0.97192, 41, -328.53, 143.48, 0.02808, 2, 32, 15.05, 3.87, 0.99915, 30, 42.24, 25.04, 0.00085, 4, 32, -21.92, 2.69, 0.00038, 31, 1.22, 3.25, 0.5368, 30, 12.27, 3.35, 0.45664, 42, -237.7, 40.63, 0.00619, 4, 32, -28.4, 5.74, 0.00042, 31, -5.18, 6.46, 0.03334, 30, 5.2, 2.25, 0.95611, 42, -238.54, 33.51, 0.01013, 4, 32, -34.73, 9.04, 0.0002, 31, -11.42, 9.93, 0.00629, 30, -1.9, 1.45, 0.97056, 42, -239.67, 26.46, 0.02295, 2, 30, -1.25, -1.85, 0.97603, 42, -236.32, 26.2, 0.02397, 2, 30, 6.87, -3.53, 0.98978, 42, -232.52, 33.57, 0.01022, 3, 31, -0.75, -3.36, 0.52581, 30, 14.51, -3.17, 0.46828, 42, -230.82, 41.03, 0.0059, 2, 32, 11.81, -5.11, 0.98616, 31, 34.74, -5.42, 0.01384, 1, 32, 15.61, -0.21, 1, 3, 32, -5.91, -4.66, 0.04006, 31, 17.04, -4.51, 0.95834, 42, -235.8, 58.14, 0.0016, 4, 32, -3.67, 3.95, 0.14977, 31, 19.5, 4.04, 0.83513, 30, 26.69, 14.62, 0.0135, 42, -244.68, 57.54, 0.0016, 4, 32, -12.65, 3.4, 0.00889, 31, 10.51, 3.72, 0.94631, 30, 19.55, 9.13, 0.04091, 42, -241.32, 49.19, 0.00389, 3, 32, 6.09, 4.42, 0.95399, 31, 29.27, 4.26, 0.04109, 30, 34.51, 20.48, 0.00492, 2, 32, 2.93, -4.83, 0.76587, 31, 25.87, -4.9, 0.23413, 2, 31, 8.36, -4.2, 0.99628, 42, -233.13, 49.88, 0.00372, 4, 35, -35.14, -7.68, 0.00021, 34, -11.05, -7.92, 0.01531, 33, -1.91, -2.14, 0.94658, 42, -237.42, 149.26, 0.0379, 4, 35, -28.02, -3.27, 1e-05, 34, -3.96, -3.46, 0.03814, 33, 6.42, -1.33, 0.94668, 42, -235.66, 141.08, 0.01516, 3, 35, 15.66, -3.71, 0.9983, 34, 39.72, -3.6, 0.00076, 33, 45.43, -20.98, 0.00093, 2, 33, -1.54, 1.23, 0.96145, 42, -234.04, 149.28, 0.03855, 2, 33, 5.61, 3.64, 0.98159, 42, -230.81, 142.46, 0.01841, 3, 34, -0.04, 3.54, 0.56121, 33, 13.06, 3.18, 0.43129, 42, -230.42, 135, 0.0075, 2, 35, 12.27, 4.75, 0.98934, 34, 36.27, 4.83, 0.01066, 2, 35, 16.58, -0.63, 0.99992, 33, 47.61, -18.63, 8e-05, 3, 35, -5.89, 4.22, 0.06555, 34, 18.12, 4.18, 0.93272, 42, -236.01, 117.72, 0.00172, 3, 34, 3.08, -2.89, 0.7984, 33, 12.98, -3.97, 0.19465, 42, -237.53, 134.26, 0.00695, 4, 35, -2.77, -3.35, 0.13539, 34, 21.29, -3.37, 0.85662, 33, 29.05, -12.53, 0.00665, 42, -244.19, 117.31, 0.00134, 4, 35, -11.8, -3.13, 0.0019, 34, 12.25, -3.21, 0.97353, 33, 21.04, -8.35, 0.02107, 42, -240.96, 125.75, 0.00351, 4, 35, -14.97, 3.96, 0.00276, 34, 9.04, 3.86, 0.99208, 33, 21.33, -0.59, 0.0013, 42, -233.21, 126.36, 0.00386, 2, 35, 3.19, 4.48, 0.8219, 34, 27.19, 4.51, 0.1781, 3, 35, 6.72, -3.93, 0.98193, 34, 30.78, -3.89, 0.01514, 33, 37.31, -17.24, 0.00293, 2, 6, 102.82, 49.41, 0.96577, 41, -282.83, 131.94, 0.03423, 2, 6, 100.78, 19.94, 0.96, 41, -284.86, 102.47, 0.04, 2, 6, 100.99, -7.52, 0.96, 41, -284.65, 75.01, 0.04, 2, 6, 104.15, -38.99, 0.96607, 41, -281.5, 43.54, 0.03393, 2, 6, 50.72, 10.25, 0.957, 41, -334.93, 92.78, 0.043, 2, 6, 30.97, 11.7, 0.955, 41, -354.68, 94.23, 0.045, 2, 6, 21.88, 15.1, 0.953, 41, -363.77, 97.63, 0.047, 2, 6, 14.62, 10.8, 0.953, 41, -371.02, 93.33, 0.047, 2, 6, 13.88, 2.64, 0.953, 41, -371.77, 85.17, 0.047, 2, 6, 20.54, -2.98, 0.953, 41, -365.11, 79.55, 0.047, 2, 6, 30.36, -0.06, 0.955, 41, -355.29, 82.47, 0.045, 2, 6, 50.08, -0.85, 0.957, 41, -335.57, 81.68, 0.043, 2, 6, 26.6, 19.05, 0.959, 41, -359.05, 101.58, 0.041, 2, 6, 20.77, 23.33, 0.959, 41, -364.88, 105.86, 0.041, 2, 6, 13.68, 24, 0.959, 41, -371.97, 106.53, 0.041, 2, 6, 7.9, 17.11, 0.959, 41, -377.75, 99.64, 0.041, 2, 6, 7.66, 6.91, 0.959, 41, -377.99, 89.44, 0.041, 2, 6, 7.24, -3.7, 0.959, 41, -378.41, 78.83, 0.041, 2, 6, 11.88, -9.71, 0.959, 41, -373.77, 72.82, 0.041, 2, 6, 19.36, -10.4, 0.959, 41, -366.29, 72.13, 0.041, 2, 6, 24.96, -7.48, 0.959, 41, -360.69, 75.05, 0.041, 2, 6, 39.63, 19.14, 0.96, 41, -346.02, 101.67, 0.04, 2, 6, 37.98, -8.67, 0.96, 41, -347.67, 73.86, 0.04, 2, 6, 52.36, 4.42, 0.956, 41, -333.29, 86.95, 0.044, 2, 6, 32.04, 5.37, 0.953, 41, -353.61, 87.9, 0.047, 2, 6, 21.74, 5.94, 0.95, 41, -363.91, 88.48, 0.05, 2, 6, -7.7, 29.68, 0.96116, 41, -393.35, 112.21, 0.03884, 2, 6, -5.09, 23.92, 0.95973, 41, -390.74, 106.45, 0.04027, 2, 6, -1.41, 17.64, 0.95925, 41, -387.06, 100.17, 0.04075, 2, 6, -1.27, 12.89, 0.95888, 41, -386.92, 95.42, 0.04112, 2, 6, -3.71, 7.1, 0.95837, 41, -389.35, 89.63, 0.04163, 2, 6, -1.87, 1.9, 0.9587, 41, -387.51, 84.43, 0.0413, 2, 6, -2.24, -2.71, 0.95902, 41, -387.89, 79.82, 0.04098, 2, 6, -5.95, -11.23, 0.95975, 41, -391.6, 71.3, 0.04025, 2, 6, -8.82, -18.56, 0.96225, 41, -394.46, 63.97, 0.03775, 2, 6, -11.52, 30.03, 0.96165, 41, -397.17, 112.56, 0.03835, 2, 6, -18.33, 25.06, 0.96046, 41, -403.98, 107.59, 0.03954, 2, 6, -23.81, 17.72, 0.96, 41, -409.46, 100.26, 0.04, 2, 6, -25.51, 10.65, 0.96, 41, -411.16, 93.18, 0.04, 2, 6, -23.97, -6.3, 0.96, 41, -409.62, 76.23, 0.04, 2, 6, -18.38, -13.08, 0.96103, 41, -404.03, 69.45, 0.03897, 2, 6, -11.92, -19.05, 0.96263, 41, -397.57, 63.48, 0.03737, 2, 6, -15.37, -25.88, 0.965, 41, -401.02, 56.65, 0.035, 2, 6, -8.88, -27.69, 0.96512, 41, -394.53, 54.84, 0.03488, 2, 6, -4.3, -21.49, 0.96278, 41, -389.95, 61.04, 0.03722, 2, 6, -4.17, 30.95, 0.96115, 41, -389.82, 113.48, 0.03885, 2, 6, -8.11, 35.31, 0.96302, 41, -393.76, 117.84, 0.03698, 2, 6, -13.88, 33.03, 0.96336, 41, -399.53, 115.56, 0.03664, 2, 6, -9.28, 30.75, 0.96167, 41, -394.93, 113.28, 0.03833, 2, 6, -10.82, 22.39, 0.96, 41, -396.47, 104.93, 0.04, 2, 6, -10.34, 15.49, 0.96, 41, -395.98, 98.02, 0.04, 2, 6, -12.36, 7.32, 0.96, 41, -398.01, 89.86, 0.04, 2, 6, -10.76, -0.9, 0.96, 41, -396.4, 81.63, 0.04, 2, 6, -11.25, -8.82, 0.96, 41, -396.89, 73.71, 0.04, 2, 6, -10.37, -20.96, 0.96313, 41, -396.02, 61.57, 0.03687, 2, 6, -11.11, -16.84, 0.96188, 41, -396.76, 65.69, 0.03812, 2, 6, -10.31, 27.61, 0.96074, 41, -395.96, 110.14, 0.03926, 2, 6, -25.79, 1.72, 0.96, 41, -411.44, 84.25, 0.04, 2, 6, -27.85, 14.31, 0.96, 41, -413.5, 96.84, 0.04, 2, 6, -28.32, -1.25, 0.96, 41, -413.97, 81.28, 0.04, 2, 6, -15.51, 17.36, 0.95896, 41, -401.15, 99.89, 0.04104, 2, 6, -17.54, 7.23, 0.95785, 41, -403.18, 89.76, 0.04215, 2, 6, -16.27, -3.95, 0.95913, 41, -401.91, 78.58, 0.04087, 2, 6, 6.7, -24.27, 0.96271, 41, -378.95, 58.27, 0.03729, 2, 6, 25.92, -28.9, 0.96231, 41, -359.73, 53.63, 0.03769, 2, 6, 29.78, -51.53, 0.96875, 41, -355.86, 31, 0.03125, 2, 6, 8.58, -46.46, 0.96928, 41, -377.06, 36.07, 0.03072, 2, 6, -10.66, -36.84, 0.97018, 41, -396.31, 45.69, 0.02982, 2, 6, -24.1, -27.43, 0.96842, 41, -409.75, 55.1, 0.03158, 2, 6, -36.23, -12.47, 0.96287, 41, -421.88, 70.06, 0.03713, 2, 6, -37.62, -0.85, 0.95835, 41, -423.27, 81.68, 0.04165, 2, 6, -37.29, 13.97, 0.95835, 41, -422.93, 96.5, 0.04165, 2, 6, -33.88, 23.71, 0.96217, 41, -419.53, 106.24, 0.03783, 2, 6, -21.3, 36.11, 0.96876, 41, -406.95, 118.64, 0.03124, 2, 6, -9.38, 44.9, 0.97138, 41, -395.03, 127.43, 0.02862, 2, 6, 11.41, 53.53, 0.9705, 41, -374.24, 136.06, 0.0295, 2, 6, 33.21, 57.61, 0.9694, 41, -352.44, 140.14, 0.0306, 2, 6, 29.88, 38.06, 0.96056, 41, -355.77, 120.59, 0.03944, 2, 6, 9.86, 35.77, 0.96143, 41, -375.79, 118.3, 0.03857], "hull": 25, "edges": [18, 20, 26, 28, 28, 30, 34, 36, 40, 42, 42, 44, 44, 46, 46, 48, 36, 38, 38, 40, 24, 26, 20, 22, 22, 24, 30, 32, 32, 34, 14, 16, 16, 18, 12, 14, 10, 12, 8, 10, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 50, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 88, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 174, 176, 176, 160, 162, 182, 182, 180, 160, 184, 184, 180, 174, 186, 186, 178, 172, 188, 188, 178, 190, 192, 190, 196, 196, 198, 198, 200, 202, 204, 204, 194, 192, 208, 208, 212, 212, 210, 200, 214, 214, 206, 202, 216, 216, 206, 194, 218, 218, 210, 2, 4, 4, 6, 6, 8, 2, 0, 0, 48, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 262, 244, 264, 260, 266, 268, 270, 268, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 290, 292, 292, 294, 294, 296, 298, 300, 300, 302, 304, 306, 306, 308, 310, 312, 312, 314, 318, 320, 320, 322, 322, 324, 324, 326, 326, 330, 330, 328, 316, 332, 332, 318, 296, 334, 334, 298, 336, 338, 340, 342, 342, 344, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 374, 376, 348, 346], "width": 145, "height": 183}}, "head2": {"head": {"type": "mesh", "uvs": [0.27423, 0.80551, 0.31945, 0.69476, 0.39477, 0.62687, 0.39656, 0.66574, 0.44883, 0.69218, 0.51878, 0.68624, 0.59175, 0.68101, 0.62869, 0.65161, 0.62673, 0.61061, 0.73284, 0.66937, 0.83438, 0.75455, 0.94532, 0.7042, 0.90755, 0.77139, 0.80604, 0.87187, 0.67554, 0.97446, 0.56893, 0.99376, 0.47926, 0.98658, 0.34384, 0.91291, 0.21492, 0.83034, 0.15742, 0.7678, 0.37679, 0.78561, 0.41385, 0.76741, 0.45352, 0.74301, 0.48588, 0.73887, 0.52764, 0.74797, 0.56157, 0.73432, 0.59341, 0.73308, 0.65501, 0.74715, 0.70773, 0.75749, 0.37782, 0.80655, 0.4179, 0.83993, 0.47298, 0.86446, 0.52286, 0.86865, 0.63741, 0.84829, 0.6788, 0.81319, 0.71383, 0.77398, 0.76362, 0.78784, 0.7702, 0.75138, 0.72368, 0.73095, 0.36499, 0.7674, 0.3387, 0.79184, 0.35943, 0.82149, 0.37091, 0.79492, 0.42943, 0.79738, 0.47622, 0.78984, 0.53386, 0.79505, 0.58864, 0.78051, 0.64325, 0.77756, 0.72548, 0.7642, 0.69801, 0.77115, 0.39332, 0.79827, 0.58421, 0.86382, 0.49995, 0.8839, 0.60675, 0.87542, 0.46804, 0.81918, 0.53913, 0.823, 0.61444, 0.8082, 0.782, 0.83401, 0.69054, 0.91034, 0.61238, 0.92613, 0.51067, 0.93479, 0.44103, 0.92325, 0.34498, 0.86387], "triangles": [16, 60, 15, 15, 59, 14, 15, 60, 59, 17, 61, 16, 16, 61, 60, 14, 58, 13, 58, 57, 13, 14, 59, 58, 61, 52, 60, 59, 60, 53, 60, 52, 53, 59, 53, 58, 17, 62, 61, 62, 30, 61, 61, 31, 52, 61, 30, 31, 17, 18, 62, 62, 18, 0, 57, 34, 36, 36, 34, 35, 35, 48, 36, 53, 33, 58, 33, 34, 58, 34, 57, 58, 53, 32, 51, 53, 52, 32, 52, 31, 32, 53, 51, 33, 12, 57, 10, 12, 13, 57, 32, 55, 51, 55, 32, 54, 30, 54, 31, 32, 31, 54, 62, 0, 41, 62, 41, 30, 41, 0, 40, 51, 56, 33, 51, 55, 56, 33, 56, 34, 41, 29, 30, 29, 50, 30, 30, 43, 54, 30, 50, 43, 57, 36, 10, 10, 36, 37, 18, 19, 0, 55, 54, 45, 54, 44, 45, 55, 46, 56, 55, 45, 46, 41, 42, 29, 41, 40, 42, 54, 43, 44, 34, 49, 35, 56, 47, 34, 34, 47, 49, 56, 46, 47, 29, 42, 50, 40, 0, 1, 42, 20, 50, 43, 50, 21, 50, 20, 21, 43, 21, 44, 44, 24, 45, 45, 24, 46, 42, 40, 20, 40, 39, 20, 39, 40, 1, 21, 22, 44, 44, 23, 24, 44, 22, 23, 36, 48, 37, 20, 39, 21, 24, 25, 46, 46, 26, 47, 46, 25, 26, 47, 27, 49, 47, 26, 27, 35, 49, 48, 12, 10, 11, 49, 28, 48, 49, 27, 28, 22, 21, 4, 4, 21, 3, 21, 39, 3, 39, 1, 3, 37, 48, 38, 48, 28, 38, 28, 27, 38, 9, 10, 37, 25, 24, 5, 27, 9, 38, 22, 4, 23, 24, 23, 5, 23, 4, 5, 26, 25, 6, 25, 5, 6, 27, 26, 6, 27, 6, 7, 37, 38, 9, 9, 27, 7, 1, 2, 3, 9, 7, 8], "vertices": [2, 6, -9.38, 44.9, 0.97138, 41, -395.03, 127.43, 0.02862, 2, 6, 9.86, 35.77, 0.96143, 41, -375.79, 118.3, 0.03857, 2, 6, 20.77, 23.33, 0.959, 41, -364.88, 105.86, 0.041, 2, 6, 13.68, 24, 0.959, 41, -371.97, 106.53, 0.041, 2, 6, 7.9, 17.11, 0.959, 41, -377.75, 99.64, 0.041, 2, 6, 7.66, 6.91, 0.959, 41, -377.99, 89.44, 0.041, 2, 6, 7.24, -3.7, 0.959, 41, -378.41, 78.83, 0.041, 2, 6, 11.88, -9.71, 0.959, 41, -373.77, 72.82, 0.041, 2, 6, 19.36, -10.4, 0.959, 41, -366.29, 72.13, 0.041, 2, 6, 6.7, -24.27, 0.96271, 41, -378.95, 58.27, 0.03729, 2, 6, -10.66, -36.84, 0.97018, 41, -396.31, 45.69, 0.02982, 2, 6, -3.61, -53.99, 0.99064, 41, -389.26, 28.54, 0.00936, 2, 6, -15.1, -46.96, 0.98782, 41, -400.74, 35.57, 0.01218, 2, 6, -31.42, -29.99, 0.97618, 41, -417.07, 52.54, 0.02382, 2, 6, -47.58, -8.79, 0.96582, 41, -433.23, 73.74, 0.03418, 2, 6, -49.08, 7, 0.96003, 41, -434.73, 89.53, 0.03997, 2, 6, -46.09, 19.72, 0.96408, 41, -431.74, 102.25, 0.03592, 2, 6, -30.18, 37.44, 0.97687, 41, -415.83, 119.97, 0.02313, 2, 6, -12.77, 54.02, 0.98788, 41, -398.42, 136.55, 0.01212, 2, 6, -0.34, 60.8, 0.99149, 41, -385.99, 143.33, 0.00851, 2, 6, -7.7, 29.68, 0.96116, 41, -393.35, 112.21, 0.03884, 2, 6, -5.09, 23.92, 0.95973, 41, -390.74, 106.45, 0.04027, 2, 6, -1.41, 17.64, 0.95925, 41, -387.06, 100.17, 0.04075, 2, 6, -1.27, 12.89, 0.95888, 41, -386.92, 95.42, 0.04112, 2, 6, -3.71, 7.1, 0.95837, 41, -389.35, 89.63, 0.04163, 2, 6, -1.87, 1.9, 0.9587, 41, -387.51, 84.43, 0.0413, 2, 6, -2.24, -2.71, 0.95902, 41, -387.89, 79.82, 0.04098, 2, 6, -5.95, -11.23, 0.95975, 41, -391.6, 71.3, 0.04025, 2, 6, -8.82, -18.56, 0.96225, 41, -394.46, 63.97, 0.03775, 2, 6, -11.52, 30.03, 0.96165, 41, -397.17, 112.56, 0.03835, 2, 6, -18.33, 25.06, 0.96046, 41, -403.98, 107.59, 0.03954, 2, 6, -23.81, 17.72, 0.96, 41, -409.46, 100.26, 0.04, 2, 6, -25.51, 10.65, 0.96, 41, -411.16, 93.18, 0.04, 2, 6, -23.97, -6.3, 0.96, 41, -409.62, 76.23, 0.04, 2, 6, -18.38, -13.08, 0.96103, 41, -404.03, 69.45, 0.03897, 2, 6, -11.92, -19.05, 0.96263, 41, -397.57, 63.48, 0.03737, 2, 6, -15.37, -25.88, 0.965, 41, -401.02, 56.65, 0.035, 2, 6, -8.88, -27.69, 0.96512, 41, -394.53, 54.84, 0.03488, 2, 6, -4.3, -21.49, 0.96278, 41, -389.95, 61.04, 0.03722, 2, 6, -4.17, 30.95, 0.96115, 41, -389.82, 113.48, 0.03885, 2, 6, -8.11, 35.31, 0.96302, 41, -393.76, 117.84, 0.03698, 2, 6, -13.88, 33.03, 0.96336, 41, -399.53, 115.56, 0.03664, 2, 6, -9.28, 30.75, 0.96167, 41, -394.93, 113.28, 0.03833, 2, 6, -10.82, 22.39, 0.96, 41, -396.47, 104.93, 0.04, 2, 6, -10.34, 15.49, 0.96, 41, -395.98, 98.02, 0.04, 2, 6, -12.36, 7.32, 0.96, 41, -398.01, 89.86, 0.04, 2, 6, -10.76, -0.9, 0.96, 41, -396.4, 81.63, 0.04, 2, 6, -11.25, -8.82, 0.96, 41, -396.89, 73.71, 0.04, 2, 6, -10.37, -20.96, 0.96313, 41, -396.02, 61.57, 0.03687, 2, 6, -11.11, -16.84, 0.96188, 41, -396.76, 65.69, 0.03812, 2, 6, -10.31, 27.61, 0.96074, 41, -395.96, 110.14, 0.03926, 2, 6, -25.79, 1.72, 0.96, 41, -411.44, 84.25, 0.04, 2, 6, -27.85, 14.31, 0.96, 41, -413.5, 96.84, 0.04, 2, 6, -28.32, -1.25, 0.96, 41, -413.97, 81.28, 0.04, 2, 6, -15.51, 17.36, 0.95896, 41, -401.15, 99.89, 0.04104, 2, 6, -17.54, 7.23, 0.95785, 41, -403.18, 89.76, 0.04215, 2, 6, -16.27, -3.95, 0.95913, 41, -401.91, 78.58, 0.04087, 2, 6, -24.1, -27.43, 0.96842, 41, -409.75, 55.1, 0.03158, 2, 6, -36.23, -12.47, 0.96287, 41, -421.88, 70.06, 0.03713, 2, 6, -37.62, -0.85, 0.95835, 41, -423.27, 81.68, 0.04165, 2, 6, -37.29, 13.97, 0.95835, 41, -422.93, 96.5, 0.04165, 2, 6, -33.88, 23.71, 0.96217, 41, -419.53, 106.24, 0.03783, 2, 6, -21.3, 36.11, 0.96876, 41, -406.95, 118.64, 0.03124], "hull": 20, "edges": [28, 30, 30, 32, 36, 38, 26, 28, 22, 24, 24, 26, 32, 34, 34, 36, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 72, 74, 74, 76, 78, 80, 80, 82, 86, 88, 88, 90, 90, 92, 92, 94, 94, 98, 98, 96, 84, 100, 100, 86, 64, 102, 102, 66, 104, 106, 108, 110, 110, 112, 20, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 0, 0, 38, 0, 2, 2, 4, 16, 18, 18, 20, 20, 22], "width": 145, "height": 183}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": -11.17, "curve": [0.444, -11.17, 0.889, 7.49, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 7.5, "curve": [1.778, 7.5, 2.222, -11.17, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -11.17, "curve": [3.111, -11.18, 3.556, 7.49, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 7.5, "curve": [4.444, 7.5, 4.889, -11.17, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -11.17, "curve": [5.667, -11.18, 6, 7.5, 5.778, 0, 5.889, 0]}, {"time": 6.3333, "x": 7.5, "curve": [6.778, 7.5, 7.222, -11.17, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -11.17, "curve": [8.111, -11.18, 8.556, 7.49, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 7.5, "curve": [9.444, 7.5, 9.889, -11.17, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -11.17, "curve": [10.778, -11.18, 11.222, 7.49, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 7.5, "curve": [12.111, 7.5, 12.556, -11.17, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -11.17}]}, "ALL2": {"translate": [{"y": -19.87, "curve": [0.444, 0, 0.889, 0, 0.444, -19.87, 0.889, 25.82]}, {"time": 1.3333, "y": 25.83, "curve": [1.778, 0, 2.222, 0, 1.778, 25.84, 2.222, -19.86]}, {"time": 2.6667, "y": -19.87, "curve": [3.111, 0, 3.556, 0, 3.111, -19.88, 3.556, 25.82]}, {"time": 4, "y": 25.83, "curve": [4.444, 0, 4.889, 0, 4.444, 25.84, 4.889, -19.85]}, {"time": 5.3333, "y": -19.87, "curve": [5.667, 0, 6, 0, 5.667, -19.88, 6, 25.82]}, {"time": 6.3333, "y": 25.83, "curve": [6.778, 0, 7.222, 0, 6.778, 25.83, 7.222, 2.99]}, {"time": 7.6667, "y": 2.98, "curve": [8.111, 0, 8.556, 0, 8.111, 2.97, 8.556, 25.82]}, {"time": 9, "y": 25.83, "curve": [9.444, 0, 9.889, 0, 9.444, 25.84, 9.889, -19.86]}, {"time": 10.3333, "y": -19.87, "curve": [10.778, 0, 11.222, 0, 10.778, -19.88, 11.222, 25.82]}, {"time": 11.6667, "y": 25.83, "curve": [12.111, 0, 12.556, 0, 12.111, 25.84, 12.556, -19.87]}, {"time": 13, "y": -19.87}]}, "body": {"rotate": [{"value": -2.89, "curve": [0.444, -2.89, 0.889, 4.21]}, {"time": 1.3333, "value": 4.21, "curve": [1.778, 4.21, 2.222, -2.89]}, {"time": 2.6667, "value": -2.89, "curve": [3.111, -2.9, 3.556, 4.21]}, {"time": 4, "value": 4.21, "curve": [4.444, 4.21, 4.889, -2.89]}, {"time": 5.3333, "value": -2.89, "curve": [5.667, -2.9, 6, 4.21]}, {"time": 6.3333, "value": 4.21, "curve": [6.778, 4.21, 7.222, 0.66]}, {"time": 7.6667, "value": 0.66, "curve": [8.111, 0.66, 8.556, 4.21]}, {"time": 9, "value": 4.21, "curve": [9.444, 4.21, 9.889, -2.89]}, {"time": 10.3333, "value": -2.89, "curve": [10.778, -2.9, 11.222, 4.21]}, {"time": 11.6667, "value": 4.21, "curve": [12.111, 4.21, 12.556, -2.89]}, {"time": 13, "value": -2.89}], "translate": [{"y": -11.41, "curve": [0.057, 0, 0.112, 0, 0.057, -12.13, 0.112, -12.67]}, {"time": 0.1667, "y": -12.67, "curve": [0.611, 0, 1.056, 0, 0.611, -12.67, 1.056, 14.02]}, {"time": 1.5, "y": 14.03, "curve": [1.944, 0, 2.389, 0, 1.944, 14.03, 2.389, -12.66]}, {"time": 2.8333, "y": -12.67, "curve": [3.278, 0, 3.722, 0, 3.278, -12.68, 3.722, 14.02]}, {"time": 4.1667, "y": 14.03, "curve": [4.611, 0, 5.056, 0, 4.611, 14.03, 5.056, -12.66]}, {"time": 5.5, "y": -12.67, "curve": [5.833, 0, 6.167, 0, 5.833, -12.68, 6.167, 14.02]}, {"time": 6.5, "y": 14.03, "curve": [6.944, 0, 7.389, 0, 6.944, 14.03, 7.389, 0.68]}, {"time": 7.8333, "y": 0.68, "curve": [8.278, 0, 8.722, 0, 8.278, 0.68, 8.722, 14.02]}, {"time": 9.1667, "y": 14.03, "curve": [9.611, 0, 10.056, 0, 9.611, 14.03, 10.056, -12.66]}, {"time": 10.5, "y": -12.67, "curve": [10.944, 0, 11.389, 0, 10.944, -12.68, 11.389, 14.02]}, {"time": 11.8333, "y": 14.03, "curve": [12.223, 0, 12.613, 0, 12.223, 14.03, 12.613, -6.35]}, {"time": 13, "y": -11.41}], "scale": [{"y": 1.048, "curve": [0.057, 1, 0.112, 1, 0.057, 1.05, 0.112, 1.052]}, {"time": 0.1667, "y": 1.052, "curve": [0.611, 1, 1.056, 1, 0.611, 1.052, 1.056, 0.953]}, {"time": 1.5, "y": 0.953, "curve": [1.944, 1, 2.389, 1, 1.944, 0.953, 2.389, 1.052]}, {"time": 2.8333, "y": 1.052, "curve": [3.278, 1, 3.722, 1, 3.278, 1.052, 3.722, 0.953]}, {"time": 4.1667, "y": 0.953, "curve": [4.611, 1, 5.056, 1, 4.611, 0.953, 5.056, 1.052]}, {"time": 5.5, "y": 1.052, "curve": [5.833, 1, 6.167, 1, 5.833, 1.052, 6.167, 0.953]}, {"time": 6.5, "y": 0.953, "curve": [6.944, 1, 7.389, 1, 6.944, 0.953, 7.389, 1.003]}, {"time": 7.8333, "y": 1.003, "curve": [8.278, 1, 8.722, 1, 8.278, 1.003, 8.722, 0.953]}, {"time": 9.1667, "y": 0.953, "curve": [9.611, 1, 10.056, 1, 9.611, 0.953, 10.056, 1.052]}, {"time": 10.5, "y": 1.052, "curve": [10.944, 1, 11.389, 1, 10.944, 1.052, 11.389, 0.953]}, {"time": 11.8333, "y": 0.953, "curve": [12.223, 1, 12.613, 1, 12.223, 0.953, 12.613, 1.029]}, {"time": 13, "y": 1.048}]}, "body2": {"rotate": [{"value": 1.34, "curve": [0.057, 1.4, 0.112, 1.44]}, {"time": 0.1667, "value": 1.44, "curve": [0.611, 1.44, 1.056, -0.67]}, {"time": 1.5, "value": -0.67, "curve": [1.944, -0.67, 2.389, 1.44]}, {"time": 2.8333, "value": 1.44, "curve": [3.278, 1.44, 3.722, -0.67]}, {"time": 4.1667, "value": -0.67, "curve": [4.611, -0.67, 5.056, 1.44]}, {"time": 5.5, "value": 1.44, "curve": [5.833, 1.44, 6.167, -0.67]}, {"time": 6.5, "value": -0.67, "curve": [6.944, -0.67, 7.389, 0.39]}, {"time": 7.8333, "value": 0.39, "curve": [8.278, 0.39, 8.722, -0.67]}, {"time": 9.1667, "value": -0.67, "curve": [9.611, -0.67, 10.056, 1.44]}, {"time": 10.5, "value": 1.44, "curve": [10.944, 1.44, 11.389, -0.67]}, {"time": 11.8333, "value": -0.67, "curve": [12.223, -0.67, 12.613, 0.94]}, {"time": 13, "value": 1.34}], "translate": [{"x": -10.1, "curve": [0.114, -12.52, 0.224, -14.33, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -14.33, "curve": [0.778, -14.33, 1.222, 12.09, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 12.1, "curve": [2.111, 12.1, 2.556, -14.32, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -14.33, "curve": [3.444, -14.34, 3.889, 12.09, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 12.1, "curve": [4.778, 12.1, 5.222, -14.32, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -14.33, "curve": [6, -14.34, 6.333, 12.09, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 12.1, "curve": [7.111, 12.1, 7.556, -1.11, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -1.12, "curve": [8.444, -1.12, 8.889, 12.09, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 12.1, "curve": [9.778, 12.1, 10.222, -14.32, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -14.33, "curve": [11.111, -14.34, 11.556, 12.09, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 12.1, "curve": [12.335, 12.1, 12.67, -2.7, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -10.1}], "scale": [{"x": 1.005, "y": 1.005, "curve": [0.114, 1.002, 0.224, 1, 0.114, 1.002, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.029, 0.778, 1, 1.222, 1.029]}, {"time": 1.6667, "x": 1.029, "y": 1.029, "curve": [2.111, 1.029, 2.556, 1, 2.111, 1.029, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.029, 3.444, 1, 3.889, 1.029]}, {"time": 4.3333, "x": 1.029, "y": 1.029, "curve": [4.778, 1.029, 5.222, 1, 4.778, 1.029, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.029, 6, 1, 6.333, 1.029]}, {"time": 6.6667, "x": 1.029, "y": 1.029, "curve": [7.111, 1.029, 7.556, 1.014, 7.111, 1.029, 7.556, 1.014]}, {"time": 8, "x": 1.014, "y": 1.014, "curve": [8.444, 1.014, 8.889, 1.029, 8.444, 1.014, 8.889, 1.029]}, {"time": 9.3333, "x": 1.029, "y": 1.029, "curve": [9.778, 1.029, 10.222, 1, 9.778, 1.029, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.029, 11.111, 1, 11.556, 1.029]}, {"time": 12, "x": 1.029, "y": 1.029, "curve": [12.335, 1.029, 12.67, 1.013, 12.335, 1.029, 12.67, 1.013]}, {"time": 13, "x": 1.005, "y": 1.005}]}, "neck": {"rotate": [{"value": 0.58, "curve": [0.168, 1.08, 0.334, 1.49]}, {"time": 0.5, "value": 1.49, "curve": [0.944, 1.49, 1.389, -1.37]}, {"time": 1.8333, "value": -1.37, "curve": [2.278, -1.37, 2.722, 1.49]}, {"time": 3.1667, "value": 1.49, "curve": [3.611, 1.49, 4.056, -1.37]}, {"time": 4.5, "value": -1.37, "curve": [4.944, -1.37, 5.389, 1.48]}, {"time": 5.8333, "value": 1.49, "curve": [6.167, 1.49, 6.5, -11.29]}, {"time": 6.8333, "value": -11.29, "curve": [7.278, -11.29, 7.722, -6.93]}, {"time": 8.1667, "value": -6.93, "curve": [8.611, -6.93, 9.056, -11.29]}, {"time": 9.5, "value": -11.29, "curve": [9.944, -11.29, 10.389, 1.49]}, {"time": 10.8333, "value": 1.49, "curve": [11.278, 1.49, 11.722, -1.37]}, {"time": 12.1667, "value": -1.37, "curve": [12.445, -1.37, 12.724, -0.26]}, {"time": 13, "value": 0.58}]}, "head": {"rotate": [{"value": 0.06, "curve": [0.225, 0.77, 0.446, 1.49]}, {"time": 0.6667, "value": 1.49, "curve": [1.111, 1.49, 1.556, -1.37]}, {"time": 2, "value": -1.37, "curve": [2.444, -1.37, 2.889, 1.49]}, {"time": 3.3333, "value": 1.49, "curve": [3.778, 1.49, 4.222, -1.37]}, {"time": 4.6667, "value": -1.37, "curve": [5.111, -1.37, 5.556, 1.48]}, {"time": 6, "value": 1.49, "curve": [6.333, 1.49, 6.667, -5.81]}, {"time": 7, "value": -5.81, "curve": [7.444, -5.82, 7.889, -3.99]}, {"time": 8.3333, "value": -3.99, "curve": [8.778, -3.99, 9.222, -5.81]}, {"time": 9.6667, "value": -5.81, "curve": [10.111, -5.82, 10.556, 1.49]}, {"time": 11, "value": 1.49, "curve": [11.444, 1.49, 11.889, -1.37]}, {"time": 12.3333, "value": -1.37, "curve": [12.557, -1.37, 12.781, -0.66]}, {"time": 13, "value": 0.06}]}, "sh_L": {"translate": [{"x": -3.86, "curve": [0.114, -4.9, 0.224, -5.68, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.68, "curve": [0.778, -5.68, 1.222, 5.68, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 5.68, "curve": [2.111, 5.69, 2.556, -5.67, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.68, "curve": [3.444, -5.68, 3.889, 5.68, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 5.68, "curve": [4.778, 5.69, 5.222, -5.67, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.68, "curve": [6, -5.68, 6.333, 5.68, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 5.68, "curve": [7.111, 5.68, 7.556, 0, 7.111, 0, 7.556, 0]}, {"time": 8, "curve": [8.444, 0, 8.889, 5.68, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 5.68, "curve": [9.778, 5.69, 10.222, -5.67, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.68, "curve": [11.111, -5.68, 11.556, 5.68, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 5.68, "curve": [12.335, 5.68, 12.67, -0.68, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -3.86}]}, "sh_R": {"translate": [{"x": -3.86, "curve": [0.114, -4.9, 0.224, -5.68, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.68, "curve": [0.778, -5.68, 1.222, 5.68, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 5.68, "curve": [2.111, 5.69, 2.556, -5.67, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.68, "curve": [3.444, -5.68, 3.889, 5.68, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 5.68, "curve": [4.778, 5.69, 5.222, -5.67, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.68, "curve": [6, -5.68, 6.333, 5.68, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 5.68, "curve": [7.111, 5.68, 7.556, 0, 7.111, 0, 7.556, 0]}, {"time": 8, "curve": [8.444, 0, 8.889, 5.68, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 5.68, "curve": [9.778, 5.69, 10.222, -5.67, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.68, "curve": [11.111, -5.68, 11.556, 5.68, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 5.68, "curve": [12.335, 5.68, 12.67, -0.68, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -3.86}]}, "tun": {"rotate": [{"value": -2.89, "curve": [0.444, -2.89, 0.889, 4.21]}, {"time": 1.3333, "value": 4.21, "curve": [1.778, 4.21, 2.222, -2.89]}, {"time": 2.6667, "value": -2.89, "curve": [3.111, -2.9, 3.556, 4.21]}, {"time": 4, "value": 4.21, "curve": [4.444, 4.21, 4.889, -2.89]}, {"time": 5.3333, "value": -2.89, "curve": [5.667, -2.9, 6, 4.21]}, {"time": 6.3333, "value": 4.21, "curve": [6.778, 4.21, 7.222, 0.66]}, {"time": 7.6667, "value": 0.66, "curve": [8.111, 0.66, 8.556, 4.21]}, {"time": 9, "value": 4.21, "curve": [9.444, 4.21, 9.889, -2.89]}, {"time": 10.3333, "value": -2.89, "curve": [10.778, -2.9, 11.222, 4.21]}, {"time": 11.6667, "value": 4.21, "curve": [12.111, 4.21, 12.556, -2.89]}, {"time": 13, "value": -2.89}]}, "arm_L": {"rotate": [{"value": -0.41, "curve": [0.168, -1.09, 0.334, -1.64]}, {"time": 0.5, "value": -1.64, "curve": [0.944, -1.64, 1.389, 2.21]}, {"time": 1.8333, "value": 2.21, "curve": [2.278, 2.22, 2.722, -1.64]}, {"time": 3.1667, "value": -1.64, "curve": [3.611, -1.64, 4.056, 2.21]}, {"time": 4.5, "value": 2.21, "curve": [4.944, 2.22, 5.389, -1.64]}, {"time": 5.8333, "value": -1.64, "curve": [6.167, -1.64, 6.5, 2.21]}, {"time": 6.8333, "value": 2.21, "curve": [7.278, 2.21, 7.722, 0.29]}, {"time": 8.1667, "value": 0.29, "curve": [8.611, 0.29, 9.056, 2.21]}, {"time": 9.5, "value": 2.21, "curve": [9.944, 2.22, 10.389, -1.64]}, {"time": 10.8333, "value": -1.64, "curve": [11.278, -1.64, 11.722, 2.21]}, {"time": 12.1667, "value": 2.21, "curve": [12.445, 2.22, 12.724, 0.72]}, {"time": 13, "value": -0.41}]}, "arm_L2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.34]}, {"time": 7.6667, "value": 0.34, "curve": [8.111, 0.34, 8.556, 0]}, {"time": 9}]}, "arm_R": {"rotate": [{"value": 0.74, "curve": [0.168, 1.35, 0.334, 1.86]}, {"time": 0.5, "value": 1.86, "curve": [0.944, 1.86, 1.389, -1.67]}, {"time": 1.8333, "value": -1.67, "curve": [2.278, -1.67, 2.722, 1.86]}, {"time": 3.1667, "value": 1.86, "curve": [3.611, 1.86, 4.056, -1.67]}, {"time": 4.5, "value": -1.67, "curve": [4.944, -1.67, 5.389, 1.86]}, {"time": 5.8333, "value": 1.86, "curve": [6.167, 1.86, 6.5, -1.67]}, {"time": 6.8333, "value": -1.67, "curve": [7.278, -1.67, 7.722, 0.09]}, {"time": 8.1667, "value": 0.1, "curve": [8.611, 0.1, 9.056, -1.67]}, {"time": 9.5, "value": -1.67, "curve": [9.944, -1.67, 10.389, 1.86]}, {"time": 10.8333, "value": 1.86, "curve": [11.278, 1.86, 11.722, -1.67]}, {"time": 12.1667, "value": -1.67, "curve": [12.445, -1.67, 12.724, -0.3]}, {"time": 13, "value": 0.74}]}, "arm_R2": {"rotate": [{"value": 0.09, "curve": [0.225, 0.97, 0.446, 1.86]}, {"time": 0.6667, "value": 1.86, "curve": [1.111, 1.86, 1.556, -1.67]}, {"time": 2, "value": -1.67, "curve": [2.444, -1.67, 2.889, 1.86]}, {"time": 3.3333, "value": 1.86, "curve": [3.778, 1.86, 4.222, -1.67]}, {"time": 4.6667, "value": -1.67, "curve": [5.111, -1.67, 5.556, 1.86]}, {"time": 6, "value": 1.86, "curve": [6.333, 1.86, 6.667, -1.67]}, {"time": 7, "value": -1.67, "curve": [7.444, -1.67, 7.889, 0.09]}, {"time": 8.3333, "value": 0.1, "curve": [8.778, 0.1, 9.222, -1.67]}, {"time": 9.6667, "value": -1.67, "curve": [10.111, -1.67, 10.556, 1.86]}, {"time": 11, "value": 1.86, "curve": [11.444, 1.86, 11.889, -1.67]}, {"time": 12.3333, "value": -1.67, "curve": [12.557, -1.67, 12.781, -0.79]}, {"time": 13, "value": 0.09}]}, "arm_R3": {"rotate": [{"value": -0.55, "curve": [0.279, 0.49, 0.556, 1.86]}, {"time": 0.8333, "value": 1.86, "curve": [1.278, 1.86, 1.722, -1.67]}, {"time": 2.1667, "value": -1.67, "curve": [2.611, -1.67, 3.056, 1.86]}, {"time": 3.5, "value": 1.86, "curve": [3.944, 1.86, 4.389, -1.67]}, {"time": 4.8333, "value": -1.67, "curve": [5.278, -1.67, 5.722, 1.86]}, {"time": 6.1667, "value": 1.86, "curve": [6.5, 1.86, 6.833, -1.67]}, {"time": 7.1667, "value": -1.67, "curve": [7.611, -1.67, 8.056, 0.09]}, {"time": 8.5, "value": 0.1, "curve": [8.944, 0.1, 9.389, -1.67]}, {"time": 9.8333, "value": -1.67, "curve": [10.278, -1.67, 10.722, 1.86]}, {"time": 11.1667, "value": 1.86, "curve": [11.611, 1.86, 12.056, -1.67]}, {"time": 12.5, "value": -1.67, "curve": [12.667, -1.67, 12.835, -1.17]}, {"time": 13, "value": -0.55}]}, "RU_L": {"translate": [{"x": -18.35, "curve": [0.168, -35.7, 0.334, -49.94, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -49.94, "curve": [0.944, -49.94, 1.389, 49.35, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 49.38, "curve": [2.278, 49.4, 2.722, -49.91, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -49.94, "curve": [3.611, -49.96, 4.056, 49.35, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 49.38, "curve": [4.944, 49.4, 5.389, -49.91, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -49.94, "curve": [6.167, -49.96, 6.5, 49.36, 6.167, 0, 6.389, 0]}, {"time": 6.8333, "x": 49.38, "curve": [7.278, 49.4, 7.722, -49.91, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -49.94, "curve": [8.611, -49.96, 9.056, 49.35, 8.5, 0, 9.056, 0]}, {"time": 9.5, "x": 49.38, "curve": [9.944, 49.4, 10.389, -49.91, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -49.94, "curve": [11.278, -49.96, 11.722, 49.35, 11.167, 0, 11.722, 0]}, {"time": 12.1667, "x": 49.38, "curve": [12.445, 49.39, 12.724, 10.78, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -18.35}], "scale": [{"x": 1.059, "y": 0.964, "curve": [0.167, 1.023, 0.333, 0.951, 0.167, 0.998, 0.333, 1.065]}, {"time": 0.5, "x": 0.951, "y": 1.065, "curve": [0.722, 0.951, 0.944, 1.079, 0.722, 1.065, 0.944, 0.944]}, {"time": 1.1667, "x": 1.079, "y": 0.944, "curve": [1.389, 1.079, 1.611, 0.951, 1.389, 0.944, 1.611, 1.065]}, {"time": 1.8333, "x": 0.951, "y": 1.065, "curve": [2.056, 0.951, 2.278, 1.079, 2.056, 1.065, 2.278, 0.944]}, {"time": 2.5, "x": 1.079, "y": 0.944, "curve": [2.722, 1.079, 2.944, 0.951, 2.722, 0.944, 2.944, 1.065]}, {"time": 3.1667, "x": 0.951, "y": 1.065, "curve": [3.389, 0.951, 3.611, 1.079, 3.389, 1.065, 3.611, 0.944]}, {"time": 3.8333, "x": 1.079, "y": 0.944, "curve": [4.056, 1.079, 4.278, 0.951, 4.056, 0.944, 4.278, 1.065]}, {"time": 4.5, "x": 0.951, "y": 1.065, "curve": [4.722, 0.951, 4.944, 1.079, 4.722, 1.065, 4.944, 0.944]}, {"time": 5.1667, "x": 1.079, "y": 0.944, "curve": [5.389, 1.079, 5.611, 0.951, 5.389, 0.944, 5.611, 1.065]}, {"time": 5.8333, "x": 0.951, "y": 1.065, "curve": [6, 0.951, 6.167, 1.079, 6, 1.065, 6.167, 0.944]}, {"time": 6.3333, "x": 1.079, "y": 0.944, "curve": [6.5, 1.079, 6.667, 0.951, 6.5, 0.944, 6.667, 1.065]}, {"time": 6.8333, "x": 0.951, "y": 1.065, "curve": [7.056, 0.951, 7.278, 1.079, 7.056, 1.065, 7.278, 0.944]}, {"time": 7.5, "x": 1.079, "y": 0.944, "curve": [7.722, 1.079, 7.944, 0.951, 7.722, 0.944, 7.944, 1.065]}, {"time": 8.1667, "x": 0.951, "y": 1.065, "curve": [8.389, 0.951, 8.611, 1.079, 8.389, 1.065, 8.611, 0.944]}, {"time": 8.8333, "x": 1.079, "y": 0.944, "curve": [9.056, 1.079, 9.278, 0.951, 9.056, 0.944, 9.278, 1.065]}, {"time": 9.5, "x": 0.951, "y": 1.065, "curve": [9.722, 0.951, 9.944, 1.079, 9.722, 1.065, 9.944, 0.944]}, {"time": 10.1667, "x": 1.079, "y": 0.944, "curve": [10.389, 1.079, 10.611, 0.951, 10.389, 0.944, 10.611, 1.065]}, {"time": 10.8333, "x": 0.951, "y": 1.065, "curve": [11.056, 0.951, 11.278, 1.079, 11.056, 1.065, 11.278, 0.944]}, {"time": 11.5, "x": 1.079, "y": 0.944, "curve": [11.722, 1.079, 11.944, 0.951, 11.722, 0.944, 11.944, 1.065]}, {"time": 12.1667, "x": 0.951, "y": 1.065, "curve": [12.389, 0.951, 12.611, 1.079, 12.389, 1.065, 12.611, 0.944]}, {"time": 12.8333, "x": 1.079, "y": 0.944, "curve": [12.889, 1.079, 12.944, 1.071, 12.889, 0.944, 12.944, 0.952]}, {"time": 13, "x": 1.059, "y": 0.964}]}, "RU_L2": {"translate": [{"x": -1.47, "curve": [0.225, -16.54, 0.446, -31.81, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -31.81, "curve": [1.111, -31.81, 1.556, 28.84, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 28.86, "curve": [2.444, 28.87, 2.889, -31.8, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -31.81, "curve": [3.778, -31.83, 4.222, 28.84, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 28.86, "curve": [5.111, 28.87, 5.556, -31.79, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -31.81, "curve": [6.333, -31.83, 6.667, 28.85, 6.333, 0, 6.556, 0]}, {"time": 7, "x": 28.86, "curve": [7.444, 28.87, 7.889, -31.8, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -31.81, "curve": [8.778, -31.83, 9.222, 28.84, 8.667, 0, 9.222, 0]}, {"time": 9.6667, "x": 28.86, "curve": [10.111, 28.87, 10.556, -31.8, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -31.81, "curve": [11.444, -31.83, 11.889, 28.84, 11.333, 0, 11.889, 0]}, {"time": 12.3333, "x": 28.86, "curve": [12.557, 28.87, 12.781, 13.8, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -1.47}], "scale": [{"x": 1.079, "y": 0.944, "curve": [0.222, 1.079, 0.444, 0.951, 0.222, 0.944, 0.444, 1.065]}, {"time": 0.6667, "x": 0.951, "y": 1.065, "curve": [0.889, 0.951, 1.111, 1.079, 0.889, 1.065, 1.111, 0.944]}, {"time": 1.3333, "x": 1.079, "y": 0.944, "curve": [1.556, 1.079, 1.778, 0.951, 1.556, 0.944, 1.778, 1.065]}, {"time": 2, "x": 0.951, "y": 1.065, "curve": [2.222, 0.951, 2.444, 1.079, 2.222, 1.065, 2.444, 0.944]}, {"time": 2.6667, "x": 1.079, "y": 0.944, "curve": [2.889, 1.079, 3.111, 0.951, 2.889, 0.944, 3.111, 1.065]}, {"time": 3.3333, "x": 0.951, "y": 1.065, "curve": [3.556, 0.951, 3.778, 1.079, 3.556, 1.065, 3.778, 0.944]}, {"time": 4, "x": 1.079, "y": 0.944, "curve": [4.222, 1.079, 4.444, 0.951, 4.222, 0.944, 4.444, 1.065]}, {"time": 4.6667, "x": 0.951, "y": 1.065, "curve": [4.889, 0.951, 5.111, 1.079, 4.889, 1.065, 5.111, 0.944]}, {"time": 5.3333, "x": 1.079, "y": 0.944, "curve": [5.556, 1.079, 5.778, 0.951, 5.556, 0.944, 5.778, 1.065]}, {"time": 6, "x": 0.951, "y": 1.065, "curve": [6.167, 0.951, 6.333, 1.079, 6.167, 1.065, 6.333, 0.944]}, {"time": 6.5, "x": 1.079, "y": 0.944, "curve": [6.667, 1.079, 6.833, 0.951, 6.667, 0.944, 6.833, 1.065]}, {"time": 7, "x": 0.951, "y": 1.065, "curve": [7.222, 0.951, 7.444, 1.079, 7.222, 1.065, 7.444, 0.944]}, {"time": 7.6667, "x": 1.079, "y": 0.944, "curve": [7.889, 1.079, 8.111, 0.951, 7.889, 0.944, 8.111, 1.065]}, {"time": 8.3333, "x": 0.951, "y": 1.065, "curve": [8.556, 0.951, 8.778, 1.079, 8.556, 1.065, 8.778, 0.944]}, {"time": 9, "x": 1.079, "y": 0.944, "curve": [9.222, 1.079, 9.444, 0.951, 9.222, 0.944, 9.444, 1.065]}, {"time": 9.6667, "x": 0.951, "y": 1.065, "curve": [9.889, 0.951, 10.111, 1.079, 9.889, 1.065, 10.111, 0.944]}, {"time": 10.3333, "x": 1.079, "y": 0.944, "curve": [10.556, 1.079, 10.778, 0.951, 10.556, 0.944, 10.778, 1.065]}, {"time": 11, "x": 0.951, "y": 1.065, "curve": [11.222, 0.951, 11.444, 1.079, 11.222, 1.065, 11.444, 0.944]}, {"time": 11.6667, "x": 1.079, "y": 0.944, "curve": [11.889, 1.079, 12.111, 0.951, 11.889, 0.944, 12.111, 1.065]}, {"time": 12.3333, "x": 0.951, "y": 1.065, "curve": [12.556, 0.951, 12.778, 1.079, 12.556, 1.065, 12.778, 0.944]}, {"time": 13, "x": 1.079, "y": 0.944}]}, "RU_L3": {"translate": [{"x": 12.3, "curve": [0.279, -4.14, 0.556, -26.01, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -26.01, "curve": [1.278, -26.01, 1.722, 30.13, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 30.15, "curve": [2.611, 30.16, 3.056, -25.99, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -26.01, "curve": [3.944, -26.02, 4.389, 30.13, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 30.15, "curve": [5.278, 30.16, 5.722, -25.99, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -26.01, "curve": [6.5, -26.02, 6.833, 30.14, 6.5, 0, 6.722, 0]}, {"time": 7.1667, "x": 30.15, "curve": [7.611, 30.16, 8.056, -25.99, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -26.01, "curve": [8.944, -26.02, 9.389, 30.13, 8.833, 0, 9.389, 0]}, {"time": 9.8333, "x": 30.15, "curve": [10.278, 30.16, 10.722, -25.99, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -26.01, "curve": [11.611, -26.02, 12.056, 30.13, 11.5, 0, 12.056, 0]}, {"time": 12.5, "x": 30.15, "curve": [12.667, 30.15, 12.835, 22.23, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 12.3}], "scale": [{"x": 1.059, "y": 0.964, "curve": [0.056, 1.071, 0.111, 1.079, 0.056, 0.952, 0.111, 0.944]}, {"time": 0.1667, "x": 1.079, "y": 0.944, "curve": [0.389, 1.079, 0.611, 0.951, 0.389, 0.944, 0.611, 1.065]}, {"time": 0.8333, "x": 0.951, "y": 1.065, "curve": [1.056, 0.951, 1.278, 1.079, 1.056, 1.065, 1.278, 0.944]}, {"time": 1.5, "x": 1.079, "y": 0.944, "curve": [1.722, 1.079, 1.944, 0.951, 1.722, 0.944, 1.944, 1.065]}, {"time": 2.1667, "x": 0.951, "y": 1.065, "curve": [2.389, 0.951, 2.611, 1.079, 2.389, 1.065, 2.611, 0.944]}, {"time": 2.8333, "x": 1.079, "y": 0.944, "curve": [3.056, 1.079, 3.278, 0.951, 3.056, 0.944, 3.278, 1.065]}, {"time": 3.5, "x": 0.951, "y": 1.065, "curve": [3.722, 0.951, 3.944, 1.079, 3.722, 1.065, 3.944, 0.944]}, {"time": 4.1667, "x": 1.079, "y": 0.944, "curve": [4.389, 1.079, 4.611, 0.951, 4.389, 0.944, 4.611, 1.065]}, {"time": 4.8333, "x": 0.951, "y": 1.065, "curve": [5.056, 0.951, 5.278, 1.079, 5.056, 1.065, 5.278, 0.944]}, {"time": 5.5, "x": 1.079, "y": 0.944, "curve": [5.722, 1.079, 5.944, 0.951, 5.722, 0.944, 5.944, 1.065]}, {"time": 6.1667, "x": 0.951, "y": 1.065, "curve": [6.333, 0.951, 6.5, 1.079, 6.333, 1.065, 6.5, 0.944]}, {"time": 6.6667, "x": 1.079, "y": 0.944, "curve": [6.833, 1.079, 7, 0.951, 6.833, 0.944, 7, 1.065]}, {"time": 7.1667, "x": 0.951, "y": 1.065, "curve": [7.389, 0.951, 7.611, 1.079, 7.389, 1.065, 7.611, 0.944]}, {"time": 7.8333, "x": 1.079, "y": 0.944, "curve": [8.056, 1.079, 8.278, 0.951, 8.056, 0.944, 8.278, 1.065]}, {"time": 8.5, "x": 0.951, "y": 1.065, "curve": [8.722, 0.951, 8.944, 1.079, 8.722, 1.065, 8.944, 0.944]}, {"time": 9.1667, "x": 1.079, "y": 0.944, "curve": [9.389, 1.079, 9.611, 0.951, 9.389, 0.944, 9.611, 1.065]}, {"time": 9.8333, "x": 0.951, "y": 1.065, "curve": [10.056, 0.951, 10.278, 1.079, 10.056, 1.065, 10.278, 0.944]}, {"time": 10.5, "x": 1.079, "y": 0.944, "curve": [10.722, 1.079, 10.944, 0.951, 10.722, 0.944, 10.944, 1.065]}, {"time": 11.1667, "x": 0.951, "y": 1.065, "curve": [11.389, 0.951, 11.611, 1.079, 11.389, 1.065, 11.611, 0.944]}, {"time": 11.8333, "x": 1.079, "y": 0.944, "curve": [12.056, 1.079, 12.278, 0.951, 12.056, 0.944, 12.278, 1.065]}, {"time": 12.5, "x": 0.951, "y": 1.065, "curve": [12.667, 0.951, 12.833, 1.022, 12.667, 1.065, 12.833, 0.998]}, {"time": 13, "x": 1.059, "y": 0.964}]}, "RU_R": {"translate": [{"x": -18.35, "curve": [0.168, -35.7, 0.334, -49.94, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -49.94, "curve": [0.944, -49.94, 1.389, 49.35, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 49.38, "curve": [2.278, 49.4, 2.722, -49.91, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -49.94, "curve": [3.611, -49.96, 4.056, 49.35, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 49.38, "curve": [4.944, 49.4, 5.389, -49.91, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -49.94, "curve": [6.167, -49.96, 6.5, 49.36, 6.167, 0, 6.389, 0]}, {"time": 6.8333, "x": 49.38, "curve": [7.278, 49.4, 7.722, -49.91, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -49.94, "curve": [8.611, -49.96, 9.056, 49.35, 8.5, 0, 9.056, 0]}, {"time": 9.5, "x": 49.38, "curve": [9.944, 49.4, 10.389, -49.91, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -49.94, "curve": [11.278, -49.96, 11.722, 49.35, 11.167, 0, 11.722, 0]}, {"time": 12.1667, "x": 49.38, "curve": [12.445, 49.39, 12.724, 10.78, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -18.35}], "scale": [{"x": 1.059, "y": 0.964, "curve": [0.167, 1.023, 0.333, 0.951, 0.167, 0.998, 0.333, 1.065]}, {"time": 0.5, "x": 0.951, "y": 1.065, "curve": [0.722, 0.951, 0.944, 1.079, 0.722, 1.065, 0.944, 0.944]}, {"time": 1.1667, "x": 1.079, "y": 0.944, "curve": [1.389, 1.079, 1.611, 0.951, 1.389, 0.944, 1.611, 1.065]}, {"time": 1.8333, "x": 0.951, "y": 1.065, "curve": [2.056, 0.951, 2.278, 1.079, 2.056, 1.065, 2.278, 0.944]}, {"time": 2.5, "x": 1.079, "y": 0.944, "curve": [2.722, 1.079, 2.944, 0.951, 2.722, 0.944, 2.944, 1.065]}, {"time": 3.1667, "x": 0.951, "y": 1.065, "curve": [3.389, 0.951, 3.611, 1.079, 3.389, 1.065, 3.611, 0.944]}, {"time": 3.8333, "x": 1.079, "y": 0.944, "curve": [4.056, 1.079, 4.278, 0.951, 4.056, 0.944, 4.278, 1.065]}, {"time": 4.5, "x": 0.951, "y": 1.065, "curve": [4.722, 0.951, 4.944, 1.079, 4.722, 1.065, 4.944, 0.944]}, {"time": 5.1667, "x": 1.079, "y": 0.944, "curve": [5.389, 1.079, 5.611, 0.951, 5.389, 0.944, 5.611, 1.065]}, {"time": 5.8333, "x": 0.951, "y": 1.065, "curve": [6, 0.951, 6.167, 1.079, 6, 1.065, 6.167, 0.944]}, {"time": 6.3333, "x": 1.079, "y": 0.944, "curve": [6.5, 1.079, 6.667, 0.951, 6.5, 0.944, 6.667, 1.065]}, {"time": 6.8333, "x": 0.951, "y": 1.065, "curve": [7.056, 0.951, 7.278, 1.079, 7.056, 1.065, 7.278, 0.944]}, {"time": 7.5, "x": 1.079, "y": 0.944, "curve": [7.722, 1.079, 7.944, 0.951, 7.722, 0.944, 7.944, 1.065]}, {"time": 8.1667, "x": 0.951, "y": 1.065, "curve": [8.389, 0.951, 8.611, 1.079, 8.389, 1.065, 8.611, 0.944]}, {"time": 8.8333, "x": 1.079, "y": 0.944, "curve": [9.056, 1.079, 9.278, 0.951, 9.056, 0.944, 9.278, 1.065]}, {"time": 9.5, "x": 0.951, "y": 1.065, "curve": [9.722, 0.951, 9.944, 1.079, 9.722, 1.065, 9.944, 0.944]}, {"time": 10.1667, "x": 1.079, "y": 0.944, "curve": [10.389, 1.079, 10.611, 0.951, 10.389, 0.944, 10.611, 1.065]}, {"time": 10.8333, "x": 0.951, "y": 1.065, "curve": [11.056, 0.951, 11.278, 1.079, 11.056, 1.065, 11.278, 0.944]}, {"time": 11.5, "x": 1.079, "y": 0.944, "curve": [11.722, 1.079, 11.944, 0.951, 11.722, 0.944, 11.944, 1.065]}, {"time": 12.1667, "x": 0.951, "y": 1.065, "curve": [12.389, 0.951, 12.611, 1.079, 12.389, 1.065, 12.611, 0.944]}, {"time": 12.8333, "x": 1.079, "y": 0.944, "curve": [12.889, 1.079, 12.944, 1.071, 12.889, 0.944, 12.944, 0.952]}, {"time": 13, "x": 1.059, "y": 0.964}]}, "RU_R2": {"translate": [{"x": -1.47, "curve": [0.225, -16.54, 0.446, -31.81, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -31.81, "curve": [1.111, -31.81, 1.556, 28.84, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 28.86, "curve": [2.444, 28.87, 2.889, -31.8, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -31.81, "curve": [3.778, -31.83, 4.222, 28.84, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 28.86, "curve": [5.111, 28.87, 5.556, -31.79, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -31.81, "curve": [6.333, -31.83, 6.667, 28.85, 6.333, 0, 6.556, 0]}, {"time": 7, "x": 28.86, "curve": [7.444, 28.87, 7.889, -31.8, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -31.81, "curve": [8.778, -31.83, 9.222, 28.84, 8.667, 0, 9.222, 0]}, {"time": 9.6667, "x": 28.86, "curve": [10.111, 28.87, 10.556, -31.8, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -31.81, "curve": [11.444, -31.83, 11.889, 28.84, 11.333, 0, 11.889, 0]}, {"time": 12.3333, "x": 28.86, "curve": [12.557, 28.87, 12.781, 13.8, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -1.47}], "scale": [{"x": 1.079, "y": 0.944, "curve": [0.222, 1.079, 0.444, 0.951, 0.222, 0.944, 0.444, 1.065]}, {"time": 0.6667, "x": 0.951, "y": 1.065, "curve": [0.889, 0.951, 1.111, 1.079, 0.889, 1.065, 1.111, 0.944]}, {"time": 1.3333, "x": 1.079, "y": 0.944, "curve": [1.556, 1.079, 1.778, 0.951, 1.556, 0.944, 1.778, 1.065]}, {"time": 2, "x": 0.951, "y": 1.065, "curve": [2.222, 0.951, 2.444, 1.079, 2.222, 1.065, 2.444, 0.944]}, {"time": 2.6667, "x": 1.079, "y": 0.944, "curve": [2.889, 1.079, 3.111, 0.951, 2.889, 0.944, 3.111, 1.065]}, {"time": 3.3333, "x": 0.951, "y": 1.065, "curve": [3.556, 0.951, 3.778, 1.079, 3.556, 1.065, 3.778, 0.944]}, {"time": 4, "x": 1.079, "y": 0.944, "curve": [4.222, 1.079, 4.444, 0.951, 4.222, 0.944, 4.444, 1.065]}, {"time": 4.6667, "x": 0.951, "y": 1.065, "curve": [4.889, 0.951, 5.111, 1.079, 4.889, 1.065, 5.111, 0.944]}, {"time": 5.3333, "x": 1.079, "y": 0.944, "curve": [5.556, 1.079, 5.778, 0.951, 5.556, 0.944, 5.778, 1.065]}, {"time": 6, "x": 0.951, "y": 1.065, "curve": [6.167, 0.951, 6.333, 1.079, 6.167, 1.065, 6.333, 0.944]}, {"time": 6.5, "x": 1.079, "y": 0.944, "curve": [6.667, 1.079, 6.833, 0.951, 6.667, 0.944, 6.833, 1.065]}, {"time": 7, "x": 0.951, "y": 1.065, "curve": [7.222, 0.951, 7.444, 1.079, 7.222, 1.065, 7.444, 0.944]}, {"time": 7.6667, "x": 1.079, "y": 0.944, "curve": [7.889, 1.079, 8.111, 0.951, 7.889, 0.944, 8.111, 1.065]}, {"time": 8.3333, "x": 0.951, "y": 1.065, "curve": [8.556, 0.951, 8.778, 1.079, 8.556, 1.065, 8.778, 0.944]}, {"time": 9, "x": 1.079, "y": 0.944, "curve": [9.222, 1.079, 9.444, 0.951, 9.222, 0.944, 9.444, 1.065]}, {"time": 9.6667, "x": 0.951, "y": 1.065, "curve": [9.889, 0.951, 10.111, 1.079, 9.889, 1.065, 10.111, 0.944]}, {"time": 10.3333, "x": 1.079, "y": 0.944, "curve": [10.556, 1.079, 10.778, 0.951, 10.556, 0.944, 10.778, 1.065]}, {"time": 11, "x": 0.951, "y": 1.065, "curve": [11.222, 0.951, 11.444, 1.079, 11.222, 1.065, 11.444, 0.944]}, {"time": 11.6667, "x": 1.079, "y": 0.944, "curve": [11.889, 1.079, 12.111, 0.951, 11.889, 0.944, 12.111, 1.065]}, {"time": 12.3333, "x": 0.951, "y": 1.065, "curve": [12.556, 0.951, 12.778, 1.079, 12.556, 1.065, 12.778, 0.944]}, {"time": 13, "x": 1.079, "y": 0.944}]}, "RU_R3": {"translate": [{"x": 12.3, "curve": [0.279, -4.14, 0.556, -26.01, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -26.01, "curve": [1.278, -26.01, 1.722, 30.13, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 30.15, "curve": [2.611, 30.16, 3.056, -25.99, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -26.01, "curve": [3.944, -26.02, 4.389, 30.13, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 30.15, "curve": [5.278, 30.16, 5.722, -25.99, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -26.01, "curve": [6.5, -26.02, 6.833, 30.14, 6.5, 0, 6.722, 0]}, {"time": 7.1667, "x": 30.15, "curve": [7.611, 30.16, 8.056, -25.99, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -26.01, "curve": [8.944, -26.02, 9.389, 30.13, 8.833, 0, 9.389, 0]}, {"time": 9.8333, "x": 30.15, "curve": [10.278, 30.16, 10.722, -25.99, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -26.01, "curve": [11.611, -26.02, 12.056, 30.13, 11.5, 0, 12.056, 0]}, {"time": 12.5, "x": 30.15, "curve": [12.667, 30.15, 12.835, 22.23, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 12.3}], "scale": [{"x": 1.059, "y": 0.964, "curve": [0.056, 1.071, 0.111, 1.079, 0.056, 0.952, 0.111, 0.944]}, {"time": 0.1667, "x": 1.079, "y": 0.944, "curve": [0.389, 1.079, 0.611, 0.951, 0.389, 0.944, 0.611, 1.065]}, {"time": 0.8333, "x": 0.951, "y": 1.065, "curve": [1.056, 0.951, 1.278, 1.079, 1.056, 1.065, 1.278, 0.944]}, {"time": 1.5, "x": 1.079, "y": 0.944, "curve": [1.722, 1.079, 1.944, 0.951, 1.722, 0.944, 1.944, 1.065]}, {"time": 2.1667, "x": 0.951, "y": 1.065, "curve": [2.389, 0.951, 2.611, 1.079, 2.389, 1.065, 2.611, 0.944]}, {"time": 2.8333, "x": 1.079, "y": 0.944, "curve": [3.056, 1.079, 3.278, 0.951, 3.056, 0.944, 3.278, 1.065]}, {"time": 3.5, "x": 0.951, "y": 1.065, "curve": [3.722, 0.951, 3.944, 1.079, 3.722, 1.065, 3.944, 0.944]}, {"time": 4.1667, "x": 1.079, "y": 0.944, "curve": [4.389, 1.079, 4.611, 0.951, 4.389, 0.944, 4.611, 1.065]}, {"time": 4.8333, "x": 0.951, "y": 1.065, "curve": [5.056, 0.951, 5.278, 1.079, 5.056, 1.065, 5.278, 0.944]}, {"time": 5.5, "x": 1.079, "y": 0.944, "curve": [5.722, 1.079, 5.944, 0.951, 5.722, 0.944, 5.944, 1.065]}, {"time": 6.1667, "x": 0.951, "y": 1.065, "curve": [6.333, 0.951, 6.5, 1.079, 6.333, 1.065, 6.5, 0.944]}, {"time": 6.6667, "x": 1.079, "y": 0.944, "curve": [6.833, 1.079, 7, 0.951, 6.833, 0.944, 7, 1.065]}, {"time": 7.1667, "x": 0.951, "y": 1.065, "curve": [7.389, 0.951, 7.611, 1.079, 7.389, 1.065, 7.611, 0.944]}, {"time": 7.8333, "x": 1.079, "y": 0.944, "curve": [8.056, 1.079, 8.278, 0.951, 8.056, 0.944, 8.278, 1.065]}, {"time": 8.5, "x": 0.951, "y": 1.065, "curve": [8.722, 0.951, 8.944, 1.079, 8.722, 1.065, 8.944, 0.944]}, {"time": 9.1667, "x": 1.079, "y": 0.944, "curve": [9.389, 1.079, 9.611, 0.951, 9.389, 0.944, 9.611, 1.065]}, {"time": 9.8333, "x": 0.951, "y": 1.065, "curve": [10.056, 0.951, 10.278, 1.079, 10.056, 1.065, 10.278, 0.944]}, {"time": 10.5, "x": 1.079, "y": 0.944, "curve": [10.722, 1.079, 10.944, 0.951, 10.722, 0.944, 10.944, 1.065]}, {"time": 11.1667, "x": 0.951, "y": 1.065, "curve": [11.389, 0.951, 11.611, 1.079, 11.389, 1.065, 11.611, 0.944]}, {"time": 11.8333, "x": 1.079, "y": 0.944, "curve": [12.056, 1.079, 12.278, 0.951, 12.056, 0.944, 12.278, 1.065]}, {"time": 12.5, "x": 0.951, "y": 1.065, "curve": [12.667, 0.951, 12.833, 1.022, 12.667, 1.065, 12.833, 0.998]}, {"time": 13, "x": 1.059, "y": 0.964}]}, "earring_L": {"rotate": [{"value": 9.3, "curve": [0.056, 11.76, 0.111, 13.49]}, {"time": 0.1667, "value": 13.5, "curve": [0.389, 13.5, 0.611, -12.74]}, {"time": 0.8333, "value": -12.74, "curve": [1.056, -12.74, 1.278, 13.49]}, {"time": 1.5, "value": 13.5, "curve": [1.722, 13.5, 1.944, -12.73]}, {"time": 2.1667, "value": -12.74, "curve": [2.389, -12.74, 2.611, 13.49]}, {"time": 2.8333, "value": 13.5, "curve": [3.056, 13.5, 3.278, -12.73]}, {"time": 3.5, "value": -12.74, "curve": [3.722, -12.74, 3.944, 13.49]}, {"time": 4.1667, "value": 13.5, "curve": [4.389, 13.5, 4.611, -12.73]}, {"time": 4.8333, "value": -12.74, "curve": [5.056, -12.74, 5.278, 13.49]}, {"time": 5.5, "value": 13.5, "curve": [5.722, 13.5, 5.944, -12.73]}, {"time": 6.1667, "value": -12.74, "curve": [6.333, -12.74, 6.5, 13.49]}, {"time": 6.6667, "value": 13.5, "curve": [6.833, 13.5, 7, -12.73]}, {"time": 7.1667, "value": -12.74, "curve": [7.389, -12.74, 7.611, 13.49]}, {"time": 7.8333, "value": 13.5, "curve": [8.056, 13.5, 8.278, -12.73]}, {"time": 8.5, "value": -12.74, "curve": [8.722, -12.74, 8.944, 13.49]}, {"time": 9.1667, "value": 13.5, "curve": [9.389, 13.5, 9.611, -12.73]}, {"time": 9.8333, "value": -12.74, "curve": [10.056, -12.74, 10.278, 13.49]}, {"time": 10.5, "value": 13.5, "curve": [10.722, 13.5, 10.944, -12.73]}, {"time": 11.1667, "value": -12.74, "curve": [11.389, -12.74, 11.611, 13.49]}, {"time": 11.8333, "value": 13.5, "curve": [12.056, 13.5, 12.278, -12.73]}, {"time": 12.5, "value": -12.74, "curve": [12.667, -12.74, 12.833, 1.92]}, {"time": 13, "value": 9.3}]}, "earring_R": {"rotate": [{"value": 9.3, "curve": [0.056, 11.76, 0.111, 13.49]}, {"time": 0.1667, "value": 13.5, "curve": [0.389, 13.5, 0.611, -12.74]}, {"time": 0.8333, "value": -12.74, "curve": [1.056, -12.74, 1.278, 13.49]}, {"time": 1.5, "value": 13.5, "curve": [1.722, 13.5, 1.944, -12.73]}, {"time": 2.1667, "value": -12.74, "curve": [2.389, -12.74, 2.611, 13.49]}, {"time": 2.8333, "value": 13.5, "curve": [3.056, 13.5, 3.278, -12.73]}, {"time": 3.5, "value": -12.74, "curve": [3.722, -12.74, 3.944, 13.49]}, {"time": 4.1667, "value": 13.5, "curve": [4.389, 13.5, 4.611, -12.73]}, {"time": 4.8333, "value": -12.74, "curve": [5.056, -12.74, 5.278, 13.49]}, {"time": 5.5, "value": 13.5, "curve": [5.722, 13.5, 5.944, -12.73]}, {"time": 6.1667, "value": -12.74, "curve": [6.333, -12.74, 6.5, 13.49]}, {"time": 6.6667, "value": 13.5, "curve": [6.833, 13.5, 7, -12.73]}, {"time": 7.1667, "value": -12.74, "curve": [7.389, -12.74, 7.611, 13.49]}, {"time": 7.8333, "value": 13.5, "curve": [8.056, 13.5, 8.278, -12.73]}, {"time": 8.5, "value": -12.74, "curve": [8.722, -12.74, 8.944, 13.49]}, {"time": 9.1667, "value": 13.5, "curve": [9.389, 13.5, 9.611, -12.73]}, {"time": 9.8333, "value": -12.74, "curve": [10.056, -12.74, 10.278, 13.49]}, {"time": 10.5, "value": 13.5, "curve": [10.722, 13.5, 10.944, -12.73]}, {"time": 11.1667, "value": -12.74, "curve": [11.389, -12.74, 11.611, 13.49]}, {"time": 11.8333, "value": 13.5, "curve": [12.056, 13.5, 12.278, -12.73]}, {"time": 12.5, "value": -12.74, "curve": [12.667, -12.74, 12.833, 1.92]}, {"time": 13, "value": 9.3}]}, "eyebrow_L": {"translate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1.8, 6.444, 0, 6.556, 0]}, {"time": 6.6667, "x": 1.8, "curve": "stepped"}, {"time": 9.8333, "x": 1.8, "curve": [9.944, 1.8, 10.056, 0, 9.944, 0, 10.056, 0]}, {"time": 10.1667}]}, "eyebrow_L2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -6.28]}, {"time": 6.6667, "value": -6.28, "curve": "stepped"}, {"time": 9.8333, "value": -6.28, "curve": [9.944, -6.28, 10.056, 0]}, {"time": 10.1667}], "scale": [{"time": 6.3333, "curve": [6.444, 1, 6.556, 0.961, 6.444, 1, 6.556, 1]}, {"time": 6.6667, "x": 0.961, "curve": "stepped"}, {"time": 9.8333, "x": 0.961, "curve": [9.944, 0.961, 10.056, 1, 9.944, 1, 10.056, 1]}, {"time": 10.1667}]}, "eyebrow_L3": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -5.84]}, {"time": 6.6667, "value": -5.84, "curve": "stepped"}, {"time": 9.8333, "value": -5.84, "curve": [9.944, -5.84, 10.056, 0]}, {"time": 10.1667}]}, "eyebrow_R": {"translate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1.8, 6.444, 0, 6.556, 0]}, {"time": 6.6667, "x": 1.8, "curve": "stepped"}, {"time": 9.8333, "x": 1.8, "curve": [9.944, 1.8, 10.056, 0, 9.944, 0, 10.056, 0]}, {"time": 10.1667}]}, "eyebrow_R2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 6.75]}, {"time": 6.6667, "value": 6.75, "curve": "stepped"}, {"time": 9.8333, "value": 6.75, "curve": [9.944, 6.75, 10.056, 0]}, {"time": 10.1667}], "scale": [{"time": 6.3333, "curve": [6.444, 1, 6.556, 0.961, 6.444, 1, 6.556, 1]}, {"time": 6.6667, "x": 0.961, "curve": "stepped"}, {"time": 9.8333, "x": 0.961, "curve": [9.944, 0.961, 10.056, 1, 9.944, 1, 10.056, 1]}, {"time": 10.1667}]}, "eyebrow_R3": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 3.67]}, {"time": 6.6667, "value": 3.67, "curve": "stepped"}, {"time": 9.8333, "value": 3.67, "curve": [9.944, 3.67, 10.056, 0]}, {"time": 10.1667}]}, "leg_L2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.9]}, {"time": 7.6667, "value": -0.9, "curve": [8.111, -0.9, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.26]}, {"time": 7.6667, "value": 0.27, "curve": [8.111, 0.27, 8.556, 0]}, {"time": 9}]}, "leg_R2": {"rotate": [{"value": -0.01, "curve": "stepped"}, {"time": 5.3333, "value": -0.01, "curve": [5.667, -0.01, 6, -0.01]}, {"time": 6.3333, "value": -0.01, "curve": [6.778, -0.01, 7.222, -0.86]}, {"time": 7.6667, "value": -0.86, "curve": [8.111, -0.86, 8.556, -0.01]}, {"time": 9, "value": -0.01}]}, "leg_R3": {"rotate": [{"value": -0.02, "curve": "stepped"}, {"time": 5.3333, "value": -0.02, "curve": [5.667, -0.02, 6, -0.02]}, {"time": 6.3333, "value": -0.02, "curve": [6.778, -0.02, 7.222, 0.24]}, {"time": 7.6667, "value": 0.24, "curve": [8.111, 0.24, 8.556, -0.02]}, {"time": 9, "value": -0.02}]}, "headround3": {"translate": [{"x": 50.32, "curve": [0.279, -26.87, 0.556, -129.56, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -129.56, "curve": [1.278, -129.56, 1.722, 134.09, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 134.15, "curve": [2.611, 134.22, 3.056, -129.5, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -129.56, "curve": [3.944, -129.63, 4.389, 134.09, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 134.15, "curve": [5.278, 134.22, 5.722, -129.44, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -129.56, "curve": [6.5, -129.65, 6.833, 234.09, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 234.09, "curve": [7.611, 234.1, 8.056, 208.44, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 208.43, "curve": [8.944, 208.42, 9.389, 234, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 234.09, "curve": [10.278, 234.18, 10.722, -129.5, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -129.56, "curve": [11.611, -129.63, 12.056, 134.09, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 134.15, "curve": [12.667, 134.18, 12.835, 96.97, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 50.32}]}, "headround": {"translate": [{"y": -69.85, "curve": [0.336, 0, 0.668, 0, 0.336, -12.01, 0.668, 102.18]}, {"time": 1, "y": 102.18, "curve": [1.444, 0, 1.889, 0, 1.444, 102.18, 1.889, -102.54]}, {"time": 2.3333, "y": -102.59, "curve": [2.778, 0, 3.222, 0, 2.778, -102.65, 3.222, 102.13]}, {"time": 3.6667, "y": 102.18, "curve": [4.111, 0, 4.556, 0, 4.111, 102.24, 4.556, -102.54]}, {"time": 5, "y": -102.59, "curve": [5.444, 0, 5.889, 0, 5.444, -102.65, 5.889, 102.24]}, {"time": 6.3333, "y": 102.18, "curve": [6.667, 0, 7, 0, 6.667, 102.14, 7, 271.57]}, {"time": 7.3333, "y": 271.57, "curve": [7.778, 0, 8.222, 0, 7.778, 271.58, 8.222, 237.47]}, {"time": 8.6667, "y": 237.46, "curve": [9.111, 0, 9.556, 0, 9.111, 237.45, 9.556, 271.53]}, {"time": 10, "y": 271.57, "curve": [10.444, 0, 10.889, 0, 10.444, 271.61, 10.889, 102.13]}, {"time": 11.3333, "y": 102.18, "curve": [11.778, 0, 12.222, 0, 11.778, 102.24, 12.222, -102.54]}, {"time": 12.6667, "y": -102.59, "curve": [12.779, 0, 12.892, 0, 12.779, -102.61, 12.892, -89.48]}, {"time": 13, "y": -69.85}]}, "bodyround": {"translate": [{"x": 42.93, "y": 63.24, "curve": [0.168, 72.85, 0.334, 97.4, 0.168, 131.89, 0.334, 188.22]}, {"time": 0.5, "x": 97.4, "y": 188.22, "curve": [0.944, 97.4, 1.389, -73.8, 0.944, 188.22, 1.389, -204.61]}, {"time": 1.8333, "x": -73.84, "y": -204.71, "curve": [2.278, -73.88, 2.722, 97.35, 2.278, -204.81, 2.722, 188.12]}, {"time": 3.1667, "x": 97.4, "y": 188.22, "curve": [3.611, 97.44, 4.056, -73.8, 3.611, 188.32, 4.056, -204.61]}, {"time": 4.5, "x": -73.84, "y": -204.71, "curve": [4.944, -73.88, 5.389, 97.34, 4.944, -204.81, 5.389, 188.09]}, {"time": 5.8333, "x": 97.4, "y": 188.22, "curve": [6.167, 97.44, 6.5, -73.82, 6.167, 188.32, 6.5, -204.67]}, {"time": 6.8333, "x": -73.84, "y": -204.71, "curve": [7.278, -73.86, 7.722, 11.76, 7.278, -204.76, 7.722, -8.29]}, {"time": 8.1667, "x": 11.78, "y": -8.24, "curve": [8.611, 11.8, 9.056, -73.8, 8.611, -8.19, 9.056, -204.61]}, {"time": 9.5, "x": -73.84, "y": -204.71, "curve": [9.944, -73.88, 10.389, 97.35, 9.944, -204.81, 10.389, 188.12]}, {"time": 10.8333, "x": 97.4, "y": 188.22, "curve": [11.278, 97.44, 11.722, -73.8, 11.278, 188.32, 11.722, -204.61]}, {"time": 12.1667, "x": -73.84, "y": -204.71, "curve": [12.445, -73.87, 12.724, -7.29, 12.445, -204.77, 12.724, -51.99]}, {"time": 13, "x": 42.93, "y": 63.24}]}, "tunround": {"translate": [{"x": -181.5, "y": -36.69, "curve": [0.057, -192.36, 0.112, -200.64, 0.057, -40.48, 0.112, -43.38]}, {"time": 0.1667, "x": -200.64, "y": -43.38, "curve": [0.611, -200.64, 1.056, 206.26, 0.611, -43.38, 1.056, 98.86]}, {"time": 1.5, "x": 206.37, "y": 98.9, "curve": [1.944, 206.47, 2.389, -200.53, 1.944, 98.93, 2.389, -43.34]}, {"time": 2.8333, "x": -200.64, "y": -43.38, "curve": [3.278, -200.74, 3.722, 206.26, 3.278, -43.41, 3.722, 98.86]}, {"time": 4.1667, "x": 206.37, "y": 98.9, "curve": [4.611, 206.47, 5.056, -200.5, 4.611, 98.93, 5.056, -43.33]}, {"time": 5.5, "x": -200.64, "y": -43.38, "curve": [5.833, -200.74, 6.167, 206.33, 5.833, -43.41, 6.167, 98.88]}, {"time": 6.5, "x": 206.37, "y": 98.9, "curve": [6.944, 206.42, 7.389, 2.92, 6.944, 98.91, 7.389, 27.78]}, {"time": 7.8333, "x": 2.87, "y": 27.76, "curve": [8.278, 2.81, 8.722, 206.26, 8.278, 27.74, 8.722, 98.86]}, {"time": 9.1667, "x": 206.37, "y": 98.9, "curve": [9.611, 206.47, 10.056, -200.53, 9.611, 98.93, 10.056, -43.34]}, {"time": 10.5, "x": -200.64, "y": -43.38, "curve": [10.944, -200.74, 11.389, 206.26, 10.944, -43.41, 11.389, 98.86]}, {"time": 11.8333, "x": 206.37, "y": 98.9, "curve": [12.223, 206.46, 12.613, -104.33, 12.223, 98.93, 12.613, -9.71]}, {"time": 13, "x": -181.5, "y": -36.69}]}, "leg_L4": {"rotate": [{"value": -0.03, "curve": "stepped"}, {"time": 5.3333, "value": -0.03, "curve": [5.667, -0.03, 6, -0.03]}, {"time": 6.3333, "value": -0.03, "curve": [6.778, -0.03, 7.222, -0.8]}, {"time": 7.6667, "value": -0.8, "curve": [8.111, -0.8, 8.556, -0.03]}, {"time": 9, "value": -0.03}]}, "leg_L1": {"translate": [{"y": 10.16, "curve": [0.444, 0, 0.889, 0, 0.444, 10.16, 0.889, -12.57]}, {"time": 1.3333, "y": -12.57, "curve": [1.778, 0, 2.222, 0, 1.778, -12.58, 2.222, 10.15]}, {"time": 2.6667, "y": 10.16, "curve": [3.111, 0, 3.556, 0, 3.111, 10.16, 3.556, -12.57]}, {"time": 4, "y": -12.57, "curve": [4.444, 0, 4.889, 0, 4.444, -12.58, 4.889, 10.15]}, {"time": 5.3333, "y": 10.16, "curve": [5.667, 0, 6, 0, 5.667, 10.16, 6, -12.57]}, {"time": 6.3333, "y": -12.57, "curve": [6.778, 0, 7.222, 0, 6.778, -12.58, 7.222, -1.21]}, {"time": 7.6667, "y": -1.21, "curve": [8.111, 0, 8.556, 0, 8.111, -1.21, 8.556, -12.57]}, {"time": 9, "y": -12.57, "curve": [9.444, 0, 9.889, 0, 9.444, -12.58, 9.889, 10.15]}, {"time": 10.3333, "y": 10.16, "curve": [10.778, 0, 11.222, 0, 10.778, 10.16, 11.222, -12.57]}, {"time": 11.6667, "y": -12.57, "curve": [12.111, 0, 12.556, 0, 12.111, -12.58, 12.556, 10.16]}, {"time": 13, "y": 10.16}]}, "leg_R4": {"rotate": [{"value": -0.04, "curve": "stepped"}, {"time": 5.3333, "value": -0.04, "curve": [5.667, -0.04, 6, -0.04]}, {"time": 6.3333, "value": -0.04, "curve": [6.778, -0.04, 7.222, -0.75]}, {"time": 7.6667, "value": -0.75, "curve": [8.111, -0.76, 8.556, -0.04]}, {"time": 9, "value": -0.04}]}, "leg_R1": {"translate": [{"y": 3.68, "curve": [0.444, 0, 0.889, 0, 0.444, 3.68, 0.889, -6.14]}, {"time": 1.3333, "y": -6.14, "curve": [1.778, 0, 2.222, 0, 1.778, -6.14, 2.222, 3.68]}, {"time": 2.6667, "y": 3.68, "curve": [3.111, 0, 3.556, 0, 3.111, 3.68, 3.556, -6.14]}, {"time": 4, "y": -6.14, "curve": [4.444, 0, 4.889, 0, 4.444, -6.14, 4.889, 3.68]}, {"time": 5.3333, "y": 3.68, "curve": [5.667, 0, 6, 0, 5.667, 3.68, 6, -6.14]}, {"time": 6.3333, "y": -6.14, "curve": [6.778, 0, 7.222, 0, 6.778, -6.14, 7.222, -1.23]}, {"time": 7.6667, "y": -1.23, "curve": [8.111, 0, 8.556, 0, 8.111, -1.23, 8.556, -6.14]}, {"time": 9, "y": -6.14, "curve": [9.444, 0, 9.889, 0, 9.444, -6.14, 9.889, 3.68]}, {"time": 10.3333, "y": 3.68, "curve": [10.778, 0, 11.222, 0, 10.778, 3.68, 11.222, -6.14]}, {"time": 11.6667, "y": -6.14, "curve": [12.111, 0, 12.556, 0, 12.111, -6.14, 12.556, 3.68]}, {"time": 13, "y": 3.68}]}, "eye_L": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -2.79, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -2.79, "curve": "stepped"}, {"time": 3.4, "x": -2.79, "curve": [3.433, -2.79, 3.467, -2.18, 3.433, 0, 3.467, 1.31]}, {"time": 3.5, "x": -2.18, "y": 1.31, "curve": "stepped"}, {"time": 4, "x": -2.18, "y": 1.31, "curve": [4.033, -2.18, 4.067, 0, 4.033, 1.31, 4.067, 0]}, {"time": 4.1, "curve": "stepped"}, {"time": 6.5, "curve": [6.533, 0, 6.567, -3, 6.533, 0, 6.567, 7.51]}, {"time": 6.6, "x": -3, "y": 7.51, "curve": "stepped"}, {"time": 7.3333, "x": -3, "y": 7.51, "curve": [7.367, -3, 7.4, -1.22, 7.367, 7.51, 7.4, 7.51]}, {"time": 7.4333, "x": -1.22, "y": 7.51, "curve": "stepped"}, {"time": 8.5667, "x": -1.22, "y": 7.51, "curve": [8.6, -1.22, 8.633, -2.38, 8.6, 7.51, 8.633, 5.7]}, {"time": 8.6667, "x": -2.38, "y": 5.7, "curve": "stepped"}, {"time": 9.2333, "x": -2.38, "y": 5.7, "curve": [9.267, -2.38, 9.3, -3, 9.267, 5.7, 9.3, 7.51]}, {"time": 9.3333, "x": -3, "y": 7.51, "curve": "stepped"}, {"time": 10.1667, "x": -3, "y": 7.51, "curve": [10.2, -3, 10.233, 0, 10.2, 7.51, 10.233, 0]}, {"time": 10.2667}]}, "eye_R": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -2.79, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -2.79, "curve": "stepped"}, {"time": 3.4, "x": -2.79, "curve": [3.433, -2.79, 3.467, -2.18, 3.433, 0, 3.467, 1.31]}, {"time": 3.5, "x": -2.18, "y": 1.31, "curve": "stepped"}, {"time": 4, "x": -2.18, "y": 1.31, "curve": [4.033, -2.18, 4.067, 0, 4.033, 1.31, 4.067, 0]}, {"time": 4.1, "curve": "stepped"}, {"time": 6.5, "curve": [6.533, 0, 6.567, -3, 6.533, 0, 6.567, 6.92]}, {"time": 6.6, "x": -3, "y": 6.92, "curve": "stepped"}, {"time": 7.3333, "x": -3, "y": 6.92, "curve": [7.367, -3, 7.4, -1.22, 7.367, 6.92, 7.4, 6.92]}, {"time": 7.4333, "x": -1.22, "y": 6.92, "curve": "stepped"}, {"time": 8.5667, "x": -1.22, "y": 6.92, "curve": [8.6, -1.22, 8.633, -2.38, 8.6, 6.92, 8.633, 5.11]}, {"time": 8.6667, "x": -2.38, "y": 5.11, "curve": "stepped"}, {"time": 9.2333, "x": -2.38, "y": 5.11, "curve": [9.267, -2.38, 9.3, -3, 9.267, 5.11, 9.3, 6.92]}, {"time": 9.3333, "x": -3, "y": 6.92, "curve": "stepped"}, {"time": 10.1667, "x": -3, "y": 6.92, "curve": [10.2, -3, 10.233, 0, 10.2, 6.92, 10.233, 0]}, {"time": 10.2667}]}, "neck2": {"rotate": [{"value": -12.72, "curve": [0.279, 1.56, 0.556, 20.55]}, {"time": 0.8333, "value": 20.55, "curve": [1.278, 20.55, 1.722, -28.23]}, {"time": 2.1667, "value": -28.23, "curve": [2.611, -28.23, 3.056, 20.55]}, {"time": 3.5, "value": 20.55, "curve": [3.944, 20.55, 4.389, -28.23]}, {"time": 4.8333, "value": -28.23, "curve": [5.278, -28.23, 5.722, 20.55]}, {"time": 6.1667, "value": 20.55, "curve": [6.5, 20.55, 6.833, -28.23]}, {"time": 7.1667, "value": -28.23, "curve": [7.611, -28.23, 8.056, 20.55]}, {"time": 8.5, "value": 20.55, "curve": [8.944, 20.55, 9.389, -28.23]}, {"time": 9.8333, "value": -28.23, "curve": [10.278, -28.23, 10.722, 20.55]}, {"time": 11.1667, "value": 20.55, "curve": [11.611, 20.55, 12.056, -28.23]}, {"time": 12.5, "value": -28.23, "curve": [12.667, -28.23, 12.835, -21.34]}, {"time": 13, "value": -12.72}], "translate": [{"x": 11.25, "y": -1.69, "curve": [0.279, 2.42, 0.556, -9.33, 0.279, -2.07, 0.556, -2.59]}, {"time": 0.8333, "x": -9.33, "y": -2.59, "curve": [1.278, -9.33, 1.722, 20.85, 1.278, -2.59, 1.722, -1.26]}, {"time": 2.1667, "x": 20.85, "y": -1.26, "curve": [2.611, 20.85, 3.056, -9.33, 2.611, -1.26, 3.056, -2.59]}, {"time": 3.5, "x": -9.33, "y": -2.59, "curve": [3.944, -9.33, 4.389, 20.85, 3.944, -2.59, 4.389, -1.26]}, {"time": 4.8333, "x": 20.85, "y": -1.26, "curve": [5.278, 20.85, 5.722, -9.33, 5.278, -1.26, 5.722, -2.59]}, {"time": 6.1667, "x": -9.33, "y": -2.59, "curve": [6.5, -9.33, 6.833, 20.85, 6.5, -2.59, 6.833, -1.26]}, {"time": 7.1667, "x": 20.85, "y": -1.26, "curve": [7.611, 20.85, 8.056, -9.33, 7.611, -1.26, 8.056, -2.59]}, {"time": 8.5, "x": -9.33, "y": -2.59, "curve": [8.944, -9.33, 9.389, 20.85, 8.944, -2.59, 9.389, -1.26]}, {"time": 9.8333, "x": 20.85, "y": -1.26, "curve": [10.278, 20.85, 10.722, -9.33, 10.278, -1.26, 10.722, -2.59]}, {"time": 11.1667, "x": -9.33, "y": -2.59, "curve": [11.611, -9.33, 12.056, 20.85, 11.611, -2.59, 12.056, -1.26]}, {"time": 12.5, "x": 20.85, "y": -1.26, "curve": [12.667, 20.85, 12.835, 16.59, 12.667, -1.26, 12.835, -1.45]}, {"time": 13, "x": 11.25, "y": -1.69}]}, "neck3": {"rotate": [{"value": 6.59, "curve": [0.279, -6.42, 0.556, -23.72]}, {"time": 0.8333, "value": -23.72, "curve": [1.278, -23.72, 1.722, 20.73]}, {"time": 2.1667, "value": 20.73, "curve": [2.611, 20.73, 3.056, -23.72]}, {"time": 3.5, "value": -23.72, "curve": [3.944, -23.72, 4.389, 20.73]}, {"time": 4.8333, "value": 20.73, "curve": [5.278, 20.73, 5.722, -23.72]}, {"time": 6.1667, "value": -23.72, "curve": [6.5, -23.72, 6.833, 20.73]}, {"time": 7.1667, "value": 20.73, "curve": [7.611, 20.73, 8.056, -23.72]}, {"time": 8.5, "value": -23.72, "curve": [8.944, -23.72, 9.389, 20.73]}, {"time": 9.8333, "value": 20.73, "curve": [10.278, 20.73, 10.722, -23.72]}, {"time": 11.1667, "value": -23.72, "curve": [11.611, -23.72, 12.056, 20.73]}, {"time": 12.5, "value": 20.73, "curve": [12.667, 20.73, 12.835, 14.46]}, {"time": 13, "value": 6.59}], "translate": [{"x": 7.45, "y": -6.63, "curve": [0.279, -3.15, 0.556, -17.24, 0.279, -4.36, 0.556, -1.35]}, {"time": 0.8333, "x": -17.24, "y": -1.35, "curve": [1.278, -17.24, 1.722, 18.96, 1.278, -1.35, 1.722, -9.09]}, {"time": 2.1667, "x": 18.96, "y": -9.09, "curve": [2.611, 18.96, 3.056, -17.24, 2.611, -9.09, 3.056, -1.35]}, {"time": 3.5, "x": -17.24, "y": -1.35, "curve": [3.944, -17.24, 4.389, 18.96, 3.944, -1.35, 4.389, -9.09]}, {"time": 4.8333, "x": 18.96, "y": -9.09, "curve": [5.278, 18.96, 5.722, -17.24, 5.278, -9.09, 5.722, -1.35]}, {"time": 6.1667, "x": -17.24, "y": -1.35, "curve": [6.5, -17.24, 6.833, 18.96, 6.5, -1.35, 6.833, -9.09]}, {"time": 7.1667, "x": 18.96, "y": -9.09, "curve": [7.611, 18.96, 8.056, -17.24, 7.611, -9.09, 8.056, -1.35]}, {"time": 8.5, "x": -17.24, "y": -1.35, "curve": [8.944, -17.24, 9.389, 18.96, 8.944, -1.35, 9.389, -9.09]}, {"time": 9.8333, "x": 18.96, "y": -9.09, "curve": [10.278, 18.96, 10.722, -17.24, 10.278, -9.09, 10.722, -1.35]}, {"time": 11.1667, "x": -17.24, "y": -1.35, "curve": [11.611, -17.24, 12.056, 18.96, 11.611, -1.35, 12.056, -9.09]}, {"time": 12.5, "x": 18.96, "y": -9.09, "curve": [12.667, 18.96, 12.835, 13.85, 12.667, -9.09, 12.835, -8]}, {"time": 13, "x": 7.45, "y": -6.63}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-6.9292, 0.95612, -6.92908, 0.95639, -3.20154, 0.09283, -3.20154, 0.09291, -3.20154, 0.09283, -3.20154, 0.09291, -2.48608, -0.14772, -2.48608, -0.14764, -1.82617, -0.13545, -1.82617, -0.13541, -1.06714, 0.03102, -1.06714, 0.03102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.31262, 0.05711, -1.31262, 0.05714, -6.38647, 0.18425, -6.38647, 0.18452, -11.21191, -0.06447, -11.21179, -0.06409, -13.36548, -0.37151, -13.36536, -0.37123, -10.55298, -0.25883, -10.55286, -0.25841, -9.06628, 0.20453, -9.06604, 0.20494, 0, 0, 0, 0, -3.88538, -0.18842, -3.88538, -0.18826, -8.61157, -0.20158, -8.61182, -0.20137, -12.16724, -0.51096, -12.1676, -0.51077, -13.26318, -0.4146, -13.26318, -0.41451, -11.39355, -0.28871, -11.39417, -0.28844, -8.16309, -0.60515, -8.16431, -0.60469, -3.2312, -0.3931, -3.2323, -0.39252, -1.57703, -0.0336, -1.57678, -0.03319, -0.47253, -0.02817, -0.47229, -0.028], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-6.9292, 0.95612, -6.92908, 0.95639, -3.20154, 0.09283, -3.20154, 0.09291, -3.20154, 0.09283, -3.20154, 0.09291, -2.48608, -0.14772, -2.48608, -0.14764, -1.82617, -0.13545, -1.82617, -0.13541, -1.06714, 0.03102, -1.06714, 0.03102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.31262, 0.05711, -1.31262, 0.05714, -6.38647, 0.18425, -6.38647, 0.18452, -11.21191, -0.06447, -11.21179, -0.06409, -13.36548, -0.37151, -13.36536, -0.37123, -10.55298, -0.25883, -10.55286, -0.25841, -9.06628, 0.20453, -9.06604, 0.20494, 0, 0, 0, 0, -3.88538, -0.18842, -3.88538, -0.18826, -8.61157, -0.20158, -8.61182, -0.20137, -12.16724, -0.51096, -12.1676, -0.51077, -13.26318, -0.4146, -13.26318, -0.41451, -11.39355, -0.28871, -11.39417, -0.28844, -8.16309, -0.60515, -8.16431, -0.60469, -3.2312, -0.3931, -3.2323, -0.39252, -1.57703, -0.0336, -1.57678, -0.03319, -0.47253, -0.02817, -0.47229, -0.028], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "vertices": [-6.9292, 0.95612, -6.92908, 0.95639, -3.20154, 0.09283, -3.20154, 0.09291, -3.20154, 0.09283, -3.20154, 0.09291, -2.48608, -0.14772, -2.48608, -0.14764, -1.82617, -0.13545, -1.82617, -0.13541, -1.06714, 0.03102, -1.06714, 0.03102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.31262, 0.05711, -1.31262, 0.05714, -6.38647, 0.18425, -6.38647, 0.18452, -11.21191, -0.06447, -11.21179, -0.06409, -13.36548, -0.37151, -13.36536, -0.37123, -10.55298, -0.25883, -10.55286, -0.25841, -9.06628, 0.20453, -9.06604, 0.20494, 0, 0, 0, 0, -3.88538, -0.18842, -3.88538, -0.18826, -8.61157, -0.20158, -8.61182, -0.20137, -12.16724, -0.51096, -12.1676, -0.51077, -13.26318, -0.4146, -13.26318, -0.41451, -11.39355, -0.28871, -11.39417, -0.28844, -8.16309, -0.60515, -8.16431, -0.60469, -3.2312, -0.3931, -3.2323, -0.39252, -1.57703, -0.0336, -1.57678, -0.03319, -0.47253, -0.02817, -0.47229, -0.028], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-7.98523, -0.56075, -7.98523, -0.56058, -8.97095, -0.72005, -8.97095, -0.71988, -11.57397, -0.35532, -11.57397, -0.35521, -12.08716, -0.21269, -12.08716, -0.21263, -11.54053, -0.2025, -11.54053, -0.20197, -8.39014, -0.13815, -8.38989, -0.13766, -3.92786, 0.06003, -3.92786, 0.06024, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.27417, -0.06032, -0.27417, -0.06013, -2.36133, -0.3744, -2.36133, -0.37399, -3.04895, -0.12273, -3.04895, -0.12238, -3.04895, -0.12273, -3.04895, -0.12238, -6.33582, -0.38995, -6.33582, -0.38948, -4.52515, 0.00937, -4.52661, 0.00954, -10.56567, 0.7354, -10.56567, 0.73549, -13.0376, 0.2244, -13.03748, 0.22464, -12.42578, 0.31564, -12.42651, 0.31589, -9.31384, 0.51276, -9.31555, 0.51291, -4.78955, 0.30798, -4.79187, 0.308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.44446, 0.04718, -0.44446, 0.04718, -1.72107, 0.18275, -1.72131, 0.18279], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-7.98523, -0.56075, -7.98523, -0.56058, -8.97095, -0.72005, -8.97095, -0.71988, -11.57397, -0.35532, -11.57397, -0.35521, -12.08716, -0.21269, -12.08716, -0.21263, -11.54053, -0.2025, -11.54053, -0.20197, -8.39014, -0.13815, -8.38989, -0.13766, -3.92786, 0.06003, -3.92786, 0.06024, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.27417, -0.06032, -0.27417, -0.06013, -2.36133, -0.3744, -2.36133, -0.37399, -3.04895, -0.12273, -3.04895, -0.12238, -3.04895, -0.12273, -3.04895, -0.12238, -6.33582, -0.38995, -6.33582, -0.38948, -4.52515, 0.00937, -4.52661, 0.00954, -10.56567, 0.7354, -10.56567, 0.73549, -13.0376, 0.2244, -13.03748, 0.22464, -12.42578, 0.31564, -12.42651, 0.31589, -9.31384, 0.51276, -9.31555, 0.51291, -4.78955, 0.30798, -4.79187, 0.308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.44446, 0.04718, -0.44446, 0.04718, -1.72107, 0.18275, -1.72131, 0.18279], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "vertices": [-7.98523, -0.56075, -7.98523, -0.56058, -8.97095, -0.72005, -8.97095, -0.71988, -11.57397, -0.35532, -11.57397, -0.35521, -12.08716, -0.21269, -12.08716, -0.21263, -11.54053, -0.2025, -11.54053, -0.20197, -8.39014, -0.13815, -8.38989, -0.13766, -3.92786, 0.06003, -3.92786, 0.06024, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.27417, -0.06032, -0.27417, -0.06013, -2.36133, -0.3744, -2.36133, -0.37399, -3.04895, -0.12273, -3.04895, -0.12238, -3.04895, -0.12273, -3.04895, -0.12238, -6.33582, -0.38995, -6.33582, -0.38948, -4.52515, 0.00937, -4.52661, 0.00954, -10.56567, 0.7354, -10.56567, 0.73549, -13.0376, 0.2244, -13.03748, 0.22464, -12.42578, 0.31564, -12.42651, 0.31589, -9.31384, 0.51276, -9.31555, 0.51291, -4.78955, 0.30798, -4.79187, 0.308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.44446, 0.04718, -0.44446, 0.04718, -1.72107, 0.18275, -1.72131, 0.18279], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 100, "vertices": [-2.85986, 0.05829, -2.8595, 0.05835, -9.08337, 0.06453, -9.08203, 0.06479, -11.88, -0.04773, -11.87805, -0.04741, -11.6687, 1.15829, -11.66809, 1.15846, -9.03101, 0.55038, -9.0304, 0.55054, -3.83203, 0.16154, -3.83167, 0.16164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.64758, 0.06885, -0.64758, 0.06885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.77051, 0.13068, -2.77026, 0.13071, -7.93286, 0.10602, -7.93176, 0.1062, -11.57349, -0.24409, -11.57153, -0.24379, -12.22058, -0.17523, -12.21838, -0.17491, -10.46521, -0.6075, -10.46313, -0.60718, -6.573, -0.20224, -6.57166, -0.20204, -1.58191, -0.24133, -1.58105, -0.24127, -1.32971, -0.18619, -1.32922, -0.18616], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "offset": 100, "vertices": [-2.85986, 0.05829, -2.8595, 0.05835, -9.08337, 0.06453, -9.08203, 0.06479, -11.88, -0.04773, -11.87805, -0.04741, -11.6687, 1.15829, -11.66809, 1.15846, -9.03101, 0.55038, -9.0304, 0.55054, -3.83203, 0.16154, -3.83167, 0.16164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.64758, 0.06885, -0.64758, 0.06885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.77051, 0.13068, -2.77026, 0.13071, -7.93286, 0.10602, -7.93176, 0.1062, -11.57349, -0.24409, -11.57153, -0.24379, -12.22058, -0.17523, -12.21838, -0.17491, -10.46521, -0.6075, -10.46313, -0.60718, -6.573, -0.20224, -6.57166, -0.20204, -1.58191, -0.24133, -1.58105, -0.24127, -1.32971, -0.18619, -1.32922, -0.18616], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "offset": 100, "vertices": [-2.85986, 0.05829, -2.8595, 0.05835, -9.08337, 0.06453, -9.08203, 0.06479, -11.88, -0.04773, -11.87805, -0.04741, -11.6687, 1.15829, -11.66809, 1.15846, -9.03101, 0.55038, -9.0304, 0.55054, -3.83203, 0.16154, -3.83167, 0.16164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.64758, 0.06885, -0.64758, 0.06885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.77051, 0.13068, -2.77026, 0.13071, -7.93286, 0.10602, -7.93176, 0.1062, -11.57349, -0.24409, -11.57153, -0.24379, -12.22058, -0.17523, -12.21838, -0.17491, -10.46521, -0.6075, -10.46313, -0.60718, -6.573, -0.20224, -6.57166, -0.20204, -1.58191, -0.24133, -1.58105, -0.24127, -1.32971, -0.18619, -1.32922, -0.18616], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "head2": {"head": {"deform": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1]}, {"time": 6.6667, "offset": 80, "vertices": [1.90906, 0.35522, 1.90735, 0.35517, 0.19897, -0.00739, 0.19263, -0.00752, -0.77454, 0.02877, -0.78076, 0.02853, -0.41699, 0.01549, -0.4231, 0.01534, -0.28088, 0.01044, -0.28699, 0.0103, -0.40454, 0.01503, -0.41064, 0.01485, -0.7688, 0.02856, -0.77502, 0.02827, 0.17188, -0.00637, 0.16553, -0.00661, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.54932, -0.05751, 1.54285, -0.05761, 1.21521, -0.04509, 1.20898, -0.04526, 1.02075, -0.03787, 1.01465, -0.03809, 1.17139, -0.04347, 1.16516, -0.0436, 1.46399, -0.05434, 1.45752, -0.05454, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 0.72375, -0.02686, 0.71753, -0.02707, -0.25037, 0.00931, -0.25671, 0.00919, 0.41382, -0.01535, 0.40759, -0.01556, -0.2616, 0.00972, -0.26794, 0.00952, 0.67957, -0.02522, 0.67334, -0.0254, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.01746, -0.03775, 1.01135, -0.038, 0.67444, -0.02503, 0.67383, -0.02503, 0.67444, -0.02503, 0.67383, -0.02503, 1.40564, 0.69686, 1.39819, 0.69659, 1.39075, -0.05161, 1.3844, -0.05183, 1.44055, -0.80252, 1.43323, -0.8028], "curve": "stepped"}, {"time": 9.8333, "offset": 80, "vertices": [1.90906, 0.35522, 1.90735, 0.35517, 0.19897, -0.00739, 0.19263, -0.00752, -0.77454, 0.02877, -0.78076, 0.02853, -0.41699, 0.01549, -0.4231, 0.01534, -0.28088, 0.01044, -0.28699, 0.0103, -0.40454, 0.01503, -0.41064, 0.01485, -0.7688, 0.02856, -0.77502, 0.02827, 0.17188, -0.00637, 0.16553, -0.00661, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.54932, -0.05751, 1.54285, -0.05761, 1.21521, -0.04509, 1.20898, -0.04526, 1.02075, -0.03787, 1.01465, -0.03809, 1.17139, -0.04347, 1.16516, -0.0436, 1.46399, -0.05434, 1.45752, -0.05454, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 0.72375, -0.02686, 0.71753, -0.02707, -0.25037, 0.00931, -0.25671, 0.00919, 0.41382, -0.01535, 0.40759, -0.01556, -0.2616, 0.00972, -0.26794, 0.00952, 0.67957, -0.02522, 0.67334, -0.0254, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.01746, -0.03775, 1.01135, -0.038, 0.67444, -0.02503, 0.67383, -0.02503, 0.67444, -0.02503, 0.67383, -0.02503, 1.40564, 0.69686, 1.39819, 0.69659, 1.39075, -0.05161, 1.3844, -0.05183, 1.44055, -0.80252, 1.43323, -0.8028], "curve": [10.056, 0, 10.278, 1]}, {"time": 10.5}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": -11.17, "curve": [0.029, -11.17, 0.072, 10.33, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 10.33, "curve": [0.544, 10.33, 0.856, -11.17, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -11.17}]}, "ALL2": {"translate": [{"y": -19.87, "curve": [0.078, 0, 0.156, 0, 0.029, -19.87, 0.072, 32.32]}, {"time": 0.2333, "y": 32.32, "curve": [0.544, 0, 0.856, 0, 0.544, 32.32, 0.856, -19.87]}, {"time": 1.1667, "y": -19.87}]}, "body": {"rotate": [{"value": -2.89, "curve": [0.029, -2.89, 0.072, 5.82]}, {"time": 0.2333, "value": 5.82, "curve": [0.544, 5.82, 0.856, -2.89]}, {"time": 1.1667, "value": -2.89}], "translate": [{"y": -11.41, "curve": [0.078, 0, 0.156, 0, 0.029, -11.41, 0.072, 21.31]}, {"time": 0.2333, "y": 21.31, "curve": [0.544, 0, 0.856, 0, 0.544, 21.31, 0.856, -11.41]}, {"time": 1.1667, "y": -11.41}], "scale": [{"y": 1.048, "curve": [0.078, 1, 0.156, 1, 0.029, 1.048, 0.072, 0.953]}, {"time": 0.2333, "y": 0.953, "curve": [0.544, 1, 0.856, 1, 0.544, 0.953, 0.856, 1.048]}, {"time": 1.1667, "y": 1.048}]}, "body2": {"rotate": [{"value": 1.34, "curve": [0.034, 1.34, 0.082, -1.83]}, {"time": 0.2667, "value": -1.83, "curve": [0.567, -1.83, 0.867, 1.34]}, {"time": 1.1667, "value": 1.34}], "translate": [{"x": -10.1, "curve": [0.034, -10.1, 0.082, 17.01, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 17.01, "curve": [0.567, 17.01, 0.867, -10.1, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -10.1}], "scale": [{"x": 1.005, "y": 1.005, "curve": [0.034, 1.005, 0.082, 1.029, 0.034, 1.005, 0.082, 1.029]}, {"time": 0.2667, "x": 1.029, "y": 1.029, "curve": [0.567, 1.029, 0.867, 1.005, 0.567, 1.029, 0.867, 1.005]}, {"time": 1.1667, "x": 1.005, "y": 1.005}]}, "neck": {"rotate": [{"value": 0.58, "curve": [0.038, 0.58, 0.093, 5.85]}, {"time": 0.3, "value": 5.85, "curve": [0.589, 5.85, 0.878, 0.58]}, {"time": 1.1667, "value": 0.58}]}, "head": {"rotate": [{"value": 0.06, "curve": [0.038, 0.06, 0.093, 5.33]}, {"time": 0.3, "value": 5.33, "curve": [0.589, 5.33, 0.878, 0.06]}, {"time": 1.1667, "value": 0.06}]}, "sh_L": {"translate": [{"x": -3.86, "curve": [0.034, -3.86, 0.082, 18.9, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 18.9, "curve": [0.567, 18.9, 0.867, -3.86, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -3.86}]}, "sh_R": {"translate": [{"x": -3.86, "curve": [0.034, -3.86, 0.082, 10.77, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 10.77, "curve": [0.567, 10.77, 0.867, -3.86, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -3.86}]}, "tun": {"rotate": [{"value": -2.89, "curve": [0.029, -2.89, 0.072, 5.82]}, {"time": 0.2333, "value": 5.82, "curve": [0.544, 5.82, 0.856, -2.89]}, {"time": 1.1667, "value": -2.89}]}, "arm_L": {"rotate": [{"value": -0.41, "curve": [0.074, -1.09, 0.16, -1.64]}, {"time": 0.2333, "value": -1.64, "curve": [0.43, -1.64, 0.604, 2.21]}, {"time": 0.8, "value": 2.21, "curve": [0.923, 2.22, 1.045, 0.72]}, {"time": 1.1667, "value": -0.41}]}, "arm_L2": {"rotate": [{}]}, "arm_R": {"rotate": [{"value": 0.74, "curve": [0.074, 1.35, 0.16, 1.86]}, {"time": 0.2333, "value": 1.86, "curve": [0.43, 1.86, 0.604, -1.67]}, {"time": 0.8, "value": -1.67, "curve": [0.923, -1.67, 1.045, -0.3]}, {"time": 1.1667, "value": 0.74}]}, "arm_R2": {"rotate": [{"value": 0.09, "curve": [0.1, 0.97, 0.202, 1.86]}, {"time": 0.3, "value": 1.86, "curve": [0.496, 1.86, 0.704, -1.67]}, {"time": 0.9, "value": -1.67, "curve": [0.999, -1.67, 1.07, -0.79]}, {"time": 1.1667, "value": 0.09}]}, "arm_R3": {"rotate": [{"value": -0.55, "curve": [0.123, 0.49, 0.244, 1.86]}, {"time": 0.3667, "value": 1.86, "curve": [0.563, 1.86, 0.77, -1.67]}, {"time": 0.9667, "value": -1.67, "curve": [1.041, -1.67, 1.094, -1.17]}, {"time": 1.1667, "value": -0.55}]}, "RU_L": {"translate": [{"x": -18.35, "curve": [0.074, -35.7, 0.16, -49.94, 0.074, 0, 0.16, 0]}, {"time": 0.2333, "x": -49.94, "curve": [0.43, -49.94, 0.604, 49.35, 0.43, 0, 0.604, 0]}, {"time": 0.8, "x": 49.38, "curve": [0.923, 49.39, 1.045, 10.79, 0.923, 0, 1.045, 0]}, {"time": 1.1667, "x": -18.34}], "scale": [{"x": 1.059, "y": 0.964, "curve": [0.074, 1.023, 0.16, 0.951, 0.074, 0.998, 0.16, 1.065]}, {"time": 0.2333, "x": 0.951, "y": 1.065, "curve": [0.332, 0.951, 0.402, 1.079, 0.332, 1.065, 0.402, 0.944]}, {"time": 0.5, "x": 1.079, "y": 0.944, "curve": [0.598, 1.079, 0.702, 0.951, 0.598, 0.944, 0.702, 1.065]}, {"time": 0.8, "x": 0.951, "y": 1.065, "curve": [0.898, 0.951, 1.002, 1.079, 0.898, 1.065, 1.002, 0.944]}, {"time": 1.1, "x": 1.079, "y": 0.944, "curve": [1.125, 1.079, 1.143, 1.071, 1.125, 0.944, 1.143, 0.952]}, {"time": 1.1667, "x": 1.059, "y": 0.964}]}, "RU_L2": {"translate": [{"x": -1.47, "curve": [0.1, -16.54, 0.202, -31.81, 0.1, 0, 0.202, 0]}, {"time": 0.3, "x": -31.81, "curve": [0.496, -31.81, 0.704, 28.84, 0.496, 0, 0.704, 0]}, {"time": 0.9, "x": 28.86, "curve": [0.999, 28.87, 1.07, 13.8, 0.999, 0, 1.07, 0]}, {"time": 1.1667, "x": -1.47}], "scale": [{"x": 1.079, "y": 0.944, "curve": [0.098, 1.079, 0.202, 0.951, 0.098, 0.944, 0.202, 1.065]}, {"time": 0.3, "x": 0.951, "y": 1.065, "curve": [0.398, 0.951, 0.502, 1.079, 0.398, 1.065, 0.502, 0.944]}, {"time": 0.6, "x": 1.079, "y": 0.944, "curve": [0.698, 1.079, 0.802, 0.951, 0.698, 0.944, 0.802, 1.065]}, {"time": 0.9, "x": 0.951, "y": 1.065, "curve": [0.998, 0.951, 1.068, 1.079, 0.998, 1.065, 1.068, 0.944]}, {"time": 1.1667, "x": 1.079, "y": 0.944}]}, "RU_L3": {"translate": [{"x": 12.3, "curve": [0.123, -4.14, 0.244, -26.01, 0.123, 0, 0.244, 0]}, {"time": 0.3667, "x": -26.01, "curve": [0.563, -26.01, 0.77, 30.13, 0.563, 0, 0.77, 0]}, {"time": 0.9667, "x": 30.15, "curve": [1.041, 30.15, 1.094, 22.23, 1.041, 0, 1.094, 0]}, {"time": 1.1667, "x": 12.3}], "scale": [{"x": 1.059, "y": 0.964, "curve": [0.025, 1.071, 0.042, 1.079, 0.025, 0.952, 0.042, 0.944]}, {"time": 0.0667, "x": 1.079, "y": 0.944, "curve": [0.165, 1.079, 0.268, 0.951, 0.165, 0.944, 0.268, 1.065]}, {"time": 0.3667, "x": 0.951, "y": 1.065, "curve": [0.465, 0.951, 0.568, 1.079, 0.465, 1.065, 0.568, 0.944]}, {"time": 0.6667, "x": 1.079, "y": 0.944, "curve": [0.765, 1.079, 0.868, 0.951, 0.765, 0.944, 0.868, 1.065]}, {"time": 0.9667, "x": 0.951, "y": 1.065, "curve": [1.041, 0.951, 1.094, 1.023, 1.041, 1.065, 1.094, 0.997]}, {"time": 1.1667, "x": 1.059, "y": 0.964}]}, "RU_R": {"translate": [{"x": -18.35, "curve": [0.074, -35.7, 0.16, -49.94, 0.074, 0, 0.16, 0]}, {"time": 0.2333, "x": -49.94, "curve": [0.43, -49.94, 0.604, 49.35, 0.43, 0, 0.604, 0]}, {"time": 0.8, "x": 49.38, "curve": [0.923, 49.39, 1.045, 10.79, 0.923, 0, 1.045, 0]}, {"time": 1.1667, "x": -18.34}], "scale": [{"x": 1.059, "y": 0.964, "curve": [0.074, 1.023, 0.16, 0.951, 0.074, 0.998, 0.16, 1.065]}, {"time": 0.2333, "x": 0.951, "y": 1.065, "curve": [0.332, 0.951, 0.402, 1.079, 0.332, 1.065, 0.402, 0.944]}, {"time": 0.5, "x": 1.079, "y": 0.944, "curve": [0.598, 1.079, 0.702, 0.951, 0.598, 0.944, 0.702, 1.065]}, {"time": 0.8, "x": 0.951, "y": 1.065, "curve": [0.898, 0.951, 1.002, 1.079, 0.898, 1.065, 1.002, 0.944]}, {"time": 1.1, "x": 1.079, "y": 0.944, "curve": [1.125, 1.079, 1.143, 1.071, 1.125, 0.944, 1.143, 0.952]}, {"time": 1.1667, "x": 1.059, "y": 0.964}]}, "RU_R2": {"translate": [{"x": -1.47, "curve": [0.1, -16.54, 0.202, -31.81, 0.1, 0, 0.202, 0]}, {"time": 0.3, "x": -31.81, "curve": [0.496, -31.81, 0.704, 28.84, 0.496, 0, 0.704, 0]}, {"time": 0.9, "x": 28.86, "curve": [0.999, 28.87, 1.07, 13.8, 0.999, 0, 1.07, 0]}, {"time": 1.1667, "x": -1.47}], "scale": [{"x": 1.079, "y": 0.944, "curve": [0.098, 1.079, 0.202, 0.951, 0.098, 0.944, 0.202, 1.065]}, {"time": 0.3, "x": 0.951, "y": 1.065, "curve": [0.398, 0.951, 0.502, 1.079, 0.398, 1.065, 0.502, 0.944]}, {"time": 0.6, "x": 1.079, "y": 0.944, "curve": [0.698, 1.079, 0.802, 0.951, 0.698, 0.944, 0.802, 1.065]}, {"time": 0.9, "x": 0.951, "y": 1.065, "curve": [0.998, 0.951, 1.068, 1.079, 0.998, 1.065, 1.068, 0.944]}, {"time": 1.1667, "x": 1.079, "y": 0.944}]}, "RU_R3": {"translate": [{"x": 12.3, "curve": [0.123, -4.14, 0.244, -26.01, 0.123, 0, 0.244, 0]}, {"time": 0.3667, "x": -26.01, "curve": [0.563, -26.01, 0.77, 30.13, 0.563, 0, 0.77, 0]}, {"time": 0.9667, "x": 30.15, "curve": [1.041, 30.15, 1.094, 22.23, 1.041, 0, 1.094, 0]}, {"time": 1.1667, "x": 12.3}], "scale": [{"x": 1.059, "y": 0.964, "curve": [0.025, 1.071, 0.042, 1.079, 0.025, 0.952, 0.042, 0.944]}, {"time": 0.0667, "x": 1.079, "y": 0.944, "curve": [0.165, 1.079, 0.268, 0.951, 0.165, 0.944, 0.268, 1.065]}, {"time": 0.3667, "x": 0.951, "y": 1.065, "curve": [0.465, 0.951, 0.568, 1.079, 0.465, 1.065, 0.568, 0.944]}, {"time": 0.6667, "x": 1.079, "y": 0.944, "curve": [0.765, 1.079, 0.868, 0.951, 0.765, 0.944, 0.868, 1.065]}, {"time": 0.9667, "x": 0.951, "y": 1.065, "curve": [1.041, 0.951, 1.094, 1.023, 1.041, 1.065, 1.094, 0.997]}, {"time": 1.1667, "x": 1.059, "y": 0.964}]}, "earring_L": {"rotate": [{"value": 9.3, "curve": [0.025, 11.76, 0.042, 13.49]}, {"time": 0.0667, "value": 13.5, "curve": [0.165, 13.5, 0.268, -12.74]}, {"time": 0.3667, "value": -12.74, "curve": [0.465, -12.74, 0.568, 13.49]}, {"time": 0.6667, "value": 13.5, "curve": [0.765, 13.5, 0.868, -12.73]}, {"time": 0.9667, "value": -12.74, "curve": [1.041, -12.74, 1.094, 1.95]}, {"time": 1.1667, "value": 9.3}]}, "earring_R": {"rotate": [{"value": 9.3, "curve": [0.025, 11.76, 0.042, 13.49]}, {"time": 0.0667, "value": 13.5, "curve": [0.165, 13.5, 0.268, -12.74]}, {"time": 0.3667, "value": -12.74, "curve": [0.465, -12.74, 0.568, 13.49]}, {"time": 0.6667, "value": 13.5, "curve": [0.765, 13.5, 0.868, -12.73]}, {"time": 0.9667, "value": -12.74, "curve": [1.041, -12.74, 1.094, 1.95]}, {"time": 1.1667, "value": 9.3}]}, "leg_L3": {"rotate": [{}]}, "leg_R2": {"rotate": [{"value": -0.01}]}, "leg_R3": {"rotate": [{"value": -0.02}]}, "headround3": {"translate": [{"x": 50.32, "curve": [0.042, 50.32, 0.103, -209.61, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -209.61, "curve": [0.611, -209.61, 0.889, 50.32, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 50.32}]}, "headround": {"translate": [{"y": -69.85, "curve": [0.111, 0, 0.222, 0, 0.042, -69.85, 0.103, -221.4]}, {"time": 0.3333, "y": -221.4, "curve": [0.611, 0, 0.889, 0, 0.611, -221.4, 0.889, -69.85]}, {"time": 1.1667, "y": -69.85}]}, "bodyround": {"translate": [{"x": 42.93, "y": 63.24, "curve": [0.034, 42.93, 0.082, -87.26, 0.034, 63.24, 0.082, -378.98]}, {"time": 0.2667, "x": -87.26, "y": -378.98, "curve": [0.567, -87.26, 0.867, 42.93, 0.567, -378.98, 0.867, 63.24]}, {"time": 1.1667, "x": 42.93, "y": 63.24}]}, "tunround": {"translate": [{"x": -181.5, "y": -36.69, "curve": [0.029, -181.5, 0.072, 364.9, 0.029, -36.69, 0.072, 154.85]}, {"time": 0.2333, "x": 364.9, "y": 154.85, "curve": [0.544, 364.9, 0.856, -181.5, 0.544, 154.85, 0.856, -36.69]}, {"time": 1.1667, "x": -181.5, "y": -36.69}]}, "leg_L4": {"rotate": [{"value": -0.03}]}, "leg_L1": {"translate": [{"y": 10.16, "curve": [0.078, 0, 0.156, 0, 0.029, 10.16, 0.072, -13.56]}, {"time": 0.2333, "y": -13.56, "curve": [0.544, 0, 0.856, 0, 0.544, -13.56, 0.856, 10.16]}, {"time": 1.1667, "y": 10.16}]}, "leg_R4": {"rotate": [{"value": -0.04}]}, "leg_R1": {"translate": [{"y": 3.68, "curve": [0.078, 0, 0.156, 0, 0.029, 3.68, 0.072, -20.82]}, {"time": 0.2333, "y": -20.82, "curve": [0.544, 0, 0.856, 0, 0.544, -20.82, 0.856, 3.68]}, {"time": 1.1667, "y": 3.68}]}, "eyebrow_R": {"translate": [{"curve": [0.029, 0, 0.072, -3.11, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -3.11, "curve": [0.544, -3.11, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.029, 0, 0.072, 8.63]}, {"time": 0.2333, "value": 8.63, "curve": [0.544, 8.63, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.029, 1, 0.072, 0.945, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.945, "curve": [0.544, 0.945, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.029, 0, 0.072, 18.23]}, {"time": 0.2333, "value": 18.23, "curve": [0.544, 18.23, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.029, 0, 0.072, -15.82]}, {"time": 0.2333, "value": -15.82, "curve": [0.544, -15.82, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.029, 0, 0.072, -8.92]}, {"time": 0.2333, "value": -8.92, "curve": [0.544, -8.92, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.029, 1, 0.072, 0.945, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.945, "curve": [0.544, 0.945, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L": {"translate": [{"curve": [0.029, 0, 0.072, -3.11, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -3.11, "curve": [0.544, -3.11, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "neck2": {"rotate": [{"value": -12.72, "curve": [0.031, -12.72, 0.074, -1.98]}, {"time": 0.3, "value": -1.98, "curve": [0.459, -1.98, 0.608, -49.08]}, {"time": 0.7667, "value": -49.08, "curve": [0.9, -49.08, 1.033, -12.72]}, {"time": 1.1667, "value": -12.72}], "translate": [{"x": 11.25, "y": -1.69, "curve": [0.031, 11.25, 0.074, -14.9, 0.031, -1.69, 0.074, -2.37]}, {"time": 0.3, "x": -14.9, "y": -2.37, "curve": [0.459, -14.9, 0.608, 43.89, 0.459, -2.37, 0.608, 19.49]}, {"time": 0.7667, "x": 43.89, "y": 19.49, "curve": [0.9, 43.89, 1.033, 11.25, 0.9, 19.49, 1.033, -1.69]}, {"time": 1.1667, "x": 11.25, "y": -1.69}]}, "neck3": {"rotate": [{"value": 6.59, "curve": [0.031, 6.59, 0.074, -23.72]}, {"time": 0.3, "value": -23.72, "curve": [0.459, -23.72, 0.608, 20.73]}, {"time": 0.7667, "value": 20.73, "curve": [0.9, 20.73, 1.033, 6.59]}, {"time": 1.1667, "value": 6.59}], "translate": [{"x": 7.45, "y": -6.63, "curve": [0.031, 7.45, 0.074, -18.03, 0.031, -6.63, 0.074, 9.04]}, {"time": 0.3, "x": -18.03, "y": 9.04, "curve": [0.459, -18.03, 0.608, 44.98, 0.459, 9.04, 0.608, -37.95]}, {"time": 0.7667, "x": 44.98, "y": -37.95, "curve": [0.9, 44.98, 1.033, 7.45, 0.9, -37.95, 1.033, -6.63]}, {"time": 1.1667, "x": 7.45, "y": -6.63}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.031, 0, 0.072, 1]}, {"time": 0.2333, "vertices": [-1.81083, 0.24986, -1.8108, 0.24994, -0.83667, 0.02426, -0.83667, 0.02428, -0.83667, 0.02426, -0.83667, 0.02428, -0.6497, -0.0386, -0.6497, -0.03858, -0.47724, -0.0354, -0.47724, -0.03539, -0.27888, 0.00811, -0.27888, 0.00811, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34303, 0.01493, -0.34303, 0.01493, -1.669, 0.04815, -1.669, 0.04822, -2.93004, -0.01685, -2.93001, -0.01675, -3.49284, -0.09709, -3.49281, -0.09701, -2.75784, -0.06764, -2.75781, -0.06753, -2.36932, 0.05345, -2.36926, 0.05356, 0, 0, 0, 0, -1.01538, -0.04924, -1.01538, -0.0492, -2.25049, -0.05268, -2.25055, -0.05262, -3.1797, -0.13353, -3.1798, -0.13348, -3.46611, -0.10835, -3.46611, -0.10832, -2.97751, -0.07545, -2.97767, -0.07538, -2.13328, -0.15815, -2.1336, -0.15803, -0.84442, -0.10273, -0.84471, -0.10258, -0.41213, -0.00878, -0.41207, -0.00867, -0.12349, -0.00736, -0.12343], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.031, 0, 0.072, 1]}, {"time": 0.2333, "vertices": [-2.0868, -0.14654, -2.0868, -0.1465, -2.34441, -0.18817, -2.34441, -0.18813, -3.02466, -0.09286, -3.02466, -0.09283, -3.15877, -0.05558, -3.15877, -0.05557, -3.01592, -0.05292, -3.01592, -0.05278, -2.19262, -0.0361, -2.19256, -0.03598, -1.02648, 0.01569, -1.02648, 0.01574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07165, -0.01576, -0.07165, -0.01572, -0.61709, -0.09784, -0.61709, -0.09774, -0.79679, -0.03207, -0.79679, -0.03198, -0.79679, -0.03207, -0.79679, -0.03198, -1.65576, -0.10191, -1.65576, -0.10178, -1.18257, 0.00245, -1.18295, 0.00249, -2.76116, 0.19218, -2.76116, 0.19221, -3.40716, 0.05864, -3.40712, 0.05871, -3.24727, 0.08249, -3.24746, 0.08255, -2.43401, 0.134, -2.43446, 0.13404, -1.25167, 0.08049, -1.25227, 0.08049, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11615, 0.01233, -0.11615, 0.01233, -0.44977, 0.04776, -0.44984, 0.04777], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.031, 0, 0.072, 1]}, {"time": 0.2333, "offset": 100, "vertices": [-0.74738, 0.01523, -0.74728, 0.01525, -2.37379, 0.01686, -2.37344, 0.01693, -3.10464, -0.01247, -3.10413, -0.01239, -3.04942, 0.3027, -3.04926, 0.30274, -2.3601, 0.14383, -2.35994, 0.14387, -1.00144, 0.04222, -1.00134, 0.04224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16923, 0.01799, -0.16923, 0.01799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.72403, 0.03415, -0.72396, 0.03416, -2.07312, 0.02771, -2.07283, 0.02775, -3.02453, -0.06379, -3.02402, -0.06371, -3.19364, -0.04579, -3.19307, -0.04571, -2.73491, -0.15876, -2.73436, -0.15868, -1.71774, -0.05285, -1.71739, -0.0528, -0.41341, -0.06307, -0.41318, -0.06305, -0.3475, -0.04866, -0.34737, -0.04865], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.031, 0, 0.072, 1]}, {"time": 0.2333, "offset": 80, "vertices": [1.90906, 0.35522, 1.90735, 0.35517, 0.19897, -0.00739, 0.19263, -0.00752, -0.77454, 0.02877, -0.78076, 0.02853, -0.41699, 0.01549, -0.4231, 0.01534, -0.28088, 0.01044, -0.28699, 0.0103, -0.40454, 0.01503, -0.41064, 0.01485, -0.7688, 0.02856, -0.77502, 0.02827, 0.17188, -0.00637, 0.16553, -0.00661, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.54932, -0.05751, 1.54285, -0.05761, 2.1405, -0.06249, 2.13428, -0.06284, 1.94604, -0.05527, 1.93994, -0.05567, 2.09668, -0.06087, 2.09045, -0.06118, 1.46399, -0.05434, 1.45752, -0.05454, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 1.90906, 0.35522, 1.90735, 0.35517, 0.72375, -0.02686, 0.71753, -0.02707, -0.25037, 0.00931, -0.25671, 0.00919, 0.41382, -0.01535, 0.40759, -0.01556, -0.2616, 0.00972, -0.26794, 0.00952, 0.67957, -0.02522, 0.67334, -0.0254, 2.06519, -0.57381, 2.06311, -0.57386, 2.06519, -0.57381, 2.06311, -0.57386, 1.90906, 0.35522, 1.90735, 0.35517, 1.94275, -0.05515, 1.93665, -0.05558, 1.43079, -0.15906, 1.43103, -0.15913, 1.43079, -0.15906, 1.43103, -0.15913, 1.98938, 0.73397, 1.98254, 0.73364, 2.31799, -0.01292, 2.31262, -0.01326, 2.02429, -0.76541, 2.01758, -0.76575]}]}}}}}}}