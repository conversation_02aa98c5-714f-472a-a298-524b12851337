﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class EntitySpawnLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.EntitySpawnLayerEditorDataVersion);

            SaveLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            var regions = layerData.regions;

            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(horizontalRegionCount);
            writer.Write(verticalRegionCount);
            Utils.WriteVector2(writer, layerData.npcMoveRange);

            var strategy = layerData.strategy;
            SaveStrategy(writer, strategy);

            //save npc move info
            var npcWaypoints = layerData.entityMoveInfo.waypoints;
            writer.Write(npcWaypoints.Count);
            for (int i = 0; i < npcWaypoints.Count; ++i)
            {
                Utils.WriteVector2(writer, npcWaypoints[i]);
            }

            //save spawn points
            for (int i = 0; i < regions.Count; ++i)
            {
                var spawnPoints = regions[i].spawnPoints;
                //if region is visible
                writer.Write(regions[i].visible);
                writer.Write(regions[i].randomGenerator.seed);
                writer.Write(spawnPoints.Count);
                for (int s = 0; s < spawnPoints.Count; ++s)
                {
                    Utils.WriteVector2(writer, spawnPoints[s]);
                }
            }

            var grids = layerData.regionBrushIDs;
            for (int i = 0; i < verticalRegionCount; ++i)
            {
                for (int j = 0; j < horizontalRegionCount; ++j)
                {
                    writer.Write(grids[i, j]);
                }
            }
            var brushes = layerData.regionBrushes;
            writer.Write(brushes.Count);
            for (int i = 0; i < brushes.Count; ++i)
            {
                writer.Write(brushes[i].id);
                writer.Write(brushes[i].priority);
                Utils.WriteColor(writer, brushes[i].color);
            }

            Utils.WriteString(writer, layerData.spawnPointTypeReferenceNPCRegionLayerName);
        }

        void SaveStrategy(BinaryWriter writer, ISpawnPointGenerationStrategy strategy)
        {
            var type = strategy.strategy;
            writer.Write((int)type);
            if (type == SpawnPointGenerateStrategyType.Random)
            {
                var s = strategy as GenerateSpawnPointsRandomly;
                writer.Write(s.density);
                writer.Write(s.oneMeterHorizontalUnitCount);
                writer.Write(s.oneMeterVerticalUnitCount);
            }
            else if (type == SpawnPointGenerateStrategyType.Grid)
            {
                var s = strategy as GenerateSpawnPointsInGridLayout;
                Utils.WriteVector3(writer, s.startOffset);
                Utils.WriteVector2(writer, s.pointDeltaDistance);
                writer.Write(s.xOffset);
                writer.Write(s.randomRange);
            }
            else
            {
                Debug.Assert(false);
            }
        }
    }
}

#endif