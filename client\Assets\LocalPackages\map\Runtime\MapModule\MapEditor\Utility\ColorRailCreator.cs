﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public static class ColorRailCreator
    {
        public static void CreateRail(string prefabPath, Material mtl, float width, float length)
        {
            var mesh = new Mesh();
            mesh.vertices = new Vector3[]
            {
                new Vector3(0, 0, -width * 0.5f),
                new Vector3(0, 0, width * 0.5f),
                new Vector3(length, 0, width * 0.5f),
                new Vector3(length, 0, -width * 0.5f),
            };
            mesh.triangles = new int[]
            {
                0, 1, 2, 0, 2, 3
            };
            mesh.RecalculateNormals();

            string prefabName = Utils.GetPathName(prefabPath, false);
            var folderPath = Utils.GetFolderPath(prefabPath) + "/Meshis";
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }
            var meshPath = folderPath + "/" + prefabName + ".asset";
            meshPath = Utils.ConvertToUnityAssetsPath(meshPath);
            AssetDatabase.CreateAsset(mesh, meshPath);

            var obj = GameObject.CreatePrimitive(PrimitiveType.Cube);
            var boxCollider = obj.GetComponent<BoxCollider>();
            if (boxCollider != null)
            {
                GameObject.DestroyImmediate(boxCollider);
            }
            obj.GetComponent<MeshFilter>().sharedMesh = mesh;
            obj.GetComponent<MeshRenderer>().sharedMaterial = mtl;

            PrefabUtility.SaveAsPrefabAsset(obj, prefabPath);

            GameObject.DestroyImmediate(obj);
        }
    }
}


#endif