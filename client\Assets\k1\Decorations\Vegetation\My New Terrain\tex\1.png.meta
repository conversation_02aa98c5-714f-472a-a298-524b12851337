fileFormatVersion: 2
guid: 1e1d09b3101bbdd49ab8bf7a9952f66b
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: 1_0
      rect:
        serializedVersion: 2
        x: 122
        y: 1509
        width: 530
        height: 522
      alignment: 9
      pivot: {x: 0.62648386, y: 0.12353126}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1a7c8d2873adb1b4e8914b336b43ddce
      internalID: -1166879974
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_1
      rect:
        serializedVersion: 2
        x: 671
        y: 1506
        width: 560
        height: 503
      alignment: 9
      pivot: {x: 0.51723427, y: 0.07148054}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ce8f37116e2f4d847b72e171792fac86
      internalID: -2070637130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_2
      rect:
        serializedVersion: 2
        x: 1395
        y: 1629
        width: 618
        height: 373
      alignment: 9
      pivot: {x: 0.5156167, y: 0.068754055}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9e36fc4c00cd94c4cbcd87afb2549e11
      internalID: -1549568734
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_3
      rect:
        serializedVersion: 2
        x: 1351
        y: 1281
        width: 691
        height: 325
      alignment: 9
      pivot: {x: 0.5090113, y: 0.062117852}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 581587d6d634cfc41822c73d8c7f7127
      internalID: 197151953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_4
      rect:
        serializedVersion: 2
        x: 1309
        y: 631
        width: 688
        height: 631
      alignment: 9
      pivot: {x: 0.57012725, y: 0.01488289}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 59efecbe89099c44f9a68480b4281ff3
      internalID: -1515804759
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_8
      rect:
        serializedVersion: 2
        x: 1148
        y: 345
        width: 355
        height: 334
      alignment: 9
      pivot: {x: 0.5093705, y: 0.040529355}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c3cc9791fba375c4bb09cd55504d8b3c
      internalID: 710005817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_9
      rect:
        serializedVersion: 2
        x: 1179
        y: 0
        width: 317
        height: 323
      alignment: 9
      pivot: {x: 0.50547993, y: 0.22831294}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a58afbfad55374849b9897c0c7d6c08f
      internalID: -814947570
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_10
      rect:
        serializedVersion: 2
        x: 1492
        y: 0
        width: 556
        height: 638
      alignment: 9
      pivot: {x: 0.43523774, y: 0.12607493}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cddac3f8165013f4fb98812c14327eb2
      internalID: 1377025439
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_5
      rect:
        serializedVersion: 2
        x: 55
        y: 798
        width: 610
        height: 317
      alignment: 9
      pivot: {x: 0.51644164, y: 0.22214043}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7808129ad5cecbe4ab8d479247f83f03
      internalID: -2093267607
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_6
      rect:
        serializedVersion: 2
        x: 661
        y: 821
        width: 526
        height: 596
      alignment: 9
      pivot: {x: 0.5183483, y: 0.12215347}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c530e69fbb05f9f4a8f7994675c8f4a0
      internalID: 753035218
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_7
      rect:
        serializedVersion: 2
        x: 37
        y: 21
        width: 517
        height: 612
      alignment: 9
      pivot: {x: 0.49999994, y: 0.1215187}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b0b08e5a429715743a54a7e0467745ca
      internalID: -191896652
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_11
      rect:
        serializedVersion: 2
        x: 536
        y: 2
        width: 629
        height: 740
      alignment: 9
      pivot: {x: 0.49488518, y: 0.1130796}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d60ec953079544f45ae7eb48dc3526c7
      internalID: -1394285875
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 1_12
      rect:
        serializedVersion: 2
        x: 156
        y: 1177
        width: 348
        height: 298
      alignment: 9
      pivot: {x: 0.50000006, y: 0.10383104}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 92689813c75d44c4d9d481708f7b8100
      internalID: 1960756688
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      1_0: -1166879974
      1_1: -2070637130
      1_10: 1377025439
      1_11: -1394285875
      1_12: 1960756688
      1_2: -1549568734
      1_3: 197151953
      1_4: -1515804759
      1_5: -2093267607
      1_6: 753035218
      1_7: -191896652
      1_8: 710005817
      1_9: -814947570
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
