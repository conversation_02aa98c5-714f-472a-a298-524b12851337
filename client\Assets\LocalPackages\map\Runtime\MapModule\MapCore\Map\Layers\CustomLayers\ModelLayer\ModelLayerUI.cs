﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using TFW;

namespace TFW.Map
{
    [CustomEditor(typeof(ModelLayerLogic))]
    public class ModelLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as ModelLayerLogic;
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.modelLayerPrefabManager;
            prefabManager.selectPrefabEvent += OnSelectPrefab;

            mPrefab = prefabManager.selectedPrefab;

            mLogic.UpdateGizmoVisibilityState();
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.modelLayerPrefabManager;
                prefabManager.selectPrefabEvent -= OnSelectPrefab;

                mLogic.SetLayerBoundsVisible(false);
                mLogic.SetGridVisible(false);
                HideIndicator();
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();

            var currentEvent = Event.current;
            CheckOperationType(currentEvent);

            if (mLogic.operationType == ModelOperationType.kCreateObject)
            {
                if (mLogic.rotationSetting.Update())
                {
                    Repaint();
                }

                var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                var pos = map.FromScreenToWorldPosition(screenPos);
                UpdateIndicator(pos);

                if (currentEvent.type == EventType.MouseDown && currentEvent.alt == false)
                {
                    if (currentEvent.button == 0)
                    {
                        screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                        AddObject(screenPos);
                    }
                    Repaint();
                }

                HandleUtility.AddDefaultControl(0);
            }
            else if (mLogic.operationType == ModelOperationType.kRemoveObject)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.alt == false)
                {
                    if (currentEvent.button == 0)
                    {
                        var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                        RemoveObject(screenPos);
                    }
                    Repaint();
                }

                HandleUtility.AddDefaultControl(0);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                if (GUILayout.Button("Remove All Objects"))
                {
                    var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as ModelLayer;
                    if (EditorUtility.DisplayDialog("Warning", "This action can't be undone, are you sure?", "Yes", "No"))
                    {
                        layer.RemoveAllObjects();
                    }
                }

                if (mLogic.operationType == ModelOperationType.kCreateObject)
                {
                    mLogic.rotationSetting.Draw();

                    mLogic.operationType = (ModelOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operationType);
                    var editorMapData = Map.currentMap.data as EditorMapData;
                    var prefabManager = editorMapData.modelLayerPrefabManager;
                    prefabManager.Draw(PrefabGroupDisplayFlag.ShowColor | PrefabGroupDisplayFlag.CanRemovePrefab);
                }
                else
                {
                    mLogic.operationType = (ModelOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operationType);
                }

                var layerID = mLogic.layerID;
                var layerData = map.FindObject(layerID) as QuadTreeObjectLayerData;
                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, 0, null, null);

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Object Count", layerData.objectCount.ToString());
                EditorGUILayout.LabelField("Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Height", layerData.tileHeight.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void AddObject(Vector2 screenPos)
        {
            var modelTemplate = GetModelTemplate();
            if (modelTemplate != null)
            {
                var layerID = mLogic.layerID;
                var map = SLGMakerEditor.GetMap();
                var layer = map.GetMapLayerByID(layerID) as ModelLayer;
                var worldPos = map.FromScreenToWorldPosition(screenPos);
                var act = new ActionAddModel(layerID, map.nextCustomObjectID, modelTemplate.id, worldPos, mLogic.rotationSetting.rotation, Vector3.one);
                ActionManager.instance.PushAction(act);
            }
        }

        void RemoveObject(Vector2 screenPos)
        {
            var layerID = mLogic.layerID;
            var map = SLGMakerEditor.GetMap();
            var layer = map.GetMapLayerByID(layerID) as ModelLayer;
            var worldPos = map.FromScreenToWorldPosition(screenPos);
            var obj = layer.FindObjectAtPosition(worldPos, 0);
            if (obj != null)
            {
                var act = new ActionRemoveModel(obj.GetEntityID(), layerID);
                ActionManager.instance.PushAction(act);
            }
        }

        ModelTemplate GetModelTemplate()
        {
            var assetPath = AssetDatabase.GetAssetPath(mPrefab);
            if (assetPath.Length == 0)
            {
                return null;
            }
            return MapEditor.instance.CreateModelTemplate(assetPath, mPrefab, false);
        }

        string GetAssetGUID(GameObject prefab)
        {
            var assetPath = AssetDatabase.GetAssetPath(prefab);
            var guid = AssetDatabase.AssetPathToGUID(assetPath);
            return guid;
        }

        void ShowIndicator()
        {
            var indicator = SLGMakerEditor.instance.prefabIndicator;
            indicator.SetPrefab(mPrefab, null);
            if (mPrefab != null)
            {
                indicator.SetActive(true);
                //indicator.SetPosition(new Vector3(1000000, 0, 0));
            }
            else
            {
                indicator.SetActive(false);
            }
        }

        void UpdateIndicator(Vector3 pos)
        {
            ShowIndicator();

            var indicator = SLGMakerEditor.instance.prefabIndicator;
            indicator.SetPosition(pos);
            indicator.SetRotation(mLogic.rotationSetting.rotation);
        }

        void HideIndicator()
        {
            var indicator = SLGMakerEditor.instance.prefabIndicator;
            indicator.SetPrefab(null, null);
        }

        void OnSelectPrefab(GameObject prefab)
        {
            mPrefab = prefab;
            mLogic.selectedPrefabGUID = GetAssetGUID(prefab);
        }

        void CheckOperationType(Event currentEvent)
        {
            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 1)
            {
                if (mLogic.operationType == ModelOperationType.kCreateObject)
                {
                    mLogic.operationType = ModelOperationType.kSelectObject;
                    HideIndicator();
                }
                else if (mLogic.operationType == ModelOperationType.kSelectObject)
                {
                    mLogic.operationType = ModelOperationType.kCreateObject;
                }
                Repaint();
                SceneView.RepaintAll();
            }
        }

        public GameObject mPrefab;
        ModelLayerLogic mLogic;
    }
}


#endif