using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.Build.Reporting;

namespace CISystem.Android
{
    public static class Jenkins
    {
        public static void DisableExportAsGoogleAndroidProject()
        {
            EditorUserBuildSettings.exportAsGoogleAndroidProject = false;
        }
        
        public static void BuildAndroid()
        {
            var argsParser = new CommandLineParser(Environment.GetCommandLineArgs());
            var argsDic = argsParser.Options;

            if (argsDic.TryGetValue("-keystorePass", out var keystorePass)
                && argsDic.TryGetValue("-keystoreName", out var keystoreName)
                && argsDic.TryGetValue("-keyaliasName", out var keyaliasName)
                && argsDic.TryGetValue("-buildDir", out var buildDir)
                && argsDic.TryGetValue("-buildUrl", out var buildUrl)
                && argsDic.TryGetValue("-bundleVersionCode", out var bundleVersionCode)
                && int.TryParse(bundleVersionCode, out var bundleVersionCodeInt))
            {
                PlayerSettings.Android.bundleVersionCode = bundleVersionCodeInt;
                PlayerSettings.Android.keystoreName = keystoreName;
                PlayerSettings.Android.keyaliasName = keyaliasName;

                PlayerSettings.keystorePass = keystorePass;
                PlayerSettings.keyaliasPass = keystorePass;

                var apkFileName =
                    $"{PlayerSettings.GetApplicationIdentifier(BuildTargetGroup.Android)}_v{PlayerSettings.bundleVersion}.{PlayerSettings.Android.bundleVersionCode}.apk";
                var locationPathName = Path.Combine(buildDir, apkFileName);

                var buildPlayerOptions = new BuildPlayerOptions
                {
                    scenes = GetBuildScenes(),
                    locationPathName = locationPathName,
                    target = BuildTarget.Android,
                    options = BuildOptions.None
                };

                EditorUserBuildSettings.exportAsGoogleAndroidProject = false;
                
                BuildReport report = BuildPipeline.BuildPlayer(buildPlayerOptions);
                BuildSummary summary = report.summary;

                if (summary.result == BuildResult.Succeeded)
                {
                    UnityEngine.Debug.Log("Build succeeded: " + summary.totalSize + " bytes");
                    
                    Uri baseUri = new Uri(buildUrl);
                    Uri fullUri = new Uri(baseUri, apkFileName);
                    
                    JenkinsDingDingHelper.NotifyMarkDown($"## APK 打包成功: \n\n [点此下载 {apkFileName}]({fullUri.AbsoluteUri}) \n\n\n\n 包大小：{summary.totalSize / 1024f / 1024f} MB");
                }

                if (summary.result == BuildResult.Failed)
                {
                    UnityEngine.Debug.Log("Build failed");
                    JenkinsDingDingHelper.NotifyMarkDown("APK 打包失败");
                }
            }
            else
            {
                UnityEngine.Debug.LogError("Argument Error");
            }
        }

        static string[] GetBuildScenes()
        {
            List<string> scenes = new List<string>();

            foreach (EditorBuildSettingsScene e in EditorBuildSettings.scenes)
                if (e.enabled)
                    scenes.Add(e.path);

            return scenes.ToArray();
        }
    }
}