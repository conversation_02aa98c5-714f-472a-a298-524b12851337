﻿ 



 
 



/*
 * created by wzw at 2019.11.6
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //游戏运行时使用
    public sealed class DynamicObjectLayerView : MapLayerView
    {
        public DynamicObjectLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = new OptimizedGameObjectPool(layerData.map, OnCreateGameObject, layerData.map.isEditorMode, true, false);
        }

        void OnCreateGameObject(int modelTemplateID, int lod,System.Action<GameObject> callBack)
        {
            var modelTemplate = layerData.map.FindObject(modelTemplateID) as ModelTemplate;
            layerData.map.view.LoadGameObject(modelTemplate.GetLODPrefabPath(lod), callBack);
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            mObjectPool.OnDestroy();
            foreach (var obj in mViews)
            {
                Utils.DestroyObject(obj.Value);
            }
            mViews = null;
        }

        public void OnObjectActiveStateChange(IDynamicObjectData data, int lod)
        {
            if (data.IsObjActive())
            {
                ShowObject(data, lod);
            }
            else
            {
                HideObject(data, lod);
            }
        }

        public void OnObjectScaleChange(IDynamicObjectData data)
        {
            GameObject obj;
            mViews.TryGetValue(data.GetEntityID(), out obj);
            obj.transform.localScale = data.GetScale();
        }

        //显示地图对象的模型
        void ShowObject(IDynamicObjectData data, int lod)
        {
#if DEBUG
            Debug.Assert(mViews.ContainsKey(data.GetEntityID()) == false);
#endif
            var obj = mObjectPool.Require(data.GetModelTemplateID(), lod, false);
            mViews[data.GetEntityID()] = obj;

            obj.SetActive(true);
            var transform = obj.transform;
            transform.localPosition = data.GetPosition();
            transform.localRotation = data.GetRotation();
            var scale = data.GetScale();
            if (scale.x != 0)
            {
                transform.localScale = scale;
            }
        }

        //隐藏地图对象的模型
        void HideObject(IDynamicObjectData data, int lod)
        {
            var id = data.GetEntityID();
            GameObject obj;
            mViews.TryGetValue(id, out obj);
#if DEBUG
            Debug.Assert(obj != null);
#endif
            mViews.Remove(id);
            mObjectPool.Release(data.GetModelTemplateID(), lod, obj, false);
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        //该层中所有地图对象的视图
        Dictionary<int, GameObject> mViews = new Dictionary<int, GameObject>();
        //显示的障碍物
        Dictionary<int, GameObject> mObstacles = new Dictionary<int, GameObject>();
        OptimizedGameObjectPool mObjectPool;
    };
}