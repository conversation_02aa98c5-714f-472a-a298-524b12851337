﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System;
using System.Collections.Generic;


namespace TFW.Map
{
    //加载ReusableModel的任务
    public class MapObjectModelLoadingTask : ModelLoadingTask
    {
        public void Init(Map map, int taskID, Action<ModelBase, bool> loadFinishedCallback, float priority, int lod)
        {
            mMap = map;
            mTaskID = taskID;
            mLoadFinishedCallback = loadFinishedCallback;
            mPriority = priority;
            mLOD = lod;
        }

        public static MapObjectModelLoadingTask Require(Map map, int taskID, Action<ModelBase, bool> loadFinishedCallback, float priority, int lod)
        {
            var task = mPool.Require();
            task.Init(map, taskID, loadFinishedCallback, priority, lod);
            return task;
        }

        public static void Release(MapObjectModelLoadingTask task)
        {
            mPool.Release(task);
        }

        protected override ModelBase LoadImpl()
        {
            var mapObjectData = mMap.FindObject(mTaskID) as IMapObjectData;
#if UNITY_EDITOR
            Debug.Assert(mapObjectData != null, "object is removed!");
#endif
            var model = ReusableModel.Require(mMap, mapObjectData.GetModelTemplateID(), mLOD, mapObjectData.GetPosition(), mapObjectData.GetScale(), mapObjectData.GetRotation());
            return model;
        }

        public override void OnDestroy()
        {
            MapObjectModelLoadingTask.Release(this);
        }

        protected int mLOD;

        static ObjectPool<MapObjectModelLoadingTask> mPool = new ObjectPool<MapObjectModelLoadingTask>(1000, () => new MapObjectModelLoadingTask());
    }

    //目前地图上所有使用prefab的对象都可以使用这种视图
    public class ModelView : MapObjectView
    {
        public ModelView(IMapObjectData data, MapLayerView layerView)
            : base(data, layerView)
        {
            mOnLoadFinish = OnLoadFinish;
        }

        protected override void DestroyModelImpl()
        {
            if (mModel != null)
            {
                mModel.Release();
                mModel = null;
            }
        }

        //创建视图使用的模型
        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            if (newLOD < 0)
            {
                newLOD = 0;
            }
            Debug.Assert(mModel == null);

            var map = mLayerView.layerData.map;
            ModelBase model = null;

            bool active = data.IsObjActive();

            var template = map.GetEntityModelTemplate(data.GetEntityID());
            Debug.Assert(template != null, "invalid model template");

            bool isRenderTextureModel = data.HasFlag((int)ObjectFlag.kUseRenderTextureModel);

            if (active && !mUseModelPlaceholder)
            {
                if (isRenderTextureModel)
                {
                    model = RenderTextureModel.Require(template.id, data.GetPosition(), data.GetScale(), data.GetRotation());
                    model.transform.SetParent(mLayerView.root.transform, false);
                    OnModelReady(model);
                }
                else
                {
                    model = ReusableModel.Require(map, data.GetModelTemplateID(), newLOD, data.GetPosition(), data.GetScale(), data.GetRotation());
                    model.transform.SetParent(mLayerView.root.transform, false);
                    OnModelReady(model);
                }
            }
            else
            {
                model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
                OnModelPlaceholderReady(model);
            }

            return model;
        }

        //模型加载完后回调
        protected void OnLoadFinish(ModelBase model, bool success)
        {
            if (model.gameObject != null)
            {
                if (mModel != null)
                {
                    //切换占位模型
                    DestroyModel();
                }

                mModel = model;
                mModel.transform.SetParent(mLayerView.root.transform, false);
                OnModelReady(mModel);
            }
        }

        protected virtual void OnModelPlaceholderReady(ModelBase model) { }
        //当模型加载完后调用
        protected virtual void OnModelReady(ModelBase model)
        {
            var map = mLayerView.layerData.map;
            var data = map.FindObject(objectDataID) as IMapObjectData;
            data.OnInit(model.gameObject);

#if UNITY_EDITOR
            //发出模型加载的事件
            map.OnMapGameObjectLoaded(layerView.layerData, objectDataID, model.gameObject);
#endif
        }

        protected virtual int CalculateLOD()
        {
            var newLOD = layerView.layerData.currentLOD;
            if (mCurrentLOD == -1)
            {
                bool hasLODModel = CheckLODExistence(newLOD);
                if (!hasLODModel)
                {
                    newLOD = GetNearestLODSmallerThan(newLOD);
                }
            }
            else
            {
                if (newLOD > mCurrentLOD)
                {
                    //如果lod变大了,则只需要判断新的lod模型是否存在
                    bool hasLODModel = CheckLODExistence(newLOD);
                    if (!hasLODModel)
                    {
                        newLOD = mCurrentLOD;
                    }
                }
                else if (newLOD < mCurrentLOD)
                {
                    //如果lod变小了,则找到刚好比mCurrentLOD小的lod模型
                    bool hasLODModel = CheckLODExistence(newLOD);
                    if (!hasLODModel)
                    {
                        newLOD = GetNearestLODSmallerThan(mCurrentLOD);
                    }
                }
            }
            return newLOD;
        }

        public override void SetZoom(float newZoom, bool lodChanged)
        {
            if (lodChanged)
            {
                int newLOD = CalculateLOD();
                if (newLOD >= 0 && newLOD != mCurrentLOD)
                {
                    mCurrentLOD = newLOD;
                    OnLODChanged();
                }
            }
        }

        protected virtual void OnLODChanged()
        {
            DestroyModel();
            var map = mLayerView.layerData.map;
            var obj = map.FindObject(objectDataID);
            var d = obj as IMapObjectData;
            CreateModel(d, mCurrentLOD);
        }

        public override void SetActive(bool active)
        {
            if (active == false)
            {
                mCurrentLOD = -1;
            }

            DestroyModel();
            var map = mLayerView.layerData.map;
            var d = map.FindObject(objectDataID) as IMapObjectData;
            CreateModel(d, layerView.layerData.currentLOD);
        }

        protected virtual void OnWillDestroyModel() { }

        protected void DestroyModel()
        {
            OnWillDestroyModel();

            DestroyModelImpl();
        }

        //返回该视图使用的模型配置
        public ModelTemplate GetModelTemplate()
        {
            var map = mLayerView.layerData.map;
            return map.GetEntityModelTemplate(objectDataID);
        }

        //检测是否有新的lod对应的模型,如果没有,就不切换lod
        bool CheckLODExistence(int newLOD)
        {
            var map = mLayerView.layerData.map;
            var data = map.FindObject(objectDataID) as IMapObjectData;
            var modelTemplate = data.GetModelTemplate();
            return modelTemplate.ExistLOD(newLOD);
        }

        int GetNearestLODSmallerThan(int newLOD)
        {
            var map = mLayerView.layerData.map;
            var data = map.FindObject(objectDataID) as IMapObjectData;
            var modelTemplate = data.GetModelTemplate();
            return modelTemplate.GetNearestLODSmallerThan(newLOD);
        }

        public int currentLOD { get { return mCurrentLOD; } }

        int mCurrentLOD = -1;
        protected bool mUseModelPlaceholder = false;

        Action<ModelBase, bool> mOnLoadFinish;
    }
}
