﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //拼接地表层使用的数据
    public partial class VaryingTileSizeTerrainLayerData : MapObjectLayerData
    {
        public class BigTileData
        {
            public BigTileData(int id, int width, int height, int x, int y)
            {
                this.id = id;
                this.width = width;
                this.height = height;
                this.x = x;
                this.y = y;
            }

            //这个id是不保存到文件的,在编辑器下是next object id,在运行时是big tile data的index
            public int id;
            //普通tile大小的多少倍
            public int width = 1;
            public int height = 1;
            public int x;
            public int y;
        }

        public VaryingTileSizeTerrainLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, VaryingTileSizeTerrainTileData[] tiles) : base(header, config, map, null)
        {
            if (tiles == null)
            {
                int n = header.rows * header.cols;
                tiles = new VaryingTileSizeTerrainTileData[n];
            }
            mTiles = tiles;

#if UNITY_EDITOR
            mIsTileChanged = new bool[mTiles.Length];
#endif
            if (map.isEditorMode)
            {
                mLastViewport = map.viewport;
            }
            else
            {
                mLastViewport = new Rect(-10000, -10000, 0, 0);
            }
        }

        public override void OnDestroy()
        {
            base.OnDestroy();

            ClearAllTiles();
        }

        //x: tile的x坐标
        //y: tile的y坐标
        //tileIndex: tile使用的拼接prefab的索引
        //tileType: tile使用的拼接图集
        public VaryingTileSizeTerrainTileData SetTile(int x, int y, int tileIndex, int tileType, int tileID, BigTileData bigTileData)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows && tileIndex > 0)
            {
                var idx = y * mCols + x;

#if UNITY_EDITOR
                mIsTileChanged[idx] = true;
#endif
                bool createNewTile = false;
                VaryingTileSizeTerrainTileData tile = mTiles[idx];
                if (mTiles[idx] == null)
                {
                    //创建一个新tile
                    var tileDataID = map.nextCustomObjectID;
                    var pos = FromCoordinateToWorldPosition(x, y);
                    tile = new VaryingTileSizeTerrainTileData(tileDataID, map, pos, null, tileIndex, tileType, tileID, bigTileData);
                    createNewTile = true;
                }

                //设置绝对值
                tile.SetTile(tileIndex, tileType, tileID, bigTileData);
                
                //获取拼接后的tile使用的模型配置的id
                var modelTemplate = GetModelTemplate(tile.id, tile.type, tile.index, tile.tileID);
                if (modelTemplate == null)
                {
                    Debug.LogError($"terrain tile model template {tile.type}_{tile.index}_{tile.tileID} not found");
                }
                tile.SetModelTemplate(modelTemplate);

                if (createNewTile)
                {
                    AddObjectData(tile);
                }
                return tile;
            }
            return null;
        }

        protected override void OnAddObjectData(IMapObjectData data)
        {
            var coord = FromWorldPositionToCoordinate(data.GetPosition());
            var idx = coord.y * mCols + coord.x;
            mTiles[idx] = data as VaryingTileSizeTerrainTileData;
            //这个tile是否在视野内
            bool isVisible = false;
            if (!isLoading)
            {
                isVisible = IsInViewRange(coord.x, coord.y, map.viewport);
            }
            SetObjectActiveImpl(data, isVisible);

            var bigTile = mTiles[idx].bigTile;
            if (bigTile != null)
            {
                if (!mBigTiles.Contains(bigTile))
                {
                    mBigTiles.Add(bigTile);
                }
            }
        }

        //删除地图对象的数据
        protected override void OnRemoveObjectData(IMapObjectData data)
        {
            var coord = FromWorldPositionToCoordinate(data.GetPosition());
            var idx = coord.y * mCols + coord.x;
            var bigTile = mTiles[idx].bigTile;
            if (bigTile != null)
            {
                mBigTiles.Remove(bigTile);
            }
            mTiles[idx] = null;
        }

        //删除tile的数据
        public void ClearTile(int x, int y)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                var idx = y * mCols + x;
#if UNITY_EDITOR
                mIsTileChanged[idx] = true;
#endif

                if (mTiles[idx] != null)
                {
                    RemoveObjectData(mTiles[idx].id);
                }
            }
        }

        public IMapObjectData GetObject(int x, int y)
        {
            return GetTile(x, y);
        }

        //获取tile的数据
        public VaryingTileSizeTerrainTileData GetTile(int x, int y)
        {
            VaryingTileSizeTerrainTileData tile = null;
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                var idx = y * mCols + x;
                tile = mTiles[idx];
            }
            return tile;
        }

        //根据tile的拼接情况来获取地表tile使用哪个prefab
        ModelTemplate GetModelTemplate(int tileDataID, int tileType, int tileIndex, int tileID)
        {
            if (tileIndex == 0)
            {
                return null;
            }

            string prefabPath = null;
            if (map.isEditorMode)
            {
#if UNITY_EDITOR
                var prefabManager = (map.data as EditorMapData).editorVaryingTileSizeTerrainPrefabManager;
                var group = prefabManager.GetGroupByID(tileType);
                prefabPath = group.GetPrefabPathFromItemID(tileID);
#endif
            }
            else
            {
                var prefabManager = map.data.varyingTileSizeTerrainPrefabManager;
                prefabPath = prefabManager.GetPrefabByID(tileType, tileIndex);
            }
            
            if (!string.IsNullOrEmpty(prefabPath))
            {
                var modelTemplate = map.GetOrCreateModelTemplate(tileDataID, prefabPath, false, MapModule.preloadGroundLayerTile);
                Debug.Assert(modelTemplate != null);
                return modelTemplate;
            }
            return null;
        }

        public override bool isGameLayer => true;

        protected override bool IsAbleToAdd(IMapObjectData objectData)
        {
            return true;
        }

        protected override void OnRotationChange(IMapObjectData objectData)
        {
            Debug.Assert(false, "terrain tile rotation can't change");
        }

        protected override void OnScaleChange(IMapObjectData objectData)
        {
            Debug.Assert(false, "terrain tile scale can't change");
        }

        //改变layer的大小
        public void Resize(int newSize, bool useLayerOffset)
        {
            //only valid for map editor
            Debug.Assert(map.isEditorMode == true);
            int oldSize = horizontalTileCount;
            if (newSize > oldSize)
            {
                int offset = (newSize - oldSize) / 2;
                VaryingTileSizeTerrainTileData[] newTiles = new VaryingTileSizeTerrainTileData[newSize * newSize];
                for (int i = 0; i < oldSize; ++i)
                {
                    for (int j = 0; j < oldSize; ++j)
                    {
                        //只是简单的移动tile,并不新增加tile
                        int oldIndex = i * oldSize + j;
                        int newIndex = (i + offset) * newSize + j + offset;
                        newTiles[newIndex] = mTiles[oldIndex];
                    }
                }
                mTiles = newTiles;
                mRows = newSize;
                mCols = newSize;
                var mapWidth = map.mapWidth;
                var mapHeight = map.mapHeight;
                var layerWidth = newSize * mTileWidth;
                var layerHeight = newSize * mTileHeight;
                var ox = (mapWidth - layerWidth) * 0.5f;
                var oz = (mapHeight - layerHeight) * 0.5f;
                mLayerOrigin = new Vector3(ox, 0, oz);
            }
            else
            {
                int offset = (oldSize - newSize) / 2;
                VaryingTileSizeTerrainTileData[] newTiles = new VaryingTileSizeTerrainTileData[newSize * newSize];
                for (int i = 0; i < newSize; ++i)
                {
                    for (int j = 0; j < newSize; ++j)
                    {
                        //只是简单的移动tile,并不新增加tile
                        int newIndex = i * newSize + j;
                        int oldIndex = (i + offset) * oldSize + j + offset;
                        newTiles[newIndex] = mTiles[oldIndex];
                    }
                }
                mTiles = newTiles;
                mRows = newSize;
                mCols = newSize;
                var mapWidth = map.mapWidth;
                var mapHeight = map.mapHeight;
                var layerWidth = newSize * mTileWidth;
                var layerHeight = newSize * mTileHeight;
                var ox = (mapWidth - layerWidth) * 0.5f;
                var oz = (mapHeight - layerHeight) * 0.5f;
                mLayerOrigin = new Vector3(ox, 0, oz);
            }

            if (!useLayerOffset)
            {
                mLayerOrigin = Vector3.zero;
            }
        }

        public override IMapObjectData FindObjectAtPosition(Vector3 pos, float radius)
        {
            throw new System.NotImplementedException();
        }

        public override void RefreshObjectsInViewport()
        {
            UpdateViewport(map.viewport, 0);
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }

            if (mLastViewport != newViewport)
            {
                var oldViewRect = GetViewRect(mLastViewport);
                var newViewRect = GetViewRect(newViewport);
                UpdateViewRect(oldViewRect, newViewRect);
            }

            mLastViewport = newViewport;

            return lodChanged;
        }

        void SetObjectVisibility(int x, int y, bool visible, int lod)
        {
            var objectData = GetObject(x, y);
            if (objectData != null)
            {
                SetObjectActive(objectData, visible, lod);
            }
        }

        protected bool UpdateViewRect(Rect2D oldViewRect, Rect2D newViewRect)
        {
            if (oldViewRect != newViewRect)
            {
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (!newViewRect.Contains(j, i))
                        {
                            //物体不在新的视野中,隐藏它
                            SetObjectVisibility(j, i, false, currentLOD);
                        }
                    }
                }

                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (!oldViewRect.Contains(j, i))
                        {
                            //物体不在旧的视野中,显示它
                            SetObjectVisibility(j, i, true, currentLOD);
                        }
                    }
                }

                return true;
            }

            return false;
        }

        public Rect2D GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new Rect2D(startCoord.x, startCoord.y, endCoord.x, endCoord.y);
        }

        public bool IsInViewRange(int x, int y, Rect viewport)
        {
            var viewRect = GetViewRect(viewport);
            return viewRect.Contains(x, y);
        }

        public virtual bool IsOneTileLOD(int lod) { return false; }

        public override void GetObjectInBounds(Bounds bounds, List<IMapObjectData> objects)
        {
            Debug.Assert(false, "todo");
        }

        public void ResetIsChangedFlags()
        {
#if UNITY_EDITOR
            for (int i = 0; i < mIsTileChanged.Length; ++i)
            {
                mIsTileChanged[i] = false;
            }
#endif
        }

        public void ClearAllTiles()
        {
            if (mTiles != null)
            {
                for (int i = 0; i < mTiles.Length; ++i)
                {
                    if (mTiles[i] != null)
                    {
                        map.DestroyObject(mTiles[i]);
                        mTiles[i] = null;
                    }
                }
            }
            mBigTiles.Clear();
        }

        public void SetSize(int newHorizontalTileCount, int newVerticalTileCount) {
#if UNITY_EDITOR
            ClearAllTiles();
            mTiles = new VaryingTileSizeTerrainTileData[newHorizontalTileCount * newVerticalTileCount];
            mIsTileChanged = new bool[mTiles.Length];
            for (int i = 0; i < mIsTileChanged.Length; ++i)
            {
                mIsTileChanged[i] = true;
            }
            mRows = newVerticalTileCount;
            mCols = newHorizontalTileCount;
#endif
        }

        public int GetValidTileCount()
        {
            int n = 0;
            for (int i = 0; i < tiles.Length; ++i)
            {
                if (tiles[i] != null)
                {
                    ++n;
                }
            }
            return n;
        }

        public Rect lastViewport { get { return mLastViewport; } }
        public bool useGeneratedLOD { set { mUseGeneratedLOD = value; } get { return mUseGeneratedLOD; } }
        public VaryingTileSizeTerrainTileData[] tiles { get { return mTiles; } }
        public List<BigTileData> bigTiles { get { return mBigTiles; } }

        //地表层上所有的tile数据,可以从编辑器导出,也可以在游戏运行时动态设置
        protected VaryingTileSizeTerrainTileData[] mTiles;
        protected Rect mLastViewport;
        protected float mLastZoom;
        bool mUseGeneratedLOD = true;

        protected List<BigTileData> mBigTiles = new List<BigTileData>();

#if UNITY_EDITOR
        bool[] mIsTileChanged;
#endif
    }
}
