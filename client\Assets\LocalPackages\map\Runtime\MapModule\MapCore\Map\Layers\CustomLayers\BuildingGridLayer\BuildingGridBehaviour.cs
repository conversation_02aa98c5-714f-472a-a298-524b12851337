﻿#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    class BuildingGridBehaviour : MonoBehaviour
    {
        public GameObject plane { get; set; }

        void Update()
        {
            float height = 0.2f;
            var pos = transform.position;
            pos.y = height;
            transform.position = pos;
            plane.transform.position = transform.position;
        }
    }
}

#endif