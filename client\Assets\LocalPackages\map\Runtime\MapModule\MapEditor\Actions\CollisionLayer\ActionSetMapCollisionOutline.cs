﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionSetMapCollisionOutline : EditorAction
    {
        public ActionSetMapCollisionOutline(int layerID, int dataID, PrefabOutlineType type, List<Vector3> outline)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mOutlineType = type;
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            mOldOutlineVertices = data.GetOutlineVerticesCopy(type);
            mNewOutlineVertices = outline;
        }

        public override bool Do()
        {
            return SetOutline(mNewOutlineVertices);
        }

        public override bool Undo()
        {
            return SetOutline(mOldOutlineVertices);
        }

        bool SetOutline(List<Vector3> outlineVertices)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetOutline(mOutlineType, mDataID, outlineVertices);
            return true;
        }

        int mLayerID;
        int mDataID;
        PrefabOutlineType mOutlineType;
        List<Vector3> mOldOutlineVertices;
        List<Vector3> mNewOutlineVertices;
    }
}

#endif