﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    [CustomEditor(typeof(MapListSetting))]
    public class MapListSettingUI : UnityEditor.Editor
    {
        string[] mMapNames;

        public override void OnInspectorGUI()
        {
            var setting = target as MapListSetting;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.IntField("Map Count", setting.maps.Length);
            if (GUILayout.Button("Change Count"))
            {
                var dlg = EditorUtils.CreateInputDialog("Change Map Count");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Count", "", setting.maps.Length.ToString()),
                };
                dlg.Show(items, OnClickChangeMapCount);
            }
            EditorGUILayout.EndHorizontal();

            if (setting.HasInvalidIDs())
            {
                setting.ResetIDs();
                EditorUtility.SetDirty(setting);
                AssetDatabase.SaveAssets();
            }

            for (int i = 0; i < setting.maps.Length; ++i)
            {
                DrawMapSetting(setting.maps[i]);
            }

            DrawSelectActiveMapGUI();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Create Map List File"))
            {
                CreateMapListFile();
            }
            if (GUILayout.Button("Select Map List File"))
            {
                string configFilePath = Utils.TryToGetValidConfigFilePath();
                var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
                var fullPath = mapConfig.mapListDir + "/" + MapCoreDef.MAP_LIST_FILE_PATH;
                var textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(fullPath);
                Selection.activeObject = textAsset;
            }
            EditorGUILayout.EndHorizontal();
        }

        bool OnClickChangeMapCount(List<InputDialog.Item> parameters)
        {
            int newCount;
            bool ok = int.TryParse((parameters[0] as InputDialog.StringItem).text, out newCount);
            if (!ok)
            {
                return false;
            }

            var setting = target as MapListSetting;
            setting.SetMapCount(newCount);
            return true;
        }

        void DrawMapSetting(MapInfo mi)
        {
            ++EditorGUI.indentLevel;
            EditorGUILayout.BeginVertical("GroupBox");
            EditorGUI.BeginChangeCheck();
            mi.mapID = EditorGUILayout.IntField("Map ID", mi.mapID);
            mi.mapName = EditorGUILayout.TextField("Map Name", mi.mapName);
            mi.isMainMap = EditorGUILayout.Toggle(new GUIContent("Is Main Map", "是否是大地图，一般非副本地图勾选这个选项。同时只能有一个main map被加载到内存中，副本地图数量则不限"), mi.isMainMap);
            mi.isPlaceholderMap = EditorGUILayout.Toggle(new GUIContent("Is Placeholder Map", "是否是空地图，p3 Dungeon使用"), mi.isPlaceholderMap);
            mi.mapDataFolder = EditorGUILayout.ObjectField("Map Data Folder", mi.mapDataFolder, typeof(DefaultAsset), false, null) as DefaultAsset;
            mi.mapOrigin = EditorGUILayout.Vector3Field("Map Origin", mi.mapOrigin);
            mi.loadAtStartup = EditorGUILayout.Toggle("Load At Startup", mi.loadAtStartup);
            mi.enableUpdateCameraFarClipPlane = EditorGUILayout.Toggle("Update Camera Far Clip Plane", mi.enableUpdateCameraFarClipPlane);
            if (mi.enableUpdateCameraFarClipPlane)
            {
                EditorGUI.indentLevel++;
                mi.farClipPlaneSetting = DrawCameraClipPlaneSetting("Far Clip Setting", mi.farClipPlaneSetting);
                EditorGUI.indentLevel--;
            }
            mi.enableUpdateCameraNearClipPlane = EditorGUILayout.Toggle("Update Camera Near Clip Plane", mi.enableUpdateCameraNearClipPlane);
            if (mi.enableUpdateCameraNearClipPlane)
            {
                EditorGUI.indentLevel++;
                mi.nearClipPlaneSetting = DrawCameraClipPlaneSetting("Near Clip Setting", mi.nearClipPlaneSetting);
                EditorGUI.indentLevel--;
            }
            mi.clampBorder = EditorGUILayout.Toggle("Camera Clamp To Border", mi.clampBorder);
            mi.defaultCameraSetting = EditorGUILayout.ObjectField("Camera Setting File", mi.defaultCameraSetting, typeof(TextAsset), false, null) as TextAsset;
            mi.cameraZoomCurveSetting = EditorGUILayout.ObjectField("Camera Zoom Curve Setting", mi.cameraZoomCurveSetting, typeof(CameraZoomCurveSetting), false, null) as CameraZoomCurveSetting;
            bool changed = EditorGUI.EndChangeCheck();
            if (changed)
            {
                EditorUtility.SetDirty(target);
                AssetDatabase.SaveAssets();
            }
            EditorGUILayout.EndVertical();
            --EditorGUI.indentLevel;
        }

        CameraClipPlaneSetting DrawCameraClipPlaneSetting(string name, CameraClipPlaneSetting oldSetting)
        {
            return EditorGUILayout.ObjectField(new GUIContent(name, "Camera Clip Plane Setting"), oldSetting, typeof(CameraClipPlaneSetting), false, null) as CameraClipPlaneSetting;
        }

        void DrawSelectActiveMapGUI()
        {
            var setting = target as MapListSetting;
            if (setting.maps.Length == 0)
            {
                return;
            }

            if (mMapNames == null || mMapNames.Length != setting.maps.Length)
            {
                mMapNames = new string[setting.maps.Length];
            }

            for (int i = 0; i < mMapNames.Length; ++i)
            {
                mMapNames[i] = setting.maps[i].mapName;
            }

            if (string.IsNullOrEmpty(setting.activeMapName))
            {
                setting.activeMapName = setting.maps[0].mapName;
            }

            int index = -1;
            for (int i = 0; i < mMapNames.Length; ++i)
            {
                if (mMapNames[i] == setting.activeMapName)
                {
                    index = i;
                    break;
                }
            }
            if (index == -1)
            {
                setting.activeMapName = setting.maps[0].mapName;
                index = 0;
            }

            bool dirty = false;
            int newIndex = EditorGUILayout.Popup("Active Map", index, mMapNames);
            if (newIndex != index)
            {
                setting.activeMapName = mMapNames[newIndex];
                dirty = true;
            }

            if (dirty)
            {
                EditorUtility.SetDirty(setting);
                AssetDatabase.SaveAssets();
            }
        }

        string CheckValidation()
        {
            var setting = target as MapListSetting;
            if (setting.maps.Length == 0)
            {
                return "no map!";
            }
            List<int> usedMapIDs = new List<int>();
            for (int i = 0; i < setting.maps.Length; ++i)
            {
                var mi = setting.maps[i];
                if (mi.defaultCameraSetting == null)
                {
                    return $"no camera setting for map {i}";
                }
                if (mi.mapDataFolder == null)
                {
                    return $"no map data folder for map {i}";
                }
                if (string.IsNullOrEmpty(mi.mapName))
                {
                    return $"invalid map name for map {i}";
                }
                if (mi.mapID == 0)
                {
                    return $"map id can't be 0!";
                }

                if (usedMapIDs.Contains(mi.mapID))
                {
                    return $"duplicated map id {mi.mapID} found!";
                }
                usedMapIDs.Add(mi.mapID);
            }

            return "";
        }

        void CreateMapListFile()
        {
            string errorMsg = CheckValidation();
            if (!string.IsNullOrEmpty(errorMsg))
            {
                EditorUtility.DisplayDialog("Error", errorMsg, "OK");
            }

            var setting = target as MapListSetting;
            if (setting.maps == null)
            {
                EditorUtility.DisplayDialog("Error", "Invalid map settings!", "OK");
                return;
            }
            List<object> mapList = new List<object>();
            for (int i = 0; i < setting.maps.Length; ++i)
            {
                object mapEntry = CreateMapSettingObject(setting.maps[i]);
                mapList.Add(mapEntry);
            }

            Dictionary<string, object> root = new Dictionary<string, object>();
            root["maps"] = mapList;
            root["active_map"] = setting.activeMapName;
            var str = JSONParser.Serialize(root);

            string configFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            var fullPath = mapConfig.mapListDir + "/" + MapCoreDef.MAP_LIST_FILE_PATH;
            File.WriteAllText(fullPath, str);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        object CreateMapSettingObject(MapInfo mi)
        {
            Dictionary<string, object> mapObject = new Dictionary<string, object>();
            mapObject.Add("id", mi.mapID);
            mapObject.Add("name", mi.mapName);
            mapObject.Add("is_main_map", mi.isMainMap);
            mapObject.Add("is_placeholder_map", mi.isPlaceholderMap);
            mapObject.Add("file_folder", AssetDatabase.GetAssetPath(mi.mapDataFolder));
            List<object> vec3Val = new List<object>();
            vec3Val.Add(mi.mapOrigin.x);
            vec3Val.Add(mi.mapOrigin.y);
            vec3Val.Add(mi.mapOrigin.z);
            mapObject.Add("origin", vec3Val);
            mapObject.Add("load_at_startup", mi.loadAtStartup);
            mapObject.Add(MapCoreDef.MAP_ENABLE_UPDATE_CLIP_PLANE, mi.enableUpdateCameraFarClipPlane);
            mapObject.Add(MapCoreDef.MAP_ENABLE_UPDATE_NEAR_CLIP_PLANE, mi.enableUpdateCameraNearClipPlane);
            mapObject.Add(MapCoreDef.MAP_CAMERA_NEAR_CLIP_PLANE_SETTING_PATH, AssetDatabase.GetAssetPath(mi.nearClipPlaneSetting));
            mapObject.Add(MapCoreDef.MAP_CAMERA_FAR_CLIP_PLANE_SETTING_PATH, AssetDatabase.GetAssetPath(mi.farClipPlaneSetting));
            mapObject.Add(MapCoreDef.MAP_CAMERA_ZOOM_CURVE_SETTING_PATH, AssetDatabase.GetAssetPath(mi.cameraZoomCurveSetting));
            mapObject.Add(MapCoreDef.MAP_CAMERA_CLAMP_BORDER_NAME, mi.clampBorder);
            string filePath = AssetDatabase.GetAssetPath(mi.defaultCameraSetting);
            mapObject.Add("default_camera_setting", Utils.GetPathName(filePath, false));
            return mapObject;
        }

        public static MapListSetting CreateMapListSettingFromMapListFile(string filePath)
        {
            string text = File.ReadAllText(filePath);
            Dictionary<string, object> root = JSONParser.Deserialize(text) as Dictionary<string, object>;
            List<object> maps = root["maps"] as List<object>;
            string activeMap = "";
            if (root.ContainsKey("active_map")) {
                activeMap = root["active_map"] as string;
            }
            MapListSetting setting = ScriptableObject.CreateInstance<MapListSetting>();
            setting.maps = CreateMapInfoList(maps);
            setting.activeMapName = activeMap;
            if (string.IsNullOrEmpty(activeMap) && setting.maps.Length > 0)
            {
                setting.activeMapName = setting.maps[0].mapName;
            }

            string folder = Utils.GetFolderPath(filePath);
            string mapListSettingPath = $"{folder}/map_list_setting.asset";
            AssetDatabase.CreateAsset(setting, mapListSettingPath);
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            return setting;
        }

        static MapInfo[] CreateMapInfoList(List<object> maps)
        {
            MapInfo[] allInfo = new MapInfo[maps.Count];

            for (int i = 0; i < maps.Count; ++i)
            {
                var mapInfo = maps[i] as Dictionary<string, object>;
                MapInfo info = new MapInfo();
                allInfo[i] = info;
                info.mapID = JsonHelper.ReadInt(mapInfo, "id", i + 1);
                info.mapName = JsonHelper.ReadString(mapInfo, "name", "");
                info.isMainMap = JsonHelper.ReadBoolean(mapInfo, "is_main_map", false);
                info.isPlaceholderMap = JsonHelper.ReadBoolean(mapInfo, "is_placeholder_map", false);
                info.mapDataFolder = AssetDatabase.LoadAssetAtPath<DefaultAsset>(mapInfo["file_folder"] as string);
                info.mapOrigin = JsonHelper.ReadVector3(mapInfo, "origin", Vector3.zero);
                info.loadAtStartup = JsonHelper.ReadBoolean(mapInfo, "load_at_startup", true);
                info.enableUpdateCameraFarClipPlane = JsonHelper.ReadBoolean(mapInfo, MapCoreDef.MAP_ENABLE_UPDATE_CLIP_PLANE, true);
                info.enableUpdateCameraNearClipPlane = JsonHelper.ReadBoolean(mapInfo, MapCoreDef.MAP_ENABLE_UPDATE_NEAR_CLIP_PLANE, false);
                string nearClipPlaneSettingPath = JsonHelper.ReadString(mapInfo, MapCoreDef.MAP_CAMERA_NEAR_CLIP_PLANE_SETTING_PATH, "");
                if (!string.IsNullOrEmpty(nearClipPlaneSettingPath))
                {
                    info.nearClipPlaneSetting = AssetDatabase.LoadAssetAtPath<CameraClipPlaneSetting>(nearClipPlaneSettingPath);
                }
                string farClipPlaneSettingPath = JsonHelper.ReadString(mapInfo, MapCoreDef.MAP_CAMERA_FAR_CLIP_PLANE_SETTING_PATH, "");
                if (!string.IsNullOrEmpty(farClipPlaneSettingPath))
                {
                    info.farClipPlaneSetting = AssetDatabase.LoadAssetAtPath<CameraClipPlaneSetting>(farClipPlaneSettingPath);
                }
                string cameraZoomCurveSettingPath = JsonHelper.ReadString(mapInfo, MapCoreDef.MAP_CAMERA_ZOOM_CURVE_SETTING_PATH, "");
                if (!string.IsNullOrEmpty(cameraZoomCurveSettingPath)) {
                    info.cameraZoomCurveSetting = AssetDatabase.LoadAssetAtPath<CameraZoomCurveSetting>(cameraZoomCurveSettingPath);
                }
                info.clampBorder = JsonHelper.ReadBoolean(mapInfo, MapCoreDef.MAP_CAMERA_CLAMP_BORDER_NAME, true);
                string settingName = mapInfo["default_camera_setting"] as string;
                var mapconfigFilePath = Utils.TryToGetValidConfigFilePath();
                var mapConfig = MapConfig.CreateFromFile(mapconfigFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
                string cameraSettingFilePath = $"{mapConfig.configResDir}{settingName}.bytes";
                info.defaultCameraSetting = AssetDatabase.LoadAssetAtPath<TextAsset>(cameraSettingFilePath);
            }
            return allInfo;
        }
    }
}

#endif