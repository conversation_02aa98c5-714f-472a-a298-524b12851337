﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class CurveRegionCreator
    {
        public void HandleEditControlPointFunction(Event e, Vector3 worldPos)
        {
            if ((e.type == EventType.MouseDown || e.type == EventType.MouseDrag) && e.button == 0 && e.alt == false)
            {
                if (e.type == EventType.MouseDown)
                {
                    Pick(worldPos);
                }
                MoveControlPoint(worldPos);
                   
                SceneView.RepaintAll();
            }

            //draw tangent line
            if (mSelectedControlPointIndex >= 0)
            {
                var t = GetSelectedTerritory();
                var controlPoint = t.controlPoints[mSelectedControlPointIndex];
                Handles.DrawLine(controlPoint.position, controlPoint.tangent0);
                Handles.DrawLine(controlPoint.position, controlPoint.tangent1);
            }

            HandleUtility.AddDefaultControl(0);
        }

        int TryPickControlPoint(Vector3 worldPos, Territory t)
        {
            float pickRadius2 = mInput.settings.vertexDisplayRadius;
            pickRadius2 *= pickRadius2;
            var controlPoints = t.controlPoints;
            for (int i = 0; i < controlPoints.Count; ++i)
            {
                var d = worldPos - controlPoints[i].position;
                if (d.sqrMagnitude <= pickRadius2)
                {
                    return i;
                }
            }
            return -1;
        }

        void PickControlPoint(Vector3 worldPos, Territory t)
        {
            mMover.Reset();
            mSelectedControlPointIndex = TryPickControlPoint(worldPos, t);
        }

        void MoveControlPoint(Vector3 worldPos)
        {
            if (mSelectedTerritoryID > 0 && mSelectedControlPointIndex >= 0)
            {
                mMover.Update(worldPos);

                var delta = mMover.GetDelta();
                if (delta != Vector3.zero)
                {
                    var territory = GetSelectedTerritory();
                    GetControlPointsWithSameCoordinates(territory.controlPoints[mSelectedControlPointIndex].position);
                    if (mSelectedTangentPointIndex >= 0)
                    {
                        var type = TangentMoveType.RotateAndScale;
                        for (int i = 0; i < mControlPointsWithSameCoordinates.Count; ++i)
                        {
                            mControlPointsWithSameCoordinates[i].MoveTangent(mSelectedTangentPointIndex, delta, type);
                        }
                    }
                    else
                    {
                        for (int i = 0; i < mControlPointsWithSameCoordinates.Count; ++i)
                        {
                            var controlPoint = mControlPointsWithSameCoordinates[i];
                            controlPoint.MoveControlPoint(delta);
                            controlPoint.MoveTangent(0, delta, TangentMoveType.Free);
                            controlPoint.MoveTangent(1, delta, TangentMoveType.Free);
                        }
                    }
                    
                    Generate("", 0, false, false, Map.currentMap.mapWidth, Map.currentMap.mapHeight, 3, 3);
                }
            }
        }

        //todo optimize this
        void GetControlPointsWithSameCoordinates(Vector3 pos)
        {
            mControlPointsWithSameCoordinates.Clear();
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                var controlPoints = mTerritories[i].controlPoints;
                for (int j = 0; j < controlPoints.Count; ++j)
                {
                    if (Utils.Approximately(controlPoints[j].position, pos, 0.01f))
                    {
                        mControlPointsWithSameCoordinates.Add(controlPoints[j]);
                    }
                }
            }
        }

        void Pick(Vector3 worldPos)
        {
            mSelectedTangentPointIndex = -1;
            var selectedTerritory = GetSelectedTerritory();
            if (selectedTerritory != null && mSelectedControlPointIndex >= 0)
            {
                int idx = TryPickControlPoint(worldPos, selectedTerritory);
                if (idx < 0)
                {
                    PickTangent(worldPos, selectedTerritory);
                }
                else
                {
                    mSelectedTangentPointIndex = -1;
                }
            }

            if (mSelectedTangentPointIndex < 0)
            {
                SetSelectedTerritory(null);
                for (int i = 0; i < mTerritories.Count; ++i)
                {
                    PickControlPoint(worldPos, mTerritories[i]);
                    int selectedControlPointIndex = mSelectedControlPointIndex;
                    if (selectedControlPointIndex >= 0)
                    {
                        SetSelectedTerritory(mTerritories[i]);
                        mSelectedControlPointIndex = selectedControlPointIndex;
                        mTerritories[i].ShowTangent(selectedControlPointIndex);
                        break;
                    }
                }
            }
        }

        void PickTangent(Vector3 worldPos, Territory t)
        {
            mMover.Reset();
            mSelectedTangentPointIndex = -1;
            float pickRadius2 = mInput.settings.vertexDisplayRadius;
            pickRadius2 *= pickRadius2;
            var controlPoint = t.controlPoints[mSelectedControlPointIndex];
            var d = worldPos - controlPoint.tangent0;
            if (d.sqrMagnitude <= pickRadius2)
            {
                mSelectedTangentPointIndex = 0;
            }
            else
            {
                d = worldPos - controlPoint.tangent1;
                if (d.sqrMagnitude <= pickRadius2)
                {
                    mSelectedTangentPointIndex = 1;
                }
            }
        }

        void SetSelectedTerritory(Territory t)
        {
            var oldSpline = GetSelectedTerritory();
            if (oldSpline != null)
            {
                oldSpline.HideTangent();
            }

            if (t != null)
            {
                mSelectedTerritoryID = t.regionID;
            }
            else
            {
                mSelectedTerritoryID = 0;
            }
            mSelectedControlPointIndex = -1;
            mSelectedTangentPointIndex = -1;
        }

        Territory GetTerritory(int id)
        {
            if (id == 0)
            {
                return null;
            }

            for (int i = 0; i < mTerritories.Count; ++i)
            {
                if (mTerritories[i].regionID == id)
                {
                    return mTerritories[i];
                }
            }
            return null;
        }

        Territory GetSelectedTerritory()
        {
            return GetTerritory(mSelectedTerritoryID);
        }

        int mSelectedTerritoryID = 0;
        int mSelectedControlPointIndex = -1;
        int mSelectedTangentPointIndex = -1;
        MouseMover mMover = new MouseMover();
        List<ControlPoint> mControlPointsWithSameCoordinates = new List<ControlPoint>();
    }
}

#endif