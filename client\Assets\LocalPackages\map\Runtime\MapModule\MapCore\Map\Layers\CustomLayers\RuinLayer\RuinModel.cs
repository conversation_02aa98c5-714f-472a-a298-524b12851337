﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RuinModel : ModelBase
    {
        public RuinModel(RuinData data, RuinSetting ruinType, Transform parent)
        {
            UsePrefab(data, ruinType, parent);
        }

        void CreateSphere(RuinData data, Material mtl, Transform parent)
        {
            Debug.Assert(parent != null);
            OnDestroy();

            mGameObject = new GameObject(data.objectType);
            mGameObject.transform.parent = parent;
            mGameObject.transform.position = data.GetPosition();
            mGameObject.transform.rotation = data.GetRotation();
            mGameObject.SetActive(data.IsObjActive());
            
            mPrefabObject = CreateRoot(data.color, mtl);
            mPrefabObject.transform.localScale = data.GetScale();
            mPrefabObject.transform.SetParent(mGameObject.transform, false);
            
            mRangeObject = CreateRangeObject();
            mRangeObject.transform.SetParent(mGameObject.transform, false);
            mRangeObject.transform.localScale = Vector3.one * data.radius * 2;
            Utils.HideGameObject(mRangeObject);
            mTextObject = Utils.CreateTextGameObject("level_text", data.level.ToString(), Color.white);
            mTextObject.transform.SetParent(gameObject.transform, false);
            mTextObject.SetActive(false);
            Utils.HideGameObject(mTextObject);
        }

        void CreateModel(RuinData data, RuinSetting ruinType, Transform parent)
        {
            Debug.Assert(parent != null);
            OnDestroy();

            mGameObject = new GameObject(data.objectType);
            mGameObject.transform.position = data.GetPosition();
            mGameObject.transform.rotation = data.GetRotation();
            mGameObject.transform.parent = parent;
            mGameObject.SetActive(data.IsObjActive());

            mPrefabObject = Object.Instantiate<GameObject>(ruinType.prefab);
            mPrefabObject.transform.localScale = data.GetScale();
            mPrefabObject.transform.SetParent(mGameObject.transform, false);
            
            mRangeObject = CreateRangeObject();
            mRangeObject.transform.SetParent(mGameObject.transform, false);
            mRangeObject.transform.localScale = Vector3.one * data.radius * 2;
            Utils.HideGameObject(mRangeObject);
            mTextObject = Utils.CreateTextGameObject("level_text", data.level.ToString(), Color.white);
            mTextObject.transform.SetParent(gameObject.transform, false);
            mTextObject.SetActive(false);
            Utils.HideGameObject(mTextObject);
        }

        public override void Release()
        {
            OnDestroy();
        }

        public void UsePrefab(RuinData data, RuinSetting ruinType, Transform parent)
        {
            if (ruinType.prefab == null)
            {
                CreateSphere(data, ruinType.mtl, parent);
            }
            else
            {
                CreateModel(data, ruinType, parent);
            }

            //选择物体时选到子节点的root
            var behaviour = mGameObject.GetComponent<RuinObjectBehaviour>();
            if (behaviour == null)
            {
                behaviour = mGameObject.AddComponent<RuinObjectBehaviour>();
            }
            behaviour.Init(data.GetEntityID(), data.layerID);
        }

        public void SetColor(Color color)
        {
            var renderer = mPrefabObject.GetComponent<MeshRenderer>();
            if (renderer != null)
            {
                renderer.sharedMaterial.color = color;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Utils.DestroyObject(mPrefabObject);
            mPrefabObject = null;
            Utils.DestroyObject(mMesh);
            mMesh = null;
            Utils.DestroyObject(mMaterial);
            mMaterial = null;
            Utils.DestroyObject(mRangeObject);
            mRangeObject = null;
            Utils.DestroyObject(mTextObject);
            mTextObject = null;
        }

        GameObject CreateRoot(Color color, Material mtl)
        {
            var obj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            var meshRenderer = obj.GetComponent<MeshRenderer>();
            var collider = obj.GetComponent<SphereCollider>();
            GameObject.DestroyImmediate(collider);
            meshRenderer.sharedMaterial = mtl;
            meshRenderer.sharedMaterial.color = color;
            return obj;
        }

        GameObject CreateRangeObject()
        {
            var obj = new GameObject("ruin range");
            var renderer = obj.AddComponent<MeshRenderer>();
            var filter = obj.AddComponent<MeshFilter>();
            renderer.sharedMaterial = CreateMaterial();
            filter.sharedMesh = CreateMesh();
            return obj;
        }

        Mesh CreateMesh()
        {
            if (mMesh == null)
            {
                int n = 24;
                Vector3[] vertices = new Vector3[n];
                for (int i = 0; i < n; ++i)
                {
                    float angle = 360.0f / n * i;
                    vertices[i] = new Vector3(Mathf.Cos(angle * Mathf.Deg2Rad), 0, Mathf.Sin(angle * Mathf.Deg2Rad)) * 0.5f;
                }
                int[] indices = new int[n + 1];
                for (int i = 0; i < indices.Length; ++i)
                {
                    indices[i] = i % n;
                }
                mMesh = new Mesh();
                mMesh.vertices = vertices;
                mMesh.SetIndices(indices, MeshTopology.LineStrip, 0);
            }
            return mMesh;
        }

        Material CreateMaterial()
        {
            if (mMaterial == null)
            {
                mMaterial = new Material(Shader.Find("SLGMaker/ColorTransparent"));
                mMaterial.SetColor("_Color", Color.red);
            }
            return mMaterial;
        }

        public void ShowText(bool show)
        {
            mTextObject.SetActive(show);
        }

        public void SetScale(Vector3 scale)
        {
            mPrefabObject.transform.localScale = scale;
            mTextObject.transform.localScale = scale;
        }

        public void SetColliderRadius(float colliderRadius)
        {
            mRangeObject.transform.localScale = Vector3.one * colliderRadius * 2;
        }

        GameObject mRangeObject;
        GameObject mPrefabObject;
        GameObject mTextObject;
        Mesh mMesh;
        Material mMaterial;
        string mPrefabPath;
    }
}

#endif