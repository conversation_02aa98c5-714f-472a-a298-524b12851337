﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(SplineEditor))]
    public partial class SplineEditorUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mEditor = target as SplineEditor;
            mMover = new MouseMover();
        }

        public override void OnInspectorGUI()
        {
            if (MapModule.showDemoFunction)
            {
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Save Objects"))
                {
                    var dataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.SPLINE_OBJECTS_EDITOR_TEMP_DATA_FILE_NAME;
                    mEditor.splineObjectManager.SaveObjectsInRange(dataPath, 0, 0, 360, 360);
                }
                if (GUILayout.Button("Load Objects"))
                {
                    var dataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.SPLINE_OBJECTS_EDITOR_TEMP_DATA_FILE_NAME;
                    mEditor.splineObjectManager.LoadObjectsInRange(dataPath);
                }
                EditorGUILayout.EndHorizontal();
            }

            mEditor.isCreatingRiver = EditorGUILayout.ToggleLeft(new GUIContent("River Mode", "是否是河流模式"), mEditor.isCreatingRiver);

            mEditor.splineObjectManager.visible = EditorGUILayout.ToggleLeft(new GUIContent("Show Splines", "是否显示spline"), mEditor.splineObjectManager.visible);

            var newMode = (SplineEditor.Mode)EditorGUILayout.EnumPopup(new GUIContent("Operation", "当前所选操作"), mEditor.mode);
            SetMode(newMode);
            if (mEditor.mode == SplineEditor.Mode.Create)
            {
                DrawCreateFunctionInspectorGUI();
            }
            else if (mEditor.mode == SplineEditor.Mode.EditControlPoint)
            {
                DrawEditControlPointFunctionInspectorGUI();
            }
            else if (mEditor.mode == SplineEditor.Mode.Move)
            {
                DrawMoveSplineFunctionInspectorGUI();
            }
            else
            {
                Debug.Assert(false, "todo");
            }

            float displayRadius = EditorGUILayout.FloatField(new GUIContent("Display Radius", "控制点显示半径"), mEditor.splineObjectManager.displayRadius);
            if (!Mathf.Approximately(displayRadius, mEditor.splineObjectManager.displayRadius))
            {
                mEditor.splineObjectManager.displayRadius = displayRadius;
            }

            SplineObject splineObject = GetSelectedSpline();
            if (splineObject != null)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUIUtility.labelWidth = 80;
                bool visible = EditorGUILayout.ToggleLeft(new GUIContent("Show Land", "是否显示land"), splineObject.IsLandVisible());
                splineObject.ShowLand(visible);
                visible = EditorGUILayout.ToggleLeft(new GUIContent("Show Water", "是否显示water"), splineObject.IsWaterVisible());
                splineObject.ShowWater(visible);
                visible = EditorGUILayout.ToggleLeft(new GUIContent("Show Mask", "是否显示mask"), splineObject.IsStencilMaskVisible());
                splineObject.ShowStencilMask(visible);
                EditorGUIUtility.labelWidth = 0;
                EditorGUILayout.EndHorizontal();

                splineObject.name = EditorGUILayout.TextField("Name", splineObject.name);

                if (!mEditor.isCreatingRiver)
                {
                    float oldRatio = splineObject.ratio;
                    float newRatio = EditorGUILayout.FloatField(new GUIContent("UV Ratio", "UV宽高比例,1表示使用贴图的宽高比,大于1会横向拉伸贴图,小于1会纵向拉伸贴图"), oldRatio);
                    newRatio = Mathf.Max(0.01f, newRatio);
                    if (!Mathf.Approximately(newRatio, oldRatio))
                    {
                        splineObject.ratio = newRatio;
                    }
                    splineObject.tileCount = EditorGUILayout.IntField(new GUIContent("Tile Count", "贴图中公有几种类型的道路样式"), splineObject.tileCount);
                }

                if (mSelectedControlPointIndex >= 0)
                {
                    if (!mEditor.isCreatingRiver)
                    {
                        var cp = splineObject.GetControlPoint(mSelectedControlPointIndex);
                        int newTileIndex = EditorGUILayout.IntField(new GUIContent("Tiling Index", "贴图的第几个tiling"), cp.tileIndex);
                        if (newTileIndex != cp.tileIndex)
                        {
                            cp.tileIndex = newTileIndex;
                            splineObject.CreateSplineMesh();
                        }
                    }

                    EditorGUILayout.BeginHorizontal();
                    var pointCount = splineObject.GetPointCountInSegment(mSelectedControlPointIndex);
                    int pointCountInSegment = EditorGUILayout.IntField(new GUIContent("Point Count In One Segment", "spline的一段有多少个顶点"), pointCount);
                    pointCountInSegment = Mathf.Max(2, pointCountInSegment);
                    if (pointCountInSegment != pointCount)
                    {
                        splineObject.SetPointCountInSegment(mSelectedControlPointIndex, pointCountInSegment);
                        UpdateDisplay(splineObject);
                        SceneView.RepaintAll();
                    }

                    if (GUILayout.Button(new GUIContent("Set All Point Count", "设置所有分段的顶点数"), GUILayout.MaxWidth(150)))
                    {
                        var dlg = EditorUtils.CreateInputDialog("Set Point Count In All Segments");
                        var items = new List<InputDialog.Item> {
                            new InputDialog.StringItem("Point Count", "", "5"),
                        };
                        dlg.Show(items, OnClickSetPointCount);
                    }
                    EditorGUILayout.EndHorizontal();

                    if (!mEditor.isCreatingRiver)
                    {
                        EditorGUILayout.BeginHorizontal();
                        var width = splineObject.GetControlPointWidth(mSelectedControlPointIndex);
                        float newWidth = EditorGUILayout.FloatField(new GUIContent("Segment Width", "spline一段的宽度"), width);
                        newWidth = Mathf.Max(0.01f, newWidth);
                        if (!Mathf.Approximately(newWidth, width))
                        {
                            splineObject.SetControlPointWidth(mSelectedControlPointIndex, newWidth);
                        }

                        if (GUILayout.Button(new GUIContent("Set All Width", "设置spline的整体宽度"), GUILayout.MaxWidth(150)))
                        {
                            var dlg = EditorUtils.CreateInputDialog("Set Width For All Segments");
                            var items = new List<InputDialog.Item> {
                            new InputDialog.StringItem("Width", "", "1"),
                        };
                            dlg.Show(items, OnClickSetWidth);
                        }
                        EditorGUILayout.EndHorizontal();

                        EditorGUILayout.IntField(new GUIContent("Control Point Index", "控制点序号"), mSelectedControlPointIndex);
                    }
                }

                if (!mEditor.isCreatingRiver)
                {
                    bool newLoop = EditorGUILayout.ToggleLeft(new GUIContent("Loop", "是否循环"), splineObject.isLoop);
                    if (newLoop != splineObject.isLoop)
                    {
                        splineObject.isLoop = newLoop;
                        UpdateDisplay(splineObject);
                        mSelectedControlPointIndex = -1;
                        mSelectedVertexIndex = -1;
                    }

                    bool newInside = EditorGUILayout.ToggleLeft(new GUIContent("Inside", "设置spline的朝向"), splineObject.inside);
                    if (newInside != splineObject.inside)
                    {
                        splineObject.inside = newInside;
                        mSelectedControlPointIndex = -1;
                        mSelectedVertexIndex = -1;
                        UpdateDisplay(splineObject);
                        SceneView.RepaintAll();
                    }
                    bool newFadeOutEndPoints = EditorGUILayout.ToggleLeft(new GUIContent("Fade Out", "通过顶点色来渐变spline两端,需要shader用顶点色调整alpha,对loop的spline无效"), splineObject.fadeoutEndPoints);
                    if (newFadeOutEndPoints != splineObject.fadeoutEndPoints)
                    {
                        splineObject.fadeoutEndPoints = newFadeOutEndPoints;
                    }
                }
            }

            if (mEditor.isCreatingRiver)
            {
                DrawMaterialField(splineObject, 0, "Land Material");
                DrawMaterialField(splineObject, 1, "Water Material");
                DrawMaterialField(splineObject, 2, "StencilMask Material");

                EditorGUILayout.BeginHorizontal();
                var folder = AssetDatabase.LoadAssetAtPath<DefaultAsset>(mEditor.splineObjectManager.riverPrefabFolder);
                var newFolder = EditorGUILayout.ObjectField($"Export Folder", folder, typeof(DefaultAsset), false, null) as DefaultAsset;
                if (newFolder != folder)
                {
                    mEditor.splineObjectManager.riverPrefabFolder = AssetDatabase.GetAssetPath(newFolder);
                }
                if (GUILayout.Button("Generate Prefab"))
                {
                    CreateRiverPrefab(mEditor.splineObjectManager.riverPrefabFolder);
                }
                EditorGUILayout.EndHorizontal();
                mEditor.splineObjectManager.generateObjFile = EditorGUILayout.Toggle("Generate OBJ File", mEditor.splineObjectManager.generateObjFile);

                if (splineObject != null)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.IntField("Stripe Count", splineObject.GetStripeCount());
                    if (GUILayout.Button("Change Count"))
                    {
                        var dlg = EditorUtils.CreateInputDialog("Set Stripe Count");
                        var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Count", "", splineObject.GetStripeCount().ToString()),
                    };
                        dlg.Show(items, (List<InputDialog.Item> parameters) =>
                        {
                            var countStr = (parameters[0] as InputDialog.StringItem).text;
                            bool suc = int.TryParse(countStr, out int count);
                            if (count >= 2)
                            {
                                splineObject.SetStripeCount(count);
                                return true;
                            }
                            return false;
                        });
                    }
                    int stripeCount = splineObject.GetStripeCount();
                    EditorGUILayout.EndHorizontal();
                    splineObject.SetStripeCount(stripeCount);
                    ++EditorGUI.indentLevel;
                    for (int i = 0; i < stripeCount; ++i)
                    {
                        EditorGUILayout.BeginVertical("GroupBox");
                        float width = EditorGUILayout.FloatField($"Width {i}", splineObject.GetStripeWidth(i));
                        splineObject.SetStripeWidth(i, width);
                        float depth = EditorGUILayout.FloatField($"Depth {i}", splineObject.GetStripeDepth(i));
                        splineObject.SetStripeDepth(i, depth);
                        EditorGUILayout.EndVertical();
                    }
                    --EditorGUI.indentLevel;
                }
            }
            else
            {
                DrawMaterialField(splineObject, 0, "Material");
            }

            if (!mEditor.isCreatingRiver)
            {
                mEditor.splineObjectManager.maxExportSegmentLength = EditorGUILayout.FloatField(new GUIContent("Export Segment Length", "导出时按多少米长一段来切分spline,将其变成多段在游戏中分段加载和显示.如果该值为0,则不会切分spline"), mEditor.splineObjectManager.maxExportSegmentLength);

                EditorGUILayout.BeginVertical("GroupBox");
                if (GUILayout.Button(new GUIContent("Generate Assets", "生成游戏内所需spline资源")))
                {
                    mEditor.splineObjectManager.GenerateAssets();
                }
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Select Generated Assets Folder", "在unity中选择生成的资源的文件夹")))
                {
                    string assetFolder = MapCoreDef.GetGeneratedSplineAssetFolderPath(SLGMakerEditor.instance.exportFolder);
                    EditorUtils.SelectFolder(assetFolder);
                }
                if (GUILayout.Button(new GUIContent("Open Generated Assets Folder", "打开生成的资源的文件夹")))
                {
                    string assetFolder = MapCoreDef.GetGeneratedSplineAssetFolderPath(SLGMakerEditor.instance.exportFolder);
                    EditorUtils.OpenFolder(assetFolder);
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();

                mEditor.setting.Draw("LOD Setting", mEditor.splineObjectManager.lodConfig, 0, null, null);
            }

            if (GUILayout.Button(new GUIContent("Remove All Splines", "删除所有的spline")))
            {
                if (EditorUtility.DisplayDialog("Warning", "Are you sure?", "Yes", "No"))
                {
                    SetSelectedSpline(null);
                    mEditor.splineObjectManager.RemoveAllSplines();
                }
            }

            EditorGUILayout.TextArea("Accelerator Keys:\n 'Left Mouse Button' to place control point.\n 'Ctrl + Left Mouse Button' to insert control point.\n 'Ctrl + Shift + Left Mouse Button' to delete control point.\n");
        }

        void SetMode(SplineEditor.Mode mode)
        {
            if (mEditor.mode != mode)
            {
                mEditor.mode = mode;
                if (mode == SplineEditor.Mode.Create)
                {
                    SetSelectedSpline(null);
                }
            }
        }

        void DrawMaterialField(SplineObject splineObject, int mtlType, string mtlName)
        {
            string materialPath = null;
            if (mtlType == 0)
            {
                materialPath = mEditor.splineMaterialPath;
                if (splineObject != null)
                {
                    materialPath = splineObject.materialPath;
                }
            }
            else if (mtlType == 1)
            {
                materialPath = mEditor.waterMaterialPath;
                if (splineObject != null)
                {
                    materialPath = splineObject.waterMaterialPath;
                }
            }
            else if (mtlType == 2)
            {
                materialPath = mEditor.stencilMaskMaterialPath;
                if (splineObject != null)
                {
                    materialPath = splineObject.stencilMaskMaterialPath;
                }
            }
            
            var material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
            var newMaterial = EditorGUILayout.ObjectField(new GUIContent(mtlName, "Spline的材质"), material, typeof(Material), false, null) as Material;
            if (newMaterial != material && newMaterial != null)
            {
                if (mtlType == 0)
                {
                    if (splineObject != null)
                    {
                        splineObject.materialPath = AssetDatabase.GetAssetPath(newMaterial);
                    }
                    mEditor.splineMaterialPath = AssetDatabase.GetAssetPath(newMaterial);
                }
                else if (mtlType == 1)
                {
                    if (splineObject != null)
                    {
                        splineObject.waterMaterialPath = AssetDatabase.GetAssetPath(newMaterial);
                    }
                    mEditor.waterMaterialPath = AssetDatabase.GetAssetPath(newMaterial);
                }
                else if (mtlType == 2)
                {
                    if (splineObject != null)
                    {
                        splineObject.stencilMaskMaterialPath = AssetDatabase.GetAssetPath(newMaterial);
                    }
                    mEditor.stencilMaskMaterialPath = AssetDatabase.GetAssetPath(newMaterial);
                }
            }
        }

        void OnSceneGUI()
        {
            var evt = Event.current;
            var map = Map.currentMap;

            var screenPos = EditorUtils.ConvertScreenPosition(evt.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (evt.type == EventType.MouseDown && evt.button == 1 && mAddedVertices.Count == 0)
            {
                PickSpline(pos);
                if (mSelectedSplineObjectID != 0)
                {
                    GenericMenu menu = new GenericMenu();
                    menu.AddItem(new GUIContent(new GUIContent("Delete Spline", "删除spline")), false, DeleteSpline);
                    menu.AddItem(new GUIContent(new GUIContent("Apply Red Tangent To Green Tangent", "将红色的切线应用到绿色的切线")), false, ApplyLeftTangentToRightTangent);
                    menu.AddItem(new GUIContent(new GUIContent("Apply Green Tangent To Red Tangent", "将绿色的切线应用到红色的切线")), false, ApplyRightTangentToLeftTangent);

                    menu.AddSeparator("");

                    var mode = mEditor.splineObjectManager.tangentMoveType;
                    menu.AddItem(new GUIContent("Tangent Mode/Free", "自由移动切线"), mode == TangentMoveType.Free, SetTangentMode, TangentMoveType.Free);
                    menu.AddItem(new GUIContent("Tangent Mode/Rotate", "只能旋转切线"), mode == TangentMoveType.Rotate, SetTangentMode, TangentMoveType.Rotate);
                    menu.AddItem(new GUIContent("Tangent Mode/Rotate And Scale", "旋转和缩放切线"), mode == TangentMoveType.RotateAndScale, SetTangentMode, TangentMoveType.RotateAndScale);

                    menu.ShowAsContext();
                }
            }

            if (mEditor.mode == SplineEditor.Mode.Create)
            {
                HandleCreateFunction(evt, pos);
                DrawCreateFunctionSceneGUI();
            }
            //else if (mEditor.mode == SplineEditor.Mode.EditMesh)
            //{
            //    HandleEditMeshFunction(evt, pos);
            //    DrawEditMeshFunctionSceneGUI();
            //}
            else if (mEditor.mode == SplineEditor.Mode.EditControlPoint)
            {
                HandleEditControlPointFunction(evt, pos);
                DrawEditControlPointFunctionSceneGUI();
            }
            else if (mEditor.mode == SplineEditor.Mode.Move)
            {
                HandleMoveSplineFunction(evt, pos);
                DrawMoveSplineFunctionSceneGUI();
            }
            else
            {
                Debug.Assert(false, "todo");
            }

            DrawSpline();
        }

        List<SplineObject.ControlPoint> CreateControlPoints(List<Vector3> pos)
        {
            bool cw = Utils.IsPolygonCW(pos);
            float sign = 1.0f;
            if (cw)
            {
                sign = -1.0f;
            }

            List<SplineObject.ControlPoint> controlPoints = new List<SplineObject.ControlPoint>();
            for (int i = 0; i < pos.Count; ++i)
            {
                var cp = new SplineObject.ControlPoint()
                {
                    pos = pos[i],
                };
                if (i > 0 && i < pos.Count - 1)
                {
                    var dir0 = pos[i - 1] - pos[i];
                    var dir1 = pos[i + 1] - pos[i];
                    dir0.Normalize();
                    dir1.Normalize();
                    var p = dir0 + dir1;
                    p.Normalize();
                    var perp = new Vector3(p.z, 0, -p.x) * sign;
                    perp.Normalize();
                    cp.tangents[0] = pos[i] - perp * 20;
                    cp.tangents[1] = pos[i] + perp * 20;
                }
                else
                {
                    cp.tangents[0] = pos[i] - new Vector3(20, 0, 0);
                    cp.tangents[1] = pos[i] + new Vector3(20, 0, 0);
                }
                controlPoints.Add(cp);
            }
            return controlPoints;
        }

        void UpdateDisplay(SplineObject spline)
        {
            if (spline != null)
            {
                var controlPoints = spline.controlPoints;
                mDisplayControlPoints = new Vector3[controlPoints.Count];
                mDisplayTangents = new Vector3[controlPoints.Count * 2];
                for (int i = 0; i < controlPoints.Count; ++i)
                {
                    mDisplayControlPoints[i] = controlPoints[i].pos;
                    mDisplayTangents[i * 2] = controlPoints[i].tangents[0];
                    mDisplayTangents[i * 2 + 1] = controlPoints[i].tangents[1];
                }
                mDisplaySplinePoints = new Vector3[spline.evaluatedPoints.Count];
                for (int i = 0; i < mDisplaySplinePoints.Length; ++i)
                {
                    mDisplaySplinePoints[i] = spline.evaluatedPoints[i].pos;
                }
            }
            else
            {
                mDisplayControlPoints = null;
                mDisplaySplinePoints = null;
                mDisplayTangents = null;
            }
        }

        void SetSelectedSpline(SplineObject spline)
        {
            var oldSpline = GetSelectedSpline();
            if (oldSpline != null)
            {
                oldSpline.HideTangent();
            }

            if (spline != null)
            {
                mSelectedSplineObjectID = spline.id;
            }
            else
            {
                mSelectedSplineObjectID = 0;
            }
            mSelectedVertexIndex = -1;
            mSelectedControlPointIndex = -1;
            mSelectedTangentPointIndex = -1;
            UpdateDisplay(spline);
        }

        void DrawSpline()
        {
            if (mDisplaySplinePoints != null && mEditor.splineObjectManager.visible)
            {
                Handles.DrawPolyLine(mDisplaySplinePoints);
            }

            if (mSelectedControlPointIndex >= 0)
            {
                var spline = GetSelectedSpline();
                if (spline.IsTangentVisible())
                {
                    var controlPoint = spline.controlPoints[mSelectedControlPointIndex];
                    Handles.color = Color.yellow;
                    Handles.DrawLine(controlPoint.pos, controlPoint.tangents[0]);
                    Handles.DrawLine(controlPoint.pos, controlPoint.tangents[1]);
                    Handles.color = Color.white;
                }
            }
        }

        void PickSpline(Vector3 worldPos)
        {
            mSelectedTangentPointIndex = -1;
            var selectedSpline = GetSelectedSpline();
            if (selectedSpline != null && mSelectedControlPointIndex >= 0)
            {
                int idx = TryPickControlPoint(worldPos, selectedSpline);
                if (idx < 0)
                {
                    PickTangent(worldPos, selectedSpline);
                }
                else
                {
                    mSelectedTangentPointIndex = -1;
                }
            }

            if (mSelectedTangentPointIndex < 0)
            {
                SetSelectedSpline(null);
                var splines = mEditor.splineObjectManager.splines;
                for (int i = 0; i < splines.Count; ++i)
                {
                    PickControlPoint(worldPos, splines[i]);
                    int selectedControlPointIndex = mSelectedControlPointIndex;
                    if (selectedControlPointIndex >= 0)
                    {
                        SetSelectedSpline(splines[i]);
                        mSelectedControlPointIndex = selectedControlPointIndex;
                        splines[i].ShowTangent(selectedControlPointIndex);
                        break;
                    }
                }
            }
        }

        void PickTangent(Vector3 worldPos, SplineObject spline)
        {
            mMover.Reset();
            mSelectedTangentPointIndex = -1;
            float pickRadius2 = mEditor.splineObjectManager.displayRadius;
            pickRadius2 *= pickRadius2;
            var controlPoint = spline.controlPoints[mSelectedControlPointIndex];
            var d = worldPos - controlPoint.tangents[0];
            if (d.sqrMagnitude <= pickRadius2)
            {
                mSelectedTangentPointIndex = 0;
            }
            else
            {
                d = worldPos - controlPoint.tangents[1];
                if (d.sqrMagnitude <= pickRadius2)
                {
                    mSelectedTangentPointIndex = 1;
                }
            }
        }

        void ApplyLeftTangentToRightTangent()
        {
            var spline = GetSelectedSpline();
            if (spline != null && mSelectedControlPointIndex >= 0)
            {
                mEditor.splineObjectManager.ApplySplineObjectTangent(spline.id, mSelectedControlPointIndex, 0, 1);
            }
        }

        void ApplyRightTangentToLeftTangent()
        {
            var spline = GetSelectedSpline();
            if (spline != null && mSelectedControlPointIndex >= 0)
            {
                mEditor.splineObjectManager.ApplySplineObjectTangent(spline.id, mSelectedControlPointIndex, 1, 0);
            }
        }

        void SetTangentMode(object mode)
        {
            var realMode = (TangentMoveType)mode;
            mEditor.splineObjectManager.tangentMoveType = realMode;
        }

        void DeleteSpline()
        {
            if (EditorUtility.DisplayDialog("Warning", "This operation can't be undone! continue?", "Yes", "No"))
            {
                if (mSelectedSplineObjectID != 0)
                {
                    mEditor.splineObjectManager.RemoveSpline(mSelectedSplineObjectID);
                    SetSelectedSpline(null);
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Select a spline first!", "OK");
                }
            }
        }

        SplineObject GetSelectedSpline()
        {
            if (mSelectedSplineObjectID != 0)
            {
                return mEditor.splineObjectManager.FindSplineObject(mSelectedSplineObjectID);
            }
            return null;
        }

        List<SplineObject.ExportSplineSegmentInfo> OffsetSegments(List<SplineObject.ExportSplineSegmentInfo> source, Vector3 offset, int splineIndex)
        {
            List<SplineObject.ExportSplineSegmentInfo> segments = new List<SplineObject.ExportSplineSegmentInfo>(source.Count);
            for (int i = 0; i < source.Count; ++i)
            {
                var segment = new SplineObject.ExportSplineSegmentInfo();
                segment.startControlPointIndex = source[i].startControlPointIndex;
                segment.endControlPointIndex = source[i].endControlPointIndex;
                segment.splineIndex = splineIndex;
                segment.prefabPath = source[i].prefabPath;
                segment.position = source[i].position + offset;
                segments.Add(segment);
            }
            return segments;
        }

        bool OnClickSetPointCount(List<InputDialog.Item> parameters)
        {
            int count;
            bool ok = int.TryParse((parameters[0] as InputDialog.StringItem).text, out count);
            if (ok)
            {
                if (count < 2)
                {
                    return false;
                }

                var spline = GetSelectedSpline();
                spline.SetAllPointCountInSegment(count);
                UpdateDisplay(spline);
                SceneView.RepaintAll();
            }

            return true;
        }

        bool OnClickSetWidth(List<InputDialog.Item> parameters)
        {
            float width;
            bool ok = float.TryParse((parameters[0] as InputDialog.StringItem).text, out width);
            if (ok)
            {
                if (width <= 0)
                {
                    return false;
                }

                var spline = GetSelectedSpline();
                spline.SetWidth(width);
                UpdateDisplay(spline);
                SceneView.RepaintAll();
            }

            return true;
        }

        Vector3[] mDisplayControlPoints;
        Vector3[] mDisplayTangents;
        Vector3[] mDisplaySplinePoints;
        SplineEditor mEditor;
        int mSelectedSplineObjectID;
    }
}

#endif