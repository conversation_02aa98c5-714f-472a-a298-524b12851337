%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &387891196925082345
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5796219753713132833}
  - component: {fileID: 1287036230147286849}
  m_Layer: 0
  m_Name: ObstacleOutline 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5796219753713132833
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 387891196925082345}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.62, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1287036230147286849
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 387891196925082345}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: 5.022474, y: 0, z: -25.255934}
    - {x: 20.116505, y: 0, z: -34.674877}
    - {x: 30.29924, y: 0, z: -33.239613}
    - {x: 30.823547, y: 0, z: -24.379848}
    - {x: 15.443344, y: 0, z: 1.34898}
    - {x: 6.067765, y: 0, z: 4.944218}
    - {x: -0.16695285, y: 0, z: -0.38290405}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: 26.2694, y: 0, z: -36.6386}
    - {x: 26.3171, y: 0, z: -36.6361}
    - {x: 29.819, y: 0, z: -36.3957}
    - {x: 30.9516, y: 0, z: -35.7916}
    - {x: 32.6744, y: 0, z: -33.5682}
    - {x: 32.9727, y: 0, z: -32.8863}
    - {x: 33.7514, y: 0, z: -28.227}
    - {x: 33.7688, y: 0, z: -27.8812}
    - {x: 33.5248, y: 0, z: -24.1288}
    - {x: 33.3462, y: 0, z: -23.5071}
    - {x: 21.1737, y: 0, z: -0.9314}
    - {x: 20.8222, y: 0, z: -0.4969}
    - {x: 14.895, y: 0, z: 4.5442}
    - {x: 14.4218, y: 0, z: 4.8171}
    - {x: 9.2336, y: 0, z: 6.662}
    - {x: 8.9569, y: 0, z: 6.7317}
    - {x: 6.0481, y: 0, z: 7.176}
    - {x: 4.919, y: 0, z: 6.9112}
    - {x: 0.9623, y: 0, z: 4.1328}
    - {x: 0.6092, y: 0, z: 3.7858}
    - {x: -1.2585, y: 0, z: 1.2207}
    - {x: -1.5533, y: 0, z: 0.2942}
    - {x: -1.4861, y: 0, z: -4.7855}
    - {x: -1.4839, y: 0, z: -4.8494}
    - {x: -1.2521, y: 0, z: -8.9976}
    - {x: -1.2371, y: 0, z: -9.1411}
    - {x: -0.9709, y: 0, z: -10.8793}
    - {x: -0.9539, y: 0, z: -10.9715}
    - {x: 2.3481, y: 0, z: -26.1317}
    - {x: 2.5535, y: 0, z: -26.6274}
    - {x: 3.7254, y: 0, z: -28.451}
    - {x: 4.2946, y: 0, z: -28.9743}
    - {x: 18.8925, y: 0, z: -36.7051}
    - {x: 19.6628, y: 0, z: -36.8815}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
