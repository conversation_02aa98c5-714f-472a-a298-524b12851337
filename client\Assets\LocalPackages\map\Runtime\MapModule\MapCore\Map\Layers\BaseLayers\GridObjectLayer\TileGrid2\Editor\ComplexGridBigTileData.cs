﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //tile grid object layer data 2使用的prefab的默认信息,包括旋转和缩放
    public class PrefabInitInfo1
    {
        public PrefabInitInfo1(string prefabPath, float boundsMinX, float boundsMinZ, float boundsWidth, float boundsHeight, Quaternion rotation, Vector3 scale, float y)
        {
            this.prefabPath = prefabPath;
            this.boundsMinX = boundsMinX;
            this.boundsMinZ = boundsMinZ;
            this.boundsWidth = boundsWidth;
            this.boundsHeight = boundsHeight;
            this.rotation = rotation;
            this.scale = scale;
            this.y = y;
            this.maxVisibleQuality = short.MaxValue;
        }

        public string prefabPath;
        //局部坐标的bounds,换算成世界坐标直接加上prefab的position
        public float boundsWidth;
        public float boundsHeight;
        public float boundsMinX;
        public float boundsMinZ;
        public Quaternion rotation;
        public Vector3 scale;
        public float y;
        //最大可显示质量等级
        public short maxVisibleQuality;
    }

    public class PrefabInitInfo2
    {
        public PrefabInitInfo2(List<string> prefabPathForEachCustomType, float boundsMinX, float boundsMinZ, float boundsWidth, float boundsHeight, Quaternion rotation, Vector3 scale, float y)
        {
            this.prefabPathForEachCustomType = prefabPathForEachCustomType;
            this.boundsMinX = boundsMinX;
            this.boundsMinZ = boundsMinZ;
            this.boundsWidth = boundsWidth;
            this.boundsHeight = boundsHeight;
            this.rotation = rotation;
            this.scale = scale;
            this.y = y;
            this.maxVisibleQuality = short.MaxValue;
        }

        public string GetPrefabPath(int state)
        {
            state = Mathf.Clamp(state, 0, prefabPathForEachCustomType.Count - 1);
            return prefabPathForEachCustomType[state];
        }

        //由customType索引
        public List<string> prefabPathForEachCustomType;
        //局部坐标的bounds,换算成世界坐标直接加上prefab的position
        public float boundsWidth;
        public float boundsHeight;
        public float boundsMinX;
        public float boundsMinZ;
        public Quaternion rotation;
        public Vector3 scale;
        public float y;
        //最大可显示质量等级
        public short maxVisibleQuality;
    }

    //子prefab的transform信息
    public class BigTileChildPrefabData1
    {
        public float x;
        public float z;
        //主要用来判断某个物体是否被多个tile共享
        public int viewID;
        //-1表示没有修改过prefab的transform信息,使用prefab默认的scale和rotation
        public short prefabInitInfoIndex = -1;
        public TileObjectType objectType;

        public Vector3 GetPosition(List<PrefabInitInfo1> table)
        {
            return new Vector3(x, table[prefabInitInfoIndex].y, z);
        }

        public Vector2 GetPositionXZ()
        {
            return new Vector2(x, z);
        }

        public Vector3 GetScale(List<PrefabInitInfo1> table)
        {
            return table[prefabInitInfoIndex].scale;
        }

        public Quaternion GetRotation(List<PrefabInitInfo1> table)
        {
            return table[prefabInitInfoIndex].rotation;
        }

        public Rect GetLocalBoundsInPrefab(List<PrefabInitInfo1> table)
        {
            var objPos = GetPosition(table);
            return new Rect(
                objPos.x + table[prefabInitInfoIndex].boundsMinX,
                objPos.z + table[prefabInitInfoIndex].boundsMinZ,
                table[prefabInitInfoIndex].boundsWidth,
                table[prefabInitInfoIndex].boundsHeight
                );
        }
    }

    //子prefab的transform信息
    public class BigTileChildPrefabData2
    {
        public float x;
        public float z;
        //主要用来判断某个物体是否被多个tile共享
        public int viewID;
        //-1表示没有修改过prefab的transform信息,使用prefab默认的scale和rotation
        public short prefabInitInfoIndex = -1;
        public TileObjectType objectType;
        //用来动态替换装饰物类型
        //public byte customType = 0;

        public Vector3 GetPosition(List<PrefabInitInfo2> table)
        {
            return new Vector3(x, table[prefabInitInfoIndex].y, z);
        }

        public Vector2 GetPositionXZ()
        {
            return new Vector2(x, z);
        }

        public Vector3 GetScale(List<PrefabInitInfo2> table)
        {
            return table[prefabInitInfoIndex].scale;
        }

        public Quaternion GetRotation(List<PrefabInitInfo2> table)
        {
            return table[prefabInitInfoIndex].rotation;
        }

        public Rect GetLocalBoundsInPrefab(List<PrefabInitInfo2> table)
        {
            var objPos = GetPosition(table);
            return new Rect(
                objPos.x + table[prefabInitInfoIndex].boundsMinX,
                objPos.z + table[prefabInitInfoIndex].boundsMinZ,
                table[prefabInitInfoIndex].boundsWidth,
                table[prefabInitInfoIndex].boundsHeight
                );
        }
    }

    public class ComplexGridBigTileData1
    {
        //每级lod下每个物体信息
        public List<List<BigTileChildPrefabData1>> objectsOfEachLOD = new List<List<BigTileChildPrefabData1>>();
    }

    public class ComplexGridBigTileData2
    {
        //每级lod下每个物体信息
        public List<List<BigTileChildPrefabData2>> objectsOfEachLOD = new List<List<BigTileChildPrefabData2>>();
    }
}
