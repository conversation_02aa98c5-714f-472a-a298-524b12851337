﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //管理tile object的视野剔除
    public class FrameActionUpdateTileObjectCull2 : FrameAction
    {
        public static FrameActionUpdateTileObjectCull2 Require(TileGridObjectLayerData2 layerData)
        {
            var act = mPool.Require();
            act.Init(layerData);
            return act;
        }

        void Init(TileGridObjectLayerData2 layerData)
        {
            InitAction();
            mLayerData = layerData;
            mKey = MakeKeyHelper(layerData.id, type);
        }

        protected override void DoImpl()
        {
            mLayerData.UpdateCulling();
        }

        public override bool isEnabled
        {
            get
            {
                return true;
            }
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mPool.Release(this);
        }

        public override long key { get { return mKey; } }
        public override FrameActionType type => FrameActionType.UpdateTileObjectCull2;
        public override string name => "Update Tile Object Cull 2";
        public override string debugInfo => "Update Tile Object Cull 2";
        public override bool keepAlive => true;

        TileGridObjectLayerData2 mLayerData;
        long mKey;

        static ObjectPool<FrameActionUpdateTileObjectCull2> mPool = new ObjectPool<FrameActionUpdateTileObjectCull2>(1, () => new FrameActionUpdateTileObjectCull2());
    }
}
