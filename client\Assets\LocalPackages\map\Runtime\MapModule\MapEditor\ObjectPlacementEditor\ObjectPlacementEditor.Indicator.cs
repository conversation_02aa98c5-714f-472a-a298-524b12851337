﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        void DestroyIndicator()
        {
            mPrefabIndicator.OnDestroy();
            mPrefabIndicator = null;
        }

        void UpdateIndicator(Vector3 worldPos, GameObject prefab, Transform parent)
        {
            mPrefabIndicator.SetPrefab(prefab, parent);
            if (prefab != null)
            {
                mPrefabIndicator.SetActive(true);

                mPrefabIndicator.SetPosition(worldPos);
                mPrefabIndicator.SetYRotation(mPrefabRotationSetting.yRotation);
            }
            else
            {
                mPrefabIndicator.SetActive(false);
            }
        }

        void HideIndicator()
        {
            mPrefabIndicator.SetPrefab(null, null);
        }

        PrefabIndicator mPrefabIndicator = new PrefabIndicator(false);
    }
}
#endif