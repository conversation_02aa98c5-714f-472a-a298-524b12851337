﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //拼接地表层使用的数据
    public partial class SimpleBlendTerrainLayerData : MapLayerData
    {
        public SimpleBlendTerrainLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, ushort[] tiles, string[] tilePrefabPaths, List<ModelData[,]> otherLODTiles, TerrainRenderTextureLODSetting[] lodSettings) : base(header, config, map)
        {
            Debug.Assert(tiles != null && tilePrefabPaths != null);
            mTiles = tiles;
            mPrefabPaths = tilePrefabPaths;
            mOtherLODTiles = otherLODTiles;
            mLODSettings = lodSettings;

            //check lod settings
            if (lodSettings != null)
            {
                mDefaultLODCount = config.lodConfigs.Length - lodSettings.Length;
            }
            else
            {
                mDefaultLODCount = config.lodConfigs.Length;
            }

            Debug.Assert(mDefaultLODCount > 0, "Invalid ground layer LOD Setting!!!");

            if (map.isEditorMode)
            {
                mLastViewport = map.viewport;
            }
            else
            {
                mLastViewport = new Rect(-10000, -10000, 0, 0);
            }
        }

        public override void OnDestroy()
        {
            DestroyLODTiles();
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            Debug.Assert(false, "Can't be here!");
            return false;
        }

        public override bool Contains(int objectID)
        {
            Debug.Assert(false, "Can't be here!");
            return false;
        }

        public string GetTilePrefabPath(int x, int y, out int tileType)
        {
            var idx = y * mCols + x;
            tileType = mTiles[idx];
            return mPrefabPaths[tileType];
        }

        public ushort GetTileType(int x, int y)
        {
            var idx = y * mCols + x;
            return mTiles[idx];
        }

        public override bool isGameLayer => true;

        public Rect lastViewport { get { return mLastViewport; } }
        public string[] tilePrefabPaths { get { return mPrefabPaths; } }
        public ushort[] tiles { get { return mTiles; } }

        ushort[] mTiles;
        Rect mLastViewport;
        float mLastZoom;
        string[] mPrefabPaths;
    }
}
