﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.8.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [ExecuteInEditMode]
    [Black]
    public class LODLayerLogic : MapLayerLogic
    {
        public LODLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as LODLayer;
                return layer;
            }
        }
    }
}


#endif