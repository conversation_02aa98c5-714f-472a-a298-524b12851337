﻿ 



 
 


using System.IO;
using TFW.Map.config;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadCityTerritoryLayer(BinaryReader reader)
        {
            var pos = GetSectionDataStartPosition(MapDataSectionType.CityTerritoryLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            List<config.CityTerritorySubLayerData> subLayers = new List<CityTerritorySubLayerData>();

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();

            int nTerritories = reader.ReadInt32();
            config.CityTerritoryData[] territories = new config.CityTerritoryData[nTerritories];
            for (int i = 0; i < nTerritories; ++i)
            {
                int territoryID = reader.ReadInt32();

                int nBuildings = reader.ReadInt32();
                Vector3[] buildingPositions = new Vector3[nBuildings];
                for (int b = 0; b < nBuildings; ++b)
                {
                    buildingPositions[b] = Utils.ReadVector3(reader);
                }

                string assetPath = Utils.ReadString(reader);
                Vector3 meshPosition = Utils.ReadVector3(reader);
                Rect worldBounds = Utils.ReadRect(reader);

                territories[i] = new config.CityTerritoryData(territoryID, assetPath, nBuildings > 0 ? buildingPositions[0] : Vector3.zero, meshPosition, worldBounds, Color.white, "", -1);
            }

            short[,] grids = new short[rows, cols];
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    grids[i, j] = reader.ReadInt16();
                }
            }

            var subLayer0 = new config.CityTerritorySubLayerData(rows, cols, tileWidth, tileHeight, grids, territories, null, new List<CityTerritoryDataBlock>(), 0, 0, null);
            subLayers.Add(subLayer0);

            var config = LoadCityTerritoryLayerLODConfig(reader);
            //-----------------------version 1 end----------------------------------
            //-----------------------version 2 start----------------------------------
            if (version >= 2)
            {
                for (int i = 0; i < nTerritories; ++i)
                {
                    territories[i].color = Utils.ReadColor(reader);
                }
            }
            //-----------------------version 2 end----------------------------------
#if true
            //-----------------------version 3 start----------------------------------
            int extraSubLayerCount = 0;
            if (version >= 3) {
                extraSubLayerCount = reader.ReadInt32();
                for (int s = 0; s < extraSubLayerCount; ++s)
                {
                    rows = reader.ReadInt32();
                    cols = reader.ReadInt32();
                    tileWidth = reader.ReadSingle();
                    tileHeight = reader.ReadSingle();

                    nTerritories = reader.ReadInt32();
                    territories = new config.CityTerritoryData[nTerritories];
                    for (int i = 0; i < nTerritories; ++i)
                    {
                        int territoryID = reader.ReadInt32();

                        int nBuildings = reader.ReadInt32();
                        Vector3[] buildingPositions = new Vector3[nBuildings];
                        for (int b = 0; b < nBuildings; ++b)
                        {
                            buildingPositions[b] = Utils.ReadVector3(reader);
                        }

                        string assetPath = Utils.ReadString(reader);
                        Vector3 meshPosition = Utils.ReadVector3(reader);
                        Rect worldBounds = Utils.ReadRect(reader);
                        Color color = Utils.ReadColor(reader);

                        territories[i] = new config.CityTerritoryData(territoryID, assetPath, nBuildings > 0 ? buildingPositions[0] : Vector3.zero, meshPosition, worldBounds, color, "", -1);
                    }

                    grids = new short[rows, cols];
                    for (int i = 0; i < rows; ++i)
                    {
                        for (int j = 0; j < cols; ++j)
                        {
                            grids[i, j] = reader.ReadInt16();
                        }
                    }

                    var subLayer = new config.CityTerritorySubLayerData(rows, cols, tileWidth, tileHeight, grids, territories, null, new List<CityTerritoryDataBlock>(), 0, 0, null);
                    subLayers.Add(subLayer);
                }
            }
            //-----------------------version 3 end----------------------------------
#endif
            var layer = new config.CityTerritoryLayerData(layerID, name, offset, config, subLayers);
            return layer;
        }

        config.MapLayerLODConfig LoadCityTerritoryLayerLODConfig(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, 100, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }
    }
}
