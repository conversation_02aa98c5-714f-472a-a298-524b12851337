﻿ 



 
 

﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class CameraColliderEditor
    {
        public CameraColliderEditor(CameraColliderLayer layer, System.Action OnRepaintGUI, System.Action OnRepaintScene)
        {
            mLayer = layer;
            mOnRepaintGUI = OnRepaintGUI;
            mOnRepaintScene = OnRepaintScene;

            CameraColliderLayerEventHandlers handlers = new CameraColliderLayerEventHandlers();
            handlers.onDeleteCollision = OnDeleteCameraCollider;
            mLayer.SetEventHandlers(handlers);
        }

        public void OnDestroy()
        {
            for (int i = 0; i < mTools.Count; ++i)
            {
                mTools[i].OnDestroy();
            }
            mTools.Clear();

            SetSelection(null, 0);
            mLayer.SetEventHandlers(null);
        }

        public void AddTool(CameraColliderEditorTool tool)
        {
            mTools.Add(tool);
        }

        CameraColliderEditorTool GetTool(CameraColliderEditorToolType type)
        {
            for (int i = 0; i < mTools.Count; ++i)
            {
                if (mTools[i].type == type)
                {
                    return mTools[i];
                }
            }
            return null;
        }

        public CameraColliderEditorTool GetActiveTool()
        {
            return mActiveTool;
        }

        public void SetActiveTool(CameraColliderEditorToolType type)
        {
            var tool = GetTool(type);
            if (mActiveTool != null)
            {
                mActiveTool.OnDisabled();
            }
            mActiveTool = tool;
            if (mActiveTool != null)
            {
                mActiveTool.OnEnabled();
            }
        }

        public void Update(Event e)
        {
            if (mActiveTool != null)
            {
                mActiveTool.Update(e);
            }
        }

        public void DrawScene()
        {
            if (mActiveTool != null)
            {
                mActiveTool.DrawScene();
            }
        }

        public void DrawGUI()
        {
            if (mActiveTool != null)
            {
                mActiveTool.DrawGUI();
            }
        }

        public void DrawMenu()
        {
            if (mActiveTool != null)
            {
                mActiveTool.DrawMenu();
            }
        }

        public void SetSelection(CameraColliderData data, int vertexIndex)
        {
            if (mSelectedCollisionDataID != 0)
            {
                var d = Map.currentMap.FindObject(mSelectedCollisionDataID) as CameraColliderData;
                if (d != null)
                {
                    mLayer.SetSelected(d.id, false);
                }
                mSelectedCollisionDataID = 0;
                mSelectedVertexIndex = -1;
            }

            if (data != null)
            {
                mSelectedCollisionDataID = data.GetEntityID();
                mLayer.SetSelected(data.id, true);
            }
            else
            {
                mSelectedCollisionDataID = 0;
            }
            mSelectedVertexIndex = vertexIndex;
            RepaintGUI();
        }

        public void Pick(Vector2 screenPos)
        {
            System.Func<IMapObjectData, bool> func = (IMapObjectData data) =>
            {
                var collisionData = data as CameraColliderData;

                int hitVertex = -1;
                var vertices = collisionData.GetOutlineVertices(mLayer.displayType);

                //first try top outline
                if (collisionData.topOutline != null)
                {
                    var worldPos1 = Map.currentMap.FromScreenToWorldPosition(screenPos, collisionData.height);
                    var topVertices = collisionData.topOutline.outline;
                    for (int i = 0; i < topVertices.Count; ++i)
                    {
                        if (Utils.IsHitRectangle(worldPos1, topVertices[i], collisionData.displayRadius))
                        {
                            hitVertex = i + vertices.Count;
                            break;
                        }
                    }
                }
                
                if (hitVertex < 0)
                {
                    //then try bottom outline
                    var worldPos0 = Map.currentMap.FromScreenToWorldPosition(screenPos);
                    for (int i = 0; i < vertices.Count; ++i)
                    {
                        if (Utils.IsHitRectangle(worldPos0, vertices[i], collisionData.displayRadius))
                        {
                            hitVertex = i;
                            break;
                        }
                    }
                }

                if (hitVertex >= 0)
                {
                    SetSelection(collisionData, hitVertex);
                }
                else
                {
                    SetSelection(null, -1);
                }

                if (mSelectedCollisionDataID != 0)
                {
                    RepaintScene();
                    return true;
                }

                return false;
            };

            mLayer.Traverse(func);
        }

        public void CreateTopOutline(int objectID, float height)
        {
            mLayer.CreateTopOutline(objectID, height);
        }

        public void RepaintScene()
        {
            mOnRepaintScene();
        }

        public void RepaintGUI()
        {
            mOnRepaintGUI();
        }

        public void ClearVertexSelection()
        {
            mSelectedVertexIndex = -1;
        }

        void OnDeleteCameraCollider(int dataID)
        {
            if (dataID == selectedObjectID)
            {
                SetSelection(null, 0);
                RepaintGUI();

                layer.ShowOutline(layer.displayType);
            }
        }

        public void DeleteCameraCollider()
        {
            if (selectedObjectID != 0)
            {
                var act = new ActionRemoveCameraCollider(mLayer.id, selectedObjectID);
                ActionManager.instance.PushAction(act);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a camera collider first!", "OK");
            }
        }

        public void Clone()
        {
            if (selectedObjectID != 0)
            {
                var act = new ActionCloneCameraCollider(mLayer.id, selectedObjectID);
                ActionManager.instance.PushAction(act);
            }
        }

        public void CheckCollideWithMapObstacles()
        {
            if (selectedObjectID != 0)
            {
                var mapObstacleManager = Map.currentMap.data.localObstacleManager;
                if (mapObstacleManager != null)
                {
                    var collisionData = Map.currentMap.FindObject(selectedObjectID) as CameraColliderData;
                    bool intersectedWithObstacles = false;
                    if (collisionData.IsConvex(layer.displayType))
                    {
                        intersectedWithObstacles = mapObstacleManager.IsIntersectedWithObstacles(collisionData.GetOutlineVertices(layer.displayType), collisionData.GetBounds());
                    }
                    layer.SetIntersectedWithObstacles(selectedObjectID, intersectedWithObstacles);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a camera collider first!", "OK");
            }
        }

        public CameraColliderLayer layer { get { return mLayer; } }
        public int selectedObjectID { get { return mSelectedCollisionDataID; } }
        public int selectedVertexIndex { get { return mSelectedVertexIndex; } }

        CameraColliderLayer mLayer;
        int mSelectedCollisionDataID;
        int mSelectedVertexIndex = -1;
        
        System.Action mOnRepaintGUI;
        System.Action mOnRepaintScene;
        List<CameraColliderEditorTool> mTools = new List<CameraColliderEditorTool>();
        CameraColliderEditorTool mActiveTool;
    }
}

#endif