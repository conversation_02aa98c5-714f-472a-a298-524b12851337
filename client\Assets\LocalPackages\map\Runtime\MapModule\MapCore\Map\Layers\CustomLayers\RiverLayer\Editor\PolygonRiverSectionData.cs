﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/11/28

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //河流的一段数据,包含一个独立的贴图
    public class PolygonRiverSectionData
    {
        public PolygonRiverSectionData(int id, List<Vector3> outline, Color[] textureData, int textureSize)
        {
            mID = id;
            if (textureData != null || textureSize > 0)
            {
                SetTextureData(textureData, textureSize);
            }
            mOutline = Utils.CalculateCopy(outline);
            CalculateBounds();
            mTextureDataDirty = false;
        }

        public void OnDestroy()
        {
            Object.DestroyImmediate(mTexture);
            mTextureCache = null;
            mOutline = null;
            mID = 0;
        }

        public void Move(Vector3 offset)
        {
            for (int i = 0; i < mOutline.Count; ++i)
            {
                mOutline[i] += offset;
            }

            CalculateBounds();
        }

        public int GetVertexIndex(Vector3 pos)
        {
            for (int i = 0; i < mOutline.Count; ++i)
            {
                if (mOutline[i] == pos)
                {
                    return i;
                }
            }
            return -1;
        }

        public Vector3 GetVertex(int i)
        {
            return mOutline[i];
        }

        public void SetVertex(int i, Vector3 pos)
        {
            mOutline[i] = pos;

            CalculateBounds();
        }

        void CalculateBounds()
        {
            mBounds = Utils.CalculateRect(mOutline);
        }

        public int GetVertexCount()
        {
            return mOutline.Count;
        }

        public void SetTextureData(Texture2D texture)
        {
            if (mTexture != null && texture.width == mTexture.width && texture.height == mTexture.height)
            {
                var pixels = texture.GetPixels();
                if (pixels != null) {
                    SetTextureData(pixels, texture.width);
                }
            }
        }

        public void SetTextureData(Color[] data, int textureSize)
        {
            if (mTexture == null)
            {
                GenerateTexture(data, textureSize);
            }
            else
            {
                if (data != null)
                {
                    Debug.Assert(mTexture.width * mTexture.height == data.Length);
                    UpdateTextureData(data, 0, 0, mTexture.width, mTexture.height, true);
                }
            }

            mTextureDataDirty = true;
        }

        public bool UpdateTextureData(Color[] data, int x, int y, int width, int height, bool updateCache)
        {
            if (mTexture == null)
            {
                return false;
            }

            mTexture.SetPixels(x, y, width, height, data);
            mTexture.Apply();

            if (updateCache)
            {
                for (int i = 0; i < height; ++i)
                {
                    for (int j = 0; j < width; ++j)
                    {
                        var dstIdx = (i + y) * mTexture.width + x + j;
                        var srcIdx = i * height + j;
                        mTextureCache[dstIdx] = data[srcIdx];
                    }
                }
            }

            mTextureDataDirty = true;

            return true;
        }


        //splitter是否属于这个section
        public int GetSplitterIndex(Vector3 startPos, Vector3 endPos)
        {
            bool foundStart = false;
            bool foundEnd = false;
            for (int i = 0; i < mOutline.Count; ++i)
            {
                if (mOutline[i] == startPos)
                {
                    foundStart = true;
                }
                if (mOutline[i] == endPos)
                {
                    foundEnd = true;
                }
                if (foundStart && foundEnd)
                {
                    return i;
                }
            }

            return -1;
        }

        void GenerateTexture(Color[] textureData, int textureSize)
        {
            Debug.Assert(mTexture == null && textureSize > 0);
            if (textureData == null || textureData.Length == 0)
            {
                int n = textureSize * textureSize;
                textureData = new Color[n];
                for (int i = 0; i < n; ++i)
                {
                    textureData[i] = Color.white;
                }
            }
            mTexture = new Texture2D(textureSize, textureSize, TextureFormat.RGBA32, false);
            mTexture.wrapMode = TextureWrapMode.Clamp;
            mTexture.SetPixels(textureData);
            mTexture.Apply();
            mTextureCache = (Color[])textureData.Clone();

            mTextureDataDirty = true;
        }

        public Rect bounds { get { return mBounds; } }
        public int id { get { return mID; } }
        public Texture2D texture { get { return mTexture; } }
        public bool textureDataDirty { get { return mTextureDataDirty; } set { mTextureDataDirty = value; } }
        public Color[] textureData { get { return mTextureCache; } }
        public List<Vector3> outlineRaw { get { return mOutline; } }
        public List<Vector3> outlineCopy
        {
            get
            {
                var copy = new List<Vector3>(mOutline.Count);
                copy.AddRange(mOutline);
                return copy;
            }
        }

        Texture2D mTexture;
        Color[] mTextureCache;
        List<Vector3> mOutline;
        int mID;
        Rect mBounds;
        bool mTextureDataDirty = false;
    }
}

#endif