﻿ 



 
 

﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class LayerHelper
    {
        public static void BeginSetLayerRecursively(GameObject gameObject, int layer)
        {
            foreach (Transform trans in gameObject.GetComponentsInChildren<Transform>(true))
            {
                var originalLayer = trans.gameObject.layer;
                mLayerCache[trans.gameObject.GetInstanceID()] = originalLayer;
                trans.gameObject.layer = layer;
            }
        }

        public static void RestoreLayerRecursively(GameObject gameObject)
        {
            foreach (Transform trans in gameObject.GetComponentsInChildren<Transform>(true))
            {
                int originalLayer;
                bool found = mLayerCache.TryGetValue(trans.gameObject.GetInstanceID(), out originalLayer);
                Debug.Assert(found);
                trans.gameObject.layer = originalLayer;
            }

            mLayerCache.Clear();
        }

        static Dictionary<int, int> mLayerCache = new Dictionary<int, int>();
    }
}
