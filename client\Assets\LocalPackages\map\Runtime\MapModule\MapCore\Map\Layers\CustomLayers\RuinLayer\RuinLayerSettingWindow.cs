﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System;

namespace TFW.Map
{
    public class RuinLayerSettingWindow : EditorWindow
    {
        public void Show(string layerName)
        {
            mLayerName = layerName;
        }

        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Height", mLayerHeight);
            GUILayout.EndHorizontal();

            mUsePerObjectProperty = EditorGUILayout.ToggleLeft("Use Per Object Property", mUsePerObjectProperty);

            if (GUILayout.Button("Create"))
            {
                bool valid = CheckParameter();
                if (valid)
                {
                    var layer = (Map.currentMap as EditorMap).CreateRuinLayer(mLayerName, mLayerWidth, mLayerHeight, !mUsePerObjectProperty);
                    layer.asyncLoading = false;

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0)
            {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public string mLayerName;
        public bool mUsePerObjectProperty = false;
    }
}

#endif