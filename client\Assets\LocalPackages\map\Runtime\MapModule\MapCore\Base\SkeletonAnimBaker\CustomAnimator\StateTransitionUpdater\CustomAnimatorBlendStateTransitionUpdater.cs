﻿using UnityEngine;

namespace TFW.Map
{
    //带动画过渡,使用unity animation transition的参数
    public class CustomAnimatorBlendStateTransitionUpdater : ICustomAnimatorStateTransitionUpdater
    {
        enum TransitionState
        {
            NotInTransition,
            InTransition,
            FinishTransitionNextFrame,
        }

        public CustomAnimatorBlendStateTransitionUpdater(CustomAnimationState initState)
        {
            mCurrentState = initState;
            mNextState = null;
            mState = TransitionState.NotInTransition;
        }

        public void Update(float playSpeed, AnimationParameterInfo[] parameters, CustomAnimationStateMachine statemachine)
        {
            if (mState == TransitionState.InTransition)
            {
                UpdateTransition();
            }
            else if (mState == TransitionState.FinishTransitionNextFrame)
            {
                StopTransition();
                SwitchToNextState();
            }
            else
            {
                //not in transition
                float transitionStartTime = 0;
                float transitionDuration = 0;
                var nextState = mCurrentState?.Update(playSpeed, parameters, true, statemachine, out transitionDuration, out transitionStartTime);
                if (nextState != null)
                {
                    SetState(nextState, 0, transitionDuration, transitionStartTime, InterruptionType.WaitUntilFinishTransition);
                }
            }
        }

        //准备切换到下一个状态,先要渡过transition duration
        public void SetState(CustomAnimationState state, float nextStateStartOffset, float transitionDuration, float curStateTransitionStartTime, InterruptionType interruptionType)
        {
            mNextStateStartOffset = nextStateStartOffset;
            mTransitionDuration = transitionDuration;
            mCurrentStateTransitionStartTime = curStateTransitionStartTime;

            if (currentState == state ||
                mState == TransitionState.FinishTransitionNextFrame ||
                mState == TransitionState.InTransition)
            {
                StopTransition();
                mNextState = state;
                SwitchToNextState();
            }
            else
            {
                //开始transition
                StartTransition();
                mNextState = state;
            }
        }

        void StartTransition()
        {
            mState = TransitionState.InTransition;
            mDurationTimer = 0;
            mNextStateTransitionStartTime = mNextStateStartOffset;
        }

        void StopTransition()
        {
            mState = TransitionState.NotInTransition;
            //Debug.Log("StopTransition: " + mTransitionDuration.ToString());
            mDurationTimer = mTransitionDuration;
        }

        void UpdateTransition()
        {
            mDurationTimer += Time.deltaTime;
            //Debug.LogError("UpdateTransition: " + mDurationTimer.ToString());
            if (mDurationTimer >= mTransitionDuration)
            {
                mState = TransitionState.FinishTransitionNextFrame;
                mDurationTimer = mTransitionDuration;
            }
        }

        //正式切换到下一个状态
        void SwitchToNextState()
        {
            Debug.Assert(mNextState != null);
            if (mCurrentState != null)
            {
                mCurrentState.Reset(0);
            }
            mCurrentState = mNextState;
            mNextState.Reset(mNextStateTransitionStartTime + mTransitionDuration);
            mNextState = null;
            mTransitionDuration = 0;

#if UNITY_EDITOR
            Debug.Assert(mState == TransitionState.NotInTransition);
#endif
        }

        public CustomAnimationState currentState { get { return mCurrentState; } }
        public CustomAnimationState nextState { get { return mNextState; } }
        public bool IsInTransition { get { return mState != TransitionState.NotInTransition; } }
        public float normalizedDurationRatio { get { return mTransitionDuration == 0 ? 0 : mDurationTimer / mTransitionDuration; } }
        public float currentStateFrameInTransitionDuration
        {
            get
            {
                float time = mCurrentStateTransitionStartTime + mDurationTimer;
                int frame = mCurrentState.GetFrame(time);
                return Mathf.Min(frame, mCurrentState.animInfo.totalFrames - 1);
            }
        }

        public float nextStateTimeInTransitionDuration
        {
            get
            {
                float time = mNextState == null ? 0 : mNextStateTransitionStartTime + mDurationTimer;
                if (mNextState != null)
                {
                    time = Mathf.Min(time, mNextState.animInfo.lengthInSeconds);
                }
                return time;
            }
        }

        public float nextStateFrameInTransitionDuration
        {
            get
            {
                int frame = mNextState == null ? 0 : mNextState.GetFrame(mNextStateTransitionStartTime + mDurationTimer);
                if (mNextState != null)
                {
                    frame = Mathf.Min(frame, mNextState.animInfo.totalFrames - 1);
                }
                return frame;
            }
        }

        CustomAnimationState mCurrentState;
        CustomAnimationState mNextState;
        //在融合过程中的打断状态
        //InterruptInfo mInterruptInfo = new InterruptInfo();
        TransitionState mState = TransitionState.NotInTransition;
        float mDurationTimer = 0;
        float mTransitionDuration = 0;
        //动画过渡开始时当前状态的时间
        float mCurrentStateTransitionStartTime;
        //动画过渡开始时下一个状态的起始时间
        float mNextStateTransitionStartTime;
        float mNextStateStartOffset;
    }
}
