﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class RailwayLayer : ModelLayer
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            mVersion = Utils.ReadVersion(reader);

            PrepareLoading();

            LoadSetting(reader);
            var layerData = LoadLayerData(reader, AllocateID(), mVersion);

            reader.Close();

            var layer = new RailwayLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static void LoadSetting(BinaryReader reader)
        {
            long pathMapperDataOffset = reader.ReadInt64();
            long curPos = reader.BaseStream.Position;
            reader.BaseStream.Position = pathMapperDataOffset;
            LoadPathMapper(reader);
            reader.BaseStream.Position = curPos;
        }

        static int[] PrecalculateObjectIDs(int n)
        {
            int[] ids = new int[n];
            for (int i = 0; i < n; ++i)
            {
                ids[i] = Map.currentMap.nextCustomObjectID;
            }
            return ids;
        }

        static config.MapLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            float layerWidth = reader.ReadSingle();
            float layerHeight = reader.ReadSingle();
            int nObjects = reader.ReadInt32();
            int nGroups = 0;
            if (mVersion.minorVersion >= 2)
            {
                nGroups = reader.ReadInt32();
            }
            config.MapObjectData[] objects = new config.MapObjectData[nObjects];

            var objectIDs = PrecalculateObjectIDs(nObjects);
            var groupIDs = PrecalculateObjectIDs(nGroups);
            for (int i = 0; i < nObjects; ++i)
            {
                objects[i] = LoadRailObjectData(reader, objectIDs, groupIDs);
            }

            var config = LoadMapLayerLODConfig(reader, version);

            config.ModelLODGroupManager lodGroupManager = new TFW.Map.config.ModelLODGroupManager();

            lodGroupManager = LoadModelLODGroupManager(reader, nGroups, objectIDs, groupIDs);

            int count = reader.ReadInt32();
            float width = reader.ReadSingle();
            float radius = reader.ReadSingle();
            Vector3 center = Utils.ReadVector3(reader);
            float railPrefabLength = reader.ReadSingle();

            var layerData = new config.RailwayLayerData(layerID, layerName, layerOffset, config, lodGroupManager, layerWidth, layerHeight, objects, count, width, radius, center, railPrefabLength);
            layerData.active = active;
            return layerData;
        }

        static config.RailObjectData LoadRailObjectData(BinaryReader reader, int[] ids, int[] groupIDs)
        {
            var data = new config.RailObjectData();
            data.flag = reader.ReadInt32();
            var localObjectID = reader.ReadInt32();
            data.position = Utils.ReadVector3(reader);
            data.rotation = Utils.ReadQuaternion(reader);
            data.scale = Utils.ReadVector3(reader);
            data.SetID(ids[localObjectID - 1]);

            short prefabPathIndex = reader.ReadInt16();
            string prefabPath = mLoadPrefabPathStringTable[prefabPathIndex];
            var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(data.id, prefabPath, false, false);
            data.modelTemplateID = modelTemplate.id;

            data.type = (RailObjectType)reader.ReadInt32();
            data.isGroupLeader = reader.ReadBoolean();
            data.groupID = groupIDs[reader.ReadInt32() - 1];
            data.railIndex = reader.ReadInt32();
            data.segmentIndex = reader.ReadInt32();

            return data;
        }

        static config.ModelLODGroupManager LoadModelLODGroupManager(BinaryReader reader, int nGroups, int[] ids, int[] groupIDs)
        {
            config.ModelLODGroupManager lodGroupManager = new config.ModelLODGroupManager();
            lodGroupManager.groups = new config.ModelLODGroup[nGroups];
            int groupCount = reader.ReadInt32();
            if (nGroups == 0)
            {
                nGroups = groupCount;
            }
            for (int i = 0; i < nGroups; ++i)
            {
                var group = new config.ModelLODGroup();
                var idIndex = reader.ReadInt32();
                group.SetID(groupIDs[idIndex - 1]);
                group.combineModels = reader.ReadBoolean();
                group.leaderObjectID = ids[reader.ReadInt32() - 1];
                group.lod = reader.ReadInt32();

                lodGroupManager.groups[i] = group;
            }
            return lodGroupManager;
        }

        static config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader, Version version)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                MapLayerLODConfigFlag flag = MapLayerLODConfigFlag.None;
                if (version.minorVersion >= 3)
                {
                    flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                }
                string name = "";
                if (version.minorVersion >= 4)
                {
                    name = Utils.ReadString(reader);
                }
                else
                {
                    name = Map.currentMap.data.lodManager.ConvertZoomToName(zoom);
                }
                config.lodConfigs[i] = new config.MapLayerLODConfigItem(name, zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, 0);
            }
            return config;
        }

        static void LoadPrefabManager(BinaryReader reader)
        {
            var prefabManagerData = LoadPrefabManagerData(reader);
            var prefabManager = (Map.currentMap.data as EditorMapData).gridModelLayerPrefabManager;
            EditorMap.CreatePrefabManager(prefabManager, prefabManagerData, false, true, false);
        }

        static config.PrefabManager LoadPrefabManagerData(BinaryReader reader)
        {
            var prefabManager = new config.PrefabManager();
            int nGroups = reader.ReadInt32();
            prefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                prefabManager.groups[i] = LoadPrefabGroup(reader);
            }
            return prefabManager;
        }

        static config.PrefabGroup LoadPrefabGroup(BinaryReader reader)
        {
            config.PrefabGroup group = new config.PrefabGroup();
            group.name = Utils.ReadString(reader);
            group.color = Utils.ReadColor32(reader);

            int nPrefabs = reader.ReadInt32();
            group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                group.prefabPaths[i] = new config.PrefabSubGroup();
                string prefabPath = Utils.ReadString(reader);
                prefabPath = mLoadPathMapper.Unmap(prefabPath);
                group.prefabPaths[i].prefabPath = prefabPath;
                //load subgroup prefabs

                int subGroupPrefabCount = reader.ReadInt32();
                group.prefabPaths[i].subGroupPrefabPaths = new string[subGroupPrefabCount];
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    string subgroupPrefabPath = Utils.ReadString(reader);
                    group.prefabPaths[i].subGroupPrefabPaths[k] = mLoadPathMapper.Unmap(subgroupPrefabPath);
                }
            }
            return group;
        }

        static void LoadPathMapper(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var path = Utils.ReadString(reader);
                var guid = Utils.ReadString(reader);

                mLoadPathMapper.pathToGuid[path] = guid;
            }

            int pathCount = reader.ReadInt32();
            mLoadPrefabPathStringTable = new List<string>(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                mLoadPrefabPathStringTable.Add(mLoadPathMapper.Unmap(Utils.ReadString(reader)));
            }
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }

        static void PrepareLoading()
        {
            mLoadPathMapper = new PathMapper();
            mLoadPrefabPathStringTable = new List<string>();
        }

        static PathMapper mLoadPathMapper;
        static List<string> mLoadPrefabPathStringTable;
        static Version mVersion;
    }
}

#endif