﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/11/25

using UnityEngine;

namespace TFW.Map
{
    public class PolygonRiverView : PolygonObjectView
    {
        public PolygonRiverView(IMapObjectData data, MapLayerView layerView) : base(data, layerView)
        {
        }

        //创建视图使用的模型
        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            Debug.Assert(mModel == null);
            Debug.Assert(data != null);

            var riverData = data as PolygonRiverData;
            ModelBase model = new PolygonRiverModel(riverData);
            model.transform.SetParent(mLayerView.root.transform, true);

            return model;
        }

        public void GenerateMesh(RiverGenerationParameter param)
        {
            if (mModel != null)
            {
                var riverModel = mModel as PolygonRiverModel;
                var river = Map.currentMap.FindObject(objectDataID) as PolygonRiverData;
                riverModel.GenerateMesh(river, param);
            }
        }

        public void RecreateMesh()
        {
            if (mModel != null)
            {
                var riverModel = mModel as PolygonRiverModel;
                var river = Map.currentMap.FindObject(objectDataID) as PolygonRiverData;
                riverModel.RecreateMesh(river);
            }
        }

        public void ShowSplitter(bool show)
        {
            Debug.Assert(mModel != null);
            var riverModel = mModel as PolygonRiverModel;
            riverModel.ShowSplitter(show);
        }

        public void SetSplitterColor(int index, Color color)
        {
            var riverModel = mModel as PolygonRiverModel;
            riverModel.SetSplitterColor(index, color);
        }

        public void AddSplitter(PolygonRiverSplitterData data, float radius, bool visible)
        {
            Debug.Assert(mModel != null);
            var riverModel = mModel as PolygonRiverModel;
            var river = Map.currentMap.FindObject(objectDataID) as PolygonRiverData;
            riverModel.GenerateSplitter(-1, data, radius, visible);
        }

        public void InsertSplitter(int idx, PolygonRiverSplitterData data, float radius, bool visible)
        {
            Debug.Assert(mModel != null);
            var riverModel = mModel as PolygonRiverModel;
            var river = Map.currentMap.FindObject(objectDataID) as PolygonRiverData;
            riverModel.GenerateSplitter(idx, data, radius, visible);
        }

        public void DeleteSplitter(int index)
        {
            var riverModel = mModel as PolygonRiverModel;
            riverModel.DeleteSplitter(index);
        }

        public GameObject GetMeshObject(int section)
        {
            if (mModel != null)
            {
                var riverModel = mModel as PolygonRiverModel;
                return riverModel.GetRiverMesh(section);
            }
            return null;
        }

        public void Move(Vector3 offset)
        {
            if (mModel != null)
            {
                var data = Map.currentMap.FindObject(objectDataID) as PolygonObjectData;
                (mModel as PolygonRiverModel).Move(data, offset);
            }
        }

        public void SetRiverMaterialProperties(int section, Material materialProperties)
        {
            (mModel as PolygonRiverModel).SetRiverMaterialProperties(section, materialProperties);
        }

        public Material GetRiverMaterial(int section)
        {
            return (mModel as PolygonRiverModel).GetRiverMaterial(section);
        }

        public void SetShaderLOD(int lod)
        {
            (mModel as PolygonRiverModel).SetShaderLOD(lod);
        }

        public void ChangeMaterial(Material newMtl)
        {
            (mModel as PolygonRiverModel).ChangeMaterial(newMtl);
        }

        public void UpdateTexture()
        {
            (mModel as PolygonRiverModel).UpdateTexture(objectDataID);
        }
    }
}


#endif