﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    [System.Flags]
    public enum MoveAxis
    {
        None = 0,
        X = 1,
        Y = 2,
        Z = 4,
        All = X | Y | Z,
    }

    [ExecuteInEditMode]
    [Black]
    public class MapLayerLogic : MonoBehaviour
    {
        public int layerID { get; set; }
        public bool showLayerGrid { get; set; } = true;
        public bool showLayerBounds { get; set; } = true;
        public EditorMapLayerLODSetting setting = new EditorMapLayerLODSetting();
        protected virtual MoveAxis moveAxis { get { return MoveAxis.None; } }

        protected virtual void OnRemoveMapLayer()
        {
            Map.currentMap.RemoveMapLayerByID(layerID);
        }

        void Update()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                //fix transform
                
                if (transform.position != Vector3.zero)
                {
                    Vector3 position = transform.position;
                    if (!moveAxis.HasFlag(MoveAxis.X))
                    {
                        position.x = 0;
                    }
                    if (!moveAxis.HasFlag(MoveAxis.Y))
                    {
                        position.y = 0;
                    }
                    if (!moveAxis.HasFlag(MoveAxis.Z))
                    {
                        position.z = 0;
                    }
                    transform.position = position;
                }
                
                if (transform.rotation != Quaternion.identity)
                {
                    transform.rotation = Quaternion.identity;
                }
                if (transform.localScale != Vector3.one)
                {
                    transform.localScale = Vector3.one;
                }

                var layer = Map.currentMap.GetMapLayerByID(layerID);
                layer.name = gameObject.name;

                OnUpdate();
            }
        }

        protected virtual void OnUpdate() { }

        public void SetGridVisible(bool visible)
        {
            if (mGrid != null)
            {
                mGrid.SetActive(visible);
            }
        }

        public void SetLayerBoundsVisible(bool visible)
        {
            if (mLayerBounds != null)
            {
                mLayerBounds.SetActive(visible);
            }
        }

        public void Initialize(MapLayerData layerData, MapLayerView layerView)
        {
            this.layerID = layerData.id;

            mBoundsCreator = EditorUtils.GetMapLayerBoundsCreator();
            if (layerData.horizontalTileCount > 1 && layerData.verticalTileCount > 1)
            {
                mGridCreator = EditorUtils.GetMapLayerGridCreator(layerData.gridType);
            }

            if (mBoundsCreator != null)
            {
                mBoundsCreator.SetMapLayer(layerData);
            }
            if (mGridCreator != null)
            {
                mGridCreator.SetMapLayer(layerData);
            }

            if (mBoundsCreator != null)
            {
                mLayerBounds = mBoundsCreator.CreateLayerBounds(Color.blue);
                mLayerBounds.transform.position = layerData.layerOffset;
                mLayerBounds.transform.SetParent(layerView.root.transform, true);
                mLayerBounds.SetActive(false);
            }

            if (mGridCreator != null)
            {
                mGrid = mGridCreator.CreateGrid(Color.yellow);
                mGrid.transform.SetParent(layerView.root.transform);
                mGrid.SetActive(false);
            }

            OnInit();
        }

        public void Initialize(MapLayerBase layer)
        {
            this.layerID = layer.id;

            OnInit();
        }

        protected virtual void OnInit() { }

        public void RecreateGrid()
        {
            if (mGridCreator != null)
            {
                if (mGrid != null)
                {
                    GameObject.DestroyImmediate(mGrid);
                    mGrid = null;
                }

                mGrid = mGridCreator.CreateGrid(Color.yellow);
                var layer = Map.currentMap.GetMapLayerByID(layerID);
                var layerView = layer.GetLayerView();
                mGrid.transform.SetParent(layerView.root.transform);
                mGrid.SetActive(false);
            }
        }

        public void UpdateGizmoVisibilityState()
        {
            SetGridVisible(showLayerGrid);
            SetLayerBoundsVisible(showLayerBounds);
        }

        public void DrawGizmoUI()
        {
            EditorGUILayout.BeginHorizontal();
            bool grid = EditorGUILayout.ToggleLeft(new GUIContent("Show Layer Grid", "显示layer的网格"), showLayerGrid);
            if (grid != showLayerGrid)
            {
                showLayerGrid = grid;
                SetGridVisible(grid);
            }
            bool bounds = EditorGUILayout.ToggleLeft(new GUIContent("Show Layer Bounds", "显示layer的包围框"), showLayerBounds);
            if (bounds != showLayerBounds)
            {
                showLayerBounds = bounds;
                SetLayerBoundsVisible(bounds);
            }
            EditorGUILayout.EndHorizontal();
        }

        GameObject mLayerBounds;
        GameObject mGrid = null;
        MapLayerBoundsCreator mBoundsCreator;
        MapLayerGridCreator mGridCreator;
    }
}

#endif