﻿ 



 
 



/*
 * created by wzw at 2019.12.9
 */

using UnityEngine;
using System;
using System.Collections.Generic;

namespace TFW.Map
{
    //目前地图上所有使用prefab的对象都可以使用这种视图
    public class RiverView : ModelView
    {
        public RiverView(IMapObjectData data, MapLayerView layerView)
            : base(data, layerView)
        {
        }

        protected override void OnModelReady(ModelBase model)
        {
            base.OnModelReady(model);

            //set shader lod
            var layerData = layerView.layerData;
            if (layerData.lodConfig != null)
            {
                var shaderLOD = layerData.lodConfig.lodConfigs[layerData.currentLOD].shaderLOD;
                if (shaderLOD != 0)
                {
                    var renderer = model.gameObject.GetComponent<MeshRenderer>();
                    if (renderer != null)
                    {
                        //设置了这个值就不会受到globalMaximumLOD的影响了
                        renderer.sharedMaterial.shader.maximumLOD = shaderLOD;
                    }
                }
            }

            CheckRiverVisibility(model);
        }

        protected override int CalculateLOD()
        {
            return layerView.layerData.currentLOD;
        }

        protected override void OnLODChanged()
        {
            bool isHide = CheckRiverVisibility(model);
            if (!isHide)
            {
                base.OnLODChanged();
            }
        }

        bool CheckRiverVisibility(ModelBase model)
        {
            var layerData = layerView.layerData;
            var riverData = layerData.map.FindObject(objectDataID) as RiverData;

            bool visible = true;
            if (riverData.hideLOD != -1)
            {
                if (riverData.hideLOD == layerData.currentLOD)
                {
                    visible = false;
                }
            }

            bool hideObject = layerData.lodConfig.lodConfigs[layerData.currentLOD].hideObject;
            if (hideObject)
            {
                visible = false;
            }

            model.active = visible;

            return visible == false;
        }
    }
}
