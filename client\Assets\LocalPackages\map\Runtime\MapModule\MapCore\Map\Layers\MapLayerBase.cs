﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.Collections;
using UnityEngine;

namespace TFW.Map
{
    /// <summary>
    /// 地图层的基类
    /// </summary>
    public abstract class MapLayerBase
    {
        public MapLayerBase(Map map)
        {
            this.map = map;
        }

        /// <summary>
        /// 删除地图层
        /// </summary>
        public abstract void OnDestroy();

        /// <summary>
        /// 加载地图层
        /// </summary>
        /// <param name="layerData"></param>
        /// <param name="setting"></param>
        /// <param name="async"></param>
        public abstract void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async);

        /// <summary>
        /// 异步加载地图层
        /// </summary>
        /// <param name="data"></param>
        /// <param name="setting"></param>
        /// <param name="async"></param>
        /// <returns></returns>
        public virtual IEnumerator LoadAsync(config.MapLayerData data, MapManager.MapSetting setting, bool async)
        {
            Load(data, setting, async);
            yield break;
        }

        /// <summary>
        /// 卸载地图层
        /// </summary>
        public abstract void Unload();

        /// <summary>
        /// 地图层的ID
        /// </summary>
        public abstract int id { get; }

        /// <summary>
        /// 
        /// </summary>
        public abstract bool active { get; set; }

        /// <summary>
        /// 地图层的根结点GameObject
        /// </summary>
        public abstract GameObject gameObject { get; }

        public abstract bool Contains(int objectID);

        public abstract int GetCurrentLOD();

        public abstract MapLayerData GetLayerData();

        public abstract MapLayerView GetLayerView();

        /// <summary>
        /// 加载完地图时刷新初始视野中的对象时使用
        /// </summary>
        public abstract void RefreshObjectsInViewport();

        public abstract bool UpdateViewport(Rect newViewport, float newCameraZoom);

        /// <summary>
        /// 显示地图层中所有的对象,而不考虑视野的范围.给编辑器使用
        /// 返回地图层的总宽度
        /// </summary>
        /// <returns></returns>
        public abstract float GetTotalWidth();

        /// <summary>
        /// 返回地图层的总高度
        /// </summary>
        /// <returns></returns>
        public abstract float GetTotalHeight();

        public abstract bool Resize(float newWidth, float newHeight, bool useLayerOffset);

        public virtual void UpdateInEditor()
        {
        }

        /// <summary>
        /// 地图层的名称
        /// </summary>
        public virtual string name { get { Debug.Assert(false); return ""; } set { } }

        /// <summary>
        /// 地图层的格子类型
        /// </summary>
        public abstract GridType gridType { get; }

        /// <summary>
        /// x方向上格子的数量
        /// </summary>
        public abstract int horizontalTileCount { get; }

        /// <summary>
        /// z方向上格子的数量
        /// </summary>
        public abstract int verticalTileCount { get; }

        /// <summary>
        /// 格子的宽
        /// </summary>
        public abstract float tileWidth { get; }

        /// <summary>
        /// 格子的高
        /// </summary>
        public abstract float tileHeight { get; }

        /// <summary>
        /// 地图层的偏移值
        /// </summary>
        public abstract Vector3 layerOffset { get; }

        public abstract int lodCount { get; }

        protected Map map { get; }
    }
}