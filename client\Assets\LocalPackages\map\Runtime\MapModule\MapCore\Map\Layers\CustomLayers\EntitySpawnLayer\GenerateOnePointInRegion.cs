﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public interface IOverlapObjectAtPosition
    {
        bool OverlapObjectAtPosition(Vector3 pos, float radius);
    }

    //按格子排列来生成npc刷新点
    public class GenerateOnePointInRegion : ISpawnPointGenerationStrategy
    {
        //distanceFromCenter: 距离中心点的最大距离,0到1
        public GenerateOnePointInRegion(float pointRadius, float distanceFromCenter, float alignment, IOverlapObjectAtPosition findObjectAtPosition, bool avoidDecorationObjects)
        {
            mPointRadius = pointRadius;
            mDistanceFromCenter = distanceFromCenter;
            mFindObjectAtPosition = findObjectAtPosition;
            mAlignment = alignment;
            mAvoidDecorationObjects = avoidDecorationObjects;
        }

        public List<Vector2> Generate(MapRegion region, float mapRadius, RandomWrapper r, TriangleNet.TrianglePool pool)
        {
            List<Vector2> spawnPoints = new List<Vector2>();

            Vector2 regionSize = region.size;
            var regionStartPos = new Vector3(region.startPosition.x, 0, region.startPosition.y);
            float halfRegionWidth = region.size.x * 0.5f;
            float halfRegionHeight = region.size.y * 0.5f;
            var centerPos = regionStartPos + new Vector3(halfRegionWidth, 0, halfRegionHeight);
            float validArea = region.GetValidArea();
            float regionArea = region.GetRegionArea();
            float ratio = validArea / regionArea;

            List<ObstacleObject> intersectedTiles = null;
            if (mAvoidDecorationObjects)
            {
                intersectedTiles = GetIntersectedTiles(regionStartPos, regionSize);
            }

            if (ratio > 0.1f)
            {
                int maxTryCount = 100;
                for (int i = 0; i < maxTryCount; ++i)
                {
                    var pos = GetRandomPosInRegion(centerPos, halfRegionWidth * mDistanceFromCenter, halfRegionHeight * mDistanceFromCenter, r);
                    if (mAlignment != 0)
                    {
                        pos = AlignPosition(pos, regionSize);
                    }
                    bool overlap = false;
                    if (mFindObjectAtPosition != null)
                    {
                        overlap = mFindObjectAtPosition.OverlapObjectAtPosition(pos, mPointRadius);
                    }
                    if (!overlap)
                    {
                        if (region.IsInEmptySpace(pos, mPointRadius))
                        {
                            overlap = false;
                            if (mAvoidDecorationObjects)
                            {
                                overlap = OverlapDecorationObject(intersectedTiles, pos);
                            }

                            if (!overlap)
                            {
                                spawnPoints.Add(new Vector2(pos.x, pos.z));
                            }
                            break;
                        }
                    }
                }
            }

            return spawnPoints;
        }

        List<ObstacleObject> GetIntersectedTiles(Vector3 regionStartPos, Vector2 regionSize)
        {
            float minX = regionStartPos.x;
            float minZ = regionStartPos.z;
            float maxX = minX + regionSize.x;
            float maxZ = minZ + regionSize.y;
            List<ObstacleObject> intersectedRegions = new List<ObstacleObject>();
            var frontLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            if (frontLayer != null)
            {
                var minCoord = frontLayer.FromWorldPositionToCoordinate(regionStartPos + frontLayer.layerOffset);
                var maxCoord = frontLayer.FromWorldPositionToCoordinate(new Vector3(maxX, 0, maxZ) + frontLayer.layerOffset);
                float tileWidth = frontLayer.tileWidth;
                float tileHeight = frontLayer.tileHeight;

                for (int i = minCoord.y; i <= maxCoord.y + 1; ++i)
                {
                    for (int j = minCoord.x; j <= maxCoord.x + 1; ++j)
                    {
                        var tile = frontLayer.GetObjectData(j, i);
                        if (tile != null)
                        {
                            var modelTemplate = tile.GetModelTemplate();
                            var idx = i * frontLayer.horizontalTileCount + j;
                            var modifier = new TilePrefabModifier(modelTemplate.generated, new Vector3(tileWidth * j, 0, tileHeight * i), new Vector2(tileWidth, tileHeight), modelTemplate.GetLODPrefabPath(0), idx);
                            var convexHulls = modifier.CreateAllConvexHulls(true);
                            intersectedRegions.AddRange(convexHulls);

#if false
                            if (mCounter < 50)
                            {
                                ++mCounter;
                                for (int k = 0; k < convexHulls.Count; ++k)
                                {
                                    var obj = new GameObject();
                                    var dp = obj.AddComponent<DrawPolygon>();
                                    dp.SetVertices(convexHulls[k].GetOutlineVertices(PrefabOutlineType.NavMeshObstacle));
                                }
                            }
#endif
                        }
                    }
                }
            }

            return intersectedRegions;
        }

        bool OverlapDecorationObject(List<ObstacleObject> convexHulls, Vector3 pos)
        {
            mTemp.Clear();
            mTemp.Add(pos + new Vector3(-mPointRadius, 0, -mPointRadius));
            mTemp.Add(pos + new Vector3(-mPointRadius, 0, mPointRadius));
            mTemp.Add(pos + new Vector3(mPointRadius, 0, mPointRadius));
            mTemp.Add(pos + new Vector3(mPointRadius, 0, -mPointRadius));

            for (int i = 0; i < convexHulls.Count; ++i)
            {
                if (ConvexPolygonCollisionCheck.ConvexPolygonHit2D(convexHulls[i].GetOutlineVertices(PrefabOutlineType.NavMeshObstacle), mTemp))
                {
                    return true;
                }
            }

            return false;
        }

        Vector3 GetRandomPosInRegion(Vector3 regionCenterPos, float halfWidth, float halfHeight, RandomWrapper r)
        {
            float w = r.Range(-1.0f, 1.0f) * halfWidth;
            float h = r.Range(-1.0f, 1.0f) * halfHeight;
            return regionCenterPos + new Vector3(w, 0, h);
        }

        Vector3 AlignPosition(Vector3 pos, Vector2 regionSize)
        {
            float x = AlignValue(pos.x, regionSize.x);
            float z = AlignValue(pos.z, regionSize.y);
            return new Vector3(x, 0, z);
        }

        float AlignValue(float v, float size)
        {
            int min = Mathf.FloorToInt(v / mAlignment);
            if (min % 2 == 0)
            {
                min -= 1;
            }
            if (min < 0)
            {
                min = 1;
            }
            int max = Mathf.CeilToInt(v / mAlignment);
            if (max % 2 == 0)
            {
                max += 1;
            }
            int maxCount = Mathf.FloorToInt(size / mAlignment);
            if (max >= maxCount)
            {
                max = maxCount - 1;
            }
            float distanceToMin = Mathf.Abs(v - min * mAlignment);
            float distanceToMax = Mathf.Abs(v - max * mAlignment);
            return distanceToMin < distanceToMax ? min * mAlignment : max * mAlignment;
        }

        public SpawnPointGenerateStrategyType strategy { get { return SpawnPointGenerateStrategyType.OnePointInRegion; } }

        float mPointRadius;
        float mDistanceFromCenter;
        IOverlapObjectAtPosition mFindObjectAtPosition;
        float mAlignment;
        bool mAvoidDecorationObjects;
        List<Vector3> mTemp = new List<Vector3>();
    }
}
#endif