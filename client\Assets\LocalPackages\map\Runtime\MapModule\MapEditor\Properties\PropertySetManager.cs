﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;

namespace TFW.Map {
    [Black]
    public interface IPropertySetManager {
        void AddPropertySet(PropertySet ps);
        PropertySet GetPropertySet(int index);
        PropertySet GetPropertySet(string name);
        int count { get; }
    }

    [Black]
    public class PropertySetManager : IPropertySetManager {
        public void AddPropertySet(PropertySet ps) {
            if (mProperties.Contains(ps) == false) {
                mProperties.Add(ps);
                CreateNames();
            }
        }

        public void RemovePropertySet(int index) {
            if (index >= 0 && index < mProperties.Count) {
                Map.currentMap.DestroyObject(mProperties[index]);
                mProperties.RemoveAt(index);
                CreateNames();
            }
        }

        public PropertySet ClonePropertySet(PropertySet ps) {
            PropertySet ret = null;
            if (ps != null) {
                ret = ps.Clone();
                AddPropertySet(ret);
            }
            return ret;
        }

        public void RenameProperty(int index, string name) {
            if (index >= 0 && index < mProperties.Count) {
                mProperties[index].name = name;
                CreateNames();
            }
        }

        public PropertySet GetPropertySet(int index) {
            if (index >= 0 && index < mProperties.Count) {
                return mProperties[index];
            }
            return null;
        }

        public PropertySet FindProperty(string name) {
            for (int i = 0; i < mProperties.Count; ++i) {
                if (mProperties[i].name == name) {
                    return mProperties[i];
                }
            }
            return null;
        }

        public PropertySet GetPropertySet(string name) {
            foreach(var ps in mProperties) {
                if (ps.name == name) {
                    return ps;
                }
            }
            return null;
        }

        void CreateNames() {
            if (mNames.Length != mProperties.Count) {
                mNames = new string[mProperties.Count];
            }
            for (int i = 0; i < mProperties.Count; ++i) {
                mNames[i] = mProperties[i].name;
            }
        }

        public int count { get { return mProperties.Count; } }
        public string[] names { get { return mNames; } }

        List<PropertySet> mProperties = new List<PropertySet>();
        string[] mNames = new string[0];
    }
}

#endif