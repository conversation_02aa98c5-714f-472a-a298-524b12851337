﻿ 



 
 

using System.Collections.Generic;

namespace TFW.Map
{
    //将多个物体的lod设置group到一起,每个group有一个leader,可以将多个物体替换成leader使用的prefab
    public class ModelLODGroup : BaseObject
    {
        public ModelLODGroup(int id, Map map) : base(id, map)
        {

        }

        public override void OnDestroy()
        {
        }

        public int lod { set { mLOD = value; } get { return mLOD; } }
        public bool combineModels { set { mCombineModels = value; } get { return mCombineModels; } }
        public int leaderObjectID { set { mLeaderObjectID = value; } get { return mLeaderObjectID; } }

        int mLOD;
        bool mCombineModels = false;
        int mLeaderObjectID;
    }

    public class ModelLODGroupManager
    {
        public void AddGroup(ModelLODGroup group)
        {
            if (mGroups.Contains(group) == false)
            {
                mGroups.Add(group);
            }
        }

        public List<ModelLODGroup> groups { get { return mGroups; } }

        List<ModelLODGroup> mGroups = new List<ModelLODGroup>();
    }
}
