﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    [Black]
    public class ActionAddComplexGridModel : EditorAction
    {
        public ActionAddComplexGridModel(int layerID, int objectID, int modelTemplateID, Vector3 position, Quaternion rotation, Vector3 scale, int occupiedGridCount, int lod, string prefabPath, string objectTag, bool useRenderTextureModel)
        {
            mLOD = lod;
            mPrefabPath = prefabPath;
            mOccupiedGridCount = occupiedGridCount;
            mLayerID = layerID;
            mObjectID = objectID;
            mModelTemplateID = modelTemplateID;
            mRotation = rotation;
            mPosition = position;
            mScale = scale;
            mObjectTag = objectTag;
            mUseRenderTextureModel = useRenderTextureModel;
            var prefabName = Path.GetFileName(mPrefabPath);
            mDescription = string.Format("add {0}", prefabName);
        }

        public override bool Do()
        {
            var map = Map.currentMap;
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null)
            {
                var layer = map.GetMapLayerByID(mLayerID) as EditorComplexGridModelLayer;
                if (layer != null)
                {
                    layer.AddObject(mLOD, mPrefabPath, mPosition, mRotation, mScale, mOccupiedGridCount, mObjectTag, mObjectID, mUseRenderTextureModel);
                    return true;
                }
            }
            return false;
        }

        public override bool Undo()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as EditorComplexGridModelLayer;
            if (layer != null)
            {
                layer.RemoveObject(mObjectID);
                return true;
            }

            return false;
        }

        public override string description { get { return mDescription; } }

        int mOccupiedGridCount;
        int mLayerID;
        int mObjectID;
        int mModelTemplateID;
        int mLOD;
        Quaternion mRotation;
        Vector3 mPosition;
        Vector3 mScale;
        string mDescription;
        string mPrefabPath;
        string mObjectTag;
        bool mUseRenderTextureModel;
    }
}

#endif