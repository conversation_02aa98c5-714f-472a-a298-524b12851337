﻿ 



 
 



/*
 * created by wzw at 2019.11.6
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //游戏运行时使用
    public sealed class TileGridObjectLayerView2 : MapLayerView
    {
        class TileObjectView
        {
            public string prefabPath;
            public GameObject gameObject;
            public int activeGridCount = 0;
        }

        public TileGridObjectLayerView2(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = layerData.map.view.reusableGameObjectPool;
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            foreach (var obj in mTileViews)
            {
                Utils.DestroyObject(obj.Value.gameObject);
            }
            mTileViews = null;
        }

        public void OnObjectActiveStateChange(TileObjectData2 data)
        {
            if (data.IsObjActive())
            {
                ShowObject(data);
            }
            else
            {
                HideObject(data);
            }
        }

        public void OnObjectScaleChange(TileObjectData2 data)
        {
            TileObjectView view;
            mTileViews.TryGetValue(data.viewID, out view);
            view.gameObject.transform.localScale = data.GetScale();
        }

        //改变装饰物的game object
        public void OnObjectTypeChange(TileObjectData2 data)
        {
            bool found = mTileViews.TryGetValue(data.viewID, out TileObjectView view);
            if (found)
            {
                mObjectPool.Release(view.prefabPath, view.gameObject, layerData.map);
                var obj = mObjectPool.Require(data.GetAssetPath());
                obj.SetActive(true);
                view.gameObject = obj;
                view.prefabPath = data.GetAssetPath();

                var transform = obj.transform;
                transform.localPosition = data.GetPosition();
                transform.localRotation = (data.objectType == TileObjectType.NoneScaleDecorationObject || data.objectType == TileObjectType.ScaleDecorationObject) && MapObjectUtils.NeedRotation ? data.GetRotation() * Quaternion.Euler(0, 180f, 0) : data.GetRotation();
                transform.localScale = data.GetScale();
                transform.SetParent(root.transform, false);
            }
        }

        public void OnObjectMaterialChange(TileObjectData2 data, int mtlType)
        {
            bool found = mTileViews.TryGetValue(data.viewID, out TileObjectView view);
            if (found)
            {
                DoChangeObjectMaterial(view.gameObject, mtlType);
            }
        }

        void DoChangeObjectMaterial(GameObject gameObject, int mtlType)
        {
            gameObject.GetComponentsInChildren(true, mChangeObjectMaterialComponentsTempCache);
            foreach (var change in mChangeObjectMaterialComponentsTempCache)
            {
                change.Change(mtlType);
            }
            mChangeObjectMaterialComponentsTempCache.Clear();
        }

        //显示地图对象的模型
        void ShowObject(TileObjectData2 data)
        {
            var layer = layerData as TileGridObjectLayerData2;
            TileObjectView view;
            bool found = mTileViews.TryGetValue(data.viewID, out view);
            if (found)
            {
                view.activeGridCount += 1;
            }
            else
            {
                var obj = mObjectPool.Require(data.GetAssetPath());

                view = mTileObjectViewPool.Require();
                view.activeGridCount = 1;
                view.gameObject = obj;
                view.prefabPath = data.GetAssetPath();
                obj.SetActive(true);
                mTileViews[data.viewID] = view;

                if (data.specialArea != null && layer.enableObjectMaterialTypeChange)
                {
                    DoChangeObjectMaterial(obj, data.specialArea.state);
                }

                var transform = obj.transform;
                transform.localPosition = data.GetPosition();
                transform.localRotation =( data.objectType == TileObjectType.NoneScaleDecorationObject || data.objectType == TileObjectType.ScaleDecorationObject) &&MapObjectUtils.NeedRotation ? data.GetRotation() * Quaternion.Euler(0, 180f, 0) : data.GetRotation();
                transform.localScale = data.GetScale();
                transform.SetParent(root.transform, false);
            }
        }

        //隐藏地图对象的模型
        void HideObject(TileObjectData2 data)
        {
            var id = data.viewID;
            TileObjectView view;
            bool found = mTileViews.TryGetValue(id, out view);
#if UNITY_EDITOR
            Debug.Assert(found);
#endif
            view.activeGridCount -= 1;
            if (view.activeGridCount == 0)
            {
                var layer = layerData as TileGridObjectLayerData2;
                if (data.specialArea != null && layer.enableObjectMaterialTypeChange)
                {
                    //还原材质
                    DoChangeObjectMaterial(view.gameObject, 0);
                }

                mTileObjectViewPool.Release(view);
                mTileViews.Remove(id);
                mObjectPool.Release(view.prefabPath, view.gameObject, layerData.map);
            }
#if UNITY_EDITOR
            else if (view.activeGridCount < 0)
            {
                Debug.Assert(false);
            }
#endif
            
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        //该层中所有地图对象的视图, key是view id
        Dictionary<int, TileObjectView> mTileViews = new Dictionary<int, TileObjectView>();
        ObjectPool<TileObjectView> mTileObjectViewPool = new ObjectPool<TileObjectView>(3000, () => { return new TileObjectView(); });
        GameObjectPool mObjectPool;
        List<ChangeObjectMaterial> mChangeObjectMaterialComponentsTempCache = new List<ChangeObjectMaterial>();
    };
}