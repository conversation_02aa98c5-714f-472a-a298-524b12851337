using System.Collections.Generic;

namespace CISystem
{
    public class CommandLineParser
    {
        public Dictionary<string, string> Options = new Dictionary<string, string>();

        public CommandLineParser(string[] args)
        {
            int num = 0;
            while (num < args.Length)
            {
                string text = args[num];
                if (!text.StartsWith("-"))
                {
                    num++;
                    continue;
                }

                string value = null;
                if (num + 1 < args.Length && !args[num + 1].StartsWith("-"))
                {
                    value = args[num + 1];
                    num++;
                }

                if (!Options.ContainsKey(text))
                {
                    Options.Add(text, value);
                }

                num++;
            }
        }
    }
}