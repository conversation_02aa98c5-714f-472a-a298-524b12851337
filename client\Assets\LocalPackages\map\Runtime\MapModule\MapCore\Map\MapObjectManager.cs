﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理地图上所有派生自BaseObject的对象
    public class MapObjectManager
    {
        public void OnDestroy()
        {
            Update();
        }

        //internal use
        public void AddObject(BaseObject obj)
        {
            mAllObjects[obj.id] = obj;
        }

        public void RemoveObject(BaseObject obj)
        {
            bool removed = mAllObjects.Remove(obj.id);
            Debug.Assert(removed);
        }

        public void RemoveObject(int id)
        {
            mAllObjects.Remove(id);
        }

        public void DestroyObject(int objectID)
        {
            var obj = FindObject(objectID);
            DestroyObject(obj);
        }

        //避免嵌套或重复删除object
        public void DestroyObject(BaseObject obj)
        {
            if (obj != null && obj.HasObjectFlag(ObjectFlag.kObjectDestroyed) == false)
            {
                BaseObject destroyObj = null;
                mAllObjects.TryGetValue(obj.id, out destroyObj);

                Debug.Assert(destroyObj == obj);

                obj.AddObjectFlag(ObjectFlag.kObjectDestroyed);

                mDestroyedObjects.Add(obj);
            }
        }

        public BaseObject FindObject(int id)
        {
            if (id == 0)
            {
                return null;
            }
            BaseObject obj = null;
            bool ok = mAllObjects.TryGetValue(id, out obj);
            if (ok && obj.HasObjectFlag(ObjectFlag.kObjectDestroyed) == false)
            {
                return obj;
            }
            return null;
        }

        //this is called every frame
        public void Update()
        {
            //删除队列中的object
            int n = mDestroyedObjects.Count;
            for (int i = 0; i < n; ++i)
            {
                var obj = mDestroyedObjects[i];
                bool hasFlag = obj.HasObjectFlag(ObjectFlag.kObjectDestroyed);
                Debug.Assert(hasFlag);
                obj.OnDestroy();
                mAllObjects.Remove(mDestroyedObjects[i].id);
            }
            mDestroyedObjects.Clear();
        }

        //所有的BaseObject
        Dictionary<int, BaseObject> mAllObjects = new Dictionary<int, BaseObject>();
        //等待被删除的BaseObject
        List<BaseObject> mDestroyedObjects = new List<BaseObject>();
    }
}
