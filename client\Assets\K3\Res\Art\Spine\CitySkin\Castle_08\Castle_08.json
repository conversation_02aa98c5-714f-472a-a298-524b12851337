{"skeleton": {"hash": "t4b0YV74d+E", "spine": "4.2.33", "x": -459.61, "y": -208.53, "width": 827.35, "height": 811.12, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "tree", "parent": "root", "x": -197.4, "y": 74.34}, {"name": "tree2", "parent": "tree", "length": 46.54, "rotation": 96.6, "x": -0.11, "y": 12.36}, {"name": "tree3", "parent": "tree2", "length": 63.11, "rotation": 13.23, "x": 46.54}, {"name": "tree4", "parent": "tree3", "length": 84.58, "rotation": 1.42, "x": 63.11}, {"name": "tree5", "parent": "tree4", "length": 104.64, "rotation": 0.3, "x": 84.58}, {"name": "tree_Fb", "parent": "root", "length": 35.47, "rotation": 89.33, "x": 15.23, "y": -74.63}, {"name": "tree_Fb2", "parent": "tree_Fb", "length": 19.39, "rotation": -0.55, "x": 35.47}, {"name": "tree_Fb3", "parent": "tree_Fb2", "length": 21.06, "rotation": -1.59, "x": 19.39}, {"name": "tree_L", "parent": "root", "x": 246.93, "y": 59.79}, {"name": "tree_L2", "parent": "tree_L", "length": 31.88, "rotation": 92.41, "x": -6.42, "y": 23.55}, {"name": "tree_L3", "parent": "tree_L2", "length": 26.78, "rotation": -0.69, "x": 31.88}, {"name": "UP", "parent": "root", "x": 2.36, "y": 486.94}, {"name": "Up", "parent": "UP", "length": 111.3, "rotation": 90, "y": 0.23}], "slots": [{"name": "tree", "bone": "tree", "attachment": "tree"}, {"name": "Castle", "bone": "root", "attachment": "Castle"}, {"name": "light", "bone": "root", "color": "ffffff95", "attachment": "light", "blend": "additive"}, {"name": "light_O", "bone": "root", "attachment": "light_O", "blend": "additive"}, {"name": "O", "bone": "root", "color": "ffffff64", "attachment": "O", "blend": "additive"}, {"name": "light_red2", "bone": "root", "attachment": "light_red", "blend": "additive"}, {"name": "light_red", "bone": "root", "attachment": "light_red"}, {"name": "X2", "bone": "root", "attachment": "X2"}, {"name": "X1", "bone": "root", "attachment": "X1"}, {"name": "tree_L", "bone": "tree_L", "attachment": "tree_L"}, {"name": "tree_Fb", "bone": "tree_Fb", "attachment": "tree_Fb"}, {"name": "tree_Fa", "bone": "root", "attachment": "tree_Fa"}, {"name": "Up", "bone": "Up", "attachment": "Up"}], "skins": [{"name": "default", "attachments": {"Castle": {"Castle": {"type": "mesh", "uvs": [0.53587, 0, 0.83566, 0.06034, 0.88775, 0.18672, 0.77815, 0.51135, 0.78497, 0.56732, 1, 0.70225, 1, 0.7231, 0.98105, 0.73203, 0.44585, 1, 0.44381, 1, 0, 0.71668, 0, 0.70029, 0.15465, 0.62239, 0.23558, 0.43949, 0.19904, 0.39317, 0.15128, 0.20427, 0.25234, 0.05567, 0.42469, 0], "triangles": [14, 15, 16, 17, 13, 14, 2, 3, 0, 2, 0, 1, 13, 17, 0, 17, 14, 16, 10, 11, 12, 7, 4, 5, 6, 7, 5, 13, 0, 3, 4, 13, 3, 4, 8, 12, 13, 4, 12, 8, 9, 12, 10, 12, 9, 7, 8, 4], "vertices": [34.96, 517.47, 249.91, 473.66, 287.26, 381.9, 208.68, 146.23, 213.57, 105.59, 367.74, 7.63, 367.74, -7.51, 354.16, -13.99, -29.58, -208.53, -31.05, -208.53, -349.26, -2.84, -349.26, 9.05, -238.38, 65.61, -180.35, 198.4, -206.55, 232.02, -240.79, 369.17, -168.33, 477.05, -44.76, 517.47], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 717, "height": 726}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-158.82, 110.94, -207.82, 110.94, -207.82, 158.94, -158.82, 158.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 49, "height": 48}}, "light_O": {"light_O": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-73.02, 187.5, -199.5, 187.5, -199.5, 331.43, -73.02, 331.43], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 132}}, "light_red": {"light_red": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [252.74, 274.47, 188.74, 274.47, 188.74, 335.47, 252.74, 335.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 61}}, "light_red2": {"light_red": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [252.74, 274.47, 188.74, 274.47, 188.74, 335.47, 252.74, 335.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 61}}, "O": {"O": {"type": "mesh", "uvs": [0.3697, 0, 0.78118, 0.17731, 1, 0.64724, 1, 0.8363, 0.87944, 1, 0.63802, 1, 0.23105, 0.82261, 0, 0.35724, 0, 0.15765, 0.13402, 0], "triangles": [0, 7, 8, 9, 0, 8, 1, 7, 0, 6, 1, 2, 1, 6, 7, 5, 6, 2, 3, 5, 2, 4, 5, 3], "vertices": [-148.25, 312.47, -110.39, 293.67, -90.26, 243.86, -90.26, 223.82, -101.35, 206.47, -123.56, 206.47, -161, 225.27, -182.26, 274.6, -182.26, 295.75, -169.93, 312.47], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 92, "height": 106}}, "tree": {"tree": {"type": "mesh", "uvs": [0.51318, 0.00095, 0.62314, 0.03382, 0.66122, 0.09386, 0.67501, 0.16778, 0.75305, 0.14552, 0.81093, 0.14926, 0.85249, 0.19538, 0.82671, 0.27193, 0.90236, 0.27628, 0.96539, 0.32263, 0.9789, 0.34945, 0.96459, 0.44231, 0.99535, 0.52836, 0.97931, 0.58212, 0.93582, 0.64224, 0.83962, 0.62024, 0.87127, 0.68547, 0.84005, 0.73781, 0.801, 0.76595, 0.75798, 0.75481, 0.70988, 0.6924, 0.7026, 0.73924, 0.70261, 0.80815, 0.70262, 0.87263, 0.70262, 0.92473, 0.70263, 0.98983, 0.66063, 0.99952, 0.66312, 0.93137, 0.66129, 0.88188, 0.64877, 0.83191, 0.61657, 0.80523, 0.58471, 0.76905, 0.57198, 0.81222, 0.51208, 0.82161, 0.39977, 0.79241, 0.37581, 0.82929, 0.37788, 0.88815, 0.3415, 0.92664, 0.2564, 0.92033, 0.24029, 0.82556, 0.20583, 0.88004, 0.1574, 0.90282, 0.11377, 0.88588, 0.07976, 0.84946, 0.07298, 0.74529, 0.01193, 0.67976, 0.05939, 0.57423, 0.12014, 0.52272, 0.06923, 0.4732, 0.0872, 0.40197, 0.13482, 0.36569, 0.20912, 0.335, 0.19147, 0.26318, 0.19973, 0.1886, 0.24102, 0.13631, 0.29927, 0.15204, 0.30978, 0.10594, 0.36878, 0.0968, 0.37941, 0.05133, 0.41212, 0.01371, 0.43519, 0.01574, 0.46313, 0.03476, 0.62437, 0.63444, 0.59178, 0.50752, 0.54689, 0.37671, 0.48971, 0.13612, 0.51799, 0.25447, 0.69447, 0.31131, 0.74243, 0.45847, 0.87894, 0.43122, 0.74366, 0.60563, 0.49608, 0.6916, 0.4266, 0.5717, 0.31284, 0.64644, 0.18433, 0.70484, 0.23229, 0.50785, 0.3897, 0.42687, 0.36203, 0.32254, 0.33805, 0.2112, 0.65206, 0.73991], "triangles": [50, 51, 75, 75, 77, 76, 75, 51, 77, 76, 77, 66, 66, 78, 65, 65, 78, 57, 61, 57, 58, 61, 58, 60, 60, 58, 59, 61, 65, 57, 51, 52, 78, 51, 78, 77, 78, 53, 55, 55, 53, 54, 52, 53, 78, 66, 77, 78, 66, 3, 67, 3, 65, 2, 2, 65, 1, 65, 3, 66, 78, 55, 57, 57, 55, 56, 65, 0, 1, 65, 61, 0, 37, 38, 39, 41, 42, 40, 40, 42, 74, 39, 40, 74, 74, 42, 43, 43, 44, 74, 35, 39, 73, 39, 74, 73, 34, 73, 72, 74, 44, 46, 44, 45, 46, 46, 47, 74, 74, 75, 73, 74, 47, 75, 71, 72, 63, 72, 73, 76, 13, 69, 11, 63, 68, 70, 73, 75, 76, 72, 64, 63, 72, 76, 64, 48, 49, 47, 47, 50, 75, 47, 49, 50, 63, 67, 68, 63, 64, 67, 68, 7, 69, 5, 7, 4, 4, 7, 67, 67, 7, 68, 11, 69, 10, 69, 9, 10, 69, 8, 9, 69, 7, 8, 76, 66, 64, 64, 66, 67, 67, 3, 4, 7, 5, 6, 36, 37, 35, 35, 37, 39, 34, 35, 73, 31, 32, 33, 33, 34, 71, 31, 33, 71, 29, 30, 79, 79, 21, 22, 30, 31, 79, 34, 72, 71, 79, 31, 62, 18, 19, 17, 20, 70, 19, 19, 15, 17, 19, 70, 15, 31, 71, 62, 21, 79, 20, 20, 79, 62, 17, 15, 16, 20, 62, 70, 71, 63, 62, 14, 15, 13, 62, 63, 70, 13, 15, 69, 69, 15, 68, 15, 70, 68, 13, 11, 12, 27, 24, 25, 27, 28, 24, 28, 23, 24, 28, 29, 23, 29, 22, 23, 22, 29, 79, 26, 27, 25], "vertices": [1, 5, 97.36, -28.57, 1, 2, 4, 157.12, -64.36, 0.02761, 5, 72.19, -64.74, 0.97239, 3, 3, 199.34, -68.14, 0.00199, 4, 134.5, -71.5, 0.09565, 5, 49.53, -71.76, 0.90236, 3, 3, 176.1, -65.48, 0.02017, 4, 111.33, -68.26, 0.32112, 5, 26.39, -68.4, 0.65871, 3, 3, 172.22, -96.44, 0.06735, 4, 106.69, -99.11, 0.56316, 5, 21.58, -99.22, 0.36949, 3, 3, 163.48, -117.28, 0.08873, 4, 97.44, -119.73, 0.60581, 5, 12.22, -119.8, 0.30546, 3, 3, 144.62, -127.71, 0.10557, 4, 78.32, -129.69, 0.6201, 5, -6.95, -129.65, 0.27433, 3, 3, 125.85, -110.25, 0.18615, 4, 59.99, -111.77, 0.63153, 5, -25.18, -111.64, 0.18232, 3, 3, 114.58, -137.55, 0.3263, 4, 48.05, -138.78, 0.60675, 5, -37.27, -138.58, 0.06695, 3, 3, 92.81, -155.83, 0.38268, 4, 25.83, -156.52, 0.58006, 5, -59.58, -156.2, 0.03726, 3, 3, 83.25, -157.99, 0.39333, 4, 16.22, -158.44, 0.57339, 5, -69.2, -158.07, 0.03328, 3, 3, 58.24, -143.03, 0.46788, 4, -8.41, -142.87, 0.51625, 5, -93.75, -142.37, 0.01588, 3, 3, 29.24, -145.32, 0.54441, 4, -37.46, -144.44, 0.45312, 5, -122.81, -143.79, 0.00247, 3, 3, 15.78, -133.82, 0.5672, 4, -50.62, -132.61, 0.43221, 5, -135.91, -131.89, 0.00059, 2, 3, 4.12, -111.58, 0.59782, 4, -61.73, -110.09, 0.40218, 3, 3, 23.23, -78.59, 0.75024, 4, -41.82, -77.58, 0.24946, 5, -126.81, -76.91, 0.0003, 3, 3, 0.14, -83.39, 0.90466, 4, -65.02, -81.8, 0.09534, 5, -150.03, -81.01, 0, 2, 3, -10.9, -66.46, 0.94113, 4, -75.63, -64.61, 0.05887, 2, 3, -13.88, -49.2, 0.96373, 4, -78.19, -47.28, 0.03627, 2, 3, -4.96, -34.58, 0.96989, 4, -68.91, -32.88, 0.03011, 2, 3, 19.48, -23.45, 0.97435, 4, -44.2, -22.37, 0.02565, 3, 2, 56.86, -13.89, 0.04063, 3, 6.87, -15.89, 0.95863, 4, -56.61, -14.49, 0.00074, 2, 2, 35.78, -11.46, 0.92477, 3, -13.09, -8.69, 0.07523, 1, 2, 16.05, -9.17, 1, 2, 1, 7.16, 13.31, 0.05404, 2, 0.11, -7.33, 0.94596, 1, 1, 7.16, -6.74, 1, 1, 1, -9.22, -9.72, 1, 2, 1, -8.25, 11.27, 0.15181, 2, -0.15, 8.21, 0.84819, 1, 2, 15.08, 7.16, 1, 2, 2, 30.93, 10.24, 0.88405, 3, -12.85, 13.54, 0.11595, 3, 2, 40.53, 21.77, 0.40897, 3, -0.86, 22.57, 0.58707, 4, -63.39, 24.15, 0.00396, 4, 2, 53.03, 32.83, 0.05478, 3, 13.84, 30.48, 0.89224, 4, -48.5, 31.69, 0.05284, 5, -132.92, 32.39, 0.00014, 4, 2, 40.4, 39.3, 0.00857, 3, 3.01, 39.66, 0.89355, 4, -59.1, 41.13, 0.09709, 5, -143.46, 41.89, 0.00078, 3, 3, 8.22, 62.62, 0.81983, 4, -53.33, 63.96, 0.17386, 5, -137.57, 64.69, 0.00631, 3, 3, 31.54, 100.77, 0.54368, 4, -29.07, 101.52, 0.40274, 5, -113.11, 102.12, 0.05358, 3, 3, 24.02, 113.41, 0.49105, 4, -36.27, 114.34, 0.44211, 5, -120.24, 114.98, 0.06683, 3, 3, 6.69, 118.8, 0.48062, 4, -53.46, 120.16, 0.45184, 5, -137.4, 120.89, 0.06755, 3, 3, 0.36, 136.17, 0.47527, 4, -59.36, 137.68, 0.45565, 5, -143.21, 138.44, 0.06908, 3, 3, 13.45, 166.73, 0.46047, 4, -45.52, 167.91, 0.46269, 5, -129.21, 168.6, 0.07684, 3, 3, 43.03, 162.74, 0.41492, 4, -16.04, 163.19, 0.47757, 5, -99.76, 163.72, 0.1075, 3, 3, 31.81, 181.07, 0.3863, 4, -26.81, 181.79, 0.48874, 5, -110.43, 182.38, 0.12496, 3, 3, 31.62, 201.22, 0.37834, 4, -26.5, 201.94, 0.49164, 5, -110.01, 202.53, 0.13001, 3, 3, 42.3, 215.46, 0.3725, 4, -15.47, 215.91, 0.49282, 5, -98.91, 216.44, 0.13468, 3, 3, 57.35, 224.13, 0.36646, 4, -0.21, 224.2, 0.49326, 5, -83.61, 224.65, 0.14028, 3, 3, 88.43, 215.73, 0.34047, 4, 30.65, 215.04, 0.4914, 5, -52.79, 215.32, 0.16813, 3, 3, 115.49, 231.28, 0.31957, 4, 58.09, 229.92, 0.48937, 5, -25.27, 230.05, 0.19106, 3, 3, 139.79, 202.84, 0.29687, 4, 81.68, 200.88, 0.48144, 5, -1.84, 200.9, 0.22169, 3, 3, 146.67, 175.17, 0.24036, 4, 87.87, 173.05, 0.45342, 5, 4.21, 173.03, 0.30622, 3, 3, 167.76, 188.68, 0.20084, 4, 109.29, 186.03, 0.42866, 5, 25.69, 185.9, 0.3705, 3, 3, 186.02, 174.64, 0.18856, 4, 127.2, 171.55, 0.41832, 5, 43.52, 171.32, 0.39312, 3, 3, 190.23, 153.38, 0.17085, 4, 130.88, 150.19, 0.39943, 5, 47.09, 149.94, 0.42973, 3, 3, 189.29, 122.91, 0.1031, 4, 129.18, 119.75, 0.29915, 5, 45.23, 119.51, 0.59774, 3, 3, 212.44, 121.88, 0.05118, 4, 152.3, 118.15, 0.18533, 5, 68.34, 117.79, 0.76349, 3, 3, 232.95, 111.06, 0.03282, 4, 172.54, 106.82, 0.13524, 5, 88.52, 106.36, 0.83194, 3, 3, 242.64, 90.45, 0.02434, 4, 181.71, 85.98, 0.10842, 5, 97.58, 85.46, 0.86724, 3, 3, 230.37, 70.72, 0.01474, 4, 168.96, 66.56, 0.07097, 5, 84.73, 66.11, 0.91429, 3, 3, 242.34, 62.05, 0.00756, 4, 180.71, 57.59, 0.03901, 5, 96.43, 57.08, 0.95343, 3, 3, 237.18, 39.45, 0.0021, 4, 174.99, 35.13, 0.01113, 5, 90.59, 34.65, 0.98677, 3, 3, 248.95, 30.8, 0.00023, 4, 186.54, 26.19, 0.00114, 5, 102.1, 25.65, 0.99863, 1, 5, 108.19, 9.53, 1, 1, 5, 104.3, 1.39, 1, 1, 5, 94.85, -6.59, 1, 1, 3, 47.59, 1.86, 1, 2, 3, 88.68, 0.56, 8e-05, 4, 25.57, -0.08, 0.99992, 3, 3, 132.51, 3.36, 8e-05, 4, 69.46, 1.64, 0.99905, 5, -15.11, 1.72, 0.00087, 1, 5, 62, -4.76, 1, 1, 5, 24.05, -1.63, 1, 3, 3, 131.94, -57.62, 0.06704, 4, 67.38, -59.31, 0.66279, 5, -17.52, -59.21, 0.27017, 3, 3, 82.95, -59.84, 0.31629, 4, 18.36, -60.31, 0.64965, 5, -66.55, -59.96, 0.03406, 3, 3, 72.79, -112.77, 0.42655, 4, 6.88, -112.97, 0.54381, 5, -78.3, -112.56, 0.02964, 3, 3, 40.15, -44.91, 0.78336, 4, -24.06, -44.33, 0.21638, 5, -108.88, -43.76, 0.00026, 3, 3, 48, 54.9, 0.65697, 4, -13.74, 55.26, 0.32726, 5, -98.03, 55.78, 0.01577, 3, 3, 91.94, 67.86, 0.2718, 4, 30.5, 67.13, 0.62243, 5, -53.73, 67.41, 0.10577, 3, 3, 85.33, 117.41, 0.35089, 4, 25.12, 116.82, 0.49945, 5, -58.84, 117.13, 0.14966, 3, 3, 85.42, 170.66, 0.34637, 4, 26.52, 170.05, 0.4882, 5, -57.16, 170.36, 0.16543, 3, 3, 136.15, 132.48, 0.21407, 4, 76.29, 130.63, 0.45413, 5, -7.6, 130.67, 0.33181, 3, 3, 138.78, 66.26, 0.08621, 4, 77.28, 64.37, 0.51343, 5, -6.96, 64.41, 0.40036, 3, 3, 172.67, 65.51, 0.04002, 4, 111.15, 62.78, 0.24577, 5, 26.89, 62.64, 0.71422, 3, 3, 208.1, 62.68, 0.0159, 4, 146.5, 59.07, 0.08325, 5, 62.23, 58.74, 0.90085, 3, 2, 58.93, 5.71, 0.01469, 3, 13.37, 2.72, 0.98521, 4, -49.66, 3.95, 0.00011], "hull": 62, "edges": [0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 50, 52, 56, 58, 58, 60, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 36, 38, 38, 40, 88, 90, 86, 88, 22, 24, 4, 6, 122, 0, 102, 104, 64, 66, 60, 62, 62, 64], "width": 390, "height": 308}}, "tree_Fa": {"tree_Fa": {"type": "mesh", "uvs": [0.57581, 0.03359, 0.85974, 0.06281, 0.91387, 0.11936, 0.98396, 0.27656, 0.98387, 0.45881, 0.98378, 0.64107, 0.96764, 0.75948, 0.91876, 0.81466, 0.76014, 0.92672, 0.5773, 0.98648, 0.31124, 0.96322, 0.03212, 0.81756, 0.00967, 0.47766, 0.0581, 0.28787, 0.23037, 0.02159, 0.29188, 0.00437, 0.33493, 0.27179, 0.74444, 0.25793, 0.31499, 0.5438, 0.79045, 0.52821, 0.3518, 0.78636, 0.72297, 0.7725], "triangles": [17, 0, 1, 17, 1, 2, 16, 15, 0, 16, 0, 17, 14, 15, 16, 17, 2, 3, 13, 14, 16, 4, 17, 3, 18, 13, 16, 17, 18, 16, 19, 17, 4, 12, 13, 18, 5, 19, 4, 5, 7, 19, 18, 17, 19, 7, 21, 19, 21, 18, 19, 20, 18, 21, 5, 6, 7, 11, 12, 18, 11, 18, 20, 8, 21, 7, 10, 11, 20, 9, 20, 21, 9, 21, 8, 10, 20, 9], "vertices": [2, 7, 41.9, -3.33, 0.00252, 8, 22.6, -2.7, 0.99748, 2, 7, 40.69, -20.68, 0.13791, 8, 21.87, -20.08, 0.86209, 2, 7, 37.71, -24.04, 0.18333, 8, 18.98, -23.53, 0.81667, 2, 7, 29.31, -28.5, 0.34789, 8, 10.71, -28.21, 0.65211, 2, 7, 19.47, -28.7, 0.59515, 8, 0.88, -28.69, 0.40485, 2, 7, 9.63, -28.91, 0.83026, 8, -8.95, -29.17, 0.16974, 2, 7, 3.22, -28.06, 0.90931, 8, -15.39, -28.5, 0.09069, 2, 7, 0.18, -25.14, 0.93961, 8, -18.51, -25.66, 0.06039, 2, 7, -6.08, -15.6, 0.99611, 8, -25.03, -16.3, 0.00389, 1, 7, -9.54, -4.51, 1, 2, 7, -8.63, 11.74, 0.99016, 8, -28.33, 10.96, 0.00984, 2, 7, -1.13, 28.93, 0.86976, 8, -21.31, 28.35, 0.13024, 2, 7, 17.19, 30.69, 0.56472, 8, -3.05, 30.61, 0.43528, 2, 7, 27.5, 27.95, 0.32462, 8, 7.33, 28.17, 0.67538, 2, 7, 42.1, 17.75, 0.029, 8, 22.21, 18.37, 0.971, 2, 7, 43.11, 14.02, 0.01427, 8, 23.32, 14.67, 0.98573, 2, 7, 28.73, 11.09, 0.10375, 8, 9.03, 11.34, 0.89625, 2, 7, 30.01, -13.87, 0.19026, 8, 11, -13.57, 0.80974, 2, 7, 14.02, 11.99, 0.71695, 8, -5.7, 11.84, 0.28305, 2, 7, 15.47, -16.99, 0.73098, 8, -3.44, -17.09, 0.26902, 2, 7, 0.97, 9.47, 0.97614, 8, -18.67, 8.95, 0.02386, 2, 7, 2.2, -13.15, 0.98381, 8, -16.82, -13.62, 0.01619], "hull": 16, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 2, 0, 0, 30, 6, 8, 8, 10], "width": 61, "height": 54}}, "tree_Fb": {"tree_Fb": {"type": "mesh", "uvs": [0.68732, 0.01142, 0.89982, 0.08736, 0.97802, 0.18868, 0.82408, 0.913, 0.6638, 0.9854, 0.34195, 0.98325, 0.15817, 0.93235, 0.00611, 0.22444, 0.07843, 0.11642, 0.33708, 0.011, 0.09735, 0.32349, 0.33572, 0.41194, 0.64743, 0.38821, 0.87394, 0.29113], "triangles": [13, 0, 1, 13, 1, 2, 10, 8, 9, 7, 8, 10, 12, 9, 0, 12, 0, 13, 11, 10, 9, 12, 11, 9, 3, 12, 13, 3, 13, 2, 6, 10, 11, 7, 10, 6, 4, 5, 11, 12, 4, 11, 6, 11, 5, 3, 4, 12], "vertices": [42.64, -8.69, 39.04, -19.15, 34.12, -23.04, -1.45, -15.91, -5.09, -8.09, -5.17, 7.68, -2.78, 16.71, 31.82, 24.56, 37.15, 21.08, 42.46, 8.47, 27.02, 20.04, 22.82, 8.31, 24.16, -6.95, 29.04, -17.99], "hull": 10, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 2, 0, 0, 18, 20, 22, 22, 24, 24, 26], "width": 49, "height": 49}}, "tree_L": {"tree_L": {"type": "mesh", "uvs": [0.26936, 0.00995, 0.50234, 0.00995, 0.68959, 0.00995, 0.77895, 0.05824, 0.9152, 0.16456, 0.99531, 0.23908, 0.98749, 0.31858, 0.88484, 0.45924, 0.78902, 0.51121, 0.63088, 0.54302, 0.62265, 0.63411, 0.71837, 0.60766, 0.78415, 0.61908, 0.83836, 0.67042, 0.85499, 0.73747, 0.81554, 0.92346, 0.77397, 0.95615, 0.66844, 0.98772, 0.50086, 0.98746, 0.38233, 0.94845, 0.3103, 0.89212, 0.29248, 0.69939, 0.31782, 0.66348, 0.31035, 0.57934, 0.18911, 0.49516, 0.06971, 0.41774, 0.01053, 0.2601, 0.05773, 0.14727, 0.12764, 0.09157, 0.32149, 0.41751, 0.64724, 0.40991, 0.30104, 0.27106, 0.68381, 0.25912, 0.32558, 0.1322, 0.65542, 0.11918, 0.48642, 0.75813, 0.70977, 0.71475], "triangles": [34, 1, 2, 34, 2, 3, 33, 0, 1, 28, 0, 33, 4, 32, 34, 4, 34, 3, 31, 28, 33, 27, 28, 31, 26, 27, 31, 6, 4, 5, 32, 4, 6, 33, 34, 32, 34, 33, 1, 32, 31, 33, 25, 26, 31, 7, 30, 32, 30, 31, 32, 29, 31, 30, 25, 31, 29, 6, 7, 32, 24, 25, 29, 8, 30, 7, 9, 29, 30, 23, 24, 29, 10, 23, 9, 36, 10, 11, 36, 11, 12, 36, 12, 13, 36, 13, 14, 35, 22, 10, 35, 10, 36, 21, 22, 35, 20, 21, 35, 15, 36, 14, 19, 20, 35, 16, 36, 15, 17, 35, 36, 18, 19, 35, 35, 17, 18, 36, 16, 17, 22, 23, 10, 23, 29, 9, 9, 30, 8], "vertices": [2, 10, 59.85, 15.25, 0.00947, 11, 27.78, 15.59, 0.99053, 1, 11, 27.24, -2.58, 1, 2, 10, 58.47, -17.49, 0.0346, 11, 26.8, -17.17, 0.9654, 2, 10, 53.45, -24.26, 0.11486, 11, 21.86, -24, 0.88514, 2, 10, 42.59, -34.44, 0.37306, 11, 11.13, -34.31, 0.62694, 2, 10, 35.03, -40.38, 0.49674, 11, 3.64, -40.34, 0.50326, 2, 10, 27.27, -39.44, 0.57308, 11, -4.13, -39.49, 0.42692, 3, 9, 23.83, 38.67, 0.34206, 10, 13.84, -30.86, 0.5214, 11, -17.67, -31.08, 0.13654, 3, 9, 16.36, 33.58, 0.53067, 10, 9.06, -23.18, 0.41829, 11, -22.53, -23.45, 0.05104, 3, 9, 4.02, 30.46, 0.67909, 10, 6.47, -10.73, 0.31835, 11, -25.28, -11.03, 0.00256, 1, 9, 3.38, 21.54, 1, 1, 9, 10.85, 24.13, 1, 1, 9, 15.98, 23.01, 1, 1, 9, 20.21, 17.98, 1, 1, 9, 21.5, 11.41, 1, 1, 9, 18.43, -6.82, 1, 1, 9, 15.18, -10.02, 1, 1, 9, 6.95, -13.12, 1, 1, 9, -6.12, -13.09, 1, 1, 9, -15.36, -9.27, 1, 1, 9, -20.98, -3.75, 1, 1, 9, -22.37, 15.14, 1, 1, 9, -20.4, 18.66, 1, 3, 9, -20.98, 26.9, 0.6789, 10, 3.96, 14.4, 0.31542, 11, -28.09, 14.07, 0.00567, 3, 9, -30.44, 35.15, 0.44661, 10, 12.6, 23.5, 0.45713, 11, -19.56, 23.27, 0.09626, 3, 9, -39.75, 42.74, 0.21494, 10, 20.57, 32.49, 0.48318, 11, -11.7, 32.35, 0.30188, 2, 10, 36.2, 36.45, 0.34778, 11, 3.89, 36.5, 0.65222, 2, 10, 47.09, 32.31, 0.19491, 11, 14.83, 32.49, 0.80509, 2, 10, 52.32, 26.63, 0.1092, 11, 20.12, 26.88, 0.8908, 3, 9, -20.11, 42.76, 0.31963, 10, 19.77, 12.87, 0.58879, 11, -12.26, 12.72, 0.09157, 3, 9, 5.3, 43.51, 0.37972, 10, 19.45, -12.55, 0.56805, 11, -12.28, -12.7, 0.05224, 2, 10, 34.18, 13.86, 0.35084, 11, 2.13, 13.89, 0.64916, 3, 9, 8.15, 58.28, 0.04919, 10, 34.09, -16.02, 0.39865, 11, 2.41, -15.99, 0.55216, 2, 10, 47.69, 11.38, 0.02795, 11, 15.68, 11.57, 0.97205, 2, 10, 47.89, -14.38, 0.06823, 11, 16.18, -14.19, 0.93177, 1, 9, -7.25, 9.38, 1, 1, 9, 10.18, 13.63, 1], "hull": 29, "edges": [0, 56, 4, 6, 10, 12, 12, 14, 28, 30, 30, 32, 32, 34, 34, 36, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 20, 22, 22, 24, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 6, 8, 8, 10, 0, 2, 2, 4, 70, 72], "width": 78, "height": 98}}, "Up": {"Up": {"type": "mesh", "uvs": [0.42419, 0.00558, 0.55115, 0.00669, 0.65266, 0.0391, 0.75592, 0.10242, 0.84787, 0.19902, 0.90426, 0.28765, 0.95864, 0.4155, 0.99223, 0.56088, 0.98954, 0.7321, 0.93346, 0.83574, 0.81662, 0.92594, 0.63276, 0.9937, 0.38445, 0.99517, 0.1952, 0.93852, 0.05823, 0.8319, 0.0046, 0.71168, 0.00812, 0.57147, 0.02817, 0.43161, 0.06087, 0.32445, 0.13241, 0.19455, 0.19105, 0.12379, 0.26243, 0.06774, 0.33025, 0.03291, 0.08741, 0.60131, 0.22996, 0.69286, 0.40839, 0.72949, 0.61972, 0.71902, 0.7822, 0.67586, 0.91677, 0.60785, 0.14423, 0.37242, 0.29176, 0.46267, 0.50807, 0.4849, 0.71043, 0.44566, 0.843, 0.39073, 0.73834, 0.18538, 0.58981, 0.22593, 0.38347, 0.2207, 0.25089, 0.16838, 0.39216, 0.07657, 0.59929, 0.07657], "triangles": [39, 1, 2, 38, 22, 0, 38, 0, 1, 38, 1, 39, 37, 20, 21, 34, 2, 3, 34, 3, 4, 38, 37, 22, 37, 21, 22, 38, 36, 37, 35, 38, 39, 36, 38, 35, 34, 35, 39, 34, 39, 2, 37, 29, 19, 37, 19, 20, 18, 19, 29, 33, 34, 4, 33, 4, 5, 33, 5, 6, 17, 18, 29, 32, 35, 34, 32, 34, 33, 30, 37, 36, 29, 37, 30, 31, 36, 35, 31, 35, 32, 30, 36, 31, 23, 17, 29, 23, 29, 30, 16, 17, 23, 28, 33, 6, 28, 6, 7, 27, 32, 33, 27, 33, 28, 24, 23, 30, 15, 16, 23, 26, 31, 32, 26, 32, 27, 25, 30, 31, 25, 31, 26, 24, 30, 25, 8, 28, 7, 14, 15, 23, 14, 23, 24, 9, 28, 8, 27, 28, 9, 10, 27, 9, 26, 27, 10, 13, 14, 24, 11, 26, 10, 25, 13, 24, 12, 13, 25, 11, 12, 25, 11, 25, 26], "vertices": [115.42, 21.23, 115.25, -4.93, 110.16, -25.84, 100.22, -47.11, 85.05, -66.05, 71.13, -77.67, 51.06, -88.87, 28.24, -95.79, 1.36, -95.24, -14.92, -83.68, -29.08, -59.61, -39.71, -21.74, -39.95, 29.41, -31.05, 68.4, -14.31, 96.61, 4.56, 107.66, 26.58, 106.94, 48.53, 102.81, 65.36, 96.07, 85.75, 81.33, 96.86, 69.25, 105.66, 54.55, 111.13, 40.58, 21.89, 90.6, 7.52, 61.24, 1.77, 24.48, 3.41, -19.05, 10.19, -52.52, 20.86, -80.24, 57.83, 78.9, 43.66, 48.51, 40.17, 3.95, 46.33, -37.74, 54.95, -65.05, 87.19, -43.49, 80.83, -12.89, 81.65, 29.62, 89.86, 56.93, 104.27, 27.83, 104.27, -14.84], "hull": 23, "edges": [16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 14, 16, 50, 52, 52, 54, 54, 56, 58, 60, 62, 64, 64, 66, 60, 62, 76, 78, 74, 72, 72, 70, 70, 68, 46, 48, 48, 50, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 10, 12], "width": 206, "height": 157}}, "X1": {"X1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [230.74, 298.47, 207.74, 298.47, 207.74, 317.47, 230.74, 317.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 19}}, "X2": {"X2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [239.74, 282.47, 205.74, 282.47, 205.74, 304.47, 239.74, 304.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 22}}}}], "animations": {"idle": {"slots": {"light": {"rgba": [{"color": "ffffffb0", "curve": [0.336, 1, 0.668, 1, 0.336, 1, 0.668, 1, 0.336, 1, 0.668, 1, 0.336, 0.52, 0.668, 0.2]}, {"time": 1, "color": "ffffff32", "curve": [1.444, 1, 1.889, 1, 1.444, 1, 1.889, 1, 1.444, 1, 1.889, 1, 1.444, 0.2, 1.889, 0.78]}, {"time": 2.3333, "color": "ffffffc8", "curve": [2.778, 1, 3.222, 1, 2.778, 1, 3.222, 1, 2.778, 1, 3.222, 1, 2.778, 0.78, 3.222, 0.2]}, {"time": 3.6667, "color": "ffffff32", "curve": [4.111, 1, 4.556, 1, 4.111, 1, 4.556, 1, 4.111, 1, 4.556, 1, 4.111, 0.2, 4.556, 0.78]}, {"time": 5, "color": "ffffffc8", "curve": [5.246, 1, 5.442, 1, 5.246, 1, 5.442, 1, 5.246, 1, 5.442, 1, 5.244, 0.78, 5.489, 0.2]}, {"time": 5.7333, "color": "ffffff32", "curve": [5.767, 1, 5.8, 1, 5.767, 1, 5.8, 1, 5.767, 1, 5.8, 1, 5.767, 0.2, 5.8, 0.78]}, {"time": 5.8333, "color": "ffffffc8", "curve": [6.102, 1, 5.668, 1, 6.102, 1, 5.668, 1, 6.102, 1, 5.668, 1, 5.867, 0.78, 5.9, 0.2]}, {"time": 5.9333, "color": "ffffff32", "curve": [5.967, 1, 6, 1, 5.967, 1, 6, 1, 5.967, 1, 6, 1, 5.967, 0.2, 6, 0.78]}, {"time": 6.0333, "color": "ffffffc8", "curve": [6.369, 1, 5.802, 1, 6.369, 1, 5.802, 1, 6.369, 1, 5.802, 1, 6.067, 0.78, 6.1, 0.2]}, {"time": 6.1333, "color": "ffffff32", "curve": [6.167, 1, 6.2, 1, 6.167, 1, 6.2, 1, 6.167, 1, 6.2, 1, 6.167, 0.2, 6.2, 0.78]}, {"time": 6.2333, "color": "ffffffc8", "curve": [6.401, 1, 6.3, 1, 6.401, 1, 6.3, 1, 6.401, 1, 6.3, 1, 6.267, 0.78, 6.3, 0.2]}, {"time": 6.3333, "color": "ffffff32", "curve": [6.778, 1, 7.222, 1, 6.778, 1, 7.222, 1, 6.778, 1, 7.222, 1, 6.778, 0.2, 7.222, 0.78]}, {"time": 7.6667, "color": "ffffffc8", "curve": [8.111, 1, 8.556, 1, 8.111, 1, 8.556, 1, 8.111, 1, 8.556, 1, 8.111, 0.78, 8.556, 0.2]}, {"time": 9, "color": "ffffff32", "curve": [9.444, 1, 9.889, 1, 9.444, 1, 9.889, 1, 9.444, 1, 9.889, 1, 9.444, 0.2, 9.889, 0.78]}, {"time": 10.3333, "color": "ffffffc8", "curve": [10.446, 1, 10.559, 1, 10.446, 1, 10.559, 1, 10.446, 1, 10.559, 1, 10.446, 0.78, 10.559, 0.75]}, {"time": 10.6667, "color": "ffffffb0"}]}, "light_O": {"rgba": [{"color": "ffffff64", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0.39, 0.889, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 0.39]}, {"time": 2.6667, "color": "ffffff64", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0.39, 3.556, 1]}, {"time": 4, "color": "ffffffff", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 0.39]}, {"time": 5.3333, "color": "ffffff64", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0.39, 6.222, 1]}, {"time": 6.6667, "color": "ffffffff", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 0.39]}, {"time": 8, "color": "ffffff64", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0.39, 8.889, 1]}, {"time": 9.3333, "color": "ffffffff", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 0.39]}, {"time": 10.6667, "color": "ffffff64"}]}, "light_red": {"rgba": [{"color": "ffffff7d", "curve": [0.114, 1, 0.224, 1, 0.114, 1, 0.224, 1, 0.114, 1, 0.224, 1, 0.114, 0.43, 0.224, 0.39]}, {"time": 0.3333, "color": "ffffff64", "curve": [0.778, 1, 1.222, 1, 0.778, 1, 1.222, 1, 0.778, 1, 1.222, 1, 0.778, 0.39, 1.222, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": [2.111, 1, 2.556, 1, 2.111, 1, 2.556, 1, 2.111, 1, 2.556, 1, 2.111, 1, 2.556, 0.39]}, {"time": 3, "color": "ffffff64", "curve": [3.444, 1, 3.889, 1, 3.444, 1, 3.889, 1, 3.444, 1, 3.889, 1, 3.444, 0.39, 3.889, 1]}, {"time": 4.3333, "color": "ffffffff", "curve": [4.778, 1, 5.222, 1, 4.778, 1, 5.222, 1, 4.778, 1, 5.222, 1, 4.778, 1, 5.222, 0.39]}, {"time": 5.6667, "color": "ffffff64", "curve": [6.111, 1, 6.556, 1, 6.111, 1, 6.556, 1, 6.111, 1, 6.556, 1, 6.111, 0.39, 6.556, 1]}, {"time": 7, "color": "ffffffff", "curve": [7.444, 1, 7.889, 1, 7.444, 1, 7.889, 1, 7.444, 1, 7.889, 1, 7.444, 1, 7.889, 0.39]}, {"time": 8.3333, "color": "ffffff64", "curve": [8.778, 1, 9.222, 1, 8.778, 1, 9.222, 1, 8.778, 1, 9.222, 1, 8.778, 0.39, 9.222, 1]}, {"time": 9.6667, "color": "ffffffff", "curve": [10.001, 1, 10.336, 1, 10.001, 1, 10.336, 1, 10.001, 1, 10.336, 1, 10.001, 1, 10.336, 0.66]}, {"time": 10.6667, "color": "ffffff7d"}]}, "light_red2": {"rgba": [{"color": "ffffff7d", "curve": [0.114, 1, 0.224, 1, 0.114, 1, 0.224, 1, 0.114, 1, 0.224, 1, 0.114, 0.43, 0.224, 0.39]}, {"time": 0.3333, "color": "ffffff64", "curve": [0.778, 1, 1.222, 1, 0.778, 1, 1.222, 1, 0.778, 1, 1.222, 1, 0.778, 0.39, 1.222, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": [2.111, 1, 2.556, 1, 2.111, 1, 2.556, 1, 2.111, 1, 2.556, 1, 2.111, 1, 2.556, 0.39]}, {"time": 3, "color": "ffffff64", "curve": [3.444, 1, 3.889, 1, 3.444, 1, 3.889, 1, 3.444, 1, 3.889, 1, 3.444, 0.39, 3.889, 1]}, {"time": 4.3333, "color": "ffffffff", "curve": [4.778, 1, 5.222, 1, 4.778, 1, 5.222, 1, 4.778, 1, 5.222, 1, 4.778, 1, 5.222, 0.39]}, {"time": 5.6667, "color": "ffffff64", "curve": [6.111, 1, 6.556, 1, 6.111, 1, 6.556, 1, 6.111, 1, 6.556, 1, 6.111, 0.39, 6.556, 1]}, {"time": 7, "color": "ffffffff", "curve": [7.444, 1, 7.889, 1, 7.444, 1, 7.889, 1, 7.444, 1, 7.889, 1, 7.444, 1, 7.889, 0.39]}, {"time": 8.3333, "color": "ffffff64", "curve": [8.778, 1, 9.222, 1, 8.778, 1, 9.222, 1, 8.778, 1, 9.222, 1, 8.778, 0.39, 9.222, 1]}, {"time": 9.6667, "color": "ffffffff", "curve": [10.001, 1, 10.336, 1, 10.001, 1, 10.336, 1, 10.001, 1, 10.336, 1, 10.001, 1, 10.336, 0.66]}, {"time": 10.6667, "color": "ffffff7d"}]}, "O": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}}, "bones": {"tree2": {"rotate": [{"value": -0.87, "curve": [0.39, -0.48, 0.779, 1.06]}, {"time": 1.1667, "value": 1.06, "curve": [1.611, 1.06, 2.056, -0.97]}, {"time": 2.5, "value": -0.97, "curve": [2.556, -0.97, 2.611, -0.93]}, {"time": 2.6667, "value": -0.87, "curve": [3.057, -0.48, 3.445, 1.06]}, {"time": 3.8333, "value": 1.06, "curve": [4.278, 1.06, 4.722, -0.97]}, {"time": 5.1667, "value": -0.97, "curve": [5.223, -0.97, 5.278, -0.93]}, {"time": 5.3333, "value": -0.87, "curve": [5.724, -0.48, 6.112, 1.06]}, {"time": 6.5, "value": 1.06, "curve": [6.944, 1.06, 7.389, -0.97]}, {"time": 7.8333, "value": -0.97, "curve": [7.89, -0.97, 7.944, -0.93]}, {"time": 8, "value": -0.87, "curve": [8.39, -0.48, 8.779, 1.06]}, {"time": 9.1667, "value": 1.06, "curve": [9.611, 1.06, 10.056, -0.97]}, {"time": 10.5, "value": -0.97, "curve": [10.556, -0.97, 10.613, -0.93]}, {"time": 10.6667, "value": -0.87}]}, "tree3": {"rotate": [{"value": -0.87, "curve": [0.057, -0.93, 0.112, -0.97]}, {"time": 0.1667, "value": -0.97, "curve": [0.611, -0.97, 1.056, 1.06]}, {"time": 1.5, "value": 1.06, "curve": [1.89, 1.06, 2.278, -0.5]}, {"time": 2.6667, "value": -0.87, "curve": [2.724, -0.93, 2.779, -0.97]}, {"time": 2.8333, "value": -0.97, "curve": [3.278, -0.97, 3.722, 1.06]}, {"time": 4.1667, "value": 1.06, "curve": [4.556, 1.06, 4.944, -0.5]}, {"time": 5.3333, "value": -0.87, "curve": [5.39, -0.93, 5.445, -0.97]}, {"time": 5.5, "value": -0.97, "curve": [5.944, -0.97, 6.389, 1.06]}, {"time": 6.8333, "value": 1.06, "curve": [7.223, 1.06, 7.611, -0.5]}, {"time": 8, "value": -0.87, "curve": [8.057, -0.93, 8.112, -0.97]}, {"time": 8.1667, "value": -0.97, "curve": [8.611, -0.97, 9.056, 1.06]}, {"time": 9.5, "value": 1.06, "curve": [9.89, 1.06, 10.279, -0.49]}, {"time": 10.6667, "value": -0.87}]}, "tree4": {"rotate": [{"value": -0.32, "curve": [0.168, -0.68, 0.334, -0.97]}, {"time": 0.5, "value": -0.97, "curve": [0.944, -0.97, 1.389, 1.06]}, {"time": 1.8333, "value": 1.06, "curve": [2.112, 1.06, 2.389, 0.26]}, {"time": 2.6667, "value": -0.32, "curve": [2.835, -0.68, 3.001, -0.97]}, {"time": 3.1667, "value": -0.97, "curve": [3.611, -0.97, 4.056, 1.06]}, {"time": 4.5, "value": 1.06, "curve": [4.779, 1.06, 5.056, 0.26]}, {"time": 5.3333, "value": -0.32, "curve": [5.501, -0.68, 5.667, -0.97]}, {"time": 5.8333, "value": -0.97, "curve": [6.278, -0.97, 6.722, 1.06]}, {"time": 7.1667, "value": 1.06, "curve": [7.445, 1.06, 7.722, 0.26]}, {"time": 8, "value": -0.32, "curve": [8.168, -0.68, 8.334, -0.97]}, {"time": 8.5, "value": -0.97, "curve": [8.944, -0.97, 9.389, 1.06]}, {"time": 9.8333, "value": 1.06, "curve": [10.112, 1.06, 10.39, 0.27]}, {"time": 10.6667, "value": -0.32}]}, "tree5": {"rotate": [{"value": 0.42, "curve": [0.279, -0.18, 0.556, -0.97]}, {"time": 0.8333, "value": -0.97, "curve": [1.278, -0.97, 1.722, 1.06]}, {"time": 2.1667, "value": 1.06, "curve": [2.334, 1.06, 2.5, 0.77]}, {"time": 2.6667, "value": 0.42, "curve": [2.946, -0.18, 3.223, -0.97]}, {"time": 3.5, "value": -0.97, "curve": [3.944, -0.97, 4.389, 1.06]}, {"time": 4.8333, "value": 1.06, "curve": [5.001, 1.06, 5.167, 0.77]}, {"time": 5.3333, "value": 0.42, "curve": [5.613, -0.18, 5.89, -0.97]}, {"time": 6.1667, "value": -0.97, "curve": [6.611, -0.97, 7.056, 1.06]}, {"time": 7.5, "value": 1.06, "curve": [7.667, 1.06, 7.833, 0.77]}, {"time": 8, "value": 0.42, "curve": [8.279, -0.18, 8.556, -0.97]}, {"time": 8.8333, "value": -0.97, "curve": [9.278, -0.97, 9.722, 1.06]}, {"time": 10.1667, "value": 1.06, "curve": [10.334, 1.06, 10.501, 0.78]}, {"time": 10.6667, "value": 0.42}]}, "tree_Fb": {"translate": [{"time": 4.2333, "curve": [4.362, 0.36, 4.522, 0.71, 4.362, 31.02, 4.522, 60.85]}, {"time": 4.6667, "x": 0.71, "y": 60.85, "curve": [4.811, 0.71, 4.973, 0.33, 4.811, 60.85, 4.973, 28.81]}, {"time": 5.1}], "scale": [{"time": 4, "curve": [4.078, 1, 4.156, 0.938, 4.078, 1, 4.156, 1.047]}, {"time": 4.2333, "x": 0.938, "y": 1.047, "curve": [4.244, 0.938, 4.256, 1.082, 4.244, 1.047, 4.256, 0.947]}, {"time": 4.2667, "x": 1.082, "y": 0.947, "curve": [4.4, 1.082, 4.533, 1, 4.4, 0.947, 4.533, 1]}, {"time": 4.6667, "curve": [4.8, 1, 4.933, 1.082, 4.8, 1, 4.933, 0.947]}, {"time": 5.0667, "x": 1.082, "y": 0.947, "curve": [5.078, 1.082, 5.089, 0.938, 5.078, 0.947, 5.089, 1.047]}, {"time": 5.1, "x": 0.938, "y": 1.047, "curve": [5.178, 0.938, 5.256, 1, 5.178, 1.047, 5.256, 1]}, {"time": 5.3333}]}, "tree_Fb2": {"rotate": [{"value": 3.62, "curve": [0.444, 3.62, 0.889, -3.7]}, {"time": 1.3333, "value": -3.7, "curve": [1.778, -3.7, 2.222, 3.62]}, {"time": 2.6667, "value": 3.62, "curve": [3.111, 3.62, 3.556, -3.7]}, {"time": 4, "value": -3.7, "curve": [4.444, -3.7, 4.889, 3.62]}, {"time": 5.3333, "value": 3.62, "curve": [5.778, 3.62, 6.222, -3.7]}, {"time": 6.6667, "value": -3.7, "curve": [7.111, -3.7, 7.556, 3.62]}, {"time": 8, "value": 3.62, "curve": [8.444, 3.62, 8.889, -3.7]}, {"time": 9.3333, "value": -3.7, "curve": [9.778, -3.7, 10.222, 3.62]}, {"time": 10.6667, "value": 3.62}]}, "tree_Fb3": {"rotate": [{"value": 2.45, "curve": [0.114, 3.12, 0.224, 3.62]}, {"time": 0.3333, "value": 3.62, "curve": [0.778, 3.62, 1.222, -3.7]}, {"time": 1.6667, "value": -3.7, "curve": [2.001, -3.7, 2.333, 0.49]}, {"time": 2.6667, "value": 2.45, "curve": [2.781, 3.12, 2.89, 3.62]}, {"time": 3, "value": 3.62, "curve": [3.444, 3.62, 3.889, -3.7]}, {"time": 4.3333, "value": -3.7, "curve": [4.668, -3.7, 5, 0.49]}, {"time": 5.3333, "value": 2.45, "curve": [5.447, 3.12, 5.557, 3.62]}, {"time": 5.6667, "value": 3.62, "curve": [6.111, 3.62, 6.556, -3.7]}, {"time": 7, "value": -3.7, "curve": [7.335, -3.7, 7.667, 0.49]}, {"time": 8, "value": 2.45, "curve": [8.114, 3.12, 8.224, 3.62]}, {"time": 8.3333, "value": 3.62, "curve": [8.778, 3.62, 9.222, -3.7]}, {"time": 9.6667, "value": -3.7, "curve": [10.001, -3.7, 10.336, 0.4]}, {"time": 10.6667, "value": 2.45}]}, "tree_L2": {"rotate": [{"value": -1.71, "curve": [0.279, 0.22, 0.556, 2.78]}, {"time": 0.8333, "value": 2.78, "curve": [1.278, 2.78, 1.722, -3.8]}, {"time": 2.1667, "value": -3.8, "curve": [2.334, -3.8, 2.5, -2.86]}, {"time": 2.6667, "value": -1.71, "curve": [2.946, 0.22, 3.223, 2.78]}, {"time": 3.5, "value": 2.78, "curve": [3.944, 2.78, 4.389, -3.8]}, {"time": 4.8333, "value": -3.8, "curve": [5.001, -3.8, 5.167, -2.86]}, {"time": 5.3333, "value": -1.71, "curve": [5.613, 0.22, 5.89, 2.78]}, {"time": 6.1667, "value": 2.78, "curve": [6.611, 2.78, 7.056, -3.8]}, {"time": 7.5, "value": -3.8, "curve": [7.667, -3.8, 7.833, -2.86]}, {"time": 8, "value": -1.71, "curve": [8.279, 0.22, 8.556, 2.78]}, {"time": 8.8333, "value": 2.78, "curve": [9.278, 2.78, 9.722, -3.8]}, {"time": 10.1667, "value": -3.8, "curve": [10.334, -3.8, 10.501, -2.87]}, {"time": 10.6667, "value": -1.71}]}, "tree_L3": {"rotate": [{"value": -3.49, "curve": [0.39, -2.22, 0.779, 2.78]}, {"time": 1.1667, "value": 2.78, "curve": [1.611, 2.78, 2.056, -3.8]}, {"time": 2.5, "value": -3.8, "curve": [2.556, -3.8, 2.611, -3.67]}, {"time": 2.6667, "value": -3.49, "curve": [3.057, -2.22, 3.445, 2.78]}, {"time": 3.8333, "value": 2.78, "curve": [4.278, 2.78, 4.722, -3.8]}, {"time": 5.1667, "value": -3.8, "curve": [5.223, -3.8, 5.278, -3.67]}, {"time": 5.3333, "value": -3.49, "curve": [5.724, -2.22, 6.112, 2.78]}, {"time": 6.5, "value": 2.78, "curve": [6.944, 2.78, 7.389, -3.8]}, {"time": 7.8333, "value": -3.8, "curve": [7.89, -3.8, 7.944, -3.67]}, {"time": 8, "value": -3.49, "curve": [8.39, -2.22, 8.779, 2.78]}, {"time": 9.1667, "value": 2.78, "curve": [9.611, 2.78, 10.056, -3.8]}, {"time": 10.5, "value": -3.8, "curve": [10.556, -3.8, 10.613, -3.68]}, {"time": 10.6667, "value": -3.49}]}, "Up": {"translate": [{"time": 5, "curve": [5.056, 0, 5.111, 0, 5.056, 0, 5.111, 17.92]}, {"time": 5.1667, "y": 17.92, "curve": "stepped"}, {"time": 7.6667, "y": 17.92, "curve": [10.13, 0, 5.703, 0, 7.833, 17.92, 8, 0]}, {"time": 8.1667}], "scale": [{"time": 5, "curve": [5.056, 1, 5.111, 1.16, 5.056, 1, 5.111, 1.16]}, {"time": 5.1667, "x": 1.16, "y": 1.16, "curve": [5.244, 1.16, 5.322, 1.094, 5.244, 1.16, 5.322, 1.094]}, {"time": 5.4, "x": 1.094, "y": 1.094, "curve": [5.478, 1.094, 5.556, 1.169, 5.478, 1.094, 5.556, 1.169]}, {"time": 5.6333, "x": 1.169, "y": 1.169, "curve": [5.711, 1.169, 5.789, 1.136, 5.711, 1.169, 5.789, 1.136]}, {"time": 5.8667, "x": 1.136, "y": 1.136, "curve": "stepped"}, {"time": 7.6667, "x": 1.136, "y": 1.136, "curve": [7.833, 1.136, 8, 1, 7.833, 1.136, 8, 1]}, {"time": 8.1667}]}, "UP": {"translate": [{"y": 0.91, "curve": [0.225, 0, 0.446, 0, 0.225, -3.84, 0.446, -8.65]}, {"time": 0.6667, "y": -8.65, "curve": [1.111, 0, 1.556, 0, 1.111, -8.65, 1.556, 10.47]}, {"time": 2, "y": 10.47, "curve": [2.444, 0, 2.889, 0, 2.444, 10.47, 2.889, -8.65]}, {"time": 3.3333, "y": -8.65, "curve": [3.778, 0, 4.222, 0, 3.778, -8.65, 4.222, 10.47]}, {"time": 4.6667, "y": 10.47, "curve": [5.111, 0, 5.556, 0, 5.111, 10.47, 5.556, -8.65]}, {"time": 6, "y": -8.65, "curve": [6.444, 0, 6.889, 0, 6.444, -8.65, 6.889, 10.47]}, {"time": 7.3333, "y": 10.47, "curve": [7.778, 0, 8.222, 0, 7.778, 10.47, 8.222, -8.65]}, {"time": 8.6667, "y": -8.65, "curve": [9.111, 0, 9.556, 0, 9.111, -8.65, 9.556, 10.47]}, {"time": 10, "y": 10.47, "curve": [10.224, 0, 10.447, 0, 10.224, 10.47, 10.447, 5.72]}, {"time": 10.6667, "y": 0.91}]}}, "attachments": {"default": {"Up": {"Up": {"deform": [{"offset": 18, "vertices": [0.14627, -0.82258, 1.05213, -2.42137, 0.46395, -3.75449, -0.39589, -3.77068, -1.09953, -2.48527, -0.1465, -0.82183, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34748, -3.20818, 0.28569, -5.44697, 0.19354, -7.68035, 0.0101, -7.66452, -0.19272, -5.3988, 0.32901, -3.19574, -0.70634, -4.98282, 0.0842, -6.37782, 0.1322, -7.59035, 0.06019, -6.32782, 0.74204, -4.99634, 0.51651, -3.38035, 0.31888, -5.21252, -0.27261, -5.21439, -0.51413, -3.36251, -0.15548, -2.04086, 0.15548, -2.03733], "curve": [0.336, 0.44, 0.668, 1]}, {"time": 1, "offset": 19, "vertices": [1.23456, -0.99478, 7.58224, -0.3606, 10.95104, 0.4274, 10.94856, 0.9776, 7.5975, 0, 1.23456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.21838, 6.75848, 4.46201, 14.23593, 1.83353, 17.41401, -1.54639, 17.41767, -4.3656, 14.37141, -2.32208, 6.84612, 1.01276, 7.18645, 3.41061, 12.50463, 0.11887, 15.53944, -3.39066, 12.56234, -1.04053, 7.24213, -0.55872, 4.63573, -0.67651, 7.93668, 0.75278, 7.88501, 0.5289, 4.6009, 0.36142, 2.80807, -0.36142, 2.81419], "curve": [1.444, 0, 1.889, 1]}, {"time": 2.3333, "offset": 18, "vertices": [0.17413, -1.21442, 1.44202, -4.32682, 0.621, -6.55555, -0.5527, -6.57434, -1.49518, -4.4058, -0.17441, -1.21352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.83621, -5.10659, -0.5098, -9.19609, -0.11884, -12.46022, 0.30658, -12.44208, 0.60211, -9.16456, 0.83398, -5.10847, -1.03378, -7.30078, -0.54941, -9.97447, 0.13474, -11.99603, 0.7175, -9.92594, 1.08157, -7.32748, 0.72131, -4.90722, 0.50848, -7.71713, -0.46793, -7.70951, -0.7128, -4.87934, -0.25394, -2.96447, 0.25394, -2.96143], "curve": [2.778, 0, 3.222, 1]}, {"time": 3.6667, "offset": 19, "vertices": [1.23456, -0.99478, 7.58224, -0.3606, 10.95104, 0.4274, 10.94856, 0.9776, 7.5975, 0, 1.23456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.21838, 6.75848, 4.46201, 14.23593, 1.83353, 17.41401, -1.54639, 17.41767, -4.3656, 14.37141, -2.32208, 6.84612, 1.01276, 7.18645, 3.41061, 12.50463, 0.11887, 15.53944, -3.39066, 12.56234, -1.04053, 7.24213, -0.55872, 4.63573, -0.67651, 7.93668, 0.75278, 7.88501, 0.5289, 4.6009, 0.36142, 2.80807, -0.36142, 2.81419], "curve": [4.111, 0, 4.653, 1.78]}, {"time": 5, "offset": 18, "vertices": [0.17413, -1.21442, 1.44202, -4.32682, 0.621, -6.55555, -0.5527, -6.57434, -1.49518, -4.4058, -0.17441, -1.21352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.83621, -5.10659, -0.5098, -9.19609, -0.11884, -12.46022, 0.30658, -12.44208, 0.60211, -9.16456, 0.83398, -5.10847, -1.03378, -7.30078, -0.54941, -9.97447, 0.13474, -11.99603, 0.7175, -9.92594, 1.08157, -7.32748, 0.72131, -4.90722, 0.50848, -7.71713, -0.46793, -7.70951, -0.7128, -4.87934, -0.25394, -2.96447, 0.25394, -2.96143], "curve": [5.444, 0, 5.889, 1]}, {"time": 6.3333, "offset": 19, "vertices": [1.23456, -0.99478, 7.58224, -0.3606, 10.95104, 0.4274, 10.94856, 0.9776, 7.5975, 0, 1.23456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.21838, 6.75848, 4.46201, 14.23593, 1.83353, 17.41401, -1.54639, 17.41767, -4.3656, 14.37141, -2.32208, 6.84612, 1.01276, 7.18645, 3.41061, 12.50463, 0.11887, 15.53944, -3.39066, 12.56234, -1.04053, 7.24213, -0.55872, 4.63573, -0.67651, 7.93668, 0.75278, 7.88501, 0.5289, 4.6009, 0.36142, 2.80807, -0.36142, 2.81419], "curve": [6.778, 0, 7.222, 1]}, {"time": 7.6667, "offset": 18, "vertices": [0.17413, -1.21442, 1.44202, -4.32682, 0.621, -6.55555, -0.5527, -6.57434, -1.49518, -4.4058, -0.17441, -1.21352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.83621, -5.10659, -0.5098, -9.19609, -0.11884, -12.46022, 0.30658, -12.44208, 0.60211, -9.16456, 0.83398, -5.10847, -1.03378, -7.30078, -0.54941, -9.97447, 0.13474, -11.99603, 0.7175, -9.92594, 1.08157, -7.32748, 0.72131, -4.90722, 0.50848, -7.71713, -0.46793, -7.70951, -0.7128, -4.87934, -0.25394, -2.96447, 0.25394, -2.96143], "curve": [8.111, 0, 8.556, 1]}, {"time": 9, "offset": 19, "vertices": [1.23456, -0.99478, 7.58224, -0.3606, 10.95104, 0.4274, 10.94856, 0.9776, 7.5975, 0, 1.23456, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.21838, 6.75848, 4.46201, 14.23593, 1.83353, 17.41401, -1.54639, 17.41767, -4.3656, 14.37141, -2.32208, 6.84612, 1.01276, 7.18645, 3.41061, 12.50463, 0.11887, 15.53944, -3.39066, 12.56234, -1.04053, 7.24213, -0.55872, 4.63573, -0.67651, 7.93668, 0.75278, 7.88501, 0.5289, 4.6009, 0.36142, 2.80807, -0.36142, 2.81419], "curve": [9.444, 0, 9.889, 1]}, {"time": 10.3333, "offset": 18, "vertices": [0.17413, -1.21442, 1.44202, -4.32682, 0.621, -6.55555, -0.5527, -6.57434, -1.49518, -4.4058, -0.17441, -1.21352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.83621, -5.10659, -0.5098, -9.19609, -0.11884, -12.46022, 0.30658, -12.44208, 0.60211, -9.16456, 0.83398, -5.10847, -1.03378, -7.30078, -0.54941, -9.97447, 0.13474, -11.99603, 0.7175, -9.92594, 1.08157, -7.32748, 0.72131, -4.90722, 0.50848, -7.71713, -0.46793, -7.70951, -0.7128, -4.87934, -0.25394, -2.96447, 0.25394, -2.96143], "curve": [10.446, 0, 10.559, 0.06]}, {"time": 10.6667, "offset": 18, "vertices": [0.14627, -0.82258, 1.05213, -2.42137, 0.46395, -3.75449, -0.39589, -3.77068, -1.09953, -2.48527, -0.1465, -0.82183, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34748, -3.20818, 0.28569, -5.44697, 0.19354, -7.68035, 0.0101, -7.66452, -0.19272, -5.3988, 0.32901, -3.19574, -0.70634, -4.98282, 0.0842, -6.37782, 0.1322, -7.59035, 0.06019, -6.32782, 0.74204, -4.99634, 0.51651, -3.38035, 0.31888, -5.21252, -0.27261, -5.21439, -0.51413, -3.36251, -0.15548, -2.04086, 0.15548, -2.03733]}]}}}}}}}