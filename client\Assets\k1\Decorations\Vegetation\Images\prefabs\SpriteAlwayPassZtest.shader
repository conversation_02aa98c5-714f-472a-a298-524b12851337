﻿Shader "Sprites/ZTestAlwaysPass"
{
    Properties
    {
        [PerRendererData]_MainTex ("Texture", 2D) = "white" {}
		_Color ("Tint", Color) = (1,1,1,1)
		_UseOverLy("Use Overlay", Range(0, 1)) = 0
    }
    SubShader
    {
        Tags { "Queue" = "Transparent" }
        LOD 200

        Pass
        {
		    ZWrite Off ZTest Always
			Blend SrcAlpha OneMinusSrcAlpha

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
				float4 color    : COLOR;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
				fixed4 color    : COLOR;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
			fixed4 _Color;
			half _UseOverLy;
			
			fixed3 Overlay(fixed3 baseColor, fixed3 topColor)
			{
				if (Luminance(baseColor) < 0.5)
				{
					return 2.0 * baseColor * topColor;
				}
				else
				{
					return 1.0 - 2.0 * (1.0 - baseColor) * (1.0 - topColor);
				}
			}

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
				o.color = v.color * _Color;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);
				col.rgb = col.rgb * (1 - step(1, _UseOverLy)) + Overlay(col.rgb, i.color.rgb) * step(1, _UseOverLy);
                
				return col;
            }
            ENDCG
        }
    }
}
