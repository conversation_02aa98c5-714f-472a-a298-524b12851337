#if USE_WXSDK
using System;
using System.IO;
using System.Net;

namespace CISystem.WeChat
{
    public static class Jenkins
    {
        public static void BuildAndExportWeChat()
        {
            var argsParser = new CommandLineParser(Environment.GetCommandLineArgs());
            var argsDic = argsParser.Options;

            if (argsDic.TryGetValue("-cdnUrl", out var cdnUrl)
                && argsDic.TryGetValue("-appid", out var appid)
                && argsDic.TryGetValue("-exportPath", out var exportPath)
                && argsDic.TryGetValue("-developmentBuild", out var developmentBuild))
            {
                var devBuild = bool.Parse(developmentBuild);
                WeChatHelper.BuildWeChat(appid, cdnUrl, exportPath, devBuild);
            }
            else
            {
                UnityEngine.Debug.LogError("Argument Error");
            }
        }

        public static void PushAssetToCDN()
        {
            var argsParser = new CommandLineParser(Environment.GetCommandLineArgs());
            var argsDic = argsParser.Options;

            if (argsDic.TryGetValue("-exportPath", out var exportPath)
                && argsDic.TryGetValue("-svnPath", out var svnPath)
                && argsDic.TryGetValue("-versionCode", out var versionCode))
            {
                WeChatHelper.CopyBuildArtifactToSVN(exportPath, svnPath, versionCode);
                WeChatHelper.PushSvnToRemote(svnPath, versionCode, "Automatically uploaded minigame assets");
            }
            else
            {
                UnityEngine.Debug.LogError("Argument Error");
            }
        }

        public static void ArchiveExportProjectAndNotify()
        {
            var argsParser = new CommandLineParser(Environment.GetCommandLineArgs());
            var argsDic = argsParser.Options;

            if (argsDic.TryGetValue("-exportPath", out var exportPath)
                && argsDic.TryGetValue("-versionCode", out var versionCode)
                && argsDic.TryGetValue("-archivePath", out var archivePath)
                && argsDic.TryGetValue("-archiveUrl", out var archiveUrl))
            {
                var archiveFileName = WeChatHelper.CompressProjectToZip(exportPath, versionCode, archivePath);

                Uri baseUri = new Uri(archiveUrl);
                Uri fullUri = new Uri(baseUri, archiveFileName);

                var projectPath = Path.Combine(exportPath, "minigame/wasmcode");
                var projectDirInfo = new DirectoryInfo(projectPath);
                var codeSizeSum = 0L;
                foreach (var fileInfo in projectDirInfo.GetFiles())
                {
                    var fileSize = fileInfo.Length;
                    UnityEngine.Debug.Log($"{fileSize} B: {fileInfo.FullName}");
                    codeSizeSum += fileSize;
                }

                var webglAssetPath = Path.Combine(exportPath, "webgl");
                var webglAssetDirInfo = new DirectoryInfo(webglAssetPath);
                var webglFirstAssetSizeSum = 0L;
                var webglFirstAssetFileName = string.Empty;
                foreach (var fileInfo in webglAssetDirInfo.GetFiles("*.txt"))
                {
                    var fileSize = fileInfo.Length;
                    UnityEngine.Debug.Log($"{fileSize} B: {fileInfo.FullName}");
                    webglFirstAssetSizeSum += fileSize;
                    webglFirstAssetFileName = fileInfo.Name;
                    break;
                }

                JenkinsDingDingHelper.NotifyMarkDown($"## {versionCode}版本工程已归档 \n\n [点此下载工程 {archiveFileName}]({fullUri.AbsoluteUri}) \n\n\n\n 代码包大小：{codeSizeSum / 1024f / 1024f} MB \n\n 首资源包大小：{webglFirstAssetSizeSum / 1024f / 1024f} MB");
                UnityEngine.Debug.Log("Compresss ZIP OK");
            }
            else
            {
                UnityEngine.Debug.LogError("Argument Error");
            }
        }

        public static void GeneratePreviewQRCode()
        {
            var argsParser = new CommandLineParser(Environment.GetCommandLineArgs());
            var argsDic = argsParser.Options;

            if (argsDic.TryGetValue("-appid", out var appid)
                && argsDic.TryGetValue("-exportProjectPath", out var projectPath)
                && argsDic.TryGetValue("-privateKeyPath", out var privateKeyPath)
                && argsDic.TryGetValue("-qrCodeOutputPath", out var qrCodeOutputPath)
                && argsDic.TryGetValue("-qrCodeOutputFileName", out var qrCodeOutputFileName)
                && argsDic.TryGetValue("-qrCodeUrl", out var qrCodeUrl)
                && argsDic.TryGetValue("-ftpUrl", out var ftpUrl)
                && argsDic.TryGetValue("-ftpUsername", out var ftpUsername)
                && argsDic.TryGetValue("-ftpPassword", out var ftpPassword))
            {
                WeChatHelper.GeneratePreviewQRCode(appid, projectPath, privateKeyPath, System.IO.Path.Combine(qrCodeOutputPath, qrCodeOutputFileName));

                using (var client = new WebClient())
                {
                    Uri baseFtpUri = new Uri(ftpUrl);
                    Uri fullFtpUri = new Uri(baseFtpUri, qrCodeOutputFileName);
                    client.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                    client.UploadFile(fullFtpUri.AbsoluteUri, System.IO.Path.Combine(qrCodeOutputPath, qrCodeOutputFileName));
                }

                Uri baseUri = new Uri(qrCodeUrl);
                Uri fullUri = new Uri(baseUri, qrCodeOutputFileName);

                JenkinsDingDingHelper.NotifyMarkDown($"新的开发版预览已生成 \n\n [{qrCodeOutputFileName}]({fullUri.AbsoluteUri}) \n\n ![点此查看二维码 {qrCodeOutputFileName}]({fullUri.AbsoluteUri})");
                UnityEngine.Debug.Log("GeneratePreviewQRCode OK");
            }
            else
            {
                UnityEngine.Debug.LogError("Argument Error");
            }
        }
    }
}
#endif