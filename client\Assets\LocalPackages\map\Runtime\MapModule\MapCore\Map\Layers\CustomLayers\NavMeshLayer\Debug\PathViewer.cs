﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //根据输入来绘制一条路径
    [Black]
    public static class PathViewer
    {
        public static void Create(List<Vector2> unitPath, List<Vector2> targetPath, Vector2 start, Vector2 end, float width, Color color)
        {
            if (unitPath.Count <= 1 || targetPath.Count <= 1)
            {
                EditorUtility.DisplayDialog("Error", "Invalid waypoints", "OK");
                return;
            }

            Destroy();

            var center = GetCenter(unitPath);

            mRootObject = new GameObject("Path Viewer");
            mRootObject.transform.position = center;

            mMaterial = CreateMaterial(color);

            mUnitPathMesh = CreatePath(center, "Unit Path", unitPath, width);
            mTargetPathMesh = CreatePath(center, "Target Path", targetPath, width);

            var startObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            startObject.name = "Start";
            startObject.transform.position = new Vector3(start.x, 0, start.y);
            startObject.transform.localScale = Vector3.one * 0.1f;
            startObject.transform.SetParent(mRootObject.transform);
            startObject.GetComponent<MeshRenderer>().sharedMaterial = mMaterial;

            var endObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            endObject.name = "End";
            endObject.transform.position = new Vector3(end.x, 0, end.y);
            endObject.transform.localScale = Vector3.one * 0.1f;
            endObject.transform.SetParent(mRootObject.transform);
            endObject.GetComponent<MeshRenderer>().sharedMaterial = mMaterial;
        }

        static Mesh CreatePath(Vector3 center, string name, List<Vector2> waypoints, float width)
        {
            var pathObject = GameObject.CreatePrimitive(PrimitiveType.Cube);
            pathObject.name = name;
            pathObject.transform.position = center;
            pathObject.transform.SetParent(mRootObject.transform);
            var mesh = CreatePathMesh(new Vector2(center.x, center.z), waypoints, width);
            
            pathObject.GetComponent<MeshFilter>().sharedMesh = mesh;
            pathObject.GetComponent<MeshRenderer>().sharedMaterial = mMaterial;

            for (int i = 0; i < waypoints.Count; ++i)
            {
                var wp = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                wp.name = "Waypoint_" + i;
                wp.transform.position = new Vector3(waypoints[i].x, 0, waypoints[i].y);
                wp.transform.localScale = Vector3.one * 0.1f;
                wp.transform.SetParent(mRootObject.transform);
                wp.GetComponent<MeshRenderer>().sharedMaterial = mMaterial;
                var textObj = new GameObject();
                Font ArialFont = (Font)Resources.GetBuiltinResource(typeof(Font), "Arial.ttf");
                var renderer = textObj.AddComponent<MeshRenderer>();
                renderer.sharedMaterial = ArialFont.material;
                var textMesh = textObj.AddComponent<TextMesh>();
                textMesh.text = i.ToString();
                textMesh.fontSize = 32;
                textObj.transform.position = wp.transform.position;
                textObj.transform.rotation = Quaternion.Euler(70, 0, 0);
                textObj.transform.SetParent(wp.transform);
                textObj.transform.localScale = Vector3.one;
            }

            return mesh;
        }

        static void Destroy()
        {
            if (mMaterial != null)
            {
                GameObject.DestroyImmediate(mMaterial);
                mMaterial = null;
            }
            if (mUnitPathMesh != null)
            {
                GameObject.DestroyImmediate(mUnitPathMesh);
                mUnitPathMesh = null;
            }
            if (mTargetPathMesh != null)
            {
                GameObject.DestroyImmediate(mTargetPathMesh);
                mTargetPathMesh = null;
            }
            if (mRootObject != null)
            {
                GameObject.DestroyImmediate(mRootObject);
                mRootObject = null;
            }
        }

        static Vector3 GetCenter(List<Vector2> waypoints)
        {
            Vector3 center = Vector3.zero;
            for (int i = 0; i < waypoints.Count; ++i)
            {
                center.x += waypoints[i].x;
                center.z += waypoints[i].y;
            }

            if (waypoints.Count > 0)
            {
                center /= waypoints.Count;
            }
            return center;
        }

        static Material CreateMaterial(Color color)
        {
            var shader = Shader.Find("SLGMaker/ColorTransparent");
            var mtl = new Material(shader);
            mtl.color = color;
            return mtl;
        }

        static Mesh CreatePathMesh(Vector2 center, List<Vector2> waypoints, float width)
        {
            int nSegments = waypoints.Count - 1;
            int vertexCount = waypoints.Count * 2;
            int indexCount = nSegments * 6;
            Vector3[] vertices = new Vector3[vertexCount];
            int[] indices = new int[indexCount];

            int nWaypoints = waypoints.Count;
            for (int i = 0; i < nWaypoints; ++i)
            {
                var cur = new Vector3(waypoints[i].x - center.x, 0, waypoints[i].y - center.y);
                Vector3 perp = Vector3.zero;
                if (i - 1 >= 0)
                {
                    var last = new Vector3(waypoints[i - 1].x - center.x, 0, waypoints[i - 1].y - center.y);
                    var d = cur - last;
                    var p = new Vector3(d.z, 0, -d.x);
                    p.Normalize();
                    perp += p;
                }
                if (i + 1 < nWaypoints) {
                    var next = new Vector3(waypoints[i + 1].x - center.x, 0, waypoints[i + 1].y - center.y);
                    var d = (next - cur).normalized;
                    var p = new Vector3(d.z, 0, -d.x);
                    p.Normalize();
                    perp += p;
                }
                
                perp.Normalize();
                vertices[i * 2] = cur + perp * width * 0.5f;
                vertices[i * 2 + 1] = cur - perp * width * 0.5f;
            }

            for (int i = 0; i < nSegments; ++i)
            {
                var a = i * 2;
                var b = i * 2 + 1;
                var c = i * 2 + 2;
                var d = i * 2 + 3;

                indices[i * 6] = a;
                indices[i * 6 + 1] = b;
                indices[i * 6 + 2] = c;
                indices[i * 6 + 3] = d;
                indices[i * 6 + 4] = c;
                indices[i * 6 + 5] = b;
            }

            Mesh mesh = new Mesh();
            mesh.vertices = vertices;
            mesh.triangles = indices;
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();

            return mesh;
        }

        static GameObject mRootObject;
        static Mesh mUnitPathMesh;
        static Mesh mTargetPathMesh;
        static Material mMaterial;
    }
}


#endif