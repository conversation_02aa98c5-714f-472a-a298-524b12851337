﻿ 



 
 



/*
 * created by wzw at 2019.11.6
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //游戏运行时使用
    public sealed class TileGridObjectLayerView : MapLayerView
    {
        public TileGridObjectLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = layerData.map.view.reusableGameObjectPool;
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            foreach (var obj in mTileViews)
            {
                Utils.DestroyObject(obj.Value);
            }
            mTileViews = null;
        }

        public void OnObjectActiveStateChange(TileObjectData data, int lod)
        {
            if (data.IsObjActive())
            {
                ShowObject(data, lod);
            }
            else
            {
                HideObject(data, lod);
            }
        }

        public void OnObjectScaleChange(TileObjectData data)
        {
            GameObject obj;
            if( mTileViews.TryGetValue(data.GetEntityID(), out obj))
                obj.transform.localScale = data.GetScale();
        }

        //显示地图对象的模型
        void ShowObject(TileObjectData data, int lod)
        {
            string prefabPath = data.GetAssetPath();
            //Debug.Log($"Font资源{prefabPath} Log：{lod}");

            if (lod == 3 && prefabPath.Contains("tree"))
                return; //3级下 树 不显示

            if (mObjectPool.Exit(prefabPath) && lod<4)
            {
                var obj = mObjectPool.Require(prefabPath);
                mTileViews[data.GetEntityID()] = obj;

                obj.SetActive(true);
                var transform = obj.transform;
                transform.localPosition = data.GetPosition();
                transform.localRotation = (data.objectType == TileObjectType.NoneScaleDecorationObject || data.objectType == TileObjectType.ScaleDecorationObject) && MapObjectUtils.NeedRotation ? data.GetRotation() * Quaternion.Euler(0, 180f, 0) : data.GetRotation();
                transform.localScale = data.GetScale();
                transform.SetParent(root.transform);
            }
        }

        //隐藏地图对象的模型
        void HideObject(TileObjectData data, int lod)
        {
            var id = data.GetEntityID();
            GameObject obj;
            mTileViews.TryGetValue(id, out obj);
            mTileViews.Remove(id);
            var layerData = mLayerData as TileGridObjectLayerData;
            string prefabPath = data.GetAssetPath();
            mObjectPool.Release(prefabPath, obj, layerData.map);
        }

        public void ShowObstacles(List<ObstacleDisplayInfo> bigTiles)
        {
            HideObstacles();
            for (int i = 0; i < bigTiles.Count; ++i)
            {
                ShowObstacle(bigTiles[i].tileData.id, bigTiles[i].position);
            }
        }

        public void HideObstacles()
        {
            foreach (var p in mObstacles)
            {
                ReleaseObstacleView(p.Key, p.Value);
            }
            mObstacles.Clear();
        }

        void ShowObstacle(int tileID, Vector3 pos)
        {
            if (mObstacles.Count == 0)
            {
                return;
            }
            if (!mObstacles.ContainsKey(tileID))
            {
                var obstacleObject = RequireObstacleView(tileID, pos);
                mObstacles[tileID] = obstacleObject;
            }
        }

        GameObject RequireObstacleView(int tileID, Vector3 position)
        {
            var map = layerData.map;
            var obstacleObject = map.data.localObstacleManager.RequireObstacleView(tileID);
            if (obstacleObject != null)
            {
                var offsetX = layerData.tileWidth * 0.5f;
                var offsetZ = layerData.tileHeight * 0.5f;
                obstacleObject.transform.position = position - new Vector3(offsetX, 0, offsetZ);
                obstacleObject.SetActive(true);
            }
            else
            {
                Debug.LogError("No obstacles are created! Please create obstacles in editor first!");
            }
            return obstacleObject;
        }

        void ReleaseObstacleView(int tileID, GameObject obj)
        {
            var map = layerData.map;
            map.data.localObstacleManager.ReleaseObstacleView(tileID, obj);
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        //该层中所有地图对象的视图
        Dictionary<int, GameObject> mTileViews = new Dictionary<int, GameObject>();
        //显示的障碍物
        Dictionary<int, GameObject> mObstacles = new Dictionary<int, GameObject>();
        GameObjectPool mObjectPool;
    };
}