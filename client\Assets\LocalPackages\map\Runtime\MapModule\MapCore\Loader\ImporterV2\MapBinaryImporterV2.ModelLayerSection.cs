﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.ModelLayerData LoadModelLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.ModelLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadModelDataV1(reader);
            }

            var config = LoadModelLayerLODConfigV1(reader);
            //-------------------version 1 end------------------------------
            //-------------------version 2 start-----------------------
            if (version >= 2)
            {
                LoadModelLayerLODConfigV2(reader, config);
            }
            //-------------------version 2 end-----------------------

            var layer = new config.ModelLayerData(layerID, name, offset, config, null, width, height, objects);
            return layer;
        }

        config.ModelData LoadModelDataV1(BinaryReader reader)
        {
            var modelData = new config.ModelData();
            bool isDefaultRotation;
            bool isDefaultScale;

            modelData.SetID(reader.ReadInt32());

            modelData.position = Utils.ReadVector3(reader);
            isDefaultRotation = reader.ReadBoolean();
            if (!isDefaultRotation)
            {
                modelData.rotation = Utils.ReadQuaternion(reader);
            }
            isDefaultScale = reader.ReadBoolean();
            if (!isDefaultScale)
            {
                modelData.scale = Utils.ReadVector3(reader);
            }
            modelData.modelTemplateID = reader.ReadInt32();
            return modelData;
        }

        config.MapLayerLODConfig LoadModelLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }

        void LoadModelLayerLODConfigV2(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].useRenderTexture = reader.ReadBoolean();
            }
        }
    }
}
