﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionTilingSplitFogLayer : EditorAction
    {
        //从prefabPaths中选取tile
        public ActionTilingSplitFogLayer(int layerID, int prefabGroupIndex, List<Vector2Int> tiles, int tileIndex)
        {
            mPrefabGroupIndex = prefabGroupIndex;
            mLayerID = layerID;
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as SplitFogLayer;
            int cols = layer.horizontalTileCount;
            int rows = layer.verticalTileCount;

            mTileIndices = new List<Vector2Int>();
            mTileIndices.AddRange(tiles);
            mOldTiles = new Dictionary<Vector2Int, int>();
            mNewTiles = new Dictionary<Vector2Int, int>();
            for (int k = 0; k < tiles.Count; ++k)
            {
                var tile = layer.GetTile(tiles[k].x, tiles[k].y);
                if (tile != 0)
                {
                    mOldTiles[tiles[k]] = tile;
                }

                mNewTiles[tiles[k]] = tileIndex;
            }

            mDescription = string.Format("tiling split fog layer {0}", layer.name);
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as SplitFogLayer;
            if (layer != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorSplitFogPrefabManager;
                var group = prefabManager.GetGroupByIndex(mPrefabGroupIndex);
                if (group != null)
                {
                    for (int i = 0; i < mTileIndices.Count; ++i)
                    {
                        int newTileData;
                        mNewTiles.TryGetValue(mTileIndices[i], out newTileData);
                        layer.SetTile(mTileIndices[i].x, mTileIndices[i].y, newTileData);
                    }

                    return true;
                }
            }
            return false;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as SplitFogLayer;
            if (layer != null)
            {
                int horizontalTileCount = layer.horizontalTileCount;
                int verticalTileCount = layer.verticalTileCount;

                for (int i = 0; i < mTileIndices.Count; ++i)
                {
                    int x = mTileIndices[i].x;
                    int y = mTileIndices[i].y;
                    int oldTileData;
                    bool found = mOldTiles.TryGetValue(mTileIndices[i], out oldTileData);
                    if (!found)
                    {
                        layer.SetTile(x, y, 0);
                    }
                    else
                    {
                        layer.SetTile(x, y, oldTileData);
                    }
                }

                return true;
            }
            return false;
        }

        int mPrefabGroupIndex;
        int mLayerID;
        string mDescription;
        List<Vector2Int> mTileIndices;
        Dictionary<Vector2Int, int> mOldTiles;
        Dictionary<Vector2Int, int> mNewTiles;
    }
}

#endif