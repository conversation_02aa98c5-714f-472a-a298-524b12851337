﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class RadialFadingTransition : IGroundLayerLODSwitchTransitionHandler
    {
        //prefab:地表切换成哪个模型
        //risingZoom:相机上升后到哪个高度,这个zoom对应
        //paperLODCameraSettingName: 切换到最高lod后使用哪个camera setting
        public RadialFadingTransition(GameObject prefab, float risingCameraHeight, float fallingCameraHeight, string paperLODCameraSettingName, bool keepOriginalDurationWhenBreakMoving, System.Action<bool> onStartTransitionCallback, System.Action<bool> onFinishTransitionCallback, System.Action<bool> onPreStartTransitionCallback)
        {
            mOnStartTransitionCallback = onStartTransitionCallback;
            mOnFinishTransitionCallback = onFinishTransitionCallback;
            mOnPreStartTransitionCallback = onPreStartTransitionCallback;
            mKeepOriginalDurationWhenBreakMoving = keepOriginalDurationWhenBreakMoving;
            mPrefab = GameObject.Instantiate<GameObject>(prefab);
            mImpl = MapCameraMgr.MapCamera.GetComponent<RadialFadingTransitionImpl>();
            Debug.Assert(mImpl != null, "RadialFadingTransitionImpl script not attached to map camera!");
            mImpl.OnChangeTexture += OnChangeTexture;
            mImpl.OnFinish += OnFinishTransitionImpl;
            mRisingCameraHeight = risingCameraHeight;
            mFallingCameraHeight = fallingCameraHeight;
            mPrefab.SetActive(false);
            mPaperLODCameraSettingName = paperLODCameraSettingName;
            mOriginalCameraSettingName = MapCameraMgr.cameraSetting.name;
        }

        public void OnDestroy()
        {
            Utils.DestroyObject(mPrefab);
        }

        public void OnStartTransition(int lastLOD, int curLOD, float forceToCameraHeight, Vector3 newViewCenter)
        {
#if UNITY_EDITOR
            //Debug.LogError("OnStartTransition");
#endif
            if (mOnPreStartTransitionCallback != null)
            {
                mOnPreStartTransitionCallback(curLOD > lastLOD);
            }

            MapCameraMgr.EnableCameraDrag(false);
            MapCameraMgr.EnableCameraZoom(false);
            mStartLOD = lastLOD;
            mEndLOD = curLOD;
            mImpl.BlitTwoScreenshot();
            mTransitionFinished = false;
            if (curLOD > lastLOD)
            {
                if (!string.IsNullOrEmpty(mPaperLODCameraSettingName))
                {
                    MapCameraMgr.SetActiveCameraSetting(mPaperLODCameraSettingName);
                }
                MapCameraMgr.MoveCameraToTargetInstantly(Map.currentMap.viewCenter + new Vector3(0, forceToCameraHeight > 0 ? forceToCameraHeight : mRisingCameraHeight, 0), null);
            }
            else
            {
                bool isMovingToTarget = MapCameraMgr.isMovingToTarget;
                if (isMovingToTarget)
                {
                    MapCameraMgr.SaveMoveToTargetState(mKeepOriginalDurationWhenBreakMoving);
                }
                bool angleChanged = false;
                if (!string.IsNullOrEmpty(mPaperLODCameraSettingName))
                {
                    var oldSetting = MapCameraMgr.cameraSetting;
                    MapCameraMgr.SetActiveCameraSetting(mOriginalCameraSettingName);
                    var newSetting = MapCameraMgr.cameraSetting;
                    if (!Mathf.Approximately(oldSetting.rotationXAngle, newSetting.rotationXAngle))
                    {
                        angleChanged = true;
                    }
                }
                //restore
                var cameraRoot = MapCameraMgr.MapCameraRoot;
                var currentCameraPos = cameraRoot.transform.position;
                //如果现在相机的位置已经低于falling height,就不要移动相机了
                float fallingCameraHeight = forceToCameraHeight > 0 ? forceToCameraHeight : mFallingCameraHeight;
                if (currentCameraPos.y > fallingCameraHeight)
                {
                    var dir = cameraRoot.transform.forward;
                    double xRot = cameraRoot.transform.eulerAngles.x;
                    float distance = (float)(fallingCameraHeight / System.Math.Sin(xRot * 0.0174532924));
                    var pos = newViewCenter - dir * distance;
                    if (isMovingToTarget)
                    {
                        MapCameraMgr.MoveCameraToTargetInstantly(pos, () => { MapCameraMgr.RestoreMoveToTargetState(angleChanged); });
                    }
                    else
                    {
                        MapCameraMgr.MoveCameraToTargetInstantly(pos, null);
                    }
                }
            }
            MapCameraMgr.EnableCameraDrag(false);
            MapCameraMgr.EnableCameraZoom(false);

            if (mOnStartTransitionCallback != null)
            {
                mOnStartTransitionCallback(curLOD > lastLOD);
            }
        }

        public void OnFinishTransition(int lastLOD, int curLOD, float forceToCameraHeight)
        {
#if UNITY_EDITOR
            //Debug.LogError("OnFinishTransition");
#endif
            MapCameraMgr.EnableCameraDrag(true);
            MapCameraMgr.EnableCameraZoom(true);
            if (curLOD < lastLOD)
            {
                mPrefab.SetActive(false);
            }
            MapCameraMgr.EnableCameraDrag(true);
            MapCameraMgr.EnableCameraZoom(true);

            if (mOnFinishTransitionCallback != null)
            {
                mOnFinishTransitionCallback(curLOD > lastLOD);
            }
        }

        public bool UpdateTransition(int lastLOD, int curLOD, float percentage)
        {
            return mTransitionFinished;
        }

        public float GetTransitionDuration()
        {
            return 0;
        }

        void OnChangeTexture()
        {
            if (mStartLOD < mEndLOD)
            {
                mPrefab.SetActive(true);
            }
            else
            {
                mPrefab.SetActive(false);
            }
        }

        void OnFinishTransitionImpl()
        {
            mTransitionFinished = true;
        }

        bool mTransitionFinished = false;
        bool mKeepOriginalDurationWhenBreakMoving;
        GameObject mPrefab;
        RadialFadingTransitionImpl mImpl;
        int mStartLOD;
        int mEndLOD;
        float mRisingCameraHeight;
        float mFallingCameraHeight;
        string mPaperLODCameraSettingName;
        string mOriginalCameraSettingName;
        System.Action<bool> mOnFinishTransitionCallback;
        System.Action<bool> mOnStartTransitionCallback;
        System.Action<bool> mOnPreStartTransitionCallback;
    }
}
