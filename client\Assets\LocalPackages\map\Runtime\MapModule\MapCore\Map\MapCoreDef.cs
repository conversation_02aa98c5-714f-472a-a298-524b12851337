﻿ 



 
 


using UnityEngine;
using System.Text.RegularExpressions;
using System.IO;

namespace TFW.Map
{
    public static class MapCoreDef
    {
        //don't delete this
        static MapCoreDef() { }

        public const int MAP_DECORATION_LAYER_CULL_GRID_SIZE = 100;
        public const float BlendTerrainLayerTileSizeThreshold = 60;

        // game object name
        public const string MAP_ROOT_NAME = "MapRoot";
        public const string MAP_CAMERA_NAME = "MapCamera";
        public const string MAP_LAYER_NAME = "MapLayer";
        public const string MAP_OLD_VERSION_MAIN_CAMERA_NAME = "MainCamera";
        public const string MAP_NEW_VERSION_CAMERA_CONTAINER_NAME = "MapCameraContainer";

        public const string BAKED_ANIM_DATA_NAME = "animData";
        public const string BAKED_CPU_DRIVEN_BONE_TRANSFORM_DATA_NAME = "cpuDrivenAnimData";
        public const string BAKED_ANIM_TEXTURE_NAME = "animTexture";
        public const string BAKED_ANIMATION_TEXTURE_PROPERTY_NAME = "_AnimationData";
        public const string BAKED_ANIMATION_TEXTURE_SIZE_PROPERTY_NAME = "_AnimTextureSize";
        //skeleton animation _AnimationParams = new Vector4(nextStateFrame, curAnimOffset, curStateFrame, nextAnimOffset);
        //vertex animation _AnimationParams = new Vector4();
        public const string BAKED_ANIMATION_PARAMETERS_PROPERTY_NAME = "_AnimationParams";
        public const string BAKED_ANIMATION_BLEND_PARAMETERS_PROPERTY_NAME = "_BlendParams";

        // 资源地址
        public static readonly string MAP_BIG_TILE_DATA_FOLDER_NAME = "BigTiles";
        public static readonly string MAP_DECORATION_LAYER_BIG_TILE_DATA_FOLDER_NAME = "BorderBigTiles";
        public static readonly string MAP_TERRAIN_MESH_FOLDER_NAME = "TerrainMesh";
        //public static readonly string MAP_LIST_FILE_PATH = "maplist.bytes";
        public static readonly string MAP_LIST_FILE_PATH = "maplist1.bytes";
        public static readonly string MAP_DATA_FILE_NAME = "mapData1.bytes";
        public static readonly string MAP_GENERATED_180M_PREFABS_SUBFOLDER = "/GeneratedPrefabs";
        public static readonly string MAP_GENERATED_BLOCK_PREFABS_SUBFOLDER = "/GeneratedBlockPrefabs";
        public static readonly string MAP_GENERATED_180M_TERRAIN_PREFABS_SUBFOLDER = "/TerrainGeneratedPrefabs";
        public static readonly string MAP_GENERATED_SPLINE_ASSETS_FOLDER_NAME = "GeneratedSplines";
        public static readonly string MAP_EXPORTED_SPLINE_DATA_NAME = "Splines.bytes";
        //编辑器资源地址,这个目录下的资源不会上真机包
        public const string MAP_EDITOR_RES_PATH = "Assets/Framework/Script/Map/MapModuleRes/EditorRes";
        public static readonly string MAP_NAV_MESH_DATA_FILE_NAME = "NavMesh1.bytes";
        public static readonly string MAP_DETAIL_OBJECT_SPAWN_POINT_FILE_NAME = "DetailSpawnPoints1.bytes";
        public static readonly string MAP_DETAIL_OBJECT_SPRITES_FILE_NAME = "DetailSprites1.bytes";
        public static readonly string MAP_GRID_MODEL_LAYER2_HEADER_FILE_NAME = "GridModelLayer2Header.bytes";
        public static readonly string MAP_DECORATION_BORDER_LAYER_HEADER_FILE_NAME = "DecorationBorderLayerHeader.bytes";
        public static readonly string MAP_GRID_MODEL_LAYER2_REMOVABLE_OBJECTS_FILE_NAME = "RemovableObjects.bytes";

        public static readonly string MAP_CAMERA_LOOK_AT_AREA_FILE = "CameraLookAtArea.bytes";
        public static readonly string MAP_GROUND_TILE_ATLAS_INFO_FILE_NAME = "GroundTileAtlasInfo.bytes";
        public static readonly string MAP_OPTIMIZED_FRONT_TILE_DATA_FOLDER_NAME = "OptimizedTiles";
        public static readonly string MAP_OPTIMIZED_FRONT_TILE_DATA_FILE_PATH_NAME = MAP_OPTIMIZED_FRONT_TILE_DATA_FOLDER_NAME + "/OptimizedTileData.bytes";

        public static readonly string MAP_RIVER_ASSETS_PATH_NAME = "RiverAssets";
        public static readonly string SPLIT_FOG_MASK_TEXTURE_NAME = "_FogMaskTex";
        public static readonly string TERRITORY_LAYER_RUNTIME_ASSETS_FOLDER_NAME = "_TerritoryAssets";
        public static readonly string NEW_TERRITORY_LAYER_RUNTIME_ASSETS_FOLDER_NAME = "_TerritoryAssetsNew";
        public static readonly string REGION_LAYER_RUNTIME_ASSETS_FOLDER_NAME = "_RegionAssets";
        public const string DEFAULT_RIVER_MASK_TEXTURE_FOLDER = "Assets/TempRes/_DefaultRiverMaskFolder";
        //保存GroundTileMaker的数据
        public const string MAP_GROUND_TILE_ASSETS_FILE = "GroundTileData.bytes";
        public const string COMPLEX_GRID_MODEL_LAYER_EDITOR_DATA_FILE_NAME = "decorationLayer.config";
        public const string DECORATION_BORDER_LAYER_EDITOR_DATA_FILE_NAME = "decorationBorderLayer.config";
        public const string GRID_MODEL_LAYER_EDITOR_DATA_FILE_NAME = "frontLayer.config";
        public const string BLEND_TERRAIN_LAYER_EDITOR_DATA_FILE_NAME = "groundLayer.config";
        public const string VARYING_TILE_SIZE_TERRAIN_LAYER_EDITOR_DATA_FILE_NAME = "varyingTileSizeGroundLayer.config";
        public const string LOD_LAYER_EDITOR_DATA_FILE_NAME = "lodLayer.config";
        public const string ENTITY_SPAWN_LAYER_EDITOR_DATA_FILE_NAME = "entitySpawnLayer.config";
        public const string NAVMESH_LAYER_EDITOR_DATA_FILE_NAME = "navMeshLayer.config";
        public const string POLYGON_RIVER_LAYER_EDITOR_DATA_FILE_NAME = "polygonRiverLayer.config";
        public const string CIRCLE_BORDER_LAYER_EDITOR_DATA_FILE_NAME = "circleBorderLayer.config";
        public const string SPLIT_FOG_LAYER_EDITOR_DATA_FILE_NAME = "splitFogLayer.config";
        public const string EDITOR_TERRITORY_LAYER_EDITOR_DATA_FILE_NAME = "cityTerritoryLayer.config";
        public const string REGION_LAYER_EDITOR_DATA_FILE_NAME = "regionLayer.config";
        public const string REGION_COLOR_LAYER_EDITOR_DATA_FILE_NAME = "regionColorLayer.config";
        public const string BUILDING_GRID_LAYER_EDITOR_DATA_FILE_NAME = "buildingGridLayer.config";
        public const string RAILWAY_LAYER_EDITOR_DATA_FILE_NAME = "railwayLayer.config";
        public const string MAP_COLLISION_LAYER_EDITOR_DATA_FILE_NAME = "mapCollisionLayer.config";
        public const string MAP_COLLISION_LAYER_EDITOR_TEMP_DATA_FILE_NAME = "mapCollisionLayer_temp.config";
        public const string MAP_CAMERA_COLLIDER_LAYER_EDITOR_DATA_FILE_NAME = "cameraColliderLayer.config";
        public const string MAP_RUIN_LAYER_EDITOR_DATA_FILE_NAME = "ruinLayer.config";
        public const string MAP_RUIN_LAYER_HEADER_FILE_NAME = "ruinLayerHeader.config";
        public const string MAP_NPC_REGION_LAYER_EDITOR_DATA_FILE_NAME = "npcRegionLayer.config";
        public const string GRID_REGION_EDITOR_DATA_FILE_NAME = "gridRegionEditor.config";
        public const string DETAIL_SPRITES_EDITOR_DATA_FILE_NAME = "detailSprites.config";
        public const string SPLINE_OBJECTS_EDITOR_DATA_FILE_NAME = "splineObjects.config";
        public const string SPLINE_OBJECTS_EDITOR_TEMP_DATA_FILE_NAME = "splineObjects_temp.config";

        public const string RUNTIME_REGION_COLOR_LAYER_DATA = "regionColorLayerData.bytes";
        public const string BUILDING_GRID_LAYER_EXPORT_DATA_FILE_NAME = "building_grid.bytes";

        public const string GROUND_TILE_VARIATION_FILE_NAME = "variation.bytes";
        public const string RIVER_MASK_PREFIX = "_river_mask_";

        // 地图层
        public const string MAP_LAYER_NODE_GROUND = "LayerGround";
        public const string MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND = "LayerVaryingTileSizeGround";
        public const string MAP_LAYER_NODE_FRONT = "LayerFront";
        public const string MAP_LAYER_NODE_DYNAMIC = "LayerDynamicObject";
        public const string MAP_LAYER_NODE_DECORATION = "LayerDecoration";
        public const string MAP_LAYER_NODE_DECORATION_BORDER = "LayerDecorationBorder";
        public const string MAP_LAYER_NODE_CITY_TERRITORY = "LayerCityTerritory";

        //铁轨层
        public const string MAP_LAYER_NODE_RAILWAY = "LayerRailway";
        public const string MAP_LAYER_NODE_SPLINE = "LayerSpline";
        public const string MAP_LAYER_NODE_SPLIT_FOG = "LayerSplitFog";
        public const string MAP_LAYER_NODE_BUILDING_GRID = "LayerBuildingGrid";

        //lod层,用来发送lod切换事件
        public const string MAP_LAYER_NODE_LOD = "LayerLOD";
        //p2的地图边界层
        public const string MAP_LAYER_NODE_CIRCLE_BORDER = "LayerCircleBorder";
        //碰撞层
        public const string MAP_LAYER_NODE_COLLISION = "LayerCollision";
        public const string MAP_LAYER_NODE_REGION = "LayerRegion";
        public const string MAP_LAYER_NODE_REGION_COLOR = "LayerRegionColor";
        public const string MAP_LAYER_NODE_NAVMESH = "NavMesh_Layer";
        public const string MAP_LAYER_NODE_NPC_REGION = "NPC Region Layer";
        public const string MAP_LAYER_NODE_ENTITY_SPAWN = "Entity Spawn Layer";
        public const string MAP_LAYER_NODE_RUIN = "LayerRuin";
        public const string MAP_LAYER_NODE_POLYGON_RIVER = "LayerPolygonRiver";
        public const string MAP_LAYER_NODE_RUNTIME_RIVER = "LayerRiver";
        public const string MAP_LAYER_NODE_CAMERA_COLLIDER = "LayerCameraCollider";

        public const float MAP_NPC_MOVE_RANGE_MIN = 2.0f;
        public const float MAP_NPC_MOVE_RANGE_MAX = 8.0f;
        public const float MAP_NPC_DENSITY = 80.0f;
        public const int MAP_NPC_WAYPOINT_COUNT = 10;

        public const string ENABLE_SKIN_ANIM_BLENDING = "ENABLE_SKIN_ANIM_BLENDING";
        public const string USE_SLERP_BLENDING = "USE_SLERP_BLENDING";

        //实体动态id区间
        public const int MAP_ENTITY_START_ID = 10000000;
        public const int MAP_ENTITY_END_ID = 20000000;

        //最大的区域id,用在MapCollision设置区域id,这个值必须不大于ushort.MaxValue
        public const int MAP_MAX_REGION_TYPE_ID = 2000;

        public const string MAP_PREFAB_LOD_PREFIX = "_lod";
        public const string MAP_DECORATION_OBJECT_TYPE_POSTFIX = "_t";

        //前景物体tag
        public const string MAP_DECORATION_OBJECT = "decoration";
        public const string MAP_DECORATION_SCALE_OBJECT = "decoration_scale";
        //立即切换lod,避免闪烁
        public const string MAP_BIG_OBJECT = "big_object";
        public const string MAP_BIG_OBJECT_ASYNC_LOADING = "big_object_async_loading";
        public const string MAP_ALWAYS_VISIBLE_OBJECT = "always_visible";
        public const string MAP_REMOVABLE_OBJECT = "removable";
        //生成coastline outline时忽略带这个tag的物体
        public const string MAP_IGNORE_MESH_TAG = "ignore_mesh";
        public const string MAP_SPECIAL_AREA_TAG = "special_area";

        public const string MAP_TERRAIN_TILE_QUEUE = "terrain tile queue";
        public const string MAP_FRONT_TILE_QUEUE = "front tile queue";
        public const string MAP_FRONT_TILE_OVERLAPE_CHECK_QUEUE = "front tile overlap check queue";
        public const string MAP_UPDATE_TILE_OBJECT_QUEUE = "update tile object queue";
        public const string MAP_OBJECT_LAYER_QUEUE = "map object layer queue";

        public const string WALL_COLLIDER_TAG = "wall_collider";

        public const string CAMERA_COLLIDER_LAYER = "camera_collider";
        public const string CAMERA_COLLIDER_TAG = "camera_collider";
        public const string IGNORED_OBJECT_TAG = "ignore";

        public const string DUMMY_OBJECT_NAME = "_Dummy";
        public const string SPHERE_OBJECT_NAME = "_Sphere";
        public const string COLLISION_MODEL_NAME = "Collision Model";
        public const string REGION_MODEL_NAME = "Region Model";
        public const string CAMERA_COLLIDER_MODEL_NAME = "Camera Collider";
        public const string POLYGON_RIVER_MODEL_NAME = "Polygon River";
        public const string RIVER_TEXTURE_ALPHA_PROPERTY_NAME = "_RiverMask";
        public const string RIVER_RENDER_TEXTURE_ALPHA_PROPERTY_NAME = "_Alpha";
        public const string RIVER_BLEND_SRC_FACTOR_PROPERTY_NAME = "_SrcFactor";
        public const string RIVER_BLEND_DST_FACTOR_PROPERTY_NAME = "_DstFactor";
        public const string VIEWPORT_OBJECT_NAME = "Viewport";
        public const string DECORATION_LAYER_VIEWPORT_NAME = "Decoration_Viewport";
        public const string GRID_REGION_EDITOR_NAME = "Grid Region Editor";

        public const string FAKE_PREFAB_EXT = "bytes";
        public const string RIVER_PREFAB_MANIFEST_FILE_NAME = "river_prefabs_manifest.bytes";

        public const string BLEND_TERRAIN_LAYER_RENDER_TEXTURE_FOLDER_NAME = "TerrainRenderTextures";
        public const string BLEND_TERRAIN_LAYER_RENDER_TEXTURE_DATA_NAME = "TerrainRenderTexturesData.bytes";

        //--------------------map config names begin----------------------------------
        public const string MAP_DIR_NAME = "map_dir";
        public const string MAP_CONFIG_DIR_NAME = "config_dir";
        public const string MAP_INTUITIVE_ZOOM_FILE_NAME = "intuitive_zoom_file";
        public const string MAP_NAVIGATION_MODE_NAME = "navigation_mode";
        public const string MAP_USE_15_GROUND_PREFAB_NAME = "use_15_ground_prefab";
        public const string MAP_USE_PREFAB_OUTLINE_NAME = "use_prefab_outline";
        public const string MAP_FRONT_TILE_SIZE_NAME = "front_tile_size";
        public const string MAP_GROUND_TILE_SIZE_NAME = "ground_tile_size";
        public const string MAP_NPC_REGION_SIZE_NAME = "npc_region_size";
        public const string MAP_SPLIT_FOG_TILE_SIZE_NAME = "split_fog_tile_size";
        public const string MAP_DISPLAY_RAILWAY_LAYER_NAME = "display_railway_layer";
        public const string MAP_DISPLAY_CIRCLE_BORDER_LAYER_NAME = "display_circle_border_layer";
        public const string MAP_CAMERA_CLAMP_BORDER_NAME = "camera_clamp_border";
        public const string MAP_CAMERA_CHECK_CIRCLE_BORDER_COLLISION_NAME = "camera_check_circle_border_collision";
        public const string MAP_CAMERA_BORDER_COLLIDER_RADIUS_NAME = "camera_border_collider_radius";
        public const string MAP_SHOW_HORDE_NAME = "show_horde";
        public const string MAP_USE_NEW_CAMERA_HEIGHT_AUTO_UPDATE_ALGORITHM = "use_new_camera_height_auto_update_algorithm";
        public const string MAP_OBSTACLE_GRID_SIZE_NAME = "obstacle_grid_size";
        public const string MAP_GLOBAL_OBSTACLE_MODE_NAME = "global_obstacle_mode";
        public const string MAP_SHOW_DEMO_FUNCTION_NAME = "show_demo_function";
        public const string MAP_ENABLE_CAMERA_COLLISION_NAME = "enable_camera_collision";
        public const string MAP_DONT_EXPORT_FRONT_LAYER_MODEL_TEMPLATES_NAME = "do_not_export_front_layer_model_templates";
        public const string MAP_GROUND_LOD_BAKING_PARAMETER_NAME = "ground_lod_baking_parameter";
        public const string MAP_GROUND_LOD_BAKING_CAMERA_HEIGHT_NAME = "camera_height";
        public const string MAP_GROUND_LOD_BAKING_TILE_COUNT_NAME = "tile_count";
        public const string MAP_NAVIGATION_MODE_LAND_NAME = "land";
        public const string MAP_NAVIGATION_MODE_ISLAND_OCEAN_NAME = "island_ocean";
        public const string MAP_NAVIGATION_MODE_ISLAND_NAME = "island";
        public const string MAP_NAVIGATION_MODE_GATE_NAME = "gate";
        public const string MAP_GROUND_BAKING_SHADER_NAME = "ground_baking_shader";
        public const string MAP_GROUND_BAKING_SHADER_TEXTURE_PROPERTY_NAME = "ground_baking_shader_texture_property_name";
        public const string MAP_OBSTACLE_MATERIAL_NAME = "obstacle_material";
        public const string MAP_BRUSH_FOLDER_NAME = "brush_folder";
        public const string MAP_USE_FAKE_PREFAB_NAME = "use_fake_prefab";
        public const string MAP_INTUITIVE_ZOOM_UNIT_NAME = "intuitive_zoom_unit_name";
        public const string MAP_GROUND_TILE_MAKER_DEFAULT_RUNTIME_ASSET_FOLDER_NAME = "ground_tile_maker_default_runtime_asset_folder";
        public const string MAP_GROUND_TILE_MAKER_DEFAULT_EDITOR_ASSET_FOLDER = "ground_tile_maker_default_editor_asset_folder";
        public const string MAP_GROUND_TILE_MAKER_DEFAULT_TILE_SIZE = "ground_tile_maker_default_tile_size";
        public const string MAP_GROUND_TILE_MAKER_DEFAULT_MASK_TEXTURE_SIZE = "ground_tile_maker_default_mask_texture_size";
        public const string MAP_GROUND_TILE_MAKER_DEFAULT_MASK_TEXTURE_NAME = "ground_tile_maker_default_mask_texture_name";
        public const string MAP_GROUND_TILE_MAKER_DEFAULT_INIT_CHANNEL_DATA = "ground_tile_maker_default_init_channel_data";
        public const string MAP_GROUND_TILE_MAKER_DEFAULT_NORMALIZE_COLOR = "ground_tile_maker_default_normalize_color";
        public const string MAP_GROUND_TILE_MAKER_DEFAULT_MATERIAL_PATH = "ground_tile_maker_default_material_path";
        public const string MAP_PLUGIN_LIST_NAME = "plugin_list";
        public const string MAP_USE_NEW_CONFIG_FORMAT = "use_new_config_format";
        public const string MAP_HIDE_WALL_WHEN_MAIN_BUILDING_AT_FINAL_POSITION = "hide_wall_when_main_building_at_final_position";
        public const string MAP_GENERATE_RIVER_MATERIAL = "generate_river_material";
        public const string MAP_CAMERA_SETTINGS_NAME = "camera_settings";
        public const string MAP_FILE_FOLDER_NAME = "file_folder";
        public const string MAP_ORIGIN_NAME = "origin";
        public const string MAP_LOAD_AT_STARTUP_NAME = "load_at_startup";
        public const string MAP_DEFAULT_CAMERA_SETTING_NAME = "default_camera_setting";
        public const string MAP_FRAME_ACTION_MAX_EXECUTE_TIME = "frame_action_max_execute_time";
        public const string MAP_LIST_DIR_NAME = "map_list_dir";
        public const string MAP_BAKED_ANIM_DATA_PATH = "baked_anim_data_path";
        public const string MAP_GROUND_TILE_ATLAS_INFO_PATH = "ground_tile_atlas_dir";
        public const string MAP_DEFAULT_TERRITORY_MATERIAL_PATH = "default_territory_material_path";
        public const string MAP_ROTATED_UV_MESH_PATH = "rotated_uv_mesh_path";
        public const string MAP_SUPPORT_GROUND_HEIGHT_CHANGE = "support_ground_height_change";
        public const string MAP_USE_CAMERA_SHAKE_BY_DISTANCE = "use_camera_shake_by_distance";
        public const string MAP_ENABLE_RESIZE_MAP = "enable_resize_map";
        public const string MAP_ENABLE_UPDATE_CLIP_PLANE = "enable_update_clip_plane";
        public const string MAP_ENABLE_UPDATE_NEAR_CLIP_PLANE = "enable_update_near_clip_plane";
        public const string MAP_CAMERA_ZOOM_CURVE_SETTING_PATH = "camera_zoom_curve_setting_path";
        public const string MAP_CAMERA_NEAR_CLIP_PLANE_SETTING_PATH = "map_camera_near_clip_plane_setting_path";
        public const string MAP_CAMERA_FAR_CLIP_PLANE_SETTING_PATH = "map_camera_far_clip_plane_setting_path";
        public const string MAP_GLOBAL_SETTING_PATH_NAME = "map_global_setting";
        public const string MAP_SETTING_IS_MAIN_MAP = "is_main_map";
        public const string MAP_IS_2D_GAME = "2d_game";
        public const string MAP_PROJECT_NAME = "project";
        public const string MAP_USE_MAP_LARGE_TILE_FOR_DECORATION_LAYER = "use_one_big_tile_for_decoration_layer";
        public const string MAP_WRONG_GROUND_LOD_ORIENTATION = "wrong_ground_lod_orientation";
        public const string MAP_PRELOAD_GROUND_LAYER_TILE = "preload_ground_layer_tile";

        /// <summary>
        /// 地图大小设置
        /// </summary>
        public const string MAP_SIZE = "map_size";
        /// <summary>
        /// 地图中心点
        /// </summary>
        public const string MAP_CENTER_POS = "map_center_pos";

        //--------------------map config names end----------------------------------
        public const string MAP_PREFAB_INDICATOR_NAME = "__PrefabIndicator";
        public const string OBJECT_PLACEMENT_EDITOR_FILE_NAME = "object_placement_setting.bytes";
        public const string MAP_PLUGIN_LAYERS = "layers";
        public const string MAP_PLUGIN_LAYER_NAMESPACE = "namespace";
        public const string MAP_PLUGIN_LAYER_ASSEMBLY = "assembly";
        public const string MAP_PLUGIN_LAYER_NAME = "name";
        public const string MAP_PLUGIN_RUNTIME_LAYER_NAME = "runtime name";
        public const string MAP_PLUGIN_PROXY_LAYER_NAME = "proxy name";
        public const string MAP_PLUGIN_LAYER_TITLE = "title";
        public const string MAP_PLUGIN_LAYER_TOOLTIP = "tooltip";
        public const string MAP_PLUGIN_LAYER_EDITOR_SAVE_FILE_NAME = "editor save file name";
        public const string MAP_PLUGIN_LAYER_RUNTIME_SAVE_FILE_NAME = "runtime save file name";

        public static string GetBigTileDataPath(string mapDataFolder, int tileX, int tileY)
        {
            return $"{GetFullBigTileDataFolderPath(mapDataFolder)}/tile_{tileX}_{tileY}.bytes";
        }

        public static string GetMapBigTileDataPath(string mapDataFolder)
        {
            return $"{GetFullBigTileDataFolderPath(mapDataFolder)}/all_tiles.bytes";
        }

        public static string GetMapBorderBigTileDataPath(string mapDataFolder)
        {
            return $"{GetFullBorderBigTileDataFolderPath(mapDataFolder)}/all_tiles.bytes";
        }

        public static TileObjectType GetTileObjectType(string tag)
        {
            if (tag == MAP_DECORATION_OBJECT)
            {
                return TileObjectType.NoneScaleDecorationObject;
            }
            else if (tag == MAP_DECORATION_SCALE_OBJECT)
            {
                return TileObjectType.ScaleDecorationObject;
            }
            else if (tag == MAP_BIG_OBJECT)
            {
                return TileObjectType.BigObject;
            }
            else if (tag == MAP_BIG_OBJECT_ASYNC_LOADING)
            {
                return TileObjectType.BigObjectAsync;
            }
            else if (tag == MAP_ALWAYS_VISIBLE_OBJECT)
            {
                return TileObjectType.AlwaysVisible;
            }
            else if (tag == MAP_REMOVABLE_OBJECT)
            {
                return TileObjectType.Removable;
            }
            else if (tag == MAP_SPECIAL_AREA_TAG)
            {
                return TileObjectType.SpecialArea;
            }
            return TileObjectType.StaticObject;
        }

        public static bool IsRemovableObject(TileObjectType type)
        {
            return type == TileObjectType.Removable || type == TileObjectType.SpecialArea;
        }

        public static bool IsInstantSwitchObjectType(TileObjectType type)
        {
            return type == TileObjectType.BigObject ||
                type == TileObjectType.SpecialArea;
        }

        public static bool IsBigObject(TileObjectType type)
        {
            return type == TileObjectType.BigObject ||
                type == TileObjectType.BigObjectAsync || 
                type == TileObjectType.SpecialArea;
        }

        public static string GetGroundTileVariationName(string fullName)
        {
            int endIndex = fullName.IndexOf(MAP_PREFAB_LOD_PREFIX);
            if (endIndex == -1)
            {
                return "";
            }
            int startIndex = -1;
            for (int k = endIndex - 1; k >= 0; --k)
            {
                if (fullName[k] == '_')
                {
                    startIndex = k + 1;
                    break;
                }
            }

            bool startWithNumber = fullName[startIndex] >= '0' && fullName[startIndex] <= '9';
            if (startIndex >= 0 && !startWithNumber)
            {
                return fullName.Substring(startIndex, endIndex - startIndex);
            }
            return "";
        }

        public static bool IsModelTemplateGenerated(ModelTemplate modelTemplate)
        {
            var path = modelTemplate.GetLODPrefabPath(0);
            return IsModelTemplateGenerated(path);
        }

        public static bool IsModelTemplateGenerated(string prefabPath)
        {
            return prefabPath.IndexOf(MAP_GENERATED_180M_PREFABS_SUBFOLDER) >= 0 ||
                prefabPath.IndexOf(MAP_GENERATED_180M_TERRAIN_PREFABS_SUBFOLDER) >= 0;
        }

        public static int GetNumberFromPath(string prefabName)
        {
            int number = 0;
            var rx = new Regex(@"\w+_(\d+)_lod", RegexOptions.Compiled);
            var match = rx.Match(prefabName);
            if (match.Success)
            {
                int.TryParse(match.Groups[1].Value, out number);
            }
            
            return number;
        }

        public static string GetOriginalPrefabNameFromGeneratedPrefabName(string generatedPrefabName)
        {
            string name = null;

            var rx = new Regex(@"new_(\w+)_autogen_\d+_lod", RegexOptions.Compiled);
            var match = rx.Match(generatedPrefabName);
            if (match.Success)
            {
                name = match.Groups[1].Value;
            }
            else
            {
                rx = new Regex(@"new_(\w+)_\d+_lod", RegexOptions.Compiled);
                match = rx.Match(generatedPrefabName);
                if (match.Success)
                {
                    name = match.Groups[1].Value;
                }
            }
            return name;
        }
        

        public static string GetTerrainMeshPath(string mapDataFolder, int x, int y)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{GetFullTerrainMeshFolderPath(mapDataFolder)}/terrain_Mesh_{x}_{y}.asset";
        }

        public static string GetFullMapDataFilePath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return mapDataFolder + "/" + MapCoreDef.MAP_DATA_FILE_NAME;
        }

        public static string GetFullBigTileDataFolderPath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return mapDataFolder + "/" + MapCoreDef.MAP_BIG_TILE_DATA_FOLDER_NAME;
        }

        public static string GetFullBorderBigTileDataFolderPath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return mapDataFolder + "/" + MapCoreDef.MAP_DECORATION_LAYER_BIG_TILE_DATA_FOLDER_NAME;
        }

        public static string GetFullTerrainMeshFolderPath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return mapDataFolder + "/" + MapCoreDef.MAP_TERRAIN_MESH_FOLDER_NAME;
        }
        public static string GetFullExportedSplineDataFilePath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return mapDataFolder + "/" + MapCoreDef.MAP_EXPORTED_SPLINE_DATA_NAME;
        }

        public static string GetOptimizedFrontTileDataPath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return mapDataFolder + "/" + MapCoreDef.MAP_OPTIMIZED_FRONT_TILE_DATA_FILE_PATH_NAME;
        }

        public static string GetOptimizedFrontTileDataFolder(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return mapDataFolder + "/" + MapCoreDef.MAP_OPTIMIZED_FRONT_TILE_DATA_FOLDER_NAME;
        }

        public static string GetGridModelLayerHeaderFilePath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_BIG_TILE_DATA_FOLDER_NAME}/{MAP_GRID_MODEL_LAYER2_HEADER_FILE_NAME}";
        }

        public static string GetDecorationBorderLayerHeaderFilePath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_DECORATION_LAYER_BIG_TILE_DATA_FOLDER_NAME}/{MAP_DECORATION_BORDER_LAYER_HEADER_FILE_NAME}";
        }

        public static string GetRemovableObjectsFilePath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_BIG_TILE_DATA_FOLDER_NAME}/{MAP_GRID_MODEL_LAYER2_REMOVABLE_OBJECTS_FILE_NAME}";
        }

        public static bool IsNotCreateFolder(string mapDataFolder)
        {
            return Directory.Exists($"{mapDataFolder}/{MAP_BIG_TILE_DATA_FOLDER_NAME}");
        }
        public static string GetRemovableBorderObjectsFilePath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_DECORATION_LAYER_BIG_TILE_DATA_FOLDER_NAME}/{MAP_GRID_MODEL_LAYER2_REMOVABLE_OBJECTS_FILE_NAME}";
        }

        public static string GetDetailSpriteObjectPath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_DETAIL_OBJECT_SPRITES_FILE_NAME}";
        }

        public static string GetDetailSpriteSpawnPointFilePath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_DETAIL_OBJECT_SPAWN_POINT_FILE_NAME}";
        }

        public static string GetGeneratedSplineAssetFolderPath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_GENERATED_SPLINE_ASSETS_FOLDER_NAME}";
        }

        public static string GetNavMeshDataFilePath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_NAV_MESH_DATA_FILE_NAME}";
        }

        public static string GetRiverAssetsFolderPath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_RIVER_ASSETS_PATH_NAME}";
        }

        public static string GetCameraLookAtAreaDataPath(string mapDataFolder)
        {
            Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
            return $"{mapDataFolder}/{MAP_CAMERA_LOOK_AT_AREA_FILE}";
        }

        public static string GetRiverPrefabManifestFilePath(string folder)
        {
            Debug.Assert(!string.IsNullOrEmpty(folder));
            return $"{folder}/{RIVER_PREFAB_MANIFEST_FILE_NAME}";
        }

        public static NavigationCreateMode GetNavigationMode(string modeName)
        {
            if (modeName == MapCoreDef.MAP_NAVIGATION_MODE_LAND_NAME)
            {
                return NavigationCreateMode.CreateLandNavigationMesh;
            }
            else if (modeName == MapCoreDef.MAP_NAVIGATION_MODE_ISLAND_OCEAN_NAME)
            {
                return NavigationCreateMode.CreateIslandAndOceanNavigationMesh;
            }
            else if (modeName == MapCoreDef.MAP_NAVIGATION_MODE_ISLAND_NAME)
            {
                return NavigationCreateMode.CreateIslandNavigationMesh;
            }
            else if (modeName == MapCoreDef.MAP_NAVIGATION_MODE_GATE_NAME)
            {
                return NavigationCreateMode.CreateClosedAreaAndGateNavMesh;
            }

            Debug.Assert(false, $"unknown string {modeName}");
            return NavigationCreateMode.CreateLandNavigationMesh;
        }

        public static NavigationCreateMode GetObstacleMode(string modeName)
        {
            if (modeName == MapCoreDef.MAP_NAVIGATION_MODE_LAND_NAME)
            {
                return NavigationCreateMode.CreateLandObstacles;
            }
            else if (modeName == MapCoreDef.MAP_NAVIGATION_MODE_ISLAND_OCEAN_NAME)
            {
                return NavigationCreateMode.CreateIslandAndOceanObstacles;
            }

            Debug.Assert(false, $"unknown string {modeName}");
            return NavigationCreateMode.CreateLandObstacles;
        }
    }

    [System.Flags]
    public enum LayerTypeMask
    {
        kNoneLayer = 0,
        kAllLayers = -1,

        kModelLayer = 1,
        kGridModelLayer = 2,
        kRailwayLayer = 4,
        kCollisionLayer = 8,
        kComplexGridModelLayer = 16,
    }

    [System.Flags]
    public enum CollisionAttribute
    {
        IsObstacle = 1 << 0,
        //是否参与碰撞检测
        CollisionCheck = 1 << 1,
        //是否能放置装饰物
        CanPlaceDecorationObject = 1 << 2,
        //是否在该区域生成导航网格
        Walkable = 1 << 3,
        //是否影响生成ruin layer的随机点,注意!该区域还是会在生成导航网格和障碍物数据时被考虑
        CanPlaceRuinPoint = 1 << 4,
        //是否能放置NPC生成点
        CanPlaceNPCSpawnPoint = 1 << 5,
        //是否是关隘的占地区域
        IsGateArea = 1 << 6,
        //是否自动扩充
        IsAutoExpandingObstacle = 1 << 7,
        //特殊区域,例如只能在海岛上生成npc刷新点.这种区域带单独的id
        SpecialRegion = 1 << 8,
        //是否初始时可通过
        Disabled = 1 << 9,
        //当摆放decoration layer的物体时是否考虑这种类型collision的碰撞检测
        TestCollisionWhenPlaceDecorationObject = 1 << 10,
        //是否用来裁剪river layer物体
        RiverClipper = 1 << 11,
        //是否只是不能传送,如果勾上,会在该区域创建navmesh,同时也会创建障碍物
        CanNotTeleportOnly = 1 << 12,
        //限制相机視野中心只能在此范围内移动
        CameraLookAtArea = 1 << 13,
        //地图边界线,用于只在边界线内生成NPC刷新点的功能
        BorderLine = 1 << 14,
        //在使用Generate Random Points In Special Region功能时作为不能生成Ruin点的区域
        RuinPointClipper = 1 << 15,
        //从prefab outline转换而来
        IsConvertedFromPrefabOutline = 1 << 16,
        //从collision segment转换而来
        IsConvertedFromCollisionSegment = 1 << 17,
        //Front Layer object clipper,是否用来裁剪front layer的物体
        IsFrontLayerObjectClipper = 1 << 18,
        //是否是mesh outline
        IsMeshOutline = 1 << 19,
    }

    //使用MapCollision的方式
    public enum CheckMapCollisionOperation
    {
        //计算地图的空地面积
        kCalculateValidArea,
        //生成地图的npc刷新点
        kCreateNPCSpawnPoints,
        //生成地图的遗迹点
        kGenerateRuinPoints,
        //生成地图的导航网格
        kGenerateNavMesh,
        //生成地图的障碍物数据
        kGenerateMapObstacles,
        //生成海面的导航网格,海面是除了所有障碍物的区域
        kGenerateWaterNavMesh,
    }

    public enum NavigationCreateMode
    {
        //陆地类型的navmesh,除了障碍物整个地面都可以走
        CreateLandNavigationMesh,
        //海岛类型的navmesh,将navmesh分为两类,海岛区域和海洋区域,海洋区域初始化为不可行走.但是可以动态调整为可以行走.海岛区域和陆地类型navmesh一样
        CreateIslandAndOceanNavigationMesh,
        //海岛类型的navmesh,但是只创建海岛的navmesh,忽略海面
        CreateIslandNavigationMesh,
        //海岛和陆地都算障碍物
        CreateIslandAndOceanObstacles,
        //只包括陆地障碍物,海岛不算障碍物
        CreateLandObstacles,
        //创建关卡地图的导航网格,分为closedAreaDetector生成的区域和关卡通道的区域
        CreateClosedAreaAndGateNavMesh,
        //海岛类型的navmesh,忽略海面,带关卡
        CreateIslandClosedAreaNavigationMesh,
        FindPath,
    }
}