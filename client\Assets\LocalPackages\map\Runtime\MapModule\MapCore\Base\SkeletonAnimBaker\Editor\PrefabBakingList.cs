﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    //需要烘培动画的prefab的列表
    [CreateAssetMenu(fileName = "prefab_baking_list", menuName = "Assets/PrefabBakingList")]
    public class PrefabBakingList : ScriptableObject
    {
        public PrefabBakeSetting[] prefabs;
        public string outputFolder;
        public float sampledFPS;
        public bool useRGBA32Texture = true;
        public bool useSRPBatcher = false;
        public bool deleteGameObjectsOfNameStartWithBIP = false;
        public bool separateCPUDrivenBoneTransformData = false;
        public Shader defaultRigidShader;
        public Shader defaultSkinShader;
    }
}
