﻿using Logic;
using System;
using DeepUI;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.UI;
using EventTriggerType = TFW.EventTriggerType;
using Cysharp.Threading.Tasks;

namespace UI.Alliance
{
    public class AllianceTemplateItemData 
    {
        public int disPlayKey;
        public int unionClassCfgID;
        public int classIndex;
        public int onlineCount;
        public TreeViewItemCountData countData;
        public bool isSelfAlliance;
        public AllianceClassEnum allianceClass;
        public Action<int, int> OnExpandClickedAction;
    }

    public class AllianceTemplateItem : UIWidgetBase
    {
        TFWImage classIcon;
        TFWText classIconText;
        TFWText rankNameText;
        TFWText powerText;
        TFWText onlineCountText;
        UIFlippable flippable;
        TFWImage bgIcon;
        TFWImage bgArrow;
        GameObject setBtnObj;
        TFWButton expandBtn;
        GameObject changeNameBtnObj;
        GameObject changeNameBtnParentObj;
        GameObject officialBtn;
        AllianceTemplateItemData data;
        /// <summary>
        /// ��ǰ�����RectTransform
        /// </summary>
        private RectTransform rectLeftPart;

        public AllianceTemplateItem(GameObject root) : base(root)
        {
        }

        public override void OnInit()
        {
            base.OnInit();
            classIcon = GetComponent<TFWImage>("LeftPart/ClassIcon/RankIcon");
            classIconText = GetComponent<TFWText>("LeftPart/ClassIcon/Text");
            rankNameText = GetComponent<TFWText>("LeftPart/RankName");
            powerText = GetComponent<TFWText>("LeftPart/Power");
            onlineCountText = GetComponent<TFWText>("CountIcon/Text");
            flippable = GetComponent<UIFlippable>("Expand/Expand");
            bgIcon = GetComponent<TFWImage>("Expand/BG1");
            bgArrow = GetComponent<TFWImage>("Expand/BG");
            setBtnObj = GetChild("LeftPart/Power/SetBtn");
            changeNameBtnObj = GetChild("LeftPart/ChangeNameBtn/BG");
            changeNameBtnParentObj = GetChild("LeftPart/ChangeNameBtn");
            expandBtn = GetComponent<TFWButton>("Expand");
            rectLeftPart = GetComponent<RectTransform>("LeftPart");
            officialBtn = GetChild("LeftPart/Power/officialBtn");
            UIBase.AddRemoveListener(EventTriggerType.Click, officialBtn, (a, b) =>
            {
                OnClickOfficial();
            });
        }

        private void OnClickOfficial()
        {
            PopupManager.I.ShowPanel<UIAllianceOfficials>();
        }

        public async UniTaskVoid SetData(AllianceTemplateItemData data)
        {
            this.data = data;
            var selfData = LAllianceMgr.I.GetMemberClassInfo(LPlayer.I.PlayerID);
            officialBtn.gameObject.SetActive(data.isSelfAlliance && selfData.Class > 4 && data.classIndex == 4);
            UITools.SetImageBySpriteName(classIcon, $"UI_Alliance_Icon_R{data.classIndex}");
            //UITools.SetImage(classIcon, data.disPlayKey, "AllianceMember");
            classIconText.text = data.classIndex.ToString();
            powerText.text = LocalizationMgr.Format("LC_UNION_class_n", data.classIndex);
            onlineCountText.text = string.Format("{0}/{1}", data.onlineCount, data.countData.mChildCount);  //string.Format("<color=#00FF25>{0}</color>/{1}", data.onlineCount, data.countData.mChildCount);
            onlineCountText.gameObject.SetActive(data.isSelfAlliance);
            flippable.vertical = data.countData.mIsExpand ? data.countData.mIsExpand : false;
            bool isArrowDown = false;
            if (data.countData.mChildCount == 0)
                //bgIcon.enabled = false;
                isArrowDown = false;
            else
                isArrowDown = data.countData.mIsExpand ? data.countData.mIsExpand : false;
            bgIcon.enabled = isArrowDown;
            bgArrow.enabled = !isArrowDown;
            if (data.isSelfAlliance)
            {
                rankNameText.enabled = true;
                rankNameText.text = await LAllianceMgr.I.GetUnionRankName(data.unionClassCfgID);
                if (LAllianceMgr.I.HavePermission(AlliancePermissionEnum.ChangeRankName))
                {
                    changeNameBtnParentObj.SetActive(true);
                    UIBase.AddRemoveListener(EventTriggerType.Click, changeNameBtnObj, async (a, b) =>
                    {
                        PopupManager.I.ShowPanel<UIAllianceRankRename>(new UIAllianceRankReNameData()
                        {
                            currentName = await LAllianceMgr.I.GetUnionRankName(data.unionClassCfgID),
                            cfgID = data.unionClassCfgID,
                        });
                    });
                }
                else
                {
                    changeNameBtnParentObj.SetActive(false);
                }

                if (LAllianceMgr.I.HavePermission(AlliancePermissionEnum.Promotion))
                {
                    if (data.classIndex == (int)AllianceClassEnum.Class1 || data.classIndex == (int)AllianceClassEnum.Class2 || data.classIndex == (int)AllianceClassEnum.Class3)
                    {
                        setBtnObj.SetActive(true);
                        UIBase.AddRemoveListener(EventTriggerType.Click, setBtnObj, (a, b) =>
                        {
                            OnAutoUp(data.allianceClass);
                        });
                    }
                    else
                    {
                        setBtnObj.SetActive(false);
                    }
                }
                else
                {
                    setBtnObj.SetActive(false);
                }


            }
            else
            {
                rankNameText.enabled = false;
                setBtnObj.SetActive(false);
                changeNameBtnParentObj.SetActive(false);
            }
            expandBtn.onClick.RemoveAllListeners();
            expandBtn.onClick.AddListener(() =>
            {
                data.OnExpandClickedAction.Invoke(data.countData.mTreeItemIndex, data.countData.mChildCount);
            });

            //NTimer.CountDown(0.1f, () =>
            //{
            //    LayoutRebuilder.ForceRebuildLayoutImmediate(rectLeftPart);
            //});
            ////todo ˢ��layout
            LayoutRebuilder.ForceRebuildLayoutImmediate(rectLeftPart);

        }

        /// <summary>
        /// �Զ���������
        /// </summary>
        /// <param name="index"></param>
        /// <param name="childCount"></param>
        public void OnAutoUp(AllianceClassEnum allianceClass)
        {
            allianceClass = allianceClass + 1;//���õ�����һ���׼���
            PopupManager.I.ShowPanel<UIAlliancePromotion>(new UIAlliancePromotionData() { allianceClass = allianceClass });
        }
    }
}