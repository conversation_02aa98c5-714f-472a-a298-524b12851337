﻿// Toony Colors Pro+Mobile 2
// (c) 2014-2021 <PERSON> "Toony Colors Pro 2/Hybrid Shader 2 (Outline)"
{
	Properties
	{
		[Enum(Front, 2, Back, 1, Both, 0)] _Cull ("Render Face", Float) = 2.0
		[TCP2ToggleNoKeyword] _ZWrite ("Depth Write", Float) = 1.0
		[Toggle(_ALPHATEST_ON)] _UseAlphaTest ("Alpha Clipping", Float) = 0
	//# IF_KEYWORD _ALPHATEST_ON
		_Cutoff ("Alpha Cutoff", Range(0,1)) = 0.5
	//# END_IF

	//# ========================================================
	//# Base
		[MainColor] _BaseColor ("Color", Color) = (1,1,1,1)
		[MainTexture] _BaseMap ("Albedo", 2D) = "white" {}
		[TCP2ColorNoAlpha] _HColor ("Highlight Color", Color) = (1,1,1,1)
		[TCP2ColorNoAlpha] _SColor ("Shadow Color", Color) = (0.2,0.2,0.2,1)
		[Toggle(TCP2_SHADOW_LIGHT_COLOR)] _ShadowColorLightAtten ("Main Light affects Shadow Color", Float) = 1
/*** OPTION: "TCP2 Shadow Albedo Texture" ***/
		[Toggle(TCP2_SHADOW_TEXTURE)] _UseShadowTexture ("Enable Shadow Albedo Texture", Float) = 0
	//# IF_KEYWORD TCP2_SHADOW_TEXTURE
		[NoScaleOffset] _ShadowBaseMap ("Shadow Albedo", 2D) = "gray" {}
	//# END_IF
/*** END OPTION ***/

	//# ========================================================

	//# Ramp Shading
/*** OPTION: "TCP2 Ramp Style Variants" ***/
		[TCP2MaterialKeywordEnumNoPrefix(Default,_,Crisp,TCP2_RAMP_CRISP,Bands,TCP2_RAMP_BANDS,Bands Crisp,TCP2_RAMP_BANDS_CRISP,Texture,TCP2_RAMPTEXT)] _RampType ("Ramp Type", Float) = 0
	//# IF_KEYWORD TCP2_RAMPTEXT
		[TCP2Gradient] _Ramp ("Ramp Texture (RGB)", 2D) = "gray" {}
		_RampScale ("Scale", Float) = 1.0
		_RampOffset ("Offset", Float) = 0.0
	//# ELSE
/*** END OPTION ***/
		[PowerSlider(0.415)] _RampThreshold ("Threshold", Range(0.01,1)) = 0.75
	//# END_IF	/*** OPTION: "TCP2 Ramp Style Variants" ***/
	//# IF_KEYWORD !TCP2_RAMPTEXT && !TCP2_RAMP_CRISP
		_RampSmoothing ("Smoothing", Range(0,1)) = 0.1
	//# END_IF
/*** OPTION: "TCP2 Ramp Style Variants" ***/
	//# IF_KEYWORD TCP2_RAMP_BANDS || TCP2_RAMP_BANDS_CRISP
		[IntRange] _RampBands ("Bands Count", Range(1,20)) = 4
	//# END_IF
	//# IF_KEYWORD TCP2_RAMP_BANDS
		_RampBandsSmoothing ("Bands Smoothing", Range(0,1)) = 0.1
	//# END_IF
/*** END OPTION ***/

	//# ========================================================

/*** OPTION: "TCP2 Normal Map" ***/
		[TCP2HeaderToggle(_NORMALMAP)] _UseNormalMap ("Normal Mapping", Float) = 0
	//# IF_KEYWORD _NORMALMAP
		_BumpMap ("Normal Map", 2D) = "bump" {}
		_BumpScale ("Scale", Range(-1,1)) = 1
	//# END_IF
	//# ========================================================
/*** END OPTION ***/

/*** OPTION: "TCP2 Specular" ***/
		[TCP2HeaderToggle(TCP2_SPECULAR)] _UseSpecular ("Specular", Float) = 0
	//# IF_KEYWORD TCP2_SPECULAR
		[TCP2MaterialKeywordEnumNoPrefix(GGX,_,Stylized,TCP2_SPECULAR_STYLIZED,Crisp,TCP2_SPECULAR_CRISP)] _SpecularType ("Type", Float) = 0
		[TCP2ColorNoAlpha] [HDR] _SpecularColor ("Color", Color) = (0.75,0.75,0.75,1)
	//# IF_KEYWORD TCP2_SPECULAR_STYLIZED || TCP2_SPECULAR_CRISP
		[PowerSlider(5.0)] _SpecularToonSize ("Size", Range(0.001,1)) = 0.25
	//# IF_KEYWORD TCP2_SPECULAR_STYLIZED
		_SpecularToonSmoothness ("Smoothing", Range(0,1)) = 0.05
	//# END_IF
	//# ELSE
		_SpecularRoughness ("Roughness", Range(0,1)) = 0.5
	//# END_IF
	//# IF_KEYWORD_DISABLE !TCP2_MOBILE
		[Enum(Disabled,0,Albedo Alpha,1,Custom R,2,Custom G,3,Custom B,4,Custom A,5)] _SpecularMapType ("Specular Map#Specular Map (A)", Float) = 0
	//# END_IF_DISABLE
	//# IF_PROPERTY _SpecularMapType >= 2 || _UseMobileMode == 1
		[NoScaleOffset] _SpecGlossMap ("Specular Texture", 2D) = "white" {}
	//# END_IF
	//# END_IF
	//# ========================================================
/*** END OPTION ***/

/*** OPTION: "TCP2 Emission" ***/
		[TCP2HeaderToggle(_EMISSION)] _UseEmission ("Emission", Float) = 0
	//# IF_KEYWORD _EMISSION
	//# IF_KEYWORD_DISABLE !TCP2_MOBILE
		[Enum(No Texture,5,R,0,G,1,B,2,A,3,RGB,4)] _EmissionChannel ("Texture Channel", Float) = 4
	//# END_IF_DISABLE
	//# IF_PROPERTY _EmissionChannel < 5 || _UseMobileMode == 1
		_EmissionMap ("Texture#Texture (A)", 2D) = "white" {}
	//# END_IF
		[TCP2ColorNoAlpha(HDR)] _EmissionColor ("Color", Color) = (1,1,0,1)
	//# END_IF
	//# ========================================================
/*** END OPTION ***/

/*** OPTION: "TCP2 Rim Lighting" ***/
		[TCP2HeaderToggle(TCP2_RIM_LIGHTING)] _UseRim ("Rim Lighting", Float) = 0
	//# IF_KEYWORD TCP2_RIM_LIGHTING
		[TCP2ColorNoAlpha] [HDR] _RimColor ("Color", Color) = (0.8,0.8,0.8,0.5)
		_RimMin ("Min", Range(0,2)) = 0.5
		_RimMax ("Max", Range(0,2)) = 1
		[Toggle(TCP2_RIM_LIGHTING_LIGHTMASK)] _UseRimLightMask ("Light-based Mask", Float) = 1
	//# END_IF
	//# ========================================================
/*** END OPTION ***/

/*** OPTION: "TCP2 MatCap" ***/
		[TCP2HeaderToggle(TCP2_MATCAP)] _UseMatCap ("MatCap", Float) = 0
	//# IF_KEYWORD TCP2_MATCAP
	//# IF_KEYWORD_DISABLE !TCP2_MOBILE
		[Enum(Additive,0,Replace,1)] _MatCapType ("MatCap Blending", Float) = 0
	//# END_IF_DISABLE
		[NoScaleOffset] _MatCapTex ("Texture", 2D) = "black" {}
		[HDR] [TCP2ColorNoAlpha] _MatCapColor ("Color", Color) = (1,1,1,1)
		[Toggle(TCP2_MATCAP_MASK)] _UseMatCapMask ("Enable Mask", Float) = 0
	//# IF_KEYWORD TCP2_MATCAP_MASK
		[NoScaleOffset] _MatCapMask ("Mask Texture#Mask Texture (A)", 2D) = "black" {}
	//# IF_KEYWORD_DISABLE !TCP2_MOBILE
		[Enum(R,0,G,1,B,2,A,3)] _MatCapMaskChannel ("Texture Channel", Float) = 0
	//# END_IF_DISABLE
	//# END_IF
	//# END_IF
	//# ========================================================
/*** END OPTION ***/

	//# Global Illumination
	//# 

	//# Indirect Diffuse
		_IndirectIntensity ("Strength", Range(0,1)) = 1
	//# IF_PROPERTY _IndirectIntensity > 0
		[TCP2ToggleNoKeyword] _SingleIndirectColor ("Single Indirect Color", Float) = 0
	//# END_IF
	//# 

/*** OPTION: "TCP2 Reflections" ***/
		[TCP2HeaderToggle(TCP2_REFLECTIONS)] _UseReflections ("Indirect Specular (Environment Reflections)", Float) = 0
	//# IF_KEYWORD TCP2_REFLECTIONS
		[TCP2ColorNoAlpha] _ReflectionColor ("Color", Color) = (1,1,1,1)
		_ReflectionSmoothness ("Smoothness", Range(0,1)) = 0.5
	//# IF_KEYWORD_DISABLE !TCP2_MOBILE
		[TCP2Enum(Disabled,0,Albedo Alpha (Smoothness),1,Custom R (Smoothness),2,Custom G (Smoothness),3,Custom B (Smoothness),4,Custom A (Smoothness),5,Albedo Alpha (Mask),6,Custom R (Mask),7,Custom G (Mask),8,Custom B (Mask),9,Custom A (Mask),10)]
		_ReflectionMapType ("Reflection Map", Float) = 0
	//# END_IF_DISABLE
	//# IF_PROPERTY (_ReflectionMapType != 0 && _ReflectionMapType != 1 && _ReflectionMapType != 6) || _UseMobileMode == 1
		[NoScaleOffset] _ReflectionTex ("Reflection Texture#Reflection Texture (A)", 2D) = "white" {}
	//# END_IF
		[Toggle(TCP2_REFLECTIONS_FRESNEL)] _UseFresnelReflections ("Fresnel Reflections", Float) = 1
	//# IF_KEYWORD TCP2_REFLECTIONS_FRESNEL
		_FresnelMin ("Fresnel Min", Range(0,2)) = 0
		_FresnelMax ("Fresnel Max", Range(0,2)) = 1.5
	//# END_IF
	//# END_IF
	//# 
/*** END OPTION ***/

/*** OPTION: "TCP2 Occlusion" ***/
		[TCP2HeaderToggle(TCP2_OCCLUSION)] _UseOcclusion ("Occlusion", Float) = 0
	//# IF_KEYWORD TCP2_OCCLUSION
		_OcclusionStrength ("Strength", Range(0.0, 1.0)) = 1.0
	//# IF_PROPERTY _OcclusionChannel >= 1 || _UseMobileMode == 1
		[NoScaleOffset] _OcclusionMap ("Texture#Texture (A)", 2D) = "white" {}
	//# END_IF
	//# IF_KEYWORD_DISABLE !TCP2_MOBILE
		[Enum(Albedo Alpha,0,Custom R,1,Custom G,2,Custom B,3,Custom A,4)] _OcclusionChannel ("Texture Channel", Float) = 0
	//# END_IF_DISABLE
	//# END_IF
	//# 
/*** END OPTION ***/

	//# ========================================================

		[TCP2HeaderToggle] _UseOutline ("Outline", Float) = 0
	//# IF_PROPERTY _UseOutline > 0
		[HDR] _OutlineColor ("Color", Color) = (0,0,0,1)
		[TCP2MaterialKeywordEnumNoPrefix(Disabled,_,Vertex Shader,TCP2_OUTLINE_TEXTURED_VERTEX,Pixel Shader,TCP2_OUTLINE_TEXTURED_FRAGMENT)] _OutlineTextureType ("Texture", Float) = 0
	//# IF_PROPERTY _OutlineTextureType >= 1
		_OutlineTextureLOD ("Texture LOD", Range(0,8)) = 5
	//# END_IF
	//# 
		_OutlineWidth ("Width", Range(0,10)) = 1
		[TCP2MaterialKeywordEnumNoPrefix(Disabled,_,Constant,TCP2_OUTLINE_CONST_SIZE,Minimum,TCP2_OUTLINE_MIN_SIZE,Min Max,TCP2_OUTLINE_MIN_MAX_SIZE)] _OutlinePixelSizeType ("Pixel Size", Float) = 0
	//# IF_KEYWORD TCP2_OUTLINE_MIN_SIZE || TCP2_OUTLINE_MIN_MAX_SIZE
		_OutlineMinWidth ("Minimum Width (Pixels)", Float) = 1
	//# END_IF
	//# IF_KEYWORD TCP2_OUTLINE_MIN_MAX_SIZE
		_OutlineMaxWidth ("Maximum Width (Pixels)", Float) = 1
	//# END_IF
	//# 
		[TCP2MaterialKeywordEnumNoPrefix(Normal, _, Vertex Colors, TCP2_COLORS_AS_NORMALS, Tangents, TCP2_TANGENT_AS_NORMALS, UV1, TCP2_UV1_AS_NORMALS, UV2, TCP2_UV2_AS_NORMALS, UV3, TCP2_UV3_AS_NORMALS, UV4, TCP2_UV4_AS_NORMALS)]
		_NormalsSource ("Outline Normals Source", Float) = 0
	//# IF_PROPERTY_DISABLE _NormalsSource > 2
		[TCP2MaterialKeywordEnumNoPrefix(Full XYZ, TCP2_UV_NORMALS_FULL, Compressed XY, _, Compressed ZW, TCP2_UV_NORMALS_ZW)]
		_NormalsUVType ("UV Data Type", Float) = 0
	//# END_IF_DISABLE
	//# 

	//# IF_URP
		[TCP2MaterialKeywordEnumNoPrefix(Disabled,_,Main Directional Light,TCP2_OUTLINE_LIGHTING_MAIN,All Lights,TCP2_OUTLINE_LIGHTING_ALL,Indirect Only, TCP2_OUTLINE_LIGHTING_INDIRECT)] _OutlineLightingTypeURP ("Lighting", Float) = 0
	//# ELSE
		[TCP2MaterialKeywordEnumNoPrefix(Disabled,_,Main Directional Light,TCP2_OUTLINE_LIGHTING_MAIN,Indirect Only, TCP2_OUTLINE_LIGHTING_INDIRECT)] _OutlineLightingType ("Lighting", Float) = 0
	//# END_IF
	//#
	//# IF_KEYWORD TCP2_OUTLINE_LIGHTING_MAIN || TCP2_OUTLINE_LIGHTING_ALL || TCP2_OUTLINE_LIGHTING_INDIRECT
	//# IF_KEYWORD TCP2_OUTLINE_LIGHTING_MAIN || TCP2_OUTLINE_LIGHTING_ALL
		_DirectIntensityOutline ("Direct Strength", Range(0,1)) = 1
	//# END_IF
		_IndirectIntensityOutline ("Indirect Strength", Range(0,1)) = 0
	//# END_IF
	//# END_IF

	//# ========================================================

	//# Options
		[ToggleOff(_RECEIVE_SHADOWS_OFF)] _ReceiveShadowsOff ("Receive Shadows", Float) = 1

		[HideInInspector] _RenderingMode ("rendering mode", Float) = 0.0
		[HideInInspector] _SrcBlend ("blending source", Float) = 1.0
		[HideInInspector] _DstBlend ("blending destination", Float) = 0.0
		[HideInInspector] _UseMobileMode ("Mobile mode", Float) = 0
	}

/*** URP ***/
	//================================================================================================================================
	//
	// UNIVERSAL RENDER PIPELINE
	//
	//================================================================================================================================

	SubShader
	{
		Tags
		{
			"RenderPipeline" = "UniversalPipeline"
			"IgnoreProjector" = "True"
			"RenderType" = "Opaque"
			"Queue" = "Geometry"
		}

		Blend [_SrcBlend] [_DstBlend]
		ZWrite [_ZWrite]
		Cull [_Cull]

		HLSLINCLUDE
			#define fixed half
			#define fixed2 half2
			#define fixed3 half3
			#define fixed4 half4

			#define TCP2_HYBRID_URP
		ENDHLSL

		Pass
		{
			Name "Main"
			Tags { "LightMode" = "UniversalForward" }

			HLSLPROGRAM

			// Required to compile gles 2.0 with standard SRP library
			// All shaders must be compiled with HLSLcc and currently only gles is not using HLSLcc by default
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 3.0

			#pragma vertex Vertex
			#pragma fragment Fragment

			#pragma multi_compile_fog    /*** OPTION: "Fog" ***/
			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/

/*** URP_VERSION >= 14 ***/
			#include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"    /*** OPTION: "DOTS Instancing (Entities Graphics)" ***/
/*** END URP_VERSION ***/

			// -------------------------------------
			// Material keywords
			#pragma shader_feature_local _ _RECEIVE_SHADOWS_OFF

			// -------------------------------------
			// Universal Render Pipeline keywords
/*** URP_VERSION >= 11 ***/
			#pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN     /*** OPTION: "Main Light Shadows (URP)" ***/
/*** END URP_VERSION ***/
/*** URP_VERSION < 11 ***/
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS             /*** OPTION: "Main Light Shadows (URP)" ***/
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE     /*** OPTION: "Main Light Shadows (URP)" ***/
/*** END URP_VERSION ***/
			#pragma multi_compile _ _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS     /*** OPTION: "Additional Lights (URP)" ***/
			#pragma multi_compile_fragment _ _ADDITIONAL_LIGHT_SHADOWS    /*** OPTION: "Additional Lights Shadows" ***/
			#pragma multi_compile_fragment _ _SHADOWS_SOFT    /*** OPTION: "Soft Shadows (URP)" ***/
/*** URP_VERSION >= 10 ***/
			// URP 10:
			#pragma multi_compile _ _SCREEN_SPACE_OCCLUSION    /*** OPTION: "SSAO (URP 10+)" ***/
/*** END URP_VERSION ***/
/*** URP_VERSION >= 12 ***/
			// URP 12:
			#pragma multi_compile_fragment _ _DBUFFER_MRT1 _DBUFFER_MRT2 _DBUFFER_MRT3    /*** OPTION: "Decals (URP 12+)" ***/
			#pragma multi_compile_fragment _ _LIGHT_LAYERS    /*** OPTION: "Light Layers (URP 12+)" ***/
			#pragma multi_compile_fragment _ _LIGHT_COOKIES    /*** OPTION: "Light Cookies (URP 12+)" ***/
/*** END URP_VERSION ***/

/*** URP_VERSION >= 14 ***/
			// URP 14:
			#pragma multi_compile_fragment _ _WRITE_RENDERING_LAYERS    /*** OPTION: "Rendering Layers (URP 14+)" ***/
			#pragma multi_compile _ _FORWARD_PLUS    /*** OPTION: "Forward+ Support (URP 14+)" ***/
			#pragma multi_compile_fragment _ DEBUG_DISPLAY    /*** OPTION: "Rendering Debugger Support (URP 14+)" ***/
/*** END URP_VERSION ***/

			// -------------------------------------
			// Unity keywords
			#pragma multi_compile _ LIGHTMAP_SHADOW_MIXING    /*** OPTION_OFF: "Lightmap" ***/
			#pragma multi_compile _ SHADOWS_SHADOWMASK        /*** OPTION_OFF: "Lightmap" ***/
			#pragma multi_compile _ DIRLIGHTMAP_COMBINED    /*** OPTION_OFF: "Lightmap" ***/
			#pragma multi_compile _ LIGHTMAP_ON    /*** OPTION_OFF: "Lightmap" ***/
			#pragma multi_compile _ DYNAMICLIGHTMAP_ON    /*** OPTION_OFF: "Lightmap" ***/
			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/

			//--------------------------------------
			// Toony Colors Pro 2 keywords
			#pragma shader_feature_local TCP2_MOBILE
			#pragma shader_feature_local_fragment _ TCP2_RAMPTEXT TCP2_RAMP_CRISP TCP2_RAMP_BANDS TCP2_RAMP_BANDS_CRISP    /*** OPTION: "TCP2 Ramp Style Variants" ***/
			#pragma shader_feature_local_fragment TCP2_SHADOW_LIGHT_COLOR
			#pragma shader_feature_local_fragment TCP2_SHADOW_TEXTURE    /*** OPTION: "TCP2 Shadow Albedo Texture" ***/
			#pragma shader_feature_local_fragment TCP2_SPECULAR                                   /*** OPTION: "TCP2 Specular" ***/
			#pragma shader_feature_local_fragment _ TCP2_SPECULAR_STYLIZED TCP2_SPECULAR_CRISP    /*** OPTION: "TCP2 Specular" ***/
			#pragma shader_feature_local TCP2_RIM_LIGHTING              /*** OPTION: "TCP2 Rim Lighting" ***/
			#pragma shader_feature_local TCP2_RIM_LIGHTING_LIGHTMASK    /*** OPTION: "TCP2 Rim Lighting" ***/
			#pragma shader_feature_local TCP2_REFLECTIONS            /*** OPTION: "TCP2 Reflections" ***/
			#pragma shader_feature_local TCP2_REFLECTIONS_FRESNEL    /*** OPTION: "TCP2 Reflections" ***/
			#pragma shader_feature_local TCP2_MATCAP         /*** OPTION: "TCP2 MatCap" ***/
			#pragma shader_feature_local_fragment TCP2_MATCAP_MASK    /*** OPTION: "TCP2 MatCap" ***/
			#pragma shader_feature_local_fragment TCP2_OCCLUSION    /*** OPTION: "TCP2 Occlusion" ***/
			#pragma shader_feature_local _NORMALMAP    /*** OPTION: "TCP2 Normal Map" ***/
			#pragma shader_feature_local_fragment _ALPHATEST_ON
			#pragma shader_feature_local_fragment _EMISSION    /*** OPTION: "TCP2 Emission" ***/

			// This is using an existing keyword to separate fade/transparent behaviors
			#pragma shader_feature_local_fragment _ _ALPHAPREMULTIPLY_ON

/*** #define URP_VERSION ***/
			#include "TCP2 Hybrid 2 Include.cginc"

			ENDHLSL
		}

		//--------------------------------------------------------------------------------------------------------------------------------

		// Outline
		Pass
		{
			Name "Outline"
			Tags { "LightMode" = "Outline" }
			Cull Front

			HLSLPROGRAM
			#pragma target 3.0

			#pragma vertex vertex_outline
			#pragma fragment fragment_outline

			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/
			#pragma multi_compile_fog    /*** OPTION: "Fog" ***/

/*** URP_VERSION >= 14 ***/
			#include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"    /*** OPTION: "DOTS Instancing (Entities Graphics)" ***/
/*** END URP_VERSION ***/

			// -------------------------------------
			// Material keywords
			//#pragma shader_feature _ALPHATEST_ON
			#pragma shader_feature_local _ _RECEIVE_SHADOWS_OFF

			// -------------------------------------
			// Universal Render Pipeline keywords
			#pragma multi_compile _ _MAIN_LIGHT_SHADOWS            /*** OPTION: "Main Light Shadows (URP)" ***/
			#pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE    /*** OPTION: "Main Light Shadows (URP)" ***/
			#pragma multi_compile _ _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS     /*** OPTION: "Additional Lights (URP)" ***/
			#pragma multi_compile_fragment _ _ADDITIONAL_LIGHT_SHADOWS    /*** OPTION: "Additional Lights Shadows" ***/
			#pragma multi_compile_fragment _ _SHADOWS_SOFT    /*** OPTION: "Soft Shadows (URP)" ***/
/*** URP_VERSION >= 10 ***/
			// URP 10:
			#pragma multi_compile _ _SCREEN_SPACE_OCCLUSION    /*** OPTION: "SSAO (URP 10+)" ***/
/*** END URP_VERSION ***/
/*** URP_VERSION >= 12 ***/
			// URP 12:
			#pragma multi_compile_fragment _ _LIGHT_LAYERS    /*** OPTION: "Light Layers (URP 12+)" ***/
			#pragma multi_compile_fragment _ _LIGHT_COOKIES    /*** OPTION: "Light Cookies (URP 12+)" ***/
/*** END URP_VERSION ***/

/*** URP_VERSION >= 14 ***/
			// URP 14:
			#pragma multi_compile_fragment _ _WRITE_RENDERING_LAYERS    /*** OPTION: "Rendering Layers (URP 14+)" ***/
			#pragma multi_compile _ _FORWARD_PLUS    /*** OPTION: "Forward+ Support (URP 14+)" ***/
/*** END URP_VERSION ***/

			// -------------------------------------
			// Unity keywords
			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/

			// -------------------------------------
			// Toony Colors Pro 2 keywords
			#pragma shader_feature_local _ TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV1_AS_NORMALS TCP2_UV2_AS_NORMALS TCP2_UV3_AS_NORMALS TCP2_UV4_AS_NORMALS
			#pragma shader_feature_local _ TCP2_UV_NORMALS_FULL TCP2_UV_NORMALS_ZW
			#pragma shader_feature_local_vertex _ TCP2_OUTLINE_CONST_SIZE TCP2_OUTLINE_MIN_SIZE TCP2_OUTLINE_MIN_MAX_SIZE
			#pragma shader_feature_local _ TCP2_OUTLINE_TEXTURED_VERTEX TCP2_OUTLINE_TEXTURED_FRAGMENT
			#pragma shader_feature_local _ TCP2_OUTLINE_LIGHTING_MAIN TCP2_OUTLINE_LIGHTING_ALL TCP2_OUTLINE_LIGHTING_INDIRECT
			#pragma shader_feature_local_fragment _ TCP2_SHADOW_TEXTURE    /*** OPTION: "TCP2 Shadow Albedo Texture" ***/

/*** #define URP_VERSION ***/
			#define TCP2_OUTLINE_PASS
			#include "TCP2 Hybrid 2 Include.cginc"

			ENDHLSL
		}

		//--------------------------------------------------------------------------------------------------------------------------------

		Pass
		{
			Name "ShadowCaster"
			Tags { "LightMode" = "ShadowCaster" }

			ZWrite On
			ZTest LEqual
			Cull [_Cull]

			HLSLPROGRAM
			// Required to compile gles 2.0 with standard srp library
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 2.0

			#pragma vertex ShadowPassVertex
			#pragma fragment ShadowPassFragment

			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/
/*** URP_VERSION >= 14 ***/
			#include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"    /*** OPTION: "DOTS Instancing (Entities Graphics)" ***/
/*** END URP_VERSION ***/

			#pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW    /*** OPTION: "Additional Lights Shadows" ***/
			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/

			#pragma shader_feature_local_fragment _ALPHATEST_ON

			#define SHADOW_CASTER_PASS
/*** #define URP_VERSION ***/
			#include "TCP2 Hybrid 2 Include.cginc"

			float3 _LightDirection;
			float3 _LightPosition;

			struct Attributes_Shadow
			{
				float4 positionOS   : POSITION;
				float3 normalOS     : NORMAL;
				float2 texcoord     : TEXCOORD0;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct Varyings_Shadow
			{
				float2 uv           : TEXCOORD0;
				float4 positionCS   : SV_POSITION;
			};

			float4 GetShadowPositionHClip(Attributes_Shadow input)
			{
				float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
				float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

				#if _CASTING_PUNCTUAL_LIGHT_SHADOW
					float3 lightDirectionWS = normalize(_LightPosition - positionWS);
				#else
					float3 lightDirectionWS = _LightDirection;
				#endif

				float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

				#if UNITY_REVERSED_Z
					positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
				#else
					positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
				#endif

				return positionCS;
			}

			Varyings_Shadow ShadowPassVertex(Attributes_Shadow input)
			{
				Varyings_Shadow output = (Varyings_Shadow)0;
				UNITY_SETUP_INSTANCE_ID(input);

				output.uv = TRANSFORM_TEX(input.texcoord, _BaseMap);
				output.positionCS = GetShadowPositionHClip(input);
				return output;
			}

			half4 ShadowPassFragment(Varyings_Shadow input) : SV_TARGET
			{
				#if defined(_ALPHATEST_ON)
					half4 albedo = tex2D(_BaseMap, input.uv.xy).rgba;
					albedo.rgb *= _BaseColor.rgb;
					half alpha = albedo.a * _BaseColor.a;
					clip(alpha - _Cutoff);
				#endif

/*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/
				#if defined(LOD_FADE_CROSSFADE)
					const float dither = Dither4x4(input.positionCS.xy);
					const float ditherThreshold = unity_LODFade.x - CopySign(dither, unity_LODFade.x);
					clip(ditherThreshold);
				#endif
/*** END OPTION ***/

				return 0;
			}

			ENDHLSL
		}

		//--------------------------------------------------------------------------------------------------------------------------------

		Pass
		{
			Name "DepthOnly"
			Tags { "LightMode" = "DepthOnly" }

			ZWrite On
			ColorMask 0
			Cull [_Cull]

			HLSLPROGRAM

			// Required to compile gles 2.0 with standard srp library
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 2.0

			#pragma vertex DepthOnlyVertex
			#pragma fragment DepthOnlyFragment

			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/
/*** URP_VERSION >= 14 ***/
			#include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"    /*** OPTION: "DOTS Instancing (Entities Graphics)" ***/
/*** END URP_VERSION ***/

			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/

			#pragma shader_feature_local_fragment _ALPHATEST_ON

/*** #define URP_VERSION ***/
			#include "TCP2 Hybrid 2 Include.cginc"

			struct Attributes_Depth
			{
				float4 position     : POSITION;
				float2 texcoord     : TEXCOORD0;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct Varyings_Depth
			{
				float2 uv           : TEXCOORD0;
				float4 positionCS   : SV_POSITION;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			Varyings_Depth DepthOnlyVertex(Attributes_Depth input)
			{
				Varyings_Depth output = (Varyings_Depth)0;
				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

				output.uv = TRANSFORM_TEX(input.texcoord, _BaseMap);
				output.positionCS = TransformObjectToHClip(input.position.xyz);
				return output;
			}

			half4 DepthOnlyFragment(Varyings_Depth input) : SV_TARGET
			{
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

/*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/
				#if defined(LOD_FADE_CROSSFADE)
					const float dither = Dither4x4(input.positionCS.xy);
					const float ditherThreshold = unity_LODFade.x - CopySign(dither, unity_LODFade.x);
					clip(ditherThreshold);
				#endif
/*** END OPTION ***/

				half4 albedo = tex2D(_BaseMap, input.uv.xy).rgba;
				albedo.rgb *= _BaseColor.rgb;
				half alpha = albedo.a * _BaseColor.a;

				#if defined(_ALPHATEST_ON)
					clip(alpha - _Cutoff);
				#endif

				return 0;
			}

			ENDHLSL
		}

		//--------------------------------------------------------------------------------------------------------------------------------

		Pass
		{
			Name "DepthNormals"
			Tags { "LightMode" = "DepthNormals" }

			ZWrite On
			Cull [_Cull]

			HLSLPROGRAM

			// Required to compile gles 2.0 with stanard srp library
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 2.0

			#pragma vertex DepthNormalsVertex
			#pragma fragment DepthNormalsFragment

			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/
/*** URP_VERSION >= 14 ***/
			#include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"    /*** OPTION: "DOTS Instancing (Entities Graphics)" ***/
/*** END URP_VERSION ***/

			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/
/*** URP_VERSION >= 14 ***/
			#pragma multi_compile_fragment _ _WRITE_RENDERING_LAYERS    /*** OPTION: "Rendering Layers (URP 14+)" ***/
/*** END URP_VERSION ***/

			#pragma shader_feature_local_fragment _ALPHATEST_ON

			#define DEPTH_NORMALS_PASS
/*** #define URP_VERSION ***/
			#include "TCP2 Hybrid 2 Include.cginc"

			struct Attributes_Depth
			{
				float4 position     : POSITION;
				float2 texcoord     : TEXCOORD0;
				float3 normal       : NORMAL;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct Varyings_Depth
			{
				float2 uv           : TEXCOORD0;
				float4 positionCS   : SV_POSITION;
				float3 normalWS     : TEXCOORD1;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			Varyings_Depth DepthNormalsVertex(Attributes_Depth input)
			{
				Varyings_Depth output = (Varyings_Depth)0;
				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

				output.uv = TRANSFORM_TEX(input.texcoord, _BaseMap);
				output.positionCS = TransformObjectToHClip(input.position.xyz);
				float3 normalWS = TransformObjectToWorldNormal(input.normal);
				output.normalWS = NormalizeNormalPerVertex(normalWS);
				return output;
			}

			half4 DepthNormalsFragment(
				Varyings_Depth input
#ifdef _WRITE_RENDERING_LAYERS
				, out float4 outRenderingLayers : SV_Target1
#endif
				) : SV_TARGET
			{
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

/*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/
				#if defined(LOD_FADE_CROSSFADE)
					const float dither = Dither4x4(input.positionCS.xy);
					const float ditherThreshold = unity_LODFade.x - CopySign(dither, unity_LODFade.x);
					clip(ditherThreshold);
				#endif
/*** END OPTION ***/

				half4 albedo = tex2D(_BaseMap, input.uv.xy).rgba;
				half alpha = albedo.a * _BaseColor.a;

				#if defined(_ALPHATEST_ON)
					clip(alpha - _Cutoff);
				#endif

				#if URP_VERSION >= 14 && defined(_WRITE_RENDERING_LAYERS)
					uint meshRenderingLayers = GetMeshRenderingLayer();
					outRenderingLayers = float4(EncodeMeshRenderingLayer(meshRenderingLayers), 0, 0, 0);
				#endif

				#if URP_VERSION >= 12
					return float4(input.normalWS.xyz, 0.0);
				#else
					return float4(PackNormalOctRectEncode(TransformWorldToViewDir(input.normalWS, true)), 0.0, 0.0);
				#endif
			}

			ENDHLSL
		}

		//--------------------------------------------------------------------------------------------------------------------------------
		
		Pass
		{
			Name "Meta"
			Tags { "LightMode"="Meta" }
			
			Cull Off

			HLSLPROGRAM
			// Required to compile gles 2.0 with standard SRP library
			// All shaders must be compiled with HLSLcc and currently only gles is not using HLSLcc by default
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 3.0

			#pragma vertex Vertex
			#pragma fragment Fragment

			//--------------------------------------
			// Toony Colors Pro 2 keywords
			#pragma shader_feature_local TCP2_MOBILE
			#pragma shader_feature_local_fragment TCP2_SPECULAR    /*** OPTION: "TCP2 Specular" ***/
			#pragma shader_feature_local_fragment _ALPHATEST_ON
			#pragma shader_feature_local_fragment _EMISSION    /*** OPTION: "TCP2 Emission" ***/

			#undef UNITY_SHOULD_SAMPLE_SH
			#define UNITY_SHOULD_SAMPLE_SH 0

			#ifndef UNITY_PASS_META
				#define UNITY_PASS_META
			#endif

			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/MetaInput.hlsl"
/*** #define URP_VERSION ***/
			#include "TCP2 Hybrid 2 Include.cginc"

			ENDHLSL
		}
		
		//--------------------------------------------------------------------------------------------------------------------------------
		
		// Depth prepass
		// UsePass "Universal Render Pipeline/Lit/DepthOnly"
	}
/*** END URP ***/

/*** BIRP ***/
	//================================================================================================================================
	//
	// BUILT-IN RENDER PIPELINE
	//
	//================================================================================================================================

	SubShader
	{
		Tags
		{
			// "RenderPipeline" = "UniversalPipeline"
			"IgnoreProjector" = "True"
			"RenderType" = "Opaque"
			"Queue" = "Geometry"
		}

		Blend [_SrcBlend] [_DstBlend]
		ZWrite [_ZWrite]
		Cull [_Cull]

		CGINCLUDE
			// Note: CG code is only used for the built-in render pipeline
			#include "UnityCG.cginc"
			#include "UnityLightingCommon.cginc"
			#include "UnityStandardUtils.cginc"
			#include "Lighting.cginc"
			#include "AutoLight.cginc"
		ENDCG

		Pass
		{
			Name "Main"
			Tags { "LightMode"="ForwardBase" }

			CGPROGRAM
			// Required to compile gles 2.0 with standard SRP library
			// All shaders must be compiled with HLSLcc and currently only gles is not using HLSLcc by default
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 3.0

			#pragma vertex Vertex
			#pragma fragment Fragment

			#pragma multi_compile_fog    /*** OPTION: "Fog" ***/
			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/
			#pragma multi_compile_fwdbase noshadowmask nodynlightmap nolightmap

			// -------------------------------------
			// Material keywords
			#pragma shader_feature_local _ _RECEIVE_SHADOWS_OFF

			// -------------------------------------
			// Unity keywords
			#pragma multi_compile _ LIGHTMAP_ON           /*** OPTION_OFF: "Lightmap" ***/
			#pragma multi_compile _ DYNAMICLIGHTMAP_ON    /*** OPTION_OFF: "Lightmap" ***/
			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/

			//--------------------------------------
			// Toony Colors Pro 2 keywords
			#pragma shader_feature_local TCP2_MOBILE
			#pragma shader_feature_local_fragment _ TCP2_RAMPTEXT TCP2_RAMP_CRISP TCP2_RAMP_BANDS TCP2_RAMP_BANDS_CRISP    /*** OPTION: "TCP2 Ramp Style Variants" ***/
			#pragma shader_feature_local_fragment TCP2_SHADOW_LIGHT_COLOR
			#pragma shader_feature_local_fragment TCP2_SHADOW_TEXTURE    /*** OPTION: "TCP2 Shadow Albedo Texture" ***/
			#pragma shader_feature_local_fragment TCP2_SPECULAR                                   /*** OPTION: "TCP2 Specular" ***/
			#pragma shader_feature_local_fragment _ TCP2_SPECULAR_STYLIZED TCP2_SPECULAR_CRISP    /*** OPTION: "TCP2 Specular" ***/
			#pragma shader_feature_local TCP2_RIM_LIGHTING              /*** OPTION: "TCP2 Rim Lighting" ***/
			#pragma shader_feature_local TCP2_RIM_LIGHTING_LIGHTMASK    /*** OPTION: "TCP2 Rim Lighting" ***/
			#pragma shader_feature_local TCP2_REFLECTIONS            /*** OPTION: "TCP2 Reflections" ***/
			#pragma shader_feature_local TCP2_REFLECTIONS_FRESNEL    /*** OPTION: "TCP2 Reflections" ***/
			#pragma shader_feature_local TCP2_MATCAP         /*** OPTION: "TCP2 MatCap" ***/
			#pragma shader_feature_local_fragment TCP2_MATCAP_MASK    /*** OPTION: "TCP2 MatCap" ***/
			#pragma shader_feature_local_fragment TCP2_OCCLUSION    /*** OPTION: "TCP2 Occlusion" ***/
			#pragma shader_feature_local _NORMALMAP    /*** OPTION: "TCP2 Normal Map" ***/
			#pragma shader_feature_local_fragment _ALPHATEST_ON
			#pragma shader_feature_local_fragment _EMISSION    /*** OPTION: "TCP2 Emission" ***/

			// This is using an existing keyword to separate fade/transparent behaviors
			#pragma shader_feature_local_fragment _ _ALPHAPREMULTIPLY_ON

			#define UNITY_INSTANCED_SH
			#include "UnityShaderVariables.cginc"
			#include "UnityShaderUtilities.cginc"

			#undef UNITY_SHOULD_SAMPLE_SH
			#define UNITY_SHOULD_SAMPLE_SH 1
			#include "AutoLight.cginc"

			#if !defined(UNITY_PASS_FORWARDBASE)
				#define UNITY_PASS_FORWARDBASE
			#endif

			#include "TCP2 Hybrid 2 Include.cginc"

			ENDCG
		}

		Pass
		{
			Name "Main"
			Tags { "LightMode"="ForwardAdd" }

			Blend [_SrcBlend] One
			Fog { Color (0,0,0,0) } // in additive pass fog should be black
			ZWrite Off
			ZTest LEqual

			CGPROGRAM
			// Required to compile gles 2.0 with standard SRP library
			// All shaders must be compiled with HLSLcc and currently only gles is not using HLSLcc by default
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 3.0

			#pragma vertex Vertex
			#pragma fragment Fragment

			#pragma multi_compile_fog    /*** OPTION: "Fog" ***/
			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/
			#pragma multi_compile_fwdadd_fullshadows    /*** OPTION: "Additional Lights Shadows" ***/

			// -------------------------------------
			// Material keywords
			#pragma shader_feature_local _ _RECEIVE_SHADOWS_OFF

			// -------------------------------------
			// Unity keywords
			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/

			//--------------------------------------
			// Toony Colors Pro 2 keywords
			#pragma shader_feature_local TCP2_MOBILE
			#pragma shader_feature_local_fragment _ TCP2_RAMPTEXT TCP2_RAMP_CRISP TCP2_RAMP_BANDS TCP2_RAMP_BANDS_CRISP    /*** OPTION: "TCP2 Ramp Style Variants" ***/
			#pragma shader_feature_local_fragment TCP2_SHADOW_LIGHT_COLOR
			#pragma shader_feature_local_fragment TCP2_SHADOW_TEXTURE    /*** OPTION: "TCP2 Shadow Albedo Texture" ***/
			#pragma shader_feature_local_fragment TCP2_SPECULAR                                   /*** OPTION: "TCP2 Specular" ***/
			#pragma shader_feature_local_fragment _ TCP2_SPECULAR_STYLIZED TCP2_SPECULAR_CRISP    /*** OPTION: "TCP2 Specular" ***/
			#pragma shader_feature_local TCP2_RIM_LIGHTING              /*** OPTION: "TCP2 Rim Lighting" ***/
			#pragma shader_feature_local TCP2_RIM_LIGHTING_LIGHTMASK    /*** OPTION: "TCP2 Rim Lighting" ***/
			#pragma shader_feature_local _NORMALMAP    /*** OPTION: "TCP2 Normal Map" ***/
			#pragma shader_feature_local_fragment _ALPHATEST_ON
			#pragma shader_feature_local_fragment _EMISSION    /*** OPTION: "TCP2 Emission" ***/

			// This is using an existing keyword to separate fade/transparent behaviors
			#pragma shader_feature_local_fragment _ _ALPHAPREMULTIPLY_ON

			#define UNITY_INSTANCED_SH
			#include "UnityShaderVariables.cginc"
			#include "UnityShaderUtilities.cginc"
			#include "AutoLight.cginc"

			#include "TCP2 Hybrid 2 Include.cginc"

			ENDCG
		}

		// ShadowCaster & Depth Pass
		Pass
		{
			Name "ShadowCaster"
			Tags { "LightMode" = "ShadowCaster" }

			CGPROGRAM
			#pragma vertex vertex_shadow
			#pragma fragment fragment_shadow
			#pragma target 2.0
			
			#pragma multi_compile_shadowcaster    /*** OPTION: "Additional Lights Shadows" ***/
			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/
			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/

			#pragma shader_feature_local_fragment _ALPHATEST_ON

			#define SHADOW_CASTER_PASS
			#include "TCP2 Hybrid 2 Include.cginc"

			struct Varyings_Shadow
			{
				V2F_SHADOW_CASTER;
				float2 uv : TEXCOORD1;
				UNITY_VERTEX_OUTPUT_STEREO
			};

			Varyings_Shadow vertex_shadow (appdata_base v)
			{
				Varyings_Shadow o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
				o.uv = TRANSFORM_TEX(v.texcoord, _BaseMap);
				return o;
			}
			
			float4 fragment_shadow (Varyings_Shadow i) : SV_Target
			{
				#if defined(_ALPHATEST_ON)
					half4 albedo = tex2D(_BaseMap, i.uv.xy).rgba;
					albedo.rgb *= _BaseColor.rgb;
					half alpha = albedo.a * _BaseColor.a;
					clip(alpha - _Cutoff);
				#endif

/*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/
				#if defined(LOD_FADE_CROSSFADE)
					const float dither = Dither4x4(i.pos.xy);
					const float ditherThreshold = unity_LODFade.x - CopySign(dither, unity_LODFade.x);
					clip(ditherThreshold);
				#endif
/*** END OPTION ***/

				SHADOW_CASTER_FRAGMENT(i)
			}
			ENDCG
		}
		
		Pass
		{
			Name "Meta"
			Tags { "LightMode"="Meta" }

			Cull Off

			CGPROGRAM
			// Required to compile gles 2.0 with standard SRP library
			// All shaders must be compiled with HLSLcc and currently only gles is not using HLSLcc by default
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 3.0

			#pragma vertex Vertex
			#pragma fragment Fragment

			//--------------------------------------
			// Toony Colors Pro 2 keywords
			#pragma shader_feature_local TCP2_MOBILE
			#pragma shader_feature_local_fragment TCP2_SPECULAR    /*** OPTION: "TCP2 Specular" ***/
			#pragma shader_feature_local_fragment _ALPHATEST_ON
			#pragma shader_feature_local_fragment _EMISSION    /*** OPTION: "TCP2 Emission" ***/

			#undef UNITY_SHOULD_SAMPLE_SH
			#define UNITY_SHOULD_SAMPLE_SH 0

			#if !defined(UNITY_PASS_META)
				#define UNITY_PASS_META
			#endif
			#include "TCP2 Hybrid 2 Include.cginc"

			ENDCG
		}

		// Outline
		Pass
		{
			Name "Outline"
			Tags { "LightMode"="ForwardBase" }
			Cull Front

			CGPROGRAM
			#pragma target 3.0

			#pragma vertex vertex_outline
			#pragma fragment fragment_outline

			#pragma multi_compile_instancing    /*** OPTION: "GPU Instancing" ***/
			#pragma multi_compile_fog    /*** OPTION: "Fog" ***/

			// -------------------------------------
			// Material keywords
			//#pragma shader_feature _ALPHATEST_ON
			#pragma shader_feature_local _ _RECEIVE_SHADOWS_OFF

			// -------------------------------------
			// Unity keywords
			#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE    /*** OPTION: "LOD Crossfading"|"Toggle LOD Crossfading support with dithering" ***/

			// -------------------------------------
			// Toony Colors Pro 2 keywords
			#pragma shader_feature_local _ TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV1_AS_NORMALS TCP2_UV2_AS_NORMALS TCP2_UV3_AS_NORMALS TCP2_UV4_AS_NORMALS
			#pragma shader_feature_local _ TCP2_UV_NORMALS_FULL TCP2_UV_NORMALS_ZW
			#pragma shader_feature_local_vertex _ TCP2_OUTLINE_CONST_SIZE TCP2_OUTLINE_MIN_SIZE TCP2_OUTLINE_MIN_MAX_SIZE
			#pragma shader_feature_local _ TCP2_OUTLINE_TEXTURED_VERTEX TCP2_OUTLINE_TEXTURED_FRAGMENT
			#pragma shader_feature_local _ TCP2_OUTLINE_LIGHTING_MAIN TCP2_OUTLINE_LIGHTING_INDIRECT
			#pragma shader_feature_local_fragment _ TCP2_SHADOW_TEXTURE    /*** OPTION: "TCP2 Shadow Albedo Texture" ***/

			#define TCP2_OUTLINE_PASS
			#include "TCP2 Hybrid 2 Include.cginc"

			ENDCG
		}
	}
/*** END BIRP ***/

	FallBack "Hidden/InternalErrorShader"
	CustomEditor "ToonyColorsPro.ShaderGenerator.MaterialInspector_Hybrid"
}