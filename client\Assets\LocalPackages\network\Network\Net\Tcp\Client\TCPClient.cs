﻿#if !UNITY_WEBGL
using Crypto;
using Helper;
using Net.Common;
using Net.Tcp.Common;
using System;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using UnityEngine;

namespace Net.Tcp.Client
{
    public sealed class TCPClient : IClient
    {
        static readonly string DefaultRsaPub = "<RSAKeyValue><Modulus>3xzU8e+jSKtePBcKoZjqfAlU3OAYmJhaCrm3WRmibuiGXNOIW/QnsFu/2wCSii556fT/kNcvcCKu8TEZ9MbVdOJ0B+4SpLcy1akLvu5qEPtZvOftei1lxiPYbjg0l5Akos7t5gpF6uxflIN18kBcE2QPLZ/o7JuLwYvgH7lHyNE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>";
        public class SocketArgs
        {
            public Socket m_Socket;
            public IConnection m_Connection;
            public bool IsConnected;
            public Semaphore m_Semaphore;
            public TCPClient m_NetClient;
            public void Close()
            {
                if (!IsConnected) return;
                IsConnected = false;
                m_Semaphore?.Release();
                m_Connection?.Close();
                m_Connection = null;
                m_Semaphore = null;
                IOCPConn.Close(m_Socket);
                m_Socket = null;
            }
        }

        SocketArgs m_SocketArgs;
        public IHandlerMessage Handler { get; private set; }

        public string RsaPub { get; private set; }
        public byte CCCFlag { get; private set; }
        public bool Running
        {
            get
            {
                return m_SocketArgs != null;
            }
        }

        public bool Connected
        {
            get
            {
                if (m_SocketArgs == null)
                {
                    return false;
                }
                return m_SocketArgs.IsConnected;
            }
        }

        public IPEndPoint RemoteEndPoint { get; private set; }
        public IPack Pack { get; private set; } = new CPack();
        private TCPClient(IHandlerMessage handler)
        {
            Handler = handler;
        }

        public static TCPClient Create<T>() where T : class, IHandlerMessage, new()
        {
            var handler = Activator.CreateInstance<T>();
            return new TCPClient(handler);
        }

        public static TCPClient Create(IHandlerMessage handler)
        {
            return new TCPClient(handler);
        }
        public void SetPack(IPack pack)
        {
            if (Running) { Debug.LogError("Please set before connect."); return; }
            Pack = pack;
        } 
        public Semaphore Connect(string addr, int port)
        {
            return Connect(addr, port, null);
        }
        public Semaphore Connect(string addr, int port, string pub)
        {
            return Connect(addr, port, pub, true, NetHelper.CCC_Compress | NetHelper.CCC_Crypto | NetHelper.CCC_Crc);
        }
        public Semaphore Connect(string addr, int port, string pub, bool async, byte cccflag)
        {
            var ip = NetHelper.ParseIpAddressV6(addr, port);
            if (ip == null)
            {
                throw new Exception("Unknown addr = " + addr);
            }
            Close();
            if (string.IsNullOrEmpty(pub)) pub = DefaultRsaPub;

            if (!Rsa.CheckIsPub(pub))
            {
                throw new Exception(string.Format("RsaPub error {0}", pub));
            }
            RsaPub = pub;
            CCCFlag = cccflag;
            RemoteEndPoint = new IPEndPoint(ip, port);
            m_SocketArgs = new SocketArgs();
            m_SocketArgs.m_NetClient = this;
            var semaphore = new Semaphore(0, 1024);
            m_SocketArgs.m_Socket = new Socket(ip.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
            //设置为不延迟，有数据立即发送
            m_SocketArgs.m_Socket.NoDelay = true;
            m_SocketArgs.m_Semaphore = semaphore;
            Debug.Log(string.Format("NetClient Connect {0}:{1}", ip, port));
            if (async)
            {
                m_SocketArgs.m_Socket.BeginConnect(RemoteEndPoint, new AsyncCallback(ConnectCallback), m_SocketArgs);
                //var asyncResult = m_SocketArgs.m_Socket.BeginConnect(RemoteEndPoint, new AsyncCallback(ConnectCallback), m_SocketArgs);

                //if (timeout > 0)
                //{
                //    var thread = new Thread(() =>
                //    {
                //        asyncResult.AsyncWaitHandle.WaitOne(new TimeSpan(0, 0, timeout));
                //        var args = (SocketArgs)asyncResult.AsyncState;
                //        if (args.m_Socket != null && !args.m_Socket.Connected && !asyncResult.IsCompleted)
                //        {
                //            Debug.Log("BeginConnect TimeOut");
                //            ConnectCallback(asyncResult);
                //        }
                //    });

                //    thread.IsBackground = true;
                //    thread.Start();
                //}

            }
            else
            {
                m_SocketArgs.m_Socket.Connect(RemoteEndPoint);
                Debug.Log(string.Format("NetClient Connect {0}", "OK"));
                OnConnected(m_SocketArgs);
            }
            return semaphore;
        }

        void OnConnected(SocketArgs args)
        {
            args.m_Semaphore?.Release();
            if (m_SocketArgs != args) return;
            var kiv = AesKeyIV.GenKeyIV();
            AesKeyIV.SendAesKeyIVRsa(args.m_Socket, args.m_NetClient.RsaPub, kiv);
            new AesKeyIVAes(args, KIVHandleConnected, KIVHandleClose, kiv);
        }

        void KIVHandleClose(SocketArgs args)
        {
            args.Close();
            if (args != m_SocketArgs) return;
            Close();
        }
        void KIVHandleConnected(SocketArgs args, byte[] kiv)
        {
            if (args != m_SocketArgs) return;
            args.IsConnected = true;
            args.m_Connection = new TCPConnection(args.m_Socket, Handler, Pack, ConnectionClose, kiv, args.m_NetClient.CCCFlag);
            args.m_Connection.Initialize();
        }

        void ConnectionClose(IConnection conn)
        {
            if (conn == m_SocketArgs?.m_Connection)
            {
                Close();
            }
        }
        void ConnectCallback(IAsyncResult ar)
        {
            //Debug.Log("NetClient ConnectCallback");
            var args = ar.AsyncState as SocketArgs;
            if (args != m_SocketArgs)
            {
                try
                {
                    args.m_Socket?.EndConnect(ar);
                    args.m_Socket?.Close();
                }
                catch (Exception e)
                {
                    Debug.LogException(e);
                }
                return;
            }
            try
            {
                var s = args.m_Socket;
                s.EndConnect(ar);
                OnConnected(args);
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                Close();
                Handler.HandleConnected(false);
                Handler.HandleClose();
            }
        }
        public void Close()
        {
            if (m_SocketArgs != null)
            {
                m_SocketArgs.Close();
                m_SocketArgs = null;
            }
        }
    }
}

#endif