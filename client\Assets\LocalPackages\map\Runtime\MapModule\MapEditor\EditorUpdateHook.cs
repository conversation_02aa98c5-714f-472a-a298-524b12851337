﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;

namespace TFW.Map
{
    [InitializeOnLoad]
    static class EditorUpdateHook
    {
        static EditorUpdateHook() {
            EditorApplication.update -= OnUpdate;
            EditorApplication.update += OnUpdate;
        }

        static void OnUpdate()
        {
            var map = Map.currentMap as EditorMap;
            if (map != null)
            {
                map.UpdateLayerInEditor();
            }
        }
    }
}

#endif