﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //拼接地表层使用的数据
    public partial class BlendTerrainLayerData : MapObjectLayerData
    {
        public BlendTerrainLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, BlendTerrainTileData[] tiles, string combinedTexturePropertyName, string groundTileAtlasSettingGuid, bool generateMeshCollider, bool getGroundHeightInGame, bool optimizeMesh) : base(header, config, map, null)
        {
            if (tiles == null)
            {
                int n = header.rows * header.cols;
                tiles = new BlendTerrainTileData[n];
            }
            mTiles = tiles;
            mUseFrameAction = false;
            mGenerateMeshCollider = generateMeshCollider;
            mGetGroundHeightInGame = getGroundHeightInGame;
            mOptimizeMesh = optimizeMesh;

#if UNITY_EDITOR
            mGroundTileAtlasSetting = AssetDatabase.LoadAssetAtPath<GroundTileAtlasSetting>(AssetDatabase.GUIDToAssetPath(groundTileAtlasSettingGuid));
            mCombinedTexturePropertyName = combinedTexturePropertyName;
            mIsTileChanged = new bool[mTiles.Length];
#endif
            if (map.isEditorMode)
            {
                mLastViewport = map.viewport;
            }
            else
            {
                mLastViewport = new Rect(-10000, -10000, 0, 0);
            }
            mFrameActionQueueIndex = map.AddFrameActionQueue(MapCoreDef.MAP_TERRAIN_TILE_QUEUE);
        }

        public override void OnDestroy()
        {
            base.OnDestroy();

            ClearAllTiles();
        }

        //计算如果push tile了会得到一个什么tile index
        public bool GetPushTileResult(int x, int y, int tileIndex, int tileType, bool changeRandomTile, List<int> excludedTileIndices, out int combinedTileIndex, out int subTypeIndex)
        {
            combinedTileIndex = tileIndex;
            subTypeIndex = 0;
            if (x >= 0 && x < mCols && y >= 0 && y < mRows && tileIndex > 0)
            {
                var idx = y * mCols + x;
                if (mTiles[idx] != null)
                {
                    combinedTileIndex = mTiles[idx].GetPushTileResult(tileIndex, tileType, changeRandomTile, excludedTileIndices, out subTypeIndex);
                }
                else
                {
                    subTypeIndex = BlendTerrainTileData.CalculateSubTypeIndex(tileType, tileIndex, map);
                }
                return true;
            }
            return false;
        }

        //拼接一个tile
        //x: tile的x坐标
        //y: tile的y坐标
        //tileIndex: tile使用的拼接prefab的索引
        //tileType: tile使用的拼接图集
        //setAbsoluteValue: 是否直接将tileIndex和tileType当成拼接后的值,而不考虑这个tile目前的情况
        public BlendTerrainTileData PushTile(int x, int y, int tileIndex, int tileType, int subTypeIndex, bool setAbsoluteValue, bool changeRandomTile, float[] heights, bool hasHeightData)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows && tileIndex > 0)
            {
                var idx = y * mCols + x;

#if UNITY_EDITOR
                mIsTileChanged[idx] = true;
#endif

                bool createNewTile = false;
                BlendTerrainTileData tile = mTiles[idx];
                if (mTiles[idx] == null)
                {
                    //创建一个新tile
                    var tileID = map.nextCustomObjectID;
                    var pos = FromCoordinateToWorldPosition(x, y);
                    tile = new BlendTerrainTileData(tileID, map, pos, null, tileIndex, tileType, subTypeIndex, heights, hasHeightData);
                    createNewTile = true;
                }

                if (setAbsoluteValue)
                {
                    //设置绝对值
                    tile.SetTile(tileIndex, tileType, subTypeIndex);
                }
                else
                {
                    //和当前的tile拼接
                    tile.PushTile(tileIndex, tileType, changeRandomTile);
                }
                //获取拼接后的tile使用的模型配置的id
                var modelTemplate = GetModelTemplate(tile.id, tile.type, tile.index, tile.subTypeIndex, x, y);
                if (modelTemplate == null)
                {
                    Debug.LogError($"terrain tile model template {tile.type}_{tile.index} not found");
                }
                tile.SetModelTemplate(modelTemplate);

                if (createNewTile)
                {
                    AddObjectData(tile);
                }
                return tile;
            }
            return null;
        }

        //反向拼接一个tile
        public int PopTile(int x, int y, int tileIndex)
        {
            int newTileIndex = 0;
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                var idx = y * mCols + x;

#if UNITY_EDITOR
                mIsTileChanged[idx] = true;
#endif

                if (mTiles[idx] != null)
                {
                    //反向拼接
                    mTiles[idx].PopTile(tileIndex);
                    newTileIndex = mTiles[idx].index;
                    if (newTileIndex != 0)
                    {
                        //得到反向拼接后的模型配置id
                        var modelTemplate = GetModelTemplate(mTiles[idx].id, mTiles[idx].type, mTiles[idx].index, mTiles[idx].subTypeIndex, x, y);
                        if (modelTemplate == null)
                        {
                            //是一个无效模型配置id,直接删除tile
                            //出现这种情况是因为允许在编辑器种使用不完整的prefab图集
                            //一个完整的prefab图集应该包含15种tile的拼接情况,但是某些情况下可以只要求有第15个tile拼接信息
                            newTileIndex = 0;
                        }
                        else
                        {
                            mTiles[idx].SetModelTemplate(modelTemplate);
                        }
                    }

                    if (newTileIndex == 0)
                    {
                        //tile为空地了,直接删除tile
                        RemoveObjectData(mTiles[idx].id);
                    }
                }
            }
            return newTileIndex;
        }

        protected override void OnAddObjectData(IMapObjectData data)
        {
            var coord = FromWorldPositionToCoordinate(data.GetPosition());
            var idx = coord.y * mCols + coord.x;
            mTiles[idx] = data as BlendTerrainTileData;
            //这个tile是否在视野内
            bool isVisible = false;
            if (!isLoading)
            {
                isVisible = IsInViewRange(coord.x, coord.y, map.viewport);
            }
            SetObjectActiveImpl(data, isVisible);
        }

        //删除地图对象的数据
        protected override void OnRemoveObjectData(IMapObjectData data)
        {
            var coord = FromWorldPositionToCoordinate(data.GetPosition());
            var idx = coord.y * mCols + coord.x;
            mTiles[idx] = null;
        }

        //删除tile的数据
        public void ClearTile(int x, int y)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                var idx = y * mCols + x;

#if UNITY_EDITOR
                mIsTileChanged[idx] = true;
#endif

                if (mTiles[idx] != null)
                {
                    RemoveObjectData(mTiles[idx].id);
                }
            }
        }

        public IMapObjectData GetObject(int x, int y)
        {
            return GetTile(x, y);
        }

        //获取tile的数据
        public BlendTerrainTileData GetTile(int x, int y)
        {
            BlendTerrainTileData tile = null;
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                var idx = y * mCols + x;
                tile = mTiles[idx];
            }
            return tile;
        }

        //根据tile的拼接情况来获取地表tile使用哪个prefab
        ModelTemplate GetModelTemplate(int tileDataID, int tileType, int tileIndex, int subTypeIndex, int x, int y)
        {
            if (tileIndex == 0)
            {
                return null;
            }
            var prefabManager = map.data.terrainPrefabManager;
            string prefabPath = prefabManager.GetPrefabByID(tileType, tileIndex, subTypeIndex);
            
            if (!string.IsNullOrEmpty(prefabPath))
            {
                var modelTemplate = map.GetOrCreateModelTemplate(tileDataID, prefabPath, false, MapModule.preloadGroundLayerTile);
                Debug.Assert(modelTemplate != null);
                return modelTemplate;
            }
            return null;
        }

        public override bool isGameLayer => true;

        protected override bool IsAbleToAdd(IMapObjectData objectData)
        {
            return true;
        }

        protected override void OnRotationChange(IMapObjectData objectData)
        {
            Debug.Assert(false, "terrain tile rotation can't change");
        }

        protected override void OnScaleChange(IMapObjectData objectData)
        {
            Debug.Assert(false, "terrain tile scale can't change");
        }

        //改变layer的大小
        public void Resize(int newSize, bool useLayerOffset)
        {
            //only valid for map editor
            Debug.Assert(map.isEditorMode == true);
            int oldSize = horizontalTileCount;
            if (newSize > oldSize)
            {
                int offset = (newSize - oldSize) / 2;
                BlendTerrainTileData[] newTiles = new BlendTerrainTileData[newSize * newSize];
                for (int i = 0; i < oldSize; ++i)
                {
                    for (int j = 0; j < oldSize; ++j)
                    {
                        //只是简单的移动tile,并不新增加tile
                        int oldIndex = i * oldSize + j;
                        int newIndex = (i + offset) * newSize + j + offset;
                        newTiles[newIndex] = mTiles[oldIndex];
                    }
                }
                mTiles = newTiles;
                mRows = newSize;
                mCols = newSize;
                var mapWidth = map.mapWidth;
                var mapHeight = map.mapHeight;
                var layerWidth = newSize * mTileWidth;
                var layerHeight = newSize * mTileHeight;
                var ox = (mapWidth - layerWidth) * 0.5f;
                var oz = (mapHeight - layerHeight) * 0.5f;
                mLayerOrigin = new Vector3(ox, 0, oz);
            }
            else
            {
                int offset = (oldSize - newSize) / 2;
                BlendTerrainTileData[] newTiles = new BlendTerrainTileData[newSize * newSize];
                for (int i = 0; i < newSize; ++i)
                {
                    for (int j = 0; j < newSize; ++j)
                    {
                        //只是简单的移动tile,并不新增加tile
                        int newIndex = i * newSize + j;
                        int oldIndex = (i + offset) * oldSize + j + offset;
                        newTiles[newIndex] = mTiles[oldIndex];
                    }
                }
                mTiles = newTiles;
                mRows = newSize;
                mCols = newSize;
                var mapWidth = map.mapWidth;
                var mapHeight = map.mapHeight;
                var layerWidth = newSize * mTileWidth;
                var layerHeight = newSize * mTileHeight;
                var ox = (mapWidth - layerWidth) * 0.5f;
                var oz = (mapHeight - layerHeight) * 0.5f;
                mLayerOrigin = new Vector3(ox, 0, oz);
            }

            if (!useLayerOffset)
            {
                mLayerOrigin = Vector3.zero;
            }
        }

        public override IMapObjectData FindObjectAtPosition(Vector3 pos, float radius)
        {
            throw new System.NotImplementedException();
        }

        public override void RefreshObjectsInViewport()
        {
            UpdateViewport(map.viewport, 0);
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }

            if (mLastViewport != newViewport)
            {
                var oldViewRect = GetViewRect(mLastViewport);
                var newViewRect = GetViewRect(newViewport);
                UpdateViewRect(oldViewRect, newViewRect);
            }

            mLastViewport = newViewport;

            return lodChanged;
        }

        void SetObjectVisibility(int x, int y, bool visible, int lod)
        {
            var objectData = GetObject(x, y);
            if (objectData != null)
            {
                SetObjectActive(objectData, visible, lod);
            }
        }

        protected bool UpdateViewRect(Rect2D oldViewRect, Rect2D newViewRect)
        {
            if (oldViewRect != newViewRect)
            {
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (!newViewRect.Contains(j, i))
                        {
                            //物体不在新的视野中,隐藏它
                            SetObjectVisibility(j, i, false, currentLOD);
                        }
                    }
                }

                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (!oldViewRect.Contains(j, i))
                        {
                            //物体不在旧的视野中,显示它
                            SetObjectVisibility(j, i, true, currentLOD);
                        }
                    }
                }

                return true;
            }

            return false;
        }

        public Rect2D GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new Rect2D(startCoord.x, startCoord.y, endCoord.x, endCoord.y);
        }

        public bool IsInViewRange(int x, int y, Rect viewport)
        {
            var viewRect = GetViewRect(viewport);
            return viewRect.Contains(x, y);
        }

        public virtual bool IsOneTileLOD(int lod) { return false; }

        public override void GetObjectInBounds(Bounds bounds, List<IMapObjectData> objects)
        {
            Debug.Assert(false, "todo");
        }

        public void SetTileResolution(int tileX, int tileY, int resolution)
        {
            var tile = GetTile(tileX, tileY);
            if (tile != null)
            {
                tile.SetResolution(resolution);
            }
        }

        public void SetHeight(int tileX, int tileY, int minX, int minY, int maxX, int maxY, int resolution, List<float> heights, bool dontChangeEdgeVertexHeight)
        {
            var tile = GetTile(tileX, tileY);
            if (tile != null)
            {
                tile.SetHeight(minX, minY, maxX, maxY, resolution, heights, 0, dontChangeEdgeVertexHeight);
            }
        }

        public void SetHeight(int tileX, int tileY, int minX, int minY, int maxX, int maxY, int resolution, float height, bool dontChangeEdgeVertexHeight)
        {
            var tile = GetTile(tileX, tileY);
            if (tile != null)
            {
                tile.SetHeight(minX, minY, maxX, maxY, resolution, null, height, dontChangeEdgeVertexHeight);
            }
        }

        public void ResetIsChangedFlags()
        {
#if UNITY_EDITOR
            for (int i = 0; i < mIsTileChanged.Length; ++i)
            {
                mIsTileChanged[i] = false;
            }
#endif
        }

        public void ClearAllTiles()
        {
            if (mTiles != null)
            {
                for (int i = 0; i < mTiles.Length; ++i)
                {
                    if (mTiles[i] != null)
                    {
                        map.DestroyObject(mTiles[i]);
                        mTiles[i] = null;
                    }
                }
            }
        }

        public void SetSize(int newHorizontalTileCount, int newVerticalTileCount) {
#if UNITY_EDITOR
            ClearAllTiles();
            mTiles = new BlendTerrainTileData[newHorizontalTileCount * newVerticalTileCount];
            mIsTileChanged = new bool[mTiles.Length];
            for (int i = 0; i < mIsTileChanged.Length; ++i)
            {
                mIsTileChanged[i] = true;
            }
            mRows = newVerticalTileCount;
            mCols = newHorizontalTileCount;
#endif
        }

        public Rect lastViewport { get { return mLastViewport; } }
        public bool useGeneratedLOD { set { mUseGeneratedLOD = value; } get { return mUseGeneratedLOD; } }
        //是否使用和地表配套的front layer物体
        public bool useDecorationObject { set { mUseDecorationObject = value; } get { return mUseDecorationObject; } }
#if UNITY_EDITOR
        public string combinedTexturePropertyName { get { return mCombinedTexturePropertyName; } set { mCombinedTexturePropertyName = value; } }
        public GroundTileAtlasSetting groundTileAtlasSetting { get { return mGroundTileAtlasSetting; } set { mGroundTileAtlasSetting = value; } }
#endif
        public BlendTerrainTileData[] tiles { get { return mTiles; } }
        public bool getGroundHeightInGame { set { mGetGroundHeightInGame = value; } get { return mGetGroundHeightInGame; } }
        public bool generateMeshCollider { set { mGenerateMeshCollider = value; } get { return mGenerateMeshCollider; } }
        public bool optimizeMesh { set { mOptimizeMesh = value; } get { return mOptimizeMesh; } }

        //地表层上所有的tile数据,可以从编辑器导出,也可以在游戏运行时动态设置
        protected BlendTerrainTileData[] mTiles;
        protected Rect mLastViewport;
        protected float mLastZoom;
        protected int mFrameActionQueueIndex;
        bool mUseGeneratedLOD = true;
        bool mUseDecorationObject = false;
        bool mGetGroundHeightInGame = false;
        bool mGenerateMeshCollider = false;
        bool mOptimizeMesh = true;

#if UNITY_EDITOR
        GroundTileAtlasSetting mGroundTileAtlasSetting;
        string mCombinedTexturePropertyName;
        bool[] mIsTileChanged;
#endif
    }
}
