﻿ 



 
 

//created by wzw at 2019/11/25

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class CollisionGameObjects
    {
        public CollisionGameObjects(PrefabOutlineType type, List<Vector3> vertices, float radius, Color defaultColor)
        {
            var shader = Shader.Find("SLGMaker/ColorNoDepthTest");
            if (shader == null)
            {
                shader = MapModuleResourceMgr.LoadResource<Shader>($"Packages/com.tfw.map/Runtime/MapModuleRes/EditorRes/ColorNoDepthTest.shader");
            }
            if (shader == null)
            {
                shader = Shader.Find("Unlit/Color");
            }
            mMtl = new Material(shader);
            mMtl.color = defaultColor;
            mMtl.renderQueue = 4100;

            RecreateObjects(type, vertices, radius);
        }

        public void OnDestroy()
        {
            if (mMtl != null)
            {
                GameObject.DestroyImmediate(mMtl);
            }

            for (int i = 0; i < mVertexObject.Count; ++i)
            {
                GameObject.DestroyImmediate(mVertexObject[i]);
            }
            mVertexObject.Clear();
            GameObject.DestroyImmediate(mOutlineObject);
            mOutlineObject = null;
        }

        void RecreateObjects(PrefabOutlineType type, List<Vector3> vertices, float radius)
        {
            Transform parent = null;
            if (mOutlineObject != null)
            {
                parent = mOutlineObject.transform.parent;
                GameObject.DestroyImmediate(mOutlineObject);
                mOutlineObject = null;
            }
            for (int i = 0; i < mVertexObject.Count; ++i)
            {
                GameObject.DestroyImmediate(mVertexObject[i]);
            }
            mVertexObject.Clear();

            mOutlineObject = new GameObject(type.ToString());
            mOutlineObject.SetActive(false);

            if (vertices.Count > 0)
            {
                var mesh = CreatePolygonMesh(vertices);
                var renderer = mOutlineObject.AddComponent<MeshRenderer>();
                renderer.sharedMaterial = mMtl;
                var meshFilter = mOutlineObject.AddComponent<MeshFilter>();
                meshFilter.sharedMesh = mesh;

                mOutlineObject.hideFlags = HideFlags.HideInHierarchy;

                int n = vertices.Count;
                for (int i = 0; i < n; ++i)
                {
                    var vertexObj = CreateVertex(type, vertices, i, radius);
                    mVertexObject.Add(vertexObj);
                }
            }

            if (parent != null)
            {
                SetParent(parent);
            }
        }

        public void Show(bool show)
        {
            if (mOutlineObject != null)
            {
                mOutlineObject.SetActive(show);
                for (int i = 0; i < mVertexObject.Count; ++i)
                {
                    if (mVertexObject[i] != null)
                    {
                        mVertexObject[i].SetActive(show);
                    }
                }
            }
        }

        public void SetParent(Transform parent)
        {
            for (int i = 0; i < mVertexObject.Count; ++i)
            {
                mVertexObject[i].transform.SetParent(parent);
            }
            mOutlineObject.transform.SetParent(parent);
        }

        void UpdatePolyline()
        {
            var meshFilter = mOutlineObject.GetComponent<MeshFilter>();
            if (meshFilter != null)
            {
                var mesh = meshFilter.sharedMesh;
                Vector3[] vertices = new Vector3[mVertexObject.Count];
                for (int i = 0; i < mVertexObject.Count; ++i)
                {
                    vertices[i] = mVertexObject[i].transform.position;
                }
                meshFilter.sharedMesh.Clear();
                meshFilter.sharedMesh.vertices = vertices;
                var indices = new int[vertices.Length + 1];
                for (int i = 0; i < vertices.Length; ++i)
                {
                    indices[i] = i;
                }
                indices[vertices.Length] = 0;
                mesh.SetIndices(indices, MeshTopology.LineStrip, 0);
            }
        }

        Mesh CreatePolygonMesh(List<Vector3> vertices)
        {
            Vector3[] meshVertices = vertices.ToArray();

            Mesh mesh = new Mesh();
            mesh.vertices = meshVertices;
            var indices = new int[vertices.Count + 1];
            for (int i = 0; i < vertices.Count; ++i)
            {
                indices[i] = i;
            }
            indices[vertices.Count] = 0;
            mesh.SetIndices(indices, MeshTopology.LineStrip, 0);
            return mesh;
        }

        GameObject CreateVertex(PrefabOutlineType type, List<Vector3> vertices, int index, float radius)
        {
            var vertexObj = GameObject.CreatePrimitive(PrimitiveType.Quad);
            Utils.HideGameObject(vertexObj);
            vertexObj.SetActive(false);
            vertexObj.transform.position = vertices[index];
            vertexObj.transform.localScale = Vector3.one * radius * 2;
            vertexObj.transform.rotation = Quaternion.Euler(90, 0, 0);
            vertexObj.transform.parent = mOutlineObject.transform;
            vertexObj.name = string.Format("{0} vertex {1}", type.ToString(), index);
            var vertexRenderer = vertexObj.GetComponent<MeshRenderer>();
            vertexRenderer.sharedMaterial = mMtl;
            return vertexObj;
        }

        public void SetVertexRadius(float radius)
        {
            for (int i = 0; i < mVertexObject.Count; ++i)
            {
                mVertexObject[i].transform.localScale = Vector3.one * radius * 2;
            }
        }

        public void UpdateVertex(int index, Vector3 pos)
        {
            mVertexObject[index].transform.position = pos;

            UpdatePolyline();
        }

        public void Update(PrefabOutlineType type, List<Vector3> vertices, float radius)
        {
            if (vertices.Count != mVertexObject.Count)
            {
                RecreateObjects(type, vertices, radius);
            }

            for (int i = 0; i < mVertexObject.Count; ++i)
            {
                if (mVertexObject[i] != null)
                {
                    mVertexObject[i].transform.position = vertices[i];
                }
            }

            UpdatePolyline();
        }

        public void InsertVertex(PrefabOutlineType type, PolygonObjectData data, int index, Vector3 pos)
        {
            var obj = CreateVertex(type, data.GetOutlineVertices(type), index, data.displayRadius);
            mVertexObject.Insert(index, obj);

            UpdatePolyline();
        }

        public void RemoveVertex(PolygonObjectData data, int index)
        {
            Debug.Assert(index >= 0 && index < mVertexObject.Count);

            GameObject.DestroyImmediate(mVertexObject[index]);
            mVertexObject.RemoveAt(index);

            UpdatePolyline();
        }

        public void SetColor(Color color)
        {
            mMtl.color = color;
        }

        public void Revert()
        {
            Utils.ReverseList(mVertexObject);
        }

        Material mMtl;
        GameObject mOutlineObject;
        List<GameObject> mVertexObject = new List<GameObject>();
    }

    //多边形的模型
    public class PolygonObjectModel : ModelBase
    {
        public PolygonObjectModel(PolygonObjectData data, string name)
        {
            mGameObject = new GameObject(name);
            mCollisionGameObjects = new CollisionGameObjects[2];

            mCollisionGameObjects[0] = new CollisionGameObjects(PrefabOutlineType.NavMeshObstacle, data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle), data.displayRadius, data.color);
            mCollisionGameObjects[1] = new CollisionGameObjects(PrefabOutlineType.ObjectPlacementObstacle, data.GetOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle), data.displayRadius, data.color);
            for (int i = 0; i < 2; ++i)
            {
                if (mCollisionGameObjects[i] != null)
                {
                    mCollisionGameObjects[i].SetParent(mGameObject.transform);
                }
            }

            mCollisionGameObjects[1].Show(false);
        }

        public override void Release()
        {
            OnDestroy();
        }

        protected override void OnDestroy()
        {
            for (int i = 0; i < mCollisionGameObjects.Length; ++i)
            {
                mCollisionGameObjects[i].OnDestroy();
            }
            mCollisionGameObjects = null;
            if (mGameObject != null)
            {
                GameObject.DestroyImmediate(mGameObject);
                mGameObject = null;
            }
        }

        public virtual void SetVertexRadius(float radius)
        {
            for (int i = 0; i < 2; ++i)
            {
                mCollisionGameObjects[i].SetVertexRadius(radius);
            }
        }

        public void SetColor(PrefabOutlineType type, Color color)
        {
            mCollisionGameObjects[(int)type].SetColor(color);
        }

        public virtual void UpdateVertex(PrefabOutlineType type, int index, PolygonObjectData data)
        {
            mCollisionGameObjects[(int)type].UpdateVertex(index, data.GetVertexPos(type, index));
        }

        public virtual void Update(PolygonObjectData data)
        {
            for (int i = 0; i < mCollisionGameObjects.Length; ++i)
            {
                mCollisionGameObjects[i].Update((PrefabOutlineType)i, data.GetOutlineVertices((PrefabOutlineType)i), data.displayRadius);
            }
        }

        public void InsertVertex(PrefabOutlineType type, PolygonObjectData data, int index, Vector3 pos)
        {
            mCollisionGameObjects[(int)type].InsertVertex(type, data, index, pos);
        }

        public void RemoveVertex(PrefabOutlineType type, PolygonObjectData data, int index)
        {
            mCollisionGameObjects[(int)type].RemoveVertex(data, index);
        }

        public void Revert(PrefabOutlineType type)
        {
            mCollisionGameObjects[(int)type].Revert();
        }

        public virtual void Show(PrefabOutlineType type)
        {
            int idx = ((int)type + 1) % 2;
            mCollisionGameObjects[(int)type].Show(true);
            mCollisionGameObjects[idx].Show(false);
        }

        public virtual void Hide()
        {
            mCollisionGameObjects[0].Show(false);
            mCollisionGameObjects[1].Show(false);
        }

        CollisionGameObjects[] mCollisionGameObjects;
    }
}