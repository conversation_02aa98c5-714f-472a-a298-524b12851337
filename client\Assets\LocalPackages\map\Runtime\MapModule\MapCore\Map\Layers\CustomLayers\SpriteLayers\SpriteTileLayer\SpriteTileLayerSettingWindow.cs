﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class SpriteTileLayerSettingWindow : EditorWindow
    {
        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Layer Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Layer Height", mLayerHeight);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            mTileWidth = EditorGUILayout.FloatField("Tile Width", mTileWidth);
            mTileHeight = EditorGUILayout.FloatField("Tile Height", mTileHeight);
            GUILayout.EndHorizontal();

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Create"))
            {
                var map = Map.currentMap;
                float tileWidth = GetTileWidth();
                float tileHeight = GetTileHeight();
                bool valid = CheckParameter();
                if (valid)
                {
                    int horizontalTileCount = Mathf.CeilToInt(mLayerWidth / mTileWidth);
                    int verticalTileCount = Mathf.CeilToInt(mLayerHeight / mTileHeight);
                    var ox = (map.mapWidth - mLayerWidth) * 0.5f;
                    var oz = (map.mapHeight - mLayerHeight) * 0.5f;
                    var offset = new Vector3(ox, 0, oz);
                    var layerData = new config.SpriteTileLayerData(Map.currentMap.nextCustomObjectID, "Sprite_Tile_Layer", offset, null, verticalTileCount, horizontalTileCount, mTileWidth, mTileHeight, mGridType, null);
                    var layer = new SpriteTileLayer(map);
                    layer.Load(layerData, null, false);
                    map.AddMapLayer(layer);

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        float GetTileWidth()
        {
            if (mGridType == GridType.Rectangle)
            {
                return mTileWidth;
            }
            return mRadius * Mathf.Cos(30 * Mathf.Deg2Rad) * 2.0f;
        }

        float GetTileHeight()
        {
            if (mGridType == GridType.Rectangle)
            {
                return mTileHeight;
            }
            return mRadius * 2.0f;
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0 ||
                mTileWidth <= 0 || mTileHeight <= 0)
            {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public float mTileWidth = 1.0f;
        public float mTileHeight = 1.0f;
        public float mRadius = 1.0f;
        public GridType mGridType = GridType.Rectangle;
    }
}

#endif