﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //相机移动并缩放到目标点
    public class CameraMoveToTargetInstantly : ZoomActionBase
    {
        public CameraMoveToTargetInstantly(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;

            Init();
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
        }

        public void MoveTo(Vector3 targetPos, System.Action onCameraReachTarget)
        {
            if (enabled == false)
            {
                Reset();

                enabled = true;
                mEndPos = targetPos;
                mCameraReachTarget = onCameraReachTarget;
            }
        }

        public override Vector3 GetTargetPosition()
        {
            isFinished = true;
            MapCameraMgr.UpdateRotateCenter();
            return mEndPos;
        }

        public override void OnFinishImpl()
        {
            if (mCameraReachTarget != null)
            {
                mCameraReachTarget();
            }
        }

        //相机最终的目标位置
        Vector3 mEndPos;
        System.Action mCameraReachTarget;
    }
}