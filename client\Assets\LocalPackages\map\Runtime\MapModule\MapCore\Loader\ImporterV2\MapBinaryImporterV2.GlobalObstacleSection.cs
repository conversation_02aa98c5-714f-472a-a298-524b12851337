﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadGlobalObstacles(BinaryReader reader)
        {
            reader.BaseStream.Position = GetSectionDataStartPosition(MapDataSectionType.GlobalObstacles);
            int version = reader.ReadInt32();

            //--------------------version 1 start--------------------------
            var globalObstacleManager = new config.MapGlobalObstacleManager();
            mEditorData.globalObstacleManager = globalObstacleManager;
            globalObstacleManager.obstacleMaterialPath = Utils.ReadString(reader);
            globalObstacleManager.obstacleVertices = Utils.ReadVector3Array(reader);
            globalObstacleManager.obstacleIndices = Utils.ReadIntArray(reader);
            //--------------------version 1 end-----------------------------
            //--------------------version 2 start--------------------------
            if (version >= 2)
            {
                int gridObstacleCount = reader.ReadInt32();
                if (gridObstacleCount > 0)
                {
                    globalObstacleManager.gridObstacles = reader.ReadBytes(gridObstacleCount);
                }
                globalObstacleManager.gridSize = reader.ReadSingle();
            }
            //--------------------version 2 end--------------------------
        }
    }
}
