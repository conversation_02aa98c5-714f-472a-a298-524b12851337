%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: RadialUVDistortion
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=13706\n234;92;1307;805;1057.718;-728.6741;2.742884;True;False\nNode;AmplifyShaderEditor.CommentaryNode;59;277.4049,1612.7;Float;False;1535;395;;11;47;50;53;52;51;48;55;54;58;56;57;<PERSON>
    Panner;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;27;-1156.5,-315;Float;False;1765;958;;20;19;12;15;1;11;13;16;18;17;3;5;8;9;10;21;23;24;25;80;81;Normal
    map 1 Panner/Size;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;46;-1152,800;Float;False;1692;591.2493;;13;32;34;39;38;36;33;31;29;70;69;78;79;86;Radial
    Math;1,1,1,1;0;0\nNode;AmplifyShaderEditor.PannerNode;18;-218.5,203;Float;False;3;0;FLOAT2;0,0;False;2;FLOAT2;1,0;False;1;FLOAT;1.0;False;1;FLOAT2\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;16;-499.5,260;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.ComponentMaskNode;3;-713.5,-240;Float;False;True;False;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.DynamicAppendNode;10;16,-80;Float;False;FLOAT2;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT2\nNode;AmplifyShaderEditor.PannerNode;19;-213.5,487;Float;False;3;0;FLOAT2;0,0;False;2;FLOAT2;0,1;False;1;FLOAT;1.0;False;1;FLOAT2\nNode;AmplifyShaderEditor.DynamicAppendNode;24;271.5,348;Float;False;FLOAT2;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT2\nNode;AmplifyShaderEditor.ComponentMaskNode;23;10.5,476;Float;False;False;True;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;17;-518.5,501;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.ComponentMaskNode;5;-709.5,65;Float;False;False;True;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.ComponentMaskNode;21;-0.5,205;Float;False;True;False;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.TextureCoordinatesNode;81;-515.3643,364.2482;Float;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;FLOAT;FLOAT;FLOAT;FLOAT\nNode;AmplifyShaderEditor.SimpleAddOpNode;25;454.5,190;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0.0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.PannerNode;54;1156.405,1662.7;Float;False;3;0;FLOAT2;0,0;False;2;FLOAT2;1,0;False;1;FLOAT;1.0;False;1;FLOAT2\nNode;AmplifyShaderEditor.PannerNode;55;1151.405,1851.699;Float;False;3;0;FLOAT2;0,0;False;2;FLOAT2;0,1;False;1;FLOAT;1.0;False;1;FLOAT2\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;52;919.5789,1859.739;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.TextureCoordinatesNode;80;-490.1459,-126.2656;Float;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;FLOAT;FLOAT;FLOAT;FLOAT\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;8;-168,-227;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;9;-157,48;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.FractNode;38;208,944;Float;False;1;0;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.FunctionInput;29;-736,960;Float;False;UV
    Channel;2;6;False;1;0;FLOAT2;0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.SimpleDivideOpNode;36;24.17026,866.1562;Float;False;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.TextureCoordinatesNode;78;-976,1024;Float;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;FLOAT;FLOAT;FLOAT;FLOAT\nNode;AmplifyShaderEditor.TauNode;70;-134.649,977.495;Float;False;0;1;FLOAT\nNode;AmplifyShaderEditor.Vector2Node;79;-1136,1040;Float;False;Constant;_Vector1;Vector
    1;0;0;2,2;0;3;FLOAT2;FLOAT;FLOAT\nNode;AmplifyShaderEditor.ATan2OpNode;34;-139.461,862.9785;Float;False;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;67;2008.955,1176.914;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.FunctionInput;47;327.4049,1740.699;Float;False;Ring
    panner speed;2;5;False;1;0;FLOAT2;1,1;False;1;FLOAT2\nNode;AmplifyShaderEditor.ComponentMaskNode;63;1601.903,863.11;Float;False;True;True;False;False;1;0;COLOR;0,0,0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;64;1860.937,863.1101;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.ComponentMaskNode;50;572.5789,1870.739;Float;False;False;True;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.Vector2Node;69;-750.9933,1135.168;Float;False;Constant;_Vector0;Vector
    0;0;0;1,1;0;3;FLOAT2;FLOAT;FLOAT\nNode;AmplifyShaderEditor.SimpleAddOpNode;66;2122.934,960.8027;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.ComponentMaskNode;57;1396.404,1844.699;Float;False;False;True;True;True;1;0;FLOAT2;0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.WireNode;62;881.3285,841.1897;Float;False;1;0;FLOAT2;0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.ComponentMaskNode;56;1395.404,1666.7;Float;False;True;False;True;True;1;0;FLOAT2;0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.ComponentMaskNode;48;556.5789,1678.739;Float;False;True;False;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.SamplerNode;61;1273.3,858.6689;Float;True;Property;_TextureSample0;Texture
    Sample 0;0;0;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;FLOAT;FLOAT;FLOAT;FLOAT\nNode;AmplifyShaderEditor.FunctionInput;11;-1097.5,295;Float;False;Noise
    map panner speed;2;2;False;1;0;FLOAT2;0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.FunctionInput;65;1581.182,996.3248;Float;False;Combined
    Noise Map Strength;1;3;False;1;0;FLOAT;1.0;False;1;FLOAT\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;31;-593.5078,943.7484;Float;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0.0,0;False;1;FLOAT2\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;51;929.3787,1738.541;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT\nNode;AmplifyShaderEditor.ComponentMaskNode;33;-384,944;Float;False;False;True;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.SimpleTimeNode;15;-773.5,372;Float;False;1;0;FLOAT;1.0;False;1;FLOAT\nNode;AmplifyShaderEditor.DynamicAppendNode;58;1645.404,1804.699;Float;False;FLOAT2;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT2\nNode;AmplifyShaderEditor.FunctionInput;1;-1106.5,-75;Float;False;Noise
    map size;2;1;False;1;0;FLOAT2;1,1;False;1;FLOAT2\nNode;AmplifyShaderEditor.SimpleTimeNode;53;652.579,1790.739;Float;False;1;0;FLOAT;1.0;False;1;FLOAT\nNode;AmplifyShaderEditor.DynamicAppendNode;39;339.0049,1105.292;Float;False;FLOAT2;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT2\nNode;AmplifyShaderEditor.ComponentMaskNode;32;-384,864;Float;False;True;False;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.FunctionInput;60;974.1886,744.1376;Float;False;Noise
    Map;9;0;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D\nNode;AmplifyShaderEditor.LengthOpNode;86;-146.1864,1204.794;Float;False;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.ComponentMaskNode;12;-814,240;Float;False;True;False;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.FunctionInput;68;1781.573,1218.733;Float;False;Texture
    Coordination;2;4;False;1;0;FLOAT2;1,1;False;1;FLOAT2\nNode;AmplifyShaderEditor.ComponentMaskNode;13;-800,512;Float;False;False;True;True;True;1;0;FLOAT2;0,0;False;1;FLOAT\nNode;AmplifyShaderEditor.FunctionOutput;0;2475.644,909.1371;Float;False;True;UV
    Output;0;1;0;FLOAT2;0,0;False;0\nWireConnection;18;0;81;0\nWireConnection;18;1;16;0\nWireConnection;16;0;12;0\nWireConnection;16;1;15;0\nWireConnection;3;0;1;0\nWireConnection;10;0;8;0\nWireConnection;10;1;9;0\nWireConnection;19;0;81;0\nWireConnection;19;1;17;0\nWireConnection;24;0;21;0\nWireConnection;24;1;23;0\nWireConnection;23;0;19;0\nWireConnection;17;0;15;0\nWireConnection;17;1;13;0\nWireConnection;5;0;1;0\nWireConnection;21;0;18;0\nWireConnection;25;0;10;0\nWireConnection;25;1;24;0\nWireConnection;54;0;39;0\nWireConnection;54;1;51;0\nWireConnection;55;0;39;0\nWireConnection;55;1;52;0\nWireConnection;52;0;53;0\nWireConnection;52;1;50;0\nWireConnection;8;0;3;0\nWireConnection;8;1;80;1\nWireConnection;9;0;80;2\nWireConnection;9;1;5;0\nWireConnection;38;0;36;0\nWireConnection;29;0;78;0\nWireConnection;36;0;34;0\nWireConnection;36;1;70;0\nWireConnection;78;0;79;0\nWireConnection;34;0;32;0\nWireConnection;34;1;33;0\nWireConnection;67;0;68;0\nWireConnection;67;1;58;0\nWireConnection;63;0;61;0\nWireConnection;64;0;63;0\nWireConnection;64;1;65;0\nWireConnection;50;0;47;0\nWireConnection;66;0;64;0\nWireConnection;66;1;67;0\nWireConnection;57;0;55;0\nWireConnection;62;0;25;0\nWireConnection;56;0;54;0\nWireConnection;48;0;47;0\nWireConnection;61;0;60;0\nWireConnection;61;1;62;0\nWireConnection;31;0;29;0\nWireConnection;31;1;69;0\nWireConnection;51;0;48;0\nWireConnection;51;1;53;0\nWireConnection;33;0;31;0\nWireConnection;58;0;56;0\nWireConnection;58;1;57;0\nWireConnection;39;0;38;0\nWireConnection;39;1;86;0\nWireConnection;32;0;31;0\nWireConnection;86;0;31;0\nWireConnection;12;0;11;0\nWireConnection;13;0;11;0\nWireConnection;0;0;66;0\nASEEND*/\n//CHKSM=6E93501F5310904D0620611C1184B482F2F90DC7"
  m_functionName: 
  m_description: "Radial UV Distortion originally created by:\nYoeri - Luos_83 - Vleer\r\n
    <EMAIL>"
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_nodeCategory: 3
  m_customNodeCategory: 
