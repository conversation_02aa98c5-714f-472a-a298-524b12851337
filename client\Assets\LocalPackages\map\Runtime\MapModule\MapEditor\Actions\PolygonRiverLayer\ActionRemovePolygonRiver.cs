﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class RiverSectionData
    {
        public void OnDestroy()
        {
            Object.DestroyImmediate(materialPropeties);
            materialPropeties = null;
        }

        public int id;
        public Color[] textureData;
        public List<Vector3> outline;
        //用来保存material的参数
        public Material materialPropeties;
    }

    class RiverSplitterData
    {
        public Vector3 startPos;
        public Vector3 endPos;
    }

    [Black]
    public class ActionRemovePolygonRiver : EditorAction
    {
        public ActionRemovePolygonRiver(int layerID, int dataID)
        {
            mLayerID = layerID;
            mDataID = dataID;
            var data = Map.currentMap.FindObject(dataID) as PolygonRiverData;
            var navMeshObstacleOutline = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            mTextureSize = data.textureSize;
            mHideLOD = data.hideLOD;
            mHeight = data.height;
            mGenerateRiverMaterial = data.generateRiverMaterial;
            mNavMeshObstacleOutline = new List<Vector3>(navMeshObstacleOutline.Count);
            mNavMeshObstacleOutline.AddRange(navMeshObstacleOutline);
            mRiverMaterialPath = data.materialPath;

            //sections
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            var sections = data.sections;
            for (int i = 0; i < sections.Count; ++i)
            {
                var sectionData = EditorUtils.CreateRiverSectionData(sections[i], layer.GetRiverMaterial(dataID, i));
                mSections.Add(sectionData);
            }

            //splitters
            var splitters = data.splitters;
            for (int i = 0; i < splitters.Count; ++i)
            {
                var splitterData = new RiverSplitterData();
                splitterData.startPos = splitters[i].startVertexPosition;
                splitterData.endPos = splitters[i].endVertexPosition;
                mSplitters.Add(splitterData);
            }
        }

        public override void OnDestroy()
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                mSections[i].OnDestroy();
            }
            mSections = null;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }

            //sections
            List<PolygonRiverSectionData> sections = new List<PolygonRiverSectionData>(mSections.Count);
            for (int i = 0; i < mSections.Count; ++i)
            {
                sections.Add(new PolygonRiverSectionData(mSections[i].id, mSections[i].outline, mSections[i].textureData, mTextureSize));
            }

            //splitters
            List<PolygonRiverSplitterData> splitters = new List<PolygonRiverSplitterData>(mSplitters.Count);
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                splitters.Add(new PolygonRiverSplitterData(mSplitters[i].startPos, mSplitters[i].endPos));
            }

            var data = new PolygonRiverData(mDataID, Map.currentMap, mNavMeshObstacleOutline, layer.displayVertexRadius, mTextureSize, mRiverMaterialPath, sections, splitters, mHideLOD, mHeight, mGenerateRiverMaterial);
            layer.AddObject(data);

            //restore material properties
            for (int i = 0; i < mSections.Count; ++i)
            {
                layer.SetRiverMaterialProperties(mDataID, i, mSections[i].materialPropeties);
            }

            return true;
        }

        int mLayerID;
        int mDataID;
        int mTextureSize;
        int mHideLOD;
        float mHeight;
        bool mGenerateRiverMaterial;
        string mRiverMaterialPath;
        List<Vector3> mNavMeshObstacleOutline;
        List<RiverSectionData> mSections = new List<RiverSectionData>();
        List<RiverSplitterData> mSplitters = new List<RiverSplitterData>();
    }
}

#endif