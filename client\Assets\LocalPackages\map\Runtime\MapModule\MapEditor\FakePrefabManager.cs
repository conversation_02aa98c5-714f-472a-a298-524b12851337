﻿ 



 
 

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public class FakePrefabManager
    {
        public class FakePrefabInfo
        {
            public Rect bounds;
            public string prefabPath;
        }

        public class FakePrefab
        {
            public List<FakePrefabInfo> childPrefabInfos = new List<FakePrefabInfo>();
            public GameObject gameObject;

            public void OnDestroy()
            {
                Utils.DestroyObject(gameObject);
                gameObject = null;
                childPrefabInfos = null;
            }
        }

        public void OnDestroy()
        {
            foreach (var p in mFakePrefabs)
            {
                p.Value.OnDestroy();
            }
            mFakePrefabs = null;
        }

        public FakePrefab LoadPrefab(string path)
        {
            FakePrefab prefab;
            mFakePrefabs.TryGetValue(path, out prefab);
            if (prefab != null)
            {
                return prefab;
            }

            prefab = null;
            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream != null)
            {
                BinaryReader reader = new BinaryReader(stream);

                int majorVersion = reader.ReadInt32();
                int minorVersion = reader.ReadInt32();

                var offset = new Vector2(Map.currentMap.frontTileSize * 0.5f, Map.currentMap.frontTileSize * 0.5f);

                prefab = new FakePrefab();
                prefab.gameObject = new GameObject(Utils.GetPathName(path, false));
                prefab.gameObject.SetActive(false);
                Utils.HideGameObject(prefab.gameObject);

                int prefabCount = reader.ReadInt32();
                prefab.childPrefabInfos = new List<FakePrefabInfo>(prefabCount);

                mFakePrefabs.Add(path, prefab);

                for (int i = 0; i < prefabCount; ++i)
                {
                    Vector3 pos = Utils.ReadVector3(reader);
                    Quaternion rot = Utils.ReadQuaternion(reader);
                    Vector3 scale = Utils.ReadVector3(reader);
                    string tag = Utils.ReadString(reader);
                    string childPrefabPath = Utils.ReadString(reader);
                    var childBounds = Utils.ReadRect(reader);

                    var childPrefab = MapModuleResourceMgr.LoadGameObject(childPrefabPath);
                    if (childPrefab != null)
                    {
                        childPrefab.transform.SetParent(prefab.gameObject.transform, false);
                        childPrefab.transform.localPosition = pos;
                        childPrefab.transform.localRotation = rot;
                        childPrefab.transform.localScale = scale;

                        prefab.childPrefabInfos.Add(new FakePrefabInfo()
                        {
                            bounds = childBounds,
                            prefabPath = childPrefabPath,
                        });
                    }
                    else
                    {
                        Debug.LogError($"child prefab {childPrefabPath} not found!");
                    }
                }

                reader.Close();
            }

            return prefab;
        }

        public GameObject LoadGameObject(string path)
        {
            var prefab = LoadPrefab(path);
            if (prefab != null)
            {
                var obj = GameObject.Instantiate<GameObject>(prefab.gameObject);
                obj.SetActive(true);
                obj.hideFlags = HideFlags.None;
                return obj;
            }
            return null;
        }

        public void CreateAsset(string filePath, List<ChildPrefabTransform> childPrefabs)
        {
#if UNITY_EDITOR
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.FakePrefabVersion.majorVersion);
            writer.Write(VersionSetting.FakePrefabVersion.minorVersion);

            var prefabList = childPrefabs;
            int validPrefabCount = 0;
            for (int k = 0; k < prefabList.Count; ++k)
            {
                var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabList[k].path);
                if (childPrefab != null)
                {
                    ++validPrefabCount;
                }
            }

            writer.Write(validPrefabCount);
            for (int k = 0; k < prefabList.Count; ++k)
            {
                var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabList[k].path);
                if (childPrefab != null)
                {
                    var pos = prefabList[k].position - new Vector3(Map.currentMap.frontTileSize * 0.5f, 0, Map.currentMap.frontTileSize * 0.5f);
                    var rot = prefabList[k].editorRotation;
                    var scale = prefabList[k].editorScaling;
                    var tag = prefabList[k].tag;

                    //optimize this later
                    Utils.WriteVector3(writer, pos);
                    Utils.WriteQuaternion(writer, rot);
                    Utils.WriteVector3(writer, scale);
                    Utils.WriteString(writer, tag);
                    Utils.WriteString(writer, prefabList[k].path);
                    Utils.WriteRect(writer, prefabList[k].localBoundsInPrefab);
                }
            }

            var data = stream.ToArray();
            File.WriteAllBytes(filePath, data);
            writer.Close();
#endif
        }

        Dictionary<string, FakePrefab> mFakePrefabs = new Dictionary<string, FakePrefab>();
    }
}