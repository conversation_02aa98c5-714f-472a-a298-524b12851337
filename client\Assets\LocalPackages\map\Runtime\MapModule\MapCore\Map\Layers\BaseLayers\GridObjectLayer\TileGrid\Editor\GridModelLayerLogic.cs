﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public enum GridModelOperationType
    {
        kCreateObject,
        kSelectObject,
        kRemoveObject,
        kSwitchObjectModel,
    }

    public enum TextureModelOperation
    {
        SwitchToTexture,
        SwitchToModel,
    }

    [ExecuteInEditMode]
    [Black]
    public class GridModelLayerLogic : MapLayerLogic
    {
        public string selectedPrefabGUID { set; get; }
        public GridModelOperationType operationType { set; get; }
        public TextureModelOperation textureModelOperation { set; get; }
        public PrefabRotationSetting rotationSetting { get { return mPrefabRotationSetting; } }
        public bool updateTexture { set; get; }
        public bool showTextureModelSetting { set; get; }

        public SparseGridObjectLayerData layerData
        {
            get
            {
                var layerData = Map.currentMap.FindObject(layerID) as SparseGridObjectLayerData;
                return layerData;
            }
        }

        void OnEnable()
        {
            mPrefabRotationSetting.rotationStep = 90;
            showTextureModelSetting = true;
            operationType = GridModelOperationType.kSelectObject;
        }

        void OnDestroy()
        {
            //base.OnDestroy();
            mPrefabRotationSetting.OnDestroy();
        }

        public static void CreatePrefabObstacles(string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath))
            {
                return;
            }

            var map = Map.currentMap;
            if (map != null)
            {
                var obstacleManager = map.data.localObstacleManager;
                var layer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
                if (layer != null)
                {
                    CheckObstacleValidation(layer);

                    obstacleManager.CreateObstacles(layer, PrefabOutlineType.ObjectPlacementObstacle);
                    ExportObstacleData(obstacleManager, "tile_mesh", folderPath);

                    //导出navmesh使用的障碍物数据
                    var obstacleManager1 = new MapLocalObstacleManager(map, map.mapWidth, map.mapHeight, obstacleManager.regionWidth, obstacleManager.regionHeight, null, null, "", map.data.isCircleMap);
                    obstacleManager1.CreateObstacles(layer, PrefabOutlineType.NavMeshObstacle);
                    ExportObstacleData(obstacleManager1, "navmesh_obstacle", folderPath);
                    obstacleManager1.OnDestroy();
                }
            }
        }

        static bool CheckObstacleValidation(EditorGridModelLayer layer)
        {
            bool valid = true;
            var map = Map.currentMap;
            int rows = layer.verticalTileCount;
            int cols = layer.horizontalTileCount;
            float halfWidth = layer.tileWidth * 0.5f;
            float halfHeight = layer.tileHeight * 0.5f;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var tileData = layer.GetObjectData(j, i);
                    if (tileData != null)
                    {
                        var modelTemplate = map.FindObject(tileData.GetModelTemplateID()) as ModelTemplate;
                        if (modelTemplate != null)
                        {
                            var outlines = modelTemplate.GetPrefabOutlines();
                            if (outlines != null)
                            {
                                for (int k = 0; k < outlines.Length; ++k)
                                {
                                    bool validNavMeshObstacle;
                                    bool validObjectPlacementObstacle;
                                    outlines[k].IsInValidRange(-halfWidth, halfWidth, -halfHeight, halfHeight, out validNavMeshObstacle, out validObjectPlacementObstacle);
                                    if (!validNavMeshObstacle)
                                    {
                                        valid = false;
                                        UnityEngine.Debug.LogWarning(string.Format("Invalid navmesh outline: {0},{1}", modelTemplate.GetLODPrefabPath(0), outlines[k].gameObject.name));
                                    }
                                    if (!validObjectPlacementObstacle)
                                    {
                                        valid = false;
                                        UnityEngine.Debug.LogWarning(string.Format("Invalid object placement outline: {0},{1}", modelTemplate.GetLODPrefabPath(0), outlines[k].gameObject.name));
                                    }
                                    if (valid == false)
                                    {
                                        outlines[k].ClampToBorder();
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return valid;
        }

        static void ExportObstacleData(MapLocalObstacleManager manager, string name, string folderPath)
        {
            string filePath = folderPath + "/" + name + ".json";
            if (!string.IsNullOrEmpty(filePath))
            {
                List<object> tileMeshList = new List<object>();

                Dictionary<string, object> rootObject = new Dictionary<string, object>();

                var tiles = manager.tiles;

                Dictionary<int, int> idToArrayIndex = new Dictionary<int, int>();
                var arrayTiles = CreateArrayTiles(tiles, idToArrayIndex);

                //转换成毫米
                float scale = 1000;
                rootObject["id"] = 1;
                rootObject["mapwidth"] = Map.currentMap.mapWidth;
                rootObject["mapheight"] = Map.currentMap.mapHeight;
                rootObject["tiles"] = arrayTiles;
                rootObject["xtilecount"] = manager.horizontalRegionCount;
                rootObject["ztilecount"] = manager.verticalRegionCount;
                rootObject["tilewidth"] = manager.regionWidth * scale;
                rootObject["tileheight"] = manager.regionHeight * scale;

                var prefabs = manager.prefabTileDatas;
                object[] prefabObject = new object[prefabs.Count];
                foreach (var p in prefabs)
                {
                    var obstacle = p.Value;
                    var obj = new Dictionary<string, object>();
                    var trianglesObject = JSONExporter.ExportTriangles(obstacle.triangles);
                    var verticesObject = JSONExporter.ExportVertices(obstacle.vertices);
                    obj["triangles"] = trianglesObject;
                    obj["vertices"] = verticesObject;
                    int arrayIndex = -1;
                    idToArrayIndex.TryGetValue(p.Key, out arrayIndex);
                    prefabObject[arrayIndex] = obj;
                }

                rootObject["obstacles"] = prefabObject;

                tileMeshList.Add(rootObject);

                var data = JSONParser.Serialize(tileMeshList);
                File.WriteAllText(filePath, data);
            }
        }

        static int[] CreateArrayTiles(int[] tileIDs, Dictionary<int, int> idToArrayIndex)
        {
            int[] tileIndices = new int[tileIDs.Length];
            int nextIdx = 0;
            for (int i = 0; i < tileIDs.Length; ++i)
            {
                if (tileIDs[i] == 0)
                {
                    tileIndices[i] = -1;
                }
                else
                {
                    int arrayIndex = -1;
                    bool found = idToArrayIndex.TryGetValue(tileIDs[i], out arrayIndex);
                    if (found == false)
                    {
                        idToArrayIndex[tileIDs[i]] = nextIdx;
                        tileIndices[i] = nextIdx;
                        ++nextIdx;
                    }
                    else
                    {
                        tileIndices[i] = arrayIndex;
                    }
                }
            }

            return tileIndices;
        }

        PrefabRotationSetting mPrefabRotationSetting = new PrefabRotationSetting();
    }
}

#endif