﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    [CustomEditor(typeof(NavMeshDebugger))]
    public class NavMeshDebuggerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mDebugger = target as NavMeshDebugger;
        }

        void OnSceneGUI()
        {
            var evt = Event.current;
            if (evt.type == EventType.MouseDown && evt.button == 0 && evt.alt == false)
            {
                var camera = SceneView.GetAllSceneCameras()[0];
                var screenPos = EditorUtils.ConvertScreenPosition(evt.mousePosition, camera);
                var pos = Utils.FromScreenToWorldPosition(screenPos, camera);
                mDebugger.PickVertex(pos, mDebugger.pickRadius, mVertexMode);
            }

            HandleUtility.AddDefaultControl(0);
        }

        public override void OnInspectorGUI()
        {
            mVertexMode = (NavMeshDebugger.PickVertexMode)EditorGUILayout.EnumPopup("Mode", mVertexMode);
            mDebugger.pickRadius = EditorGUILayout.FloatField("Vertex Radius", mDebugger.pickRadius);
        }

        NavMeshDebugger.PickVertexMode mVertexMode;
        NavMeshDebugger mDebugger;
    }
}

#endif