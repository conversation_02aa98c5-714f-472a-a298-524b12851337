﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public partial class BuildingGridLayer : MapLayerBase
    {
        public void Export(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.BuildingGridLayerDataVersion);

            ExportLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();

            AssetDatabase.Refresh();
        }

        void ExportLayer(BinaryWriter writer)
        {
            Utils.WriteVector3(writer, gridStartPosition);

            //only export 1 layer now
            int layerCount = 1;
            writer.Write(layerCount);
            for (int s = 0; s < layerCount; ++s)
            {
                var layer = mLayerData.GetLayer(s);
                writer.Write(layer.tileWidth);
                writer.Write(layer.tileHeight);
                writer.Write(layer.horizontalTileCount);
                writer.Write(layer.verticalTileCount);

                var gridIDs = layer.gridIDs;
                for (int i = 0; i < layer.verticalTileCount; ++i)
                {
                    for (int j = 0; j < layer.horizontalTileCount; ++j)
                    {
                        writer.Write(gridIDs[i, j]);
                    }
                }
                var brushes = layer.grids;
                writer.Write(brushes.Count);
                for (int i = 0; i < brushes.Count; ++i)
                {
                    writer.Write(brushes[i].id);
                    var region = brushes[i] as BuildingGridData;
                    writer.Write(region.walkable);
                }
            }
        }
    }
}

#endif