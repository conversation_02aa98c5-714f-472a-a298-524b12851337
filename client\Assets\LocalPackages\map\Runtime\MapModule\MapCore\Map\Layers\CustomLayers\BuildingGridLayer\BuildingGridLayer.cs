﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class BuildingGridLayer : MapLayerBase
    {
        public BuildingGridLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }

            if (mLayerData != null)
            {
                mLayerData.OnDestroy();
                Map.currentMap.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }
        //加载地图的数据
        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.BuildingGridLayerData;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 0, 0, 0, 0, GridType.Rectangle, sourceLayer.origin);

            //create layers
            List<TextureGridLayerData.Layer> layers = new List<TextureGridLayerData.Layer>();
            for (int i = 0; i < sourceLayer.layers.Length; ++i)
            {
                List<TextureGridData> regions = new List<TextureGridData>();
                var sourceSubLayer = sourceLayer.layers[i];
                if (sourceSubLayer.grids != null)
                {
                    int n = sourceSubLayer.grids.Count;
                    for (int k = 0; k < n; ++k)
                    {
                        var sourceRegion = sourceSubLayer.grids[k] as config.BuildingGridData;
                        var region = new BuildingGridData(sourceRegion.id, sourceRegion.color, sourceRegion.walkable);
                        regions.Add(region);
                    }
                }

                var subLayer = new TextureGridLayerData.Layer(sourceSubLayer.name, sourceSubLayer.horizontalTileCount, sourceSubLayer.verticalTileCount, sourceSubLayer.tileWidth, sourceSubLayer.tileHeight, sourceSubLayer.gridIDs, regions, Vector3.zero);
                layers.Add(subLayer);
            }
            mLayerData = new BuildingGridLayerData(header, Map.currentMap, layers);
            mLayerView = new BuildingGridLayerView(mLayerData);
            mLayerView.active = layerData.active;
            mLayerView.gridPosition = sourceLayer.gridStartPosition;
            mLayerData.SetOnPixelsChangeCallback(mLayerView.OnSetPixels);

            Map.currentMap.AddMapLayer(this);
        }
        //卸载地图层
        public override void Unload()
        {
            Map.currentMap.RemoveMapLayerByID(id);
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            Debug.Assert(false, "todo");
            return true;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            return false;
        }

        public void SetLayer(int layer)
        {
            mLayerView.OnSwitchLayer(layer);
        }

        //地图层的id
        public override int id { get { return mLayerData.id; } }
        //地图层的root gameobject
        public override GameObject gameObject { get { return mLayerView.root; } }
        public override bool Contains(int objectID) { return false; }
        public override int GetCurrentLOD() { return 0; }
        public override void RefreshObjectsInViewport() { }
        public override Vector3 layerOffset => mLayerData.layerOffset;

        //返回地图层的总宽度
        public override float GetTotalWidth() { return mLayerData.GetLayerWidthInMeter(); }
        //返回地图层的总高度
        public override float GetTotalHeight() { return mLayerData.GetLayerHeightInMeter(); }
        //地图层的名称
        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override int horizontalTileCount { get { Debug.LogError("Not implemented"); return 0; } }
        public override int verticalTileCount { get { Debug.LogError("Not implemented"); return 0; } }
        public override float tileHeight { get { Debug.LogError("Not implemented"); return 0; } }
        public override float tileWidth { get { Debug.LogError("Not implemented"); return 0; } }
        public override GridType gridType => GridType.Rectangle;
        public override int lodCount => mLayerData.lodCount;
        public Vector3 gridStartPosition { get { return mLayerView.gridPosition; } set { mLayerView.gridPosition = value; } }

        public BuildingGridLayerData layerData { get { return mLayerData; } }
        public BuildingGridLayerView layerView { get { return mLayerView; } }

       BuildingGridLayerData mLayerData;
       BuildingGridLayerView mLayerView;
    }
}
#endif