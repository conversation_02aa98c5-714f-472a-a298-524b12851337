﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

namespace TFW.Map
{
    //所有地图上的数据都派生自BaseObject,方便统一管理
    public abstract class BaseObject
    {
        public BaseObject(int id, Map map, ObjectFlag flag = ObjectFlag.kAddToObjectList)
        {
            mID = id;
            mFlag = flag;
            mMap = map;

            if (sAddObjects)
            {
                map.AddObject(this);
            }
        }
        public abstract void OnDestroy();

        public void AddObjectFlag(ObjectFlag flag)
        {
            mFlag |= flag;
        }
        public void RemoveObjectFlag(ObjectFlag flag)
        {
            mFlag &= ~flag;
        }
        public bool HasObjectFlag(ObjectFlag flag)
        {
            return (mFlag & flag) != 0;
        }
        public void SetObjectFlag(ObjectFlag flag)
        {
            mFlag = flag;
        }

        public int id { get { return mID; } protected set { mID = value; } }
        public ObjectFlag flag { get { return mFlag; } }
        public Map map { get { return mMap; } }

        public virtual int propertySetID { get { return 0; } set { } }

        int mID;
        protected Map mMap;
        ObjectFlag mFlag = ObjectFlag.kAddToObjectList;

        public static void AddObject(BaseObject obj)
        {
            obj.map.AddObject(obj);
        }

        public static void RemoveObject(BaseObject obj)
        {
            obj.map.RemoveObject(obj);
        }

        public static void RemoveObject(Map map, int id)
        {
            map.RemoveObject(id);
        }

        public static bool sAddObjects = true;
    }
}
