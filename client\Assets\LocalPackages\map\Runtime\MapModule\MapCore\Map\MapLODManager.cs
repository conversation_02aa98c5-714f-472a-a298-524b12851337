﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理地图的lod分级
    public class MapLODManager
    {
        public class MapLODUnit
        {
            public MapLODUnit(string unit, string relation)
            {
                this.unit = unit;
                this.relation = relation;
            }
            public string unit;
            public string relation;
        }

        //地图的lod层级配置
        public class LOD
        {
            public LOD(string name, float cameraHeight, bool showTerritory)
            {
                this.cameraHeight = cameraHeight;
                this.name = name;
                this.showTerritory = showTerritory;
            }

            public void SetDisplayingUnitCount(int newCount)
            {
                if (newCount < 0)
                {
                    return;
                }

                int oldCount = displayingUnits.Count;
                if (newCount == oldCount)
                {
                    return;
                }

                var newUnits = new List<MapLODUnit>();
                int delta = newCount - oldCount;
                if (delta > 0)
                {
                    for (int i = 0; i < oldCount; ++i)
                    {
                        newUnits.Add(displayingUnits[i]);
                    }

                    for (int i = 0; i < delta; ++i)
                    {
                        newUnits.Add(new MapLODUnit("", ""));
                    }
                }
                else if (delta < 0)
                {
                    for (int i = 0; i < newCount; ++i)
                    {
                        newUnits.Add(displayingUnits[i]);
                    }
                }
                else
                {
                    Debug.Assert(false);
                }

                displayingUnits = newUnits;
            }

            public string GetUnitString()
            {
                string s = "[";
                for (int i = 0; i < displayingUnits.Count; ++i)
                {
                    s += string.Format("{0}\"unit\":\"{1}\", \"relation\":\"{2}\"{3}", "{", displayingUnits[i].unit, displayingUnits[i].relation, "}");
                    if (i != displayingUnits.Count - 1)
                    {
                        s += ",";
                    }
                }
                s += "]";
                return s;
            }

            public string name;
            //该lod等级对应的相机高度
            public float cameraHeight;
            public float viewWidth;
            public float viewHeight;
            public bool showTerritory = false;
            public List<MapLODUnit> displayingUnits = new List<MapLODUnit>();
        }

        public MapLODManager(Map map, MapLODManager.LOD[] lods)
        {
            if (lods == null)
            {
                lods = new MapLODManager.LOD[1];
                lods[0] = new MapLODManager.LOD(MapLayerLODConfig.GetDefaultLODName(0), 0, false);
            }
            mLODs = lods;
            mMap = map;
        }

        public float ConvertNameToZoom(string name)
        {
            for (int i = 0; i < mLODs.Length; ++i)
            {
                if (mLODs[i].name == name)
                {
                    return i;
                }
            }

            //Debug.Assert(false, $"invalid lod name {name}");
            return -1;
        }

        public string ConvertZoomToName(float zoom)
        {
            for (int i = 0; i < mLODs.Length; ++i)
            {
                if (Mathf.Approximately(i, zoom))
                {
                    return mLODs[i].name;
                }
            }

            Debug.Assert(false, $"invalid zoom {zoom}");
            return "";
        }

        public MapLODManager.LOD GetLOD(int lod)
        {
            if (lod >= 0 && lod < mLODs.Length)
            {
                return mLODs[lod];
            }
            return null;
        }

        public MapLODManager.LOD GetLOD(string name)
        {
            for (int i = 0; i < mLODs.Length; ++i)
            {
                if (mLODs[i].name == name)
                {
                    return mLODs[i];
                }
            }
            return null;
        }

        public bool Validate()
        {
            float maxHeight = float.MinValue;
            for (int i = 0; i < mLODs.Length - 1; ++i)
            {
                if (mLODs[i].cameraHeight < maxHeight)
                {
                    return false;
                }
                maxHeight = mLODs[i].cameraHeight;
            }
            return true;
        }

        //返回cameraHeight对应的Zoom
        public float GetZoom(float cameraHeight)
        {
            int low = -1;
            for (int i = mLODs.Length - 1; i >= 0; --i)
            {
                if (cameraHeight >= mLODs[i].cameraHeight)
                {
                    low = i;
                    break;
                }
            }

            if (low == -1)
            {
                return 0;
            }
            int high = Mathf.Clamp(low + 1, 0, lodCount - 1);
            float upperHeight;
            if (high == low)
            {
                upperHeight = mMap.GetCameraMoveHeightRange().y;
            }
            else
            {
                upperHeight = mLODs[high].cameraHeight;
            }
            var deltaHeight = upperHeight - mLODs[low].cameraHeight;
            if (Mathf.Approximately(deltaHeight, 0))
            {
                return high;
            }
            return low + (cameraHeight - mLODs[low].cameraHeight) / deltaHeight;
        }

        //返回zoom值对应的相机高度
        public float GetCameraHeight(float zoom)
        {
            if (mLODs.Length == 0)
            {
                return 0;
            }
            zoom = Mathf.Clamp(zoom, 0, mLODs.Length);
            if (zoom < 0)
            {
                return mLODs[0].cameraHeight;
            }
            else if (zoom <= mLODs.Length - 1)
            {
                int lowerIdx = Mathf.FloorToInt(zoom);
                int upperIdx = Mathf.Clamp(lowerIdx + 1, 0, mLODs.Length - 1);
                float ratio = zoom - lowerIdx;
                return Mathf.Lerp(mLODs[lowerIdx].cameraHeight, mLODs[upperIdx].cameraHeight, ratio);
            }

            int idx = Mathf.Clamp(Mathf.FloorToInt(zoom), 0, mLODs.Length - 1);
            float r = zoom - idx;
            return Mathf.Lerp(mLODs[idx].cameraHeight, mMap.GetCameraMoveHeightRange().y, r);
        }

        //注意,修改了height后并没有修改viewWidth和viewHeight,只用来测试调整lod高度,最后还是在编辑器里设置最终值
        public void SetLODHeight(int lod, float height)
        {
            var lodConfig = GetLOD(lod);
            if (lodConfig != null)
            {
                lodConfig.cameraHeight = height;
            }
        }

        public float GetLODHeight(int lod)
        {
            var lodConfig = GetLOD(lod);
            if (lodConfig != null)
            {
                return lodConfig.cameraHeight;
            }
            return 0;
        }

        public void SetLODCount(int newCount)
        {
            var newLODs = new MapLODManager.LOD[newCount];
            int oldCount = mLODs.Length;
            int delta = newCount - oldCount;
            if (delta > 0)
            {
                for (int i = 0; i < oldCount; ++i)
                {
                    newLODs[i] = new MapLODManager.LOD(mLODs[i].name, mLODs[i].cameraHeight, mLODs[i].showTerritory);
                }
                float lastHeight = 0;
                if (mLODs.Length > 0)
                {
                    lastHeight = mLODs[oldCount - 1].cameraHeight;
                }
                float initDelta = 100.0f;
                for (int i = 0; i < delta; ++i)
                {
                    newLODs[i + oldCount] = new MapLODManager.LOD(MapLayerLODConfig.GetDefaultLODName(i + oldCount), lastHeight + initDelta, false);
                    newLODs[i + oldCount].cameraHeight = lastHeight + initDelta;
                    lastHeight = lastHeight + initDelta;
                }
            }
            else if (delta < 0)
            {
                for (int i = 0; i < newCount; ++i)
                {
                    newLODs[i] = new MapLODManager.LOD(mLODs[i].name, mLODs[i].cameraHeight, mLODs[i].showTerritory);
                }
            }
            else
            {
                Debug.Assert(false);
            }

            mLODs = newLODs;
        }

        class CompareLOD : IComparer<LOD>
        {
            public int Compare(LOD x, LOD y)
            {
                return (int)(x.cameraHeight - y.cameraHeight);
            }
        }

        public void Sort()
        {
            var lodList = new List<LOD>(mLODs.Length);
            lodList.AddRange(mLODs);
            lodList.Sort(new CompareLOD());
            mLODs = lodList.ToArray();
        }

        public int lodCount { get { return mLODs.Length; } }

        LOD[] mLODs;
        Map mMap;
    }
}
