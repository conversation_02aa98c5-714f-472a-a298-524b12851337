﻿Shader "K1/Particle/Unlit/Dissolve_New"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _NoiseTex("Noise", 2D) = "white"{}
	    _RampTex("Ramp", 2D) = "black"{}
	    _Emission("Emission", Range(0, 2)) = 1
	    _BorderWidth("BorderWidth", Range(0, 1)) = 0
	    
	    _DissolveStart("DissolveStart", Vector) = (0,0,0)
	    _DissolveEnd("DissolveEnd", Vector) = (1,0,0)
    }
    SubShader
    {
        Tags { "Queue" = "Transparent" "RenderType"="Opaque" }
		Zwrite off
		Cull Off
        LOD 100

        Pass
        {
			Blend SrcAlpha OneMinusSrcAlpha
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float4 uv : TEXCOORD0;
                float4 uv2 : TEXCOORD1;
                fixed4 color : COLOR;
            };

            struct v2f
            {
                float4 uv : TEXCOORD0;
                float4 uv2 : TEXCOORD1;
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            
            sampler2D _NoiseTex;
            float4 _NoiseTex_ST;
            
            sampler2D _RampTex;
            float _Emission;
            float _BorderWidth;
            
            float3 _DissolveStart;
            float3 _DissolveEnd;
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv.xy = TRANSFORM_TEX(v.uv, _MainTex);
                o.uv.zw = v.uv.zw;
                
                o.uv2.xy = v.uv2.xy + TRANSFORM_TEX(v.uv, _NoiseTex) / 2;
                
                o.color = v.color;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed Noise = tex2D(_NoiseTex, i.uv2).r;
                
                fixed s = _DissolveStart.x + i.uv.w * _DissolveStart.y + _DissolveStart.z;
                fixed e = _DissolveEnd.x + i.uv.w * _DissolveEnd.y + _DissolveEnd.z;
                
                fixed dissolve = Noise - lerp(s,e,i.uv.z);
	            clip(dissolve);
	            
                fixed4 col = tex2D(_MainTex,i.uv.xy) * i.color;

                half2 rampuv = half2((dissolve - _BorderWidth) / _BorderWidth,0.5);
				fixed4 rampcol = tex2D(_RampTex, rampuv) * _Emission;
				rampcol.a = col.a;
                col = lerp(col, rampcol,step(dissolve,_BorderWidth));
                
                return col;
            }
            ENDCG
        }
    }
}
