%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Terrain Wind Value
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=16103\n282;100;993;689;732.5;318;1;True;False\nNode;AmplifyShaderEditor.Vector4Node;2;-275,-56;Float;False;Global;_Wind;_Wind;0;0;Fetch;True;0;0;False;0;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionOutput;0;1,-32;Float;False;True;Value;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nWireConnection;0;0;2;0\nASEEND*/\n//CHKSM=BA65DE015BF1BA5B4089E1D34F116B49DF410865"
  m_functionName: 
  m_description: 'Returns terrain current wind value '
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives:
    - {fileID: 0}
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems:
    - LineType: 0
      LineValue: TerrainEngine.cginc
      GUIDToggle: 0
      GUIDValue: 
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
