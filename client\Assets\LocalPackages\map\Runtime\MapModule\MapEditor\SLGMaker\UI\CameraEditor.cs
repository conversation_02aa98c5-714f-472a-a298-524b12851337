﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class CameraEditor
    {
        public void DrawInspector()
        {
            EditorGUILayout.BeginVertical("GroupBox");

            Camera camera = null;
            var map = Map.currentMap;
            if (map != null)
            {
                //camera = map.camera;
                //EditorGUILayout.FloatField("LOD Zoom", map.CalculateCameraZoom(camera.transform.position.y));

                //bool enableLOD = EditorGUILayout.Toggle("Enable LOD", mEnableLOD);
                //if (enableLOD != mEnableLOD)
                //{
                //    mEnableLOD = enableLOD;
                //    if (!enableLOD)
                //    {
                //        SetMapZoom(0);
                //        map.enableLOD = false;
                //    }
                //    else
                //    {
                //        float zoom = map.CalculateCameraZoom(camera.transform.position.y);
                //        SetMapZoom(zoom);
                //        map.enableLOD = true;
                //    }
                //}
            }
            else
            {
                var sceneCameras = SceneView.GetAllSceneCameras();
                if (sceneCameras.Length > 0)
                {
                    camera = SceneView.GetAllSceneCameras()[0];
                }
            }

            if (EditorApplication.isPlaying == false)
            {
                if (camera != null)
                {
                    EditorGUILayout.BeginHorizontal();
                    //SLGMakerEditor.fov = EditorGUILayout.FloatField("Camera FOV", SLGMakerEditor.fov);
                    mEnableCameraRotationSetting = GUILayout.Toggle(mEnableCameraRotationSetting, "Set Camera Rotation");
                    EditorGUILayout.EndHorizontal();

                    if (mEnableCameraRotationSetting)
                    {
                        var rot = Quaternion.Euler(EditorGUILayout.Vector3Field("Camera Rotation", camera.transform.rotation.eulerAngles));
                        if (rot != camera.transform.rotation)
                        {
                            SceneView.lastActiveSceneView.rotation = rot;
                        }
                        SceneView.RepaintAll();
                    }
                    else
                    {
                        Quaternion.Euler(EditorGUILayout.Vector3Field("Camera Rotation", camera.transform.rotation.eulerAngles));
                    }
                    EditorGUILayout.Vector3Field("Camera Position", camera.transform.position);
                }

                mCameraBoundsEditor.DrawInspector();
            }
            EditorGUILayout.EndVertical();
        }

        //void SetMapZoom(float zoom)
        //{
        //Debug.Assert(false, "todo");
        //var map = Map.currentMap;
        //map.SetZoom(zoom);
        //int nLayers = map.GetMapLayerCount();
        //for (int i = 0; i < nLayers; ++i)
        //{
        //    map.GetMapLayer(i).SetZoom(zoom);
        //}
        //}

        public CameraBoundsEditor cameraBoundsEditor { get { return mCameraBoundsEditor; } }

        bool mEnableCameraRotationSetting = false;
        //bool mEnableLOD = true;
        CameraBoundsEditor mCameraBoundsEditor = new CameraBoundsEditor();
    }
}

#endif