﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public partial class MapGlobalObstacleManager
    {
        GameObject mPlaneObject;
        Mesh mMesh;
        Material mMaterial;
        Texture2D mGridTexture = null;

        void DestroyTexturePlane()
        {
#if UNITY_EDITOR
            Object.DestroyImmediate(mMesh);
            Object.DestroyImmediate(mMaterial);
            Object.DestroyImmediate(mGridTexture);
            Utils.DestroyObject(mPlaneObject);
#endif
        }

        void CreateTexturePlane()
        {
#if UNITY_EDITOR
            var map = Map.currentMap;
            if (map != null)
            {
                DestroyTexturePlane();
                if (mPlaneObject == null && mGridSize > 0 && mObstacleGrids != null)
                {
                    int gridCount = Mathf.CeilToInt(mMapWidth / mGridSize);
                    //create region texture and materials
                    mGridTexture = new Texture2D(gridCount, gridCount, TextureFormat.RGBA32, false, false);
                    Color32[] gridColors = new Color32[gridCount * gridCount];
                    Color32 obstacleColor = new Color32(255, 0, 0, 100);
                    Color32 emptyGroundColor = new Color32(0, 255, 255, 100);
                    for (int i = 0; i < gridCount; ++i)
                    {
                        for (int j = 0; j < gridCount; ++j)
                        {
                            bool isObstacleGrid = IsCollidedWithObstacle(j, i);
                            if (isObstacleGrid)
                            {
                                gridColors[i * gridCount + j] = obstacleColor;
                            }
                            else
                            {
                                gridColors[i * gridCount + j] = emptyGroundColor;
                            }
                        }
                    }

                    mGridTexture.filterMode = FilterMode.Point;
                    mGridTexture.SetPixels32(gridColors);
                    mGridTexture.Apply();

                    //create plane
                    mPlaneObject = new GameObject("region plane");
                    mPlaneObject.SetActive(true);
                    mPlaneObject.transform.parent = mObstacleObject.transform;
                    var meshRenderer = mPlaneObject.AddComponent<MeshRenderer>();
                    var meshFilter = mPlaneObject.AddComponent<MeshFilter>();
                    meshFilter.sharedMesh = CreateMesh(mMapWidth, mMapHeight);
                    meshRenderer.sharedMaterial = CreateMaterial();
                    meshRenderer.sharedMaterial.SetTexture("_MainTex", mGridTexture);
                }
            }
#endif
        }

        Mesh CreateMesh(float mapWidth, float mapHeight)
        {
            if (mMesh == null)
            {
                mMesh = new Mesh();
                mMesh.vertices = new Vector3[]{
                    new Vector3(0, 0, 0),
                    new Vector3(0, 0, mapHeight),
                    new Vector3(mapWidth, 0, mapHeight),
                    new Vector3(mapWidth, 0, 0),
                };
                mMesh.uv = new Vector2[] {
                    new Vector2(0, 0),
                    new Vector2(0, 1),
                    new Vector2(1, 1),
                    new Vector2(1, 0),
                };
                mMesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            }
            return mMesh;
        }

        Material CreateMaterial()
        {
            if (mMaterial == null)
            {
                mMaterial = new Material(Shader.Find("SLGMaker/DiffuseTransparent"));
                mMaterial.renderQueue = 3201;
            }
            return mMaterial;
        }

        void SetPixels(int x, int y, int width, int height, Color32[] pixels)
        {
            if (mGridTexture != null)
            {
                mGridTexture.SetPixels32(x, y, width, height, pixels);
                mGridTexture.Apply();
            }
        }

        public void RefreshTexture()
        {
            int gridCount = Mathf.CeilToInt(mMapWidth / mGridSize);
            Color32 black = new Color32(0, 0, 0, 0);
            Color32[] gridColors = new Color32[gridCount * gridCount];
            Color32 obstacleColor = new Color32(255, 0, 0, 100);
            Color32 emptyGroundColor = new Color32(0, 255, 255, 100);
            for (int i = 0; i < gridCount; ++i)
            {
                for (int j = 0; j < gridCount; ++j)
                {
                    bool isObstacleGrid = IsCollidedWithObstacle(j, i);
                    if (isObstacleGrid)
                    {
                        gridColors[i * gridCount + j] = obstacleColor;
                    }
                    else
                    {
                        gridColors[i * gridCount + j] = emptyGroundColor;
                    }
                }
            }
            SetPixels(0, 0, gridCount, gridCount, gridColors);
        }
    }
}