﻿ 
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
 


public class I18NSync
{
    private static string fileId = "12cE6eMaq1b7dvbQjU62OG8E2n8NwqkbVAA79U5-QzWg";
    private static string sheetId = "0";
    private static string url = $"https://docs.google.com/spreadsheets/d/{fileId}/export?format=csv&id={fileId}&gid={sheetId}";

    private static readonly Dictionary<string, string> TabDictionary = new Dictionary<string, string>
    {
        ["cn"] = "简体中文",
        ["en"] = "英文",
        ["zh"] = "繁體中文",
        ["fr"] = "法語",
        ["de"] = "德語",
        ["ru"] = "俄語",
        ["id"] = "印尼語",
        ["ms"] = "馬來語",
        ["pl"] = "波蘭語",
        ["th"] = "泰語",
        ["vi"] = "越南",
        ["tr"] = "土耳其",
        ["ja"] = "日本",
        ["kr"] = "韩国",
        ["ar"] = "阿拉伯",
        ["es"] = "西班牙",
        ["pt"] = "葡萄牙",
        ["gr"] = "希臘",
        ["it"] = "義大利",
        ["nl"] = "荷蘭",
        ["se"] = "瑞典",
        ["uk"] = "乌克兰语"
    };

    private static async Task ProcessLanguage(string langKey, List<Dictionary<string, object>> data)
    {
        try
        {
            var tsvContent = $"Id\tValue\n";
            foreach (var row in data)
            {
                var id = row.TryGetValue("ID", out var idVal) ? idVal?.ToString() ?? "" : "";
                var value = row.TryGetValue(TabDictionary[langKey], out var val) ? val?.ToString() ?? "" : "";

                // 替换TSV中的制表符和换行符
                id = id.Replace("\t", " ").Replace("\n", " ");
                value = value.Replace("\t", " ").Replace("\n", " ");

                tsvContent += $"{id}\t{value}\n";
            }

            await File.WriteAllTextAsync($"{langKey}.tsv", tsvContent);
            Console.WriteLine($"✅ {langKey}.tsv 生成完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 处理 {langKey} 时出错: {ex.Message}");
        }
    }


    static async Task Main(string[] args)
    {
        try
        {

            Console.WriteLine("正在下载数据...");
            var csvData = await GetCSVFromGoogleSheet();
            if (string.IsNullOrEmpty(csvData))
            { 
                return;
            }

            Console.WriteLine("正在解析数据...");
            var jsonData = ParseCsv(csvData);

            if (jsonData.Count == 0 || !jsonData[0].ContainsKey("简体中文"))
                throw new Exception("表格格式异常，未找到'简体中文'列或数据为空");

            Console.WriteLine($"找到 {jsonData.Count} 条记录，开始生成TSV文件...");

            var tasks = new List<Task>();
            foreach (var kvp in TabDictionary)
            {
                if (jsonData[0].ContainsKey(kvp.Value))
                {
                    tasks.Add(ProcessLanguage(kvp.Key, jsonData));
                }
                else
                {
                    Console.WriteLine($"⚠️ 警告: 表格中未找到列 '{kvp.Value}'，跳过 {kvp.Key}");
                }
            }

            await Task.WhenAll(tasks);
            Console.WriteLine("🎉 所有文件生成完成!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 未知错误: {ex.Message}");
        }
    }


    static async Task<string> GetCSVFromGoogleSheet()
    {
        HttpClient _httpClient = new HttpClient();

        HttpResponseMessage response = await _httpClient.GetAsync(url);

        // 确保请求成功
        response.EnsureSuccessStatusCode();

        // 读取响应内容
        return await response.Content.ReadAsStringAsync();
    }

    // 手动解析CSV的方法，不使用CsvHelper
    private static List<Dictionary<string, object>> ParseCsv(string csvData)
    {
        var result = new List<Dictionary<string, object>>();
        var lines = csvData.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

        if (lines.Length == 0)
            return result;

        // 解析表头
        var headers = ParseCsvLine(lines[0]);

        // 解析数据行
        for (int i = 1; i < lines.Length; i++)
        {
            var values = ParseCsvLine(lines[i]);
            if (values.Count == 0)
                continue;

            var row = new Dictionary<string, object>();
            for (int j = 0; j < Math.Min(headers.Count, values.Count); j++)
            {
                row[headers[j]] = values[j];
            }
            result.Add(row);
        }

        return result;
    }

    // 解析单行CSV，处理引号和逗号
    private static List<string> ParseCsvLine(string line)
    {
        var result = new List<string>();
        if (string.IsNullOrEmpty(line))
            return result;

        bool inQuotes = false;
        StringBuilder currentValue = new StringBuilder();
        int startIndex = 0;

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];

            if (c == '"')
            {
                // 处理引号
                if (i + 1 < line.Length && line[i + 1] == '"')
                {
                    // 连续两个引号表示一个引号字符
                    currentValue.Append('"');
                    i++;
                }
                else
                {
                    inQuotes = !inQuotes;
                }
            }
            else if (c == ',' && !inQuotes)
            {
                // 逗号且不在引号内，表示字段分隔
                result.Add(currentValue.ToString());
                currentValue.Clear();
                startIndex = i + 1;
            }
            else
            {
                currentValue.Append(c);
            }
        }

        // 添加最后一个字段
        result.Add(currentValue.ToString());

        return result;
    }

}
