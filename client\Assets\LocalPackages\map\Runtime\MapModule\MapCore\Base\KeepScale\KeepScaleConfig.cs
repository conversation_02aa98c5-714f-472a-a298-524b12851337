﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //缩放分为3段距离,小于minimumCameraHeight和大于maximumCameraHeight后物体的scale就不会再改变了,
    //在这个区间内scale会根据相机的高度进行相应的缩放,使物体在屏幕上的投影大小保持不变
    [CreateAssetMenu(fileName = "keep_scale_config", menuName = "Assets/KeepObjectScaleConfig")]
    public class KeepScaleConfig : ScriptableObject
    {
        //物体scale为1时相机的fov
        public float cameraFovWhenScaleIsOne = 40;
        //物体scale为1时相机的高度
        public float cameraHeightWhenScaleIsBaseScale = 18;
        public float cameraBaseScale = 1.0f;
        //物体可以动态缩放的相机最高高度
        public float maximumCameraHeight = 18;
        ////物体可以动态缩放的相机最低高度
        public float minimumCameraHeight = 15;
        //当主城在中心时相机的最大高度,这个值必须要在[minimumCameraHeight,maximumCameraHeight]之间
        public float maximumCameraHeightWhenCityIsInCenter = 16.6f;
    }
}
