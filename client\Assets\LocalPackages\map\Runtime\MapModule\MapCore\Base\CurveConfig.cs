﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public class CurveConfig : ScriptableObject
    {
        public void SetInfo(float startTime, float startValue, float deltaTime, float deltaValue)
        {
            mStartTime = startTime;
            mStartValue = startValue;
            mDeltaTime = deltaTime;
            mDeltaValue = deltaValue;
        }

        public float GetValue(float time)
        {
            float val = curve.Evaluate(time);
            return val;
        }

        public void SetNodeCount(int count)
        {
            if (count != curve.length)
            {
                int delta = count - curve.length;
                if (delta < 0)
                {
                    int n = -delta;
                    int removeIndex = curve.length - n;
                    for (int i = n - 1; i >= 0; --i)
                    {
                        curve.RemoveKey(removeIndex + i);
                    }
                }
                else
                {
                    float lastTime = mStartTime;
                    float lastValue = mStartValue;
                    if (curve.length > 0)
                    {
                        var lastKey = curve[curve.length - 1];
                        lastTime = lastKey.time;
                        lastValue = lastKey.value;
                    }
                    for (int i = 1; i <= delta; ++i)
                    {
                        curve.AddKey(lastTime + i * mDeltaTime, lastValue + i * mDeltaValue);
                    }
                }
            }
        }

        protected float mStartTime = 0;
        protected float mStartValue = 1;
        protected float mDeltaTime = 10;
        protected float mDeltaValue = 1;
        public AnimationCurve curve;
    }
}
