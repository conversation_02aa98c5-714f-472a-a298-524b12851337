﻿ 



 
 

﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    class PlaneBorderMakerWindow : EditorWindow
    {
        void OnGUI()
        {
            mOutputFolder = EditorGUILayout.TextField("Output Folder", mOutputFolder);
            mBorderName = EditorGUILayout.TextField("Border Name", mBorderName);
            mTotalWidth = EditorGUILayout.FloatField("Border Width", mTotalWidth);
            mTotalHeight = EditorGUILayout.FloatField("Border Height", mTotalHeight);
            mMapWidth = EditorGUILayout.FloatField("Map Width", mMapWidth);
            mMapHeight = EditorGUILayout.FloatField("Map Height", mMapHeight);
            mBorderMaterial = EditorGUILayout.ObjectField("Border Material", mBorderMaterial, typeof(Material), false, null) as Material;

            if (GUILayout.Button("Create"))
            {
                string errMsg = CheckValidation();
                if (!string.IsNullOrEmpty(errMsg))
                {
                    EditorUtility.DisplayDialog("Error", errMsg, "OK");
                    return;
                }
                PlaneBorderMaker.Create(mTotalWidth, mTotalHeight, mMapWidth, mMapHeight, mBorderMaterial, mBorderName, mOutputFolder);
                Close();
            }
        }

        string CheckValidation()
        {
            if (string.IsNullOrEmpty(mOutputFolder))
            {
                return $"Invalid output folder";
            }

            if (string.IsNullOrEmpty(mBorderName))
            {
                return $"Invalid border name";
            }

            if (mBorderMaterial == null)
            {
                return $"Invalid border material";
            }

            if (mTotalWidth <= 0 || mTotalHeight <= 0 || mMapWidth <= 0 || mMapHeight <= 0 ||
                mTotalWidth < mMapWidth || mTotalHeight < mMapHeight)
            {
                return $"Invalid border size";
            }

            return "";
        }

        string mOutputFolder;
        string mBorderName;
        Material mBorderMaterial;
        float mTotalWidth = 21600;
        float mTotalHeight = 21600;
        float mMapWidth = 7200;
        float mMapHeight = 7200;
    }
}
#endif