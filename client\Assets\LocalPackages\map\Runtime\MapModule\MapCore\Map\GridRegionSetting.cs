﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class GridRegionTemplate
    {
        public GridRegionTemplate(Color32 color, byte type)
        {
            this.color = color;
            this.type = type;
        }
        public Color32 color;
        public byte type;
    }

    public class GridRegionSetting
    {
        public GridRegionSetting(int horizontalGridCount, int verticalGridCount, float gridWidth, float gridHeight, byte[] gridData, List<GridRegionTemplate> templates)
        {
            mGridData = gridData;
            mGridWidth = gridWidth;
            mGridHeight = gridHeight;
            mHorizontalGridCount = horizontalGridCount;
            mVerticalGridCount = verticalGridCount;
            mTemplates = templates;
        }

        public int GetRegionID(Vector3 pos)
        {
            var coord = FromPositionToCoordinate(pos);
            return GetRegionID(coord.y, coord.x);
        }

        public int GetRegionID(int row, int col)
        {
            if (col >= 0 && col < mHorizontalGridCount &&
                row >= 0 && row < mVerticalGridCount)
            {
                int idx = row * mHorizontalGridCount + col;
                return mGridData[idx];
            }
            return 0;
        }

        public void GetIntersectedRegions(Vector3 center, float radius, List<int> intersectedRegions)
        {
            float minX = center.x - radius;
            float minZ = center.z - radius;
            float maxX = center.x + radius;
            float maxZ = center.z + radius;

            var minCoord = FromPositionToCoordinate(new Vector3(minX, 0, minZ));
            var maxCoord = FromPositionToCoordinate(new Vector3(maxX, 0, maxZ));

            intersectedRegions.Clear();
            for (int i = minCoord.y; i <= maxCoord.y; ++i)
            {
                for (int j = minCoord.x; j <= maxCoord.x; ++j)
                {
                    var id = GetRegionID(i, j);
                    if (id != 0 && intersectedRegions.Contains(id) == false)
                    {
                        intersectedRegions.Add(id);
                    }
                }
            }
        }

        public Vector2Int FromPositionToCoordinate(Vector3 pos)
        {
            return new Vector2Int(Mathf.FloorToInt(pos.x / mGridWidth), Mathf.FloorToInt(pos.z / mGridHeight));
        }

        public Vector3 FromCoordinateToPosition(int x, int y)
        {
            return new Vector3(x * mGridWidth + mGridWidth * 0.5f, 0, y * mGridHeight + mGridHeight * 0.5f);
        }

        public void ShowRegions()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                if (mPlaneObject == null)
                {
                    //create region texture and materials
                    Texture2D texture = new Texture2D(mHorizontalGridCount, mVerticalGridCount, TextureFormat.RGBA32, false, false);
                    Color32[] gridColors = new Color32[mHorizontalGridCount * mVerticalGridCount];
                    for (int i = 0; i < mVerticalGridCount; ++i)
                    {
                        for (int j = 0; j < mHorizontalGridCount; ++j)
                        {
                            byte regionType = (byte)GetRegionID(i, j);
                            GridRegionTemplate template = GetGridTemplate(regionType);
                            if (template != null)
                            {
                                gridColors[i * mHorizontalGridCount + j] = template.color;
                            }
                        }
                    }
                    texture.filterMode = FilterMode.Point;
                    texture.SetPixels32(gridColors);
                    texture.Apply();

                    //create plane
                    mPlaneObject = new GameObject("region plane");
                    mPlaneObject.SetActive(false);
                    var meshRenderer = mPlaneObject.AddComponent<MeshRenderer>();
                    var meshFilter = mPlaneObject.AddComponent<MeshFilter>();
                    meshFilter.sharedMesh = CreateMesh(map.mapWidth, map.mapHeight);
                    meshRenderer.sharedMaterial = CreateMaterial();
                    meshRenderer.sharedMaterial.SetTexture("_MainTex", texture);
                }

                mPlaneObject.SetActive(!mPlaneObject.activeSelf);
            }
        }

        GridRegionTemplate GetGridTemplate(byte type)
        {
            for (int i = 0; i < mTemplates.Count; ++i)
            {
                if (mTemplates[i].type == type)
                {
                    return mTemplates[i];
                }
            }

            return null;
        }

        Mesh CreateMesh(float mapWidth, float mapHeight)
        {
            if (mMesh == null)
            {
                mMesh = new Mesh();
                mMesh.vertices = new Vector3[]{
                    new Vector3(0, 0, 0),
                    new Vector3(0, 0, mapHeight),
                    new Vector3(mapWidth, 0, mapHeight),
                    new Vector3(mapWidth, 0, 0),
                };
                mMesh.uv = new Vector2[] {
                    new Vector2(0, 0),
                    new Vector2(0, 1),
                    new Vector2(1, 1),
                    new Vector2(1, 0),
                };
                mMesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            }
            return mMesh;
        }

        Material CreateMaterial()
        {
            if (mMaterial == null)
            {
                mMaterial = new Material(Shader.Find("SLGMaker/DiffuseTransparent"));
            }
            return mMaterial;
        }

        int mHorizontalGridCount;
        int mVerticalGridCount;
        float mGridWidth;
        float mGridHeight;
        byte[] mGridData;
        List<GridRegionTemplate> mTemplates;

        //display
        Mesh mMesh;
        Material mMaterial;
        GameObject mPlaneObject;
    }
}
