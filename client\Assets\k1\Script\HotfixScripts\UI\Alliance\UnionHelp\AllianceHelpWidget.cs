


using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Utils;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI.Alliance 
{

    public class AllianceHelpWidgetData 
    {
        public Action close;
    }



    public class AllianceHelpWidget : UIWidgetBase 
    {

        #region UIField
        private GameObject _helpAllBtn;
        private ScrollViewPro _listView;
        private TFWText _desc;
        private TFWText _time;
        private GameObject _timeObj;
        private GameObject btn_Tip;
        private GameObject TipPanel;
        private GameObject BlackBtn;
        private GameObject _noMessageTip;
        private GameObject _topResource;



        //�������
        //[PopupField("Root/Bottom/Request")]
        private GameObject RequestObj;
        //[PopupField("Root/Bottom/Request/Btn")]
        private GameObject RequestBtn;
        //[PopupField("Root/Bottom/Request/Num")]
        private TFWText RequestNum;
         
        #endregion

        private List<GameObject> DiscTexts = new List<GameObject>();
        private float saveTime = 0f;
        private float changeTime = 3f;
        private bool IsChangeShow = false;
        private int TimerId = 0;
        private TopResourceWidget _topResBar;

        /// <summary>
        /// �����б�
        /// </summary>
        private List<UnionHelpInfo> _helpList = new List<UnionHelpInfo>();

        AllianceHelpWidgetData data;

        UnionHelpInfo curHelpInfo;

        public AllianceHelpWidget(GameObject obj):base(obj) { }


        public override void OnInit()
        {
            base.OnInit();

            _helpAllBtn = GetChild("Root/Button");
            _listView = GetComponent<ScrollViewPro>("Root/ScrollView");
            _desc = GetComponent<TFWText>("Root/Time/Desc");
            _time = GetComponent<TFWText>("Root/Time/Text");
            _timeObj = GetChild("Root/Time");
            btn_Tip = GetChild("Root/Title/TipBtn");
            TipPanel = GetChild("Root/BGMask");
            BlackBtn = GetChild("Root/Black");
            _noMessageTip = GetChild("Root/NoMessageTip");
            _topResource = GetChild("Root/TopBar/TopResource");

            _topResBar = new TopResourceWidget(_topResource);
            if (_topResBar != null)
            {
                _topResBar.SetData();
            }
            _topResBar.SetJumpCallBack(null, ClickDiamondAdd, false, true);


            RequestObj = GetChild("Root/Bottom/Request");
            RequestBtn = GetChild("Root/Bottom/Request/Btn");
            RequestNum = GetComponent<TFWText>("Root/Bottom/Request/Num");
            UIBase.AddRemoveListener( TFW.EventTriggerType.Click,RequestBtn, (x, y) =>
            {
                PopupManager.I.ShowDialog<UIUnioRequestHelpPanel>();
            });

            
        }

        public override void Destroy()
        {
            base.Destroy();
            data = null;
            OnHide();
        }

        public void SetData(AllianceHelpWidgetData data) 
        {
            this.data = data;
        }

        #region

        private void ClickDiamondAdd()
        {
            this.data?.close?.Invoke();
            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
            //�����ʯ�Ӻ�ֱ��������ֵ����
            ShopMgr.I.OpenShopPanel(ShopJumpMark.gem);
        }


        private void OnClickHelpTipBtn(GameObject arg0, PointerEventData arg1)
        {
            PopupManager.I.ShowDialog<UIActivityRules>(
                new UIActivityRuleData()
                {
                    rules = "Alliance_Help_Text6"
                });
        }

        /// <summary>
        /// ����
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnHelpAllClick(GameObject arg0, PointerEventData arg1)
        {
            if (_helpList.Count > 0)
            {
                List<long> ids = new List<long>();
                foreach (var item in _helpList)
                {
                    if (item.Helped)
                    {
                        continue;
                    }
                    if (!ids.Contains(item.ID))
                    {
                        ids.Add(item.ID);
                    }
                }

                if (ids.Count > 0)
                {
                    LAllianceHelp.I.ReqHelpAll();
                }
            }
        }

        #endregion


        public new void OnShow()
        {
            AddListener();
            this._listView.maxNum = 0;
            BlackBtn.SetActive(false);
            this._listView.refresh_list();
            LAllianceHelp.I.ReqUnionHelpList();
            LAllianceHelp.I.ReqUnionHelpCount();
            IsChangeShow = true;

            _noMessageTip.SetActive(this._listView.maxNum == 0);
        }


        public void OnHide() 
        {
            NTimer.Destroy(TimerId);
            IsChangeShow = false;
            RemoveListener();
        }

        /// <summary>
        /// ÿ�����
        /// </summary>
        /// <param name="deltaTime"></param>
        private void PerSecondUpdate(float deltaTime)
        {
            RefreshResetTime();
            #region ˢ�µ�ǰ�������������ϵ�������ʾ
            if (IsChangeShow)
            {
                IsChangeShow = false;
                TimerId = NTimer.CountDown(changeTime, () =>
                {
                    if (DiscTexts.Count > 1)
                    {
                        foreach (var obj in DiscTexts)
                        {
                            obj.SetActive(!obj.activeSelf);
                        }
                    }
                    IsChangeShow = true;
                });
            }
            #endregion
        }

        private void OnItemInit(GameObject go, int index)
        {
            index = index - 1;
            if (go != null && index < this._helpList.Count)
            {
                SetItem(go, _helpList[index]);
            }
        }

        /// <summary>
        /// item
        /// </summary>
        /// <param name="root"></param>
        /// <param name="info"></param>
        private void SetItem(GameObject root, UnionHelpInfo info)
        {

            int maxHelpNum = MetaConfig.AllianceBeHelpUplimits[info.type] + info.extra;
            #region ��ʾ������Դͼ��
            GameObject clock = UIHelper.GetChild(root, "Icon/Clock");
            GameObject cion = UIHelper.GetChild(root, "Icon/Coin");
            GameObject itemIcon = UIHelper.GetChild(root, "Icon/ItemIcon");
            if (clock != null)
            {
                clock.SetActive(info.type == (int)UnionHelpType.AddSpeed);
            }
            if (cion != null)
            {
                cion.SetActive(info.type == (int)UnionHelpType.Coin);
            }
            if (itemIcon != null)
            {
                bool isShow = false;
                if (info.type == (int)UnionHelpType.Stone)
                {
                    isShow = true;
                    UITools.SetImage(itemIcon.GetComponent<TFWImage>(), "UI_Item_icon_ore", "NewItem");
                }
                else if (info.type == (int)UnionHelpType.Wood)
                {
                    isShow = true;
                    UITools.SetImage(itemIcon.GetComponent<TFWImage>(), "UI_Item_icon_wood", "NewItem");
                }
                else if (info.type == (int)UnionHelpType.Iron)
                {
                    isShow = true;
                    UITools.SetImage(itemIcon.GetComponent<TFWImage>(), "UI_Item_icon_iron", "NewItem");
                }
                itemIcon.SetActive(isShow);
            }
            #endregion
            GameObject head = UIHelper.GetChild(root, "Head_k1");
            TFWText descTxt = UIHelper.GetComponent<TFWText>(root, "Desc");
            TFWText totalTxt = UIHelper.GetComponent<TFWText>(root, "Slider/TotalText");
            TFWText nameTxt = UIHelper.GetComponent<TFWText>(root, "Name");
            TFWSlider slider = UIHelper.GetComponent<TFWSlider>(root, "Slider");
            PlayerIconWidget playerIcon = new PlayerIconWidget(head);
            descTxt.text = LocalizationMgr.Get("Alliance_Help_Request_Text");
            UIHelper.GetChild(root, "BGYellow").SetActive(info.playerID == LPlayer.I.PlayerID);
            TFWText progressTxt = UIHelper.GetComponent<TFWText>(root, "Slider/Text");
            if (info.playerID == LPlayer.I.PlayerID)
            {
                UnifyPlayerHead mhead = new UnifyPlayerHead();
                mhead.avatarCfgID = LPlayer.I.AvatarCfgID;
                mhead.customHead = LPlayer.I.CustomPlayerHead;
                mhead.useCustomHead = LPlayer.I.UseCustomHeadIcon;
                mhead.avatarFrrame = LPlayer.I.FrameId;
                playerIcon.InitData(mhead);
                nameTxt.text = LPlayer.I.GetPlayerName();
                string num = (info.progress * MetaConfig.AllianceHelpAllyPoints[info.type]).ToString();
                if (info.type == 1)
                    num = ResourceUtils.GetResourceShowStr(info.progress * MetaConfig.AllianceHelpAllyPoints[info.type]);
                if (progressTxt)
                    progressTxt.text = $"{info.progress}/{maxHelpNum}";
                if (totalTxt)
                    totalTxt.text = $"{LocalizationMgr.Get("Alliance_Help_Text3")}{num}";
                progressTxt.gameObject.SetActive(true);
                DiscTexts.Add(progressTxt.gameObject);
                totalTxt.gameObject.SetActive(false);
                DiscTexts.Add(totalTxt.gameObject);
            }
            else
            {
                playerIcon.InitData(info.head);
                totalTxt.gameObject.SetActive(false);
                nameTxt.text = info.head.name;
                if (progressTxt)
                    progressTxt.text = $"{info.progress}/{maxHelpNum}";
            }
            if (maxHelpNum > 0)
                slider.value = (float)info.progress / (float)maxHelpNum;
            else
                slider.value = 0;


        }

        private void AddListener()
        {
            EventMgr.RegisterEvent(TEventType.UnionHelpStateChange, OnAllianceHelpStateChange, this);
            EventMgr.RegisterEvent(TEventType.UnionHelpCountAck, OnSetDescText, this);
            EventMgr.RegisterEvent(TEventType.UnionHelpListAck, OnCoordsFavoritesAck, this);
            EventMgr.RegisterEvent(TEventType.UnionHelpNtf, OnUnionHelpNtf, this);
            EventMgr.RegisterEvent(TEventType.UnionHelpAck, OnUnionHelpAck, this);
            EventMgr.RegisterEvent(TEventType.UnionHelpDelNtf, OnUnionHelpDelNtf, this);
            UIBase.AddListener(TFW.EventTriggerType.Click, btn_Tip, OnClickHelpTipBtn);
            UIBase.AddListener(TFW.EventTriggerType.Click, _helpAllBtn, OnHelpAllClick);
            FrameUpdateMgr.RegisterPerSecondFunListUpdate(this, PerSecondUpdate);
            _listView.onInitializeItem += OnItemInit;
        }

        /// <summary>
        /// �Ƴ��¼�����
        /// </summary>
        private void RemoveListener()
        {
            FrameUpdateMgr.UnregisterPerSecondFunListUpdate(this);
            EventMgr.UnregisterEvent(this);
            UIBase.RemoveListener(TFW.EventTriggerType.Click, _helpAllBtn);
            UIBase.RemoveListener(TFW.EventTriggerType.Click, btn_Tip);
            if(_listView)
                this._listView.onInitializeItem -= OnItemInit;
        }


        private void OnAllianceHelpStateChange(object[] objs)
        {
            if (LAllianceHelp.I.CurState == UnionHelpState.CanUse)
            {
                for (int i = 0; i < LAllianceHelp.I.SelfHelpList.Count; i++)
                {
                    if (LAllianceHelp.I.SelfHelpList[i].type == 0)
                    {
                        List<long> del = new List<long>();
                        del.Add(LAllianceHelp.I.SelfHelpList[i].ID);
                        AllianceGameData.I.AllianceHelp.Del(del);
                        RefreshList();
                    }
                }
            }
        }

        /// <summary>
        /// ˢ���б�
        /// </summary>
        private void RefreshList()
        {
            DiscTexts.Clear();
            this._helpList = LAllianceHelp.I.SelfHelpList;
            this._helpList.AddRange(LAllianceHelp.I.HelpList);
            this._listView.maxNum = this._helpList.Count;
            this._listView.refresh_list();
            _helpAllBtn.SetActive(LAllianceHelp.I.HelpList.Count > 0);
            _desc.text = $"{LocalizationMgr.Get("Alliance_Help_Text2")} <color=#34E36E>{AllianceGameData.I.AllianceHelp.unionPoint}/{MetaConfig.union_help_daily_max_contribute}</color>";
            _timeObj?.SetActive(true);

            _noMessageTip.SetActive(this._listView.maxNum == 0);

            UpdateHelpInfo();
        }

        private void UpdateHelpInfo()
        { 
            RequestNum.text = string.Format(LocalizationMgr.Get("Alliance_Help_Apply_Times"), AllianceGameData.I.AllianceHelp.GetTotalCount(), MetaConfig.UnionHelp_ApplicationLimitTotal);
            foreach (var item in _helpList)
            {
                if (item.playerID == LPlayer.I.PlayerID)
                {
                    curHelpInfo = item;
                   
                    RequestObj.SetActive(false); 
                    return;
                }
            }
            RequestObj.SetActive(true);
        }


        /// <summary>
        /// ˢ������ʱ��
        /// </summary>
        private void RefreshResetTime()
        {
            #region ������ 0�㻹�ж���ʱ��

            if (this._time != null)
            {
                var cur = TFW.Common.ConvertUnixTimeStampToDateTime(GameTime.Time);
                //DateTime today = new DateTime(cur.Year, cur.Month, cur.Day);
                DateTime today2 = new DateTime(cur.Year, cur.Month, cur.Day).AddDays(1);
                //var totalTime = today2 - today;//24Сʱ
                var remainTime = today2 - cur;
                _time.text = $"{string.Format(LocalizationMgr.Get("Alliance_Help_Text4"), UIHelper.GetFormatTime((long)remainTime.TotalMilliseconds))}";
            }
            #endregion
        }

        
        private void OnSetDescText(object[] obj)
        {
            _desc.text = $"{LocalizationMgr.Get("Alliance_Help_Text2")} <color=#34E36E>{AllianceGameData.I.AllianceHelp.unionPoint}/{MetaConfig.union_help_daily_max_contribute}</color>";
        }
        /// <summary>
        /// ����
        /// </summary>
        /// <param name="obj"></param>
        private void OnCoordsFavoritesAck(object[] obj)
        {
            this.RefreshList();
        }

        /// <summary>
        /// ����
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpNtf(object[] obj)
        {
            this.RefreshList();
        }

        /// <summary>
        /// ��������
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpAck(object[] obj)
        {
            List<UnionHelpInfo> list = (List<UnionHelpInfo>)obj[0];
            if (list.Count > 0)
            {
                this.RefreshList();
            }
        }

        /// <summary>
        /// ɾ��
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpDelNtf(object[] obj)
        {
            //var delIds = (List<long>)obj[0];
            this.RefreshList();
            //FloatTips.I.FloatMsg(LocalizationMgr.Get("Coordinate_Record_Delete"));
        }
    }


}