// Amplify Shader Editor - Visual Shader Editing Tool
// Copyright (c) Amplify Creations, Lda <<EMAIL>>

namespace AmplifyShaderEditor
{
	[System.Serializable]
	[NodeAttributes( "Camera To World Matrix", "Matrix Transform", "Current camera to world matrix" )]
	public sealed class CameraToWorldMatrix : ConstantShaderVariable
	{
		protected override void CommonInit( int uniqueId )
		{
			base.CommonInit( uniqueId );
			ChangeOutputProperties( 0, "Out", WirePortDataType.FLOAT4x4 );
            m_value = "unity_CameraToWorld";
			m_drawPreview = false;
		}

		public override string GenerateShaderForOutput( int outputId, ref MasterNodeDataCollector dataCollector, bool ignoreLocalvar )
		{
			GeneratorUtils.RegisterUnity2019MatrixDefines( ref dataCollector );
			return base.GenerateShaderForOutput( outputId, ref dataCollector, ignoreLocalvar );
		}
	}
}
