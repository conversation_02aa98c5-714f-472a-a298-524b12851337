﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public partial class GridRegionEditor
    {
        GameObject mPlaneObject;
        Mesh mMesh;
        Material mMaterial;
        Texture2D mGridTexture;

        void DestroyTexturePlane()
        {
            Object.DestroyImmediate(mMesh);
            Object.DestroyImmediate(mMaterial);
            Object.DestroyImmediate(mGridTexture);
            Utils.DestroyObject(mPlaneObject);
        }

        void CreateTexturePlane(float width, float height)
        {
            var map = Map.currentMap;
            if (map != null)
            {
                if (mPlaneObject == null && mHorizontalGridCount > 0)
                {
                    //create region texture and materials
                    mGridTexture = new Texture2D(mHorizontalGridCount, mVerticalGridCount, TextureFormat.RGBA32, false, false);
                    Color32[] gridColors = new Color32[mHorizontalGridCount * mVerticalGridCount];
                    for (int i = 0; i < mVerticalGridCount; ++i)
                    {
                        for (int j = 0; j < mHorizontalGridCount; ++j)
                        {
                            int regionType = mGrids[i, j];
                            var template = GetGridTemplate(regionType);
                            if (template != null)
                            {
                                gridColors[i * mHorizontalGridCount + j] = template.color;
                            }
                        }
                    }

                    mGridTexture.filterMode = FilterMode.Point;
                    mGridTexture.SetPixels32(gridColors);
                    mGridTexture.Apply();

                    //create plane
                    mPlaneObject = new GameObject("region plane");
                    mPlaneObject.SetActive(true);
                    mPlaneObject.transform.parent = mTileRoot.transform;
                    var meshRenderer = mPlaneObject.AddComponent<MeshRenderer>();
                    var meshFilter = mPlaneObject.AddComponent<MeshFilter>();
                    meshFilter.sharedMesh = CreateMesh(width, height);
                    meshRenderer.sharedMaterial = CreateMaterial();
                    meshRenderer.sharedMaterial.SetTexture("_MainTex", mGridTexture);
                }
            }
        }

        Mesh CreateMesh(float mapWidth, float mapHeight)
        {
            if (mMesh == null)
            {
                mMesh = new Mesh();
                mMesh.vertices = new Vector3[]{
                    new Vector3(0, 0, 0),
                    new Vector3(0, 0, mapHeight),
                    new Vector3(mapWidth, 0, mapHeight),
                    new Vector3(mapWidth, 0, 0),
                };
                mMesh.uv = new Vector2[] {
                    new Vector2(0, 0),
                    new Vector2(0, 1),
                    new Vector2(1, 1),
                    new Vector2(1, 0),
                };
                mMesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            }
            return mMesh;
        }

        Material CreateMaterial()
        {
            if (mMaterial == null)
            {
                mMaterial = new Material(Shader.Find("SLGMaker/DiffuseTransparent"));
            }
            return mMaterial;
        }

        void SetPixels(int x, int y, int width, int height, Color32[] pixels)
        {
            mGridTexture.SetPixels32(x, y, width, height, pixels);
            mGridTexture.Apply();
        }

        public void RefreshTexture()
        {
            Color32[] colors = new Color32[mVerticalGridCount * mHorizontalGridCount];
            Color32 black = new Color32(0, 0, 0, 0);
            int idx = 0;
            for (int i = 0; i < mVerticalGridCount; ++i)
            {
                for (int j = 0; j < mHorizontalGridCount; ++j)
                {
                    var template = GetGridTemplate(mGrids[i, j]);
                    if (template != null)
                    {
                        colors[idx] = template.color;
                    }
                    else
                    {
                        colors[idx] = black;
                    }
                    ++idx;
                }
            }
            SetPixels(0, 0, mHorizontalGridCount, mVerticalGridCount, colors);
        }
    }
}


#endif