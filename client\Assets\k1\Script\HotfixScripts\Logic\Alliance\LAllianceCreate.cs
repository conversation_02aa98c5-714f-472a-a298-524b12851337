﻿








using Common;
using cspb;
using THelper;
using TFW.Localization;
using UI;
using Game.Data;
using Logic.Alliance.Achievement;
using DeepUI;

namespace Logic
{
    /// <summary>
    /// 联盟创建 Logic
    /// </summary>
    public class LAllianceCreate : Ins<LAllianceCreate>
    {
        #region Property
        #endregion

        #region Interface

        public void Init()
        {
            // 联盟 简称|名称|公告 检查结果 ack
            MessageMgr.RegisterMsg<CheckUnionCreateStrAck>(this, OnCheckUnionCreateStrAck);
            // 创建 联盟 ack
            MessageMgr.RegisterMsg<CreateUnionAck>(this, OnCreateUnionAck);
        }

        public void DeInit()
        {
            MessageMgr.UnregisterMsg(this);
        }

        /// <summary>
        /// 检查联盟 名称|简称|公告 合法性
        /// </summary>
        /// <param name="str"></param>
        /// <param name="typ"></param>
        public void CheckUnionCreateStrReq(string str, AllianceDescEnum typ)
        {
            var req = new CheckUnionCreateStrReq()
            {
                str = str,
                typ = (int)typ,
            };

            Debug("CheckUnionCreateStrReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 创建联盟
        /// </summary>
        /// <param name="nickName">简称</param>
        /// <param name="name">名称</param>
        /// <param name="desc">公告</param>
        /// <param name="unionFlag">联盟旗帜</param>
        /// <param name="needApply">是否需要申请</param>
        /// <param name="lang">联盟语言</param>
        public void CreateUnionReq(string nickName, string name, string desc, UnionFlagInfo unionFlag, bool needApply,
            int lang, int limitLv, bool isFree = false)
        {
            uint flag = LAllianceMgr.I.Table2UnionFlag(unionFlag);

            var req = new CreateUnionReq()
            {
                nickName = nickName,
                name = name,
                flag = flag,
                needApply = needApply,
                lang = lang,
                limitLv = limitLv,
                manifesto = desc,
                isFree = isFree
            };

            LAllianceMgr.I.UnionCreatingMark(true);

            Debug("CreateUnionReq req = {0}", req);
            MessageMgr.Send(req);
        }

        public void NewCreateUnionReq(string nickName, string name, string lan, UnionFlagInfo unionFlag)
        {
            uint flag = LAllianceMgr.I.Table2UnionFlag(unionFlag);
            var req = new CreateUnionReq()
            {
                nickName = nickName,
                name = name,
                flag = flag,
                language = lan
            };
            LAllianceMgr.I.UnionCreatingMark(true);
            MessageMgr.Send(req);
        }
        #endregion

        #region Method
        /// <summary>
        /// 检查联盟 名称|简称|公告 合法性 ack
        /// </summary>
        /// <param name="args"></param>
        void OnCheckUnionCreateStrAck(CheckUnionCreateStrAck args)
        {
            Debug("OnCheckUnionCreateStrAck args = {0}", args);

            var typ = args.typ;
            var checkRet = args.checkRet;

            EventMgr.FireEvent(TEventType.AllianceCreateCheck, typ, checkRet);
        }

        /// <summary>
        /// 创建联盟 ack
        /// </summary>
        /// <param name="args"></param>
        void OnCreateUnionAck(CreateUnionAck args)
        {
            Debug("OnCreateUnionAck args = {0}", args);
            var ok = args.errCode == 0;
            LAllianceMgr.I.UnionCreatingMark(false);
            

            if (ok)
            {
                EventMgr.FireEvent(TEventType.AllianceCreate, ok);
                UITools.PopTips(LocalizationMgr.Get("MAIL_union_create_title"));
                PopupManager.I.ShowLayer<UI.Alliance.UIAllianceMain>(new UI.Alliance.UIAllianceMainData
                {
                    CurrentTabKey = GameData.I.MainData.CurrMenuType,
                });
                LAllianceNewAchievement.I.ReqAllianceAchieveInfoReq();
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                //var chatMenu = WndMgr.Show<UI.Alliance.UIAllianceMain>("UIAllianceMain", new UI.Alliance.UIAllianceMainData
                //{
                //    CurrentTabKey = GameData.I.MainData.CurrMenuType,
                //});
                //LAllianceMgr.I.ShowAllianceMain("");
            }
            else 
            {
                //FloatTips.I.FloatErrcodeMsg(args.errCode);
            }

        }
        #endregion

        #region Field
        #endregion
    }
}
