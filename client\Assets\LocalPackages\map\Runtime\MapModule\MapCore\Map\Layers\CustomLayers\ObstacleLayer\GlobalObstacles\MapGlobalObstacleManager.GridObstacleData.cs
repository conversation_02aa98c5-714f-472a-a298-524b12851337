﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapGlobalObstacleManager
    {
        public void InitGridObstacles(byte[] gridObstacles, float gridSize)
        {
            mGridSize = gridSize;
            if (gridObstacles != null && gridObstacles.Length > 0)
            {
                mObstacleGrids = gridObstacles;
            }
        }

        public void GenerateGridObstacleData(float gridSize)
        {
#if UNITY_EDITOR
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            mGridSize = gridSize;
            int horizontalGridCount = Mathf.CeilToInt(mMapWidth / gridSize);
            int verticalGridCount = Mathf.CeilToInt(mMapWidth / gridSize);
            int byteCount = Mathf.CeilToInt((float)(horizontalGridCount * verticalGridCount) / 8.0f);
            mObstacleGrids = new byte[byteCount];
            for (int y = 0; y < verticalGridCount; ++y)
            {
                for (int x = 0; x < horizontalGridCount; ++x)
                {
                    float minX = x * gridSize;
                    float minZ = y * gridSize;
                    float maxX = minX + gridSize;
                    float maxZ = minZ + gridSize;

                    if (mQuadTree.IsTriangleIntersectedWithRectangle(minX, minZ, maxX, maxZ))
                    {
                        SetObstacle(x, y, horizontalGridCount);
#if false
                        var obj = new GameObject();
                        obj.transform.parent = testRoot.transform;
                        var dp = obj.AddComponent<DrawBounds>();
                        dp.bounds = new Bounds();
                        dp.bounds.SetMinMax(new Vector3(minX, 0, minZ), new Vector3(maxX, 0, maxZ));
#endif
                    }
                }
            }
            var time = w.Stop();
            Debug.Log($"GenerateGridObstacleData cost {time}");

            RefreshTexture();
#endif
        }

        void SetObstacle(int x, int y, int horizontalGridCount)
        {
            int idx = y * horizontalGridCount + x;
            int byteIndex = idx / 8;
            int mod = idx % 8;
            byte mask = (byte)((byte)0x1 << mod);
            mObstacleGrids[byteIndex] |= mask;
        }

        public bool IsCollidedWithObstacle(int gx, int gy)
        {
            int horizontalGridCount = Mathf.CeilToInt(mMapWidth / gridSize);
            int idx = gy * horizontalGridCount + gx;
            int byteIndex = idx / 8;
            int mod = idx % 8;
            byte mask = (byte)((byte)0x1 << mod);
            return (mObstacleGrids[byteIndex] & mask) == mask;
        }

        public float gridSize
        {
            get { return mGridSize; }
            set
            {
                if (!Mathf.Approximately(mGridSize, value))
                {
                    mGridSize = value;
                    mObstacleGrids = null;
                }
            }
        }

        public byte[] obstacleGrids
        {
            get { return mObstacleGrids; }
        }

        float mGridSize = 0;
        byte[] mObstacleGrids;
    }
}
