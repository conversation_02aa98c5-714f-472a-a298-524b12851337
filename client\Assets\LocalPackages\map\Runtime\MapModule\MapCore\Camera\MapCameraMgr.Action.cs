﻿using System;
using System.Collections.Generic;

namespace TFW.Map
{
    //相机行为的更新顺序,前面的行为会导致后面的行为失效
    public enum CameraActionType
    {
        //global actions
        CurveZoom,
        MoveToTarget,
        MoveToTargetRev,
        MoveToTargetStraight,
        MoveToTargetInstantly,
        Maya<PERSON><PERSON>,
        <PERSON>roll,
        Drag,
        DragRotate,
        Zoom,
        FollowTarget,
        ChangeCameraHeight,
        AutoUpdateHeight,
        CollisionResolve,
        CollisionCheck,
        KeyboardMovement,

        //local actions
        Shake,

        /// <summary>
        /// 方向移动
        /// </summary>
        DirMovement,
    }

    public static partial class MapCameraMgr
    {
        static Dictionary<CameraActionType, int> mTypeToUpdateOrder = new Dictionary<CameraActionType, int>()
        {
            { CameraActionType.CurveZoom, 1},
            { CameraActionType.MoveToTarget, 200},
            { CameraActionType.MoveToTargetRev, 200},
            { CameraActionType.MoveToTargetStraight, 200},
            { CameraActionType.MoveToTargetInstantly, 200},
            { CameraActionType.MayaZoom, 500},
            { CameraActionType.Scroll, 600},
            { CameraActionType.Drag, 700},
            { CameraActionType.DragRotate, 750},
            { CameraActionType.Zoom, 800},
            { CameraActionType.FollowTarget, 900},
            { CameraActionType.ChangeCameraHeight, 1000},
            { CameraActionType.AutoUpdateHeight, 1100},
            { CameraActionType.CollisionResolve, 1200},
            { CameraActionType.CollisionCheck, 1300},
            { CameraActionType.KeyboardMovement, 1400},
            { CameraActionType.Shake, 1500},
            { CameraActionType.DirMovement, 1600},
        };

        static void AddGlobalAction(CameraAction action)
        {
            if (GetGlobalAction(action.GetType()) == null)
            {
                mGlobalActions.Add(action);
                mGlobalActions.Sort(new ComapreCameraAction());
            }
        }

        static void AddLocalAction(CameraAction action)
        {
            if (GetLocalAction(action.GetType()) == null)
            {
                mLocalActions.Add(action);
                mLocalActions.Sort(new ComapreCameraAction());
            }
        }

        static CameraAction GetGlobalAction(Type type)
        {
            for (int i = 0; i < mGlobalActions.Count; ++i)
            {
                if (mGlobalActions[i].GetType() == type)
                {
                    return mGlobalActions[i];
                }
            }

            return null;
        }

        static CameraAction GetGlobalAction(CameraActionType type)
        {
            for (int i = 0; i < mGlobalActions.Count; ++i)
            {
                if (mGlobalActions[i].actionType == type)
                {
                    return mGlobalActions[i];
                }
            }

            return null;
        }

        static CameraAction GetLocalAction(Type type)
        {
            for (int i = 0; i < mLocalActions.Count; ++i)
            {
                if (mLocalActions[i].GetType() == type)
                {
                    return mLocalActions[i];
                }
            }

            return null;
        }

        public static void BlockOtherActionsWhenCameraMoveToTarget(bool block)
        {
            var action = GetGlobalAction(typeof(CameraMoveToTarget)) as CameraMoveToTarget;
            if (action != null)
            {
                action.blockOtherActions = block;
            }
        }

        public static void StopAction(CameraActionType type)
        {
            var action = GetGlobalAction(type);
            if (action != null)
            {
                bool enabled = action.enabled;
                action.isFinished = true;
                if (enabled)
                {
                    action.OnFinish();
                }
            }
        }
    }
}
