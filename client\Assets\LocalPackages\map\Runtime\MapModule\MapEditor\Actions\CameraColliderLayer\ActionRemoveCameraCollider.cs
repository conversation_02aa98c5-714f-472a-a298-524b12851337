﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionRemoveCameraCollider : EditorAction
    {
        public ActionRemoveCameraCollider(int layerID, int dataID)
        {
            mLayerID = layerID;
            mDataID = dataID;
            var data = Map.currentMap.FindObject(dataID) as CameraColliderData;
            var navMeshObstacleOutline = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            mBottomOutline = new List<Vector3>(navMeshObstacleOutline.Count);
            mBottomOutline.AddRange(navMeshObstacleOutline);
            mTopOutline = new List<Vector3>();
            var topOutline = data.topOutline;
            if (topOutline != null)
            {
                mTopOutline.AddRange(topOutline.outline);
            }
            mHeight = data.height;
            mMeshVertices = data.verticesCopy;
            mMeshIndices = data.indicesCopy;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }

            var data = new CameraColliderData(mDataID, Map.currentMap, mBottomOutline, mTopOutline, layer.displayVertexRadius, mHeight, mMeshVertices, mMeshIndices);
            layer.AddObject(data);
            return true;
        }

        int mLayerID;
        int mDataID;
        float mHeight;
        List<Vector3> mBottomOutline;
        List<Vector3> mTopOutline;
        Vector3[] mMeshVertices;
        int[] mMeshIndices;
    }
}

#endif