﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadSplitFogLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.SplitFogLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            float fogHeight = reader.ReadSingle();
            var tiles = Utils.ReadIntArray(reader);

            var config = LoadSplitFogLayerLODConfigV1(reader);

            string fogLOD1PrefabPath = Utils.ReadString(reader);
            string selectionMaterialPath = Utils.ReadString(reader);
            string fogMaskTexPropertyName = Utils.ReadString(reader);
            string[] prefabPaths = Utils.ReadStringArray(reader);
            //-----------------------version 1 end------------------------------

            var layer = new config.SplitFogLayerData(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, tiles, prefabPaths, fogHeight, fogLOD1PrefabPath, selectionMaterialPath, fogMaskTexPropertyName);
            return layer;
        }

        config.MapLayerLODConfig LoadSplitFogLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                int flag = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, (MapLayerLODConfigFlag)flag, 0);
            }
            return config;
        }
    }
}
