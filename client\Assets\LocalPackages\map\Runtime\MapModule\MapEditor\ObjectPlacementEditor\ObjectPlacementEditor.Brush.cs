﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        public class BrushObject
        {
            public string prefabPath;
            public Vector3 position;
            public Vector3 scale;
            public Quaternion rotation;
            public string tag;
            public int layer;
        }

        public class Brush
        {
            public string name;
            public List<BrushObject> objects = new List<BrushObject>();
        }

        void DrawBrushUI()
        {
            mShowCustomPrefabBrush = EditorGUILayout.Foldout(mShowCustomPrefabBrush, "Brush");
            if (mShowCustomPrefabBrush)
            {
                if (GUILayout.Button("Create"))
                {
                    var dlg = EditorUtils.CreateInputDialog("Set Sprite Prefab Count");
                    var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", "brush"),
                    };
                    dlg.Show(items, OnClickCreateBrush);
                }

                for (int i = 0; i < mBrushies.Count; ++i)
                {
                    bool removed = DrawBrush(i, mBrushies[i]);
                    if (removed)
                    {
                        break;
                    }
                }
            }
        }

        bool OnClickCreateBrush(List<InputDialog.Item> parameters)
        {
            string text = (parameters[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(text))
            {
                return false;
            }

            CreateBrush(text);
            return true;
        }

        bool DrawBrush(int idx, Brush brush)
        {
            bool removed = false;
            EditorGUILayout.BeginHorizontal();
            bool selected = EditorGUILayout.Toggle("", mSelectedBrush == idx, GUILayout.MaxWidth(30));
            if (selected)
            {
                SetSelectedBrush(idx);
            }
            brush.name = EditorGUILayout.TextField(brush.name);
            if (GUILayout.Button("X", GUILayout.MaxWidth(30)))
            {
                RemoveBrush(idx);
                removed = true;
            }
            EditorGUILayout.EndHorizontal();
            return removed;
        }

        void CreateBrush(string name)
        {
            var objects = Selection.gameObjects;
            if (objects.Length == 0)
            {
                EditorUtility.DisplayDialog("Error", $"select at least one prefab!", "OK");
                return;
            }

            for (int i = 0; i < objects.Length; ++i)
            {
                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(objects[i]);
                string assetPath = AssetDatabase.GetAssetPath(childPrefab);
                if (string.IsNullOrEmpty(assetPath))
                {
                    EditorUtility.DisplayDialog("Error", $"{objects[i].name} is not prefab!", "OK");
                    return;
                }
            }

            Brush brush = new Brush();
            brush.name = name;
            var center = Utils.CalculateCentroid(objects);
            for (int i = 0; i < objects.Length; ++i)
            {
                var brushObject = new BrushObject();
                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(objects[i]);
                string assetPath = AssetDatabase.GetAssetPath(childPrefab);
                brushObject.prefabPath = assetPath;
                brushObject.position = objects[i].transform.position - center;
                brushObject.rotation = objects[i].transform.rotation;
                brushObject.scale = objects[i].transform.localScale;
                brushObject.tag = objects[i].tag;
                brushObject.layer = objects[i].layer;
                brush.objects.Add(brushObject);
            }

            mBrushIndicator.SetBrush(brush, mToolObjectRoot.transform);

            mBrushies.Add(brush);

            SetSelectedBrush(mBrushies.Count - 1);
        }

        void RemoveBrush(int idx)
        {
            mBrushies.RemoveAt(idx);
            SetSelectedBrush(mBrushies.Count - 1);
        }

        void DrawCustomBrushSceneGUI(Event e, Vector3 worldPos)
        {
            if (e.alt)
            {
                return;
            }

            if (mSelectedBrush >= 0)
            {
                if (mBrushIndicator != null)
                {
                    mBrushIndicator.ShowBrush();
                    mBrushIndicator.Update(worldPos, mPrefabRotationSetting.rotation);
                    if (e.type == EventType.MouseDown && e.button == 0)
                    {
                        ApplyBrush(worldPos);
                    }
                }
            }
        }

        void ApplyBrush(Vector3 cursorWorldPos)
        {
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
            var brush = mBrushies[mSelectedBrush];
            var objects = brush.objects;
            for (int i = 0; i < brush.objects.Count; ++i)
            {
                //var pos = mPrefabRotationSetting.rotation * objects[i].position;
                var pos = objects[i].position;
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(objects[i].prefabPath);
                CreateObjectEx(pos + cursorWorldPos, objects[i].scale, objects[i].rotation, objects[i].tag, objects[i].layer, prefab, stage.prefabContentsRoot.transform);
            }
        }

        void SetSelectedBrush(int idx)
        {
            if (mSelectedBrush != idx)
            {
                mSelectedBrush = idx;
                if (idx >= 0)
                {
                    mBrushIndicator.SetBrush(mBrushies[idx], mToolObjectRoot.transform);
                }
                else
                {
                    mBrushIndicator.OnDestroy();
                }
            }
        }

        List<Brush> mBrushies = new List<Brush>();
        ObjectPlacementEditorBrushIndicator mBrushIndicator = new ObjectPlacementEditorBrushIndicator();
        int mSelectedBrush = -1;
    }
}

#endif