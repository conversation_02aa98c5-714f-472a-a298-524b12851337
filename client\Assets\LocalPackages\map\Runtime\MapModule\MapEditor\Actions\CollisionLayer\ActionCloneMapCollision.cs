﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionCloneMapCollision : EditorAction
    {
        public ActionCloneMapCollision(int layerID, int cloneDataID, int newDataID, float xOffset)
        {
            mLayerID = layerID;
            mDataID = newDataID;
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            var data = layer.layerData.GetObjectData(cloneDataID) as MapCollisionData;
            Vector3 offset = new Vector3(xOffset, 0, 0);
            mVertices = new List<Vector3>[2]
            {
                data.GetOutlineVerticesCopy(PrefabOutlineType.NavMeshObstacle),
                data.GetOutlineVerticesCopy(PrefabOutlineType.ObjectPlacementObstacle),
            };

            for (int i = 0; i < 2; ++i)
            {
                for (int k = 0; k < mVertices[i].Count; ++k)
                {
                    mVertices[i][k] = mVertices[i][k] + offset;
                }
            }

            mIsExtendable = data.IsExtendable();
            mAttribute = data.attribute;
            mType = data.type;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            var outlineDatas = new OutlineData[2]
            {
                new OutlineData(mVertices[0]),
                new OutlineData(mVertices[1]),
            };
            var data = new MapCollisionData(mDataID, Map.currentMap, outlineDatas, layer.displayVertexRadius, mIsExtendable, mAttribute, mType, true);
            layer.AddObject(data);

            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        int mLayerID;
        int mDataID;
        int mType;
        bool mIsObstacle;
        bool mIsExtendable;
        CollisionAttribute mAttribute;
        List<Vector3>[] mVertices;
    }
}

#endif