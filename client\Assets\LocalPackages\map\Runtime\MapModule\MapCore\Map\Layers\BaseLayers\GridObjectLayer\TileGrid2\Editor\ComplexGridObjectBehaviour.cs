﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [SelectionBase]
    [ExecuteInEditMode]
    class ComplexGridObjectBehaviour : MonoBehaviour
    {
#if UNITY_EDITOR
        public void Init(int objectID)
        {
            mObjectID = objectID;
            Reset();
        }

        void OnDestroy()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                var layer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as EditorComplexGridModelLayer;
                if (layer != null)
                {
                    layer.AddDestroyedObject(mObjectID);
                }
            }
        }

        public bool IsTransformChanged()
        {
            return
                mInitPosition != gameObject.transform.localPosition ||
                mInitScale != gameObject.transform.localScale ||
                mInitRotation != gameObject.transform.localRotation;
        }

        public void Reset()
        {
            mInitPosition = gameObject.transform.localPosition;
            mInitRotation = gameObject.transform.localRotation;
            mInitScale = gameObject.transform.localScale;
        }

        public string objectTag
        {
            set
            {
                var data = Map.currentMap.FindObject(mObjectID) as ComplexGridModelData;
                if (data != null)
                {
                    data.objectTag = value;
                }
            }
            get
            {
                var data = Map.currentMap.FindObject(mObjectID) as ComplexGridModelData;
                if (data != null)
                {
                    return data.objectTag;
                }

                return "";
            }
        }

#endif

        public int objectID { get { return mObjectID; } }

        Vector3 mInitPosition;
        Quaternion mInitRotation;
        Vector3 mInitScale;

        int mObjectID;
    }
}

#endif