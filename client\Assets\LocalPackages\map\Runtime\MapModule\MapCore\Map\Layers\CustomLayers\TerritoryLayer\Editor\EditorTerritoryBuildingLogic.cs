﻿#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    class EditorTerritoryBuildingLogic : MonoBehaviour
    {
        void OnDestroy()
        {
            if (Map.currentMap != null)
            {
                if (mBuilding.isDestroying)
                {
                    return;
                }

                var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY) as EditorTerritoryLayer;
                layer.RemoveTerritoryBuilding(mBuilding, false);
            }
        }

        public void SetBuilding(EditorTerritoryBuilding building)
        {
            mBuilding = building;
        }

        EditorTerritoryBuilding mBuilding;
    }
}

#endif