﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class MapLocalObstacleViewer
    {
        public MapLocalObstacleViewer(string materialPath)
        {
            if (string.IsNullOrEmpty(materialPath))
            {
                materialPath = "Assets/Framework/Script/Map/MapModuleRes/RuntimeRes/obstacle/obstacle_material.mat";
            }
            mObstacleMaterialPath = materialPath;
        }

        public void OnDestroy()
        {
            foreach (var p in mPoolObjects)
            {
                var list = p.Value;
                for (int i = 0; i < list.Count; ++i)
                {
                    Utils.DestroyObject(list[i]);
                }
            }

            mPoolObjects = null;
        }

        public GameObject Require(int modelTemplateID, MapLocalObstacleTileData tileData)
        {
            GameObject gameObject = null;
            List<GameObject> objects;
            mPoolObjects.TryGetValue(modelTemplateID, out objects);
            if (objects == null)
            {
                objects = new List<GameObject>();
                mPoolObjects[modelTemplateID] = objects;
            }

            if (objects.Count == 0)
            {
                gameObject = CreateObstacleView(tileData);
            }
            else
            {
                gameObject = objects[objects.Count - 1];
                objects.RemoveAt(objects.Count - 1);
            }

            return gameObject;
        }

        public void Release(int modelTemplateID, GameObject gameObject)
        {
            Debug.Assert(gameObject != null);
            mPoolObjects[modelTemplateID].Add(gameObject);
        }

        GameObject CreateObstacleView(MapLocalObstacleTileData tileData)
        {
            var obj = new GameObject("Obstacle View");
            Utils.HideGameObject(obj);
            var renderer = obj.AddComponent<MeshRenderer>();
            renderer.sharedMaterial = MapModuleResourceMgr.LoadMaterial(mObstacleMaterialPath);
            var meshFilter = obj.AddComponent<MeshFilter>();
            var mesh = new Mesh();
            mesh.vertices = tileData.vertices;
            mesh.uv = tileData.uvs;
            mesh.triangles = tileData.triangles;
            meshFilter.sharedMesh = mesh;
            mesh.UploadMeshData(true);

            return obj;
        }

        public string obstacleMaterialPath { get { return mObstacleMaterialPath; } }

        Dictionary<int, List<GameObject>> mPoolObjects = new Dictionary<int, List<GameObject>>();
        string mObstacleMaterialPath;
    }
}
