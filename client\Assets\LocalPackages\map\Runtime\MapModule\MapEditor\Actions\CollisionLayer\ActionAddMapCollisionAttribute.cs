﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ActionAddMapCollisionAttribute : EditorAction
    {
        public ActionAddMapCollisionAttribute(int layerID, MapCollisionData data, CollisionAttribute attribute)
        {
            mLayerID = layerID;
            mAttribute = attribute;
            mCollisionDataID = data.id;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            return layer.AddCollisionAttribute(mCollisionDataID, mAttribute);
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            return layer.RemoveCollisionAttribute(mCollisionDataID, mAttribute);
        }

        int mCollisionDataID;
        int mLayerID;
        //bool mOriginalCheckFlag;
        //bool mNewCheckFlag;
        CollisionAttribute mAttribute;
    }
}


#endif