﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //检查烘培的prefab是否有效
    public partial class SkeletonAnimationBaker
    {
        public override string Check(GameObject prefab)
        {
            if (!Utils.IsTransformIdentity(prefab))
            {
                EditorUtility.DisplayDialog("Warning", $"{prefab.name} transform is not identity, baked position maybe not be correct!" +
                    "\nPrefab的transform值不是默认值,可能会导致烘培后的game object位置不对", "OK");
            }

            var skinnedMeshRenderers = prefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            if (skinnedMeshRenderers.Length == 0)
            {
                return "no skinned mesh renderers found!";
            }

            for (int i = 0; i < skinnedMeshRenderers.Length; ++i)
            {
                var r = skinnedMeshRenderers[i];
                var bones = r.bones;
                var bindPoses = r.sharedMesh.bindposes;

                if (bones.Length != bindPoses.Length)
                {
                    return $"skinned renderer {r.name} bone and bindpose count don't match!";
                }

                string msg = "";
                for (int k = 0; k < bones.Length; ++k)
                {
                    var bone = mBones.GetBoneTransform(bones[k].name);
                    if (bone == null)
                    {
                        mBones.AddBone(bones[k], bindPoses[k]);
                    }
                    else
                    {
                        var pose = mBones.GetBindPose(bones[k].name);
                        if (bindPoses[k] != pose)
                        {
                            var renderer = FindRendererUseThisBone(skinnedMeshRenderers, r, bones[k].name, bindPoses[k]);
                            msg += $"skinned renderer {r.name} and {renderer.name} use different({bones[k].name}) bindposes## ";
                        }
                    }
                }

                if (msg.Length > 0)
                {
                    return msg;
                }
            }

            List<Transform> shaderDrivenCustomBones;
            List<Transform> cpuDrivenCustomBones;
            FindCustomBones(out shaderDrivenCustomBones, out cpuDrivenCustomBones);

            //check custom bones
            var root = mBones.GetRootBone();
            for (int i = 0; i < shaderDrivenCustomBones.Count; ++i)
            {
                if (shaderDrivenCustomBones[i] != null && Utils.FindGameObject(root.gameObject, shaderDrivenCustomBones[i].name) == null)
                {
                    return $"invalid custom bone {shaderDrivenCustomBones[i].name}";
                }
            }

            for (int i = 0; i < shaderDrivenCustomBones.Count; ++i)
            {
                if (shaderDrivenCustomBones[i] != null && mBones.GetBoneIndex(shaderDrivenCustomBones[i]) >= 0)
                {
                    return $"can't bake exported bone as custom bone {shaderDrivenCustomBones[i].name}";
                }
            }

            //获取所有需要处理的mesh renderer
            var validRenderers = GetValidRenderers(prefab);

            for (int i = 0; i < validRenderers.Count; ++i)
            {
                if (validRenderers[i].GetType() == typeof(SkinnedMeshRenderer))
                {
#if false
                    var r = validRenderers[i] as SkinnedMeshRenderer;
                    if (r.sharedMesh.isReadable == false)
                    {
                        return $"mesh {r.sharedMesh.name} must be readable!";
                    }
#endif
                }
                else
                {
                    var meshFilter = validRenderers[i].GetComponent<MeshFilter>();
                    if (meshFilter.sharedMesh.isReadable == false)
                    {
                        return $"mesh {meshFilter.sharedMesh.name} must be readable!";
                    }
                }
            }

            //检查是否有相同命名的节点,只要求骨骼节点名字唯一
            HashSet<string> gameObjectNames = new HashSet<string>();
            var transforms = prefab.GetComponentsInChildren<Transform>(true);
            for (int i = 0; i < transforms.Length; ++i)
            {
                if (IsSomeKindOfBone(transforms[i].name, shaderDrivenCustomBones, cpuDrivenCustomBones))
                {
                    if (gameObjectNames.Contains(transforms[i].name))
                    {
                        return $"found duplicated bone name \"{transforms[i].name}\"!";
                    }
                    gameObjectNames.Add(transforms[i].name);
                }
            }

            mRenderers = validRenderers;
            mShaderDrivenCustomBones = shaderDrivenCustomBones;
            mCPUDrivenCustomBones = cpuDrivenCustomBones;

            return "";
        }

        bool IsSomeKindOfBone(string name, List<Transform> shaderDrivenCustomBones, List<Transform> cpuDrivenCustomBones)
        {
            if (mBones.GetBoneTransform(name) != null)
            {
                return true;
            }

            for (int i = 0; i < shaderDrivenCustomBones.Count; ++i)
            {
                if (name == shaderDrivenCustomBones[i].name)
                {
                    return true;
                }
            }

            for (int i = 0; i < cpuDrivenCustomBones.Count; ++i)
            {
                if (name == cpuDrivenCustomBones[i].name)
                {
                    return true;
                }
            }

            return false;
        }

        SkinnedMeshRenderer FindRendererUseThisBone(SkinnedMeshRenderer[] allRenderers, SkinnedMeshRenderer exceptionRenderer, string boneName, Matrix4x4 bindpose)
        {
            for (int i = 0; i < allRenderers.Length; ++i)
            {
                if (allRenderers[i] == exceptionRenderer)
                {
                    continue;
                }

                var bones = allRenderers[i].bones;
                var bindposes = allRenderers[i].sharedMesh.bindposes;
                for (int b = 0; b < bones.Length; ++b)
                {
                    if (bones[b].name == boneName && bindposes[b] != bindpose)
                    {
                        return allRenderers[i];
                    }
                }
            }

            Debug.Assert(false, "Can't be here!");
            return null;
        }

        //获取需要处理的renderer,包括skin renderer和挂接在骨骼节点下的mesh renderer
        List<Renderer> GetValidRenderers(GameObject prefab)
        {
            List<Renderer> renderers = new List<Renderer>();
            var skinRenderers = prefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            renderers.AddRange(skinRenderers);

            var rootBone = mBones.GetRootBone();
            var meshRenderers = rootBone.gameObject.GetComponentsInChildren<MeshRenderer>(true);
            for (int i = 0; i < meshRenderers.Length; ++i)
            {
                //忽略cpu animation的renderer
                if (meshRenderers[i].GetComponent<CPUAnimationTag>() == null)
                {
                    renderers.Add(meshRenderers[i]);
                }
            }

            return renderers;
        }

        bool IsAncestorHasCPUAnimationTag(Transform t)
        {
            t = t.parent;
            while (t != null)
            {
                if (t.GetComponent<CPUAnimationTag>() != null)
                {
                    return true;
                }
                t = t.parent;
            }
            return false;
        }

        //找出bone hierarchy中非bone的节点
        void FindCustomBones(out List<Transform> shaderDrivenBones, out List<Transform> cpuDrivenBones)
        {
            shaderDrivenBones = new List<Transform>();
            cpuDrivenBones = new List<Transform>();
            var root = mBones.GetRootBone();
            var bones = mBones.bones;
            var children = root.GetComponentsInChildren<Transform>(true);
            for (int i = 0; i < children.Length; ++i)
            {
                if (mBones.GetBoneIndex(children[i]) < 0)
                {
                    if (children[i].GetComponent<CPUAnimationTag>() != null)
                    {
                        cpuDrivenBones.Add(children[i]);
                    }
                    else if (IsAncestorHasCPUAnimationTag(children[i]))
                    {
                        //ignore children game objects of cpu bones
                    }
                    else
                    {
                        for (int b = 0; b < bones.Count; ++b)
                        {
                            if (Utils.IsChild(bones[b].transform.gameObject, children[i].gameObject) &&
                                children[i].GetComponent<SkinnedMeshRenderer>() == null)
                            {
                                shaderDrivenBones.Add(children[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
}
#endif