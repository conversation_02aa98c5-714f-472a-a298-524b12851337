﻿ 



 
 


using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //地图对象的模型层
    public class CityTerritoryLayerView : MapObjectLayerView
    {
        public CityTerritoryLayerView(MapLayerData layerData, bool asyncLoading)
        : base(layerData, asyncLoading)
        {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new CityTerritoryView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }

        public SharedModel RequireSharedModel(int objectID)
        {
            var cityTerritoryLayerData = layerData as CityTerritoryLayerData;
            var block = cityTerritoryLayerData.GetOwnerBlock(objectID);
            if (block == null)
            {
                return null;
            }
            mSharedModels.TryGetValue(block.prefabPath, out SharedModel model);
            if (model == null)
            {
                model = SharedModel.Require(layerData.map, block.prefabPath, block.position, Vector3.one, Quaternion.identity, OnRefCountBeZero);
                model.transform.SetParent(root.transform, false);
                mSharedModels.Add(block.prefabPath, model);
            }
            model.IncRef();
            return model;
        }

        void OnRefCountBeZero(SharedModel model)
        {
            mSharedModels.Remove(model.prefabPath);
        }

        Dictionary<string, SharedModel> mSharedModels = new Dictionary<string, SharedModel>();
    }
}
