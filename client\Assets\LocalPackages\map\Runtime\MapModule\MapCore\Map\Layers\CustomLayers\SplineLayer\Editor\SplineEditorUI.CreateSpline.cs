﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplineEditorUI : UnityEditor.Editor
    {
        void HandleCreateFunction(Event e, Vector3 worldPos)
        {
            if (e.button == 0)
            {
                if (e.type == EventType.MouseDown && e.alt == false)
                {
                    mAddedVertices.Add(worldPos);
                    if (mAddedVertices.Count == 1)
                    {
                        mAddedVertices.Add(worldPos);
                    }
                    mDisplayedVertices = mAddedVertices.ToArray();
                    SceneView.RepaintAll();
                }
                else if (e.type == EventType.MouseMove && e.alt == false)
                {
                    if (mAddedVertices.Count > 0)
                    {
                        mAddedVertices[mAddedVertices.Count - 1] = worldPos;
                        mDisplayedVertices[mDisplayedVertices.Length - 1] = worldPos;
                    }

                    SceneView.RepaintAll();
                }
            }
            else if (e.button == 1 && e.type == EventType.MouseDown)
            {
                //press right key
                CreateSpline();
                Repaint();
            }

            HandleUtility.AddDefaultControl(0);
        }

        public void DrawCreateFunctionSceneGUI()
        {
            Handles.DrawPolyLine(mDisplayedVertices);
        }

        public void DrawCreateFunctionInspectorGUI()
        {
        }

        bool CreateSpline()
        {
            int minVertexCount = mEditor.isLoop ? 4 : 3;
            bool created = mAddedVertices.Count >= minVertexCount;
            if (created)
            {
                bool valid = true;
                var mtl = AssetDatabase.LoadAssetAtPath<Material>(mEditor.splineMaterialPath);
                if (mtl != null)
                {
                    if (!mEditor.isCreatingRiver)
                    {
                        var texture = mtl.GetTexture("_MainTex");
                        if (texture == null)
                        {
                            valid = false;
                        }
                    }
                    
                    if (valid)
                    {
                        mAddedVertices.RemoveAt(mAddedVertices.Count - 1);

                        var controlPoints = CreateControlPoints(mAddedVertices);
                        SplineObject.Attribute attributes = SplineObject.Attribute.None;
                        if (mEditor.isCreatingRiver)
                        {
                            attributes |= SplineObject.Attribute.Loop;
                        }
                        var riverData = new SplineObject.RiverData();
                        riverData.isRiverObject = mEditor.isCreatingRiver;
                        var spline = mEditor.splineObjectManager.CreateSplineObject(controlPoints, mEditor.splineMaterialPath, 0, attributes, null, 1, "", riverData);
                        SetSelectedSpline(spline);
                        if (mEditor.isCreatingRiver)
                        {
                            SetMode(SplineEditor.Mode.EditControlPoint);
                            mSelectedControlPointIndex = 0;
                        }
                    }
                    else
                    {
                        Debug.LogError($"Invalid material {mEditor.splineMaterialPath}, there must be a property named _MainTex in shader!");
                        created = false;
                    }
                }
                else
                {
                    created = false;
                    Debug.LogError("Invalid material!");
                }
            }
            mAddedVertices.Clear();
            mDisplayedVertices = new Vector3[0];

            return created;
        }

        List<Vector3> mAddedVertices = new List<Vector3>();
        Vector3[] mDisplayedVertices = new Vector3[0];
    }
}

#endif