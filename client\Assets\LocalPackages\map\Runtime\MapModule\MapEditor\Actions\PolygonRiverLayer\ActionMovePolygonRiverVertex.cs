﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionMovePolygonRiverVertex : EditorAction
    {
        public ActionMovePolygonRiverVertex(int layerID, int dataID, int vertexIdx, Vector3 startPosition, PrefabOutlineType outlineType)
        {
            mDataID = dataID;
            mLayerID = layerID;
            mVertexIndex = vertexIdx;
            mStartPosition = startPosition;
            mEndPosition = startPosition;
            mOutlineType = outlineType;
        }

        public void SetEndPosition(Vector3 pos)
        {
            mEndPosition = pos;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetVertexPosition(layer.displayType, mDataID, mVertexIndex, mEndPosition);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetVertexPosition(layer.displayType, mDataID, mVertexIndex, mStartPosition);
            return true;
        }

        public bool isMoved { get { return mStartPosition != mEndPosition; } }

        public override string description
        {
            get
            {
                return string.Format("{0}, idx: {1}, start: {2}, end: {3}", GetType().Name, mVertexIndex, mStartPosition.ToString(), mEndPosition.ToString());
            }
        }

        Vector3 mEndPosition;
        Vector3 mStartPosition;
        int mDataID;
        int mLayerID;
        int mVertexIndex;
        PrefabOutlineType mOutlineType;
    }
}

#endif