%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1187798945428978630
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2433808627927726889}
  - component: {fileID: 1232760361407349354}
  m_Layer: 0
  m_Name: ObstacleOutline 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2433808627927726889
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1187798945428978630}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1232760361407349354
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1187798945428978630}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -20.131874, y: 0, z: 22.303537}
    - {x: -7.911519, y: 0, z: 27.15236}
    - {x: 0.9160595, y: 0, z: 16.714806}
    - {x: -2.0999231, y: 0, z: 9.297755}
    - {x: -13.773573, y: 0, z: 4.8050303}
    - {x: -20.873375, y: 0, z: 13.209906}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -13.2, y: 0, z: 3.094}
    - {x: -14.1629, y: 0, z: 3.3271}
    - {x: -16.7984, y: 0, z: 4.9974}
    - {x: -17.0242, y: 0, z: 5.1726}
    - {x: -17.6672, y: 0, z: 5.7779}
    - {x: -17.7556, y: 0, z: 5.8685}
    - {x: -19.0856, y: 0, z: 7.351}
    - {x: -19.0905, y: 0, z: 7.3564}
    - {x: -21.2228, y: 0, z: 9.7567}
    - {x: -21.5134, y: 0, z: 10.2441}
    - {x: -22.329, y: 0, z: 12.4842}
    - {x: -22.3639, y: 0, z: 12.593}
    - {x: -22.5281, y: 0, z: 13.1793}
    - {x: -22.5556, y: 0, z: 13.2951}
    - {x: -22.914, y: 0, z: 15.1228}
    - {x: -22.9294, y: 0, z: 15.6096}
    - {x: -22.2153, y: 0, z: 21.0563}
    - {x: -22.013, y: 0, z: 21.6377}
    - {x: -21.6687, y: 0, z: 22.213}
    - {x: -21.4591, y: 0, z: 22.4864}
    - {x: -19.7451, y: 0, z: 24.2584}
    - {x: -19.5728, y: 0, z: 24.4112}
    - {x: -17.9359, y: 0, z: 25.652}
    - {x: -17.6927, y: 0, z: 25.8023}
    - {x: -15.9129, y: 0, z: 26.68}
    - {x: -15.9066, y: 0, z: 26.6831}
    - {x: -15.5651, y: 0, z: 26.8495}
    - {x: -15.4465, y: 0, z: 26.9011}
    - {x: -10.0055, y: 0, z: 28.9946}
    - {x: -8.8282, y: 0, z: 28.9707}
    - {x: -5.5866, y: 0, z: 27.57}
    - {x: -5.016, y: 0, z: 27.1409}
    - {x: -4.0862, y: 0, z: 26.0126}
    - {x: -4.0842, y: 0, z: 26.0101}
    - {x: 1.9085, y: 0, z: 18.7062}
    - {x: 1.9592, y: 0, z: 18.6408}
    - {x: 2.8889, y: 0, z: 17.3709}
    - {x: 3.0801, y: 0, z: 15.8213}
    - {x: 0.5686, y: 0, z: 9.648}
    - {x: 0.2603, y: 0, z: 9.1721}
    - {x: -0.464, y: 0, z: 8.4151}
    - {x: -0.6351, y: 0, z: 8.2617}
    - {x: -2.4926, y: 0, z: 6.8367}
    - {x: -2.7422, y: 0, z: 6.6814}
    - {x: -4.7834, y: 0, z: 5.6737}
    - {x: -4.853, y: 0, z: 5.6415}
    - {x: -7.1155, y: 0, z: 4.6649}
    - {x: -7.2071, y: 0, z: 4.6288}
    - {x: -8.2595, y: 0, z: 4.2543}
    - {x: -8.3092, y: 0, z: 4.2376}
    - {x: -11.2526, y: 0, z: 3.3046}
    - {x: -11.5724, y: 0, z: 3.2404}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
