﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        void DrawCircleFillToolSceneGUI(Event e, Vector3 worldPos)
        {
            if (e.alt)
            {
                return;
            }

            Handles.DrawWireDisc(worldPos, Vector3.up, mCircleRadius);
            
            if (e.type == EventType.MouseDown && e.button == 0)
            {
                mFillCenter = worldPos;
                if (e.control)
                {
                    CircleRemoveObject();
                }
                else
                {
                    CircleFillObject();
                }
            }
        }

        void RegenerateObjectsInCircle()
        {
            Undo.PerformUndo();
            CircleFillObject();
        }

        void CircleRemoveObject()
        {
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
            var rootTransform = stage.prefabContentsRoot.transform;
            int n = rootTransform.childCount;
            float r2 = mCircleRadius * mCircleRadius;

            if (mRemoveObjectOfSeletedPrefab)
            {
                var selectedPrefab = mPrefabManager.selectedPrefab;
                for (int i = n - 1; i >= 0; --i)
                {
                    var childTransform = rootTransform.GetChild(i);
                    var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childTransform.gameObject);
                    if (selectedPrefab == childPrefab)
                    {
                        var dis = (childTransform.position - mFillCenter).sqrMagnitude;
                        if (dis <= r2)
                        {
                            Undo.DestroyObjectImmediate(childTransform.gameObject);
                        }
                    }
                }
            }
            else
            {
                for (int i = n - 1; i >= 0; --i)
                {
                    var childTransform = rootTransform.GetChild(i);
                    var dis = (childTransform.position - mFillCenter).sqrMagnitude;
                    if (dis <= r2)
                    {
                        Undo.DestroyObjectImmediate(childTransform.gameObject);
                    }
                }
            }
        }

        void CircleFillObject()
        {
            if (mPrefabManager.selectedPrefab != null)
            {
                var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();

                var positions = CalculateObjectPositions(mFillCount, mFillCenter);
                for (int i = 0; i < positions.Count; ++i)
                {
                    var prefab = mPrefabManager.selectedPrefab;
                    CreateObject(positions[i], prefab, stage.prefabContentsRoot.transform);
                }
            }
        }

        List<Vector3> CalculateObjectPositions(int n, Vector3 center)
        {
            List<Vector3> validPositions = new List<Vector3>();
            int tryCount = 500;
            Vector3 candidate = Vector3.zero;
            for (int i = 0; i < n; ++i)
            {
                for (int k = 0; k < tryCount; ++k)
                {
                    if (mFillEdge)
                    {
                        float angle = UnityEngine.Random.Range(0, 360.0f) * Mathf.Deg2Rad;
                        float x = Mathf.Sin(angle);
                        float z = Mathf.Cos(angle);
                        float r = UnityEngine.Random.Range(mCircleRadius - mEdgeSize * 0.5f, mCircleRadius + mEdgeSize * 0.5f);
                        candidate = center + new Vector3(x, 0, z) * r;
                    }
                    else
                    {
                        candidate = Utils.ToVector3(UnityEngine.Random.insideUnitCircle * mCircleRadius) + center;
                    }
                    if (IsValid(candidate, validPositions, mMinDistance))
                    {
                        validPositions.Add(candidate);
                        break;
                    }
                }
            }

            return validPositions;
        }

        float mCircleRadius = 10;
        Vector3 mFillCenter;
    }
}
#endif