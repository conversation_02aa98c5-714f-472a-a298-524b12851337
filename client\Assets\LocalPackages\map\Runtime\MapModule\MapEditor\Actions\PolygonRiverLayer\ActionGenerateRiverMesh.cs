﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionGenerateRiverMesh : EditorAction
    {
        public ActionGenerateRiverMesh(int layerID, int dataID, RiverGenerationParameter param)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mNewParam.mtlPath = param.mtlPath;
            mNewParam.textureSize = param.textureSize;
            
            var river = Map.currentMap.FindObject(dataID) as PolygonRiverData;
            mOldParam.textureSize = river.textureSize;
            mOldParam.mtlPath = river.materialPath;
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            //sections
            var sections = river.sections;
            for (int i = 0; i < sections.Count; ++i)
            {
                var sectionData = EditorUtils.CreateRiverSectionData(sections[i], layer.GetRiverMaterial(dataID, i));
                mSections.Add(sectionData);
            }
        }

        public override void OnDestroy()
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                mSections[i].OnDestroy();
            }
            mSections = null;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.GenerateRiverMesh(mDataID, mNewParam, null);
            return true;
        }

        public override bool Undo()
        {
            var riverData = Map.currentMap.FindObject(mDataID) as PolygonRiverData;
            if (riverData == null)
            {
                return false;
            }
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }

            List<PolygonRiverSectionData> sections = new List<PolygonRiverSectionData>(mSections.Count);
            for (int i = 0; i < mSections.Count; ++i)
            {
                var data = new PolygonRiverSectionData(mSections[i].id, mSections[i].outline, mSections[i].textureData, mOldParam.textureSize);
                sections.Add(data);
            }
            layer.GenerateRiverMesh(mDataID, mOldParam, sections);

            //restore material properties
            for (int i = 0; i < mSections.Count; ++i)
            {
                layer.SetRiverMaterialProperties(mDataID, i, mSections[i].materialPropeties);
            }
            return true;
        }

        int mLayerID;
        int mDataID;
        RiverGenerationParameter mOldParam = new RiverGenerationParameter();
        RiverGenerationParameter mNewParam = new RiverGenerationParameter();
        List<RiverSectionData> mSections = new List<RiverSectionData>();
    }
}

#endif