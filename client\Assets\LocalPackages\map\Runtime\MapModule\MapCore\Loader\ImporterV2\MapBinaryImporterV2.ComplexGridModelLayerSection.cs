﻿ 



 
 


using System.IO;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.GridModelLayerData2 LoadComplexGridModelLayerData(BinaryReader reader, MapDataSectionType section)
        {
            long pos = GetSectionDataStartPosition(section);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            
            Rect realLayerBounds = new Rect(offset.x, offset.z, cols * tileWidth, rows * tileHeight);
            if (version >= 2)
            {
                realLayerBounds = Utils.ReadRect(reader);
            }

            bool[] tileIsNotEmpty = new bool[rows * cols];
            int idx = 0;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    tileIsNotEmpty[idx] = reader.ReadBoolean();
                    ++idx;
                }
            }

            var lodConfig = LoadComplexGridModelLayerLODConfigV1(reader);
            //---------------------version 1 end-------------------------

            bool enableObjectMaterialChange = true;
            if (version >= 3)
            {
                enableObjectMaterialChange = reader.ReadBoolean();
            }

            var layer = new config.GridModelLayerData2(layerID, name, offset, lodConfig, rows, cols, tileWidth, tileHeight, GridType.Rectangle, tileIsNotEmpty, realLayerBounds, enableObjectMaterialChange);
            return layer;
        }

        config.MapLayerLODConfig LoadComplexGridModelLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                bool useRenderTexture = reader.ReadBoolean();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, useRenderTexture, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }
    }
}
