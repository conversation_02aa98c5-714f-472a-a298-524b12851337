﻿using UnityEngine;

public class ParticleSystemSign : MonoBehaviour
{
    private void Start()
    {
        ParticleSystem[] particleSystems = GetComponentsInChildren<ParticleSystem>();
        foreach (var ps in particleSystems)
        {
            ParticleSystemRenderer particleSystemRenderer = ps.GetComponent<ParticleSystemRenderer>();
            if (particleSystemRenderer && particleSystemRenderer.enabled)
            {
                ParticleSystemManager.Instance.AddParticleSystem(ps);
            }
        }
    }

    private void OnDestroy()
    {
        ParticleSystem[] particleSystems = GetComponentsInChildren<ParticleSystem>();
        foreach (var ps in particleSystems)
        {
            ParticleSystemManager.Instance.RemoveParticleSystem(ps);
        }
    }
}