﻿ 



 
 

#if UNITY_EDITOR

//#define USE_NAIVE

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ObstacleObject : IObstacle
    {
        public bool IsSimplePolygon(PrefabOutlineType type)
        {
            return true;
        }
        //是否可扩充,有些障碍物不能扩充,不然会有问题.例如圆形地图的边界
        public bool IsExtendable()
        {
            return true;
        }
        public List<Vector3> GetOutlineVertices(PrefabOutlineType type)
        {
            return polygon;
        }
        public Vector3[] GetWorldSpaceOutlineVertices(PrefabOutlineType type)
        {
            return polygon.ToArray();
        }
        public GameObject gameObject { get { return null; } }
        public Rect GetOutlineBounds(PrefabOutlineType type)
        {
            return Utils.BoundsToRect(bounds);
        }
        public Vector3 offset { set; get; }

        public List<Vector3> polygon;
        public Bounds bounds;
    }

    //针对180x180米的tile prefab,去掉与障碍物碰撞的prefab, 再将剩下的prefab组合成新的tile prefab
    public class TilePrefabModifier
    {
        public TilePrefabModifier(bool isGeneratedPrefab, Vector3 tileOriginPos, Vector2 tileSize, string tilePrefabPath, int tileIndex)
        {
            mPrefabName = Utils.GetPathName(tilePrefabPath, false);
            mPrefabName = Utils.GetNameWithLOD(mPrefabName, false);
            mTileOriginPos = tileOriginPos;
            mTileSize = tileSize;
            mTileIndex = tileIndex;
            mIsGeneratedPrefab = isGeneratedPrefab;

            mOriginalPrefabPath = tilePrefabPath;

            var modelTemplate = Map.currentMap.FindModelTemplate(tilePrefabPath);
            if (modelTemplate.isTileModelTemplate)
            {
                //保存model template使用的每一层lod的子prefab信息
                int nLODs = modelTemplate.lodCount;
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    List<ChildPrefabTransform> childPrefabs = new List<ChildPrefabTransform>();
                    mValidPrefabs.Add(childPrefabs);
                    //保存子prefeb的信息
                    var tilePrefab = MapModuleResourceMgr.LoadPrefab(modelTemplate.GetLODPrefabPath(lod));
                    int n = tilePrefab.transform.childCount;
                    var childPrefabTransformList = modelTemplate.GetChildPrefabTransform(lod);
                    for (int i = 0; i < n; ++i)
                    {
                        var newChildPrefabTransform = new ChildPrefabTransform();
                        newChildPrefabTransform.path = childPrefabTransformList[i].path;
                        newChildPrefabTransform.localBoundsInPrefab = childPrefabTransformList[i].localBoundsInPrefab;
                        newChildPrefabTransform.tag = childPrefabTransformList[i].tag;
                        newChildPrefabTransform.position = childPrefabTransformList[i].position;
                        newChildPrefabTransform.editorScaling = childPrefabTransformList[i].editorScaling;
                        newChildPrefabTransform.editorRotation = childPrefabTransformList[i].editorRotation;
                        newChildPrefabTransform.objectType = MapCoreDef.GetTileObjectType(childPrefabTransformList[i].tag);

                        childPrefabs.Add(newChildPrefabTransform);
                    }
                }
            }
        }

        //创建所有物体的包围盒,作为障碍物测试用
        //onlyDecoration:是否只创建decoration标记的object的convex hull
        public List<ObstacleObject> CreateAllConvexHulls(bool onlyDecoration)
        {
            List<ObstacleObject> convexHullsInThisTile = new List<ObstacleObject>();
            //tile prefab的每个lod都需要检测碰撞
            var childPrefabsInLOD0 = mValidPrefabs[0];
            for (int j = childPrefabsInLOD0.Count - 1; j >= 0; --j)
            {
                if (onlyDecoration && (childPrefabsInLOD0[j].tag != MapCoreDef.MAP_DECORATION_OBJECT && childPrefabsInLOD0[j].tag != MapCoreDef.MAP_DECORATION_SCALE_OBJECT))
                {
                    continue;
                }

                if (!string.IsNullOrEmpty(childPrefabsInLOD0[j].path))
                {
                    var hull = GetPrefabHull(childPrefabsInLOD0[j].path, childPrefabsInLOD0[j].position, childPrefabsInLOD0[j].editorScaling, childPrefabsInLOD0[j].editorRotation, mTileOriginPos);
                    var bounds = Utils.CreateBounds(hull);
                    if (hull.Count > 0)
                    {
                        convexHullsInThisTile.Add(new ObstacleObject() { polygon = hull, bounds = bounds });
                    }
                }
            }
            return convexHullsInThisTile;
        }

        //将tile prefab中和collider polygon碰撞的子prefab删除
        public void RemoveIntersectedPrefabsWithConvexCollider(List<List<Vector3>> convexColliderPolygons)
        {
            bool intersected = IsTileIntersectedWithPolygons(convexColliderPolygons);
            if (intersected)
            {
                int nLODs = mValidPrefabs.Count;
                for (int i = 0; i < nLODs; ++i)
                {
                    //tile prefab的每个lod都需要检测碰撞
                    var childPrefabs = mValidPrefabs[i];
                    for (int j = childPrefabs.Count - 1; j >= 0; --j)
                    {
                        var hull = GetPrefabHull(childPrefabs[j].path, childPrefabs[j].position, childPrefabs[j].editorScaling, childPrefabs[j].editorRotation, mTileOriginPos);
                        if (hull.Count > 0)
                        {
#if false
                            //显示生成的hull,用于调试
                            var obj = new GameObject();
                            var dp = obj.AddComponent<DrawPolygon>();
                            dp.SetVertices(hull);
#endif
                            bool prefabCollided = IsConvexHullIntersectWithPolygons(hull, convexColliderPolygons);
                            if (prefabCollided)
                            {
                                childPrefabs.RemoveAt(j);
                                mModified = true;
                            }
                        }
                    }
                }
            }
        }

        //将tile prefab中和collider polygon碰撞的子prefab删除
        public void RemoveIntersectedPrefabsWithAnyCollider(List<ObstacleObject> simplePolygons, List<BVH> bvhTreesForLODs)
        {
            bool intersected = IsTileIntersectedWithSimplePolygons(simplePolygons);
            if (intersected)
            {
                int nLODs = mValidPrefabs.Count;
                List<int> intersectedChildPrefabIndices = new List<int>();
                for (int i = 0; i < nLODs; ++i)
                {
                    //tile prefab的每个lod都需要检测碰撞
                    var childPrefabs = mValidPrefabs[i];
#if USE_NAIVE
                    //使用一个个遍历的碰撞检测
                    for (int j = childPrefabs.Count - 1; j >= 0; --j)
                    {
                        var hull = GetPrefabHull(childPrefabs[j].path, childPrefabs[j].position, childPrefabs[j].editorScaling, childPrefabs[j].editorRotation, mTileOriginPos);
                        if (hull.Count > 0)
                        {
#if false
                            //显示生成的hull,用于调试
                            var obj = new GameObject();
                            var dp = obj.AddComponent<DrawPolygon>();
                            dp.SetVertices(hull);
#endif
                            ++testCount;
                            bool prefabCollided = IsConvexHullIntersectWithSimplePolygons(hull, simplePolygons);
                            if (prefabCollided)
                            {
                                childPrefabs.RemoveAt(j);
                                mModified = true;
                            }
                        }
                    }
#else
                    //使用bvh的碰撞检测
                    intersectedChildPrefabIndices.Clear();
                    for (int p = 0; p < simplePolygons.Count; ++p)
                    {
                        bvhTreesForLODs[i].GetIntersectedObjects(mTileOriginPos, simplePolygons[p], intersectedChildPrefabIndices);
                    }

                    //检测可能相交的child prefab
                    for (int j = intersectedChildPrefabIndices.Count - 1; j >= 0; --j)
                    {
                        var prefabIdx = intersectedChildPrefabIndices[j];
                        if (childPrefabs[prefabIdx] != null)
                        {
                            var hull = GetPrefabHull(childPrefabs[prefabIdx].path, childPrefabs[prefabIdx].position, childPrefabs[prefabIdx].editorScaling, childPrefabs[prefabIdx].editorRotation, mTileOriginPos);
                            if (hull.Count > 0)
                            {
#if false
                            //显示生成的hull,用于调试
                            var obj = new GameObject();
                            var dp = obj.AddComponent<DrawPolygon>();
                            dp.SetVertices(hull);
#endif
                                ++testCount;
                                bool prefabCollided = IsConvexHullIntersectWithSimplePolygons(hull, simplePolygons);
                                if (prefabCollided)
                                {
                                    childPrefabs[prefabIdx] = null;
                                    mModified = true;
                                }
                            }
                        }
                    }
#endif
                }
            }
        }

        bool IsTileIntersectedWithPolygons(List<List<Vector3>> polygons)
        {
            List<Vector3> tilePolygon = new List<Vector3>()
            {
                mTileOriginPos,
                new Vector3(mTileOriginPos.x, 0, mTileOriginPos.z + mTileSize.y),
                new Vector3(mTileOriginPos.x + mTileSize.x, 0, mTileOriginPos.z + mTileSize.y),
                new Vector3(mTileOriginPos.x + mTileSize.x, 0, mTileOriginPos.z),
            };

            for (int i = 0; i < polygons.Count; ++i)
            {
                bool hit = ConvexPolygonCollisionCheck.ConvexPolygonHit2D(tilePolygon, polygons[i]);
                if (hit)
                {
                    return true;
                }
            }
            return false;
        }

        bool IsTileIntersectedWithSimplePolygons(List<ObstacleObject> polygons)
        {
            List<Vector3> tilePolygon = new List<Vector3>()
            {
                mTileOriginPos,
                new Vector3(mTileOriginPos.x, 0, mTileOriginPos.z + mTileSize.y),
                new Vector3(mTileOriginPos.x + mTileSize.x, 0, mTileOriginPos.z + mTileSize.y),
                new Vector3(mTileOriginPos.x + mTileSize.x, 0, mTileOriginPos.z),
            };
            var tileBounds = new Bounds();
            tileBounds.SetMinMax(mTileOriginPos, new Vector3(mTileOriginPos.x + mTileSize.x, 0, mTileOriginPos.z + mTileSize.y));

            for (int i = 0; i < polygons.Count; ++i)
            {
                if (polygons[i].bounds.Intersects(tileBounds))
                {
                    bool hit = PolygonCollisionCheck.Overlap(tilePolygon, polygons[i].polygon);
                    if (hit)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        //创建prefab convex hull
        public static List<Vector3> GetPrefabHull(string prefabPath, Vector3 pos, Vector3 scale, Quaternion rot, Vector3 tilePos)
        {
            List<Vector3> hull = TilePrefabConvexHullCache.GetConvexHullFromCache(prefabPath, pos, scale, rot);
            if (hull == null)
            {
                var prefabRoot = Map.currentMap.view.reusableGameObjectPool.Require(prefabPath, pos, scale, rot);
                hull = ConvexHullBuilder.CreateGameObject2DConvexHullInXZPlane(prefabRoot, true);

                Map.currentMap.view.reusableGameObjectPool.Release(prefabPath, prefabRoot, Map.currentMap);
                TilePrefabConvexHullCache.AddConvexHullToCache(prefabPath, pos, scale, rot, hull);
            }

            if (tilePos != Vector3.zero)
            {
                List<Vector3> globalHull = new List<Vector3>();
                for (int i = 0; i < hull.Count; ++i)
                {
                    globalHull.Add(hull[i] + tilePos);
                }
                return globalHull;
            }
            return hull;
        }

        //polygons 必须是convex的
        bool IsConvexHullIntersectWithPolygons(List<Vector3> hull, List<List<Vector3>> polygons)
        {
            for (int i = 0; i < polygons.Count; ++i)
            {
                bool hit = ConvexPolygonCollisionCheck.ConvexPolygonHit2D(hull, polygons[i]);
                if (hit)
                {
                    return true;
                }
            }
            return false;
        }

        bool IsConvexHullIntersectWithSimplePolygons(List<Vector3> hull, List<ObstacleObject> polygons)
        {
            for (int i = 0; i < polygons.Count; ++i)
            {
                bool hit = PolygonCollisionCheck.Overlap(hull, polygons[i].polygon);
                if (hit)
                {
                    return true;
                }
            }
            return false;
        }

        //将tile prefab中不完全在圆内的子prefab删掉
        public void RemoveIntersectedPrefabs(Vector3 center, float radius)
        {
            List<Vector3> tilePolygon = new List<Vector3>()
            {
                new Vector3(mTileOriginPos.x, 0, mTileOriginPos.z),
                new Vector3(mTileOriginPos.x, 0, mTileOriginPos.z + mTileSize.y),
                new Vector3(mTileOriginPos.x + mTileSize.x, 0, mTileOriginPos.z + mTileSize.y),
                new Vector3(mTileOriginPos.x + mTileSize.x, 0, mTileOriginPos.z),
            };

            bool fullOutside = Utils.IsConvexHullFullOutsideOfCircle(tilePolygon, center, radius);
            if (fullOutside)
            {
                //tile is full outof map, clear all prefabs on it
                mValidPrefabs.Clear();
            }
            else
            {
                int nLODs = mValidPrefabs.Count;
                for (int i = 0; i < nLODs; ++i)
                {
                    var childPrefabs = mValidPrefabs[i];
                    for (int j = childPrefabs.Count - 1; j >= 0; --j)
                    {
                        var hull = GetPrefabHull(childPrefabs[j].path, childPrefabs[j].position, childPrefabs[j].editorScaling, childPrefabs[j].editorRotation, mTileOriginPos);
                        if (hull.Count > 0)
                        {
                            bool fullInside = Utils.IsConvexHullFullInsideOfCircle(hull, center, radius);
                            if (!fullInside)
                            {
                                //childPrefabs.RemoveAt(j);
                                //不要删除,先标记为null,因为后续还会使用这些index
                                childPrefabs[j] = null;
                                mModified = true;
                            }
                        }
                    }
                }
            }
        }

        //创建修改后的嵌套的prefab
        //返回修改后的prefab的lod0的名字
        public string SavePrefab(string prefabFolder, bool forceSave = false)
        {
            if (mModified || forceSave)
            {
                ++modifiedPrefabCount;
                string lod0Path = "";
                string newPrefabPath = "";
                for (int i = 0; i < mValidPrefabs.Count; ++i)
                {
                    //先删除null的项目
                    var childPrefabs = mValidPrefabs[i];
                    for (int k = 0; k < childPrefabs.Count;)
                    {
                        if (childPrefabs[k] == null)
                        {
                            childPrefabs.RemoveAt(k);
                        }
                        else
                        {
                            ++k;
                        }
                    }

                    string ext = ".prefab";
                    if (MapModule.useFakePrefab)
                    {
                        ext = "." + MapCoreDef.FAKE_PREFAB_EXT;
                    }

                    if (mIsGeneratedPrefab)
                    {
                        newPrefabPath = prefabFolder + "/" + mPrefabName + mTileIndex + MapCoreDef.MAP_PREFAB_LOD_PREFIX + i + ext;
                    }
                    else
                    {
                        newPrefabPath = $"{prefabFolder}/new_{mPrefabName}_autogen_{mTileIndex}{MapCoreDef.MAP_PREFAB_LOD_PREFIX}{i}{ext}";
                    }
                    if (i == 0)
                    {
                        lod0Path = newPrefabPath;
                    }

                    if (MapModule.useFakePrefab)
                    {
                        Map.currentMap.fakePrefabManager.CreateAsset(newPrefabPath, mValidPrefabs[i]);
                    }
                    else
                    {
                        var newPrefabRoot = new GameObject(mPrefabName);
                        PrefabUtility.SaveAsPrefabAsset(newPrefabRoot, newPrefabPath);

                        var prefabList = mValidPrefabs[i];
                        //加载一个独立场景的prefab
                        var prefabInOtherScene = PrefabUtility.LoadPrefabContents(newPrefabPath);
                        for (int k = 0; k < prefabList.Count; ++k)
                        {
                            var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabList[k].path);
                            //生成一个prefab instance,用来嵌套到新的prefab中
                            var childPrefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(childPrefab);

                            if (childPrefabInstance != null)
                            {
                                childPrefabInstance.transform.position = prefabList[k].position - new Vector3(Map.currentMap.frontTileSize * 0.5f, 0, Map.currentMap.frontTileSize * 0.5f);
                                childPrefabInstance.transform.rotation = prefabList[k].editorRotation;
                                childPrefabInstance.transform.localScale = prefabList[k].editorScaling;
                                childPrefabInstance.tag = prefabList[k].tag;
                                if (childPrefabInstance != null)
                                {
                                    childPrefabInstance.transform.SetParent(prefabInOtherScene.transform, true);
                                }
                                else
                                {
                                    Debug.Assert(false, "Invalid child prefab");
                                }
                            }
                        }
                        PrefabUtility.SaveAsPrefabAsset(prefabInOtherScene, newPrefabPath);

                        PrefabUtility.UnloadPrefabContents(prefabInOtherScene);

                        GameObject.DestroyImmediate(newPrefabRoot);
                    }
                }

                return lod0Path;
            }

            return "";
        }

        public bool isCleared { get { return mValidPrefabs.Count == 0; } }
        List<List<ChildPrefabTransform>> mValidPrefabs = new List<List<ChildPrefabTransform>>();
        //temp variable
        List<Vector3> mMeshVertices = new List<Vector3>();
        string mPrefabName;
        string mOriginalPrefabPath;
        Vector3 mTileOriginPos;
        Vector2 mTileSize;
        //用来生成不同的prefab名字,因为同一个prefab在去掉和障碍物相交的prefab后就不再是同一个prefab了
        int mTileIndex;
        bool mModified = false;
        bool mIsGeneratedPrefab = false;
        public static int testCount;
        public static int modifiedPrefabCount;
    }
}

#endif