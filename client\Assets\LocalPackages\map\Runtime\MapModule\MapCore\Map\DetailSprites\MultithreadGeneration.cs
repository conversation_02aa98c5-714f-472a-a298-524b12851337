﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;

//create by wzw at 2020.2.4

namespace TFW.Map
{
    public partial class DetailSprites
    {
        class Block
        {
            public int x;
            public int y;
            public int width;
            public int height;
            public int startKey;
        }

        public void PrecalculateValidKeysMultithread(float mapWidth, float mapHeight, float tileSize, float subgridSize, List<List<string>> spriteGroups, float alpha0Height, float alpha1Height, int objectCountPerGrid, byte[] subgridSpriteGroupIndices, bool updateScale, bool crossFading)
        {
            var w = new Stopwatch();
            w.Start();
            mAlpha0Height = alpha0Height;
            mAlpha1Height = alpha1Height;
            mTileSize = tileSize;
            mGridSize = subgridSize;
            mSpriteCountPerGrid = objectCountPerGrid;
            mUpdateScale = updateScale;
            mCrossfading = crossFading;
            mCols = Mathf.FloorToInt(mapWidth / tileSize);
            mRows = Mathf.FloorToInt(mapHeight / tileSize);
            mHorizontalGridCount = Mathf.FloorToInt(mTileSize / mGridSize);

            if (subgridSpriteGroupIndices == null)
            {
                mSubgridSpriteGroupIndices = new byte[mRows * mCols * mHorizontalGridCount * mHorizontalGridCount];
            }
            else
            {
                UnityEngine.Debug.Assert(subgridSpriteGroupIndices.Length == mRows * mCols * mHorizontalGridCount * mHorizontalGridCount);
                mSubgridSpriteGroupIndices = subgridSpriteGroupIndices;
            }

            //create sprite groups
            int groupCount = spriteGroups.Count;
            mSpriteGroups = new List<SpriteGroup>(groupCount);
            for (int i = 0; i < groupCount; ++i)
            {
                int nSprites = spriteGroups[i].Count;
                var group = new SpriteGroup(nSprites);
                for (int k = 0; k < nSprites; ++k)
                {
                    group.sprites[k] = spriteGroups[i][k];
                }
                mSpriteGroups.Add(group);
            }

            InitSpriteData();

            int totalKeyCount = 0;
            var gridModelLayer = mMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            mSubGridObjects = new SubGridObjects[mRows, mCols];

            int totalGridCount = mRows * mCols * mHorizontalGridCount * mHorizontalGridCount;
            mSubgridKeysStartOffset = new int[totalGridCount];
            for (int i = 0; i < mSubgridKeysStartOffset.Length; ++i)
            {
                mSubgridKeysStartOffset[i] = -1;
            }

            //calculate blocks
            int coreCount = SystemInfo.processorCount;
            int gridCountPerBlock = Mathf.CeilToInt((mRows * mCols) / coreCount);
            int blockSize = Mathf.CeilToInt(Mathf.Sqrt(gridCountPerBlock));
            if (blockSize <= 0)
            {
                blockSize = 1;
            }

            int blockStartX = 0, blockStartY = 0;
            List<Block> blocks = new List<Block>();
            while (true)
            {
                int endX = Mathf.Clamp(blockStartX + blockSize - 1, 0, mCols - 1);
                int endY = Mathf.Clamp(blockStartY + blockSize - 1, 0, mRows - 1);
                var block = new Block();
                block.x = blockStartX;
                block.y = blockStartY;
                block.width = endX - blockStartX + 1;
                block.height = endY - blockStartY + 1;
                block.startKey = UnityEngine.Random.Range(0, 256);

                if (block.width == 0 || block.height == 0)
                {
                    break;
                }

                blocks.Add(block);

                blockStartX = endX + 1;
                if (blockStartX >= mCols)
                {
                    blockStartX = 0;
                    blockStartY = endY + 1;
                }
            }

            //for (int i = 0; i < vBlocks; ++i)
            //{
            //    for (int j = 0; j < hBlocks; ++j)
            //    {
            //        var block = new Block();
            //        block.x = j * blockWidth;
            //        block.y = i * blockHeight;
            //        block.width = blockWidth;
            //        block.height = blockHeight;
            //        block.startKey = UnityEngine.Random.Range(0, 256);
            //        blocks.Add(block);
            //    }
            //}

            List<ObstacleObject>[,] convextHullsForAllTiles = new List<ObstacleObject>[mRows, mCols];
            for (int i = 0; i < mRows; ++i)
            {
                for (int j = 0; j < mCols; ++j)
                {
                    List<ObstacleObject> convexHulls = null;
                    if (gridModelLayer != null)
                    {
                        var tile = gridModelLayer.GetObjectData(j, i);
                        if (tile != null)
                        {
                            var modelTemplate = tile.GetModelTemplate();
                            var idx = j * mCols + i;
                            var modifier = new TilePrefabModifier(modelTemplate.generated, new Vector3(mTileSize * j, 0, mTileSize * i), new Vector2(mTileSize, mTileSize), modelTemplate.GetLODPrefabPath(0), idx);
                            convexHulls = modifier.CreateAllConvexHulls(true);
                        }
                    }
                    convextHullsForAllTiles[i, j] = convexHulls;
                }
            }

            List<Task<List<byte>>> tasks = new List<Task<List<byte>>>();
            //List<System.Func<List<byte>>> tasks = new List<System.Func<List<byte>>>();
            for (int t = 0; t < blocks.Count; ++t)
            {
                var block = blocks[t];
                var task = Task<List<byte>>.Run(() =>
                //System.Func<List<byte>> task = () =>
                {
                    List<byte> blockValidKeys = new List<byte>();
                    List<Vector2> blockValidPos = new List<Vector2>();
                    List<Vector3> testRect = new List<Vector3>();
                    for (int i = 0; i < block.height; ++i)
                    {
                        for (int j = 0; j < block.width; ++j)
                        {
                            int x = j + block.x;
                            int y = i + block.y;
                            mSubGridObjects[y, x] = new SubGridObjects();

                            List<byte> tileValidKeys = new List<byte>();
                            for (int sy = 0; sy < mHorizontalGridCount; sy++)
                            {
                                for (int sx = 0; sx < mHorizontalGridCount; sx++)
                                {
                                    var idx = GetSubGridIndex(x, y, sx, sy);
                                    mSubgridKeysStartOffset[idx] = blockValidKeys.Count;
                                    var usedGroupIdx = mSubgridSpriteGroupIndices[idx];
                                    var group = mSpriteGroups[usedGroupIdx];
                                    int ntypes = group.sprites.Length - 1;

                                    tileValidKeys.Clear();
                                    for (int k = 0; k < mSpriteCountPerGrid; k++) // how many element by 10*10 square ?
                                    {
                                        int key = block.startKey;
                                        float k1 = GetNum(block.startKey, x, y, sy, sx);
                                        Step(ref block.startKey);
                                        int type = (int)(ntypes * k1);// we have a set of n sprites to use, which one could it be ?
                                        float k2 = GetNum(block.startKey, x, y, sy, sx);
                                        Step(ref block.startKey);
                                        float k3 = GetNum(block.startKey, x, y, sy, sx);
                                        Step(ref block.startKey);
                                        //used to find lie in which sub grid
                                        float k4 = GetNum(block.startKey, x, y, sy, sx);
                                        Step(ref block.startKey);

                                        var pos = GenerateGridObjectPosition(k2, k3, x, y, sx, sy);

                                        bool isValid = IsValidSpawnPointMultithread(convextHullsForAllTiles[y, x], pos.x, pos.y, group.spriteRadius[type], testRect);

                                        if (isValid)
                                        {
                                            tileValidKeys.Add((byte)key);
                                            //blockValidPos.Add(pos);
                                            //temp code
                                            //GameTestGameObject(p1x, p1y, group.spriteRadius[type]);
                                        }
                                    }

                                    blockValidKeys.Add((byte)tileValidKeys.Count);
                                    for (int k = 0; k < tileValidKeys.Count; ++k)
                                    {
                                        blockValidKeys.Add(tileValidKeys[k]);
                                    }
                                }
                            }
                        }
                    }

                    return blockValidKeys;
                });

                tasks.Add(task);
            }

            List<byte> allValidKeys = new List<byte>();
            int offset = 0;
            for (int i = 0; i < tasks.Count; ++i)
            {
                var blockValidKeys = tasks[i].Result;
                //var blockValidKeys = tasks[i]();

                //update offset
                int blockWidth = blocks[i].width;
                int blockHeight = blocks[i].height;
                for (int y = 0; y < blockHeight; ++y)
                {
                    for (int x = 0; x < blockWidth; ++x)
                    {
                        for (int sy = 0; sy < mHorizontalGridCount; ++sy)
                        {
                            for (int sx = 0; sx < mHorizontalGridCount; ++sx)
                            {
                                int blockStartGridIdx = GetSubGridIndex(blocks[i].x + x, blocks[i].y + y, sx, sy);
                                mSubgridKeysStartOffset[blockStartGridIdx] += offset;
                            }
                        }
                    }
                }

                allValidKeys.AddRange(blockValidKeys);
                offset = allValidKeys.Count;
            }

            mAllKeys = allValidKeys.ToArray();
            UnityEngine.Debug.Log(totalKeyCount);
            w.Stop();
            var elapsedTime = w.ElapsedMilliseconds;
            UnityEngine.Debug.Log("DetailSprite.GenerateValidSpawnPoints Elapsed Time: " + elapsedTime);
        }

        bool IsValidSpawnPointMultithread(List<ObstacleObject> convexHulls, float centerX, float centerZ, float radius, List<Vector3> rect)
        {
            bool hitObstacle = mMap.IsIntersectedWithObstacles(centerX, centerZ, radius, true);
            if (hitObstacle)
            {
                return false;
            }

            if (convexHulls != null)
            {
                rect.Clear();
                rect.Add(new Vector3(centerX - radius, 0, centerZ - radius));
                rect.Add(new Vector3(centerX - radius, 0, centerZ + radius));
                rect.Add(new Vector3(centerX + radius, 0, centerZ + radius));
                rect.Add(new Vector3(centerX + radius, 0, centerZ - radius));

                for (int i = 0; i < convexHulls.Count; ++i)
                {
                    bool prefabCollided = ConvexPolygonCollisionCheck.ConvexPolygonHit2D(convexHulls[i].polygon, rect);
                    if (prefabCollided)
                    {
                        return false;
                    }
                }
            }
            return true;
        }
    }
}

#endif
