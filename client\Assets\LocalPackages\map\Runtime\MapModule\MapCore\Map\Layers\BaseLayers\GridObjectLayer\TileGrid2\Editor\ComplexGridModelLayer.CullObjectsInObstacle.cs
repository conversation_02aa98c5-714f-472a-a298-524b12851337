﻿ 



 
 

#if UNITY_EDITOR

#define USE_LIST

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;



namespace TFW.Map
{
    public partial class ComplexGridModelLayer : MapLayerBase
    {
        class ObstacleGroup
        {
            //除去自身以外的节点的obstacles
            public List<MapGlobalObstacleManager> obstacleManagers = new List<MapGlobalObstacleManager>();

#if USE_LIST
            public List<Vector3> childrenObjects = new List<Vector3>();
#else
            public SortedDictionary<Vector3, List<GameObject>> childrenObjects = new SortedDictionary<Vector3, List<GameObject>>(new CompareVector3(1f));
#endif

            public bool IsPointInObstacle(float x, float z)
            {
                foreach (var ob in obstacleManagers)
                {
                    if (ob.IsPointInObstacle(x, z))
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        /*
         * layer下最高一级节点之间穿插的物体才裁剪,例如如下结构
         * Layer
         *     A
         *     B
         * A或B的子节点如果有穿插就忽略,只裁剪A穿插到B中的物体和B穿插到A中的物体
         */
        public bool IsObjectInObstacle(Vector3 pos, int lod)
        {
            ObstacleGroup group = GetObjectObstacleGroup(pos);
            if (group == null)
            {
                Debug.LogError($"group not found at pos: {pos}, {lod}");
                return false;
            }
            else
            {
                return group.IsPointInObstacle(pos.x, pos.z);
            }
        }

        public void CreateObstacleGroups()
        {
            var layer = Map.currentMap.GetMapLayer<MapCollisionLayer>();
            if (layer == null)
            {
                Debug.LogError("需要创建Collision Layer");
                return;
            }

            ClearObstacleGroups();

            EditorUtility.DisplayProgressBar($"Create Obstacles...", "Please wait", 0);
            List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
            mLayerData.GetAllObjects(objects);
            List<GameObject> rootObjects = new List<GameObject>();
            for (int i = 0; i < objects.Count; ++i)
            {    
                var gameObject = mLayerView.GetGameObject(objects[i].GetEntityID());
                rootObjects.Add(gameObject);
            }

            var obstacleManagers = CreateGlobalObstacleManagers(rootObjects);

            for (int i = 0; i < rootObjects.Count; ++i)
            {
                string prefabPath = objects[i].GetAssetPath(0);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                List<MapGlobalObstacleManager> excludeSelfObstacleManagers = new List<MapGlobalObstacleManager>();
                excludeSelfObstacleManagers.AddRange(obstacleManagers);
                excludeSelfObstacleManagers.RemoveAt(i);

                var group = CreateObstacleGroup(rootObjects[i], prefab, i, objects.Count, excludeSelfObstacleManagers);
                mObstacleGroups.Add(group);
            }
        }

        void ClearObstacleGroups()
        {
            foreach (var manager in mObstacleManagers)
            {
                manager.OnDestroy();
            }
            mObstacleGroups.Clear();
            mObstacleManagers.Clear();
        }

        ObstacleGroup CreateObstacleGroup(GameObject rootObject, GameObject prefab, int groupIndex, int groupCount, List<MapGlobalObstacleManager> obstacleManagers)
        {
            ObstacleGroup group = new ObstacleGroup();
            AddGroupObjectPositions(rootObject, group, prefab);
            group.obstacleManagers = obstacleManagers;
            return group;
        }

        void RemovePrefabOutlines()
        {
            var layer = Map.currentMap.GetMapLayer<MapCollisionLayer>();

            List<MapCollisionData> collisions = new List<MapCollisionData>();
            layer.GetCollisionsOfType(collisions, CollisionAttribute.IsConvertedFromPrefabOutline);
            for (int i = 0; i < collisions.Count; ++i)
            {
                layer.RemoveObject(collisions[i].id);
            }
        }

        //void AddPrefabOutlines(GameObject excludedRootObject, int groupIndex, int groupCount)
        //{
        //    var layer = Map.currentMap.GetMapLayer<MapCollisionLayer>();

        //    EditorUtility.DisplayProgressBar("Remove Collisions Converted From Prefab Outlines", "Please wait", 0.0f);
        //    //先删除从prefab outline转换的collision
        //    RemovePrefabOutlines();

        //    HashSet<PrefabOutline> prefabOutlines = new HashSet<PrefabOutline>();
        //    List<IObstacle> obstacles = new List<IObstacle>();
        //    List<IObstacle> complexGridModelLayerObstacles = Utils.GetComplexGridModelLayerObstacles(prefabOutlines);

        //    foreach (var obstacle in complexGridModelLayerObstacles)
        //    {
        //        var root = GetRootObject(obstacle.gameObject);
        //        if (root != excludedRootObject)
        //        {
        //            obstacles.Add(obstacle);
        //        }
        //    }

        //    for (int i = 0; i < obstacles.Count; ++i)
        //    {
        //        EditorUtility.DisplayProgressBar($"Add PrefabOutline {i} for obstacle group {groupIndex}/{groupCount}", "Please wait", (float)(i + 1) / obstacles.Count);

        //        List<Vector3> v0 = new List<Vector3>();
        //        List<Vector3> v1 = new List<Vector3>();
        //        v0.AddRange(obstacles[i].GetWorldSpaceOutlineVertices(PrefabOutlineType.NavMeshObstacle));
        //        v1.AddRange(obstacles[i].GetWorldSpaceOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle));

        //        OutlineData[] outlineData = new OutlineData[2]
        //        {
        //            new OutlineData(v0),
        //            new OutlineData(v1),
        //        };

        //        var collision = new MapCollisionData(Map.currentMap.nextCustomObjectID, Map.currentMap, outlineData, 10, true, CollisionAttribute.IsAutoExpandingObstacle | CollisionAttribute.IsConvertedFromPrefabOutline, 0, true);
        //        layer.AddObject(collision);
        //    }

        //    EditorUtility.ClearProgressBar();
        //}

        class ObstacleList
        {
            public string name;
            public List<IObstacle> obstacles = new List<IObstacle>();
        }

        List<MapGlobalObstacleManager> CreateGlobalObstacleManagers(List<GameObject> rootObjects)
        {
            var layer = Map.currentMap.GetMapLayer<MapCollisionLayer>();

            HashSet<PrefabOutline> prefabOutlines = new HashSet<PrefabOutline>();
            List<IObstacle> complexGridModelLayerObstacles = Utils.GetComplexGridModelLayerObstacles(prefabOutlines);

            List<ObstacleList> allObstacles = new List<ObstacleList>(rootObjects.Count);
            for (int i = 0; i < rootObjects.Count; ++i)
            {
                var list = new ObstacleList();
                allObstacles.Add(list);
            }

            foreach (var obstacle in complexGridModelLayerObstacles)
            {
                var root = GetRootObject(obstacle.gameObject);
                int index = rootObjects.IndexOf(root);
                Debug.Assert(index >= 0);

                allObstacles[index].name = root.name;
                allObstacles[index].obstacles.Add(obstacle);
            }

            List<MapGlobalObstacleManager> globalObstacleManagers = new List<MapGlobalObstacleManager>();
            //create obstacle managers
            foreach (var list in allObstacles)
            {
                RemovePrefabOutlines();
                var obstacles = list.obstacles;
                for (int i = 0; i < obstacles.Count; ++i)
                {
                    List<Vector3> v0 = new List<Vector3>();
                    List<Vector3> v1 = new List<Vector3>();
                    v0.AddRange(obstacles[i].GetWorldSpaceOutlineVertices(PrefabOutlineType.NavMeshObstacle));
                    v1.AddRange(obstacles[i].GetWorldSpaceOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle));

                    OutlineData[] outlineData = new OutlineData[2]
                    {
                    new OutlineData(v0),
                    new OutlineData(v1),
                    };

                    var collision = new MapCollisionData(Map.currentMap.nextCustomObjectID, Map.currentMap, outlineData, 10, true, CollisionAttribute.IsAutoExpandingObstacle | CollisionAttribute.IsConvertedFromPrefabOutline, 0, true);
                    layer.AddObject(collision);
                }

                var map = Map.currentMap;
                var obstacleManager = new MapGlobalObstacleManager(list.name, map, map.mapWidth, map.mapHeight, false, MapModule.defaultObstacleMaterial);
                obstacleManager.CreateObstacles((Map.currentMap as EditorMap).globalObstacleMode, LayerTypeMask.kCollisionLayer, false);
                globalObstacleManagers.Add(obstacleManager);
            }

            mObstacleManagers = globalObstacleManagers;
            return globalObstacleManagers;
        }

        GameObject GetRootObject(GameObject prefabOutlineGameObject)
        {
            var root = mLayerView.root;
            GameObject obj = prefabOutlineGameObject;
            while (obj.transform.parent != root.transform)
            {
                obj = obj.transform.parent.gameObject;
            }
            return obj;
        }

        bool Contains(List<Vector3> objects, Vector3 pos, float error = 2.0f)
        {
            for (int i = 0; i < objects.Count; ++i)
            {
                var p = objects[i];
                bool equal = Utils.Approximately(p.z, pos.z, error) && Utils.Approximately(p.x, pos.x, error);
                if (equal)
                {
                    return true;
                }
            }
            return false;
        }

        ObstacleGroup GetObjectObstacleGroup(Vector3 pos)
        {
            foreach (var group in mObstacleGroups)
            {
#if USE_LIST
                bool found = Contains(group.childrenObjects, pos);
#else
                bool found = group.childrenObjects.TryGetValue(pos, out _);
#endif
                if (found)
                {
                    return group;
                }
            }
            return null;
        }

        void AddGroupObjectPositions(GameObject rootObject, ObstacleGroup group, GameObject prefab)
        {
            List<Transform> stack = new List<Transform>();
            List<Transform> prefabStack = new List<Transform>();
            stack.Add(rootObject.transform);
            prefabStack.Add(prefab.transform);
            while (stack.Count > 0)
            {
                var cur = stack[stack.Count - 1];
                var curPrefab = prefabStack[prefabStack.Count - 1];
                stack.RemoveAt(stack.Count - 1);
                prefabStack.RemoveAt(prefabStack.Count - 1);
                bool isPrefab = PrefabUtility.IsAnyPrefabInstanceRoot(curPrefab.gameObject);
                if (isPrefab)
                {
                    AddToGroup(group.childrenObjects, cur);
                }
                
                int n = cur.childCount;
                Debug.Assert(n == curPrefab.childCount);
                for (int i = 0; i < n; ++i)
                {
                    stack.Add(cur.GetChild(i));
                    prefabStack.Add(curPrefab.GetChild(i));
                }
            }
        }

#if USE_LIST
        void AddToGroup(List<Vector3> objects, Transform trans)
#else
        void AddToGroup(SortedDictionary<Vector3, List<GameObject>> objects, Transform trans)
#endif
        {
#if USE_LIST
            objects.Add(trans.position);
#else
            objects.TryGetValue(pos, out var list);
            if (list == null)
            {
                list = new List<GameObject>();
                objects.Add(pos, list);
            }
            list.Add(trans.gameObject);
#endif
        }

        List<ObstacleGroup> mObstacleGroups = new List<ObstacleGroup>();
        List<MapGlobalObstacleManager> mObstacleManagers = new List<MapGlobalObstacleManager>();
    }
}

#endif