﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public class RuntimeRegionLayer : MapLayerBase
    {
        public RuntimeRegionLayer(Map map) :base(map){ }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.RuntimeRegionLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.layerWidth, sourceLayer.layerHeight, GridType.Rectangle, sourceLayer.origin + setting.origin);

            RuntimeRegionData[] regions = null;
            if (sourceLayer.regions != null)
            {
                int n = sourceLayer.regions.Length;
                regions = new RuntimeRegionData[n];
                for (int i = 0; i < n; ++i)
                {
                    var regionData = sourceLayer.regions[i] as config.RuntimeRegionData;
                    var regionID = map.nextCustomObjectID;
                    var modelTemplate = map.GetOrCreateModelTemplate(regionID, regionData.assetPath, false);
                    var data = new RuntimeRegionData(regionID, map, regionData.number, modelTemplate, regionData.worldBounds, regionData.borderLinePrefabPath);
                    regions[i] = data;
                }
            }

            mLayerData = new RuntimeRegionLayerData(header, map, regions);
            mLayerView = new RuntimeRegionLayerView(mLayerData, false, sourceLayer.borderPrefabPath);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            mLayerData.isLoading = true;
            int nt = regions.Length;
            for (int i = 0; i < nt; ++i)
            {
                mLayerData.AddObjectData(regions[i]);
            }
            mLayerData.isLoading = false;

            map.AddMapLayer(this);
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            if (active)
            {
                bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
                if (lodChanged)
                {
                    mLayerView.SetZoom(newCameraZoom, lodChanged);
                }
                return lodChanged;
            }
            return false;
        }

        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public void ShowRegionMesh(int regionID, bool show)
        {
            var layerData = mLayerData as RuntimeRegionLayerData;
            var data = layerData.FindRegion(regionID);
            if (data != null)
            {
                data.regionMeshVisible = show;
                var id = layerData.RegionNumberToID(regionID);
                var view = mLayerView.GetObjectView(id) as RuntimeRegionView;
                if (view != null)
                {
                    view.ShowRegionMesh(show);
                }
            }
        }

        public bool IsRegionMeshVisible(int regionID)
        {
            var layerData = mLayerData as RuntimeRegionLayerData;
            var data = layerData.FindRegion(regionID);
            if (data != null)
            {
                return data.regionMeshVisible;
            }
            return false;
        }

        public void ShowRegionBorderLineMesh(int regionID, bool show)
        {
            var layerData = mLayerData as RuntimeRegionLayerData;
            var data = layerData.FindRegion(regionID);
            if (data != null)
            {
                data.regionMeshVisible = show;
                var id = layerData.RegionNumberToID(regionID);
                var view = layerView.GetObjectView(id) as RuntimeRegionView;
                if (view != null)
                {
                    view.ShowBorderLineMesh(show);
                }
            }
        }

        public bool IsRegionBorderLineMeshVisible(int regionID)
        {
            var layerData = mLayerData as RuntimeRegionLayerData;
            var data = layerData.FindRegion(regionID);
            if (data != null)
            {
                return data.borderLineVisible;
            }
            return false;
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public MapObjectLayerData layerData { get { return mLayerData; } }
        public RuntimeRegionLayerView layerView { get { return mLayerView; } }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override int lodCount => mLayerData.lodCount;

        protected RuntimeRegionLayerData mLayerData;
        protected RuntimeRegionLayerView mLayerView;
    }
}
