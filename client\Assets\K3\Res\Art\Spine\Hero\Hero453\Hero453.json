{"skeleton": {"hash": "3CWzkJF+Vw4", "spine": "4.2.33", "x": -378.36, "y": -46.5, "width": 742.73, "height": 1837.53, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 351.84, "y": 1187.23}, {"name": "ALL2", "parent": "ALL", "x": -420.05, "y": -29.44, "icon": "arrows"}, {"name": "tun", "parent": "ALL2", "length": 168.19, "rotation": -86.28, "x": 1.48, "y": -10.82}, {"name": "leg_L", "parent": "tun", "x": 15.7, "y": 79.7}, {"name": "leg_R", "parent": "tun", "x": 45.48, "y": -66.29}, {"name": "body", "parent": "ALL2", "length": 113.06, "rotation": 93.22, "x": -0.11, "y": 9.19}, {"name": "body2", "parent": "body", "length": 256.85, "rotation": 68.2, "x": 113.06, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 93.88, "rotation": 92.43, "x": 256.85, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 155.08, "rotation": -1.1, "x": 93.88}, {"name": "eyebrow_L", "parent": "head", "length": 20.63, "rotation": 65.76, "x": 71.33, "y": 0.88}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 19.7, "rotation": 40.27, "x": 20.63}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 12.48, "rotation": 4.78, "x": 19.7}, {"name": "eyebrow_R", "parent": "head", "length": 10.26, "rotation": -104.57, "x": 72.27, "y": 91.86}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 8.63, "rotation": 10.64, "x": 10.26}, {"name": "eye_L", "parent": "head", "x": 55.74, "y": 20.33}, {"name": "eye_R", "parent": "head", "x": 48.45, "y": 75.09}, {"name": "hair_B", "parent": "head", "x": 133.95, "y": -61.08}, {"name": "hair_B2", "parent": "hair_B", "length": 54.97, "rotation": -82.12, "x": 11.55, "y": -35.25, "inherit": "noRotationOrReflection"}, {"name": "hair_B3", "parent": "hair_B2", "length": 48.85, "rotation": -13.79, "x": 54.97, "color": "abe323ff"}, {"name": "hair_B4", "parent": "hair_B3", "length": 39.39, "rotation": 4.08, "x": 48.48, "y": -0.46, "color": "abe323ff"}, {"name": "hair_B5", "parent": "hair_B4", "length": 31.23, "rotation": 8.76, "x": 39.39, "color": "abe323ff"}, {"name": "hair_B6", "parent": "hair_B5", "length": 27.28, "rotation": -10.46, "x": 31.23, "color": "abe323ff"}, {"name": "hair_B7", "parent": "hair_B6", "length": 18.72, "rotation": -36.94, "x": 27.28, "color": "abe323ff"}, {"name": "hair_B8", "parent": "hair_B7", "length": 16.84, "rotation": -55.25, "x": 18.72, "color": "abe323ff"}, {"name": "sh_L", "parent": "body2", "x": 282.37, "y": -105.64, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "x": 182.21, "y": 102.83, "inherit": "noScale"}, {"name": "leg_L2", "parent": "leg_L", "length": 466.46, "rotation": -7.03, "x": 87.53, "y": 34.73}, {"name": "leg_L3", "parent": "leg_L2", "length": 470.4, "rotation": 1.48, "x": 466.46, "inherit": "noScale"}, {"name": "leg_L4", "parent": "leg_L3", "length": 231.43, "rotation": -137.88, "x": 470.4, "inherit": "onlyTranslation"}, {"name": "leg_R2", "parent": "leg_R", "length": 421.96, "rotation": -7.78, "x": 101.22}, {"name": "leg_R3", "parent": "leg_R2", "length": 415.25, "rotation": 40.43, "x": 421.96, "inherit": "noScale"}, {"name": "foot_R", "parent": "root", "length": 89.29, "rotation": 68.03, "x": 101.31, "y": 24.04}, {"name": "foot_R2", "parent": "foot_R", "length": 139.98, "rotation": 107.24, "x": 89.29, "inherit": "noRotationOrReflection"}, {"name": "hand_L", "parent": "leg_L", "length": 90.94, "rotation": 119.36, "x": 32.61, "y": -35.27}, {"name": "hand_L2", "parent": "hand_L", "length": 57.7, "rotation": -51.8, "x": 90.94}, {"name": "arm_L", "parent": "sh_L", "length": 252.88, "rotation": -117.68, "x": -21.5, "y": -7.91}, {"name": "arm_L2", "parent": "arm_L", "length": 236.88, "rotation": -89.58, "x": 252.88, "inherit": "noScale"}, {"name": "arm_R", "parent": "sh_R", "length": 234.53, "rotation": 174.09, "x": -14.93, "y": -15.36}, {"name": "arm_R2", "parent": "arm_R", "length": 209.57, "rotation": -138.39, "x": 234.53, "inherit": "noScale"}, {"name": "arm_R5", "parent": "sh_R", "rotation": -76.83, "x": -77.99, "y": 131.09, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R3", "parent": "arm_R5", "length": 94.54, "rotation": 93.39, "inherit": "noScaleOrReflection"}, {"name": "arm_R4", "parent": "arm_R3", "length": 610.32, "rotation": -80.95, "x": 66.09, "y": 0.46}, {"name": "hair_F", "parent": "head", "x": 123.94, "y": 48.42}, {"name": "hair_FR", "parent": "hair_F", "length": 27.2, "rotation": 156.29, "x": -0.97, "y": 40.74}, {"name": "hair_FR2", "parent": "hair_FR", "length": 30.51, "rotation": 28.64, "x": 27.2, "color": "abe323ff"}, {"name": "hair_FR3", "parent": "hair_FR2", "length": 24.21, "rotation": -24.98, "x": 30.51, "color": "abe323ff"}, {"name": "hair_FR4", "parent": "hair_FR3", "length": 17.94, "rotation": 4.39, "x": 24.21, "color": "abe323ff"}, {"name": "hair_FR5", "parent": "hair_FR4", "length": 19.23, "rotation": 36.94, "x": 17.94, "color": "abe323ff"}, {"name": "hair_FL", "parent": "hair_F", "length": 24.43, "rotation": -133.09, "x": 15.36, "y": -28.04}, {"name": "hair_FL2", "parent": "hair_FL", "length": 27.95, "rotation": -20.48, "x": 24.43, "color": "abe323ff"}, {"name": "hair_FL3", "parent": "hair_FL2", "length": 24.31, "rotation": 0.18, "x": 27.95, "color": "abe323ff"}, {"name": "hair_FL4", "parent": "hair_FL3", "length": 22.84, "rotation": -23.85, "x": 24.31, "color": "abe323ff"}, {"name": "hair_FL5", "parent": "hair_FL4", "length": 24.77, "rotation": -7.1, "x": 22.84, "color": "abe323ff"}, {"name": "hair_FL6", "parent": "hair_FL5", "length": 21.25, "rotation": 14.49, "x": 24.77, "color": "abe323ff"}, {"name": "RU_L", "parent": "body2", "length": 30, "rotation": 14.85, "x": 115.45, "y": -2.99}, {"name": "RU_R", "parent": "body2", "length": 30, "rotation": 14.85, "x": 64.4, "y": 109.72}, {"name": "RU_R2", "parent": "RU_R", "length": 30, "x": -11.07, "y": 33.04}, {"name": "RU_R3", "parent": "RU_R2", "length": 30, "x": -9.36, "y": 24.76}, {"name": "RU_L2", "parent": "RU_L", "length": 30, "x": -8.69, "y": 11.24}, {"name": "RU_L3", "parent": "RU_L2", "length": 30, "x": -8.14, "y": 10.95}, {"name": "leg_R4", "parent": "leg_R", "length": 447.2, "rotation": -13.99, "x": 101.2, "y": -0.01}, {"name": "leg_R5", "parent": "leg_R4", "length": 431.28, "rotation": 53.35, "x": 446.66, "y": -0.38, "color": "abe323ff"}, {"name": "leg_R6", "parent": "foot_R2", "rotation": -107.24, "x": 140.5, "y": 0.03, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "root", "x": -153.21, "y": 575.37, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L5", "parent": "leg_L", "length": 936.97, "rotation": -6.27, "x": 87.53, "y": 34.73}, {"name": "leg_L6", "parent": "root", "x": 12.38, "y": 115.55, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "leg_L5", "rotation": 92.56, "x": 466.52, "y": -6.07, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_L2", "parent": "sh_L", "length": 277.59, "rotation": -113.81, "x": -21.49, "y": -7.9}, {"name": "sh_L3", "parent": "sh_L2", "length": 255.99, "rotation": -98.79, "x": 277.59, "color": "abe323ff"}, {"name": "arm_L3", "parent": "hand_L2", "rotation": 18.72, "x": 59.07, "y": 0.14, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "sh_L", "rotation": -68.2, "x": -138.99, "y": -231.84, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_R2", "parent": "sh_R", "length": 278.5, "rotation": 177.86, "x": -14.93, "y": -15.34}, {"name": "sh_R3", "parent": "sh_R2", "length": 254.77, "rotation": -145.51, "x": 278.5, "color": "abe323ff"}, {"name": "arm_R1", "parent": "sh_R", "rotation": -68.2, "x": -248.21, "y": 8.79, "color": "ff3f00ff", "icon": "ik"}, {"name": "headround3", "parent": "head", "x": 361.61, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -68.07, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 298.55, "y": -68.07, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "rotation": 25.03, "x": 340.07, "y": -373.16, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "rotation": 25.03, "x": 280.87, "y": -400.8, "icon": "warning"}, {"name": "tunround", "parent": "ALL2", "x": 421.54, "y": -110.89, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 421.54, "y": -181.56, "icon": "warning"}], "slots": [{"name": "arm_Ra", "bone": "root", "attachment": "arm_Ra"}, {"name": "arm_Rb", "bone": "root", "attachment": "arm_Rb"}, {"name": "hair_B", "bone": "root", "attachment": "hair_B"}, {"name": "leg_R", "bone": "root", "attachment": "leg_R"}, {"name": "head_B", "bone": "root", "attachment": "head_B"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "body2", "bone": "root", "attachment": "body"}, {"name": "hair_FR", "bone": "root", "attachment": "hair_FR"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "nose", "bone": "root", "attachment": "nose"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "hair_FL", "bone": "root", "attachment": "hair_FL"}], "ik": [{"name": "arm_L", "order": 2, "bones": ["sh_L2", "sh_L3"], "target": "arm_L3", "bendPositive": false}, {"name": "arm_L1", "order": 4, "bones": ["arm_L"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 5, "bones": ["arm_L2"], "target": "arm_L3", "compress": true, "stretch": true}, {"name": "arm_R", "order": 7, "bones": ["sh_R2", "sh_R3"], "target": "arm_R5", "bendPositive": false}, {"name": "arm_R1", "order": 9, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 10, "bones": ["arm_R2"], "target": "arm_R5", "compress": true, "stretch": true}, {"name": "leg_L", "order": 11, "bones": ["leg_L5"], "target": "leg_L6", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 12, "bones": ["leg_L2"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 13, "bones": ["leg_L3"], "target": "leg_L6", "compress": true, "stretch": true}, {"name": "leg_R", "order": 15, "bones": ["leg_R4", "leg_R5"], "target": "leg_R6"}, {"name": "leg_R1", "order": 17, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 18, "bones": ["leg_R3"], "target": "leg_R6", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 3, "bones": ["arm_L1"], "target": "sh_L3", "rotation": 144.5, "x": 20.17, "y": -22.43, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "arm_R3", "order": 8, "bones": ["arm_R1"], "target": "sh_R3", "rotation": -100.54, "x": 45.53, "y": -12.27, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 20, "bones": ["bodyround2"], "target": "bodyround", "x": -65.34, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "order": 1, "bones": ["sh_L"], "target": "bodyround", "rotation": -25.03, "x": 60.88, "y": 266.82, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "order": 6, "bones": ["sh_R"], "target": "bodyround", "rotation": -25.03, "x": 58.31, "y": 498.08, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 21, "bones": ["headround2"], "target": "headround", "x": -63.07, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 22, "bones": ["eyebrow_L"], "target": "headround", "rotation": 65.76, "x": -290.29, "y": 68.95, "mixRotate": 0, "mixX": 0.033, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 23, "bones": ["eyebrow_R"], "target": "headround", "rotation": -104.57, "x": -289.35, "y": 159.93, "mixRotate": 0, "mixX": 0.025, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 24, "bones": ["hair_F"], "target": "headround", "x": -237.67, "y": 116.48, "mixRotate": 0, "mixX": 0.02, "mixY": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 25, "bones": ["hair_B"], "target": "headround", "x": -227.66, "y": 6.99, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_R3", "order": 16, "bones": ["leg_R1"], "target": "leg_R5", "rotation": 47, "x": 18.8, "y": 47.91, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "tunround", "order": 19, "bones": ["tunround2"], "target": "tunround", "y": -70.67, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "bones": ["leg_L"], "target": "tunround", "rotation": -86.28, "x": -339.51, "y": 89.57, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 14, "bones": ["leg_R"], "target": "tunround", "rotation": -86.28, "x": -483.26, "y": 50.37, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "hair_B3", "order": 26, "bone": "hair_B3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B4", "order": 27, "bone": "hair_B4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B5", "order": 28, "bone": "hair_B5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B6", "order": 29, "bone": "hair_B6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B7", "order": 30, "bone": "hair_B7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B8", "order": 31, "bone": "hair_B8", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL2", "order": 32, "bone": "hair_FL2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL3", "order": 33, "bone": "hair_FL3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL4", "order": 34, "bone": "hair_FL4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL5", "order": 35, "bone": "hair_FL5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL6", "order": 36, "bone": "hair_FL6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR2", "order": 37, "bone": "hair_FR2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR3", "order": 38, "bone": "hair_FR3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR4", "order": 39, "bone": "hair_FR4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR5", "order": 40, "bone": "hair_FR5", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_Ra": {"arm_Ra": {"type": "mesh", "uvs": [0.69045, 0.01426, 0.86351, 0.02839, 0.96672, 0.03681, 0.9979, 0.14674, 0.89356, 0.23574, 0.74906, 0.27385, 0.6557, 0.29847, 0.61787, 0.35074, 0.5735, 0.44856, 0.50657, 0.55514, 0.4262, 0.66976, 0.39767, 0.66728, 0.27753, 0.83944, 0.21357, 0.91224, 0.17143, 0.95054, 0.13687, 0.97655, 0.08736, 0.99795, 0.05284, 0.9975, 0.02184, 0.97763, 0.00179, 0.93332, 0.002, 0.87384, 0.0239, 0.79471, 0.0567, 0.70982, 0.15489, 0.53967, 0.14585, 0.52107, 0.2281, 0.38743, 0.28392, 0.29672, 0.32417, 0.2075, 0.36644, 0.11815, 0.41823, 0.05532, 0.47425, 0.0168, 0.5161, 0.00124, 0.57542, 0.00487, 0.43834, 0.06604, 0.42114, 0.11851, 0.17114, 0.85034, 0.2099, 0.79073, 0.35529, 0.59522, 0.23835, 0.54194, 0.12454, 0.75035, 0.09381, 0.82237, 0.09381, 0.90639, 0.42194, 0.21991, 0.55456, 0.25865, 0.53096, 0.32826, 0.36787, 0.31562, 0.49929, 0.38155, 0.55409, 0.16175, 0.69159, 0.13589, 0.86085, 0.12307, 0.54644, 0.06024, 0.43552, 0.47838, 0.31079, 0.40983], "triangles": [41, 16, 17, 19, 20, 41, 17, 18, 41, 18, 19, 41, 20, 21, 40, 20, 40, 41, 42, 28, 34, 47, 42, 34, 42, 47, 43, 28, 42, 27, 7, 46, 44, 8, 46, 7, 44, 42, 43, 15, 16, 41, 14, 41, 35, 13, 14, 35, 41, 40, 35, 40, 21, 39, 35, 36, 12, 36, 35, 39, 39, 22, 23, 36, 39, 38, 36, 37, 11, 37, 51, 9, 51, 45, 46, 38, 23, 24, 52, 45, 51, 52, 26, 45, 38, 25, 52, 25, 26, 52, 38, 24, 25, 26, 27, 45, 45, 27, 42, 37, 38, 52, 51, 37, 52, 45, 42, 44, 46, 45, 44, 51, 46, 8, 36, 38, 37, 10, 11, 37, 9, 10, 37, 9, 51, 8, 39, 23, 38, 12, 36, 11, 35, 40, 39, 21, 22, 39, 13, 35, 12, 15, 41, 14, 7, 44, 43, 6, 7, 43, 50, 31, 32, 33, 29, 30, 34, 29, 33, 28, 29, 34, 30, 31, 50, 33, 30, 50, 34, 33, 50, 6, 43, 47, 50, 47, 34, 50, 48, 47, 49, 0, 1, 49, 1, 2, 48, 0, 49, 49, 2, 3, 4, 49, 3, 5, 48, 49, 5, 49, 4, 5, 6, 48, 48, 50, 0, 48, 6, 47, 0, 50, 32], "vertices": [1, 7, 210.78, 58.34, 1, 1, 7, 223.73, 15.8, 1, 1, 7, 231.45, -9.57, 1, 1, 7, 207.17, -27.88, 1, 2, 7, 175.18, -11.9, 0.97454, 38, -18.09, 98.03, 0.02546, 2, 7, 152, 18.66, 0.84104, 38, 8.12, 70.01, 0.15896, 3, 7, 137.02, 38.41, 0.32665, 26, -45.19, -64.42, 0.37907, 38, 25.05, 51.91, 0.29429, 3, 7, 120.46, 42.22, 0.06659, 26, -61.75, -60.61, 0.39955, 38, 41.91, 49.83, 0.53385, 2, 26, -90.21, -59.76, 0.04947, 38, 70.31, 51.92, 0.95053, 1, 38, 103.47, 49.98, 1, 1, 38, 140.13, 46, 1, 1, 38, 142.94, 39.22, 1, 1, 38, 197.94, 33.37, 1, 2, 38, 222.76, 27.91, 0.95579, 39, -9.74, -28.68, 0.04421, 2, 38, 236.83, 23.12, 0.84051, 39, -17.07, -15.76, 0.15949, 2, 38, 247.09, 18.51, 0.67161, 39, -21.69, -5.5, 0.32839, 2, 38, 258.05, 9.95, 0.48485, 39, -24.19, 8.18, 0.51515, 2, 38, 262.05, 2.07, 0.31718, 39, -21.95, 16.73, 0.68282, 2, 38, 261.04, -7.42, 0.17608, 39, -14.9, 23.16, 0.82392, 2, 38, 252.95, -17.47, 0.1008, 39, -2.18, 25.3, 0.8992, 2, 38, 238.87, -24.81, 0.49059, 39, 13.22, 21.43, 0.50941, 1, 38, 217.56, -29.67, 1, 1, 38, 193.59, -32.77, 1, 1, 38, 141.68, -31.64, 1, 1, 38, 138.36, -36, 1, 1, 38, 96.98, -33.95, 1, 1, 38, 68.89, -32.56, 1, 2, 26, -54.16, 23.41, 0.16514, 38, 43.01, -34.52, 0.83486, 2, 26, -27.99, 22.22, 0.53982, 38, 16.86, -36.04, 0.46018, 2, 26, -7.49, 16.14, 0.82418, 38, -4.16, -32.1, 0.17582, 2, 7, 189.59, 109.47, 0.01537, 26, 7.39, 6.65, 0.98463, 2, 7, 197.43, 101.07, 0.09287, 26, 15.22, -1.76, 0.90713, 2, 7, 202.17, 86.61, 0.3688, 26, 19.96, -16.22, 0.6312, 3, 26, -8.24, 10.3, 0.81338, 38, -4.02, -26.21, 0.18382, 78, 55.21, 510.9, 0.0028, 4, 7, 159.33, 112.01, 0.16237, 26, -22.88, 9.18, 0.44308, 38, 10.43, -23.59, 0.38523, 78, 41.47, 516.08, 0.00932, 1, 38, 213.18, 10.61, 1, 1, 38, 194.48, 11.99, 1, 2, 38, 130.96, 20.67, 0.99033, 78, -84.66, 540.07, 0.00967, 2, 38, 132.28, -12.45, 0.99011, 78, -68.78, 569.16, 0.00989, 1, 38, 195.09, -12.37, 1, 1, 38, 215.77, -10.39, 1, 2, 38, 235.64, 0.04, 0.57951, 39, -0.86, 0.7, 0.42049, 4, 7, 134.27, 101.76, 0.10984, 26, -47.94, -1.06, 0.18064, 38, 34.31, -10.82, 0.6938, 78, 14.42, 517.4, 0.01573, 4, 7, 137.27, 66.4, 0.17792, 26, -44.94, -36.43, 0.28336, 38, 27.68, 24.04, 0.52236, 78, 2.19, 484.09, 0.01635, 4, 7, 117.77, 65.11, 0.01924, 26, -64.43, -37.72, 0.16279, 38, 46.94, 27.34, 0.80117, 78, -16.03, 491.16, 0.0168, 2, 38, 63.37, -11.19, 0.98444, 78, -10.31, 532.66, 0.01556, 2, 38, 63.31, 26.78, 0.98273, 78, -29.78, 500.06, 0.01727, 3, 7, 161.25, 76.12, 0.74631, 38, 4.83, 11.91, 0.246, 78, 28.03, 482.75, 0.00769, 2, 7, 180.73, 46, 0.96455, 38, -17.65, 39.86, 0.03545, 1, 7, 200, 7.04, 1, 3, 7, 185.69, 88.01, 0.46626, 26, 3.48, -14.82, 0.49526, 38, -18.26, -2.43, 0.03848, 2, 38, 93.79, 24.35, 0.98352, 78, -54.67, 517.81, 0.01648, 2, 38, 92.43, -12.43, 0.98583, 78, -34.6, 548.66, 0.01417], "hull": 33, "edges": [6, 8, 12, 14, 20, 22, 22, 24, 34, 36, 52, 54, 54, 56, 56, 58, 62, 64, 58, 60, 60, 62, 44, 46, 46, 48, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 24, 26, 14, 16, 16, 18, 18, 20, 48, 50, 50, 52, 26, 28, 28, 30, 70, 72, 72, 74, 76, 78, 88, 86, 90, 84, 92, 88, 4, 6, 0, 64, 0, 2, 2, 4, 8, 10, 10, 12, 74, 102, 102, 92, 76, 104, 104, 90], "width": 256, "height": 267}}, "arm_Rb": {"arm_Rb": {"type": "mesh", "uvs": [0.96867, 0, 0.98718, 0.00868, 0.99904, 0.03542, 1, 0.0711, 0.99013, 0.09481, 0.96145, 0.11471, 0.70818, 0.20143, 0.43689, 0.28884, 0.27325, 0.34387, 0.26396, 0.39186, 0.24217, 0.43487, 0.21952, 0.46528, 0.22205, 0.48457, 0.2244, 0.50901, 0.23044, 0.53554, 0.24038, 0.5791, 0.233, 0.5945, 0.246, 0.67153, 0.26638, 0.75125, 0.28061, 0.81098, 0.28317, 0.84746, 0.28213, 0.88355, 0.2786, 0.91001, 0.27874, 0.97023, 0.26593, 0.98734, 0.25297, 0.99802, 0.23611, 0.99883, 0.22581, 0.98597, 0.21766, 0.95667, 0.20648, 0.92607, 0.19376, 0.89381, 0.1837, 0.85496, 0.17837, 0.81279, 0.16928, 0.69835, 0.1641, 0.62043, 0.1519, 0.61857, 0.14728, 0.56312, 0.14469, 0.53197, 0.13927, 0.5046, 0.1394, 0.48231, 0.12831, 0.45126, 0.12203, 0.39912, 0.07606, 0.41722, 0.02289, 0.45146, 0.00834, 0.43365, 1e-05, 0.3891, 0.00631, 0.35181, 0.07067, 0.3416, 0.121, 0.32188, 0.1278, 0.28366, 0.20297, 0.24133, 0.21484, 0.24874, 0.22224, 0.28552, 0.42473, 0.21107, 0.75368, 0.07615, 0.93541, 0.00867, 0.2494, 0.95122, 0.22747, 0.36421, 0.17961, 0.38917, 0.18074, 0.4275, 0.17993, 0.47009, 0.18315, 0.49376, 0.18597, 0.52137, 0.19, 0.55162, 0.2206, 0.41035], "triangles": [42, 43, 45, 43, 44, 45, 42, 46, 47, 46, 42, 45, 42, 47, 41, 47, 48, 41, 41, 48, 58, 9, 57, 8, 48, 49, 58, 52, 50, 51, 50, 52, 49, 52, 58, 49, 57, 52, 8, 8, 53, 7, 8, 52, 53, 7, 53, 6, 6, 53, 54, 5, 54, 55, 5, 6, 54, 2, 4, 0, 4, 5, 0, 5, 55, 0, 4, 2, 3, 2, 0, 1, 61, 39, 60, 61, 60, 11, 60, 39, 59, 60, 59, 11, 59, 39, 40, 11, 64, 10, 11, 59, 64, 40, 41, 59, 10, 64, 9, 41, 58, 59, 59, 58, 64, 64, 57, 9, 64, 58, 57, 58, 52, 57, 22, 29, 21, 21, 30, 20, 25, 26, 56, 56, 26, 27, 27, 28, 56, 28, 29, 56, 56, 29, 22, 21, 29, 30, 20, 30, 31, 31, 19, 20, 31, 32, 19, 32, 18, 19, 18, 33, 17, 18, 32, 33, 17, 34, 16, 16, 34, 63, 34, 17, 33, 63, 34, 36, 34, 35, 36, 63, 14, 16, 16, 14, 15, 36, 62, 63, 36, 37, 62, 63, 13, 14, 63, 62, 13, 37, 38, 61, 37, 61, 62, 39, 61, 38, 13, 61, 12, 13, 62, 61, 61, 11, 12, 24, 25, 56, 24, 56, 23, 56, 22, 23], "vertices": [1, 42, 595.31, 22.89, 1, 1, 42, 607.63, 16.2, 1, 1, 42, 613.49, 2.7, 1, 1, 42, 610.75, -12.99, 1, 1, 42, 601.46, -21.77, 1, 1, 42, 579.15, -25.94, 1, 1, 42, 390.77, -23.99, 1, 1, 42, 189.51, -19.52, 1, 2, 41, 59.17, -69.39, 0.05898, 42, 67.89, -17.82, 0.94102, 2, 41, 38.21, -61.37, 0.35617, 42, 56.67, -37.26, 0.64383, 3, 39, 221.13, -47.3, 0.02159, 41, 19.99, -44.4, 0.71567, 42, 37.05, -52.58, 0.26274, 3, 39, 211.92, -28.04, 0.26345, 41, 7.43, -27.14, 0.69877, 42, 18.03, -62.27, 0.03779, 3, 39, 203.13, -27.76, 0.57693, 41, -1.27, -28.47, 0.41515, 42, 17.97, -71.07, 0.00791, 2, 39, 192.14, -26.8, 0.86281, 41, -12.25, -29.53, 0.13719, 2, 39, 179.6, -28.23, 0.98136, 41, -24.32, -33.22, 0.01864, 1, 39, 159.01, -30.59, 1, 1, 39, 153.63, -23.72, 1, 1, 39, 118.01, -24.66, 1, 1, 39, 79.94, -30.53, 1, 2, 38, 173.24, -8.7, 0.05149, 39, 51.6, -34.19, 0.94851, 2, 38, 186.78, 0.51, 0.21171, 39, 35.36, -32.08, 0.78829, 2, 38, 201.38, 7.32, 0.49276, 39, 19.92, -27.48, 0.50724, 1, 38, 213.02, 10.54, 1, 2, 38, 236.75, 23.11, 0.85097, 39, -17.01, -15.81, 0.14903, 2, 38, 247.84, 18.4, 0.68605, 39, -22.18, -4.92, 0.31395, 2, 38, 256.45, 12.27, 0.51756, 39, -24.53, 5.38, 0.48244, 2, 38, 262.47, 1.57, 0.35745, 39, -21.94, 17.38, 0.64255, 2, 38, 260.88, -7.73, 0.25981, 39, -14.57, 23.28, 0.74019, 2, 38, 252.07, -19.06, 0.08051, 39, -0.46, 25.91, 0.91949, 1, 39, 14.74, 30.53, 1, 1, 39, 30.92, 36.06, 1, 1, 39, 49.5, 39.01, 1, 1, 39, 68.69, 38.27, 1, 1, 39, 119.82, 32.43, 1, 1, 39, 154.45, 27.75, 1, 2, 39, 157.39, 36.16, 1, 42, -44.77, -118.42, 0, 3, 39, 182.2, 33.49, 0.96775, 41, -33.02, 27.94, 0.0321, 42, -42.73, -93.55, 0.00015, 3, 39, 196.14, 31.99, 0.81358, 41, -19.04, 29.01, 0.17955, 42, -41.58, -79.58, 0.00686, 3, 39, 208.93, 32.88, 0.55657, 41, -6.62, 32.22, 0.41276, 42, -42.8, -66.81, 0.03067, 3, 39, 218.56, 30.41, 0.30316, 41, 3.3, 31.54, 0.61907, 42, -40.57, -57.12, 0.07777, 3, 39, 233.94, 34.92, 0.07198, 41, 17.6, 38.78, 0.68349, 42, -45.47, -41.86, 0.24453, 2, 41, 41.08, 41.97, 0.24676, 42, -44.92, -18.17, 0.75324, 2, 41, 35, 75.85, 0.0019, 42, -79.34, -18.84, 0.9981, 1, 42, -120.43, -25.41, 1, 1, 42, -129.06, -15.37, 1, 1, 42, -130.7, 5.34, 1, 1, 42, -122.64, 20.59, 1, 1, 42, -75.9, 14.94, 1, 1, 42, -38.23, 15.64, 1, 2, 41, 92.24, 34.73, 0.15581, 42, -29.72, 31.21, 0.84419, 2, 41, 107.85, -21.02, 0.65003, 42, 27.78, 37.86, 0.34997, 2, 41, 104.04, -29.45, 0.56098, 42, 35.51, 32.77, 0.43902, 2, 41, 87.34, -33.85, 0.14068, 42, 37.23, 15.59, 0.85932, 1, 42, 188.34, 16.26, 1, 1, 42, 435.16, 23.44, 1, 1, 42, 570.83, 24.33, 1, 2, 38, 239.18, 0.26, 0.43244, 39, -3.65, 2.89, 0.56756, 2, 41, 52.09, -35.58, 0.37645, 42, 33.39, -19.49, 0.62355, 2, 41, 43.03, -0.14, 0.98521, 42, -3.03, -22.86, 0.01479, 1, 41, 25.92, 0.05, 1, 3, 39, 216.76, 0.46, 0.09241, 41, 6.99, 1.76, 0.90628, 42, -10.58, -58.15, 0.00131, 2, 39, 205.95, 0.71, 0.70732, 41, -3.68, 0.04, 0.29268, 2, 39, 193.51, 1.68, 0.99992, 42, -11.21, -81.44, 8e-05, 1, 39, 179.71, 2.07, 1, 3, 39, 235.52, -34.69, 0.00966, 41, 31.84, -29.37, 0.78994, 42, 24.08, -38.51, 0.2004], "hull": 56, "edges": [0, 110, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 22, 24, 62, 64, 68, 70, 82, 84, 84, 86, 86, 88, 88, 90, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 108, 110, 104, 106, 106, 108, 16, 18, 18, 20, 20, 22, 24, 26, 30, 32, 32, 34, 64, 66, 66, 68, 80, 82, 76, 78, 78, 80, 74, 76, 70, 72, 72, 74, 26, 28, 28, 30, 92, 94, 90, 92, 52, 54, 54, 56, 48, 50, 50, 52, 46, 48, 42, 44, 44, 46, 38, 40, 40, 42, 34, 36, 36, 38, 60, 62, 56, 58, 58, 60, 82, 116, 116, 114, 12, 14, 14, 16], "width": 728, "height": 446}}, "body": {"body": {"type": "mesh", "uvs": [0.43991, 0.0003, 0.48558, 0.00298, 0.53143, 0.00872, 0.56349, 0.02092, 0.58705, 0.03956, 0.58911, 0.05483, 0.57969, 0.07029, 0.56443, 0.08085, 0.53945, 0.09109, 0.54011, 0.09803, 0.5511, 0.10552, 0.53747, 0.1058, 0.52471, 0.1016, 0.51961, 0.10457, 0.52106, 0.11137, 0.52275, 0.11791, 0.54028, 0.11881, 0.54951, 0.13278, 0.56754, 0.14149, 0.59901, 0.14498, 0.63344, 0.14182, 0.66855, 0.14219, 0.7003, 0.1477, 0.72856, 0.15677, 0.75101, 0.17071, 0.76612, 0.18337, 0.82409, 0.19938, 0.8852, 0.21846, 0.88491, 0.22239, 0.92017, 0.2337, 0.95741, 0.24763, 0.97932, 0.25733, 0.99273, 0.26472, 0.99844, 0.27159, 0.99356, 0.2776, 0.97024, 0.28442, 0.92255, 0.30138, 0.88696, 0.31196, 0.86724, 0.31782, 0.81405, 0.33077, 0.78311, 0.31309, 0.74195, 0.30179, 0.78145, 0.27975, 0.80755, 0.27005, 0.82322, 0.26582, 0.8007, 0.26129, 0.77523, 0.25405, 0.7555, 0.25611, 0.69848, 0.23977, 0.66312, 0.22943, 0.64189, 0.22254, 0.62222, 0.23432, 0.57122, 0.25271, 0.52785, 0.26471, 0.4972, 0.27334, 0.48241, 0.28339, 0.48153, 0.29313, 0.46945, 0.29613, 0.43216, 0.29895, 0.43196, 0.30626, 0.44698, 0.31371, 0.48583, 0.32639, 0.50882, 0.3245, 0.52678, 0.32672, 0.55191, 0.33175, 0.58505, 0.33618, 0.60382, 0.33718, 0.61917, 0.33769, 0.63364, 0.33539, 0.64637, 0.33227, 0.67021, 0.32417, 0.69404, 0.31607, 0.7044, 0.31719, 0.76951, 0.33925, 0.78054, 0.34477, 0.73657, 0.35218, 0.69261, 0.3596, 0.66825, 0.36374, 0.64323, 0.36802, 0.6199, 0.37105, 0.59641, 0.37131, 0.63083, 0.38363, 0.65982, 0.39784, 0.68071, 0.41442, 0.69097, 0.43726, 0.6915, 0.45541, 0.70186, 0.47329, 0.68223, 0.4769, 0.67598, 0.5218, 0.65778, 0.56693, 0.63078, 0.60448, 0.62043, 0.61888, 0.61785, 0.62885, 0.61919, 0.64083, 0.62231, 0.65081, 0.62553, 0.66179, 0.64054, 0.67684, 0.66879, 0.70044, 0.67831, 0.72212, 0.67169, 0.74622, 0.64064, 0.78273, 0.6033, 0.80938, 0.59673, 0.83159, 0.53754, 0.88029, 0.52667, 0.8926, 0.52198, 0.90446, 0.5212, 0.91259, 0.5188, 0.92076, 0.53436, 0.93616, 0.54264, 0.94984, 0.54017, 0.98376, 0.46624, 0.98902, 0.41298, 0.98146, 0.35701, 0.99234, 0.26615, 0.99974, 0.13504, 0.99944, 0.08392, 0.99598, 0.04982, 0.9894, 0.06539, 0.96705, 0.09117, 0.95935, 0.17049, 0.95656, 0.20798, 0.95077, 0.24335, 0.94184, 0.27346, 0.93258, 0.3083, 0.9229, 0.33465, 0.91322, 0.35217, 0.9044, 0.3622, 0.89468, 0.37394, 0.88227, 0.38263, 0.86922, 0.32909, 0.83442, 0.40199, 0.82571, 0.39389, 0.82115, 0.40707, 0.815, 0.44008, 0.81084, 0.44541, 0.80006, 0.45176, 0.77242, 0.45335, 0.74728, 0.45297, 0.71164, 0.45272, 0.68855, 0.44792, 0.68049, 0.43057, 0.66764, 0.42551, 0.65749, 0.41233, 0.64618, 0.40547, 0.63574, 0.4057, 0.61904, 0.39298, 0.60278, 0.3596, 0.55877, 0.339, 0.5182, 0.32883, 0.48248, 0.3221, 0.47985, 0.32623, 0.45904, 0.30986, 0.45775, 0.23808, 0.44483, 0.17509, 0.43587, 0.10097, 0.43173, 0.10634, 0.42306, 0.11821, 0.40746, 0.1211, 0.39278, 0.11282, 0.38029, 0.10684, 0.3607, 0.12964, 0.35606, 0.13678, 0.34049, 0.14851, 0.3264, 0.14822, 0.30949, 0.13591, 0.29122, 0.0996, 0.28512, 0.11328, 0.26802, 0.10789, 0.26036, 0.0731, 0.25812, 0.03376, 0.25048, 0.00947, 0.23907, 0.00079, 0.22318, 0.01051, 0.20912, 0.04008, 0.19729, 0.08666, 0.18782, 0.14134, 0.18143, 0.20725, 0.17632, 0.21273, 0.16833, 0.22109, 0.16054, 0.23692, 0.15344, 0.25828, 0.15095, 0.28243, 0.15005, 0.3126, 0.1342, 0.38716, 0.12628, 0.38552, 0.11733, 0.38293, 0.10743, 0.37835, 0.09806, 0.37174, 0.08771, 0.36134, 0.07213, 0.31887, 0.0259, 0.33246, 0.01804, 0.35175, 0.00965, 0.37133, 0.00409, 0.40131, 0.00112, 0.51914, 0.09382, 0.52442, 0.0808, 0.85754, 0.26039, 0.89467, 0.25712, 0.94065, 0.25807, 0.94366, 0.27013, 0.76203, 0.24984, 0.87654, 0.22013, 0.82972, 0.25424, 0.8946, 0.24052, 0.91938, 0.24876, 0.92872, 0.28019, 0.90383, 0.26313, 0.87407, 0.26784, 0.81735, 0.2882, 0.86108, 0.3014, 0.14873, 0.25895, 0.20332, 0.25073, 0.28456, 0.25855, 0.38179, 0.26124, 0.47767, 0.24925, 0.54281, 0.22986, 0.61389, 0.21173, 0.56234, 0.20558, 0.58839, 0.20291, 0.58135, 0.18432, 0.58895, 0.16783, 0.60944, 0.15501, 0.63662, 0.14688, 0.51835, 0.18097, 0.443, 0.16998, 0.36156, 0.17297, 0.2938, 0.17974, 0.25503, 0.17345, 0.03729, 0.22276, 0.32238, 0.22346, 0.05262, 0.20285, 0.15489, 0.18699, 0.26865, 0.18764, 0.35629, 0.18271, 0.50303, 0.22556, 0.50462, 0.20433, 0.45393, 0.18779, 0.23101, 0.24358, 0.36859, 0.25175, 0.45725, 0.24227, 0.33, 0.19822, 0.40326, 0.20541, 0.42074, 0.22259, 0.26283, 0.2054, 0.24035, 0.2223, 0.26612, 0.23819, 0.40219, 0.23768, 0.33463, 0.2426, 0.19464, 0.20228, 0.17912, 0.23347, 0.14622, 0.24666, 0.09573, 0.2537, 0.09573, 0.20867, 0.10931, 0.22542, 0.09094, 0.23995, 0.04939, 0.24488, 0.29054, 0.25005, 0.17796, 0.21845, 0.39173, 0.14207, 0.25248, 0.16371, 0.32997, 0.14843, 0.30937, 0.16177, 0.34986, 0.16091, 0.51367, 0.15365, 0.40844, 0.13505, 0.40701, 0.15286, 0.48073, 0.12782, 0.42153, 0.12606, 0.4201, 0.11594, 0.41796, 0.10539, 0.41439, 0.09593, 0.41011, 0.0845, 0.47502, 0.08208, 0.47645, 0.09351, 0.47574, 0.10407, 0.47788, 0.1144, 0.54685, 0.15952, 0.46757, 0.16969, 0.4415, 0.15362, 0.49711, 0.14136, 0.66586, 0.15414, 0.6909, 0.16443, 0.70684, 0.178, 0.72605, 0.18999, 0.84188, 0.22569, 0.78808, 0.23961, 0.67274, 0.20351, 0.86574, 0.24642, 0.64077, 0.18967, 0.62293, 0.17375, 0.12983, 0.28043, 0.44414, 0.28739, 0.22024, 0.27592, 0.22348, 0.28967, 0.22754, 0.30516, 0.23159, 0.3229, 0.23544, 0.34108, 0.24363, 0.36305, 0.16402, 0.27719, 0.16927, 0.29059, 0.17506, 0.30734, 0.17889, 0.32429, 0.1761, 0.34569, 0.18817, 0.36498, 0.13096, 0.36142, 0.29825, 0.27734, 0.29593, 0.2895, 0.29733, 0.3028, 0.30881, 0.31823, 0.3217, 0.33804, 0.32915, 0.35676, 0.37537, 0.28077, 0.37224, 0.29079, 0.37412, 0.3033, 0.38036, 0.31409, 0.39895, 0.32553, 0.41909, 0.32962, 0.35216, 0.34722, 0.3625, 0.35223, 0.45181, 0.2672, 0.18076, 0.38802, 0.26039, 0.38605, 0.186, 0.40373, 0.1803, 0.41986, 0.26981, 0.40356, 0.28039, 0.42296, 0.30085, 0.44472, 0.35277, 0.38054, 0.37035, 0.39483, 0.38981, 0.40886, 0.41572, 0.42135, 0.35632, 0.43727, 0.42062, 0.34435, 0.35092, 0.36821, 0.37337, 0.38059, 0.43834, 0.38533, 0.49209, 0.37349, 0.53285, 0.36785, 0.56444, 0.38325, 0.59276, 0.40041, 0.4697, 0.39469, 0.475, 0.41133, 0.61182, 0.42115, 0.61502, 0.4719, 0.61877, 0.44652, 0.45812, 0.42982, 0.45277, 0.44959, 0.44598, 0.47264, 0.43366, 0.51754, 0.43486, 0.5704, 0.44096, 0.60302, 0.44357, 0.61726, 0.43747, 0.63391, 0.44357, 0.65244, 0.45838, 0.67016, 0.47929, 0.68628, 0.48756, 0.74684, 0.58513, 0.74496, 0.48857, 0.82648, 0.57223, 0.7129, 0.53913, 0.68416, 0.48517, 0.71333, 0.53798, 0.66566, 0.52927, 0.64713, 0.51794, 0.63236, 0.52056, 0.61813, 0.537, 0.60263, 0.57044, 0.57182, 0.59675, 0.51796, 0.34849, 0.02251, 0.39737, 0.02169, 0.51498, 0.06914, 0.51893, 0.06155, 0.54457, 0.05993, 0.57368, 0.04943, 0.37242, 0.02194, 0.39078, 0.01211, 0.40715, 0.00591, 0.35727, 0.01464, 0.49122, 0.00887, 0.42415, 0.02304, 0.44442, 0.02619, 0.46453, 0.03474, 0.48175, 0.04757, 0.49723, 0.06024, 0.50676, 0.06725, 0.41339, 0.01374, 0.42913, 0.01894, 0.37178, 0.01701, 0.37884, 0.011, 0.44143, 0.00353, 0.44739, 0.00993, 0.48665, 0.02105, 0.50806, 0.03412, 0.52455, 0.04715, 0.52188, 0.0175, 0.542, 0.0274, 0.56232, 0.0396, 0.55505, 0.06809, 0.8444, 0.27751, 0.8995, 0.29072], "triangles": [157, 158, 323, 154, 324, 153, 153, 324, 326, 325, 324, 323, 155, 156, 154, 154, 156, 324, 156, 157, 324, 324, 157, 323, 115, 120, 114, 120, 121, 114, 113, 114, 122, 115, 118, 119, 115, 119, 120, 117, 118, 116, 115, 116, 118, 114, 121, 122, 113, 122, 112, 110, 111, 109, 109, 111, 112, 122, 123, 112, 123, 124, 112, 112, 124, 108, 108, 124, 125, 112, 108, 109, 107, 125, 126, 125, 107, 108, 107, 126, 106, 106, 126, 105, 105, 126, 127, 105, 127, 104, 104, 127, 128, 128, 129, 104, 104, 129, 103, 129, 359, 103, 129, 131, 359, 359, 131, 134, 131, 133, 134, 103, 359, 102, 129, 130, 131, 102, 359, 101, 134, 135, 359, 358, 100, 101, 136, 359, 135, 131, 132, 133, 101, 359, 358, 136, 137, 357, 359, 357, 358, 357, 359, 136, 100, 358, 99, 137, 362, 357, 137, 138, 362, 357, 360, 358, 357, 362, 360, 99, 358, 98, 358, 360, 98, 360, 97, 98, 362, 139, 356, 362, 361, 360, 362, 356, 361, 360, 96, 97, 360, 361, 96, 362, 138, 139, 139, 140, 356, 363, 361, 356, 356, 140, 355, 356, 355, 363, 96, 363, 95, 96, 361, 363, 140, 141, 355, 364, 363, 355, 354, 355, 142, 355, 141, 142, 355, 354, 364, 363, 94, 95, 363, 364, 94, 149, 150, 151, 340, 341, 339, 340, 81, 82, 142, 143, 354, 143, 353, 354, 354, 365, 364, 354, 353, 365, 364, 93, 94, 364, 365, 93, 143, 144, 353, 365, 92, 93, 144, 145, 353, 353, 352, 365, 353, 145, 352, 365, 366, 92, 365, 352, 366, 92, 366, 91, 145, 351, 352, 145, 146, 351, 366, 367, 91, 91, 367, 90, 367, 366, 351, 366, 352, 351, 367, 368, 90, 90, 368, 89, 367, 351, 350, 351, 146, 350, 146, 147, 350, 367, 350, 368, 89, 368, 369, 147, 349, 350, 369, 368, 349, 368, 350, 349, 89, 369, 88, 147, 148, 349, 88, 369, 87, 148, 149, 349, 349, 348, 369, 369, 348, 344, 369, 344, 87, 344, 348, 347, 344, 347, 345, 349, 149, 348, 348, 149, 151, 86, 87, 85, 87, 344, 85, 345, 347, 346, 346, 342, 343, 342, 340, 343, 348, 151, 347, 344, 345, 85, 345, 346, 343, 345, 84, 85, 347, 332, 346, 345, 343, 84, 343, 83, 84, 346, 331, 342, 343, 82, 83, 343, 340, 82, 341, 340, 342, 347, 151, 332, 282, 22, 23, 290, 281, 282, 208, 44, 197, 207, 199, 200, 200, 31, 32, 200, 199, 31, 208, 198, 207, 208, 197, 198, 44, 203, 197, 44, 45, 203, 207, 198, 199, 45, 46, 203, 197, 288, 198, 197, 203, 288, 198, 205, 199, 199, 30, 31, 199, 205, 30, 288, 204, 198, 198, 204, 205, 47, 201, 46, 47, 48, 201, 288, 203, 286, 203, 46, 286, 46, 201, 286, 201, 48, 286, 30, 205, 29, 205, 204, 29, 286, 285, 288, 204, 285, 28, 28, 285, 202, 285, 204, 288, 204, 28, 29, 286, 48, 287, 48, 49, 287, 287, 284, 286, 286, 284, 285, 49, 50, 287, 285, 284, 26, 284, 25, 26, 285, 26, 202, 50, 217, 287, 28, 202, 27, 202, 26, 27, 287, 217, 289, 287, 289, 284, 289, 283, 284, 284, 283, 25, 289, 290, 283, 283, 24, 25, 290, 282, 283, 283, 282, 24, 282, 23, 24, 220, 290, 289, 78, 67, 77, 67, 68, 77, 77, 68, 76, 68, 69, 76, 76, 69, 75, 75, 69, 70, 73, 75, 70, 70, 71, 72, 75, 73, 74, 73, 70, 72, 39, 40, 38, 40, 210, 38, 38, 210, 37, 40, 209, 210, 40, 41, 209, 37, 210, 36, 41, 42, 209, 209, 400, 210, 210, 401, 36, 210, 400, 401, 36, 206, 35, 36, 401, 206, 400, 208, 401, 401, 208, 206, 42, 43, 209, 209, 43, 400, 34, 35, 200, 208, 207, 206, 35, 206, 200, 206, 207, 200, 34, 200, 33, 43, 44, 400, 208, 400, 44, 200, 32, 33, 81, 340, 339, 79, 67, 78, 80, 66, 67, 80, 65, 66, 338, 64, 65, 64, 61, 63, 81, 339, 80, 339, 338, 80, 64, 338, 61, 338, 65, 80, 80, 67, 79, 330, 336, 341, 337, 338, 339, 329, 335, 336, 341, 336, 337, 337, 336, 333, 335, 333, 336, 337, 333, 338, 339, 341, 337, 63, 61, 62, 303, 297, 298, 158, 321, 323, 322, 321, 304, 158, 159, 321, 321, 159, 304, 304, 159, 305, 159, 160, 305, 304, 161, 303, 304, 303, 298, 160, 161, 305, 304, 305, 161, 161, 162, 303, 162, 163, 303, 322, 323, 321, 304, 298, 322, 60, 315, 59, 318, 310, 316, 333, 61, 338, 334, 319, 333, 333, 318, 317, 333, 319, 318, 318, 316, 317, 333, 317, 61, 317, 60, 61, 317, 316, 60, 60, 316, 315, 331, 330, 342, 332, 151, 327, 153, 327, 152, 151, 152, 327, 153, 326, 327, 324, 325, 326, 327, 326, 332, 332, 330, 331, 332, 326, 330, 330, 325, 329, 330, 326, 325, 330, 329, 336, 323, 322, 325, 325, 328, 329, 325, 322, 328, 329, 328, 335, 322, 334, 328, 334, 298, 311, 334, 322, 298, 335, 328, 334, 335, 334, 333, 334, 311, 319, 311, 297, 310, 311, 298, 297, 311, 318, 319, 311, 310, 318, 330, 341, 342, 346, 332, 331, 166, 167, 291, 293, 213, 306, 303, 302, 297, 303, 163, 302, 302, 296, 297, 310, 296, 309, 310, 297, 296, 316, 309, 315, 316, 310, 309, 163, 164, 302, 164, 301, 302, 302, 301, 296, 301, 295, 296, 309, 295, 308, 309, 296, 295, 309, 314, 315, 309, 308, 314, 315, 314, 59, 301, 165, 300, 301, 164, 165, 295, 300, 294, 295, 301, 300, 59, 314, 58, 308, 294, 307, 308, 295, 294, 308, 313, 314, 314, 313, 58, 308, 307, 313, 58, 292, 57, 58, 313, 292, 57, 292, 56, 165, 291, 300, 313, 312, 292, 313, 307, 312, 294, 300, 293, 300, 299, 293, 307, 294, 306, 307, 306, 312, 306, 294, 293, 312, 306, 214, 291, 299, 300, 166, 291, 165, 228, 178, 260, 228, 177, 178, 178, 179, 260, 182, 262, 260, 260, 181, 182, 262, 228, 260, 179, 180, 260, 260, 180, 181, 221, 277, 222, 281, 290, 222, 281, 222, 223, 282, 281, 22, 277, 19, 222, 222, 19, 223, 281, 21, 22, 281, 223, 21, 19, 20, 223, 223, 20, 21, 221, 222, 290, 277, 18, 19, 51, 217, 50, 51, 216, 217, 220, 221, 290, 218, 220, 219, 220, 224, 221, 231, 174, 175, 232, 176, 177, 249, 232, 177, 233, 177, 228, 262, 261, 263, 18, 280, 17, 183, 184, 259, 56, 292, 55, 292, 320, 55, 292, 312, 320, 55, 320, 54, 312, 214, 320, 306, 213, 214, 211, 212, 293, 293, 212, 213, 53, 54, 215, 167, 168, 211, 54, 320, 215, 320, 214, 215, 53, 215, 52, 214, 240, 215, 214, 213, 239, 213, 257, 239, 214, 239, 240, 169, 252, 168, 211, 168, 251, 168, 252, 251, 211, 251, 212, 212, 238, 213, 213, 238, 257, 169, 170, 252, 170, 256, 252, 256, 255, 252, 252, 255, 251, 215, 216, 52, 51, 52, 216, 257, 248, 239, 239, 247, 240, 239, 248, 247, 251, 250, 212, 212, 250, 238, 170, 171, 256, 238, 246, 257, 257, 246, 248, 216, 218, 217, 215, 235, 216, 215, 240, 235, 255, 254, 251, 251, 254, 250, 255, 256, 229, 246, 238, 245, 246, 230, 248, 248, 230, 247, 247, 243, 240, 240, 243, 235, 256, 171, 229, 255, 229, 254, 171, 172, 229, 238, 250, 245, 246, 245, 230, 247, 230, 243, 254, 258, 250, 250, 258, 245, 216, 235, 218, 235, 236, 218, 235, 243, 236, 229, 253, 254, 254, 253, 258, 245, 244, 230, 230, 242, 243, 230, 241, 242, 230, 244, 241, 172, 173, 229, 229, 231, 253, 229, 173, 231, 243, 242, 236, 258, 249, 245, 245, 249, 244, 258, 253, 249, 173, 174, 231, 231, 175, 253, 236, 224, 218, 218, 224, 220, 241, 234, 242, 242, 237, 236, 242, 234, 237, 244, 233, 241, 244, 249, 233, 236, 237, 224, 241, 233, 234, 237, 234, 225, 234, 226, 225, 237, 225, 224, 233, 227, 234, 234, 227, 226, 225, 278, 224, 224, 278, 277, 224, 277, 221, 277, 278, 264, 263, 226, 227, 279, 226, 266, 279, 225, 226, 226, 263, 266, 225, 279, 278, 278, 279, 264, 263, 259, 266, 263, 261, 259, 277, 264, 18, 279, 280, 264, 18, 264, 280, 266, 265, 279, 280, 279, 265, 266, 259, 265, 261, 183, 259, 259, 184, 265, 263, 227, 262, 182, 183, 261, 262, 182, 261, 227, 228, 262, 233, 228, 227, 233, 249, 177, 253, 232, 249, 253, 175, 232, 175, 176, 232, 218, 219, 217, 219, 220, 289, 217, 219, 289, 167, 211, 299, 299, 211, 293, 291, 167, 299, 280, 265, 267, 17, 15, 16, 15, 17, 267, 17, 280, 267, 265, 268, 267, 265, 184, 268, 268, 276, 267, 267, 276, 15, 184, 185, 268, 185, 269, 268, 268, 269, 276, 276, 14, 15, 185, 186, 269, 186, 270, 269, 269, 270, 276, 270, 275, 276, 14, 275, 13, 14, 276, 275, 186, 187, 270, 10, 11, 9, 11, 12, 9, 187, 271, 270, 270, 271, 275, 12, 13, 195, 13, 275, 195, 275, 274, 195, 275, 271, 274, 12, 195, 9, 187, 188, 271, 195, 8, 9, 271, 272, 274, 188, 272, 271, 8, 195, 196, 272, 273, 274, 196, 195, 273, 195, 274, 273, 8, 196, 7, 188, 189, 272, 385, 273, 272, 381, 382, 383, 189, 384, 272, 273, 372, 196, 273, 386, 372, 273, 385, 386, 385, 272, 384, 383, 384, 189, 196, 399, 7, 7, 399, 6, 196, 372, 399, 399, 373, 374, 373, 399, 372, 381, 383, 371, 189, 371, 383, 189, 376, 371, 190, 370, 189, 189, 370, 376, 6, 399, 5, 372, 386, 373, 399, 374, 5, 386, 385, 373, 374, 373, 395, 373, 385, 395, 385, 384, 395, 374, 375, 5, 374, 395, 375, 375, 4, 5, 395, 398, 375, 375, 398, 4, 384, 394, 395, 384, 383, 394, 395, 394, 398, 394, 397, 398, 4, 398, 3, 398, 397, 3, 383, 393, 394, 383, 382, 393, 394, 396, 397, 394, 393, 396, 397, 396, 3, 381, 388, 382, 393, 382, 392, 190, 191, 370, 381, 371, 388, 370, 389, 376, 370, 379, 389, 370, 191, 379, 371, 389, 377, 377, 389, 390, 389, 371, 376, 371, 387, 388, 371, 377, 387, 382, 388, 392, 393, 380, 396, 393, 392, 380, 396, 2, 3, 388, 387, 392, 191, 192, 379, 396, 380, 2, 389, 379, 390, 379, 192, 390, 377, 378, 387, 392, 378, 391, 392, 387, 378, 377, 390, 378, 192, 193, 390, 378, 193, 194, 378, 390, 193, 392, 1, 380, 392, 391, 1, 380, 1, 2, 378, 0, 391, 378, 194, 0, 391, 0, 1], "vertices": [3, 9, 163.51, 5.03, 0.99579, 7, 490.85, 107.39, 0, 76, -198.1, 73.1, 0.00421, 2, 9, 158.03, -20.52, 0.9963, 76, -203.59, 47.55, 0.0037, 3, 9, 146.97, -46.03, 0.99609, 7, 495.7, 53.93, 0, 76, -214.65, 22.03, 0.00391, 2, 9, 124.32, -63.53, 0.99616, 76, -237.3, 4.53, 0.00384, 2, 9, 90.05, -75.98, 0.99648, 76, -271.57, -7.91, 0.00352, 3, 9, 62.19, -76.49, 0.9916, 8, 154.6, -77.67, 0.00533, 76, -299.42, -8.42, 0.00308, 3, 9, 34.13, -70.54, 0.93972, 8, 126.66, -71.18, 0.05777, 76, -327.49, -2.48, 0.00251, 3, 9, 15.09, -61.52, 0.83247, 8, 107.8, -61.8, 0.16553, 76, -346.52, 6.55, 0.002, 4, 9, -3.24, -47.06, 0.52296, 8, 89.74, -46.99, 0.47562, 7, 357.96, -6.02, 0, 77, -301.79, 21.01, 0.00142, 3, 9, -15.91, -47.13, 0.29311, 8, 77.08, -46.82, 0.70278, 77, -314.46, 20.93, 0.00411, 4, 9, -29.7, -52.99, 0.23382, 8, 63.18, -52.41, 0.75845, 7, 335.97, -21.87, 0, 77, -328.24, 15.08, 0.00773, 4, 9, -30.03, -45.32, 0.24482, 8, 62.99, -44.74, 0.74847, 7, 332.65, -14.95, 0, 77, -328.58, 22.75, 0.00671, 2, 9, -22.22, -38.33, 0.22861, 8, 70.93, -37.9, 0.77139, 3, 9, -27.57, -35.34, 0.12716, 8, 65.64, -34.8, 0.8726, 7, 330.99, -4.8, 0.00024, 3, 9, -39.97, -35.86, 0.03239, 8, 53.24, -35.09, 0.96165, 7, 319.8, -10.15, 0.00596, 2, 8, 41.28, -35.54, 0.95622, 7, 309.07, -15.47, 0.04378, 2, 8, 39.23, -45.31, 0.90724, 7, 311.22, -25.22, 0.09276, 2, 8, 13.57, -49.42, 0.57377, 7, 289.5, -39.5, 0.42623, 3, 8, -2.72, -58.87, 0.04638, 7, 278.52, -54.8, 0.78088, 25, -3.85, 50.84, 0.17274, 2, 7, 279.17, -73.59, 0.41806, 25, -3.2, 32.05, 0.58194, 2, 7, 291.71, -89.41, 0.07196, 25, 9.34, 16.23, 0.92804, 2, 17, -232.02, -56.35, 0, 25, 16.04, -2.35, 1, 3, 17, -242.47, -73.96, 0, 25, 13.35, -22.64, 0.83584, 36, -3.14, 37.71, 0.16416, 3, 17, -259.37, -89.45, 0, 25, 3.89, -43.53, 0.46258, 36, 19.75, 39.04, 0.53742, 3, 17, -285.08, -101.47, 0, 25, -15.03, -64.68, 0.11651, 36, 47.27, 32.11, 0.88349, 3, 17, -308.34, -109.43, 0, 25, -33.29, -81.14, 0.00528, 36, 70.32, 23.59, 0.99472, 2, 17, -338.28, -141.32, 0, 36, 113.68, 29.39, 1, 2, 17, -373.85, -174.85, 0, 36, 162.43, 32.9, 1, 2, 17, -381, -174.52, 0, 36, 167.77, 28.13, 1, 2, 17, -402.07, -193.85, 0, 36, 196.32, 29.8, 1, 3, 17, -427.96, -214.18, 0, 37, -29.37, -23.44, 0.00697, 36, 229.23, 29.2, 0.99303, 3, 17, -445.92, -226.08, 0, 37, -27.09, -2.02, 0.17816, 36, 250.67, 27.08, 0.82184, 3, 17, -459.57, -233.3, 0, 37, -23.96, 13.1, 0.44352, 36, 265.81, 24.05, 0.55648, 3, 17, -472.16, -236.22, 0, 37, -18.18, 24.67, 0.62457, 36, 277.41, 18.36, 0.37543, 3, 17, -483.05, -233.22, 0, 37, -8.93, 31.15, 0.76289, 36, 283.97, 9.15, 0.23711, 3, 17, -495.17, -219.83, 0, 37, 9.11, 31.95, 0.94393, 36, 284.9, -8.88, 0.05607, 2, 17, -525.46, -192.32, 0, 37, 49.62, 37.75, 1, 2, 17, -544.27, -171.88, 0, 37, 77.36, 39.22, 1, 2, 17, -554.7, -160.55, 0, 37, 92.74, 40.03, 1, 2, 17, -577.61, -130.12, 0, 37, 130.79, 38.28, 1, 2, 17, -544.98, -113.48, 0, 37, 122.81, 2.53, 1, 2, 17, -523.85, -90.83, 0, 37, 126.79, -28.18, 1, 2, 37, 83.69, -44, 0.91856, 36, 209.49, -84.01, 0.08144, 2, 37, 61.03, -47.74, 0.69281, 36, 205.58, -61.37, 0.30719, 3, 7, 121.43, -272.4, 0, 37, 49.33, -47.79, 0.47945, 36, 205.45, -49.67, 0.52054, 3, 7, 124.39, -257.58, 0.00027, 37, 53.48, -62.32, 0.19745, 36, 190.95, -53.93, 0.80228, 3, 7, 131.34, -239.39, 0.00334, 37, 55.64, -81.68, 0.05827, 36, 171.61, -56.23, 0.93839, 3, 7, 123.74, -230.48, 0.00615, 37, 66.48, -86.11, 0.02972, 36, 167.26, -67.1, 0.96413, 4, 7, 139.49, -189.67, 0.04948, 6, 159.21, -230.87, 1e-05, 37, 71.18, -129.6, 0.00021, 36, 123.8, -72.11, 0.9503, 3, 7, 149.62, -164.21, 0.26951, 6, 179.16, -212.09, 0.00084, 36, 96.55, -74.97, 0.72966, 3, 7, 156.84, -148.48, 0.50984, 6, 192.36, -200.88, 0.00359, 36, 79.26, -75.89, 0.48657, 3, 7, 132.8, -146.19, 0.77456, 6, 171.55, -188.64, 0.01555, 36, 88.4, -98.24, 0.20989, 2, 7, 91.03, -132.02, 0.93733, 6, 139.69, -158.14, 0.06267, 3, 7, 61.67, -117.52, 0.85153, 6, 119.22, -132.57, 0.13931, 4, -153.84, 53.61, 0.00916, 4, 17, -468.81, 45.48, 0, 7, 40.66, -107.37, 0.7072, 6, 104.48, -114.49, 0.24058, 4, -139.25, 35.4, 0.05222, 4, 17, -486.93, 54.21, 0, 7, 20.56, -106.45, 0.52581, 6, 86.66, -105.16, 0.35562, 4, -121.51, 25.92, 0.11857, 4, 17, -504.68, 55.12, 0, 7, 3.88, -112.59, 0.4187, 6, 68.95, -103.66, 0.40715, 4, -103.82, 24.27, 0.17415, 4, 17, -509.98, 62.03, 0, 7, -3.71, -108.32, 0.39316, 6, 63.87, -96.58, 0.42049, 4, -98.8, 17.14, 0.18635, 5, 17, -514.63, 83.1, 0, 7, -16.26, -90.77, 0.24256, 6, 59.93, -75.37, 0.49963, 4, -95.04, -4.1, 0.25763, 34, 89.74, 95.99, 0.00019, 6, 17, -527.96, 83.52, 0, 7, -28.68, -95.61, 0.11451, 6, 46.62, -74.5, 0.47318, 3, -66.04, 74.62, 0.00026, 4, -81.74, -5.08, 0.40837, 34, 82.37, 84.88, 0.00368, 6, 17, -541.72, 75.4, 0, 7, -38.16, -108.49, 0.05466, 6, 32.59, -82.17, 0.26148, 3, -51.94, 82.16, 0.0027, 4, -67.65, 2.46, 0.65139, 34, 82.03, 68.89, 0.02977, 7, 17, -565.34, 54.11, 0, 7, -51.51, -137.35, 0.00655, 3, -27.45, 102.45, 0.00019, 4, -43.16, 22.74, 0.56264, 34, 87.71, 37.6, 0.3589, 35, -31.55, 20.72, 0.07157, 36, 166.2, -265.56, 0.00015, 6, 17, -562.2, 41.11, 0, 7, -43.52, -148.07, 0.00221, 4, -45.76, 35.86, 0.35042, 34, 100.41, 33.44, 0.41261, 35, -20.42, 28.12, 0.23473, 36, 171.98, -253.5, 3e-05, 6, 17, -566.47, 31.12, 0, 7, -43.51, -158.94, 0.00067, 4, -41.07, 45.67, 0.22632, 34, 106.67, 24.55, 0.37221, 35, -9.56, 27.54, 0.39984, 37, 247.92, -73.07, 0.00097, 6, 17, -575.97, 17.21, 0, 7, -46.79, -175.46, 2e-05, 4, -31, 59.17, 0.05926, 34, 113.5, 9.15, 0.14032, 35, 6.76, 23.38, 0.79011, 37, 243.26, -56.89, 0.0103, 4, 17, -584.47, -1.22, 0, 4, -21.73, 77.23, 0.00036, 35, 26.99, 21.72, 0.88144, 37, 234.48, -38.59, 0.1182, 3, 17, -586.55, -11.73, 0, 35, 37.57, 23.36, 0.72088, 37, 227.71, -30.29, 0.27912, 3, 17, -587.67, -20.33, 0, 35, 46.03, 25.26, 0.48479, 37, 221.79, -23.95, 0.51521, 3, 17, -583.67, -28.56, 0, 35, 52.39, 31.84, 0.24701, 37, 212.9, -21.78, 0.75299, 3, 17, -578.15, -35.84, 0, 35, 57.35, 39.52, 0.1148, 37, 203.77, -21.39, 0.8852, 2, 35, 65.29, 57.81, 0.01823, 37, 183.98, -23.77, 0.98177, 2, 35, 73.24, 76.09, 0.00046, 37, 164.18, -26.16, 0.99954, 1, 37, 161.12, -20.8, 1, 3, 17, -592.48, -104.73, 0, 35, 126.97, 49.68, 2e-05, 37, 159.83, 33.56, 0.99998, 3, 17, -602.68, -110.69, 0, 35, 136.07, 42.15, 2e-05, 37, 161.73, 45.22, 0.99998, 1, 37, 189.25, 39.24, 1, 3, 17, -628.56, -60.66, 0, 35, 97.94, 0.68, 0.01733, 37, 216.78, 33.27, 0.98267, 3, 34, 136.45, -75.4, 0.00087, 35, 87.4, -10.86, 0.14905, 37, 232.07, 30, 0.85009, 3, 34, 120.41, -74.26, 0.0192, 35, 76.58, -22.77, 0.44566, 37, 247.8, 26.69, 0.53514, 3, 34, 106.41, -71.74, 0.06837, 35, 65.94, -32.21, 0.68552, 37, 261.33, 22.27, 0.24611, 2, 34, 95.09, -64.92, 0.17567, 35, 53.59, -36.89, 0.82433, 3, 34, 99.04, -94.3, 0.07446, 35, 79.11, -51.95, 0.61636, 27, -28.77, 59.49, 0.30917, 3, 17, -697.82, -40.63, 0, 35, 102.86, -71.26, 0.41906, 27, -3.84, 77.25, 0.58094, 3, 17, -728.32, -51.66, 0, 35, 123.68, -96.12, 0.20722, 27, 25.66, 90.7, 0.79278, 3, 17, -770.08, -56.45, 0, 35, 142.51, -133.71, 0.0081, 27, 66.9, 98.86, 0.9919, 3, 17, -803.16, -55.98, 0, 35, 153.4, -164.94, 0.00079, 27, 99.91, 101.06, 0.99921, 2, 17, -835.88, -61.05, 0, 27, 132.11, 108.75, 1, 2, 17, -842.21, -49.86, 0, 27, 139.33, 98.12, 1, 2, 17, -923.95, -44.46, 0, 27, 221.24, 99.33, 1, 2, 17, -1005.97, -32.32, 0, 27, 303.97, 93.85, 1, 3, 17, -1074.06, -15.56, 0, 27, 373.19, 82.65, 0.99412, 28, -91.1, 85.04, 0.00588, 3, 17, -1100.17, -9.14, 0, 27, 399.73, 78.36, 0.95196, 28, -64.67, 80.06, 0.04804, 3, 17, -1118.3, -7.26, 0, 27, 417.96, 77.95, 0.87296, 28, -46.47, 79.18, 0.12704, 3, 17, -1140.16, -7.51, 0, 27, 439.72, 79.96, 0.70302, 28, -24.66, 80.63, 0.29698, 3, 17, -1158.37, -8.84, 0, 27, 457.77, 82.76, 0.51797, 28, -6.54, 82.96, 0.48203, 3, 17, -1178.44, -10.18, 0, 27, 477.66, 85.72, 0.31386, 28, 13.42, 85.4, 0.68614, 3, 17, -1206.06, -17.98, 0, 27, 504.56, 95.72, 0.11251, 28, 40.57, 94.7, 0.88749, 2, 27, 546.61, 114.05, 0.00963, 28, 83.08, 111.93, 0.99037, 1, 28, 122.4, 118.54, 1, 1, 28, 166.43, 116.21, 1, 1, 28, 233.52, 100.88, 1, 1, 28, 282.75, 81.45, 1, 1, 28, 323.33, 79.04, 1, 2, 17, -1575.5, 48.5, 0, 28, 413.12, 48.61, 1, 3, 17, -1597.8, 55.13, 0, 28, 435.74, 43.21, 0.99263, 29, -55.16, 5.03, 0.00737, 3, 17, -1619.35, 58.27, 0, 28, 457.44, 41.26, 0.87563, 29, -38.7, 19.3, 0.12437, 3, 17, -1634.15, 59.05, 0, 28, 472.26, 41.29, 0.64253, 29, -28.44, 29.99, 0.35747, 3, 17, -1649.01, 60.75, 0, 28, 487.19, 40.42, 0.36264, 29, -17.45, 40.14, 0.63736, 3, 17, -1677.28, 52.65, 0, 28, 514.98, 50.05, 0.08527, 29, -5.1, 66.83, 0.91473, 3, 17, -1702.33, 48.58, 0, 28, 539.77, 55.49, 0.01483, 29, 8.18, 88.45, 0.98517, 2, 17, -1764.11, 51.4, 0, 29, 50.67, 133.38, 1, 2, 17, -1772.73, 93.16, 0, 29, 87.92, 112.62, 1, 2, 17, -1758.26, 122.77, 0, 29, 100.88, 82.32, 1, 2, 17, -1777.35, 154.68, 0, 29, 137.51, 75.93, 1, 2, 17, -1789.66, 206.04, 0, 29, 184.44, 51.69, 1, 1, 29, 238.72, 1.87, 1, 1, 29, 255.8, -22.09, 1, 1, 29, 261.96, -43.84, 1, 1, 29, 228.14, -68.2, 1, 1, 29, 207.98, -68.89, 1, 1, 29, 171.51, -42.76, 1, 1, 29, 148.8, -36.45, 1, 1, 29, 123.14, -35.2, 1, 2, 28, 513.1, -96.71, 0.0003, 29, 99.26, -36.36, 0.9997, 2, 28, 494.85, -77.7, 0.02293, 29, 72.91, -36.31, 0.97707, 2, 28, 476.74, -63.45, 0.12777, 29, 50.09, -39.46, 0.87223, 2, 28, 460.35, -54.13, 0.34395, 29, 32, -44.8, 0.65605, 2, 28, 442.47, -49.05, 0.63479, 29, 15.94, -54.15, 0.36521, 3, 17, -1576.97, 140.5, 0, 28, 419.64, -43.17, 0.89988, 29, -4.14, -66.51, 0.10012, 3, 17, -1553.31, 135.07, 0, 28, 395.71, -39.05, 0.98892, 29, -23.71, -80.87, 0.01108, 2, 17, -1489.19, 163.68, 0, 28, 333.26, -71.14, 1, 2, 17, -1474.26, 122.35, 0, 28, 316.08, -30.69, 1, 2, 17, -1465.84, 126.71, 0, 28, 307.92, -35.5, 1, 2, 17, -1454.81, 119.04, 0, 28, 296.48, -28.45, 1, 2, 17, -1447.65, 100.32, 0, 28, 288.3, -10.15, 1, 2, 17, -1428.08, 96.87, 0, 28, 268.57, -7.78, 1, 2, 17, -1377.79, 92.13, 0, 28, 218.09, -5.81, 1, 2, 17, -1332, 90.17, 0, 28, 172.26, -6.37, 1, 2, 17, -1267.03, 88.88, 0, 28, 107.32, -8.65, 1, 1, 28, 65.25, -10.12, 1, 1, 28, 50.66, -13.28, 1, 2, 27, 494.62, -23.05, 0.03648, 28, 27.56, -23.77, 0.96352, 2, 27, 476.31, -26.96, 0.32415, 28, 9.15, -27.2, 0.67585, 2, 27, 456.16, -35.54, 0.77921, 28, -11.22, -35.26, 0.22079, 2, 27, 437.37, -40.48, 0.96886, 28, -30.13, -39.71, 0.03114, 1, 27, 406.97, -42.11, 1, 1, 27, 377.78, -50.95, 1, 2, 3, 390.67, 4.15, 0.00575, 27, 298.77, -74.3, 0.99425, 2, 3, 316.11, -2.6, 0.09093, 27, 225.59, -90.12, 0.90907, 2, 3, 250.76, -4.08, 0.31367, 27, 160.92, -99.57, 0.68633, 2, 3, 245.73, -7.54, 0.34998, 27, 156.35, -103.62, 0.65002, 3, 3, 208.03, -2.76, 0.54636, 27, 118.35, -103.5, 0.44095, 30, 52.17, 71.24, 0.01269, 3, 3, 205.09, -11.79, 0.59257, 27, 116.53, -112.82, 0.3649, 30, 50.47, 61.9, 0.04253, 3, 3, 178.96, -50.51, 0.3718, 30, 29.82, 20, 0.6032, 80, -458.85, -81.8, 0.025, 4, 17, -760.81, 233.33, 0, 5, 114.88, -18.49, 0.02792, 30, 16.03, -16.47, 0.94708, 80, -494.25, -65.46, 0.025, 3, 17, -752.3, 274.8, 0, 5, 104.65, -59.57, 0.11188, 30, 11.46, -58.56, 0.88812, 3, 17, -736.57, 271.42, 0, 5, 89.07, -55.53, 0.14773, 30, -4.52, -56.67, 0.85227, 3, 17, -708.3, 264.09, 0, 5, 61.13, -47.03, 0.35911, 30, -33.35, -52.02, 0.64089, 3, 17, -681.58, 261.84, 0, 5, 34.53, -43.66, 0.6603, 30, -60.17, -52.29, 0.3397, 4, 17, -658.7, 265.96, 0, 6, -78.03, 112.16, 0.00798, 5, 11.5, -46.83, 0.85624, 30, -82.56, -58.55, 0.13578, 3, 6, -42.19, 113.5, 0.06768, 5, -24.35, -47.87, 0.91867, 30, -117.93, -64.43, 0.01365, 4, 6, -34.46, 100.23, 0.13484, 3, 13.52, -100.82, 0.00805, 5, -31.96, -34.53, 0.85273, 30, -127.28, -52.24, 0.00437, 4, 26, -330.43, -67.59, 7e-05, 6, -6.34, 94.64, 0.37968, 3, -14.55, -94.97, 0.03465, 5, -60.03, -28.68, 0.58561, 5, 7, -121.93, 38.66, 0.00065, 26, -304.14, -64.17, 0.00159, 6, 18.93, 86.61, 0.62738, 3, -39.75, -86.72, 0.02584, 5, -85.23, -20.44, 0.34455, 4, 7, -93.37, 50.25, 0.02755, 6, 49.71, 85.04, 0.82061, 3, -70.52, -84.88, 0.00226, 5, -116, -18.6, 0.14958, 3, 7, -65.01, 69.05, 0.18953, 6, 83.36, 90.07, 0.77317, 5, -149.69, -23.34, 0.03729, 3, 7, -62.27, 92.13, 0.3428, 6, 95.61, 109.82, 0.6273, 5, -162.11, -42.98, 0.02989, 3, 7, -30.47, 96.57, 0.61883, 6, 126.3, 100.39, 0.26117, 56, -95.07, 11.6, 0.12, 3, 7, -18.63, 104.57, 0.74254, 6, 140.41, 102.63, 0.05746, 56, -81.58, 16.3, 0.2, 5, 7, -22.09, 124.23, 0.75646, 6, 145.59, 121.92, 0.04119, 57, -68.81, 3.16, 0.09975, 56, -79.89, 36.2, 0.08978, 78, -117.75, 603.9, 0.01281, 4, 7, -17.37, 149.94, 0.77583, 6, 160.74, 143.21, 0.00852, 57, -57.66, 26.8, 0.19934, 78, -102.6, 625.2, 0.0163, 3, 7, -3.13, 170.33, 0.77849, 58, -29.31, 18.1, 0.19909, 78, -81.07, 637.66, 0.02242, 3, 7, 21.94, 185.62, 0.77449, 58, -1.15, 26.45, 0.19891, 78, -51.88, 640.9, 0.0266, 3, 7, 47.78, 190.07, 0.77451, 58, 24.96, 24.13, 0.19891, 78, -26.59, 634, 0.02657, 4, 7, 73.96, 182.65, 0.78456, 58, 48.37, 10.24, 0.09, 57, 39.01, 35, 0.1, 78, -6, 616.2, 0.02545, 4, 7, 99.73, 164.76, 0.71903, 26, -82.48, 61.93, 0.06092, 57, 59.33, 11.11, 0.19915, 78, 9.78, 589.09, 0.02091, 5, 7, 121.95, 140.55, 0.60634, 26, -60.25, 37.72, 0.18477, 57, 74.6, -17.99, 0.08966, 56, 63.53, 15.05, 0.09962, 78, 19.67, 557.75, 0.01962, 4, 7, 144.36, 109.62, 0.43808, 26, -37.85, 6.79, 0.34714, 56, 77.26, -20.6, 0.19938, 78, 26.89, 520.25, 0.0154, 4, 7, 159.03, 112.17, 0.24423, 26, -23.18, 9.34, 0.66644, 56, 92.09, -21.89, 0.08002, 78, 41.26, 516.35, 0.00932, 2, 26, -8.25, 10.25, 0.9972, 78, 55.18, 510.86, 0.0028, 1, 26, 7.08, 6.8, 1, 2, 7, 197.96, 100.17, 0.10585, 26, 15.75, -2.66, 0.89415, 3, 17, -241.31, 160.93, 0, 7, 204.51, 88.17, 0.31163, 26, 22.3, -14.66, 0.68837, 5, 17, -212.82, 143.3, 0, 8, 16.61, 83.72, 0.10819, 7, 237.63, 83.16, 0.56182, 26, 55.43, -19.67, 0.31056, 78, 100.22, 456.81, 0.01943, 2, 8, 29.26, 41.24, 0.72207, 7, 266.61, 49.62, 0.27793, 3, 9, -49.06, 40.54, 0.00963, 8, 45.61, 41.47, 0.9291, 7, 281.42, 56.53, 0.06127, 3, 9, -30.99, 41.58, 0.10063, 8, 63.7, 42.16, 0.88957, 7, 297.63, 64.59, 0.0098, 3, 9, -13.85, 43.75, 0.33526, 8, 80.88, 44.01, 0.66454, 7, 312.54, 73.32, 0.00021, 2, 9, 5.1, 47.03, 0.67328, 8, 99.88, 46.92, 0.32672, 3, 9, 33.63, 52.21, 0.95601, 8, 128.51, 51.56, 0.04399, 7, 352.88, 99.76, 0, 3, 9, 118.44, 74.12, 0.9804, 7, 422.26, 153.22, 0, 76, -243.18, 142.19, 0.0196, 3, 9, 132.59, 66.15, 0.98501, 7, 438.41, 151.45, 0, 76, -229.02, 134.22, 0.01499, 3, 9, 147.63, 54.96, 0.98923, 7, 456.63, 147.06, 0, 76, -213.99, 123.03, 0.01077, 3, 9, 157.5, 43.72, 0.99343, 7, 470.12, 140.61, 0, 76, -204.12, 111.79, 0.00657, 3, 9, 162.52, 26.75, 0.99472, 7, 481.4, 126.97, 0, 76, -199.09, 94.82, 0.00528, 2, 9, -7.97, -35.53, 0.38856, 8, 85.24, -35.37, 0.61144, 2, 9, 15.7, -39.04, 0.77975, 8, 108.84, -39.34, 0.22025, 2, 37, 28.26, -42.65, 0.29213, 36, 210.44, -28.57, 0.70787, 2, 37, 8.59, -33.48, 0.09298, 36, 219.47, -8.83, 0.90702, 2, 17, -446.76, -204.32, 0, 36, 237.57, 9.68, 1, 3, 17, -468.79, -205.51, 0, 37, 3.34, 2.49, 0.67617, 36, 255.39, -3.32, 0.32383, 3, 7, 135.71, -229.65, 0.00648, 37, 56.22, -92.34, 0.02889, 36, 160.95, -56.89, 0.96462, 2, 17, -376.78, -169.91, 0, 36, 161.59, 27.22, 1, 4, 7, 142.39, -267.95, 3e-05, 37, 32.73, -61.36, 0.15823, 36, 191.76, -33.18, 0.84025, 78, -134.61, 178.96, 0.0015, 3, 17, -414.18, -179.2, 0, 36, 196.44, 10.79, 0.99837, 78, -111.69, 141.15, 0.00163, 2, 17, -429.51, -192.76, 0, 36, 216.9, 11.62, 1, 2, 17, -486.92, -196.69, 0, 37, 21.69, 10.84, 1, 2, 37, 11.88, -21.82, 0.27151, 36, 231.14, -12.04, 0.72849, 2, 37, 30.14, -26.3, 0.55404, 36, 226.8, -30.34, 0.44596, 2, 37, 78.54, -19.14, 0.96094, 36, 234.31, -78.68, 0.03906, 2, 17, -524.69, -157.78, 0, 37, 75.74, 15.14, 1, 4, 7, -7.71, 84.21, 0.6908, 6, 141.7, 79.57, 0.08776, 56, -76.24, -6.17, 0.19909, 78, -121.65, 561.55, 0.02235, 5, 7, 17.59, 61.29, 0.76925, 6, 154.93, 48.09, 0.00251, 55, -78.11, 87.22, 0.09908, 56, -57.66, -34.81, 0.08917, 78, -108.42, 530.08, 0.04, 4, 7, 21.3, 13.6, 0.74933, 6, 138.11, 3.31, 0.01232, 55, -86.75, 40.17, 0.19835, 78, -125.23, 485.3, 0.04, 5, 17, -445.24, 109.81, 0, 7, 37.05, -38.95, 0.63978, 6, 130.15, -50.97, 0.12187, 55, -85, -14.67, 0.19835, 78, -133.19, 431.02, 0.04, 4, 7, 77.36, -80.86, 0.69662, 6, 148.94, -106, 0.07055, 55, -56.78, -65.51, 0.19859, 78, -114.4, 375.99, 0.03424, 5, 7, 123.77, -101.73, 0.7007, 25, -158.6, 3.91, 0.05399, 6, 182.17, -144.54, 0.01748, 55, -17.27, -97.58, 0.19881, 78, -81.17, 337.45, 0.02903, 5, 7, 169.29, -126.55, 0.67814, 25, -113.08, -20.91, 0.00854, 6, 212.92, -186.28, 0.0026, 36, 54.06, -75.05, 0.29941, 78, -50.42, 295.71, 0.01132, 5, 7, 168.94, -95.48, 0.63048, 25, -113.42, 10.16, 0.13899, 6, 225.75, -157.99, 0.00123, 55, 28, -103.12, 0.19875, 78, -37.6, 324, 0.03056, 5, 7, 178.9, -107.27, 0.61635, 25, -103.46, -1.63, 0.16953, 6, 229.79, -172.88, 0.00069, 36, 32.52, -75.49, 0.19661, 78, -33.56, 309.11, 0.01682, 4, 7, 208.89, -91.01, 0.51579, 25, -73.48, 14.63, 0.26522, 36, 4.19, -56.49, 0.19516, 78, 0.49, 311.16, 0.02384, 4, 7, 238.39, -83.81, 0.41077, 25, -43.97, 21.83, 0.44752, 36, -15.89, -33.71, 0.11861, 78, 30.27, 305.2, 0.02309, 4, 7, 264.38, -85.82, 0.23431, 25, -17.99, 19.82, 0.57591, 36, -26.18, -9.77, 0.1727, 78, 52.96, 292.39, 0.01708, 3, 7, 283.81, -94.49, 0.04134, 25, 1.44, 11.15, 0.95078, 78, 66.91, 276.31, 0.00788, 4, 7, 201.42, -55.86, 0.55437, 25, -80.94, 49.78, 0.20728, 55, 69.55, -73.14, 0.19835, 78, 8.6, 346.16, 0.04, 4, 7, 204.29, -9.11, 0.62303, 25, -78.08, 96.53, 0.13862, 55, 84.3, -28.69, 0.19835, 78, 30.97, 387.32, 0.04, 5, 17, -284.1, 117.44, 0, 7, 182.24, 31.37, 0.73961, 25, -100.13, 137.01, 0.02204, 55, 73.37, 16.09, 0.19835, 78, 28.12, 433.32, 0.04, 6, 17, -295.57, 155.8, 0, 7, 156.63, 62.14, 0.73244, 26, -25.58, -40.69, 0.03905, 55, 56.5, 52.4, 0.09921, 56, 76.95, -69.63, 0.08929, 78, 17.93, 472.04, 0.04, 4, 7, 159.19, 86.63, 0.3959, 26, -23.02, -16.2, 0.3743, 56, 85.7, -46.62, 0.19873, 78, 30.6, 493.14, 0.03108, 3, 7, 30.27, 166.86, 0.67895, 58, 2.09, 6.18, 0.24496, 78, -52.27, 620.38, 0.0761, 3, 7, 88.59, 17.62, 0.67532, 60, -3.84, 4.62, 0.24468, 78, -62.56, 460.48, 0.08, 3, 7, 67.17, 172.34, 0.74469, 58, 39.16, 2.02, 0.19758, 78, -16.52, 609.73, 0.05773, 4, 7, 115.36, 129.71, 0.66635, 26, -66.84, 26.89, 0.0932, 57, 65.46, -26.77, 0.19825, 78, 9.12, 550.72, 0.04221, 3, 7, 138.01, 69.91, 0.7473, 59, 49.18, 53.44, 0.1977, 78, 4.34, 486.95, 0.055, 5, 17, -301.79, 120.81, 0, 7, 164.65, 27.52, 0.72374, 25, -117.72, 133.16, 0.02356, 59, 64.07, 5.63, 0.1977, 78, 10.55, 437.27, 0.055, 5, 7, 122.74, -78.06, 0.70877, 25, -159.63, 27.58, 0.02602, 6, 191.25, -122.66, 0.01251, 59, -3.5, -85.68, 0.1977, 78, -72.09, 359.33, 0.055, 5, 7, 159.02, -64.52, 0.67083, 25, -123.35, 41.12, 0.07563, 6, 229.86, -125.73, 0.00085, 59, 35.04, -81.88, 0.1977, 78, -33.49, 356.26, 0.055, 4, 7, 176.43, -26.87, 0.63396, 25, -105.93, 78.77, 0.11334, 59, 61.52, -49.96, 0.1977, 78, -1.78, 383.01, 0.055, 3, 7, 35.47, 51.68, 0.7473, 59, -54.6, 62.1, 0.1977, 78, -96.28, 513.81, 0.055, 5, 17, -427.78, 116.82, 0, 7, 50.36, -25.64, 0.71124, 6, 147.84, -44.54, 0.03606, 59, -60.03, -16.45, 0.1977, 78, -115.5, 437.45, 0.055, 4, 7, 84.9, -65.49, 0.70423, 6, 162.29, -95.25, 0.04307, 59, -36.85, -63.82, 0.1977, 78, -101.06, 386.73, 0.055, 4, 17, -329.71, 136.24, 0, 7, 132.91, 30.74, 0.73297, 60, 42.36, 5.94, 0.19703, 78, -16.85, 453.62, 0.07, 3, 7, 136.03, -12.36, 0.73297, 60, 34.33, -36.52, 0.19703, 78, -32.26, 413.25, 0.07, 4, 7, 110.6, -33.11, 0.7291, 6, 199.27, -76.79, 0.00386, 60, 4.43, -50.06, 0.19703, 78, -64.08, 405.2, 0.07, 3, 7, 106.74, 60.93, 0.73297, 60, 24.8, 41.82, 0.19703, 78, -27.79, 492.04, 0.07, 3, 7, 73.45, 61.22, 0.73297, 60, -7.31, 50.64, 0.19703, 78, -57.84, 506.39, 0.07, 3, 7, 51.91, 37.01, 0.73297, 60, -34.33, 32.76, 0.19703, 78, -87.59, 493.56, 0.07, 5, 17, -402.58, 97.35, 0, 7, 81.18, -33.65, 0.71663, 6, 172.39, -64.83, 0.01633, 60, -24.14, -43.04, 0.19703, 78, -90.96, 417.16, 0.07, 5, 17, -410.65, 135.51, 0, 7, 58.76, -1.72, 0.73234, 6, 165.58, -26.42, 0.00063, 60, -37.63, -6.44, 0.19703, 78, -97.77, 455.57, 0.07, 4, 7, 97.79, 98.62, 0.75708, 59, 17.67, 91.5, 0.09891, 57, 40.5, -52.32, 0.08902, 78, -19.96, 529.98, 0.055, 4, 7, 41.75, 85.6, 0.75708, 59, -39.83, 93.28, 0.09891, 57, -17.01, -50.54, 0.08902, 78, -76.24, 541.89, 0.055, 3, 7, 12.56, 93.84, 0.7569, 57, -43.11, -35.1, 0.19813, 78, -99.21, 561.7, 0.04497, 4, 7, -9.89, 115.42, 0.74707, 6, 152.93, 108.77, 0.0251, 57, -59.27, -8.48, 0.19881, 78, -110.42, 590.76, 0.02902, 3, 7, 66.32, 145.9, 0.73622, 58, 31.56, -23.31, 0.19719, 78, -28.47, 586.14, 0.06659, 3, 7, 40.81, 127.48, 0.73297, 58, 2.18, -34.58, 0.19703, 78, -59.38, 580.24, 0.07, 3, 7, 12.37, 127.23, 0.74571, 58, -25.37, -27.54, 0.19763, 78, -85.25, 592.04, 0.05667, 3, 7, -4.63, 145.57, 0.75286, 58, -37.11, -5.45, 0.19795, 78, -92.91, 615.85, 0.04918, 4, 7, 36.95, 16.24, 0.74586, 6, 153.41, -0.92, 0.00144, 59, -62.25, 27.46, 0.1977, 78, -109.94, 481.07, 0.055, 6, 7, 66.93, 96.37, 0.77838, 60, -4.59, 86.29, 0.04952, 59, -12.73, 97.24, 0.03575, 58, 19.46, -71.35, 0.04704, 57, 10.1, -46.58, 0.03432, 78, -48.87, 541, 0.055, 3, 17, -228.19, 99.17, 0, 8, 0.39, 39.89, 0.18246, 7, 240.83, 36.54, 0.81754, 4, 17, -265.81, 178.33, 0, 7, 175.14, 94.55, 0.20284, 26, -7.07, -8.28, 0.76853, 78, 48.41, 493.58, 0.02863, 4, 17, -238.97, 134.14, 0, 7, 217.19, 64.46, 0.65405, 26, 34.98, -38.36, 0.32169, 78, 73.78, 448.52, 0.02425, 4, 17, -263.02, 146.28, 0, 7, 190.29, 66.18, 0.58713, 26, 8.09, -36.65, 0.38094, 78, 50.14, 461.45, 0.03192, 4, 17, -261.98, 123.5, 0, 7, 200.2, 45.63, 0.91754, 26, 18, -57.2, 0.05077, 78, 50.43, 438.64, 0.03169, 3, 7, 246.68, -34.93, 0.80513, 25, -35.68, 70.71, 0.16781, 78, 58.46, 345.98, 0.02706, 4, 17, -215.61, 89.49, 0, 8, 12.79, 29.97, 0.5225, 7, 256.21, 32.58, 0.45733, 78, 95.65, 403.12, 0.02017, 3, 17, -248.05, 91.05, 0, 7, 225.76, 21.26, 0.97342, 78, 63.27, 405.75, 0.02658, 2, 8, 24.24, -11.18, 0.97937, 78, 106.52, 361.82, 0.02063, 3, 8, 28.85, 21.93, 0.83492, 7, 274.15, 31.84, 0.14442, 78, 111.6, 394.86, 0.02067, 4, 9, -46.99, 21.05, 0.00451, 8, 47.31, 21.95, 0.94212, 7, 290.98, 39.43, 0.03277, 78, 130.05, 394.62, 0.0206, 4, 9, -27.72, 21.81, 0.06281, 8, 66.58, 22.34, 0.91216, 7, 308.4, 47.69, 0.0039, 78, 149.33, 394.74, 0.02113, 3, 9, -10.44, 23.41, 0.31312, 8, 83.89, 23.61, 0.66539, 78, 166.66, 395.77, 0.02149, 3, 9, 10.45, 25.33, 0.75871, 8, 104.82, 25.13, 0.21949, 78, 187.6, 397, 0.02179, 3, 9, 14.01, -11.24, 0.9176, 8, 107.68, -11.5, 0.06222, 78, 189.96, 360.33, 0.02018, 3, 9, -6.85, -11.56, 0.23314, 8, 86.82, -11.42, 0.74606, 78, 169.1, 360.7, 0.0208, 3, 9, -26.07, -10.71, 0.02481, 8, 67.61, -10.21, 0.95425, 78, 149.91, 362.19, 0.02094, 3, 9, -44.94, -11.47, 0.00111, 8, 48.74, -10.61, 0.97838, 78, 131.03, 362.05, 0.02051, 3, 7, 243.67, -56.21, 0.55383, 25, -38.69, 49.42, 0.41891, 78, 46.73, 327.97, 0.02726, 4, 7, 209.91, -21.73, 0.62396, 25, -72.46, 83.91, 0.16046, 55, 86.5, -42.33, 0.17877, 78, 30.72, 373.5, 0.0368, 4, 17, -249.89, 71.7, 0, 7, 231.67, 2.75, 0.90026, 25, -50.7, 108.39, 0.07269, 78, 60.79, 386.48, 0.02705, 3, 8, -0.82, -19.33, 0.29674, 7, 264.03, -17.96, 0.68286, 78, 81.36, 354.02, 0.02041, 4, 17, -253.76, -54.33, 0, 25, -4.74, -9.03, 0.72448, 36, -6.79, 15.37, 0.26793, 78, 52.77, 260.64, 0.0076, 4, 17, -272.85, -67.97, 0, 25, -16.94, -29.07, 0.17855, 36, 16.62, 13.87, 0.81212, 78, 33.24, 247.64, 0.00933, 4, 17, -297.79, -76.35, 0, 25, -36.58, -46.57, 0.01191, 36, 41.24, 4.61, 0.97894, 78, 8.03, 240.09, 0.00915, 3, 7, 229.5, -170.35, 0.00128, 36, 64.87, -1.38, 0.98901, 78, -14.39, 230.55, 0.00971, 3, 17, -386.46, -150.2, 0, 36, 156.64, 5.83, 0.99266, 78, -83.03, 169.21, 0.00734, 4, 7, 158.46, -236.32, 0.00347, 37, 32.94, -96.83, 0.01578, 36, 156.29, -33.65, 0.97244, 78, -106.67, 200.83, 0.00831, 3, 7, 195.5, -151.68, 0.13465, 36, 64.14, -40.16, 0.84666, 78, -37.31, 261.84, 0.01869, 3, 37, 8.1, -58.86, 0.02705, 36, 194.08, -8.53, 0.96979, 78, -121.52, 157.95, 0.00316, 3, 7, 212.25, -125.63, 0.33325, 36, 33.29, -37.43, 0.64455, 78, -11.11, 278.36, 0.0222, 4, 7, 235.47, -105.55, 0.37368, 25, -46.9, 0.09, 0.03541, 36, 4.72, -26.21, 0.56864, 78, 18.42, 286.74, 0.02227, 4, 7, -48.02, 79.53, 0.39963, 6, 103.19, 92.38, 0.58416, 5, -169.54, -25.47, 0.01189, 78, -160.16, 574.36, 0.00432, 4, 17, -493.72, 75.88, 0, 7, 5.81, -89.19, 0.42829, 6, 80.59, -83.27, 0.42977, 4, -115.63, 3.98, 0.14194, 3, 7, -21.52, 35.4, 0.31739, 6, 108.53, 41.18, 0.64709, 78, -154.81, 523.17, 0.03553, 4, 7, -44.11, 24.4, 0.0763, 6, 83.42, 40.77, 0.88557, 78, -179.93, 522.76, 0.03287, 80, -467.05, 201.06, 0.00526, 4, 7, -69.48, 11.8, 0.00329, 6, 55.09, 40.09, 0.95641, 78, -208.25, 522.07, 0.0275, 80, -464.78, 172.82, 0.01281, 5, 6, 22.67, 39.63, 0.88829, 3, -43.09, -39.72, 0.04321, 5, -88.56, 26.57, 0.02767, 78, -240.67, 521.62, 0.01936, 80, -462.5, 140.48, 0.02148, 5, 6, -10.55, 39.34, 0.46939, 3, -9.87, -39.71, 0.31759, 5, -55.35, 26.58, 0.1715, 78, -273.89, 521.32, 0.01116, 80, -460.34, 107.33, 0.03036, 5, 6, -50.79, 36.99, 0.02158, 3, 30.4, -37.72, 0.58282, 5, -15.08, 28.57, 0.3541, 78, -314.14, 518.98, 0.00151, 80, -455.73, 67.28, 0.04, 3, 7, -35.4, 63.88, 0.41419, 6, 108, 72.86, 0.56182, 78, -155.34, 554.85, 0.02399, 5, 7, -56.99, 52.07, 0.14503, 6, 83.44, 71.29, 0.81064, 5, -149.61, -4.55, 0.01742, 78, -179.9, 553.28, 0.02125, 80, -497.52, 199.37, 0.00566, 6, 7, -84.13, 37.7, 0.02682, 6, 52.77, 69.76, 0.85034, 3, -73.45, -69.58, 0.00167, 5, -118.93, -3.29, 0.09093, 78, -210.57, 551.74, 0.01653, 80, -494.27, 168.84, 0.01372, 6, 7, -112.03, 24.23, 0.00019, 6, 21.8, 69.35, 0.69146, 3, -42.47, -69.44, 0.02832, 5, -87.95, -3.15, 0.24731, 78, -241.54, 551.33, 0.01077, 80, -492.12, 137.94, 0.02196, 5, 6, -17.05, 73.11, 0.33121, 3, -3.66, -73.54, 0.07748, 5, -49.14, -7.25, 0.55522, 78, -280.39, 555.09, 0.00372, 80, -493.69, 98.94, 0.03238, 5, 6, -52.55, 68.31, 0.07803, 3, 31.89, -69.05, 0.05322, 5, -13.59, -2.76, 0.82792, 78, -315.9, 550.3, 0.00083, 80, -486.9, 63.76, 0.04, 4, 6, -44.25, 100.05, 0.08636, 3, 23.31, -100.71, 0.00217, 5, -22.17, -34.42, 0.90027, 30, -117.6, -50.82, 0.0112, 4, 17, -473.5, 157.42, 0, 7, -7.64, -6.27, 0.12398, 6, 103.48, -2.45, 0.83725, 78, -159.86, 479.54, 0.03876, 3, 6, 81.43, 0.11, 0.9599, 78, -181.92, 482.09, 0.0332, 80, -426.34, 201.36, 0.0069, 3, 6, 57.18, 0.68, 0.9597, 78, -206.16, 482.67, 0.02709, 80, -425.55, 177.13, 0.01321, 7, 17, -548.16, 153.22, 0, 7, -74.64, -39.46, 0.00133, 6, 28.73, -4.18, 0.93918, 4, -64.47, -75.56, 0.01873, 36, 90.27, -331.52, 4e-05, 78, -234.62, 477.81, 0.01977, 80, -419.1, 148.99, 0.02095, 8, 17, -584.44, 146.82, 0, 7, -105.49, -59.6, 0.00016, 6, -7.74, -9.38, 0.39763, 3, -12.25, 9.02, 0.45429, 4, -27.96, -70.68, 0.10084, 34, -1.17, 70.15, 0.00562, 78, -271.08, 472.61, 0.01059, 80, -411.86, 112.87, 0.03086, 6, 17, -618.65, 143.43, 0, 3, 22.07, 10.98, 0.80789, 4, 6.37, -68.72, 0.05587, 34, -16.29, 39.27, 0.09453, 78, -305.39, 470.35, 0.00234, 80, -407.67, 78.75, 0.03937, 5, 17, -480.76, 114.24, 0, 7, 2.65, -48.83, 0.41187, 6, 94.81, -45.36, 0.52849, 4, -130.18, -33.8, 0.0241, 78, -168.54, 436.62, 0.03554, 6, 17, -498.97, 116.42, 0, 7, -14.95, -53.98, 0.21763, 6, 76.68, -42.58, 0.65803, 4, -112.08, -36.74, 0.08982, 78, -186.67, 439.4, 0.031, 80, -383.45, 199.02, 0.00353, 6, 17, -521.81, 115.9, 0, 7, -35.75, -63.43, 0.09343, 6, 53.83, -42.35, 0.66591, 4, -89.24, -37.17, 0.20769, 78, -209.51, 439.64, 0.02524, 80, -382.4, 176.2, 0.00774, 8, 17, -541.55, 112.84, 0, 7, -52.7, -73.99, 0.04391, 6, 34, -44.75, 0.56847, 3, -53.68, 44.76, 0.00825, 4, -69.39, -34.94, 0.34618, 36, 110.65, -296.05, 0.0018, 78, -229.34, 437.24, 0.02019, 80, -378.89, 156.54, 0.0112, 8, 17, -562.63, 102.89, 0, 7, -68.18, -91.43, 0.02113, 6, 12.6, -54, 0.20448, 3, -32.2, 53.83, 0.04704, 4, -47.9, -25.88, 0.70104, 36, 133.28, -301.65, 0.00069, 78, -250.74, 427.98, 0.01466, 80, -368.44, 135.69, 0.01096, 7, 17, -570.36, 91.74, 0, 7, -70.91, -104.72, 0.01479, 3, -24.01, 64.64, 0.04359, 4, -39.72, -15.06, 0.91109, 34, 53.07, 53.14, 0.012, 36, 146.32, -297.89, 0.00039, 80, -357.12, 128.22, 0.01812, 6, 17, -601.56, 130.09, 0, 7, -114.67, -81.71, 9e-05, 3, 5.56, 25.02, 0.56029, 4, -10.14, -54.68, 0.28901, 34, 4.04, 46.79, 0.11914, 80, -394.74, 96.14, 0.03146, 5, 17, -610.82, 124.5, 0, 3, 15.05, 30.22, 0.46448, 4, -0.66, -49.48, 0.26846, 34, 3.92, 35.97, 0.23634, 80, -388.93, 87.01, 0.03072, 6, 17, -457.02, 70.72, 0, 7, 41.58, -79.52, 0.6398, 6, 117.1, -89.65, 0.21066, 4, -152.09, 10.68, 0.01212, 55, -91.02, -55.04, 0.1104, 78, -146.25, 392.34, 0.02702, 5, 17, -673.68, 228.12, 0, 3, 73.52, -75.93, 0.06583, 5, 28.04, -9.64, 0.66339, 30, -71.2, -19.46, 0.236, 80, -491.06, 21.77, 0.03477, 5, 17, -671.14, 183.3, 0, 3, 72.85, -31.05, 0.7134, 5, 27.37, 35.24, 0.16758, 30, -77.94, 24.92, 0.07902, 80, -446.31, 25.35, 0.04, 5, 17, -702.38, 225.84, 0, 3, 102.29, -74.85, 0.09916, 5, 56.81, -8.56, 0.35167, 30, -42.84, -14.5, 0.51837, 80, -488.12, -6.87, 0.0308, 5, 17, -731.7, 229.73, 0, 3, 131.42, -79.96, 0.01117, 5, 85.94, -13.67, 0.12269, 30, -13.29, -15.62, 0.83741, 80, -491.32, -36.28, 0.02872, 5, 17, -703.16, 178.74, 0, 3, 105.04, -27.83, 0.74879, 5, 59.56, 38.46, 0.02216, 30, -46.49, 32.46, 0.19425, 80, -441.02, -6.56, 0.0348, 4, 17, -738.67, 173.62, 0, 3, 140.73, -24.2, 0.69815, 30, -11.62, 40.89, 0.26836, 80, -435.07, -41.94, 0.03349, 4, 3, 181.05, -15.3, 0.75627, 27, 93.1, -119.24, 0.11349, 30, 27.13, 55.17, 0.11265, 80, -423.57, -81.6, 0.01759, 4, 3, 66.2, 21.42, 0.60539, 34, -28.83, -4.3, 0.34096, 27, -25.37, -96.85, 0.01754, 80, -394.4, 35.39, 0.0361, 4, 3, 92.84, 29.58, 0.60698, 34, -34.77, -31.52, 0.2506, 27, 0.06, -85.48, 0.10621, 80, -384.52, 9.34, 0.0362, 4, 3, 119.06, 38.83, 0.64508, 34, -39.56, -58.91, 0.13788, 27, 24.95, -73.09, 0.18272, 80, -373.58, -16.22, 0.03432, 4, 3, 142.74, 51.89, 0.50329, 34, -39.79, -85.95, 0.05436, 27, 46.86, -57.24, 0.41379, 80, -359.02, -39, 0.02856, 4, 3, 169.52, 16.69, 0.68099, 34, -83.6, -92.04, 0.00296, 27, 77.75, -88.9, 0.29605, 80, -392.4, -68.02, 0.02, 5, 17, -597.23, 91.51, 0, 7, -95.53, -115.49, 0.00125, 3, 2.84, 63.75, 0.0602, 4, -12.86, -15.95, 0.91975, 80, -356.26, 101.37, 0.01881, 5, 17, -639.8, 131.68, 0, 3, 43.69, 21.84, 0.56618, 4, 27.99, -57.87, 0.03548, 34, -17.43, 15.11, 0.36575, 80, -395.44, 57.88, 0.03259, 3, 3, 67.04, 32.96, 0.40891, 34, -19.18, -10.69, 0.56316, 80, -382.82, 35.3, 0.02794, 4, 3, 78.02, 68.84, 0.13169, 34, 6.71, -37.86, 0.84198, 35, -22.34, -89.61, 0.00373, 80, -346.3, 26.67, 0.0226, 3, 34, 43.8, -36.26, 0.85719, 35, -0.66, -59.47, 0.12554, 80, -316.1, 48.25, 0.01727, 3, 34, 68.61, -40.14, 0.58807, 35, 17.73, -42.38, 0.39843, 80, -293.19, 58.54, 0.0135, 4, 34, 68.16, -73.35, 0.27167, 35, 43.56, -63.27, 0.51501, 27, -27.31, 22.2, 0.19414, 80, -275.44, 30.47, 0.01918, 5, 17, -701.64, -2.84, 0, 34, 64.42, -108.26, 0.00221, 35, 68.67, -87.79, 0.37739, 27, 3.01, 39.89, 0.6004, 80, -259.52, -0.82, 0.02, 5, 3, 96.2, 85.32, 0.1862, 34, 12.16, -61.77, 0.62119, 35, -0.18, -100.11, 0.0035, 27, -3.42, -29.76, 0.16533, 80, -328.68, 9.61, 0.02378, 4, 3, 126.67, 86.32, 0.31389, 34, -1.91, -88.83, 0.17213, 27, 26.7, -25.03, 0.48687, 80, -325.7, -20.74, 0.02711, 4, 17, -739.68, -12.67, 0, 35, 90.95, -120.16, 0.12621, 27, 40.14, 52.76, 0.85379, 80, -248.81, -38.63, 0.02, 3, 17, -832.21, -12.32, 0, 27, 132.39, 59.89, 0.98, 80, -247.01, -131.14, 0.02, 4, 17, -786.02, -15.5, 0, 35, 109.49, -162.72, 0.00192, 27, 86.09, 59.32, 0.97808, 80, -244.91, -84.88, 0.02, 4, 3, 159.68, 74.66, 0.24602, 34, -28.25, -111.88, 0.01508, 27, 60.89, -32.57, 0.71623, 80, -335.19, -54.43, 0.02266, 4, 3, 195.46, 69.32, 0.1778, 34, -50.44, -140.45, 0.00023, 27, 97.05, -33.49, 0.79887, 80, -338.19, -90.48, 0.02311, 3, 3, 237.14, 62.78, 0.1142, 27, 139.22, -34.88, 0.86412, 80, -342.01, -132.5, 0.02168, 3, 3, 318.36, 50.56, 0.03614, 27, 221.33, -37.08, 0.94386, 80, -348.93, -214.35, 0.02, 3, 3, 414.57, 44.97, 0.00014, 27, 317.5, -30.85, 0.97986, 80, -348.26, -310.71, 0.02, 2, 27, 376.67, -24.01, 0.98, 80, -344.84, -370.18, 0.02, 2, 27, 402.49, -21.05, 0.98038, 80, -343.37, -396.13, 0.01962, 3, 27, 433, -22.72, 0.97815, 28, -34.04, -21.85, 0.00511, 80, -346.79, -426.49, 0.01674, 3, 27, 466.52, -17.35, 0.48354, 28, -0.38, -17.35, 0.49908, 80, -343.37, -460.27, 0.01737, 2, 28, 31.65, -8.01, 0.98275, 80, -335.04, -492.58, 0.01725, 4, 17, -1221.15, 73.02, 0, 27, 526.96, 6.24, 0.00019, 28, 60.64, 4.67, 0.98221, 80, -323.29, -521.96, 0.0176, 2, 28, 170.84, 12.82, 0.99146, 80, -318.64, -632.36, 0.00854, 2, 28, 165.68, 67.52, 0.992, 80, -263.81, -628.93, 0.008, 1, 28, 315.94, 17.99, 1, 3, 27, 572.39, 61.18, 4e-05, 28, 107.49, 58.42, 0.98549, 80, -271.06, -570.48, 0.01447, 4, 17, -1218.07, 39.31, 0, 27, 521.16, 39.59, 0.02494, 28, 55.71, 38.16, 0.95801, 80, -289.66, -518.1, 0.01705, 2, 28, 109.84, 9.54, 0.98394, 80, -319.99, -571.28, 0.01606, 4, 17, -1184.35, 39.17, 0, 27, 487.54, 37.01, 0.18502, 28, 22.03, 36.45, 0.79874, 80, -290.31, -484.38, 0.01624, 4, 17, -1150.46, 43.28, 0, 27, 454.09, 30.17, 0.69946, 28, -11.58, 30.48, 0.28412, 80, -295.2, -450.59, 0.01642, 4, 17, -1123.39, 49.02, 0, 27, 427.58, 22.27, 0.93211, 28, -38.29, 23.27, 0.05075, 80, -301.57, -423.67, 0.01714, 4, 17, -1097.48, 46.95, 0, 27, 401.59, 22.24, 0.9727, 28, -64.28, 23.91, 0.00766, 80, -300.1, -397.72, 0.01964, 4, 17, -1069.46, 37.05, 0, 27, 372.86, 29.84, 0.9793, 28, -92.8, 32.26, 0.0007, 80, -290.86, -369.48, 0.02, 3, 17, -1013.75, 16.96, 0, 27, 315.71, 45.37, 0.98, 80, -272.06, -313.31, 0.02, 3, 17, -915.93, -0.1, 0, 27, 216.82, 54.47, 0.98, 80, -257.28, -215.12, 0.02, 3, 9, 124.24, 57.33, 0.97849, 7, 434.19, 140.05, 0, 76, -237.38, 125.4, 0.02151, 3, 9, 125.09, 29.84, 0.97713, 7, 445.77, 115.1, 0, 76, -236.53, 97.9, 0.02287, 3, 9, 37.06, -34.24, 0.94657, 8, 130.29, -34.94, 0.03873, 76, -324.55, 33.83, 0.0147, 3, 9, 50.85, -36.77, 0.975, 8, 144.02, -37.74, 0.01044, 76, -310.76, 31.29, 0.01456, 3, 9, 53.47, -51.25, 0.98123, 8, 146.36, -52.26, 0.00771, 76, -308.15, 16.82, 0.01106, 3, 9, 72.23, -68.05, 0.99386, 8, 164.8, -69.42, 0.00052, 76, -289.38, 0.02, 0.00563, 3, 9, 124.95, 43.86, 0.97766, 7, 440.14, 127.95, 0, 76, -236.66, 111.93, 0.02234, 3, 9, 142.64, 33.13, 0.9862, 7, 460.62, 125.03, 0, 76, -218.98, 101.2, 0.0138, 3, 9, 153.72, 23.67, 0.98869, 7, 474.53, 120.68, 0, 76, -207.89, 91.74, 0.01131, 3, 9, 138.45, 52.07, 0.98537, 7, 449.32, 140.8, 0, 76, -223.16, 120.14, 0.01463, 2, 9, 147.22, -23.44, 0.99158, 76, -214.39, 44.63, 0.00842, 3, 9, 122.27, 14.85, 0.97825, 7, 449.07, 100.21, 0, 76, -239.34, 82.91, 0.02175, 3, 9, 116.26, 3.59, 0.97831, 7, 447.97, 87.5, 0, 76, -245.35, 71.66, 0.02169, 2, 9, 100.42, -7.35, 0.98034, 76, -261.2, 60.72, 0.01966, 2, 9, 76.81, -16.48, 0.98075, 76, -284.8, 51.59, 0.01925, 3, 9, 53.53, -24.64, 0.98154, 8, 146.93, -25.66, 0.00151, 76, -308.08, 43.43, 0.01695, 3, 9, 40.62, -29.7, 0.96328, 8, 133.93, -30.47, 0.02096, 76, -321, 38.37, 0.01576, 3, 9, 139.36, 20.5, 0.98297, 7, 462.56, 112.12, 0, 76, -222.26, 88.56, 0.01703, 3, 9, 129.68, 11.88, 0.97978, 7, 457.05, 100.39, 0, 76, -231.94, 79.94, 0.02022, 3, 9, 133.95, 44.01, 0.98174, 7, 448.35, 131.62, 0, 76, -227.66, 112.08, 0.01826, 3, 9, 144.8, 39.8, 0.98608, 7, 459.99, 132.01, 0, 76, -216.81, 107.86, 0.01392, 3, 9, 157.61, 4.31, 0.9917, 7, 485.7, 104.41, 0, 76, -204.01, 72.38, 0.0083, 3, 9, 145.86, 1.23, 0.98683, 7, 476.11, 96.96, 0, 76, -215.75, 69.3, 0.01317, 2, 9, 125.08, -20.35, 0.98323, 76, -236.53, 47.72, 0.01677, 2, 9, 100.98, -31.83, 0.98209, 76, -260.63, 36.24, 0.01791, 2, 9, 77.02, -40.54, 0.98428, 76, -284.6, 27.52, 0.01572, 2, 9, 131.1, -40.3, 0.98922, 76, -230.51, 27.77, 0.01078, 2, 9, 112.79, -51.18, 0.98869, 76, -248.82, 16.88, 0.01131, 2, 9, 90.28, -62.08, 0.99142, 76, -271.33, 5.98, 0.00858, 3, 9, 38.46, -56.79, 0.94262, 8, 131.25, -57.52, 0.04944, 76, -323.15, 11.28, 0.00794, 2, 37, 54.29, -23.9, 0.8242, 36, 229.38, -54.47, 0.1758, 2, 17, -505.73, -179.82, 0, 37, 46.67, 14.58, 1], "hull": 195, "edges": [0, 388, 10, 12, 34, 36, 36, 38, 42, 44, 44, 46, 64, 66, 146, 148, 174, 176, 176, 178, 182, 184, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 212, 214, 220, 222, 222, 224, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 278, 280, 300, 302, 310, 312, 312, 314, 314, 316, 320, 322, 332, 334, 334, 336, 336, 338, 354, 356, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 378, 380, 384, 386, 386, 388, 0, 2, 380, 382, 382, 384, 376, 378, 374, 376, 370, 372, 372, 374, 30, 32, 32, 34, 28, 30, 28, 26, 26, 24, 24, 22, 22, 20, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 4, 6, 26, 390, 390, 392, 38, 40, 40, 42, 46, 48, 48, 50, 50, 52, 52, 54, 356, 358, 358, 360, 348, 350, 344, 346, 346, 348, 342, 344, 338, 340, 340, 342, 330, 332, 326, 328, 328, 330, 322, 324, 324, 326, 316, 318, 318, 320, 308, 310, 302, 304, 304, 306, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 132, 134, 128, 130, 130, 132, 134, 136, 136, 138, 142, 144, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 100, 98, 94, 96, 96, 98, 58, 60, 66, 68, 68, 70, 70, 72, 76, 78, 60, 62, 62, 64, 394, 396, 396, 398, 54, 56, 56, 58, 336, 422, 422, 424, 424, 426, 426, 428, 428, 430, 434, 100, 432, 436, 434, 438, 438, 440, 440, 442, 442, 444, 42, 446, 446, 444, 436, 448, 448, 450, 450, 452, 452, 454, 454, 456, 456, 354, 346, 462, 350, 464, 466, 468, 470, 472, 472, 474, 468, 474, 350, 352, 352, 354, 478, 480, 480, 470, 482, 484, 484, 486, 482, 488, 488, 490, 490, 492, 486, 494, 492, 496, 496, 494, 464, 498, 500, 502, 502, 504, 504, 340, 462, 506, 506, 508, 508, 510, 510, 512, 512, 342, 500, 476, 498, 466, 476, 514, 514, 478, 432, 430, 454, 526, 530, 532, 534, 530, 536, 538, 538, 540, 540, 542, 542, 544, 546, 548, 548, 550, 550, 552, 556, 558, 558, 452, 568, 570, 570, 404, 572, 574, 570, 572, 572, 402, 582, 330, 116, 584, 586, 588, 588, 590, 590, 592, 592, 594, 594, 596, 598, 600, 600, 602, 602, 604, 604, 606, 606, 608, 610, 608, 612, 614, 614, 616, 616, 618, 618, 620, 620, 622, 624, 626, 626, 628, 628, 630, 630, 632, 122, 634, 634, 636, 636, 638, 638, 622, 622, 596, 596, 608, 642, 646, 646, 648, 648, 308, 306, 308, 644, 650, 650, 652, 652, 654, 656, 658, 658, 660, 660, 662, 664, 302, 638, 666, 666, 668, 668, 670, 670, 672, 672, 674, 674, 676, 676, 160, 162, 160, 160, 158, 158, 156, 152, 154, 154, 156, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 298, 300, 294, 296, 296, 298, 290, 292, 292, 294, 286, 288, 288, 290, 280, 282, 282, 284, 284, 286, 184, 186, 186, 188, 188, 190, 194, 196, 190, 192, 192, 194, 178, 180, 180, 182, 248, 250, 250, 252, 208, 210, 210, 212, 252, 254, 254, 256, 256, 258, 258, 260, 214, 216, 216, 218, 218, 220, 224, 226, 246, 248, 242, 244, 244, 246, 680, 686, 686, 690, 690, 688, 692, 694, 694, 696, 696, 698, 698, 700, 702, 704, 704, 706, 706, 708, 708, 710, 710, 712, 714, 718, 716, 720, 720, 722, 716, 718, 712, 724, 724, 714, 274, 276, 276, 278, 722, 726, 726, 728, 728, 730, 730, 732, 732, 734, 734, 736, 700, 702, 736, 738, 738, 688, 780, 756, 756, 782, 784, 760, 72, 74, 74, 76, 138, 140, 140, 142, 148, 150, 150, 152, 144, 146, 140, 150, 84, 418, 418, 420, 420, 74, 82, 80, 80, 78, 2, 4, 6, 8], "width": 562, "height": 1823}}, "body2": {"body": {"type": "mesh", "uvs": [0.67021, 0.32417, 0.69404, 0.31607, 0.7044, 0.31719, 0.74195, 0.30179, 0.78145, 0.27975, 0.81735, 0.2882, 0.86108, 0.3014, 0.88696, 0.31196, 0.86724, 0.31782, 0.81405, 0.33077, 0.76951, 0.33925, 0.78054, 0.34477, 0.73657, 0.35218, 0.78311, 0.31309], "triangles": [3, 4, 5, 13, 3, 5, 13, 5, 6, 2, 3, 13, 8, 6, 7, 13, 6, 8, 9, 13, 8, 10, 2, 13, 10, 13, 9, 10, 0, 2, 12, 10, 11, 0, 1, 2, 10, 12, 0], "vertices": [2, 37, 183.98, -23.77, 0.98177, 35, 65.29, 57.81, 0.01823, 1, 37, 164.18, -26.16, 1, 1, 37, 161.12, -20.8, 1, 1, 37, 126.79, -28.18, 1, 2, 37, 83.69, -44, 0.91856, 36, 209.49, -84.01, 0.08144, 2, 37, 78.54, -19.14, 0.96094, 36, 234.31, -78.68, 0.03906, 1, 37, 75.74, 15.14, 1, 1, 37, 77.36, 39.22, 1, 1, 37, 92.74, 40.03, 1, 1, 37, 130.79, 38.28, 1, 1, 37, 159.83, 33.56, 1, 1, 37, 161.73, 45.22, 1, 1, 37, 189.25, 39.24, 1, 1, 37, 122.81, 2.53, 1], "hull": 13, "edges": [20, 22, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20, 14, 16, 0, 2, 22, 24, 0, 24, 8, 10, 10, 12, 12, 14], "width": 562, "height": 1823}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.04239, 0.50801, 0.09641, 0.82478, 0.62067, 0.92886, 0.94793, 0.58946, 0.92251, 0.19124, 0.42367, 0.05096], "triangles": [2, 1, 0, 3, 2, 5, 3, 5, 4, 2, 0, 5], "vertices": [2, 9, 53.21, 45.84, 0.969, 76, -308.4, 113.91, 0.031, 2, 9, 42.7, 43.54, 0.969, 76, -318.91, 111.61, 0.031, 2, 9, 38.7, 18.99, 0.969, 76, -322.92, 87.06, 0.031, 2, 9, 49.54, 3.35, 0.969, 76, -312.08, 71.42, 0.031, 2, 9, 62.7, 4.24, 0.969, 76, -298.91, 72.31, 0.031, 2, 9, 67.88, 27.57, 0.969, 76, -293.74, 95.64, 0.031], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 47, "height": 33}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.37256, 0.0847, 0.09449, 0.35762, 0.07905, 0.73352, 0.30047, 0.93436, 0.88751, 0.77472, 0.90811, 0.34732], "triangles": [5, 1, 0, 5, 3, 1, 3, 2, 1, 5, 4, 3], "vertices": [2, 9, 60.34, 80.87, 0.97751, 76, -301.28, 148.94, 0.02249, 2, 9, 52.61, 89.12, 0.98051, 76, -309, 157.18, 0.01949, 2, 9, 41.72, 89.82, 0.98126, 76, -319.89, 157.88, 0.01874, 2, 9, 35.75, 83.53, 0.97848, 76, -325.86, 151.6, 0.02152, 2, 9, 39.98, 66.41, 0.973, 76, -321.63, 134.47, 0.027, 2, 9, 52.36, 65.52, 0.973, 76, -309.25, 133.59, 0.027], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 29, "height": 29}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 15, -8.52, -7.37, 0.969, 76, -314.4, 81.03, 0.031, 2, 15, -8.15, 8.63, 0.969, 76, -314.03, 97.02, 0.031, 2, 15, 7.84, 8.25, 0.969, 76, -298.03, 96.65, 0.031, 2, 15, 7.47, -7.74, 0.969, 76, -298.41, 80.66, 0.031], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 16}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.40513, 0.11747, 0.58397, 0.11764, 0.65923, 0.0271, 0.84167, 0.07292, 0.96383, 0.14377, 0.96344, 0.34613, 0.87866, 0.41934, 0.83368, 0.65435, 0.74669, 0.81583, 0.56838, 0.91093, 0.38882, 0.93197, 0.22662, 0.9215, 0.10451, 0.8109, 0.0447, 0.80045, 0.02969, 0.70965, 0.09501, 0.51749, 0.15292, 0.33434, 0.26208, 0.1531, 0.12931, 0.66058, 0.16348, 0.53154, 0.2097, 0.39673, 0.30415, 0.2677, 0.42272, 0.23303, 0.5644, 0.24266, 0.67392, 0.30236, 0.74225, 0.40829, 0.69904, 0.57199, 0.60459, 0.70295, 0.47698, 0.74532, 0.33731, 0.73569, 0.20166, 0.71451], "triangles": [22, 0, 1, 23, 22, 1, 21, 17, 0, 21, 0, 22, 24, 2, 3, 1, 2, 24, 23, 1, 24, 20, 16, 17, 4, 6, 3, 21, 20, 17, 25, 24, 3, 6, 25, 3, 4, 5, 6, 19, 16, 20, 15, 16, 19, 26, 24, 25, 7, 25, 6, 26, 25, 7, 18, 15, 19, 26, 27, 23, 26, 23, 24, 14, 15, 18, 30, 19, 20, 18, 19, 30, 29, 21, 22, 20, 21, 29, 30, 20, 29, 28, 22, 23, 28, 23, 27, 29, 22, 28, 12, 13, 14, 18, 12, 14, 12, 18, 30, 8, 26, 7, 27, 26, 8, 9, 28, 27, 9, 27, 8, 11, 30, 29, 12, 30, 11, 10, 29, 28, 10, 28, 9, 11, 29, 10], "vertices": [2, 9, 62.68, 24.97, 0.969, 76, -298.94, 93.04, 0.031, 2, 9, 62.48, 16.74, 0.969, 76, -299.13, 84.81, 0.031, 2, 9, 64.57, 13.23, 0.969, 76, -297.04, 81.3, 0.031, 2, 9, 63.28, 4.87, 0.969, 76, -298.34, 72.94, 0.031, 2, 9, 61.45, -0.71, 0.969, 76, -300.17, 67.36, 0.031, 2, 9, 56.59, -0.58, 0.969, 76, -305.02, 67.49, 0.031, 2, 9, 54.93, 3.36, 0.969, 76, -306.69, 71.43, 0.031, 2, 9, 49.34, 5.56, 0.969, 76, -312.28, 73.63, 0.031, 2, 9, 45.56, 9.65, 0.969, 76, -316.06, 77.72, 0.031, 2, 9, 43.46, 17.9, 0.969, 76, -318.15, 85.97, 0.031, 2, 9, 43.15, 26.17, 0.969, 76, -318.46, 94.24, 0.031, 2, 9, 43.58, 33.63, 0.969, 76, -318.04, 101.69, 0.031, 2, 9, 46.36, 39.18, 0.969, 76, -315.25, 107.25, 0.031, 2, 9, 46.67, 41.92, 0.969, 76, -314.94, 109.99, 0.031, 2, 9, 48.87, 42.56, 0.969, 76, -312.75, 110.63, 0.031, 2, 9, 53.41, 39.45, 0.969, 76, -308.2, 107.52, 0.031, 2, 9, 57.74, 36.69, 0.969, 76, -303.87, 104.76, 0.031, 2, 9, 61.97, 31.57, 0.969, 76, -299.64, 99.64, 0.031, 2, 9, 49.94, 37.96, 0.969, 76, -311.67, 106.02, 0.031, 2, 9, 53, 36.31, 0.969, 76, -308.61, 104.38, 0.031, 2, 9, 56.18, 34.11, 0.969, 76, -305.43, 102.18, 0.031, 2, 9, 59.18, 29.7, 0.969, 76, -302.43, 97.76, 0.031, 2, 9, 59.88, 24.22, 0.969, 76, -301.73, 92.29, 0.031, 2, 9, 59.5, 17.71, 0.969, 76, -302.11, 85.78, 0.031, 2, 9, 57.95, 12.71, 0.969, 76, -303.66, 80.78, 0.031, 2, 9, 55.34, 9.63, 0.969, 76, -306.28, 77.7, 0.031, 2, 9, 51.46, 11.71, 0.969, 76, -310.16, 79.77, 0.031, 2, 9, 48.42, 16.12, 0.969, 76, -313.2, 84.19, 0.031, 2, 9, 47.54, 22.01, 0.969, 76, -314.08, 90.08, 0.031, 2, 9, 47.92, 28.43, 0.969, 76, -313.7, 96.5, 0.031, 2, 9, 48.57, 34.66, 0.969, 76, -313.05, 102.73, 0.031], "hull": 18, "edges": [26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 22, 24, 24, 26, 18, 20, 20, 22, 14, 16, 16, 18, 12, 14, 10, 12, 8, 10, 4, 6, 6, 8, 0, 2, 2, 4, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 36], "width": 46, "height": 24}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 16, -7.96, -6.96, 0.97431, 76, -321.12, 136.2, 0.02569, 2, 16, -7.61, 8.03, 0.97605, 76, -320.77, 151.2, 0.02395, 2, 16, 7.39, 7.69, 0.9763, 76, -305.78, 150.85, 0.0237, 2, 16, 7.04, -7.31, 0.97436, 76, -306.13, 135.85, 0.02564], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 15}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.36318, 0.05575, 0.42392, 0.14418, 0.5828, 0.14826, 0.71795, 0.22322, 0.83286, 0.3504, 0.92571, 0.46595, 0.97263, 0.56893, 0.9676, 0.66304, 0.8859, 0.74809, 0.74062, 0.85675, 0.57162, 0.94718, 0.33394, 0.94832, 0.22922, 0.77162, 0.20962, 0.57739, 0.0619, 0.44187, 0.06518, 0.07007, 0.28029, 0.48809, 0.34993, 0.34944, 0.44278, 0.27416, 0.60682, 0.27813, 0.71205, 0.34547, 0.80955, 0.44055, 0.89466, 0.55346, 0.87609, 0.62081, 0.79407, 0.66836, 0.67801, 0.72778, 0.54182, 0.75353, 0.3979, 0.7456, 0.31898, 0.65845], "triangles": [18, 1, 2, 19, 2, 3, 18, 2, 19, 20, 19, 3, 17, 15, 0, 17, 0, 1, 17, 1, 18, 20, 3, 4, 21, 20, 4, 14, 15, 17, 21, 4, 5, 16, 14, 17, 22, 21, 5, 22, 5, 6, 13, 14, 16, 23, 21, 22, 28, 16, 17, 13, 16, 28, 6, 23, 22, 7, 23, 6, 21, 25, 20, 24, 21, 23, 20, 26, 19, 24, 25, 21, 26, 27, 17, 28, 17, 27, 18, 26, 17, 19, 26, 18, 8, 23, 7, 24, 23, 8, 20, 25, 26, 12, 13, 28, 9, 25, 24, 9, 24, 8, 10, 26, 25, 10, 25, 9, 27, 12, 28, 11, 12, 27, 10, 11, 27, 10, 27, 26], "vertices": [2, 9, 58.52, 86.1, 0.97917, 76, -303.09, 154.16, 0.02083, 2, 9, 56.26, 84.2, 0.97872, 76, -305.35, 152.27, 0.02128, 2, 9, 56.04, 79.12, 0.97698, 76, -305.57, 147.19, 0.02302, 2, 9, 54.07, 74.84, 0.97557, 76, -307.54, 142.91, 0.02443, 2, 9, 50.81, 71.24, 0.97437, 76, -310.81, 139.31, 0.02563, 2, 9, 47.85, 68.34, 0.97333, 76, -313.76, 136.41, 0.02667, 2, 9, 45.24, 66.9, 0.973, 76, -316.37, 134.97, 0.027, 2, 9, 42.89, 67.11, 0.973, 76, -318.72, 135.18, 0.027, 2, 9, 40.83, 69.78, 0.97367, 76, -320.79, 137.84, 0.02633, 2, 9, 38.22, 74.49, 0.97515, 76, -323.39, 142.55, 0.02485, 2, 9, 36.09, 79.95, 0.97681, 76, -325.53, 148.01, 0.02319, 2, 9, 36.23, 87.55, 0.97934, 76, -325.38, 155.62, 0.02066, 2, 9, 40.73, 90.8, 0.98094, 76, -320.89, 158.87, 0.01906, 2, 9, 45.6, 91.31, 0.98143, 76, -316.02, 159.38, 0.01857, 2, 9, 49.09, 95.96, 0.98311, 76, -312.52, 164.03, 0.01689, 2, 9, 58.38, 95.64, 0.9823, 76, -303.23, 163.71, 0.0177, 2, 9, 47.78, 89, 0.98064, 76, -313.84, 157.07, 0.01936, 2, 9, 51.19, 86.69, 0.97982, 76, -310.42, 154.76, 0.02018, 2, 9, 53, 83.68, 0.97871, 76, -308.61, 151.74, 0.02129, 2, 9, 52.78, 78.43, 0.97687, 76, -308.83, 146.5, 0.02313, 2, 9, 51.02, 75.1, 0.97573, 76, -310.59, 143.17, 0.02427, 2, 9, 48.57, 72.04, 0.97464, 76, -313.04, 140.11, 0.02536, 2, 9, 45.69, 69.38, 0.97368, 76, -315.93, 137.45, 0.02632, 2, 9, 44.02, 70.02, 0.97387, 76, -317.6, 138.08, 0.02613, 2, 9, 42.89, 72.67, 0.97477, 76, -318.73, 140.74, 0.02523, 2, 9, 41.49, 76.41, 0.97603, 76, -320.12, 144.48, 0.02397, 2, 9, 40.95, 80.79, 0.97752, 76, -320.67, 148.85, 0.02248, 2, 9, 41.25, 85.39, 0.97913, 76, -320.36, 153.45, 0.02087, 2, 9, 43.49, 87.86, 0.98013, 76, -318.12, 155.93, 0.01987], "hull": 16, "edges": [12, 14, 20, 22, 28, 30, 2, 0, 0, 30, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 18, 20, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 32], "width": 32, "height": 25}}, "hair_B": {"hair_B": {"type": "mesh", "uvs": [0.50759, 0.00285, 0.69082, 0.04442, 0.82326, 0.10089, 0.90805, 0.16335, 0.93619, 0.27233, 1, 0.37075, 0.98381, 0.42868, 0.97475, 0.50924, 0.96393, 0.58255, 0.90353, 0.6446, 0.79387, 0.685, 0.81209, 0.72862, 0.86847, 0.74774, 0.99999, 0.72735, 0.89776, 0.81612, 0.83919, 0.78718, 0.70298, 0.75661, 0.74817, 0.80387, 0.76679, 0.85259, 0.80841, 0.88897, 0.8977, 0.90369, 0.99364, 0.88526, 0.99999, 0.90396, 0.89776, 0.92724, 0.7584, 0.91812, 0.66433, 0.88765, 0.63396, 0.92044, 0.54252, 0.9643, 0.41845, 0.99057, 0.32452, 0.99609, 0.23863, 0.97861, 0.2363, 0.94192, 0.29572, 0.96394, 0.35834, 0.96524, 0.44623, 0.93731, 0.45873, 0.88795, 0.38536, 0.81897, 0.39034, 0.88511, 0.19391, 0.87694, 0.17202, 0.83979, 0.21261, 0.77699, 0.23876, 0.68714, 0.24834, 0.61589, 0.23812, 0.52971, 0.24521, 0.44161, 0.25661, 0.3604, 0.25199, 0.28593, 0.23663, 0.23929, 0.19342, 0.21976, 0.12201, 0.21609, 0.06233, 0.18184, 0.00265, 0.14759, 0.04411, 0.13124, 0.11135, 0.07373, 0.21217, 0.02425, 0.34617, 0, 0.16425, 0.10974, 0.31994, 0.09531, 0.4724, 0.10875, 0.61236, 0.15177, 0.67238, 0.22359, 0.6852, 0.28823, 0.67959, 0.36167, 0.66677, 0.42897, 0.64145, 0.50427, 0.62183, 0.57359, 0.62835, 0.64664, 0.65357, 0.70089, 0.4481, 0.73575, 0.51951, 0.79525, 0.57832, 0.84269, 0.18323, 0.17037, 0.29694, 0.17203, 0.41994, 0.20282, 0.46635, 0.25025, 0.46171, 0.31349, 0.44779, 0.37424, 0.42198, 0.43641, 0.40806, 0.49716, 0.3948, 0.56403, 0.39971, 0.62227, 0.41732, 0.67818], "triangles": [30, 31, 32, 29, 32, 33, 29, 33, 28, 30, 32, 29, 27, 34, 26, 28, 33, 34, 28, 34, 27, 70, 17, 18, 25, 70, 18, 70, 35, 36, 35, 70, 25, 25, 18, 19, 20, 21, 22, 24, 25, 19, 26, 35, 25, 23, 20, 22, 24, 19, 20, 23, 24, 20, 34, 35, 26, 67, 10, 11, 68, 81, 67, 16, 67, 11, 68, 67, 16, 12, 16, 11, 15, 16, 12, 69, 68, 16, 69, 16, 17, 14, 12, 13, 15, 12, 14, 41, 68, 40, 68, 41, 81, 36, 68, 69, 36, 40, 68, 39, 40, 36, 70, 69, 17, 36, 69, 70, 38, 39, 36, 38, 36, 37, 78, 44, 77, 43, 44, 78, 79, 43, 78, 79, 78, 64, 65, 79, 64, 42, 43, 79, 80, 79, 65, 42, 79, 80, 66, 65, 9, 80, 65, 66, 81, 80, 66, 10, 66, 9, 41, 42, 80, 41, 80, 81, 67, 66, 10, 81, 66, 67, 76, 45, 75, 76, 75, 62, 4, 5, 62, 6, 62, 5, 63, 76, 62, 63, 62, 6, 77, 45, 76, 77, 76, 63, 44, 45, 77, 64, 77, 63, 78, 77, 64, 7, 63, 6, 64, 63, 7, 8, 64, 7, 65, 64, 8, 9, 65, 8, 73, 59, 60, 59, 2, 3, 60, 59, 3, 74, 73, 60, 60, 3, 4, 61, 60, 4, 74, 60, 61, 75, 46, 74, 75, 74, 61, 45, 46, 75, 62, 75, 61, 4, 62, 61, 59, 58, 1, 58, 0, 1, 73, 58, 59, 57, 54, 55, 53, 54, 57, 72, 57, 58, 73, 72, 58, 47, 72, 73, 56, 53, 57, 52, 53, 56, 72, 71, 56, 52, 56, 71, 57, 72, 56, 50, 52, 71, 51, 52, 50, 49, 50, 71, 48, 71, 72, 49, 71, 48, 47, 48, 72, 0, 57, 55, 58, 57, 0, 47, 73, 74, 46, 47, 74, 59, 1, 2], "vertices": [3, 17, 42.03, -35.67, 0.99303, 76, -185.63, -28.67, 0.00197, 77, -122.56, -28.67, 0.005, 4, 17, 31.22, -51.91, 0.85761, 18, -17.64, 18.79, 0.13674, 76, -196.44, -44.92, 0.00066, 77, -133.38, -44.92, 0.005, 3, 17, 16.77, -63.5, 0.51628, 18, -1.97, 28.66, 0.47872, 77, -147.82, -56.5, 0.005, 3, 17, 0.92, -70.76, 0.19068, 18, 14.61, 34.07, 0.80432, 77, -163.67, -63.77, 0.005, 3, 18, 42.05, 32.82, 0.84968, 19, -20.37, 28.8, 0.14532, 77, -191.08, -65.67, 0.005, 3, 18, 67.31, 35.12, 0.2953, 19, 3.61, 37.05, 0.6997, 77, -215.91, -70.83, 0.005, 3, 18, 81.51, 31.69, 0.08891, 19, 18.22, 37.1, 0.90609, 77, -230.41, -69.04, 0.005, 4, 18, 101.43, 28.11, 0.00205, 19, 38.42, 38.37, 0.96121, 20, -7.27, 39.45, 0.03174, 77, -250.61, -67.76, 0.005, 4, 19, 56.82, 39.29, 0.75514, 20, 11.15, 39.06, 0.22767, 21, -21.96, 42.9, 0.01219, 77, -268.98, -66.35, 0.005, 4, 19, 72.87, 35.49, 0.45453, 20, 26.89, 34.12, 0.43833, 21, -7.16, 35.63, 0.10214, 77, -284.42, -60.56, 0.005, 4, 19, 83.98, 26.72, 0.15704, 20, 37.34, 24.58, 0.40735, 21, 1.72, 24.61, 0.4306, 77, -294.33, -50.46, 0.005, 4, 19, 94.7, 29.47, 0.02699, 20, 48.24, 26.57, 0.11455, 21, 12.79, 24.91, 0.85346, 77, -305.32, -51.84, 0.005, 4, 19, 98.95, 35.01, 0.00459, 20, 52.87, 31.79, 0.0231, 21, 18.16, 29.37, 0.96731, 77, -310.23, -56.8, 0.005, 2, 21, 14.51, 41.74, 0.995, 77, -305.39, -68.76, 0.005, 2, 21, 35.52, 29.91, 0.995, 77, -327.45, -59.04, 0.005, 4, 19, 109.07, 33.41, 0.00159, 20, 62.85, 29.48, 0.00929, 21, 27.67, 25.56, 0.98412, 77, -320.07, -53.94, 0.005, 5, 19, 102.7, 20.43, 0.00801, 20, 55.57, 16.98, 0.04001, 21, 18.57, 14.32, 0.93336, 22, -15.04, 11.78, 0.01362, 77, -312.11, -41.86, 0.005, 3, 21, 30.84, 16.92, 0.56601, 22, -3.45, 16.57, 0.42899, 77, -324.07, -45.65, 0.005, 3, 21, 43.18, 17.11, 0.10004, 22, 8.65, 18.99, 0.89496, 77, -336.33, -47.04, 0.005, 3, 21, 52.7, 19.72, 0.00315, 22, 17.54, 23.29, 0.99185, 77, -345.55, -50.58, 0.005, 2, 22, 20.73, 31.54, 0.995, 77, -349.43, -58.52, 0.005, 3, 21, 53.79, 36.39, 0, 22, 15.58, 39.88, 0.995, 77, -345, -67.26, 0.005, 3, 21, 58.52, 36.39, 0, 22, 20.23, 40.73, 0.995, 77, -349.71, -67.73, 0.005, 2, 22, 26.63, 31.91, 0.995, 77, -355.34, -58.39, 0.005, 2, 22, 25.12, 19.25, 0.995, 77, -352.76, -45.91, 0.005, 3, 21, 50.8, 6.89, 0.0023, 22, 18, 10.33, 0.9927, 77, -344.91, -37.62, 0.005, 3, 22, 26.39, 8.11, 0.81089, 23, -5.59, 5.94, 0.18411, 77, -353.08, -34.7, 0.005, 2, 23, 8.13, 6.83, 0.995, 77, -363.89, -26.21, 0.005, 3, 23, 20.39, 2.61, 0.52563, 24, -1.19, 2.86, 0.46937, 77, -370.23, -14.9, 0.005, 2, 24, 7.08, 5.08, 0.995, 77, -371.41, -6.41, 0.005, 2, 24, 15.21, 1.49, 0.995, 77, -366.85, 1.21, 0.005, 3, 23, 21.74, -17.79, 4e-05, 24, 16.34, -7.66, 0.99496, 77, -357.64, 1.21, 0.005, 2, 24, 10.47, -2.69, 0.995, 77, -363.29, -4.01, 0.005, 3, 23, 19.07, -5.63, 0.12723, 24, 4.83, -2.93, 0.86777, 77, -363.74, -9.64, 0.005, 3, 22, 31.65, -8.5, 0.02455, 23, 8.6, -4.16, 0.97045, 77, -356.92, -17.71, 0.005, 4, 21, 48.65, -11.49, 0.00271, 22, 19.22, -8.13, 0.9243, 23, -1.56, -11.35, 0.068, 77, -344.56, -19.12, 0.005, 4, 21, 30.66, -15.95, 0.73376, 22, 2.34, -15.79, 0.26064, 76, -390.16, -12.92, 0.0006, 77, -327.1, -12.92, 0.005, 3, 21, 47.19, -17.51, 0.88015, 22, 18.88, -14.32, 0.11485, 77, -343.7, -12.98, 0.005, 3, 21, 43.03, -34.81, 0.89943, 22, 17.92, -32.09, 0.09557, 77, -341.24, 4.64, 0.005, 3, 21, 33.53, -35.64, 0.90539, 22, 8.73, -34.63, 0.08961, 77, -331.88, 6.4, 0.005, 4, 20, 62.09, -26.97, 0.03016, 21, 18.32, -30.11, 0.92032, 22, -7.22, -31.95, 0.04452, 77, -316.2, 2.38, 0.005, 3, 20, 39.47, -25.34, 0.51603, 21, -3.78, -25.05, 0.47897, 77, -293.71, -0.5, 0.005, 3, 20, 21.57, -25.04, 0.94844, 21, -21.43, -22.04, 0.04656, 77, -275.85, -1.77, 0.005, 3, 19, 50.35, -27.05, 0.02501, 20, -0.02, -26.65, 0.96999, 77, -254.2, -1.36, 0.005, 6, 17, -67.52, -9.5, 0.00077, 18, 75.61, -34.6, 0.03499, 19, 28.29, -28.69, 0.50402, 20, -22.14, -26.72, 0.45391, 76, -295.18, -2.51, 0.0013, 77, -232.11, -2.51, 0.005, 6, 17, -47.17, -11, 0.02477, 18, 55.56, -30.79, 0.34658, 19, 7.91, -29.77, 0.53398, 20, -42.55, -26.35, 0.08685, 76, -274.83, -4.01, 0.00282, 77, -211.76, -4.01, 0.005, 6, 17, -28.47, -11.02, 0.32104, 18, 36.99, -28.64, 0.56117, 19, -10.64, -32.1, 0.1029, 20, -61.22, -27.36, 0.00583, 76, -256.13, -4.03, 0.00406, 77, -193.06, -4.03, 0.005, 6, 17, -16.73, -9.91, 0.81993, 18, 25.2, -28.4, 0.16399, 19, -22.14, -34.68, 0.00592, 20, -72.87, -29.11, 8e-05, 76, -244.39, -2.92, 0.00508, 77, -181.33, -2.92, 0.005, 3, 17, -11.74, -6.13, 0.98893, 76, -239.4, 0.86, 0.00607, 77, -176.33, 0.86, 0.005, 3, 17, -10.67, 0.27, 0.98766, 76, -238.33, 7.26, 0.00734, 77, -175.26, 7.26, 0.00499, 3, 17, -1.95, 5.44, 0.98499, 76, -229.61, 12.43, 0.01002, 77, -166.55, 12.43, 0.00498, 3, 17, 6.77, 10.61, 0.98258, 76, -220.89, 17.6, 0.01245, 77, -157.83, 17.6, 0.00497, 3, 17, 10.78, 6.79, 0.98391, 76, -216.88, 13.78, 0.01111, 77, -153.81, 13.78, 0.00498, 3, 17, 25.07, 0.4, 0.98671, 76, -202.59, 7.39, 0.00831, 77, -139.52, 7.39, 0.00499, 3, 17, 37.28, -8.96, 0.99033, 76, -190.38, -1.97, 0.00467, 77, -127.31, -1.97, 0.005, 3, 17, 43.08, -21.16, 0.99171, 76, -184.58, -14.17, 0.00329, 77, -121.51, -14.17, 0.005, 3, 17, 15.93, -4.15, 0.98691, 76, -211.73, 2.84, 0.00809, 77, -148.67, 2.84, 0.00499, 3, 17, 19.22, -18.24, 0.986, 76, -208.44, -11.25, 0.009, 77, -145.37, -11.25, 0.005, 4, 17, 15.53, -31.88, 0.87419, 18, -4.34, -2.89, 0.11054, 76, -212.13, -24.89, 0.01027, 77, -149.06, -24.89, 0.005, 4, 17, 4.44, -44.22, 0.4961, 18, 8.08, 8.1, 0.48869, 76, -223.22, -37.23, 0.01021, 77, -160.15, -37.23, 0.005, 4, 17, -13.7, -49.21, 0.14551, 18, 26.68, 10.98, 0.83946, 76, -241.37, -42.21, 0.01003, 77, -178.3, -42.21, 0.005, 4, 18, 42.91, 9.9, 0.95663, 19, -14.08, 6.74, 0.02898, 76, -257.61, -42.99, 0.00939, 77, -194.54, -42.99, 0.005, 4, 18, 61.1, 6.87, 0.19342, 19, 4.31, 8.13, 0.79264, 76, -276.03, -42.06, 0.00894, 77, -212.96, -42.06, 0.005, 4, 18, 77.67, 3.41, 0.00276, 19, 21.23, 8.72, 0.98327, 76, -292.89, -40.51, 0.00897, 77, -229.82, -40.51, 0.005, 3, 19, 40.27, 8.4, 0.98621, 76, -311.73, -37.8, 0.00879, 77, -248.66, -37.8, 0.005, 5, 19, 57.75, 8.44, 0.42775, 20, 9.89, 8.21, 0.55979, 21, -27.91, 12.61, 0.00013, 76, -329.09, -35.63, 0.00733, 77, -266.02, -35.63, 0.005, 5, 19, 75.93, 10.91, 0.09569, 20, 28.2, 9.39, 0.81269, 21, -9.64, 10.98, 0.08109, 76, -347.43, -35.79, 0.00553, 77, -284.36, -35.79, 0.005, 5, 19, 89.24, 14.57, 0.03775, 20, 41.73, 12.09, 0.29782, 21, 4.15, 11.59, 0.65527, 76, -361.09, -37.74, 0.00416, 77, -298.03, -37.74, 0.005, 5, 20, 51.07, -6.12, 0.02965, 21, 10.61, -7.82, 0.96193, 22, -18.86, -11.44, 0.00012, 76, -369.41, -19.05, 0.0033, 77, -306.35, -19.05, 0.005, 4, 21, 26.21, -3.25, 0.98386, 22, -4.35, -4.1, 0.00926, 76, -384.49, -25.13, 0.00188, 77, -321.42, -25.13, 0.005, 3, 22, 7.22, 1.91, 0.99431, 76, -396.52, -30.14, 0.00069, 77, -333.45, -30.14, 0.005, 3, 17, 0.67, -5.5, 0.98667, 76, -226.99, 1.49, 0.00834, 77, -163.92, 1.49, 0.005, 3, 17, 0.02, -15.73, 0.98551, 76, -227.64, -8.73, 0.00949, 77, -164.58, -8.73, 0.005, 5, 17, -7.96, -26.61, 0.67664, 18, 18.4, -10.81, 0.30759, 19, -32.94, -19.21, 3e-05, 76, -235.63, -19.62, 0.01074, 77, -172.56, -19.62, 0.005, 6, 17, -19.96, -30.51, 0.32984, 18, 30.76, -8.3, 0.65047, 19, -21.53, -13.83, 0.00446, 20, -70.78, -8.36, 1e-05, 76, -247.63, -23.52, 0.01022, 77, -184.56, -23.52, 0.005, 6, 17, -35.82, -29.73, 0.02417, 18, 46.43, -10.89, 0.79005, 19, -5.7, -12.62, 0.16509, 20, -54.9, -8.27, 0.00638, 76, -263.48, -22.74, 0.0093, 77, -200.42, -22.74, 0.005, 6, 17, -51.04, -28.12, 0.00576, 18, 61.36, -14.23, 0.17322, 19, 9.59, -12.29, 0.76063, 20, -39.62, -9.04, 0.04689, 76, -278.7, -21.13, 0.00849, 77, -215.63, -21.13, 0.005, 6, 17, -66.58, -25.44, 0.00034, 18, 76.5, -18.67, 0.0186, 19, 25.36, -13, 0.73705, 20, -23.95, -10.86, 0.23098, 76, -294.25, -18.44, 0.00803, 77, -231.18, -18.44, 0.005, 4, 19, 40.65, -12.68, 0.20949, 20, -8.67, -11.63, 0.777, 76, -309.46, -16.84, 0.0085, 77, -246.39, -16.84, 0.005, 3, 20, 8.14, -12.28, 0.98773, 76, -326.21, -15.25, 0.00727, 77, -263.14, -15.25, 0.005, 4, 20, 22.74, -11.38, 0.97398, 21, -18.19, -8.71, 0.01505, 76, -340.84, -15.36, 0.00597, 77, -277.77, -15.36, 0.005, 4, 20, 36.71, -9.35, 0.68711, 21, -4.07, -8.83, 0.30322, 76, -354.9, -16.62, 0.00467, 77, -291.83, -16.62, 0.005], "hull": 56, "edges": [0, 110, 0, 2, 24, 26, 42, 44, 44, 46, 54, 56, 56, 58, 58, 60, 82, 84, 102, 104, 108, 110, 96, 98, 92, 94, 94, 96, 90, 92, 88, 90, 84, 86, 86, 88, 78, 80, 80, 82, 76, 78, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 50, 52, 52, 54, 46, 48, 48, 50, 40, 42, 36, 38, 38, 40, 26, 28, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 74, 76, 70, 72, 72, 74, 14, 16, 10, 12, 12, 14, 6, 8, 8, 10, 2, 4, 4, 6, 104, 106, 106, 108, 98, 100, 100, 102], "width": 90, "height": 251}}, "hair_FL": {"hair_FL": {"type": "mesh", "uvs": [0.16414, 0.00306, 0.28062, 0.00424, 0.40772, 0.03614, 0.50427, 0.07734, 0.57629, 0.13695, 0.65509, 0.21561, 0.71012, 0.3041, 0.74818, 0.38596, 0.78367, 0.4623, 0.80165, 0.52885, 0.87424, 0.57476, 0.99887, 0.59415, 0.99774, 0.60794, 0.90132, 0.63438, 0.79737, 0.62952, 0.78723, 0.72508, 0.77406, 0.82237, 0.76717, 0.91098, 0.77837, 1, 0.75656, 0.99707, 0.70245, 0.91668, 0.6634, 0.8321, 0.66218, 0.72689, 0.66097, 0.62169, 0.65432, 0.54743, 0.53682, 0.48146, 0.45928, 0.41053, 0.4001, 0.31734, 0.35618, 0.23212, 0.31452, 0.1585, 0.25615, 0.11183, 0.17968, 0.09995, 0.11887, 0.13465, 0.06733, 0.2058, 0.01654, 0.20568, 0.01367, 0.10183, 0.07733, 0.03333], "triangles": [16, 20, 21, 17, 20, 16, 19, 20, 17, 19, 17, 18, 22, 23, 15, 16, 22, 15, 21, 22, 16, 24, 8, 9, 11, 13, 10, 14, 23, 24, 9, 14, 24, 14, 9, 10, 12, 13, 11, 14, 10, 13, 15, 23, 14, 26, 6, 7, 25, 26, 7, 25, 7, 8, 24, 25, 8, 5, 27, 28, 5, 28, 4, 27, 5, 6, 26, 27, 6, 30, 1, 2, 32, 35, 36, 31, 32, 36, 33, 34, 35, 32, 33, 35, 31, 0, 1, 36, 0, 31, 30, 31, 1, 29, 30, 2, 29, 2, 3, 28, 29, 3, 28, 3, 4], "vertices": [2, 43, 24.39, -12.87, 0.99894, 76, -213.28, 103.62, 0.00106, 2, 43, 23.94, -24.97, 0.99929, 76, -213.73, 91.51, 0.00071, 3, 43, 19.11, -38.08, 0.77854, 49, 4.77, 9.6, 0.22114, 76, -218.57, 78.4, 0.00032, 4, 43, 13.02, -47.99, 0.5479, 49, 16.16, 11.93, 0.44311, 50, -11.92, 8.28, 0.00835, 76, -224.65, 68.5, 0.00064, 4, 43, 4.39, -55.28, 0.33427, 49, 27.39, 10.6, 0.28245, 50, -0.94, 10.96, 0.38207, 76, -233.29, 61.21, 0.0012, 6, 43, -6.97, -63.21, 0.07038, 49, 40.94, 7.73, 7e-05, 50, 12.76, 13.01, 0.92708, 51, -15.15, 13.06, 0.00082, 76, -244.64, 53.27, 0.00155, 77, -181.58, 53.27, 0.00011, 4, 50, 26.55, 12.23, 0.62287, 51, -1.36, 12.23, 0.37236, 76, -257.34, 47.84, 0.00334, 77, -194.27, 47.84, 0.00143, 4, 50, 38.68, 10.32, 0.02259, 51, 10.76, 10.28, 0.97, 76, -269.05, 44.16, 0.00488, 77, -205.98, 44.16, 0.00254, 4, 51, 22.07, 8.46, 0.83577, 52, -5.48, 6.83, 0.15464, 76, -279.97, 40.72, 0.00605, 77, -216.91, 40.72, 0.00354, 4, 51, 31.29, 5.69, 0.03978, 52, 4.08, 8.03, 0.9492, 76, -289.47, 39.07, 0.00672, 77, -226.4, 39.07, 0.0043, 3, 52, 11.12, 15.09, 0.98889, 76, -296.16, 31.67, 0.00579, 77, -233.09, 31.67, 0.00531, 3, 52, 14.8, 27.82, 0.99034, 76, -299.21, 18.78, 0.00314, 77, -236.15, 18.78, 0.00652, 3, 52, 16.74, 27.57, 0.99015, 76, -301.17, 18.94, 0.00321, 77, -238.1, 18.94, 0.00664, 3, 52, 19.77, 17.3, 0.98816, 76, -304.69, 29.05, 0.0058, 77, -241.62, 29.05, 0.00604, 4, 52, 18.31, 6.56, 0.96459, 53, -5.3, 5.95, 0.02335, 76, -303.75, 39.84, 0.00699, 77, -240.68, 39.84, 0.00508, 3, 53, 8.3, 5.61, 0.98714, 76, -317.29, 41.21, 0.00715, 77, -254.22, 41.21, 0.00571, 4, 53, 22.17, 4.97, 0.77246, 54, -1.27, 5.46, 0.21424, 76, -331.07, 42.9, 0.00709, 77, -268, 42.9, 0.00621, 3, 54, 10.92, 2.26, 0.98581, 76, -343.63, 43.91, 0.00781, 77, -280.56, 43.91, 0.00638, 3, 54, 23.54, 0.88, 0.98514, 76, -356.3, 43.04, 0.00855, 77, -293.23, 43.04, 0.00631, 3, 54, 22.68, -1.26, 0.98556, 76, -355.83, 45.3, 0.00834, 77, -292.76, 45.3, 0.00611, 3, 54, 10.37, -4.5, 0.98661, 76, -344.28, 50.66, 0.00769, 77, -281.22, 50.66, 0.0057, 4, 53, 24.16, -6.45, 0.67047, 54, -2.2, -6.09, 0.31723, 76, -332.18, 54.44, 0.00716, 77, -269.11, 54.44, 0.00515, 4, 52, 31.1, -8.45, 0.00677, 53, 9.24, -7.36, 0.98153, 76, -317.24, 54.22, 0.00714, 77, -254.18, 54.22, 0.00457, 4, 52, 16.19, -7.51, 0.94148, 53, -5.67, -8.27, 0.04743, 76, -302.31, 54, 0.00726, 77, -239.24, 54, 0.00383, 4, 51, 26.44, -9.08, 0.25242, 52, 5.62, -7.45, 0.73681, 76, -291.75, 54.45, 0.0076, 77, -228.68, 54.45, 0.00317, 5, 50, 40.44, -15.45, 0.06236, 51, 12.44, -15.49, 0.91596, 52, -4.59, -18.97, 0.01206, 76, -282.1, 66.45, 0.008, 77, -219.03, 66.45, 0.00162, 4, 50, 27.77, -17.9, 0.52361, 51, -0.23, -17.9, 0.46761, 76, -271.84, 74.27, 0.0084, 77, -208.77, 74.27, 0.00037, 5, 43, -20.79, -36.36, 0.18736, 49, 30.78, -20.71, 0.05372, 50, 13.19, -17.18, 0.72345, 51, -14.81, -17.13, 0.02621, 76, -258.47, 80.12, 0.00926, 4, 43, -8.59, -32.08, 0.43277, 49, 19.31, -14.73, 0.30476, 50, 0.36, -15.59, 0.25358, 76, -246.27, 84.41, 0.00889, 4, 43, 1.96, -27.99, 0.63993, 49, 9.12, -9.81, 0.34224, 50, -10.91, -14.55, 0.01044, 76, -235.71, 88.49, 0.00739, 3, 43, 8.73, -22.07, 0.88327, 49, 0.17, -8.91, 0.1114, 76, -228.95, 94.41, 0.00533, 2, 43, 10.6, -14.16, 0.99667, 76, -227.08, 102.32, 0.00333, 2, 43, 5.82, -7.73, 0.99748, 76, -231.86, 108.76, 0.00252, 2, 43, -4.16, -2.13, 0.99719, 76, -241.83, 114.35, 0.00281, 2, 43, -4.02, 3.15, 0.99716, 76, -241.69, 119.63, 0.00284, 2, 43, 10.73, 3.1, 0.99798, 76, -226.94, 119.59, 0.00202, 2, 43, 20.3, -3.74, 0.99856, 76, -217.37, 112.74, 0.00144], "hull": 37, "edges": [16, 18, 22, 24, 24, 26, 26, 28, 36, 38, 38, 40, 40, 42, 66, 68, 68, 70, 70, 72, 62, 64, 64, 66, 58, 60, 60, 62, 52, 54, 50, 52, 46, 48, 48, 50, 42, 44, 44, 46, 28, 30, 30, 32, 32, 34, 34, 36, 18, 20, 20, 22, 14, 16, 10, 12, 12, 14, 6, 8, 8, 10, 2, 4, 4, 6, 54, 56, 56, 58, 2, 0, 0, 72], "width": 104, "height": 142}}, "hair_FR": {"hair_FR": {"type": "mesh", "uvs": [0.55562, 0.01107, 0.70621, 0.00646, 0.85663, 0.03178, 0.96727, 0.08901, 0.98634, 0.19731, 0.98258, 0.26869, 0.98115, 0.29578, 0.71902, 0.35348, 0.59515, 0.37754, 0.47049, 0.42875, 0.3848, 0.49647, 0.40082, 0.59564, 0.39523, 0.66415, 0.29933, 0.76262, 0.22022, 0.82345, 0.18589, 0.87728, 0.19823, 0.92866, 0.26504, 0.98798, 0.20081, 0.98614, 0.12126, 0.94632, 0.06193, 0.88133, 0.04656, 0.80234, 0.12774, 0.70941, 0.1946, 0.63677, 0.05142, 0.53218, 0, 0.43655, 0.0499, 0.33187, 0.14436, 0.24971, 0.24517, 0.18958, 0.27004, 0.14282, 0.39331, 0.07198, 0.91761, 0.26781, 0.66181, 0.29503, 0.48275, 0.34548, 0.38279, 0.41628, 0.81486, 0.16865, 0.60681, 0.12045, 0.693, 0.21424, 0.479, 0.21033, 0.2977, 0.28589, 0.25906, 0.3888, 0.27986, 0.50213, 0.41361, 0.13578], "triangles": [16, 19, 20, 16, 20, 15, 17, 18, 16, 19, 16, 18, 14, 21, 13, 14, 20, 21, 15, 20, 14, 12, 23, 11, 12, 22, 23, 13, 22, 12, 21, 22, 13, 41, 34, 10, 25, 40, 41, 24, 25, 41, 41, 10, 11, 23, 24, 41, 23, 41, 11, 40, 26, 39, 25, 26, 40, 10, 34, 9, 9, 34, 33, 26, 27, 39, 40, 39, 33, 8, 9, 33, 33, 39, 38, 8, 33, 32, 38, 42, 36, 30, 0, 36, 38, 36, 37, 32, 38, 37, 7, 8, 32, 36, 0, 1, 36, 1, 2, 35, 36, 2, 35, 2, 3, 35, 3, 4, 37, 36, 35, 31, 35, 4, 37, 35, 31, 5, 31, 4, 32, 37, 31, 6, 31, 5, 7, 32, 31, 7, 31, 6, 42, 30, 36, 32, 33, 38, 29, 30, 42, 28, 42, 38, 39, 28, 38, 42, 28, 29, 27, 28, 39, 34, 40, 33, 41, 40, 34], "vertices": [3, 43, 42.99, 28.33, 0.99353, 76, -194.68, 144.82, 0.00194, 77, -131.61, 144.82, 0.00453, 3, 43, 43.49, 17.63, 0.99429, 76, -194.18, 134.11, 0.00255, 77, -131.12, 134.11, 0.00316, 3, 43, 39.14, 7.05, 0.99514, 76, -198.53, 123.53, 0.00288, 77, -135.46, 123.53, 0.00198, 3, 43, 29.69, -0.59, 0.99639, 76, -207.98, 115.89, 0.00256, 77, -144.92, 115.89, 0.00105, 2, 43, 12.12, -1.54, 0.99735, 76, -225.55, 114.94, 0.00265, 2, 43, 0.57, -1, 0.99711, 76, -237.11, 115.48, 0.00289, 2, 43, -3.82, -0.8, 0.99701, 76, -241.49, 115.68, 0.00299, 3, 43, -12.73, 18.02, 0.99662, 76, -250.41, 134.51, 0.00181, 77, -187.34, 134.51, 0.00157, 5, 43, -16.42, 26.91, 0.85518, 44, 8.59, 18.89, 0.1396, 45, -7.28, 25.5, 0.00029, 76, -254.1, 143.39, 0.00125, 77, -191.03, 143.39, 0.00369, 5, 43, -24.51, 35.95, 0.50532, 44, 19.63, 13.86, 0.40927, 45, 0, 15.79, 0.07832, 76, -262.19, 152.43, 0.00063, 77, -199.12, 152.43, 0.00646, 5, 43, -35.34, 42.28, 0.24617, 44, 32.09, 12.41, 0.07753, 45, 10.24, 8.55, 0.667, 76, -273.01, 158.77, 0.00016, 77, -209.95, 158.77, 0.00914, 3, 45, 26.34, 7.93, 0.95435, 46, -7.13, 5.42, 0.03449, 77, -226.03, 158, 0.01116, 3, 45, 37.33, 6.32, 0.11235, 46, 3.51, 8.61, 0.8749, 77, -237.12, 158.66, 0.01275, 3, 46, 20.8, 7.28, 0.86194, 47, -2.84, 7.51, 0.12191, 77, -252.91, 165.84, 0.01615, 3, 46, 31.94, 5.12, 0.00726, 47, 8.09, 4.51, 0.97431, 77, -262.63, 171.68, 0.01844, 3, 47, 17.15, 4.31, 0.4331, 48, 1.95, 3.92, 0.54691, 77, -271.29, 174.32, 0.01999, 2, 48, 9.97, 1.53, 0.97894, 77, -279.63, 173.64, 0.02106, 2, 48, 20.67, 2.21, 0.97783, 77, -289.35, 169.12, 0.02217, 2, 48, 18.64, -1.89, 0.97728, 77, -288.95, 173.67, 0.02272, 2, 48, 10.51, -4.62, 0.9776, 77, -282.37, 179.17, 0.0224, 3, 47, 19.96, -4.06, 0.42082, 48, -0.83, -4.46, 0.55732, 77, -271.74, 183.13, 0.02185, 3, 46, 32.65, -7.66, 0.00683, 47, 7.83, -8.28, 0.97265, 77, -258.93, 183.93, 0.02052, 3, 46, 16.55, -7.03, 0.97751, 47, -8.18, -6.42, 0.00511, 77, -244.01, 177.82, 0.01738, 3, 45, 31.36, -7.35, 0.26815, 46, 3.88, -6.31, 0.717, 77, -232.35, 172.8, 0.01485, 3, 44, 46.45, -7.27, 0.00842, 45, 13.41, -15.61, 0.97732, 77, -215.18, 182.57, 0.01426, 5, 43, -25, 69.37, 0.06318, 44, 33.52, -16.55, 0.39796, 45, -2.39, -17.55, 0.52647, 46, -22.41, -29.8, 0, 77, -199.61, 185.86, 0.01239, 5, 43, -8.13, 65.44, 0.29678, 44, 16.49, -19.73, 0.67504, 45, -18.86, -12.18, 0.01894, 46, -39.61, -31.88, 0, 77, -182.74, 181.92, 0.00924, 4, 43, 5.02, 58.42, 0.58252, 44, 1.63, -18.59, 0.41144, 46, -54.37, -29.8, 0, 77, -169.59, 174.91, 0.00604, 4, 43, 14.59, 51.04, 0.88164, 44, -10.11, -15.68, 0.113, 76, -223.08, 167.52, 0.00034, 77, -160.01, 167.52, 0.00502, 3, 43, 22.13, 49.1, 0.99396, 76, -215.55, 165.58, 0.00049, 77, -152.48, 165.58, 0.00555, 3, 43, 33.4, 40.08, 0.99345, 76, -204.28, 156.57, 0.00114, 77, -141.21, 156.57, 0.00541, 3, 43, 0.82, 3.6, 0.99616, 76, -236.86, 120.09, 0.00285, 77, -173.79, 120.09, 0.00099, 3, 43, -3.17, 21.86, 0.99616, 76, -240.85, 138.35, 0.00179, 77, -177.78, 138.35, 0.00205, 5, 43, -11.05, 34.76, 0.82087, 44, 6.83, 9.53, 0.1711, 45, -13.31, 18.13, 0.00011, 76, -248.72, 151.25, 0.00092, 77, -185.65, 151.25, 0.007, 5, 43, -22.35, 42.12, 0.48788, 44, 20.13, 7.33, 0.43282, 45, -2.69, 9.82, 0.06881, 76, -260.02, 158.61, 0.00031, 77, -196.96, 158.61, 0.01018, 3, 43, 17.04, 10.52, 0.99712, 76, -220.63, 127.01, 0.00269, 77, -157.56, 127.01, 0.00019, 3, 43, 25.19, 25.11, 0.99535, 76, -212.48, 141.6, 0.00189, 77, -149.41, 141.6, 0.00276, 3, 43, 9.86, 19.35, 0.99599, 76, -227.81, 135.83, 0.00291, 77, -164.74, 135.83, 0.00109, 3, 43, 10.85, 34.52, 0.99374, 76, -226.83, 151.01, 0.00348, 77, -163.76, 151.01, 0.00278, 5, 43, -1.09, 47.67, 0.6587, 44, 2.9, -6.3, 0.33309, 46, -52.31, -17.61, 0, 76, -238.76, 164.16, 0.00324, 77, -175.7, 164.16, 0.00497, 5, 43, -17.69, 50.8, 0.44545, 44, 19.36, -2.48, 0.54356, 46, -35.64, -14.86, 0, 76, -255.37, 167.29, 0.00296, 77, -192.3, 167.29, 0.00804, 5, 43, -36.08, 49.75, 0.17167, 44, 35.78, 5.87, 0.00289, 45, 10.34, 1.04, 0.81271, 76, -273.76, 166.24, 0.0021, 77, -210.69, 166.24, 0.01063, 3, 43, 23.03, 38.88, 0.99453, 76, -214.65, 155.37, 0.00108, 77, -151.58, 155.37, 0.00439], "hull": 31, "edges": [2, 4, 4, 6, 32, 34, 34, 36, 36, 38, 46, 48, 48, 50, 58, 60, 12, 14, 14, 16, 6, 8, 2, 0, 0, 60, 56, 58, 54, 56, 50, 52, 52, 54, 42, 44, 44, 46, 24, 26, 26, 28, 38, 40, 40, 42, 28, 30, 30, 32, 20, 22, 22, 24, 16, 18, 18, 20, 8, 10, 10, 12], "width": 71, "height": 162}}, "head": {"head": {"type": "mesh", "uvs": [0.55852, 0.04824, 0.6257, 0.1339, 0.68244, 0.26402, 0.73328, 0.3929, 0.76416, 0.4642, 0.7895, 0.47913, 0.81456, 0.40633, 0.87364, 0.351, 0.94198, 0.34912, 0.99512, 0.41725, 0.98724, 0.52978, 0.9343, 0.62035, 0.84104, 0.68293, 0.78693, 0.67766, 0.72676, 0.79049, 0.65927, 0.84341, 0.50943, 0.9164, 0.3349, 0.98701, 0.25324, 0.99444, 0.19295, 0.97478, 0.1543, 0.92621, 0.13476, 0.87714, 0.0824, 0.77521, 0.03003, 0.67328, 0.02135, 0.5833, 0.03605, 0.49598, 0.04005, 0.44886, 0.01283, 0.38418, 0.00933, 0.33998, 0.01018, 0.30436, 0.02192, 0.21387, 0.05135, 0.12387, 0.09123, 0.07452, 0.16841, 0.03438, 0.27022, 0.00806, 0.31682, 0.00569, 0.40019, 0.00328, 0.49042, 0.01534, 0.27844, 0.28815, 0.44066, 0.24053, 0.48122, 0.2393, 0.53747, 0.26617, 0.59896, 0.30402, 0.58457, 0.31135, 0.53289, 0.28754, 0.48253, 0.27044, 0.28236, 0.32472, 0.31965, 0.32661, 0.44982, 0.2741, 0.40632, 0.29303, 0.36102, 0.31348, 0.35824, 0.26189, 0.31441, 0.27563, 0.40076, 0.24816, 0.15124, 0.30177, 0.15451, 0.34145, 0.12224, 0.34295, 0.04331, 0.32558, 0.04135, 0.292, 0.07274, 0.29322, 0.11199, 0.29689, 0.07994, 0.33443, 0.04677, 0.41783, 0.06374, 0.40082, 0.05723, 0.43316, 0.06772, 0.41932, 0.08725, 0.40582, 0.11582, 0.40413, 0.14185, 0.41392, 0.16536, 0.43181, 0.18344, 0.45375, 0.16862, 0.46455, 0.14149, 0.47535, 0.11365, 0.48075, 0.08472, 0.48007, 0.06519, 0.46961, 0.05687, 0.45239, 0.08906, 0.38919, 0.1198, 0.39156, 0.14692, 0.40506, 0.17115, 0.42362, 0.19031, 0.44691, 0.06586, 0.50202, 0.10889, 0.50641, 0.14252, 0.4946, 0.17037, 0.47942, 0.19134, 0.46186, 0.19003, 0.39343, 0.2657, 0.39343, 0.31041, 0.42423, 0.22786, 0.39343, 0.34064, 0.42063, 0.3617, 0.38781, 0.38048, 0.36577, 0.35662, 0.41784, 0.36865, 0.40059, 0.38328, 0.3799, 0.40878, 0.36375, 0.44554, 0.35899, 0.48131, 0.36136, 0.5131, 0.36992, 0.5419, 0.3873, 0.51958, 0.41789, 0.45327, 0.4348, 0.42044, 0.43479, 0.37427, 0.42852, 0.35677, 0.42852, 0.39278, 0.43268, 0.4867, 0.43178, 0.40694, 0.34934, 0.44664, 0.34561, 0.4816, 0.34762, 0.51791, 0.35624, 0.55476, 0.3706, 0.33949, 0.43925, 0.36482, 0.44326, 0.39787, 0.45848, 0.45669, 0.45848, 0.51465, 0.44806, 0.55501, 0.42723, 0.57003, 0.38676, 0.15762, 0.63826, 0.19238, 0.65248, 0.22126, 0.64026, 0.26859, 0.63951, 0.29828, 0.6163, 0.29854, 0.57954, 0.29429, 0.56441, 0.30235, 0.49432, 0.21307, 0.43604, 0.20601, 0.49466, 0.15843, 0.58469, 0.14827, 0.76961, 0.16608, 0.75858, 0.15272, 0.79336, 0.17307, 0.81651, 0.20488, 0.82423, 0.25385, 0.8177, 0.30092, 0.79633, 0.33145, 0.77377, 0.35031, 0.74862, 0.36446, 0.73731, 0.34239, 0.7435, 0.30907, 0.74481, 0.27202, 0.74217, 0.23479, 0.73794, 0.20587, 0.74355, 0.18285, 0.74362, 0.22929, 0.67741, 0.27202, 0.69836, 0.30612, 0.7185, 0.33979, 0.72857, 0.36008, 0.71044, 0.389, 0.72736, 0.38382, 0.75153, 0.20701, 0.68062, 0.18715, 0.69391, 0.16298, 0.68666, 0.14874, 0.69552, 0.13838, 0.72332, 0.13363, 0.74749, 0.13665, 0.76844, 0.06842, 0.57538, 0.07053, 0.66517, 0.11111, 0.77186, 0.151, 0.8389, 0.20494, 0.84675, 0.26869, 0.84021, 0.17201, 0.87094, 0.35104, 0.87102, 0.15169, 0.17435, 0.27292, 0.15675, 0.41992, 0.13225, 0.54164, 0.1269, 0.56736, 0.52762, 0.5583, 0.64363, 0.50262, 0.75389, 0.43666, 0.83608, 0.43727, 0.53698, 0.43601, 0.63912, 0.65638, 0.38658, 0.78354, 0.56736, 0.21071, 0.92229, 0.29349, 0.9211, 0.57582, 0.22683, 0.3858, 0.91345, 0.49157, 0.87042, 0.60141, 0.79574, 0.66557, 0.72159, 0.68339, 0.62344, 0.68166, 0.48584, 0.17052, 0.77614, 0.20126, 0.77887, 0.24176, 0.77386, 0.2808, 0.76293], "triangles": [173, 37, 0, 173, 0, 1, 184, 173, 1, 40, 172, 173, 40, 173, 184, 184, 1, 2, 42, 184, 2, 112, 111, 44, 111, 98, 110, 99, 111, 112, 99, 98, 111, 100, 99, 112, 113, 112, 43, 100, 112, 113, 180, 42, 2, 3, 180, 2, 120, 113, 43, 101, 100, 113, 101, 113, 120, 102, 100, 101, 119, 101, 120, 102, 101, 119, 108, 99, 100, 108, 100, 102, 104, 97, 98, 98, 103, 104, 99, 103, 98, 99, 108, 103, 118, 108, 102, 118, 102, 119, 117, 103, 108, 104, 103, 117, 117, 108, 118, 190, 180, 3, 190, 3, 4, 190, 174, 119, 118, 119, 174, 120, 43, 180, 119, 120, 180, 180, 190, 119, 9, 7, 8, 181, 4, 5, 190, 4, 181, 9, 6, 7, 10, 6, 9, 10, 5, 6, 10, 181, 5, 11, 181, 10, 189, 190, 181, 174, 190, 189, 118, 178, 117, 175, 174, 189, 12, 13, 181, 189, 181, 13, 11, 12, 181, 118, 174, 178, 175, 178, 174, 188, 175, 189, 188, 189, 13, 175, 179, 178, 187, 176, 175, 176, 179, 175, 153, 179, 176, 14, 188, 13, 188, 187, 175, 187, 188, 14, 15, 187, 14, 186, 177, 176, 186, 176, 187, 16, 186, 187, 16, 187, 15, 172, 36, 37, 172, 37, 173, 171, 34, 35, 33, 34, 171, 35, 36, 172, 171, 35, 172, 170, 33, 171, 53, 171, 172, 110, 48, 111, 109, 49, 110, 98, 109, 110, 97, 109, 98, 170, 171, 38, 93, 50, 109, 93, 109, 97, 96, 93, 97, 92, 93, 96, 92, 47, 93, 88, 90, 46, 95, 92, 96, 88, 47, 92, 94, 92, 95, 94, 91, 92, 91, 89, 92, 92, 89, 88, 107, 105, 95, 94, 95, 105, 106, 94, 105, 91, 94, 106, 104, 107, 96, 107, 95, 96, 104, 96, 97, 114, 89, 91, 114, 91, 106, 115, 106, 105, 114, 106, 115, 116, 107, 104, 115, 105, 107, 116, 115, 107, 116, 104, 117, 89, 129, 88, 128, 89, 114, 129, 90, 88, 89, 128, 129, 178, 116, 117, 127, 130, 128, 178, 126, 127, 128, 114, 115, 128, 115, 116, 127, 126, 124, 128, 116, 178, 128, 178, 127, 179, 126, 178, 125, 126, 179, 152, 125, 179, 153, 152, 179, 141, 152, 153, 151, 152, 141, 154, 141, 153, 154, 153, 176, 177, 154, 176, 154, 140, 141, 177, 139, 154, 185, 177, 186, 185, 186, 16, 78, 77, 56, 87, 55, 90, 67, 77, 78, 79, 55, 87, 79, 78, 55, 67, 78, 79, 66, 63, 77, 66, 77, 67, 68, 67, 79, 65, 63, 66, 62, 63, 65, 80, 79, 87, 68, 79, 80, 69, 68, 80, 64, 62, 65, 129, 87, 90, 80, 87, 129, 81, 80, 129, 69, 80, 81, 26, 62, 64, 76, 26, 64, 70, 69, 81, 86, 81, 129, 70, 81, 86, 71, 69, 70, 65, 76, 64, 74, 75, 76, 68, 73, 67, 72, 68, 69, 72, 69, 71, 86, 85, 71, 86, 71, 70, 72, 71, 85, 73, 74, 65, 74, 76, 65, 73, 66, 67, 72, 73, 68, 73, 65, 66, 84, 72, 85, 73, 72, 84, 130, 86, 129, 85, 86, 130, 75, 25, 26, 75, 26, 76, 82, 75, 74, 25, 75, 82, 83, 74, 73, 83, 73, 84, 82, 74, 83, 128, 130, 129, 162, 82, 83, 162, 24, 25, 162, 25, 82, 130, 131, 84, 130, 84, 85, 131, 130, 127, 83, 84, 131, 162, 83, 131, 127, 124, 131, 121, 162, 131, 131, 123, 121, 126, 125, 124, 124, 123, 131, 122, 121, 123, 163, 162, 121, 24, 162, 163, 23, 24, 163, 148, 123, 124, 155, 122, 123, 148, 155, 123, 157, 121, 122, 156, 157, 122, 158, 163, 121, 155, 156, 122, 157, 158, 121, 150, 149, 124, 148, 124, 149, 125, 150, 124, 152, 150, 125, 159, 163, 158, 151, 150, 152, 145, 148, 149, 155, 148, 145, 156, 155, 145, 144, 145, 149, 144, 149, 150, 142, 151, 141, 146, 156, 145, 156, 158, 157, 147, 156, 146, 147, 158, 156, 159, 158, 147, 143, 150, 151, 143, 151, 142, 144, 150, 143, 159, 164, 163, 133, 160, 159, 140, 142, 141, 147, 133, 159, 194, 144, 143, 132, 161, 160, 133, 132, 160, 160, 164, 159, 164, 160, 161, 139, 143, 142, 139, 142, 140, 193, 145, 144, 193, 144, 194, 146, 145, 193, 22, 163, 164, 23, 163, 22, 191, 133, 147, 132, 133, 191, 192, 147, 146, 192, 146, 193, 191, 147, 192, 134, 132, 191, 139, 138, 194, 139, 194, 143, 135, 191, 192, 134, 191, 135, 137, 193, 194, 137, 194, 138, 136, 192, 193, 136, 193, 137, 135, 192, 136, 134, 161, 132, 165, 134, 135, 134, 164, 161, 165, 164, 134, 167, 137, 138, 166, 136, 137, 166, 137, 167, 166, 168, 165, 135, 166, 165, 166, 135, 136, 140, 154, 139, 177, 169, 139, 138, 139, 169, 167, 138, 169, 165, 22, 164, 21, 165, 168, 21, 22, 165, 185, 169, 177, 183, 167, 169, 183, 169, 185, 182, 166, 167, 182, 167, 183, 168, 166, 182, 20, 21, 168, 20, 168, 182, 19, 20, 182, 17, 183, 185, 17, 185, 16, 18, 182, 183, 18, 183, 17, 19, 182, 18, 170, 32, 33, 31, 32, 170, 30, 31, 170, 170, 59, 30, 29, 30, 58, 28, 29, 57, 27, 28, 57, 63, 61, 77, 27, 57, 63, 62, 27, 63, 26, 27, 62, 59, 58, 30, 170, 60, 59, 57, 58, 59, 29, 58, 57, 61, 59, 60, 57, 59, 61, 63, 57, 61, 46, 90, 55, 38, 54, 170, 170, 54, 60, 55, 54, 46, 56, 60, 54, 56, 54, 55, 61, 60, 56, 77, 61, 56, 55, 78, 56, 52, 171, 51, 38, 171, 52, 52, 51, 50, 47, 46, 38, 52, 47, 38, 54, 38, 46, 50, 47, 52, 93, 47, 50, 47, 88, 46, 39, 172, 40, 53, 172, 39, 51, 171, 53, 48, 39, 40, 48, 49, 53, 48, 53, 39, 51, 53, 49, 50, 51, 49, 110, 49, 48, 50, 49, 109, 41, 40, 184, 45, 40, 41, 48, 40, 45, 44, 45, 41, 41, 184, 42, 43, 44, 41, 42, 43, 41, 111, 48, 45, 111, 45, 44, 112, 44, 43, 43, 42, 180], "vertices": [2, 9, 116.33, 3.52, 0.97831, 76, -245.28, 71.59, 0.02169, 2, 9, 100.65, -7.4, 0.98034, 76, -260.96, 60.67, 0.01966, 2, 9, 77.02, -16.39, 0.98075, 76, -284.6, 51.68, 0.01925, 2, 9, 53.63, -24.39, 0.98305, 76, -307.99, 43.68, 0.01695, 2, 9, 40.67, -29.28, 0.98424, 76, -320.94, 38.79, 0.01576, 2, 9, 37.89, -33.47, 0.9853, 76, -323.73, 34.6, 0.0147, 2, 9, 50.89, -37.98, 0.98544, 76, -310.72, 30.08, 0.01456, 2, 9, 60.62, -48.14, 0.98873, 76, -301, 19.93, 0.01127, 2, 9, 60.69, -59.62, 0.99185, 76, -300.92, 8.44, 0.00815, 2, 9, 48.22, -68.26, 0.99434, 76, -313.39, -0.2, 0.00566, 2, 9, 28, -66.47, 0.99417, 76, -333.61, 1.6, 0.00583, 2, 9, 11.91, -57.2, 0.99288, 76, -349.7, 10.87, 0.00712, 2, 9, 1.01, -41.28, 0.98801, 76, -360.6, 26.79, 0.01199, 2, 9, 2.17, -32.21, 0.98709, 76, -359.44, 35.86, 0.01291, 2, 9, -17.9, -21.63, 0.98376, 76, -379.51, 46.44, 0.01624, 2, 9, -27.16, -10.08, 0.98081, 76, -388.77, 57.99, 0.01919, 2, 9, -39.71, 15.4, 0.97544, 76, -401.32, 83.46, 0.02456, 2, 9, -51.73, 45, 0.96982, 76, -413.35, 113.07, 0.03018, 2, 9, -52.75, 58.75, 0.97067, 76, -414.36, 126.82, 0.02933, 2, 9, -48.98, 68.79, 0.97342, 76, -410.59, 136.86, 0.02658, 2, 9, -40.09, 75.08, 0.97571, 76, -401.7, 143.15, 0.02429, 2, 9, -31.18, 78.16, 0.97764, 76, -392.79, 146.23, 0.02236, 2, 9, -12.63, 86.53, 0.98042, 76, -374.25, 154.6, 0.01958, 2, 9, 5.91, 94.9, 0.98311, 76, -355.7, 162.97, 0.01689, 2, 9, 22.14, 95.98, 0.98392, 76, -339.47, 164.05, 0.01608, 2, 9, 37.8, 93.15, 0.98542, 76, -323.82, 161.21, 0.01458, 2, 9, 46.26, 92.28, 0.98557, 76, -315.35, 160.35, 0.01443, 2, 9, 58.01, 96.58, 0.98576, 76, -303.61, 164.65, 0.01424, 2, 9, 65.97, 96.98, 0.98494, 76, -295.64, 165.05, 0.01506, 2, 9, 72.38, 96.69, 0.98558, 76, -289.23, 164.76, 0.01442, 2, 9, 88.62, 94.34, 0.98615, 76, -273, 162.41, 0.01385, 2, 9, 104.7, 89.02, 0.98535, 76, -256.92, 157.09, 0.01465, 2, 9, 113.42, 82.12, 0.98216, 76, -248.19, 150.18, 0.01784, 2, 9, 120.34, 68.99, 0.9804, 76, -241.27, 137.05, 0.0196, 2, 9, 124.68, 51.78, 0.97849, 76, -236.93, 119.84, 0.02151, 2, 9, 124.93, 43.94, 0.97766, 76, -236.69, 112.01, 0.02234, 2, 9, 125.04, 29.93, 0.97713, 76, -236.58, 98, 0.02287, 2, 9, 122.51, 14.82, 0.97825, 76, -239.1, 82.89, 0.02175, 3, 11, 32.17, -3.46, 0.01999, 12, 12.13, -4.49, 0.97511, 77, -224.3, 119.63, 0.0049, 3, 10, 25.65, -0.36, 0.04392, 11, 3.6, -3.52, 0.95589, 12, -16.34, -2.16, 0.00019, 2, 10, 19.46, -3.21, 0.74655, 11, -2.97, -1.69, 0.25345, 2, 10, 8.87, -2.44, 0.99862, 77, -221.35, 76.04, 0.00138, 3, 10, -3.3, -0.19, 0.99119, 12, -36.67, 18.45, 0.00056, 77, -228.4, 65.87, 0.00825, 4, 10, -1.59, 1.97, 0.98826, 11, -15.68, 15.86, 0.0025, 12, -33.93, 18.76, 0.00293, 77, -229.67, 68.31, 0.0063, 4, 10, 8.08, 1.4, 0.9916, 11, -8.67, 9.18, 0.00597, 12, -27.51, 11.52, 0.00115, 77, -225.18, 76.89, 0.00127, 3, 10, 17.07, 1.86, 0.8927, 11, -1.51, 3.72, 0.10672, 12, -20.83, 5.48, 0.00058, 3, 10, 44.24, 23.95, 0.00319, 12, 14, 1.85, 0.99175, 77, -230.89, 119.13, 0.00506, 4, 10, 38.34, 21.83, 0.01469, 11, 27.62, 5.21, 0.00418, 12, 8.33, 4.53, 0.97732, 77, -231.38, 112.87, 0.00381, 3, 10, 21.88, 4.61, 0.14022, 11, 3.93, 2.71, 0.85919, 12, -15.49, 4.02, 0.00059, 4, 10, 27.28, 10.59, 0.0431, 11, 11.92, 3.78, 0.94551, 12, -7.44, 4.42, 0.0107, 77, -225.67, 98.18, 0.00069, 4, 10, 32.86, 16.95, 0.02679, 11, 20.28, 5.03, 0.38529, 12, 1, 4.96, 0.58557, 77, -229.18, 105.87, 0.00235, 3, 11, 17.96, -3.98, 0.71262, 12, -2.07, -3.82, 0.28536, 77, -219.88, 106.12, 0.00202, 3, 11, 25.72, -3.81, 0.07569, 12, 5.68, -4.3, 0.92071, 77, -222.18, 113.54, 0.0036, 3, 11, 10.4, -4.2, 0.98437, 12, -9.62, -3.42, 0.01514, 77, -217.58, 98.92, 0.00049, 3, 14, 8.74, 3.22, 0.98082, 13, 18.26, 4.78, 0.01766, 76, -289.32, 141.05, 0.00153, 3, 14, 9.61, -3.89, 0.99408, 13, 20.43, -2.05, 0.00428, 76, -296.47, 140.67, 0.00164, 3, 14, 4.21, -4.41, 0.92915, 13, 15.22, -3.55, 0.06975, 76, -296.62, 146.1, 0.0011, 2, 13, 1.59, -3.55, 0.99142, 77, -230.12, 159.28, 0.00858, 3, 14, -9.78, 4.14, 0.01436, 13, -0.11, 2.26, 0.97515, 77, -224.06, 159.47, 0.01048, 4, 14, -4.5, 4.16, 0.07094, 13, 5.07, 3.25, 0.92703, 76, -287.48, 154.2, 0.0002, 77, -224.41, 154.2, 0.00183, 4, 14, 2.11, 3.8, 0.78926, 13, 11.64, 4.12, 0.20974, 76, -288.29, 147.63, 0.00086, 77, -225.22, 147.63, 0.00014, 4, 14, -2.96, -3.2, 0.22763, 13, 7.95, -3.69, 0.77053, 76, -294.92, 153.17, 0.00039, 77, -231.85, 153.17, 0.00145, 2, 9, 51.82, 91.02, 0.98218, 76, -309.8, 159.09, 0.01782, 2, 9, 54.81, 88.1, 0.9797, 76, -306.8, 156.17, 0.0203, 2, 9, 49.02, 89.32, 0.98099, 76, -312.6, 157.39, 0.01901, 2, 9, 51.47, 87.5, 0.97932, 76, -310.15, 155.57, 0.02068, 2, 9, 53.82, 84.17, 0.9766, 76, -307.79, 152.24, 0.0234, 2, 9, 54.01, 79.36, 0.97558, 76, -307.6, 147.43, 0.02442, 2, 9, 52.15, 75.03, 0.97465, 76, -309.46, 143.1, 0.02535, 2, 9, 48.84, 71.16, 0.97386, 76, -312.78, 139.23, 0.02614, 2, 9, 44.82, 68.21, 0.97323, 76, -316.79, 136.28, 0.02677, 2, 9, 42.93, 70.75, 0.9738, 76, -318.68, 138.82, 0.0262, 2, 9, 41.1, 75.35, 0.9748, 76, -320.52, 143.42, 0.0252, 2, 9, 40.23, 80.05, 0.9758, 76, -321.38, 148.12, 0.0242, 2, 9, 40.47, 84.9, 0.978, 76, -321.15, 152.97, 0.022, 2, 9, 42.43, 88.14, 0.98042, 76, -319.19, 156.21, 0.01958, 2, 9, 45.56, 89.47, 0.98125, 76, -316.06, 157.53, 0.01875, 2, 9, 56.8, 83.8, 0.97654, 76, -304.81, 151.86, 0.02346, 2, 9, 56.26, 78.64, 0.97544, 76, -305.35, 146.71, 0.02456, 2, 9, 53.72, 74.14, 0.97446, 76, -307.89, 142.21, 0.02554, 2, 9, 50.29, 70.15, 0.97364, 76, -311.32, 138.22, 0.02636, 2, 9, 46.02, 67.03, 0.97299, 76, -315.59, 135.1, 0.02701, 2, 9, 36.59, 88.16, 0.98072, 76, -325.02, 156.23, 0.01928, 2, 9, 35.63, 80.95, 0.97598, 76, -325.98, 149.02, 0.02402, 2, 9, 37.63, 75.26, 0.97475, 76, -323.99, 143.32, 0.02525, 2, 9, 40.25, 70.52, 0.97376, 76, -321.36, 138.58, 0.02624, 2, 9, 43.33, 66.92, 0.97298, 76, -318.28, 134.99, 0.02702, 2, 9, 55.65, 66.85, 0.97292, 76, -305.97, 134.92, 0.02708, 2, 9, 55.35, 54.15, 0.97021, 76, -306.26, 122.21, 0.02979, 2, 9, 49.64, 46.77, 0.969, 76, -311.98, 114.83, 0.031, 2, 9, 55.5, 60.5, 0.97156, 76, -306.11, 128.57, 0.02844, 2, 9, 50.17, 41.67, 0.969, 76, -311.45, 109.74, 0.031, 2, 9, 55.99, 38, 0.969, 76, -305.62, 106.07, 0.031, 2, 9, 59.88, 34.75, 0.969, 76, -301.73, 102.82, 0.031, 2, 9, 50.61, 38.98, 0.969, 76, -311.01, 107.05, 0.031, 2, 9, 53.66, 36.88, 0.969, 76, -307.95, 104.95, 0.031, 2, 9, 57.33, 34.34, 0.969, 76, -304.29, 102.41, 0.031, 2, 9, 60.14, 29.99, 0.969, 76, -301.48, 98.06, 0.031, 2, 9, 60.85, 23.8, 0.969, 76, -300.76, 91.86, 0.031, 2, 9, 60.28, 17.8, 0.969, 76, -301.33, 85.87, 0.031, 2, 9, 58.62, 12.49, 0.969, 76, -303, 80.56, 0.031, 2, 9, 55.38, 7.73, 0.96905, 76, -306.23, 75.8, 0.03095, 2, 9, 49.96, 11.61, 0.969, 76, -311.65, 79.68, 0.031, 2, 9, 47.18, 22.81, 0.969, 76, -314.44, 90.88, 0.031, 2, 9, 47.31, 28.33, 0.969, 76, -314.31, 96.4, 0.031, 2, 9, 48.61, 36.06, 0.969, 76, -313, 104.13, 0.031, 2, 9, 48.68, 39, 0.969, 76, -312.93, 107.06, 0.031, 2, 9, 47.79, 32.96, 0.969, 76, -313.82, 101.03, 0.031, 2, 9, 47.59, 17.19, 0.969, 76, -314.02, 85.26, 0.031, 2, 9, 62.74, 30.24, 0.969, 76, -298.88, 98.31, 0.031, 2, 9, 63.25, 23.56, 0.969, 76, -298.36, 91.62, 0.031, 2, 9, 62.76, 17.69, 0.969, 76, -298.86, 85.76, 0.031, 2, 9, 61.06, 11.63, 0.969, 76, -300.55, 79.7, 0.031, 2, 9, 58.33, 5.5, 0.97008, 76, -303.28, 73.57, 0.02992, 2, 9, 46.82, 41.94, 0.969, 76, -314.79, 110.01, 0.031, 2, 9, 46, 37.71, 0.969, 76, -315.61, 105.77, 0.031, 2, 9, 43.13, 32.22, 0.969, 76, -318.48, 100.29, 0.031, 2, 9, 42.9, 22.34, 0.969, 76, -318.71, 90.41, 0.031, 2, 9, 44.55, 12.56, 0.969, 76, -317.06, 80.63, 0.031, 2, 9, 48.14, 5.7, 0.96991, 76, -313.47, 73.76, 0.03009, 2, 9, 55.37, 3, 0.97113, 76, -306.25, 71.07, 0.02887, 2, 9, 11.72, 73.32, 0.97236, 76, -349.9, 141.39, 0.02764, 2, 9, 9.02, 67.54, 0.97081, 76, -352.59, 135.61, 0.02919, 2, 9, 11.11, 62.64, 0.97013, 76, -350.5, 130.71, 0.02987, 2, 9, 11.06, 54.69, 0.96976, 76, -350.55, 122.76, 0.03024, 2, 9, 15.12, 49.61, 0.96882, 76, -346.49, 117.67, 0.03118, 2, 9, 21.74, 49.41, 0.96891, 76, -339.88, 117.48, 0.03109, 2, 9, 24.47, 50.06, 0.96911, 76, -337.14, 118.13, 0.03089, 2, 9, 37.06, 48.41, 0.969, 76, -324.56, 116.48, 0.031, 2, 9, 47.89, 63.16, 0.97216, 76, -313.72, 131.23, 0.02784, 2, 9, 37.37, 64.59, 0.97243, 76, -324.24, 132.66, 0.02757, 2, 9, 21.35, 72.96, 0.97325, 76, -340.26, 141.03, 0.02675, 2, 9, -11.88, 75.44, 0.97337, 76, -373.5, 143.51, 0.02663, 2, 9, -9.97, 72.4, 0.97233, 76, -371.58, 140.47, 0.02767, 2, 9, -16.17, 74.79, 0.97258, 76, -377.79, 142.86, 0.02742, 2, 9, -20.42, 71.47, 0.971, 76, -382.03, 139.54, 0.029, 2, 9, -21.93, 66.16, 0.96924, 76, -383.55, 134.23, 0.03076, 2, 9, -20.95, 57.91, 0.96759, 76, -382.56, 125.98, 0.03241, 2, 9, -17.29, 49.91, 0.96688, 76, -378.9, 117.98, 0.03312, 2, 9, -13.35, 44.69, 0.96748, 76, -374.96, 112.76, 0.03252, 2, 9, -8.89, 41.42, 0.96785, 76, -370.51, 109.49, 0.03215, 2, 9, -6.91, 39, 0.9679, 76, -368.53, 107.06, 0.0321, 2, 9, -7.94, 42.73, 0.96789, 76, -369.55, 110.8, 0.03211, 2, 9, -8.05, 48.33, 0.96777, 76, -369.66, 116.4, 0.03223, 2, 9, -7.43, 54.54, 0.96889, 76, -369.04, 122.61, 0.03111, 2, 9, -6.52, 60.78, 0.96972, 76, -368.13, 128.85, 0.03028, 2, 9, -7.42, 65.66, 0.97056, 76, -369.03, 133.73, 0.02944, 2, 9, -7.34, 69.52, 0.97141, 76, -368.95, 137.59, 0.02859, 2, 9, 4.39, 61.45, 0.96906, 76, -357.22, 129.52, 0.03094, 2, 9, 0.46, 54.36, 0.96873, 76, -361.16, 122.43, 0.03127, 2, 9, -3.3, 48.72, 0.96811, 76, -364.91, 116.78, 0.03189, 2, 9, -5.24, 43.1, 0.96795, 76, -366.86, 111.17, 0.03205, 2, 9, -2.06, 39.62, 0.96806, 76, -363.68, 107.69, 0.03194, 2, 9, -5.22, 34.83, 0.96792, 76, -366.83, 102.9, 0.03208, 2, 9, -9.55, 35.8, 0.9678, 76, -371.16, 103.87, 0.0322, 2, 9, 3.9, 65.2, 0.96949, 76, -357.71, 133.27, 0.03051, 2, 9, 1.59, 68.59, 0.97025, 76, -360.03, 136.66, 0.02975, 2, 9, 2.99, 72.62, 0.9709, 76, -358.63, 140.69, 0.0291, 2, 9, 1.45, 75.05, 0.97172, 76, -360.17, 143.12, 0.02828, 2, 9, -3.51, 76.91, 0.97286, 76, -365.13, 144.98, 0.02714, 2, 9, -7.84, 77.81, 0.97366, 76, -369.46, 145.88, 0.02634, 2, 9, -11.63, 77.39, 0.97398, 76, -373.24, 145.46, 0.02602, 2, 9, 23.38, 88.04, 0.97868, 76, -338.23, 156.11, 0.02132, 2, 9, 7.21, 88.06, 0.97879, 76, -354.4, 156.13, 0.02121, 2, 9, -12.14, 81.69, 0.97647, 76, -373.76, 149.76, 0.02353, 2, 9, -24.36, 75.27, 0.97314, 76, -385.98, 143.34, 0.02686, 2, 9, -25.98, 66.24, 0.97081, 76, -387.6, 134.31, 0.02919, 2, 9, -25.06, 55.51, 0.96859, 76, -386.67, 123.58, 0.03141, 2, 9, -30.21, 71.88, 0.97249, 76, -391.82, 139.94, 0.02751, 2, 9, -30.92, 41.81, 0.96737, 76, -392.54, 109.88, 0.03263, 2, 9, 95.22, 72.38, 0.97444, 76, -266.39, 140.45, 0.02556, 2, 9, 97.92, 51.94, 0.97136, 76, -263.7, 120.01, 0.02864, 2, 9, 101.75, 27.15, 0.97118, 76, -259.86, 95.22, 0.02882, 2, 9, 102.24, 6.69, 0.97419, 76, -259.38, 74.75, 0.02581, 2, 9, 30.03, 4.04, 0.96852, 76, -331.58, 72.11, 0.03148, 2, 9, 9.19, 6.05, 0.96929, 76, -352.43, 74.12, 0.03071, 2, 9, -10.44, 15.86, 0.96841, 76, -372.05, 83.93, 0.03159, 2, 9, -24.97, 27.28, 0.96855, 76, -386.58, 95.35, 0.03145, 2, 9, 28.85, 25.93, 0.9689, 76, -332.76, 94, 0.0311, 2, 9, 10.48, 26.57, 0.96838, 76, -351.14, 94.64, 0.03162, 2, 9, 55.06, -11.5, 0.97527, 76, -306.55, 56.57, 0.02473, 2, 9, 22.04, -32.1, 0.98624, 76, -339.58, 35.97, 0.01376, 2, 9, -39.6, 65.59, 0.96918, 76, -401.21, 133.66, 0.03082, 2, 9, -39.71, 51.68, 0.96687, 76, -401.32, 119.75, 0.03313, 2, 9, 84.12, 1.36, 0.9732, 76, -277.49, 69.43, 0.0268, 2, 9, -38.69, 36.15, 0.96807, 76, -400.31, 104.22, 0.03193, 2, 9, -31.36, 18.2, 0.97076, 76, -392.98, 86.27, 0.02924, 2, 9, -18.35, -0.56, 0.97425, 76, -379.97, 67.51, 0.02575, 2, 9, -5.26, -11.64, 0.97719, 76, -366.87, 56.42, 0.02281, 2, 9, 12.33, -15.05, 0.97687, 76, -349.28, 53.02, 0.02313, 2, 9, 37.1, -15.33, 0.97563, 76, -324.51, 52.74, 0.02437, 2, 9, -13.14, 71.73, 0.97145, 76, -374.76, 139.8, 0.02855, 2, 9, -13.76, 66.58, 0.96922, 76, -375.37, 134.65, 0.03078, 2, 9, -13.01, 59.76, 0.96789, 76, -374.63, 127.82, 0.03211, 2, 9, -11.2, 53.15, 0.96765, 76, -372.81, 121.22, 0.03235], "hull": 38, "edges": [16, 18, 18, 20, 20, 22, 22, 24, 34, 36, 60, 62, 72, 74, 52, 54, 46, 48, 58, 60, 66, 68, 62, 64, 64, 66, 68, 70, 70, 72, 2, 0, 0, 74, 2, 4, 4, 6, 10, 12, 6, 8, 8, 10, 12, 14, 14, 16, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 48, 50, 50, 52, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 76, 92, 92, 94, 96, 90, 98, 96, 94, 100, 100, 98, 76, 104, 104, 102, 78, 106, 106, 102, 108, 110, 110, 112, 54, 56, 56, 58, 118, 116, 108, 120, 120, 118, 112, 122, 122, 114, 124, 126, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 128, 126, 154, 154, 156, 156, 158, 158, 160, 160, 162, 164, 166, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 206, 208, 210, 212, 212, 188, 208, 214, 214, 210, 204, 216, 216, 206, 202, 204, 186, 218, 218, 220, 220, 222, 222, 224, 224, 226, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 178, 256, 256, 254, 174, 258, 258, 260, 260, 262, 262, 242, 264, 266, 264, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 266, 296, 298, 298, 300, 300, 302, 296, 310, 310, 312, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 324, 326, 326, 328, 42, 44, 44, 46, 328, 330, 332, 334, 336, 332, 334, 338, 330, 336, 356, 358, 364, 366, 366, 370, 370, 372, 372, 374, 374, 376, 376, 378, 378, 380, 348, 350, 382, 384, 384, 386, 386, 388], "width": 168, "height": 180}}, "head2": {"head": {"type": "mesh", "uvs": [0.07053, 0.66517, 0.15762, 0.63826, 0.19238, 0.65248, 0.22126, 0.64026, 0.26859, 0.63951, 0.29828, 0.6163, 0.43601, 0.63912, 0.50262, 0.75389, 0.49157, 0.87042, 0.50943, 0.9164, 0.3349, 0.98701, 0.25324, 0.99444, 0.19295, 0.97478, 0.1543, 0.92621, 0.13476, 0.87714, 0.0824, 0.77521, 0.14827, 0.76961, 0.16608, 0.75858, 0.15272, 0.79336, 0.17307, 0.81651, 0.20488, 0.82423, 0.25385, 0.8177, 0.30092, 0.79633, 0.33145, 0.77377, 0.35031, 0.74862, 0.36446, 0.73731, 0.34239, 0.7435, 0.30907, 0.74481, 0.27202, 0.74217, 0.23479, 0.73794, 0.20587, 0.74355, 0.18285, 0.74362, 0.22929, 0.67741, 0.27202, 0.69836, 0.30612, 0.7185, 0.33979, 0.72857, 0.36008, 0.71044, 0.389, 0.72736, 0.38382, 0.75153, 0.20701, 0.68062, 0.18715, 0.69391, 0.16298, 0.68666, 0.14874, 0.69552, 0.13838, 0.72332, 0.13363, 0.74749, 0.13665, 0.76844, 0.11111, 0.77186, 0.151, 0.8389, 0.20494, 0.84675, 0.26869, 0.84021, 0.17201, 0.87094, 0.35104, 0.87102, 0.43666, 0.83608, 0.21071, 0.92229, 0.29349, 0.9211, 0.3858, 0.91345, 0.17052, 0.77614, 0.20126, 0.77887, 0.24176, 0.77386, 0.2808, 0.76293], "triangles": [12, 53, 11, 11, 54, 10, 11, 53, 54, 10, 55, 9, 10, 54, 55, 12, 13, 53, 13, 50, 53, 13, 14, 50, 50, 48, 53, 53, 49, 54, 53, 48, 49, 54, 51, 55, 54, 49, 51, 55, 8, 9, 55, 52, 8, 55, 51, 52, 14, 15, 47, 14, 47, 50, 47, 15, 46, 49, 22, 51, 22, 23, 51, 52, 51, 23, 52, 23, 38, 38, 24, 25, 24, 38, 23, 48, 19, 20, 19, 48, 47, 48, 50, 47, 8, 52, 7, 48, 21, 49, 48, 20, 21, 49, 21, 22, 47, 46, 18, 18, 46, 45, 47, 18, 19, 18, 45, 16, 52, 38, 7, 19, 57, 20, 20, 58, 21, 20, 57, 58, 21, 59, 22, 21, 58, 59, 18, 56, 19, 19, 56, 57, 23, 59, 27, 23, 22, 59, 18, 16, 56, 56, 31, 57, 57, 30, 58, 57, 31, 30, 16, 17, 56, 56, 17, 31, 15, 0, 46, 30, 29, 58, 58, 28, 59, 58, 29, 28, 23, 26, 24, 23, 27, 26, 46, 44, 45, 44, 46, 43, 17, 16, 44, 16, 45, 44, 59, 28, 27, 31, 17, 43, 38, 37, 7, 37, 6, 7, 38, 25, 37, 24, 26, 25, 17, 44, 43, 43, 46, 0, 28, 34, 27, 27, 35, 26, 27, 34, 35, 43, 42, 31, 31, 42, 40, 31, 40, 30, 40, 42, 41, 30, 40, 29, 26, 35, 25, 28, 33, 34, 28, 29, 33, 40, 39, 29, 39, 32, 29, 29, 32, 33, 35, 36, 25, 25, 36, 37, 35, 34, 36, 37, 36, 6, 43, 0, 42, 36, 34, 5, 5, 34, 4, 36, 5, 6, 32, 4, 33, 34, 33, 4, 41, 42, 1, 39, 40, 2, 42, 0, 1, 40, 41, 2, 41, 1, 2, 32, 39, 3, 39, 2, 3, 32, 3, 4], "vertices": [2, 9, 7.21, 88.06, 0.97879, 76, -354.4, 156.13, 0.02121, 2, 9, 11.72, 73.32, 0.97236, 76, -349.9, 141.39, 0.02764, 2, 9, 9.02, 67.54, 0.97081, 76, -352.59, 135.61, 0.02919, 2, 9, 11.11, 62.64, 0.97013, 76, -350.5, 130.71, 0.02987, 2, 9, 11.06, 54.69, 0.96976, 76, -350.55, 122.76, 0.03024, 2, 9, 15.12, 49.61, 0.96882, 76, -346.49, 117.67, 0.03118, 2, 9, 10.48, 26.57, 0.96838, 76, -351.14, 94.64, 0.03162, 2, 9, -10.44, 15.86, 0.96841, 76, -372.05, 83.93, 0.03159, 2, 9, -31.36, 18.2, 0.97076, 76, -392.98, 86.27, 0.02924, 2, 9, -39.71, 15.4, 0.97544, 76, -401.32, 83.46, 0.02456, 2, 9, -51.73, 45, 0.96982, 76, -413.35, 113.07, 0.03018, 2, 9, -52.75, 58.75, 0.97067, 76, -414.36, 126.82, 0.02933, 2, 9, -48.98, 68.79, 0.97342, 76, -410.59, 136.86, 0.02658, 2, 9, -40.09, 75.08, 0.97571, 76, -401.7, 143.15, 0.02429, 2, 9, -31.18, 78.16, 0.97764, 76, -392.79, 146.23, 0.02236, 2, 9, -12.63, 86.53, 0.98042, 76, -374.25, 154.6, 0.01958, 2, 9, -11.88, 75.44, 0.97337, 76, -373.5, 143.51, 0.02663, 2, 9, -9.97, 72.4, 0.97233, 76, -371.58, 140.47, 0.02767, 2, 9, -16.17, 74.79, 0.97258, 76, -377.79, 142.86, 0.02742, 2, 9, -20.42, 71.47, 0.971, 76, -382.03, 139.54, 0.029, 2, 9, -21.93, 66.16, 0.96924, 76, -383.55, 134.23, 0.03076, 2, 9, -20.95, 57.91, 0.96759, 76, -382.56, 125.98, 0.03241, 2, 9, -17.29, 49.91, 0.96688, 76, -378.9, 117.98, 0.03312, 2, 9, -13.35, 44.69, 0.96748, 76, -374.96, 112.76, 0.03252, 2, 9, -8.89, 41.42, 0.96785, 76, -370.51, 109.49, 0.03215, 2, 9, -6.91, 39, 0.9679, 76, -368.53, 107.06, 0.0321, 2, 9, -7.94, 42.73, 0.96789, 76, -369.55, 110.8, 0.03211, 2, 9, -8.05, 48.33, 0.96777, 76, -369.66, 116.4, 0.03223, 2, 9, -7.43, 54.54, 0.96889, 76, -369.04, 122.61, 0.03111, 2, 9, -6.52, 60.78, 0.96972, 76, -368.13, 128.85, 0.03028, 2, 9, -7.42, 65.66, 0.97056, 76, -369.03, 133.73, 0.02944, 2, 9, -7.34, 69.52, 0.97141, 76, -368.95, 137.59, 0.02859, 2, 9, 4.39, 61.45, 0.96906, 76, -357.22, 129.52, 0.03094, 2, 9, 0.46, 54.36, 0.96873, 76, -361.16, 122.43, 0.03127, 2, 9, -3.3, 48.72, 0.96811, 76, -364.91, 116.78, 0.03189, 2, 9, -5.24, 43.1, 0.96795, 76, -366.86, 111.17, 0.03205, 2, 9, -2.06, 39.62, 0.96806, 76, -363.68, 107.69, 0.03194, 2, 9, -5.22, 34.83, 0.96792, 76, -366.83, 102.9, 0.03208, 2, 9, -9.55, 35.8, 0.9678, 76, -371.16, 103.87, 0.0322, 2, 9, 3.9, 65.2, 0.96949, 76, -357.71, 133.27, 0.03051, 2, 9, 1.59, 68.59, 0.97025, 76, -360.03, 136.66, 0.02975, 2, 9, 2.99, 72.62, 0.9709, 76, -358.63, 140.69, 0.0291, 2, 9, 1.45, 75.05, 0.97172, 76, -360.17, 143.12, 0.02828, 2, 9, -3.51, 76.91, 0.97286, 76, -365.13, 144.98, 0.02714, 2, 9, -7.84, 77.81, 0.97366, 76, -369.46, 145.88, 0.02634, 2, 9, -11.63, 77.39, 0.97398, 76, -373.24, 145.46, 0.02602, 2, 9, -12.14, 81.69, 0.97647, 76, -373.76, 149.76, 0.02353, 2, 9, -24.36, 75.27, 0.97314, 76, -385.98, 143.34, 0.02686, 2, 9, -25.98, 66.24, 0.97081, 76, -387.6, 134.31, 0.02919, 2, 9, -25.06, 55.51, 0.96859, 76, -386.67, 123.58, 0.03141, 2, 9, -30.21, 71.88, 0.97249, 76, -391.82, 139.94, 0.02751, 2, 9, -30.92, 41.81, 0.96737, 76, -392.54, 109.88, 0.03263, 2, 9, -24.97, 27.28, 0.96855, 76, -386.58, 95.35, 0.03145, 2, 9, -39.6, 65.59, 0.96918, 76, -401.21, 133.66, 0.03082, 2, 9, -39.71, 51.68, 0.96687, 76, -401.32, 119.75, 0.03313, 2, 9, -38.69, 36.15, 0.96807, 76, -400.31, 104.22, 0.03193, 2, 9, -13.14, 71.73, 0.97145, 76, -374.76, 139.8, 0.02855, 2, 9, -13.76, 66.58, 0.96922, 76, -375.37, 134.65, 0.03078, 2, 9, -13.01, 59.76, 0.96789, 76, -374.63, 127.82, 0.03211, 2, 9, -11.2, 53.15, 0.96765, 76, -372.81, 121.22, 0.03235], "hull": 16, "edges": [20, 22, 18, 20, 22, 24, 24, 26, 26, 28, 2, 4, 4, 6, 6, 8, 8, 10, 32, 34, 32, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 34, 64, 66, 66, 68, 68, 70, 64, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 92, 28, 30, 92, 94, 96, 98, 100, 96, 98, 102, 94, 100, 106, 108, 108, 110, 110, 16, 112, 114, 114, 116, 116, 118, 2, 0, 10, 12, 12, 14, 14, 16, 16, 18, 0, 30], "width": 168, "height": 180}}, "head_B": {"head_B": {"type": "mesh", "uvs": [0.60815, 0.11851, 0.80513, 0.30281, 0.9786, 0.56003, 0.97858, 0.85436, 0.72141, 0.99999, 0.53377, 0.85508, 0.30408, 0.59903, 0.13654, 0.3531, 0.01006, 0.11194, 0.41658, 0.00018], "triangles": [4, 5, 3, 3, 5, 2, 2, 5, 6, 6, 1, 2, 1, 7, 0, 1, 6, 7, 7, 9, 0, 7, 8, 9], "vertices": [2, 17, 16.25, 0.24, 0.9883, 76, -211.41, 7.23, 0.0117, 2, 17, 6.3, -7.41, 0.98773, 76, -221.36, -0.42, 0.01227, 2, 17, -7.49, -14.03, 0.98917, 76, -235.15, -7.04, 0.01083, 2, 17, -23.08, -13.67, 0.99116, 76, -250.74, -6.67, 0.00884, 2, 17, -34.72, 4.83, 0.985, 76, -262.39, 11.82, 0.015, 2, 17, -26.87, 12.16, 0.985, 76, -254.53, 19.15, 0.015, 2, 17, -13.09, 21.03, 0.985, 76, -240.75, 28.02, 0.015, 2, 17, 0.09, 27.42, 0.98526, 76, -227.57, 34.41, 0.01474, 2, 17, 12.99, 32.18, 0.98603, 76, -214.67, 39.18, 0.01397, 2, 17, 22.7, 7.76, 0.99177, 76, -204.96, 14.75, 0.00823], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 8, 10, 10, 12, 12, 14], "width": 40, "height": 53}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [0.45836, 0.05807, 0.51124, 0.10108, 0.51311, 0.18898, 0.49329, 0.23034, 0.47061, 0.23092, 0.44208, 0.28097, 0.40293, 0.33254, 0.35099, 0.38226, 0.32598, 0.41232, 0.31253, 0.43515, 0.30786, 0.45586, 0.30973, 0.47188, 0.31859, 0.48776, 0.35978, 0.49892, 0.40297, 0.51162, 0.46842, 0.54147, 0.55523, 0.62134, 0.60864, 0.66438, 0.64496, 0.66198, 0.71586, 0.73962, 0.73445, 0.75564, 0.75383, 0.76498, 0.77805, 0.77226, 0.82293, 0.77176, 0.86944, 0.7758, 0.96674, 0.79422, 1, 0.8038, 1, 0.80865, 0.97889, 0.8362, 0.94217, 0.8636, 0.89314, 0.85289, 0.88545, 0.86192, 0.87986, 0.87624, 0.88161, 0.89393, 0.89257, 0.9129, 0.87823, 0.93303, 0.84619, 0.95877, 0.7981, 0.98275, 0.7404, 0.99992, 0.71544, 0.99889, 0.64515, 0.97191, 0.68125, 0.94648, 0.70394, 0.93327, 0.71633, 0.92231, 0.71864, 0.9079, 0.70629, 0.89656, 0.68712, 0.88124, 0.67141, 0.86628, 0.65552, 0.84377, 0.61855, 0.8213, 0.59352, 0.80068, 0.56544, 0.78203, 0.53913, 0.78399, 0.46516, 0.76751, 0.48712, 0.73938, 0.44196, 0.71644, 0.44968, 0.70829, 0.33243, 0.65669, 0.20979, 0.59472, 0.15761, 0.56097, 0.12685, 0.54007, 0.09081, 0.52544, 0.05591, 0.50443, 0.02687, 0.48244, 0.02197, 0.46563, 0.0276, 0.44636, 0.02796, 0.42293, 0.02241, 0.38353, 0.00444, 0.30517, 0.00202, 0.22666, 0.00929, 0.18885, 0.00097, 0.1853, 0.01285, 0.16679, 0.03114, 0.13689, 0.04968, 0.11592, 0.0417, 0.10807, 0.07261, 0.09023, 0.09552, 0.0643, 0.10296, 0.04349, 0.10441, 0.01312, 0.2157, 0.00774, 0.37451, 6e-05, 0.14267, 0.11543, 0.22782, 0.13067, 0.32553, 0.15228, 0.12039, 0.14422, 0.10966, 0.17799, 0.20061, 0.18113, 0.30767, 0.19165, 0.42451, 0.1709, 0.39824, 0.2071, 0.22176, 0.14863, 0.16225, 0.08898, 0.17336, 0.03948, 0.17336, 0.06408, 0.30192, 0.12057, 0.28843, 0.0899, 0.27494, 0.06044, 0.26462, 0.03158, 0.09879, 0.22232, 0.08796, 0.29829, 0.08796, 0.39499, 0.07851, 0.42393, 0.06596, 0.44881, 0.0645, 0.47405, 0.0815, 0.49804, 0.13131, 0.52091, 0.17981, 0.53783, 0.30626, 0.23735, 0.26523, 0.30575, 0.22677, 0.3961, 0.20719, 0.42399, 0.18769, 0.44636, 0.17975, 0.46729, 0.18769, 0.4891, 0.21493, 0.50881, 0.24749, 0.52762, 0.22934, 0.57186, 0.51661, 0.70299, 0.40719, 0.59539, 0.33555, 0.55395, 0.30709, 0.61576], "triangles": [38, 39, 37, 37, 39, 41, 39, 40, 41, 41, 42, 37, 37, 42, 36, 42, 43, 36, 36, 43, 35, 35, 43, 44, 34, 35, 44, 44, 33, 34, 44, 45, 33, 33, 45, 32, 45, 46, 32, 32, 46, 31, 23, 31, 46, 22, 23, 48, 23, 46, 47, 23, 30, 31, 23, 47, 48, 29, 30, 28, 30, 23, 24, 30, 25, 28, 30, 24, 25, 22, 48, 49, 28, 25, 27, 25, 26, 27, 22, 49, 21, 49, 50, 21, 50, 20, 21, 20, 51, 19, 20, 50, 51, 51, 52, 54, 19, 118, 17, 17, 18, 19, 52, 53, 54, 51, 54, 19, 118, 19, 54, 54, 55, 118, 118, 55, 56, 56, 57, 118, 16, 118, 119, 118, 16, 17, 121, 119, 118, 58, 121, 57, 118, 57, 121, 119, 15, 16, 58, 117, 121, 121, 120, 119, 121, 117, 120, 119, 120, 15, 58, 59, 117, 59, 107, 117, 117, 116, 120, 117, 107, 116, 59, 60, 107, 120, 14, 15, 120, 13, 14, 120, 116, 13, 60, 106, 107, 60, 61, 106, 107, 115, 116, 107, 106, 115, 116, 12, 13, 116, 115, 12, 62, 105, 61, 61, 105, 106, 106, 114, 115, 106, 105, 114, 115, 114, 12, 114, 105, 113, 114, 11, 12, 62, 63, 105, 63, 104, 105, 105, 104, 113, 114, 113, 11, 63, 64, 104, 104, 103, 113, 104, 64, 103, 113, 10, 11, 113, 112, 10, 113, 103, 112, 64, 65, 103, 10, 112, 9, 9, 112, 111, 103, 102, 112, 103, 65, 102, 65, 66, 102, 112, 102, 111, 9, 111, 8, 102, 101, 111, 111, 110, 8, 111, 101, 110, 102, 66, 101, 66, 67, 101, 8, 110, 7, 7, 110, 109, 67, 100, 101, 109, 110, 100, 110, 101, 100, 109, 100, 108, 108, 100, 99, 99, 86, 87, 67, 68, 100, 7, 109, 6, 6, 109, 5, 109, 108, 5, 108, 99, 87, 68, 69, 100, 100, 69, 99, 5, 108, 4, 4, 108, 90, 108, 88, 90, 108, 87, 88, 69, 70, 99, 99, 70, 86, 87, 91, 88, 88, 91, 84, 86, 70, 72, 70, 71, 72, 86, 85, 87, 87, 85, 91, 86, 72, 85, 85, 72, 73, 91, 83, 84, 91, 85, 83, 73, 74, 85, 85, 82, 83, 85, 74, 82, 82, 92, 83, 83, 92, 96, 94, 96, 92, 74, 75, 82, 75, 76, 82, 82, 76, 92, 76, 77, 92, 92, 77, 94, 77, 78, 94, 78, 93, 94, 94, 93, 97, 78, 79, 93, 93, 80, 98, 93, 79, 80, 3, 4, 2, 2, 4, 90, 90, 89, 2, 88, 84, 89, 89, 1, 2, 89, 84, 1, 84, 95, 1, 83, 96, 95, 96, 94, 97, 95, 96, 1, 96, 0, 1, 96, 97, 0, 93, 98, 97, 97, 98, 0, 98, 81, 0, 98, 80, 81, 90, 88, 89, 83, 95, 84], "vertices": [1, 3, 106.55, 50.28, 1, 1, 3, 154.63, 69.19, 1, 2, 3, 250.03, 63.76, 0.68294, 30, 84.77, 142.84, 0.31706, 2, 3, 294.35, 52.62, 0.46126, 30, 130.19, 137.8, 0.53874, 2, 3, 294.37, 43.16, 0.42441, 30, 131.5, 128.43, 0.57559, 2, 3, 347.89, 27.78, 0.1511, 30, 186.6, 120.44, 0.8489, 2, 3, 402.77, 7.89, 0.03702, 30, 243.67, 108.16, 0.96298, 3, 3, 455.3, -17.18, 0.00393, 30, 299.11, 90.43, 0.99378, 31, -34.87, 148.51, 0.00229, 3, 3, 487.23, -29.69, 0.00023, 30, 332.44, 82.36, 0.96568, 31, -14.73, 120.76, 0.03409, 2, 30, 357.59, 78.54, 0.87545, 31, 1.93, 101.53, 0.12455, 2, 30, 380.18, 78.19, 0.70284, 31, 18.91, 86.62, 0.29716, 2, 30, 397.5, 80.2, 0.49971, 31, 33.39, 76.92, 0.50029, 2, 30, 414.45, 85.1, 0.26323, 31, 49.47, 69.65, 0.73677, 2, 30, 425.34, 103.05, 0.10207, 31, 69.4, 76.25, 0.89793, 2, 30, 437.84, 121.95, 0.03535, 31, 91.17, 82.53, 0.96465, 2, 30, 468.28, 151.4, 0.0012, 31, 133.45, 85.21, 0.9988, 1, 31, 224.77, 62.8, 1, 1, 31, 275.61, 52.94, 1, 1, 31, 282.48, 66.65, 1, 1, 31, 367.92, 40.35, 1, 2, 31, 386.53, 36.25, 0.9397, 33, 179.52, -24.81, 0.0603, 2, 31, 399.48, 36.73, 0.78661, 33, 167.43, -29.5, 0.21339, 2, 31, 411.83, 40.14, 0.51603, 33, 156.89, -36.77, 0.48397, 2, 31, 422.47, 55.5, 0.25383, 33, 151.87, -54.77, 0.74617, 2, 31, 437.47, 68.47, 0.12151, 33, 141.95, -71.94, 0.87849, 2, 31, 477.6, 89.19, 0.00809, 33, 110.83, -104.67, 0.99191, 2, 31, 494.19, 94.16, 0.00062, 33, 96.79, -114.8, 0.99938, 2, 31, 498.44, 91.03, 0.00061, 33, 91.75, -113.24, 0.99939, 1, 33, 65.75, -95.98, 1, 1, 33, 41.83, -72.56, 1, 1, 33, 58.99, -56.53, 1, 1, 33, 50.56, -50.57, 1, 1, 33, 36.39, -43.73, 1, 2, 33, 17.81, -38.73, 0.97317, 32, 127.57, -18.76, 0.02683, 2, 33, -3.23, -36.98, 0.74004, 32, 110.16, -30.7, 0.25996, 2, 33, -22.37, -24.79, 0.31481, 32, 87.63, -33.35, 0.68519, 1, 32, 56.69, -31.45, 1, 1, 32, 25.04, -22.65, 1, 1, 32, -1.25, -7.38, 1, 1, 32, -4.1, 2.67, 1, 1, 32, 12.16, 40.76, 1, 2, 33, -12.05, 57.8, 0.02371, 32, 43.42, 37.18, 0.97629, 2, 33, -1.13, 44.53, 0.12416, 32, 60.27, 33.79, 0.87584, 2, 33, 8.72, 36.08, 0.35201, 32, 73.24, 33.47, 0.64799, 2, 33, 23.39, 30.52, 0.76654, 32, 88.12, 38.43, 0.23346, 2, 33, 36.69, 31.78, 0.9345, 32, 97.63, 47.81, 0.0655, 2, 33, 54.95, 34.46, 0.99738, 32, 110.09, 61.44, 0.00262, 2, 31, 467.81, -56.19, 0.00022, 33, 72.42, 35.88, 0.99978, 2, 31, 444.19, -47, 0.05333, 33, 97.75, 34.94, 0.94667, 2, 31, 415.41, -44.9, 0.40349, 33, 125.63, 42.4, 0.59651, 2, 31, 391.18, -39.98, 0.82601, 33, 150.13, 45.69, 0.17399, 2, 31, 367.93, -37.37, 0.98518, 33, 172.95, 50.85, 0.01482, 2, 31, 363.15, -47.45, 0.99772, 33, 174.16, 61.93, 0.00228, 1, 31, 330.48, -61.6, 1, 1, 31, 311.28, -36.11, 1, 1, 31, 280.06, -36.45, 1, 1, 31, 274.83, -28.61, 1, 1, 31, 200.74, -34.61, 1, 1, 31, 116.25, -35.75, 1, 1, 31, 73.83, -31.47, 1, 1, 31, 47.95, -28.3, 1, 2, 30, 462.01, -6.52, 0.00473, 31, 26.25, -30.94, 0.99527, 2, 30, 440.26, -22.62, 0.34377, 31, -0.74, -29.09, 0.65623, 2, 30, 417.27, -36.36, 0.86059, 31, -27.15, -24.63, 0.13941, 2, 30, 399.19, -39.69, 0.98575, 31, -43.07, -15.44, 0.01425, 1, 30, 378.13, -38.83, 1, 1, 30, 352.71, -40.48, 1, 1, 30, 310.16, -45.82, 1, 1, 30, 225.72, -59.3, 1, 1, 30, 140.67, -66.34, 1, 1, 30, 99.46, -66.23, 1, 1, 30, 95.85, -69.96, 1, 1, 30, 75.44, -66.45, 1, 2, 5, 135.03, -66.35, 0.00116, 30, 42.48, -61.16, 0.99884, 2, 5, 112.78, -57.17, 0.01891, 30, 19.19, -55.08, 0.98109, 2, 5, 104.05, -59.93, 0.03066, 30, 10.92, -59, 0.96934, 2, 5, 85.53, -45.84, 0.10141, 30, -9.34, -47.54, 0.89859, 3, 3, 103.5, -100.78, 0.00853, 5, 58.02, -34.5, 0.36077, 30, -38.13, -40.03, 0.63069, 3, 3, 81.13, -96.23, 0.01414, 5, 35.65, -29.94, 0.66726, 30, -60.91, -38.54, 0.31859, 2, 5, -1.97, -27.92, 0.97504, 30, -98.46, -41.64, 0.02496, 3, 3, 40.68, -47.63, 0.26257, 5, -4.8, 18.66, 0.72847, 30, -107.57, 4.13, 0.00896, 1, 3, 41.37, 19.57, 1, 3, 5, 114.76, -18.53, 0.01089, 30, 15.92, -16.53, 0.96411, 80, -494.3, -65.34, 0.025, 3, 3, 179.07, -50.55, 0.18397, 30, 29.94, 19.97, 0.79103, 80, -458.88, -81.91, 0.025, 3, 3, 205.15, -11.51, 0.60833, 30, 50.5, 62.18, 0.36667, 80, -418.23, -105.4, 0.025, 2, 30, 47.79, -23.56, 0.975, 80, -503.57, -96.64, 0.025, 2, 30, 84.73, -25.42, 0.975, 80, -508.03, -133.35, 0.025, 3, 3, 233.07, -65.41, 0.04128, 30, 85.46, 12.56, 0.93372, 80, -470.2, -136.76, 0.025, 3, 3, 247.38, -21.71, 0.31179, 30, 93.71, 57.8, 0.66321, 80, -425.66, -148.2, 0.025, 3, 3, 228.02, 28.26, 0.71321, 30, 67.78, 104.68, 0.26179, 80, -377.06, -125.65, 0.025, 3, 3, 266.58, 14.8, 0.44491, 30, 107.8, 96.57, 0.53009, 80, -387.98, -164.99, 0.025, 3, 3, 198.39, -54.33, 0.13189, 30, 49.59, 18.84, 0.84311, 80, -461.4, -101.43, 0.025, 4, 3, 132.08, -74.83, 0.02224, 5, 86.6, -8.54, 0.09219, 30, -13.33, -10.44, 0.86057, 80, -486.16, -36.6, 0.025, 3, 3, 78.69, -66.72, 0.13087, 5, 33.21, -0.43, 0.65312, 30, -67.33, -9.64, 0.21601, 4, 3, 105.37, -68.46, 0.13224, 5, 59.89, -2.17, 0.32848, 30, -40.66, -7.74, 0.51428, 80, -481.53, -9.53, 0.025, 3, 3, 170.12, -19.08, 0.71287, 30, 16.81, 49.95, 0.26213, 80, -428.05, -70.93, 0.025, 4, 3, 136.48, -22.51, 0.72006, 5, 91, 43.78, 0.01865, 30, -16.05, 41.99, 0.23628, 80, -433.66, -37.59, 0.025, 4, 3, 104.16, -26.03, 0.69444, 5, 58.68, 40.26, 0.1189, 30, -47.6, 34.13, 0.16166, 80, -439.28, -5.57, 0.025, 3, 3, 72.59, -28.28, 0.6654, 5, 27.11, 38.01, 0.27457, 30, -78.58, 27.63, 0.06004, 2, 30, 133.12, -26.52, 0.975, 80, -512.56, -181.54, 0.025, 2, 30, 215.8, -25.17, 0.975, 80, -517.06, -264.11, 0.025, 2, 30, 320.66, -17.74, 0.98237, 80, -517.06, -369.23, 0.01763, 2, 30, 352.31, -19.43, 0.98157, 80, -520.99, -400.69, 0.01843, 2, 30, 379.66, -22.73, 0.98265, 80, -526.21, -427.73, 0.01735, 3, 30, 407.07, -21.39, 0.93642, 31, -25.21, -6.62, 0.04688, 80, -526.82, -455.17, 0.0167, 3, 30, 432.58, -12.49, 0.35617, 31, -0.03, -16.39, 0.62768, 80, -519.75, -481.24, 0.01616, 2, 31, 32.28, -14.45, 0.98342, 80, -499.03, -506.11, 0.01658, 2, 31, 59.06, -9.11, 0.98252, 80, -478.85, -524.5, 0.01748, 3, 3, 296.91, -25.52, 0.15647, 30, 143.31, 60.73, 0.81853, 80, -426.25, -197.88, 0.025, 3, 3, 369.99, -47.38, 0.02407, 30, 218.67, 48.96, 0.95093, 80, -443.32, -272.22, 0.025, 4, 3, 466.95, -69.73, 9e-05, 30, 317.77, 39.95, 0.97734, 31, -53.41, 97.98, 0.00577, 80, -459.31, -370.43, 0.01679, 3, 30, 348.59, 33.97, 0.95029, 31, -33.82, 73.45, 0.03461, 80, -467.46, -400.75, 0.0151, 3, 30, 373.42, 27.6, 0.90344, 31, -19.06, 52.49, 0.0805, 80, -475.57, -425.07, 0.01606, 3, 30, 396.34, 25.91, 0.77253, 31, -2.7, 36.34, 0.21127, 80, -478.88, -447.82, 0.0162, 3, 30, 419.76, 30.88, 0.34262, 31, 18.35, 24.94, 0.64187, 80, -475.57, -471.53, 0.0155, 3, 30, 440.33, 43.7, 0.09232, 31, 42.32, 21.36, 0.89141, 80, -464.24, -492.95, 0.01627, 3, 30, 459.76, 58.66, 0.02848, 31, 66.82, 20.14, 0.95454, 80, -450.69, -513.4, 0.01698, 2, 31, 101.06, -14.46, 0.98399, 80, -458.25, -561.48, 0.01601, 1, 31, 286.7, -2.77, 1, 2, 31, 165.54, 29.94, 0.99355, 80, -384.26, -587.07, 0.00645, 3, 30, 485.72, 97.23, 0.00295, 31, 111.59, 32.66, 0.98229, 80, -414.06, -542.02, 0.01476, 2, 31, 158.67, -16.72, 0.99214, 80, -425.9, -609.21, 0.00786], "hull": 82, "edges": [0, 162, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 72, 74, 74, 76, 76, 78, 78, 80, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 118, 120, 124, 126, 126, 128, 152, 154, 150, 152, 148, 150, 142, 144, 144, 146, 146, 148, 140, 142, 138, 140, 136, 138, 154, 156, 156, 158, 170, 164, 170, 172, 172, 174, 174, 176, 176, 180, 158, 160, 160, 162, 4, 6, 6, 8, 8, 10, 10, 12, 132, 134, 134, 136, 128, 130, 130, 132, 120, 122, 122, 124, 114, 116, 116, 118, 108, 110, 44, 46, 46, 48, 40, 42, 42, 44, 36, 38, 38, 40, 96, 98, 98, 100, 100, 102, 84, 86, 80, 82, 82, 84, 68, 70, 70, 72, 64, 66, 66, 68, 60, 62, 62, 64, 24, 26, 26, 28, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 214, 234, 236, 238, 232, 240, 240, 238, 234, 242, 242, 236, 164, 150], "width": 416, "height": 1087}}, "nose": {"nose": {"type": "mesh", "uvs": [0.56114, 0.01765, 0.74023, 0.01619, 0.97491, 0.13244, 0.93388, 0.3881, 0.89286, 0.64377, 0.92233, 0.73061, 0.91759, 0.83306, 0.76664, 0.92493, 0.53168, 0.92513, 0.39163, 0.96519, 0.23215, 0.92149, 0.10583, 0.82918, 0.02066, 0.70377, 0.05132, 0.587, 0.3137, 0.36874, 0.39713, 0.24844, 0.38205, 0.0191, 0.46561, 0.69923, 0.47834, 0.56229, 0.56428, 0.37013, 0.63113, 0.25748, 0.23392, 0.74911, 0.73686, 0.75132, 0.72095, 0.57903, 0.49475, 0.2404, 0.42472, 0.35656, 0.25308, 0.55805], "triangles": [8, 9, 21, 9, 10, 21, 21, 17, 8, 8, 22, 7, 8, 17, 22, 7, 22, 6, 10, 11, 21, 6, 22, 5, 11, 12, 21, 17, 23, 22, 22, 4, 5, 22, 23, 4, 12, 13, 21, 21, 26, 17, 21, 13, 26, 17, 18, 23, 17, 26, 18, 4, 23, 3, 26, 13, 14, 18, 19, 23, 23, 19, 3, 26, 25, 18, 18, 25, 19, 26, 14, 25, 19, 20, 3, 3, 20, 2, 25, 24, 19, 19, 24, 20, 14, 15, 25, 25, 15, 24, 24, 0, 20, 20, 1, 2, 20, 0, 1, 15, 16, 24, 24, 16, 0], "vertices": [2, 9, 55.46, 60.7, 0.97156, 76, -306.16, 128.77, 0.02844, 2, 9, 55.39, 54.61, 0.97021, 76, -306.23, 122.68, 0.02979, 2, 9, 49.51, 46.77, 0.969, 76, -312.11, 114.84, 0.031, 2, 9, 37.02, 48.45, 0.969, 76, -324.6, 116.52, 0.031, 2, 9, 24.53, 50.14, 0.96911, 76, -337.09, 118.21, 0.03089, 2, 9, 20.25, 49.24, 0.96891, 76, -341.37, 117.31, 0.03109, 2, 9, 15.23, 49.52, 0.96882, 76, -346.38, 117.58, 0.03118, 2, 9, 10.85, 54.75, 0.96976, 76, -350.76, 122.82, 0.03024, 2, 9, 11.03, 62.74, 0.97013, 76, -350.59, 130.81, 0.02987, 2, 9, 9.18, 67.54, 0.97081, 76, -352.44, 135.61, 0.02919, 2, 9, 11.44, 72.91, 0.97236, 76, -350.17, 140.98, 0.02764, 2, 9, 16.06, 77.1, 0.97095, 76, -345.55, 145.17, 0.02905, 2, 9, 22.27, 79.86, 0.97021, 76, -339.34, 147.92, 0.02979, 2, 9, 27.97, 78.68, 0.97005, 76, -333.64, 146.75, 0.02995, 2, 9, 38.46, 69.51, 0.97101, 76, -323.16, 137.58, 0.02899, 2, 9, 44.28, 66.54, 0.97145, 76, -317.33, 134.61, 0.02855, 2, 9, 55.53, 66.79, 0.97292, 76, -306.09, 134.86, 0.02708, 2, 9, 22.15, 64.73, 0.96678, 76, -339.47, 132.79, 0.03322, 2, 9, 28.84, 64.14, 0.96716, 76, -332.77, 132.21, 0.03284, 2, 9, 38.19, 61, 0.96807, 76, -323.42, 129.07, 0.03193, 2, 9, 43.65, 58.6, 0.96896, 76, -317.96, 126.67, 0.03104, 2, 9, 19.89, 72.66, 0.96774, 76, -341.73, 140.73, 0.03226, 2, 9, 19.38, 55.57, 0.96769, 76, -342.23, 123.63, 0.03231, 2, 9, 27.83, 55.91, 0.96816, 76, -333.78, 123.98, 0.03184, 2, 9, 44.6, 63.21, 0.96994, 76, -317.02, 131.28, 0.03006, 2, 9, 38.96, 65.73, 0.96908, 76, -322.65, 133.79, 0.03092, 2, 9, 29.23, 71.79, 0.96796, 76, -332.38, 139.86, 0.03204], "hull": 17, "edges": [12, 14, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 2, 4, 20, 22, 22, 24, 14, 16, 16, 18, 10, 12, 8, 10, 34, 36, 36, 38, 38, 40, 40, 2, 42, 34, 44, 46, 2, 0, 0, 32, 0, 48, 48, 50, 50, 52, 4, 6, 6, 8], "width": 34, "height": 49}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": 6.13, "curve": [0.444, 6.13, 0.889, -6.89, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -6.9, "curve": [1.778, -6.9, 2.222, 6.13, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 6.13, "curve": [3.111, 6.13, 3.556, -6.89, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -6.9, "curve": [4.444, -6.9, 4.889, 6.13, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 6.13, "curve": [5.667, 6.13, 6, -6.9, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": -6.9, "curve": [6.778, -6.9, 7.222, -0.38, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -0.38, "curve": [8.111, -0.38, 8.556, -6.89, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -6.9, "curve": [9.444, -6.9, 9.889, 6.13, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 6.13, "curve": [10.778, 6.13, 11.222, -6.89, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": -6.9, "curve": [12.111, -6.9, 12.556, 6.13, 12.111, 0, 12.556, 0]}, {"time": 13, "x": 6.13}]}, "ALL2": {"translate": [{"y": -15.19, "curve": [0.444, 0, 0.889, 0, 0.444, -15.19, 0.889, 16.96]}, {"time": 1.3333, "y": 16.97, "curve": [1.778, 0, 2.222, 0, 1.778, 16.98, 2.222, -15.18]}, {"time": 2.6667, "y": -15.19, "curve": [3.111, 0, 3.556, 0, 3.111, -15.19, 3.556, 16.96]}, {"time": 4, "y": 16.97, "curve": [4.444, 0, 4.889, 0, 4.444, 16.98, 4.889, -15.17]}, {"time": 5.3333, "y": -15.19, "curve": [5.667, 0, 6, 0, 5.667, -15.19, 6, 16.97]}, {"time": 6.3333, "y": 16.97, "curve": [6.778, 0, 7.222, 0, 6.778, 16.98, 7.222, 0.9]}, {"time": 7.6667, "y": 0.89, "curve": [8.111, 0, 8.556, 0, 8.111, 0.89, 8.556, 16.96]}, {"time": 9, "y": 16.97, "curve": [9.444, 0, 9.889, 0, 9.444, 16.98, 9.889, -15.18]}, {"time": 10.3333, "y": -15.19, "curve": [10.778, 0, 11.222, 0, 10.778, -15.19, 11.222, 16.96]}, {"time": 11.6667, "y": 16.97, "curve": [12.111, 0, 12.556, 0, 12.111, 16.98, 12.556, -15.19]}, {"time": 13, "y": -15.19}]}, "tun": {"rotate": [{"value": 4.07, "curve": [0.444, 4.07, 0.889, -2.42]}, {"time": 1.3333, "value": -2.42, "curve": [1.778, -2.42, 2.222, 4.06]}, {"time": 2.6667, "value": 4.07, "curve": [3.111, 4.07, 3.556, -2.42]}, {"time": 4, "value": -2.42, "curve": [4.444, -2.42, 4.889, 4.06]}, {"time": 5.3333, "value": 4.07, "curve": [5.667, 4.07, 6, -2.42]}, {"time": 6.3333, "value": -2.42, "curve": [6.778, -2.42, 7.222, 0.82]}, {"time": 7.6667, "value": 0.82, "curve": [8.111, 0.82, 8.556, -2.42]}, {"time": 9, "value": -2.42, "curve": [9.444, -2.42, 9.889, 4.06]}, {"time": 10.3333, "value": 4.07, "curve": [10.778, 4.07, 11.222, -2.42]}, {"time": 11.6667, "value": -2.42, "curve": [12.111, -2.42, 12.556, 4.07]}, {"time": 13, "value": 4.07}]}, "body": {"rotate": [{"value": 2.86, "curve": [0.444, 2.86, 0.889, -3.05]}, {"time": 1.3333, "value": -3.05, "curve": [1.778, -3.05, 2.222, 2.86]}, {"time": 2.6667, "value": 2.86, "curve": [3.111, 2.87, 3.556, -3.05]}, {"time": 4, "value": -3.05, "curve": [4.444, -3.05, 4.889, 2.86]}, {"time": 5.3333, "value": 2.86, "curve": [5.667, 2.87, 6, -3.05]}, {"time": 6.3333, "value": -3.05, "curve": [6.778, -3.05, 7.222, -0.09]}, {"time": 7.6667, "value": -0.09, "curve": [8.111, -0.09, 8.556, -3.05]}, {"time": 9, "value": -3.05, "curve": [9.444, -3.05, 9.889, 2.86]}, {"time": 10.3333, "value": 2.86, "curve": [10.778, 2.87, 11.222, -3.05]}, {"time": 11.6667, "value": -3.05, "curve": [12.111, -3.05, 12.556, 2.86]}, {"time": 13, "value": 2.86}], "translate": [{"y": -8.2, "curve": [0.057, 0, 0.112, 0, 0.057, -8.61, 0.112, -8.93]}, {"time": 0.1667, "y": -8.93, "curve": [0.611, 0, 1.056, 0, 0.611, -8.93, 1.056, 6.69]}, {"time": 1.5, "y": 6.7, "curve": [1.944, 0, 2.389, 0, 1.944, 6.7, 2.389, -8.93]}, {"time": 2.8333, "y": -8.93, "curve": [3.278, 0, 3.722, 0, 3.278, -8.93, 3.722, 6.69]}, {"time": 4.1667, "y": 6.7, "curve": [4.611, 0, 5.056, 0, 4.611, 6.7, 5.056, -8.93]}, {"time": 5.5, "y": -8.93, "curve": [5.833, 0, 6.167, 0, 5.833, -8.93, 6.167, 6.7]}, {"time": 6.5, "y": 6.7, "curve": [6.944, 0, 7.389, 0, 6.944, 6.7, 7.389, -1.11]}, {"time": 7.8333, "y": -1.12, "curve": [8.278, 0, 8.722, 0, 8.278, -1.12, 8.722, 6.69]}, {"time": 9.1667, "y": 6.7, "curve": [9.611, 0, 10.056, 0, 9.611, 6.7, 10.056, -8.93]}, {"time": 10.5, "y": -8.93, "curve": [10.944, 0, 11.389, 0, 10.944, -8.93, 11.389, 6.69]}, {"time": 11.8333, "y": 6.7, "curve": [12.223, 0, 12.613, 0, 12.223, 6.7, 12.613, -5.23]}, {"time": 13, "y": -8.2}], "scale": [{"y": 1.04, "curve": [0.057, 1, 0.112, 1, 0.057, 1.042, 0.112, 1.044]}, {"time": 0.1667, "y": 1.044, "curve": [0.611, 1, 1.056, 1, 0.611, 1.044, 1.056, 0.955]}, {"time": 1.5, "y": 0.955, "curve": [1.944, 1, 2.389, 1, 1.944, 0.955, 2.389, 1.044]}, {"time": 2.8333, "y": 1.044, "curve": [3.278, 1, 3.722, 1, 3.278, 1.044, 3.722, 0.955]}, {"time": 4.1667, "y": 0.955, "curve": [4.611, 1, 5.056, 1, 4.611, 0.955, 5.056, 1.044]}, {"time": 5.5, "y": 1.044, "curve": [5.833, 1, 6.167, 1, 5.833, 1.044, 6.167, 0.955]}, {"time": 6.5, "y": 0.955, "curve": [6.944, 1, 7.389, 1, 6.944, 0.955, 7.389, 1]}, {"time": 7.8333, "curve": [8.278, 1, 8.722, 1, 8.278, 1, 8.722, 0.955]}, {"time": 9.1667, "y": 0.955, "curve": [9.611, 1, 10.056, 1, 9.611, 0.955, 10.056, 1.044]}, {"time": 10.5, "y": 1.044, "curve": [10.944, 1, 11.389, 1, 10.944, 1.044, 11.389, 0.955]}, {"time": 11.8333, "y": 0.955, "curve": [12.223, 1, 12.613, 1, 12.223, 0.955, 12.613, 1.023]}, {"time": 13, "y": 1.04}]}, "body2": {"rotate": [{"value": -1.81, "curve": [0.114, -2.22, 0.224, -2.52]}, {"time": 0.3333, "value": -2.52, "curve": [0.778, -2.52, 1.222, 1.92]}, {"time": 1.6667, "value": 1.92, "curve": [2.111, 1.92, 2.556, -2.52]}, {"time": 3, "value": -2.52, "curve": [3.444, -2.52, 3.889, 1.92]}, {"time": 4.3333, "value": 1.92, "curve": [4.778, 1.92, 5.222, -2.52]}, {"time": 5.6667, "value": -2.52, "curve": [6, -2.52, 6.333, 1.92]}, {"time": 6.6667, "value": 1.92, "curve": [7.111, 1.92, 7.556, -0.3]}, {"time": 8, "value": -0.3, "curve": [8.444, -0.3, 8.889, 1.92]}, {"time": 9.3333, "value": 1.92, "curve": [9.778, 1.92, 10.222, -2.52]}, {"time": 10.6667, "value": -2.52, "curve": [11.111, -2.52, 11.556, 1.92]}, {"time": 12, "value": 1.92, "curve": [12.335, 1.92, 12.67, -0.57]}, {"time": 13, "value": -1.81}], "translate": [{"x": -5.88, "curve": [0.114, -7.13, 0.224, -8.07, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -8.07, "curve": [0.778, -8.07, 1.222, 5.58, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 5.58, "curve": [2.111, 5.58, 2.556, -8.06, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -8.07, "curve": [3.444, -8.07, 3.889, 5.58, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 5.58, "curve": [4.778, 5.58, 5.222, -8.06, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -8.07, "curve": [6, -8.07, 6.333, 5.58, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 5.58, "curve": [7.111, 5.58, 7.556, -1.24, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -1.24, "curve": [8.444, -1.24, 8.889, 5.58, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 5.58, "curve": [9.778, 5.58, 10.222, -8.06, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -8.07, "curve": [11.111, -8.07, 11.556, 5.58, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 5.58, "curve": [12.335, 5.58, 12.67, -2.06, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -5.88}]}, "neck": {"rotate": [{"value": 0.4, "curve": [0.168, 1.02, 0.334, 1.53]}, {"time": 0.5, "value": 1.53, "curve": [0.944, 1.53, 1.389, -2.02]}, {"time": 1.8333, "value": -2.02, "curve": [2.278, -2.02, 2.722, 1.53]}, {"time": 3.1667, "value": 1.53, "curve": [3.611, 1.53, 4.056, -2.02]}, {"time": 4.5, "value": -2.02, "curve": [4.944, -2.02, 5.389, 1.53]}, {"time": 5.8333, "value": 1.53, "curve": [6.167, 1.53, 6.5, -4.43]}, {"time": 6.8333, "value": -4.43, "curve": [7.278, -4.44, 7.722, -1.49]}, {"time": 8.1667, "value": -1.49, "curve": [8.611, -1.49, 9.056, -4.43]}, {"time": 9.5, "value": -4.43, "curve": [9.944, -4.44, 10.389, 1.53]}, {"time": 10.8333, "value": 1.53, "curve": [11.278, 1.53, 11.722, -2.02]}, {"time": 12.1667, "value": -2.02, "curve": [12.445, -2.02, 12.724, -0.64]}, {"time": 13, "value": 0.4}]}, "head": {"rotate": [{"value": -0.24, "curve": [0.225, 0.64, 0.446, 1.53]}, {"time": 0.6667, "value": 1.53, "curve": [1.111, 1.53, 1.556, -2.02]}, {"time": 2, "value": -2.02, "curve": [2.444, -2.02, 2.889, 1.53]}, {"time": 3.3333, "value": 1.53, "curve": [3.778, 1.53, 4.222, -2.02]}, {"time": 4.6667, "value": -2.02, "curve": [5.111, -2.02, 5.556, 1.53]}, {"time": 6, "value": 1.53, "curve": [6.333, 1.53, 6.667, -4.12]}, {"time": 7, "value": -4.12, "curve": [7.444, -4.12, 7.889, -2.15]}, {"time": 8.3333, "value": -2.15, "curve": [8.778, -2.15, 9.222, -4.12]}, {"time": 9.6667, "value": -4.12, "curve": [10.111, -4.12, 10.556, 1.53]}, {"time": 11, "value": 1.53, "curve": [11.444, 1.53, 11.889, -2.02]}, {"time": 12.3333, "value": -2.02, "curve": [12.557, -2.02, 12.781, -1.14]}, {"time": 13, "value": -0.24}]}, "eyebrow_L": {"translate": [{"curve": [0.444, 0, 5.704, 0, 0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 1.41, 6.148, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.41, "curve": "stepped"}, {"time": 9.6667, "x": 1.41, "curve": [9.889, 1.41, 10.111, 0, 9.889, 0, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_L2": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, -6]}, {"time": 6.3333, "value": -6, "curve": "stepped"}, {"time": 9.6667, "value": -6, "curve": [9.889, -6.01, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R": {"rotate": [{"curve": [0.444, 0, 5.704, 0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, 8.96]}, {"time": 6.3333, "value": 8.96, "curve": "stepped"}, {"time": 9.6667, "value": 8.96, "curve": [9.889, 8.97, 10.111, 0]}, {"time": 10.3333}], "translate": [{"curve": [0.444, 0, 5.704, 0, 0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 1.41, 6.148, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.41, "curve": "stepped"}, {"time": 9.6667, "x": 1.41, "curve": [9.889, 1.41, 10.111, 0, 9.889, 0, 10.111, 0]}, {"time": 10.3333}]}, "hair_B2": {"rotate": [{"value": -7.06, "curve": [0.336, -1.98, 0.668, 8.05]}, {"time": 1, "value": 8.05, "curve": [1.444, 8.05, 1.889, -9.94]}, {"time": 2.3333, "value": -9.94, "curve": [2.778, -9.94, 3.222, 8.05]}, {"time": 3.6667, "value": 8.05, "curve": [4.111, 8.06, 4.556, -9.94]}, {"time": 5, "value": -9.94, "curve": [5.444, -9.94, 5.889, 8.04]}, {"time": 6.3333, "value": 8.05, "curve": [6.667, 8.06, 7, -9.94]}, {"time": 7.3333, "value": -9.94, "curve": [7.778, -9.94, 8.222, 8.05]}, {"time": 8.6667, "value": 8.05, "curve": [9.111, 8.06, 9.556, -9.94]}, {"time": 10, "value": -9.94, "curve": [10.444, -9.94, 10.889, 8.05]}, {"time": 11.3333, "value": 8.05, "curve": [11.778, 8.06, 12.222, -9.94]}, {"time": 12.6667, "value": -9.94, "curve": [12.779, -9.94, 12.892, -8.79]}, {"time": 13, "value": -7.06}]}, "sh_L": {"translate": [{"x": -1.88, "y": -0.94, "curve": [0.114, -2.76, 0.224, -3.41, 0.114, -1.29, 0.224, -1.54]}, {"time": 0.3333, "x": -3.41, "y": -1.54, "curve": [0.778, -3.41, 1.222, 6.16, 0.778, -1.54, 1.222, 2.23]}, {"time": 1.6667, "x": 6.17, "y": 2.23, "curve": [2.111, 6.17, 2.556, -3.41, 2.111, 2.23, 2.556, -1.54]}, {"time": 3, "x": -3.41, "y": -1.54, "curve": [3.444, -3.42, 3.889, 6.16, 3.444, -1.54, 3.889, 2.23]}, {"time": 4.3333, "x": 6.17, "y": 2.23, "curve": [4.778, 6.17, 5.222, -3.41, 4.778, 2.23, 5.222, -1.54]}, {"time": 5.6667, "x": -3.41, "y": -1.54, "curve": [6, -3.42, 6.333, 6.17, 6, -1.54, 6.333, 2.23]}, {"time": 6.6667, "x": 6.17, "y": 2.23, "curve": [7.111, 6.17, 7.556, 1.38, 7.111, 2.23, 7.556, 0.34]}, {"time": 8, "x": 1.38, "y": 0.34, "curve": [8.444, 1.37, 8.889, 6.16, 8.444, 0.34, 8.889, 2.23]}, {"time": 9.3333, "x": 6.17, "y": 2.23, "curve": [9.778, 6.17, 10.222, -3.41, 9.778, 2.23, 10.222, -1.54]}, {"time": 10.6667, "x": -3.41, "y": -1.54, "curve": [11.111, -3.42, 11.556, 6.16, 11.111, -1.54, 11.556, 2.23]}, {"time": 12, "x": 6.17, "y": 2.23, "curve": [12.335, 6.17, 12.67, 0.8, 12.335, 2.23, 12.67, 0.12]}, {"time": 13, "x": -1.88, "y": -0.94}]}, "sh_R": {"translate": [{"x": -5.47, "y": -2.56, "curve": [0.114, -6.74, 0.224, -7.68, 0.114, -3.08, 0.224, -3.47]}, {"time": 0.3333, "x": -7.68, "y": -3.47, "curve": [0.778, -7.68, 1.222, 6.16, 0.778, -3.47, 1.222, 2.23]}, {"time": 1.6667, "x": 6.17, "y": 2.23, "curve": [2.111, 6.17, 2.556, -7.68, 2.111, 2.23, 2.556, -3.47]}, {"time": 3, "x": -7.68, "y": -3.47, "curve": [3.444, -7.69, 3.889, 6.16, 3.444, -3.47, 3.889, 2.23]}, {"time": 4.3333, "x": 6.17, "y": 2.23, "curve": [4.778, 6.17, 5.222, -7.68, 4.778, 2.23, 5.222, -3.47]}, {"time": 5.6667, "x": -7.68, "y": -3.47, "curve": [6, -7.69, 6.333, 6.17, 6, -3.47, 6.333, 2.23]}, {"time": 6.6667, "x": 6.17, "y": 2.23, "curve": [7.111, 6.17, 7.556, -0.76, 7.111, 2.23, 7.556, -0.62]}, {"time": 8, "x": -0.76, "y": -0.62, "curve": [8.444, -0.76, 8.889, 6.16, 8.444, -0.62, 8.889, 2.23]}, {"time": 9.3333, "x": 6.17, "y": 2.23, "curve": [9.778, 6.17, 10.222, -7.68, 9.778, 2.23, 10.222, -3.47]}, {"time": 10.6667, "x": -7.68, "y": -3.47, "curve": [11.111, -7.69, 11.556, 6.16, 11.111, -3.47, 11.556, 2.23]}, {"time": 12, "x": 6.17, "y": 2.23, "curve": [12.335, 6.17, 12.67, -1.59, 12.335, 2.23, 12.67, -0.96]}, {"time": 13, "x": -5.47, "y": -2.56}]}, "leg_L2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.86]}, {"time": 7.6667, "value": -0.86, "curve": [8.111, -0.86, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0]}, {"time": 7.6667, "curve": [8.111, 0, 8.556, 0]}, {"time": 9}]}, "leg_R2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.95]}, {"time": 7.6667, "value": -0.95, "curve": [8.111, -0.95, 8.556, 0]}, {"time": 9}]}, "leg_R3": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.06]}, {"time": 7.6667, "value": 0.06, "curve": [8.111, 0.06, 8.556, 0]}, {"time": 9}]}, "foot_R": {"rotate": [{"value": 7.05, "curve": [0.444, 7.05, 0.889, -8.02]}, {"time": 1.3333, "value": -8.02, "curve": [1.778, -8.03, 2.222, 7.05]}, {"time": 2.6667, "value": 7.05, "curve": [3.111, 7.05, 3.556, -8.02]}, {"time": 4, "value": -8.02, "curve": [4.444, -8.03, 4.889, 7.05]}, {"time": 5.3333, "value": 7.05, "curve": [5.667, 7.05, 6, -8.02]}, {"time": 6.3333, "value": -8.02, "curve": [6.778, -8.02, 7.222, -0.49]}, {"time": 7.6667, "value": -0.49, "curve": [8.111, -0.48, 8.556, -8.02]}, {"time": 9, "value": -8.02, "curve": [9.444, -8.03, 9.889, 7.05]}, {"time": 10.3333, "value": 7.05, "curve": [10.778, 7.05, 11.222, -8.02]}, {"time": 11.6667, "value": -8.02, "curve": [12.111, -8.03, 12.556, 7.05]}, {"time": 13, "value": 7.05}]}, "arm_L": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.56]}, {"time": 7.6667, "value": 0.56, "curve": [8.111, 0.56, 8.556, 0]}, {"time": 9}]}, "arm_L2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -1.28]}, {"time": 7.6667, "value": -1.28, "curve": [8.111, -1.28, 8.556, 0]}, {"time": 9}]}, "arm_R": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -3.13]}, {"time": 7.6667, "value": -3.13, "curve": [8.111, -3.13, 8.556, 0]}, {"time": 9}]}, "arm_R2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, -0.01, 7.222, 14.29]}, {"time": 7.6667, "value": 14.3, "curve": [8.111, 14.3, 8.556, 0]}, {"time": 9}]}, "arm_R5": {"rotate": [{"value": 1.31, "curve": [0.168, 2.74, 0.334, 3.91]}, {"time": 0.5, "value": 3.91, "curve": [0.944, 3.91, 1.389, -4.27]}, {"time": 1.8333, "value": -4.27, "curve": [2.278, -4.27, 2.722, 3.91]}, {"time": 3.1667, "value": 3.91, "curve": [3.611, 3.91, 4.056, -4.27]}, {"time": 4.5, "value": -4.27, "curve": [4.944, -4.27, 5.389, 3.93]}, {"time": 5.8333, "value": 3.91, "curve": [6.144, 3.89, 6.522, 73.09]}, {"time": 6.8333, "value": 73.09, "curve": [7.333, 73.09, 7.833, 66.14]}, {"time": 8.3333, "value": 66.14, "curve": [8.778, 66.14, 9.222, 73.05]}, {"time": 9.6667, "value": 73.09, "curve": [9.833, 73.11, 10, -2.93]}, {"time": 10.1667, "value": -2.93, "curve": [10.389, -2.94, 10.611, 3.91]}, {"time": 10.8333, "value": 3.91, "curve": [11.318, 3.91, 11.682, -4.27]}, {"time": 12.1667, "value": -4.27, "curve": [12.445, -4.27, 12.724, -1.09]}, {"time": 13, "value": 1.31}], "translate": [{"x": -3.7, "y": -1.24, "curve": [0.168, -7.33, 0.334, -10.3, 0.168, -5, 0.334, -8.08]}, {"time": 0.5, "x": -10.3, "y": -8.08, "curve": [0.944, -10.3, 1.389, 10.47, 0.944, -8.08, 1.389, 13.41]}, {"time": 1.8333, "x": 10.48, "y": 13.42, "curve": [2.278, 10.48, 2.722, -10.3, 2.278, 13.42, 2.722, -8.08]}, {"time": 3.1667, "x": -10.3, "y": -8.08, "curve": [3.611, -10.31, 4.056, 10.47, 3.611, -8.09, 4.056, 13.41]}, {"time": 4.5, "x": 10.48, "y": 13.42, "curve": [4.944, 10.48, 5.389, -10.36, 4.944, 13.42, 5.389, -8.06]}, {"time": 5.8333, "x": -10.3, "y": -8.08, "curve": [6.111, -10.27, 6.389, -169.59, 6.111, -8.09, 6.389, 47.25]}, {"time": 6.6667, "x": -169.59, "y": 47.25, "curve": [7.167, -169.6, 7.667, -134.03, 7.167, 47.25, 7.667, 40.29]}, {"time": 8.1667, "x": -134.02, "y": 40.29, "curve": [8.611, -134.01, 9.056, -169.5, 8.611, 40.29, 9.056, 47.24]}, {"time": 9.5, "x": -169.59, "y": 47.25, "curve": [9.722, -169.64, 9.944, 16.78, 9.722, 47.26, 9.944, 21.67]}, {"time": 10.1667, "x": 16.79, "y": 21.68, "curve": [10.389, 16.8, 10.611, -10.3, 10.389, 21.69, 10.611, -8.08]}, {"time": 10.8333, "x": -10.3, "y": -8.08, "curve": [11.278, -10.3, 11.722, 10.48, 11.278, -8.09, 11.722, 13.42]}, {"time": 12.1667, "x": 10.48, "y": 13.42, "curve": [12.445, 10.48, 12.724, 2.4, 12.445, 13.42, 12.724, 5.06]}, {"time": 13, "x": -3.7, "y": -1.24}]}, "hair_FR": {"rotate": [{"value": -2.22, "curve": [0.279, 0.66, 0.556, 4.5]}, {"time": 0.8333, "value": 4.5, "curve": [1.278, 4.5, 1.722, -5.35]}, {"time": 2.1667, "value": -5.36, "curve": [2.611, -5.36, 3.056, 4.5]}, {"time": 3.5, "value": 4.5, "curve": [3.944, 4.5, 4.389, -5.35]}, {"time": 4.8333, "value": -5.36, "curve": [5.278, -5.36, 5.722, 4.5]}, {"time": 6.1667, "value": 4.5, "curve": [6.5, 4.5, 6.833, -5.36]}, {"time": 7.1667, "value": -5.36, "curve": [7.611, -5.36, 8.056, -0.43]}, {"time": 8.5, "value": -0.43, "curve": [8.944, -0.43, 9.389, -5.35]}, {"time": 9.8333, "value": -5.36, "curve": [10.278, -5.36, 10.722, 4.5]}, {"time": 11.1667, "value": 4.5, "curve": [11.611, 4.5, 12.056, -5.35]}, {"time": 12.5, "value": -5.36, "curve": [12.667, -5.36, 12.835, -3.97]}, {"time": 13, "value": -2.22}]}, "hair_FL": {"rotate": [{"value": 1.06, "curve": [0.279, -1.86, 0.556, -5.75]}, {"time": 0.8333, "value": -5.75, "curve": [1.278, -5.75, 1.722, 4.24]}, {"time": 2.1667, "value": 4.24, "curve": [2.611, 4.24, 3.056, -5.75]}, {"time": 3.5, "value": -5.75, "curve": [3.944, -5.75, 4.389, 4.24]}, {"time": 4.8333, "value": 4.24, "curve": [5.278, 4.24, 5.722, -5.75]}, {"time": 6.1667, "value": -5.75, "curve": [6.5, -5.75, 6.833, 4.24]}, {"time": 7.1667, "value": 4.24, "curve": [7.611, 4.24, 8.056, -0.75]}, {"time": 8.5, "value": -0.76, "curve": [8.944, -0.76, 9.389, 4.24]}, {"time": 9.8333, "value": 4.24, "curve": [10.278, 4.24, 10.722, -5.75]}, {"time": 11.1667, "value": -5.75, "curve": [11.611, -5.75, 12.056, 4.24]}, {"time": 12.5, "value": 4.24, "curve": [12.667, 4.24, 12.835, 2.83]}, {"time": 13, "value": 1.06}]}, "RU_L": {"translate": [{"x": -7.17, "y": -1.9, "curve": [0.168, -20.23, 0.334, -30.94, 0.168, -5.37, 0.334, -8.21]}, {"time": 0.5, "x": -30.94, "y": -8.21, "curve": [0.944, -30.94, 1.389, 43.78, 0.944, -8.21, 1.389, 11.61]}, {"time": 1.8333, "x": 43.8, "y": 11.61, "curve": [2.278, 43.81, 2.722, -30.93, 2.278, 11.62, 2.722, -8.2]}, {"time": 3.1667, "x": -30.94, "y": -8.21, "curve": [3.611, -30.96, 4.056, 43.78, 3.611, -8.21, 4.056, 11.61]}, {"time": 4.5, "x": 43.8, "y": 11.61, "curve": [4.944, 43.81, 5.389, -30.92, 4.944, 11.62, 5.389, -8.2]}, {"time": 5.8333, "x": -30.94, "y": -8.21, "curve": [6.167, -30.96, 6.5, 43.79, 6.167, -8.21, 6.5, 11.61]}, {"time": 6.8333, "x": 43.8, "y": 11.61, "curve": [7.278, 43.8, 7.722, 6.43, 7.278, 11.62, 7.722, 1.71]}, {"time": 8.1667, "x": 6.43, "y": 1.7, "curve": [8.611, 6.42, 9.056, 43.78, 8.611, 1.7, 9.056, 11.61]}, {"time": 9.5, "x": 43.8, "y": 11.61, "curve": [9.944, 43.81, 10.389, -30.93, 9.944, 11.62, 10.389, -8.2]}, {"time": 10.8333, "x": -30.94, "y": -8.21, "curve": [11.278, -30.96, 11.722, 43.78, 11.278, -8.21, 11.722, 11.61]}, {"time": 12.1667, "x": 43.8, "y": 11.61, "curve": [12.445, 43.81, 12.724, 14.75, 12.445, 11.62, 12.724, 3.91]}, {"time": 13, "x": -7.17, "y": -1.9}], "scale": [{"x": 1.044, "y": 0.962, "curve": [0.167, 1.004, 0.333, 0.923, 0.167, 1, 0.333, 1.075]}, {"time": 0.5, "x": 0.923, "y": 1.075, "curve": [0.722, 0.923, 0.944, 1.067, 0.722, 1.075, 0.944, 0.941]}, {"time": 1.1667, "x": 1.067, "y": 0.941, "curve": [1.389, 1.068, 1.611, 0.923, 1.389, 0.941, 1.611, 1.075]}, {"time": 1.8333, "x": 0.923, "y": 1.075, "curve": [2.056, 0.923, 2.278, 1.067, 2.056, 1.075, 2.278, 0.941]}, {"time": 2.5, "x": 1.067, "y": 0.941, "curve": [2.722, 1.068, 2.944, 0.923, 2.722, 0.941, 2.944, 1.075]}, {"time": 3.1667, "x": 0.923, "y": 1.075, "curve": [3.389, 0.923, 3.611, 1.067, 3.389, 1.075, 3.611, 0.941]}, {"time": 3.8333, "x": 1.067, "y": 0.941, "curve": [4.056, 1.068, 4.278, 0.923, 4.056, 0.941, 4.278, 1.075]}, {"time": 4.5, "x": 0.923, "y": 1.075, "curve": [4.722, 0.923, 4.944, 1.067, 4.722, 1.075, 4.944, 0.941]}, {"time": 5.1667, "x": 1.067, "y": 0.941, "curve": [5.389, 1.068, 5.611, 0.923, 5.389, 0.941, 5.611, 1.075]}, {"time": 5.8333, "x": 0.923, "y": 1.075, "curve": [6, 0.923, 6.167, 1.067, 6, 1.075, 6.167, 0.941]}, {"time": 6.3333, "x": 1.067, "y": 0.941, "curve": [6.5, 1.068, 6.667, 0.923, 6.5, 0.941, 6.667, 1.075]}, {"time": 6.8333, "x": 0.923, "y": 1.075, "curve": [7.056, 0.923, 7.278, 1.067, 7.056, 1.075, 7.278, 0.941]}, {"time": 7.5, "x": 1.067, "y": 0.941, "curve": [7.722, 1.068, 7.944, 0.923, 7.722, 0.941, 7.944, 1.075]}, {"time": 8.1667, "x": 0.923, "y": 1.075, "curve": [8.389, 0.923, 8.611, 1.067, 8.389, 1.075, 8.611, 0.941]}, {"time": 8.8333, "x": 1.067, "y": 0.941, "curve": [9.056, 1.068, 9.278, 0.923, 9.056, 0.941, 9.278, 1.075]}, {"time": 9.5, "x": 0.923, "y": 1.075, "curve": [9.722, 0.923, 9.944, 1.067, 9.722, 1.075, 9.944, 0.941]}, {"time": 10.1667, "x": 1.067, "y": 0.941, "curve": [10.389, 1.068, 10.611, 0.923, 10.389, 0.941, 10.611, 1.075]}, {"time": 10.8333, "x": 0.923, "y": 1.075, "curve": [11.056, 0.923, 11.278, 1.067, 11.056, 1.075, 11.278, 0.941]}, {"time": 11.5, "x": 1.067, "y": 0.941, "curve": [11.722, 1.068, 11.944, 0.923, 11.722, 0.941, 11.944, 1.075]}, {"time": 12.1667, "x": 0.923, "y": 1.075, "curve": [12.389, 0.923, 12.611, 1.067, 12.389, 1.075, 12.611, 0.941]}, {"time": 12.8333, "x": 1.067, "y": 0.941, "curve": [12.889, 1.067, 12.944, 1.058, 12.889, 0.941, 12.944, 0.95]}, {"time": 13, "x": 1.044, "y": 0.962}]}, "RU_R": {"translate": [{"x": -8.57, "y": -2.27, "curve": [0.168, -21.98, 0.334, -32.99, 0.168, -5.83, 0.334, -8.75]}, {"time": 0.5, "x": -32.99, "y": -8.75, "curve": [0.944, -32.99, 1.389, 43.78, 0.944, -8.75, 1.389, 11.61]}, {"time": 1.8333, "x": 43.8, "y": 11.61, "curve": [2.278, 43.81, 2.722, -32.97, 2.278, 11.62, 2.722, -8.74]}, {"time": 3.1667, "x": -32.99, "y": -8.75, "curve": [3.611, -33.01, 4.056, 43.78, 3.611, -8.75, 4.056, 11.61]}, {"time": 4.5, "x": 43.8, "y": 11.61, "curve": [4.944, 43.81, 5.389, -32.96, 4.944, 11.62, 5.389, -8.74]}, {"time": 5.8333, "x": -32.99, "y": -8.75, "curve": [6.167, -33.01, 6.5, 43.79, 6.167, -8.75, 6.5, 11.61]}, {"time": 6.8333, "x": 43.8, "y": 11.61, "curve": [7.278, 43.81, 7.722, 5.41, 7.278, 11.62, 7.722, 1.44]}, {"time": 8.1667, "x": 5.4, "y": 1.43, "curve": [8.611, 5.39, 9.056, 43.78, 8.611, 1.43, 9.056, 11.61]}, {"time": 9.5, "x": 43.8, "y": 11.61, "curve": [9.944, 43.81, 10.389, -32.97, 9.944, 11.62, 10.389, -8.74]}, {"time": 10.8333, "x": -32.99, "y": -8.75, "curve": [11.278, -33.01, 11.722, 43.78, 11.278, -8.75, 11.722, 11.61]}, {"time": 12.1667, "x": 43.8, "y": 11.61, "curve": [12.445, 43.81, 12.724, 13.95, 12.445, 11.62, 12.724, 3.7]}, {"time": 13, "x": -8.57, "y": -2.27}], "scale": [{"x": 1.044, "y": 0.962, "curve": [0.167, 1.004, 0.333, 0.923, 0.167, 1, 0.333, 1.075]}, {"time": 0.5, "x": 0.923, "y": 1.075, "curve": [0.722, 0.923, 0.944, 1.067, 0.722, 1.075, 0.944, 0.941]}, {"time": 1.1667, "x": 1.067, "y": 0.941, "curve": [1.389, 1.068, 1.611, 0.923, 1.389, 0.941, 1.611, 1.075]}, {"time": 1.8333, "x": 0.923, "y": 1.075, "curve": [2.056, 0.923, 2.278, 1.067, 2.056, 1.075, 2.278, 0.941]}, {"time": 2.5, "x": 1.067, "y": 0.941, "curve": [2.722, 1.068, 2.944, 0.923, 2.722, 0.941, 2.944, 1.075]}, {"time": 3.1667, "x": 0.923, "y": 1.075, "curve": [3.389, 0.923, 3.611, 1.067, 3.389, 1.075, 3.611, 0.941]}, {"time": 3.8333, "x": 1.067, "y": 0.941, "curve": [4.056, 1.068, 4.278, 0.923, 4.056, 0.941, 4.278, 1.075]}, {"time": 4.5, "x": 0.923, "y": 1.075, "curve": [4.722, 0.923, 4.944, 1.067, 4.722, 1.075, 4.944, 0.941]}, {"time": 5.1667, "x": 1.067, "y": 0.941, "curve": [5.389, 1.068, 5.611, 0.923, 5.389, 0.941, 5.611, 1.075]}, {"time": 5.8333, "x": 0.923, "y": 1.075, "curve": [6, 0.923, 6.167, 1.067, 6, 1.075, 6.167, 0.941]}, {"time": 6.3333, "x": 1.067, "y": 0.941, "curve": [6.5, 1.068, 6.667, 0.923, 6.5, 0.941, 6.667, 1.075]}, {"time": 6.8333, "x": 0.923, "y": 1.075, "curve": [7.056, 0.923, 7.278, 1.067, 7.056, 1.075, 7.278, 0.941]}, {"time": 7.5, "x": 1.067, "y": 0.941, "curve": [7.722, 1.068, 7.944, 0.923, 7.722, 0.941, 7.944, 1.075]}, {"time": 8.1667, "x": 0.923, "y": 1.075, "curve": [8.389, 0.923, 8.611, 1.067, 8.389, 1.075, 8.611, 0.941]}, {"time": 8.8333, "x": 1.067, "y": 0.941, "curve": [9.056, 1.068, 9.278, 0.923, 9.056, 0.941, 9.278, 1.075]}, {"time": 9.5, "x": 0.923, "y": 1.075, "curve": [9.722, 0.923, 9.944, 1.067, 9.722, 1.075, 9.944, 0.941]}, {"time": 10.1667, "x": 1.067, "y": 0.941, "curve": [10.389, 1.068, 10.611, 0.923, 10.389, 0.941, 10.611, 1.075]}, {"time": 10.8333, "x": 0.923, "y": 1.075, "curve": [11.056, 0.923, 11.278, 1.067, 11.056, 1.075, 11.278, 0.941]}, {"time": 11.5, "x": 1.067, "y": 0.941, "curve": [11.722, 1.068, 11.944, 0.923, 11.722, 0.941, 11.944, 1.075]}, {"time": 12.1667, "x": 0.923, "y": 1.075, "curve": [12.389, 0.923, 12.611, 1.067, 12.389, 1.075, 12.611, 0.941]}, {"time": 12.8333, "x": 1.067, "y": 0.941, "curve": [12.889, 1.067, 12.944, 1.058, 12.889, 0.941, 12.944, 0.95]}, {"time": 13, "x": 1.044, "y": 0.962}]}, "RU_R2": {"translate": [{"x": -0.53, "curve": [0.225, -15.21, 0.446, -30.1, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -30.1, "curve": [1.111, -30.1, 1.556, 29.02, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 29.04, "curve": [2.444, 29.05, 2.889, -30.09, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -30.1, "curve": [3.778, -30.12, 4.222, 29.02, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 29.04, "curve": [5.111, 29.05, 5.556, -30.08, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -30.1, "curve": [6.333, -30.12, 6.667, 29.03, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 29.04, "curve": [7.444, 29.04, 7.889, -0.53, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -0.53, "curve": [8.778, -0.54, 9.222, 29.02, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 29.04, "curve": [10.111, 29.05, 10.556, -30.09, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -30.1, "curve": [11.444, -30.12, 11.889, 29.02, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 29.04, "curve": [12.557, 29.04, 12.781, 14.36, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -0.53}], "scale": [{"x": 1.067, "y": 0.941, "curve": [0.222, 1.068, 0.444, 0.923, 0.222, 0.941, 0.444, 1.075]}, {"time": 0.6667, "x": 0.923, "y": 1.075, "curve": [0.889, 0.923, 1.111, 1.067, 0.889, 1.075, 1.111, 0.941]}, {"time": 1.3333, "x": 1.067, "y": 0.941, "curve": [1.556, 1.068, 1.778, 0.923, 1.556, 0.941, 1.778, 1.075]}, {"time": 2, "x": 0.923, "y": 1.075, "curve": [2.222, 0.923, 2.444, 1.067, 2.222, 1.075, 2.444, 0.941]}, {"time": 2.6667, "x": 1.067, "y": 0.941, "curve": [2.889, 1.068, 3.111, 0.923, 2.889, 0.941, 3.111, 1.075]}, {"time": 3.3333, "x": 0.923, "y": 1.075, "curve": [3.556, 0.923, 3.778, 1.067, 3.556, 1.075, 3.778, 0.941]}, {"time": 4, "x": 1.067, "y": 0.941, "curve": [4.222, 1.068, 4.444, 0.923, 4.222, 0.941, 4.444, 1.075]}, {"time": 4.6667, "x": 0.923, "y": 1.075, "curve": [4.889, 0.923, 5.111, 1.067, 4.889, 1.075, 5.111, 0.941]}, {"time": 5.3333, "x": 1.067, "y": 0.941, "curve": [5.556, 1.068, 5.778, 0.923, 5.556, 0.941, 5.778, 1.075]}, {"time": 6, "x": 0.923, "y": 1.075, "curve": [6.167, 0.923, 6.333, 1.067, 6.167, 1.075, 6.333, 0.941]}, {"time": 6.5, "x": 1.067, "y": 0.941, "curve": [6.667, 1.068, 6.833, 0.923, 6.667, 0.941, 6.833, 1.075]}, {"time": 7, "x": 0.923, "y": 1.075, "curve": [7.222, 0.923, 7.444, 1.067, 7.222, 1.075, 7.444, 0.941]}, {"time": 7.6667, "x": 1.067, "y": 0.941, "curve": [7.889, 1.068, 8.111, 0.923, 7.889, 0.941, 8.111, 1.075]}, {"time": 8.3333, "x": 0.923, "y": 1.075, "curve": [8.556, 0.923, 8.778, 1.067, 8.556, 1.075, 8.778, 0.941]}, {"time": 9, "x": 1.067, "y": 0.941, "curve": [9.222, 1.068, 9.444, 0.923, 9.222, 0.941, 9.444, 1.075]}, {"time": 9.6667, "x": 0.923, "y": 1.075, "curve": [9.889, 0.923, 10.111, 1.067, 9.889, 1.075, 10.111, 0.941]}, {"time": 10.3333, "x": 1.067, "y": 0.941, "curve": [10.556, 1.068, 10.778, 0.923, 10.556, 0.941, 10.778, 1.075]}, {"time": 11, "x": 0.923, "y": 1.075, "curve": [11.222, 0.923, 11.444, 1.067, 11.222, 1.075, 11.444, 0.941]}, {"time": 11.6667, "x": 1.067, "y": 0.941, "curve": [11.889, 1.068, 12.111, 0.923, 11.889, 0.941, 12.111, 1.075]}, {"time": 12.3333, "x": 0.923, "y": 1.075, "curve": [12.556, 0.923, 12.778, 1.067, 12.556, 1.075, 12.778, 0.941]}, {"time": 13, "x": 1.067, "y": 0.941}]}, "RU_R3": {"translate": [{"x": 10.93, "curve": [0.279, -4.05, 0.556, -23.98, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -23.98, "curve": [1.278, -23.98, 1.722, 27.18, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 27.19, "curve": [2.611, 27.21, 3.056, -23.96, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -23.98, "curve": [3.944, -23.99, 4.389, 27.18, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 27.19, "curve": [5.278, 27.21, 5.722, -23.96, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -23.98, "curve": [6.5, -23.99, 6.833, 27.19, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 27.19, "curve": [7.611, 27.2, 8.056, 1.62, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 1.61, "curve": [8.944, 1.6, 9.389, 27.18, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 27.19, "curve": [10.278, 27.21, 10.722, -23.96, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -23.98, "curve": [11.611, -23.99, 12.056, 27.18, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 27.19, "curve": [12.667, 27.2, 12.835, 19.98, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 10.93}], "scale": [{"x": 1.044, "y": 0.962, "curve": [0.056, 1.058, 0.111, 1.067, 0.056, 0.95, 0.111, 0.941]}, {"time": 0.1667, "x": 1.067, "y": 0.941, "curve": [0.389, 1.068, 0.611, 0.923, 0.389, 0.941, 0.611, 1.075]}, {"time": 0.8333, "x": 0.923, "y": 1.075, "curve": [1.056, 0.923, 1.278, 1.067, 1.056, 1.075, 1.278, 0.941]}, {"time": 1.5, "x": 1.067, "y": 0.941, "curve": [1.722, 1.068, 1.944, 0.923, 1.722, 0.941, 1.944, 1.075]}, {"time": 2.1667, "x": 0.923, "y": 1.075, "curve": [2.389, 0.923, 2.611, 1.067, 2.389, 1.075, 2.611, 0.941]}, {"time": 2.8333, "x": 1.067, "y": 0.941, "curve": [3.056, 1.068, 3.278, 0.923, 3.056, 0.941, 3.278, 1.075]}, {"time": 3.5, "x": 0.923, "y": 1.075, "curve": [3.722, 0.923, 3.944, 1.067, 3.722, 1.075, 3.944, 0.941]}, {"time": 4.1667, "x": 1.067, "y": 0.941, "curve": [4.389, 1.068, 4.611, 0.923, 4.389, 0.941, 4.611, 1.075]}, {"time": 4.8333, "x": 0.923, "y": 1.075, "curve": [5.056, 0.923, 5.278, 1.067, 5.056, 1.075, 5.278, 0.941]}, {"time": 5.5, "x": 1.067, "y": 0.941, "curve": [5.722, 1.068, 5.944, 0.923, 5.722, 0.941, 5.944, 1.075]}, {"time": 6.1667, "x": 0.923, "y": 1.075, "curve": [6.333, 0.923, 6.5, 1.067, 6.333, 1.075, 6.5, 0.941]}, {"time": 6.6667, "x": 1.067, "y": 0.941, "curve": [6.833, 1.068, 7, 0.923, 6.833, 0.941, 7, 1.075]}, {"time": 7.1667, "x": 0.923, "y": 1.075, "curve": [7.389, 0.923, 7.611, 1.067, 7.389, 1.075, 7.611, 0.941]}, {"time": 7.8333, "x": 1.067, "y": 0.941, "curve": [8.056, 1.068, 8.278, 0.923, 8.056, 0.941, 8.278, 1.075]}, {"time": 8.5, "x": 0.923, "y": 1.075, "curve": [8.722, 0.923, 8.944, 1.067, 8.722, 1.075, 8.944, 0.941]}, {"time": 9.1667, "x": 1.067, "y": 0.941, "curve": [9.389, 1.068, 9.611, 0.923, 9.389, 0.941, 9.611, 1.075]}, {"time": 9.8333, "x": 0.923, "y": 1.075, "curve": [10.056, 0.923, 10.278, 1.067, 10.056, 1.075, 10.278, 0.941]}, {"time": 10.5, "x": 1.067, "y": 0.941, "curve": [10.722, 1.068, 10.944, 0.923, 10.722, 0.941, 10.944, 1.075]}, {"time": 11.1667, "x": 0.923, "y": 1.075, "curve": [11.389, 0.923, 11.611, 1.067, 11.389, 1.075, 11.611, 0.941]}, {"time": 11.8333, "x": 1.067, "y": 0.941, "curve": [12.056, 1.068, 12.278, 0.923, 12.056, 0.941, 12.278, 1.075]}, {"time": 12.5, "x": 0.923, "y": 1.075, "curve": [12.667, 0.923, 12.833, 1.004, 12.667, 1.075, 12.833, 1]}, {"time": 13, "x": 1.044, "y": 0.962}]}, "RU_L2": {"translate": [{"x": -1.01, "curve": [0.225, -15.94, 0.446, -31.07, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -31.07, "curve": [1.111, -31.07, 1.556, 29.02, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 29.04, "curve": [2.444, 29.05, 2.889, -31.05, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -31.07, "curve": [3.778, -31.08, 4.222, 29.02, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 29.04, "curve": [5.111, 29.05, 5.556, -31.04, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -31.07, "curve": [6.333, -31.08, 6.667, 29.03, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 29.04, "curve": [7.444, 29.04, 7.889, -1.01, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -1.01, "curve": [8.778, -1.02, 9.222, 29.02, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 29.04, "curve": [10.111, 29.05, 10.556, -31.05, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -31.07, "curve": [11.444, -31.08, 11.889, 29.02, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 29.04, "curve": [12.557, 29.04, 12.781, 14.12, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -1.01}], "scale": [{"x": 1.067, "y": 0.941, "curve": [0.222, 1.068, 0.444, 0.923, 0.222, 0.941, 0.444, 1.075]}, {"time": 0.6667, "x": 0.923, "y": 1.075, "curve": [0.889, 0.923, 1.111, 1.067, 0.889, 1.075, 1.111, 0.941]}, {"time": 1.3333, "x": 1.067, "y": 0.941, "curve": [1.556, 1.068, 1.778, 0.923, 1.556, 0.941, 1.778, 1.075]}, {"time": 2, "x": 0.923, "y": 1.075, "curve": [2.222, 0.923, 2.444, 1.067, 2.222, 1.075, 2.444, 0.941]}, {"time": 2.6667, "x": 1.067, "y": 0.941, "curve": [2.889, 1.068, 3.111, 0.923, 2.889, 0.941, 3.111, 1.075]}, {"time": 3.3333, "x": 0.923, "y": 1.075, "curve": [3.556, 0.923, 3.778, 1.067, 3.556, 1.075, 3.778, 0.941]}, {"time": 4, "x": 1.067, "y": 0.941, "curve": [4.222, 1.068, 4.444, 0.923, 4.222, 0.941, 4.444, 1.075]}, {"time": 4.6667, "x": 0.923, "y": 1.075, "curve": [4.889, 0.923, 5.111, 1.067, 4.889, 1.075, 5.111, 0.941]}, {"time": 5.3333, "x": 1.067, "y": 0.941, "curve": [5.556, 1.068, 5.778, 0.923, 5.556, 0.941, 5.778, 1.075]}, {"time": 6, "x": 0.923, "y": 1.075, "curve": [6.167, 0.923, 6.333, 1.067, 6.167, 1.075, 6.333, 0.941]}, {"time": 6.5, "x": 1.067, "y": 0.941, "curve": [6.667, 1.068, 6.833, 0.923, 6.667, 0.941, 6.833, 1.075]}, {"time": 7, "x": 0.923, "y": 1.075, "curve": [7.222, 0.923, 7.444, 1.067, 7.222, 1.075, 7.444, 0.941]}, {"time": 7.6667, "x": 1.067, "y": 0.941, "curve": [7.889, 1.068, 8.111, 0.923, 7.889, 0.941, 8.111, 1.075]}, {"time": 8.3333, "x": 0.923, "y": 1.075, "curve": [8.556, 0.923, 8.778, 1.067, 8.556, 1.075, 8.778, 0.941]}, {"time": 9, "x": 1.067, "y": 0.941, "curve": [9.222, 1.068, 9.444, 0.923, 9.222, 0.941, 9.444, 1.075]}, {"time": 9.6667, "x": 0.923, "y": 1.075, "curve": [9.889, 0.923, 10.111, 1.067, 9.889, 1.075, 10.111, 0.941]}, {"time": 10.3333, "x": 1.067, "y": 0.941, "curve": [10.556, 1.068, 10.778, 0.923, 10.556, 0.941, 10.778, 1.075]}, {"time": 11, "x": 0.923, "y": 1.075, "curve": [11.222, 0.923, 11.444, 1.067, 11.222, 1.075, 11.444, 0.941]}, {"time": 11.6667, "x": 1.067, "y": 0.941, "curve": [11.889, 1.068, 12.111, 0.923, 11.889, 0.941, 12.111, 1.075]}, {"time": 12.3333, "x": 0.923, "y": 1.075, "curve": [12.556, 0.923, 12.778, 1.067, 12.556, 1.075, 12.778, 0.941]}, {"time": 13, "x": 1.067, "y": 0.941}]}, "RU_L3": {"translate": [{"x": 10.35, "curve": [0.279, -5.16, 0.556, -25.8, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -25.8, "curve": [1.278, -25.8, 1.722, 27.18, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 27.19, "curve": [2.611, 27.21, 3.056, -25.79, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -25.8, "curve": [3.944, -25.81, 4.389, 27.18, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 27.19, "curve": [5.278, 27.21, 5.722, -25.78, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -25.8, "curve": [6.5, -25.81, 6.833, 27.19, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 27.19, "curve": [7.611, 27.2, 8.056, 0.7, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 0.7, "curve": [8.944, 0.69, 9.389, 27.18, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 27.19, "curve": [10.278, 27.21, 10.722, -25.79, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -25.8, "curve": [11.611, -25.81, 12.056, 27.18, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 27.19, "curve": [12.667, 27.2, 12.835, 19.72, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 10.35}], "scale": [{"x": 1.044, "y": 0.962, "curve": [0.056, 1.058, 0.111, 1.067, 0.056, 0.95, 0.111, 0.941]}, {"time": 0.1667, "x": 1.067, "y": 0.941, "curve": [0.389, 1.068, 0.611, 0.923, 0.389, 0.941, 0.611, 1.075]}, {"time": 0.8333, "x": 0.923, "y": 1.075, "curve": [1.056, 0.923, 1.278, 1.067, 1.056, 1.075, 1.278, 0.941]}, {"time": 1.5, "x": 1.067, "y": 0.941, "curve": [1.722, 1.068, 1.944, 0.923, 1.722, 0.941, 1.944, 1.075]}, {"time": 2.1667, "x": 0.923, "y": 1.075, "curve": [2.389, 0.923, 2.611, 1.067, 2.389, 1.075, 2.611, 0.941]}, {"time": 2.8333, "x": 1.067, "y": 0.941, "curve": [3.056, 1.068, 3.278, 0.923, 3.056, 0.941, 3.278, 1.075]}, {"time": 3.5, "x": 0.923, "y": 1.075, "curve": [3.722, 0.923, 3.944, 1.067, 3.722, 1.075, 3.944, 0.941]}, {"time": 4.1667, "x": 1.067, "y": 0.941, "curve": [4.389, 1.068, 4.611, 0.923, 4.389, 0.941, 4.611, 1.075]}, {"time": 4.8333, "x": 0.923, "y": 1.075, "curve": [5.056, 0.923, 5.278, 1.067, 5.056, 1.075, 5.278, 0.941]}, {"time": 5.5, "x": 1.067, "y": 0.941, "curve": [5.722, 1.068, 5.944, 0.923, 5.722, 0.941, 5.944, 1.075]}, {"time": 6.1667, "x": 0.923, "y": 1.075, "curve": [6.333, 0.923, 6.5, 1.067, 6.333, 1.075, 6.5, 0.941]}, {"time": 6.6667, "x": 1.067, "y": 0.941, "curve": [6.833, 1.068, 7, 0.923, 6.833, 0.941, 7, 1.075]}, {"time": 7.1667, "x": 0.923, "y": 1.075, "curve": [7.389, 0.923, 7.611, 1.067, 7.389, 1.075, 7.611, 0.941]}, {"time": 7.8333, "x": 1.067, "y": 0.941, "curve": [8.056, 1.068, 8.278, 0.923, 8.056, 0.941, 8.278, 1.075]}, {"time": 8.5, "x": 0.923, "y": 1.075, "curve": [8.722, 0.923, 8.944, 1.067, 8.722, 1.075, 8.944, 0.941]}, {"time": 9.1667, "x": 1.067, "y": 0.941, "curve": [9.389, 1.068, 9.611, 0.923, 9.389, 0.941, 9.611, 1.075]}, {"time": 9.8333, "x": 0.923, "y": 1.075, "curve": [10.056, 0.923, 10.278, 1.067, 10.056, 1.075, 10.278, 0.941]}, {"time": 10.5, "x": 1.067, "y": 0.941, "curve": [10.722, 1.068, 10.944, 0.923, 10.722, 0.941, 10.944, 1.075]}, {"time": 11.1667, "x": 0.923, "y": 1.075, "curve": [11.389, 0.923, 11.611, 1.067, 11.389, 1.075, 11.611, 0.941]}, {"time": 11.8333, "x": 1.067, "y": 0.941, "curve": [12.056, 1.068, 12.278, 0.923, 12.056, 0.941, 12.278, 1.075]}, {"time": 12.5, "x": 0.923, "y": 1.075, "curve": [12.667, 0.923, 12.833, 1.004, 12.667, 1.075, 12.833, 1]}, {"time": 13, "x": 1.044, "y": 0.962}]}, "leg_R4": {"rotate": [{"value": 0.29, "curve": "stepped"}, {"time": 5.3333, "value": 0.29, "curve": [5.667, 0.29, 6, 0.29]}, {"time": 6.3333, "value": 0.29, "curve": [6.778, 0.29, 7.222, -0.62]}, {"time": 7.6667, "value": -0.62, "curve": [8.111, -0.62, 8.556, 0.29]}, {"time": 9, "value": 0.29}]}, "leg_R5": {"rotate": [{"value": -0.38, "curve": "stepped"}, {"time": 6.3333, "value": -0.38, "curve": [6.778, -0.38, 7.222, -0.38]}, {"time": 7.6667, "value": -0.38, "curve": [8.111, -0.38, 8.556, -0.38]}, {"time": 9, "value": -0.38}]}, "leg_L5": {"rotate": [{"value": -0.01, "curve": "stepped"}, {"time": 5.3333, "value": -0.01, "curve": [5.667, -0.01, 6, -0.01]}, {"time": 6.3333, "value": -0.01, "curve": [6.778, -0.01, 7.222, -0.87]}, {"time": 7.6667, "value": -0.87, "curve": [8.111, -0.88, 8.556, -0.01]}, {"time": 9, "value": -0.01}]}, "sh_L2": {"rotate": [{"value": -0.13, "curve": "stepped"}, {"time": 5.3333, "value": -0.13, "curve": [5.667, -0.13, 6, -0.13]}, {"time": 6.3333, "value": -0.13, "curve": [6.778, -0.13, 7.222, 0.45]}, {"time": 7.6667, "value": 0.45, "curve": [8.111, 0.45, 8.556, -0.13]}, {"time": 9, "value": -0.13}]}, "sh_L3": {"rotate": [{"value": 0.03, "curve": "stepped"}, {"time": 5.3333, "value": 0.03, "curve": [5.667, 0.03, 6, 0.03]}, {"time": 6.3333, "value": 0.03, "curve": [6.778, 0.03, 7.222, -1.24]}, {"time": 7.6667, "value": -1.24, "curve": [8.111, -1.24, 8.556, 0.03]}, {"time": 9, "value": 0.03}]}, "sh_R2": {"rotate": [{"value": 0.06, "curve": "stepped"}, {"time": 5.3333, "value": 0.06, "curve": [5.667, 0.06, 6, 0.06]}, {"time": 6.3333, "value": 0.06, "curve": [6.778, 0.06, 7.222, -1.8]}, {"time": 7.6667, "value": -1.8, "curve": [8.111, -1.8, 8.556, 0.06]}, {"time": 9, "value": 0.06}]}, "sh_R3": {"rotate": [{"value": -0.07, "curve": "stepped"}, {"time": 5.3333, "value": -0.07, "curve": [5.667, -0.07, 6, -0.07]}, {"time": 6.3333, "value": -0.07, "curve": [6.778, -0.07, 7.222, 11.79]}, {"time": 7.6667, "value": 11.8, "curve": [8.111, 11.8, 8.556, -0.07]}, {"time": 9, "value": -0.07}]}, "headround3": {"translate": [{"x": 15.73, "curve": [0.279, -67.09, 0.556, -177.25, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -177.25, "curve": [1.278, -177.25, 1.722, 105.59, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 105.66, "curve": [2.611, 105.74, 3.056, -177.18, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -177.25, "curve": [3.944, -177.33, 4.389, 105.59, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 105.66, "curve": [5.278, 105.74, 5.722, -177.16, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -177.25, "curve": [6.5, -177.33, 6.833, 105.64, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 105.66, "curve": [7.611, 105.7, 8.056, -35.76, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -35.8, "curve": [8.944, -35.83, 9.389, 105.59, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 105.66, "curve": [10.278, 105.74, 10.722, -177.18, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -177.25, "curve": [11.611, -177.33, 12.056, 105.59, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 105.66, "curve": [12.667, 105.69, 12.835, 65.77, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 15.73}]}, "headround": {"translate": [{"y": 186.72, "curve": [0.336, 0, 0.668, 0, 0.336, 39.96, 0.668, -249.83]}, {"time": 1, "y": -249.83, "curve": [1.444, 0, 1.889, 0, 1.444, -249.83, 1.889, 269.68]}, {"time": 2.3333, "y": 269.81, "curve": [2.778, 0, 3.222, 0, 2.778, 269.94, 3.222, -249.7]}, {"time": 3.6667, "y": -249.83, "curve": [4.111, 0, 4.556, 0, 4.111, -249.96, 4.556, 269.68]}, {"time": 5, "y": 269.81, "curve": [5.333, 0, 5.667, 0, 5.333, 269.91, 5.667, -249.64]}, {"time": 6, "y": -249.83, "curve": [6.333, 0, 6.667, 0, 6.333, -250.02, 6.667, 490.02]}, {"time": 7, "y": 490.05, "curve": [7.444, 0, 7.889, 0, 7.444, 490.09, 7.889, 291.23]}, {"time": 8.3333, "y": 291.18, "curve": [8.778, 0, 9.222, 0, 8.778, 291.13, 9.222, 489.86]}, {"time": 9.6667, "y": 490.05, "curve": [10, 0, 10.333, 0, 10, 490.19, 10.333, -249.83]}, {"time": 10.6667, "y": -249.83, "curve": [11.333, 0, 12, 0, 11.333, -249.83, 12, 269.61]}, {"time": 12.6667, "y": 269.81, "curve": [12.779, 0, 12.892, 0, 12.779, 269.84, 12.892, 236.52]}, {"time": 13, "y": 186.72}]}, "bodyround": {"translate": [{"x": 38.35, "y": -82.15, "curve": [0.168, 74.02, 0.334, 103.28, 0.168, -158.55, 0.334, -221.22]}, {"time": 0.5, "x": 103.28, "y": -221.22, "curve": [0.944, 103.28, 1.389, -100.8, 0.944, -221.22, 1.389, 215.91]}, {"time": 1.8333, "x": -100.85, "y": 216.02, "curve": [2.278, -100.9, 2.722, 103.23, 2.278, 216.13, 2.722, -221.11]}, {"time": 3.1667, "x": 103.28, "y": -221.22, "curve": [3.611, 103.33, 4.056, -100.8, 3.611, -221.33, 4.056, 215.91]}, {"time": 4.5, "x": -100.85, "y": 216.02, "curve": [4.944, -100.9, 5.389, 103.21, 4.944, 216.13, 5.389, -221.08]}, {"time": 5.8333, "x": 103.28, "y": -221.22, "curve": [6.167, 103.33, 6.5, -100.83, 6.167, -221.33, 6.5, 215.98]}, {"time": 6.8333, "x": -100.85, "y": 216.02, "curve": [7.278, -100.88, 7.722, 1.19, 7.278, 216.07, 7.722, -2.55]}, {"time": 8.1667, "x": 1.22, "y": -2.6, "curve": [8.611, 1.24, 9.056, -100.8, 8.611, -2.66, 9.056, 215.91]}, {"time": 9.5, "x": -100.85, "y": 216.02, "curve": [9.944, -100.9, 10.389, 103.23, 9.944, 216.13, 10.389, -221.11]}, {"time": 10.8333, "x": 103.28, "y": -221.22, "curve": [11.278, 103.33, 11.722, -100.8, 11.278, -221.33, 11.722, 215.91]}, {"time": 12.1667, "x": -100.85, "y": 216.02, "curve": [12.445, -100.88, 12.724, -21.51, 12.445, 216.09, 12.724, 46.08]}, {"time": 13, "x": 38.35, "y": -82.15}]}, "tunround": {"translate": [{"x": 221.99, "curve": [0.057, 235.43, 0.112, 245.67, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": 245.67, "curve": [0.611, 245.67, 1.056, -257.88, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": -258, "curve": [1.944, -258.13, 2.389, 245.54, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": 245.67, "curve": [3.278, 245.8, 3.722, -257.88, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": -258, "curve": [4.611, -258.13, 5.056, 245.5, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": 245.67, "curve": [5.833, 245.8, 6.167, -257.96, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": -258, "curve": [6.944, -258.07, 7.389, -6.23, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -6.17, "curve": [8.278, -6.1, 8.722, -257.88, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -258, "curve": [9.611, -258.13, 10.056, 245.54, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 245.67, "curve": [10.944, 245.8, 11.389, -257.88, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": -258, "curve": [12.223, -258.12, 12.613, 126.48, 12.223, 0, 12.613, 0]}, {"time": 13, "x": 221.99}]}, "eye_L": {"translate": [{"time": 2.0667, "curve": [2.1, 0, 2.133, -1.65, 2.1, 0, 2.133, 0]}, {"time": 2.1667, "x": -1.65, "curve": "stepped"}, {"time": 3.2333, "x": -1.65, "curve": [3.267, -1.65, 3.3, -0.94, 3.267, 0, 3.3, 1.44]}, {"time": 3.3333, "x": -0.94, "y": 1.44, "curve": "stepped"}, {"time": 3.9, "x": -0.94, "y": 1.44, "curve": [3.933, -0.94, 3.967, 0, 3.933, 1.44, 3.967, 0]}, {"time": 4, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -0.82, 6.2, 0, 6.233, 9.22]}, {"time": 6.2667, "x": -0.82, "y": 9.22, "curve": "stepped"}, {"time": 6.8333, "x": -0.82, "y": 9.22, "curve": [6.867, -0.82, 6.9, -2.87, 6.867, 9.22, 6.9, 7.86]}, {"time": 6.9333, "x": -2.87, "y": 7.86, "curve": "stepped"}, {"time": 8, "x": -2.87, "y": 7.86, "curve": [8.033, -2.87, 8.067, -0.71, 8.033, 7.86, 8.067, 7.86]}, {"time": 8.1, "x": -0.71, "y": 7.86, "curve": "stepped"}, {"time": 8.5, "x": -0.71, "y": 7.86, "curve": [8.533, -0.71, 8.567, -0.82, 8.533, 7.86, 8.567, 9.22]}, {"time": 8.6, "x": -0.82, "y": 9.22, "curve": "stepped"}, {"time": 10, "x": -0.82, "y": 9.22, "curve": [10.033, -0.82, 10.067, 0, 10.033, 9.22, 10.067, 0]}, {"time": 10.1}]}, "eye_R": {"translate": [{"time": 2.0667, "curve": [2.1, 0, 2.133, -1.65, 2.1, 0, 2.133, 0]}, {"time": 2.1667, "x": -1.65, "curve": "stepped"}, {"time": 3.2333, "x": -1.65, "curve": [3.267, -1.65, 3.3, -0.94, 3.267, 0, 3.3, 1.44]}, {"time": 3.3333, "x": -0.94, "y": 1.44, "curve": "stepped"}, {"time": 3.9, "x": -0.94, "y": 1.44, "curve": [3.933, -0.94, 3.967, 0, 3.933, 1.44, 3.967, 0]}, {"time": 4, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -0.82, 6.2, 0, 6.233, 7.98]}, {"time": 6.2667, "x": -0.82, "y": 7.98, "curve": "stepped"}, {"time": 6.8333, "x": -0.82, "y": 7.98, "curve": [6.867, -0.82, 6.9, -2.87, 6.867, 7.98, 6.9, 6.97]}, {"time": 6.9333, "x": -2.87, "y": 6.97, "curve": "stepped"}, {"time": 8, "x": -2.87, "y": 6.97, "curve": [8.033, -2.87, 8.067, -0.71, 8.033, 6.97, 8.067, 6.97]}, {"time": 8.1, "x": -0.71, "y": 6.97, "curve": "stepped"}, {"time": 8.5, "x": -0.71, "y": 6.97, "curve": [8.533, -0.71, 8.567, -0.82, 8.533, 6.97, 8.567, 7.98]}, {"time": 8.6, "x": -0.82, "y": 7.98, "curve": "stepped"}, {"time": 10, "x": -0.82, "y": 7.98, "curve": [10.033, -0.82, 10.067, 0, 10.033, 7.98, 10.067, 0]}, {"time": 10.1}], "scale": [{"time": 6.1667, "curve": [6.2, 1, 6.233, 1, 6.2, 1, 6.233, 0.749]}, {"time": 6.2667, "y": 0.749, "curve": "stepped"}, {"time": 10, "y": 0.749, "curve": [10.033, 1, 10.067, 1, 10.033, 0.749, 10.067, 1]}, {"time": 10.1}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-11.86731, -0.43311, -11.86719, -0.43372, -10.71216, -0.25296, -10.71106, -0.25333, -9.17163, 0.22593, -9.16992, 0.22565, -6.54993, 0.34239, -6.547, 0.3421, -4.88953, -0.02147, -4.88928, -0.02164, -3.03442, 0.01116, -3.03418, 0.01103, -3.03442, 0.01116, -3.03418, 0.01103, -1.48547, 0.03841, -1.48535, 0.0383, -0.64526, -0.01135, -0.64526, -0.01144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.3551, -0.02384, -1.3551, -0.02403, -6.00146, -0.10557, -6.00146, -0.10594, -10.89954, -0.51445, -10.89917, -0.51482, 0, 0, 0, 0, -3.77319, 0.02035, -3.77319, 0.0202, -7.47876, 0.62893, -7.47876, 0.62875, -11.34412, 0.08639, -11.34412, 0.08609, -12.67139, -0.31816, -12.67139, -0.31854, -11.72168, -0.11086, -11.7218, -0.11116, -8.58093, 0.13499, -8.58093, 0.13474, -4.10083, 0.11848, -4.10083, 0.11835, -1.43262, 0.1654, -1.43262, 0.16531], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-11.86731, -0.43311, -11.86719, -0.43372, -10.71216, -0.25296, -10.71106, -0.25333, -9.17163, 0.22593, -9.16992, 0.22565, -6.54993, 0.34239, -6.547, 0.3421, -4.88953, -0.02147, -4.88928, -0.02164, -3.03442, 0.01116, -3.03418, 0.01103, -3.03442, 0.01116, -3.03418, 0.01103, -1.48547, 0.03841, -1.48535, 0.0383, -0.64526, -0.01135, -0.64526, -0.01144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.3551, -0.02384, -1.3551, -0.02403, -6.00146, -0.10557, -6.00146, -0.10594, -10.89954, -0.51445, -10.89917, -0.51482, 0, 0, 0, 0, -3.77319, 0.02035, -3.77319, 0.0202, -7.47876, 0.62893, -7.47876, 0.62875, -11.34412, 0.08639, -11.34412, 0.08609, -12.67139, -0.31816, -12.67139, -0.31854, -11.72168, -0.11086, -11.7218, -0.11116, -8.58093, 0.13499, -8.58093, 0.13474, -4.10083, 0.11848, -4.10083, 0.11835, -1.43262, 0.1654, -1.43262, 0.16531], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-11.86731, -0.43311, -11.86719, -0.43372, -10.71216, -0.25296, -10.71106, -0.25333, -9.17163, 0.22593, -9.16992, 0.22565, -6.54993, 0.34239, -6.547, 0.3421, -4.88953, -0.02147, -4.88928, -0.02164, -3.03442, 0.01116, -3.03418, 0.01103, -3.03442, 0.01116, -3.03418, 0.01103, -1.48547, 0.03841, -1.48535, 0.0383, -0.64526, -0.01135, -0.64526, -0.01144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.3551, -0.02384, -1.3551, -0.02403, -6.00146, -0.10557, -6.00146, -0.10594, -10.89954, -0.51445, -10.89917, -0.51482, 0, 0, 0, 0, -3.77319, 0.02035, -3.77319, 0.0202, -7.47876, 0.62893, -7.47876, 0.62875, -11.34412, 0.08639, -11.34412, 0.08609, -12.67139, -0.31816, -12.67139, -0.31854, -11.72168, -0.11086, -11.7218, -0.11116, -8.58093, 0.13499, -8.58093, 0.13474, -4.10083, 0.11848, -4.10083, 0.11835, -1.43262, 0.1654, -1.43262, 0.16531], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-11.37024, 0.52468, -11.36816, 0.52425, -11.48718, 0.74387, -11.48499, 0.74348, -11.85718, 0.2428, -11.85706, 0.24237, -9.33606, 0.41002, -9.33691, 0.40948, -4.87891, 0.11422, -4.87891, 0.11388, -1.58289, 0.21218, -1.58276, 0.21187, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.92273, -0.07169, -1.92261, -0.07185, -3.81311, 0.23239, -3.8125, 0.2323, -3.81311, 0.23239, -3.8125, 0.2323, -8.52808, -0.49294, -8.52515, -0.49339, -4.74939, -0.36, -4.74939, -0.36026, -9.46106, 0.33118, -9.46179, 0.3311, -12.13281, 0.29776, -12.13293, 0.29747, -11.88025, 0.18822, -11.88013, 0.18785, -9.44482, 0.15391, -9.44495, 0.15347, -5.64209, 0.02079, -5.64209, 0.02046, -1.24219, 0.09818, -1.24219, 0.09789, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.27246, 0.03291, -1.27246, 0.03281], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-11.37024, 0.52468, -11.36816, 0.52425, -11.48718, 0.74387, -11.48499, 0.74348, -11.85718, 0.2428, -11.85706, 0.24237, -9.33606, 0.41002, -9.33691, 0.40948, -4.87891, 0.11422, -4.87891, 0.11388, -1.58289, 0.21218, -1.58276, 0.21187, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.92273, -0.07169, -1.92261, -0.07185, -3.81311, 0.23239, -3.8125, 0.2323, -3.81311, 0.23239, -3.8125, 0.2323, -8.52808, -0.49294, -8.52515, -0.49339, -4.74939, -0.36, -4.74939, -0.36026, -9.46106, 0.33118, -9.46179, 0.3311, -12.13281, 0.29776, -12.13293, 0.29747, -11.88025, 0.18822, -11.88013, 0.18785, -9.44482, 0.15391, -9.44495, 0.15347, -5.64209, 0.02079, -5.64209, 0.02046, -1.24219, 0.09818, -1.24219, 0.09789, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.27246, 0.03291, -1.27246, 0.03281], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-11.37024, 0.52468, -11.36816, 0.52425, -11.48718, 0.74387, -11.48499, 0.74348, -11.85718, 0.2428, -11.85706, 0.24237, -9.33606, 0.41002, -9.33691, 0.40948, -4.87891, 0.11422, -4.87891, 0.11388, -1.58289, 0.21218, -1.58276, 0.21187, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.92273, -0.07169, -1.92261, -0.07185, -3.81311, 0.23239, -3.8125, 0.2323, -3.81311, 0.23239, -3.8125, 0.2323, -8.52808, -0.49294, -8.52515, -0.49339, -4.74939, -0.36, -4.74939, -0.36026, -9.46106, 0.33118, -9.46179, 0.3311, -12.13281, 0.29776, -12.13293, 0.29747, -11.88025, 0.18822, -11.88013, 0.18785, -9.44482, 0.15391, -9.44495, 0.15347, -5.64209, 0.02079, -5.64209, 0.02046, -1.24219, 0.09818, -1.24219, 0.09789, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.27246, 0.03291, -1.27246, 0.03281], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 314, "vertices": [-4.76953, -0.08388, -4.76929, -0.08421, -7.80505, -0.13727, -7.80383, -0.13762, -11.27759, 0.09073, -11.27698, 0.09041, -11.49597, 0.15919, -11.49622, 0.15881, -8.81335, -0.29958, -8.8136, -0.29973, -4.48315, 0.06571, -4.48291, 0.06551, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.51758, -0.02668, -1.51758, -0.02689, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.25195, -0.05719, -3.25195, -0.05742, -7.52014, 0.08458, -7.5188, 0.08429, -10.84619, 0.17062, -10.84558, 0.17024, -11.91638, -0.571, -11.91553, -0.57137, -10.90991, -0.26416, -10.91052, -0.2646, -7.51404, -0.20444, -7.51416, -0.2047, -2.30884, -0.25742, -2.30847, -0.25764], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 314, "vertices": [-4.76953, -0.08388, -4.76929, -0.08421, -7.80505, -0.13727, -7.80383, -0.13762, -11.27759, 0.09073, -11.27698, 0.09041, -11.49597, 0.15919, -11.49622, 0.15881, -8.81335, -0.29958, -8.8136, -0.29973, -4.48315, 0.06571, -4.48291, 0.06551, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.51758, -0.02668, -1.51758, -0.02689, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.25195, -0.05719, -3.25195, -0.05742, -7.52014, 0.08458, -7.5188, 0.08429, -10.84619, 0.17062, -10.84558, 0.17024, -11.91638, -0.571, -11.91553, -0.57137, -10.90991, -0.26416, -10.91052, -0.2646, -7.51404, -0.20444, -7.51416, -0.2047, -2.30884, -0.25742, -2.30847, -0.25764], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 314, "vertices": [-4.76953, -0.08388, -4.76929, -0.08421, -7.80505, -0.13727, -7.80383, -0.13762, -11.27759, 0.09073, -11.27698, 0.09041, -11.49597, 0.15919, -11.49622, 0.15881, -8.81335, -0.29958, -8.8136, -0.29973, -4.48315, 0.06571, -4.48291, 0.06551, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.51758, -0.02668, -1.51758, -0.02689, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.25195, -0.05719, -3.25195, -0.05742, -7.52014, 0.08458, -7.5188, 0.08429, -10.84619, 0.17062, -10.84558, 0.17024, -11.91638, -0.571, -11.91553, -0.57137, -10.90991, -0.26416, -10.91052, -0.2646, -7.51404, -0.20444, -7.51416, -0.2047, -2.30884, -0.25742, -2.30847, -0.25764], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 64, "vertices": [0.60266, 1.76917, 0.60022, 1.76918, 0.2876, 0.80793, 0.2865, 0.80794, 1.0166, 0.67559, 1.0166, 0.67557, 1.37415, 0.93679, 1.37415, 0.93678, 2.03809, 0.36941, 2.03809, 0.36937, 1.78601, 0.36912, 1.78601, 0.36909, 0.56458, 0.00065, 0.56458, 0.00066, 0.91394, 0.00105, 0.91394, 0.00104, 0.1792, -0.73049, 0.1792, -0.73048, 0.04407, -1.74172, 0.04407, -1.74171, 0.1792, -0.73049, 0.1792, -0.73048, 0.24158, 0.00028, 0.24158, 0.00031, 0.14441, 0.00017, 0.14441, 0.00017, -0.33618, -0.00038, -0.33618, -0.00038, -0.33594, -0.08443, -0.33594, -0.08441, -0.42017, -0.00048, -0.42017, -0.00047, -1.27271, -0.35336, -1.27271, -0.35335, -0.2489, -0.16593, -0.2489, -0.16591, -0.00195, 0, -0.00195, 3e-05, -0.91443, -0.95049, -0.91443, -0.95045, 0.1499, -1.63556, 0.1499, -1.63556, 0.14941, -1.21143, 0.14941, -1.21143, 0.04407, -1.74172, 0.04407, -1.74171, -1.27271, -0.35336, -1.27271, -0.35335, -1.35583, -0.18779, -1.35583, -0.18779, -1.27271, -0.35336, -1.27271, -0.35335, -1.27271, -0.35336, -1.27271, -0.35335, -0.74524, -0.21821, -0.74524, -0.21819, -0.54687, 0.02359, -0.54785, 0.02362, 0.60266, 1.76917, 0.60022, 1.76918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67444, 0.31293, 0.67371, 0.31293, 2.20825, 0.33418, 2.20825, 0.33412, 2.20825, 0.33418, 2.20825, 0.33412, 0.43066, 0.00049, 0.43066], "curve": "stepped"}, {"time": 9.6667, "offset": 64, "vertices": [0.60266, 1.76917, 0.60022, 1.76918, 0.2876, 0.80793, 0.2865, 0.80794, 1.0166, 0.67559, 1.0166, 0.67557, 1.37415, 0.93679, 1.37415, 0.93678, 2.03809, 0.36941, 2.03809, 0.36937, 1.78601, 0.36912, 1.78601, 0.36909, 0.56458, 0.00065, 0.56458, 0.00066, 0.91394, 0.00105, 0.91394, 0.00104, 0.1792, -0.73049, 0.1792, -0.73048, 0.04407, -1.74172, 0.04407, -1.74171, 0.1792, -0.73049, 0.1792, -0.73048, 0.24158, 0.00028, 0.24158, 0.00031, 0.14441, 0.00017, 0.14441, 0.00017, -0.33618, -0.00038, -0.33618, -0.00038, -0.33594, -0.08443, -0.33594, -0.08441, -0.42017, -0.00048, -0.42017, -0.00047, -1.27271, -0.35336, -1.27271, -0.35335, -0.2489, -0.16593, -0.2489, -0.16591, -0.00195, 0, -0.00195, 3e-05, -0.91443, -0.95049, -0.91443, -0.95045, 0.1499, -1.63556, 0.1499, -1.63556, 0.14941, -1.21143, 0.14941, -1.21143, 0.04407, -1.74172, 0.04407, -1.74171, -1.27271, -0.35336, -1.27271, -0.35335, -1.35583, -0.18779, -1.35583, -0.18779, -1.27271, -0.35336, -1.27271, -0.35335, -1.27271, -0.35336, -1.27271, -0.35335, -0.74524, -0.21821, -0.74524, -0.21819, -0.54687, 0.02359, -0.54785, 0.02362, 0.60266, 1.76917, 0.60022, 1.76918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67444, 0.31293, 0.67371, 0.31293, 2.20825, 0.33418, 2.20825, 0.33412, 2.20825, 0.33418, 2.20825, 0.33412, 0.43066, 0.00049, 0.43066], "curve": [9.889, 0, 10.111, 1]}, {"time": 10.3333}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": 6.13, "curve": [0.024, 6.13, 0.057, -7.99, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -7.99, "curve": [0.544, -7.99, 0.856, 6.13, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 6.13}]}, "ALL2": {"translate": [{"y": -15.19, "curve": [0.078, 0, 0.156, 0, 0.024, -15.19, 0.057, 20.78]}, {"time": 0.2333, "y": 20.78, "curve": [0.544, 0, 0.856, 0, 0.544, 20.78, 0.856, -15.19]}, {"time": 1.1667, "y": -15.19}]}, "tun": {"rotate": [{"value": 4.07, "curve": [0.024, 4.07, 0.057, -4.54]}, {"time": 0.2333, "value": -4.54, "curve": [0.544, -4.54, 0.856, 4.07]}, {"time": 1.1667, "value": 4.07}]}, "body": {"rotate": [{"value": 2.86, "curve": [0.024, 2.86, 0.057, -5.16]}, {"time": 0.2333, "value": -5.16, "curve": [0.544, -5.16, 0.856, 2.86]}, {"time": 1.1667, "value": 2.86}], "translate": [{"y": -8.2, "curve": [0.078, 0, 0.156, 0, 0.024, -8.2, 0.057, 9.62]}, {"time": 0.2333, "y": 9.62, "curve": [0.544, 0, 0.856, 0, 0.544, 9.62, 0.856, -8.2]}, {"time": 1.1667, "y": -8.2}], "scale": [{"y": 1.04, "curve": [0.078, 1, 0.156, 1, 0.024, 1.04, 0.057, 0.955]}, {"time": 0.2333, "y": 0.955, "curve": [0.544, 1, 0.856, 1, 0.544, 0.955, 0.856, 1.04]}, {"time": 1.1667, "y": 1.04}]}, "body2": {"rotate": [{"value": -1.81, "curve": [0.028, -1.81, 0.066, 3.32]}, {"time": 0.2667, "value": 3.32, "curve": [0.567, 3.32, 0.867, -1.81]}, {"time": 1.1667, "value": -1.81}], "translate": [{"x": -5.88, "curve": [0.028, -5.88, 0.066, 7.92, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 7.92, "curve": [0.567, 7.92, 0.867, -5.88, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -5.88}], "scale": [{"curve": [0.028, 1, 0.066, 1.011, 0.028, 1, 0.066, 1.011]}, {"time": 0.2667, "x": 1.011, "y": 1.011, "curve": [0.567, 1.011, 0.867, 1, 0.567, 1.011, 0.867, 1]}, {"time": 1.1667}]}, "neck": {"rotate": [{"value": 0.4, "curve": [0.031, 0.4, 0.074, -5.27]}, {"time": 0.3, "value": -5.27, "curve": [0.589, -5.27, 0.878, 0.4]}, {"time": 1.1667, "value": 0.4}]}, "head": {"rotate": [{"value": -0.24, "curve": [0.031, -0.24, 0.074, -5.27]}, {"time": 0.3, "value": -5.27, "curve": [0.589, -5.27, 0.878, -0.24]}, {"time": 1.1667, "value": -0.24}]}, "eyebrow_L": {"translate": [{"curve": [0.024, 0, 0.057, 3.16, 0.024, 0, 0.057, -0.08]}, {"time": 0.2333, "x": 3.16, "y": -0.08, "curve": [0.544, 3.16, 0.856, 0, 0.544, -0.08, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.024, 0, 0.057, -10.11]}, {"time": 0.2333, "value": -10.11, "curve": [0.544, -10.11, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.057, 0.964, 0.311, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.964, "curve": [0.544, 0.964, 0.856, 1, 0.311, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.024, 0, 0.057, -11.41]}, {"time": 0.2333, "value": -11.41, "curve": [0.544, -11.41, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"rotate": [{"curve": [0.024, 0, 0.057, 12.14]}, {"time": 0.2333, "value": 12.14, "curve": [0.544, 12.14, 0.856, 0]}, {"time": 1.1667}], "translate": [{"curve": [0.024, 0, 0.057, 3.16, 0.024, 0, 0.057, -0.08]}, {"time": 0.2333, "x": 3.16, "y": -0.08, "curve": [0.544, 3.16, 0.856, 0, 0.544, -0.08, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.057, 0.964, 0.311, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.964, "curve": [0.544, 0.964, 0.856, 1, 0.311, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.024, 0, 0.057, 15.99]}, {"time": 0.2333, "value": 15.99, "curve": [0.544, 15.99, 0.856, 0]}, {"time": 1.1667}]}, "hair_B2": {"rotate": [{"value": -7.06, "curve": [0.146, -1.98, 0.289, 8.05]}, {"time": 0.4333, "value": 8.05, "curve": [0.627, 8.05, 0.807, -9.94]}, {"time": 1, "value": -9.94, "curve": [1.049, -9.94, 1.12, -8.79]}, {"time": 1.1667, "value": -7.06}]}, "sh_L": {"translate": [{"x": -1.88, "y": -0.94, "curve": [0.028, -1.88, 0.066, 6.17, 0.028, -0.94, 0.066, 2.23]}, {"time": 0.2667, "x": 6.17, "y": 2.23, "curve": [0.567, 6.17, 0.867, -1.88, 0.567, 2.23, 0.867, -0.94]}, {"time": 1.1667, "x": -1.88, "y": -0.94}]}, "sh_R": {"translate": [{"x": -5.47, "y": -2.56, "curve": [0.028, -5.47, 0.066, 11.9, 0.028, -2.56, 0.066, 4.14]}, {"time": 0.2667, "x": 11.9, "y": 4.14, "curve": [0.567, 11.9, 0.867, -5.47, 0.567, 4.14, 0.867, -2.56]}, {"time": 1.1667, "x": -5.47, "y": -2.56}]}, "leg_L3": {"rotate": [{}]}, "leg_R3": {"rotate": [{}]}, "foot_R": {"rotate": [{"value": 7.05, "curve": [0.024, 7.05, 0.057, -8.02]}, {"time": 0.2333, "value": -8.02, "curve": [0.544, -8.02, 0.856, 7.05]}, {"time": 1.1667, "value": 7.05}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{}]}, "arm_R5": {"rotate": [{"value": 1.31, "curve": [0.031, 1.31, 0.074, 6.49]}, {"time": 0.3, "value": 6.49, "curve": [0.589, 6.49, 0.878, 1.31]}, {"time": 1.1667, "value": 1.31}], "translate": [{"x": -3.7, "y": -1.24, "curve": [0.031, -3.7, 0.074, -28.22, 0.031, -1.24, 0.074, -14.66]}, {"time": 0.3, "x": -28.22, "y": -14.66, "curve": [0.589, -28.22, 0.878, -3.7, 0.589, -14.66, 0.878, -1.24]}, {"time": 1.1667, "x": -3.7, "y": -1.24}]}, "hair_FR": {"rotate": [{"value": -2.22, "curve": [0.122, 0.66, 0.246, 4.5]}, {"time": 0.3667, "value": 4.5, "curve": [0.56, 4.5, 0.74, -5.35]}, {"time": 0.9333, "value": -5.36, "curve": [1.006, -5.36, 1.095, -3.97]}, {"time": 1.1667, "value": -2.22}]}, "hair_FL": {"rotate": [{"value": 1.06, "curve": [0.122, -1.86, 0.246, -5.75]}, {"time": 0.3667, "value": -5.75, "curve": [0.56, -5.75, 0.74, 4.24]}, {"time": 0.9333, "value": 4.24, "curve": [1.006, 4.24, 1.095, 2.83]}, {"time": 1.1667, "value": 1.06}]}, "RU_L": {"translate": [{"x": -7.17, "y": -1.9, "curve": [0.073, -20.23, 0.161, -30.94, 0.073, -5.37, 0.161, -8.21]}, {"time": 0.2333, "x": -30.94, "y": -8.21, "curve": [0.426, -30.94, 0.607, 43.78, 0.426, -8.21, 0.607, 11.61]}, {"time": 0.8, "x": 43.8, "y": 11.61, "curve": [0.921, 43.81, 1.047, 14.75, 0.921, 11.62, 1.047, 3.91]}, {"time": 1.1667, "x": -7.16, "y": -1.9}], "scale": [{"x": 1.044, "y": 0.962, "curve": [0.072, 1.004, 0.161, 0.923, 0.072, 1, 0.161, 1.075]}, {"time": 0.2333, "x": 0.923, "y": 1.075, "curve": [0.33, 0.923, 0.404, 1.067, 0.33, 1.075, 0.404, 0.941]}, {"time": 0.5, "x": 1.067, "y": 0.941, "curve": [0.596, 1.068, 0.704, 0.923, 0.596, 0.941, 0.704, 1.075]}, {"time": 0.8, "x": 0.923, "y": 1.075, "curve": [0.896, 0.923, 1.004, 1.067, 0.896, 1.075, 1.004, 0.941]}, {"time": 1.1, "x": 1.067, "y": 0.941, "curve": [1.124, 1.067, 1.143, 1.058, 1.124, 0.941, 1.143, 0.95]}, {"time": 1.1667, "x": 1.044, "y": 0.962}]}, "RU_R": {"translate": [{"x": -8.57, "y": -2.27, "curve": [0.073, -21.98, 0.161, -26.68, 0.073, -5.83, 0.161, -6.63]}, {"time": 0.2333, "x": -26.68, "y": -6.63, "curve": [0.426, -26.68, 0.607, 43.78, 0.426, -6.63, 0.607, 11.61]}, {"time": 0.8, "x": 43.8, "y": 11.61, "curve": [0.921, 43.81, 1.047, 13.96, 0.921, 11.62, 1.047, 3.7]}, {"time": 1.1667, "x": -8.56, "y": -2.27}], "scale": [{"x": 1.044, "y": 0.962, "curve": [0.072, 1.004, 0.161, 0.923, 0.072, 1, 0.161, 1.075]}, {"time": 0.2333, "x": 0.923, "y": 1.075, "curve": [0.33, 0.923, 0.404, 1.067, 0.33, 1.075, 0.404, 0.941]}, {"time": 0.5, "x": 1.067, "y": 0.941, "curve": [0.596, 1.068, 0.704, 0.923, 0.596, 0.941, 0.704, 1.075]}, {"time": 0.8, "x": 0.923, "y": 1.075, "curve": [0.896, 0.923, 1.004, 1.067, 0.896, 1.075, 1.004, 0.941]}, {"time": 1.1, "x": 1.067, "y": 0.941, "curve": [1.124, 1.067, 1.143, 1.058, 1.124, 0.941, 1.143, 0.95]}, {"time": 1.1667, "x": 1.044, "y": 0.962}]}, "RU_R2": {"translate": [{"x": -0.53, "curve": [0.098, -15.21, 0.204, -22.19, 0.098, 0, 0.204, 0.46]}, {"time": 0.3, "x": -22.19, "y": 0.46, "curve": [0.493, -22.19, 0.674, 29.02, 0.493, 0.46, 0.674, 0]}, {"time": 0.8667, "x": 29.04, "curve": [0.964, 29.04, 1.072, 14.36, 0.964, 0, 1.072, 0]}, {"time": 1.1667, "x": -0.52}], "scale": [{"x": 1.067, "y": 0.941, "curve": [0.096, 1.068, 0.204, 0.923, 0.096, 0.941, 0.204, 1.075]}, {"time": 0.3, "x": 0.923, "y": 1.075, "curve": [0.396, 0.923, 0.47, 1.067, 0.396, 1.075, 0.47, 0.941]}, {"time": 0.5667, "x": 1.067, "y": 0.941, "curve": [0.663, 1.068, 0.77, 0.923, 0.663, 0.941, 0.77, 1.075]}, {"time": 0.8667, "x": 0.923, "y": 1.075, "curve": [0.963, 0.923, 1.07, 1.067, 0.963, 1.075, 1.07, 0.941]}, {"time": 1.1667, "x": 1.067, "y": 0.941}]}, "RU_R3": {"translate": [{"x": 10.93, "curve": [0.121, -4.05, 0.247, -16.03, 0.121, 0, 0.247, 0.47]}, {"time": 0.3667, "x": -16.03, "y": 0.47, "curve": [0.559, -16.03, 0.741, 27.18, 0.559, 0.47, 0.741, 0]}, {"time": 0.9333, "x": 27.19, "curve": [1.006, 27.2, 1.095, 19.98, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 10.93}], "scale": [{"x": 1.044, "y": 0.962, "curve": [0.024, 1.058, 0.043, 1.067, 0.024, 0.95, 0.043, 0.941]}, {"time": 0.0667, "x": 1.067, "y": 0.941, "curve": [0.163, 1.068, 0.27, 0.923, 0.163, 0.941, 0.27, 1.075]}, {"time": 0.3667, "x": 0.923, "y": 1.075, "curve": [0.463, 0.923, 0.57, 1.067, 0.463, 1.075, 0.57, 0.941]}, {"time": 0.6667, "x": 1.067, "y": 0.941, "curve": [0.763, 1.068, 0.837, 0.923, 0.763, 0.941, 0.837, 1.075]}, {"time": 0.9333, "x": 0.923, "y": 1.075, "curve": [1.006, 0.923, 1.095, 1.004, 1.006, 1.075, 1.095, 1]}, {"time": 1.1667, "x": 1.044, "y": 0.962}]}, "RU_L2": {"translate": [{"x": -1.01, "curve": [0.098, -15.94, 0.204, -31.07, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -31.07, "curve": [0.493, -31.07, 0.674, 29.02, 0.493, 0, 0.674, 0]}, {"time": 0.8667, "x": 29.04, "curve": [0.964, 29.04, 1.072, 14.12, 0.964, 0, 1.072, 0]}, {"time": 1.1667, "x": -1}], "scale": [{"x": 1.067, "y": 0.941, "curve": [0.096, 1.068, 0.204, 0.923, 0.096, 0.941, 0.204, 1.075]}, {"time": 0.3, "x": 0.923, "y": 1.075, "curve": [0.396, 0.923, 0.47, 1.067, 0.396, 1.075, 0.47, 0.941]}, {"time": 0.5667, "x": 1.067, "y": 0.941, "curve": [0.663, 1.068, 0.77, 0.923, 0.663, 0.941, 0.77, 1.075]}, {"time": 0.8667, "x": 0.923, "y": 1.075, "curve": [0.963, 0.923, 1.07, 1.067, 0.963, 1.075, 1.07, 0.941]}, {"time": 1.1667, "x": 1.067, "y": 0.941}]}, "RU_L3": {"translate": [{"x": 10.35, "curve": [0.121, -5.16, 0.247, -25.8, 0.121, 0, 0.247, 0]}, {"time": 0.3667, "x": -25.8, "curve": [0.559, -25.8, 0.741, 27.18, 0.559, 0, 0.741, 0]}, {"time": 0.9333, "x": 27.19, "curve": [1.006, 27.2, 1.095, 19.72, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 10.35}], "scale": [{"x": 1.044, "y": 0.962, "curve": [0.024, 1.058, 0.043, 1.067, 0.024, 0.95, 0.043, 0.941]}, {"time": 0.0667, "x": 1.067, "y": 0.941, "curve": [0.163, 1.068, 0.27, 0.923, 0.163, 0.941, 0.27, 1.075]}, {"time": 0.3667, "x": 0.923, "y": 1.075, "curve": [0.463, 0.923, 0.57, 1.067, 0.463, 1.075, 0.57, 0.941]}, {"time": 0.6667, "x": 1.067, "y": 0.941, "curve": [0.763, 1.068, 0.837, 0.923, 0.763, 0.941, 0.837, 1.075]}, {"time": 0.9333, "x": 0.923, "y": 1.075, "curve": [1.006, 0.923, 1.095, 1.004, 1.006, 1.075, 1.095, 1]}, {"time": 1.1667, "x": 1.044, "y": 0.962}]}, "leg_R4": {"rotate": [{"value": 0.29}]}, "leg_R5": {"rotate": [{"value": -0.38}]}, "leg_L5": {"rotate": [{"value": -0.01}]}, "sh_L2": {"rotate": [{"value": -0.13}]}, "sh_L3": {"rotate": [{"value": 0.03}]}, "sh_R2": {"rotate": [{"value": 0.06}]}, "sh_R3": {"rotate": [{"value": -0.07}]}, "headround3": {"translate": [{"x": 15.73, "curve": [0.035, 15.73, 0.082, 301.23, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 301.23, "curve": [0.611, 301.23, 0.889, 15.73, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 15.73}]}, "headround": {"translate": [{"y": 186.72, "curve": [0.035, 0, 0.082, 0, 0.035, 186.72, 0.082, -408.95]}, {"time": 0.3333, "y": -408.95, "curve": [0.611, 0, 0.889, 0, 0.611, -408.95, 0.889, 186.72]}, {"time": 1.1667, "y": 186.72}]}, "bodyround": {"translate": [{"x": 38.35, "y": -82.15, "curve": [0.028, 38.35, 0.066, -131.97, 0.028, -82.15, 0.066, 309.11]}, {"time": 0.2667, "x": -131.97, "y": 309.11, "curve": [0.567, -131.97, 0.867, 38.35, 0.567, 309.11, 0.867, -82.15]}, {"time": 1.1667, "x": 38.35, "y": -82.15}]}, "tunround": {"translate": [{"x": 221.99, "curve": [0.024, 221.99, 0.057, -316.48, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -316.48, "curve": [0.544, -316.48, 0.856, 221.99, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 221.99}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.023, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-2.56334, -0.09355, -2.56331, -0.09368, -2.31382, -0.05464, -2.31359, -0.05472, -1.98107, 0.0488, -1.9807, 0.04874, -1.41478, 0.07396, -1.41415, 0.07389, -1.05614, -0.00464, -1.05608, -0.00467, -0.65543, 0.00241, -0.65538, 0.00238, -0.65543, 0.00241, -0.65538, 0.00238, -0.32086, 0.0083, -0.32084, 0.00827, -0.13938, -0.00245, -0.13938, -0.00247, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.2927, -0.00515, -0.2927, -0.00519, -1.29631, -0.0228, -1.29631, -0.02288, -2.3543, -0.11112, -2.35422, -0.1112, 0, 0, 0, 0, -0.81501, 0.0044, -0.81501, 0.00436, -1.61541, 0.13585, -1.61541, 0.13581, -2.45033, 0.01866, -2.45033, 0.0186, -2.73702, -0.06872, -2.73702, -0.06881, -2.53188, -0.02395, -2.53191, -0.02401, -1.85348, 0.02916, -1.85348, 0.0291, -0.88578, 0.02559, -0.88578, 0.02556, -0.30944, 0.03573, -0.30944, 0.03571], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.023, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-2.45597, 0.11333, -2.45552, 0.11324, -2.48123, 0.16068, -2.48075, 0.16059, -2.56115, 0.05244, -2.56112, 0.05235, -2.01659, 0.08857, -2.01677, 0.08845, -1.05384, 0.02467, -1.05384, 0.0246, -0.3419, 0.04583, -0.34188, 0.04576, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.41531, -0.01548, -0.41528, -0.01552, -0.82363, 0.0502, -0.8235, 0.05018, -0.82363, 0.0502, -0.8235, 0.05018, -1.84206, -0.10648, -1.84143, -0.10657, -1.02587, -0.07776, -1.02587, -0.07782, -2.04359, 0.07154, -2.04374, 0.07152, -2.62068, 0.06432, -2.62071, 0.06425, -2.56613, 0.04066, -2.5661, 0.04058, -2.04008, 0.03324, -2.04011, 0.03315, -1.21869, 0.00449, -1.21869, 0.00442, -0.26831, 0.02121, -0.26831, 0.02114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.27485, 0.00711, -0.27485], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.023, 0, 0.058, 1]}, {"time": 0.2333, "offset": 314, "vertices": [-1.03022, -0.01812, -1.03016, -0.01819, -1.68589, -0.02965, -1.68563, -0.02973, -2.43596, 0.0196, -2.43582, 0.01953, -2.48313, 0.03438, -2.48318, 0.0343, -1.90368, -0.06471, -1.90373, -0.06474, -0.96836, 0.01419, -0.96831, 0.01415, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.3278, -0.00576, -0.3278, -0.00581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.70242, -0.01235, -0.70242, -0.0124, -1.62435, 0.01827, -1.62406, 0.01821, -2.34277, 0.03685, -2.34264, 0.03677, -2.57394, -0.12334, -2.57375, -0.12341, -2.35654, -0.05706, -2.35667, -0.05715, -1.62303, -0.04416, -1.62306, -0.04421, -0.49871, -0.0556, -0.49863, -0.05565], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.023, 0, 0.058, 1]}, {"time": 0.2333, "offset": 64, "vertices": [0.60266, 1.76917, 0.60022, 1.76918, 0.2876, 0.80793, 0.2865, 0.80794, 1.0166, 0.67559, 1.0166, 0.67557, 1.37415, 0.93679, 1.37415, 0.93678, 2.03809, 0.36941, 2.03809, 0.36937, 1.78601, 0.36912, 1.78601, 0.36909, 0.56458, 0.00065, 0.56458, 0.00066, 0.91394, 0.00105, 0.91394, 0.00104, 0.1792, -0.73049, 0.1792, -0.73048, 0.04407, -1.74172, 0.04407, -1.74171, 0.1792, -0.73049, 0.1792, -0.73048, 0.24158, 0.00028, 0.24158, 0.00031, 0.14441, 0.00017, 0.14441, 0.00017, -0.33618, -0.00038, -0.33618, -0.00038, -0.33594, -0.08443, -0.33594, -0.08441, -0.42017, -0.00048, -0.42017, -0.00047, -1.27271, -0.35336, -1.27271, -0.35335, -0.2489, -0.16593, -0.2489, -0.16591, -0.00195, 0, -0.00195, 3e-05, -0.91443, -0.95049, -0.91443, -0.95045, 0.1499, -1.63556, 0.1499, -1.63556, 0.14941, -1.21143, 0.14941, -1.21143, 0.04407, -1.74172, 0.04407, -1.74171, -1.27271, -0.35336, -1.27271, -0.35335, -1.35583, -0.18779, -1.35583, -0.18779, -1.27271, -0.35336, -1.27271, -0.35335, -1.27271, -0.35336, -1.27271, -0.35335, -0.74524, -0.21821, -0.74524, -0.21819, -0.54687, 0.02359, -0.54785, 0.02362, 0.60266, 1.76917, 0.60022, 1.76918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67444, 0.31293, 0.67371, 0.31293, 2.20825, 0.33418, 2.20825, 0.33412, 2.20825, 0.33418, 2.20825, 0.33412, 0.43066, 0.00049, 0.43066], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}}}}}}