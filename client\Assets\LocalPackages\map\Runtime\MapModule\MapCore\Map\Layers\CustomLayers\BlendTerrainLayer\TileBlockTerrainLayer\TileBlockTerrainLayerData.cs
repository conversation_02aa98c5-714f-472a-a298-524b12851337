﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //拼接地表层使用的数据
    public partial class TileBlockTerrainLayerData : MapLayerData
    {
        public struct TileBlock
        {
            public TileBlock(int minX, int minY, int maxX, int maxY, int singleTileType)
            {
                this.minX = (ushort)minX;
                this.minY = (ushort)minY;
                this.width = (ushort)(maxX - minX + 1);
                this.height = (ushort)(maxY - minY + 1);
                this.singleTileType = (ushort)singleTileType;
            }

            public ushort minX;
            public ushort minY;
            public ushort width;
            public ushort height;
            public ushort singleTileType;
        }

        public TileBlockTerrainLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, ushort[] tiles, string[] tilePrefabPaths, List<ModelData[,]> otherLODTiles, TerrainRenderTextureLODSetting[] lodSettings, TileBlock[] tileBlocks, Dictionary<int, Vector2[]> singleTileTypeAtlasUVMappings, Dictionary<int, Material> singleTileTypeAtlasMaterialMappings) : base(header, config, map)
        {
            Debug.Assert(tiles != null && tilePrefabPaths != null);
            mTiles = tiles;
            mPrefabPaths = tilePrefabPaths;
            mOtherLODTiles = otherLODTiles;
            mLODSettings = lodSettings;
            mTileblocks = tileBlocks;
            mTileBlockVisibleTileCount = new ushort[mTileblocks.Length];
            mSingleTileTypeAtlasUVMappings = singleTileTypeAtlasUVMappings;
            mSingleTileTypeAtlasMaterialMappings = singleTileTypeAtlasMaterialMappings;

            //check lod settings
            if (lodSettings != null)
            {
                mDefaultLODCount = config.lodConfigs.Length - lodSettings.Length;
            }
            else
            {
                mDefaultLODCount = config.lodConfigs.Length;
            }

            Debug.Assert(mDefaultLODCount > 0, "Invalid ground layer LOD Setting!!!");

            if (map.isEditorMode)
            {
                mLastViewport = map.viewport;
            }
            else
            {
                mLastViewport = new Rect(-10000, -10000, 0, 0);
            }
        }

        public override void OnDestroy()
        {
            DestroyLODTiles();
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            Debug.Assert(false, "Can't be here!");
            return false;
        }

        public override bool Contains(int objectID)
        {
            Debug.Assert(false, "Can't be here!");
            return false;
        }

        public string GetTilePrefabPath(int x, int y, out int tileType)
        {
            var idx = y * mCols + x;
            tileType = mTiles[idx];
            return mPrefabPaths[tileType];
        }

        public ushort GetTileType(int x, int y)
        {
            var idx = y * mCols + x;
            return mTiles[idx];
        }

        public TileBlock GetTileBlock(int tileType)
        {
            return mTileblocks[tileType - mPrefabPaths.Length];
        }

        public Material GetMaterialForCombinedTiles(int singleTileType)
        {
            Material mtl;
            mSingleTileTypeAtlasMaterialMappings.TryGetValue(singleTileType, out mtl);
#if UNITY_EDITOR
            Debug.Assert(mtl != null);
#endif
            return mtl;
        }

        public override bool isGameLayer => true;

        public Rect lastViewport { get { return mLastViewport; } }
        public string[] tilePrefabPaths { get { return mPrefabPaths; } }
        public ushort[] tiles { get { return mTiles; } }
        public Dictionary<int, Vector2[]> singleTileTypeAtlasUVMappings { get { return mSingleTileTypeAtlasUVMappings; } }

        //mPrefabPaths的index或者tile block的index
        ushort[] mTiles;
        ushort[] mTileBlockVisibleTileCount;
        //tile block是只读的
        TileBlock[] mTileblocks;
        //single tile type与atlas uv的映射关系
        Dictionary<int, Vector2[]> mSingleTileTypeAtlasUVMappings = new Dictionary<int, Vector2[]>();
        //single tile type与atlas material(替换成combine tile shader的material)的映射关系
        Dictionary<int, Material> mSingleTileTypeAtlasMaterialMappings = new Dictionary<int, Material>();

        Rect mLastViewport;
        float mLastZoom;
        string[] mPrefabPaths;
    }
}
