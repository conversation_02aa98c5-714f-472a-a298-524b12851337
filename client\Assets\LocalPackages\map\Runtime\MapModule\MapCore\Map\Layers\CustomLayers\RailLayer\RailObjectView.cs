﻿ 



 
 

﻿using UnityEngine;
using System;

namespace TFW.Map
{
    public class GroupLODModelLoadingTask : ModelLoadingTask
    {
        public void Init(int taskID, Action<ModelBase, bool> loadFinishedCallback, float priority, int lod, ModelLODGroup group, bool isGroupLeader)
        {
            mTaskID = taskID;
            mLoadFinishedCallback = loadFinishedCallback;
            mPriority = priority;
            mNewLOD = lod;
            mGroup = group;
            mIsGroupLeader = isGroupLeader;
        }

        public static GroupLODModelLoadingTask Require(int taskID, Action<ModelBase, bool> loadFinishedCallback, float priority, int lod, ModelLODGroup group, bool isGroupLeader)
        {
            var task = mPool.Require();
            task.Init(taskID, loadFinishedCallback, priority, lod, group, isGroupLeader);
            return task;
        }

        public static void Release(GroupLODModelLoadingTask task)
        {
            mPool.Release(task);
        }

        protected override ModelBase LoadImpl()
        {
            var mapObjectData = Map.currentMap.FindObject(mTaskID) as IMapObjectData;
#if UNITY_EDITOR
            Debug.Assert(mapObjectData != null, "object is removed!");
#endif
            var model = GroupLODModel.Require(Map.currentMap, mapObjectData.GetModelTemplateID(), mNewLOD, mGroup, mIsGroupLeader, mapObjectData.GetPosition(), mapObjectData.GetScale(), mapObjectData.GetRotation());
            return model;
        }

        public override void OnDestroy()
        {
            GroupLODModelLoadingTask.Release(this);
        }

        int mNewLOD;
        bool mIsGroupLeader;
        ModelLODGroup mGroup;

        static ObjectPool<GroupLODModelLoadingTask> mPool = new ObjectPool<GroupLODModelLoadingTask>(1000, ()=>new GroupLODModelLoadingTask());
    }

    public class RailObjectView : ModelView
    {
        public RailObjectView(IMapObjectData data, MapLayerView layerView) : base(data, layerView)
        {
            mOnLoadFinish = OnLoadFinish;
        }

        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            var objectData = data as RailObjectData;
            if (newLOD < 0)
            {
                newLOD = 0;
            }
            Debug.Assert(mModel == null);

            var map = Map.currentMap;
            ModelBase model = null;
            var objectLayerData = mLayerView.layerData as MapObjectLayerData;

            bool active = data.IsObjActive();

            var template = map.GetEntityModelTemplate(data.GetEntityID());
            Debug.Assert(template != null, "invalid model template");

            var loadingTaskManager = map.loadingTaskManager;
            loadingTaskManager.CancelTask(data.GetEntityID());

            var group = Map.currentMap.FindObject(data.GetModelLODGroupID()) as ModelLODGroup;

            //如果对象池中有缓存的prefab,直接加载它
            if (mUseModelPlaceholder ||
                mLayerView.asyncLoading == false ||
                map.HasCachedObject(template.GetLODPrefabPath(newLOD)))
            {
                //不使用加载队列加载,直接生成一个ReusableModel,这种模型使用对象池来管理它的game object
                if (active && !mUseModelPlaceholder)
                {
                    //向下切换lod时只使用低层的lod
                    model = GroupLODModel.Require(map, data.GetModelTemplateID(), newLOD, group, objectData.isGroupLeader, data.GetPosition(), data.GetScale(), data.GetRotation());
                    OnModelReady(model);
                }
                else
                {
                    model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
                    OnModelPlaceholderReady(model);
                }
            }
            else
            {
                //往加载队列里添加一个新的任务
                //先使用一个占位模型来显示,当实际的模型加载完毕后再替换占位模型
                model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
                OnModelPlaceholderReady(model);

                if (active)
                {
                    //向下切换lod时只使用低层的lod
                    var task = GroupLODModelLoadingTask.Require(data.GetEntityID(), mOnLoadFinish, 0, newLOD, group, objectData.isGroupLeader);
                    loadingTaskManager.AddTask(task);
                }
            }

            return model;
        }

        System.Action<ModelBase, bool> mOnLoadFinish;
    }
}
