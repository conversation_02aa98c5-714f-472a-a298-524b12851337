﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    public abstract class MapLayerHexagonalCoordinateConverter : MapLayerCoordinateConverter {
        protected override void OnSetLayer() {
            float tileHeight = mLayerData.tileHeight;
            float tileWidth = mLayerData.tileWidth;
            mSize = tileHeight * 0.5f;
            mOffsetX = tileWidth;
            mOffsetY = tileHeight * 0.75f;
            mTileOffsetX = tileWidth * 0.5f;
            mTileOffsetY = tileHeight * 0.5f;
        }

        public override Vector3 FromCoordinateToWorldPositionCenter(int x, int y) {
            return FromCoordinateToWorldPosition(x, y);
        }

        public override Vector3 FromCoordinateToWorldPositionCenter(int x, int y, int width, int height) {
            float tileWidth = mLayerData.tileWidth;
            float tileHeight = mLayerData.tileHeight;
            Vector3 lt = FromCoordinateToWorldPosition(x, y);
            Vector3 rb = FromCoordinateToWorldPosition(x + width, y + height);
            return (lt + rb) * 0.5f;
        }

        public override float GetLayerWidthInMeter(int width) {
            if (width == 0) {
                width = mLayerData.horizontalTileCount;
            }
            float tileWidth = mLayerData.tileWidth;
            float totalWidth = width * tileWidth + tileWidth * 0.5f;
            return totalWidth;
        }

        public override float GetLayerHeightInMeter(int height) {
            if (height == 0) {
                height = mLayerData.verticalTileCount;
            }
            float tileHeight = mLayerData.tileHeight;
            float totalHeight = (height - 1) * 0.75f * tileHeight + tileHeight;
            return totalHeight;
        }

        protected float mSize;
        protected float mOffsetX;
        protected float mOffsetY;
        protected float mTileOffsetX;
        protected float mTileOffsetY;
        static int[] evenOffsetX = { 0, -1, -1, -1, 0, 1, 0 };
        static int[] oddOffsetX = { 1, 0, -1, 0, 1, 1, 0 };
        static int[] offsetY = { -1, -1, 0, 1, 1, 0, 0 };
    }
}
