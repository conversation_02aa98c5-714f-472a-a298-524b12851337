%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5240052104242630646
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5044611026447617059}
  - component: {fileID: 5684429250302477404}
  m_Layer: 0
  m_Name: ObstacleOutline 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5044611026447617059
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5240052104242630646}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5684429250302477404
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5240052104242630646}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -88.785416, y: 0, z: -66.554794}
    - {x: -82.1899, y: 0, z: -51.556}
    - {x: -68.29802, y: 0, z: -50.53857}
    - {x: -64.56276, y: 0, z: -64.81447}
    - {x: -77.97725, y: 0, z: -77.69201}
    - {x: -87.51601, y: 0, z: -77.29183}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -80.8292, y: 0, z: -80.5152}
    - {x: -85.553, y: 0, z: -80.471}
    - {x: -86.2729, y: 0, z: -80.2824}
    - {x: -88.7372, y: 0, z: -78.9266}
    - {x: -89.5103, y: 0, z: -77.9019}
    - {x: -90, y: 0, z: -74.48}
    - {x: -90, y: 0, z: -74.4334}
    - {x: -90, y: 0, z: -67.945}
    - {x: -90, y: 0, z: -67.1567}
    - {x: -86.1515, y: 0, z: -51.5335}
    - {x: -85.7232, y: 0, z: -50.8898}
    - {x: -84.0193, y: 0, z: -49.3728}
    - {x: -83.34, y: 0, z: -49.0241}
    - {x: -80.9478, y: 0, z: -48.4865}
    - {x: -80.6608, y: 0, z: -48.4505}
    - {x: -68.4538, y: 0, z: -48.1042}
    - {x: -67.2058, y: 0, z: -48.6608}
    - {x: -63.916, y: 0, z: -52.5395}
    - {x: -63.5962, y: 0, z: -53.1719}
    - {x: -62.0499, y: 0, z: -59.6824}
    - {x: -62.0133, y: 0, z: -59.9191}
    - {x: -61.7184, y: 0, z: -63.9198}
    - {x: -62.006, y: 0, z: -64.9375}
    - {x: -66.1793, y: 0, z: -70.73}
    - {x: -66.4203, y: 0, z: -70.9923}
    - {x: -76.1151, y: 0, z: -79.3136}
    - {x: -76.7727, y: 0, z: -79.6437}
    - {x: -80.4849, y: 0, z: -80.4786}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
