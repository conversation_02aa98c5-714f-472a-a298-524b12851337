﻿ 



 
 



/*
 * created by wzw at 2020.6.18
 */

#if true

using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public class SplineLayer : MapLayerBase
    {
        public SplineLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var header = new MapLayerDataHeader(map.nextCustomObjectID, MapCoreDef.MAP_LAYER_NODE_SPLINE, 1, 1, map.mapWidth, map.mapHeight, GridType.Rectangle, setting.origin);

            //读取LOD配置信息
            var lod0 = new MapLayerLODConfig.LOD(MapLayerLODConfig.GetDefaultLODName(0), 1, 0, false, 100, false, MapLayerLODConfigFlag.None, 0);
            MapLayerLODConfig config = new MapLayerLODConfig(map, new MapLayerLODConfig.LOD[1] { lod0 });
            mLayerData = new QuadTreeObjectLayerData(header, config, map, null, null);
            mLayerView = new SplineLayerView(mLayerData, true);
            mLayerView.active = true;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            mLayerData.isLoading = true;
//#if UNITY_WEBGL
            MapModuleResourceMgr.LoadTextStreamAsync(MapCoreDef.GetFullExportedSplineDataFilePath(map.dataFolder), (str, result) =>
             {
                 var stream = result;
                 if (stream != null)
                 {
                     using BinaryReader reader = new BinaryReader(stream);

                     var version = Utils.ReadVersion(reader);

                     //--------------------------version 1 start------------------------------------
                     int segmentCount = reader.ReadInt32();
                     for (int i = 0; i < segmentCount; ++i)
                     {
                         var bounds = Utils.ReadRect(reader);
                         var prefabPath = Utils.ReadString(reader);
                         var position = Utils.ReadVector3(reader);
                         int objectID = map.nextCustomObjectID;
                         var modelTemplate = map.GetOrCreateModelTemplate(objectID, prefabPath, false);
#if UNITY_EDITOR
                         Debug.Assert(modelTemplate != null, $"invalid spline prefab path {prefabPath}");
#endif
                         var modelData = new ModelData(objectID, map, 0, position + mLayerData.layerOffset, Quaternion.identity, Vector3.one, modelTemplate, true);
                         mLayerData.AddObjectData(modelData);
                     }

                     mLayerData.lodConfig = LoadMapLayerLODConfig(reader);
                     //--------------------------version 1 end------------------------------------

                     reader.Close();
                 }

                 mLayerData.isLoading = false;

                 map.AddMapLayer(this);
             });

//#else
//            var stream = MapModuleResourceMgr.LoadTextStream(MapCoreDef.GetFullExportedSplineDataFilePath(map.dataFolder), true);
//            if (stream != null)
//            {
//                BinaryReader reader = new BinaryReader(stream);

//                var version = Utils.ReadVersion(reader);

//                //--------------------------version 1 start------------------------------------
//                int segmentCount = reader.ReadInt32();
//                for (int i = 0; i < segmentCount; ++i)
//                {
//                    var bounds = Utils.ReadRect(reader);
//                    var prefabPath = Utils.ReadString(reader);
//                    var position = Utils.ReadVector3(reader);
//                    int objectID = map.nextCustomObjectID;
//                    var modelTemplate = map.GetOrCreateModelTemplate(objectID, prefabPath, false);
//#if UNITY_EDITOR
//                    Debug.Assert(modelTemplate != null, $"invalid spline prefab path {prefabPath}");
//#endif
//                    var modelData = new ModelData(objectID, map, 0, position + mLayerData.layerOffset, Quaternion.identity, Vector3.one, modelTemplate, true);
//                    mLayerData.AddObjectData(modelData);
//                }

//                mLayerData.lodConfig = LoadMapLayerLODConfig(reader);
//                //--------------------------version 1 end------------------------------------

//                reader.Close();
//            }

//            mLayerData.isLoading = false;

//            map.AddMapLayer(this);
//#endif
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            if (lodChanged)
            {
                mLayerView.SetZoom(newCameraZoom, lodChanged);
            }
            return lodChanged;
        }

        public override void RefreshObjectsInViewport()
        {
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[n];
            for (int i = 0; i < n; ++i)
            {
                float changeZoom = reader.ReadSingle();
                var changeZoomThreshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                lods[i] = new MapLayerLODConfig.LOD("", changeZoom, changeZoomThreshold, hideObject, shaderLOD, useRenderTexture, MapLayerLODConfigFlag.None, 0);
            }
            var config = new MapLayerLODConfig(map, lods);
            return config;
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public MapObjectLayerData layerData { get { return mLayerData; } }
        public SplineLayerView layerView { get { return mLayerView; } }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override int lodCount => mLayerData.lodCount;

        protected MapObjectLayerData mLayerData;
        protected SplineLayerView mLayerView;
    }
}
#endif