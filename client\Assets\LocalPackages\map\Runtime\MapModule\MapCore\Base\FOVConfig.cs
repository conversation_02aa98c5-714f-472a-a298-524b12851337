﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //相机的分段fov设置
    [CreateAssetMenu(fileName = "camera_fov_config", menuName = "Assets/CameraFOVConfig")]
    public class FOVConfig : CurveConfig
    {
        public FOVConfig()
        {
            mStartTime = 0;
            mStartValue = 15;
            mDeltaTime = 50;
            mDeltaValue = 5;
        }

        public float GetFOV(float cameraHeight)
        {
            float fov = GetValue(cameraHeight);
            fov = Mathf.Clamp(fov, 2, 170);
            return fov;
        }
    }
}
