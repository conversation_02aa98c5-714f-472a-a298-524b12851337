﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Diagnostics;

#if UNITY_EDITOR
using UnityEditor;
#endif

//create by wzw at 2020.2.4

namespace TFW.Map
{
    public partial class DetailSprites
    {
        List<Vector3> mRect = new List<Vector3>(4);

        public void PrecalculateValidKeys(float mapWidth, float mapHeight, float tileSize, float subgridSize, List<List<string>> spriteGroups, float alpha0Height, float alpha1Height, int objectCountPerGrid, byte[] subgridSpriteGroupIndices, bool updateScale, bool crossFading)
        {
            var w = new Stopwatch();
            w.Start();
            mAlpha0Height = alpha0Height;
            mAlpha1Height = alpha1Height;
            mTileSize = tileSize;
            mGridSize = subgridSize;
            mUpdateScale = updateScale;
            mCrossfading = crossFading;
            mSpriteCountPerGrid = objectCountPerGrid;
            mCols = Mathf.FloorToInt(mapWidth / tileSize);
            mRows = Mathf.FloorToInt(mapHeight / tileSize);
            mHorizontalGridCount = Mathf.FloorToInt(mTileSize / mGridSize);

            mListPool = new ObjectPool<List<SpriteObject>>(40, () => { return new List<SpriteObject>(); });

            if (subgridSpriteGroupIndices == null)
            {
                mSubgridSpriteGroupIndices = new byte[mRows * mCols * mHorizontalGridCount * mHorizontalGridCount];
            }
            else
            {
                UnityEngine.Debug.Assert(subgridSpriteGroupIndices.Length == mRows * mCols * mHorizontalGridCount * mHorizontalGridCount);
                mSubgridSpriteGroupIndices = subgridSpriteGroupIndices;
            }

            //create sprite groups
            int groupCount = spriteGroups.Count;
            mSpriteGroups = new List<SpriteGroup>(groupCount);
            for (int i = 0; i < groupCount; ++i)
            {
                int nSprites = spriteGroups[i].Count;
                var group = new SpriteGroup(nSprites);
                for (int k = 0; k < nSprites; ++k)
                {
                    group.sprites[k] = spriteGroups[i][k];
                }
                mSpriteGroups.Add(group);
            }

            InitSpriteData();

            int currNum = 0;
            int totalKeyCount = 0;
            var gridModelLayer = mMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            mSubGridObjects = new SubGridObjects[mRows, mCols];

            mSubgridKeysStartOffset = new int[mRows * mCols * mHorizontalGridCount * mHorizontalGridCount];
            for (int i = 0; i < mSubgridKeysStartOffset.Length; ++i)
            {
                mSubgridKeysStartOffset[i] = -1;
            }
            List<byte> allValidKeys = new List<byte>();
            for (int y = 0; y < mRows; ++y)
            {
                for (int x = 0; x < mCols; ++x)
                {
                    mSubGridObjects[y, x] = new SubGridObjects();
                    mSubGridObjects[y, x].objs = new List<SpriteObject>[mHorizontalGridCount, mHorizontalGridCount];

                    var tile = gridModelLayer?.GetObjectData(x, y);
                    List<ObstacleObject> convexHulls = null;
                    if (tile != null)
                    {
                        var modelTemplate = tile.GetModelTemplate();
                        var idx = y * mCols + x;
                        var modifier = new TilePrefabModifier(modelTemplate.generated, new Vector3(mTileSize * x, 0, mTileSize * y), new Vector2(mTileSize, mTileSize), modelTemplate.GetLODPrefabPath(0), idx);
                        convexHulls = modifier.CreateAllConvexHulls(true);
                    }

                    List<byte> tileValidKeys = new List<byte>();
                    for (int sy = 0; sy < mHorizontalGridCount; sy++)
                    {
                        for (int sx = 0; sx < mHorizontalGridCount; sx++)
                        {
                            //mSubGridObjects[y, x].objPositions[sy, sx] = new List<Vector2>();
                            var idx = GetSubGridIndex(x, y, sx, sy);
                            mSubgridKeysStartOffset[idx] = allValidKeys.Count;
                            var usedGroupIdx = mSubgridSpriteGroupIndices[idx];
                            var group = mSpriteGroups[usedGroupIdx];
                            int ntypes = group.sprites.Length - 1;

                            tileValidKeys.Clear();
                            for (int k = 0; k < mSpriteCountPerGrid; k++) // how many element by 10*10 square ?
                            {
                                int key = currNum;
                                float k1 = GetNum(currNum, x, y, sy, sx);
                                Step(ref currNum);
                                int type = (int)(ntypes * k1);// we have a set of n sprites to use, which one could it be ?
                                float k2 = GetNum(currNum, x, y, sy, sx);
                                Step(ref currNum);
                                float k3 = GetNum(currNum, x, y, sy, sx);
                                Step(ref currNum);
                                //used to find lie in which sub grid
                                float k4 = GetNum(currNum, x, y, sy, sx);
                                Step(ref currNum);
                                Vector2 pos = GenerateGridObjectPosition(k2, k3, x, y, sx, sy);
                                bool isValid = IsValidSpawnPoint(convexHulls, pos.x, pos.y, group.spriteRadius[type]);

                                if (isValid)
                                {
                                    ++totalKeyCount;
                                    tileValidKeys.Add((byte)key);

                                    //temp code
                                    //mSubGridObjects[y, x].objPositions[sy, sx].Add(pos);
                                    //GameTestGameObject(pos.x, pos.y, group.spriteRadius[type]);
                                }
                            }

                            //注意key start offset第一个元素是这个subgrid的sprite count
                            allValidKeys.Add((byte)tileValidKeys.Count);
                            for (int i = 0; i < tileValidKeys.Count; ++i)
                            {
                                allValidKeys.Add(tileValidKeys[i]);
                            }
                        }
                    }
                }
            }

            mAllKeys = allValidKeys.ToArray();
            UnityEngine.Debug.Log(totalKeyCount);
            w.Stop();
            var elapsedTime = w.ElapsedMilliseconds;
            UnityEngine.Debug.Log("DetailSprite.GenerateValidSpawnPoints Elapsed Time: " + elapsedTime);

            //test code
            //for (int y = 0; y < mRows; ++y)
            //{
            //    for (int x = 0; x < mCols; ++x)
            //    {
            //        for (int sy = 0; sy < mHorizontalGridCount; ++sy)
            //        {
            //            for (int sx = 0; sx < mHorizontalGridCount; ++sx)
            //            {
            //                GenerateObjects(x, y, sx, sy, false);
            //            }
            //        }
            //    }
            //}
        }

        void GameTestGameObject(float x, float z, float radius)
        {
            var obj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            obj.transform.localScale = Vector3.one * radius;
            obj.transform.position = new Vector3(x, 0, z);
        }

        public void UpdateSprites(float mapWidth, float mapHeight, float tileSize, float subgridSize, List<List<string>> spriteGroups, float alpha0Height, float alpha1Height, int objectCountPerGrid, byte[] subgridSpriteGroupIndices)
        {
            mAlpha0Height = alpha0Height;
            mAlpha1Height = alpha1Height;
            mTileSize = tileSize;
            mGridSize = subgridSize;
            mSpriteCountPerGrid = objectCountPerGrid;
            mCols = Mathf.FloorToInt(mapWidth / tileSize);
            mRows = Mathf.FloorToInt(mapHeight / tileSize);
            mHorizontalGridCount = Mathf.FloorToInt(mTileSize / mGridSize);

            //create sprite groups
            int groupCount = spriteGroups.Count;
            mSpriteGroups = new List<SpriteGroup>(groupCount);
            for (int i = 0; i < groupCount; ++i)
            {
                int nSprites = spriteGroups[i].Count;
                var group = new SpriteGroup(nSprites);
                for (int k = 0; k < nSprites; ++k)
                {
                    group.sprites[k] = spriteGroups[i][k];
                }
                mSpriteGroups.Add(group);
            }

            if (subgridSpriteGroupIndices == null)
            {
                mSubgridSpriteGroupIndices = new byte[mRows * mCols * mHorizontalGridCount * mHorizontalGridCount];
            }
            else
            {
                UnityEngine.Debug.Assert(subgridSpriteGroupIndices.Length == mRows * mCols * mHorizontalGridCount * mHorizontalGridCount);
                mSubgridSpriteGroupIndices = subgridSpriteGroupIndices;
            }

            string fullPath = SLGMakerEditor.instance.exportFolder + "/" + MapCoreDef.MAP_DETAIL_OBJECT_SPRITES_FILE_NAME;
            ExportSprites1(fullPath);
        }

        public void ExportSprites1(string spritesPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.DetailSpritePathVersion.majorVersion);
            writer.Write(VersionSetting.DetailSpritePathVersion.minorVersion);

            int groupCount = mSpriteGroups.Count;
            writer.Write(groupCount);
            for (int i = 0; i < groupCount; ++i)
            {
                int nSprites = mSpriteGroups[i].sprites.Length;
                writer.Write(nSprites);
                for (int k = 0; k < nSprites; ++k)
                {
                    Utils.WriteString(writer, mSpriteGroups[i].sprites[k]);
                }
            }

            writer.Write(mAlpha0Height);
            writer.Write(mAlpha1Height);
            writer.Write(mCrossfading);
            writer.Write(mUpdateScale);
            var prefab = MapModuleResourceMgr.LoadPrefab(mSpriteGroups[0].sprites[0]);
            var mtl = prefab.GetComponent<Renderer>().sharedMaterial;
            Utils.WriteString(writer, AssetDatabase.GetAssetPath(mtl));
            writer.Write(mRows);
            writer.Write(mCols);
            writer.Write(mTileSize);
            writer.Write(mGridSize);
            writer.Write(mSpriteCountPerGrid);
            writer.Write(mSubgridSpriteGroupIndices);

            var data = stream.ToArray();
            File.WriteAllBytes(spritesPath, data);
            writer.Close();
        }

        public void ExportSpawnPoints1(string keyPath)
        {
            if (mSubGridObjects != null)
            {
                using MemoryStream stream = new MemoryStream();
                using BinaryWriter writer = new BinaryWriter(stream);

                writer.Write(VersionSetting.DetailSpriteSpawnPointVersion.majorVersion);
                writer.Write(VersionSetting.DetailSpriteSpawnPointVersion.minorVersion);

                int length = mSubgridKeysStartOffset.Length;
                writer.Write(length);
                for (int i = 0; i < length; ++i)
                {
                    writer.Write(mSubgridKeysStartOffset[i]);
                }

                int n = mAllKeys.Length;
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    writer.Write(mAllKeys[i]);
                }

                var data = stream.ToArray();
                File.WriteAllBytes(keyPath, data);
                writer.Close();
            }
        }

        bool IsValidSpawnPoint(List<ObstacleObject> convexHulls, float centerX, float centerZ, float radius)
        {
            bool hitObstacle = mMap.IsIntersectedWithObstacles(centerX, centerZ, radius, false);
            if (hitObstacle)
            {
                return false;
            }

            if (convexHulls != null)
            {
                mRect.Clear();
                mRect.Add(new Vector3(centerX - radius, 0, centerZ - radius));
                mRect.Add(new Vector3(centerX - radius, 0, centerZ + radius));
                mRect.Add(new Vector3(centerX + radius, 0, centerZ + radius));
                mRect.Add(new Vector3(centerX + radius, 0, centerZ - radius));

                for (int i = 0; i < convexHulls.Count; ++i)
                {
                    bool prefabCollided = ConvexPolygonCollisionCheck.ConvexPolygonHit2D(convexHulls[i].polygon, mRect);
                    if (prefabCollided)
                    {
                        return false;
                    }
                }
            }
            return true;
        }
    }
}

#endif
