﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;

using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Diagnostics;

namespace TFW.Map
{
    public partial class GridRegionEditorUI : UnityEditor.Editor
    {
        void LoadGate()
        {

            string path = $"{MapModule.configResDirectory}pass.tsv";
            bool suc = TSVReader.Load(path);
            if (suc)
            {
                GameObject gateRoot = null;
                var rows = TSVReader.rows;
                for (int i = 0; i < rows.Count; ++i)
                {
                    bool get;
                    var pos = TSVReader.GetCoord(i, "pos", out get);
                    if (get)
                    {
                        bool getPrefab;
                        string prefabPath = TSVReader.GetString(i, "prefab", out getPrefab);
                        if (getPrefab)
                        {
                            prefabPath = $"{MapModule.configResDirectory}" + prefabPath + ".prefab";
                            var obj = MapModuleResourceMgr.LoadGameObject(prefabPath);
                            obj.transform.position = new Vector3(Utils.I2F(pos.x), 0, Utils.I2F(pos.y));
                            if (gateRoot == null)
                            {
                                string gateRootName = "Gate Root";
                                var root = SLGMakerEditor.instance.gameObject.transform;
                                var oldGateRoot = root.Find(gateRootName);
                                if (oldGateRoot != null)
                                {
                                    Utils.DestroyObject(oldGateRoot.gameObject);
                                }
                                if (gateRoot == null)
                                {
                                    gateRoot = new GameObject("Gate Root");
                                }
                                gateRoot.transform.parent = root.transform;
                                Utils.HideGameObject(gateRoot);
                            }
                            obj.transform.SetParent(gateRoot.transform);
                        }
                    }
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Can't find pass.tsv file!", "OK");
            }

        }
    }
}

#endif