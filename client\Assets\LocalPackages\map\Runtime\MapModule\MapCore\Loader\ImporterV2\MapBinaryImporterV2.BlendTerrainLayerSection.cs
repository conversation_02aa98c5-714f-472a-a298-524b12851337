﻿ 



 
 


using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadBlendTerrainLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.BlendTerrainLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;

            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();

            var objects = new config.BlendTerrainTileData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var idx = i * cols + j;
                    objects[idx] = LoadBlendTerrainTileDataV1(reader, layerID);
                }
            }

            var config = LoadBlendTerrainLayerLODConfigV1(reader);
            //-------------------version 1 end-----------------------
            //-------------------version 2 start-----------------------
            if (version >= 2)
            {
                LoadBlendTerrainLayerLODConfigV2(reader, config);
            }
            //-------------------version 2 end-----------------------
            //-------------------version 3 start-----------------------
            bool useGeneratedLOD = true;
            if (version >= 3)
            {
                useGeneratedLOD = reader.ReadBoolean();
            }
            //-------------------version 3 end-----------------------
            //-------------------version 4 start-----------------------
            if (version >= 4)
            {
                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        var idx = i * cols + j;

                        int subTypeIndex = reader.ReadInt32();
                        if (objects[idx] != null)
                        {
                            objects[idx].subTypeIndex = subTypeIndex;
                        }
                    }
                }
            }
            //-------------------version 4 end-----------------------
            //-------------------version 5 start-----------------------
            if (version >= 5)
            {
                LoadBlendTerrainLayerLODConfigV5(reader, config);
            }
            //-------------------version 5 end-----------------------
            //-------------------version 6 start-----------------------
            if (version >= 6)
            {
                LoadTerrainHeightsV6(reader, rows, cols, objects);
            }
            //-------------------version 6 end-----------------------
            //-------------------version 7 start-----------------------
            bool useCombinedTiles = false;
            if (version >= 7)
            {
                useCombinedTiles = reader.ReadBoolean();
            }
            //-------------------version 7 end-----------------------
            //-------------------version 8 start-----------------------
            if (version >= 8)
            {
                LoadBlendTerrainLayerLODConfigV8(reader, config);
            }
            //-------------------version 8 end-----------------------
            //-------------------version 9 start-----------------------
            bool useTileHeight = false;
            string[] usedTilePrefabPaths = null;
            if (version >= 9)
            {
                useTileHeight = reader.ReadBoolean();
                usedTilePrefabPaths = Utils.ReadStringArray(reader);
            }
            //-------------------version 9 end-----------------------
            //-------------------version 10 start-----------------------
            bool getGroundHeightInGame = false;
            if (version >= 10)
            {
                getGroundHeightInGame = reader.ReadBoolean();
            }
            //-------------------version 10 end-----------------------
            //-------------------version 11 start-----------------------
            Vector3 layerPosition = Vector3.zero;
            if (version >= 11)
            {
                layerPosition = Utils.ReadVector3(reader);
            }
            //-------------------version 11 end-----------------------
            var layer = new config.BlendTerrainLayerData(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, objects, useGeneratedLOD, false, "", "", useTileHeight, usedTilePrefabPaths, false, getGroundHeightInGame, false, layerPosition);
            return layer;
        }

        config.BlendTerrainTileData LoadBlendTerrainTileDataV1(BinaryReader reader, int layerID)
        {
            bool hasTile = reader.ReadBoolean();
            if (!hasTile)
            {
                return null;
            }

            config.BlendTerrainTileData tileData = null;
            var tileID = reader.ReadInt32();
            var templateID = reader.ReadInt32();
            var type = reader.ReadInt32();
            var index = reader.ReadInt32();
            tileData = new config.BlendTerrainTileData(tileID, layerID, type, index, 0, templateID, null);

            return tileData;
        }

        config.MapLayerLODConfig LoadBlendTerrainLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }

        void LoadBlendTerrainLayerLODConfigV2(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].useRenderTexture = reader.ReadBoolean();
            }
        }

        void LoadBlendTerrainLayerLODConfigV5(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].flag = (MapLayerLODConfigFlag)reader.ReadInt32();
            }
        }

        void LoadBlendTerrainLayerLODConfigV8(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].terrainLODTileCount = reader.ReadInt32();
            }
        }

        void LoadTerrainHeightsV6(BinaryReader reader, int v, int h, config.BlendTerrainTileData[] objects)
        {
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    bool hasHeights = reader.ReadBoolean();
                    if (hasHeights)
                    {
                        objects[i * h + j].heights = Utils.ReadFloatArray(reader);
                    }
                }
            }
        }
    }
}
