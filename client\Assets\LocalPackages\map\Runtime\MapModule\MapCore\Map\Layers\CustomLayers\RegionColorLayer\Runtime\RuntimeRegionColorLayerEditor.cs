﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    class RuntimeRegionColorLayerEditor : MonoBehaviour
    {
        public RuntimeRegionColorLayer GetLayer()
        {
            return Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_REGION_COLOR) as RuntimeRegionColorLayer;
        }

        public void SetReferencePrefab(GameObject newPrefab)
        {
            if (prefab != newPrefab)
            {
                prefab = newPrefab;
            }
        }

        public int selectedLayerIndex = 0;
        public int selectedRegionIndex = -1;

        public GameObject prefab;
    }
}


#endif