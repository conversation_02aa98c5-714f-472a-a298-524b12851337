﻿Shader "Custom/terrain_NoLight"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
		_Height("Height", Range(-10.0000, 10.0)) = -0.0001
		_Transparent("Transparent", Range(1, 10.0)) = 1
		
		[Toggle] DayNightToggle("Day Night Toggle", Float) = 0
		_ExtraLightTex ("Extra Light Tex", 2D) = "black" {}
        _ExtraLightIntensity("Extra Light Intensity", Range(0, 2)) = 1
        _ExtraLightTexClip("Extra Light Clip", Range(0.01, 1)) = 0.01
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 100
		blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            // make fog work
            #pragma multi_compile_fog
			#pragma multi_compile _ DAYNIGHTTOGGLE_GLOBAL_ON
			#pragma multi_compile_local _ DAYNIGHTTOGGLE_ON
			
            #include "UnityCG.cginc"
            #include "Assets/k1/Res/Shader/CG/DayNightSystemShaderHelper.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                UNITY_FOG_COORDS(1)
                float4 vertex : SV_POSITION;
				float3 worldPos : TEXCOORD1;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
			float _Height;
			float _Transparent;
			
			sampler2D _ExtraLightTex;
            float4 _ExtraLightTex_ST;
            
            float _ExtraLightIntensity;
            float _ExtraLightTexClip;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				fixed3 fixedCol = tex2D(_MainTex, i.uv).rgb;
                float fAlpha = step(_Height, i.worldPos.y) + step(i.worldPos.y, _Height) * (1.0 - fixedCol.g * _Transparent) + step(i.worldPos.y, _Height) * (i.worldPos.y / _Height);
                
                #ifdef DAYNIGHTTOGGLE_GLOBAL_ON
            		#ifdef DAYNIGHTTOGGLE_ON
                        fixedCol.rgb = ApplyDayNightLut(fixedCol.rgb);
                        
                        fixed4 extraLightMap = tex2D(_ExtraLightTex, i.uv);
                        fixed useExtraLightTex = step(_ExtraLightTexClip, extraLightMap.rgb);
                        fixed3 mergeColor = useExtraLightTex * extraLightMap.rgb * _ExtraLightIntensity + (1 - useExtraLightTex) * fixedCol.rgb;
                        fixedCol.rgb = lerp(fixedCol.rgb, mergeColor, _LightMapIntensity);
			        #endif
			    #endif
			    
                return fixed4(fixedCol.rgb, saturate(fAlpha));
            }
            ENDCG
        }
    }
}
