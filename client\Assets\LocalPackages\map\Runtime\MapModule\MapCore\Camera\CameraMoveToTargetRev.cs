﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //相机缩放并移动到目标点
    public class CameraMoveToTargetRev : ZoomActionBase
    {
        public CameraMoveToTargetRev(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;

            Init();
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
        }

        public void StartMoving(Vector3 targetPos, float moveDuration, float zoomDuration, System.Action onCameraReachTarget)
        {
            if (enabled == false)
            {
                Reset();

                MapCameraMgr.InvokeStartMovingRevCallback();

                enabled = true;

                MapCameraMgr.isMovingToTarget = true;

                mStartPos = MapCameraMgr.updatedCameraPosition;
                mEndPos = targetPos;
                mZoomDuration = zoomDuration;
                mCameraReachTarget = onCameraReachTarget;

                if (NeedZoomCamera(mStartPos, mEndPos))
                {
                    mMoveEndPos = CalculateZoomEndPos(mStartPos, MapCameraMgr.MapCamera.transform.forward, targetPos.y);
                    mZoomDuration = zoomDuration;
                    mIsZooming = true;

                    var cameraSetting = MapCameraMgr.cameraSetting;
                    float dxf = cameraSetting.GetCameraDXFFromHeight(mEndPos.y);
                    AutoZoomToVDF(dxf, mZoomDuration * 1000);
                }
                else
                {
                    mMoveEndPos = mEndPos;
                    mZoomDuration = 0;
                    mIsZooming = false;
                }

                mElapsedTime = 0;
                mMoveDuration = moveDuration;
            }
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            if (mStartPos == mEndPos)
            {
                isFinished = true;
                return mEndPos;
            }

            if (mIsZooming)
            {
                //缩放相机
                bool finished = UpdateZooming();
                if (finished)
                {
                    mStartPos = mMoveEndPos;
                    mMoveEndPos = mEndPos;
                    mIsZooming = false;
                }

                var pos = GetCameraPos();
                return pos;
            }
            else
            {
                //平移相机
                mElapsedTime += Time.deltaTime;

                float t = 1.0f;
                if (!Mathf.Approximately(mMoveDuration, 0))
                {
                    t = Mathf.Clamp(mElapsedTime, 0, mMoveDuration) / mMoveDuration;
                }

                if (mElapsedTime >= mMoveDuration)
                {
                    isFinished = true;
                }

                float x = Mathf.SmoothStep(mStartPos.x, mMoveEndPos.x, t);
                float y = Mathf.SmoothStep(mStartPos.y, mMoveEndPos.y, t);
                float z = Mathf.SmoothStep(mStartPos.z, mMoveEndPos.z, t);

                return new Vector3(x, y, z);
            }
        }

        bool NeedZoomCamera(Vector3 startPos, Vector3 targetPos)
        {
            if (mZoomDuration == 0)
            {
                return false;
            }

            if (Mathf.Abs(targetPos.y - startPos.y) <= MapCameraMgr.minDelta)
            {
                return false;
            }

            return true;
        }

        Vector3 CalculateZoomEndPos(Vector3 startPos, Vector3 cameraDir, float endY)
        {
            float t = (startPos.y - endY) / cameraDir.y;
            return startPos - t * cameraDir;
        }

        public override void OnFinishImpl()
        {
            MapCameraMgr.isMovingToTarget = false;
            if (mCameraReachTarget != null)
            {
                mCameraReachTarget();
            }
        }

        //相机的起始位置
        Vector3 mStartPos;

        //相机最终的目标位置
        Vector3 mEndPos;

        //相机平移的目标位置
        Vector3 mMoveEndPos;
        float mElapsedTime;
        float mMoveDuration;
        float mZoomDuration;
        bool mIsZooming = false;
        System.Action mCameraReachTarget;
    }
}