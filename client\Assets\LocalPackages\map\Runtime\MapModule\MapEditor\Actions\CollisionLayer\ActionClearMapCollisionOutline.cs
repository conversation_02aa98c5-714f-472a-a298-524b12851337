﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionClearMapCollisionOutline : EditorAction
    {
        public ActionClearMapCollisionOutline(int layerID, int dataID, PrefabOutlineType type)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mOutlineType = type;
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            mOutlineVertices = data.GetOutlineVerticesCopy(type);
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            layer.ClearOutline(mOutlineType, mDataID);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetOutline(mOutlineType, mDataID, mOutlineVertices);
            return true;
        }

        int mLayerID;
        int mDataID;
        PrefabOutlineType mOutlineType;
        List<Vector3> mOutlineVertices;
    }
}

#endif