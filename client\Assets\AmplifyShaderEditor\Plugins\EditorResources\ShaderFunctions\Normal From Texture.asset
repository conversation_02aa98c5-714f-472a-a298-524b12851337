%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Normal From Texture
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity
    Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18702\n0;411;1729;948;621.1696;-899.756;1.578781;True;False\nNode;AmplifyShaderEditor.WireNode;94;-934.7373,3153.675;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.FunctionSwitch;149;-35.36745,2507.335;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;148;-173.2692,2512.505;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionSwitch;147;-37.1351,2265.176;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;146;-175.0368,2270.346;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SamplerNode;54;-534.2678,2265.954;Inherit;True;Property;_TextureSample3;Texture
    Sample 3;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionSwitch;143;-45.9734,2033.621;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;142;-183.8751,2038.791;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionSwitch;141;-44.20639,1805.602;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;139;-50.45546,1508.225;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;151;-33.13635,2759.903;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;150;-171.0381,2765.073;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.BreakToComponentsNode;140;-182.1081,1810.772;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.BreakToComponentsNode;138;-188.3572,1513.395;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.BreakToComponentsNode;136;-192.5089,1278.04;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.BreakToComponentsNode;134;-200.3653,1049.094;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.WireNode;88;-950.8482,2862.768;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;83;-942.6831,2954.455;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.CustomExpressionNode;46;-1267.346,2170.667;Inherit;False;{$   
    float3 pos = float3( TexelSize.xy, 0 )@$    float3 neg = float3( -pos.xy, 0 )@$   
    UV0 = UV + neg.xy@$    UV1 = UV + neg.zy@$    UV2 = UV + float2( pos.x, neg.y
    )@$    UV3 = UV + neg.xz@$    UV4 = UV@$    UV5 = UV + pos.xz@$    UV6 = UV +
    float2( neg.x, pos.y )@$    UV7 = UV + pos.zy@$    UV8 = UV + pos.xy@$    return@$};7;False;11;True;UV;FLOAT2;0,0;In;;Inherit;False;True;TexelSize;FLOAT4;0,0,0,0;In;;Inherit;False;True;UV0;FLOAT2;0,0;Out;;Inherit;False;True;UV1;FLOAT2;0,0;Out;;Inherit;False;True;UV2;FLOAT2;0,0;Out;;Inherit;False;True;UV3;FLOAT2;0,0;Out;;Inherit;False;True;UV4;FLOAT2;0,0;Out;;Inherit;False;True;UV5;FLOAT2;0,0;Out;;Inherit;False;True;UV6;FLOAT2;0,0;Out;;Inherit;False;True;UV7;FLOAT2;0,0;Out;;Inherit;False;True;UV8;FLOAT2;0,0;Out;;Inherit;False;Calculate
    UVs Smooth;True;False;0;12;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT2;0,0;False;8;FLOAT2;0,0;False;9;FLOAT2;0,0;False;10;FLOAT2;0,0;False;11;FLOAT2;0,0;False;10;FLOAT;0;FLOAT2;4;FLOAT2;5;FLOAT2;6;FLOAT2;7;FLOAT2;8;FLOAT2;9;FLOAT2;10;FLOAT2;11;FLOAT2;12\nNode;AmplifyShaderEditor.WireNode;71;-950.5735,2126.853;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;86;-1725.35,1955.66;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionSwitch;137;-54.60703,1271.102;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;152;-168.3932,3013.224;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionSwitch;153;-30.49147,3008.054;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;109;980.571,1698.853;Inherit;False;Smooth
    (Slower);1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;163;284.3418,2049.237;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TransformDirectionNode;68;1495.271,1695.723;Inherit;False;Tangent;World;False;Fast;1;0;FLOAT3;0,0,0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionSubtitle;107;986.7996,1572.163;Inherit;False;Sharp
    (Faster);1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;91;451.2825,1651.488;Inherit;False;Strength;1;3;False;1;0;FLOAT;1.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.CustomExpressionNode;128;687.2171,1466.29;Inherit;False;{$   
    float3 va = float3( 0.13, 0, ( S1 - S0 ) * Strength )@$    float3 vb = float3(
    0, 0.13, ( S2 - S0 ) * Strength )@$    return normalize( cross( va, vb ) )@$};3;False;4;True;S0;FLOAT;0;In;;Inherit;False;True;S1;FLOAT;0;In;;Inherit;False;True;S2;FLOAT;0;In;;Inherit;False;True;Strength;FLOAT;0;In;;Inherit;False;Combine
    Samples Sharp;True;False;0;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;58;683.8669,1781.724;Inherit;False;{$   
    float3 normal@$    normal.x = Strength * ( S0 - S2 + 2 * S3 - 2 * S5 + S6 - S8
    )@$    normal.y = Strength * ( S0 + 2 * S1 + S2 - S6 - 2 * S7 - S8 )@$    normal.z
    = 1.0@$    return normalize( normal )@$};3;False;10;True;Strength;FLOAT;0;In;;Inherit;False;True;S0;FLOAT;0;In;;Inherit;False;True;S1;FLOAT;0;In;;Inherit;False;True;S2;FLOAT;0;In;;Inherit;False;True;S3;FLOAT;0;In;;Inherit;False;True;S4;FLOAT;0;In;;Inherit;False;True;S5;FLOAT;0;In;;Inherit;False;True;S6;FLOAT;0;In;;Inherit;False;True;S7;FLOAT;0;In;;Inherit;False;True;S8;FLOAT;0;In;;Inherit;False;Combine
    Samples Smooth;True;False;0;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;167;365.7449,2175.053;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;166;341.2415,2147.356;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;165;322.1609,2118.973;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;164;300.2653,2089.528;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;162;266.8397,2013.852;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;161;248.7872,1964.641;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;160;229.3277,1907.705;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;159;-27.41323,3728.875;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;158;-165.3149,3734.044;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionSwitch;157;-27.81758,3490.377;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;156;-165.7193,3495.547;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionSwitch;155;-31.35663,3251.417;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Instance;135;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;154;-169.2584,3256.587;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.WireNode;43;-879.6338,1932.672;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;168;262.7348,1474.22;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;135;-62.46368,1043.924;Inherit;False;Height
    Channel;False;0;4;-1;R;G;B;A;Object;-1;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;92;-928.7188,3207.844;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;64;-978.6859,2405.781;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;78;-920.438,2056.731;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;72;-939.3794,3058.119;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;119;-1239.437,2845.394;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;59;-873.6181,2000.867;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;62;-972.6644,2535.252;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;80;-548.3456,1807.032;Inherit;True;Property;_TextureSample1;Texture
    Sample 1;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;97;-503.1219,3726.325;Inherit;True;Property;_TextureSample7;Texture
    Sample 7;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;90;-540.4975,2031.932;Inherit;True;Property;_TextureSample2;Texture
    Sample 2;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CustomExpressionNode;110;-1103.439,1234.901;Inherit;False;{$   
    UV.y -= TexelSize.y * 0.5@$    UV0 = UV@$    UV1 = UV + float2( TexelSize.x,
    0 )@$    UV2 = UV + float2( 0, TexelSize.y )@$};7;False;5;True;UV;FLOAT2;0,0;In;;Inherit;False;True;TexelSize;FLOAT4;0,0,0,0;In;;Inherit;False;True;UV0;FLOAT2;0,0;Out;;Inherit;False;True;UV1;FLOAT2;0,0;Out;;Inherit;False;True;UV2;FLOAT2;0,0;Out;;Inherit;False;Calculate
    UVs Sharp;True;False;0;6;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;4;FLOAT;0;FLOAT2;4;FLOAT2;5;FLOAT2;6\nNode;AmplifyShaderEditor.WireNode;117;-865.0287,1683.958;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;120;-2141.424,2414.817;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;113;-558.706,1273.719;Inherit;True;Property;_TextureSample10;Texture
    Sample 10;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexelSizeNode;89;-1718.808,2243.135;Inherit;False;-1;1;0;SAMPLER2D;;False;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;45;-875.6236,1969.773;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;121;-856.3488,1450.987;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;73;-944.3997,3034.064;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RelayNode;118;-1535.702,1475.698;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;63;-960.6208,2234.158;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;115;-879.8671,1597.852;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SamplerNode;79;-531.1535,2508.48;Inherit;True;Property;_TextureSample4;Texture
    Sample 4;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;96;-966.6425,2188.994;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;111;-1245.284,2052.146;Inherit;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SamplerNode;112;-551.6846,1042.588;Inherit;True;Property;_TextureSample9;Texture
    Sample 9;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;48;-512.4654,3249.776;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionSwitch;108;1233.465,1567.899;Inherit;False;Filtering;False;0;2;-1;Sharp;Smooth;Object;-1;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;65;-879.3245,1845.746;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;67;-969.6533,2279.322;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;82;-524.924,2757.656;Inherit;True;Property;_TextureSample5;Texture
    Sample 5;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;133;-863.5597,1493.908;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;66;-932.4942,2095.913;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SamplerNode;114;-565.3553,1507.426;Inherit;True;Property;_TextureSample11;Texture
    Sample 11;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;87;-2095.677,1799.138;Inherit;False;Texture;9;0;False;1;0;SAMPLER2D;0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;76;-948.5767,2913.511;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;57;-907.7392,2017.885;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;69;-878.6321,1893.557;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;81;-939.6157,3105.523;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;70;-528.0388,3006.831;Inherit;True;Property;_TextureSample6;Texture
    Sample 6;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;44;-975.6751,2360.617;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;47;-972.6643,2327.497;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;84;-503.1212,3489.608;Inherit;True;Property;_TextureSample8;Texture
    Sample 8;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;93;-972.6644,2493.099;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;123;-870.4584,1531.294;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;75;-972.6643,2444.924;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;74;-1443.075,2964.874;Inherit;False;SS;13;2;False;1;0;SAMPLERSTATE;0;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;116;-875.6149,1642.134;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;95;-943.1602,3001.948;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.FunctionInput;85;-1457.552,1951.121;Inherit;False;UV;2;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;169;281.6801,1420.541;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;127;-2142.816,1680.113;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1748.021,1700.925;Inherit;False;True;-1;World
    Normal;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;40;1492.221,1570.54;Inherit;False;False;-1;Tangent
    Normal;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;94;0;74;0\nWireConnection;149;0;148;0\nWireConnection;149;1;148;1\nWireConnection;149;2;148;2\nWireConnection;149;3;148;3\nWireConnection;148;0;79;0\nWireConnection;147;0;146;0\nWireConnection;147;1;146;1\nWireConnection;147;2;146;2\nWireConnection;147;3;146;3\nWireConnection;146;0;54;0\nWireConnection;54;0;43;0\nWireConnection;54;1;67;0\nWireConnection;54;7;83;0\nWireConnection;143;0;142;0\nWireConnection;143;1;142;1\nWireConnection;143;2;142;2\nWireConnection;143;3;142;3\nWireConnection;142;0;90;0\nWireConnection;141;0;140;0\nWireConnection;141;1;140;1\nWireConnection;141;2;140;2\nWireConnection;141;3;140;3\nWireConnection;139;0;138;0\nWireConnection;139;1;138;1\nWireConnection;139;2;138;2\nWireConnection;139;3;138;3\nWireConnection;151;0;150;0\nWireConnection;151;1;150;1\nWireConnection;151;2;150;2\nWireConnection;151;3;150;3\nWireConnection;150;0;82;0\nWireConnection;140;0;80;0\nWireConnection;138;0;114;0\nWireConnection;136;0;113;0\nWireConnection;134;0;112;0\nWireConnection;88;0;74;0\nWireConnection;83;0;74;0\nWireConnection;46;1;85;0\nWireConnection;46;2;89;0\nWireConnection;71;0;87;0\nWireConnection;86;2;87;0\nWireConnection;137;0;136;0\nWireConnection;137;1;136;1\nWireConnection;137;2;136;2\nWireConnection;137;3;136;3\nWireConnection;152;0;70;0\nWireConnection;153;0;152;0\nWireConnection;153;1;152;1\nWireConnection;153;2;152;2\nWireConnection;153;3;152;3\nWireConnection;109;0;58;0\nWireConnection;163;0;151;0\nWireConnection;68;0;108;0\nWireConnection;107;0;128;0\nWireConnection;128;0;169;0\nWireConnection;128;1;168;0\nWireConnection;128;2;139;0\nWireConnection;128;3;91;0\nWireConnection;58;0;91;0\nWireConnection;58;1;141;0\nWireConnection;58;2;160;0\nWireConnection;58;3;161;0\nWireConnection;58;4;162;0\nWireConnection;58;5;163;0\nWireConnection;58;6;164;0\nWireConnection;58;7;165;0\nWireConnection;58;8;166;0\nWireConnection;58;9;167;0\nWireConnection;167;0;159;0\nWireConnection;166;0;157;0\nWireConnection;165;0;155;0\nWireConnection;164;0;153;0\nWireConnection;162;0;149;0\nWireConnection;161;0;147;0\nWireConnection;160;0;143;0\nWireConnection;159;0;158;0\nWireConnection;159;1;158;1\nWireConnection;159;2;158;2\nWireConnection;159;3;158;3\nWireConnection;158;0;97;0\nWireConnection;157;0;156;0\nWireConnection;157;1;156;1\nWireConnection;157;2;156;2\nWireConnection;157;3;156;3\nWireConnection;156;0;84;0\nWireConnection;155;0;154;0\nWireConnection;155;1;154;1\nWireConnection;155;2;154;2\nWireConnection;155;3;154;3\nWireConnection;154;0;48;0\nWireConnection;43;0;87;0\nWireConnection;168;0;137;0\nWireConnection;135;0;134;0\nWireConnection;135;1;134;1\nWireConnection;135;2;134;2\nWireConnection;135;3;134;3\nWireConnection;92;0;74;0\nWireConnection;64;0;46;9\nWireConnection;78;0;87;0\nWireConnection;72;0;74;0\nWireConnection;119;0;74;0\nWireConnection;59;0;87;0\nWireConnection;62;0;46;12\nWireConnection;80;0;65;0\nWireConnection;80;1;96;0\nWireConnection;80;7;88;0\nWireConnection;97;0;71;0\nWireConnection;97;1;62;0\nWireConnection;97;7;92;0\nWireConnection;90;0;69;0\nWireConnection;90;1;63;0\nWireConnection;90;7;76;0\nWireConnection;110;1;85;0\nWireConnection;110;2;111;0\nWireConnection;117;0;87;0\nWireConnection;120;0;119;0\nWireConnection;113;0;116;0\nWireConnection;113;1;110;5\nWireConnection;113;7;133;0\nWireConnection;89;0;87;0\nWireConnection;45;0;87;0\nWireConnection;121;0;118;0\nWireConnection;73;0;74;0\nWireConnection;118;0;127;0\nWireConnection;63;0;46;5\nWireConnection;115;0;87;0\nWireConnection;79;0;45;0\nWireConnection;79;1;47;0\nWireConnection;79;7;95;0\nWireConnection;96;0;46;4\nWireConnection;111;0;89;0\nWireConnection;112;0;115;0\nWireConnection;112;1;110;4\nWireConnection;112;7;121;0\nWireConnection;48;0;78;0\nWireConnection;48;1;75;0\nWireConnection;48;7;81;0\nWireConnection;108;0;107;0\nWireConnection;108;1;109;0\nWireConnection;65;0;87;0\nWireConnection;67;0;46;6\nWireConnection;82;0;59;0\nWireConnection;82;1;44;0\nWireConnection;82;7;73;0\nWireConnection;133;0;118;0\nWireConnection;66;0;87;0\nWireConnection;114;0;117;0\nWireConnection;114;1;110;6\nWireConnection;114;7;123;0\nWireConnection;76;0;74;0\nWireConnection;57;0;87;0\nWireConnection;69;0;87;0\nWireConnection;81;0;74;0\nWireConnection;70;0;57;0\nWireConnection;70;1;64;0\nWireConnection;70;7;72;0\nWireConnection;44;0;46;8\nWireConnection;47;0;46;7\nWireConnection;84;0;66;0\nWireConnection;84;1;93;0\nWireConnection;84;7;94;0\nWireConnection;93;0;46;11\nWireConnection;123;0;118;0\nWireConnection;75;0;46;10\nWireConnection;116;0;87;0\nWireConnection;95;0;74;0\nWireConnection;85;0;86;0\nWireConnection;169;0;135;0\nWireConnection;127;0;120;0\nWireConnection;0;0;68;0\nWireConnection;40;0;108;0\nASEEND*/\n//CHKSM=31A91DFE3C09739865ECF32FB1E5EB483581411D"
  m_functionName: 
  m_description: Derive a normal vector from a texture.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
