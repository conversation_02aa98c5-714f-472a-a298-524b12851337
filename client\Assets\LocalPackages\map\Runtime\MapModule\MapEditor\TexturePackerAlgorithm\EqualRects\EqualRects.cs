﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //打包算法,适用于所有tile贴图大小相同的条件
    public class EqualRects : TexturePackStrategy
    {
        public void OnDestroy()
        {
        }

        public int GetMaxPackCountInOneTexture(int textureSize, int borderSize, int padding, int maxTextureSize)
        {
            int width = textureSize + borderSize * 2 + padding * 2;
            int count = maxTextureSize / width;
            return count * count;
        }

        public bool Pack(TexturePackerSetting setting, List<TexturePackerItem> images, bool sortInput, bool deleteTextureAtlas, out TexturePackResult output)
        {
            Debug.Assert(sortInput == false, "todo");

            output = new TexturePackResult();
            Clear(deleteTextureAtlas);
            Debug.Assert(setting.atlasTextureMaxWidth > 0 && setting.atlasTextureMaxHeight > 0);
            Debug.Assert(Mathf.IsPowerOfTwo(setting.atlasTextureMaxWidth) && Mathf.IsPowerOfTwo(setting.atlasTextureMaxHeight));

            int totalWidth = 0;
            int totalHeight = 0;
            for (int i = 0; i < images.Count; ++i)
            {
                var image = images[i];
                totalWidth += image.texture.width;
                totalHeight += image.texture.height;
                Debug.Assert(image.texture.format == TextureFormat.RGBA32);
            }

            for (int i = 0; i < images.Count; ++i)
            {
                PackTextureToAtlas(images[i].uniqueID, images[i].texture, setting, images[i].treatAlphaAsZero, out output.missingTextures);
            }

            for (int i = 0; i < mTextureAtlas.Count; ++i)
            {
                mTextureAtlas[i].OptimizeTextureRegion(setting);
            }

            output.atlas = new List<TextureAtlas>();
            output.atlas.AddRange(mTextureAtlas);

            return output.missingTextures.Count == 0;
        }

        public bool CanPackInOneTexture(TexturePackerSetting setting, List<TexturePackerItem> images, bool sortInput, out TexturePackResult output)
        {
            bool success = Pack(setting, images, sortInput, true, out output);
            if (!success)
            {
                return false;
            }
            if (output.atlas.Count != 1)
            {
                return false;
            }

            return true;
        }

        void Clear(bool deleteTextureAtlas)
        {
            if (deleteTextureAtlas)
            {
                for (int i = 0; i < mTextureAtlas.Count; ++i)
                {
                    mTextureAtlas[i].OnDestroy();
                }
            }
            mTextureAtlas.Clear();
            mAlreadyPackedTextures.Clear();
        }

        bool FindEmptyBin(int imageWidth, int imageHeight, TexturePackerSetting setting, out int textureAtlasIndex, out PackRect emptyBin)
        {
            textureAtlasIndex = -1;
            emptyBin = new PackRect();

            for (int i = 0; i < mTextureAtlas.Count; ++i)
            {
                var binIndex = mTextureAtlas[i].FindEmptyBin(imageWidth, imageHeight, setting.borderSize, setting.padding, setting.atlasTextureMaxWidth, setting.atlasTextureMaxHeight, out emptyBin);
                if (binIndex == -2)
                {
                    //can't pack this texture!
                    return false;
                }

                if (binIndex >= 0)
                {
                    textureAtlasIndex = i;
                    break;
                }

                //只使用一张texture atlas
                if (setting.onlyPackOneTexture)
                {
                    break;
                }
            }

            if (textureAtlasIndex < 0)
            {
                if (setting.onlyPackOneTexture == false || mTextureAtlas.Count == 0)
                {
                    //no texture atlas found, create a new one
                    var atlas = new TextureAtlasEqualRects(setting.inputTextureWidth, setting.inputTextureHeight, setting.atlasTextureMaxWidth, setting.atlasTextureMaxHeight, setting.borderSize, setting.padding, setting.backgroundColor);
                    mTextureAtlas.Add(atlas);
                    textureAtlasIndex = mTextureAtlas.Count - 1;
                    atlas.FindEmptyBin(imageWidth, imageHeight, setting.borderSize, setting.padding, setting.atlasTextureMaxWidth, setting.atlasTextureMaxHeight, out emptyBin);
                }
                else
                {
                    //can't pack this texture
                    textureAtlasIndex = -1;
                    return false;
                }
            }

            Debug.Assert(!emptyBin.IsEmpty());
            return true;
        }

        void PackTextureToAtlas(string textureUniqueID, Texture2D input, TexturePackerSetting setting, bool treatAlphaAsZero, out List<string> missingTextures)
        {
            missingTextures = new List<string>();
            Debug.Assert(!string.IsNullOrEmpty(textureUniqueID));
            if (!mAlreadyPackedTextures.Contains(textureUniqueID))
            {
                mAlreadyPackedTextures.Add(textureUniqueID);
                var image = input;
                int w = image.width;
                int h = image.height;
                if (w <= setting.atlasTextureMaxWidth && h <= setting.atlasTextureMaxHeight)
                {
                    int textureAtlasIndex;
                    PackRect emptyBin;
                    bool validTextureSize = FindEmptyBin(w, h, setting, out textureAtlasIndex, out emptyBin);
                    if (!validTextureSize)
                    {
                        missingTextures.Add(textureUniqueID);
                    }
                    else
                    {
                        mTextureAtlas[textureAtlasIndex].AddImage(input.GetInstanceID(), textureUniqueID, image, emptyBin, setting, treatAlphaAsZero);
                    }
                }
                else
                {
                    missingTextures.Add(textureUniqueID);
                }
            }
        }

        List<TextureAtlasEqualRects> mTextureAtlas = new List<TextureAtlasEqualRects>();
        HashSet<string> mAlreadyPackedTextures = new HashSet<string>();
    };
}


#endif