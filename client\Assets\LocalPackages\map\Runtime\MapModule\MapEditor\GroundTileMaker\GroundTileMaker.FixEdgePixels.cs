﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class GroundTileMaker : MonoBehaviour
    {
        enum Corner
        {
            TopLeft,
            TopRight,
            BottomRight,
            BottomLeft,
        }

        public void CopyEdgePixels(EdgeID edge, EdgeDirection direction, int selfTileX, int selfTileY, int lod)
        {
            int curTileIndex = mInstances[selfTileX, selfTileY].tileIndex;
            var variation = mTiles[curTileIndex].GetCurrentVariation();
            var texture = variation.GetLOD(lod).maskTextures[mMaskTextureIndex].texture;
            var textureData = variation.GetLOD(lod).maskTextures[mMaskTextureIndex].textureData;
            int n = texture.width;

            int affectedPixelCount = n;
            Color[] pixels = new Color[affectedPixelCount];

            //获取当前tile的edge像素
            switch (edge)
            {
                case EdgeID.HorizontalLeft:
                case EdgeID.Horizontal:
                case EdgeID.HorizontalRight:
                    if (direction == EdgeDirection.Down)
                    {
                        for (int i = 0; i < affectedPixelCount; ++i)
                        {
                            pixels[i] = textureData[i];
                        }
                    }
                    else
                    {
                        for (int i = 0; i < affectedPixelCount; ++i)
                        {
                            pixels[i] = textureData[i + n * (n - 1)];
                        }
                    }
                    break;

                case EdgeID.Vertical:
                case EdgeID.VerticalDown:
                case EdgeID.VerticalUp:
                    if (direction == EdgeDirection.Left)
                    {
                        for (int i = 0; i < affectedPixelCount; ++i)
                        {
                            pixels[i] = textureData[i * n];
                        }
                    }
                    else
                    {
                        for (int i = 0; i < affectedPixelCount; ++i)
                        {
                            pixels[i] = textureData[i * n + n - 1];
                        }
                    }
                    break;
            }

            //把这些edge像素复制到同样id的边上
            CompoundAction actions = new CompoundAction("Copy Edge Pixels");
            for (int i = 0; i < mTiles.Length; ++i)
            {
                var edgeIDs = mTiles[i].edgeIDs;
                for (int e = 0; e < edgeIDs.Length; ++e)
                {
                    if (edgeIDs[e] == edge)
                    {
                        var instanceID = mTiles[i].variations[mTiles[i].currentVariationIndex].instanceID;
                        var action = new ActionSetGroundTileMaskTextureData(this, lod, i, mTiles[i].currentVariationIndex, instanceID, mMaskTextureIndex, edge, (EdgeDirection)e, pixels);
                        actions.Add(action);
                    }
                }
            }

            if (!actions.IsEmpty())
            {
                ActionManager.instance.PushAction(actions);
            }
        }

        public int PickTile(Vector3 worldPos, out int tileX, out int tileY)
        {
            tileX = Mathf.FloorToInt(worldPos.x / mTileSize);
            tileY = Mathf.FloorToInt(worldPos.z / mTileSize);
            if (tileX >= 0 && tileX < mInstances.GetLength(1) && tileY >= 0 && tileY < mInstances.GetLength(0))
            {
                Vector3 startPos = new Vector3(tileX * mTileSize, 0, tileY * mTileSize);
                Vector3 endPos = new Vector3(startPos.x + mTileSize, 0, startPos.z + mTileSize);
                if (mInstances[tileX, tileY] != null)
                {
                    return mInstances[tileX, tileY].tileIndex;
                }
            }

            return -1;
        }

        public bool PickEdge(Vector3 worldPos, out int tileX, out int tileY, out EdgeDirection direction, out int tileIndex)
        {
            int x = Mathf.FloorToInt(worldPos.x / mTileSize);
            int y = Mathf.FloorToInt(worldPos.z / mTileSize);
            if (x >= 0 && x < mInstances.GetLength(1) && y >= 0 && y < mInstances.GetLength(0))
            {
                Vector3 startPos = new Vector3(x * mTileSize, 0, y * mTileSize);
                Vector3 endPos = new Vector3(startPos.x + mTileSize, 0, startPos.z + mTileSize);
                float edgeSize = mEdgeWidth * (mTileSize / 180.0f);
                if (mInstances[x, y] != null)
                {
                    tileIndex = mInstances[x, y].tileIndex;
                    if (worldPos.x <= edgeSize + startPos.x)
                    {
                        //hit left edge
                        tileX = x;
                        tileY = y;
                        direction = EdgeDirection.Left;
                        return true;
                    }
                    if (worldPos.x >= endPos.x - edgeSize)
                    {
                        //hit right edge
                        tileX = x;
                        tileY = y;
                        direction = EdgeDirection.Right;
                        return true;
                    }
                    if (worldPos.z <= edgeSize + startPos.z)
                    {
                        //hit bottom edge
                        tileX = x;
                        tileY = y;
                        direction = EdgeDirection.Down;
                        return true;
                    }
                    if (worldPos.z >= endPos.z - edgeSize)
                    {
                        //hit top edge
                        tileX = x;
                        tileY = y;
                        direction = EdgeDirection.Up;
                        return true;
                    }
                }
            }
            tileX = -1;
            tileY = -1;
            direction = EdgeDirection.Up;
            tileIndex = -1;
            return false;
        }

        public bool PickEdge(Vector3 worldPos, out int tileX, out int tileY, out int tileIndex, out EdgeDirection direction, out EdgeID edgeID)
        {
            edgeID = 0;
            tileIndex = -1;
            direction = EdgeDirection.Up;
            tileX = Mathf.FloorToInt(worldPos.x / mTileSize);
            tileY = Mathf.FloorToInt(worldPos.z / mTileSize);
            float edgeSize = mEdgeWidth * (tileSize / 180.0f);
            if (tileX >= 0 && tileX < mInstances.GetLength(1) && tileY >= 0 && tileY < mInstances.GetLength(0))
            {
                if (mInstances[tileX, tileY] != null)
                {
                    tileIndex = mInstances[tileX, tileY].tileIndex;
                    Vector3 tileMinPos = new Vector3(tileX * mTileSize, 0, tileY * mTileSize);
                    Vector3 tileMaxPos = new Vector3(tileMinPos.x + mTileSize, 0, tileMinPos.z + mTileSize);
                    if (worldPos.x <= edgeSize + tileMinPos.x)
                    {
                        edgeID = mTiles[tileIndex].edgeIDs[(int)EdgeDirection.Left];
                        direction = EdgeDirection.Left;
                        return true;
                    }
                    if (worldPos.x >= tileMaxPos.x - edgeSize)
                    {
                        edgeID = mTiles[tileIndex].edgeIDs[(int)EdgeDirection.Right];
                        direction = EdgeDirection.Right;
                        return true;
                    }
                    if (worldPos.z <= edgeSize + tileMinPos.z)
                    {
                        edgeID = mTiles[tileIndex].edgeIDs[(int)EdgeDirection.Down];
                        direction = EdgeDirection.Down;
                        return true;
                    }
                    if (worldPos.z >= tileMaxPos.z - edgeSize)
                    {
                        edgeID = mTiles[tileIndex].edgeIDs[(int)EdgeDirection.Up];
                        direction = EdgeDirection.Up;
                        return true;
                    }
                }
            }
            return false;
        }

        void DrawPaintAreaGuideline()
        {
            if (mShowPaintAreaGuideline)
            {
                if (mInstances == null || mTiles.Length != 16)
                {
                    return;
                }
                Gizmos.color = new Color(0, 1, 0, 0.5f);
                switch (mPaintMode)
                {
                    case PaintMode.Paint1_2_4_8_3_5_10_12_15:
                        var center = new Vector3(horizontalInstanceCount / 2 * mTileSize + mTileSize * 0.5f, 0, verticalInstanceCount / 2 * mTileSize + mTileSize * 0.5f);
                        float radius = (horizontalInstanceCount - 2) * mTileSize * 0.75f;
                        DrawCircle(center, radius, 10);
                        break;
                    case PaintMode.Paint11:
                        DrawCorner(3, 2, Corner.BottomRight);
                        break;
                    case PaintMode.Paint13:
                        DrawCorner(1, 2, Corner.TopLeft);
                        break;
                    case PaintMode.Paint14:
                        DrawCorner(3, 2, Corner.TopRight);
                        break;
                    case PaintMode.Paint6:
                        DrawCorner(1, 3, Corner.TopRight);
                        DrawCorner(1, 3, Corner.BottomLeft);
                        break;
                    case PaintMode.Paint7:
                        DrawCorner(1, 2, Corner.BottomLeft);
                        break;
                    case PaintMode.Paint9:
                        DrawCorner(3, 3, Corner.BottomRight);
                        DrawCorner(3, 3, Corner.TopLeft);
                        break;
                    case PaintMode.Paint_1_2_4_8:
                        DrawCorner(1, 1, Corner.TopRight);
                        DrawCorner(2, 1, Corner.TopLeft);
                        DrawCorner(1, 2, Corner.BottomRight);
                        DrawCorner(2, 2, Corner.BottomLeft);
                        break;
                    case PaintMode.Paint_14_9_7:
                        DrawCorner(1, 1, Corner.BottomLeft);
                        DrawCorner(0, 1, Corner.TopLeft);
                        DrawCorner(0, 1, Corner.BottomRight);
                        DrawCorner(0, 0, Corner.TopRight);
                        DrawCorner(1, 0, Corner.TopLeft);
                        DrawCorner(1, 0, Corner.BottomRight);
                        break;
                    case PaintMode.Paint_13_11_6:
                        DrawCorner(1, 0, Corner.TopLeft);
                        DrawCorner(0, 0, Corner.TopRight);
                        DrawCorner(0, 0, Corner.BottomLeft);
                        DrawCorner(1, 1, Corner.TopRight);
                        DrawCorner(1, 1, Corner.BottomLeft);
                        DrawCorner(0, 1, Corner.BottomRight);
                        break;
                    case PaintMode.Paint_11_9_6:
                        DrawCorner(0, 0, Corner.TopRight);
                        DrawCorner(0, 0, Corner.BottomLeft);
                        DrawCorner(0, 1, Corner.BottomRight);
                        DrawCorner(1, 1, Corner.TopRight);
                        DrawCorner(1, 1, Corner.BottomLeft);
                        DrawCorner(1, 0, Corner.TopLeft);
                        DrawCorner(1, 0, Corner.BottomRight);
                        break;
                    case PaintMode.Paint_11_7_14_13:
                        DrawCorner(0, 0, Corner.TopRight);
                        DrawCorner(1, 0, Corner.TopLeft);
                        DrawCorner(0, 1, Corner.BottomRight);
                        DrawCorner(1, 1, Corner.BottomLeft);
                        break;
                    case PaintMode.Paint_10_5:
                    case PaintMode.Paint_10_1:
                    case PaintMode.Paint_10_4:
                    case PaintMode.Paint_1_2_4_8_12_3:
                    case PaintMode.Paint_1_2_4_8_5_10:
                    case PaintMode.Paint_1_2_6_8:
                    case PaintMode.Paint_9_2_4_8:
                        break;
                    default:
                        Debug.Assert(false, "todo");
                        break;
                }
            }
        }

        void DrawCircle(Vector3 center, float radius, int sectionCount)
        {
            DrawQuater(center, radius, sectionCount, 0);
            DrawQuater(center, radius, sectionCount, 90);
            DrawQuater(center, radius, sectionCount, 180);
            DrawQuater(center, radius, sectionCount, 270);
        }

        void DrawQuater(Vector3 center, float radius, int sectionCount, int startAngle)
        {
            float a = startAngle;
            var delta = 90 / (sectionCount - 1);
            Vector3 lastPos = Vector3.zero;
            for (int i = 0; i < sectionCount; ++i)
            {
                var linePos = new Vector3(Mathf.Cos(a * Mathf.Deg2Rad) * radius * 0.5f, 0, Mathf.Sin(a * Mathf.Deg2Rad) * radius * 0.5f) + center;
                if (i > 0)
                {
                    Gizmos.DrawLine(lastPos, linePos);
                }
                lastPos = linePos;
                a += delta;
            }
        }

        void DrawCorner(int tileX, int tileY, Corner corner)
        {
            int sectionCount = 10;
            var pos = new Vector3(tileX * mTileSize, 0, tileY * mTileSize);
            switch (corner)
            {
                case Corner.BottomLeft:
                    {
                        DrawQuater(pos, mTileSize, sectionCount, 0);
                        break;
                    }
                case Corner.BottomRight:
                    {
                        DrawQuater(new Vector3(pos.x + mTileSize, 0, pos.z), mTileSize, sectionCount, 90);
                        break;
                    }
                case Corner.TopLeft:
                    {
                        DrawQuater(new Vector3(pos.x, 0, pos.z + mTileSize), mTileSize, sectionCount, 270);
                        break;
                    }
                case Corner.TopRight:
                    {
                        DrawQuater(new Vector3(pos.x + mTileSize, 0, pos.z + mTileSize), mTileSize, sectionCount, 180);
                        break;
                    }
                default:
                    Debug.Assert(false);
                    break;
            }
        }

        void DrawEdges()
        {
            if (mShowEdge)
            {
                if (mTiles != null)
                {
                    int h = horizontalInstanceCount;
                    int v = verticalInstanceCount;
                    float edgeSize = mEdgeWidth * mTileSize / 180.0f;
                    for (int y = 0; y < v; ++y)
                    {
                        for (int x = 0; x < h; ++x)
                        {
                            var instance = mInstances[x, y];
                            if (instance != null)
                            {
                                var pos = new Vector3(x * mTileSize, 0, y * mTileSize);
                                var tileIndex = instance.tileIndex;
                                var edgeIDs = mTiles[tileIndex].edgeIDs;
                                for (int e = 0; e < edgeIDs.Length; ++e)
                                {
                                    var edgeID = edgeIDs[e];
                                    if (edgeID != EdgeID.Empty)
                                    {
                                        float edgeLength = mTileSize;
                                        if (edgeID == EdgeID.HorizontalLeft)
                                        {
                                            Vector3 center;
                                            if (e == (int)EdgeDirection.Up)
                                            {
                                                center = new Vector3(pos.x, 0, pos.z + mTileSize) + edgeLength * 0.5f * Vector3.right;
                                            }
                                            else
                                            {
                                                center = pos + edgeLength * 0.5f * Vector3.right;
                                            }

                                            var size = new Vector3(edgeLength, 0, edgeSize);
                                            Gizmos.DrawWireCube(center, size);
                                        }
                                        else if (edgeID == EdgeID.HorizontalRight)
                                        {
                                            Vector3 center;
                                            if (e == (int)EdgeDirection.Up)
                                            {
                                                center = new Vector3(pos.x + mTileSize, 0, pos.z + mTileSize) + edgeLength * 0.5f * Vector3.left;
                                            }
                                            else
                                            {
                                                center = new Vector3(pos.x + mTileSize, 0, pos.z) + edgeLength * 0.5f * Vector3.left;
                                            }

                                            var size = new Vector3(edgeLength, 0, edgeSize);
                                            Gizmos.DrawWireCube(center, size);
                                        }
                                        else if (edgeID == EdgeID.VerticalDown)
                                        {
                                            Vector3 center;
                                            if (e == (int)EdgeDirection.Left)
                                            {
                                                center = new Vector3(pos.x, 0, pos.z) + edgeLength * 0.5f * Vector3.forward;
                                            }
                                            else
                                            {
                                                center = new Vector3(pos.x + mTileSize, 0, pos.z) + edgeLength * 0.5f * Vector3.forward;
                                            }

                                            var size = new Vector3(edgeSize, 0, edgeLength);
                                            Gizmos.DrawWireCube(center, size);
                                        }
                                        else if (edgeID == EdgeID.VerticalUp)
                                        {
                                            Vector3 center;
                                            if (e == (int)EdgeDirection.Left)
                                            {
                                                center = new Vector3(pos.x, 0, pos.z + mTileSize) + edgeLength * 0.5f * Vector3.back;
                                            }
                                            else
                                            {
                                                center = new Vector3(pos.x + mTileSize, 0, pos.z + mTileSize) + edgeLength * 0.5f * Vector3.back;
                                            }

                                            var size = new Vector3(edgeSize, 0, edgeLength);
                                            Gizmos.DrawWireCube(center, size);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        public enum EdgeID
        {
            Empty = -1,
            HorizontalLeft,
            HorizontalRight,
            VerticalDown,
            VerticalUp,
            Horizontal,
            Vertical,

            EdgeIDCount,
        }

        void AssignEdgeID()
        {
            if (mTiles.Length == 16)
            {
                mTiles[1].edgeIDs[(int)EdgeDirection.Right] = EdgeID.VerticalUp;
                mTiles[1].edgeIDs[(int)EdgeDirection.Up] = EdgeID.HorizontalRight;
                mTiles[2].edgeIDs[(int)EdgeDirection.Left] = EdgeID.VerticalUp;
                mTiles[2].edgeIDs[(int)EdgeDirection.Up] = EdgeID.HorizontalLeft;
                mTiles[3].edgeIDs[(int)EdgeDirection.Left] = EdgeID.VerticalUp;
                mTiles[3].edgeIDs[(int)EdgeDirection.Right] = EdgeID.VerticalUp;
                mTiles[3].edgeIDs[(int)EdgeDirection.Up] = EdgeID.Horizontal;

                mTiles[4].edgeIDs[(int)EdgeDirection.Right] = EdgeID.VerticalDown;
                mTiles[4].edgeIDs[(int)EdgeDirection.Down] = EdgeID.HorizontalRight;

                mTiles[5].edgeIDs[(int)EdgeDirection.Up] = EdgeID.HorizontalRight;
                mTiles[5].edgeIDs[(int)EdgeDirection.Down] = EdgeID.HorizontalRight;
                mTiles[5].edgeIDs[(int)EdgeDirection.Right] = EdgeID.Vertical;

                mTiles[6].edgeIDs[(int)EdgeDirection.Right] = EdgeID.VerticalDown;
                mTiles[6].edgeIDs[(int)EdgeDirection.Down] = EdgeID.HorizontalRight;
                mTiles[6].edgeIDs[(int)EdgeDirection.Left] = EdgeID.VerticalUp;
                mTiles[6].edgeIDs[(int)EdgeDirection.Up] = EdgeID.HorizontalLeft;

                mTiles[7].edgeIDs[(int)EdgeDirection.Left] = EdgeID.VerticalUp;
                mTiles[7].edgeIDs[(int)EdgeDirection.Right] = EdgeID.Vertical;
                mTiles[7].edgeIDs[(int)EdgeDirection.Down] = EdgeID.HorizontalRight;
                mTiles[7].edgeIDs[(int)EdgeDirection.Up] = EdgeID.Horizontal;

                mTiles[8].edgeIDs[(int)EdgeDirection.Left] = EdgeID.VerticalDown;
                mTiles[8].edgeIDs[(int)EdgeDirection.Down] = EdgeID.HorizontalLeft;

                mTiles[9].edgeIDs[(int)EdgeDirection.Left] = EdgeID.VerticalDown;
                mTiles[9].edgeIDs[(int)EdgeDirection.Down] = EdgeID.HorizontalLeft;
                mTiles[9].edgeIDs[(int)EdgeDirection.Up] = EdgeID.HorizontalRight;
                mTiles[9].edgeIDs[(int)EdgeDirection.Right] = EdgeID.VerticalUp;

                mTiles[10].edgeIDs[(int)EdgeDirection.Up] = EdgeID.HorizontalLeft;
                mTiles[10].edgeIDs[(int)EdgeDirection.Down] = EdgeID.HorizontalLeft;
                mTiles[10].edgeIDs[(int)EdgeDirection.Left] = EdgeID.Vertical;

                mTiles[11].edgeIDs[(int)EdgeDirection.Left] = EdgeID.Vertical;
                mTiles[11].edgeIDs[(int)EdgeDirection.Right] = EdgeID.VerticalUp;
                mTiles[11].edgeIDs[(int)EdgeDirection.Up] = EdgeID.Horizontal;
                mTiles[11].edgeIDs[(int)EdgeDirection.Down] = EdgeID.HorizontalLeft;

                mTiles[12].edgeIDs[(int)EdgeDirection.Left] = EdgeID.VerticalDown;
                mTiles[12].edgeIDs[(int)EdgeDirection.Right] = EdgeID.VerticalDown;
                mTiles[12].edgeIDs[(int)EdgeDirection.Down] = EdgeID.Horizontal;

                mTiles[13].edgeIDs[(int)EdgeDirection.Left] = EdgeID.VerticalDown;
                mTiles[13].edgeIDs[(int)EdgeDirection.Down] = EdgeID.Horizontal;
                mTiles[13].edgeIDs[(int)EdgeDirection.Right] = EdgeID.Vertical;
                mTiles[13].edgeIDs[(int)EdgeDirection.Up] = EdgeID.HorizontalRight;

                mTiles[14].edgeIDs[(int)EdgeDirection.Left] = EdgeID.Vertical;
                mTiles[14].edgeIDs[(int)EdgeDirection.Right] = EdgeID.VerticalDown;
                mTiles[14].edgeIDs[(int)EdgeDirection.Down] = EdgeID.Horizontal;
                mTiles[14].edgeIDs[(int)EdgeDirection.Up] = EdgeID.HorizontalLeft;

                mTiles[15].edgeIDs[(int)EdgeDirection.Up] = EdgeID.Horizontal;
                mTiles[15].edgeIDs[(int)EdgeDirection.Down] = EdgeID.Horizontal;
                mTiles[15].edgeIDs[(int)EdgeDirection.Right] = EdgeID.Vertical;
                mTiles[15].edgeIDs[(int)EdgeDirection.Left] = EdgeID.Vertical;
            }
        }
    }
}

#endif