﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using UnityEditor.SceneManagement;

namespace TFW.Map
{
    [CustomEditor(typeof(TextureTilingConfig))]
    public class TextureTilingConfigUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            var textureTilingConfig = target as TextureTilingConfig;
            mUCurveUI = new CurveConfigUI(textureTilingConfig, OnSetDirty, "Height", "Tiling", "Texture Tiling Setting");
        }

        void OnDisable()
        {
            if (mUCurveUI != null)
            {
                mUCurveUI.OnDestroy();
            }
        }

        public override void OnInspectorGUI()
        {
            if (mUCurveUI != null)
            {
                mUCurveUI.OnInspectorGUI();
            }
        }

        void OnSetDirty()
        {
            var textureTilingConfig = target as TextureTilingConfig;
            UnityEditor.EditorUtility.SetDirty(textureTilingConfig);
            UnityEditor.EditorUtility.SetDirty(this);

            if (EditorApplication.isPlaying == false)
            {
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            }
        }

        CurveConfigUI mUCurveUI;
    }
}

#endif