{"skeleton": {"hash": "QU3T+Wgee4Y", "spine": "4.2.33", "x": -304.47, "y": -73.51, "width": 592, "height": 1692.41, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "x": 37.09, "y": 853.59, "icon": "arrows"}, {"name": "body", "parent": "ALL", "length": 131.04, "rotation": 93.39, "x": -0.57, "y": 26.74}, {"name": "body2", "parent": "body", "length": 80.71, "rotation": 94.82, "x": 131.04, "inherit": "noRotationOrReflection"}, {"name": "body3", "parent": "body2", "length": 300.73, "rotation": 93.07, "x": 80.71, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body3", "length": 37.79, "rotation": 89.91, "x": 300.73, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 188.7, "rotation": 7.05, "x": 37.79}, {"name": "eyebrow_L", "parent": "head", "length": 13.67, "rotation": 72.06, "x": 63.58, "y": -43.61}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 19.27, "rotation": 38.88, "x": 13.67}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 13.62, "rotation": -1.71, "x": 19.27}, {"name": "eyebrow_R", "parent": "head", "length": 11.34, "rotation": -53.39, "x": 59.04, "y": 65.97}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 19.34, "rotation": -53.71, "x": 11.34}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 15.08, "rotation": -6.85, "x": 19.34}, {"name": "eye_L", "parent": "head", "x": 48.03, "y": -19.37}, {"name": "eye_R", "parent": "head", "x": 48.15, "y": 38.93}, {"name": "earring_L", "parent": "head", "length": 34.53, "rotation": -90.5, "x": 16.81, "y": -55.19, "inherit": "noRotationOrReflection"}, {"name": "earring_R", "parent": "head", "length": 34.46, "rotation": -88.77, "x": 15.23, "y": 70.47, "inherit": "noRotationOrReflection"}, {"name": "sh_L", "parent": "body3", "x": 263.9, "y": -146.81, "inherit": "noScale"}, {"name": "sh_R", "parent": "body3", "x": 271.96, "y": 150.01, "inherit": "noScale"}, {"name": "RU_L", "parent": "body3", "length": 40, "x": 112.47, "y": -37.78}, {"name": "RU_L2", "parent": "RU_L", "length": 40, "x": -10.54, "y": -3.96}, {"name": "RU_L3", "parent": "RU_L2", "length": 40, "x": -13.78, "y": -5.32}, {"name": "RU_R", "parent": "body3", "length": 40, "x": 118.63, "y": 99.92}, {"name": "RU_R2", "parent": "RU_R", "length": 40, "x": -11.06, "y": 30.71}, {"name": "RU_R3", "parent": "RU_R2", "length": 40, "x": -9.63, "y": 22.38}, {"name": "arm_L", "parent": "sh_L", "length": 241.84, "rotation": 177.63, "x": -19.5, "y": 3.7}, {"name": "arm_R", "parent": "sh_R", "length": 222.52, "rotation": 177.9, "x": -14.83, "y": -11.38}, {"name": "arm_R2", "parent": "arm_R", "length": 231.04, "rotation": 25.52, "x": 222.52}, {"name": "leg_L", "parent": "ALL", "x": -55.11, "y": 93.74, "icon": "circle"}, {"name": "leg_L2", "parent": "leg_L", "length": 362.96, "rotation": -80.9}, {"name": "leg_L3", "parent": "leg_L2", "length": 149.44, "rotation": -3.99, "x": 362.96}, {"name": "leg_L4", "parent": "leg_L3", "length": 84.43, "rotation": -22.29, "x": 149.44}, {"name": "leg_R", "parent": "ALL", "x": -231.09, "y": -458.22, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone", "parent": "leg_R", "length": 100, "rotation": -91.33, "color": "ff3f00ff"}, {"name": "foot_R", "parent": "bone", "length": 59.9, "rotation": -171.13, "x": 203.43, "y": 7.46}, {"name": "foot_R2", "parent": "foot_R", "length": 143.31, "rotation": -9.63, "x": 59.9}, {"name": "bone38", "parent": "ALL", "length": 34.74, "rotation": 37.12, "x": -284.34, "y": 46.46}, {"name": "sh_R2", "parent": "sh_R", "length": 234.73, "rotation": 170.59, "x": -14.01, "y": -10.8}, {"name": "sh_R3", "parent": "sh_R2", "length": 237.1, "rotation": 40.44, "x": 234.73, "color": "abe323ff"}, {"name": "arm_R3", "parent": "ALL", "x": -60.94, "y": 58.47, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "ALL", "x": -163.55, "y": 264.81, "color": "ff3f00ff", "icon": "ik"}, {"name": "headround3", "parent": "head", "x": 405.17, "y": -2.76, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -63.24, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 344.09, "y": -66, "icon": "warning"}, {"name": "bodyround", "parent": "body3", "x": 167.45, "y": -351.95, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body3", "x": 102.4, "y": -351.95, "icon": "warning"}, {"name": "body4", "parent": "body", "x": 122.61, "y": -123.59}, {"name": "body5", "parent": "body", "x": 116.41, "y": 137.63}, {"name": "arm_Lb", "parent": "arm_L", "length": 108, "rotation": 166.96, "x": 241.84}, {"name": "hand_L", "parent": "arm_Lb", "length": 42, "rotation": 70.51, "x": 108, "inherit": "onlyTranslation"}, {"name": "hand_L2", "parent": "hand_L", "length": 58, "rotation": 43.3, "x": 42}, {"name": "arm_L2", "parent": "body3", "rotation": -93.07, "x": 106.88, "y": -161.82, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R4", "parent": "ALL", "rotation": 71.3, "x": -106.12, "y": -89.63}, {"name": "leg_R5", "parent": "leg_R4", "length": 381.47, "rotation": 179.95, "x": -7.73, "y": -0.16}, {"name": "arm_Lb2", "parent": "arm_Lb", "x": 95.07, "y": -0.83, "inherit": "noScale"}], "slots": [{"name": "chair0", "bone": "ALL", "attachment": "chair0"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "leg_R", "bone": "root", "attachment": "leg_R"}, {"name": "body3", "bone": "root", "attachment": "body"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "arm", "bone": "root", "attachment": "arm"}, {"name": "body2", "bone": "root", "attachment": "body"}, {"name": "leg_Lb", "bone": "root", "attachment": "leg_Lb"}, {"name": "arm_Lb", "bone": "root", "attachment": "arm_Lb"}, {"name": "hand_L", "bone": "root", "attachment": "hand_L"}, {"name": "ear_R", "bone": "root", "attachment": "ear_R"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "nose", "bone": "root", "attachment": "nose"}, {"name": "hair_F", "bone": "root", "attachment": "body"}], "ik": [{"name": "arm_L", "order": 5, "bones": ["arm_Lb"], "target": "arm_L2", "compress": true, "stretch": true}, {"name": "arm_R", "bones": ["sh_R2", "sh_R3"], "target": "arm_R3"}, {"name": "arm_R1", "order": 2, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 3, "bones": ["arm_R2"], "target": "arm_R3", "compress": true, "stretch": true}, {"name": "leg_R", "order": 6, "bones": ["leg_R5"], "target": "leg_R", "compress": true, "stretch": true}], "transform": [{"name": "arm_R3", "order": 1, "bones": ["arm_R1"], "target": "sh_R3", "rotation": 55.9, "x": 8.7, "y": 30.71, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 7, "bones": ["bodyround2"], "target": "bodyround", "x": -65.04, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 4, "bones": ["headround2"], "target": "headround", "x": -61.08, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 8, "bones": ["eyebrow_L"], "target": "headround", "rotation": 72.06, "x": -341.59, "y": 22.39, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 9, "bones": ["eyebrow_R"], "target": "headround", "rotation": -53.39, "x": -346.13, "y": 131.96, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 10, "bones": ["earring_L"], "target": "headround", "rotation": 172.54, "x": -388.36, "y": 10.8, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 11, "bones": ["earring_R"], "target": "headround", "rotation": 175.77, "x": -389.94, "y": 136.47, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"arm": {"arm": {"type": "mesh", "uvs": [0.98106, 0.05102, 0.9886, 0.09886, 0.99534, 0.14718, 0.99657, 0.20725, 0.99266, 0.25439, 0.98867, 0.2951, 0.98263, 0.3753, 0.98029, 0.47395, 0.97987, 0.54596, 0.96876, 0.58957, 0.94918, 0.61884, 0.90907, 0.63507, 0.88012, 0.63511, 0.85256, 0.62088, 0.83654, 0.60266, 0.81908, 0.5794, 0.80503, 0.54933, 0.79523, 0.47986, 0.78859, 0.38235, 0.78365, 0.28922, 0.78213, 0.24546, 0.7809, 0.19914, 0.64349, 0.19949, 0.50608, 0.19983, 0.36867, 0.20018, 0.23125, 0.20053, 0.2233, 0.24645, 0.21768, 0.29761, 0.21207, 0.34876, 0.20471, 0.45016, 0.20201, 0.51099, 0.20639, 0.53407, 0.21156, 0.55715, 0.22348, 0.58237, 0.23592, 0.60899, 0.2514, 0.64555, 0.27997, 0.68472, 0.33675, 0.782, 0.35474, 0.80635, 0.45124, 0.91965, 0.44576, 0.92526, 0.32984, 1, 0.29966, 0.97978, 0.1887, 0.87031, 0.09732, 0.73806, 0.04355, 0.6482, 0.01825, 0.62645, 0.03147, 0.60194, 0.01551, 0.57253, 0.02577, 0.55255, 0.01327, 0.52993, 0.01215, 0.49232, 0.00407, 0.43106, 0.0097, 0.31642, 0.00046, 0.24379, 0.01536, 0.19008, 0.01802, 0.14371, 0.02251, 0.09717, 0.02818, 0.05464, 0.03781, 0.02628, 0.05173, 0.01604, 0.09371, 0.01615, 0.1502, 0.01519, 0.31614, 0.01238, 0.48208, 0.00957, 0.64802, 0.00675, 0.81396, 0.00394, 0.88113, 0.0028, 0.93155, 0.00196, 0.95024, 0.00269, 0.9699, 0.01555, 0.83253, 0.21091, 0.86143, 0.14663, 0.89612, 0.07135, 0.91117, 0.02884, 0.05034, 0.04708, 0.10471, 0.20549, 0.08152, 0.15064, 0.06027, 0.09049, 0.16493, 0.25262, 0.15804, 0.30945, 0.14948, 0.50589, 0.1517, 0.53452, 0.15541, 0.5668, 0.16802, 0.59851, 0.09518, 0.6133, 0.08554, 0.57989, 0.07738, 0.54366, 0.07515, 0.51138, 0.06535, 0.30989, 0.06348, 0.25576, 0.05978, 0.2013, 0.92973, 0.14441, 0.92779, 0.20371, 0.92775, 0.08538, 0.05719, 0.14774, 0.84308, 0.25049, 0.84762, 0.29271, 0.84678, 0.44635, 0.92741, 0.25326, 0.92922, 0.2934, 0.92951, 0.45076, 0.82758, 0.5279, 0.82524, 0.57594, 0.84009, 0.48372, 0.92733, 0.4994, 0.90873, 0.59387, 0.9205, 0.54722], "triangles": [96, 71, 93, 93, 92, 2, 97, 96, 99, 93, 2, 3, 5, 100, 4, 6, 100, 5, 100, 101, 98, 18, 97, 98, 100, 6, 101, 7, 101, 6, 17, 18, 98, 104, 17, 98, 101, 104, 98, 105, 101, 7, 105, 104, 101, 102, 17, 104, 102, 104, 105, 8, 105, 7, 107, 102, 105, 107, 105, 8, 16, 17, 102, 103, 16, 102, 103, 102, 107, 15, 16, 103, 9, 107, 8, 106, 103, 107, 106, 107, 9, 14, 103, 106, 15, 103, 14, 10, 106, 9, 13, 14, 106, 11, 106, 10, 12, 13, 106, 11, 12, 106, 98, 97, 100, 4, 99, 3, 100, 99, 4, 99, 93, 3, 97, 99, 100, 18, 19, 97, 99, 96, 93, 19, 96, 97, 19, 20, 96, 92, 1, 2, 83, 82, 31, 83, 31, 32, 86, 87, 83, 48, 49, 86, 84, 83, 32, 84, 32, 33, 47, 48, 86, 84, 33, 34, 85, 86, 83, 85, 83, 84, 47, 86, 85, 45, 47, 85, 46, 47, 45, 44, 85, 84, 45, 85, 44, 35, 44, 84, 43, 44, 37, 34, 35, 84, 36, 44, 35, 37, 44, 36, 43, 37, 38, 40, 38, 39, 42, 43, 38, 42, 38, 40, 41, 42, 40, 91, 77, 76, 95, 57, 78, 56, 57, 95, 55, 56, 95, 91, 95, 77, 55, 95, 91, 54, 55, 91, 90, 91, 76, 90, 76, 79, 54, 91, 90, 27, 79, 26, 80, 90, 79, 80, 79, 27, 89, 90, 80, 53, 54, 90, 53, 90, 89, 28, 80, 27, 52, 89, 88, 89, 52, 53, 51, 52, 88, 89, 81, 88, 28, 81, 80, 28, 29, 81, 30, 81, 29, 89, 80, 81, 50, 51, 88, 82, 81, 30, 82, 30, 31, 88, 81, 82, 87, 88, 82, 50, 88, 87, 49, 50, 87, 87, 82, 83, 49, 87, 86, 95, 78, 77, 62, 77, 78, 62, 78, 61, 75, 59, 60, 58, 59, 75, 61, 75, 60, 78, 75, 61, 58, 75, 78, 57, 58, 78, 73, 74, 94, 94, 0, 1, 92, 94, 1, 73, 72, 66, 72, 73, 94, 72, 94, 92, 21, 66, 72, 93, 72, 92, 71, 72, 93, 73, 66, 67, 74, 67, 68, 73, 67, 74, 74, 68, 69, 74, 69, 70, 0, 94, 74, 0, 74, 70, 26, 79, 25, 25, 62, 63, 25, 77, 62, 25, 63, 24, 20, 71, 96, 20, 21, 71, 71, 21, 72, 65, 23, 64, 22, 65, 21, 22, 23, 65, 24, 63, 64, 24, 64, 23, 21, 65, 66, 76, 77, 25, 25, 79, 76], "vertices": [2, 17, -11.36, -28.93, 0.73033, 25, -9.47, 32.27, 0.26967, 3, 4, 229.99, -177.24, 0.00041, 17, -33.91, -30.43, 0.41561, 25, 12.99, 34.69, 0.58398, 3, 4, 207.23, -178.43, 0.0013, 17, -56.67, -31.62, 0.27355, 25, 35.68, 36.83, 0.72515, 3, 4, 179.07, -177.36, 0.00046, 17, -84.83, -30.55, 0.1609, 25, 63.86, 36.92, 0.83864, 2, 17, -106.83, -27.97, 0.09143, 25, 85.95, 35.25, 0.90857, 2, 17, -125.82, -25.52, 0.04857, 25, 105.02, 33.58, 0.95143, 1, 25, 142.61, 30.96, 1, 1, 25, 188.86, 29.55, 1, 1, 25, 222.63, 28.98, 1, 1, 25, 243.03, 24.75, 1, 1, 25, 256.67, 17.57, 1, 1, 25, 264.1, 3.12, 1, 1, 25, 263.99, -7.24, 1, 1, 25, 257.2, -17.03, 1, 1, 25, 248.58, -22.66, 1, 1, 25, 237.6, -28.77, 1, 1, 25, 223.43, -33.63, 1, 1, 25, 190.82, -36.73, 1, 2, 17, -162.84, 48.2, 0.15714, 25, 145.05, -38.54, 0.84286, 3, 4, 144.77, -99.18, 0.04807, 17, -119.13, 47.63, 0.39672, 25, 101.36, -39.78, 0.55521, 3, 4, 165.29, -99.74, 0.26614, 17, -98.61, 47.07, 0.43705, 25, 80.83, -40.07, 0.29681, 2, 4, 187.01, -100.47, 0.9208, 17, -76.89, 46.34, 0.0792, 1, 4, 189.48, -51.34, 1, 1, 4, 191.95, -2.2, 1, 1, 4, 194.43, 46.93, 1, 1, 4, 196.9, 96.06, 1, 2, 4, 175.55, 100.06, 0.63722, 26, 80.11, 41.54, 0.36278, 2, 4, 151.7, 103.35, 0.35826, 26, 104.07, 39.12, 0.64174, 2, 4, 127.85, 106.64, 0.16571, 26, 128.02, 36.7, 0.83429, 3, 4, 80.5, 111.82, 0.04286, 26, 175.52, 33.27, 0.95431, 27, -28.2, 50.24, 0.00283, 2, 26, 204.03, 31.82, 0.77698, 27, -3.02, 36.68, 0.22302, 2, 26, 214.88, 33.2, 0.51631, 27, 7.4, 33.26, 0.48369, 2, 26, 225.73, 34.87, 0.24202, 27, 17.94, 30.1, 0.75798, 2, 26, 237.63, 38.93, 0.05948, 27, 30.46, 28.65, 0.94052, 2, 26, 250.19, 43.18, 0.00569, 27, 43.66, 27.08, 0.99431, 1, 27, 61.53, 24.41, 1, 1, 27, 82.59, 25.39, 1, 1, 27, 132.62, 23.27, 1, 1, 27, 145.75, 23.96, 1, 1, 27, 208.87, 31.23, 1, 1, 27, 210.36, 28.3, 1, 1, 27, 223.3, -24.46, 1, 1, 27, 209.96, -29.92, 1, 1, 27, 146.14, -42.63, 1, 1, 27, 75.85, -44.3, 1, 2, 26, 267.41, -26, 0.0137, 27, 29.43, -42.77, 0.9863, 2, 26, 257.06, -34.88, 0.05407, 27, 16.23, -46.34, 0.94593, 2, 26, 245.65, -29.95, 0.15165, 27, 8.02, -36.98, 0.84835, 2, 26, 231.76, -35.43, 0.41484, 27, -6.91, -35.96, 0.58516, 2, 26, 222.45, -31.6, 0.62761, 27, -13.69, -28.5, 0.37239, 2, 26, 211.77, -35.89, 0.85258, 27, -25.21, -27.78, 0.14742, 2, 26, 194.13, -36, 0.98437, 27, -41.22, -20.29, 0.01563, 1, 26, 165.35, -38.4, 1, 1, 26, 111.63, -35.48, 1, 1, 26, 77.51, -38.2, 1, 2, 18, -66.02, 22.97, 0.00404, 26, 52.41, -32.45, 0.99596, 2, 18, -44.36, 20.85, 0.06359, 26, 30.69, -31.13, 0.93641, 2, 18, -22.65, 18.08, 0.33823, 26, 8.89, -29.15, 0.66177, 2, 18, -2.84, 14.98, 0.89306, 26, -11.02, -26.78, 0.10694, 1, 18, 10.26, 10.83, 1, 2, 4, 286.75, 155.6, 9e-05, 18, 14.79, 5.59, 0.99991, 2, 4, 285.89, 140.59, 0.11794, 18, 13.93, -9.41, 0.88206, 3, 4, 285.26, 120.38, 0.37575, 18, 13.29, -29.63, 0.5949, 26, -28.78, 17.21, 0.02935, 1, 4, 283.39, 60.98, 1, 1, 4, 281.52, 1.59, 1, 2, 4, 279.66, -57.8, 0.68704, 17, 15.76, 89.01, 0.31296, 2, 4, 277.79, -117.19, 0.15202, 17, 13.89, 29.62, 0.84798, 2, 4, 277.03, -141.23, 0.01528, 17, 13.14, 5.58, 0.98472, 1, 17, 12.56, -12.47, 1, 2, 17, 11.86, -19.13, 0.98244, 25, -32.28, 21.52, 0.01756, 2, 17, 5.46, -25.84, 0.89565, 25, -26.16, 28.48, 0.10435, 2, 4, 180.5, -118.63, 0.77629, 17, -83.39, 28.18, 0.22371, 2, 4, 210.05, -130.57, 0.34149, 17, -53.85, 16.24, 0.65851, 2, 4, 244.64, -144.87, 0.07393, 17, -19.25, 1.94, 0.92607, 1, 17, 0.37, -4.51, 1, 1, 18, 0.28, 6.87, 1, 3, 4, 197.01, 141.42, 0.21714, 18, -74.95, -8.59, 0.23038, 26, 60.18, -0.58, 0.55247, 2, 4, 223.14, 148.33, 0.44, 18, -48.82, -1.67, 0.56, 2, 4, 251.72, 154.42, 0.15036, 18, -20.24, 4.41, 0.84964, 3, 4, 173.78, 121.08, 0.33424, 18, -98.18, -28.93, 0.08286, 26, 82.65, 20.6, 0.5829, 2, 4, 147.3, 124.97, 0.24063, 26, 109.26, 17.68, 0.75937, 2, 26, 201.32, 13.05, 0.93641, 27, -13.56, 20.9, 0.06359, 2, 26, 214.76, 13.62, 0.65378, 27, -1.15, 15.63, 0.34622, 2, 26, 229.92, 14.69, 0.12901, 27, 13.03, 10.08, 0.87099, 2, 26, 244.87, 18.95, 0.01423, 27, 28.4, 7.5, 0.98577, 2, 26, 251.36, -7.24, 0.01344, 27, 22.98, -18.94, 0.98656, 2, 26, 235.64, -10.42, 0.16708, 27, 7.38, -15.05, 0.83292, 2, 26, 218.59, -13.06, 0.75299, 27, -9.18, -10.1, 0.24701, 1, 26, 203.45, -13.6, 1, 1, 26, 108.9, -15.5, 1, 2, 18, -97.7, 7.42, 0.00571, 26, 83.51, -15.74, 0.99429, 2, 18, -72.13, 7.37, 0.07451, 26, 57.95, -16.63, 0.92549, 3, 4, 209.78, -155.05, 0.01377, 17, -54.11, -8.24, 0.54983, 25, 34.09, 13.36, 0.43641, 3, 4, 182.05, -152.86, 0.00828, 17, -81.85, -6.05, 0.33692, 25, 61.9, 12.32, 0.6548, 3, 4, 237.47, -155.82, 0.00969, 17, -26.43, -9.01, 0.81542, 25, 6.4, 12.99, 0.17489, 2, 18, -46.99, 6.95, 0.19147, 26, 32.81, -17.13, 0.80853, 3, 4, 161.77, -121.4, 0.11733, 17, -102.13, 25.41, 0.35955, 25, 83.46, -18.27, 0.52312, 3, 4, 141.91, -121.96, 0.00601, 17, -121.99, 24.85, 0.2892, 25, 103.28, -16.9, 0.70479, 1, 25, 175.33, -18.09, 1, 3, 4, 158.85, -151.48, 0.00165, 17, -105.05, -4.67, 0.18577, 25, 85.13, 11.9, 0.81258, 2, 17, -123.88, -4.31, 0.11143, 25, 103.96, 12.31, 0.88857, 1, 25, 177.76, 11.51, 1, 1, 25, 213.49, -25.43, 1, 1, 25, 236.01, -26.55, 1, 1, 25, 192.82, -20.7, 1, 1, 25, 200.56, 10.44, 1, 1, 25, 244.78, 3.24, 1, 1, 25, 222.96, 7.72, 1], "hull": 71, "edges": [0, 140, 16, 18, 18, 20, 20, 22, 56, 58, 58, 60, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 118, 120, 110, 112, 108, 110, 112, 114, 104, 106, 106, 108, 90, 92, 86, 88, 88, 90, 68, 70, 60, 62, 62, 64, 96, 98, 98, 100, 92, 94, 94, 96, 64, 66, 66, 68, 50, 52, 100, 102, 102, 104, 138, 140, 0, 2, 2, 4, 4, 6, 40, 42, 38, 40, 32, 34, 34, 36, 30, 32, 22, 24, 24, 26, 12, 14, 14, 16, 122, 124, 132, 134, 46, 48, 48, 50, 124, 126, 126, 128, 128, 130, 130, 132, 42, 44, 44, 46, 52, 54, 54, 56, 134, 136, 136, 138, 120, 122, 114, 116, 116, 118, 158, 160, 160, 162, 176, 178, 194, 196, 200, 202, 6, 8, 10, 12, 8, 10, 36, 38, 26, 28, 28, 30], "width": 358, "height": 469}}, "arm_Lb": {"arm_Lb": {"type": "mesh", "uvs": [0.32475, 0.00837, 0.48817, 0.00912, 0.66499, 0.04606, 0.80628, 0.09383, 0.93079, 0.16182, 0.99086, 0.2399, 0.97716, 0.30848, 0.89773, 0.50739, 0.91983, 0.6418, 0.81651, 0.78354, 0.66985, 0.8973, 0.48534, 0.96972, 0.31001, 1, 0.17973, 0.97055, 0.05832, 0.89175, 0.01234, 0.80118, 0.01207, 0.64963, 0.09488, 0.5042, 0.11899, 0.37096, 0.18841, 0.23963, 0.22943, 0.05661, 0.20126, 0.50701, 0.3036, 0.2684, 0.24656, 0.17659, 0.46772, 0.36804, 0.37192, 0.58966, 0.5752, 0.61136, 0.66327, 0.38757, 0.84446, 0.36674, 0.76337, 0.57381, 0.29611, 0.87176, 0.54004, 0.77272, 0.19073, 0.70708], "triangles": [19, 23, 22, 22, 3, 24, 27, 24, 3, 28, 29, 27, 19, 20, 23, 1, 22, 23, 0, 1, 23, 5, 28, 4, 4, 27, 3, 6, 28, 5, 2, 22, 1, 3, 22, 2, 23, 20, 0, 4, 28, 27, 7, 28, 6, 24, 21, 22, 27, 26, 24, 25, 21, 24, 26, 25, 24, 18, 19, 22, 10, 11, 31, 21, 16, 17, 32, 21, 25, 31, 25, 26, 32, 25, 31, 26, 29, 9, 31, 26, 9, 21, 18, 22, 17, 18, 21, 28, 7, 29, 26, 27, 29, 29, 7, 8, 9, 29, 8, 10, 31, 9, 32, 16, 21, 15, 16, 32, 30, 32, 31, 11, 30, 31, 14, 15, 30, 13, 14, 30, 12, 13, 30, 11, 12, 30, 15, 32, 30], "vertices": [1, 54, 20.4, 27.18, 1, 1, 54, 23.08, 14.38, 1, 1, 54, 20.95, -0.57, 1, 1, 54, 16.69, -13.07, 1, 1, 54, 9.32, -24.88, 1, 1, 54, -0.56, -31.95, 1, 1, 54, -10.38, -32.98, 1, 1, 48, 55.55, -33.68, 1, 1, 48, 37.15, -39.51, 1, 2, 25, 234.74, 38.36, 0.02338, 48, 15.58, -35.77, 0.97662, 2, 25, 250.86, 26.43, 0.29226, 48, -2.82, -27.78, 0.70774, 2, 25, 261.03, 11.54, 0.70268, 48, -16.09, -15.57, 0.29732, 1, 25, 265.19, -2.54, 1, 1, 25, 260.85, -12.91, 1, 1, 25, 249.46, -22.48, 1, 2, 25, 236.46, -26, 0.93026, 48, -0.63, 26.54, 0.06974, 2, 25, 214.79, -25.75, 0.66772, 48, 20.54, 31.2, 0.33228, 2, 25, 194.08, -18.87, 0.33186, 48, 42.27, 29.17, 0.66814, 2, 25, 175.05, -16.71, 0.03084, 48, 61.3, 31.35, 0.96916, 2, 48, 80.83, 29.94, 0.52286, 54, -14.24, 30.77, 0.47714, 1, 54, 12.03, 33.15, 1, 3, 25, 194.59, -10.37, 0.24072, 48, 43.7, 20.77, 0.63348, 54, -51.37, 21.59, 0.1258, 1, 54, -16.29, 20.89, 1, 1, 54, -4.44, 28.15, 1, 1, 54, -27.41, 5.02, 1, 3, 25, 206.57, 3.14, 0.18646, 48, 35.07, 4.9, 0.42017, 54, -60, 5.73, 0.39338, 3, 25, 209.88, 19.36, 0.1046, 48, 35.51, -11.65, 0.55941, 54, -59.56, -10.82, 0.33599, 1, 54, -26.79, -10.86, 1, 1, 54, -20.78, -24.39, 1, 2, 48, 43.97, -25.21, 0.90085, 54, -51.1, -24.38, 0.09915, 2, 25, 246.84, -3.42, 0.76625, 48, -5.64, 2.21, 0.23375, 2, 25, 232.92, 16.26, 0.42065, 48, 12.37, -13.83, 0.57935, 2, 25, 223.18, -11.56, 0.66138, 48, 15.57, 15.48, 0.33862], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 32, 34, 34, 36, 28, 30, 30, 32, 20, 22, 22, 24, 36, 38, 38, 40, 8, 10, 42, 44, 48, 50, 52, 54, 56, 58, 10, 12, 12, 14], "width": 80, "height": 143}}, "body": {"body": {"type": "mesh", "uvs": [0.49116, 0.00023, 0.5168, 0.00801, 0.53412, 0.00321, 0.55716, 0.00419, 0.58978, 0.01499, 0.62582, 0.03661, 0.65615, 0.07077, 0.7, 0.10134, 0.7276, 0.12462, 0.73914, 0.16055, 0.74492, 0.19564, 0.73634, 0.23022, 0.75449, 0.25721, 0.68842, 0.28773, 0.71475, 0.29276, 0.78765, 0.29961, 0.86054, 0.30647, 0.84591, 0.31868, 0.8339, 0.34389, 0.81095, 0.38475, 0.78413, 0.44026, 0.79502, 0.48364, 0.7991, 0.53005, 0.79402, 0.57893, 0.7669, 0.6145, 0.76066, 0.63895, 0.7813, 0.66405, 0.81719, 0.69097, 0.87752, 0.71518, 0.93736, 0.74624, 0.97902, 0.77961, 0.99925, 0.80975, 0.99249, 0.83367, 0.96839, 0.87344, 0.92084, 0.92305, 0.85934, 0.96092, 0.77252, 0.98241, 0.65695, 0.99577, 0.58116, 1, 0.5125, 0.99693, 0.40825, 0.9873, 0.31991, 0.9741, 0.23788, 0.94652, 0.1877, 0.91519, 0.12179, 0.90401, 0.06912, 0.8987, 0.0345, 0.90148, 0, 0.90946, 0.03599, 0.87197, 0.06707, 0.84644, 0.10536, 0.82029, 0.14497, 0.80383, 0.19268, 0.77906, 0.23947, 0.75645, 0.27859, 0.73954, 0.31924, 0.716, 0.34129, 0.68687, 0.33842, 0.66136, 0.3254, 0.63973, 0.23834, 0.62526, 0.19594, 0.59879, 0.17554, 0.56883, 0.16538, 0.53622, 0.17038, 0.49406, 0.20241, 0.45728, 0.20276, 0.43858, 0.24254, 0.40998, 0.22738, 0.37727, 0.21477, 0.34586, 0.20994, 0.32744, 0.21159, 0.31498, 0.2454, 0.31013, 0.24492, 0.285, 0.26442, 0.25463, 0.22719, 0.24075, 0.22088, 0.21962, 0.23614, 0.18831, 0.26797, 0.14481, 0.28474, 0.1106, 0.31452, 0.07364, 0.34705, 0.04256, 0.39701, 0.01505, 0.45394, 0.00056, 0.44696, 0.21943, 0.60394, 0.21885, 0.44942, 0.27391, 0.60485, 0.26415, 0.60403, 0.24162, 0.44866, 0.24645, 0.64106, 0.2823, 0.30042, 0.30251, 0.3651, 0.29364, 0.35623, 0.23235, 0.37622, 0.22731, 0.42341, 0.19017, 0.44143, 0.17156, 0.46211, 0.13971, 0.47257, 0.11541, 0.48137, 0.10382, 0.4978, 0.0923, 0.5167, 0.08758, 0.54009, 0.09012, 0.45151, 0.1628, 0.40464, 0.21185, 0.59004, 0.11805, 0.62082, 0.1447, 0.63079, 0.17022, 0.62422, 0.15837, 0.63321, 0.15753, 0.6512, 0.14061, 0.52209, 0.09589, 0.31287, 0.24949, 0.29516, 0.27631, 0.35976, 0.26416, 0.41097, 0.28472, 0.40379, 0.24928, 0.27018, 0.22821, 0.31829, 0.16444, 0.33588, 0.1375, 0.35578, 0.09705, 0.38602, 0.06175, 0.52297, 0.03608, 0.54594, 0.06114, 0.49813, 0.0591, 0.46146, 0.067, 0.43042, 0.08837, 0.40638, 0.12045, 0.29265, 0.19663, 0.32327, 0.21235, 0.36553, 0.19101, 0.39554, 0.15769, 0.42354, 0.03675, 0.47254, 0.02664, 0.56332, 0.02608, 0.57435, 0.06202, 0.61206, 0.0919, 0.64548, 0.12006, 0.60506, 0.05342, 0.63079, 0.08524, 0.6729, 0.11069, 0.69311, 0.13989, 0.69924, 0.17546, 0.69985, 0.2084, 0.69495, 0.24959, 0.64657, 0.24697, 0.64838, 0.20811, 0.65678, 0.17114, 0.45183, 0.19446, 0.59797, 0.18997, 0.27254, 0.33065, 0.27553, 0.36516, 0.41297, 0.41407, 0.57051, 0.4142, 0.73232, 0.43539, 0.48103, 0.43495, 0.40009, 0.62676, 0.45654, 0.575, 0.51652, 0.62191, 0.59325, 0.63808, 0.69601, 0.62574, 0.62511, 0.31209, 0.69073, 0.31511, 0.77951, 0.31928, 0.72727, 0.35427, 0.77345, 0.35642, 0.75346, 0.39185, 0.77791, 0.48751, 0.75071, 0.58811, 0.77776, 0.54233, 0.64824, 0.55542, 0.23215, 0.55821, 0.23419, 0.45431, 0.38207, 0.47947, 0.32031, 0.45156, 0.53145, 0.4765, 0.6012, 0.44794, 0.69244, 0.45424, 0.73832, 0.4907, 0.71526, 0.59817, 0.64296, 0.61687, 0.55275, 0.61043, 0.50268, 0.57362, 0.49983, 0.5174, 0.3996, 0.52691, 0.39338, 0.57777, 0.35572, 0.61131, 0.29425, 0.62439, 0.45943, 0.50949, 0.20709, 0.49941, 0.26797, 0.49902, 0.31327, 0.53133, 0.31248, 0.57476, 0.27403, 0.59743, 0.35254, 0.32888, 0.42197, 0.31931, 0.34442, 0.37301, 0.41448, 0.37054, 0.62425, 0.36781, 0.65449, 0.41316, 0.55819, 0.27787, 0.55748, 0.24731, 0.55676, 0.21903, 0.49704, 0.24971, 0.49358, 0.21926, 0.49753, 0.28153, 0.49015, 0.31827, 0.46219, 0.30218, 0.59232, 0.2908, 0.49995, 0.37724, 0.35465, 0.41367, 0.50943, 0.33566, 0.58612, 0.35311, 0.53011, 0.31997, 0.43917, 0.35362, 0.62044, 0.28699, 0.43785, 0.29428, 0.29533, 0.42878, 0.62237, 0.49529, 0.69151, 0.49758, 0.72531, 0.52934, 0.75195, 0.54338, 0.56872, 0.52496, 0.56298, 0.57202, 0.59538, 0.59592, 0.66679, 0.59709, 0.71092, 0.57245, 0.28312, 0.39668, 0.39688, 0.65387, 0.47468, 0.66261, 0.46125, 0.62216, 0.41652, 0.6925, 0.39831, 0.72064, 0.34269, 0.7476, 0.48269, 0.69582, 0.49227, 0.72924, 0.51003, 0.77293, 0.68596, 0.6419, 0.67593, 0.68006, 0.69168, 0.70263, 0.77599, 0.72519, 0.59104, 0.68974, 0.60254, 0.71904, 0.58624, 0.65575, 0.22625, 0.78672, 0.16936, 0.80473, 0.12324, 0.87156, 0.15742, 0.8438, 0.20747, 0.81539, 0.84073, 0.73899, 0.96994, 0.79265, 0.98447, 0.81469, 0.91412, 0.7564, 0.94971, 0.77393, 0.74039, 0.74509, 0.68117, 0.78802, 0.62479, 0.84365, 0.59499, 0.89781, 0.5966, 0.94754, 0.6264, 0.98152, 0.62853, 0.76546, 0.28867, 0.76507, 0.11963, 0.8231, 0.21551, 0.88247, 0.24097, 0.84744, 0.26668, 0.8127, 0.35121, 0.77779, 0.4316, 0.80395, 0.40745, 0.75974, 0.38559, 0.84387, 0.42734, 0.8961, 0.43608, 0.94952, 0.81053, 0.78832, 0.90123, 0.82975, 0.78968, 0.86736, 0.73233, 0.92599, 0.32845, 0.9063, 0.71999, 0.64498, 0.7417, 0.61366, 0.38323, 0.66369, 0.5031, 0.85193, 0.52986, 0.90195, 0.53062, 0.94869, 0.18348, 0.53359, 0.19129, 0.56868, 0.22427, 0.59524], "triangles": [67, 150, 226, 67, 68, 150, 68, 149, 150, 150, 149, 193, 149, 68, 69, 149, 69, 71, 71, 69, 70, 149, 90, 193, 149, 71, 90, 193, 90, 91, 71, 72, 90, 72, 112, 90, 90, 112, 91, 112, 113, 91, 72, 73, 112, 112, 111, 113, 112, 73, 111, 73, 116, 111, 73, 74, 116, 165, 164, 19, 165, 163, 164, 19, 164, 18, 164, 162, 18, 164, 163, 162, 163, 161, 162, 18, 162, 17, 161, 14, 162, 162, 15, 17, 162, 14, 15, 17, 15, 16, 13, 143, 12, 111, 92, 113, 115, 92, 93, 115, 113, 92, 143, 11, 12, 88, 203, 202, 202, 203, 201, 144, 145, 143, 143, 142, 11, 143, 145, 142, 111, 128, 92, 111, 116, 128, 115, 83, 88, 115, 103, 83, 115, 93, 103, 200, 201, 87, 145, 144, 84, 88, 83, 203, 201, 84, 87, 144, 87, 84, 74, 75, 116, 92, 128, 93, 11, 142, 10, 75, 76, 116, 116, 127, 128, 116, 76, 127, 128, 129, 93, 93, 129, 103, 103, 94, 83, 83, 147, 203, 83, 94, 147, 201, 203, 148, 148, 203, 147, 96, 148, 102, 201, 148, 84, 104, 96, 97, 96, 104, 148, 84, 148, 145, 128, 127, 129, 103, 129, 94, 145, 141, 142, 142, 141, 10, 148, 106, 145, 145, 146, 141, 145, 106, 146, 127, 117, 129, 117, 127, 77, 141, 9, 10, 104, 97, 110, 97, 98, 110, 110, 101, 104, 94, 95, 147, 95, 102, 147, 148, 147, 102, 129, 130, 94, 129, 117, 130, 94, 130, 95, 148, 107, 106, 148, 105, 107, 148, 104, 105, 127, 76, 77, 9, 141, 140, 95, 130, 102, 106, 108, 146, 108, 109, 146, 141, 146, 140, 146, 109, 140, 106, 107, 108, 117, 118, 130, 117, 77, 118, 102, 130, 96, 140, 8, 9, 107, 105, 108, 130, 126, 96, 130, 118, 126, 108, 105, 109, 77, 78, 118, 105, 136, 109, 105, 104, 136, 140, 109, 139, 109, 136, 139, 8, 139, 7, 8, 140, 139, 98, 99, 110, 96, 126, 97, 118, 119, 126, 118, 78, 119, 126, 125, 97, 126, 119, 125, 104, 135, 136, 135, 138, 136, 136, 138, 139, 104, 101, 135, 97, 125, 98, 7, 139, 6, 78, 79, 119, 99, 98, 124, 139, 138, 6, 119, 120, 125, 119, 79, 120, 99, 100, 110, 110, 100, 101, 98, 125, 124, 99, 123, 100, 99, 124, 123, 101, 134, 135, 134, 137, 135, 135, 137, 138, 101, 122, 134, 101, 100, 122, 125, 120, 124, 100, 123, 122, 138, 137, 6, 79, 80, 120, 137, 5, 6, 120, 131, 124, 124, 132, 123, 124, 131, 132, 137, 134, 133, 120, 80, 131, 123, 121, 122, 134, 122, 133, 122, 121, 133, 123, 132, 121, 133, 4, 137, 137, 4, 5, 80, 81, 131, 131, 82, 132, 131, 81, 82, 133, 2, 3, 2, 133, 1, 132, 1, 121, 133, 121, 1, 132, 0, 1, 132, 82, 0, 133, 3, 4, 85, 202, 204, 204, 202, 199, 202, 200, 199, 199, 200, 86, 115, 88, 85, 85, 88, 202, 200, 87, 86, 86, 87, 144, 202, 201, 200, 276, 26, 237, 26, 276, 25, 56, 278, 230, 278, 227, 230, 230, 227, 228, 229, 227, 155, 227, 229, 228, 233, 228, 242, 56, 57, 278, 237, 236, 276, 237, 242, 236, 236, 242, 158, 227, 278, 58, 228, 157, 242, 228, 229, 157, 278, 57, 58, 242, 157, 158, 227, 58, 155, 236, 159, 276, 25, 276, 24, 276, 159, 277, 236, 158, 159, 58, 185, 155, 59, 186, 58, 58, 186, 185, 24, 276, 277, 158, 179, 159, 157, 180, 158, 158, 180, 179, 156, 229, 155, 277, 159, 167, 159, 179, 178, 60, 284, 59, 59, 192, 186, 59, 284, 192, 186, 192, 185, 155, 185, 184, 155, 184, 156, 229, 156, 157, 156, 181, 157, 157, 181, 180, 180, 223, 179, 179, 224, 178, 179, 223, 224, 277, 167, 24, 24, 167, 23, 167, 159, 178, 192, 191, 185, 185, 191, 184, 180, 222, 223, 180, 181, 222, 61, 283, 60, 60, 283, 284, 224, 225, 178, 167, 178, 220, 191, 192, 170, 223, 169, 224, 224, 169, 225, 223, 222, 169, 192, 284, 170, 284, 283, 170, 23, 167, 168, 167, 220, 168, 220, 178, 225, 23, 168, 22, 191, 190, 184, 184, 183, 156, 184, 190, 183, 156, 182, 181, 156, 187, 182, 156, 183, 187, 191, 170, 190, 222, 181, 221, 225, 219, 220, 225, 169, 219, 222, 221, 169, 221, 181, 182, 283, 61, 282, 61, 62, 282, 283, 282, 170, 282, 188, 170, 170, 189, 190, 170, 188, 189, 221, 217, 169, 169, 218, 219, 169, 217, 218, 166, 168, 177, 166, 22, 168, 219, 177, 220, 220, 177, 168, 62, 63, 282, 282, 63, 188, 183, 190, 172, 166, 21, 22, 219, 218, 177, 190, 189, 172, 183, 172, 187, 182, 174, 221, 221, 174, 217, 182, 187, 174, 187, 154, 174, 187, 172, 154, 189, 188, 171, 188, 63, 171, 189, 173, 172, 189, 171, 173, 177, 218, 176, 174, 175, 217, 218, 217, 176, 217, 175, 176, 63, 64, 171, 166, 177, 153, 177, 176, 153, 166, 20, 21, 166, 153, 20, 172, 151, 154, 172, 173, 151, 174, 152, 175, 174, 154, 152, 64, 65, 171, 171, 216, 173, 171, 66, 216, 171, 65, 66, 175, 198, 176, 176, 198, 153, 173, 209, 151, 173, 216, 209, 175, 152, 198, 153, 165, 20, 20, 165, 19, 153, 198, 165, 154, 151, 208, 154, 208, 152, 208, 151, 196, 66, 226, 216, 216, 226, 209, 152, 197, 198, 152, 208, 197, 196, 213, 208, 151, 209, 196, 226, 195, 209, 209, 195, 196, 198, 197, 163, 197, 161, 163, 198, 163, 165, 161, 197, 160, 161, 89, 13, 89, 161, 160, 66, 67, 226, 226, 150, 195, 208, 211, 197, 208, 210, 211, 208, 213, 210, 195, 193, 196, 195, 150, 193, 194, 196, 193, 194, 213, 196, 197, 211, 160, 213, 205, 210, 205, 194, 206, 205, 213, 194, 210, 212, 211, 160, 212, 207, 160, 211, 212, 210, 205, 212, 193, 91, 194, 205, 204, 212, 212, 199, 207, 212, 204, 199, 91, 114, 194, 194, 215, 206, 194, 114, 215, 205, 206, 204, 161, 13, 14, 207, 214, 160, 160, 214, 89, 215, 85, 206, 206, 85, 204, 215, 114, 85, 91, 113, 114, 214, 207, 86, 13, 89, 143, 143, 89, 144, 207, 199, 86, 214, 86, 89, 113, 115, 114, 114, 115, 85, 89, 86, 144, 267, 234, 235, 235, 241, 259, 235, 234, 241, 259, 238, 253, 259, 241, 238, 234, 231, 233, 233, 231, 230, 231, 234, 267, 234, 240, 241, 234, 233, 240, 231, 56, 230, 241, 237, 238, 241, 240, 237, 26, 238, 237, 230, 228, 233, 240, 233, 242, 240, 242, 237, 34, 35, 273, 273, 272, 34, 34, 272, 33, 33, 272, 32, 273, 271, 272, 273, 254, 271, 272, 250, 32, 32, 250, 31, 272, 249, 250, 272, 252, 249, 272, 251, 252, 272, 271, 251, 250, 249, 31, 249, 30, 31, 249, 252, 30, 254, 253, 271, 253, 239, 271, 271, 248, 251, 271, 239, 248, 251, 29, 252, 30, 252, 29, 248, 28, 251, 251, 28, 29, 253, 238, 239, 239, 27, 248, 248, 27, 28, 239, 238, 26, 239, 26, 27, 41, 42, 275, 275, 269, 270, 43, 262, 42, 42, 262, 275, 262, 43, 245, 46, 47, 48, 262, 263, 275, 275, 263, 268, 275, 268, 269, 268, 263, 264, 43, 44, 245, 44, 45, 245, 46, 48, 45, 45, 48, 245, 245, 48, 49, 245, 246, 262, 262, 246, 263, 49, 261, 245, 245, 261, 246, 246, 247, 263, 263, 247, 264, 49, 50, 261, 264, 265, 268, 268, 265, 266, 246, 244, 247, 246, 261, 244, 261, 51, 244, 261, 50, 51, 247, 243, 264, 247, 244, 243, 264, 260, 265, 264, 243, 260, 244, 52, 243, 244, 51, 52, 265, 267, 266, 243, 53, 260, 243, 52, 53, 260, 232, 265, 265, 232, 267, 53, 54, 260, 260, 54, 232, 232, 231, 267, 54, 55, 232, 232, 55, 231, 55, 56, 231, 270, 280, 281, 270, 269, 280, 281, 280, 257, 280, 256, 257, 257, 256, 274, 256, 255, 274, 35, 274, 273, 274, 255, 273, 269, 279, 280, 280, 279, 256, 256, 279, 255, 269, 268, 279, 255, 254, 273, 268, 266, 279, 279, 235, 255, 279, 266, 235, 255, 259, 254, 255, 235, 259, 266, 267, 235, 254, 259, 253, 38, 258, 37, 39, 281, 38, 38, 257, 258, 38, 281, 257, 40, 270, 39, 39, 270, 281, 36, 37, 274, 40, 41, 270, 37, 258, 274, 36, 274, 35, 258, 257, 274, 41, 275, 270], "vertices": [2, 6, 190.1, -4.57, 0.99168, 43, -153.98, 61.43, 0.00832, 3, 6, 182.45, -16.19, 0.99476, 17, 257.69, 140.94, 0, 43, -161.63, 49.8, 0.00524, 3, 6, 185.22, -25, 0.99336, 17, 261.05, 132.34, 0, 43, -158.87, 40.99, 0.00664, 3, 6, 183.09, -36.03, 0.99478, 17, 259.67, 121.2, 0, 43, -161, 29.97, 0.00522, 3, 6, 172.64, -50.72, 0.99431, 17, 250.25, 105.82, 0, 43, -171.45, 15.27, 0.00569, 3, 6, 153.46, -66.02, 0.99463, 17, 232.14, 89.26, 0, 43, -190.63, -0.03, 0.00537, 4, 5, 171.06, -61.47, 0.00494, 6, 124.71, -77.37, 0.99043, 17, 204.23, 75.99, 0.00013, 43, -219.37, -11.37, 0.0045, 4, 5, 146.8, -82.82, 0.03092, 6, 98.01, -95.57, 0.96167, 17, 178.83, 56.02, 0.00243, 43, -246.07, -29.58, 0.00498, 4, 5, 128.3, -96.26, 0.06869, 6, 78.01, -106.65, 0.91604, 17, 159.63, 43.61, 0.0068, 43, -266.08, -40.65, 0.00847, 4, 5, 99.75, -101.92, 0.16414, 6, 48.98, -108.75, 0.80785, 17, 130.8, 39.54, 0.02066, 43, -295.11, -42.76, 0.00736, 5, 4, 366.7, -108.58, 0.00328, 5, 71.86, -104.77, 0.26631, 6, 20.95, -108.16, 0.67521, 17, 102.8, 38.23, 0.046, 43, -323.14, -42.16, 0.00919, 5, 4, 339.46, -102.94, 0.04582, 5, 44.36, -100.65, 0.26985, 6, -5.84, -100.69, 0.5632, 17, 75.57, 43.87, 0.11613, 43, -349.92, -34.7, 0.005, 5, 4, 317.57, -110.6, 0.10053, 5, 22.92, -109.5, 0.2509, 6, -28.2, -106.85, 0.46117, 17, 53.67, 36.21, 0.18314, 43, -372.29, -40.85, 0.00425, 2, 4, 295.06, -77.23, 0.60554, 17, 31.16, 69.58, 0.39446, 2, 4, 290.38, -89.8, 0.44524, 17, 26.48, 57.01, 0.55476, 2, 4, 283.04, -124.88, 0.17944, 17, 19.14, 21.93, 0.82056, 1, 17, 11.8, -13.15, 1, 2, 17, 2.49, -5.54, 0.99055, 44, 98.94, 199.6, 0.00945, 3, 4, 246.69, -145.44, 0.07297, 17, -17.21, 1.37, 0.91398, 44, 79.24, 206.51, 0.01305, 3, 4, 214.84, -132.57, 0.33615, 17, -49.05, 14.24, 0.64822, 44, 47.4, 219.38, 0.01563, 5, 46, 259.93, 7.47, 0.00049, 4, 171.47, -117.18, 0.68931, 17, -92.42, 29.63, 0.19816, 19, 59, -79.4, 0.09866, 44, 4.03, 234.77, 0.01338, 5, 46, 225.2, 4.22, 0.00484, 4, 136.76, -120.62, 0.85447, 17, -127.14, 26.19, 0.02689, 19, 24.29, -82.84, 0.09847, 44, -30.68, 231.33, 0.01533, 4, 46, 188.24, 4.42, 0.01791, 4, 99.8, -120.62, 0.86774, 19, -12.67, -82.84, 0.09841, 44, -67.64, 231.33, 0.01594, 4, 46, 149.6, 9.19, 0.04887, 4, 61.14, -116.07, 0.84062, 19, -51.34, -78.29, 0.09883, 44, -106.31, 235.87, 0.01168, 5, 46, 122.15, 24.02, 0.1083, 3, 111.2, -102.38, 0.08167, 4, 33.6, -101.4, 0.70405, 19, -78.87, -63.62, 0.09934, 44, -133.84, 250.55, 0.00664, 3, 46, 102.92, 28.19, 0.22955, 3, 92.09, -97.72, 0.20972, 4, 14.35, -97.33, 0.56073, 3, 46, 82.41, 19.36, 0.4218, 3, 71.36, -106.04, 0.27682, 4, -6.11, -106.27, 0.30137, 4, 2, 182.63, -120.37, 0.00082, 46, 60.02, 3.21, 0.68692, 3, 48.57, -121.62, 0.20058, 4, -28.41, -122.54, 0.11168, 3, 46, 39.08, -24.91, 0.9286, 3, 26.93, -149.22, 0.05095, 4, -49.2, -150.79, 0.02045, 4, 2, 135.31, -176.07, 0.00048, 46, 12.7, -52.49, 0.99849, 3, -0.12, -176.12, 0.00063, 4, -75.42, -178.51, 0.0004, 4, 2, 107.63, -194.71, 0.0195, 46, -14.98, -71.13, 0.98049, 3, -28.26, -194.07, 0, 4, -102.99, -197.3, 0, 4, 2, 83.13, -203.11, 0.04703, 46, -39.48, -79.52, 0.95297, 3, -52.96, -201.85, 0, 4, -127.45, -205.83, 0, 4, 2, 64.34, -198.71, 0.07691, 46, -58.26, -75.12, 0.92309, 3, -71.63, -196.98, 0, 4, -146.26, -201.54, 0, 5, 1, 182.27, 71.11, 0.12613, 2, 33.47, -185.14, 0.14323, 46, -89.14, -61.56, 0.73065, 3, -102.16, -182.65, 0, 4, -177.21, -188.15, 0, 4, 1, 159.16, 31.67, 0.32806, 2, -4.53, -159.74, 0.20905, 46, -127.14, -36.16, 0.46289, 3, -139.51, -156.31, 0, 4, 1, 129.27, 1.56, 0.50099, 2, -32.82, -128.12, 0.22073, 46, -155.42, -4.54, 0.27829, 3, -167, -124, 0, 3, 1, 87.07, -15.52, 0.64114, 2, -47.37, -84.99, 0.21032, 46, -169.98, 38.59, 0.14854, 3, 1, 30.91, -26.14, 0.9428, 2, -54.65, -28.29, 0.03991, 46, -177.26, 95.29, 0.01728, 1, 1, -5.93, -29.51, 1, 3, 1, -39.29, -27.06, 0.95458, 2, -51.42, 41.84, 0.0265, 47, -167.83, -95.8, 0.01892, 3, 1, -89.96, -19.41, 0.70146, 2, -40.78, 91.96, 0.13931, 47, -157.2, -45.67, 0.15924, 3, 1, -132.89, -8.92, 0.54975, 2, -27.77, 134.2, 0.13914, 47, -144.18, -3.44, 0.3111, 4, 1, -172.76, 13.01, 0.35246, 2, -3.52, 172.7, 0.10905, 47, -119.93, 35.07, 0.53524, 36, 68.78, -94, 0.00324, 4, 1, -197.15, 37.92, 0.1553, 2, 22.78, 195.57, 0.05228, 47, -93.63, 57.93, 0.67097, 36, 64.37, -59.43, 0.12145, 4, 1, -229.18, 46.8, 0.03875, 2, 33.55, 227.02, 0.01126, 47, -82.86, 89.39, 0.69419, 36, 44.19, -33.01, 0.2558, 3, 2, 39.28, 252.33, 0.00142, 47, -77.13, 114.69, 0.63824, 36, 26.32, -14.2, 0.36034, 3, 2, 38.07, 269.25, 6e-05, 47, -78.34, 131.62, 0.56905, 36, 11.57, -5.81, 0.43089, 4, 1, -288.37, 42.47, 0.00149, 2, 32.73, 286.36, 0, 47, -83.68, 148.73, 0.50716, 36, -5.63, -0.75, 0.49135, 2, 47, -54.96, 129.51, 0.63345, 36, 26.31, 12.47, 0.36655, 3, 2, 80.81, 250.86, 2e-05, 47, -35.6, 113.22, 0.73862, 36, 50.6, 19.53, 0.26136, 2, 47, -15.95, 93.42, 0.85416, 36, 77.98, 24.88, 0.14584, 2, 47, -4.02, 73.43, 0.94688, 36, 101.23, 23.7, 0.05312, 1, 47, 14.26, 49.12, 1, 3, 47, 30.87, 25.36, 0.97408, 3, 20.3, 162.54, 0.01831, 4, -65.34, 160.62, 0.0076, 4, 2, 159.57, 143.21, 0.00244, 47, 43.16, 5.58, 0.86362, 3, 32.09, 142.46, 0.09317, 4, -52.94, 140.91, 0.04077, 4, 2, 177.08, 122.39, 0.01619, 47, 60.67, -15.25, 0.61081, 3, 49.08, 121.2, 0.22715, 4, -35.31, 120.18, 0.14584, 4, 2, 199.57, 110.32, 0.0076, 47, 83.16, -27.32, 0.35605, 3, 71.26, 108.57, 0.2711, 4, -12.76, 108.23, 0.36525, 5, 2, 219.89, 110.51, 0.00056, 47, 103.48, -27.12, 0.17986, 3, 91.58, 108.26, 0.17226, 4, 7.57, 108.54, 0.54733, 22, -111.06, 8.62, 0.1, 4, 47, 121.02, -21.82, 0.08891, 3, 109.25, 113.12, 0.06469, 4, 25.08, 113.94, 0.6464, 22, -93.55, 14.02, 0.2, 4, 47, 135.01, 19.73, 0.03316, 4, 38.83, 155.57, 0.74811, 23, -68.73, 24.94, 0.19532, 44, -128.61, 507.52, 0.02342, 4, 47, 157.23, 39.06, 0.0215, 4, 60.95, 175.02, 0.75677, 23, -46.62, 44.39, 0.19457, 44, -106.5, 526.97, 0.02716, 4, 47, 181.6, 47.55, 0.01543, 4, 85.27, 183.65, 0.75963, 23, -22.3, 53.02, 0.19376, 44, -82.18, 535.6, 0.03118, 4, 47, 207.77, 50.94, 0.00996, 4, 111.42, 187.19, 0.7647, 23, 3.86, 56.56, 0.19367, 44, -56.02, 539.14, 0.03167, 5, 47, 241.09, 46.53, 0.00533, 4, 144.76, 182.96, 0.76321, 18, -127.2, 32.96, 0.00516, 23, 37.2, 52.33, 0.19711, 44, -22.69, 534.91, 0.02919, 6, 47, 269.35, 29.26, 0.00201, 4, 173.12, 165.85, 0.71231, 18, -98.84, 15.84, 0.10902, 23, 65.56, 35.22, 0.0835, 22, 54.49, 65.93, 0.06438, 44, 5.67, 517.8, 0.02877, 5, 47, 284.19, 28.22, 0.00103, 4, 187.96, 164.89, 0.66062, 18, -84, 14.88, 0.17498, 22, 69.33, 64.96, 0.1362, 44, 20.51, 516.83, 0.02717, 5, 47, 305.74, 7.57, 0.00013, 4, 209.63, 144.36, 0.552, 18, -62.33, -5.65, 0.31895, 22, 91, 44.44, 0.10928, 44, 42.18, 496.31, 0.01964, 3, 4, 235.98, 150.33, 0.31793, 18, -35.98, 0.32, 0.66527, 44, 68.54, 502.28, 0.0168, 3, 4, 261.25, 155.1, 0.06389, 18, -10.71, 5.1, 0.92448, 44, 93.8, 507.05, 0.01162, 2, 18, 4.04, 6.66, 0.99346, 44, 108.55, 508.61, 0.00654, 1, 18, 13.89, 5.32, 1, 2, 4, 288.81, 138.72, 0.05505, 18, 16.85, -11.29, 0.94495, 5, 4, 308.78, 137.88, 0.03776, 5, 0.43, 138.11, 0.08851, 6, -20.12, 141.66, 0.04598, 18, 36.82, -12.13, 0.8251, 43, -364.21, 207.65, 0.00265, 5, 4, 332.38, 127.13, 0.0206, 5, 24.59, 128.68, 0.18503, 6, 2.7, 129.33, 0.29021, 18, 60.42, -22.88, 0.50039, 43, -341.39, 195.32, 0.00376, 5, 4, 344.37, 144.6, 0.00154, 5, 35.59, 146.79, 0.156, 6, 15.84, 145.95, 0.39069, 18, 72.41, -5.41, 0.44304, 43, -328.25, 211.94, 0.00873, 4, 5, 52.39, 149.88, 0.14392, 6, 32.89, 146.95, 0.45345, 18, 89.35, -3.25, 0.393, 43, -311.2, 212.95, 0.00962, 4, 5, 77.29, 142.5, 0.11783, 6, 56.7, 136.58, 0.57097, 18, 113.81, -11.99, 0.30246, 43, -287.39, 202.57, 0.00874, 4, 5, 111.9, 127.09, 0.06615, 6, 89.15, 117.03, 0.79049, 18, 147.51, -29.29, 0.13514, 43, -254.93, 183.03, 0.00822, 4, 5, 139.11, 118.98, 0.03156, 6, 115.16, 105.64, 0.92387, 18, 174.23, -38.88, 0.03551, 43, -228.93, 171.64, 0.00906, 3, 5, 168.51, 104.56, 0.00975, 6, 142.57, 87.72, 0.98156, 43, -201.52, 153.71, 0.00869, 3, 5, 193.25, 88.79, 0.00168, 6, 165.18, 69.03, 0.99053, 43, -178.9, 135.03, 0.00779, 2, 6, 183.95, 42.28, 0.99273, 43, -160.14, 108.27, 0.00727, 2, 6, 192.03, 13.42, 0.99317, 43, -152.06, 79.41, 0.00683, 3, 4, 355.57, 37.04, 0.01403, 5, 52.71, 40.01, 0.22209, 6, 19.72, 37.87, 0.76388, 3, 4, 351.94, -39.17, 0.00971, 5, 53.29, -36.28, 0.25303, 6, 10.93, -37.91, 0.73727, 2, 4, 312.25, 38.17, 0.62713, 5, 9.4, 38.74, 0.37287, 3, 4, 315.96, -37.68, 0.4167, 5, 17.28, -36.79, 0.51944, 6, -24.87, -33.99, 0.06386, 3, 4, 333.86, -38.25, 0.08009, 5, 35.2, -36.36, 0.42686, 6, -7.04, -35.77, 0.49306, 3, 4, 334.08, 37.37, 0.12251, 5, 31.23, 39.15, 0.41721, 6, -1.7, 39.66, 0.46028, 3, 4, 300.6, -54.48, 0.70966, 5, 2.88, -54.4, 0.17084, 17, 36.7, 92.33, 0.1195, 3, 4, 293.43, 111.69, 0.24226, 18, 21.47, -38.32, 0.73994, 44, 125.99, 463.64, 0.0178, 3, 4, 298.79, 79.93, 0.55931, 18, 26.83, -70.08, 0.42381, 44, 131.35, 431.87, 0.01688, 5, 4, 347.67, 81.62, 0.04973, 5, 42.37, 84.09, 0.21385, 6, 14.87, 82.89, 0.53048, 18, 75.71, -68.39, 0.19543, 42, -390.3, 148.88, 0.01051, 5, 4, 351.15, 71.7, 0.04533, 5, 46.39, 74.38, 0.22292, 6, 17.67, 72.76, 0.59826, 18, 79.19, -78.31, 0.11313, 42, -387.5, 138.75, 0.02037, 4, 4, 379.41, 47.22, 0.00074, 5, 75.96, 51.49, 0.1368, 6, 44.2, 46.41, 0.81854, 42, -360.97, 112.41, 0.04392, 3, 5, 90.77, 42.76, 0.06529, 6, 57.82, 35.93, 0.89161, 42, -347.34, 101.93, 0.04309, 3, 5, 116.1, 32.75, 0.01376, 6, 81.74, 22.88, 0.94251, 42, -323.43, 88.88, 0.04372, 3, 5, 135.43, 27.69, 0.00269, 6, 100.3, 15.5, 0.95493, 42, -304.87, 81.49, 0.04238, 3, 5, 144.65, 23.43, 0.00036, 6, 108.93, 10.14, 0.9616, 42, -296.24, 76.13, 0.03804, 3, 5, 153.82, 15.46, 0, 6, 117.05, 1.1, 0.96649, 42, -288.12, 67.09, 0.0335, 2, 6, 119.66, -8.47, 0.97169, 42, -285.51, 57.52, 0.02831, 2, 6, 116.28, -19.51, 0.97683, 42, -288.89, 46.48, 0.02317, 3, 5, 97.74, 37.87, 0.04263, 6, 64.14, 30.22, 0.91308, 42, -341.02, 96.22, 0.04428, 4, 4, 362.69, 57.25, 0.0147, 5, 58.71, 60.59, 0.21645, 6, 28.2, 57.56, 0.7313, 42, -376.97, 123.56, 0.03755, 4, 5, 133.42, -29.4, 0.02045, 6, 91.3, -40.92, 0.9531, 17, 168.42, 110.09, 0.0019, 42, -313.87, 25.08, 0.02455, 4, 5, 112.26, -44.4, 0.07081, 6, 68.46, -53.2, 0.90504, 17, 146.47, 96.29, 0.00815, 42, -336.71, 12.79, 0.01599, 4, 5, 91.98, -49.27, 0.14321, 6, 47.73, -55.55, 0.83692, 17, 125.95, 92.54, 0.0062, 42, -357.43, 10.44, 0.01368, 4, 5, 101.39, -46.06, 0.10223, 6, 57.47, -53.52, 0.87069, 17, 135.52, 95.22, 0.01235, 42, -347.7, 12.47, 0.01473, 4, 5, 102.07, -50.43, 0.1078, 6, 57.6, -57.94, 0.86795, 17, 135.96, 90.82, 0.01289, 42, -347.56, 8.05, 0.01136, 4, 5, 115.53, -59.15, 0.07957, 6, 69.89, -68.25, 0.90204, 17, 148.92, 81.37, 0.00853, 42, -335.27, -2.25, 0.00986, 2, 6, 112.79, -10.27, 0.97304, 42, -292.38, 55.72, 0.02696, 6, 4, 335.2, 103.39, 0.05235, 5, 28.71, 105.14, 0.18103, 6, 3.9, 105.46, 0.36392, 18, 63.24, -46.62, 0.39708, 42, -401.27, 171.45, 0.0037, 44, 167.75, 455.34, 0.00191, 6, 4, 314.37, 113.13, 0.09571, 5, 7.37, 113.71, 0.10629, 6, -16.22, 116.58, 0.15921, 18, 42.41, -36.88, 0.62959, 42, -421.39, 182.58, 0.00154, 44, 146.92, 465.08, 0.00766, 6, 4, 322.33, 81.26, 0.22412, 5, 17.08, 82.33, 0.14777, 6, -10.44, 84.25, 0.28344, 18, 50.37, -68.75, 0.33735, 42, -415.61, 150.24, 0.00328, 44, 154.89, 433.21, 0.00403, 3, 4, 304.67, 57.28, 0.82004, 5, 0.78, 57.42, 0.07767, 18, 32.71, -92.72, 0.10229, 5, 4, 333, 59.26, 0.18746, 5, 28.95, 60.95, 0.27772, 6, -1.29, 61.57, 0.46532, 18, 61.04, -90.75, 0.06488, 43, -345.38, 127.57, 0.00462, 6, 4, 353.21, 123.2, 0.00469, 5, 45.6, 125.91, 0.1722, 6, 23.21, 124, 0.43853, 18, 81.24, -26.81, 0.38268, 42, -381.96, 189.99, 0.00158, 43, -320.88, 189.99, 0.00031, 4, 5, 96.33, 102.61, 0.09385, 6, 70.7, 94.65, 0.80216, 18, 130.61, -52.87, 0.08888, 42, -334.47, 160.64, 0.01512, 3, 5, 117.76, 94.09, 0.05019, 6, 90.92, 83.57, 0.93307, 42, -314.25, 149.56, 0.01674, 3, 5, 149.94, 84.48, 0.01221, 6, 121.67, 70.07, 0.97135, 42, -283.5, 136.06, 0.01644, 3, 5, 178.03, 69.83, 0.00165, 6, 147.75, 52.08, 0.9829, 42, -257.42, 118.08, 0.01545, 3, 6, 159.93, -16.46, 0.989, 17, 235.24, 139.15, 0, 42, -245.23, 49.54, 0.011, 2, 6, 138.81, -25.13, 0.9793, 42, -266.36, 40.87, 0.0207, 2, 6, 143.24, -2.26, 0.97285, 42, -261.93, 63.74, 0.02715, 2, 6, 139.16, 16.19, 0.97159, 42, -266.01, 82.19, 0.02841, 3, 5, 156.89, 48.21, 0.0016, 6, 124.12, 33.23, 0.96792, 42, -281.05, 99.22, 0.03048, 3, 5, 131.37, 59.86, 0.01464, 6, 100.22, 47.91, 0.95079, 42, -304.95, 113.91, 0.03457, 5, 4, 377.69, 110.95, 0.00028, 5, 70.72, 115.03, 0.13962, 6, 46.81, 110.12, 0.59126, 18, 105.73, -39.06, 0.25953, 42, -358.36, 176.11, 0.00931, 5, 4, 364.41, 96.76, 0.00798, 5, 58.25, 100.13, 0.19233, 6, 32.59, 96.86, 0.56518, 18, 92.45, -53.25, 0.22252, 42, -372.57, 162.85, 0.01199, 5, 4, 380.25, 75.34, 0.00245, 5, 75.24, 79.62, 0.15818, 6, 46.95, 74.42, 0.75802, 18, 108.29, -74.67, 0.0542, 42, -358.22, 140.41, 0.02715, 3, 5, 101.76, 65.07, 0.06224, 6, 71.47, 56.73, 0.90414, 42, -333.69, 122.72, 0.03363, 2, 6, 165.26, 31.57, 0.98593, 42, -239.91, 97.57, 0.01407, 2, 6, 170.36, 6.96, 0.98604, 42, -234.81, 72.96, 0.01396, 3, 6, 165.45, -36.89, 0.98612, 17, 242.13, 119.14, 0, 42, -239.72, 29.11, 0.01388, 2, 6, 136.44, -38.75, 0.97762, 42, -268.73, 27.25, 0.02238, 4, 5, 154.22, -40.07, 0.00361, 6, 110.64, -54.06, 0.97299, 17, 188.61, 98.29, 0.00012, 42, -294.53, 11.93, 0.02327, 4, 5, 131.87, -56.35, 0.03801, 6, 86.45, -67.47, 0.94411, 17, 165.39, 83.27, 0.00332, 42, -318.72, -1.48, 0.01456, 3, 5, 184.82, -36.62, 9e-05, 6, 141.42, -54.39, 0.98408, 42, -263.75, 11.6, 0.01583, 4, 5, 159.54, -49.16, 0.00498, 6, 114.79, -63.74, 0.97806, 17, 193.41, 88.92, 0.00016, 42, -290.38, 2.26, 0.0168, 4, 5, 139.34, -69.66, 0.03023, 6, 92.23, -81.6, 0.96074, 17, 172.11, 69.56, 0.00241, 42, -312.94, -15.61, 0.00662, 4, 5, 116.14, -79.52, 0.09681, 6, 67.99, -88.54, 0.88715, 17, 148.4, 61, 0.01034, 42, -337.17, -22.54, 0.0057, 4, 5, 87.87, -82.54, 0.21084, 6, 39.57, -88.07, 0.75733, 17, 120.01, 59.54, 0.02811, 42, -365.6, -22.07, 0.00371, 5, 4, 357.73, -86.16, 0.01273, 5, 61.68, -82.88, 0.25947, 6, 13.53, -85.19, 0.65924, 17, 93.84, 60.65, 0.06533, 42, -391.64, -19.19, 0.00322, 5, 4, 325.17, -82.03, 0.13692, 5, 28.93, -80.55, 0.26124, 6, -18.68, -78.86, 0.47585, 17, 61.27, 64.78, 0.12274, 42, -423.85, -12.86, 0.00325, 5, 4, 328.51, -58.66, 0.15891, 5, 30.98, -57.04, 0.35107, 6, -13.76, -55.77, 0.46792, 17, 64.61, 88.15, 0.01627, 43, -357.85, 10.23, 0.00584, 4, 4, 359.31, -61.19, 0.00976, 5, 61.87, -57.87, 0.25809, 6, 16.79, -60.39, 0.72638, 43, -327.3, 5.61, 0.00577, 3, 5, 91.27, -61.9, 0.17143, 6, 45.47, -68, 0.807, 17, 124.54, 79.96, 0.02156, 2, 5, 72.57, 37.67, 0.1348, 6, 39.14, 33.12, 0.8652, 2, 5, 76.26, -33.34, 0.15947, 6, 34.08, -37.82, 0.84053, 3, 4, 271.82, 126.42, 0.17044, 18, -0.14, -23.59, 0.80987, 44, 104.37, 478.37, 0.01969, 4, 4, 244.35, 126.44, 0.32747, 18, -27.61, -23.57, 0.6118, 22, 125.72, 26.52, 0.03821, 44, 76.9, 478.39, 0.02252, 3, 4, 201.94, 61.83, 0.768, 22, 83.31, -38.09, 0.192, 44, 34.49, 413.78, 0.04, 3, 4, 197.73, -14.62, 0.768, 19, 85.25, 23.16, 0.192, 44, 30.28, 337.33, 0.04, 5, 46, 265.29, 32.37, 0.0002, 4, 176.7, -92.25, 0.67579, 17, -87.2, 54.56, 0.09978, 19, 64.22, -54.47, 0.19394, 44, 9.25, 259.7, 0.0303, 4, 4, 183.59, 29.68, 0.7776, 22, 64.96, -70.24, 0.096, 19, 71.11, 67.47, 0.0864, 44, 16.14, 381.63, 0.04, 5, 47, 129.17, -58.67, 0.07132, 3, 116.48, 76.08, 0.06098, 4, 33.43, 77.14, 0.6357, 22, -85.2, -22.78, 0.192, 44, -134.02, 429.09, 0.04, 5, 47, 168.62, -88.49, 0.0132, 4, 73.05, 47.54, 0.7644, 22, -45.58, -52.38, 0.096, 19, -39.42, 85.32, 0.0864, 44, -94.4, 399.49, 0.04, 4, 47, 129.67, -115.38, 0.01044, 4, 34.25, 20.43, 0.75756, 19, -78.23, 58.21, 0.192, 44, -133.2, 372.38, 0.04, 5, 46, 108.43, 109.37, 0.01264, 3, 99.62, -16.71, 0.03948, 4, 19.41, -16.12, 0.71588, 19, -93.07, 21.66, 0.192, 44, -148.04, 335.83, 0.04, 5, 46, 115.27, 58.94, 0.09343, 3, 105.19, -67.29, 0.11011, 4, 26.53, -66.51, 0.56738, 19, -85.95, -28.73, 0.19273, 44, -140.92, 285.43, 0.03636, 2, 4, 277.37, -45.47, 0.97673, 44, 109.92, 306.48, 0.02327, 3, 4, 273.26, -77.19, 0.60623, 17, 9.36, 69.62, 0.37222, 44, 105.82, 274.76, 0.02155, 3, 4, 267.64, -120.09, 0.17148, 17, 3.74, 26.72, 0.81203, 44, 100.19, 231.85, 0.01649, 4, 4, 241.22, -93.25, 0.48578, 17, -22.68, 53.56, 0.44595, 22, 122.6, -193.17, 0.03911, 44, 73.78, 258.7, 0.02916, 4, 4, 238.31, -115.57, 0.28948, 17, -25.59, 31.24, 0.66322, 22, 119.68, -215.49, 0.02493, 44, 70.86, 236.38, 0.02237, 4, 4, 210.71, -104.36, 0.49836, 17, -53.19, 42.45, 0.36358, 22, 92.08, -204.29, 0.11406, 44, 43.26, 247.59, 0.024, 4, 46, 222.62, 12.7, 0.00479, 4, 134.13, -112.15, 0.77579, 19, 21.66, -74.37, 0.19515, 44, -33.31, 239.8, 0.02427, 5, 46, 143.57, 30.63, 0.0556, 3, 132.77, -96.3, 0.00984, 4, 54.98, -94.66, 0.71125, 19, -57.49, -56.88, 0.19417, 44, -112.47, 257.28, 0.02915, 4, 46, 179.12, 15.36, 0.02035, 4, 90.62, -109.74, 0.7575, 19, -21.86, -71.96, 0.19446, 44, -76.83, 242.21, 0.02769, 4, 46, 172.45, 78.81, 0.00996, 4, 83.6, -46.33, 0.68379, 21, -4.56, 0.73, 0.23125, 44, -83.85, 305.62, 0.075, 4, 47, 188.4, 19.58, 0.01432, 4, 92.22, 155.72, 0.69344, 24, -5.71, 2.71, 0.23592, 44, -75.22, 507.67, 0.05632, 5, 47, 270.79, 13.71, 0.00136, 4, 174.65, 150.3, 0.67665, 18, -97.31, 0.29, 0.0922, 23, 67.09, 19.67, 0.19255, 44, 7.2, 502.25, 0.03724, 4, 47, 246.58, -56.85, 0.00169, 4, 150.83, 79.61, 0.75431, 23, 43.26, -51.02, 0.189, 44, -16.62, 431.56, 0.055, 4, 47, 270.5, -28.2, 0.00094, 4, 174.59, 108.39, 0.75916, 23, 67.03, -22.24, 0.19002, 44, 7.14, 460.34, 0.04988, 3, 4, 149.29, 6.99, 0.756, 20, 47.35, 48.73, 0.189, 44, -18.15, 358.94, 0.055, 3, 4, 170.15, -28.08, 0.756, 20, 68.21, 13.66, 0.189, 44, 2.7, 323.87, 0.055, 4, 46, 251.48, 52.6, 0.00045, 4, 162.77, -72.09, 0.75746, 20, 60.83, -30.35, 0.18948, 44, -4.67, 279.86, 0.05262, 4, 46, 221.22, 32.06, 0.00425, 4, 132.63, -92.8, 0.7577, 20, 30.69, -51.06, 0.19049, 44, -34.82, 259.14, 0.04756, 5, 46, 136.6, 48.3, 0.061, 3, 126.25, -78.46, 0.02874, 4, 47.91, -77.03, 0.67076, 20, -54.02, -35.29, 0.19012, 44, -119.53, 274.92, 0.04938, 5, 46, 123.83, 84.26, 0.03837, 3, 114.39, -42.2, 0.03707, 4, 34.95, -41.15, 0.68191, 20, -66.99, 0.59, 0.18934, 44, -132.5, 310.8, 0.05331, 4, 47, 137.74, -133.5, 0.00039, 4, 42.42, 2.36, 0.75561, 20, -59.52, 44.1, 0.189, 44, -125.03, 354.3, 0.055, 4, 47, 168.39, -110.94, 0.00464, 4, 72.94, 25.09, 0.75136, 20, -28.99, 66.83, 0.189, 44, -94.5, 377.03, 0.055, 4, 47, 213.09, -112.2, 0.00075, 4, 117.65, 24.07, 0.75525, 20, 15.71, 65.82, 0.189, 44, -49.8, 376.02, 0.055, 4, 47, 208.42, -63.13, 0.00676, 4, 112.71, 73.12, 0.74924, 23, 5.14, -57.51, 0.189, 44, -54.74, 425.07, 0.055, 4, 47, 168.24, -57.72, 0.02505, 4, 72.5, 78.31, 0.73095, 23, -35.07, -52.32, 0.189, 44, -94.95, 430.26, 0.055, 5, 47, 142.7, -37.87, 0.05624, 3, 130.53, 96.54, 0.01501, 4, 46.85, 98.01, 0.68706, 23, -60.72, -32.62, 0.18958, 44, -120.6, 449.96, 0.05211, 5, 47, 134.09, -7.43, 0.05088, 3, 122.67, 127.18, 0.00715, 4, 38.06, 128.4, 0.71108, 23, -69.5, -2.23, 0.19228, 44, -129.38, 480.35, 0.03862, 5, 47, 220.53, -92.97, 0.00174, 4, 124.98, 43.35, 0.77586, 22, 6.35, -56.58, 0.096, 19, 12.51, 81.13, 0.0864, 44, -42.47, 395.29, 0.04, 4, 47, 235.78, 28.98, 0.00546, 4, 139.56, 165.38, 0.75634, 24, 41.62, 12.37, 0.19045, 44, -27.89, 517.33, 0.04774, 4, 47, 234.34, -0.58, 0.00519, 4, 138.27, 135.81, 0.74523, 24, 40.34, -17.19, 0.1876, 44, -29.17, 487.76, 0.06198, 4, 47, 207.4, -21.04, 0.01049, 4, 111.45, 115.21, 0.73457, 24, 13.52, -37.8, 0.18627, 44, -56, 467.15, 0.06867, 4, 47, 172.95, -18.61, 0.02498, 4, 76.99, 117.44, 0.72196, 24, -20.94, -35.57, 0.18673, 44, -90.45, 469.39, 0.06633, 4, 47, 156.07, 1.11, 0.03113, 4, 60, 137.06, 0.72736, 24, -37.94, -15.94, 0.18962, 44, -107.45, 489.01, 0.05189, 3, 4, 271.14, 87.52, 0.57879, 18, -0.82, -62.49, 0.39505, 44, 103.7, 439.47, 0.02616, 2, 4, 276.93, 53.42, 0.975, 44, 109.49, 405.37, 0.025, 4, 4, 236.32, 93.34, 0.58902, 18, -35.64, -56.66, 0.28456, 22, 117.69, -6.58, 0.09297, 44, 68.88, 445.29, 0.03345, 3, 4, 236.46, 59.24, 0.87572, 22, 117.83, -40.68, 0.09048, 44, 69.01, 411.19, 0.03381, 3, 4, 233.16, -42.68, 0.88076, 22, 114.53, -142.6, 0.08637, 44, 65.71, 309.27, 0.03287, 3, 4, 196.37, -55.42, 0.768, 19, 83.89, -17.64, 0.192, 44, 28.92, 296.53, 0.04, 3, 4, 306.28, -14.45, 0.51496, 5, 6.34, -14.13, 0.4641, 44, 138.83, 337.49, 0.02094, 4, 4, 330.56, -15.41, 0.04616, 5, 30.64, -13.74, 0.64427, 6, -8.79, -12.76, 0.28913, 44, 163.11, 336.54, 0.02044, 3, 5, 53.12, -13.35, 0.12984, 6, 13.57, -15.13, 0.85012, 44, 185.58, 335.68, 0.02004, 3, 5, 28.68, 15.63, 0.73124, 6, -7.12, 16.63, 0.24825, 44, 162.78, 365.97, 0.02051, 3, 5, 52.89, 17.35, 0.11918, 6, 17.11, 15.37, 0.86051, 44, 187.04, 366.35, 0.0203, 3, 4, 304.95, 15.14, 0.69897, 5, 3.38, 15.35, 0.28085, 44, 137.5, 367.09, 0.02018, 2, 4, 275.98, 20.29, 0.97482, 44, 108.53, 372.24, 0.02518, 2, 4, 289.48, 33.17, 0.98059, 44, 122.03, 385.12, 0.01941, 3, 4, 295.12, -30.47, 0.85757, 5, -3.92, -30.73, 0.12377, 44, 127.68, 321.48, 0.01866, 3, 4, 228.91, 18.05, 0.86015, 22, 110.29, -81.88, 0.10453, 44, 61.47, 369.99, 0.03532, 5, 47, 299.58, -46.65, 1e-05, 4, 203.77, 90.11, 0.69158, 18, -68.19, -59.9, 0.07604, 22, 85.15, -9.81, 0.19191, 44, 36.33, 442.06, 0.04046, 2, 4, 261.67, 11.67, 0.97183, 44, 94.23, 363.62, 0.02817, 3, 4, 245.82, -24.8, 0.91965, 22, 127.19, -124.72, 0.04978, 44, 78.37, 327.15, 0.03058, 2, 4, 273.59, 0.97, 0.97465, 44, 106.14, 352.92, 0.02535, 3, 4, 249.24, 46.54, 0.92115, 22, 130.62, -53.39, 0.04784, 44, 81.8, 398.48, 0.03102, 4, 4, 297.42, -44.27, 0.84206, 5, -0.86, -44.39, 0.13917, 17, 33.52, 102.54, 0.00542, 44, 129.97, 307.67, 0.01335, 2, 4, 296.39, 44.65, 0.98314, 44, 128.94, 396.6, 0.01686, 6, 47, 289.3, -17.15, 0.00029, 4, 193.32, 119.55, 0.64533, 18, -78.64, -30.46, 0.13121, 23, 85.76, -11.08, 0.09591, 22, 74.7, 19.62, 0.08632, 44, 25.88, 471.49, 0.04094, 4, 46, 220.92, 88.53, 0.00075, 4, 132.01, -36.33, 0.74325, 21, 43.85, 10.73, 0.186, 44, -35.44, 315.61, 0.07, 4, 46, 217.11, 55.1, 0.00344, 4, 128.39, -69.79, 0.74493, 21, 40.22, -22.73, 0.18709, 44, -39.06, 282.16, 0.06453, 4, 46, 190.93, 40.19, 0.01226, 4, 102.29, -84.84, 0.7387, 21, 14.13, -37.78, 0.18774, 44, -65.15, 267.11, 0.0613, 4, 46, 179.03, 27.93, 0.01964, 4, 90.45, -97.17, 0.74217, 20, -11.48, -55.43, 0.19045, 44, -76.99, 254.78, 0.04773, 4, 46, 198.91, 115.95, 0.00024, 4, 109.85, -9.04, 0.74376, 21, 21.69, 38.03, 0.186, 44, -57.59, 342.91, 0.07, 4, 46, 161.73, 120.95, 0.00041, 4, 72.64, -4.24, 0.74359, 21, -15.52, 42.82, 0.186, 44, -94.8, 347.71, 0.07, 4, 46, 141.83, 106.36, 0.00628, 4, 52.82, -18.95, 0.73772, 21, -35.34, 28.11, 0.186, 44, -114.62, 333, 0.07, 5, 46, 138.85, 71.77, 0.03832, 3, 129.09, -55.06, 0.00423, 4, 50.03, -53.56, 0.70606, 21, -38.13, -6.49, 0.18715, 44, -117.41, 298.39, 0.06424, 4, 46, 157.14, 49.2, 0.03276, 4, 68.45, -76.02, 0.71759, 21, -19.71, -28.96, 0.18759, 44, -99, 275.93, 0.06206, 4, 4, 219.13, 124.1, 0.51575, 18, -52.83, -25.91, 0.35011, 22, 100.5, 24.18, 0.10479, 44, 51.68, 476.05, 0.02935, 6, 2, 224.15, 81.8, 0.00031, 47, 107.74, -55.84, 0.13787, 3, 95.13, 79.45, 0.17637, 4, 11.99, 79.85, 0.55344, 22, -106.64, -20.07, 0.09644, 44, -155.46, 431.8, 0.03556, 7, 2, 214.99, 44.47, 0.00107, 47, 98.58, -93.17, 0.0729, 3, 85.03, 42.36, 0.29616, 4, 3.03, 42.47, 0.49627, 22, -115.6, -57.45, 0.048, 19, -109.45, 80.25, 0.0456, 44, -164.42, 394.42, 0.04, 6, 47, 131.06, -88.56, 0.03984, 3, 117.62, 46.16, 0.02652, 4, 35.49, 47.26, 0.74618, 22, -83.14, -52.66, 0.07066, 19, -76.99, 85.04, 0.0768, 44, -131.96, 399.21, 0.04, 5, 2, 192.93, 74.08, 0.02059, 47, 76.52, -63.55, 0.24438, 3, 63.72, 72.52, 0.39239, 4, -19.19, 71.97, 0.31166, 44, -186.63, 423.91, 0.03098, 5, 2, 171.13, 84.25, 0.05634, 47, 54.72, -53.39, 0.38833, 3, 42.18, 83.22, 0.37459, 4, -41.05, 82.01, 0.1607, 44, -208.5, 433.95, 0.02004, 5, 2, 151.33, 112.5, 0.04315, 47, 34.92, -25.14, 0.68877, 3, 23.09, 111.95, 0.18986, 4, -61.01, 110.14, 0.06827, 44, -228.45, 462.09, 0.00996, 5, 2, 188.39, 42.14, 0.0202, 47, 71.98, -95.49, 0.11559, 3, 58.39, 40.7, 0.63016, 4, -23.55, 40, 0.19405, 44, -191, 391.95, 0.04, 5, 2, 161.6, 39.06, 0.10528, 47, 45.19, -98.57, 0.1312, 3, 31.53, 38.29, 0.6886, 4, -50.32, 36.77, 0.03788, 44, -217.77, 388.72, 0.03704, 5, 2, 126.42, 32.5, 0.50877, 47, 10, -105.13, 0.1215, 3, -3.81, 32.61, 0.34565, 4, -85.47, 30.02, 0.00157, 44, -252.92, 381.96, 0.02251, 5, 46, 102.74, 64.57, 0.12469, 3, 92.81, -61.35, 0.20327, 4, 13.96, -60.95, 0.54056, 19, -98.51, -23.17, 0.0965, 44, -153.48, 291, 0.03498, 5, 2, 195.35, -52.35, 0.00738, 46, 72.74, 71.23, 0.19586, 3, 62.99, -53.94, 0.49972, 4, -16.07, -54.46, 0.2667, 44, -183.51, 297.49, 0.03035, 5, 2, 176.99, -58.93, 0.03911, 46, 54.38, 64.65, 0.28569, 3, 44.46, -60.06, 0.51247, 4, -34.4, -61.14, 0.13591, 44, -201.84, 290.81, 0.02682, 5, 2, 156.65, -98.77, 0.03798, 46, 34.05, 24.81, 0.67436, 3, 23.14, -99.38, 0.21738, 4, -54.51, -101.09, 0.06152, 44, -221.95, 250.86, 0.00875, 4, 46, 67.5, 112.88, 0.02877, 3, 58.79, -12.18, 0.89016, 4, -21.54, -12.84, 0.04106, 44, -188.99, 339.1, 0.04, 5, 2, 166.52, -14.91, 0.01199, 46, 43.91, 108.67, 0.04386, 3, 35.1, -15.79, 0.90487, 4, -45.11, -17.18, 0.00392, 44, -212.55, 334.77, 0.03536, 5, 46, 94.61, 113.6, 0.01225, 3, 85.91, -12.13, 0.22498, 4, 5.57, -11.97, 0.62677, 19, -106.91, 25.82, 0.096, 44, -161.88, 339.98, 0.04, 4, 2, 123.63, 170.83, 2e-05, 47, 7.22, 33.19, 0.99943, 3, -3.15, 170.96, 0.00024, 4, -89.03, 168.32, 0.00031, 3, 2, 110.98, 199.27, 0.00029, 47, -5.43, 61.64, 0.97812, 36, 110.25, 15.98, 0.02159, 3, 2, 59.26, 224.79, 0.00638, 47, -57.15, 87.16, 0.766, 36, 60.32, -12.87, 0.22763, 3, 2, 80.31, 206.91, 0.00681, 47, -36.1, 69.27, 0.87099, 36, 86.88, -5.29, 0.1222, 2, 2, 101.42, 181.29, 0.00467, 47, -15, 43.65, 0.99533, 3, 46, 21.23, -5.94, 0.93288, 3, 9.57, -129.81, 0.05043, 4, -67.15, -131.92, 0.0167, 4, 2, 97.55, -189.69, 0.02844, 46, -25.06, -66.11, 0.97156, 3, -38.21, -188.8, 0, 4, -113.11, -192.34, 0, 4, 2, 79.63, -195.71, 0.05245, 46, -42.98, -72.12, 0.94755, 3, -56.27, -194.36, 0, 4, -130.99, -198.45, 0, 4, 2, 127.92, -164.32, 0.0015, 46, 5.31, -40.73, 0.99709, 3, -7.22, -164.19, 0.00089, 4, -82.88, -166.8, 0.00052, 2, 2, 112.98, -180.76, 0.01159, 46, -9.63, -57.17, 0.98841, 5, 2, 141.89, -80.57, 0.139, 46, 19.28, 43.02, 0.56305, 3, 8.83, -80.81, 0.25377, 4, -69.38, -82.97, 0.02974, 44, -236.82, 268.98, 0.01443, 5, 2, 109.52, -49.82, 0.57491, 46, -13.09, 73.77, 0.2916, 3, -22.75, -49.26, 0.13117, 4, -101.91, -52.4, 0.00033, 44, -269.35, 299.55, 0.00198, 2, 2, 66.99, -19.85, 0.93334, 46, -55.62, 103.74, 0.06666, 3, 1, 0.8, 51.73, 0.1, 2, 24.87, -2.84, 0.89641, 46, -97.74, 120.74, 0.00359, 4, 1, 1.58, 12.2, 0.53809, 2, -14.65, -1.29, 0.45774, 47, -131.06, -138.92, 0.00067, 46, -137.26, 122.3, 0.00351, 3, 1, 16.06, -14.81, 0.9726, 2, -42.46, -14.15, 0.0201, 46, -165.07, 109.44, 0.0073, 4, 2, 128.94, -25.34, 0.51141, 46, 6.33, 98.25, 0.10547, 3, -2.73, -25.28, 0.36372, 44, -250.07, 324.13, 0.01941, 4, 2, 139.01, 139.52, 0.00121, 47, 22.6, 1.89, 0.93282, 3, 11.45, 139.28, 0.0486, 4, -73.47, 137.1, 0.01737, 3, 2, 97.82, 224.26, 0.00011, 47, -18.59, 86.63, 0.86491, 36, 82.17, 18.91, 0.13499, 4, 1, -183.63, 63.93, 0.08911, 2, 47.95, 180.54, 0.05488, 47, -68.46, 42.91, 0.79694, 36, 90.84, -46.84, 0.05907, 2, 2, 75.02, 166.54, 0.04616, 47, -41.39, 28.91, 0.95384, 2, 2, 101.85, 152.43, 0.01525, 47, -14.56, 14.8, 0.98475, 4, 2, 127.13, 109.78, 0.09826, 47, 10.72, -27.85, 0.76499, 3, -1.17, 109.85, 0.11626, 4, -85.19, 107.3, 0.02049, 4, 2, 104.05, 72.01, 0.43796, 47, -12.36, -65.62, 0.4321, 3, -25.18, 72.66, 0.12692, 4, -108.06, 69.4, 0.00302, 5, 2, 139.83, 81.65, 0.17658, 47, 23.42, -55.99, 0.47411, 3, 10.83, 81.4, 0.29089, 4, -72.33, 79.23, 0.0423, 44, -239.78, 431.18, 0.01612, 3, 2, 73.7, 96.21, 0.36833, 47, -42.71, -41.42, 0.6118, 3, -54.92, 97.61, 0.01987, 3, 1, -80.68, 53.09, 0.11697, 2, 31.05, 78.41, 0.51011, 47, -85.37, -59.22, 0.37292, 3, 1, -76.44, 10.63, 0.55324, 2, -11.6, 76.69, 0.25001, 47, -128.01, -60.95, 0.19675, 4, 1, 105.55, 138.78, 0.0056, 2, 105.56, -112.56, 0.1224, 46, -17.05, 11.03, 0.8601, 3, -28.28, -111.89, 0.0119, 5, 1, 149.63, 105.84, 0.03184, 2, 70.08, -154.62, 0.11771, 46, -52.53, -31.03, 0.85045, 3, -64.8, -153.05, 0, 4, -140.77, -157.42, 0, 3, 1, 95.41, 75.95, 0.09413, 2, 43.44, -98.73, 0.40602, 46, -79.17, 24.86, 0.49985, 3, 1, 67.54, 29.33, 0.35878, 2, -1.45, -68.15, 0.43548, 46, -124.06, 55.44, 0.20574, 3, 1, -128.74, 44.98, 0.23519, 2, 25.79, 126.87, 0.20963, 47, -90.62, -10.76, 0.55518, 6, 2, 221.92, -75.38, 2e-05, 46, 99.31, 48.21, 0.18078, 3, 88.98, -77.62, 0.21142, 4, 10.63, -77.33, 0.48342, 19, -101.84, -39.55, 0.09729, 44, -156.81, 274.62, 0.02706, 5, 46, 123.54, 36.21, 0.10133, 3, 112.9, -90.23, 0.07956, 4, 34.92, -89.2, 0.64509, 19, -77.55, -51.42, 0.14576, 44, -132.52, 262.75, 0.02826, 6, 2, 216.76, 88.88, 0.00173, 47, 100.35, -48.75, 0.17976, 3, 87.91, 86.72, 0.21008, 4, 4.55, 86.9, 0.50126, 22, -114.08, -13.03, 0.07764, 44, -162.89, 438.84, 0.02953, 3, 2, 63.92, 39.58, 0.83316, 47, -52.49, -98.05, 0.16108, 3, -66.11, 41.24, 0.00576, 3, 1, -30.86, 48.45, 0.09042, 2, 23.46, 28.95, 0.83109, 47, -92.95, -108.68, 0.0785, 3, 1, -30.49, 11.29, 0.58337, 2, -13.66, 30.78, 0.35767, 47, -130.07, -106.86, 0.05896, 4, 47, 209.33, 42.04, 0.00861, 4, 113.03, 178.29, 0.7576, 24, 15.1, 25.28, 0.19155, 44, -54.42, 530.24, 0.04224, 4, 47, 181.26, 39.9, 0.01582, 4, 84.97, 175.99, 0.73788, 24, -12.96, 22.99, 0.20345, 44, -82.47, 527.94, 0.04286, 4, 47, 159.23, 25.15, 0.02619, 4, 63.03, 161.12, 0.73842, 24, -34.91, 8.12, 0.19115, 44, -104.42, 513.07, 0.04423], "hull": 83, "edges": [0, 164, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 50, 52, 62, 64, 64, 66, 66, 68, 68, 70, 80, 82, 88, 90, 90, 92, 108, 110, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 138, 140, 158, 160, 28, 30, 30, 32, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 14, 16, 10, 12, 12, 14, 6, 8, 8, 10, 0, 2, 160, 162, 162, 164, 154, 156, 156, 158, 2, 4, 4, 6, 16, 18, 18, 20, 152, 154, 148, 150, 150, 152, 146, 148, 144, 146, 140, 142, 142, 144, 136, 138, 134, 136, 132, 134, 130, 132, 124, 126, 126, 128, 128, 130, 110, 112, 112, 114, 100, 102, 102, 104, 98, 100, 92, 94, 86, 88, 82, 84, 84, 86, 72, 74, 74, 76, 70, 72, 184, 186, 188, 190, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 190, 204, 204, 192, 186, 206, 206, 188, 166, 176, 176, 170, 168, 174, 174, 172, 202, 208, 208, 210, 210, 214, 214, 212, 212, 216, 218, 216, 296, 168, 294, 166, 302, 308, 308, 304, 116, 310, 310, 312, 312, 314, 314, 316, 316, 318, 306, 332, 318, 334, 332, 336, 336, 334, 40, 42, 42, 44, 342, 346, 346, 344, 348, 350, 350, 352, 352, 354, 356, 358, 360, 362, 348, 364, 364, 362, 344, 366, 366, 368, 368, 370, 370, 372, 312, 374, 376, 378, 346, 302, 346, 378, 378, 380, 380, 382, 382, 384, 372, 118, 304, 396, 396, 306, 398, 400, 402, 168, 400, 402, 166, 406, 406, 402, 404, 406, 404, 408, 408, 410, 418, 302, 424, 414, 410, 412, 416, 394, 342, 432, 432, 418, 434, 436, 436, 438, 354, 440, 440, 356, 434, 442, 442, 444, 444, 446, 446, 448, 438, 450, 450, 448, 360, 358, 104, 106, 106, 108, 96, 98, 94, 96, 522, 98, 522, 488, 488, 486, 48, 552, 556, 116, 78, 80, 76, 78, 388, 392, 342, 126, 564, 376, 564, 566, 568, 384, 566, 568], "width": 486, "height": 795}}, "body2": {"body": {"type": "mesh", "uvs": [0.49116, 0.00023, 0.5168, 0.00801, 0.53412, 0.00321, 0.55716, 0.00419, 0.58978, 0.01499, 0.62582, 0.03661, 0.65615, 0.07077, 0.7, 0.10134, 0.7276, 0.12462, 0.73914, 0.16055, 0.74492, 0.19564, 0.73634, 0.23022, 0.75449, 0.25721, 0.68842, 0.28773, 0.71475, 0.29276, 0.78765, 0.29961, 0.86054, 0.30647, 0.84591, 0.31868, 0.8339, 0.34389, 0.81095, 0.38475, 0.78413, 0.44026, 0.79502, 0.48364, 0.7991, 0.53005, 0.79402, 0.57893, 0.7669, 0.6145, 0.76066, 0.63895, 0.7813, 0.66405, 0.81719, 0.69097, 0.77599, 0.72519, 0.69168, 0.70263, 0.67593, 0.68006, 0.59104, 0.68974, 0.48269, 0.69582, 0.41652, 0.6925, 0.38323, 0.66369, 0.3254, 0.63973, 0.23834, 0.62526, 0.19594, 0.59879, 0.17554, 0.56883, 0.16538, 0.53622, 0.17038, 0.49406, 0.20241, 0.45728, 0.20276, 0.43858, 0.24254, 0.40998, 0.22738, 0.37727, 0.21477, 0.34586, 0.20994, 0.32744, 0.21159, 0.31498, 0.2454, 0.31013, 0.24492, 0.285, 0.26442, 0.25463, 0.22719, 0.24075, 0.22088, 0.21962, 0.23614, 0.18831, 0.26797, 0.14481, 0.28474, 0.1106, 0.31452, 0.07364, 0.34705, 0.04256, 0.39701, 0.01505, 0.45394, 0.00056, 0.44696, 0.21943, 0.60394, 0.21885, 0.44942, 0.27391, 0.60485, 0.26415, 0.60403, 0.24162, 0.44866, 0.24645, 0.64106, 0.2823, 0.30042, 0.30251, 0.3651, 0.29364, 0.35623, 0.23235, 0.37622, 0.22731, 0.42341, 0.19017, 0.44143, 0.17156, 0.46211, 0.13971, 0.47257, 0.11541, 0.48137, 0.10382, 0.4978, 0.0923, 0.5167, 0.08758, 0.54009, 0.09012, 0.45151, 0.1628, 0.40464, 0.21185, 0.59004, 0.11805, 0.62082, 0.1447, 0.63079, 0.17022, 0.62422, 0.15837, 0.63321, 0.15753, 0.6512, 0.14061, 0.52209, 0.09589, 0.31287, 0.24949, 0.29516, 0.27631, 0.35976, 0.26416, 0.41097, 0.28472, 0.40379, 0.24928, 0.27018, 0.22821, 0.31829, 0.16444, 0.33588, 0.1375, 0.35578, 0.09705, 0.38602, 0.06175, 0.52297, 0.03608, 0.54594, 0.06114, 0.49813, 0.0591, 0.46146, 0.067, 0.43042, 0.08837, 0.40638, 0.12045, 0.29265, 0.19663, 0.32327, 0.21235, 0.36553, 0.19101, 0.39554, 0.15769, 0.42354, 0.03675, 0.47254, 0.02664, 0.56332, 0.02608, 0.57435, 0.06202, 0.61206, 0.0919, 0.64548, 0.12006, 0.60506, 0.05342, 0.63079, 0.08524, 0.6729, 0.11069, 0.69311, 0.13989, 0.69924, 0.17546, 0.69985, 0.2084, 0.69495, 0.24959, 0.64657, 0.24697, 0.64838, 0.20811, 0.65678, 0.17114, 0.45183, 0.19446, 0.59797, 0.18997, 0.27254, 0.33065, 0.27553, 0.36516, 0.41297, 0.41407, 0.57051, 0.4142, 0.73232, 0.43539, 0.48103, 0.43495, 0.40009, 0.62676, 0.45654, 0.575, 0.51652, 0.62191, 0.59325, 0.63808, 0.69601, 0.62574, 0.62511, 0.31209, 0.69073, 0.31511, 0.77951, 0.31928, 0.72727, 0.35427, 0.77345, 0.35642, 0.75346, 0.39185, 0.77791, 0.48751, 0.75071, 0.58811, 0.77776, 0.54233, 0.64824, 0.55542, 0.23215, 0.55821, 0.23419, 0.45431, 0.38207, 0.47947, 0.32031, 0.45156, 0.53145, 0.4765, 0.6012, 0.44794, 0.69244, 0.45424, 0.73832, 0.4907, 0.71526, 0.59817, 0.64296, 0.61687, 0.55275, 0.61043, 0.50268, 0.57362, 0.49983, 0.5174, 0.3996, 0.52691, 0.39338, 0.57777, 0.35572, 0.61131, 0.29425, 0.62439, 0.45943, 0.50949, 0.20709, 0.49941, 0.26797, 0.49902, 0.31327, 0.53133, 0.31248, 0.57476, 0.27403, 0.59743, 0.35254, 0.32888, 0.42197, 0.31931, 0.34442, 0.37301, 0.41448, 0.37054, 0.62425, 0.36781, 0.65449, 0.41316, 0.55819, 0.27787, 0.55748, 0.24731, 0.55676, 0.21903, 0.49704, 0.24971, 0.49358, 0.21926, 0.49753, 0.28153, 0.49015, 0.31827, 0.46219, 0.30218, 0.59232, 0.2908, 0.49995, 0.37724, 0.35465, 0.41367, 0.50943, 0.33566, 0.58612, 0.35311, 0.53011, 0.31997, 0.43917, 0.35362, 0.62044, 0.28699, 0.43785, 0.29428, 0.29533, 0.42878, 0.62237, 0.49529, 0.69151, 0.49758, 0.72531, 0.52934, 0.75195, 0.54338, 0.56872, 0.52496, 0.56298, 0.57202, 0.59538, 0.59592, 0.66679, 0.59709, 0.71092, 0.57245, 0.28312, 0.39668, 0.39688, 0.65387, 0.47468, 0.66261, 0.46125, 0.62216, 0.68596, 0.6419, 0.58624, 0.65575, 0.71999, 0.64498, 0.7417, 0.61366, 0.18348, 0.53359, 0.19129, 0.56868, 0.22427, 0.59524], "triangles": [44, 127, 203, 44, 45, 127, 45, 126, 127, 127, 126, 170, 126, 45, 46, 126, 46, 48, 48, 46, 47, 126, 67, 170, 126, 48, 67, 170, 67, 68, 48, 49, 67, 49, 89, 67, 67, 89, 68, 89, 90, 68, 49, 50, 89, 89, 88, 90, 89, 50, 88, 50, 93, 88, 50, 51, 93, 142, 141, 19, 142, 140, 141, 19, 141, 18, 141, 139, 18, 141, 140, 139, 140, 138, 139, 18, 139, 17, 138, 14, 139, 139, 15, 17, 139, 14, 15, 17, 15, 16, 13, 120, 12, 88, 69, 90, 92, 69, 70, 92, 90, 69, 120, 11, 12, 65, 180, 179, 179, 180, 178, 121, 122, 120, 120, 119, 11, 120, 122, 119, 88, 105, 69, 88, 93, 105, 92, 60, 65, 92, 80, 60, 92, 70, 80, 177, 178, 64, 122, 121, 61, 65, 60, 180, 178, 61, 64, 121, 64, 61, 51, 52, 93, 69, 105, 70, 11, 119, 10, 52, 53, 93, 93, 104, 105, 93, 53, 104, 105, 106, 70, 70, 106, 80, 80, 71, 60, 60, 124, 180, 60, 71, 124, 178, 180, 125, 125, 180, 124, 73, 125, 79, 178, 125, 61, 81, 73, 74, 73, 81, 125, 61, 125, 122, 105, 104, 106, 80, 106, 71, 122, 118, 119, 119, 118, 10, 125, 83, 122, 122, 123, 118, 122, 83, 123, 104, 94, 106, 94, 104, 54, 118, 9, 10, 81, 74, 87, 74, 75, 87, 87, 78, 81, 71, 72, 124, 72, 79, 124, 125, 124, 79, 106, 107, 71, 106, 94, 107, 71, 107, 72, 125, 84, 83, 125, 82, 84, 125, 81, 82, 104, 53, 54, 9, 118, 117, 72, 107, 79, 83, 85, 123, 85, 86, 123, 118, 123, 117, 123, 86, 117, 83, 84, 85, 94, 95, 107, 94, 54, 95, 79, 107, 73, 117, 8, 9, 84, 82, 85, 107, 103, 73, 107, 95, 103, 85, 82, 86, 54, 55, 95, 82, 113, 86, 82, 81, 113, 117, 86, 116, 86, 113, 116, 8, 116, 7, 8, 117, 116, 75, 76, 87, 73, 103, 74, 95, 96, 103, 95, 55, 96, 103, 102, 74, 103, 96, 102, 81, 112, 113, 112, 115, 113, 113, 115, 116, 81, 78, 112, 74, 102, 75, 7, 116, 6, 55, 56, 96, 76, 75, 101, 116, 115, 6, 96, 97, 102, 96, 56, 97, 76, 77, 87, 87, 77, 78, 75, 102, 101, 76, 100, 77, 76, 101, 100, 78, 111, 112, 111, 114, 112, 112, 114, 115, 78, 99, 111, 78, 77, 99, 102, 97, 101, 77, 100, 99, 115, 114, 6, 56, 57, 97, 114, 5, 6, 97, 108, 101, 101, 109, 100, 101, 108, 109, 114, 111, 110, 97, 57, 108, 100, 98, 99, 111, 99, 110, 99, 98, 110, 100, 109, 98, 110, 4, 114, 114, 4, 5, 57, 58, 108, 108, 59, 109, 108, 58, 59, 110, 2, 3, 2, 110, 1, 109, 1, 98, 110, 98, 1, 109, 0, 1, 109, 59, 0, 110, 3, 4, 62, 179, 181, 181, 179, 176, 179, 177, 176, 176, 177, 63, 92, 65, 62, 62, 65, 179, 177, 64, 63, 63, 64, 121, 179, 178, 177, 209, 26, 30, 26, 209, 25, 34, 204, 33, 33, 204, 205, 206, 204, 132, 204, 206, 205, 32, 205, 208, 30, 207, 209, 30, 208, 207, 207, 208, 135, 34, 35, 204, 205, 134, 208, 205, 206, 134, 208, 134, 135, 204, 35, 132, 207, 136, 209, 25, 209, 24, 209, 136, 210, 207, 135, 136, 35, 162, 132, 36, 163, 35, 35, 163, 162, 24, 209, 210, 135, 156, 136, 134, 157, 135, 135, 157, 156, 133, 206, 132, 210, 136, 144, 136, 156, 155, 37, 213, 36, 36, 169, 163, 36, 213, 169, 163, 169, 162, 132, 162, 161, 132, 161, 133, 206, 133, 134, 133, 158, 134, 134, 158, 157, 157, 200, 156, 156, 201, 155, 156, 200, 201, 210, 144, 24, 24, 144, 23, 144, 136, 155, 169, 168, 162, 162, 168, 161, 157, 199, 200, 157, 158, 199, 38, 212, 37, 37, 212, 213, 201, 202, 155, 144, 155, 197, 168, 169, 147, 200, 146, 201, 201, 146, 202, 200, 199, 146, 169, 213, 147, 213, 212, 147, 23, 144, 145, 144, 197, 145, 197, 155, 202, 23, 145, 22, 168, 167, 161, 161, 160, 133, 161, 167, 160, 133, 159, 158, 133, 164, 159, 133, 160, 164, 168, 147, 167, 199, 158, 198, 202, 196, 197, 202, 146, 196, 199, 198, 146, 198, 158, 159, 212, 38, 211, 38, 39, 211, 212, 211, 147, 211, 165, 147, 147, 166, 167, 147, 165, 166, 198, 194, 146, 146, 195, 196, 146, 194, 195, 143, 145, 154, 143, 22, 145, 196, 154, 197, 197, 154, 145, 39, 40, 211, 211, 40, 165, 160, 167, 149, 143, 21, 22, 196, 195, 154, 167, 166, 149, 160, 149, 164, 159, 151, 198, 198, 151, 194, 159, 164, 151, 164, 131, 151, 164, 149, 131, 166, 165, 148, 165, 40, 148, 166, 150, 149, 166, 148, 150, 154, 195, 153, 151, 152, 194, 195, 194, 153, 194, 152, 153, 40, 41, 148, 143, 154, 130, 154, 153, 130, 143, 20, 21, 143, 130, 20, 149, 128, 131, 149, 150, 128, 151, 129, 152, 151, 131, 129, 41, 42, 148, 148, 193, 150, 148, 43, 193, 148, 42, 43, 152, 175, 153, 153, 175, 130, 150, 186, 128, 150, 193, 186, 152, 129, 175, 130, 142, 20, 20, 142, 19, 130, 175, 142, 131, 128, 185, 131, 185, 129, 185, 128, 173, 43, 203, 193, 193, 203, 186, 129, 174, 175, 129, 185, 174, 173, 190, 185, 128, 186, 173, 203, 172, 186, 186, 172, 173, 175, 174, 140, 174, 138, 140, 175, 140, 142, 138, 174, 137, 138, 66, 13, 66, 138, 137, 43, 44, 203, 203, 127, 172, 185, 188, 174, 185, 187, 188, 185, 190, 187, 172, 170, 173, 172, 127, 170, 171, 173, 170, 171, 190, 173, 174, 188, 137, 190, 182, 187, 182, 171, 183, 182, 190, 171, 187, 189, 188, 137, 189, 184, 137, 188, 189, 187, 182, 189, 170, 68, 171, 182, 181, 189, 189, 176, 184, 189, 181, 176, 68, 91, 171, 171, 192, 183, 171, 91, 192, 182, 183, 181, 138, 13, 14, 184, 191, 137, 137, 191, 66, 192, 62, 183, 183, 62, 181, 192, 91, 62, 68, 90, 91, 191, 184, 63, 13, 66, 120, 120, 66, 121, 184, 176, 63, 191, 63, 66, 90, 92, 91, 91, 92, 62, 66, 63, 121, 26, 29, 30, 33, 205, 32, 31, 32, 208, 31, 208, 30, 28, 29, 26, 28, 26, 27], "vertices": [2, 6, 190.1, -4.57, 0.99168, 43, -153.98, 61.43, 0.00832, 3, 6, 182.45, -16.19, 0.99476, 17, 257.69, 140.94, 0, 43, -161.63, 49.8, 0.00524, 3, 6, 185.22, -25, 0.99336, 17, 261.05, 132.34, 0, 43, -158.87, 40.99, 0.00664, 3, 6, 183.09, -36.03, 0.99478, 17, 259.67, 121.2, 0, 43, -161, 29.97, 0.00522, 3, 6, 172.64, -50.72, 0.99431, 17, 250.25, 105.82, 0, 43, -171.45, 15.27, 0.00569, 3, 6, 153.46, -66.02, 0.99463, 17, 232.14, 89.26, 0, 43, -190.63, -0.03, 0.00537, 4, 5, 171.06, -61.47, 0.00494, 6, 124.71, -77.37, 0.99043, 17, 204.23, 75.99, 0.00013, 43, -219.37, -11.37, 0.0045, 4, 5, 146.8, -82.82, 0.03092, 6, 98.01, -95.57, 0.96167, 17, 178.83, 56.02, 0.00243, 43, -246.07, -29.58, 0.00498, 4, 5, 128.3, -96.26, 0.06869, 6, 78.01, -106.65, 0.91604, 17, 159.63, 43.61, 0.0068, 43, -266.08, -40.65, 0.00847, 4, 5, 99.75, -101.92, 0.16414, 6, 48.98, -108.75, 0.80785, 17, 130.8, 39.54, 0.02066, 43, -295.11, -42.76, 0.00736, 5, 4, 366.7, -108.58, 0.00328, 5, 71.86, -104.77, 0.26631, 6, 20.95, -108.16, 0.67521, 17, 102.8, 38.23, 0.046, 43, -323.14, -42.16, 0.00919, 5, 4, 339.46, -102.94, 0.04582, 5, 44.36, -100.65, 0.26985, 6, -5.84, -100.69, 0.5632, 17, 75.57, 43.87, 0.11613, 43, -349.92, -34.7, 0.005, 5, 4, 317.57, -110.6, 0.10053, 5, 22.92, -109.5, 0.2509, 6, -28.2, -106.85, 0.46117, 17, 53.67, 36.21, 0.18314, 43, -372.29, -40.85, 0.00425, 2, 4, 295.06, -77.23, 0.60554, 17, 31.16, 69.58, 0.39446, 2, 4, 290.38, -89.8, 0.44524, 17, 26.48, 57.01, 0.55476, 2, 4, 283.04, -124.88, 0.17944, 17, 19.14, 21.93, 0.82056, 1, 17, 11.8, -13.15, 1, 2, 17, 2.49, -5.54, 0.99055, 44, 98.94, 199.6, 0.00945, 3, 4, 246.69, -145.44, 0.07297, 17, -17.21, 1.37, 0.91398, 44, 79.24, 206.51, 0.01305, 3, 4, 214.84, -132.57, 0.33615, 17, -49.05, 14.24, 0.64822, 44, 47.4, 219.38, 0.01563, 5, 46, 259.93, 7.47, 0.00049, 4, 171.47, -117.18, 0.68931, 17, -92.42, 29.63, 0.19816, 19, 59, -79.4, 0.09866, 44, 4.03, 234.77, 0.01338, 5, 46, 225.2, 4.22, 0.00484, 4, 136.76, -120.62, 0.85447, 17, -127.14, 26.19, 0.02689, 19, 24.29, -82.84, 0.09847, 44, -30.68, 231.33, 0.01533, 4, 46, 188.24, 4.42, 0.01791, 4, 99.8, -120.62, 0.86774, 19, -12.67, -82.84, 0.09841, 44, -67.64, 231.33, 0.01594, 4, 46, 149.6, 9.19, 0.04887, 4, 61.14, -116.07, 0.84062, 19, -51.34, -78.29, 0.09883, 44, -106.31, 235.87, 0.01168, 5, 46, 122.15, 24.02, 0.1083, 3, 111.2, -102.38, 0.08167, 4, 33.6, -101.4, 0.70405, 19, -78.87, -63.62, 0.09934, 44, -133.84, 250.55, 0.00664, 3, 46, 102.92, 28.19, 0.22955, 3, 92.09, -97.72, 0.20972, 4, 14.35, -97.33, 0.56073, 3, 46, 82.41, 19.36, 0.4218, 3, 71.36, -106.04, 0.27682, 4, -6.11, -106.27, 0.30137, 4, 2, 182.63, -120.37, 0.00082, 46, 60.02, 3.21, 0.68692, 3, 48.57, -121.62, 0.20058, 4, -28.41, -122.54, 0.11168, 5, 2, 156.65, -98.77, 0.03798, 46, 34.05, 24.81, 0.67436, 3, 23.14, -99.38, 0.21738, 4, -54.51, -101.09, 0.06152, 44, -221.95, 250.86, 0.00875, 5, 2, 176.99, -58.93, 0.03911, 46, 54.38, 64.65, 0.28569, 3, 44.46, -60.06, 0.51247, 4, -34.4, -61.14, 0.13591, 44, -201.84, 290.81, 0.02682, 5, 2, 195.35, -52.35, 0.00738, 46, 72.74, 71.23, 0.19586, 3, 62.99, -53.94, 0.49972, 4, -16.07, -54.46, 0.2667, 44, -183.51, 297.49, 0.03035, 4, 46, 67.5, 112.88, 0.02877, 3, 58.79, -12.18, 0.89016, 4, -21.54, -12.84, 0.04106, 44, -188.99, 339.1, 0.04, 5, 2, 188.39, 42.14, 0.0202, 47, 71.98, -95.49, 0.11559, 3, 58.39, 40.7, 0.63016, 4, -23.55, 40, 0.19405, 44, -191, 391.95, 0.04, 5, 2, 192.93, 74.08, 0.02059, 47, 76.52, -63.55, 0.24438, 3, 63.72, 72.52, 0.39239, 4, -19.19, 71.97, 0.31166, 44, -186.63, 423.91, 0.03098, 6, 2, 216.76, 88.88, 0.00173, 47, 100.35, -48.75, 0.17976, 3, 87.91, 86.72, 0.21008, 4, 4.55, 86.9, 0.50126, 22, -114.08, -13.03, 0.07764, 44, -162.89, 438.84, 0.02953, 4, 47, 121.02, -21.82, 0.08891, 3, 109.25, 113.12, 0.06469, 4, 25.08, 113.94, 0.6464, 22, -93.55, 14.02, 0.2, 4, 47, 135.01, 19.73, 0.03316, 4, 38.83, 155.57, 0.74811, 23, -68.73, 24.94, 0.19532, 44, -128.61, 507.52, 0.02342, 4, 47, 157.23, 39.06, 0.0215, 4, 60.95, 175.02, 0.75677, 23, -46.62, 44.39, 0.19457, 44, -106.5, 526.97, 0.02716, 4, 47, 181.6, 47.55, 0.01543, 4, 85.27, 183.65, 0.75963, 23, -22.3, 53.02, 0.19376, 44, -82.18, 535.6, 0.03118, 4, 47, 207.77, 50.94, 0.00996, 4, 111.42, 187.19, 0.7647, 23, 3.86, 56.56, 0.19367, 44, -56.02, 539.14, 0.03167, 5, 47, 241.09, 46.53, 0.00533, 4, 144.76, 182.96, 0.76321, 18, -127.2, 32.96, 0.00516, 23, 37.2, 52.33, 0.19711, 44, -22.69, 534.91, 0.02919, 6, 47, 269.35, 29.26, 0.00201, 4, 173.12, 165.85, 0.71231, 18, -98.84, 15.84, 0.10902, 23, 65.56, 35.22, 0.0835, 22, 54.49, 65.93, 0.06438, 44, 5.67, 517.8, 0.02877, 5, 47, 284.19, 28.22, 0.00103, 4, 187.96, 164.89, 0.66062, 18, -84, 14.88, 0.17498, 22, 69.33, 64.96, 0.1362, 44, 20.51, 516.83, 0.02717, 5, 47, 305.74, 7.57, 0.00013, 4, 209.63, 144.36, 0.552, 18, -62.33, -5.65, 0.31895, 22, 91, 44.44, 0.10928, 44, 42.18, 496.31, 0.01964, 3, 4, 235.98, 150.33, 0.31793, 18, -35.98, 0.32, 0.66527, 44, 68.54, 502.28, 0.0168, 3, 4, 261.25, 155.1, 0.06389, 18, -10.71, 5.1, 0.92448, 44, 93.8, 507.05, 0.01162, 2, 18, 4.04, 6.66, 0.99346, 44, 108.55, 508.61, 0.00654, 1, 18, 13.89, 5.32, 1, 2, 4, 288.81, 138.72, 0.05505, 18, 16.85, -11.29, 0.94495, 5, 4, 308.78, 137.88, 0.03776, 5, 0.43, 138.11, 0.08851, 6, -20.12, 141.66, 0.04598, 18, 36.82, -12.13, 0.8251, 43, -364.21, 207.65, 0.00265, 5, 4, 332.38, 127.13, 0.0206, 5, 24.59, 128.68, 0.18503, 6, 2.7, 129.33, 0.29021, 18, 60.42, -22.88, 0.50039, 43, -341.39, 195.32, 0.00376, 5, 4, 344.37, 144.6, 0.00154, 5, 35.59, 146.79, 0.156, 6, 15.84, 145.95, 0.39069, 18, 72.41, -5.41, 0.44304, 43, -328.25, 211.94, 0.00873, 4, 5, 52.39, 149.88, 0.14392, 6, 32.89, 146.95, 0.45345, 18, 89.35, -3.25, 0.393, 43, -311.2, 212.95, 0.00962, 4, 5, 77.29, 142.5, 0.11783, 6, 56.7, 136.58, 0.57097, 18, 113.81, -11.99, 0.30246, 43, -287.39, 202.57, 0.00874, 4, 5, 111.9, 127.09, 0.06615, 6, 89.15, 117.03, 0.79049, 18, 147.51, -29.29, 0.13514, 43, -254.93, 183.03, 0.00822, 4, 5, 139.11, 118.98, 0.03156, 6, 115.16, 105.64, 0.92387, 18, 174.23, -38.88, 0.03551, 43, -228.93, 171.64, 0.00906, 3, 5, 168.51, 104.56, 0.00975, 6, 142.57, 87.72, 0.98156, 43, -201.52, 153.71, 0.00869, 3, 5, 193.25, 88.79, 0.00168, 6, 165.18, 69.03, 0.99053, 43, -178.9, 135.03, 0.00779, 2, 6, 183.95, 42.28, 0.99273, 43, -160.14, 108.27, 0.00727, 2, 6, 192.03, 13.42, 0.99317, 43, -152.06, 79.41, 0.00683, 3, 4, 355.57, 37.04, 0.01403, 5, 52.71, 40.01, 0.22209, 6, 19.72, 37.87, 0.76388, 3, 4, 351.94, -39.17, 0.00971, 5, 53.29, -36.28, 0.25303, 6, 10.93, -37.91, 0.73727, 2, 4, 312.25, 38.17, 0.62713, 5, 9.4, 38.74, 0.37287, 3, 4, 315.96, -37.68, 0.4167, 5, 17.28, -36.79, 0.51944, 6, -24.87, -33.99, 0.06386, 3, 4, 333.86, -38.25, 0.08009, 5, 35.2, -36.36, 0.42686, 6, -7.04, -35.77, 0.49306, 3, 4, 334.08, 37.37, 0.12251, 5, 31.23, 39.15, 0.41721, 6, -1.7, 39.66, 0.46028, 3, 4, 300.6, -54.48, 0.70966, 5, 2.88, -54.4, 0.17084, 17, 36.7, 92.33, 0.1195, 3, 4, 293.43, 111.69, 0.24226, 18, 21.47, -38.32, 0.73994, 44, 125.99, 463.64, 0.0178, 3, 4, 298.79, 79.93, 0.55931, 18, 26.83, -70.08, 0.42381, 44, 131.35, 431.87, 0.01688, 5, 4, 347.67, 81.62, 0.04973, 5, 42.37, 84.09, 0.21385, 6, 14.87, 82.89, 0.53048, 18, 75.71, -68.39, 0.19543, 42, -390.3, 148.88, 0.01051, 5, 4, 351.15, 71.7, 0.04533, 5, 46.39, 74.38, 0.22292, 6, 17.67, 72.76, 0.59826, 18, 79.19, -78.31, 0.11313, 42, -387.5, 138.75, 0.02037, 4, 4, 379.41, 47.22, 0.00074, 5, 75.96, 51.49, 0.1368, 6, 44.2, 46.41, 0.81854, 42, -360.97, 112.41, 0.04392, 3, 5, 90.77, 42.76, 0.06529, 6, 57.82, 35.93, 0.89161, 42, -347.34, 101.93, 0.04309, 3, 5, 116.1, 32.75, 0.01376, 6, 81.74, 22.88, 0.94251, 42, -323.43, 88.88, 0.04372, 3, 5, 135.43, 27.69, 0.00269, 6, 100.3, 15.5, 0.95493, 42, -304.87, 81.49, 0.04238, 3, 5, 144.65, 23.43, 0.00036, 6, 108.93, 10.14, 0.9616, 42, -296.24, 76.13, 0.03804, 3, 5, 153.82, 15.46, 0, 6, 117.05, 1.1, 0.96649, 42, -288.12, 67.09, 0.0335, 2, 6, 119.66, -8.47, 0.97169, 42, -285.51, 57.52, 0.02831, 2, 6, 116.28, -19.51, 0.97683, 42, -288.89, 46.48, 0.02317, 3, 5, 97.74, 37.87, 0.04263, 6, 64.14, 30.22, 0.91308, 42, -341.02, 96.22, 0.04428, 4, 4, 362.69, 57.25, 0.0147, 5, 58.71, 60.59, 0.21645, 6, 28.2, 57.56, 0.7313, 42, -376.97, 123.56, 0.03755, 4, 5, 133.42, -29.4, 0.02045, 6, 91.3, -40.92, 0.9531, 17, 168.42, 110.09, 0.0019, 42, -313.87, 25.08, 0.02455, 4, 5, 112.26, -44.4, 0.07081, 6, 68.46, -53.2, 0.90504, 17, 146.47, 96.29, 0.00815, 42, -336.71, 12.79, 0.01599, 4, 5, 91.98, -49.27, 0.14321, 6, 47.73, -55.55, 0.83692, 17, 125.95, 92.54, 0.0062, 42, -357.43, 10.44, 0.01368, 4, 5, 101.39, -46.06, 0.10223, 6, 57.47, -53.52, 0.87069, 17, 135.52, 95.22, 0.01235, 42, -347.7, 12.47, 0.01473, 4, 5, 102.07, -50.43, 0.1078, 6, 57.6, -57.94, 0.86795, 17, 135.96, 90.82, 0.01289, 42, -347.56, 8.05, 0.01136, 4, 5, 115.53, -59.15, 0.07957, 6, 69.89, -68.25, 0.90204, 17, 148.92, 81.37, 0.00853, 42, -335.27, -2.25, 0.00986, 2, 6, 112.79, -10.27, 0.97304, 42, -292.38, 55.72, 0.02696, 6, 4, 335.2, 103.39, 0.05235, 5, 28.71, 105.14, 0.18103, 6, 3.9, 105.46, 0.36392, 18, 63.24, -46.62, 0.39708, 42, -401.27, 171.45, 0.0037, 44, 167.75, 455.34, 0.00191, 6, 4, 314.37, 113.13, 0.09571, 5, 7.37, 113.71, 0.10629, 6, -16.22, 116.58, 0.15921, 18, 42.41, -36.88, 0.62959, 42, -421.39, 182.58, 0.00154, 44, 146.92, 465.08, 0.00766, 6, 4, 322.33, 81.26, 0.22412, 5, 17.08, 82.33, 0.14777, 6, -10.44, 84.25, 0.28344, 18, 50.37, -68.75, 0.33735, 42, -415.61, 150.24, 0.00328, 44, 154.89, 433.21, 0.00403, 3, 4, 304.67, 57.28, 0.82004, 5, 0.78, 57.42, 0.07767, 18, 32.71, -92.72, 0.10229, 5, 4, 333, 59.26, 0.18746, 5, 28.95, 60.95, 0.27772, 6, -1.29, 61.57, 0.46532, 18, 61.04, -90.75, 0.06488, 43, -345.38, 127.57, 0.00462, 6, 4, 353.21, 123.2, 0.00469, 5, 45.6, 125.91, 0.1722, 6, 23.21, 124, 0.43853, 18, 81.24, -26.81, 0.38268, 42, -381.96, 189.99, 0.00158, 43, -320.88, 189.99, 0.00031, 4, 5, 96.33, 102.61, 0.09385, 6, 70.7, 94.65, 0.80216, 18, 130.61, -52.87, 0.08888, 42, -334.47, 160.64, 0.01512, 3, 5, 117.76, 94.09, 0.05019, 6, 90.92, 83.57, 0.93307, 42, -314.25, 149.56, 0.01674, 3, 5, 149.94, 84.48, 0.01221, 6, 121.67, 70.07, 0.97135, 42, -283.5, 136.06, 0.01644, 3, 5, 178.03, 69.83, 0.00165, 6, 147.75, 52.08, 0.9829, 42, -257.42, 118.08, 0.01545, 3, 6, 159.93, -16.46, 0.989, 17, 235.24, 139.15, 0, 42, -245.23, 49.54, 0.011, 2, 6, 138.81, -25.13, 0.9793, 42, -266.36, 40.87, 0.0207, 2, 6, 143.24, -2.26, 0.97285, 42, -261.93, 63.74, 0.02715, 2, 6, 139.16, 16.19, 0.97159, 42, -266.01, 82.19, 0.02841, 3, 5, 156.89, 48.21, 0.0016, 6, 124.12, 33.23, 0.96792, 42, -281.05, 99.22, 0.03048, 3, 5, 131.37, 59.86, 0.01464, 6, 100.22, 47.91, 0.95079, 42, -304.95, 113.91, 0.03457, 5, 4, 377.69, 110.95, 0.00028, 5, 70.72, 115.03, 0.13962, 6, 46.81, 110.12, 0.59126, 18, 105.73, -39.06, 0.25953, 42, -358.36, 176.11, 0.00931, 5, 4, 364.41, 96.76, 0.00798, 5, 58.25, 100.13, 0.19233, 6, 32.59, 96.86, 0.56518, 18, 92.45, -53.25, 0.22252, 42, -372.57, 162.85, 0.01199, 5, 4, 380.25, 75.34, 0.00245, 5, 75.24, 79.62, 0.15818, 6, 46.95, 74.42, 0.75802, 18, 108.29, -74.67, 0.0542, 42, -358.22, 140.41, 0.02715, 3, 5, 101.76, 65.07, 0.06224, 6, 71.47, 56.73, 0.90414, 42, -333.69, 122.72, 0.03363, 2, 6, 165.26, 31.57, 0.98593, 42, -239.91, 97.57, 0.01407, 2, 6, 170.36, 6.96, 0.98604, 42, -234.81, 72.96, 0.01396, 3, 6, 165.45, -36.89, 0.98612, 17, 242.13, 119.14, 0, 42, -239.72, 29.11, 0.01388, 2, 6, 136.44, -38.75, 0.97762, 42, -268.73, 27.25, 0.02238, 4, 5, 154.22, -40.07, 0.00361, 6, 110.64, -54.06, 0.97299, 17, 188.61, 98.29, 0.00012, 42, -294.53, 11.93, 0.02327, 4, 5, 131.87, -56.35, 0.03801, 6, 86.45, -67.47, 0.94411, 17, 165.39, 83.27, 0.00332, 42, -318.72, -1.48, 0.01456, 3, 5, 184.82, -36.62, 9e-05, 6, 141.42, -54.39, 0.98408, 42, -263.75, 11.6, 0.01583, 4, 5, 159.54, -49.16, 0.00498, 6, 114.79, -63.74, 0.97806, 17, 193.41, 88.92, 0.00016, 42, -290.38, 2.26, 0.0168, 4, 5, 139.34, -69.66, 0.03023, 6, 92.23, -81.6, 0.96074, 17, 172.11, 69.56, 0.00241, 42, -312.94, -15.61, 0.00662, 4, 5, 116.14, -79.52, 0.09681, 6, 67.99, -88.54, 0.88715, 17, 148.4, 61, 0.01034, 42, -337.17, -22.54, 0.0057, 4, 5, 87.87, -82.54, 0.21084, 6, 39.57, -88.07, 0.75733, 17, 120.01, 59.54, 0.02811, 42, -365.6, -22.07, 0.00371, 5, 4, 357.73, -86.16, 0.01273, 5, 61.68, -82.88, 0.25947, 6, 13.53, -85.19, 0.65924, 17, 93.84, 60.65, 0.06533, 42, -391.64, -19.19, 0.00322, 5, 4, 325.17, -82.03, 0.13692, 5, 28.93, -80.55, 0.26124, 6, -18.68, -78.86, 0.47585, 17, 61.27, 64.78, 0.12274, 42, -423.85, -12.86, 0.00325, 5, 4, 328.51, -58.66, 0.15891, 5, 30.98, -57.04, 0.35107, 6, -13.76, -55.77, 0.46792, 17, 64.61, 88.15, 0.01627, 43, -357.85, 10.23, 0.00584, 4, 4, 359.31, -61.19, 0.00976, 5, 61.87, -57.87, 0.25809, 6, 16.79, -60.39, 0.72638, 43, -327.3, 5.61, 0.00577, 3, 5, 91.27, -61.9, 0.17143, 6, 45.47, -68, 0.807, 17, 124.54, 79.96, 0.02156, 2, 5, 72.57, 37.67, 0.1348, 6, 39.14, 33.12, 0.8652, 2, 5, 76.26, -33.34, 0.15947, 6, 34.08, -37.82, 0.84053, 3, 4, 271.82, 126.42, 0.17044, 18, -0.14, -23.59, 0.80987, 44, 104.37, 478.37, 0.01969, 4, 4, 244.35, 126.44, 0.32747, 18, -27.61, -23.57, 0.6118, 22, 125.72, 26.52, 0.03821, 44, 76.9, 478.39, 0.02252, 3, 4, 201.94, 61.83, 0.768, 22, 83.31, -38.09, 0.192, 44, 34.49, 413.78, 0.04, 3, 4, 197.73, -14.62, 0.768, 19, 85.25, 23.16, 0.192, 44, 30.28, 337.33, 0.04, 5, 46, 265.29, 32.37, 0.0002, 4, 176.7, -92.25, 0.67579, 17, -87.2, 54.56, 0.09978, 19, 64.22, -54.47, 0.19394, 44, 9.25, 259.7, 0.0303, 4, 4, 183.59, 29.68, 0.7776, 22, 64.96, -70.24, 0.096, 19, 71.11, 67.47, 0.0864, 44, 16.14, 381.63, 0.04, 5, 47, 129.17, -58.67, 0.07132, 3, 116.48, 76.08, 0.06098, 4, 33.43, 77.14, 0.6357, 22, -85.2, -22.78, 0.192, 44, -134.02, 429.09, 0.04, 5, 47, 168.62, -88.49, 0.0132, 4, 73.05, 47.54, 0.7644, 22, -45.58, -52.38, 0.096, 19, -39.42, 85.32, 0.0864, 44, -94.4, 399.49, 0.04, 4, 47, 129.67, -115.38, 0.01044, 4, 34.25, 20.43, 0.75756, 19, -78.23, 58.21, 0.192, 44, -133.2, 372.38, 0.04, 5, 46, 108.43, 109.37, 0.01264, 3, 99.62, -16.71, 0.03948, 4, 19.41, -16.12, 0.71588, 19, -93.07, 21.66, 0.192, 44, -148.04, 335.83, 0.04, 5, 46, 115.27, 58.94, 0.09343, 3, 105.19, -67.29, 0.11011, 4, 26.53, -66.51, 0.56738, 19, -85.95, -28.73, 0.19273, 44, -140.92, 285.43, 0.03636, 2, 4, 277.37, -45.47, 0.97673, 44, 109.92, 306.48, 0.02327, 3, 4, 273.26, -77.19, 0.60623, 17, 9.36, 69.62, 0.37222, 44, 105.82, 274.76, 0.02155, 3, 4, 267.64, -120.09, 0.17148, 17, 3.74, 26.72, 0.81203, 44, 100.19, 231.85, 0.01649, 4, 4, 241.22, -93.25, 0.48578, 17, -22.68, 53.56, 0.44595, 22, 122.6, -193.17, 0.03911, 44, 73.78, 258.7, 0.02916, 4, 4, 238.31, -115.57, 0.28948, 17, -25.59, 31.24, 0.66322, 22, 119.68, -215.49, 0.02493, 44, 70.86, 236.38, 0.02237, 4, 4, 210.71, -104.36, 0.49836, 17, -53.19, 42.45, 0.36358, 22, 92.08, -204.29, 0.11406, 44, 43.26, 247.59, 0.024, 4, 46, 222.62, 12.7, 0.00479, 4, 134.13, -112.15, 0.77579, 19, 21.66, -74.37, 0.19515, 44, -33.31, 239.8, 0.02427, 5, 46, 143.57, 30.63, 0.0556, 3, 132.77, -96.3, 0.00984, 4, 54.98, -94.66, 0.71125, 19, -57.49, -56.88, 0.19417, 44, -112.47, 257.28, 0.02915, 4, 46, 179.12, 15.36, 0.02035, 4, 90.62, -109.74, 0.7575, 19, -21.86, -71.96, 0.19446, 44, -76.83, 242.21, 0.02769, 4, 46, 172.45, 78.81, 0.00996, 4, 83.6, -46.33, 0.68379, 21, -4.56, 0.73, 0.23125, 44, -83.85, 305.62, 0.075, 4, 47, 188.4, 19.58, 0.01432, 4, 92.22, 155.72, 0.69344, 24, -5.71, 2.71, 0.23592, 44, -75.22, 507.67, 0.05632, 5, 47, 270.79, 13.71, 0.00136, 4, 174.65, 150.3, 0.67665, 18, -97.31, 0.29, 0.0922, 23, 67.09, 19.67, 0.19255, 44, 7.2, 502.25, 0.03724, 4, 47, 246.58, -56.85, 0.00169, 4, 150.83, 79.61, 0.75431, 23, 43.26, -51.02, 0.189, 44, -16.62, 431.56, 0.055, 4, 47, 270.5, -28.2, 0.00094, 4, 174.59, 108.39, 0.75916, 23, 67.03, -22.24, 0.19002, 44, 7.14, 460.34, 0.04988, 3, 4, 149.29, 6.99, 0.756, 20, 47.35, 48.73, 0.189, 44, -18.15, 358.94, 0.055, 3, 4, 170.15, -28.08, 0.756, 20, 68.21, 13.66, 0.189, 44, 2.7, 323.87, 0.055, 4, 46, 251.48, 52.6, 0.00045, 4, 162.77, -72.09, 0.75746, 20, 60.83, -30.35, 0.18948, 44, -4.67, 279.86, 0.05262, 4, 46, 221.22, 32.06, 0.00425, 4, 132.63, -92.8, 0.7577, 20, 30.69, -51.06, 0.19049, 44, -34.82, 259.14, 0.04756, 5, 46, 136.6, 48.3, 0.061, 3, 126.25, -78.46, 0.02874, 4, 47.91, -77.03, 0.67076, 20, -54.02, -35.29, 0.19012, 44, -119.53, 274.92, 0.04938, 5, 46, 123.83, 84.26, 0.03837, 3, 114.39, -42.2, 0.03707, 4, 34.95, -41.15, 0.68191, 20, -66.99, 0.59, 0.18934, 44, -132.5, 310.8, 0.05331, 4, 47, 137.74, -133.5, 0.00039, 4, 42.42, 2.36, 0.75561, 20, -59.52, 44.1, 0.189, 44, -125.03, 354.3, 0.055, 4, 47, 168.39, -110.94, 0.00464, 4, 72.94, 25.09, 0.75136, 20, -28.99, 66.83, 0.189, 44, -94.5, 377.03, 0.055, 4, 47, 213.09, -112.2, 0.00075, 4, 117.65, 24.07, 0.75525, 20, 15.71, 65.82, 0.189, 44, -49.8, 376.02, 0.055, 4, 47, 208.42, -63.13, 0.00676, 4, 112.71, 73.12, 0.74924, 23, 5.14, -57.51, 0.189, 44, -54.74, 425.07, 0.055, 4, 47, 168.24, -57.72, 0.02505, 4, 72.5, 78.31, 0.73095, 23, -35.07, -52.32, 0.189, 44, -94.95, 430.26, 0.055, 5, 47, 142.7, -37.87, 0.05624, 3, 130.53, 96.54, 0.01501, 4, 46.85, 98.01, 0.68706, 23, -60.72, -32.62, 0.18958, 44, -120.6, 449.96, 0.05211, 5, 47, 134.09, -7.43, 0.05088, 3, 122.67, 127.18, 0.00715, 4, 38.06, 128.4, 0.71108, 23, -69.5, -2.23, 0.19228, 44, -129.38, 480.35, 0.03862, 5, 47, 220.53, -92.97, 0.00174, 4, 124.98, 43.35, 0.77586, 22, 6.35, -56.58, 0.096, 19, 12.51, 81.13, 0.0864, 44, -42.47, 395.29, 0.04, 4, 47, 235.78, 28.98, 0.00546, 4, 139.56, 165.38, 0.75634, 24, 41.62, 12.37, 0.19045, 44, -27.89, 517.33, 0.04774, 4, 47, 234.34, -0.58, 0.00519, 4, 138.27, 135.81, 0.74523, 24, 40.34, -17.19, 0.1876, 44, -29.17, 487.76, 0.06198, 4, 47, 207.4, -21.04, 0.01049, 4, 111.45, 115.21, 0.73457, 24, 13.52, -37.8, 0.18627, 44, -56, 467.15, 0.06867, 4, 47, 172.95, -18.61, 0.02498, 4, 76.99, 117.44, 0.72196, 24, -20.94, -35.57, 0.18673, 44, -90.45, 469.39, 0.06633, 4, 47, 156.07, 1.11, 0.03113, 4, 60, 137.06, 0.72736, 24, -37.94, -15.94, 0.18962, 44, -107.45, 489.01, 0.05189, 3, 4, 271.14, 87.52, 0.57879, 18, -0.82, -62.49, 0.39505, 44, 103.7, 439.47, 0.02616, 2, 4, 276.93, 53.42, 0.975, 44, 109.49, 405.37, 0.025, 4, 4, 236.32, 93.34, 0.58902, 18, -35.64, -56.66, 0.28456, 22, 117.69, -6.58, 0.09297, 44, 68.88, 445.29, 0.03345, 3, 4, 236.46, 59.24, 0.87572, 22, 117.83, -40.68, 0.09048, 44, 69.01, 411.19, 0.03381, 3, 4, 233.16, -42.68, 0.88076, 22, 114.53, -142.6, 0.08637, 44, 65.71, 309.27, 0.03287, 3, 4, 196.37, -55.42, 0.768, 19, 83.89, -17.64, 0.192, 44, 28.92, 296.53, 0.04, 3, 4, 306.28, -14.45, 0.51496, 5, 6.34, -14.13, 0.4641, 44, 138.83, 337.49, 0.02094, 4, 4, 330.56, -15.41, 0.04616, 5, 30.64, -13.74, 0.64427, 6, -8.79, -12.76, 0.28913, 44, 163.11, 336.54, 0.02044, 3, 5, 53.12, -13.35, 0.12984, 6, 13.57, -15.13, 0.85012, 44, 185.58, 335.68, 0.02004, 3, 5, 28.68, 15.63, 0.73124, 6, -7.12, 16.63, 0.24825, 44, 162.78, 365.97, 0.02051, 3, 5, 52.89, 17.35, 0.11918, 6, 17.11, 15.37, 0.86051, 44, 187.04, 366.35, 0.0203, 3, 4, 304.95, 15.14, 0.69897, 5, 3.38, 15.35, 0.28085, 44, 137.5, 367.09, 0.02018, 2, 4, 275.98, 20.29, 0.97482, 44, 108.53, 372.24, 0.02518, 2, 4, 289.48, 33.17, 0.98059, 44, 122.03, 385.12, 0.01941, 3, 4, 295.12, -30.47, 0.85757, 5, -3.92, -30.73, 0.12377, 44, 127.68, 321.48, 0.01866, 3, 4, 228.91, 18.05, 0.86015, 22, 110.29, -81.88, 0.10453, 44, 61.47, 369.99, 0.03532, 5, 47, 299.58, -46.65, 1e-05, 4, 203.77, 90.11, 0.69158, 18, -68.19, -59.9, 0.07604, 22, 85.15, -9.81, 0.19191, 44, 36.33, 442.06, 0.04046, 2, 4, 261.67, 11.67, 0.97183, 44, 94.23, 363.62, 0.02817, 3, 4, 245.82, -24.8, 0.91965, 22, 127.19, -124.72, 0.04978, 44, 78.37, 327.15, 0.03058, 2, 4, 273.59, 0.97, 0.97465, 44, 106.14, 352.92, 0.02535, 3, 4, 249.24, 46.54, 0.92115, 22, 130.62, -53.39, 0.04784, 44, 81.8, 398.48, 0.03102, 4, 4, 297.42, -44.27, 0.84206, 5, -0.86, -44.39, 0.13917, 17, 33.52, 102.54, 0.00542, 44, 129.97, 307.67, 0.01335, 2, 4, 296.39, 44.65, 0.98314, 44, 128.94, 396.6, 0.01686, 6, 47, 289.3, -17.15, 0.00029, 4, 193.32, 119.55, 0.64533, 18, -78.64, -30.46, 0.13121, 23, 85.76, -11.08, 0.09591, 22, 74.7, 19.62, 0.08632, 44, 25.88, 471.49, 0.04094, 4, 46, 220.92, 88.53, 0.00075, 4, 132.01, -36.33, 0.74325, 21, 43.85, 10.73, 0.186, 44, -35.44, 315.61, 0.07, 4, 46, 217.11, 55.1, 0.00344, 4, 128.39, -69.79, 0.74493, 21, 40.22, -22.73, 0.18709, 44, -39.06, 282.16, 0.06453, 4, 46, 190.93, 40.19, 0.01226, 4, 102.29, -84.84, 0.7387, 21, 14.13, -37.78, 0.18774, 44, -65.15, 267.11, 0.0613, 4, 46, 179.03, 27.93, 0.01964, 4, 90.45, -97.17, 0.74217, 20, -11.48, -55.43, 0.19045, 44, -76.99, 254.78, 0.04773, 4, 46, 198.91, 115.95, 0.00024, 4, 109.85, -9.04, 0.74376, 21, 21.69, 38.03, 0.186, 44, -57.59, 342.91, 0.07, 4, 46, 161.73, 120.95, 0.00041, 4, 72.64, -4.24, 0.74359, 21, -15.52, 42.82, 0.186, 44, -94.8, 347.71, 0.07, 4, 46, 141.83, 106.36, 0.00628, 4, 52.82, -18.95, 0.73772, 21, -35.34, 28.11, 0.186, 44, -114.62, 333, 0.07, 5, 46, 138.85, 71.77, 0.03832, 3, 129.09, -55.06, 0.00423, 4, 50.03, -53.56, 0.70606, 21, -38.13, -6.49, 0.18715, 44, -117.41, 298.39, 0.06424, 4, 46, 157.14, 49.2, 0.03276, 4, 68.45, -76.02, 0.71759, 21, -19.71, -28.96, 0.18759, 44, -99, 275.93, 0.06206, 4, 4, 219.13, 124.1, 0.51575, 18, -52.83, -25.91, 0.35011, 22, 100.5, 24.18, 0.10479, 44, 51.68, 476.05, 0.02935, 6, 2, 224.15, 81.8, 0.00031, 47, 107.74, -55.84, 0.13787, 3, 95.13, 79.45, 0.17637, 4, 11.99, 79.85, 0.55344, 22, -106.64, -20.07, 0.09644, 44, -155.46, 431.8, 0.03556, 7, 2, 214.99, 44.47, 0.00107, 47, 98.58, -93.17, 0.0729, 3, 85.03, 42.36, 0.29616, 4, 3.03, 42.47, 0.49627, 22, -115.6, -57.45, 0.048, 19, -109.45, 80.25, 0.0456, 44, -164.42, 394.42, 0.04, 6, 47, 131.06, -88.56, 0.03984, 3, 117.62, 46.16, 0.02652, 4, 35.49, 47.26, 0.74618, 22, -83.14, -52.66, 0.07066, 19, -76.99, 85.04, 0.0768, 44, -131.96, 399.21, 0.04, 5, 46, 102.74, 64.57, 0.12469, 3, 92.81, -61.35, 0.20327, 4, 13.96, -60.95, 0.54056, 19, -98.51, -23.17, 0.0965, 44, -153.48, 291, 0.03498, 5, 46, 94.61, 113.6, 0.01225, 3, 85.91, -12.13, 0.22498, 4, 5.57, -11.97, 0.62677, 19, -106.91, 25.82, 0.096, 44, -161.88, 339.98, 0.04, 6, 2, 221.92, -75.38, 2e-05, 46, 99.31, 48.21, 0.18078, 3, 88.98, -77.62, 0.21142, 4, 10.63, -77.33, 0.48342, 19, -101.84, -39.55, 0.09729, 44, -156.81, 274.62, 0.02706, 5, 46, 123.54, 36.21, 0.10133, 3, 112.9, -90.23, 0.07956, 4, 34.92, -89.2, 0.64509, 19, -77.55, -51.42, 0.14576, 44, -132.52, 262.75, 0.02826, 4, 47, 209.33, 42.04, 0.00861, 4, 113.03, 178.29, 0.7576, 24, 15.1, 25.28, 0.19155, 44, -54.42, 530.24, 0.04224, 4, 47, 181.26, 39.9, 0.01582, 4, 84.97, 175.99, 0.73788, 24, -12.96, 22.99, 0.20345, 44, -82.47, 527.94, 0.04286, 4, 47, 159.23, 25.15, 0.02619, 4, 63.03, 161.12, 0.73842, 24, -34.91, 8.12, 0.19115, 44, -104.42, 513.07, 0.04423], "hull": 60, "edges": [0, 118, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 50, 52, 70, 72, 72, 74, 74, 76, 76, 78, 92, 94, 112, 114, 28, 30, 30, 32, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 52, 54, 14, 16, 10, 12, 12, 14, 6, 8, 8, 10, 0, 2, 114, 116, 116, 118, 108, 110, 110, 112, 2, 4, 4, 6, 16, 18, 18, 20, 106, 108, 102, 104, 104, 106, 100, 102, 98, 100, 94, 96, 96, 98, 90, 92, 88, 90, 86, 88, 84, 86, 78, 80, 80, 82, 82, 84, 138, 140, 142, 144, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 144, 158, 158, 146, 140, 160, 160, 142, 120, 130, 130, 124, 122, 128, 128, 126, 156, 162, 162, 164, 164, 168, 168, 166, 166, 170, 172, 170, 250, 122, 248, 120, 256, 262, 262, 258, 70, 264, 264, 266, 266, 268, 268, 270, 270, 272, 260, 286, 272, 288, 286, 290, 290, 288, 40, 42, 42, 44, 296, 300, 300, 298, 302, 304, 304, 306, 306, 308, 310, 312, 314, 316, 302, 318, 318, 316, 298, 320, 320, 322, 322, 324, 324, 326, 266, 328, 330, 332, 300, 256, 300, 332, 332, 334, 334, 336, 336, 338, 326, 72, 258, 350, 350, 260, 352, 354, 356, 122, 354, 356, 120, 360, 360, 356, 358, 360, 358, 362, 362, 364, 372, 256, 378, 368, 364, 366, 370, 348, 296, 386, 386, 372, 388, 390, 390, 392, 308, 394, 394, 310, 388, 396, 396, 398, 398, 400, 400, 402, 392, 404, 404, 402, 314, 312, 48, 418, 68, 70, 342, 346, 296, 80, 422, 330, 422, 424, 426, 338, 424, 426, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54], "width": 486, "height": 795}}, "body3": {"body": {"type": "mesh", "uvs": [0.40009, 0.62676, 0.39688, 0.65387, 0.41652, 0.6925, 0.39831, 0.72064, 0.34269, 0.7476, 0.28867, 0.76507, 0.22625, 0.78672, 0.16936, 0.80473, 0.11963, 0.8231, 0.06707, 0.84644, 0.10536, 0.82029, 0.14497, 0.80383, 0.19268, 0.77906, 0.23947, 0.75645, 0.27859, 0.73954, 0.31924, 0.716, 0.34129, 0.68687, 0.33842, 0.66136, 0.3254, 0.63973, 0.38323, 0.66369], "triangles": [16, 19, 2, 19, 1, 2, 16, 17, 19, 1, 19, 18, 19, 17, 18, 1, 18, 0, 3, 16, 2, 9, 10, 8, 8, 11, 7, 8, 10, 11, 7, 12, 6, 7, 11, 12, 6, 13, 5, 6, 12, 13, 13, 14, 5, 5, 14, 4, 14, 15, 4, 4, 15, 3, 15, 16, 3], "vertices": [5, 47, 129.17, -58.67, 0.07132, 3, 116.48, 76.08, 0.06098, 4, 33.43, 77.14, 0.6357, 22, -85.2, -22.78, 0.192, 44, -134.02, 429.09, 0.04, 6, 2, 224.15, 81.8, 0.00031, 47, 107.74, -55.84, 0.13787, 3, 95.13, 79.45, 0.17637, 4, 11.99, 79.85, 0.55344, 22, -106.64, -20.07, 0.09644, 44, -155.46, 431.8, 0.03556, 5, 2, 192.93, 74.08, 0.02059, 47, 76.52, -63.55, 0.24438, 3, 63.72, 72.52, 0.39239, 4, -19.19, 71.97, 0.31166, 44, -186.63, 423.91, 0.03098, 5, 2, 171.13, 84.25, 0.05634, 47, 54.72, -53.39, 0.38833, 3, 42.18, 83.22, 0.37459, 4, -41.05, 82.01, 0.1607, 44, -208.5, 433.95, 0.02004, 5, 2, 151.33, 112.5, 0.04315, 47, 34.92, -25.14, 0.68877, 3, 23.09, 111.95, 0.18986, 4, -61.01, 110.14, 0.06827, 44, -228.45, 462.09, 0.00996, 4, 2, 139.01, 139.52, 0.00121, 47, 22.6, 1.89, 0.93282, 3, 11.45, 139.28, 0.0486, 4, -73.47, 137.1, 0.01737, 4, 2, 123.63, 170.83, 2e-05, 47, 7.22, 33.19, 0.99943, 3, -3.15, 170.96, 0.00024, 4, -89.03, 168.32, 0.00031, 3, 2, 110.98, 199.27, 0.00029, 47, -5.43, 61.64, 0.97812, 36, 110.25, 15.98, 0.02159, 3, 2, 97.82, 224.26, 0.00011, 47, -18.59, 86.63, 0.86491, 36, 82.17, 18.91, 0.13499, 3, 2, 80.81, 250.86, 2e-05, 47, -35.6, 113.22, 0.73862, 36, 50.6, 19.53, 0.26136, 2, 47, -15.95, 93.42, 0.85416, 36, 77.98, 24.88, 0.14584, 2, 47, -4.02, 73.43, 0.94688, 36, 101.23, 23.7, 0.05312, 1, 47, 14.26, 49.12, 1, 3, 47, 30.87, 25.36, 0.97408, 3, 20.3, 162.54, 0.01831, 4, -65.34, 160.62, 0.0076, 4, 2, 159.57, 143.21, 0.00244, 47, 43.16, 5.58, 0.86362, 3, 32.09, 142.46, 0.09317, 4, -52.94, 140.91, 0.04077, 4, 2, 177.08, 122.39, 0.01619, 47, 60.67, -15.25, 0.61081, 3, 49.08, 121.2, 0.22715, 4, -35.31, 120.18, 0.14584, 4, 2, 199.57, 110.32, 0.0076, 47, 83.16, -27.32, 0.35605, 3, 71.26, 108.57, 0.2711, 4, -12.76, 108.23, 0.36525, 5, 2, 219.89, 110.51, 0.00056, 47, 103.48, -27.12, 0.17986, 3, 91.58, 108.26, 0.17226, 4, 7.57, 108.54, 0.54733, 22, -111.06, 8.62, 0.1, 4, 47, 121.02, -21.82, 0.08891, 3, 109.25, 113.12, 0.06469, 4, 25.08, 113.94, 0.6464, 22, -93.55, 14.02, 0.2, 6, 2, 216.76, 88.88, 0.00173, 47, 100.35, -48.75, 0.17976, 3, 87.91, 86.72, 0.21008, 4, 4.55, 86.9, 0.50126, 22, -114.08, -13.03, 0.07764, 44, -162.89, 438.84, 0.02953], "hull": 19, "edges": [28, 30, 34, 36, 30, 32, 32, 34, 20, 22, 22, 24, 18, 20, 36, 0, 24, 26, 26, 28, 16, 18, 16, 14, 14, 12, 38, 36, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0], "width": 486, "height": 795}}, "chair0": {"chair0": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250.44, -927.1, -341.56, -927.1, -341.56, -21.1, 250.44, -21.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 592, "height": 906}}, "ear_R": {"ear_R": {"type": "mesh", "uvs": [0.36636, 0.00794, 0.68435, 0.13679, 0.95929, 0.49561, 0.95257, 0.58637, 0.81089, 0.60248, 0.82598, 0.63822, 0.84363, 0.68025, 0.86766, 0.72584, 0.91682, 0.83478, 0.89832, 0.95774, 0.76674, 1, 0.73371, 1, 0.5828, 0.95229, 0.53275, 0.83894, 0.54731, 0.72353, 0.5527, 0.68086, 0.55853, 0.63464, 0.57077, 0.58375, 0.41491, 0.51, 0.09703, 0.2574, 0.0511, 0.08337, 0.20609, 0.00525], "triangles": [15, 16, 5, 11, 12, 9, 9, 12, 8, 10, 11, 9, 13, 7, 8, 7, 14, 6, 14, 15, 6, 7, 13, 14, 12, 13, 8, 15, 5, 6, 2, 4, 17, 16, 4, 5, 19, 1, 2, 1, 20, 0, 18, 19, 2, 17, 18, 2, 0, 20, 21, 3, 4, 2, 16, 17, 4, 20, 1, 19], "vertices": [2, 6, 70.1, 73.26, 0.9871, 42, -335.07, 139.26, 0.0129, 2, 6, 57.18, 65.23, 0.98589, 42, -347.99, 131.22, 0.01411, 2, 6, 23.41, 61.04, 0.98626, 42, -381.76, 127.04, 0.01374, 2, 6, 15.14, 62.25, 0.9869, 42, -390.02, 128.25, 0.0131, 2, 6, 14.19, 66.65, 0.98805, 42, -390.98, 132.65, 0.01195, 3, 6, 10.87, 66.6, 0.59856, 16, 4.06, 4.18, 0.3936, 42, -394.3, 132.6, 0.00783, 3, 6, 6.97, 66.54, 0.16608, 16, 7.95, 4.53, 0.83044, 42, -398.2, 132.54, 0.00348, 2, 16, 12.17, 5.05, 0.99886, 42, -402.45, 132.33, 0.00114, 2, 16, 22.25, 6.05, 0.99832, 42, -412.58, 132.08, 0.00168, 2, 16, 33.52, 4.95, 0.99881, 42, -423.74, 134, 0.00119, 1, 16, 37.22, 0.83, 1, 1, 16, 37.17, -0.16, 1, 1, 16, 32.57, -4.48, 1, 1, 16, 22.09, -5.48, 1, 1, 16, 11.5, -4.54, 1, 3, 6, 7.97, 75.21, 0.15189, 16, 7.59, -4.19, 0.84666, 42, -397.2, 141.21, 0.00144, 3, 6, 12.17, 74.52, 0.57606, 16, 3.35, -3.81, 0.41785, 42, -393, 140.52, 0.00609, 2, 6, 16.77, 73.59, 0.98973, 42, -388.4, 139.59, 0.01027, 2, 6, 24.07, 77.41, 0.99, 42, -381.09, 143.41, 0.01, 2, 6, 48.3, 84.06, 0.99, 42, -356.87, 150.06, 0.01, 2, 6, 64.36, 83.49, 0.99, 42, -340.81, 149.48, 0.01, 2, 6, 70.93, 78, 0.98823, 42, -334.24, 144, 0.01177], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 18, 20, 20, 22, 22, 24, 24, 26, 32, 34, 38, 40, 40, 42, 4, 6, 6, 8, 12, 14, 16, 18, 14, 16, 34, 36, 36, 38, 30, 32, 26, 28, 28, 30, 8, 10, 10, 12], "width": 30, "height": 92}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.04102, 0.52619, 0.09534, 0.88835, 0.59248, 0.91732, 0.9283, 0.50688, 0.89373, 0.08919, 0.37683, 0.05297], "triangles": [2, 1, 0, 3, 2, 5, 3, 5, 4, 2, 0, 5], "vertices": [2, 6, 46.09, 0.82, 0.96, 42, -359.08, 66.82, 0.04, 2, 6, 35.01, -0.23, 0.96, 42, -370.15, 65.76, 0.04, 2, 6, 31.5, -21.84, 0.96, 42, -373.67, 44.15, 0.04, 2, 6, 41.93, -38, 0.96217, 42, -363.24, 27.99, 0.03783, 2, 6, 54.56, -38.01, 0.96261, 42, -350.61, 27.99, 0.03739, 2, 6, 58.39, -15.57, 0.96, 42, -346.78, 50.43, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 44, "height": 30}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.05581, 0.28578, 0.08215, 0.74607, 0.48216, 0.93532, 0.94144, 0.72037, 0.94144, 0.35354, 0.41796, 0.06148], "triangles": [4, 3, 2, 2, 1, 5, 1, 0, 5, 2, 5, 4], "vertices": [2, 6, 54.08, 58.63, 0.9621, 42, -351.09, 124.62, 0.0379, 2, 6, 39.77, 59.21, 0.96229, 42, -365.39, 125.2, 0.03771, 2, 6, 31.82, 42.45, 0.96, 42, -373.35, 108.44, 0.04, 2, 6, 35.98, 21.58, 0.96, 42, -369.18, 87.58, 0.04, 2, 6, 47.27, 20.2, 0.96, 42, -357.9, 86.2, 0.04, 2, 6, 59.05, 41.97, 0.96, 42, -346.12, 107.96, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 44, "height": 31}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 13, -8.43, -6.42, 0.96, 42, -365.57, 40.21, 0.04, 2, 13, -6.61, 8.47, 0.96, 42, -363.75, 55.1, 0.04, 2, 13, 8.28, 6.66, 0.96, 42, -348.86, 53.28, 0.04, 2, 13, 6.46, -8.23, 0.96, 42, -350.68, 38.39, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 15}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.90999, 0.10639, 0.98872, 0.24056, 0.84585, 0.44969, 0.84327, 0.63083, 0.73856, 0.80099, 0.60942, 0.90764, 0.46695, 0.95787, 0.31244, 0.95391, 0.2023, 0.9076, 0.11454, 0.85776, 0.0391, 0.85979, 0.03413, 0.77819, 0.07606, 0.68345, 0.13382, 0.54106, 0.20969, 0.40636, 0.31587, 0.29878, 0.44343, 0.25571, 0.54951, 0.25888, 0.59321, 0.07955, 0.7827, 0.04275, 0.0998, 0.77038, 0.15084, 0.64002, 0.23798, 0.49705, 0.35151, 0.40314, 0.49497, 0.37511, 0.61555, 0.39893, 0.71878, 0.46061, 0.66686, 0.6148, 0.5583, 0.76178, 0.4254, 0.82625, 0.28546, 0.82205, 0.18601, 0.78561], "triangles": [24, 16, 17, 25, 18, 19, 17, 18, 25, 24, 17, 25, 23, 15, 16, 23, 16, 24, 14, 15, 23, 2, 19, 0, 2, 0, 1, 26, 25, 19, 2, 26, 19, 22, 14, 23, 13, 14, 22, 27, 25, 26, 3, 26, 2, 27, 26, 3, 21, 13, 22, 12, 13, 21, 28, 24, 25, 28, 25, 27, 20, 12, 21, 11, 12, 20, 31, 21, 22, 20, 21, 31, 4, 27, 3, 28, 27, 4, 30, 22, 23, 31, 22, 30, 29, 23, 24, 29, 24, 28, 30, 23, 29, 9, 20, 31, 10, 11, 20, 10, 20, 9, 8, 31, 30, 9, 31, 8, 5, 28, 4, 7, 30, 29, 8, 30, 7, 6, 29, 28, 6, 28, 5, 7, 29, 6], "vertices": [2, 6, 55.88, -42.01, 0.96475, 42, -349.28, 23.99, 0.03525, 2, 6, 51.88, -44.93, 0.96584, 42, -353.29, 21.07, 0.03416, 2, 6, 47.02, -38.14, 0.96202, 42, -358.15, 27.85, 0.03798, 2, 6, 42.18, -37.44, 0.96244, 42, -362.99, 28.55, 0.03756, 2, 6, 38.16, -32.42, 0.9606, 42, -367.01, 33.58, 0.0394, 2, 6, 35.98, -26.55, 0.96, 42, -369.19, 39.44, 0.04, 2, 6, 35.37, -20.31, 0.96, 42, -369.8, 45.69, 0.04, 2, 6, 36.28, -13.73, 0.96, 42, -368.88, 52.27, 0.04, 2, 6, 38.1, -9.18, 0.96, 42, -367.07, 56.82, 0.04, 2, 6, 39.89, -5.59, 0.96, 42, -365.28, 60.4, 0.04, 2, 6, 40.23, -2.37, 0.96, 42, -364.94, 63.63, 0.04, 2, 6, 42.44, -2.42, 0.96, 42, -362.72, 63.57, 0.04, 2, 6, 44.76, -4.52, 0.96, 42, -360.4, 61.47, 0.04, 2, 6, 48.28, -7.45, 0.96, 42, -356.89, 58.54, 0.04, 2, 6, 51.49, -11.13, 0.96, 42, -353.67, 54.86, 0.04, 2, 6, 53.82, -16.02, 0.96, 42, -351.34, 49.98, 0.04, 2, 6, 54.31, -21.6, 0.96, 42, -350.85, 44.39, 0.04, 2, 6, 53.68, -26.12, 0.96, 42, -351.49, 39.88, 0.04, 2, 6, 58.25, -28.57, 0.96, 42, -346.91, 37.42, 0.04, 2, 6, 58.25, -36.78, 0.96219, 42, -346.92, 29.21, 0.03781, 2, 6, 42.31, -5.25, 0.96, 42, -362.86, 60.74, 0.04, 2, 6, 45.54, -7.86, 0.96, 42, -359.63, 58.14, 0.04, 2, 6, 48.92, -12.04, 0.96, 42, -356.25, 53.95, 0.04, 2, 6, 50.84, -17.2, 0.96, 42, -354.33, 48.8, 0.04, 2, 6, 50.84, -23.41, 0.96, 42, -354.32, 42.58, 0.04, 2, 6, 49.58, -28.48, 0.96, 42, -355.59, 37.51, 0.04, 2, 6, 47.39, -32.68, 0.96, 42, -357.78, 33.31, 0.04, 2, 6, 43.53, -29.96, 0.96, 42, -361.64, 36.03, 0.04, 2, 6, 40.15, -24.85, 0.96, 42, -365.02, 41.15, 0.04, 2, 6, 39.12, -18.97, 0.96, 42, -366.05, 47.03, 0.04, 2, 6, 39.96, -13.01, 0.96, 42, -365.21, 52.99, 0.04, 2, 6, 41.45, -8.88, 0.96, 42, -363.72, 57.11, 0.04], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 20, 22, 26, 28, 28, 30, 36, 38, 34, 36, 12, 14, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 40, 22, 24, 24, 26, 16, 18, 18, 20, 8, 10, 10, 12, 30, 32, 32, 34], "width": 43, "height": 27}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 14, -8.46, -6.29, 0.96, 42, -365.49, 98.63, 0.04, 2, 14, -6.65, 8.6, 0.96, 42, -363.67, 113.52, 0.04, 2, 14, 8.24, 6.78, 0.96, 42, -348.78, 111.7, 0.04, 2, 14, 6.43, -8.11, 0.96, 42, -350.6, 96.82, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 15}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.36923, 0.10259, 0.41141, 0.20496, 0.51555, 0.16108, 0.63039, 0.16261, 0.74085, 0.22567, 0.83765, 0.35014, 0.90483, 0.47462, 0.96627, 0.56831, 0.96133, 0.67449, 0.88225, 0.69831, 0.79476, 0.82126, 0.67623, 0.91403, 0.50976, 0.93963, 0.3428, 0.92237, 0.20623, 0.75146, 0.16052, 0.56951, 1e-05, 0.39451, 0.08994, 0.15956, 0.25758, 0.0331, 0.86389, 0.56016, 0.80798, 0.45586, 0.71947, 0.353, 0.63172, 0.31014, 0.52535, 0.30728, 0.42596, 0.34443, 0.34986, 0.42443, 0.3056, 0.51587, 0.35685, 0.63874, 0.43263, 0.72279, 0.53823, 0.74136, 0.64616, 0.73993, 0.73546, 0.69278, 0.81466, 0.62278], "triangles": [11, 29, 30, 11, 12, 29, 13, 28, 12, 12, 28, 29, 13, 27, 28, 13, 14, 27, 11, 31, 10, 11, 30, 31, 10, 32, 9, 10, 31, 32, 14, 26, 27, 14, 15, 26, 29, 24, 23, 29, 22, 30, 29, 23, 22, 30, 21, 31, 30, 22, 21, 27, 24, 28, 29, 28, 24, 32, 19, 9, 8, 9, 7, 31, 20, 32, 31, 21, 20, 7, 9, 19, 7, 19, 6, 26, 25, 27, 27, 25, 24, 32, 20, 19, 26, 15, 17, 19, 20, 6, 15, 16, 17, 17, 18, 26, 26, 18, 25, 20, 5, 6, 20, 21, 5, 25, 1, 24, 25, 0, 1, 25, 18, 0, 21, 4, 5, 21, 22, 4, 24, 1, 23, 23, 3, 22, 22, 3, 4, 1, 2, 23, 23, 2, 3], "vertices": [2, 6, 56.32, 49.76, 0.96, 42, -348.85, 115.76, 0.04, 2, 6, 53.54, 48.15, 0.96, 42, -351.63, 114.14, 0.04, 2, 6, 54.05, 43.26, 0.96, 42, -351.12, 109.25, 0.04, 2, 6, 53.37, 38.02, 0.96, 42, -351.8, 104.02, 0.04, 2, 6, 51.19, 33.17, 0.96, 42, -353.98, 99.16, 0.04, 2, 6, 47.56, 29.13, 0.96, 42, -357.61, 95.12, 0.04, 2, 6, 44.1, 26.43, 0.96, 42, -361.07, 92.43, 0.04, 2, 6, 41.43, 23.91, 0.96, 42, -363.74, 89.91, 0.04, 2, 6, 38.82, 24.46, 0.96, 42, -366.34, 90.46, 0.04, 2, 6, 38.67, 28.14, 0.96, 42, -366.49, 94.14, 0.04, 2, 6, 36.11, 32.51, 0.96, 42, -369.06, 98.51, 0.04, 2, 6, 34.47, 38.2, 0.96, 42, -370.7, 104.2, 0.04, 2, 6, 34.76, 45.88, 0.96, 42, -370.41, 111.88, 0.04, 2, 6, 36.12, 53.45, 0.96077, 42, -369.05, 119.45, 0.03923, 2, 6, 41.12, 59.17, 0.96321, 42, -364.05, 125.17, 0.03679, 2, 6, 45.89, 60.71, 0.96337, 42, -359.28, 126.7, 0.03663, 2, 6, 51.13, 67.51, 0.96873, 42, -354.04, 133.5, 0.03127, 2, 6, 56.46, 62.69, 0.96596, 42, -348.71, 128.68, 0.03404, 2, 6, 58.66, 54.65, 0.96159, 42, -346.51, 120.65, 0.03841, 2, 6, 42.2, 28.56, 0.96, 42, -362.96, 94.56, 0.04, 2, 6, 45.1, 30.8, 0.96, 42, -360.06, 96.8, 0.04, 2, 6, 48.15, 34.53, 0.96, 42, -357.02, 100.53, 0.04, 2, 6, 49.7, 38.41, 0.96, 42, -355.47, 104.4, 0.04, 2, 6, 50.37, 43.26, 0.96, 42, -354.8, 109.25, 0.04, 2, 6, 50, 47.91, 0.96, 42, -355.17, 113.9, 0.04, 2, 6, 48.44, 51.62, 0.96, 42, -356.73, 117.62, 0.04, 2, 6, 46.41, 53.92, 0.96, 42, -358.75, 119.92, 0.04, 2, 6, 43.08, 51.95, 0.96, 42, -362.09, 117.95, 0.04, 2, 6, 40.57, 48.75, 0.96, 42, -364.6, 114.74, 0.04, 2, 6, 39.52, 43.98, 0.96, 42, -365.65, 109.98, 0.04, 2, 6, 38.96, 39.05, 0.96, 42, -366.21, 105.04, 0.04, 2, 6, 39.63, 34.83, 0.96, 42, -365.54, 100.82, 0.04, 2, 6, 40.92, 31, 0.96, 42, -364.24, 97, 0.04], "hull": 19, "edges": [12, 14, 14, 16, 22, 24, 34, 36, 32, 34, 28, 30, 30, 32, 24, 26, 26, 28, 20, 22, 16, 18, 18, 20, 6, 8, 2, 4, 4, 6, 2, 0, 0, 36, 8, 10, 10, 12, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 38], "width": 46, "height": 25}}, "hair_F": {"body": {"type": "mesh", "uvs": [0.49116, 0.00023, 0.5168, 0.00801, 0.52297, 0.03608, 0.54594, 0.06114, 0.54009, 0.09012, 0.5167, 0.08758, 0.4978, 0.0923, 0.48137, 0.10382, 0.47257, 0.11541, 0.46211, 0.13971, 0.45151, 0.1628, 0.44143, 0.17156, 0.42341, 0.19017, 0.40464, 0.21185, 0.37622, 0.22731, 0.35623, 0.23235, 0.31287, 0.24949, 0.26442, 0.25463, 0.22719, 0.24075, 0.22088, 0.21962, 0.23614, 0.18831, 0.26797, 0.14481, 0.28474, 0.1106, 0.31452, 0.07364, 0.34705, 0.04256, 0.39701, 0.01505, 0.45394, 0.00056, 0.27018, 0.22821, 0.31829, 0.16444, 0.33588, 0.1375, 0.35578, 0.09705, 0.38602, 0.06175, 0.49813, 0.0591, 0.46146, 0.067, 0.43042, 0.08837, 0.40638, 0.12045, 0.29265, 0.19663, 0.32327, 0.21235, 0.36553, 0.19101, 0.39554, 0.15769, 0.42354, 0.03675, 0.47254, 0.02664], "triangles": [17, 27, 16, 17, 18, 27, 16, 37, 15, 16, 27, 37, 18, 19, 27, 15, 37, 14, 19, 20, 27, 27, 36, 37, 27, 20, 36, 37, 38, 14, 14, 38, 13, 37, 36, 38, 13, 38, 12, 36, 28, 38, 28, 36, 21, 38, 39, 12, 38, 28, 39, 12, 39, 11, 36, 20, 21, 11, 39, 10, 28, 29, 39, 28, 21, 29, 10, 39, 9, 39, 35, 9, 39, 29, 35, 21, 22, 29, 9, 35, 8, 29, 30, 35, 29, 22, 30, 35, 34, 8, 35, 30, 34, 8, 34, 7, 22, 23, 30, 6, 7, 33, 30, 31, 34, 30, 23, 31, 7, 34, 33, 6, 32, 5, 6, 33, 32, 4, 5, 3, 34, 31, 33, 5, 32, 3, 23, 24, 31, 31, 40, 33, 33, 41, 32, 33, 40, 41, 31, 24, 40, 32, 2, 3, 32, 41, 2, 24, 25, 40, 40, 26, 41, 40, 25, 26, 41, 1, 2, 41, 0, 1, 41, 26, 0], "vertices": [2, 6, 190.1, -4.57, 0.99168, 43, -153.98, 61.43, 0.00832, 3, 6, 182.45, -16.19, 0.99476, 17, 257.69, 140.94, 0, 43, -161.63, 49.8, 0.00524, 3, 6, 159.93, -16.46, 0.989, 17, 235.24, 139.15, 0, 42, -245.23, 49.54, 0.011, 2, 6, 138.81, -25.13, 0.9793, 42, -266.36, 40.87, 0.0207, 2, 6, 116.28, -19.51, 0.97683, 42, -288.89, 46.48, 0.02317, 2, 6, 119.66, -8.47, 0.97169, 42, -285.51, 57.52, 0.02831, 3, 5, 153.82, 15.46, 0, 6, 117.05, 1.1, 0.96649, 42, -288.12, 67.09, 0.0335, 3, 5, 144.65, 23.43, 0.00036, 6, 108.93, 10.14, 0.9616, 42, -296.24, 76.13, 0.03804, 3, 5, 135.43, 27.69, 0.00269, 6, 100.3, 15.5, 0.95493, 42, -304.87, 81.49, 0.04238, 3, 5, 116.1, 32.75, 0.01376, 6, 81.74, 22.88, 0.94251, 42, -323.43, 88.88, 0.04372, 3, 5, 97.74, 37.87, 0.04263, 6, 64.14, 30.22, 0.91308, 42, -341.02, 96.22, 0.04428, 3, 5, 90.77, 42.76, 0.06529, 6, 57.82, 35.93, 0.89161, 42, -347.34, 101.93, 0.04309, 4, 4, 379.41, 47.22, 0.00074, 5, 75.96, 51.49, 0.1368, 6, 44.2, 46.41, 0.81854, 42, -360.97, 112.41, 0.04392, 4, 4, 362.69, 57.25, 0.0147, 5, 58.71, 60.59, 0.21645, 6, 28.2, 57.56, 0.7313, 42, -376.97, 123.56, 0.03755, 5, 4, 351.15, 71.7, 0.04533, 5, 46.39, 74.38, 0.22292, 6, 17.67, 72.76, 0.59826, 18, 79.19, -78.31, 0.11313, 42, -387.5, 138.75, 0.02037, 5, 4, 347.67, 81.62, 0.04973, 5, 42.37, 84.09, 0.21385, 6, 14.87, 82.89, 0.53048, 18, 75.71, -68.39, 0.19543, 42, -390.3, 148.88, 0.01051, 6, 4, 335.2, 103.39, 0.05235, 5, 28.71, 105.14, 0.18103, 6, 3.9, 105.46, 0.36392, 18, 63.24, -46.62, 0.39708, 42, -401.27, 171.45, 0.0037, 44, 167.75, 455.34, 0.00191, 5, 4, 332.38, 127.13, 0.0206, 5, 24.59, 128.68, 0.18503, 6, 2.7, 129.33, 0.29021, 18, 60.42, -22.88, 0.50039, 43, -341.39, 195.32, 0.00376, 5, 4, 344.37, 144.6, 0.00154, 5, 35.59, 146.79, 0.156, 6, 15.84, 145.95, 0.39069, 18, 72.41, -5.41, 0.44304, 43, -328.25, 211.94, 0.00873, 4, 5, 52.39, 149.88, 0.14392, 6, 32.89, 146.95, 0.45345, 18, 89.35, -3.25, 0.393, 43, -311.2, 212.95, 0.00962, 4, 5, 77.29, 142.5, 0.11783, 6, 56.7, 136.58, 0.57097, 18, 113.81, -11.99, 0.30246, 43, -287.39, 202.57, 0.00874, 4, 5, 111.9, 127.09, 0.06615, 6, 89.15, 117.03, 0.79049, 18, 147.51, -29.29, 0.13514, 43, -254.93, 183.03, 0.00822, 4, 5, 139.11, 118.98, 0.03156, 6, 115.16, 105.64, 0.92387, 18, 174.23, -38.88, 0.03551, 43, -228.93, 171.64, 0.00906, 3, 5, 168.51, 104.56, 0.00975, 6, 142.57, 87.72, 0.98156, 43, -201.52, 153.71, 0.00869, 3, 5, 193.25, 88.79, 0.00168, 6, 165.18, 69.03, 0.99053, 43, -178.9, 135.03, 0.00779, 2, 6, 183.95, 42.28, 0.99273, 43, -160.14, 108.27, 0.00727, 2, 6, 192.03, 13.42, 0.99317, 43, -152.06, 79.41, 0.00683, 6, 4, 353.21, 123.2, 0.00469, 5, 45.6, 125.91, 0.1722, 6, 23.21, 124, 0.43853, 18, 81.24, -26.81, 0.38268, 42, -381.96, 189.99, 0.00158, 43, -320.88, 189.99, 0.00031, 4, 5, 96.33, 102.61, 0.09385, 6, 70.7, 94.65, 0.80216, 18, 130.61, -52.87, 0.08888, 42, -334.47, 160.64, 0.01512, 3, 5, 117.76, 94.09, 0.05019, 6, 90.92, 83.57, 0.93307, 42, -314.25, 149.56, 0.01674, 3, 5, 149.94, 84.48, 0.01221, 6, 121.67, 70.07, 0.97135, 42, -283.5, 136.06, 0.01644, 3, 5, 178.03, 69.83, 0.00165, 6, 147.75, 52.08, 0.9829, 42, -257.42, 118.08, 0.01545, 2, 6, 143.24, -2.26, 0.97285, 42, -261.93, 63.74, 0.02715, 2, 6, 139.16, 16.19, 0.97159, 42, -266.01, 82.19, 0.02841, 3, 5, 156.89, 48.21, 0.0016, 6, 124.12, 33.23, 0.96792, 42, -281.05, 99.22, 0.03048, 3, 5, 131.37, 59.86, 0.01464, 6, 100.22, 47.91, 0.95079, 42, -304.95, 113.91, 0.03457, 5, 4, 377.69, 110.95, 0.00028, 5, 70.72, 115.03, 0.13962, 6, 46.81, 110.12, 0.59126, 18, 105.73, -39.06, 0.25953, 42, -358.36, 176.11, 0.00931, 5, 4, 364.41, 96.76, 0.00798, 5, 58.25, 100.13, 0.19233, 6, 32.59, 96.86, 0.56518, 18, 92.45, -53.25, 0.22252, 42, -372.57, 162.85, 0.01199, 5, 4, 380.25, 75.34, 0.00245, 5, 75.24, 79.62, 0.15818, 6, 46.95, 74.42, 0.75802, 18, 108.29, -74.67, 0.0542, 42, -358.22, 140.41, 0.02715, 3, 5, 101.76, 65.07, 0.06224, 6, 71.47, 56.73, 0.90414, 42, -333.69, 122.72, 0.03363, 2, 6, 165.26, 31.57, 0.98593, 42, -239.91, 97.57, 0.01407, 2, 6, 170.36, 6.96, 0.98604, 42, -234.81, 72.96, 0.01396], "hull": 27, "edges": [0, 52, 46, 48, 0, 2, 48, 50, 50, 52, 42, 44, 44, 46, 40, 42, 36, 38, 38, 40, 34, 36, 30, 28, 24, 22, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 22, 20, 20, 18, 28, 26, 26, 24, 8, 6, 6, 4, 4, 2, 30, 32, 32, 34], "width": 486, "height": 795}}, "hand_L": {"hand_L": {"type": "mesh", "uvs": [0.29287, 0, 0.32721, 0, 0.62014, 0.04697, 0.66942, 0.11039, 0.71444, 0.17354, 0.97876, 0.08551, 0.99541, 0.15569, 0.91537, 0.26013, 0.85243, 0.37241, 0.83162, 0.4249, 0.77297, 0.49323, 0.64279, 0.61184, 0.58526, 0.6639, 0.62188, 0.69077, 0.62356, 0.76603, 0.6042, 0.8397, 0.5936, 0.95515, 0.48225, 0.98609, 0.35144, 0.98768, 0.22109, 0.94499, 0.13509, 0.88544, 0.07364, 0.80817, 0.10102, 0.73219, 0.11604, 0.67836, 0.14864, 0.64101, 0.1397, 0.5629, 0.17467, 0.48745, 0.21493, 0.43047, 0.19506, 0.38142, 0.1424, 0.30792, 0.02486, 0.21248, 0.00693, 0.17488, 0.0488, 0.08209, 0.19525, 0.74097, 0.27126, 0.79629, 0.37768, 0.82043, 0.50234, 0.80434, 0.56923, 0.7554, 0.17531, 0.66281, 0.16773, 0.70668, 0.24945, 0.71608, 0.45556, 0.73591, 0.27028, 0.66775, 0.4724, 0.67788, 0.296, 0.59966, 0.488, 0.61091, 0.38314, 0.42761, 0.68661, 0.38182, 0.67597, 0.46787, 0.36717, 0.49781, 0.3512, 0.33929, 0.63782, 0.29571, 0.77537, 0.277], "triangles": [33, 39, 40, 22, 39, 33, 33, 40, 34, 33, 21, 22, 16, 36, 15, 35, 34, 41, 33, 20, 21, 17, 18, 35, 19, 20, 34, 19, 34, 35, 18, 19, 35, 17, 35, 36, 17, 36, 16, 33, 34, 20, 36, 35, 41, 27, 28, 50, 46, 50, 51, 47, 51, 52, 50, 29, 0, 0, 1, 50, 50, 28, 29, 8, 52, 7, 51, 50, 3, 3, 50, 1, 30, 32, 29, 29, 32, 0, 51, 4, 52, 51, 3, 4, 2, 3, 1, 52, 4, 7, 7, 4, 6, 30, 31, 32, 4, 5, 6, 46, 51, 47, 9, 47, 8, 47, 52, 8, 38, 24, 44, 37, 12, 13, 37, 43, 12, 41, 42, 43, 42, 44, 43, 43, 45, 12, 43, 44, 45, 42, 38, 44, 12, 45, 11, 24, 25, 44, 11, 48, 10, 11, 45, 48, 44, 49, 45, 45, 49, 48, 25, 26, 44, 44, 26, 49, 26, 27, 49, 49, 46, 48, 49, 27, 46, 10, 48, 9, 48, 47, 9, 48, 46, 47, 46, 27, 50, 34, 40, 41, 22, 23, 39, 39, 38, 40, 40, 38, 42, 39, 23, 38, 23, 24, 38, 36, 37, 15, 15, 37, 14, 36, 41, 37, 37, 13, 14, 41, 43, 37, 40, 42, 41], "vertices": [1, 50, 57.09, -0.74, 1, 1, 50, 55.87, -3.5, 1, 1, 50, 39.75, -24.57, 1, 1, 50, 30.28, -25.13, 1, 2, 49, 74.67, -4.06, 1e-05, 50, 21, -25.36, 0.99999, 1, 50, 22.32, -51.37, 1, 2, 49, 85.16, -26.58, 2e-05, 50, 13.19, -48.94, 0.99998, 2, 49, 69.72, -24.57, 0.02865, 50, 3.32, -36.89, 0.97135, 2, 49, 53.79, -24.33, 0.26415, 50, -8.1, -25.8, 0.73585, 2, 49, 46.6, -24.94, 0.46938, 50, -13.75, -21.3, 0.53062, 2, 49, 36.31, -23.1, 0.7826, 50, -19.98, -12.91, 0.2174, 3, 48, 123.29, -19.63, 0.00942, 49, 17.62, -17.57, 0.99055, 50, -29.79, 3.93, 3e-05, 2, 48, 115.45, -16.16, 0.17797, 49, 9.4, -15.11, 0.82203, 2, 48, 112.64, -20.07, 0.36322, 49, 7.11, -19.34, 0.63678, 2, 48, 102.9, -22.35, 0.68485, 49, -2.28, -22.82, 0.31515, 3, 48, 92.96, -22.78, 0.84857, 49, -12.08, -24.48, 0.08133, 54, -2.11, -21.96, 0.07011, 1, 54, -17.31, -24.32, 1, 1, 54, -23.42, -15.63, 1, 1, 54, -26.09, -4.43, 1, 1, 54, -22.99, 7.99, 1, 1, 54, -16.87, 17.07, 1, 1, 54, -7.99, 24.55, 1, 3, 48, 97.47, 23.53, 0.81994, 49, -13.38, 22.03, 0.11007, 54, 2.4, 24.36, 0.06999, 3, 48, 104.75, 23.77, 0.70081, 49, -6.19, 23.18, 0.29885, 50, -19.18, 49.92, 0.00033, 3, 48, 110.21, 22.03, 0.47171, 49, -0.55, 22.13, 0.52326, 50, -15.79, 45.29, 0.00503, 3, 48, 120.19, 25.01, 0.14585, 49, 8.98, 26.34, 0.81347, 50, -5.97, 41.81, 0.04068, 3, 48, 130.65, 24.15, 0.03038, 49, 19.47, 26.79, 0.82495, 50, 1.97, 34.95, 0.14467, 3, 48, 138.81, 22.31, 0.00242, 49, 27.79, 25.97, 0.62852, 50, 7.47, 28.65, 0.36906, 2, 49, 33.36, 29.8, 0.36647, 50, 14.15, 27.61, 0.63353, 2, 49, 41.03, 37.43, 0.11528, 50, 24.96, 27.91, 0.88472, 2, 49, 49.54, 51.42, 0.00951, 50, 40.75, 32.25, 0.99049, 2, 49, 53.73, 54.57, 0.0038, 50, 45.96, 31.67, 0.9962, 1, 50, 55.77, 23.32, 1, 3, 48, 98.1, 15.18, 0.14706, 49, -11.71, 13.83, 0.01663, 54, 3.03, 16.01, 0.83632, 1, 54, -2.73, 7.9, 1, 1, 54, -3.86, -1.93, 1, 3, 48, 95.64, -13.02, 0.13491, 49, -10.64, -14.46, 0.01501, 54, 0.57, -12.19, 0.85009, 2, 48, 103.26, -17.38, 0.66307, 49, -2.54, -17.84, 0.33693, 3, 48, 107.88, 19.11, 0.57071, 49, -2.5, 18.95, 0.42746, 50, -19.39, 44.31, 0.00183, 4, 48, 102.04, 18.52, 0.32058, 49, -8.22, 17.63, 0.08957, 50, -24.46, 47.27, 1e-05, 54, 6.97, 19.35, 0.58985, 2, 48, 102.35, 11.23, 0.82725, 49, -7, 10.44, 0.17275, 2, 48, 103.65, -7.06, 0.75847, 49, -3.44, -7.54, 0.24153, 3, 48, 109.02, 10.81, 0.50194, 49, -0.33, 10.85, 0.49732, 50, -23.37, 36.93, 0.00074, 2, 48, 111.51, -6.85, 0.17405, 49, 4.33, -6.36, 0.82595, 3, 48, 118.35, 10.53, 0.11205, 49, 8.96, 11.74, 0.87594, 50, -15.99, 31.2, 0.01201, 2, 48, 120.5, -6.29, 0.00422, 49, 13.19, -4.69, 0.99578, 2, 49, 33.09, 12.15, 0.67161, 50, 1.85, 14.95, 0.32839, 2, 49, 47.74, -11, 0.34792, 50, -3.36, -11.94, 0.65208, 2, 49, 36.64, -13.93, 0.83783, 50, -13.45, -6.47, 0.16217, 3, 48, 132.93, 7.31, 0.00063, 49, 23.82, 10.36, 0.90894, 50, -6.13, 20, 0.09043, 2, 49, 43.23, 18.72, 0.21193, 50, 13.73, 12.78, 0.78807, 2, 49, 57.11, -3.13, 0.0183, 50, 8.85, -12.64, 0.9817, 2, 49, 63.49, -13.71, 0.05082, 50, 6.24, -24.72, 0.94918], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 56, 58, 58, 60, 60, 62, 62, 64, 24, 26, 26, 28, 18, 20, 16, 18, 12, 14, 14, 16, 46, 48, 48, 50, 54, 56, 50, 52, 52, 54, 66, 40, 68, 38, 70, 36, 34, 72, 74, 24, 76, 78, 78, 66, 66, 68, 68, 70, 70, 72, 72, 74, 28, 30, 30, 32, 42, 44, 44, 46], "width": 88, "height": 133}}, "head": {"head": {"type": "mesh", "uvs": [0.56718, 1e-05, 0.63458, 0.04807, 0.74338, 0.14449, 0.82817, 0.24688, 0.84317, 0.31583, 0.86279, 0.37506, 0.87278, 0.30744, 0.92899, 0.23742, 0.97013, 0.23519, 0.99577, 0.2706, 0.99218, 0.36689, 0.95288, 0.4751, 0.90897, 0.55039, 0.91038, 0.57556, 0.91206, 0.60548, 0.91327, 0.62706, 0.91874, 0.72462, 0.90022, 0.77516, 0.85934, 0.77459, 0.84596, 0.71413, 0.85448, 0.62646, 0.85704, 0.6001, 0.85853, 0.58483, 0.86069, 0.56256, 0.8407, 0.55639, 0.8288, 0.67102, 0.80401, 0.74619, 0.71973, 0.84651, 0.58897, 0.96625, 0.52503, 0.99429, 0.45389, 0.99551, 0.40538, 0.97601, 0.21548, 0.85456, 0.15241, 0.79061, 0.042, 0.58682, 0.0291, 0.51816, 0.0189, 0.46037, 0.00615, 0.39386, 0.00982, 0.30186, 0.03954, 0.19277, 0.10377, 0.07054, 0.22759, 0.07004, 0.35142, 0.06954, 0.51124, 0.0258, 0.48237, 0.34514, 0.66097, 0.26462, 0.69609, 0.25814, 0.74221, 0.27172, 0.77713, 0.2904, 0.77375, 0.30164, 0.73955, 0.29214, 0.7034, 0.28862, 0.48568, 0.38715, 0.47108, 0.37499, 0.67523, 0.29653, 0.57509, 0.30529, 0.53101, 0.32443, 0.61695, 0.28575, 0.58008, 0.34324, 0.5334, 0.36541, 0.62721, 0.31949, 0.09536, 0.36892, 0.12714, 0.3704, 0.32373, 0.41044, 0.33612, 0.39641, 0.31804, 0.3729, 0.11933, 0.33502, 0.08756, 0.33826, 0.05522, 0.36842, 0.03404, 0.39957, 0.04017, 0.40996, 0.0686, 0.38622, 0.22191, 0.39054, 0.22265, 0.34899, 0.16979, 0.34111, 0.2723, 0.36114, 0.2693, 0.40007, 0.17453, 0.38029, 0.52655, 0.44715, 0.54062, 0.42142, 0.56537, 0.39894, 0.5941, 0.38369, 0.6261, 0.37636, 0.66221, 0.37556, 0.69672, 0.37943, 0.72063, 0.39782, 0.54966, 0.44978, 0.5783, 0.45553, 0.61172, 0.45674, 0.64582, 0.45371, 0.67957, 0.4395, 0.70481, 0.41833, 0.51092, 0.44795, 0.52934, 0.41861, 0.55286, 0.39321, 0.58627, 0.37355, 0.62454, 0.36147, 0.66374, 0.35848, 0.70614, 0.36119, 0.74637, 0.36959, 0.51516, 0.4606, 0.54051, 0.46149, 0.59321, 0.47658, 0.65158, 0.47539, 0.70428, 0.45794, 0.74831, 0.4248, 0.74898, 0.39433, 0.12286, 0.46436, 0.14438, 0.44643, 0.17439, 0.43225, 0.2057, 0.42647, 0.24223, 0.42705, 0.27647, 0.43717, 0.30158, 0.45192, 0.31887, 0.46957, 0.1434, 0.48345, 0.17537, 0.49965, 0.20961, 0.50197, 0.24418, 0.5011, 0.27614, 0.49126, 0.30061, 0.47883, 0.3376, 0.47144, 0.32293, 0.45956, 0.30221, 0.44034, 0.27576, 0.42479, 0.24324, 0.41433, 0.20119, 0.41179, 0.15834, 0.4168, 0.12327, 0.43263, 0.08948, 0.45384, 0.33628, 0.48677, 0.31173, 0.49158, 0.27029, 0.51589, 0.21865, 0.52635, 0.1549, 0.52607, 0.10932, 0.50486, 0.09083, 0.47546, 0.85969, 0.45785, 0.36924, 0.48666, 0.48326, 0.47957, 0.39026, 0.48535, 0.46157, 0.48092, 0.42591, 0.48314, 0.3717, 0.43863, 0.45944, 0.4316, 0.41895, 0.43665, 0.37353, 0.61768, 0.36201, 0.63471, 0.36201, 0.67521, 0.37695, 0.69035, 0.41792, 0.69338, 0.48449, 0.68619, 0.52247, 0.67975, 0.53784, 0.66953, 0.54083, 0.6332, 0.51991, 0.60594, 0.34483, 0.79739, 0.37242, 0.77626, 0.40647, 0.75135, 0.43737, 0.74132, 0.46461, 0.75553, 0.49082, 0.73771, 0.51682, 0.7388, 0.56097, 0.75383, 0.59437, 0.76642, 0.33571, 0.80493, 0.36935, 0.80493, 0.40513, 0.79878, 0.44144, 0.79688, 0.47188, 0.80256, 0.50552, 0.79167, 0.54397, 0.78741, 0.58188, 0.78362, 0.60858, 0.77462, 0.34265, 0.81725, 0.36241, 0.84708, 0.39659, 0.87218, 0.44678, 0.88165, 0.50445, 0.87692, 0.5461, 0.85892, 0.58562, 0.83003, 0.60195, 0.79018, 0.63169, 0.78616, 0.62795, 0.75299, 0.31682, 0.78998, 0.31345, 0.82009, 0.41167, 0.8267, 0.44941, 0.83212, 0.49735, 0.82896, 0.53663, 0.81629, 0.56519, 0.80381, 0.38412, 0.81873, 0.43659, 0.89872, 0.5267, 0.89139, 0.59713, 0.89983, 0.38897, 0.91811, 0.68196, 0.8195, 0.77997, 0.63096, 0.78745, 0.51721, 0.80915, 0.44289, 0.80915, 0.36061, 0.67205, 0.55377, 0.65792, 0.6717, 0.5682, 0.53113, 0.31857, 0.56504, 0.23145, 0.60781, 0.26742, 0.7164, 0.73377, 0.74517, 0.11586, 0.5933, 0.15856, 0.69672, 0.23495, 0.79471, 0.31167, 0.86767, 0.29551, 0.21754, 0.45597, 0.19927, 0.60317, 0.15022, 0.15105, 0.20407, 0.43053, 0.94778, 0.4894, 0.95534, 0.55253, 0.9387], "triangles": [17, 18, 16, 18, 19, 16, 20, 15, 19, 20, 14, 15, 22, 13, 14, 20, 21, 14, 21, 22, 14, 19, 15, 16, 215, 40, 41, 212, 41, 42, 212, 42, 213, 213, 42, 43, 213, 43, 214, 46, 214, 2, 214, 43, 1, 4, 48, 3, 200, 4, 5, 5, 6, 10, 11, 5, 10, 28, 218, 194, 29, 217, 218, 29, 218, 28, 30, 216, 217, 195, 176, 192, 211, 195, 31, 211, 32, 210, 195, 211, 176, 211, 185, 175, 176, 175, 186, 177, 176, 187, 192, 176, 177, 192, 177, 178, 176, 211, 175, 186, 168, 187, 187, 188, 178, 177, 187, 178, 180, 179, 189, 188, 189, 179, 179, 180, 194, 194, 196, 27, 196, 194, 180, 28, 194, 27, 137, 5, 11, 23, 24, 12, 22, 23, 13, 207, 197, 25, 26, 207, 25, 27, 196, 207, 27, 207, 26, 196, 182, 207, 182, 196, 180, 173, 164, 183, 172, 164, 173, 181, 172, 173, 190, 171, 172, 189, 171, 190, 181, 190, 172, 153, 152, 154, 152, 153, 163, 162, 152, 163, 171, 162, 163, 170, 162, 171, 188, 169, 170, 187, 168, 169, 160, 161, 170, 168, 159, 160, 168, 167, 158, 170, 169, 160, 191, 166, 167, 191, 167, 186, 191, 175, 174, 151, 150, 146, 158, 150, 159, 166, 157, 167, 174, 165, 166, 191, 174, 166, 185, 174, 175, 149, 148, 147, 149, 206, 148, 210, 33, 209, 184, 210, 206, 36, 70, 129, 35, 36, 136, 34, 35, 135, 128, 62, 127, 107, 129, 128, 127, 77, 126, 126, 72, 125, 125, 76, 124, 110, 126, 125, 107, 128, 108, 135, 136, 107, 108, 128, 127, 135, 107, 115, 34, 135, 208, 115, 108, 109, 115, 107, 108, 111, 125, 124, 117, 110, 111, 111, 124, 112, 63, 123, 124, 135, 115, 134, 208, 135, 134, 116, 115, 109, 134, 115, 116, 123, 112, 124, 112, 118, 111, 147, 206, 205, 134, 205, 208, 205, 134, 133, 133, 117, 118, 134, 116, 133, 205, 133, 132, 122, 123, 63, 113, 112, 123, 119, 118, 112, 133, 118, 132, 113, 123, 122, 113, 119, 112, 120, 119, 113, 132, 118, 119, 114, 122, 121, 131, 120, 114, 131, 114, 130, 132, 131, 204, 204, 131, 130, 147, 204, 146, 121, 122, 143, 130, 114, 121, 204, 138, 146, 151, 146, 155, 130, 121, 138, 138, 204, 130, 143, 64, 145, 138, 121, 143, 140, 138, 143, 146, 138, 140, 155, 146, 142, 141, 144, 139, 142, 145, 141, 141, 139, 155, 142, 141, 155, 139, 144, 92, 155, 139, 203, 93, 92, 144, 139, 92, 100, 80, 95, 81, 87, 86, 79, 87, 79, 80, 101, 86, 87, 81, 88, 80, 81, 95, 96, 88, 87, 80, 102, 101, 87, 102, 87, 88, 103, 203, 102, 99, 49, 200, 98, 50, 99, 84, 97, 98, 106, 99, 200, 85, 98, 99, 85, 99, 106, 84, 98, 85, 91, 84, 85, 105, 85, 106, 91, 85, 105, 91, 90, 83, 91, 83, 84, 199, 106, 200, 199, 200, 5, 105, 106, 199, 137, 199, 5, 104, 90, 91, 104, 91, 105, 198, 105, 199, 198, 199, 137, 104, 105, 198, 24, 198, 137, 201, 104, 198, 197, 198, 24, 97, 54, 98, 83, 96, 97, 82, 96, 83, 83, 97, 84, 90, 82, 83, 90, 89, 82, 88, 81, 82, 82, 89, 88, 103, 89, 90, 103, 90, 104, 103, 102, 88, 103, 88, 89, 201, 103, 104, 197, 201, 198, 202, 201, 197, 103, 201, 203, 202, 154, 201, 96, 60, 97, 95, 58, 96, 82, 81, 96, 80, 94, 95, 101, 78, 86, 203, 101, 102, 203, 100, 101, 154, 155, 203, 201, 154, 203, 79, 94, 80, 78, 79, 86, 94, 59, 95, 79, 93, 94, 78, 93, 79, 100, 78, 101, 93, 59, 94, 78, 92, 93, 100, 92, 78, 100, 203, 139, 155, 154, 151, 144, 52, 93, 145, 144, 141, 140, 143, 145, 140, 145, 142, 146, 140, 142, 144, 145, 53, 143, 122, 63, 114, 113, 122, 205, 204, 147, 147, 146, 149, 120, 113, 114, 119, 120, 131, 132, 119, 131, 205, 132, 204, 117, 116, 109, 117, 111, 118, 117, 109, 110, 116, 117, 133, 111, 110, 125, 109, 126, 110, 108, 127, 109, 209, 208, 205, 209, 34, 208, 109, 127, 126, 136, 129, 107, 35, 136, 135, 129, 71, 128, 36, 129, 136, 209, 205, 206, 33, 34, 209, 148, 206, 147, 206, 210, 209, 146, 150, 149, 157, 206, 149, 184, 206, 157, 185, 210, 184, 157, 149, 158, 156, 184, 157, 165, 184, 156, 166, 156, 157, 165, 156, 166, 185, 184, 165, 158, 149, 150, 167, 157, 158, 185, 165, 174, 185, 211, 210, 159, 150, 151, 161, 159, 151, 160, 159, 161, 158, 159, 168, 154, 152, 151, 162, 161, 151, 152, 162, 151, 170, 161, 162, 169, 168, 160, 186, 167, 168, 172, 163, 164, 171, 163, 172, 189, 170, 171, 188, 170, 189, 202, 153, 154, 183, 153, 202, 163, 153, 183, 164, 163, 183, 180, 190, 181, 180, 189, 190, 180, 181, 182, 207, 202, 197, 183, 202, 207, 182, 183, 207, 173, 183, 182, 181, 173, 182, 24, 25, 197, 12, 24, 137, 23, 12, 13, 12, 137, 11, 187, 169, 188, 178, 188, 179, 193, 178, 179, 193, 179, 194, 186, 175, 191, 187, 176, 186, 32, 33, 210, 32, 211, 31, 192, 178, 193, 216, 195, 192, 216, 192, 217, 31, 195, 216, 31, 216, 30, 218, 193, 194, 217, 192, 193, 217, 193, 218, 30, 217, 29, 10, 6, 9, 200, 49, 4, 9, 6, 7, 9, 7, 8, 3, 47, 2, 214, 1, 2, 1, 43, 0, 39, 40, 215, 215, 41, 212, 215, 67, 39, 212, 73, 215, 44, 212, 213, 55, 213, 214, 67, 38, 39, 37, 38, 68, 70, 36, 37, 70, 69, 71, 70, 37, 69, 69, 37, 68, 69, 68, 71, 68, 38, 67, 68, 67, 61, 71, 61, 128, 71, 68, 61, 70, 71, 129, 215, 66, 67, 61, 66, 62, 126, 77, 72, 73, 74, 215, 72, 77, 73, 77, 74, 73, 127, 62, 77, 66, 215, 74, 62, 74, 77, 62, 66, 74, 128, 61, 62, 61, 67, 66, 72, 73, 75, 75, 73, 212, 76, 72, 75, 76, 65, 63, 124, 76, 63, 53, 145, 64, 75, 212, 65, 64, 65, 53, 63, 65, 64, 63, 64, 143, 76, 75, 65, 125, 72, 76, 59, 56, 58, 44, 213, 56, 59, 58, 95, 59, 52, 44, 56, 213, 55, 53, 65, 44, 52, 53, 44, 93, 52, 59, 144, 53, 52, 44, 56, 59, 44, 65, 212, 54, 45, 46, 60, 57, 54, 55, 57, 60, 58, 55, 60, 57, 55, 214, 56, 55, 58, 96, 58, 60, 97, 60, 54, 57, 214, 45, 57, 45, 54, 45, 214, 46, 50, 51, 47, 54, 46, 51, 54, 51, 98, 98, 51, 50, 47, 46, 2, 51, 46, 47, 48, 47, 3, 50, 47, 48, 49, 50, 48, 49, 48, 4, 99, 50, 49], "vertices": [2, 6, 115.92, -19.5, 0.97683, 42, -289.24, 46.5, 0.02317, 2, 6, 106.69, -28.49, 0.974, 42, -298.47, 37.51, 0.026, 2, 6, 88.65, -42.61, 0.97545, 42, -316.52, 23.38, 0.02455, 2, 6, 70.04, -53.07, 0.98401, 42, -335.12, 12.92, 0.01599, 2, 6, 58.28, -53.89, 0.98527, 42, -346.89, 12.11, 0.01473, 2, 6, 48.04, -55.58, 0.98632, 42, -357.12, 10.41, 0.01368, 2, 6, 59.14, -58.44, 0.98864, 42, -346.03, 7.56, 0.01136, 2, 6, 69.8, -68.17, 0.99014, 42, -335.37, -2.18, 0.00986, 2, 6, 69.43, -74.3, 0.9935, 42, -335.74, -8.31, 0.0065, 2, 6, 63.06, -77.38, 0.99391, 42, -342.11, -11.38, 0.00609, 2, 6, 47.07, -74.88, 0.99322, 42, -358.1, -8.89, 0.00678, 2, 6, 29.73, -66.87, 0.99201, 42, -375.43, -0.87, 0.00799, 2, 6, 17.97, -58.84, 0.99029, 42, -387.2, 7.15, 0.00971, 3, 6, 13.75, -58.54, 0.58341, 15, 2.6, 3.71, 0.41088, 42, -391.42, 7.46, 0.00571, 3, 6, 8.73, -58.18, 0.1054, 15, 7.62, 4.01, 0.8934, 42, -396.44, 7.82, 0.0012, 1, 15, 11.25, 4.22, 1, 1, 15, 27.63, 5.18, 1, 1, 15, 36.14, 2.49, 1, 2, 15, 36.1, -3.6, 0.99814, 42, -423.69, 19.06, 0.00186, 2, 15, 25.96, -5.68, 0.99729, 42, -413.36, 19.81, 0.00271, 3, 6, 6.27, -49.23, 0.08614, 15, 11.22, -4.54, 0.91103, 42, -398.9, 16.76, 0.00283, 3, 6, 10.62, -50.15, 0.32795, 15, 6.79, -4.2, 0.66667, 42, -394.55, 15.85, 0.00539, 3, 6, 13.14, -50.68, 0.57668, 15, 4.22, -4, 0.41543, 42, -392.03, 15.32, 0.00789, 2, 6, 16.81, -51.45, 0.98803, 42, -388.35, 14.54, 0.01197, 2, 6, 18.2, -48.62, 0.98659, 42, -386.96, 17.37, 0.01341, 2, 6, -0.7, -44.53, 0.98432, 42, -405.87, 21.47, 0.01568, 2, 6, -12.79, -39.33, 0.9796, 42, -417.95, 26.66, 0.0204, 2, 6, -27.99, -24.82, 0.97385, 42, -433.16, 41.17, 0.02615, 2, 6, -45.6, -3.05, 0.96581, 42, -450.77, 62.95, 0.03419, 2, 6, -49.12, 6.98, 0.96292, 42, -454.29, 72.98, 0.03708, 2, 6, -48.04, 17.53, 0.96321, 42, -453.21, 83.52, 0.03679, 2, 6, -43.91, 24.31, 0.96469, 42, -449.08, 90.3, 0.03531, 2, 6, -20.23, 49.92, 0.97606, 42, -425.4, 115.91, 0.02394, 2, 6, -8.43, 57.95, 0.98054, 42, -413.6, 123.94, 0.01946, 2, 6, 27.55, 70.13, 0.98167, 42, -377.62, 136.12, 0.01833, 2, 6, 39.23, 70.64, 0.98403, 42, -365.93, 136.63, 0.01597, 2, 6, 49.05, 70.97, 0.98346, 42, -356.11, 136.96, 0.01654, 2, 6, 60.38, 71.5, 0.98202, 42, -344.79, 137.5, 0.01798, 2, 6, 75.65, 69.08, 0.9809, 42, -329.52, 135.08, 0.0191, 2, 6, 93.31, 62.47, 0.97685, 42, -311.86, 128.46, 0.02315, 2, 6, 112.53, 50.48, 0.97399, 42, -292.64, 116.48, 0.02601, 2, 6, 110.38, 32.16, 0.97245, 42, -294.79, 98.15, 0.02755, 2, 6, 108.23, 13.83, 0.97152, 42, -296.94, 79.83, 0.02848, 2, 6, 112.63, -10.7, 0.97304, 42, -292.53, 55.3, 0.02696, 5, 8, 31.46, -3.59, 0.01289, 9, 12.3, -3.23, 0.95493, 12, 35.1, 15.36, 0.02681, 11, 56.01, 11.07, 0.00437, 42, -345.27, 66.07, 0.001, 6, 7, 16.88, -1.4, 0.14854, 8, 1.62, -3.1, 0.84763, 12, 56.59, 36.08, 0.00018, 11, 79.82, 29.07, 0.00156, 42, -335.06, 38.02, 0.001, 43, -273.98, 38.02, 0.00109, 5, 7, 11.95, -3.46, 0.82601, 8, -3.52, -1.61, 0.16826, 11, 84.78, 31.07, 0.00082, 42, -334.62, 32.69, 0.001, 43, -273.54, 32.69, 0.00391, 4, 7, 4.77, -2.53, 0.9895, 11, 91.95, 30.03, 4e-05, 42, -337.71, 26.14, 0.00099, 43, -276.63, 26.14, 0.00947, 3, 7, -0.94, -0.44, 0.98378, 42, -341.46, 21.36, 0.00098, 43, -280.38, 21.36, 0.01524, 6, 7, -0.81, 1.51, 0.9743, 8, -10.33, 10.26, 0.0071, 9, -29.89, 9.37, 0.00337, 12, 74.48, 35.04, 0.00013, 42, -343.27, 22.09, 0.00099, 43, -282.19, 22.09, 0.01412, 6, 7, 4.5, 0.91, 0.98193, 8, -6.57, 6.47, 0.00821, 9, -26.02, 5.69, 0.00077, 12, 69.14, 35.08, 1e-05, 42, -341.07, 26.95, 0.00099, 43, -279.99, 26.95, 0.00808, 5, 7, 9.9, 1.36, 0.96512, 8, -2.08, 3.42, 0.02901, 9, -21.45, 2.78, 0.00097, 42, -339.83, 32.23, 0.001, 43, -278.75, 32.23, 0.0039, 5, 7, 38.6, 23.79, 0.0041, 9, 14.97, 3.32, 0.94447, 12, 37.63, 8.76, 0.04971, 10, 48.9, -44.05, 0.00072, 42, -352.33, 66.44, 0.001, 6, 7, 41.12, 22.19, 0.00067, 9, 16.02, 0.53, 0.93375, 12, 34.95, 10.08, 0.06407, 11, 55.24, 5.84, 0.00027, 10, 48.73, -41.07, 0.00024, 42, -350.04, 68.35, 0.001, 5, 7, 13.77, 3.46, 0.27404, 8, 2.25, 2.63, 0.72212, 9, -17.09, 2.13, 0.00125, 42, -340.64, 36.55, 0.001, 43, -279.56, 36.55, 0.0016, 5, 8, 16.12, -3.05, 0.87432, 9, -3.06, -3.14, 0.12046, 12, 46.35, 25.8, 0.00159, 11, 68.43, 20.09, 0.00263, 42, -340.29, 51.54, 0.001, 5, 8, 23.43, -3.28, 0.0854, 9, 4.26, -3.15, 0.90521, 12, 41.01, 20.81, 0.00484, 11, 62.53, 15.77, 0.00355, 42, -342.69, 58.45, 0.001, 5, 8, 9.07, -3.03, 0.99155, 9, -10.1, -3.33, 0.00478, 12, 51.36, 30.77, 0.00061, 11, 73.99, 24.43, 0.00205, 42, -337.79, 44.96, 0.001, 5, 7, 26.19, 13.86, 0.01427, 8, 18.45, 2.94, 0.59673, 9, -0.91, 2.91, 0.38797, 10, 64.18, -48.4, 3e-05, 42, -346.71, 51.58, 0.001, 4, 7, 32.31, 18.85, 0.00707, 9, 6.98, 3.18, 0.99177, 10, 56.57, -46.3, 0.00016, 42, -349.57, 58.94, 0.001, 4, 7, 20.06, 8.61, 0.03419, 8, 10.37, 2.69, 0.95991, 9, -8.97, 2.43, 0.0049, 42, -343.6, 44.13, 0.001, 5, 12, -18.88, -5.31, 0.0021, 11, -0.05, -3.02, 0.20641, 10, 8.88, -1.75, 0.78515, 42, -342.24, 123.79, 0.00099, 43, -281.16, 123.79, 0.00535, 5, 12, -14.28, -4.16, 0.00562, 11, 4.66, -2.43, 0.87057, 10, 12.13, -5.19, 0.121, 42, -343.06, 119.12, 0.001, 43, -281.98, 119.12, 0.0018, 5, 7, 61.54, 32.22, 1e-05, 9, 38.35, -3.82, 0.00375, 12, 15.69, -2.03, 0.9906, 10, 28.72, -30.25, 0.00463, 42, -353.29, 90.86, 0.001, 4, 8, 54.75, -6.18, 0.00035, 9, 35.65, -5.12, 0.04079, 12, 16.77, 0.76, 0.95786, 42, -351.18, 88.74, 0.001, 5, 8, 55.29, -10.93, 0.00387, 9, 36.33, -9.85, 0.04651, 12, 13.04, 3.75, 0.94136, 11, 32.73, 2.17, 0.00726, 42, -346.93, 90.94, 0.001, 6, 8, 78.47, -30.41, 0.00151, 9, 60.09, -28.63, 0.00132, 11, 2.47, 3.22, 0.94386, 10, 15.39, -0.08, 0.05065, 42, -337.02, 119.56, 0.001, 43, -275.94, 119.56, 0.00166, 6, 8, 82.91, -32.14, 0.00074, 9, 64.58, -30.23, 0.00045, 11, -2.1, 1.85, 0.36794, 10, 11.58, 2.78, 0.62458, 42, -336.99, 124.32, 0.00099, 43, -275.91, 124.32, 0.00529, 4, 8, 89.54, -29.92, 0, 10, 4.6, 2.43, 0.98444, 42, -341.44, 129.72, 0.00099, 43, -280.36, 129.72, 0.01457, 4, 12, -26.12, -12.9, 0.0013, 10, -1.29, 0.82, 0.97228, 42, -346.25, 133.49, 0.00097, 43, -285.17, 133.49, 0.02544, 5, 12, -24.73, -14.3, 0.00981, 11, -6.93, -11.25, 0.00894, 10, -1.83, -1.08, 0.95589, 42, -348.09, 132.79, 0.00098, 43, -287.01, 132.79, 0.02438, 5, 12, -21.85, -9.25, 0.00296, 11, -3.46, -6.58, 0.02357, 10, 3.98, -1.11, 0.96033, 42, -344.65, 128.1, 0.00099, 43, -283.57, 128.1, 0.01215, 4, 12, 0.21, -3.27, 0.52851, 11, 19.15, -3.27, 0.45059, 10, 20.04, -17.38, 0.01991, 42, -348.13, 105.52, 0.001, 5, 8, 65.97, -21.13, 0.00385, 9, 47.31, -19.73, 0.00767, 12, -1.72, 3.44, 0.30227, 11, 18.03, 3.62, 0.68521, 42, -341.22, 104.56, 0.001, 5, 8, 72.31, -25.98, 0.00254, 9, 53.79, -24.39, 0.00316, 12, -9.64, 2.4, 0.00271, 11, 10.05, 3.53, 0.99059, 42, -338.95, 112.22, 0.001, 5, 8, 60.38, -15.86, 0.00427, 9, 41.57, -14.63, 0.0166, 12, 5.95, 3.65, 0.93567, 11, 25.67, 2.91, 0.04246, 42, -344.14, 97.46, 0.001, 3, 12, 7.43, -2.74, 0.98933, 10, 24.05, -23.4, 0.00967, 42, -350.58, 98.7, 0.001, 4, 12, -7.05, -3.69, 0.03325, 11, 11.9, -2.82, 0.92749, 10, 16.11, -11.26, 0.03826, 42, -345.57, 112.32, 0.001, 2, 6, 42.09, -4.38, 0.96, 42, -363.08, 61.61, 0.04, 2, 6, 46.13, -6.99, 0.96, 42, -359.04, 59.01, 0.04, 2, 6, 49.43, -11.11, 0.96, 42, -355.74, 54.89, 0.04, 2, 6, 51.46, -15.66, 0.96, 42, -353.71, 50.33, 0.04, 2, 6, 52.1, -20.55, 0.96, 42, -353.07, 45.45, 0.04, 2, 6, 51.58, -25.9, 0.96, 42, -353.59, 40.09, 0.04, 2, 6, 50.31, -30.93, 0.96, 42, -354.86, 35.07, 0.04, 2, 6, 46.82, -34.09, 0.96, 42, -358.35, 31.9, 0.04, 2, 6, 41.24, -7.75, 0.96, 42, -363.93, 58.25, 0.04, 2, 6, 39.76, -11.87, 0.96, 42, -365.41, 54.13, 0.04, 2, 6, 38.96, -16.78, 0.96, 42, -366.21, 49.21, 0.04, 2, 6, 38.84, -21.89, 0.96, 42, -366.32, 44.11, 0.04, 2, 6, 40.61, -27.17, 0.96, 42, -364.56, 38.82, 0.04, 2, 6, 43.68, -31.33, 0.96, 42, -361.49, 34.66, 0.04, 2, 6, 42.24, -2.06, 0.96, 42, -362.93, 63.94, 0.04, 2, 6, 46.8, -5.38, 0.96, 42, -358.37, 60.62, 0.04, 2, 6, 50.61, -9.37, 0.96, 42, -354.55, 56.62, 0.04, 2, 6, 53.29, -14.71, 0.96, 42, -351.88, 51.28, 0.04, 2, 6, 54.61, -20.62, 0.96031, 42, -350.56, 45.38, 0.03969, 2, 6, 54.4, -26.48, 0.96098, 42, -350.77, 39.52, 0.03902, 2, 6, 53.19, -32.7, 0.96158, 42, -351.98, 33.3, 0.03842, 2, 6, 51.06, -38.47, 0.96453, 42, -354.11, 27.52, 0.03547, 2, 6, 40.06, -2.42, 0.96, 42, -365.11, 63.57, 0.04, 2, 6, 39.45, -6.16, 0.96, 42, -365.72, 59.84, 0.04, 2, 6, 35.98, -13.64, 0.96, 42, -369.19, 52.35, 0.04, 2, 6, 35.13, -22.3, 0.96, 42, -370.04, 43.69, 0.04, 2, 6, 37.08, -30.45, 0.96057, 42, -368.08, 35.54, 0.03943, 2, 6, 41.81, -37.64, 0.9633, 42, -363.35, 28.36, 0.0367, 2, 6, 46.88, -38.36, 0.96399, 42, -358.28, 27.64, 0.03601, 2, 6, 46.51, 55.67, 0.96048, 42, -358.66, 121.67, 0.03952, 2, 6, 49.11, 52.13, 0.96, 42, -356.05, 118.12, 0.04, 2, 6, 50.94, 47.4, 0.96, 42, -354.23, 113.39, 0.04, 2, 6, 51.34, 42.65, 0.96, 42, -353.83, 108.65, 0.04, 2, 6, 50.58, 37.26, 0.96, 42, -354.59, 103.25, 0.04, 2, 6, 48.27, 32.4, 0.96, 42, -356.9, 98.4, 0.04, 2, 6, 45.36, 28.99, 0.96, 42, -359.81, 94.98, 0.04, 2, 6, 42.1, 26.79, 0.96, 42, -363.06, 92.78, 0.04, 2, 6, 42.96, 53.02, 0.96, 42, -362.21, 119.02, 0.04, 2, 6, 39.68, 48.63, 0.96, 42, -365.49, 114.62, 0.04, 2, 6, 38.67, 43.61, 0.96, 42, -366.49, 109.6, 0.04, 2, 6, 38.19, 38.48, 0.96, 42, -366.97, 104.47, 0.04, 2, 6, 39.26, 33.55, 0.96, 42, -365.91, 99.55, 0.04, 2, 6, 40.89, 29.68, 0.96, 42, -364.28, 95.67, 0.04, 2, 6, 41.45, 24.06, 0.96, 42, -363.71, 90.05, 0.04, 2, 6, 43.7, 25.98, 0.96, 42, -361.47, 91.98, 0.04, 2, 6, 47.28, 28.66, 0.96, 42, -357.89, 94.65, 0.04, 2, 6, 50.35, 32.25, 0.96, 42, -354.82, 98.25, 0.04, 2, 6, 52.68, 36.85, 0.96, 42, -352.49, 102.85, 0.04, 2, 6, 53.86, 43.02, 0.96013, 42, -351.3, 109.01, 0.03987, 2, 6, 53.8, 49.46, 0.96127, 42, -351.37, 115.45, 0.03873, 2, 6, 51.8, 54.97, 0.96246, 42, -353.37, 120.96, 0.03754, 2, 6, 48.87, 60.4, 0.96468, 42, -356.3, 126.39, 0.03532, 2, 6, 38.92, 24.57, 0.96, 42, -366.25, 90.56, 0.04, 2, 6, 38.56, 28.29, 0.96, 42, -366.61, 94.29, 0.04, 2, 6, 35.26, 34.92, 0.96, 42, -369.91, 100.91, 0.04, 2, 6, 34.44, 42.77, 0.96, 42, -370.72, 108.76, 0.04, 2, 6, 35.64, 52.19, 0.96175, 42, -369.53, 118.19, 0.03825, 2, 6, 40, 58.5, 0.96524, 42, -365.17, 124.5, 0.03476, 2, 6, 45.24, 60.64, 0.96553, 42, -359.93, 126.63, 0.03447, 2, 6, 34.29, -53.44, 0.9865, 42, -370.88, 12.56, 0.0135, 2, 6, 38.34, 19.69, 0.96, 42, -366.82, 85.68, 0.04, 2, 6, 37.47, 2.68, 0.96, 42, -367.7, 68.67, 0.04, 2, 6, 38.18, 16.55, 0.959, 42, -366.98, 82.55, 0.041, 2, 6, 37.63, 5.92, 0.959, 42, -367.53, 71.91, 0.041, 2, 6, 37.91, 11.23, 0.959, 42, -367.26, 77.23, 0.041, 2, 6, 46.31, 18.35, 0.959, 42, -358.86, 84.34, 0.041, 2, 6, 45.9, 5.23, 0.959, 42, -359.27, 71.22, 0.041, 2, 6, 45.79, 11.32, 0.959, 42, -359.38, 77.31, 0.041, 2, 6, 16.42, 21.72, 0.96, 42, -388.75, 87.72, 0.04, 2, 6, 13.79, 23.77, 0.96, 42, -391.38, 89.77, 0.04, 2, 6, 7.03, 24.6, 0.96, 42, -398.14, 90.59, 0.04, 2, 6, 4.24, 22.69, 0.96, 42, -400.93, 88.69, 0.04, 2, 6, 2.99, 16.7, 0.96, 42, -402.17, 82.69, 0.04, 2, 6, 2.99, 6.7, 0.96, 42, -402.18, 72.7, 0.04, 2, 6, 3.38, 0.96, 0.96, 42, -401.79, 66.95, 0.04, 2, 6, 4.8, -1.53, 0.96, 42, -400.36, 64.47, 0.04, 2, 6, 10.81, -2.71, 0.96, 42, -394.36, 63.29, 0.04, 2, 6, 15.73, -0.17, 0.96, 42, -389.44, 65.83, 0.04, 2, 6, -13.03, 29.62, 0.96, 42, -418.2, 95.62, 0.04, 2, 6, -10.01, 25.11, 0.95962, 42, -415.18, 91.11, 0.04038, 2, 6, -6.47, 19.57, 0.95864, 42, -411.64, 85.56, 0.04136, 2, 6, -5.35, 14.8, 0.95815, 42, -410.52, 80.79, 0.04185, 2, 6, -8.22, 11.06, 0.95849, 42, -413.38, 77.05, 0.04151, 2, 6, -5.72, 6.82, 0.95816, 42, -410.88, 72.81, 0.04184, 2, 6, -6.37, 2.99, 0.95852, 42, -411.54, 68.99, 0.04148, 2, 6, -9.67, -3.23, 0.95949, 42, -414.84, 62.77, 0.04051, 2, 6, -12.37, -7.91, 0.96, 42, -417.54, 58.08, 0.04, 2, 6, -14.13, 31.13, 0.96, 42, -419.29, 97.12, 0.04, 2, 6, -14.73, 26.15, 0.96, 42, -419.9, 92.15, 0.04, 2, 6, -14.35, 20.73, 0.96, 42, -419.52, 86.73, 0.04, 2, 6, -14.69, 15.32, 0.96, 42, -419.86, 81.32, 0.04, 2, 6, -16.19, 10.94, 0.96, 42, -421.36, 76.93, 0.04, 2, 6, -14.98, 5.74, 0.96, 42, -420.15, 71.74, 0.04, 2, 6, -14.96, -0.03, 0.96, 42, -420.13, 65.96, 0.04, 2, 6, -15.02, -5.72, 0.96, 42, -420.18, 60.28, 0.04, 2, 6, -14, -9.85, 0.96, 42, -419.17, 56.15, 0.04, 2, 6, -16.3, 30.35, 0.96, 42, -421.47, 96.35, 0.04, 2, 6, -21.64, 28.04, 0.95965, 42, -426.8, 94.03, 0.04035, 2, 6, -26.44, 23.49, 0.95936, 42, -431.61, 89.49, 0.04064, 2, 6, -28.93, 16.26, 0.95902, 42, -434.09, 82.26, 0.04098, 2, 6, -29.18, 7.63, 0.95901, 42, -434.35, 73.63, 0.04099, 2, 6, -26.93, 1.11, 0.95924, 42, -432.1, 67.1, 0.04076, 2, 6, -22.82, -5.32, 0.95967, 42, -427.99, 60.67, 0.04033, 2, 6, -16.47, -8.55, 0.96, 42, -421.64, 57.44, 0.04, 2, 6, -16.34, -13.03, 0.96, 42, -421.51, 52.96, 0.04, 2, 6, -10.74, -13.15, 0.96, 42, -415.91, 52.84, 0.04, 2, 6, -11.29, 33.62, 0.96, 42, -416.46, 99.61, 0.04, 2, 6, -16.25, 34.73, 0.96, 42, -421.42, 100.72, 0.04, 2, 6, -19.13, 20.34, 0.95885, 42, -424.29, 86.33, 0.04115, 2, 6, -20.71, 14.86, 0.95831, 42, -425.88, 80.86, 0.04169, 2, 6, -21.05, 7.71, 0.9584, 42, -426.22, 73.7, 0.0416, 2, 6, -19.65, 1.64, 0.95896, 42, -424.82, 67.64, 0.04104, 2, 6, -18.08, -2.84, 0.95941, 42, -423.25, 63.16, 0.04059, 2, 6, -17.3, 24.25, 0.95927, 42, -422.47, 90.24, 0.04073, 2, 6, -31.59, 18.12, 0.96158, 42, -436.76, 84.11, 0.03842, 2, 6, -31.99, 4.64, 0.96155, 42, -437.16, 70.63, 0.03845, 2, 6, -34.67, -5.61, 0.96126, 42, -439.84, 60.39, 0.03874, 2, 6, -33.96, 25.55, 0.96119, 42, -439.13, 91.55, 0.03881, 2, 6, -22.81, -19.79, 0.96491, 42, -427.97, 46.21, 0.03509, 2, 6, 6.86, -38.12, 0.97103, 42, -398.3, 27.87, 0.02897, 2, 6, 25.7, -41.54, 0.96882, 42, -379.47, 24.45, 0.03118, 2, 6, 37.7, -46.27, 0.9738, 42, -367.47, 19.73, 0.0262, 2, 6, 51.42, -47.94, 0.97726, 42, -353.75, 18.05, 0.02274, 2, 6, 21.68, -23.73, 0.96, 42, -383.48, 42.26, 0.04, 2, 6, 2.27, -19.24, 0.96061, 42, -402.89, 46.75, 0.03939, 2, 6, 27.34, -8.83, 0.96, 42, -377.83, 57.16, 0.04, 2, 6, 26.19, 28.78, 0.96, 42, -378.98, 94.77, 0.04, 2, 6, 20.63, 42.53, 0.96, 42, -384.54, 108.53, 0.04, 2, 6, 1.87, 39.42, 0.96073, 42, -403.3, 105.42, 0.03927, 2, 6, -11.35, -28.96, 0.96886, 42, -416.52, 37.03, 0.03114, 2, 6, 25.14, 59.33, 0.96699, 42, -380.03, 125.33, 0.03301, 2, 6, 7.12, 55.12, 0.9674, 42, -398.05, 121.12, 0.0326, 2, 6, -10.6, 45.82, 0.96561, 42, -415.77, 111.82, 0.03439, 2, 6, -24.15, 35.96, 0.96371, 42, -429.32, 101.95, 0.03629, 2, 6, 84.56, 25.11, 0.96227, 42, -320.61, 91.11, 0.03773, 2, 6, 84.7, 1.01, 0.96219, 42, -320.46, 67, 0.03781, 2, 6, 90.23, -21.76, 0.96693, 42, -314.94, 44.23, 0.03307, 2, 6, 89.41, 46.21, 0.96681, 42, -315.76, 112.2, 0.03319, 2, 6, -39.66, 20.01, 0.96, 42, -444.83, 86.01, 0.04, 2, 6, -41.98, 11.46, 0.96, 42, -447.15, 77.45, 0.04, 2, 6, -40.35, 1.78, 0.96, 42, -445.52, 67.78, 0.04], "hull": 44, "edges": [0, 86, 0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 22, 24, 34, 36, 36, 38, 74, 76, 76, 78, 78, 80, 84, 86, 4, 6, 10, 12, 6, 8, 8, 10, 12, 14, 14, 16, 50, 52, 50, 48, 48, 46, 32, 34, 44, 46, 24, 26, 42, 44, 26, 28, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 64, 66, 66, 68, 62, 64, 68, 70, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 104, 106, 106, 88, 102, 108, 88, 112, 112, 110, 90, 114, 114, 110, 104, 118, 118, 116, 108, 120, 120, 116, 122, 124, 126, 128, 128, 130, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 122, 132, 148, 148, 146, 130, 150, 150, 146, 126, 152, 152, 144, 124, 154, 154, 144, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 156, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 170, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 214, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 228, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 274, 10, 274, 48, 292, 276, 278, 310, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 292, 294, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 346, 348, 350, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 364, 366, 368, 370, 372, 374, 374, 376, 376, 378, 380, 378, 372, 382, 384, 386, 388, 392, 400, 398, 402, 404, 410, 412, 396, 394, 392, 414, 414, 394, 416, 418, 418, 420, 390, 422, 422, 420, 70, 72, 72, 74, 80, 82, 82, 84, 432, 434, 434, 436, 38, 40, 40, 42, 28, 30, 30, 32], "width": 149, "height": 168}}, "head2": {"head": {"type": "mesh", "uvs": [0.23495, 0.79471, 0.26742, 0.7164, 0.37695, 0.69035, 0.41792, 0.69338, 0.48449, 0.68619, 0.52247, 0.67975, 0.53784, 0.66953, 0.65792, 0.6717, 0.73377, 0.74517, 0.71973, 0.84651, 0.58897, 0.96625, 0.52503, 0.99429, 0.45389, 0.99551, 0.40538, 0.97601, 0.21548, 0.85456, 0.34483, 0.79739, 0.37242, 0.77626, 0.40647, 0.75135, 0.43737, 0.74132, 0.46461, 0.75553, 0.49082, 0.73771, 0.51682, 0.7388, 0.56097, 0.75383, 0.59437, 0.76642, 0.33571, 0.80493, 0.36935, 0.80493, 0.40513, 0.79878, 0.44144, 0.79688, 0.47188, 0.80256, 0.50552, 0.79167, 0.54397, 0.78741, 0.58188, 0.78362, 0.60858, 0.77462, 0.34265, 0.81725, 0.36241, 0.84708, 0.39659, 0.87218, 0.44678, 0.88165, 0.50445, 0.87692, 0.5461, 0.85892, 0.58562, 0.83003, 0.60195, 0.79018, 0.63169, 0.78616, 0.62795, 0.75299, 0.31682, 0.78998, 0.31345, 0.82009, 0.41167, 0.8267, 0.44941, 0.83212, 0.49735, 0.82896, 0.53663, 0.81629, 0.56519, 0.80381, 0.38412, 0.81873, 0.43659, 0.89872, 0.5267, 0.89139, 0.59713, 0.89983, 0.38897, 0.91811, 0.68196, 0.8195, 0.31167, 0.86767, 0.43053, 0.94778, 0.4894, 0.95534, 0.55253, 0.9387], "triangles": [13, 57, 12, 12, 58, 11, 12, 57, 58, 11, 59, 10, 11, 58, 59, 14, 56, 13, 56, 54, 13, 13, 54, 57, 10, 53, 9, 53, 55, 9, 10, 59, 53, 57, 51, 58, 58, 52, 59, 58, 51, 52, 57, 54, 51, 59, 52, 53, 54, 56, 35, 54, 35, 51, 35, 56, 34, 41, 55, 39, 39, 40, 41, 52, 38, 53, 38, 39, 53, 55, 53, 39, 51, 37, 52, 51, 36, 37, 51, 35, 36, 52, 37, 38, 36, 46, 37, 46, 47, 37, 36, 35, 46, 46, 35, 45, 37, 47, 38, 35, 34, 45, 45, 34, 50, 56, 44, 34, 44, 33, 34, 44, 56, 0, 47, 48, 38, 39, 48, 49, 39, 38, 48, 56, 14, 0, 50, 33, 25, 50, 34, 33, 9, 55, 8, 45, 27, 46, 46, 28, 47, 46, 27, 28, 39, 49, 40, 40, 49, 31, 47, 29, 48, 47, 28, 29, 50, 26, 45, 45, 26, 27, 44, 24, 33, 44, 43, 24, 44, 0, 43, 55, 41, 8, 50, 25, 26, 33, 24, 25, 48, 30, 49, 48, 29, 30, 24, 15, 25, 25, 16, 26, 25, 15, 16, 24, 43, 15, 49, 30, 31, 29, 28, 19, 27, 26, 17, 15, 43, 16, 26, 16, 17, 17, 18, 27, 28, 27, 19, 27, 18, 19, 0, 1, 43, 19, 20, 29, 29, 21, 30, 29, 20, 21, 40, 32, 41, 40, 31, 32, 43, 1, 16, 30, 22, 31, 30, 21, 22, 32, 42, 41, 41, 42, 8, 31, 23, 32, 31, 22, 23, 16, 2, 17, 16, 1, 2, 32, 23, 42, 23, 22, 42, 19, 18, 20, 21, 5, 22, 5, 6, 22, 22, 6, 42, 42, 7, 8, 42, 6, 7, 17, 3, 18, 17, 2, 3, 20, 18, 4, 5, 21, 4, 18, 3, 4, 21, 20, 4], "vertices": [2, 6, -10.6, 45.82, 0.96561, 42, -415.77, 111.82, 0.03439, 2, 6, 1.87, 39.42, 0.96073, 42, -403.3, 105.42, 0.03927, 2, 6, 4.24, 22.69, 0.96, 42, -400.93, 88.69, 0.04, 2, 6, 2.99, 16.7, 0.96, 42, -402.17, 82.69, 0.04, 2, 6, 2.99, 6.7, 0.96, 42, -402.18, 72.7, 0.04, 2, 6, 3.38, 0.96, 0.96, 42, -401.79, 66.95, 0.04, 2, 6, 4.8, -1.53, 0.96, 42, -400.36, 64.47, 0.04, 2, 6, 2.27, -19.24, 0.96061, 42, -402.89, 46.75, 0.03939, 2, 6, -11.35, -28.96, 0.96886, 42, -416.52, 37.03, 0.03114, 2, 6, -27.99, -24.82, 0.97385, 42, -433.16, 41.17, 0.02615, 2, 6, -45.6, -3.05, 0.96581, 42, -450.77, 62.95, 0.03419, 2, 6, -49.12, 6.98, 0.96292, 42, -454.29, 72.98, 0.03708, 2, 6, -48.04, 17.53, 0.96321, 42, -453.21, 83.52, 0.03679, 2, 6, -43.91, 24.31, 0.96469, 42, -449.08, 90.3, 0.03531, 2, 6, -20.23, 49.92, 0.97606, 42, -425.4, 115.91, 0.02394, 2, 6, -13.03, 29.62, 0.96, 42, -418.2, 95.62, 0.04, 2, 6, -10.01, 25.11, 0.95962, 42, -415.18, 91.11, 0.04038, 2, 6, -6.47, 19.57, 0.95864, 42, -411.64, 85.56, 0.04136, 2, 6, -5.35, 14.8, 0.95815, 42, -410.52, 80.79, 0.04185, 2, 6, -8.22, 11.06, 0.95849, 42, -413.38, 77.05, 0.04151, 2, 6, -5.72, 6.82, 0.95816, 42, -410.88, 72.81, 0.04184, 2, 6, -6.37, 2.99, 0.95852, 42, -411.54, 68.99, 0.04148, 2, 6, -9.67, -3.23, 0.95949, 42, -414.84, 62.77, 0.04051, 2, 6, -12.37, -7.91, 0.96, 42, -417.54, 58.08, 0.04, 2, 6, -14.13, 31.13, 0.96, 42, -419.29, 97.12, 0.04, 2, 6, -14.73, 26.15, 0.96, 42, -419.9, 92.15, 0.04, 2, 6, -14.35, 20.73, 0.96, 42, -419.52, 86.73, 0.04, 2, 6, -14.69, 15.32, 0.96, 42, -419.86, 81.32, 0.04, 2, 6, -16.19, 10.94, 0.96, 42, -421.36, 76.93, 0.04, 2, 6, -14.98, 5.74, 0.96, 42, -420.15, 71.74, 0.04, 2, 6, -14.96, -0.03, 0.96, 42, -420.13, 65.96, 0.04, 2, 6, -15.02, -5.72, 0.96, 42, -420.18, 60.28, 0.04, 2, 6, -14, -9.85, 0.96, 42, -419.17, 56.15, 0.04, 2, 6, -16.3, 30.35, 0.96, 42, -421.47, 96.35, 0.04, 2, 6, -21.64, 28.04, 0.95965, 42, -426.8, 94.03, 0.04035, 2, 6, -26.44, 23.49, 0.95936, 42, -431.61, 89.49, 0.04064, 2, 6, -28.93, 16.26, 0.95902, 42, -434.09, 82.26, 0.04098, 2, 6, -29.18, 7.63, 0.95901, 42, -434.35, 73.63, 0.04099, 2, 6, -26.93, 1.11, 0.95924, 42, -432.1, 67.1, 0.04076, 2, 6, -22.82, -5.32, 0.95967, 42, -427.99, 60.67, 0.04033, 2, 6, -16.47, -8.55, 0.96, 42, -421.64, 57.44, 0.04, 2, 6, -16.34, -13.03, 0.96, 42, -421.51, 52.96, 0.04, 2, 6, -10.74, -13.15, 0.96, 42, -415.91, 52.84, 0.04, 2, 6, -11.29, 33.62, 0.96, 42, -416.46, 99.61, 0.04, 2, 6, -16.25, 34.73, 0.96, 42, -421.42, 100.72, 0.04, 2, 6, -19.13, 20.34, 0.95885, 42, -424.29, 86.33, 0.04115, 2, 6, -20.71, 14.86, 0.95831, 42, -425.88, 80.86, 0.04169, 2, 6, -21.05, 7.71, 0.9584, 42, -426.22, 73.7, 0.0416, 2, 6, -19.65, 1.64, 0.95896, 42, -424.82, 67.64, 0.04104, 2, 6, -18.08, -2.84, 0.95941, 42, -423.25, 63.16, 0.04059, 2, 6, -17.3, 24.25, 0.95927, 42, -422.47, 90.24, 0.04073, 2, 6, -31.59, 18.12, 0.96158, 42, -436.76, 84.11, 0.03842, 2, 6, -31.99, 4.64, 0.96155, 42, -437.16, 70.63, 0.03845, 2, 6, -34.67, -5.61, 0.96126, 42, -439.84, 60.39, 0.03874, 2, 6, -33.96, 25.55, 0.96119, 42, -439.13, 91.55, 0.03881, 2, 6, -22.81, -19.79, 0.96491, 42, -427.97, 46.21, 0.03509, 2, 6, -24.15, 35.96, 0.96371, 42, -429.32, 101.95, 0.03629, 2, 6, -39.66, 20.01, 0.96, 42, -444.83, 86.01, 0.04, 2, 6, -41.98, 11.46, 0.96, 42, -447.15, 77.45, 0.04, 2, 6, -40.35, 1.78, 0.96, 42, -445.52, 67.78, 0.04], "hull": 15, "edges": [18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 4, 6, 6, 8, 8, 10, 10, 12, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 82, 84, 86, 88, 90, 92, 92, 94, 94, 96, 98, 96, 90, 100, 102, 104, 106, 110, 110, 16, 108, 112, 112, 0, 114, 116, 116, 118, 4, 2, 2, 0, 0, 28, 14, 16, 16, 18, 14, 12], "width": 149, "height": 168}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.85793, 0.06751, 0.91374, 0.12608, 0.9609, 0.21483, 0.98848, 0.34266, 1, 0.50108, 0.98824, 0.61571, 0.95147, 0.74994, 0.87787, 0.87886, 0.79539, 0.95158, 0.69906, 0.99455, 0.5745, 0.99631, 0.47914, 0.97678, 0.39138, 0.92517, 0.21755, 0.76544, 0.10721, 0.68311, 0.05006, 0.62044, 0.00637, 0.50782, 0.00221, 0.41393, 0.01454, 0.3208, 0.05823, 0.22723, 0.12056, 0.17066, 0.21016, 0.13265, 0.3601, 0.07228, 0.46409, 0.05327, 0.65514, 0.00719, 0.74512, 0.00115, 0.57369, 0.09258, 0.64524, 0.15695, 0.72046, 0.29184, 0.75927, 0.46922, 0.74807, 0.6588, 0.70796, 0.80885, 0.62773, 0.91662, 0.50645, 0.94779, 0.40253, 0.59372, 0.24893, 0.24872, 0.28965, 0.32849, 0.34318, 0.44672, 0.20121, 0.19243, 0.40025, 0.12793, 0.12989, 0.36869, 0.16934, 0.50538, 0.24602, 0.64921, 0.35161, 0.72268, 0.12333, 0.26928, 0.73546, 0.0631, 0.80233, 0.14321, 0.86261, 0.29779, 0.88593, 0.48164, 0.86676, 0.68355, 0.79965, 0.86356, 0.67385, 0.96764], "triangles": [13, 43, 12, 43, 42, 34, 13, 14, 42, 42, 41, 37, 13, 42, 43, 14, 41, 42, 14, 15, 41, 41, 15, 16, 41, 16, 40, 43, 34, 32, 33, 12, 43, 11, 12, 33, 32, 33, 43, 31, 34, 30, 50, 30, 49, 7, 50, 49, 31, 30, 50, 6, 7, 49, 8, 50, 7, 8, 9, 50, 32, 34, 31, 50, 51, 31, 51, 32, 31, 51, 50, 9, 10, 33, 32, 10, 32, 51, 10, 51, 9, 11, 33, 10, 39, 22, 23, 34, 37, 29, 38, 20, 21, 38, 21, 22, 38, 22, 39, 35, 38, 39, 44, 20, 38, 19, 20, 44, 36, 35, 27, 35, 40, 44, 35, 44, 38, 40, 35, 36, 18, 19, 44, 18, 44, 40, 37, 36, 28, 27, 35, 39, 18, 40, 17, 40, 16, 17, 41, 40, 36, 41, 36, 37, 42, 37, 34, 36, 27, 28, 37, 28, 29, 30, 34, 29, 39, 23, 26, 45, 24, 25, 45, 25, 0, 26, 24, 45, 46, 45, 0, 27, 26, 45, 27, 45, 46, 28, 27, 46, 1, 46, 0, 47, 1, 2, 47, 46, 1, 28, 46, 47, 47, 2, 3, 29, 28, 47, 48, 47, 3, 29, 47, 48, 48, 3, 4, 5, 48, 4, 48, 30, 29, 49, 48, 5, 26, 23, 24, 27, 39, 26, 6, 49, 5, 49, 30, 48], "vertices": [1, 46, 5.73, -39.59, 1, 2, 46, -8.27, -56.04, 0.99989, 28, 227.08, 57.76, 0.00011, 1, 46, -28.8, -69.42, 1, 2, 46, -57.64, -76.25, 0.95103, 1, 195.07, 103.42, 0.04897, 2, 46, -92.95, -77.72, 0.77898, 1, 198.63, 68.25, 0.22102, 2, 46, -118.14, -72.59, 0.63935, 1, 195, 42.81, 0.36065, 2, 46, -147.22, -59.48, 0.45602, 1, 183.63, 13.01, 0.54398, 2, 46, -174.44, -35.09, 0.24695, 1, 160.89, -15.61, 0.75305, 2, 46, -189.05, -8.69, 0.10091, 1, 135.41, -31.76, 0.89909, 3, 46, -196.81, 21.59, 0.05982, 28, 160.75, -135.04, 0.02571, 1, 105.64, -41.3, 0.91446, 3, 46, -194.93, 60.03, 0.32369, 28, 122.26, -135.43, 0.12008, 1, 67.15, -41.69, 0.55623, 3, 46, -188.85, 89.19, 0.27633, 28, 92.79, -131.09, 0.22492, 1, 37.68, -37.35, 0.49876, 4, 46, -175.81, 115.58, 0.25935, 28, 65.68, -119.64, 0.35534, 1, 10.57, -25.89, 0.3101, 29, 128.51, 45.94, 0.0752, 4, 46, -137.24, 167.1, 0.03495, 28, 11.96, -84.18, 0.23366, 1, -43.15, 9.57, 0.01287, 29, 85.01, -1.5, 0.71851, 3, 46, -116.97, 200.06, 0.0028, 28, -22.13, -65.9, 0.10045, 29, 61.57, -32.27, 0.89675, 3, 46, -102.04, 216.86, 0.00167, 28, -39.79, -51.99, 0.29155, 29, 45.04, -47.51, 0.70678, 2, 28, -53.29, -26.98, 0.51609, 29, 18.22, -56.89, 0.48391, 2, 28, -54.58, -6.14, 0.66243, 29, -2.56, -54.86, 0.33757, 2, 28, -50.77, 14.53, 0.80753, 29, -22.38, -47.83, 0.19247, 3, 46, -15.05, 209.18, 0.00058, 28, -37.27, 35.31, 0.94469, 29, -40.76, -31.22, 0.05473, 2, 46, -3.65, 189.21, 0.01447, 28, -18.01, 47.86, 0.98553, 2, 46, 3.13, 161.07, 0.12625, 28, 9.68, 56.31, 0.87375, 2, 46, 13.77, 114.03, 0.40393, 28, 56.01, 69.71, 0.59607, 2, 46, 16.08, 81.7, 0.57761, 28, 88.14, 73.93, 0.42239, 2, 46, 22.8, 22.17, 0.94401, 28, 147.18, 84.16, 0.05599, 2, 46, 22.49, -5.67, 0.99913, 28, 174.98, 85.5, 0.00087, 2, 46, 5.37, 48.41, 0.66925, 28, 122.01, 65.2, 0.33075, 2, 46, -10.21, 27.19, 0.73944, 28, 144.12, 50.91, 0.26056, 3, 46, -41.48, 5.76, 0.69983, 28, 167.36, 20.96, 0.2408, 1, 112.25, 114.7, 0.05937, 3, 46, -81.49, -3.88, 0.54249, 28, 179.35, -18.42, 0.19797, 1, 124.24, 75.33, 0.25954, 3, 46, -123.3, 2.06, 0.35971, 28, 175.89, -60.5, 0.17266, 1, 120.78, 33.24, 0.46763, 3, 46, -155.82, 16.4, 0.19684, 28, 163.5, -93.81, 0.14036, 1, 108.39, -0.07, 0.66281, 3, 46, -178.24, 42.57, 0.11731, 28, 138.71, -117.74, 0.14602, 1, 83.6, -24, 0.73667, 3, 46, -182.93, 80.38, 0.23731, 28, 101.23, -124.66, 0.22391, 1, 46.12, -30.91, 0.53878, 4, 46, -102.56, 107.79, 0.2161, 28, 69.12, -46.05, 0.4595, 1, 14.01, 47.69, 0.1219, 29, 56.4, 60.97, 0.2025, 3, 46, -23.3, 150.64, 0.09264, 28, 21.66, 30.54, 0.82193, 29, -26.73, 26.21, 0.08543, 3, 46, -41.72, 139.12, 0.1148, 28, 34.24, 12.83, 0.69749, 29, -7.25, 35.84, 0.18771, 3, 46, -68.9, 124.17, 0.16179, 28, 50.78, -13.42, 0.57676, 29, 21.28, 48.02, 0.26144, 3, 46, -9.95, 164.62, 0.06197, 28, 6.91, 43.03, 0.93528, 29, -41.4, 13.63, 0.00275, 2, 46, 0.7, 102.37, 0.38858, 28, 68.42, 57.35, 0.61142, 2, 28, -15.13, 3.9, 0.59347, 29, -6.25, -14.32, 0.40653, 3, 46, -78.72, 178.56, 0.00554, 28, -2.94, -26.44, 0.2911, 29, 25.65, -7.08, 0.70335, 4, 46, -112, 156.79, 0.03524, 28, 20.76, -58.37, 0.26544, 1, -34.35, 35.37, 0.00951, 29, 60.92, 11.27, 0.68981, 4, 46, -130.21, 125.19, 0.1337, 28, 53.39, -74.68, 0.36859, 1, -1.72, 19.06, 0.15223, 29, 82.18, 40.91, 0.34549, 3, 46, -25.56, 189.65, 0.00391, 28, -17.15, 25.97, 0.81117, 29, -28.36, -12.83, 0.18492, 2, 46, 8.94, -1.87, 0.99929, 28, 171.99, 71.74, 0.00071, 2, 46, -10.03, -21.45, 0.99816, 28, 192.66, 53.96, 0.00184, 3, 46, -45.39, -38.01, 0.91813, 28, 211.29, 19.64, 0.01939, 1, 156.18, 113.38, 0.06248, 3, 46, -86.56, -42.79, 0.69574, 28, 218.49, -21.17, 0.02387, 1, 163.38, 72.57, 0.28039, 3, 46, -130.96, -34.23, 0.42812, 28, 212.57, -66, 0.03264, 1, 157.46, 27.75, 0.53924, 3, 46, -169.62, -11.16, 0.18479, 28, 191.83, -105.96, 0.01908, 1, 136.72, -12.22, 0.79613, 3, 46, -190.39, 29.01, 0.09148, 28, 152.96, -129.06, 0.06571, 1, 97.85, -35.32, 0.84281], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 30, 32, 34, 36, 48, 50, 4, 6, 6, 8, 28, 30, 26, 28, 22, 24, 24, 26, 32, 34, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 46, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 24, 68, 60, 54, 70, 56, 72, 74, 58, 52, 78, 78, 76, 86, 64, 90, 52, 54, 92, 56, 94, 58, 96, 60, 98, 62, 100, 64, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90], "width": 309, "height": 222}}, "leg_Lb": {"leg_Lb": {"type": "mesh", "uvs": [0.45065, 0.02944, 0.48626, 0.05512, 0.53064, 0.08813, 0.57066, 0.13072, 0.57031, 0.15914, 0.52431, 0.18062, 0.60706, 0.22473, 0.64997, 0.26199, 0.68267, 0.30943, 0.69008, 0.35928, 0.66225, 0.44892, 0.59071, 0.59593, 0.58267, 0.62393, 0.58195, 0.64797, 0.6024, 0.66418, 0.64162, 0.666, 0.7108, 0.6787, 0.74654, 0.70208, 0.74558, 0.74784, 0.73822, 0.75641, 0.78267, 0.8216, 0.75001, 0.82837, 0.72492, 0.81019, 0.73426, 0.83749, 0.75441, 0.8622, 0.76891, 0.89009, 0.7627, 0.91079, 0.72826, 0.9341, 0.66103, 0.95952, 0.53394, 0.99872, 0.40827, 0.99852, 0.36694, 0.96892, 0.3471, 0.93815, 0.34823, 0.90315, 0.37311, 0.86036, 0.37257, 0.83153, 0.37203, 0.80219, 0.36499, 0.72299, 0.33879, 0.6831, 0.35167, 0.64977, 0.35299, 0.61919, 0.34345, 0.58609, 0.31471, 0.54694, 0.09855, 0.30403, 0.06685, 0.25485, 0.04492, 0.19582, 0.00736, 0.15168, 0.00548, 0.10869, 0.03574, 0.07631, 0.0871, 0.05386, 0.1566, 0.03869, 0.27577, 0.02533, 0.41881, 0.00929, 0.27435, 0.04694, 0.33528, 0.0683, 0.40347, 0.0988, 0.45772, 0.13374, 0.14161, 0.06571, 0.09632, 0.10973, 0.11026, 0.15437, 0.14509, 0.20149, 0.3861, 0.23257, 0.34312, 0.18122, 0.3048, 0.13534, 0.23861, 0.09008, 0.18531, 0.25563, 0.26819, 0.37301, 0.4028, 0.50839, 0.47277, 0.35892, 0.45473, 0.28359, 0.21634, 0.3121], "triangles": [62, 63, 56, 62, 56, 5, 53, 50, 51, 57, 49, 50, 51, 0, 53, 54, 53, 0, 53, 57, 50, 64, 53, 54, 64, 57, 53, 57, 48, 49, 58, 57, 64, 58, 48, 57, 47, 48, 58, 55, 64, 54, 63, 58, 64, 46, 47, 58, 55, 63, 64, 63, 55, 56, 4, 56, 3, 5, 56, 4, 0, 51, 52, 54, 0, 1, 56, 2, 3, 55, 54, 1, 55, 1, 2, 56, 55, 2, 34, 25, 27, 26, 27, 25, 33, 34, 27, 28, 32, 33, 27, 28, 33, 31, 32, 28, 29, 30, 31, 28, 29, 31, 38, 13, 14, 13, 38, 39, 37, 38, 14, 17, 37, 16, 17, 18, 37, 36, 37, 19, 14, 16, 37, 15, 16, 14, 37, 18, 19, 22, 36, 19, 22, 19, 20, 21, 22, 20, 35, 36, 22, 35, 22, 23, 34, 35, 23, 24, 25, 34, 24, 34, 23, 61, 62, 5, 61, 5, 6, 69, 61, 6, 69, 6, 7, 69, 7, 8, 70, 61, 69, 43, 65, 70, 68, 69, 8, 70, 69, 68, 68, 8, 9, 66, 70, 68, 10, 68, 9, 67, 66, 68, 67, 68, 10, 42, 66, 67, 43, 70, 66, 42, 43, 66, 41, 42, 67, 11, 67, 10, 41, 67, 11, 40, 41, 11, 12, 40, 11, 12, 39, 40, 13, 39, 12, 70, 65, 61, 61, 65, 60, 62, 59, 63, 44, 60, 65, 61, 60, 62, 62, 60, 59, 45, 59, 60, 43, 44, 65, 44, 45, 60, 45, 46, 59, 63, 59, 58, 46, 58, 59], "vertices": [2, 28, 51.09, 53.24, 0.65816, 46, -2.38, 119.91, 0.34184, 2, 28, 59.5, 36.21, 0.62856, 46, -19.88, 112.53, 0.37144, 2, 28, 69.97, 14.32, 0.59538, 46, -42.34, 103.37, 0.40462, 3, 29, 26.29, 76.22, 0.00485, 28, 79.41, -13.91, 0.79109, 46, -71.09, 95.61, 0.20406, 3, 29, 44.89, 73.16, 0.06538, 28, 79.33, -32.76, 0.89358, 46, -89.89, 96.81, 0.04104, 2, 29, 57.23, 60.18, 0.19919, 28, 68.47, -47, 0.80081, 2, 29, 89.2, 74.85, 0.75391, 28, 88.01, -76.24, 0.24609, 2, 29, 115.19, 80.94, 0.87714, 28, 98.13, -100.94, 0.12286, 1, 29, 147.47, 83.59, 1, 1, 29, 180.38, 80.09, 1, 1, 29, 238.02, 64.21, 1, 2, 29, 331.6, 32.13, 0.99899, 30, -33.52, 29.87, 0.00101, 2, 29, 349.63, 27.32, 0.89256, 30, -15.2, 26.33, 0.10744, 2, 29, 365.34, 24.64, 0.44752, 30, 0.67, 24.74, 0.55248, 2, 29, 376.72, 27.7, 0.11392, 30, 11.8, 28.59, 0.88608, 2, 29, 379.37, 36.65, 0.02382, 30, 13.82, 37.7, 0.97618, 1, 30, 23.67, 53.22, 1, 1, 30, 39.86, 60.24, 1, 1, 30, 70.05, 57.31, 1, 1, 30, 75.56, 55.07, 1, 1, 30, 119.54, 61.67, 1, 1, 30, 123.33, 53.6, 1, 2, 30, 110.79, 48.77, 0.98949, 31, -54.26, 30.46, 0.01051, 2, 30, 129.02, 49.36, 0.87228, 31, -37.62, 37.92, 0.12772, 2, 30, 145.75, 52.64, 0.6564, 31, -23.38, 47.3, 0.3436, 2, 30, 164.48, 54.4, 0.40708, 31, -6.72, 56.03, 0.59292, 2, 30, 178.02, 51.71, 0.26434, 31, 6.82, 58.69, 0.73566, 2, 30, 192.69, 42.24, 0.12068, 31, 23.99, 55.49, 0.87932, 2, 30, 208.06, 24.94, 0.01471, 31, 44.78, 45.31, 0.98529, 1, 31, 78.47, 24.34, 1, 1, 31, 87.11, -4.04, 1, 1, 31, 71.24, -19.15, 1, 2, 30, 187.36, -47.59, 0.00725, 31, 53.14, -29.65, 0.99275, 2, 30, 164.26, -45.26, 0.11436, 31, 30.89, -36.25, 0.88564, 2, 30, 136.53, -36.89, 0.63637, 31, 2.05, -39.03, 0.36363, 2, 30, 117.48, -35.31, 0.92394, 31, -16.18, -44.8, 0.07606, 2, 30, 98.09, -33.71, 0.99558, 31, -34.72, -50.66, 0.00442, 1, 30, 45.65, -30.69, 1, 2, 29, 379.26, -35.71, 0.08652, 30, 18.75, -34.49, 0.91348, 2, 29, 357.93, -29.22, 0.55788, 30, -2.99, -29.49, 0.44212, 2, 29, 337.96, -25.7, 0.96432, 30, -23.16, -27.38, 0.03568, 1, 29, 315.93, -24.46, 1, 1, 29, 289.23, -27.05, 1, 2, 29, 122.14, -51.96, 0.94571, 28, -32, -128.82, 0.05429, 2, 29, 88.76, -54.2, 0.85109, 28, -39.49, -96.22, 0.14891, 2, 29, 49.3, -53.12, 0.68573, 28, -44.66, -57.07, 0.31427, 2, 29, 19, -57.25, 0.49911, 28, -53.53, -27.81, 0.50089, 2, 29, -9.21, -53.18, 0.30076, 28, -53.97, 0.69, 0.69924, 2, 29, -29.28, -42.73, 0.15336, 28, -46.83, 22.16, 0.84664, 2, 29, -42.07, -28.41, 0.05339, 28, -34.71, 37.04, 0.94661, 1, 28, -18.3, 47.1, 1, 2, 28, 9.82, 55.96, 0.86781, 46, 2.78, 160.95, 0.13219, 2, 28, 43.58, 66.59, 0.67303, 46, 11.39, 126.62, 0.32697, 3, 29, -39.61, 15.95, 0.0132, 28, 9.49, 41.63, 0.86486, 46, -11.5, 162.13, 0.12194, 3, 29, -23.35, 27.91, 0.08767, 28, 23.87, 27.47, 0.73294, 46, -26.49, 148.62, 0.17939, 3, 29, -0.84, 40.6, 0.15614, 28, 39.96, 7.25, 0.63792, 46, -47.63, 133.75, 0.20594, 3, 29, 24.05, 49.58, 0.21993, 28, 52.76, -15.91, 0.6533, 46, -71.51, 122.34, 0.12677, 2, 29, -32.27, -16.95, 0.15484, 28, -21.84, 29.19, 0.84516, 2, 29, -5.14, -32.12, 0.41337, 28, -32.53, 0, 0.58663, 2, 29, 24.6, -33.55, 0.69139, 28, -29.24, -29.59, 0.30861, 2, 29, 56.75, -30.37, 0.92469, 28, -21.02, -60.83, 0.07531, 2, 29, 86.08, 22.53, 0.81472, 28, 35.86, -81.44, 0.18528, 2, 29, 50.87, 17.9, 0.62339, 28, 25.71, -47.4, 0.37661, 2, 29, 19.4, 13.78, 0.53108, 28, 16.67, -16.98, 0.46892, 3, 29, -12.7, 3.1, 0.30448, 28, 1.05, 13.03, 0.67129, 46, -39.56, 172.24, 0.02423, 1, 29, 93.69, -26.68, 1, 1, 29, 173.63, -19.66, 1, 1, 29, 267.28, -2.48, 1, 1, 29, 172.03, 29.49, 1, 2, 29, 122.04, 33.18, 0.95143, 28, 52.05, -115.27, 0.04857, 1, 29, 131.82, -25.36, 1], "hull": 53, "edges": [8, 10, 18, 20, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 56, 58, 58, 60, 60, 62, 62, 64, 84, 86, 92, 94, 10, 12, 12, 14, 14, 16, 16, 18, 24, 26, 26, 28, 28, 30, 30, 32, 20, 22, 22, 24, 98, 100, 94, 96, 96, 98, 90, 92, 86, 88, 88, 90, 80, 82, 82, 84, 76, 78, 78, 80, 72, 74, 74, 76, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 64, 66, 66, 68, 68, 70, 70, 72, 0, 104, 0, 2, 2, 4, 6, 8, 4, 6, 100, 102, 102, 104, 132, 134, 134, 136, 122, 138, 138, 136, 130, 140, 140, 132], "width": 236, "height": 663}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [0.61275, 0.01087, 0.74951, 0.03109, 0.84352, 0.05259, 0.92404, 0.08211, 0.97392, 0.11309, 0.99525, 0.14174, 0.99649, 0.17782, 0.96981, 0.21998, 0.93742, 0.26062, 0.92327, 0.29831, 0.90054, 0.33174, 0.86678, 0.35612, 0.82189, 0.38271, 0.79445, 0.42107, 0.75615, 0.46417, 0.68467, 0.50846, 0.55284, 0.57761, 0.44015, 0.64792, 0.38774, 0.68816, 0.36485, 0.70981, 0.34893, 0.73081, 0.34355, 0.74865, 0.34615, 0.76962, 0.3395, 0.79873, 0.3531, 0.83923, 0.36289, 0.8667, 0.36928, 0.89363, 0.38702, 0.91972, 0.39003, 0.94298, 0.37591, 0.96606, 0.36433, 0.98832, 0.30717, 0.99772, 0.15863, 0.99881, 0.07275, 0.98776, 0.03015, 0.97582, 0.00478, 0.96012, 0.00205, 0.93916, 0.01715, 0.91707, 0.03306, 0.89376, 0.03401, 0.876, 0.02258, 0.84882, 0.00904, 0.80719, 0.0349, 0.78544, 0.09196, 0.76302, 0.09521, 0.7456, 0.1125, 0.73108, 0.1429, 0.71254, 0.15915, 0.69323, 0.1655, 0.66806, 0.17401, 0.62518, 0.19346, 0.54535, 0.21509, 0.4852, 0.25623, 0.42448, 0.31703, 0.37678, 0.36375, 0.34957, 0.40821, 0.32937, 0.4282, 0.31621, 0.43609, 0.29568, 0.38956, 0.28437, 0.33172, 0.26718, 0.28479, 0.25078, 0.21516, 0.23259, 0.16317, 0.20355, 0.14605, 0.16549, 0.16556, 0.12083, 0.21146, 0.08786, 0.26928, 0.0603, 0.35099, 0.03429, 0.42618, 0.01452, 0.49014, 0.00066, 0.25612, 0.22886, 0.25372, 0.19638, 0.29933, 0.16057, 0.38176, 0.13135, 0.50499, 0.11141, 0.6381, 0.10848, 0.76614, 0.12254, 0.87897, 0.1517, 0.95799, 0.19147, 0.54741, 0.03368, 0.69472, 0.05335, 0.81668, 0.07119, 0.90325, 0.09146, 0.84935, 0.28808, 0.82512, 0.32501, 0.76836, 0.35715, 0.70449, 0.3824, 0.86208, 0.24883, 0.52883, 0.36626, 0.55837, 0.3387, 0.57223, 0.3052, 0.61669, 0.27321, 0.62962, 0.23742, 0.52151, 0.24839, 0.47103, 0.27204, 0.75, 0.23561, 0.74989, 0.27948, 0.72105, 0.31472, 0.67694, 0.35159, 0.62091, 0.37784, 0.67891, 0.42447, 0.61137, 0.47615, 0.3719, 0.57689, 0.39646, 0.44867, 0.44763, 0.40288, 0.55846, 0.42293], "triangles": [63, 64, 72, 71, 63, 72, 61, 62, 71, 62, 63, 71, 70, 61, 71, 61, 70, 60, 75, 80, 81, 81, 1, 2, 76, 75, 81, 80, 1, 81, 7, 78, 6, 78, 77, 6, 76, 81, 82, 77, 76, 82, 81, 2, 3, 82, 81, 3, 77, 82, 4, 77, 4, 5, 82, 3, 4, 77, 5, 6, 34, 35, 36, 28, 33, 27, 37, 33, 36, 33, 34, 36, 33, 28, 32, 27, 33, 37, 32, 28, 29, 30, 31, 29, 31, 32, 29, 44, 21, 22, 22, 43, 44, 23, 43, 22, 23, 40, 41, 40, 24, 39, 42, 43, 23, 42, 23, 41, 24, 40, 23, 39, 26, 38, 39, 24, 25, 27, 38, 26, 26, 39, 25, 27, 37, 38, 56, 57, 90, 89, 56, 90, 89, 90, 97, 55, 56, 89, 98, 89, 97, 11, 84, 10, 85, 97, 84, 85, 84, 11, 98, 97, 85, 88, 55, 89, 54, 55, 88, 99, 89, 98, 88, 89, 99, 86, 98, 85, 99, 98, 86, 12, 85, 11, 86, 85, 12, 104, 54, 88, 53, 54, 104, 13, 86, 12, 105, 88, 99, 104, 88, 105, 100, 99, 86, 100, 86, 13, 105, 99, 100, 52, 53, 104, 103, 52, 104, 103, 104, 105, 14, 100, 13, 101, 105, 100, 101, 100, 14, 103, 105, 101, 51, 52, 103, 15, 101, 14, 102, 51, 103, 102, 103, 101, 16, 102, 101, 50, 51, 102, 15, 16, 101, 49, 50, 102, 17, 102, 16, 49, 102, 17, 48, 49, 17, 18, 48, 17, 47, 48, 18, 19, 47, 18, 19, 46, 47, 20, 46, 19, 21, 46, 20, 45, 46, 21, 21, 44, 45, 78, 87, 77, 93, 72, 73, 94, 60, 70, 71, 93, 70, 57, 94, 90, 57, 58, 94, 90, 91, 97, 84, 97, 83, 10, 84, 9, 87, 95, 77, 95, 92, 76, 93, 73, 92, 8, 87, 7, 70, 93, 94, 91, 93, 92, 94, 93, 91, 96, 92, 95, 96, 95, 87, 91, 92, 96, 58, 59, 94, 83, 96, 87, 83, 87, 8, 9, 83, 8, 90, 94, 91, 97, 91, 96, 97, 96, 83, 84, 83, 9, 76, 92, 75, 94, 59, 60, 7, 87, 78, 72, 93, 71, 92, 74, 75, 95, 76, 77, 72, 64, 73, 73, 74, 92, 79, 69, 0, 68, 69, 79, 80, 0, 1, 79, 0, 80, 75, 79, 80, 79, 67, 68, 64, 65, 73, 73, 65, 66, 74, 73, 66, 74, 79, 75, 74, 67, 79, 66, 67, 74], "vertices": [2, 47, 16.83, -15.27, 0.8625, 2, 133.24, 122.37, 0.1375, 3, 47, -1.97, -50.32, 0.41561, 52, 217.27, 61.27, 3e-05, 2, 114.44, 87.31, 0.58436, 3, 47, -21.17, -74.05, 0.12057, 52, 208.41, 32.07, 0.01229, 2, 95.24, 63.59, 0.86714, 1, 2, 69.64, 43.81, 1, 1, 2, 43.32, 32.18, 1, 2, 52, 151.49, -29.48, 0.03714, 2, 19.36, 27.96, 0.96286, 3, 47, -126.82, -108.24, 0.03808, 52, 123.38, -39.35, 0.19472, 2, -10.41, 29.39, 0.7672, 3, 47, -161.16, -99.15, 0.04166, 52, 88.13, -43.84, 0.46597, 2, -44.75, 38.48, 0.49237, 4, 47, -194.15, -88.64, 0.0391, 52, 53.62, -46.5, 0.72812, 53, -61.39, 46.29, 0.0034, 2, -77.73, 49, 0.22939, 4, 47, -224.58, -83.18, 0.03526, 52, 23.37, -52.89, 0.70921, 53, -31.15, 52.71, 0.18282, 2, -108.17, 54.45, 0.07271, 3, 47, -250.92, -75.8, 0.00308, 52, -3.82, -55.96, 0.45472, 53, -3.96, 55.8, 0.5422, 2, 52, -25.32, -53.93, 0.1737, 53, 17.54, 53.79, 0.8263, 2, 52, -49.86, -49.74, 0.01795, 53, 42.09, 49.62, 0.98205, 1, 53, 74.59, 52.97, 1, 1, 53, 111.75, 54.86, 1, 1, 53, 152.67, 48.78, 1, 1, 53, 218.31, 34.23, 1, 1, 53, 283.22, 24.77, 1, 1, 53, 319.34, 22.37, 1, 1, 53, 338.31, 22.41, 1, 2, 53, 356.19, 24.02, 0.94377, 35, 161.58, -30.12, 0.05623, 2, 53, 370.7, 27.43, 0.66355, 35, 146.71, -29.22, 0.33645, 2, 53, 386.93, 33.65, 0.23341, 35, 129.38, -30.53, 0.76659, 2, 53, 410.27, 39.72, 0.00909, 35, 105.28, -29.65, 0.99091, 1, 35, 71.99, -34.47, 1, 2, 35, 49.41, -37.88, 0.99061, 34, 102.27, -45.61, 0.00939, 2, 35, 27.24, -40.38, 0.8709, 34, 79.99, -44.36, 0.1291, 2, 35, 5.87, -45.84, 0.53488, 34, 58.01, -46.18, 0.46512, 2, 35, -13.3, -47.34, 0.23547, 34, 38.86, -44.45, 0.76453, 2, 35, -32.49, -44.31, 0.0543, 34, 20.45, -38.25, 0.9457, 2, 35, -50.97, -41.93, 0.00277, 34, 2.63, -32.81, 0.99723, 1, 34, -3.09, -16.83, 1, 1, 34, 1.16, 22.17, 1, 2, 35, -53.32, 35.01, 0.02326, 34, 13.19, 43.44, 0.97674, 2, 35, -43.89, 46.61, 0.07489, 34, 24.44, 53.3, 0.92511, 2, 35, -31.17, 53.78, 0.1531, 34, 38.17, 58.24, 0.8469, 2, 35, -13.89, 55.13, 0.31544, 34, 55.43, 56.68, 0.68456, 2, 35, 4.49, 51.82, 0.58173, 34, 73, 50.33, 0.41827, 2, 35, 23.89, 48.32, 0.86569, 34, 91.54, 43.64, 0.13431, 2, 35, 38.55, 48.61, 0.96886, 34, 106.05, 41.47, 0.03114, 1, 35, 60.88, 52.44, 1, 1, 35, 95.11, 57.27, 1, 2, 53, 425.72, -39.96, 0.00933, 35, 113.32, 51.11, 0.99067, 2, 53, 403.37, -31.65, 0.16328, 35, 132.35, 36.74, 0.83672, 2, 53, 389.47, -35.46, 0.44678, 35, 146.76, 36.41, 0.55322, 2, 53, 376.61, -35, 0.6927, 35, 158.94, 32.28, 0.3073, 2, 53, 359.44, -32.33, 0.94035, 35, 174.62, 24.8, 0.05965, 2, 53, 342.87, -33.41, 0.99938, 35, 190.82, 21.08, 0.00062, 1, 53, 322.53, -38.52, 1, 1, 53, 288.09, -47.8, 1, 1, 53, 223.67, -64.18, 1, 1, 53, 174.52, -74.77, 1, 1, 53, 123.27, -80.64, 1, 3, 47, -278.73, 80.12, 0.00019, 52, -88.23, 78.06, 0.00048, 53, 80.57, -78.14, 0.99933, 3, 47, -256.9, 66.45, 0.00493, 52, -62.86, 73.59, 0.01919, 53, 55.19, -73.7, 0.97588, 3, 47, -240.9, 53.73, 0.02434, 52, -43.24, 67.83, 0.10104, 53, 35.58, -67.95, 0.87463, 3, 47, -230.45, 47.84, 0.05467, 52, -31.35, 66.3, 0.22011, 53, 23.68, -66.44, 0.72522, 3, 47, -214.05, 44.87, 0.14128, 52, -15.04, 69.72, 0.46952, 53, 7.37, -69.87, 0.3892, 4, 47, -204.41, 56.7, 0.27731, 52, -10.55, 84.3, 0.50772, 53, 2.9, -84.45, 0.1658, 1, -189.36, -72.6, 0.04917, 4, 47, -189.64, 71.18, 0.2649, 52, -2.31, 103.28, 0.48044, 53, -5.32, -103.44, 0.04857, 1, -204.69, -58.71, 0.20608, 4, 47, -175.56, 82.8, 0.29402, 52, 6.37, 119.34, 0.38765, 53, -13.99, -119.5, 0.02286, 1, -217.12, -45.34, 0.29546, 3, 47, -159.6, 100.3, 0.20525, 52, 14.57, 141.55, 0.1825, 1, -235.53, -30.45, 0.61225, 3, 47, -134.92, 112.6, 0.10717, 52, 32.81, 162.23, 0.11983, 1, -249.27, -6.54, 0.773, 3, 47, -103.32, 115.26, 0.20089, 52, 61.09, 176.59, 0.06989, 1, -253.8, 24.85, 0.72922, 3, 47, -66.84, 107.95, 0.38868, 52, 97.64, 183.53, 0.05199, 1, -248.66, 61.7, 0.55933, 3, 47, -40.38, 94.25, 0.601, 52, 127.32, 180.79, 0.02734, 1, -236.54, 88.93, 0.37166, 2, 47, -18.56, 77.66, 0.80154, 1, -221.28, 111.68, 0.19846, 2, 47, 1.61, 54.86, 0.95698, 1, -199.71, 133.17, 0.04302, 1, 47, 16.74, 34.08, 1, 1, 47, 27.16, 16.54, 1, 3, 47, -157.14, 89.31, 0.25738, 52, 20.98, 132.3, 0.40605, 1, -224.71, -27.34, 0.33657, 3, 47, -130.41, 88.38, 0.28422, 52, 46.1, 141.49, 0.39352, 1, -225.36, -0.61, 0.32226, 3, 47, -101.67, 74.63, 0.51537, 52, 77.9, 139.56, 0.35659, 1, -213.34, 28.9, 0.12804, 2, 47, -78.9, 51.49, 0.65741, 52, 107.71, 126.68, 0.34259, 2, 47, -64.38, 18.04, 0.65833, 52, 133.74, 101.15, 0.34167, 3, 47, -64.04, -17.18, 0.46335, 52, 147.29, 68.64, 0.31887, 2, 52.37, 120.45, 0.21778, 3, 47, -77.64, -50.24, 0.19916, 52, 147.13, 32.89, 0.31565, 2, 38.77, 87.4, 0.48519, 3, 47, -103.45, -78.55, 0.06371, 52, 133.86, -3.04, 0.3526, 2, 12.97, 59.09, 0.58369, 3, 47, -137.48, -97.43, 0.06079, 52, 109.43, -33.34, 0.36029, 2, -21.07, 40.21, 0.57892, 2, 47, -0.96, 3.06, 0.96386, 2, 115.45, 140.7, 0.03614, 3, 47, -19.47, -34.8, 0.53225, 52, 195.22, 69.08, 0.00595, 2, 96.94, 102.84, 0.4618, 3, 47, -36.09, -66.07, 0.166, 52, 191.58, 33.85, 0.03077, 2, 80.32, 71.57, 0.80323, 2, 52, 183.05, 6.84, 0.01429, 2, 62.26, 49.74, 0.98571, 3, 47, -215.22, -64.14, 0.04035, 52, 24.88, -31.73, 0.87642, 53, -32.63, 31.54, 0.08324, 3, 47, -244.13, -56.27, 0.00216, 52, -4.87, -35.31, 0.44001, 53, -2.89, 35.16, 0.55783, 2, 52, -34.34, -29.58, 0.03939, 53, 26.59, 29.44, 0.96061, 1, 53, 51.87, 20.19, 1, 4, 47, -183.27, -69.35, 0.12362, 52, 56.44, -24.54, 0.78732, 53, -64.19, 24.33, 0.00047, 2, -66.86, 68.28, 0.08859, 3, 47, -273.2, 23.75, 0.00164, 52, -61.9, 27.9, 0.00399, 53, 54.2, -28, 0.99436, 3, 47, -250.82, 14.58, 0.00944, 52, -37.72, 27.82, 0.04498, 53, 30.02, -27.95, 0.94558, 3, 47, -223.57, 9.33, 0.04588, 52, -10.49, 33.21, 0.35038, 53, 2.8, -33.36, 0.60374, 3, 47, -199.09, -3.61, 0.10174, 52, 17.06, 30.42, 0.7908, 53, -24.76, -30.6, 0.10746, 2, 47, -170.21, -8.65, 0.20563, 52, 45.71, 36.61, 0.79437, 2, 47, -177.2, 20.27, 0.24629, 52, 28.36, 60.79, 0.75371, 3, 47, -195.48, 34.62, 0.20132, 52, 6.03, 67.2, 0.63073, 53, -13.7, -67.37, 0.16795, 3, 47, -170.62, -40.46, 0.17309, 52, 57.3, 6.99, 0.81436, 2, -54.21, 97.18, 0.01255, 2, 47, -206.79, -38.29, 0.04917, 52, 22.96, -4.6, 0.95083, 2, 52, -4.98, -6.49, 0.36672, 53, -2.76, 6.33, 0.63328, 1, 53, 30, 5.11, 1, 2, 47, -284.18, 0.04, 1e-05, 53, 55.43, -1.91, 0.99999, 1, 53, 87.13, 24.99, 1, 1, 53, 133.53, 21.86, 1, 1, 53, 233.21, -11.19, 1, 1, 53, 130.3, -39.16, 1, 1, 53, 89.93, -38.55, 1, 1, 53, 96.22, -5.52, 1], "hull": 70, "edges": [0, 138, 0, 2, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 28, 30, 38, 40, 40, 42, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 76, 78, 86, 88, 92, 94, 100, 102, 102, 104, 124, 126, 130, 132, 56, 58, 58, 60, 52, 54, 54, 56, 72, 74, 74, 76, 78, 80, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92, 98, 100, 30, 32, 32, 34, 42, 44, 44, 46, 48, 50, 50, 52, 46, 48, 94, 96, 96, 98, 34, 36, 36, 38, 18, 20, 16, 18, 12, 14, 14, 16, 120, 122, 122, 124, 118, 120, 110, 112, 112, 114, 108, 110, 114, 116, 116, 118, 24, 26, 26, 28, 104, 106, 106, 108, 126, 128, 128, 130, 132, 134, 2, 4, 4, 6, 120, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 14, 134, 136, 136, 138, 176, 178, 178, 180, 180, 182, 182, 184, 174, 154, 188, 140, 172, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 176], "width": 264, "height": 826}}, "nose": {"nose": {"type": "mesh", "uvs": [0.17833, 0.06158, 0.36334, 0.05367, 0.54835, 0.04575, 0.65206, 0.04132, 0.84363, 0.58092, 0.94741, 0.68523, 0.93689, 0.83172, 0.84531, 0.87894, 0.67032, 0.90239, 0.54444, 0.97712, 0.39926, 0.97628, 0.31534, 0.93355, 0.10877, 0.91834, 0.03389, 0.86097, 0.02748, 0.70074, 0.0882, 0.62334, 0.07116, 0.06617, 0.21096, 0.73092, 0.35136, 0.8274, 0.57326, 0.81524, 0.69452, 0.71607, 0.62657, 0.56687, 0.54952, 0.31446, 0.23314, 0.34162, 0.23281, 0.58599, 0.44509, 0.68729, 0.42562, 0.53602, 0.39224, 0.30609], "triangles": [27, 1, 2, 22, 2, 3, 27, 2, 22, 27, 23, 0, 27, 0, 1, 26, 27, 22, 23, 27, 26, 4, 21, 22, 3, 4, 22, 26, 22, 21, 0, 23, 16, 24, 23, 26, 23, 15, 16, 24, 15, 23, 25, 26, 21, 24, 26, 25, 20, 21, 4, 20, 4, 5, 25, 21, 20, 17, 15, 24, 17, 24, 25, 14, 15, 17, 19, 25, 20, 18, 17, 25, 18, 25, 19, 6, 20, 5, 13, 14, 17, 7, 20, 6, 8, 19, 20, 7, 8, 20, 12, 13, 17, 12, 17, 18, 11, 12, 18, 10, 18, 19, 9, 10, 19, 11, 18, 10, 8, 9, 19], "vertices": [2, 6, 38.19, 16.48, 0.959, 42, -366.98, 82.48, 0.041, 2, 6, 37.85, 11.12, 0.959, 42, -367.31, 77.12, 0.041, 2, 6, 37.52, 5.76, 0.959, 42, -367.65, 71.75, 0.041, 2, 6, 37.33, 2.75, 0.96, 42, -367.84, 68.74, 0.04, 2, 6, 15.23, -0.15, 0.96, 42, -389.94, 65.84, 0.04, 2, 6, 10.72, -2.63, 0.96, 42, -394.44, 63.36, 0.04, 2, 6, 4.95, -1.62, 0.96, 42, -400.22, 64.38, 0.04, 2, 6, 3.39, 1.25, 0.96, 42, -401.78, 67.24, 0.04, 2, 6, 3.08, 6.4, 0.958, 42, -402.09, 72.39, 0.042, 2, 6, 0.55, 10.38, 0.958, 42, -404.62, 76.38, 0.042, 2, 6, 1.1, 14.56, 0.958, 42, -404.07, 80.55, 0.042, 2, 6, 3.09, 16.77, 0.958, 42, -402.08, 82.76, 0.042, 2, 6, 4.42, 22.64, 0.96, 42, -400.75, 88.63, 0.04, 2, 6, 6.96, 24.52, 0.96, 42, -398.21, 90.51, 0.04, 2, 6, 13.34, 23.92, 0.96, 42, -391.83, 89.92, 0.04, 2, 6, 16.2, 21.8, 0.96, 42, -388.97, 87.8, 0.04, 2, 6, 38.38, 19.59, 0.96, 42, -366.78, 85.59, 0.04, 2, 6, 11.5, 18.79, 0.956, 42, -393.67, 84.78, 0.044, 2, 6, 7.17, 15.22, 0.955, 42, -397.99, 81.21, 0.045, 2, 6, 6.88, 8.77, 0.955, 42, -398.29, 74.76, 0.045, 2, 6, 10.39, 4.8, 0.956, 42, -394.78, 70.79, 0.044, 2, 6, 16.55, 6.03, 0.957, 42, -388.62, 72.03, 0.043, 2, 6, 26.84, 7.02, 0.958, 42, -378.32, 73.02, 0.042, 2, 6, 26.88, 16.26, 0.958, 42, -378.29, 82.26, 0.042, 2, 6, 17.18, 17.46, 0.957, 42, -387.99, 83.45, 0.043, 2, 6, 12.41, 11.84, 0.955, 42, -392.76, 77.83, 0.045, 2, 6, 18.48, 11.67, 0.955, 42, -386.68, 77.66, 0.045, 2, 6, 27.73, 11.51, 0.956, 42, -377.44, 77.51, 0.044], "hull": 17, "edges": [10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 12, 14, 14, 16, 6, 8, 8, 10, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 34, 0, 32, 46, 0, 4, 6, 4, 44, 50, 52, 52, 54, 0, 2, 2, 4, 54, 2], "width": 29, "height": 40}}}}], "animations": {"0": {"bones": {"body": {"rotate": [{"value": 2.05, "curve": [0.444, 2.05, 0.889, -2.55]}, {"time": 1.3333, "value": -2.55, "curve": [1.778, -2.55, 2.222, 2.05]}, {"time": 2.6667, "value": 2.05, "curve": [3.111, 2.05, 3.556, -2.55]}, {"time": 4, "value": -2.55, "curve": [4.444, -2.55, 4.889, 2.05]}, {"time": 5.3333, "value": 2.05, "curve": [5.667, 2.05, 6, -3.52]}, {"time": 6.3333, "value": -3.52, "curve": [6.778, -3.52, 7.222, -2.33]}, {"time": 7.6667, "value": -2.33, "curve": [8.111, -2.33, 8.556, -3.52]}, {"time": 9, "value": -3.52, "curve": [9.444, -3.52, 9.889, 2.05]}, {"time": 10.3333, "value": 2.05, "curve": [10.778, 2.05, 11.222, -2.55]}, {"time": 11.6667, "value": -2.55, "curve": [12.111, -2.55, 12.556, 2.05]}, {"time": 13, "value": 2.05}]}, "body2": {"rotate": [{"curve": [0.444, 0, 0.889, -2.49]}, {"time": 1.3333, "value": -2.49, "curve": [1.778, -2.49, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -2.49]}, {"time": 4, "value": -2.49, "curve": [4.444, -2.49, 4.889, 0]}, {"time": 5.3333, "curve": [5.667, 0, 6, -4.23]}, {"time": 6.3333, "value": -4.23, "curve": [6.778, -4.23, 7.222, -3.71]}, {"time": 7.6667, "value": -3.71, "curve": [8.111, -3.71, 8.556, -4.23]}, {"time": 9, "value": -4.23, "curve": [9.444, -4.23, 9.889, 0]}, {"time": 10.3333, "curve": [10.778, 0, 11.222, -2.49]}, {"time": 11.6667, "value": -2.49, "curve": [12.111, -2.49, 12.556, 0]}, {"time": 13}], "translate": [{"x": -5.67, "curve": [0.444, -5.67, 0.889, 4.3, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 4.3, "curve": [1.778, 4.3, 2.222, -5.67, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -5.67, "curve": [3.111, -5.67, 3.556, 4.3, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 4.3, "curve": [4.444, 4.3, 4.889, -5.67, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -5.67, "curve": [5.667, -5.67, 6, 8.65, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 8.65, "curve": [6.778, 8.65, 7.222, 1.49, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 1.49, "curve": [8.111, 1.49, 8.556, 8.65, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 8.65, "curve": [9.444, 8.65, 9.889, -5.67, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -5.67, "curve": [10.778, -5.67, 11.222, 4.3, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 4.3, "curve": [12.111, 4.3, 12.556, -5.67, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -5.67}], "scale": [{"y": 1.06, "curve": [0.444, 1, 0.889, 1, 0.444, 1.06, 0.889, 0.915]}, {"time": 1.3333, "y": 0.915, "curve": [1.778, 1, 2.222, 1, 1.778, 0.915, 2.222, 1.06]}, {"time": 2.6667, "y": 1.06, "curve": [3.111, 1, 3.556, 1, 3.111, 1.06, 3.556, 0.915]}, {"time": 4, "y": 0.915, "curve": [4.444, 1, 4.889, 1, 4.444, 0.915, 4.889, 1.06]}, {"time": 5.3333, "y": 1.06, "curve": [5.667, 1, 6, 1, 5.667, 1.06, 6, 0.886]}, {"time": 6.3333, "y": 0.886, "curve": [6.778, 1, 7.222, 1, 6.778, 0.886, 7.222, 0.973]}, {"time": 7.6667, "y": 0.973, "curve": [8.111, 1, 8.556, 1, 8.111, 0.973, 8.556, 0.886]}, {"time": 9, "y": 0.886, "curve": [9.444, 1, 9.889, 1, 9.444, 0.886, 9.889, 1.06]}, {"time": 10.3333, "y": 1.06, "curve": [10.778, 1, 11.222, 1, 10.778, 1.06, 11.222, 0.915]}, {"time": 11.6667, "y": 0.915, "curve": [12.111, 1, 12.556, 1, 12.111, 0.915, 12.556, 1.06]}, {"time": 13, "y": 1.06}]}, "body3": {"rotate": [{"value": -0.07, "curve": [0.057, -0.03, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -1.5]}, {"time": 1.5, "value": -1.5, "curve": [1.944, -1.5, 2.389, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -1.5]}, {"time": 4.1667, "value": -1.5, "curve": [4.611, -1.5, 5.056, 0]}, {"time": 5.5, "curve": [5.833, 0, 6.167, -3.25]}, {"time": 6.5, "value": -3.25, "curve": [6.944, -3.25, 7.389, -2.52]}, {"time": 7.8333, "value": -2.52, "curve": [8.278, -2.52, 8.722, -3.25]}, {"time": 9.1667, "value": -3.25, "curve": [9.611, -3.25, 10.056, 0]}, {"time": 10.5, "curve": [10.944, 0, 11.389, -1.5]}, {"time": 11.8333, "value": -1.5, "curve": [12.223, -1.5, 12.613, -0.36]}, {"time": 13, "value": -0.07}], "translate": [{"x": -5.25, "curve": [0.057, -5.59, 0.112, -5.84, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -5.84, "curve": [0.611, -5.84, 1.056, 6.81, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 6.81, "curve": [1.944, 6.81, 2.389, -5.84, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -5.84, "curve": [3.278, -5.84, 3.722, 6.81, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 6.81, "curve": [4.611, 6.81, 5.056, -5.84, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -5.84, "curve": [5.833, -5.84, 6.167, 7.98, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 7.98, "curve": [6.944, 7.98, 7.389, 1.07, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 1.07, "curve": [8.278, 1.07, 8.722, 7.98, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 7.98, "curve": [9.611, 7.98, 10.056, -5.84, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -5.84, "curve": [10.944, -5.84, 11.389, 6.81, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 6.81, "curve": [12.223, 6.81, 12.613, -2.85, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -5.25}], "scale": [{"x": 1.001, "y": 1.001, "curve": [0.057, 1, 0.112, 1, 0.057, 1, 0.112, 1]}, {"time": 0.1667, "curve": [0.611, 1, 1.056, 1.021, 0.611, 1, 1.056, 1.021]}, {"time": 1.5, "x": 1.021, "y": 1.021, "curve": [1.944, 1.021, 2.389, 1, 1.944, 1.021, 2.389, 1]}, {"time": 2.8333, "curve": [3.278, 1, 3.722, 1.021, 3.278, 1, 3.722, 1.021]}, {"time": 4.1667, "x": 1.021, "y": 1.021, "curve": [4.611, 1.021, 5.056, 1, 4.611, 1.021, 5.056, 1]}, {"time": 5.5, "curve": [5.833, 1, 6.167, 1.021, 5.833, 1, 6.167, 1.021]}, {"time": 6.5, "x": 1.021, "y": 1.021, "curve": [6.944, 1.021, 7.389, 1.01, 6.944, 1.021, 7.389, 1.01]}, {"time": 7.8333, "x": 1.01, "y": 1.01, "curve": [8.278, 1.01, 8.722, 1.021, 8.278, 1.01, 8.722, 1.021]}, {"time": 9.1667, "x": 1.021, "y": 1.021, "curve": [9.611, 1.021, 10.056, 1, 9.611, 1.021, 10.056, 1]}, {"time": 10.5, "curve": [10.944, 1, 11.389, 1.021, 10.944, 1, 11.389, 1.021]}, {"time": 11.8333, "x": 1.021, "y": 1.021, "curve": [12.223, 1.021, 12.613, 1.005, 12.223, 1.021, 12.613, 1.005]}, {"time": 13, "x": 1.001, "y": 1.001}]}, "neck": {"rotate": [{"value": 0.62, "curve": [0.114, 0.88, 0.224, 1.07]}, {"time": 0.3333, "value": 1.07, "curve": [0.778, 1.07, 1.222, -1.72]}, {"time": 1.6667, "value": -1.72, "curve": [2.111, -1.72, 2.556, 1.07]}, {"time": 3, "value": 1.07, "curve": [3.444, 1.07, 3.889, -1.72]}, {"time": 4.3333, "value": -1.72, "curve": [4.778, -1.72, 5.222, 1.07]}, {"time": 5.6667, "value": 1.07, "curve": [6, 1.07, 6.333, -5.98]}, {"time": 6.6667, "value": -5.98, "curve": [7.111, -5.98, 7.556, -2.46]}, {"time": 8, "value": -2.46, "curve": [8.444, -2.46, 8.889, -5.98]}, {"time": 9.3333, "value": -5.98, "curve": [9.778, -5.98, 10.222, 1.07]}, {"time": 10.6667, "value": 1.07, "curve": [11.111, 1.07, 11.556, -1.72]}, {"time": 12, "value": -1.72, "curve": [12.335, -1.72, 12.67, -0.16]}, {"time": 13, "value": 0.62}]}, "head": {"rotate": [{"value": -0.29, "curve": [0.168, 0.46, 0.334, 1.07]}, {"time": 0.5, "value": 1.07, "curve": [0.944, 1.07, 1.389, -3.19]}, {"time": 1.8333, "value": -3.19, "curve": [2.278, -3.19, 2.722, 1.07]}, {"time": 3.1667, "value": 1.07, "curve": [3.611, 1.07, 4.056, -3.19]}, {"time": 4.5, "value": -3.19, "curve": [4.944, -3.19, 5.389, 1.07]}, {"time": 5.8333, "value": 1.07, "curve": [6.167, 1.07, 6.5, -7.45]}, {"time": 6.8333, "value": -7.45, "curve": [7.278, -7.45, 7.722, -3.19]}, {"time": 8.1667, "value": -3.19, "curve": [8.611, -3.19, 9.056, -7.45]}, {"time": 9.5, "value": -7.45, "curve": [9.944, -7.45, 10.389, 1.07]}, {"time": 10.8333, "value": 1.07, "curve": [11.278, 1.07, 11.722, -3.19]}, {"time": 12.1667, "value": -3.19, "curve": [12.445, -3.19, 12.724, -1.53]}, {"time": 13, "value": -0.29}]}, "eyebrow_L": {"translate": [{"time": 6, "curve": [6.111, 0, 6.222, 1.48, 6.111, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.48, "curve": [6.778, 1.48, 9.111, 0, 6.778, 0, 9.111, 0]}, {"time": 9.8333}]}, "eyebrow_L2": {"rotate": [{"curve": [0.444, 0, 5.333, -0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, -4.71]}, {"time": 6.3333, "value": -4.71, "curve": "stepped"}, {"time": 9.8333, "value": -4.71, "curve": [10.056, -4.71, 10.278, 0]}, {"time": 10.5}], "scale": [{"curve": [0.444, 1, 5.333, 1, 0.444, 1, 5.333, 1]}, {"time": 6, "curve": [6.111, 1, 6.222, 0.963, 6.111, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.963, "curve": "stepped"}, {"time": 9.8333, "x": 0.963, "curve": [10.056, 0.963, 10.278, 1, 10.056, 1, 10.278, 1]}, {"time": 10.5}]}, "eyebrow_L3": {"rotate": [{"time": 6, "curve": [6.111, 0, 6.222, -2.56]}, {"time": 6.3333, "value": -2.56, "curve": [6.778, -2.56, 9.111, 0]}, {"time": 9.8333}]}, "eyebrow_R": {"translate": [{"time": 6, "curve": [6.111, 0, 6.222, 1.48, 6.111, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.48, "curve": [6.778, 1.48, 9.111, 0, 6.778, 0, 9.111, 0]}, {"time": 9.8333}]}, "eyebrow_R2": {"rotate": [{"curve": [0.444, 0, 5.333, 0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, 5.08]}, {"time": 6.3333, "value": 5.08, "curve": "stepped"}, {"time": 9.8333, "value": 5.08, "curve": [10.056, 5.08, 10.278, 0]}, {"time": 10.5}], "scale": [{"curve": [0.444, 1, 5.333, 1, 0.444, 1, 5.333, 1]}, {"time": 6, "curve": [6.111, 1, 6.222, 0.963, 6.111, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.963, "curve": "stepped"}, {"time": 9.8333, "x": 0.963, "curve": [10.056, 0.963, 10.278, 1, 10.056, 1, 10.278, 1]}, {"time": 10.5}]}, "eyebrow_R3": {"rotate": [{"time": 6, "curve": [6.111, 0, 6.222, 4.82]}, {"time": 6.3333, "value": 4.82, "curve": [6.778, 4.82, 9.111, 0]}, {"time": 9.8333}]}, "earring_L": {"rotate": [{"value": 5.66, "curve": [0.222, 5.66, 0.444, -5.07]}, {"time": 0.6667, "value": -5.07, "curve": [0.889, -5.07, 1.111, 5.66]}, {"time": 1.3333, "value": 5.66, "curve": [1.556, 5.66, 1.778, -5.07]}, {"time": 2, "value": -5.07, "curve": [2.222, -5.07, 2.444, 5.66]}, {"time": 2.6667, "value": 5.66, "curve": [2.889, 5.66, 3.111, -5.07]}, {"time": 3.3333, "value": -5.07, "curve": [3.556, -5.07, 3.778, 5.66]}, {"time": 4, "value": 5.66, "curve": [4.222, 5.66, 4.444, -5.07]}, {"time": 4.6667, "value": -5.07, "curve": [4.889, -5.07, 5.111, 5.66]}, {"time": 5.3333, "value": 5.66, "curve": [5.556, 5.66, 5.778, -5.07]}, {"time": 6, "value": -5.07, "curve": [6.167, -5.07, 6.333, 5.66]}, {"time": 6.5, "value": 5.66, "curve": [6.667, 5.66, 6.833, -5.07]}, {"time": 7, "value": -5.07, "curve": [7.222, -5.07, 7.444, 5.66]}, {"time": 7.6667, "value": 5.66, "curve": [7.889, 5.66, 8.111, -5.07]}, {"time": 8.3333, "value": -5.07, "curve": [8.556, -5.07, 8.778, 5.66]}, {"time": 9, "value": 5.66, "curve": [9.222, 5.66, 9.444, -5.07]}, {"time": 9.6667, "value": -5.07, "curve": [9.889, -5.07, 10.111, 5.66]}, {"time": 10.3333, "value": 5.66, "curve": [10.556, 5.66, 10.778, -5.07]}, {"time": 11, "value": -5.07, "curve": [11.222, -5.07, 11.444, 5.66]}, {"time": 11.6667, "value": 5.66, "curve": [11.889, 5.66, 12.111, -5.07]}, {"time": 12.3333, "value": -5.07, "curve": [12.556, -5.07, 12.778, 5.66]}, {"time": 13, "value": 5.66}]}, "earring_R": {"rotate": [{"value": 5.66, "curve": [0.222, 5.66, 0.444, -5.07]}, {"time": 0.6667, "value": -5.07, "curve": [0.889, -5.07, 1.111, 5.66]}, {"time": 1.3333, "value": 5.66, "curve": [1.556, 5.66, 1.778, -5.07]}, {"time": 2, "value": -5.07, "curve": [2.222, -5.07, 2.444, 5.66]}, {"time": 2.6667, "value": 5.66, "curve": [2.889, 5.66, 3.111, -5.07]}, {"time": 3.3333, "value": -5.07, "curve": [3.556, -5.07, 3.778, 5.66]}, {"time": 4, "value": 5.66, "curve": [4.222, 5.66, 4.444, -5.07]}, {"time": 4.6667, "value": -5.07, "curve": [4.889, -5.07, 5.111, 5.66]}, {"time": 5.3333, "value": 5.66, "curve": [5.556, 5.66, 5.778, -5.07]}, {"time": 6, "value": -5.07, "curve": [6.167, -5.07, 6.333, 5.66]}, {"time": 6.5, "value": 5.66, "curve": [6.667, 5.66, 6.833, -5.07]}, {"time": 7, "value": -5.07, "curve": [7.222, -5.07, 7.444, 5.66]}, {"time": 7.6667, "value": 5.66, "curve": [7.889, 5.66, 8.111, -5.07]}, {"time": 8.3333, "value": -5.07, "curve": [8.556, -5.07, 8.778, 5.66]}, {"time": 9, "value": 5.66, "curve": [9.222, 5.66, 9.444, -5.07]}, {"time": 9.6667, "value": -5.07, "curve": [9.889, -5.07, 10.111, 5.66]}, {"time": 10.3333, "value": 5.66, "curve": [10.556, 5.66, 10.778, -5.07]}, {"time": 11, "value": -5.07, "curve": [11.222, -5.07, 11.444, 5.66]}, {"time": 11.6667, "value": 5.66, "curve": [11.889, 5.66, 12.111, -5.07]}, {"time": 12.3333, "value": -5.07, "curve": [12.556, -5.07, 12.778, 5.66]}, {"time": 13, "value": 5.66}]}, "sh_L": {"translate": [{"x": -4.48, "curve": [0.057, -4.79, 0.112, -5.03, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -5.03, "curve": [0.611, -5.03, 1.056, 6.63, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 6.63, "curve": [1.944, 6.63, 2.389, -5.03, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -5.03, "curve": [3.278, -5.03, 3.722, 6.63, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 6.63, "curve": [4.611, 6.63, 5.056, -5.03, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -5.03, "curve": [5.833, -5.03, 6.167, 6.63, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 6.63, "curve": [6.944, 6.63, 7.389, -5.03, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -5.03, "curve": [8.278, -5.03, 8.722, 6.63, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 6.63, "curve": [9.611, 6.63, 10.056, -5.03, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -5.03, "curve": [10.944, -5.03, 11.389, 6.63, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 6.63, "curve": [12.223, 6.63, 12.613, -2.27, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -4.48}]}, "sh_R": {"translate": [{"x": -4.48, "curve": [0.057, -4.79, 0.112, -5.03, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -5.03, "curve": [0.611, -5.03, 1.056, 6.63, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 6.63, "curve": [1.944, 6.63, 2.389, -5.03, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -5.03, "curve": [3.278, -5.03, 3.722, 6.63, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 6.63, "curve": [4.611, 6.63, 5.056, -5.03, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -5.03, "curve": [5.833, -5.03, 6.167, 6.63, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 6.63, "curve": [6.944, 6.63, 7.389, -5.03, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -5.03, "curve": [8.278, -5.03, 8.722, 6.63, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 6.63, "curve": [9.611, 6.63, 10.056, -5.03, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -5.03, "curve": [10.944, -5.03, 11.389, 6.63, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 6.63, "curve": [12.223, 6.63, 12.613, -2.27, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -4.48}]}, "RU_L": {"translate": [{"x": -24.19, "curve": [0.114, -30.75, 0.224, -35.64, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -35.64, "curve": [0.778, -35.64, 1.222, 35.91, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 35.91, "curve": [2.111, 35.91, 2.556, -35.64, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -35.64, "curve": [3.444, -35.64, 3.889, 35.91, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 35.91, "curve": [4.778, 35.91, 5.222, -35.64, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -35.64, "curve": [6, -35.64, 6.333, 35.91, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 35.91, "curve": [7.111, 35.91, 7.556, -35.64, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -35.64, "curve": [8.444, -35.64, 8.889, 35.91, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 35.91, "curve": [9.778, 35.91, 10.222, -35.64, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -35.64, "curve": [11.111, -35.64, 11.556, 35.91, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 35.91, "curve": [12.335, 35.91, 12.67, -4.16, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -24.19}], "scale": [{"x": 1.007, "y": 0.997, "curve": [0.111, 0.979, 0.222, 0.952, 0.111, 1.026, 0.222, 1.055]}, {"time": 0.3333, "x": 0.952, "y": 1.055, "curve": [0.556, 0.952, 0.778, 1.062, 0.556, 1.055, 0.778, 0.938]}, {"time": 1, "x": 1.062, "y": 0.938, "curve": [1.222, 1.062, 1.444, 0.952, 1.222, 0.938, 1.444, 1.055]}, {"time": 1.6667, "x": 0.952, "y": 1.055, "curve": [1.889, 0.952, 2.111, 1.062, 1.889, 1.055, 2.111, 0.938]}, {"time": 2.3333, "x": 1.062, "y": 0.938, "curve": [2.556, 1.062, 2.778, 0.952, 2.556, 0.938, 2.778, 1.055]}, {"time": 3, "x": 0.952, "y": 1.055, "curve": [3.222, 0.952, 3.444, 1.062, 3.222, 1.055, 3.444, 0.938]}, {"time": 3.6667, "x": 1.062, "y": 0.938, "curve": [3.889, 1.062, 4.111, 0.952, 3.889, 0.938, 4.111, 1.055]}, {"time": 4.3333, "x": 0.952, "y": 1.055, "curve": [4.556, 0.952, 4.778, 1.062, 4.556, 1.055, 4.778, 0.938]}, {"time": 5, "x": 1.062, "y": 0.938, "curve": [5.222, 1.062, 5.444, 0.952, 5.222, 0.938, 5.444, 1.055]}, {"time": 5.6667, "x": 0.952, "y": 1.055, "curve": [5.833, 0.952, 6, 1.062, 5.833, 1.055, 6, 0.938]}, {"time": 6.1667, "x": 1.062, "y": 0.938, "curve": [6.333, 1.062, 6.5, 0.952, 6.333, 0.938, 6.5, 1.055]}, {"time": 6.6667, "x": 0.952, "y": 1.055, "curve": [6.889, 0.952, 7.111, 1.062, 6.889, 1.055, 7.111, 0.938]}, {"time": 7.3333, "x": 1.062, "y": 0.938, "curve": [7.556, 1.062, 7.778, 0.952, 7.556, 0.938, 7.778, 1.055]}, {"time": 8, "x": 0.952, "y": 1.055, "curve": [8.222, 0.952, 8.444, 1.062, 8.222, 1.055, 8.444, 0.938]}, {"time": 8.6667, "x": 1.062, "y": 0.938, "curve": [8.889, 1.062, 9.111, 0.952, 8.889, 0.938, 9.111, 1.055]}, {"time": 9.3333, "x": 0.952, "y": 1.055, "curve": [9.556, 0.952, 9.778, 1.062, 9.556, 1.055, 9.778, 0.938]}, {"time": 10, "x": 1.062, "y": 0.938, "curve": [10.222, 1.062, 10.444, 0.952, 10.222, 0.938, 10.444, 1.055]}, {"time": 10.6667, "x": 0.952, "y": 1.055, "curve": [10.889, 0.952, 11.111, 1.062, 10.889, 1.055, 11.111, 0.938]}, {"time": 11.3333, "x": 1.062, "y": 0.938, "curve": [11.556, 1.062, 11.778, 0.952, 11.556, 0.938, 11.778, 1.055]}, {"time": 12, "x": 0.952, "y": 1.055, "curve": [12.222, 0.952, 12.444, 1.062, 12.222, 1.055, 12.444, 0.938]}, {"time": 12.6667, "x": 1.062, "y": 0.938, "curve": [12.778, 1.062, 12.889, 1.034, 12.778, 0.938, 12.889, 0.967]}, {"time": 13, "x": 1.007, "y": 0.997}]}, "RU_L2": {"translate": [{"x": -13.4, "curve": [0.168, -24.19, 0.334, -33.04, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -33.04, "curve": [0.944, -33.04, 1.389, 28.72, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 28.72, "curve": [2.278, 28.72, 2.722, -33.04, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -33.04, "curve": [3.611, -33.04, 4.056, 28.72, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 28.72, "curve": [4.944, 28.72, 5.389, -33.04, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -33.04, "curve": [6.167, -33.04, 6.5, 28.72, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 28.72, "curve": [7.278, 28.72, 7.722, -33.04, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -33.04, "curve": [8.611, -33.04, 9.056, 28.72, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 28.72, "curve": [9.944, 28.72, 10.389, -33.04, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -33.04, "curve": [11.278, -33.04, 11.722, 28.72, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 28.72, "curve": [12.445, 28.72, 12.724, 4.71, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -13.4}], "scale": [{"x": 1.044, "y": 0.957, "curve": [0.167, 1.013, 0.333, 0.952, 0.167, 0.99, 0.333, 1.055]}, {"time": 0.5, "x": 0.952, "y": 1.055, "curve": [0.722, 0.952, 0.944, 1.062, 0.722, 1.055, 0.944, 0.938]}, {"time": 1.1667, "x": 1.062, "y": 0.938, "curve": [1.389, 1.062, 1.611, 0.952, 1.389, 0.938, 1.611, 1.055]}, {"time": 1.8333, "x": 0.952, "y": 1.055, "curve": [2.056, 0.952, 2.278, 1.062, 2.056, 1.055, 2.278, 0.938]}, {"time": 2.5, "x": 1.062, "y": 0.938, "curve": [2.722, 1.062, 2.944, 0.952, 2.722, 0.938, 2.944, 1.055]}, {"time": 3.1667, "x": 0.952, "y": 1.055, "curve": [3.389, 0.952, 3.611, 1.062, 3.389, 1.055, 3.611, 0.938]}, {"time": 3.8333, "x": 1.062, "y": 0.938, "curve": [4.056, 1.062, 4.278, 0.952, 4.056, 0.938, 4.278, 1.055]}, {"time": 4.5, "x": 0.952, "y": 1.055, "curve": [4.722, 0.952, 4.944, 1.062, 4.722, 1.055, 4.944, 0.938]}, {"time": 5.1667, "x": 1.062, "y": 0.938, "curve": [5.389, 1.062, 5.611, 0.952, 5.389, 0.938, 5.611, 1.055]}, {"time": 5.8333, "x": 0.952, "y": 1.055, "curve": [6, 0.952, 6.167, 1.062, 6, 1.055, 6.167, 0.938]}, {"time": 6.3333, "x": 1.062, "y": 0.938, "curve": [6.5, 1.062, 6.667, 0.952, 6.5, 0.938, 6.667, 1.055]}, {"time": 6.8333, "x": 0.952, "y": 1.055, "curve": [7.056, 0.952, 7.278, 1.062, 7.056, 1.055, 7.278, 0.938]}, {"time": 7.5, "x": 1.062, "y": 0.938, "curve": [7.722, 1.062, 7.944, 0.952, 7.722, 0.938, 7.944, 1.055]}, {"time": 8.1667, "x": 0.952, "y": 1.055, "curve": [8.389, 0.952, 8.611, 1.062, 8.389, 1.055, 8.611, 0.938]}, {"time": 8.8333, "x": 1.062, "y": 0.938, "curve": [9.056, 1.062, 9.278, 0.952, 9.056, 0.938, 9.278, 1.055]}, {"time": 9.5, "x": 0.952, "y": 1.055, "curve": [9.722, 0.952, 9.944, 1.062, 9.722, 1.055, 9.944, 0.938]}, {"time": 10.1667, "x": 1.062, "y": 0.938, "curve": [10.389, 1.062, 10.611, 0.952, 10.389, 0.938, 10.611, 1.055]}, {"time": 10.8333, "x": 0.952, "y": 1.055, "curve": [11.056, 0.952, 11.278, 1.062, 11.056, 1.055, 11.278, 0.938]}, {"time": 11.5, "x": 1.062, "y": 0.938, "curve": [11.722, 1.062, 11.944, 0.952, 11.722, 0.938, 11.944, 1.055]}, {"time": 12.1667, "x": 0.952, "y": 1.055, "curve": [12.389, 0.952, 12.611, 1.062, 12.389, 1.055, 12.611, 0.938]}, {"time": 12.8333, "x": 1.062, "y": 0.938, "curve": [12.889, 1.062, 12.944, 1.054, 12.889, 0.938, 12.944, 0.946]}, {"time": 13, "x": 1.044, "y": 0.957}]}, "RU_L3": {"translate": [{"x": -2.48, "curve": [0.225, -16.58, 0.446, -30.86, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -30.86, "curve": [1.111, -30.86, 1.556, 25.9, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 25.9, "curve": [2.444, 25.9, 2.889, -30.86, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -30.86, "curve": [3.778, -30.86, 4.222, 25.9, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 25.9, "curve": [5.111, 25.9, 5.556, -30.86, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -30.86, "curve": [6.333, -30.86, 6.667, 25.9, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 25.9, "curve": [7.444, 25.9, 7.889, -30.86, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -30.86, "curve": [8.778, -30.86, 9.222, 25.9, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 25.9, "curve": [10.111, 25.9, 10.556, -30.86, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -30.86, "curve": [11.444, -30.86, 11.889, 25.9, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 25.9, "curve": [12.557, 25.9, 12.781, 11.8, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -2.48}], "scale": [{"x": 1.062, "y": 0.938, "curve": [0.222, 1.062, 0.444, 0.952, 0.222, 0.938, 0.444, 1.055]}, {"time": 0.6667, "x": 0.952, "y": 1.055, "curve": [0.889, 0.952, 1.111, 1.062, 0.889, 1.055, 1.111, 0.938]}, {"time": 1.3333, "x": 1.062, "y": 0.938, "curve": [1.556, 1.062, 1.778, 0.952, 1.556, 0.938, 1.778, 1.055]}, {"time": 2, "x": 0.952, "y": 1.055, "curve": [2.222, 0.952, 2.444, 1.062, 2.222, 1.055, 2.444, 0.938]}, {"time": 2.6667, "x": 1.062, "y": 0.938, "curve": [2.889, 1.062, 3.111, 0.952, 2.889, 0.938, 3.111, 1.055]}, {"time": 3.3333, "x": 0.952, "y": 1.055, "curve": [3.556, 0.952, 3.778, 1.062, 3.556, 1.055, 3.778, 0.938]}, {"time": 4, "x": 1.062, "y": 0.938, "curve": [4.222, 1.062, 4.444, 0.952, 4.222, 0.938, 4.444, 1.055]}, {"time": 4.6667, "x": 0.952, "y": 1.055, "curve": [4.889, 0.952, 5.111, 1.062, 4.889, 1.055, 5.111, 0.938]}, {"time": 5.3333, "x": 1.062, "y": 0.938, "curve": [5.556, 1.062, 5.778, 0.952, 5.556, 0.938, 5.778, 1.055]}, {"time": 6, "x": 0.952, "y": 1.055, "curve": [6.167, 0.952, 6.333, 1.062, 6.167, 1.055, 6.333, 0.938]}, {"time": 6.5, "x": 1.062, "y": 0.938, "curve": [6.667, 1.062, 6.833, 0.952, 6.667, 0.938, 6.833, 1.055]}, {"time": 7, "x": 0.952, "y": 1.055, "curve": [7.222, 0.952, 7.444, 1.062, 7.222, 1.055, 7.444, 0.938]}, {"time": 7.6667, "x": 1.062, "y": 0.938, "curve": [7.889, 1.062, 8.111, 0.952, 7.889, 0.938, 8.111, 1.055]}, {"time": 8.3333, "x": 0.952, "y": 1.055, "curve": [8.556, 0.952, 8.778, 1.062, 8.556, 1.055, 8.778, 0.938]}, {"time": 9, "x": 1.062, "y": 0.938, "curve": [9.222, 1.062, 9.444, 0.952, 9.222, 0.938, 9.444, 1.055]}, {"time": 9.6667, "x": 0.952, "y": 1.055, "curve": [9.889, 0.952, 10.111, 1.062, 9.889, 1.055, 10.111, 0.938]}, {"time": 10.3333, "x": 1.062, "y": 0.938, "curve": [10.556, 1.062, 10.778, 0.952, 10.556, 0.938, 10.778, 1.055]}, {"time": 11, "x": 0.952, "y": 1.055, "curve": [11.222, 0.952, 11.444, 1.062, 11.222, 1.055, 11.444, 0.938]}, {"time": 11.6667, "x": 1.062, "y": 0.938, "curve": [11.889, 1.062, 12.111, 0.952, 11.889, 0.938, 12.111, 1.055]}, {"time": 12.3333, "x": 0.952, "y": 1.055, "curve": [12.556, 0.952, 12.778, 1.062, 12.556, 1.055, 12.778, 0.938]}, {"time": 13, "x": 1.062, "y": 0.938}]}, "RU_R": {"translate": [{"x": -27.64, "curve": [0.444, -27.64, 0.889, 33.71, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 33.71, "curve": [1.778, 33.71, 2.222, -27.64, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -27.64, "curve": [3.111, -27.64, 3.556, 33.71, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 33.71, "curve": [4.444, 33.71, 4.889, -27.64, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -27.64, "curve": [5.667, -27.64, 6, 33.71, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 33.71, "curve": [6.778, 33.71, 7.222, -27.64, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -27.64, "curve": [8.111, -27.64, 8.556, 33.71, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 33.71, "curve": [9.444, 33.71, 9.889, -27.64, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -27.64, "curve": [10.778, -27.64, 11.222, 33.71, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 33.71, "curve": [12.111, 33.71, 12.556, -27.64, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -27.64}], "scale": [{"x": 0.952, "y": 1.055, "curve": [0.222, 0.952, 0.444, 1.062, 0.222, 1.055, 0.444, 0.938]}, {"time": 0.6667, "x": 1.062, "y": 0.938, "curve": [0.889, 1.062, 1.111, 0.952, 0.889, 0.938, 1.111, 1.055]}, {"time": 1.3333, "x": 0.952, "y": 1.055, "curve": [1.556, 0.952, 1.778, 1.062, 1.556, 1.055, 1.778, 0.938]}, {"time": 2, "x": 1.062, "y": 0.938, "curve": [2.222, 1.062, 2.444, 0.952, 2.222, 0.938, 2.444, 1.055]}, {"time": 2.6667, "x": 0.952, "y": 1.055, "curve": [2.889, 0.952, 3.111, 1.062, 2.889, 1.055, 3.111, 0.938]}, {"time": 3.3333, "x": 1.062, "y": 0.938, "curve": [3.556, 1.062, 3.778, 0.952, 3.556, 0.938, 3.778, 1.055]}, {"time": 4, "x": 0.952, "y": 1.055, "curve": [4.222, 0.952, 4.444, 1.062, 4.222, 1.055, 4.444, 0.938]}, {"time": 4.6667, "x": 1.062, "y": 0.938, "curve": [4.889, 1.062, 5.111, 0.952, 4.889, 0.938, 5.111, 1.055]}, {"time": 5.3333, "x": 0.952, "y": 1.055, "curve": [5.5, 0.952, 5.667, 1.062, 5.5, 1.055, 5.667, 0.938]}, {"time": 5.8333, "x": 1.062, "y": 0.938, "curve": [6, 1.062, 6.167, 0.952, 6, 0.938, 6.167, 1.055]}, {"time": 6.3333, "x": 0.952, "y": 1.055, "curve": [6.556, 0.952, 6.778, 1.062, 6.556, 1.055, 6.778, 0.938]}, {"time": 7, "x": 1.062, "y": 0.938, "curve": [7.222, 1.062, 7.444, 0.952, 7.222, 0.938, 7.444, 1.055]}, {"time": 7.6667, "x": 0.952, "y": 1.055, "curve": [7.889, 0.952, 8.111, 1.062, 7.889, 1.055, 8.111, 0.938]}, {"time": 8.3333, "x": 1.062, "y": 0.938, "curve": [8.556, 1.062, 8.778, 0.952, 8.556, 0.938, 8.778, 1.055]}, {"time": 9, "x": 0.952, "y": 1.055, "curve": [9.222, 0.952, 9.444, 1.062, 9.222, 1.055, 9.444, 0.938]}, {"time": 9.6667, "x": 1.062, "y": 0.938, "curve": [9.889, 1.062, 10.111, 0.952, 9.889, 0.938, 10.111, 1.055]}, {"time": 10.3333, "x": 0.952, "y": 1.055, "curve": [10.556, 0.952, 10.778, 1.062, 10.556, 1.055, 10.778, 0.938]}, {"time": 11, "x": 1.062, "y": 0.938, "curve": [11.222, 1.062, 11.444, 0.952, 11.222, 0.938, 11.444, 1.055]}, {"time": 11.6667, "x": 0.952, "y": 1.055, "curve": [11.889, 0.952, 12.111, 1.062, 11.889, 1.055, 12.111, 0.938]}, {"time": 12.3333, "x": 1.062, "y": 0.938, "curve": [12.556, 1.062, 12.778, 0.952, 12.556, 0.938, 12.778, 1.055]}, {"time": 13, "x": 0.952, "y": 1.055}]}, "RU_R2": {"translate": [{"x": -10.24, "curve": [0.168, -19.45, 0.334, -27.01, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -27.01, "curve": [0.944, -27.01, 1.389, 25.72, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 25.72, "curve": [2.278, 25.72, 2.722, -27.01, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -27.01, "curve": [3.611, -27.01, 4.056, 25.72, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 25.72, "curve": [4.944, 25.72, 5.389, -27.01, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -27.01, "curve": [6.167, -27.01, 6.5, 25.72, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 25.72, "curve": [7.278, 25.72, 7.722, -27.01, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -27.01, "curve": [8.611, -27.01, 9.056, 25.72, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 25.72, "curve": [9.944, 25.72, 10.389, -27.01, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -27.01, "curve": [11.278, -27.01, 11.722, 25.72, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 25.72, "curve": [12.445, 25.72, 12.724, 5.22, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -10.24}], "scale": [{"x": 1.044, "y": 0.957, "curve": [0.167, 1.013, 0.333, 0.952, 0.167, 0.99, 0.333, 1.055]}, {"time": 0.5, "x": 0.952, "y": 1.055, "curve": [0.722, 0.952, 0.944, 1.062, 0.722, 1.055, 0.944, 0.938]}, {"time": 1.1667, "x": 1.062, "y": 0.938, "curve": [1.389, 1.062, 1.611, 0.952, 1.389, 0.938, 1.611, 1.055]}, {"time": 1.8333, "x": 0.952, "y": 1.055, "curve": [2.056, 0.952, 2.278, 1.062, 2.056, 1.055, 2.278, 0.938]}, {"time": 2.5, "x": 1.062, "y": 0.938, "curve": [2.722, 1.062, 2.944, 0.952, 2.722, 0.938, 2.944, 1.055]}, {"time": 3.1667, "x": 0.952, "y": 1.055, "curve": [3.389, 0.952, 3.611, 1.062, 3.389, 1.055, 3.611, 0.938]}, {"time": 3.8333, "x": 1.062, "y": 0.938, "curve": [4.056, 1.062, 4.278, 0.952, 4.056, 0.938, 4.278, 1.055]}, {"time": 4.5, "x": 0.952, "y": 1.055, "curve": [4.722, 0.952, 4.944, 1.062, 4.722, 1.055, 4.944, 0.938]}, {"time": 5.1667, "x": 1.062, "y": 0.938, "curve": [5.389, 1.062, 5.611, 0.952, 5.389, 0.938, 5.611, 1.055]}, {"time": 5.8333, "x": 0.952, "y": 1.055, "curve": [6, 0.952, 6.167, 1.062, 6, 1.055, 6.167, 0.938]}, {"time": 6.3333, "x": 1.062, "y": 0.938, "curve": [6.5, 1.062, 6.667, 0.952, 6.5, 0.938, 6.667, 1.055]}, {"time": 6.8333, "x": 0.952, "y": 1.055, "curve": [7.056, 0.952, 7.278, 1.062, 7.056, 1.055, 7.278, 0.938]}, {"time": 7.5, "x": 1.062, "y": 0.938, "curve": [7.722, 1.062, 7.944, 0.952, 7.722, 0.938, 7.944, 1.055]}, {"time": 8.1667, "x": 0.952, "y": 1.055, "curve": [8.389, 0.952, 8.611, 1.062, 8.389, 1.055, 8.611, 0.938]}, {"time": 8.8333, "x": 1.062, "y": 0.938, "curve": [9.056, 1.062, 9.278, 0.952, 9.056, 0.938, 9.278, 1.055]}, {"time": 9.5, "x": 0.952, "y": 1.055, "curve": [9.722, 0.952, 9.944, 1.062, 9.722, 1.055, 9.944, 0.938]}, {"time": 10.1667, "x": 1.062, "y": 0.938, "curve": [10.389, 1.062, 10.611, 0.952, 10.389, 0.938, 10.611, 1.055]}, {"time": 10.8333, "x": 0.952, "y": 1.055, "curve": [11.056, 0.952, 11.278, 1.062, 11.056, 1.055, 11.278, 0.938]}, {"time": 11.5, "x": 1.062, "y": 0.938, "curve": [11.722, 1.062, 11.944, 0.952, 11.722, 0.938, 11.944, 1.055]}, {"time": 12.1667, "x": 0.952, "y": 1.055, "curve": [12.389, 0.952, 12.611, 1.062, 12.389, 1.055, 12.611, 0.938]}, {"time": 12.8333, "x": 1.062, "y": 0.938, "curve": [12.889, 1.062, 12.944, 1.054, 12.889, 0.938, 12.944, 0.946]}, {"time": 13, "x": 1.044, "y": 0.957}]}, "RU_R3": {"translate": [{"x": -1.42, "curve": [0.225, -13.03, 0.446, -24.79, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -24.79, "curve": [1.111, -24.79, 1.556, 21.94, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 21.94, "curve": [2.444, 21.94, 2.889, -24.79, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -24.79, "curve": [3.778, -24.79, 4.222, 21.94, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 21.94, "curve": [5.111, 21.94, 5.556, -24.79, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -24.79, "curve": [6.333, -24.79, 6.667, 21.94, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 21.94, "curve": [7.444, 21.94, 7.889, -24.79, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -24.79, "curve": [8.778, -24.79, 9.222, 21.94, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 21.94, "curve": [10.111, 21.94, 10.556, -24.79, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -24.79, "curve": [11.444, -24.79, 11.889, 21.94, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 21.94, "curve": [12.557, 21.94, 12.781, 10.34, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -1.42}], "scale": [{"x": 1.062, "y": 0.938, "curve": [0.222, 1.062, 0.444, 0.952, 0.222, 0.938, 0.444, 1.055]}, {"time": 0.6667, "x": 0.952, "y": 1.055, "curve": [0.889, 0.952, 1.111, 1.062, 0.889, 1.055, 1.111, 0.938]}, {"time": 1.3333, "x": 1.062, "y": 0.938, "curve": [1.556, 1.062, 1.778, 0.952, 1.556, 0.938, 1.778, 1.055]}, {"time": 2, "x": 0.952, "y": 1.055, "curve": [2.222, 0.952, 2.444, 1.062, 2.222, 1.055, 2.444, 0.938]}, {"time": 2.6667, "x": 1.062, "y": 0.938, "curve": [2.889, 1.062, 3.111, 0.952, 2.889, 0.938, 3.111, 1.055]}, {"time": 3.3333, "x": 0.952, "y": 1.055, "curve": [3.556, 0.952, 3.778, 1.062, 3.556, 1.055, 3.778, 0.938]}, {"time": 4, "x": 1.062, "y": 0.938, "curve": [4.222, 1.062, 4.444, 0.952, 4.222, 0.938, 4.444, 1.055]}, {"time": 4.6667, "x": 0.952, "y": 1.055, "curve": [4.889, 0.952, 5.111, 1.062, 4.889, 1.055, 5.111, 0.938]}, {"time": 5.3333, "x": 1.062, "y": 0.938, "curve": [5.556, 1.062, 5.778, 0.952, 5.556, 0.938, 5.778, 1.055]}, {"time": 6, "x": 0.952, "y": 1.055, "curve": [6.167, 0.952, 6.333, 1.062, 6.167, 1.055, 6.333, 0.938]}, {"time": 6.5, "x": 1.062, "y": 0.938, "curve": [6.667, 1.062, 6.833, 0.952, 6.667, 0.938, 6.833, 1.055]}, {"time": 7, "x": 0.952, "y": 1.055, "curve": [7.222, 0.952, 7.444, 1.062, 7.222, 1.055, 7.444, 0.938]}, {"time": 7.6667, "x": 1.062, "y": 0.938, "curve": [7.889, 1.062, 8.111, 0.952, 7.889, 0.938, 8.111, 1.055]}, {"time": 8.3333, "x": 0.952, "y": 1.055, "curve": [8.556, 0.952, 8.778, 1.062, 8.556, 1.055, 8.778, 0.938]}, {"time": 9, "x": 1.062, "y": 0.938, "curve": [9.222, 1.062, 9.444, 0.952, 9.222, 0.938, 9.444, 1.055]}, {"time": 9.6667, "x": 0.952, "y": 1.055, "curve": [9.889, 0.952, 10.111, 1.062, 9.889, 1.055, 10.111, 0.938]}, {"time": 10.3333, "x": 1.062, "y": 0.938, "curve": [10.556, 1.062, 10.778, 0.952, 10.556, 0.938, 10.778, 1.055]}, {"time": 11, "x": 0.952, "y": 1.055, "curve": [11.222, 0.952, 11.444, 1.062, 11.222, 1.055, 11.444, 0.938]}, {"time": 11.6667, "x": 1.062, "y": 0.938, "curve": [11.889, 1.062, 12.111, 0.952, 11.889, 0.938, 12.111, 1.055]}, {"time": 12.3333, "x": 0.952, "y": 1.055, "curve": [12.556, 0.952, 12.778, 1.062, 12.556, 1.055, 12.778, 0.938]}, {"time": 13, "x": 1.062, "y": 0.938}]}, "arm_L": {"rotate": [{"value": 2.05, "curve": [0.114, 2.43, 0.224, 2.72]}, {"time": 0.3333, "value": 2.72, "curve": [0.778, 2.72, 1.222, -1.5]}, {"time": 1.6667, "value": -1.5, "curve": [2.111, -1.5, 2.556, 2.72]}, {"time": 3, "value": 2.72, "curve": [3.444, 2.72, 3.889, -1.5]}, {"time": 4.3333, "value": -1.5, "curve": [4.778, -1.5, 5.222, 2.72]}, {"time": 5.6667, "value": 2.72, "curve": [6, 2.72, 6.333, -1.5]}, {"time": 6.6667, "value": -1.5, "curve": [7.111, -1.5, 7.556, 2.72]}, {"time": 8, "value": 2.72, "curve": [8.444, 2.72, 8.889, -1.5]}, {"time": 9.3333, "value": -1.5, "curve": [9.778, -1.5, 10.222, 2.72]}, {"time": 10.6667, "value": 2.72, "curve": [11.111, 2.72, 11.556, -1.5]}, {"time": 12, "value": -1.5, "curve": [12.335, -1.5, 12.67, 0.86]}, {"time": 13, "value": 2.05}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{"value": -0.06}]}, "leg_L": {"translate": [{"x": 11.66, "y": -15.54, "curve": [0.444, 11.66, 0.889, -10.36, 0.444, -15.54, 0.889, 10.36]}, {"time": 1.3333, "x": -10.36, "y": 10.36, "curve": [1.778, -10.36, 2.222, 11.66, 1.778, 10.36, 2.222, -15.54]}, {"time": 2.6667, "x": 11.66, "y": -15.54, "curve": [3.111, 11.66, 3.556, -10.36, 3.111, -15.54, 3.556, 10.36]}, {"time": 4, "x": -10.36, "y": 10.36, "curve": [4.444, -10.36, 4.889, 11.66, 4.444, 10.36, 4.889, -15.54]}, {"time": 5.3333, "x": 11.66, "y": -15.54, "curve": [5.667, 11.66, 6, -10.36, 5.667, -15.54, 6, 10.36]}, {"time": 6.3333, "x": -10.36, "y": 10.36, "curve": [6.778, -10.36, 7.222, 11.66, 6.778, 10.36, 7.222, -15.54]}, {"time": 7.6667, "x": 11.66, "y": -15.54, "curve": [8.111, 11.66, 8.556, 0.65, 8.111, -15.54, 8.556, -2.59]}, {"time": 9, "x": 0.65, "y": -2.59, "curve": [9.444, 0.65, 9.889, 11.66, 9.444, -2.59, 9.889, -15.54]}, {"time": 10.3333, "x": 11.66, "y": -15.54, "curve": [10.778, 11.66, 11.222, -10.36, 10.778, -15.54, 11.222, 10.36]}, {"time": 11.6667, "x": -10.36, "y": 10.36, "curve": [12.111, -10.36, 12.556, 11.66, 12.111, 10.36, 12.556, -15.54]}, {"time": 13, "x": 11.66, "y": -15.54}]}, "leg_L2": {"rotate": [{"value": -1.58, "curve": [0.444, -1.58, 0.889, 1.51]}, {"time": 1.3333, "value": 1.51, "curve": [1.778, 1.51, 2.222, -1.58]}, {"time": 2.6667, "value": -1.58, "curve": [3.111, -1.58, 3.556, 1.51]}, {"time": 4, "value": 1.51, "curve": [4.444, 1.51, 4.889, -1.58]}, {"time": 5.3333, "value": -1.58, "curve": [5.667, -1.58, 6, 1.51]}, {"time": 6.3333, "value": 1.51, "curve": [6.778, 1.51, 7.222, -1.58]}, {"time": 7.6667, "value": -1.58, "curve": [8.111, -1.58, 8.556, -0.03]}, {"time": 9, "value": -0.03, "curve": [9.444, -0.03, 9.889, -1.58]}, {"time": 10.3333, "value": -1.58, "curve": [10.778, -1.58, 11.222, 1.51]}, {"time": 11.6667, "value": 1.51, "curve": [12.111, 1.51, 12.556, -1.58]}, {"time": 13, "value": -1.58}]}, "leg_L3": {"rotate": [{"value": -1.58, "curve": [0.444, -1.58, 0.889, 2.4]}, {"time": 1.3333, "value": 2.4, "curve": [1.778, 2.4, 2.222, -1.58]}, {"time": 2.6667, "value": -1.58, "curve": [3.111, -1.58, 3.556, 2.4]}, {"time": 4, "value": 2.4, "curve": [4.444, 2.4, 4.889, -1.58]}, {"time": 5.3333, "value": -1.58, "curve": [5.667, -1.58, 6, 2.4]}, {"time": 6.3333, "value": 2.4, "curve": [6.778, 2.4, 7.222, -1.58]}, {"time": 7.6667, "value": -1.58, "curve": [8.111, -1.58, 8.556, 0.41]}, {"time": 9, "value": 0.41, "curve": [9.444, 0.41, 9.889, -1.58]}, {"time": 10.3333, "value": -1.58, "curve": [10.778, -1.58, 11.222, 2.4]}, {"time": 11.6667, "value": 2.4, "curve": [12.111, 2.4, 12.556, -1.58]}, {"time": 13, "value": -1.58}]}, "leg_L4": {"rotate": [{"value": -1.58, "curve": [0.444, -1.58, 0.889, 2.4]}, {"time": 1.3333, "value": 2.4, "curve": [1.778, 2.4, 2.222, -1.58]}, {"time": 2.6667, "value": -1.58, "curve": [3.111, -1.58, 3.556, 2.4]}, {"time": 4, "value": 2.4, "curve": [4.444, 2.4, 4.889, -1.58]}, {"time": 5.3333, "value": -1.58, "curve": [5.667, -1.58, 6, 2.4]}, {"time": 6.3333, "value": 2.4, "curve": [6.778, 2.4, 7.222, -1.58]}, {"time": 7.6667, "value": -1.58, "curve": [8.111, -1.58, 8.556, 0.41]}, {"time": 9, "value": 0.41, "curve": [9.444, 0.41, 9.889, -1.58]}, {"time": 10.3333, "value": -1.58, "curve": [10.778, -1.58, 11.222, 2.4]}, {"time": 11.6667, "value": 2.4, "curve": [12.111, 2.4, 12.556, -1.58]}, {"time": 13, "value": -1.58}]}, "foot_R": {"rotate": [{"value": 2.42, "curve": [0.444, 2.42, 0.889, -2.75]}, {"time": 1.3333, "value": -2.75, "curve": [1.778, -2.75, 2.222, 2.42]}, {"time": 2.6667, "value": 2.42, "curve": [3.111, 2.42, 3.556, -2.75]}, {"time": 4, "value": -2.75, "curve": [4.444, -2.75, 4.889, 2.42]}, {"time": 5.3333, "value": 2.42, "curve": [5.667, 2.42, 6, -2.75]}, {"time": 6.3333, "value": -2.75, "curve": [6.778, -2.75, 7.222, 2.42]}, {"time": 7.6667, "value": 2.42, "curve": [8.111, 2.42, 8.556, -0.17]}, {"time": 9, "value": -0.17, "curve": [9.444, -0.17, 9.889, 2.42]}, {"time": 10.3333, "value": 2.42, "curve": [10.778, 2.42, 11.222, -2.75]}, {"time": 11.6667, "value": -2.75, "curve": [12.111, -2.75, 12.556, 2.42]}, {"time": 13, "value": 2.42}]}, "foot_R2": {"rotate": [{"value": 2.42, "curve": [0.444, 2.42, 0.889, -2.75]}, {"time": 1.3333, "value": -2.75, "curve": [1.778, -2.75, 2.222, 2.42]}, {"time": 2.6667, "value": 2.42, "curve": [3.111, 2.42, 3.556, -2.75]}, {"time": 4, "value": -2.75, "curve": [4.444, -2.75, 4.889, 2.42]}, {"time": 5.3333, "value": 2.42, "curve": [5.667, 2.42, 6, -2.75]}, {"time": 6.3333, "value": -2.75, "curve": [6.778, -2.75, 7.222, 2.42]}, {"time": 7.6667, "value": 2.42, "curve": [8.111, 2.42, 8.556, -0.17]}, {"time": 9, "value": -0.17, "curve": [9.444, -0.17, 9.889, 2.42]}, {"time": 10.3333, "value": 2.42, "curve": [10.778, 2.42, 11.222, -2.75]}, {"time": 11.6667, "value": -2.75, "curve": [12.111, -2.75, 12.556, 2.42]}, {"time": 13, "value": 2.42}]}, "bone38": {"rotate": [{"value": 20.79, "curve": [0.114, 26.1, 0.224, 30.05]}, {"time": 0.3333, "value": 30.05, "curve": [0.778, 30.05, 1.222, -27.84]}, {"time": 1.6667, "value": -27.84, "curve": [2.111, -27.84, 2.556, 30.05]}, {"time": 3, "value": 30.05, "curve": [3.444, 30.05, 3.889, -27.84]}, {"time": 4.3333, "value": -27.84, "curve": [4.778, -27.84, 5.222, 30.05]}, {"time": 5.6667, "value": 30.05, "curve": [6, 30.05, 6.333, -27.84]}, {"time": 6.6667, "value": -27.84, "curve": [7.111, -27.84, 7.556, 30.05]}, {"time": 8, "value": 30.05, "curve": [8.444, 30.05, 8.889, -27.84]}, {"time": 9.3333, "value": -27.84, "curve": [9.778, -27.84, 10.222, 30.05]}, {"time": 10.6667, "value": 30.05, "curve": [11.111, 30.05, 11.556, -27.84]}, {"time": 12, "value": -27.84, "curve": [12.335, -27.84, 12.67, 4.58]}, {"time": 13, "value": 20.79}], "translate": [{"x": 18.52, "y": -33.85, "curve": [0.057, 19.64, 0.112, 20.49, 0.057, -35.79, 0.112, -37.26]}, {"time": 0.1667, "x": 20.49, "y": -37.26, "curve": [0.611, 20.49, 1.056, -21.43, 0.611, -37.26, 1.056, 35.4]}, {"time": 1.5, "x": -21.43, "y": 35.4, "curve": [1.944, -21.43, 2.389, 20.49, 1.944, 35.4, 2.389, -37.26]}, {"time": 2.8333, "x": 20.49, "y": -37.26, "curve": [3.278, 20.49, 3.722, -21.43, 3.278, -37.26, 3.722, 35.4]}, {"time": 4.1667, "x": -21.43, "y": 35.4, "curve": [4.611, -21.43, 5.056, 20.49, 4.611, 35.4, 5.056, -37.26]}, {"time": 5.5, "x": 20.49, "y": -37.26, "curve": [5.833, 20.49, 6.167, -21.43, 5.833, -37.26, 6.167, 35.4]}, {"time": 6.5, "x": -21.43, "y": 35.4, "curve": [6.944, -21.43, 7.389, 20.49, 6.944, 35.4, 7.389, -37.26]}, {"time": 7.8333, "x": 20.49, "y": -37.26, "curve": [8.278, 20.49, 8.722, -21.43, 8.278, -37.26, 8.722, 35.4]}, {"time": 9.1667, "x": -21.43, "y": 35.4, "curve": [9.611, -21.43, 10.056, 20.49, 9.611, 35.4, 10.056, -37.26]}, {"time": 10.5, "x": 20.49, "y": -37.26, "curve": [10.944, 20.49, 11.389, -21.43, 10.944, -37.26, 11.389, 35.4]}, {"time": 11.8333, "x": -21.43, "y": 35.4, "curve": [12.223, -21.43, 12.613, 10.58, 12.223, 35.4, 12.613, -20.07]}, {"time": 13, "x": 18.52, "y": -33.85}]}, "sh_R2": {"rotate": [{}]}, "sh_R3": {"rotate": [{}]}, "arm_R3": {"translate": [{"x": -0.48, "y": 0.56, "curve": [0.057, -0.21, 0.112, 0, 0.057, 0.24, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -10.25, 0.611, 0, 1.056, 12.01]}, {"time": 1.5, "x": -10.25, "y": 12.01, "curve": [1.944, -10.25, 2.389, 0, 1.944, 12.01, 2.389, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -10.25, 3.278, 0, 3.722, 12.01]}, {"time": 4.1667, "x": -10.25, "y": 12.01, "curve": [4.611, -10.25, 5.056, 0, 4.611, 12.01, 5.056, 0]}, {"time": 5.5, "curve": [5.833, 0, 6.167, -11.79, 5.833, 0, 6.167, 15.08]}, {"time": 6.5, "x": -11.79, "y": 15.08, "curve": [6.944, -11.79, 7.389, 0, 6.944, 15.08, 7.389, 0]}, {"time": 7.8333, "curve": [8.278, 0, 8.722, -10.25, 8.278, 0, 8.722, 12.01]}, {"time": 9.1667, "x": -10.25, "y": 12.01, "curve": [9.611, -10.25, 10.056, 0, 9.611, 12.01, 10.056, 0]}, {"time": 10.5, "curve": [10.944, 0, 11.389, -10.25, 10.944, 0, 11.389, 12.01]}, {"time": 11.8333, "x": -10.25, "y": 12.01, "curve": [12.223, -10.25, 12.613, -2.42, 12.223, 12.01, 12.613, 2.84]}, {"time": 13, "x": -0.48, "y": 0.56}]}, "headround3": {"translate": [{"x": -6.43, "curve": [0.225, -76.74, 0.446, -147.99, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -147.99, "curve": [1.111, -147.99, 1.556, 135.12, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 135.12, "curve": [2.444, 135.12, 2.889, -147.99, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -147.99, "curve": [3.778, -147.99, 4.222, 135.12, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 135.12, "curve": [5.111, 135.12, 5.556, -147.99, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -147.99, "curve": [6.333, -147.99, 6.667, 135.12, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 135.12, "curve": [7.444, 135.12, 7.889, -6.43, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -6.43, "curve": [8.778, -6.43, 9.222, 135.12, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 135.12, "curve": [10.111, 135.12, 10.556, -147.99, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -147.99, "curve": [11.444, -147.99, 11.889, 135.12, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 135.12, "curve": [12.557, 135.12, 12.781, 64.81, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -6.43}]}, "headround": {"translate": [{"y": -59.75, "curve": [0.279, 0, 0.556, 0, 0.279, 19.75, 0.556, 125.5]}, {"time": 0.8333, "y": 125.5, "curve": [1.278, 0, 1.722, 0, 1.278, 125.5, 1.722, -146.13]}, {"time": 2.1667, "y": -146.13, "curve": [2.611, 0, 3.056, 0, 2.611, -146.13, 3.056, 125.5]}, {"time": 3.5, "y": 125.5, "curve": [3.944, 0, 4.389, 0, 3.944, 125.5, 4.389, -146.13]}, {"time": 4.8333, "y": -146.13, "curve": [5.278, 0, 5.722, 0, 5.278, -146.13, 5.722, 125.5]}, {"time": 6.1667, "y": 125.5, "curve": [6.5, 0, 6.833, 0, 6.5, 125.5, 6.833, -146.13]}, {"time": 7.1667, "y": -146.13, "curve": [7.611, 0, 8.056, 0, 7.611, -146.13, 8.056, -10.32]}, {"time": 8.5, "y": -10.32, "curve": [8.944, 0, 9.389, 0, 8.944, -10.32, 9.389, -146.13]}, {"time": 9.8333, "y": -146.13, "curve": [10.278, 0, 10.722, 0, 10.278, -146.13, 10.722, 125.5]}, {"time": 11.1667, "y": 125.5, "curve": [11.611, 0, 12.056, 0, 11.611, 125.5, 12.056, -146.13]}, {"time": 12.5, "y": -146.13, "curve": [12.667, 0, 12.835, 0, 12.667, -146.13, 12.835, -107.8]}, {"time": 13, "y": -59.75}]}, "bodyround": {"translate": [{"x": -97.46, "y": 75.45, "curve": [0.114, -124.68, 0.224, -144.97, 0.114, 97.29, 0.224, 113.57]}, {"time": 0.3333, "x": -144.97, "y": 113.57, "curve": [0.778, -144.97, 1.222, 152.01, 0.778, 113.57, 1.222, -124.69]}, {"time": 1.6667, "x": 152.01, "y": -124.69, "curve": [2.111, 152.01, 2.556, -144.97, 2.111, -124.69, 2.556, 113.57]}, {"time": 3, "x": -144.97, "y": 113.57, "curve": [3.444, -144.97, 3.889, 152.01, 3.444, 113.57, 3.889, -124.69]}, {"time": 4.3333, "x": 152.01, "y": -124.69, "curve": [4.778, 152.01, 5.222, -144.97, 4.778, -124.69, 5.222, 113.57]}, {"time": 5.6667, "x": -144.97, "y": 113.57, "curve": [6, -144.97, 6.333, 152.01, 6, 113.57, 6.333, -124.69]}, {"time": 6.6667, "x": 152.01, "y": -124.69, "curve": [7.111, 152.01, 7.556, 3.52, 7.111, -124.69, 7.556, -5.56]}, {"time": 8, "x": 3.52, "y": -5.56, "curve": [8.444, 3.52, 8.889, 152.01, 8.444, -5.56, 8.889, -124.69]}, {"time": 9.3333, "x": 152.01, "y": -124.69, "curve": [9.778, 152.01, 10.222, -144.97, 9.778, -124.69, 10.222, 113.57]}, {"time": 10.6667, "x": -144.97, "y": 113.57, "curve": [11.111, -144.97, 11.556, 152.01, 11.111, 113.57, 11.556, -124.69]}, {"time": 12, "x": 152.01, "y": -124.69, "curve": [12.335, 152.01, 12.67, -14.31, 12.335, -124.69, 12.67, 8.74]}, {"time": 13, "x": -97.46, "y": 75.45}]}, "arm_Lb": {"rotate": [{}]}, "hand_L": {"rotate": [{"value": -1.9, "curve": [0.168, -3.12, 0.334, -4.13]}, {"time": 0.5, "value": -4.13, "curve": [0.944, -4.13, 1.389, 2.87]}, {"time": 1.8333, "value": 2.87, "curve": [2.278, 2.87, 2.722, -4.13]}, {"time": 3.1667, "value": -4.13, "curve": [3.611, -4.13, 4.056, 2.87]}, {"time": 4.5, "value": 2.87, "curve": [4.944, 2.87, 5.389, -4.13]}, {"time": 5.8333, "value": -4.13, "curve": [6.167, -4.13, 6.5, 2.87]}, {"time": 6.8333, "value": 2.87, "curve": [7.278, 2.87, 7.722, -4.13]}, {"time": 8.1667, "value": -4.13, "curve": [8.611, -4.13, 9.056, 2.87]}, {"time": 9.5, "value": 2.87, "curve": [9.944, 2.87, 10.389, -4.13]}, {"time": 10.8333, "value": -4.13, "curve": [11.278, -4.13, 11.722, 2.87]}, {"time": 12.1667, "value": 2.87, "curve": [12.445, 2.87, 12.724, 0.15]}, {"time": 13, "value": -1.9}]}, "hand_L2": {"rotate": [{"value": -0.63, "curve": [0.225, -2.37, 0.446, -4.13]}, {"time": 0.6667, "value": -4.13, "curve": [1.111, -4.13, 1.556, 2.87]}, {"time": 2, "value": 2.87, "curve": [2.444, 2.87, 2.889, -4.13]}, {"time": 3.3333, "value": -4.13, "curve": [3.778, -4.13, 4.222, 2.87]}, {"time": 4.6667, "value": 2.87, "curve": [5.111, 2.87, 5.556, -4.13]}, {"time": 6, "value": -4.13, "curve": [6.333, -4.13, 6.667, 2.87]}, {"time": 7, "value": 2.87, "curve": [7.444, 2.87, 7.889, -4.13]}, {"time": 8.3333, "value": -4.13, "curve": [8.778, -4.13, 9.222, 2.87]}, {"time": 9.6667, "value": 2.87, "curve": [10.111, 2.87, 10.556, -4.13]}, {"time": 11, "value": -4.13, "curve": [11.444, -4.13, 11.889, 2.87]}, {"time": 12.3333, "value": 2.87, "curve": [12.557, 2.87, 12.781, 1.13]}, {"time": 13, "value": -0.63}]}, "arm_L2": {"translate": [{"x": -10.93, "y": -17.14, "curve": [0.114, -15.12, 0.224, -18.25, 0.114, -19.8, 0.224, -21.79]}, {"time": 0.3333, "x": -18.25, "y": -21.79, "curve": [0.778, -18.25, 1.222, 27.48, 0.778, -21.79, 1.222, 7.29]}, {"time": 1.6667, "x": 27.48, "y": 7.29, "curve": [2.111, 27.48, 2.556, -18.25, 2.111, 7.29, 2.556, -21.79]}, {"time": 3, "x": -18.25, "y": -21.79, "curve": [3.444, -18.25, 3.889, 27.48, 3.444, -21.79, 3.889, 7.29]}, {"time": 4.3333, "x": 27.48, "y": 7.29, "curve": [4.778, 27.48, 5.222, -18.25, 4.778, 7.29, 5.222, -21.79]}, {"time": 5.6667, "x": -18.25, "y": -21.79, "curve": [6, -18.25, 6.333, 27.48, 6, -21.79, 6.333, 7.29]}, {"time": 6.6667, "x": 27.48, "y": 7.29, "curve": [7.111, 27.48, 7.556, -18.25, 7.111, 7.29, 7.556, -21.79]}, {"time": 8, "x": -18.25, "y": -21.79, "curve": [8.444, -18.25, 8.889, 27.48, 8.444, -21.79, 8.889, 7.29]}, {"time": 9.3333, "x": 27.48, "y": 7.29, "curve": [9.778, 27.48, 10.222, -18.25, 9.778, 7.29, 10.222, -21.79]}, {"time": 10.6667, "x": -18.25, "y": -21.79, "curve": [11.111, -18.25, 11.556, 27.48, 11.111, -21.79, 11.556, 7.29]}, {"time": 12, "x": 27.48, "y": 7.29, "curve": [12.335, 27.48, 12.67, 1.87, 12.335, 7.29, 12.67, -9]}, {"time": 13, "x": -10.93, "y": -17.14}]}, "leg_R4": {"translate": [{"x": -22.23, "y": 7.41, "curve": [0.444, -22.23, 0.889, 11.86, 0.444, 7.41, 0.889, -4.45]}, {"time": 1.3333, "x": 11.86, "y": -4.45, "curve": [1.778, 11.86, 2.222, -22.23, 1.778, -4.45, 2.222, 7.41]}, {"time": 2.6667, "x": -22.23, "y": 7.41, "curve": [3.111, -22.23, 3.556, 11.86, 3.111, 7.41, 3.556, -4.45]}, {"time": 4, "x": 11.86, "y": -4.45, "curve": [4.444, 11.86, 4.889, -22.23, 4.444, -4.45, 4.889, 7.41]}, {"time": 5.3333, "x": -22.23, "y": 7.41, "curve": [5.667, -22.23, 6, 11.86, 5.667, 7.41, 6, -4.45]}, {"time": 6.3333, "x": 11.86, "y": -4.45, "curve": [6.778, 11.86, 7.222, -22.23, 6.778, -4.45, 7.222, 7.41]}, {"time": 7.6667, "x": -22.23, "y": 7.41, "curve": [8.111, -22.23, 8.556, -5.19, 8.111, 7.41, 8.556, 1.48]}, {"time": 9, "x": -5.19, "y": 1.48, "curve": [9.444, -5.19, 9.889, -22.23, 9.444, 1.48, 9.889, 7.41]}, {"time": 10.3333, "x": -22.23, "y": 7.41, "curve": [10.778, -22.23, 11.222, 11.86, 10.778, 7.41, 11.222, -4.45]}, {"time": 11.6667, "x": 11.86, "y": -4.45, "curve": [12.111, 11.86, 12.556, -22.23, 12.111, -4.45, 12.556, 7.41]}, {"time": 13, "x": -22.23, "y": 7.41}]}, "leg_R5": {"rotate": [{}]}, "arm_Lb2": {"scale": [{"x": 1.074, "curve": [0.114, 1.092, 0.224, 1.105, 0.114, 1, 0.224, 1]}, {"time": 0.3333, "x": 1.105, "curve": [0.778, 1.105, 1.222, 0.907, 0.778, 1, 1.222, 1]}, {"time": 1.6667, "x": 0.907, "curve": [2.111, 0.907, 2.556, 1.105, 2.111, 1, 2.556, 1]}, {"time": 3, "x": 1.105, "curve": [3.444, 1.105, 3.889, 0.907, 3.444, 1, 3.889, 1]}, {"time": 4.3333, "x": 0.907, "curve": [4.778, 0.907, 5.222, 1.105, 4.778, 1, 5.222, 1]}, {"time": 5.6667, "x": 1.105, "curve": [6, 1.105, 6.333, 0.907, 6, 1, 6.333, 1]}, {"time": 6.6667, "x": 0.907, "curve": [7.111, 0.907, 7.556, 1.105, 7.111, 1, 7.556, 1]}, {"time": 8, "x": 1.105, "curve": [8.444, 1.105, 8.889, 0.907, 8.444, 1, 8.889, 1]}, {"time": 9.3333, "x": 0.907, "curve": [9.778, 0.907, 10.222, 1.105, 9.778, 1, 10.222, 1]}, {"time": 10.6667, "x": 1.105, "curve": [11.111, 1.105, 11.556, 0.907, 11.111, 1, 11.556, 1]}, {"time": 12, "x": 0.907, "curve": [12.335, 0.907, 12.67, 1.018, 12.335, 1, 12.67, 1]}, {"time": 13, "x": 1.074}]}, "eye_L": {"translate": [{"time": 2.1667, "curve": [2.2, 0, 2.233, -2.44, 2.2, 0, 2.233, -0.39]}, {"time": 2.2667, "x": -2.44, "y": -0.39, "curve": "stepped"}, {"time": 3.1667, "x": -2.44, "y": -0.39, "curve": [3.2, -2.44, 3.233, -0.99, 3.2, -0.39, 3.233, -0.45]}, {"time": 3.2667, "x": -0.99, "y": -0.45, "curve": "stepped"}, {"time": 3.7333, "x": -0.99, "y": -0.45, "curve": [3.767, -0.99, 3.8, 0, 3.767, -0.45, 3.8, 0]}, {"time": 3.8333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -0.66, 6.2, 0, 6.233, -3]}, {"time": 6.2667, "x": -0.66, "y": -3, "curve": "stepped"}, {"time": 7, "x": -0.66, "y": -3, "curve": [7.033, -0.66, 7.067, -0.66, 7.033, -3, 7.067, -4.45]}, {"time": 7.1, "x": -0.66, "y": -4.45, "curve": "stepped"}, {"time": 8, "x": -0.66, "y": -4.45, "curve": [8.033, -0.66, 8.067, -1.89, 8.033, -4.45, 8.067, -4.25]}, {"time": 8.1, "x": -1.89, "y": -4.25, "curve": "stepped"}, {"time": 8.4, "x": -1.89, "y": -4.25, "curve": [8.433, -1.89, 8.467, -0.66, 8.433, -4.25, 8.467, -3]}, {"time": 8.5, "x": -0.66, "y": -3, "curve": "stepped"}, {"time": 10.1667, "x": -0.66, "y": -3, "curve": [10.2, -0.66, 10.233, 0, 10.2, -3, 10.233, 0]}, {"time": 10.2667}]}, "eye_R": {"translate": [{"time": 2.1667, "curve": [2.2, 0, 2.233, -2.93, 2.2, 0, 2.233, -0.39]}, {"time": 2.2667, "x": -2.93, "y": -0.39, "curve": "stepped"}, {"time": 3.1667, "x": -2.93, "y": -0.39, "curve": [3.2, -2.93, 3.233, -1.48, 3.2, -0.39, 3.233, -0.45]}, {"time": 3.2667, "x": -1.48, "y": -0.45, "curve": "stepped"}, {"time": 3.7333, "x": -1.48, "y": -0.45, "curve": [3.767, -1.48, 3.8, 0, 3.767, -0.45, 3.8, 0]}, {"time": 3.8333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -1.77, 6.2, 0, 6.233, -3.26]}, {"time": 6.2667, "x": -1.77, "y": -3.26, "curve": "stepped"}, {"time": 7, "x": -1.77, "y": -3.26, "curve": [7.033, -1.77, 7.067, -1.77, 7.033, -3.26, 7.067, -4.71]}, {"time": 7.1, "x": -1.77, "y": -4.71, "curve": "stepped"}, {"time": 8, "x": -1.77, "y": -4.71, "curve": [8.033, -1.77, 8.067, -2.99, 8.033, -4.71, 8.067, -4.5]}, {"time": 8.1, "x": -2.99, "y": -4.5, "curve": "stepped"}, {"time": 8.4, "x": -2.99, "y": -4.5, "curve": [8.433, -2.99, 8.467, -1.77, 8.433, -4.5, 8.467, -3.26]}, {"time": 8.5, "x": -1.77, "y": -3.26, "curve": "stepped"}, {"time": 10.1667, "x": -1.77, "y": -3.26, "curve": [10.2, -1.77, 10.233, 0, 10.2, -3.26, 10.233, 0]}, {"time": 10.2667}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-4.14209, -0.38168, -4.1405, -0.38161, -3.33032, -0.34869, -3.32898, -0.34863, -1.90186, 0.24239, -1.90125, 0.24246, -0.66089, 0.03307, -0.66077, 0.03308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -8.3313, -0.56827, -8.32947, -0.56792, -11.11658, -0.1275, -11.11218, -0.12712, -11.07812, -0.69712, -11.07568, -0.69698, -8.87781, -1.01617, -8.87659, -1.01605, -8.12317, -0.47701, -8.11951, -0.47681, -6.02515, -0.20921, -6.02161, -0.20905, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -9.06165, -0.45806, -9.06018, -0.45772, -11.86047, -0.09027, -11.85608, -0.08986, -11.19104, -0.50333, -11.18933, -0.50302, -7.83594, -0.63855, -7.83411, -0.63843, -2.72815, -0.08435, -2.72766, -0.08424, -1.11218, -0.16521, -1.11206, -0.16516], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-4.14209, -0.38168, -4.1405, -0.38161, -3.33032, -0.34869, -3.32898, -0.34863, -1.90186, 0.24239, -1.90125, 0.24246, -0.66089, 0.03307, -0.66077, 0.03308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -8.3313, -0.56827, -8.32947, -0.56792, -11.11658, -0.1275, -11.11218, -0.12712, -11.07812, -0.69712, -11.07568, -0.69698, -8.87781, -1.01617, -8.87659, -1.01605, -8.12317, -0.47701, -8.11951, -0.47681, -6.02515, -0.20921, -6.02161, -0.20905, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -9.06165, -0.45806, -9.06018, -0.45772, -11.86047, -0.09027, -11.85608, -0.08986, -11.19104, -0.50333, -11.18933, -0.50302, -7.83594, -0.63855, -7.83411, -0.63843, -2.72815, -0.08435, -2.72766, -0.08424, -1.11218, -0.16521, -1.11206, -0.16516], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "vertices": [-4.14209, -0.38168, -4.1405, -0.38161, -3.33032, -0.34869, -3.32898, -0.34863, -1.90186, 0.24239, -1.90125, 0.24246, -0.66089, 0.03307, -0.66077, 0.03308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -8.3313, -0.56827, -8.32947, -0.56792, -11.11658, -0.1275, -11.11218, -0.12712, -11.07812, -0.69712, -11.07568, -0.69698, -8.87781, -1.01617, -8.87659, -1.01605, -8.12317, -0.47701, -8.11951, -0.47681, -6.02515, -0.20921, -6.02161, -0.20905, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -9.06165, -0.45806, -9.06018, -0.45772, -11.86047, -0.09027, -11.85608, -0.08986, -11.19104, -0.50333, -11.18933, -0.50302, -7.83594, -0.63855, -7.83411, -0.63843, -2.72815, -0.08435, -2.72766, -0.08424, -1.11218, -0.16521, -1.11206, -0.16516], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-7.79968, -0.17298, -7.79968, -0.17278, -8.48132, -0.09947, -8.47998, -0.09935, -10.70215, 0.08652, -10.70056, 0.08669, -10.76172, -0.90234, -10.75903, -0.90211, -8.13037, -0.74312, -8.12842, -0.74286, -3.18921, -0.32513, -3.18921, -0.32494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88721, 0.11578, -0.88708, 0.11595, -2.03784, -0.04106, -2.03772, -0.04082, -4.03076, 0.10783, -4.03076, 0.10791, -5.72546, 0.00484, -5.72546, 0.00502, -7.40625, 0.18278, -7.40625, 0.18303, 0, 0, 0, 0, -4.19592, -0.18666, -4.19592, -0.18642, -8.4165, -0.83958, -8.41455, -0.8394, -10.76172, -0.90234, -10.75903, -0.90211, -11.3075, -0.21767, -11.30603, -0.21758, -9.91382, -0.26749, -9.91223, -0.26755, -6.68518, -0.01719, -6.6842, -0.01723, -2.80884, 0.01843, -2.80859, 0.01837, -1.05066, -0.25283, -1.05066, -0.25284], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-7.79968, -0.17298, -7.79968, -0.17278, -8.48132, -0.09947, -8.47998, -0.09935, -10.70215, 0.08652, -10.70056, 0.08669, -10.76172, -0.90234, -10.75903, -0.90211, -8.13037, -0.74312, -8.12842, -0.74286, -3.18921, -0.32513, -3.18921, -0.32494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88721, 0.11578, -0.88708, 0.11595, -2.03784, -0.04106, -2.03772, -0.04082, -4.03076, 0.10783, -4.03076, 0.10791, -5.72546, 0.00484, -5.72546, 0.00502, -7.40625, 0.18278, -7.40625, 0.18303, 0, 0, 0, 0, -4.19592, -0.18666, -4.19592, -0.18642, -8.4165, -0.83958, -8.41455, -0.8394, -10.76172, -0.90234, -10.75903, -0.90211, -11.3075, -0.21767, -11.30603, -0.21758, -9.91382, -0.26749, -9.91223, -0.26755, -6.68518, -0.01719, -6.6842, -0.01723, -2.80884, 0.01843, -2.80859, 0.01837, -1.05066, -0.25283, -1.05066, -0.25284], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "vertices": [-7.79968, -0.17298, -7.79968, -0.17278, -8.48132, -0.09947, -8.47998, -0.09935, -10.70215, 0.08652, -10.70056, 0.08669, -10.76172, -0.90234, -10.75903, -0.90211, -8.13037, -0.74312, -8.12842, -0.74286, -3.18921, -0.32513, -3.18921, -0.32494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88721, 0.11578, -0.88708, 0.11595, -2.03784, -0.04106, -2.03772, -0.04082, -4.03076, 0.10783, -4.03076, 0.10791, -5.72546, 0.00484, -5.72546, 0.00502, -7.40625, 0.18278, -7.40625, 0.18303, 0, 0, 0, 0, -4.19592, -0.18666, -4.19592, -0.18642, -8.4165, -0.83958, -8.41455, -0.8394, -10.76172, -0.90234, -10.75903, -0.90211, -11.3075, -0.21767, -11.30603, -0.21758, -9.91382, -0.26749, -9.91223, -0.26755, -6.68518, -0.01719, -6.6842, -0.01723, -2.80884, 0.01843, -2.80859, 0.01837, -1.05066, -0.25283, -1.05066, -0.25284], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 512, "vertices": [-3.23401, 0.16182, -3.23376, 0.16196, -7.46509, 0.19348, -7.46484, 0.1936, -10.37207, -0.29153, -10.37048, -0.29131, -10.89832, 0.005, -10.89673, 0.00514, -9.29431, -0.34551, -9.29224, -0.34523, -5.58826, -0.08058, -5.58716, -0.08054, -0.45374, -0.06736, -0.45361, -0.06735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.95459, -0.02225, -4.95398, -0.02216, -8.62476, 0.4316, -8.6239, 0.43181, -10.51965, 0.34635, -10.51904, 0.34656, -10.16089, 0.32837, -10.16028, 0.32857, -7.00769, 0.35069, -7.00732, 0.35086, -2.69946, 0.045, -2.69934, 0.04514], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 512, "vertices": [-3.23401, 0.16182, -3.23376, 0.16196, -7.46509, 0.19348, -7.46484, 0.1936, -10.37207, -0.29153, -10.37048, -0.29131, -10.89832, 0.005, -10.89673, 0.00514, -9.29431, -0.34551, -9.29224, -0.34523, -5.58826, -0.08058, -5.58716, -0.08054, -0.45374, -0.06736, -0.45361, -0.06735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.95459, -0.02225, -4.95398, -0.02216, -8.62476, 0.4316, -8.6239, 0.43181, -10.51965, 0.34635, -10.51904, 0.34656, -10.16089, 0.32837, -10.16028, 0.32857, -7.00769, 0.35069, -7.00732, 0.35086, -2.69946, 0.045, -2.69934, 0.04514], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "offset": 512, "vertices": [-3.23401, 0.16182, -3.23376, 0.16196, -7.46509, 0.19348, -7.46484, 0.1936, -10.37207, -0.29153, -10.37048, -0.29131, -10.89832, 0.005, -10.89673, 0.00514, -9.29431, -0.34551, -9.29224, -0.34523, -5.58826, -0.08058, -5.58716, -0.08054, -0.45374, -0.06736, -0.45361, -0.06735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.95459, -0.02225, -4.95398, -0.02216, -8.62476, 0.4316, -8.6239, 0.43181, -10.51965, 0.34635, -10.51904, 0.34656, -10.16089, 0.32837, -10.16028, 0.32857, -7.00769, 0.35069, -7.00732, 0.35086, -2.69946, 0.045, -2.69934, 0.04514], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 60, "vertices": [0.38232, 0.62472, 0.38269, 0.62472, -0.04468, -0.00028, -0.04468, -0.00029, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, 0.20764, 0.00136, 0.20764, 0.00134, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, -0.04468, -0.00028, -0.04468, -0.00029, -0.27136, -0.98679, -0.27124, -0.98678, 0.96606, 0.5452, 0.96606, 0.5452, 0.30103, 0.29408, 0.30127, 0.29408, -0.41516, -0.00267, -0.41516, -0.00268, -0.33948, -0.00218, -0.33948, -0.00219, 0.20764, 0.00136, 0.20764, 0.00134, -0.18799, -0.00121, -0.18799, -0.00121, -0.26367, -0.00169, -0.26367, -0.0017, 0.30481, -0.29018, 0.30481, -0.29018, 0.39453, -0.89921, 0.39453, -0.8992, 1.54956, 0.46568, 1.54968, 0.46567, 1.22864, -0.20518, 1.22876, -0.20523, 0.94617, -0.20421, 0.94641, -0.20422, 1.24609, -0.09574, 1.24622, -0.09576, 1.24609, -0.09574, 1.24622, -0.09576, 0.94617, -0.20421, 0.94641, -0.20422, 1.2959, 0.00842, 1.29602, 0.00836, 1.06091, -0.8949, 1.06091, -0.8949, 0.39453, -0.89921, 0.39453, -0.8992, 0.39453, -0.89921, 0.39453, -0.8992, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.31958, 0.00206, 0.31958, 0.00206, 1.03857, -0.09709, 1.0387, -0.09711, 1.03857, -0.09709, 1.0387, -0.09711, 0.31958, 0.00206, 0.31958, 0.00206, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298], "curve": "stepped"}, {"time": 9.8333, "offset": 60, "vertices": [0.38232, 0.62472, 0.38269, 0.62472, -0.04468, -0.00028, -0.04468, -0.00029, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, 0.20764, 0.00136, 0.20764, 0.00134, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, -0.04468, -0.00028, -0.04468, -0.00029, -0.27136, -0.98679, -0.27124, -0.98678, 0.96606, 0.5452, 0.96606, 0.5452, 0.30103, 0.29408, 0.30127, 0.29408, -0.41516, -0.00267, -0.41516, -0.00268, -0.33948, -0.00218, -0.33948, -0.00219, 0.20764, 0.00136, 0.20764, 0.00134, -0.18799, -0.00121, -0.18799, -0.00121, -0.26367, -0.00169, -0.26367, -0.0017, 0.30481, -0.29018, 0.30481, -0.29018, 0.39453, -0.89921, 0.39453, -0.8992, 1.54956, 0.46568, 1.54968, 0.46567, 1.22864, -0.20518, 1.22876, -0.20523, 0.94617, -0.20421, 0.94641, -0.20422, 1.24609, -0.09574, 1.24622, -0.09576, 1.24609, -0.09574, 1.24622, -0.09576, 0.94617, -0.20421, 0.94641, -0.20422, 1.2959, 0.00842, 1.29602, 0.00836, 1.06091, -0.8949, 1.06091, -0.8949, 0.39453, -0.89921, 0.39453, -0.8992, 0.39453, -0.89921, 0.39453, -0.8992, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.31958, 0.00206, 0.31958, 0.00206, 1.03857, -0.09709, 1.0387, -0.09711, 1.03857, -0.09709, 1.0387, -0.09711, 0.31958, 0.00206, 0.31958, 0.00206, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298], "curve": [10.056, 0, 10.278, 1]}, {"time": 10.5}]}}}}}, "1": {"bones": {"body": {"rotate": [{"value": 2.05, "curve": [0.024, 2.05, 0.059, -3.52]}, {"time": 0.2333, "value": -3.52, "curve": [0.544, -3.52, 0.856, 2.05]}, {"time": 1.1667, "value": 2.05}]}, "body2": {"rotate": [{"curve": [0.024, 0, 0.059, -4.23]}, {"time": 0.2333, "value": -4.23, "curve": [0.544, -4.23, 0.856, 0]}, {"time": 1.1667}], "translate": [{"x": -5.67, "curve": [0.024, -5.67, 0.059, 10.68, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 10.68, "curve": [0.544, 10.68, 0.856, -5.67, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -5.67}], "scale": [{"y": 1.06, "curve": [0.078, 1, 0.156, 1, 0.024, 1.06, 0.059, 0.886]}, {"time": 0.2333, "y": 0.886, "curve": [0.544, 1, 0.856, 1, 0.544, 0.886, 0.856, 1.06]}, {"time": 1.1667, "y": 1.06}]}, "body3": {"rotate": [{"value": -0.07, "curve": [0.028, -0.07, 0.067, -2.11]}, {"time": 0.2667, "value": -2.11, "curve": [0.567, -2.11, 0.867, -0.07]}, {"time": 1.1667, "value": -0.07}], "translate": [{"x": -5.25, "curve": [0.028, -5.25, 0.067, 11.38, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 11.38, "curve": [0.567, 11.38, 0.867, -5.25, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -5.25}], "scale": [{"x": 1.001, "y": 1.001, "curve": [0.028, 1.001, 0.067, 1.021, 0.028, 1.001, 0.067, 1.021]}, {"time": 0.2667, "x": 1.021, "y": 1.021, "curve": [0.567, 1.021, 0.867, 1.001, 0.567, 1.021, 0.867, 1.001]}, {"time": 1.1667, "x": 1.001, "y": 1.001}]}, "neck": {"rotate": [{"value": 0.62, "curve": [0.031, 0.62, 0.076, -6.96]}, {"time": 0.3, "value": -6.96, "curve": [0.589, -6.96, 0.878, 0.62]}, {"time": 1.1667, "value": 0.62}]}, "head": {"rotate": [{"value": -0.29, "curve": [0.031, -0.29, 0.076, -8.43]}, {"time": 0.3, "value": -8.43, "curve": [0.589, -8.43, 0.878, -0.29]}, {"time": 1.1667, "value": -0.29}]}, "eyebrow_L": {"translate": [{"curve": [0.024, 0, 0.059, -2.02, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -2.02, "curve": [0.544, -2.02, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.024, 0, 0.059, -1.06]}, {"time": 0.2333, "value": -1.06, "curve": [0.544, -1.06, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.059, 0.968, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.968, "curve": [0.544, 0.968, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.024, 0, 0.059, -16.69]}, {"time": 0.2333, "value": -16.69, "curve": [0.544, -16.69, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.024, 0, 0.059, -2.02, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -2.02, "curve": [0.544, -2.02, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.024, 0, 0.059, 0.11]}, {"time": 0.2333, "value": 0.11, "curve": [0.544, 0.11, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.059, 0.968, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.968, "curve": [0.544, 0.968, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.024, 0, 0.059, 17.88]}, {"time": 0.2333, "value": 17.88, "curve": [0.544, 17.88, 0.856, 0]}, {"time": 1.1667}]}, "earring_L": {"rotate": [{"value": 5.66, "curve": [0.098, 5.66, 0.202, -5.07]}, {"time": 0.3, "value": -5.07, "curve": [0.398, -5.07, 0.502, 5.66]}, {"time": 0.6, "value": 5.66, "curve": [0.698, 5.66, 0.802, -5.07]}, {"time": 0.9, "value": -5.07, "curve": [0.998, -5.07, 1.068, 5.66]}, {"time": 1.1667, "value": 5.66}]}, "earring_R": {"rotate": [{"value": 5.66, "curve": [0.098, 5.66, 0.202, -5.07]}, {"time": 0.3, "value": -5.07, "curve": [0.398, -5.07, 0.502, 5.66]}, {"time": 0.6, "value": 5.66, "curve": [0.698, 5.66, 0.802, -5.07]}, {"time": 0.9, "value": -5.07, "curve": [0.998, -5.07, 1.068, 5.66]}, {"time": 1.1667, "value": 5.66}]}, "sh_L": {"translate": [{"x": -4.48, "curve": [0.028, -4.48, 0.067, 10.24, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 10.24, "curve": [0.567, 10.24, 0.867, -4.48, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.48}]}, "sh_R": {"translate": [{"x": -4.48, "curve": [0.028, -4.48, 0.067, 10.24, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 10.24, "curve": [0.567, 10.24, 0.867, -4.48, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.48}]}, "RU_L": {"translate": [{"x": -24.19, "curve": [0.05, -30.75, 0.085, -35.64, 0.05, 0, 0.085, 0]}, {"time": 0.1333, "x": -35.64, "curve": [0.329, -35.64, 0.538, 35.91, 0.329, 0, 0.538, 0]}, {"time": 0.7333, "x": 35.91, "curve": [0.881, 35.91, 1.021, -4.16, 0.881, 0, 1.021, 0]}, {"time": 1.1667, "x": -24.19}], "scale": [{"x": 1.007, "y": 0.997, "curve": [0.049, 0.979, 0.084, 0.952, 0.049, 1.026, 0.084, 1.055]}, {"time": 0.1333, "x": 0.952, "y": 1.055, "curve": [0.231, 0.952, 0.335, 1.062, 0.231, 1.055, 0.335, 0.938]}, {"time": 0.4333, "x": 1.062, "y": 0.938, "curve": [0.531, 1.062, 0.635, 0.952, 0.531, 0.938, 0.635, 1.055]}, {"time": 0.7333, "x": 0.952, "y": 1.055, "curve": [0.831, 0.952, 0.935, 1.062, 0.831, 1.055, 0.935, 0.938]}, {"time": 1.0333, "x": 1.062, "y": 0.938, "curve": [1.083, 1.062, 1.118, 1.034, 1.083, 0.938, 1.118, 0.967]}, {"time": 1.1667, "x": 1.007, "y": 0.997}]}, "RU_L2": {"translate": [{"x": -13.4, "curve": [0.074, -24.19, 0.16, -33.04, 0.074, 0, 0.16, 0]}, {"time": 0.2333, "x": -33.04, "curve": [0.429, -33.04, 0.604, 28.72, 0.429, 0, 0.604, 0]}, {"time": 0.8, "x": 28.72, "curve": [0.923, 28.72, 1.045, 4.71, 0.923, 0, 1.045, 0]}, {"time": 1.1667, "x": -13.4}], "scale": [{"x": 1.044, "y": 0.957, "curve": [0.073, 1.013, 0.16, 0.952, 0.073, 0.99, 0.16, 1.055]}, {"time": 0.2333, "x": 0.952, "y": 1.055, "curve": [0.331, 0.952, 0.402, 1.062, 0.331, 1.055, 0.402, 0.938]}, {"time": 0.5, "x": 1.062, "y": 0.938, "curve": [0.598, 1.062, 0.702, 0.952, 0.598, 0.938, 0.702, 1.055]}, {"time": 0.8, "x": 0.952, "y": 1.055, "curve": [0.898, 0.952, 1.002, 1.062, 0.898, 1.055, 1.002, 0.938]}, {"time": 1.1, "x": 1.062, "y": 0.938, "curve": [1.125, 1.062, 1.143, 1.055, 1.125, 0.938, 1.143, 0.946]}, {"time": 1.1667, "x": 1.044, "y": 0.957}]}, "RU_L3": {"translate": [{"x": -2.48, "curve": [0.099, -16.58, 0.203, -30.86, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -30.86, "curve": [0.496, -30.86, 0.671, 25.9, 0.496, 0, 0.671, 0]}, {"time": 0.8667, "x": 25.9, "curve": [0.965, 25.9, 1.07, 11.8, 0.965, 0, 1.07, 0]}, {"time": 1.1667, "x": -2.48}], "scale": [{"x": 1.062, "y": 0.938, "curve": [0.098, 1.062, 0.202, 0.952, 0.098, 0.938, 0.202, 1.055]}, {"time": 0.3, "x": 0.952, "y": 1.055, "curve": [0.398, 0.952, 0.502, 1.062, 0.398, 1.055, 0.502, 0.938]}, {"time": 0.6, "x": 1.062, "y": 0.938, "curve": [0.698, 1.062, 0.769, 0.952, 0.698, 0.938, 0.769, 1.055]}, {"time": 0.8667, "x": 0.952, "y": 1.055, "curve": [0.965, 0.952, 1.069, 1.062, 0.965, 1.055, 1.069, 0.938]}, {"time": 1.1667, "x": 1.062, "y": 0.938}]}, "RU_R": {"translate": [{"x": -27.64, "curve": [0.196, -27.64, 0.404, 33.71, 0.196, 0, 0.404, 0]}, {"time": 0.6, "x": 33.71, "curve": [0.796, 33.71, 0.971, -27.64, 0.796, 0, 0.971, 0]}, {"time": 1.1667, "x": -27.64}], "scale": [{"x": 0.952, "y": 1.055, "curve": [0.098, 0.952, 0.202, 1.062, 0.098, 1.055, 0.202, 0.938]}, {"time": 0.3, "x": 1.062, "y": 0.938, "curve": [0.398, 1.062, 0.502, 0.952, 0.398, 0.938, 0.502, 1.055]}, {"time": 0.6, "x": 0.952, "y": 1.055, "curve": [0.698, 0.952, 0.769, 1.062, 0.698, 1.055, 0.769, 0.938]}, {"time": 0.8667, "x": 1.062, "y": 0.938, "curve": [0.965, 1.062, 1.069, 0.952, 0.965, 0.938, 1.069, 1.055]}, {"time": 1.1667, "x": 0.952, "y": 1.055}]}, "RU_R2": {"translate": [{"x": -10.24, "curve": [0.074, -19.45, 0.16, -27.01, 0.074, 0, 0.16, 0]}, {"time": 0.2333, "x": -27.01, "curve": [0.429, -27.01, 0.604, 25.72, 0.429, 0, 0.604, 0]}, {"time": 0.8, "x": 25.72, "curve": [0.923, 25.72, 1.045, 5.22, 0.923, 0, 1.045, 0]}, {"time": 1.1667, "x": -10.24}], "scale": [{"x": 1.044, "y": 0.957, "curve": [0.073, 1.013, 0.16, 0.952, 0.073, 0.99, 0.16, 1.055]}, {"time": 0.2333, "x": 0.952, "y": 1.055, "curve": [0.331, 0.952, 0.402, 1.062, 0.331, 1.055, 0.402, 0.938]}, {"time": 0.5, "x": 1.062, "y": 0.938, "curve": [0.598, 1.062, 0.702, 0.952, 0.598, 0.938, 0.702, 1.055]}, {"time": 0.8, "x": 0.952, "y": 1.055, "curve": [0.898, 0.952, 1.002, 1.062, 0.898, 1.055, 1.002, 0.938]}, {"time": 1.1, "x": 1.062, "y": 0.938, "curve": [1.125, 1.062, 1.143, 1.055, 1.125, 0.938, 1.143, 0.946]}, {"time": 1.1667, "x": 1.044, "y": 0.957}]}, "RU_R3": {"translate": [{"x": -1.42, "curve": [0.099, -13.03, 0.203, -24.79, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -24.79, "curve": [0.496, -24.79, 0.671, 21.94, 0.496, 0, 0.671, 0]}, {"time": 0.8667, "x": 21.94, "curve": [0.965, 21.94, 1.07, 10.34, 0.965, 0, 1.07, 0]}, {"time": 1.1667, "x": -1.42}], "scale": [{"x": 1.062, "y": 0.938, "curve": [0.098, 1.062, 0.202, 0.952, 0.098, 0.938, 0.202, 1.055]}, {"time": 0.3, "x": 0.952, "y": 1.055, "curve": [0.398, 0.952, 0.502, 1.062, 0.398, 1.055, 0.502, 0.938]}, {"time": 0.6, "x": 1.062, "y": 0.938, "curve": [0.698, 1.062, 0.769, 0.952, 0.698, 0.938, 0.769, 1.055]}, {"time": 0.8667, "x": 0.952, "y": 1.055, "curve": [0.965, 0.952, 1.069, 1.062, 0.965, 1.055, 1.069, 0.938]}, {"time": 1.1667, "x": 1.062, "y": 0.938}]}, "arm_L": {"rotate": [{"value": 2.05, "curve": [0.028, 2.05, 0.067, -2.09]}, {"time": 0.2667, "value": -2.09, "curve": [0.567, -2.09, 0.867, 2.05]}, {"time": 1.1667, "value": 2.05}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{"value": -0.06}]}, "leg_L": {"translate": [{"x": 11.66, "y": -15.54, "curve": [0.024, 11.66, 0.059, -18.03, 0.024, -15.54, 0.059, 19.95]}, {"time": 0.2333, "x": -18.03, "y": 19.95, "curve": [0.544, -18.03, 0.856, 11.66, 0.544, 19.95, 0.856, -15.54]}, {"time": 1.1667, "x": 11.66, "y": -15.54}]}, "leg_L2": {"rotate": [{"value": -1.58, "curve": [0.024, -1.58, 0.059, 4.28]}, {"time": 0.2333, "value": 4.28, "curve": [0.544, 4.28, 0.856, -1.58]}, {"time": 1.1667, "value": -1.58}]}, "leg_L3": {"rotate": [{"value": -1.58, "curve": [0.024, -1.58, 0.059, 2.4]}, {"time": 0.2333, "value": 2.4, "curve": [0.544, 2.4, 0.856, -1.58]}, {"time": 1.1667, "value": -1.58}]}, "leg_L4": {"rotate": [{"value": -1.58, "curve": [0.024, -1.58, 0.059, 2.4]}, {"time": 0.2333, "value": 2.4, "curve": [0.544, 2.4, 0.856, -1.58]}, {"time": 1.1667, "value": -1.58}]}, "foot_R": {"rotate": [{"value": 2.42, "curve": [0.024, 2.42, 0.059, -4.09]}, {"time": 0.2333, "value": -4.09, "curve": [0.544, -4.09, 0.856, 2.42]}, {"time": 1.1667, "value": 2.42}]}, "foot_R2": {"rotate": [{"value": 2.42, "curve": [0.024, 2.42, 0.059, -4.09]}, {"time": 0.2333, "value": -4.09, "curve": [0.544, -4.09, 0.856, 2.42]}, {"time": 1.1667, "value": 2.42}]}, "bone38": {"rotate": [{"value": 20.79, "curve": [0.051, 26.1, 0.085, 30.05]}, {"time": 0.1333, "value": 30.05, "curve": [0.33, 30.05, 0.536, -27.84]}, {"time": 0.7333, "value": -27.84, "curve": [0.882, -27.84, 1.02, 4.58]}, {"time": 1.1667, "value": 20.79}], "translate": [{"x": 18.52, "y": -33.85, "curve": [0.025, 19.64, 0.042, 20.49, 0.025, -35.79, 0.042, -37.26]}, {"time": 0.0667, "x": 20.49, "y": -37.26, "curve": [0.264, 20.49, 0.47, -21.43, 0.264, -37.26, 0.47, 35.4]}, {"time": 0.6667, "x": -21.43, "y": 35.4, "curve": [0.839, -21.43, 0.995, 10.58, 0.839, 35.4, 0.995, -20.07]}, {"time": 1.1667, "x": 18.52, "y": -33.85}]}, "sh_R2": {"rotate": [{}]}, "sh_R3": {"rotate": [{}]}, "arm_R3": {"translate": [{"x": -0.48, "y": 0.56, "curve": [0.028, -0.48, 0.067, -13.5, 0.028, 0.56, 0.067, 18.61]}, {"time": 0.2667, "x": -13.5, "y": 18.61, "curve": [0.567, -13.5, 0.867, -0.48, 0.567, 18.61, 0.867, 0.56]}, {"time": 1.1667, "x": -0.48, "y": 0.56}]}, "headround3": {"translate": [{"x": -6.43, "curve": [0.035, -6.43, 0.084, 257.22, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 257.22, "curve": [0.611, 257.22, 0.889, -6.43, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": -6.43}]}, "headround": {"translate": [{"y": -59.75, "curve": [0.111, 0, 0.222, 0, 0.035, -59.75, 0.084, -198.63]}, {"time": 0.3333, "y": -198.63, "curve": [0.611, 0, 0.889, 0, 0.611, -198.63, 0.889, -59.75]}, {"time": 1.1667, "y": -59.75}]}, "bodyround": {"translate": [{"x": -97.46, "y": 75.45, "curve": [0.028, -97.46, 0.067, 172.93, 0.028, 75.45, 0.067, -221.64]}, {"time": 0.2667, "x": 172.93, "y": -221.64, "curve": [0.567, 172.93, 0.867, -97.46, 0.567, -221.64, 0.867, 75.45]}, {"time": 1.1667, "x": -97.46, "y": 75.45}]}, "arm_Lb": {"rotate": [{}]}, "hand_L": {"rotate": [{"value": -1.9, "curve": [0.075, -3.12, 0.16, -4.13]}, {"time": 0.2333, "value": -4.13, "curve": [0.43, -4.13, 0.603, 2.87]}, {"time": 0.8, "value": 2.87, "curve": [0.923, 2.87, 1.044, 0.15]}, {"time": 1.1667, "value": -1.9}]}, "hand_L2": {"rotate": [{"value": -0.63, "curve": [0.1, -2.37, 0.202, -4.13]}, {"time": 0.3, "value": -4.13, "curve": [0.497, -4.13, 0.703, 2.87]}, {"time": 0.9, "value": 2.87, "curve": [0.999, 2.87, 1.069, 1.13]}, {"time": 1.1667, "value": -0.63}]}, "arm_L2": {"translate": [{"x": -10.93, "y": -17.14, "curve": [0.028, -10.93, 0.067, 26.26, 0.028, -17.14, 0.067, 12.05]}, {"time": 0.2667, "x": 26.26, "y": 12.05, "curve": [0.567, 26.26, 0.867, -10.93, 0.567, 12.05, 0.867, -17.14]}, {"time": 1.1667, "x": -10.93, "y": -17.14}]}, "leg_R4": {"translate": [{"x": -22.23, "y": 7.41, "curve": [0.024, -22.23, 0.059, 22.84, 0.024, 7.41, 0.059, -4.45]}, {"time": 0.2333, "x": 22.84, "y": -4.45, "curve": [0.544, 22.84, 0.856, -22.23, 0.544, -4.45, 0.856, 7.41]}, {"time": 1.1667, "x": -22.23, "y": 7.41}]}, "leg_R5": {"rotate": [{}]}, "arm_Lb2": {"scale": [{"x": 1.074, "curve": [0.028, 1.074, 0.067, 0.856, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.856, "curve": [0.567, 0.856, 0.867, 1.074, 0.567, 1, 0.867, 1]}, {"time": 1.1667, "x": 1.074}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.024, 0, 0.06, 0.99]}, {"time": 0.2333, "vertices": [-0.89469, -0.08244, -0.89435, -0.08243, -0.71935, -0.07532, -0.71906, -0.0753, -0.4108, 0.05236, -0.41067, 0.05237, -0.14275, 0.00714, -0.14273, 0.00715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.92303, -0.08507, -0.9228, -0.08502, -1.79956, -0.12275, -1.79916, -0.12267, -2.40118, -0.02754, -2.40023, -0.02746, -2.39287, -0.15058, -2.39234, -0.15055, -1.9176, -0.21949, -1.91734, -0.21947, -1.7546, -0.10303, -1.75381, -0.10299, -1.30143, -0.04519, -1.30067, -0.04515, 0, 0, 0, 0, -0.92303, -0.08507, -0.9228, -0.08502, -1.95731, -0.09894, -1.957, -0.09887, -2.56186, -0.0195, -2.56091, -0.01941, -2.41726, -0.10872, -2.41689, -0.10865, -1.69256, -0.13793, -1.69216, -0.1379, -0.58928, -0.01822, -0.58917, -0.0182, -0.24023, -0.03569, -0.2402, -0.03567], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.024, 0, 0.06, 0.99]}, {"time": 0.2333, "vertices": [-1.68473, -0.03736, -1.68473, -0.03732, -1.83196, -0.02149, -1.83167, -0.02146, -2.31166, 0.01869, -2.31132, 0.01872, -2.32453, -0.19491, -2.32395, -0.19486, -1.75616, -0.16051, -1.75574, -0.16046, -0.68887, -0.07023, -0.68887, -0.07019, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.19164, 0.02501, -0.19161, 0.02505, -0.44017, -0.00887, -0.44015, -0.00882, -0.87064, 0.02329, -0.87064, 0.02331, -1.2367, 0.00104, -1.2367, 0.00108, -1.59975, 0.03948, -1.59975, 0.03953, 0, 0, 0, 0, -0.90632, -0.04032, -0.90632, -0.04027, -1.81796, -0.18135, -1.81754, -0.18131, -2.32453, -0.19491, -2.32395, -0.19486, -2.44242, -0.04702, -2.4421, -0.047, -2.14138, -0.05778, -2.14104, -0.05779, -1.444, -0.00371, -1.44379, -0.00372, -0.60671, 0.00398, -0.60666, 0.00397, -0.22694, -0.05461, -0.22694, -0.05461], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.024, 0, 0.06, 0.99]}, {"time": 0.2333, "offset": 512, "vertices": [-0.69854, 0.03495, -0.69849, 0.03498, -1.61246, 0.04179, -1.6124, 0.04182, -2.24036, -0.06297, -2.24002, -0.06292, -2.35403, 0.00108, -2.35369, 0.00111, -2.00757, -0.07463, -2.00712, -0.07457, -1.20706, -0.01741, -1.20682, -0.0174, -0.09801, -0.01455, -0.09798, -0.01455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.07019, -0.00481, -1.07006, -0.00479, -1.86295, 0.09323, -1.86276, 0.09327, -2.27224, 0.07481, -2.27211, 0.07486, -2.19475, 0.07093, -2.19462, 0.07097, -1.51366, 0.07575, -1.51358, 0.07579, -0.58308, 0.00972, -0.58306], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.024, 0, 0.06, 0.99]}, {"time": 0.2333, "offset": 60, "vertices": [0.38232, 0.62472, 0.38269, 0.62472, -0.04468, -0.00028, -0.04468, -0.00029, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, 0.20764, 0.00136, 0.20764, 0.00134, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, -0.04468, -0.00028, -0.04468, -0.00029, -0.27136, -0.98679, -0.27124, -0.98678, 0.96606, 0.5452, 0.96606, 0.5452, 0.30103, 0.29408, 0.30127, 0.29408, -0.41516, -0.00267, -0.41516, -0.00268, -0.33948, -0.00218, -0.33948, -0.00219, 0.20764, 0.00136, 0.20764, 0.00134, -0.18799, -0.00121, -0.18799, -0.00121, -0.26367, -0.00169, -0.26367, -0.0017, 0.30481, -0.29018, 0.30481, -0.29018, 0.39453, -0.89921, 0.39453, -0.8992, 1.54956, 0.46568, 1.54968, 0.46567, 1.22864, -0.20518, 1.22876, -0.20523, 0.94617, -0.20421, 0.94641, -0.20422, 1.24609, -0.09574, 1.24622, -0.09576, 1.24609, -0.09574, 1.24622, -0.09576, 0.94617, -0.20421, 0.94641, -0.20422, 1.2959, 0.00842, 1.29602, 0.00836, 1.06091, -0.8949, 1.06091, -0.8949, 0.39453, -0.89921, 0.39453, -0.8992, 0.39453, -0.89921, 0.39453, -0.8992, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.31958, 0.00206, 0.31958, 0.00206, 1.03857, -0.09709, 1.0387, -0.09711, 1.03857, -0.09709, 1.0387, -0.09711, 0.31958, 0.00206, 0.31958, 0.00206, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}}}}, "idle": {"bones": {"body": {"rotate": [{"value": 2.05, "curve": [0.444, 2.05, 0.889, -2.55]}, {"time": 1.3333, "value": -2.55, "curve": [1.778, -2.55, 2.222, 2.05]}, {"time": 2.6667, "value": 2.05, "curve": [3.111, 2.05, 3.556, -2.55]}, {"time": 4, "value": -2.55, "curve": [4.444, -2.55, 4.889, 2.05]}, {"time": 5.3333, "value": 2.05, "curve": [5.667, 2.05, 6, -3.52]}, {"time": 6.3333, "value": -3.52, "curve": [6.778, -3.52, 7.222, -2.33]}, {"time": 7.6667, "value": -2.33, "curve": [8.111, -2.33, 8.556, -3.52]}, {"time": 9, "value": -3.52, "curve": [9.444, -3.52, 9.889, 2.05]}, {"time": 10.3333, "value": 2.05, "curve": [10.778, 2.05, 11.222, -2.55]}, {"time": 11.6667, "value": -2.55, "curve": [12.111, -2.55, 12.556, 2.05]}, {"time": 13, "value": 2.05}]}, "body2": {"rotate": [{"curve": [0.444, 0, 0.889, -2.49]}, {"time": 1.3333, "value": -2.49, "curve": [1.778, -2.49, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -2.49]}, {"time": 4, "value": -2.49, "curve": [4.444, -2.49, 4.889, 0]}, {"time": 5.3333, "curve": [5.667, 0, 6, -4.23]}, {"time": 6.3333, "value": -4.23, "curve": [6.778, -4.23, 7.222, -3.71]}, {"time": 7.6667, "value": -3.71, "curve": [8.111, -3.71, 8.556, -4.23]}, {"time": 9, "value": -4.23, "curve": [9.444, -4.23, 9.889, 0]}, {"time": 10.3333, "curve": [10.778, 0, 11.222, -2.49]}, {"time": 11.6667, "value": -2.49, "curve": [12.111, -2.49, 12.556, 0]}, {"time": 13}], "translate": [{"x": -5.67, "curve": [0.444, -5.67, 0.889, 4.3, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 4.3, "curve": [1.778, 4.3, 2.222, -5.67, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -5.67, "curve": [3.111, -5.67, 3.556, 4.3, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 4.3, "curve": [4.444, 4.3, 4.889, -5.67, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -5.67, "curve": [5.667, -5.67, 6, 8.65, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 8.65, "curve": [6.778, 8.65, 7.222, 1.49, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 1.49, "curve": [8.111, 1.49, 8.556, 8.65, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 8.65, "curve": [9.444, 8.65, 9.889, -5.67, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -5.67, "curve": [10.778, -5.67, 11.222, 4.3, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 4.3, "curve": [12.111, 4.3, 12.556, -5.67, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -5.67}], "scale": [{"y": 1.06, "curve": [0.444, 1, 0.889, 1, 0.444, 1.06, 0.889, 0.915]}, {"time": 1.3333, "y": 0.915, "curve": [1.778, 1, 2.222, 1, 1.778, 0.915, 2.222, 1.06]}, {"time": 2.6667, "y": 1.06, "curve": [3.111, 1, 3.556, 1, 3.111, 1.06, 3.556, 0.915]}, {"time": 4, "y": 0.915, "curve": [4.444, 1, 4.889, 1, 4.444, 0.915, 4.889, 1.06]}, {"time": 5.3333, "y": 1.06, "curve": [5.667, 1, 6, 1, 5.667, 1.06, 6, 0.886]}, {"time": 6.3333, "y": 0.886, "curve": [6.778, 1, 7.222, 1, 6.778, 0.886, 7.222, 0.973]}, {"time": 7.6667, "y": 0.973, "curve": [8.111, 1, 8.556, 1, 8.111, 0.973, 8.556, 0.886]}, {"time": 9, "y": 0.886, "curve": [9.444, 1, 9.889, 1, 9.444, 0.886, 9.889, 1.06]}, {"time": 10.3333, "y": 1.06, "curve": [10.778, 1, 11.222, 1, 10.778, 1.06, 11.222, 0.915]}, {"time": 11.6667, "y": 0.915, "curve": [12.111, 1, 12.556, 1, 12.111, 0.915, 12.556, 1.06]}, {"time": 13, "y": 1.06}]}, "body3": {"rotate": [{"value": -0.07, "curve": [0.057, -0.03, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -1.5]}, {"time": 1.5, "value": -1.5, "curve": [1.944, -1.5, 2.389, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -1.5]}, {"time": 4.1667, "value": -1.5, "curve": [4.611, -1.5, 5.056, 0]}, {"time": 5.5, "curve": [5.833, 0, 6.167, -3.25]}, {"time": 6.5, "value": -3.25, "curve": [6.944, -3.25, 7.389, -2.52]}, {"time": 7.8333, "value": -2.52, "curve": [8.278, -2.52, 8.722, -3.25]}, {"time": 9.1667, "value": -3.25, "curve": [9.611, -3.25, 10.056, 0]}, {"time": 10.5, "curve": [10.944, 0, 11.389, -1.5]}, {"time": 11.8333, "value": -1.5, "curve": [12.223, -1.5, 12.613, -0.36]}, {"time": 13, "value": -0.07}], "translate": [{"x": -5.25, "curve": [0.057, -5.59, 0.112, -5.84, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -5.84, "curve": [0.611, -5.84, 1.056, 6.81, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 6.81, "curve": [1.944, 6.81, 2.389, -5.84, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -5.84, "curve": [3.278, -5.84, 3.722, 6.81, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 6.81, "curve": [4.611, 6.81, 5.056, -5.84, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -5.84, "curve": [5.833, -5.84, 6.167, 7.98, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 7.98, "curve": [6.944, 7.98, 7.389, 1.07, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 1.07, "curve": [8.278, 1.07, 8.722, 7.98, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 7.98, "curve": [9.611, 7.98, 10.056, -5.84, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -5.84, "curve": [10.944, -5.84, 11.389, 6.81, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 6.81, "curve": [12.223, 6.81, 12.613, -2.85, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -5.25}], "scale": [{"x": 1.001, "y": 1.001, "curve": [0.057, 1, 0.112, 1, 0.057, 1, 0.112, 1]}, {"time": 0.1667, "curve": [0.611, 1, 1.056, 1.021, 0.611, 1, 1.056, 1.021]}, {"time": 1.5, "x": 1.021, "y": 1.021, "curve": [1.944, 1.021, 2.389, 1, 1.944, 1.021, 2.389, 1]}, {"time": 2.8333, "curve": [3.278, 1, 3.722, 1.021, 3.278, 1, 3.722, 1.021]}, {"time": 4.1667, "x": 1.021, "y": 1.021, "curve": [4.611, 1.021, 5.056, 1, 4.611, 1.021, 5.056, 1]}, {"time": 5.5, "curve": [5.833, 1, 6.167, 1.021, 5.833, 1, 6.167, 1.021]}, {"time": 6.5, "x": 1.021, "y": 1.021, "curve": [6.944, 1.021, 7.389, 1.01, 6.944, 1.021, 7.389, 1.01]}, {"time": 7.8333, "x": 1.01, "y": 1.01, "curve": [8.278, 1.01, 8.722, 1.021, 8.278, 1.01, 8.722, 1.021]}, {"time": 9.1667, "x": 1.021, "y": 1.021, "curve": [9.611, 1.021, 10.056, 1, 9.611, 1.021, 10.056, 1]}, {"time": 10.5, "curve": [10.944, 1, 11.389, 1.021, 10.944, 1, 11.389, 1.021]}, {"time": 11.8333, "x": 1.021, "y": 1.021, "curve": [12.223, 1.021, 12.613, 1.005, 12.223, 1.021, 12.613, 1.005]}, {"time": 13, "x": 1.001, "y": 1.001}]}, "neck": {"rotate": [{"value": 0.62, "curve": [0.114, 0.88, 0.224, 1.07]}, {"time": 0.3333, "value": 1.07, "curve": [0.778, 1.07, 1.222, -1.72]}, {"time": 1.6667, "value": -1.72, "curve": [2.111, -1.72, 2.556, 1.07]}, {"time": 3, "value": 1.07, "curve": [3.444, 1.07, 3.889, -1.72]}, {"time": 4.3333, "value": -1.72, "curve": [4.778, -1.72, 5.222, 1.07]}, {"time": 5.6667, "value": 1.07, "curve": [6, 1.07, 6.333, -5.98]}, {"time": 6.6667, "value": -5.98, "curve": [7.111, -5.98, 7.556, -2.46]}, {"time": 8, "value": -2.46, "curve": [8.444, -2.46, 8.889, -5.98]}, {"time": 9.3333, "value": -5.98, "curve": [9.778, -5.98, 10.222, 1.07]}, {"time": 10.6667, "value": 1.07, "curve": [11.111, 1.07, 11.556, -1.72]}, {"time": 12, "value": -1.72, "curve": [12.335, -1.72, 12.67, -0.16]}, {"time": 13, "value": 0.62}]}, "head": {"rotate": [{"value": -0.29, "curve": [0.168, 0.46, 0.334, 1.07]}, {"time": 0.5, "value": 1.07, "curve": [0.944, 1.07, 1.389, -3.19]}, {"time": 1.8333, "value": -3.19, "curve": [2.278, -3.19, 2.722, 1.07]}, {"time": 3.1667, "value": 1.07, "curve": [3.611, 1.07, 4.056, -3.19]}, {"time": 4.5, "value": -3.19, "curve": [4.944, -3.19, 5.389, 1.07]}, {"time": 5.8333, "value": 1.07, "curve": [6.167, 1.07, 6.5, -7.45]}, {"time": 6.8333, "value": -7.45, "curve": [7.278, -7.45, 7.722, -3.19]}, {"time": 8.1667, "value": -3.19, "curve": [8.611, -3.19, 9.056, -7.45]}, {"time": 9.5, "value": -7.45, "curve": [9.944, -7.45, 10.389, 1.07]}, {"time": 10.8333, "value": 1.07, "curve": [11.278, 1.07, 11.722, -3.19]}, {"time": 12.1667, "value": -3.19, "curve": [12.445, -3.19, 12.724, -1.53]}, {"time": 13, "value": -0.29}]}, "eyebrow_L": {"translate": [{"time": 6, "curve": [6.111, 0, 6.222, 1.48, 6.111, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.48, "curve": [6.778, 1.48, 9.111, 0, 6.778, 0, 9.111, 0]}, {"time": 9.8333}]}, "eyebrow_L2": {"rotate": [{"curve": [0.444, 0, 5.333, -0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, -4.71]}, {"time": 6.3333, "value": -4.71, "curve": "stepped"}, {"time": 9.8333, "value": -4.71, "curve": [10.056, -4.71, 10.278, 0]}, {"time": 10.5}], "scale": [{"curve": [0.444, 1, 5.333, 1, 0.444, 1, 5.333, 1]}, {"time": 6, "curve": [6.111, 1, 6.222, 0.963, 6.111, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.963, "curve": "stepped"}, {"time": 9.8333, "x": 0.963, "curve": [10.056, 0.963, 10.278, 1, 10.056, 1, 10.278, 1]}, {"time": 10.5}]}, "eyebrow_L3": {"rotate": [{"time": 6, "curve": [6.111, 0, 6.222, -2.56]}, {"time": 6.3333, "value": -2.56, "curve": [6.778, -2.56, 9.111, 0]}, {"time": 9.8333}]}, "eyebrow_R": {"translate": [{"time": 6, "curve": [6.111, 0, 6.222, 1.48, 6.111, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.48, "curve": [6.778, 1.48, 9.111, 0, 6.778, 0, 9.111, 0]}, {"time": 9.8333}]}, "eyebrow_R2": {"rotate": [{"curve": [0.444, 0, 5.333, 0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, 5.08]}, {"time": 6.3333, "value": 5.08, "curve": "stepped"}, {"time": 9.8333, "value": 5.08, "curve": [10.056, 5.08, 10.278, 0]}, {"time": 10.5}], "scale": [{"curve": [0.444, 1, 5.333, 1, 0.444, 1, 5.333, 1]}, {"time": 6, "curve": [6.111, 1, 6.222, 0.963, 6.111, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.963, "curve": "stepped"}, {"time": 9.8333, "x": 0.963, "curve": [10.056, 0.963, 10.278, 1, 10.056, 1, 10.278, 1]}, {"time": 10.5}]}, "eyebrow_R3": {"rotate": [{"time": 6, "curve": [6.111, 0, 6.222, 4.82]}, {"time": 6.3333, "value": 4.82, "curve": [6.778, 4.82, 9.111, 0]}, {"time": 9.8333}]}, "earring_L": {"rotate": [{"value": 5.66, "curve": [0.222, 5.66, 0.444, -5.07]}, {"time": 0.6667, "value": -5.07, "curve": [0.889, -5.07, 1.111, 5.66]}, {"time": 1.3333, "value": 5.66, "curve": [1.556, 5.66, 1.778, -5.07]}, {"time": 2, "value": -5.07, "curve": [2.222, -5.07, 2.444, 5.66]}, {"time": 2.6667, "value": 5.66, "curve": [2.889, 5.66, 3.111, -5.07]}, {"time": 3.3333, "value": -5.07, "curve": [3.556, -5.07, 3.778, 5.66]}, {"time": 4, "value": 5.66, "curve": [4.222, 5.66, 4.444, -5.07]}, {"time": 4.6667, "value": -5.07, "curve": [4.889, -5.07, 5.111, 5.66]}, {"time": 5.3333, "value": 5.66, "curve": [5.556, 5.66, 5.778, -5.07]}, {"time": 6, "value": -5.07, "curve": [6.167, -5.07, 6.333, 5.66]}, {"time": 6.5, "value": 5.66, "curve": [6.667, 5.66, 6.833, -5.07]}, {"time": 7, "value": -5.07, "curve": [7.222, -5.07, 7.444, 5.66]}, {"time": 7.6667, "value": 5.66, "curve": [7.889, 5.66, 8.111, -5.07]}, {"time": 8.3333, "value": -5.07, "curve": [8.556, -5.07, 8.778, 5.66]}, {"time": 9, "value": 5.66, "curve": [9.222, 5.66, 9.444, -5.07]}, {"time": 9.6667, "value": -5.07, "curve": [9.889, -5.07, 10.111, 5.66]}, {"time": 10.3333, "value": 5.66, "curve": [10.556, 5.66, 10.778, -5.07]}, {"time": 11, "value": -5.07, "curve": [11.222, -5.07, 11.444, 5.66]}, {"time": 11.6667, "value": 5.66, "curve": [11.889, 5.66, 12.111, -5.07]}, {"time": 12.3333, "value": -5.07, "curve": [12.556, -5.07, 12.778, 5.66]}, {"time": 13, "value": 5.66}]}, "earring_R": {"rotate": [{"value": 5.66, "curve": [0.222, 5.66, 0.444, -5.07]}, {"time": 0.6667, "value": -5.07, "curve": [0.889, -5.07, 1.111, 5.66]}, {"time": 1.3333, "value": 5.66, "curve": [1.556, 5.66, 1.778, -5.07]}, {"time": 2, "value": -5.07, "curve": [2.222, -5.07, 2.444, 5.66]}, {"time": 2.6667, "value": 5.66, "curve": [2.889, 5.66, 3.111, -5.07]}, {"time": 3.3333, "value": -5.07, "curve": [3.556, -5.07, 3.778, 5.66]}, {"time": 4, "value": 5.66, "curve": [4.222, 5.66, 4.444, -5.07]}, {"time": 4.6667, "value": -5.07, "curve": [4.889, -5.07, 5.111, 5.66]}, {"time": 5.3333, "value": 5.66, "curve": [5.556, 5.66, 5.778, -5.07]}, {"time": 6, "value": -5.07, "curve": [6.167, -5.07, 6.333, 5.66]}, {"time": 6.5, "value": 5.66, "curve": [6.667, 5.66, 6.833, -5.07]}, {"time": 7, "value": -5.07, "curve": [7.222, -5.07, 7.444, 5.66]}, {"time": 7.6667, "value": 5.66, "curve": [7.889, 5.66, 8.111, -5.07]}, {"time": 8.3333, "value": -5.07, "curve": [8.556, -5.07, 8.778, 5.66]}, {"time": 9, "value": 5.66, "curve": [9.222, 5.66, 9.444, -5.07]}, {"time": 9.6667, "value": -5.07, "curve": [9.889, -5.07, 10.111, 5.66]}, {"time": 10.3333, "value": 5.66, "curve": [10.556, 5.66, 10.778, -5.07]}, {"time": 11, "value": -5.07, "curve": [11.222, -5.07, 11.444, 5.66]}, {"time": 11.6667, "value": 5.66, "curve": [11.889, 5.66, 12.111, -5.07]}, {"time": 12.3333, "value": -5.07, "curve": [12.556, -5.07, 12.778, 5.66]}, {"time": 13, "value": 5.66}]}, "sh_L": {"translate": [{"x": -4.48, "curve": [0.057, -4.79, 0.112, -5.03, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -5.03, "curve": [0.611, -5.03, 1.056, 6.63, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 6.63, "curve": [1.944, 6.63, 2.389, -5.03, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -5.03, "curve": [3.278, -5.03, 3.722, 6.63, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 6.63, "curve": [4.611, 6.63, 5.056, -5.03, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -5.03, "curve": [5.833, -5.03, 6.167, 6.63, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 6.63, "curve": [6.944, 6.63, 7.389, -5.03, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -5.03, "curve": [8.278, -5.03, 8.722, 6.63, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 6.63, "curve": [9.611, 6.63, 10.056, -5.03, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -5.03, "curve": [10.944, -5.03, 11.389, 6.63, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 6.63, "curve": [12.223, 6.63, 12.613, -2.27, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -4.48}]}, "sh_R": {"translate": [{"x": -4.48, "curve": [0.057, -4.79, 0.112, -5.03, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -5.03, "curve": [0.611, -5.03, 1.056, 6.63, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 6.63, "curve": [1.944, 6.63, 2.389, -5.03, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -5.03, "curve": [3.278, -5.03, 3.722, 6.63, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 6.63, "curve": [4.611, 6.63, 5.056, -5.03, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -5.03, "curve": [5.833, -5.03, 6.167, 6.63, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 6.63, "curve": [6.944, 6.63, 7.389, -5.03, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -5.03, "curve": [8.278, -5.03, 8.722, 6.63, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 6.63, "curve": [9.611, 6.63, 10.056, -5.03, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -5.03, "curve": [10.944, -5.03, 11.389, 6.63, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 6.63, "curve": [12.223, 6.63, 12.613, -2.27, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -4.48}]}, "RU_L": {"translate": [{"x": -24.19, "curve": [0.114, -30.75, 0.224, -35.64, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -35.64, "curve": [0.778, -35.64, 1.222, 35.91, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 35.91, "curve": [2.111, 35.91, 2.556, -35.64, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -35.64, "curve": [3.444, -35.64, 3.889, 35.91, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 35.91, "curve": [4.778, 35.91, 5.222, -35.64, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -35.64, "curve": [6, -35.64, 6.333, 35.91, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 35.91, "curve": [7.111, 35.91, 7.556, -35.64, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -35.64, "curve": [8.444, -35.64, 8.889, 35.91, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 35.91, "curve": [9.778, 35.91, 10.222, -35.64, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -35.64, "curve": [11.111, -35.64, 11.556, 35.91, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 35.91, "curve": [12.335, 35.91, 12.67, -4.16, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -24.19}], "scale": [{"x": 1.007, "y": 0.997, "curve": [0.111, 0.979, 0.222, 0.952, 0.111, 1.026, 0.222, 1.055]}, {"time": 0.3333, "x": 0.952, "y": 1.055, "curve": [0.556, 0.952, 0.778, 1.062, 0.556, 1.055, 0.778, 0.938]}, {"time": 1, "x": 1.062, "y": 0.938, "curve": [1.222, 1.062, 1.444, 0.952, 1.222, 0.938, 1.444, 1.055]}, {"time": 1.6667, "x": 0.952, "y": 1.055, "curve": [1.889, 0.952, 2.111, 1.062, 1.889, 1.055, 2.111, 0.938]}, {"time": 2.3333, "x": 1.062, "y": 0.938, "curve": [2.556, 1.062, 2.778, 0.952, 2.556, 0.938, 2.778, 1.055]}, {"time": 3, "x": 0.952, "y": 1.055, "curve": [3.222, 0.952, 3.444, 1.062, 3.222, 1.055, 3.444, 0.938]}, {"time": 3.6667, "x": 1.062, "y": 0.938, "curve": [3.889, 1.062, 4.111, 0.952, 3.889, 0.938, 4.111, 1.055]}, {"time": 4.3333, "x": 0.952, "y": 1.055, "curve": [4.556, 0.952, 4.778, 1.062, 4.556, 1.055, 4.778, 0.938]}, {"time": 5, "x": 1.062, "y": 0.938, "curve": [5.222, 1.062, 5.444, 0.952, 5.222, 0.938, 5.444, 1.055]}, {"time": 5.6667, "x": 0.952, "y": 1.055, "curve": [5.833, 0.952, 6, 1.062, 5.833, 1.055, 6, 0.938]}, {"time": 6.1667, "x": 1.062, "y": 0.938, "curve": [6.333, 1.062, 6.5, 0.952, 6.333, 0.938, 6.5, 1.055]}, {"time": 6.6667, "x": 0.952, "y": 1.055, "curve": [6.889, 0.952, 7.111, 1.062, 6.889, 1.055, 7.111, 0.938]}, {"time": 7.3333, "x": 1.062, "y": 0.938, "curve": [7.556, 1.062, 7.778, 0.952, 7.556, 0.938, 7.778, 1.055]}, {"time": 8, "x": 0.952, "y": 1.055, "curve": [8.222, 0.952, 8.444, 1.062, 8.222, 1.055, 8.444, 0.938]}, {"time": 8.6667, "x": 1.062, "y": 0.938, "curve": [8.889, 1.062, 9.111, 0.952, 8.889, 0.938, 9.111, 1.055]}, {"time": 9.3333, "x": 0.952, "y": 1.055, "curve": [9.556, 0.952, 9.778, 1.062, 9.556, 1.055, 9.778, 0.938]}, {"time": 10, "x": 1.062, "y": 0.938, "curve": [10.222, 1.062, 10.444, 0.952, 10.222, 0.938, 10.444, 1.055]}, {"time": 10.6667, "x": 0.952, "y": 1.055, "curve": [10.889, 0.952, 11.111, 1.062, 10.889, 1.055, 11.111, 0.938]}, {"time": 11.3333, "x": 1.062, "y": 0.938, "curve": [11.556, 1.062, 11.778, 0.952, 11.556, 0.938, 11.778, 1.055]}, {"time": 12, "x": 0.952, "y": 1.055, "curve": [12.222, 0.952, 12.444, 1.062, 12.222, 1.055, 12.444, 0.938]}, {"time": 12.6667, "x": 1.062, "y": 0.938, "curve": [12.778, 1.062, 12.889, 1.034, 12.778, 0.938, 12.889, 0.967]}, {"time": 13, "x": 1.007, "y": 0.997}]}, "RU_L2": {"translate": [{"x": -13.4, "curve": [0.168, -24.19, 0.334, -33.04, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -33.04, "curve": [0.944, -33.04, 1.389, 28.72, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 28.72, "curve": [2.278, 28.72, 2.722, -33.04, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -33.04, "curve": [3.611, -33.04, 4.056, 28.72, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 28.72, "curve": [4.944, 28.72, 5.389, -33.04, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -33.04, "curve": [6.167, -33.04, 6.5, 28.72, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 28.72, "curve": [7.278, 28.72, 7.722, -33.04, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -33.04, "curve": [8.611, -33.04, 9.056, 28.72, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 28.72, "curve": [9.944, 28.72, 10.389, -33.04, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -33.04, "curve": [11.278, -33.04, 11.722, 28.72, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 28.72, "curve": [12.445, 28.72, 12.724, 4.71, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -13.4}], "scale": [{"x": 1.044, "y": 0.957, "curve": [0.167, 1.013, 0.333, 0.952, 0.167, 0.99, 0.333, 1.055]}, {"time": 0.5, "x": 0.952, "y": 1.055, "curve": [0.722, 0.952, 0.944, 1.062, 0.722, 1.055, 0.944, 0.938]}, {"time": 1.1667, "x": 1.062, "y": 0.938, "curve": [1.389, 1.062, 1.611, 0.952, 1.389, 0.938, 1.611, 1.055]}, {"time": 1.8333, "x": 0.952, "y": 1.055, "curve": [2.056, 0.952, 2.278, 1.062, 2.056, 1.055, 2.278, 0.938]}, {"time": 2.5, "x": 1.062, "y": 0.938, "curve": [2.722, 1.062, 2.944, 0.952, 2.722, 0.938, 2.944, 1.055]}, {"time": 3.1667, "x": 0.952, "y": 1.055, "curve": [3.389, 0.952, 3.611, 1.062, 3.389, 1.055, 3.611, 0.938]}, {"time": 3.8333, "x": 1.062, "y": 0.938, "curve": [4.056, 1.062, 4.278, 0.952, 4.056, 0.938, 4.278, 1.055]}, {"time": 4.5, "x": 0.952, "y": 1.055, "curve": [4.722, 0.952, 4.944, 1.062, 4.722, 1.055, 4.944, 0.938]}, {"time": 5.1667, "x": 1.062, "y": 0.938, "curve": [5.389, 1.062, 5.611, 0.952, 5.389, 0.938, 5.611, 1.055]}, {"time": 5.8333, "x": 0.952, "y": 1.055, "curve": [6, 0.952, 6.167, 1.062, 6, 1.055, 6.167, 0.938]}, {"time": 6.3333, "x": 1.062, "y": 0.938, "curve": [6.5, 1.062, 6.667, 0.952, 6.5, 0.938, 6.667, 1.055]}, {"time": 6.8333, "x": 0.952, "y": 1.055, "curve": [7.056, 0.952, 7.278, 1.062, 7.056, 1.055, 7.278, 0.938]}, {"time": 7.5, "x": 1.062, "y": 0.938, "curve": [7.722, 1.062, 7.944, 0.952, 7.722, 0.938, 7.944, 1.055]}, {"time": 8.1667, "x": 0.952, "y": 1.055, "curve": [8.389, 0.952, 8.611, 1.062, 8.389, 1.055, 8.611, 0.938]}, {"time": 8.8333, "x": 1.062, "y": 0.938, "curve": [9.056, 1.062, 9.278, 0.952, 9.056, 0.938, 9.278, 1.055]}, {"time": 9.5, "x": 0.952, "y": 1.055, "curve": [9.722, 0.952, 9.944, 1.062, 9.722, 1.055, 9.944, 0.938]}, {"time": 10.1667, "x": 1.062, "y": 0.938, "curve": [10.389, 1.062, 10.611, 0.952, 10.389, 0.938, 10.611, 1.055]}, {"time": 10.8333, "x": 0.952, "y": 1.055, "curve": [11.056, 0.952, 11.278, 1.062, 11.056, 1.055, 11.278, 0.938]}, {"time": 11.5, "x": 1.062, "y": 0.938, "curve": [11.722, 1.062, 11.944, 0.952, 11.722, 0.938, 11.944, 1.055]}, {"time": 12.1667, "x": 0.952, "y": 1.055, "curve": [12.389, 0.952, 12.611, 1.062, 12.389, 1.055, 12.611, 0.938]}, {"time": 12.8333, "x": 1.062, "y": 0.938, "curve": [12.889, 1.062, 12.944, 1.054, 12.889, 0.938, 12.944, 0.946]}, {"time": 13, "x": 1.044, "y": 0.957}]}, "RU_L3": {"translate": [{"x": -2.48, "curve": [0.225, -16.58, 0.446, -30.86, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -30.86, "curve": [1.111, -30.86, 1.556, 25.9, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 25.9, "curve": [2.444, 25.9, 2.889, -30.86, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -30.86, "curve": [3.778, -30.86, 4.222, 25.9, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 25.9, "curve": [5.111, 25.9, 5.556, -30.86, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -30.86, "curve": [6.333, -30.86, 6.667, 25.9, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 25.9, "curve": [7.444, 25.9, 7.889, -30.86, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -30.86, "curve": [8.778, -30.86, 9.222, 25.9, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 25.9, "curve": [10.111, 25.9, 10.556, -30.86, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -30.86, "curve": [11.444, -30.86, 11.889, 25.9, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 25.9, "curve": [12.557, 25.9, 12.781, 11.8, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -2.48}], "scale": [{"x": 1.062, "y": 0.938, "curve": [0.222, 1.062, 0.444, 0.952, 0.222, 0.938, 0.444, 1.055]}, {"time": 0.6667, "x": 0.952, "y": 1.055, "curve": [0.889, 0.952, 1.111, 1.062, 0.889, 1.055, 1.111, 0.938]}, {"time": 1.3333, "x": 1.062, "y": 0.938, "curve": [1.556, 1.062, 1.778, 0.952, 1.556, 0.938, 1.778, 1.055]}, {"time": 2, "x": 0.952, "y": 1.055, "curve": [2.222, 0.952, 2.444, 1.062, 2.222, 1.055, 2.444, 0.938]}, {"time": 2.6667, "x": 1.062, "y": 0.938, "curve": [2.889, 1.062, 3.111, 0.952, 2.889, 0.938, 3.111, 1.055]}, {"time": 3.3333, "x": 0.952, "y": 1.055, "curve": [3.556, 0.952, 3.778, 1.062, 3.556, 1.055, 3.778, 0.938]}, {"time": 4, "x": 1.062, "y": 0.938, "curve": [4.222, 1.062, 4.444, 0.952, 4.222, 0.938, 4.444, 1.055]}, {"time": 4.6667, "x": 0.952, "y": 1.055, "curve": [4.889, 0.952, 5.111, 1.062, 4.889, 1.055, 5.111, 0.938]}, {"time": 5.3333, "x": 1.062, "y": 0.938, "curve": [5.556, 1.062, 5.778, 0.952, 5.556, 0.938, 5.778, 1.055]}, {"time": 6, "x": 0.952, "y": 1.055, "curve": [6.167, 0.952, 6.333, 1.062, 6.167, 1.055, 6.333, 0.938]}, {"time": 6.5, "x": 1.062, "y": 0.938, "curve": [6.667, 1.062, 6.833, 0.952, 6.667, 0.938, 6.833, 1.055]}, {"time": 7, "x": 0.952, "y": 1.055, "curve": [7.222, 0.952, 7.444, 1.062, 7.222, 1.055, 7.444, 0.938]}, {"time": 7.6667, "x": 1.062, "y": 0.938, "curve": [7.889, 1.062, 8.111, 0.952, 7.889, 0.938, 8.111, 1.055]}, {"time": 8.3333, "x": 0.952, "y": 1.055, "curve": [8.556, 0.952, 8.778, 1.062, 8.556, 1.055, 8.778, 0.938]}, {"time": 9, "x": 1.062, "y": 0.938, "curve": [9.222, 1.062, 9.444, 0.952, 9.222, 0.938, 9.444, 1.055]}, {"time": 9.6667, "x": 0.952, "y": 1.055, "curve": [9.889, 0.952, 10.111, 1.062, 9.889, 1.055, 10.111, 0.938]}, {"time": 10.3333, "x": 1.062, "y": 0.938, "curve": [10.556, 1.062, 10.778, 0.952, 10.556, 0.938, 10.778, 1.055]}, {"time": 11, "x": 0.952, "y": 1.055, "curve": [11.222, 0.952, 11.444, 1.062, 11.222, 1.055, 11.444, 0.938]}, {"time": 11.6667, "x": 1.062, "y": 0.938, "curve": [11.889, 1.062, 12.111, 0.952, 11.889, 0.938, 12.111, 1.055]}, {"time": 12.3333, "x": 0.952, "y": 1.055, "curve": [12.556, 0.952, 12.778, 1.062, 12.556, 1.055, 12.778, 0.938]}, {"time": 13, "x": 1.062, "y": 0.938}]}, "RU_R": {"translate": [{"x": -27.64, "curve": [0.444, -27.64, 0.889, 33.71, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 33.71, "curve": [1.778, 33.71, 2.222, -27.64, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -27.64, "curve": [3.111, -27.64, 3.556, 33.71, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 33.71, "curve": [4.444, 33.71, 4.889, -27.64, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -27.64, "curve": [5.667, -27.64, 6, 33.71, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 33.71, "curve": [6.778, 33.71, 7.222, -27.64, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -27.64, "curve": [8.111, -27.64, 8.556, 33.71, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 33.71, "curve": [9.444, 33.71, 9.889, -27.64, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -27.64, "curve": [10.778, -27.64, 11.222, 33.71, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 33.71, "curve": [12.111, 33.71, 12.556, -27.64, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -27.64}], "scale": [{"x": 0.952, "y": 1.055, "curve": [0.222, 0.952, 0.444, 1.062, 0.222, 1.055, 0.444, 0.938]}, {"time": 0.6667, "x": 1.062, "y": 0.938, "curve": [0.889, 1.062, 1.111, 0.952, 0.889, 0.938, 1.111, 1.055]}, {"time": 1.3333, "x": 0.952, "y": 1.055, "curve": [1.556, 0.952, 1.778, 1.062, 1.556, 1.055, 1.778, 0.938]}, {"time": 2, "x": 1.062, "y": 0.938, "curve": [2.222, 1.062, 2.444, 0.952, 2.222, 0.938, 2.444, 1.055]}, {"time": 2.6667, "x": 0.952, "y": 1.055, "curve": [2.889, 0.952, 3.111, 1.062, 2.889, 1.055, 3.111, 0.938]}, {"time": 3.3333, "x": 1.062, "y": 0.938, "curve": [3.556, 1.062, 3.778, 0.952, 3.556, 0.938, 3.778, 1.055]}, {"time": 4, "x": 0.952, "y": 1.055, "curve": [4.222, 0.952, 4.444, 1.062, 4.222, 1.055, 4.444, 0.938]}, {"time": 4.6667, "x": 1.062, "y": 0.938, "curve": [4.889, 1.062, 5.111, 0.952, 4.889, 0.938, 5.111, 1.055]}, {"time": 5.3333, "x": 0.952, "y": 1.055, "curve": [5.5, 0.952, 5.667, 1.062, 5.5, 1.055, 5.667, 0.938]}, {"time": 5.8333, "x": 1.062, "y": 0.938, "curve": [6, 1.062, 6.167, 0.952, 6, 0.938, 6.167, 1.055]}, {"time": 6.3333, "x": 0.952, "y": 1.055, "curve": [6.556, 0.952, 6.778, 1.062, 6.556, 1.055, 6.778, 0.938]}, {"time": 7, "x": 1.062, "y": 0.938, "curve": [7.222, 1.062, 7.444, 0.952, 7.222, 0.938, 7.444, 1.055]}, {"time": 7.6667, "x": 0.952, "y": 1.055, "curve": [7.889, 0.952, 8.111, 1.062, 7.889, 1.055, 8.111, 0.938]}, {"time": 8.3333, "x": 1.062, "y": 0.938, "curve": [8.556, 1.062, 8.778, 0.952, 8.556, 0.938, 8.778, 1.055]}, {"time": 9, "x": 0.952, "y": 1.055, "curve": [9.222, 0.952, 9.444, 1.062, 9.222, 1.055, 9.444, 0.938]}, {"time": 9.6667, "x": 1.062, "y": 0.938, "curve": [9.889, 1.062, 10.111, 0.952, 9.889, 0.938, 10.111, 1.055]}, {"time": 10.3333, "x": 0.952, "y": 1.055, "curve": [10.556, 0.952, 10.778, 1.062, 10.556, 1.055, 10.778, 0.938]}, {"time": 11, "x": 1.062, "y": 0.938, "curve": [11.222, 1.062, 11.444, 0.952, 11.222, 0.938, 11.444, 1.055]}, {"time": 11.6667, "x": 0.952, "y": 1.055, "curve": [11.889, 0.952, 12.111, 1.062, 11.889, 1.055, 12.111, 0.938]}, {"time": 12.3333, "x": 1.062, "y": 0.938, "curve": [12.556, 1.062, 12.778, 0.952, 12.556, 0.938, 12.778, 1.055]}, {"time": 13, "x": 0.952, "y": 1.055}]}, "RU_R2": {"translate": [{"x": -10.24, "curve": [0.168, -19.45, 0.334, -27.01, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -27.01, "curve": [0.944, -27.01, 1.389, 25.72, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 25.72, "curve": [2.278, 25.72, 2.722, -27.01, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -27.01, "curve": [3.611, -27.01, 4.056, 25.72, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 25.72, "curve": [4.944, 25.72, 5.389, -27.01, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -27.01, "curve": [6.167, -27.01, 6.5, 25.72, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 25.72, "curve": [7.278, 25.72, 7.722, -27.01, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -27.01, "curve": [8.611, -27.01, 9.056, 25.72, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 25.72, "curve": [9.944, 25.72, 10.389, -27.01, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -27.01, "curve": [11.278, -27.01, 11.722, 25.72, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 25.72, "curve": [12.445, 25.72, 12.724, 5.22, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -10.24}], "scale": [{"x": 1.044, "y": 0.957, "curve": [0.167, 1.013, 0.333, 0.952, 0.167, 0.99, 0.333, 1.055]}, {"time": 0.5, "x": 0.952, "y": 1.055, "curve": [0.722, 0.952, 0.944, 1.062, 0.722, 1.055, 0.944, 0.938]}, {"time": 1.1667, "x": 1.062, "y": 0.938, "curve": [1.389, 1.062, 1.611, 0.952, 1.389, 0.938, 1.611, 1.055]}, {"time": 1.8333, "x": 0.952, "y": 1.055, "curve": [2.056, 0.952, 2.278, 1.062, 2.056, 1.055, 2.278, 0.938]}, {"time": 2.5, "x": 1.062, "y": 0.938, "curve": [2.722, 1.062, 2.944, 0.952, 2.722, 0.938, 2.944, 1.055]}, {"time": 3.1667, "x": 0.952, "y": 1.055, "curve": [3.389, 0.952, 3.611, 1.062, 3.389, 1.055, 3.611, 0.938]}, {"time": 3.8333, "x": 1.062, "y": 0.938, "curve": [4.056, 1.062, 4.278, 0.952, 4.056, 0.938, 4.278, 1.055]}, {"time": 4.5, "x": 0.952, "y": 1.055, "curve": [4.722, 0.952, 4.944, 1.062, 4.722, 1.055, 4.944, 0.938]}, {"time": 5.1667, "x": 1.062, "y": 0.938, "curve": [5.389, 1.062, 5.611, 0.952, 5.389, 0.938, 5.611, 1.055]}, {"time": 5.8333, "x": 0.952, "y": 1.055, "curve": [6, 0.952, 6.167, 1.062, 6, 1.055, 6.167, 0.938]}, {"time": 6.3333, "x": 1.062, "y": 0.938, "curve": [6.5, 1.062, 6.667, 0.952, 6.5, 0.938, 6.667, 1.055]}, {"time": 6.8333, "x": 0.952, "y": 1.055, "curve": [7.056, 0.952, 7.278, 1.062, 7.056, 1.055, 7.278, 0.938]}, {"time": 7.5, "x": 1.062, "y": 0.938, "curve": [7.722, 1.062, 7.944, 0.952, 7.722, 0.938, 7.944, 1.055]}, {"time": 8.1667, "x": 0.952, "y": 1.055, "curve": [8.389, 0.952, 8.611, 1.062, 8.389, 1.055, 8.611, 0.938]}, {"time": 8.8333, "x": 1.062, "y": 0.938, "curve": [9.056, 1.062, 9.278, 0.952, 9.056, 0.938, 9.278, 1.055]}, {"time": 9.5, "x": 0.952, "y": 1.055, "curve": [9.722, 0.952, 9.944, 1.062, 9.722, 1.055, 9.944, 0.938]}, {"time": 10.1667, "x": 1.062, "y": 0.938, "curve": [10.389, 1.062, 10.611, 0.952, 10.389, 0.938, 10.611, 1.055]}, {"time": 10.8333, "x": 0.952, "y": 1.055, "curve": [11.056, 0.952, 11.278, 1.062, 11.056, 1.055, 11.278, 0.938]}, {"time": 11.5, "x": 1.062, "y": 0.938, "curve": [11.722, 1.062, 11.944, 0.952, 11.722, 0.938, 11.944, 1.055]}, {"time": 12.1667, "x": 0.952, "y": 1.055, "curve": [12.389, 0.952, 12.611, 1.062, 12.389, 1.055, 12.611, 0.938]}, {"time": 12.8333, "x": 1.062, "y": 0.938, "curve": [12.889, 1.062, 12.944, 1.054, 12.889, 0.938, 12.944, 0.946]}, {"time": 13, "x": 1.044, "y": 0.957}]}, "RU_R3": {"translate": [{"x": -1.42, "curve": [0.225, -13.03, 0.446, -24.79, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -24.79, "curve": [1.111, -24.79, 1.556, 21.94, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 21.94, "curve": [2.444, 21.94, 2.889, -24.79, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -24.79, "curve": [3.778, -24.79, 4.222, 21.94, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 21.94, "curve": [5.111, 21.94, 5.556, -24.79, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -24.79, "curve": [6.333, -24.79, 6.667, 21.94, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 21.94, "curve": [7.444, 21.94, 7.889, -24.79, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -24.79, "curve": [8.778, -24.79, 9.222, 21.94, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 21.94, "curve": [10.111, 21.94, 10.556, -24.79, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -24.79, "curve": [11.444, -24.79, 11.889, 21.94, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 21.94, "curve": [12.557, 21.94, 12.781, 10.34, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -1.42}], "scale": [{"x": 1.062, "y": 0.938, "curve": [0.222, 1.062, 0.444, 0.952, 0.222, 0.938, 0.444, 1.055]}, {"time": 0.6667, "x": 0.952, "y": 1.055, "curve": [0.889, 0.952, 1.111, 1.062, 0.889, 1.055, 1.111, 0.938]}, {"time": 1.3333, "x": 1.062, "y": 0.938, "curve": [1.556, 1.062, 1.778, 0.952, 1.556, 0.938, 1.778, 1.055]}, {"time": 2, "x": 0.952, "y": 1.055, "curve": [2.222, 0.952, 2.444, 1.062, 2.222, 1.055, 2.444, 0.938]}, {"time": 2.6667, "x": 1.062, "y": 0.938, "curve": [2.889, 1.062, 3.111, 0.952, 2.889, 0.938, 3.111, 1.055]}, {"time": 3.3333, "x": 0.952, "y": 1.055, "curve": [3.556, 0.952, 3.778, 1.062, 3.556, 1.055, 3.778, 0.938]}, {"time": 4, "x": 1.062, "y": 0.938, "curve": [4.222, 1.062, 4.444, 0.952, 4.222, 0.938, 4.444, 1.055]}, {"time": 4.6667, "x": 0.952, "y": 1.055, "curve": [4.889, 0.952, 5.111, 1.062, 4.889, 1.055, 5.111, 0.938]}, {"time": 5.3333, "x": 1.062, "y": 0.938, "curve": [5.556, 1.062, 5.778, 0.952, 5.556, 0.938, 5.778, 1.055]}, {"time": 6, "x": 0.952, "y": 1.055, "curve": [6.167, 0.952, 6.333, 1.062, 6.167, 1.055, 6.333, 0.938]}, {"time": 6.5, "x": 1.062, "y": 0.938, "curve": [6.667, 1.062, 6.833, 0.952, 6.667, 0.938, 6.833, 1.055]}, {"time": 7, "x": 0.952, "y": 1.055, "curve": [7.222, 0.952, 7.444, 1.062, 7.222, 1.055, 7.444, 0.938]}, {"time": 7.6667, "x": 1.062, "y": 0.938, "curve": [7.889, 1.062, 8.111, 0.952, 7.889, 0.938, 8.111, 1.055]}, {"time": 8.3333, "x": 0.952, "y": 1.055, "curve": [8.556, 0.952, 8.778, 1.062, 8.556, 1.055, 8.778, 0.938]}, {"time": 9, "x": 1.062, "y": 0.938, "curve": [9.222, 1.062, 9.444, 0.952, 9.222, 0.938, 9.444, 1.055]}, {"time": 9.6667, "x": 0.952, "y": 1.055, "curve": [9.889, 0.952, 10.111, 1.062, 9.889, 1.055, 10.111, 0.938]}, {"time": 10.3333, "x": 1.062, "y": 0.938, "curve": [10.556, 1.062, 10.778, 0.952, 10.556, 0.938, 10.778, 1.055]}, {"time": 11, "x": 0.952, "y": 1.055, "curve": [11.222, 0.952, 11.444, 1.062, 11.222, 1.055, 11.444, 0.938]}, {"time": 11.6667, "x": 1.062, "y": 0.938, "curve": [11.889, 1.062, 12.111, 0.952, 11.889, 0.938, 12.111, 1.055]}, {"time": 12.3333, "x": 0.952, "y": 1.055, "curve": [12.556, 0.952, 12.778, 1.062, 12.556, 1.055, 12.778, 0.938]}, {"time": 13, "x": 1.062, "y": 0.938}]}, "arm_L": {"rotate": [{"value": 2.05, "curve": [0.114, 2.43, 0.224, 2.72]}, {"time": 0.3333, "value": 2.72, "curve": [0.778, 2.72, 1.222, -1.5]}, {"time": 1.6667, "value": -1.5, "curve": [2.111, -1.5, 2.556, 2.72]}, {"time": 3, "value": 2.72, "curve": [3.444, 2.72, 3.889, -1.5]}, {"time": 4.3333, "value": -1.5, "curve": [4.778, -1.5, 5.222, 2.72]}, {"time": 5.6667, "value": 2.72, "curve": [6, 2.72, 6.333, -1.5]}, {"time": 6.6667, "value": -1.5, "curve": [7.111, -1.5, 7.556, 2.72]}, {"time": 8, "value": 2.72, "curve": [8.444, 2.72, 8.889, -1.5]}, {"time": 9.3333, "value": -1.5, "curve": [9.778, -1.5, 10.222, 2.72]}, {"time": 10.6667, "value": 2.72, "curve": [11.111, 2.72, 11.556, -1.5]}, {"time": 12, "value": -1.5, "curve": [12.335, -1.5, 12.67, 0.86]}, {"time": 13, "value": 2.05}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{"value": -0.06}]}, "leg_L": {"translate": [{"x": 11.66, "y": -15.54, "curve": [0.444, 11.66, 0.889, -10.36, 0.444, -15.54, 0.889, 10.36]}, {"time": 1.3333, "x": -10.36, "y": 10.36, "curve": [1.778, -10.36, 2.222, 11.66, 1.778, 10.36, 2.222, -15.54]}, {"time": 2.6667, "x": 11.66, "y": -15.54, "curve": [3.111, 11.66, 3.556, -10.36, 3.111, -15.54, 3.556, 10.36]}, {"time": 4, "x": -10.36, "y": 10.36, "curve": [4.444, -10.36, 4.889, 11.66, 4.444, 10.36, 4.889, -15.54]}, {"time": 5.3333, "x": 11.66, "y": -15.54, "curve": [5.667, 11.66, 6, -10.36, 5.667, -15.54, 6, 10.36]}, {"time": 6.3333, "x": -10.36, "y": 10.36, "curve": [6.778, -10.36, 7.222, 11.66, 6.778, 10.36, 7.222, -15.54]}, {"time": 7.6667, "x": 11.66, "y": -15.54, "curve": [8.111, 11.66, 8.556, 0.65, 8.111, -15.54, 8.556, -2.59]}, {"time": 9, "x": 0.65, "y": -2.59, "curve": [9.444, 0.65, 9.889, 11.66, 9.444, -2.59, 9.889, -15.54]}, {"time": 10.3333, "x": 11.66, "y": -15.54, "curve": [10.778, 11.66, 11.222, -10.36, 10.778, -15.54, 11.222, 10.36]}, {"time": 11.6667, "x": -10.36, "y": 10.36, "curve": [12.111, -10.36, 12.556, 11.66, 12.111, 10.36, 12.556, -15.54]}, {"time": 13, "x": 11.66, "y": -15.54}]}, "leg_L2": {"rotate": [{"value": -1.58, "curve": [0.444, -1.58, 0.889, 1.51]}, {"time": 1.3333, "value": 1.51, "curve": [1.778, 1.51, 2.222, -1.58]}, {"time": 2.6667, "value": -1.58, "curve": [3.111, -1.58, 3.556, 1.51]}, {"time": 4, "value": 1.51, "curve": [4.444, 1.51, 4.889, -1.58]}, {"time": 5.3333, "value": -1.58, "curve": [5.667, -1.58, 6, 1.51]}, {"time": 6.3333, "value": 1.51, "curve": [6.778, 1.51, 7.222, -1.58]}, {"time": 7.6667, "value": -1.58, "curve": [8.111, -1.58, 8.556, -0.03]}, {"time": 9, "value": -0.03, "curve": [9.444, -0.03, 9.889, -1.58]}, {"time": 10.3333, "value": -1.58, "curve": [10.778, -1.58, 11.222, 1.51]}, {"time": 11.6667, "value": 1.51, "curve": [12.111, 1.51, 12.556, -1.58]}, {"time": 13, "value": -1.58}]}, "leg_L3": {"rotate": [{"value": -1.58, "curve": [0.444, -1.58, 0.889, 2.4]}, {"time": 1.3333, "value": 2.4, "curve": [1.778, 2.4, 2.222, -1.58]}, {"time": 2.6667, "value": -1.58, "curve": [3.111, -1.58, 3.556, 2.4]}, {"time": 4, "value": 2.4, "curve": [4.444, 2.4, 4.889, -1.58]}, {"time": 5.3333, "value": -1.58, "curve": [5.667, -1.58, 6, 2.4]}, {"time": 6.3333, "value": 2.4, "curve": [6.778, 2.4, 7.222, -1.58]}, {"time": 7.6667, "value": -1.58, "curve": [8.111, -1.58, 8.556, 0.41]}, {"time": 9, "value": 0.41, "curve": [9.444, 0.41, 9.889, -1.58]}, {"time": 10.3333, "value": -1.58, "curve": [10.778, -1.58, 11.222, 2.4]}, {"time": 11.6667, "value": 2.4, "curve": [12.111, 2.4, 12.556, -1.58]}, {"time": 13, "value": -1.58}]}, "leg_L4": {"rotate": [{"value": -1.58, "curve": [0.444, -1.58, 0.889, 2.4]}, {"time": 1.3333, "value": 2.4, "curve": [1.778, 2.4, 2.222, -1.58]}, {"time": 2.6667, "value": -1.58, "curve": [3.111, -1.58, 3.556, 2.4]}, {"time": 4, "value": 2.4, "curve": [4.444, 2.4, 4.889, -1.58]}, {"time": 5.3333, "value": -1.58, "curve": [5.667, -1.58, 6, 2.4]}, {"time": 6.3333, "value": 2.4, "curve": [6.778, 2.4, 7.222, -1.58]}, {"time": 7.6667, "value": -1.58, "curve": [8.111, -1.58, 8.556, 0.41]}, {"time": 9, "value": 0.41, "curve": [9.444, 0.41, 9.889, -1.58]}, {"time": 10.3333, "value": -1.58, "curve": [10.778, -1.58, 11.222, 2.4]}, {"time": 11.6667, "value": 2.4, "curve": [12.111, 2.4, 12.556, -1.58]}, {"time": 13, "value": -1.58}]}, "bone38": {"rotate": [{"value": 20.79, "curve": [0.114, 26.1, 0.224, 30.05]}, {"time": 0.3333, "value": 30.05, "curve": [0.778, 30.05, 1.222, -27.84]}, {"time": 1.6667, "value": -27.84, "curve": [2.111, -27.84, 2.556, 30.05]}, {"time": 3, "value": 30.05, "curve": [3.444, 30.05, 3.889, -27.84]}, {"time": 4.3333, "value": -27.84, "curve": [4.778, -27.84, 5.222, 30.05]}, {"time": 5.6667, "value": 30.05, "curve": [6, 30.05, 6.333, -27.84]}, {"time": 6.6667, "value": -27.84, "curve": [7.111, -27.84, 7.556, 30.05]}, {"time": 8, "value": 30.05, "curve": [8.444, 30.05, 8.889, -27.84]}, {"time": 9.3333, "value": -27.84, "curve": [9.778, -27.84, 10.222, 30.05]}, {"time": 10.6667, "value": 30.05, "curve": [11.111, 30.05, 11.556, -27.84]}, {"time": 12, "value": -27.84, "curve": [12.335, -27.84, 12.67, 4.58]}, {"time": 13, "value": 20.79}], "translate": [{"x": 18.52, "y": -33.85, "curve": [0.057, 19.64, 0.112, 20.49, 0.057, -35.79, 0.112, -37.26]}, {"time": 0.1667, "x": 20.49, "y": -37.26, "curve": [0.611, 20.49, 1.056, -21.43, 0.611, -37.26, 1.056, 35.4]}, {"time": 1.5, "x": -21.43, "y": 35.4, "curve": [1.944, -21.43, 2.389, 20.49, 1.944, 35.4, 2.389, -37.26]}, {"time": 2.8333, "x": 20.49, "y": -37.26, "curve": [3.278, 20.49, 3.722, -21.43, 3.278, -37.26, 3.722, 35.4]}, {"time": 4.1667, "x": -21.43, "y": 35.4, "curve": [4.611, -21.43, 5.056, 20.49, 4.611, 35.4, 5.056, -37.26]}, {"time": 5.5, "x": 20.49, "y": -37.26, "curve": [5.833, 20.49, 6.167, -21.43, 5.833, -37.26, 6.167, 35.4]}, {"time": 6.5, "x": -21.43, "y": 35.4, "curve": [6.944, -21.43, 7.389, 20.49, 6.944, 35.4, 7.389, -37.26]}, {"time": 7.8333, "x": 20.49, "y": -37.26, "curve": [8.278, 20.49, 8.722, -21.43, 8.278, -37.26, 8.722, 35.4]}, {"time": 9.1667, "x": -21.43, "y": 35.4, "curve": [9.611, -21.43, 10.056, 20.49, 9.611, 35.4, 10.056, -37.26]}, {"time": 10.5, "x": 20.49, "y": -37.26, "curve": [10.944, 20.49, 11.389, -21.43, 10.944, -37.26, 11.389, 35.4]}, {"time": 11.8333, "x": -21.43, "y": 35.4, "curve": [12.223, -21.43, 12.613, 10.58, 12.223, 35.4, 12.613, -20.07]}, {"time": 13, "x": 18.52, "y": -33.85}]}, "sh_R2": {"rotate": [{}]}, "sh_R3": {"rotate": [{}]}, "arm_R3": {"translate": [{"x": -0.48, "y": 0.56, "curve": [0.057, -0.21, 0.112, 0, 0.057, 0.24, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -10.25, 0.611, 0, 1.056, 12.01]}, {"time": 1.5, "x": -10.25, "y": 12.01, "curve": [1.944, -10.25, 2.389, 0, 1.944, 12.01, 2.389, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -10.25, 3.278, 0, 3.722, 12.01]}, {"time": 4.1667, "x": -10.25, "y": 12.01, "curve": [4.611, -10.25, 5.056, 0, 4.611, 12.01, 5.056, 0]}, {"time": 5.5, "curve": [5.833, 0, 6.167, -11.79, 5.833, 0, 6.167, 15.08]}, {"time": 6.5, "x": -11.79, "y": 15.08, "curve": [6.944, -11.79, 7.389, 0, 6.944, 15.08, 7.389, 0]}, {"time": 7.8333, "curve": [8.278, 0, 8.722, -10.25, 8.278, 0, 8.722, 12.01]}, {"time": 9.1667, "x": -10.25, "y": 12.01, "curve": [9.611, -10.25, 10.056, 0, 9.611, 12.01, 10.056, 0]}, {"time": 10.5, "curve": [10.944, 0, 11.389, -10.25, 10.944, 0, 11.389, 12.01]}, {"time": 11.8333, "x": -10.25, "y": 12.01, "curve": [12.223, -10.25, 12.613, -2.42, 12.223, 12.01, 12.613, 2.84]}, {"time": 13, "x": -0.48, "y": 0.56}]}, "headround3": {"translate": [{"x": -6.43, "curve": [0.225, -76.74, 0.446, -147.99, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -147.99, "curve": [1.111, -147.99, 1.556, 135.12, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 135.12, "curve": [2.444, 135.12, 2.889, -147.99, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -147.99, "curve": [3.778, -147.99, 4.222, 135.12, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 135.12, "curve": [5.111, 135.12, 5.556, -147.99, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -147.99, "curve": [6.333, -147.99, 6.667, 135.12, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 135.12, "curve": [7.444, 135.12, 7.889, -6.43, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -6.43, "curve": [8.778, -6.43, 9.222, 135.12, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 135.12, "curve": [10.111, 135.12, 10.556, -147.99, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -147.99, "curve": [11.444, -147.99, 11.889, 135.12, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 135.12, "curve": [12.557, 135.12, 12.781, 64.81, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -6.43}]}, "headround": {"translate": [{"y": -59.75, "curve": [0.279, 0, 0.556, 0, 0.279, 19.75, 0.556, 125.5]}, {"time": 0.8333, "y": 125.5, "curve": [1.278, 0, 1.722, 0, 1.278, 125.5, 1.722, -146.13]}, {"time": 2.1667, "y": -146.13, "curve": [2.611, 0, 3.056, 0, 2.611, -146.13, 3.056, 125.5]}, {"time": 3.5, "y": 125.5, "curve": [3.944, 0, 4.389, 0, 3.944, 125.5, 4.389, -146.13]}, {"time": 4.8333, "y": -146.13, "curve": [5.278, 0, 5.722, 0, 5.278, -146.13, 5.722, 125.5]}, {"time": 6.1667, "y": 125.5, "curve": [6.5, 0, 6.833, 0, 6.5, 125.5, 6.833, -146.13]}, {"time": 7.1667, "y": -146.13, "curve": [7.611, 0, 8.056, 0, 7.611, -146.13, 8.056, -10.32]}, {"time": 8.5, "y": -10.32, "curve": [8.944, 0, 9.389, 0, 8.944, -10.32, 9.389, -146.13]}, {"time": 9.8333, "y": -146.13, "curve": [10.278, 0, 10.722, 0, 10.278, -146.13, 10.722, 125.5]}, {"time": 11.1667, "y": 125.5, "curve": [11.611, 0, 12.056, 0, 11.611, 125.5, 12.056, -146.13]}, {"time": 12.5, "y": -146.13, "curve": [12.667, 0, 12.835, 0, 12.667, -146.13, 12.835, -107.8]}, {"time": 13, "y": -59.75}]}, "bodyround": {"translate": [{"x": -97.46, "y": 75.45, "curve": [0.114, -124.68, 0.224, -144.97, 0.114, 97.29, 0.224, 113.57]}, {"time": 0.3333, "x": -144.97, "y": 113.57, "curve": [0.778, -144.97, 1.222, 152.01, 0.778, 113.57, 1.222, -124.69]}, {"time": 1.6667, "x": 152.01, "y": -124.69, "curve": [2.111, 152.01, 2.556, -144.97, 2.111, -124.69, 2.556, 113.57]}, {"time": 3, "x": -144.97, "y": 113.57, "curve": [3.444, -144.97, 3.889, 152.01, 3.444, 113.57, 3.889, -124.69]}, {"time": 4.3333, "x": 152.01, "y": -124.69, "curve": [4.778, 152.01, 5.222, -144.97, 4.778, -124.69, 5.222, 113.57]}, {"time": 5.6667, "x": -144.97, "y": 113.57, "curve": [6, -144.97, 6.333, 152.01, 6, 113.57, 6.333, -124.69]}, {"time": 6.6667, "x": 152.01, "y": -124.69, "curve": [7.111, 152.01, 7.556, 3.52, 7.111, -124.69, 7.556, -5.56]}, {"time": 8, "x": 3.52, "y": -5.56, "curve": [8.444, 3.52, 8.889, 152.01, 8.444, -5.56, 8.889, -124.69]}, {"time": 9.3333, "x": 152.01, "y": -124.69, "curve": [9.778, 152.01, 10.222, -144.97, 9.778, -124.69, 10.222, 113.57]}, {"time": 10.6667, "x": -144.97, "y": 113.57, "curve": [11.111, -144.97, 11.556, 152.01, 11.111, 113.57, 11.556, -124.69]}, {"time": 12, "x": 152.01, "y": -124.69, "curve": [12.335, 152.01, 12.67, -14.31, 12.335, -124.69, 12.67, 8.74]}, {"time": 13, "x": -97.46, "y": 75.45}]}, "arm_Lb": {"rotate": [{}]}, "hand_L": {"rotate": [{"value": -1.9, "curve": [0.168, -3.12, 0.334, -4.13]}, {"time": 0.5, "value": -4.13, "curve": [0.944, -4.13, 1.389, 2.87]}, {"time": 1.8333, "value": 2.87, "curve": [2.278, 2.87, 2.722, -4.13]}, {"time": 3.1667, "value": -4.13, "curve": [3.611, -4.13, 4.056, 2.87]}, {"time": 4.5, "value": 2.87, "curve": [4.944, 2.87, 5.389, -4.13]}, {"time": 5.8333, "value": -4.13, "curve": [6.167, -4.13, 6.5, 2.87]}, {"time": 6.8333, "value": 2.87, "curve": [7.278, 2.87, 7.722, -4.13]}, {"time": 8.1667, "value": -4.13, "curve": [8.611, -4.13, 9.056, 2.87]}, {"time": 9.5, "value": 2.87, "curve": [9.944, 2.87, 10.389, -4.13]}, {"time": 10.8333, "value": -4.13, "curve": [11.278, -4.13, 11.722, 2.87]}, {"time": 12.1667, "value": 2.87, "curve": [12.445, 2.87, 12.724, 0.15]}, {"time": 13, "value": -1.9}]}, "hand_L2": {"rotate": [{"value": -0.63, "curve": [0.225, -2.37, 0.446, -4.13]}, {"time": 0.6667, "value": -4.13, "curve": [1.111, -4.13, 1.556, 2.87]}, {"time": 2, "value": 2.87, "curve": [2.444, 2.87, 2.889, -4.13]}, {"time": 3.3333, "value": -4.13, "curve": [3.778, -4.13, 4.222, 2.87]}, {"time": 4.6667, "value": 2.87, "curve": [5.111, 2.87, 5.556, -4.13]}, {"time": 6, "value": -4.13, "curve": [6.333, -4.13, 6.667, 2.87]}, {"time": 7, "value": 2.87, "curve": [7.444, 2.87, 7.889, -4.13]}, {"time": 8.3333, "value": -4.13, "curve": [8.778, -4.13, 9.222, 2.87]}, {"time": 9.6667, "value": 2.87, "curve": [10.111, 2.87, 10.556, -4.13]}, {"time": 11, "value": -4.13, "curve": [11.444, -4.13, 11.889, 2.87]}, {"time": 12.3333, "value": 2.87, "curve": [12.557, 2.87, 12.781, 1.13]}, {"time": 13, "value": -0.63}]}, "arm_L2": {"translate": [{"x": -10.93, "y": -17.14, "curve": [0.114, -15.12, 0.224, -18.25, 0.114, -19.8, 0.224, -21.79]}, {"time": 0.3333, "x": -18.25, "y": -21.79, "curve": [0.778, -18.25, 1.222, 27.48, 0.778, -21.79, 1.222, 7.29]}, {"time": 1.6667, "x": 27.48, "y": 7.29, "curve": [2.111, 27.48, 2.556, -18.25, 2.111, 7.29, 2.556, -21.79]}, {"time": 3, "x": -18.25, "y": -21.79, "curve": [3.444, -18.25, 3.889, 27.48, 3.444, -21.79, 3.889, 7.29]}, {"time": 4.3333, "x": 27.48, "y": 7.29, "curve": [4.778, 27.48, 5.222, -18.25, 4.778, 7.29, 5.222, -21.79]}, {"time": 5.6667, "x": -18.25, "y": -21.79, "curve": [6, -18.25, 6.333, 27.48, 6, -21.79, 6.333, 7.29]}, {"time": 6.6667, "x": 27.48, "y": 7.29, "curve": [7.111, 27.48, 7.556, -18.25, 7.111, 7.29, 7.556, -21.79]}, {"time": 8, "x": -18.25, "y": -21.79, "curve": [8.444, -18.25, 8.889, 27.48, 8.444, -21.79, 8.889, 7.29]}, {"time": 9.3333, "x": 27.48, "y": 7.29, "curve": [9.778, 27.48, 10.222, -18.25, 9.778, 7.29, 10.222, -21.79]}, {"time": 10.6667, "x": -18.25, "y": -21.79, "curve": [11.111, -18.25, 11.556, 27.48, 11.111, -21.79, 11.556, 7.29]}, {"time": 12, "x": 27.48, "y": 7.29, "curve": [12.335, 27.48, 12.67, 1.87, 12.335, 7.29, 12.67, -9]}, {"time": 13, "x": -10.93, "y": -17.14}]}, "leg_R4": {"translate": [{"x": -22.23, "y": 7.41, "curve": [0.444, -22.23, 0.889, 11.86, 0.444, 7.41, 0.889, -4.45]}, {"time": 1.3333, "x": 11.86, "y": -4.45, "curve": [1.778, 11.86, 2.222, -22.23, 1.778, -4.45, 2.222, 7.41]}, {"time": 2.6667, "x": -22.23, "y": 7.41, "curve": [3.111, -22.23, 3.556, 11.86, 3.111, 7.41, 3.556, -4.45]}, {"time": 4, "x": 11.86, "y": -4.45, "curve": [4.444, 11.86, 4.889, -22.23, 4.444, -4.45, 4.889, 7.41]}, {"time": 5.3333, "x": -22.23, "y": 7.41, "curve": [5.667, -22.23, 6, 11.86, 5.667, 7.41, 6, -4.45]}, {"time": 6.3333, "x": 11.86, "y": -4.45, "curve": [6.778, 11.86, 7.222, -22.23, 6.778, -4.45, 7.222, 7.41]}, {"time": 7.6667, "x": -22.23, "y": 7.41, "curve": [8.111, -22.23, 8.556, -5.19, 8.111, 7.41, 8.556, 1.48]}, {"time": 9, "x": -5.19, "y": 1.48, "curve": [9.444, -5.19, 9.889, -22.23, 9.444, 1.48, 9.889, 7.41]}, {"time": 10.3333, "x": -22.23, "y": 7.41, "curve": [10.778, -22.23, 11.222, 11.86, 10.778, 7.41, 11.222, -4.45]}, {"time": 11.6667, "x": 11.86, "y": -4.45, "curve": [12.111, 11.86, 12.556, -22.23, 12.111, -4.45, 12.556, 7.41]}, {"time": 13, "x": -22.23, "y": 7.41}]}, "leg_R5": {"rotate": [{}]}, "leg_R": {"rotate": [{"value": -5.77, "curve": [0.114, -6.9, 0.224, -7.74]}, {"time": 0.3333, "value": -7.74, "curve": [0.778, -7.74, 1.222, 4.55]}, {"time": 1.6667, "value": 4.55, "curve": [2.111, 4.55, 2.556, -7.74]}, {"time": 3, "value": -7.74, "curve": [3.444, -7.74, 3.889, 4.55]}, {"time": 4.3333, "value": 4.55, "curve": [4.778, 4.55, 5.222, -7.74]}, {"time": 5.6667, "value": -7.74, "curve": [6, -7.74, 6.333, 4.55]}, {"time": 6.6667, "value": 4.55, "curve": [7.111, 4.55, 7.556, -7.74]}, {"time": 8, "value": -7.74, "curve": [8.444, -7.74, 8.889, 4.55]}, {"time": 9.3333, "value": 4.55, "curve": [9.778, 4.55, 10.222, -7.74]}, {"time": 10.6667, "value": -7.74, "curve": [11.111, -7.74, 11.556, 4.55]}, {"time": 12, "value": 4.55, "curve": [12.335, 4.55, 12.67, -2.33]}, {"time": 13, "value": -5.77}], "translate": [{"x": -28.61, "y": 9.43, "curve": [0.057, -31.03, 0.112, -32.87, 0.057, 10.26, 0.112, 10.9]}, {"time": 0.1667, "x": -32.87, "y": 10.9, "curve": [0.611, -32.87, 1.056, 57.7, 0.611, 10.9, 1.056, -20.37]}, {"time": 1.5, "x": 57.72, "y": -20.37, "curve": [1.944, 57.74, 2.389, -32.85, 1.944, -20.38, 2.389, 10.89]}, {"time": 2.8333, "x": -32.87, "y": 10.9, "curve": [3.278, -32.9, 3.722, 57.7, 3.278, 10.91, 3.722, -20.37]}, {"time": 4.1667, "x": 57.72, "y": -20.37, "curve": [4.611, 57.74, 5.056, -32.84, 4.611, -20.38, 5.056, 10.89]}, {"time": 5.5, "x": -32.87, "y": 10.9, "curve": [5.833, -32.9, 6.167, 57.7, 5.833, 10.91, 6.167, -20.37]}, {"time": 6.5, "x": 57.72, "y": -20.37, "curve": [6.944, 57.74, 7.389, -32.85, 6.944, -20.38, 7.389, 10.89]}, {"time": 7.8333, "x": -32.87, "y": 10.9, "curve": [8.278, -32.9, 8.722, 57.7, 8.278, 10.91, 8.722, -20.37]}, {"time": 9.1667, "x": 57.72, "y": -20.37, "curve": [9.611, 57.74, 10.056, -32.85, 9.611, -20.38, 10.056, 10.89]}, {"time": 10.5, "x": -32.87, "y": 10.9, "curve": [10.944, -32.9, 11.389, 57.7, 10.944, 10.91, 11.389, -20.37]}, {"time": 11.8333, "x": 57.72, "y": -20.37, "curve": [12.223, 57.74, 12.613, -11.44, 12.223, -20.38, 12.613, 3.5]}, {"time": 13, "x": -28.61, "y": 9.43}]}, "arm_Lb2": {"scale": [{"x": 1.074, "curve": [0.114, 1.092, 0.224, 1.105, 0.114, 1, 0.224, 1]}, {"time": 0.3333, "x": 1.105, "curve": [0.778, 1.105, 1.222, 0.907, 0.778, 1, 1.222, 1]}, {"time": 1.6667, "x": 0.907, "curve": [2.111, 0.907, 2.556, 1.105, 2.111, 1, 2.556, 1]}, {"time": 3, "x": 1.105, "curve": [3.444, 1.105, 3.889, 0.907, 3.444, 1, 3.889, 1]}, {"time": 4.3333, "x": 0.907, "curve": [4.778, 0.907, 5.222, 1.105, 4.778, 1, 5.222, 1]}, {"time": 5.6667, "x": 1.105, "curve": [6, 1.105, 6.333, 0.907, 6, 1, 6.333, 1]}, {"time": 6.6667, "x": 0.907, "curve": [7.111, 0.907, 7.556, 1.105, 7.111, 1, 7.556, 1]}, {"time": 8, "x": 1.105, "curve": [8.444, 1.105, 8.889, 0.907, 8.444, 1, 8.889, 1]}, {"time": 9.3333, "x": 0.907, "curve": [9.778, 0.907, 10.222, 1.105, 9.778, 1, 10.222, 1]}, {"time": 10.6667, "x": 1.105, "curve": [11.111, 1.105, 11.556, 0.907, 11.111, 1, 11.556, 1]}, {"time": 12, "x": 0.907, "curve": [12.335, 0.907, 12.67, 1.018, 12.335, 1, 12.67, 1]}, {"time": 13, "x": 1.074}]}, "eye_L": {"translate": [{"time": 2.1667, "curve": [2.2, 0, 2.233, -2.44, 2.2, 0, 2.233, -0.39]}, {"time": 2.2667, "x": -2.44, "y": -0.39, "curve": "stepped"}, {"time": 3.1667, "x": -2.44, "y": -0.39, "curve": [3.2, -2.44, 3.233, -0.99, 3.2, -0.39, 3.233, -0.45]}, {"time": 3.2667, "x": -0.99, "y": -0.45, "curve": "stepped"}, {"time": 3.7333, "x": -0.99, "y": -0.45, "curve": [3.767, -0.99, 3.8, 0, 3.767, -0.45, 3.8, 0]}, {"time": 3.8333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -0.66, 6.2, 0, 6.233, -3]}, {"time": 6.2667, "x": -0.66, "y": -3, "curve": "stepped"}, {"time": 7, "x": -0.66, "y": -3, "curve": [7.033, -0.66, 7.067, -0.66, 7.033, -3, 7.067, -4.45]}, {"time": 7.1, "x": -0.66, "y": -4.45, "curve": "stepped"}, {"time": 8, "x": -0.66, "y": -4.45, "curve": [8.033, -0.66, 8.067, -1.89, 8.033, -4.45, 8.067, -4.25]}, {"time": 8.1, "x": -1.89, "y": -4.25, "curve": "stepped"}, {"time": 8.4, "x": -1.89, "y": -4.25, "curve": [8.433, -1.89, 8.467, -0.66, 8.433, -4.25, 8.467, -3]}, {"time": 8.5, "x": -0.66, "y": -3, "curve": "stepped"}, {"time": 10.1667, "x": -0.66, "y": -3, "curve": [10.2, -0.66, 10.233, 0, 10.2, -3, 10.233, 0]}, {"time": 10.2667}]}, "eye_R": {"translate": [{"time": 2.1667, "curve": [2.2, 0, 2.233, -2.93, 2.2, 0, 2.233, -0.39]}, {"time": 2.2667, "x": -2.93, "y": -0.39, "curve": "stepped"}, {"time": 3.1667, "x": -2.93, "y": -0.39, "curve": [3.2, -2.93, 3.233, -1.48, 3.2, -0.39, 3.233, -0.45]}, {"time": 3.2667, "x": -1.48, "y": -0.45, "curve": "stepped"}, {"time": 3.7333, "x": -1.48, "y": -0.45, "curve": [3.767, -1.48, 3.8, 0, 3.767, -0.45, 3.8, 0]}, {"time": 3.8333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -1.77, 6.2, 0, 6.233, -3.26]}, {"time": 6.2667, "x": -1.77, "y": -3.26, "curve": "stepped"}, {"time": 7, "x": -1.77, "y": -3.26, "curve": [7.033, -1.77, 7.067, -1.77, 7.033, -3.26, 7.067, -4.71]}, {"time": 7.1, "x": -1.77, "y": -4.71, "curve": "stepped"}, {"time": 8, "x": -1.77, "y": -4.71, "curve": [8.033, -1.77, 8.067, -2.99, 8.033, -4.71, 8.067, -4.5]}, {"time": 8.1, "x": -2.99, "y": -4.5, "curve": "stepped"}, {"time": 8.4, "x": -2.99, "y": -4.5, "curve": [8.433, -2.99, 8.467, -1.77, 8.433, -4.5, 8.467, -3.26]}, {"time": 8.5, "x": -1.77, "y": -3.26, "curve": "stepped"}, {"time": 10.1667, "x": -1.77, "y": -3.26, "curve": [10.2, -1.77, 10.233, 0, 10.2, -3.26, 10.233, 0]}, {"time": 10.2667}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-4.14209, -0.38168, -4.1405, -0.38161, -3.33032, -0.34869, -3.32898, -0.34863, -1.90186, 0.24239, -1.90125, 0.24246, -0.66089, 0.03307, -0.66077, 0.03308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -8.3313, -0.56827, -8.32947, -0.56792, -11.11658, -0.1275, -11.11218, -0.12712, -11.07812, -0.69712, -11.07568, -0.69698, -8.87781, -1.01617, -8.87659, -1.01605, -8.12317, -0.47701, -8.11951, -0.47681, -6.02515, -0.20921, -6.02161, -0.20905, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -9.06165, -0.45806, -9.06018, -0.45772, -11.86047, -0.09027, -11.85608, -0.08986, -11.19104, -0.50333, -11.18933, -0.50302, -7.83594, -0.63855, -7.83411, -0.63843, -2.72815, -0.08435, -2.72766, -0.08424, -1.11218, -0.16521, -1.11206, -0.16516], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-4.14209, -0.38168, -4.1405, -0.38161, -3.33032, -0.34869, -3.32898, -0.34863, -1.90186, 0.24239, -1.90125, 0.24246, -0.66089, 0.03307, -0.66077, 0.03308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -8.3313, -0.56827, -8.32947, -0.56792, -11.11658, -0.1275, -11.11218, -0.12712, -11.07812, -0.69712, -11.07568, -0.69698, -8.87781, -1.01617, -8.87659, -1.01605, -8.12317, -0.47701, -8.11951, -0.47681, -6.02515, -0.20921, -6.02161, -0.20905, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -9.06165, -0.45806, -9.06018, -0.45772, -11.86047, -0.09027, -11.85608, -0.08986, -11.19104, -0.50333, -11.18933, -0.50302, -7.83594, -0.63855, -7.83411, -0.63843, -2.72815, -0.08435, -2.72766, -0.08424, -1.11218, -0.16521, -1.11206, -0.16516], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "vertices": [-4.14209, -0.38168, -4.1405, -0.38161, -3.33032, -0.34869, -3.32898, -0.34863, -1.90186, 0.24239, -1.90125, 0.24246, -0.66089, 0.03307, -0.66077, 0.03308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -8.3313, -0.56827, -8.32947, -0.56792, -11.11658, -0.1275, -11.11218, -0.12712, -11.07812, -0.69712, -11.07568, -0.69698, -8.87781, -1.01617, -8.87659, -1.01605, -8.12317, -0.47701, -8.11951, -0.47681, -6.02515, -0.20921, -6.02161, -0.20905, 0, 0, 0, 0, -4.27332, -0.39386, -4.27222, -0.39362, -9.06165, -0.45806, -9.06018, -0.45772, -11.86047, -0.09027, -11.85608, -0.08986, -11.19104, -0.50333, -11.18933, -0.50302, -7.83594, -0.63855, -7.83411, -0.63843, -2.72815, -0.08435, -2.72766, -0.08424, -1.11218, -0.16521, -1.11206, -0.16516], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-7.79968, -0.17298, -7.79968, -0.17278, -8.48132, -0.09947, -8.47998, -0.09935, -10.70215, 0.08652, -10.70056, 0.08669, -10.76172, -0.90234, -10.75903, -0.90211, -8.13037, -0.74312, -8.12842, -0.74286, -3.18921, -0.32513, -3.18921, -0.32494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88721, 0.11578, -0.88708, 0.11595, -2.03784, -0.04106, -2.03772, -0.04082, -4.03076, 0.10783, -4.03076, 0.10791, -5.72546, 0.00484, -5.72546, 0.00502, -7.40625, 0.18278, -7.40625, 0.18303, 0, 0, 0, 0, -4.19592, -0.18666, -4.19592, -0.18642, -8.4165, -0.83958, -8.41455, -0.8394, -10.76172, -0.90234, -10.75903, -0.90211, -11.3075, -0.21767, -11.30603, -0.21758, -9.91382, -0.26749, -9.91223, -0.26755, -6.68518, -0.01719, -6.6842, -0.01723, -2.80884, 0.01843, -2.80859, 0.01837, -1.05066, -0.25283, -1.05066, -0.25284], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-7.79968, -0.17298, -7.79968, -0.17278, -8.48132, -0.09947, -8.47998, -0.09935, -10.70215, 0.08652, -10.70056, 0.08669, -10.76172, -0.90234, -10.75903, -0.90211, -8.13037, -0.74312, -8.12842, -0.74286, -3.18921, -0.32513, -3.18921, -0.32494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88721, 0.11578, -0.88708, 0.11595, -2.03784, -0.04106, -2.03772, -0.04082, -4.03076, 0.10783, -4.03076, 0.10791, -5.72546, 0.00484, -5.72546, 0.00502, -7.40625, 0.18278, -7.40625, 0.18303, 0, 0, 0, 0, -4.19592, -0.18666, -4.19592, -0.18642, -8.4165, -0.83958, -8.41455, -0.8394, -10.76172, -0.90234, -10.75903, -0.90211, -11.3075, -0.21767, -11.30603, -0.21758, -9.91382, -0.26749, -9.91223, -0.26755, -6.68518, -0.01719, -6.6842, -0.01723, -2.80884, 0.01843, -2.80859, 0.01837, -1.05066, -0.25283, -1.05066, -0.25284], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "vertices": [-7.79968, -0.17298, -7.79968, -0.17278, -8.48132, -0.09947, -8.47998, -0.09935, -10.70215, 0.08652, -10.70056, 0.08669, -10.76172, -0.90234, -10.75903, -0.90211, -8.13037, -0.74312, -8.12842, -0.74286, -3.18921, -0.32513, -3.18921, -0.32494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88721, 0.11578, -0.88708, 0.11595, -2.03784, -0.04106, -2.03772, -0.04082, -4.03076, 0.10783, -4.03076, 0.10791, -5.72546, 0.00484, -5.72546, 0.00502, -7.40625, 0.18278, -7.40625, 0.18303, 0, 0, 0, 0, -4.19592, -0.18666, -4.19592, -0.18642, -8.4165, -0.83958, -8.41455, -0.8394, -10.76172, -0.90234, -10.75903, -0.90211, -11.3075, -0.21767, -11.30603, -0.21758, -9.91382, -0.26749, -9.91223, -0.26755, -6.68518, -0.01719, -6.6842, -0.01723, -2.80884, 0.01843, -2.80859, 0.01837, -1.05066, -0.25283, -1.05066, -0.25284], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 512, "vertices": [-3.23401, 0.16182, -3.23376, 0.16196, -7.46509, 0.19348, -7.46484, 0.1936, -10.37207, -0.29153, -10.37048, -0.29131, -10.89832, 0.005, -10.89673, 0.00514, -9.29431, -0.34551, -9.29224, -0.34523, -5.58826, -0.08058, -5.58716, -0.08054, -0.45374, -0.06736, -0.45361, -0.06735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.95459, -0.02225, -4.95398, -0.02216, -8.62476, 0.4316, -8.6239, 0.43181, -10.51965, 0.34635, -10.51904, 0.34656, -10.16089, 0.32837, -10.16028, 0.32857, -7.00769, 0.35069, -7.00732, 0.35086, -2.69946, 0.045, -2.69934, 0.04514], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 512, "vertices": [-3.23401, 0.16182, -3.23376, 0.16196, -7.46509, 0.19348, -7.46484, 0.1936, -10.37207, -0.29153, -10.37048, -0.29131, -10.89832, 0.005, -10.89673, 0.00514, -9.29431, -0.34551, -9.29224, -0.34523, -5.58826, -0.08058, -5.58716, -0.08054, -0.45374, -0.06736, -0.45361, -0.06735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.95459, -0.02225, -4.95398, -0.02216, -8.62476, 0.4316, -8.6239, 0.43181, -10.51965, 0.34635, -10.51904, 0.34656, -10.16089, 0.32837, -10.16028, 0.32857, -7.00769, 0.35069, -7.00732, 0.35086, -2.69946, 0.045, -2.69934, 0.04514], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 10, "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667, "offset": 512, "vertices": [-3.23401, 0.16182, -3.23376, 0.16196, -7.46509, 0.19348, -7.46484, 0.1936, -10.37207, -0.29153, -10.37048, -0.29131, -10.89832, 0.005, -10.89673, 0.00514, -9.29431, -0.34551, -9.29224, -0.34523, -5.58826, -0.08058, -5.58716, -0.08054, -0.45374, -0.06736, -0.45361, -0.06735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.95459, -0.02225, -4.95398, -0.02216, -8.62476, 0.4316, -8.6239, 0.43181, -10.51965, 0.34635, -10.51904, 0.34656, -10.16089, 0.32837, -10.16028, 0.32857, -7.00769, 0.35069, -7.00732, 0.35086, -2.69946, 0.045, -2.69934, 0.04514], "curve": [10.222, 0, 10.278, 1]}, {"time": 10.3333}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 60, "vertices": [0.38232, 0.62472, 0.38269, 0.62472, -0.04468, -0.00028, -0.04468, -0.00029, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, 0.20764, 0.00136, 0.20764, 0.00134, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, -0.04468, -0.00028, -0.04468, -0.00029, -0.27136, -0.98679, -0.27124, -0.98678, 0.96606, 0.5452, 0.96606, 0.5452, 0.30103, 0.29408, 0.30127, 0.29408, -0.41516, -0.00267, -0.41516, -0.00268, -0.33948, -0.00218, -0.33948, -0.00219, 0.20764, 0.00136, 0.20764, 0.00134, -0.18799, -0.00121, -0.18799, -0.00121, -0.26367, -0.00169, -0.26367, -0.0017, 0.30481, -0.29018, 0.30481, -0.29018, 0.39453, -0.89921, 0.39453, -0.8992, 1.54956, 0.46568, 1.54968, 0.46567, 1.22864, -0.20518, 1.22876, -0.20523, 0.94617, -0.20421, 0.94641, -0.20422, 1.24609, -0.09574, 1.24622, -0.09576, 1.24609, -0.09574, 1.24622, -0.09576, 0.94617, -0.20421, 0.94641, -0.20422, 1.2959, 0.00842, 1.29602, 0.00836, 1.06091, -0.8949, 1.06091, -0.8949, 0.39453, -0.89921, 0.39453, -0.8992, 0.39453, -0.89921, 0.39453, -0.8992, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.31958, 0.00206, 0.31958, 0.00206, 1.03857, -0.09709, 1.0387, -0.09711, 1.03857, -0.09709, 1.0387, -0.09711, 0.31958, 0.00206, 0.31958, 0.00206, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298], "curve": "stepped"}, {"time": 9.8333, "offset": 60, "vertices": [0.38232, 0.62472, 0.38269, 0.62472, -0.04468, -0.00028, -0.04468, -0.00029, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, 0.20764, 0.00136, 0.20764, 0.00134, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, -0.04468, -0.00028, -0.04468, -0.00029, -0.27136, -0.98679, -0.27124, -0.98678, 0.96606, 0.5452, 0.96606, 0.5452, 0.30103, 0.29408, 0.30127, 0.29408, -0.41516, -0.00267, -0.41516, -0.00268, -0.33948, -0.00218, -0.33948, -0.00219, 0.20764, 0.00136, 0.20764, 0.00134, -0.18799, -0.00121, -0.18799, -0.00121, -0.26367, -0.00169, -0.26367, -0.0017, 0.30481, -0.29018, 0.30481, -0.29018, 0.39453, -0.89921, 0.39453, -0.8992, 1.54956, 0.46568, 1.54968, 0.46567, 1.22864, -0.20518, 1.22876, -0.20523, 0.94617, -0.20421, 0.94641, -0.20422, 1.24609, -0.09574, 1.24622, -0.09576, 1.24609, -0.09574, 1.24622, -0.09576, 0.94617, -0.20421, 0.94641, -0.20422, 1.2959, 0.00842, 1.29602, 0.00836, 1.06091, -0.8949, 1.06091, -0.8949, 0.39453, -0.89921, 0.39453, -0.8992, 0.39453, -0.89921, 0.39453, -0.8992, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.31958, 0.00206, 0.31958, 0.00206, 1.03857, -0.09709, 1.0387, -0.09711, 1.03857, -0.09709, 1.0387, -0.09711, 0.31958, 0.00206, 0.31958, 0.00206, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298], "curve": [10.056, 0, 10.278, 1]}, {"time": 10.5}]}}}}}, "touch": {"bones": {"body": {"rotate": [{"value": 2.05, "curve": [0.024, 2.05, 0.059, -3.52]}, {"time": 0.2333, "value": -3.52, "curve": [0.544, -3.52, 0.856, 2.05]}, {"time": 1.1667, "value": 2.05}]}, "body2": {"rotate": [{"curve": [0.024, 0, 0.059, -4.23]}, {"time": 0.2333, "value": -4.23, "curve": [0.544, -4.23, 0.856, 0]}, {"time": 1.1667}], "translate": [{"x": -5.67, "curve": [0.024, -5.67, 0.059, 10.68, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 10.68, "curve": [0.544, 10.68, 0.856, -5.67, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -5.67}], "scale": [{"y": 1.06, "curve": [0.078, 1, 0.156, 1, 0.024, 1.06, 0.059, 0.886]}, {"time": 0.2333, "y": 0.886, "curve": [0.544, 1, 0.856, 1, 0.544, 0.886, 0.856, 1.06]}, {"time": 1.1667, "y": 1.06}]}, "body3": {"rotate": [{"value": -0.07, "curve": [0.028, -0.07, 0.067, -2.11]}, {"time": 0.2667, "value": -2.11, "curve": [0.567, -2.11, 0.867, -0.07]}, {"time": 1.1667, "value": -0.07}], "translate": [{"x": -5.25, "curve": [0.028, -5.25, 0.067, 11.38, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 11.38, "curve": [0.567, 11.38, 0.867, -5.25, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -5.25}], "scale": [{"x": 1.001, "y": 1.001, "curve": [0.028, 1.001, 0.067, 1.021, 0.028, 1.001, 0.067, 1.021]}, {"time": 0.2667, "x": 1.021, "y": 1.021, "curve": [0.567, 1.021, 0.867, 1.001, 0.567, 1.021, 0.867, 1.001]}, {"time": 1.1667, "x": 1.001, "y": 1.001}]}, "neck": {"rotate": [{"value": 0.62, "curve": [0.031, 0.62, 0.076, -6.96]}, {"time": 0.3, "value": -6.96, "curve": [0.589, -6.96, 0.878, 0.62]}, {"time": 1.1667, "value": 0.62}]}, "head": {"rotate": [{"value": -0.29, "curve": [0.031, -0.29, 0.076, -8.43]}, {"time": 0.3, "value": -8.43, "curve": [0.589, -8.43, 0.878, -0.29]}, {"time": 1.1667, "value": -0.29}]}, "eyebrow_L": {"translate": [{"curve": [0.024, 0, 0.059, -2.02, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -2.02, "curve": [0.544, -2.02, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.024, 0, 0.059, -1.06]}, {"time": 0.2333, "value": -1.06, "curve": [0.544, -1.06, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.059, 0.968, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.968, "curve": [0.544, 0.968, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.024, 0, 0.059, -16.69]}, {"time": 0.2333, "value": -16.69, "curve": [0.544, -16.69, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.024, 0, 0.059, -2.02, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -2.02, "curve": [0.544, -2.02, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.024, 0, 0.059, 0.11]}, {"time": 0.2333, "value": 0.11, "curve": [0.544, 0.11, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.059, 0.968, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.968, "curve": [0.544, 0.968, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.024, 0, 0.059, 17.88]}, {"time": 0.2333, "value": 17.88, "curve": [0.544, 17.88, 0.856, 0]}, {"time": 1.1667}]}, "earring_L": {"rotate": [{"value": 5.66, "curve": [0.098, 5.66, 0.202, -5.07]}, {"time": 0.3, "value": -5.07, "curve": [0.398, -5.07, 0.502, 5.66]}, {"time": 0.6, "value": 5.66, "curve": [0.698, 5.66, 0.802, -5.07]}, {"time": 0.9, "value": -5.07, "curve": [0.998, -5.07, 1.068, 5.66]}, {"time": 1.1667, "value": 5.66}]}, "earring_R": {"rotate": [{"value": 5.66, "curve": [0.098, 5.66, 0.202, -5.07]}, {"time": 0.3, "value": -5.07, "curve": [0.398, -5.07, 0.502, 5.66]}, {"time": 0.6, "value": 5.66, "curve": [0.698, 5.66, 0.802, -5.07]}, {"time": 0.9, "value": -5.07, "curve": [0.998, -5.07, 1.068, 5.66]}, {"time": 1.1667, "value": 5.66}]}, "sh_L": {"translate": [{"x": -4.48, "curve": [0.028, -4.48, 0.067, 10.24, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 10.24, "curve": [0.567, 10.24, 0.867, -4.48, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.48}]}, "sh_R": {"translate": [{"x": -4.48, "curve": [0.028, -4.48, 0.067, 10.24, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 10.24, "curve": [0.567, 10.24, 0.867, -4.48, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.48}]}, "RU_L": {"translate": [{"x": -24.19, "curve": [0.05, -30.75, 0.085, -35.64, 0.05, 0, 0.085, 0]}, {"time": 0.1333, "x": -35.64, "curve": [0.329, -35.64, 0.538, 35.91, 0.329, 0, 0.538, 0]}, {"time": 0.7333, "x": 35.91, "curve": [0.881, 35.91, 1.021, -4.16, 0.881, 0, 1.021, 0]}, {"time": 1.1667, "x": -24.19}], "scale": [{"x": 1.007, "y": 0.997, "curve": [0.049, 0.979, 0.084, 0.952, 0.049, 1.026, 0.084, 1.055]}, {"time": 0.1333, "x": 0.952, "y": 1.055, "curve": [0.231, 0.952, 0.335, 1.062, 0.231, 1.055, 0.335, 0.938]}, {"time": 0.4333, "x": 1.062, "y": 0.938, "curve": [0.531, 1.062, 0.635, 0.952, 0.531, 0.938, 0.635, 1.055]}, {"time": 0.7333, "x": 0.952, "y": 1.055, "curve": [0.831, 0.952, 0.935, 1.062, 0.831, 1.055, 0.935, 0.938]}, {"time": 1.0333, "x": 1.062, "y": 0.938, "curve": [1.083, 1.062, 1.118, 1.034, 1.083, 0.938, 1.118, 0.967]}, {"time": 1.1667, "x": 1.007, "y": 0.997}]}, "RU_L2": {"translate": [{"x": -13.4, "curve": [0.074, -24.19, 0.16, -33.04, 0.074, 0, 0.16, 0]}, {"time": 0.2333, "x": -33.04, "curve": [0.429, -33.04, 0.604, 28.72, 0.429, 0, 0.604, 0]}, {"time": 0.8, "x": 28.72, "curve": [0.923, 28.72, 1.045, 4.71, 0.923, 0, 1.045, 0]}, {"time": 1.1667, "x": -13.4}], "scale": [{"x": 1.044, "y": 0.957, "curve": [0.073, 1.013, 0.16, 0.952, 0.073, 0.99, 0.16, 1.055]}, {"time": 0.2333, "x": 0.952, "y": 1.055, "curve": [0.331, 0.952, 0.402, 1.062, 0.331, 1.055, 0.402, 0.938]}, {"time": 0.5, "x": 1.062, "y": 0.938, "curve": [0.598, 1.062, 0.702, 0.952, 0.598, 0.938, 0.702, 1.055]}, {"time": 0.8, "x": 0.952, "y": 1.055, "curve": [0.898, 0.952, 1.002, 1.062, 0.898, 1.055, 1.002, 0.938]}, {"time": 1.1, "x": 1.062, "y": 0.938, "curve": [1.125, 1.062, 1.143, 1.055, 1.125, 0.938, 1.143, 0.946]}, {"time": 1.1667, "x": 1.044, "y": 0.957}]}, "RU_L3": {"translate": [{"x": -2.48, "curve": [0.099, -16.58, 0.203, -30.86, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -30.86, "curve": [0.496, -30.86, 0.671, 25.9, 0.496, 0, 0.671, 0]}, {"time": 0.8667, "x": 25.9, "curve": [0.965, 25.9, 1.07, 11.8, 0.965, 0, 1.07, 0]}, {"time": 1.1667, "x": -2.48}], "scale": [{"x": 1.062, "y": 0.938, "curve": [0.098, 1.062, 0.202, 0.952, 0.098, 0.938, 0.202, 1.055]}, {"time": 0.3, "x": 0.952, "y": 1.055, "curve": [0.398, 0.952, 0.502, 1.062, 0.398, 1.055, 0.502, 0.938]}, {"time": 0.6, "x": 1.062, "y": 0.938, "curve": [0.698, 1.062, 0.769, 0.952, 0.698, 0.938, 0.769, 1.055]}, {"time": 0.8667, "x": 0.952, "y": 1.055, "curve": [0.965, 0.952, 1.069, 1.062, 0.965, 1.055, 1.069, 0.938]}, {"time": 1.1667, "x": 1.062, "y": 0.938}]}, "RU_R": {"translate": [{"x": -27.64, "curve": [0.196, -27.64, 0.404, 33.71, 0.196, 0, 0.404, 0]}, {"time": 0.6, "x": 33.71, "curve": [0.796, 33.71, 0.971, -27.64, 0.796, 0, 0.971, 0]}, {"time": 1.1667, "x": -27.64}], "scale": [{"x": 0.952, "y": 1.055, "curve": [0.098, 0.952, 0.202, 1.062, 0.098, 1.055, 0.202, 0.938]}, {"time": 0.3, "x": 1.062, "y": 0.938, "curve": [0.398, 1.062, 0.502, 0.952, 0.398, 0.938, 0.502, 1.055]}, {"time": 0.6, "x": 0.952, "y": 1.055, "curve": [0.698, 0.952, 0.769, 1.062, 0.698, 1.055, 0.769, 0.938]}, {"time": 0.8667, "x": 1.062, "y": 0.938, "curve": [0.965, 1.062, 1.069, 0.952, 0.965, 0.938, 1.069, 1.055]}, {"time": 1.1667, "x": 0.952, "y": 1.055}]}, "RU_R2": {"translate": [{"x": -10.24, "curve": [0.074, -19.45, 0.16, -27.01, 0.074, 0, 0.16, 0]}, {"time": 0.2333, "x": -27.01, "curve": [0.429, -27.01, 0.604, 25.72, 0.429, 0, 0.604, 0]}, {"time": 0.8, "x": 25.72, "curve": [0.923, 25.72, 1.045, 5.22, 0.923, 0, 1.045, 0]}, {"time": 1.1667, "x": -10.24}], "scale": [{"x": 1.044, "y": 0.957, "curve": [0.073, 1.013, 0.16, 0.952, 0.073, 0.99, 0.16, 1.055]}, {"time": 0.2333, "x": 0.952, "y": 1.055, "curve": [0.331, 0.952, 0.402, 1.062, 0.331, 1.055, 0.402, 0.938]}, {"time": 0.5, "x": 1.062, "y": 0.938, "curve": [0.598, 1.062, 0.702, 0.952, 0.598, 0.938, 0.702, 1.055]}, {"time": 0.8, "x": 0.952, "y": 1.055, "curve": [0.898, 0.952, 1.002, 1.062, 0.898, 1.055, 1.002, 0.938]}, {"time": 1.1, "x": 1.062, "y": 0.938, "curve": [1.125, 1.062, 1.143, 1.055, 1.125, 0.938, 1.143, 0.946]}, {"time": 1.1667, "x": 1.044, "y": 0.957}]}, "RU_R3": {"translate": [{"x": -1.42, "curve": [0.099, -13.03, 0.203, -24.79, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -24.79, "curve": [0.496, -24.79, 0.671, 21.94, 0.496, 0, 0.671, 0]}, {"time": 0.8667, "x": 21.94, "curve": [0.965, 21.94, 1.07, 10.34, 0.965, 0, 1.07, 0]}, {"time": 1.1667, "x": -1.42}], "scale": [{"x": 1.062, "y": 0.938, "curve": [0.098, 1.062, 0.202, 0.952, 0.098, 0.938, 0.202, 1.055]}, {"time": 0.3, "x": 0.952, "y": 1.055, "curve": [0.398, 0.952, 0.502, 1.062, 0.398, 1.055, 0.502, 0.938]}, {"time": 0.6, "x": 1.062, "y": 0.938, "curve": [0.698, 1.062, 0.769, 0.952, 0.698, 0.938, 0.769, 1.055]}, {"time": 0.8667, "x": 0.952, "y": 1.055, "curve": [0.965, 0.952, 1.069, 1.062, 0.965, 1.055, 1.069, 0.938]}, {"time": 1.1667, "x": 1.062, "y": 0.938}]}, "arm_L": {"rotate": [{"value": 2.05, "curve": [0.028, 2.05, 0.067, -2.09]}, {"time": 0.2667, "value": -2.09, "curve": [0.567, -2.09, 0.867, 2.05]}, {"time": 1.1667, "value": 2.05}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{"value": -0.06}]}, "leg_L": {"translate": [{"x": 11.66, "y": -15.54, "curve": [0.024, 11.66, 0.059, -18.03, 0.024, -15.54, 0.059, 19.95]}, {"time": 0.2333, "x": -18.03, "y": 19.95, "curve": [0.544, -18.03, 0.856, 11.66, 0.544, 19.95, 0.856, -15.54]}, {"time": 1.1667, "x": 11.66, "y": -15.54}]}, "leg_L2": {"rotate": [{"value": -1.58, "curve": [0.024, -1.58, 0.059, 4.28]}, {"time": 0.2333, "value": 4.28, "curve": [0.544, 4.28, 0.856, -1.58]}, {"time": 1.1667, "value": -1.58}]}, "leg_L3": {"rotate": [{"value": -1.58, "curve": [0.024, -1.58, 0.059, 2.4]}, {"time": 0.2333, "value": 2.4, "curve": [0.544, 2.4, 0.856, -1.58]}, {"time": 1.1667, "value": -1.58}]}, "leg_L4": {"rotate": [{"value": -1.58, "curve": [0.024, -1.58, 0.059, 2.4]}, {"time": 0.2333, "value": 2.4, "curve": [0.544, 2.4, 0.856, -1.58]}, {"time": 1.1667, "value": -1.58}]}, "bone38": {"rotate": [{"value": 20.79, "curve": [0.051, 26.1, 0.085, 30.05]}, {"time": 0.1333, "value": 30.05, "curve": [0.33, 30.05, 0.536, -27.84]}, {"time": 0.7333, "value": -27.84, "curve": [0.882, -27.84, 1.02, 4.58]}, {"time": 1.1667, "value": 20.79}], "translate": [{"x": 18.52, "y": -33.85, "curve": [0.025, 19.64, 0.042, 20.49, 0.025, -35.79, 0.042, -37.26]}, {"time": 0.0667, "x": 20.49, "y": -37.26, "curve": [0.264, 20.49, 0.47, -21.43, 0.264, -37.26, 0.47, 35.4]}, {"time": 0.6667, "x": -21.43, "y": 35.4, "curve": [0.839, -21.43, 0.995, 10.58, 0.839, 35.4, 0.995, -20.07]}, {"time": 1.1667, "x": 18.52, "y": -33.85}]}, "sh_R2": {"rotate": [{}]}, "sh_R3": {"rotate": [{}]}, "arm_R3": {"translate": [{"x": -0.48, "y": 0.56, "curve": [0.028, -0.48, 0.067, -13.5, 0.028, 0.56, 0.067, 18.61]}, {"time": 0.2667, "x": -13.5, "y": 18.61, "curve": [0.567, -13.5, 0.867, -0.48, 0.567, 18.61, 0.867, 0.56]}, {"time": 1.1667, "x": -0.48, "y": 0.56}]}, "headround3": {"translate": [{"x": -6.43, "curve": [0.035, -6.43, 0.084, 257.22, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 257.22, "curve": [0.611, 257.22, 0.889, -6.43, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": -6.43}]}, "headround": {"translate": [{"y": -59.75, "curve": [0.111, 0, 0.222, 0, 0.035, -59.75, 0.084, -198.63]}, {"time": 0.3333, "y": -198.63, "curve": [0.611, 0, 0.889, 0, 0.611, -198.63, 0.889, -59.75]}, {"time": 1.1667, "y": -59.75}]}, "bodyround": {"translate": [{"x": -97.46, "y": 75.45, "curve": [0.028, -97.46, 0.067, 172.93, 0.028, 75.45, 0.067, -221.64]}, {"time": 0.2667, "x": 172.93, "y": -221.64, "curve": [0.567, 172.93, 0.867, -97.46, 0.567, -221.64, 0.867, 75.45]}, {"time": 1.1667, "x": -97.46, "y": 75.45}]}, "arm_Lb": {"rotate": [{}]}, "hand_L": {"rotate": [{"value": -1.9, "curve": [0.075, -3.12, 0.16, -4.13]}, {"time": 0.2333, "value": -4.13, "curve": [0.43, -4.13, 0.603, 2.87]}, {"time": 0.8, "value": 2.87, "curve": [0.923, 2.87, 1.044, 0.15]}, {"time": 1.1667, "value": -1.9}]}, "hand_L2": {"rotate": [{"value": -0.63, "curve": [0.1, -2.37, 0.202, -4.13]}, {"time": 0.3, "value": -4.13, "curve": [0.497, -4.13, 0.703, 2.87]}, {"time": 0.9, "value": 2.87, "curve": [0.999, 2.87, 1.069, 1.13]}, {"time": 1.1667, "value": -0.63}]}, "arm_L2": {"translate": [{"x": -10.93, "y": -17.14, "curve": [0.028, -10.93, 0.067, 26.26, 0.028, -17.14, 0.067, 12.05]}, {"time": 0.2667, "x": 26.26, "y": 12.05, "curve": [0.567, 26.26, 0.867, -10.93, 0.567, 12.05, 0.867, -17.14]}, {"time": 1.1667, "x": -10.93, "y": -17.14}]}, "leg_R4": {"translate": [{"x": -22.23, "y": 7.41, "curve": [0.024, -22.23, 0.059, 22.84, 0.024, 7.41, 0.059, -4.45]}, {"time": 0.2333, "x": 22.84, "y": -4.45, "curve": [0.544, 22.84, 0.856, -22.23, 0.544, -4.45, 0.856, 7.41]}, {"time": 1.1667, "x": -22.23, "y": 7.41}]}, "leg_R5": {"rotate": [{}]}, "leg_R": {"rotate": [{"value": -1.88, "curve": [0.038, -1.88, 0.093, 2.24]}, {"time": 0.3667, "value": 2.24, "curve": [0.633, 2.24, 0.9, -5.77]}, {"time": 1.1667, "value": -5.77}], "translate": [{"x": -28.61, "y": 9.43, "curve": [0.031, -28.61, 0.076, 43.78, 0.031, 9.43, 0.076, -10.68]}, {"time": 0.3, "x": 43.78, "y": -10.68, "curve": [0.589, 43.78, 0.878, -28.61, 0.589, -10.68, 0.878, 9.43]}, {"time": 1.1667, "x": -28.61, "y": 9.43}]}, "arm_Lb2": {"scale": [{"x": 1.074, "curve": [0.028, 1.074, 0.067, 0.856, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.856, "curve": [0.567, 0.856, 0.867, 1.074, 0.567, 1, 0.867, 1]}, {"time": 1.1667, "x": 1.074}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.024, 0, 0.06, 0.99]}, {"time": 0.2333, "vertices": [-0.89469, -0.08244, -0.89435, -0.08243, -0.71935, -0.07532, -0.71906, -0.0753, -0.4108, 0.05236, -0.41067, 0.05237, -0.14275, 0.00714, -0.14273, 0.00715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.92303, -0.08507, -0.9228, -0.08502, -1.79956, -0.12275, -1.79916, -0.12267, -2.40118, -0.02754, -2.40023, -0.02746, -2.39287, -0.15058, -2.39234, -0.15055, -1.9176, -0.21949, -1.91734, -0.21947, -1.7546, -0.10303, -1.75381, -0.10299, -1.30143, -0.04519, -1.30067, -0.04515, 0, 0, 0, 0, -0.92303, -0.08507, -0.9228, -0.08502, -1.95731, -0.09894, -1.957, -0.09887, -2.56186, -0.0195, -2.56091, -0.01941, -2.41726, -0.10872, -2.41689, -0.10865, -1.69256, -0.13793, -1.69216, -0.1379, -0.58928, -0.01822, -0.58917, -0.0182, -0.24023, -0.03569, -0.2402, -0.03567], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.024, 0, 0.06, 0.99]}, {"time": 0.2333, "vertices": [-1.68473, -0.03736, -1.68473, -0.03732, -1.83196, -0.02149, -1.83167, -0.02146, -2.31166, 0.01869, -2.31132, 0.01872, -2.32453, -0.19491, -2.32395, -0.19486, -1.75616, -0.16051, -1.75574, -0.16046, -0.68887, -0.07023, -0.68887, -0.07019, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.19164, 0.02501, -0.19161, 0.02505, -0.44017, -0.00887, -0.44015, -0.00882, -0.87064, 0.02329, -0.87064, 0.02331, -1.2367, 0.00104, -1.2367, 0.00108, -1.59975, 0.03948, -1.59975, 0.03953, 0, 0, 0, 0, -0.90632, -0.04032, -0.90632, -0.04027, -1.81796, -0.18135, -1.81754, -0.18131, -2.32453, -0.19491, -2.32395, -0.19486, -2.44242, -0.04702, -2.4421, -0.047, -2.14138, -0.05778, -2.14104, -0.05779, -1.444, -0.00371, -1.44379, -0.00372, -0.60671, 0.00398, -0.60666, 0.00397, -0.22694, -0.05461, -0.22694, -0.05461], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.024, 0, 0.06, 0.99]}, {"time": 0.2333, "offset": 512, "vertices": [-0.69854, 0.03495, -0.69849, 0.03498, -1.61246, 0.04179, -1.6124, 0.04182, -2.24036, -0.06297, -2.24002, -0.06292, -2.35403, 0.00108, -2.35369, 0.00111, -2.00757, -0.07463, -2.00712, -0.07457, -1.20706, -0.01741, -1.20682, -0.0174, -0.09801, -0.01455, -0.09798, -0.01455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.07019, -0.00481, -1.07006, -0.00479, -1.86295, 0.09323, -1.86276, 0.09327, -2.27224, 0.07481, -2.27211, 0.07486, -2.19475, 0.07093, -2.19462, 0.07097, -1.51366, 0.07575, -1.51358, 0.07579, -0.58308, 0.00972, -0.58306], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.024, 0, 0.06, 0.99]}, {"time": 0.2333, "offset": 60, "vertices": [0.38232, 0.62472, 0.38269, 0.62472, -0.04468, -0.00028, -0.04468, -0.00029, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, 0.20764, 0.00136, 0.20764, 0.00134, -0.41516, -0.00267, -0.41516, -0.00268, -0.41516, -0.00267, -0.41516, -0.00268, -0.04468, -0.00028, -0.04468, -0.00029, -0.27136, -0.98679, -0.27124, -0.98678, 0.96606, 0.5452, 0.96606, 0.5452, 0.30103, 0.29408, 0.30127, 0.29408, -0.41516, -0.00267, -0.41516, -0.00268, -0.33948, -0.00218, -0.33948, -0.00219, 0.20764, 0.00136, 0.20764, 0.00134, -0.18799, -0.00121, -0.18799, -0.00121, -0.26367, -0.00169, -0.26367, -0.0017, 0.30481, -0.29018, 0.30481, -0.29018, 0.39453, -0.89921, 0.39453, -0.8992, 1.54956, 0.46568, 1.54968, 0.46567, 1.22864, -0.20518, 1.22876, -0.20523, 0.94617, -0.20421, 0.94641, -0.20422, 1.24609, -0.09574, 1.24622, -0.09576, 1.24609, -0.09574, 1.24622, -0.09576, 0.94617, -0.20421, 0.94641, -0.20422, 1.2959, 0.00842, 1.29602, 0.00836, 1.06091, -0.8949, 1.06091, -0.8949, 0.39453, -0.89921, 0.39453, -0.8992, 0.39453, -0.89921, 0.39453, -0.8992, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.96606, 0.5452, 0.31958, 0.00206, 0.31958, 0.00206, 1.03857, -0.09709, 1.0387, -0.09711, 1.03857, -0.09709, 1.0387, -0.09711, 0.31958, 0.00206, 0.31958, 0.00206, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298, 0.00196, 0.30298], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}}}}}}