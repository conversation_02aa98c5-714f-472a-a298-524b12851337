﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Diagnostics;

namespace TFW.Map
{
    [CustomEditor(typeof(EntitySpawnLayerLogic))]
    public partial class EntitySpawnLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as EntitySpawnLayerLogic;

            mLogic.UpdateGizmoVisibilityState();

            var layerData = mLogic.layer.layerData;
            mHorizontalTileCountStr = layerData.horizontalTileCount.ToString();
            mVerticalTileCountStr = layerData.verticalTileCount.ToString();
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                EditorGUILayout.BeginHorizontal();
                mLogic.selectedOption = (EntitySpawnLayerLogic.Option)EditorGUILayout.EnumPopup(mLogic.selectedOption);

                if (mLogic.selectedIndex == -1 && mLogic.layer.layerData.regionBrushes.Count > 0)
                {
                    mLogic.selectedIndex = 0;
                }

                mLogic.brushSize = EditorGUILayout.IntField("Brush Size", mLogic.brushSize);
                mLogic.brushSize = Mathf.Clamp(mLogic.brushSize, 1, 20);
                EditorGUILayout.EndHorizontal();

                if (mLogic.selectedOption == EntitySpawnLayerLogic.Option.SetBrush)
                {
                    DrawBrushes();
                }

                //select generate strategy
                mShowStrategy = EditorGUILayout.Foldout(mShowStrategy, new GUIContent("Strategy Setting", "使用哪种策略生成npc出生点"));
                if (mShowStrategy)
                {
                    EditorGUILayout.BeginVertical("GroupBox");
                    var curStrategy = mLogic.layer.layerData.strategy.strategy;
                    var strategy = (SpawnPointGenerateStrategyType)EditorGUILayout.Popup(new GUIContent("Strategy", "刷新点生成策略"), (int)curStrategy, mStrategyTexts);
                    if (strategy != curStrategy)
                    {
                        ISpawnPointGenerationStrategy s = null;
                        if (strategy == SpawnPointGenerateStrategyType.Grid)
                        {
                            s = new GenerateSpawnPointsInGridLayout(new Vector3(4.5f, 0, 3.75f), new Vector2(9, 7.5f), 4.5f, 1.5f, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX);
                        }
                        else if (strategy == SpawnPointGenerateStrategyType.Random)
                        {
                            s = new GenerateSpawnPointsRandomly(MapCoreDef.MAP_NPC_DENSITY, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX, 4, 4);
                        }
                        else
                        {
                            UnityEngine.Debug.Assert(false, "unknown strategy!");
                            strategy = SpawnPointGenerateStrategyType.Random;
                            s = new GenerateSpawnPointsRandomly(MapCoreDef.MAP_NPC_DENSITY, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX, 4, 4);
                        }

                        if (s != null)
                        {
                            mLogic.layer.SetStrategy(s);
                        }
                    }
                    if (strategy == SpawnPointGenerateStrategyType.Random)
                    {
                        var randomStrategy = mLogic.layer.layerData.strategy as GenerateSpawnPointsRandomly;
                        float newDensity = EditorGUILayout.FloatField(new GUIContent("Density (Count = Empty Area / Density)", "设置npc出生点的密度"), randomStrategy.density);
                        newDensity = Mathf.Clamp(newDensity, 0.1f, 1000000f);
                        randomStrategy.density = newDensity;
                        EditorGUILayout.Vector2Field(new GUIContent("NPC Move Range", "设置npc在游戏中的移动范围,编辑器会在最大和最小区间内生成若干局部坐标点,npc在这些点内随机移动"), mLogic.layer.layerData.npcMoveRange);
                    }
                    else if (strategy == SpawnPointGenerateStrategyType.Grid)
                    {
                        var gridStrategy = mLogic.layer.layerData.strategy as GenerateSpawnPointsInGridLayout;
                        
                        gridStrategy.startOffset = EditorGUILayout.Vector3Field("Offset", gridStrategy.startOffset);
                        gridStrategy.pointDeltaDistance = EditorGUILayout.Vector2Field("Delta Distance Between Neighbours", gridStrategy.pointDeltaDistance);
                        gridStrategy.xOffset = EditorGUILayout.FloatField("X Offset", gridStrategy.xOffset);
                        gridStrategy.randomRange = EditorGUILayout.FloatField("Random Range", gridStrategy.randomRange);
                        EditorGUILayout.Vector2Field(new GUIContent("NPC Move Range", "设置npc在游戏中的移动范围,编辑器会在最大和最小区间内生成若干局部坐标点,npc在这些点内随机移动"), mLogic.layer.layerData.npcMoveRange);
                    }
                    EditorGUILayout.EndVertical();
                }

                mLogic.layer.layerData.spawnPointTypeReferenceNPCRegionLayerName = EditorGUILayout.TextField("Spawn Point Type Reference Layer Name", mLogic.layer.layerData.spawnPointTypeReferenceNPCRegionLayerName);

                if (GUILayout.Button(new GUIContent("Clear All Grids", "清理所有用Set Brush绘制的格子")))
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure? this operation can't be undone!", "Yes", "No"))
                    {
                        ClearAllGrids();
                    }
                }

                float mapRadius = Map.currentMap.data.isCircleMap ? Map.currentMap.mapWidth * 0.5f : 0;
                var layerData = mLogic.layer.layerData;
                if (GUILayout.Button(new GUIContent("Change Seed", "每个格子有一个随机种子,如果不改变这个种子,则在该格子内生成的点不会变"))) {
                    layerData.ChangeSeed();
                }
                if (GUILayout.Button(new GUIContent("Generate Spawn Points", "生成全地图的刷新点")))
                {
                    mLogic.layer.GenerateSpawnPoints(false, mapRadius);
                    SceneView.RepaintAll();
                }
                if (GUILayout.Button(new GUIContent("Generate Visible Region Spawn Points", "重新生成所有可见的格子的刷新点")))
                {
                    mLogic.layer.GenerateSpawnPoints(true, mapRadius);
                    SceneView.RepaintAll();
                }

                if (GUILayout.Button(new GUIContent("Hide All Spawn Points", "不显示所有的刷新点")))
                {
                    HideAllRegions();
                }
                if (GUILayout.Button(new GUIContent("Export Data", "导出该层的运行时数据")))
                {
                    var folder = EditorUtility.SaveFolderPanel("Select export folder", "", "");
                    mLogic.layer.Export(folder, false);
                }

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Width", mLogic.layer.GetTotalWidth().ToString());
                EditorGUILayout.LabelField("Height", mLogic.layer.GetTotalHeight().ToString());
                EditorGUILayout.LabelField("Horizontal Region Count", mHorizontalTileCountStr);
                EditorGUILayout.LabelField("Vertical Region Count", mVerticalTileCountStr);
                EditorGUILayout.EndVertical();
            }
        }

        void OnSceneGUI()
        {
            if (mLogic.selectedOption == EntitySpawnLayerLogic.Option.SetBrush || mLogic.selectedOption == EntitySpawnLayerLogic.Option.ClearBrush)
            {
                OnSceneGUISetClearGrid();
                return;
            }

            var layerData = mLogic.layer.layerData;
            var regions = layerData.regions;
            float regionWidth = layerData.tileWidth;
            float regionHeight = layerData.tileHeight;
            if (regions != null)
            {
                Color regionColor = new Color(0, 1, 0, 0.3f);
                Quaternion rot = Quaternion.Euler(90, 0, 0);
                for (int i = 0; i < regions.Count; ++i)
                {
                    if (regions[i].visible)
                    {
                        Handles.color = regionColor;
                        Handles.DrawSolidRectangleWithOutline(layerData.GetRegionRect(i), regionColor, regionColor);
                        Handles.color = Color.red;
                        var spawnPoints = regions[i].spawnPoints;
                        for (int p = 0; p < spawnPoints.Count; ++p)
                        {
                            Handles.RectangleHandleCap(0, new Vector3(spawnPoints[p].x, 0, spawnPoints[p].y), rot, 0.125f, EventType.Repaint);
                        }
                    }
                }
            }

            var camera = Map.currentMap.camera;
            var currentEvent = Event.current;

            var worldPos = EditorUtils.FromEditorScreenToWorldPosition(currentEvent.mousePosition, camera.firstCamera);

            if (currentEvent.type == EventType.MouseDown && currentEvent.alt == false)
            {
                if (currentEvent.button == 0)
                {
                    mLeftButtonDown = true;

                    if (mLogic.selectedOption == EntitySpawnLayerLogic.Option.EditWaypoint)
                    {
                        if (mSelectedSpawnPoint == -1)
                        {
                            PickSpawnPoint(worldPos);
                        }
                        else
                        {
                            bool pick = PickWaypoint(worldPos);
                            if (!pick)
                            {
                                mSelectedSpawnPoint = -1;
                                mSelectedWaypointIndex = -1;
                            }

                            SceneView.RepaintAll();
                        }
                    }
                }
            }

            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                mLeftButtonDown = false;
            }

            if (mLeftButtonDown)
            {
                if (mLogic.selectedOption == EntitySpawnLayerLogic.Option.EditWaypoint)
                {
                    if (mSelectedWaypointIndex >= 0)
                    {
                        MoveWaypoint(worldPos);
                    }
                }
                else if (mLogic.selectedOption == EntitySpawnLayerLogic.Option.SetVisibleRegion)
                {
                    ToggleRegionVisibility(mBrushStartX, mBrushStartY, mBrushEndX, mBrushEndY, true);
                }
                else
                {
                    ToggleRegionVisibility(mBrushStartX, mBrushStartY, mBrushEndX, mBrushEndY, false);
                }
                SceneView.RepaintAll();
            }

            if (mSelectedSpawnPoint != -1)
            {
                DrawMoveWaypoint();
            }

            if (Event.current.type == EventType.MouseMove)
            {
                SceneView.RepaintAll();
            }

            DrawBrushSize(worldPos);

            HandleUtility.AddDefaultControl(0);
        }

        void OnSceneGUISetClearGrid()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                mLeftButtonDown = false;
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            var layer = mLogic.layer.layerData;
            if (mLeftButtonDown)
            {
                if (mLogic.selectedOption == EntitySpawnLayerLogic.Option.SetBrush)
                {
                    if (mLogic.selectedIndex >= 0)
                    {
                        int type = 0;
                        if (!currentEvent.control)
                        {
                            type = layer.regionBrushes[mLogic.selectedIndex].id;
                        }
                        layer.SetGrid(pos, mLogic.brushSize, type);
                    }
                }
                else if (mLogic.selectedOption == EntitySpawnLayerLogic.Option.ClearBrush)
                {
                    layer.SetGrid(pos, mLogic.brushSize, 0);
                }
                else
                {
                    UnityEngine.Debug.Assert(false, "todo");
                }
            }

            if (currentEvent.type == EventType.KeyDown)
            {
                if (currentEvent.keyCode == KeyCode.UpArrow)
                {
                    mLogic.brushSize = mLogic.brushSize + 1;
                    Repaint();
                }
                else if (currentEvent.keyCode == KeyCode.DownArrow)
                {
                    mLogic.brushSize = mLogic.brushSize - 1;
                    Repaint();
                }
            }

            var coord = layer.FromWorldPositionToCoordinate(pos);
            DrawBrush(coord);

            SceneView.RepaintAll();
            HandleUtility.AddDefaultControl(0);
        }

        void DrawBrush(Vector2Int centerCoord)
        {
            int brushSize = mLogic.brushSize;
            int startX = centerCoord.x - brushSize / 2;
            int startY = centerCoord.y - brushSize / 2;
            int endX = startX + brushSize;
            int endY = startY + brushSize;
            var layer = mLogic.layer;
            var startPos = layer.layerData.FromCoordinateToWorldPosition(startX, startY);
            var endPos = layer.layerData.FromCoordinateToWorldPosition(endX, endY);

            Handles.color = Color.white;
            Handles.DrawWireCube((startPos + endPos) * 0.5f, endPos - startPos);
        }

        void ClearAllGrids()
        {
            var layer = mLogic.layer.layerData;
            int horizontalGridCount = layer.horizontalTileCount;
            int verticalGridCount = layer.verticalTileCount;
            for (int i = 0; i < verticalGridCount; ++i)
            {
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    layer.regionBrushIDs[i, j] = 0;
                }
            }

            mLogic.layer.layerView.RefreshTexture();
        }

        void DrawBrushSize(Vector3 worldPos)
        {
            if (mLogic.selectedOption != EntitySpawnLayerLogic.Option.EditWaypoint)
            {
                float brushSize;
                Vector3 center = GetBrushCenter(worldPos, out brushSize);
                Handles.color = Color.blue;
                Handles.RectangleHandleCap(0, center, Quaternion.Euler(90, 0, 0), brushSize, EventType.Repaint);
            }
        }

        Vector3 GetBrushCenter(Vector3 worldPos, out float brushSize)
        {
            var layerData = mLogic.layer.layerData;
            int halfSize = mLogic.brushSize / 2;
            Vector2Int regionIndex = layerData.GetRegionIndex(worldPos);
            mBrushStartX = regionIndex.x - halfSize;
            mBrushStartY = regionIndex.y - halfSize;
            mBrushEndX = mBrushStartX + mLogic.brushSize - 1;
            mBrushEndY = mBrushStartY + mLogic.brushSize - 1;
            var startPos = layerData.FromCoordinateToWorldPosition(mBrushStartX, mBrushStartY);
            var endPos = layerData.FromCoordinateToWorldPosition(mBrushEndX + 1, mBrushEndY + 1);
            brushSize = (endPos.x - startPos.x) * 0.5f;
            return (startPos + endPos) / 2;
        }

        void PickSpawnPoint(Vector3 worldPos)
        {
            bool found = mLogic.layer.layerData.PickSpawnPoint(new Vector2(worldPos.x, worldPos.z), 0.5f, out mSelectedSpawnPoint, out mSelectedSpawnPointRegion, out mSelectedSpawnPointPosition);
            if (found)
            {
                mSelectedWaypointIndex = -1;
                SceneView.RepaintAll();
            }
        }

        void ToggleRegionVisibility(int startX, int startY, int endX, int endY, bool visible)
        {
            mLogic.layer.layerData.ToggleRegionVisibility(startX, startY, endX, endY, visible);
        }

        bool PickWaypoint(Vector3 worldPos)
        {
            UnityEngine.Debug.Assert(mSelectedSpawnPoint >= 0);

            var worldPos2 = new Vector2(worldPos.x, worldPos.z);
            var moveInfo = mLogic.layer.layerData.entityMoveInfo;

            for (int i = 0; i < moveInfo.waypoints.Count; ++i)
            {
                var pos = moveInfo.waypoints[i] + mSelectedSpawnPointPosition;
                if ((pos - worldPos2).sqrMagnitude <= mWaypointDisplayRadius * mWaypointDisplayRadius)
                {
                    mSelectedWaypointIndex = i;
                    return true;
                }
            }
            return false;
        }

        void MoveWaypoint(Vector3 worldPos)
        {
            UnityEngine.Debug.Assert(mSelectedWaypointIndex >= 0);
            UnityEngine.Debug.Assert(mSelectedSpawnPoint >= 0);

            var moveInfo = mLogic.layer.layerData.entityMoveInfo;
            var moveRange = mLogic.layer.layerData.npcMoveRange;
            var localPos = new Vector2(worldPos.x, worldPos.z) - mSelectedSpawnPointPosition;
            float distance = localPos.magnitude;
            distance = Mathf.Clamp(distance, moveRange.x, moveRange.y);
            var clampedPos = localPos.normalized * distance;
            moveInfo.waypoints[mSelectedWaypointIndex] = clampedPos;

            SceneView.RepaintAll();
        }

        void DrawMoveWaypoint()
        {
            Handles.color = Color.green;
            var moveInfo = mLogic.layer.layerData.entityMoveInfo;

            for (int i = 0; i < moveInfo.waypoints.Count; ++i)
            {
                var pos = moveInfo.waypoints[i] + mSelectedSpawnPointPosition;
                if (mSelectedWaypointIndex == i)
                {
                    Handles.color = Color.blue;
                }
                else
                {
                    Handles.color = Color.green;
                }
                Handles.SphereHandleCap(0, new Vector3(pos.x, 0, pos.y), Quaternion.identity, mWaypointDisplayRadius, EventType.Repaint);
            }

            var spawnPos = new Vector3(mSelectedSpawnPointPosition.x, 0, mSelectedSpawnPointPosition.y);

            Handles.color = Color.red;
            Handles.SphereHandleCap(0, spawnPos, Quaternion.identity, mWaypointDisplayRadius, EventType.Repaint);
            Handles.color = Color.white;

            //draw move range
            var moveRange = mLogic.layer.layerData.npcMoveRange;
            var rot = Quaternion.Euler(90, 0, 0);
            Handles.CircleHandleCap(0, spawnPos, rot, moveRange.x, EventType.Repaint);
            Handles.CircleHandleCap(0, spawnPos, rot, moveRange.y, EventType.Repaint);
        }

        void HideAllRegions()
        {
            var regions = mLogic.layer.layerData.regions;
            int n = regions.Count;
            for (int i = 0; i < n; ++i)
            {
                regions[i].visible = false;
            }

            SceneView.RepaintAll();
        }

        EntitySpawnLayerLogic mLogic;
        int mSelectedSpawnPoint = -1;
        int mSelectedSpawnPointRegion = -1;
        int mSelectedWaypointIndex = -1;
        float mWaypointDisplayRadius = 0.5f;
        int mBrushStartX = -1;
        int mBrushStartY = -1;
        int mBrushEndX = -1;
        int mBrushEndY = -1;
        bool mLeftButtonDown = false;
        bool mShowBrushTemplate = true;
        bool mShowStrategy = true;
        Vector2 mSelectedSpawnPointPosition;

        string mHorizontalTileCountStr;
        string mVerticalTileCountStr;
        string[] mStrategyTexts = new string[] { "Random", "Grid" };
    }
}

#endif