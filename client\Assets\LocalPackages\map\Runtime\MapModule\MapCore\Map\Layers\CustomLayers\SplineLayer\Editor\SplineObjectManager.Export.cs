﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class SplineObjectManager
    {
        public void Export()
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.SplineLayerDataVersion);

            //-----------------version 1 start---------------------------
            int totalSegmentCount = PrepareExport();
            writer.Write(totalSegmentCount);
            for (int i = 0; i < mSplineObjects.Count; ++i)
            {
                if (!mSplineObjects[i].riverData.isRiverObject)
                {
                    var segments = mSplineObjects[i].segments;
                    Debug.Assert(segments != null);
                    for (int s = 0; s < segments.Count; ++s)
                    {
                        var seg = segments[s];
                        var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(seg.prefabPath);
                        var bounds = GameObjectBoundsCalculator.CalculateBounds(prefab, true);

                        Utils.WriteRect(writer, Utils.BoundsToRect(bounds));
                        Utils.WriteString(writer, seg.prefabPath);
                        Utils.WriteVector3(writer, seg.position);
                    }
                }
            }

            //export lod config
            ExportMapLayerLODConfig(writer, mLODConfig);

            var data = stream.ToArray();
            string path = MapCoreDef.GetFullExportedSplineDataFilePath(SLGMakerEditor.instance.exportFolder);
            File.WriteAllBytes(path, data);
            writer.Close();
        }

        void ExportMapLayerLODConfig(BinaryWriter writer, MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
                writer.Write(c.useRenderTexture);
            }
        }

        int PrepareExport()
        {
            for (int i = 0; i < mSplineObjects.Count; ++i)
            {
                if (!mSplineObjects[i].riverData.isRiverObject && mSplineObjects[i].segments == null)
                {
                    GenerateAssets();
                    break;
                }
            }

            int totalSegmentCount = 0;
            for (int i = 0; i < mSplineObjects.Count; ++i)
            {
                if (!mSplineObjects[i].riverData.isRiverObject)
                {
                    totalSegmentCount += mSplineObjects[i].segments.Count;
                }
            }
            return totalSegmentCount;
        }
    }
}

#endif