﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        bool SaveRegionColorLayer(BinaryWriter writer, RegionColorLayer layer)
        {
            BeginSection(MapDataSectionType.RegionColorLayer, writer);
            //版本号
            writer.Write(VersionSetting.RegionColorLayerStructVersion);

            //-----------------version 1 start------------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return false;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);
            writer.Write(layer.layerData.showRegionInGame);

            int layerCount = layer.layerData.layerCount;
            writer.Write(layerCount);
            for (int s = 0; s < layerCount; ++s)
            {
                var subLayer = layer.layerData.GetLayer(s);

                Utils.WriteString(writer, subLayer.name);
                writer.Write(subLayer.horizontalTileCount);
                writer.Write(subLayer.verticalTileCount);
                writer.Write(subLayer.tileWidth);
                writer.Write(subLayer.tileHeight);

                short[] gridData = Utils.ConvertToShortGrids(subLayer.gridIDs);
                writer.Write(gridData.Length);
                for (int i = 0; i < gridData.Length; ++i)
                {
                    writer.Write(gridData[i]);
                }

                //save grid templates
                var templates = subLayer.grids;
                int n = templates.Count;
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var region = templates[i] as RegionColorData;
                    Utils.WriteColor32(writer, region.color);
                    writer.Write(templates[i].id);
                }
            }
            //-----------------version 1 end------------------------------

            return true;
        }
    }
}

#endif