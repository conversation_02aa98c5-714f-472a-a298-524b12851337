﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        bool SaveSimpleBlendTerrainLayer(BinaryWriter writer, BlendTerrainLayer layer)
        {
            BeginSection(MapDataSectionType.SimpleBlendT<PERSON>rainLay<PERSON>, writer);
            //版本号
            writer.Write(VersionSetting.SimpleBlendTerrainLayerStructVersion);

            //-----------------version 1 start------------------------------
            bool useLayer = true;
            if (layer == null)
            {
                useLayer = false;
            }
            else
            {
                var terrainLayerData1 = layer.layerData as BlendTerrainLayerData;
                var groundTileAtlasSetting = terrainLayerData1.groundTileAtlasSetting;
                if (groundTileAtlasSetting != null && groundTileAtlasSetting.useTileBlock)
                {
                    useLayer = false;
                }
            }
            writer.Write(useLayer);
            if (!useLayer)
            {
                return false;
            }

            var terrainLayerData = layer.layerData as BlendTerrainLayerData;
            int horizontalTileCount = layer.layerData.horizontalTileCount;
            int verticalTileCount = layer.layerData.verticalTileCount;
            float tileWidth = layer.layerData.tileWidth;
            float tileHeight = layer.layerData.tileHeight;
            ushort[] tiles;
            string[] tilePrefabPaths;
            CalculateTilesAndPrefabs(terrainLayerData, out tiles, out tilePrefabPaths);

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);
            writer.Write(terrainLayerData.useGeneratedLOD);

            var map = Map.currentMap;

            writer.Write(verticalTileCount);
            writer.Write(horizontalTileCount);
            writer.Write(tileWidth);
            writer.Write(tileHeight);
            int n = tiles.Length;
            for (int i = 0; i < n; ++i)
            {
                writer.Write(tiles[i]);
            }
            Utils.WriteStringArray(writer, tilePrefabPaths);

            //save map layer lod config
            SaveSimpleBlendTerrainLayerLayerLODConfig(writer, layer.layerData);
            //-----------------version 1 end------------------------------
            //-----------------version 2 start------------------------------
            SaveSimpleBlendTerrainLayerLayerLODConfigV2(writer, layer.layerData);
            //-----------------version 2 end------------------------------
            //-----------------version 3 start------------------------------
            Utils.WriteVector3(writer, layer.layerView.root.transform.position);
            //-----------------version 3 end------------------------------

            return true;
        }

        //tile值为0表示空,所以tilePrefabPaths的第0项是null
        void CalculateTilesAndPrefabs(BlendTerrainLayerData layerData, out ushort[] tiles, out string[] tilePrefabPaths)
        {
            List<string> usedPrefabPaths = new List<string>();
            usedPrefabPaths.Add(null);

            int rows = layerData.verticalTileCount;
            int cols = layerData.horizontalTileCount;
            tiles = new ushort[rows * cols];
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var tile = layerData.GetTile(j, i);
                    if (tile != null)
                    {
                        var prefabPath = tile.GetAssetPath(0);
                        int idx = usedPrefabPaths.IndexOf(prefabPath);
                        if (idx < 0)
                        {
                            idx = usedPrefabPaths.Count;
                            usedPrefabPaths.Add(prefabPath);
                        }
                        Debug.Assert(idx <= ushort.MaxValue && idx >= 1);
                        tiles[i * cols + j] = (ushort)idx;
                    }
                }
            }

            tilePrefabPaths = usedPrefabPaths.ToArray();
        }

        void SaveSimpleBlendTerrainLayerLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
                writer.Write((int)c.flag);
            }
        }

        void SaveSimpleBlendTerrainLayerLayerLODConfigV2(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.terrainLODTileCount);
            }
        }
    }
}

#endif