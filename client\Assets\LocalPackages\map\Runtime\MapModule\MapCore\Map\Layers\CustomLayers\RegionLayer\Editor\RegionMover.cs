﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class RegionMover
    {
        public RegionMover(int layerID, List<RegionSelection> movedRegions, Vector3 pos)
        {
            for (int i = 0; i < movedRegions.Count; ++i)
            {
                mActions.Add(new ActionMoveRegion(layerID, movedRegions[i].regionID, pos));
            }
        }

        public void Stop(Vector3 pos)
        {
            for (int i = 0; i < mActions.Count; ++i)
            {
                mActions[i].SetEndPosition(pos);
            }
            if (mActions[0].moved)
            {
                var actions = new CompoundAction("move regions");
                for (int i = 0; i < mActions.Count; ++i)
                {
                    actions.Add(mActions[i]);
                }
                ActionManager.instance.PushAction(actions, true, false);
            }
            mActions = null;
        }

        List<ActionMoveRegion> mActions = new List<ActionMoveRegion>();
    }
}


#endif