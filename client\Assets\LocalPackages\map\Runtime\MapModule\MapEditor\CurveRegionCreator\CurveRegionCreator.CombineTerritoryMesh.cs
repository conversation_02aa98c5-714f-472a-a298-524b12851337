﻿#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public partial class CurveRegionCreator
    {
        public class BlockRegion
        {
            public int territoryID;
            public Mesh regionMesh;
        }

        public class BlockEdge
        {
            public int territoryID;
            public int neighbourTerritoryID;
            public Mesh edgeMesh;
        }

        public class Block
        {
            public int index;
            public Bounds bounds;
            public List<BlockRegion> regions = new List<BlockRegion>();
            public List<BlockEdge> edges = new List<BlockEdge>();
            public string prefabPath;
        }

        //合并区域的mesh，生成lod1
        void CombineTerritoryMesh(float totalWidth, float totalHeight, int horizontalTileCount, int verticalTileCount, bool generateAssets, string folder, int lod)
        {
            //这个texture只是用在prefab展示中，游戏运行时根据mask data来创建新的mask texture
            var maskTexture = CreateMaskTexture(folder, lod, out string maskTexturePath);
            mLOD1MaskTextureWidth = maskTexture.width;
            mLOD1MaskTextureHeight = maskTexture.height;
            mLOD1MaskTextureData = maskTexture.GetPixels32();

            mBlocks = new List<Block>(horizontalTileCount * verticalTileCount);
            HashSet<int> addedToBlock = new HashSet<int>();

            float blockWidth = totalWidth / horizontalTileCount;
            float blockHeight = totalHeight / verticalTileCount;
            int blockIndex = 0;
            for (int v = 0; v < verticalTileCount; ++v)
            {
                for (int h = 0; h < horizontalTileCount; ++h)
                {
                    float minX = blockWidth * h;
                    float minY = blockHeight * v;
                    float maxX = minX + blockWidth;
                    float maxY = minY + blockHeight;
                    var blockBounds = new Bounds(new Vector3((minX + maxX) * 0.5f, 0, (minY + maxY) * 0.5f), new Vector3(blockWidth, 0, blockHeight));

                    var block = new Block();
                    block.index = blockIndex;
                    mBlocks.Add(block);

                    for (int i = 0; i < mTerritories.Count; ++i)
                    {
                        //check region
                        var t = mTerritories[i];
                        GameObject regionObject = t.GetGameObject(Territory.ObjectType.Region);
                        if (!addedToBlock.Contains(regionObject.GetInstanceID()))
                        {
                            Mesh regionMesh = regionObject.GetComponent<MeshFilter>().sharedMesh;
                            Bounds meshBounds = regionMesh.bounds;
                            if (meshBounds.Intersects(blockBounds))
                            {
                                addedToBlock.Add(regionObject.GetInstanceID());
                                var blockRegion = new BlockRegion();
                                blockRegion.territoryID = t.regionID;
                                blockRegion.regionMesh = regionMesh;
                                block.regions.Add(blockRegion);
                            }
                        }

                        //check edge
                        var sharedEdges = t.sharedEdges;
                        for (int e = 0; e < sharedEdges.Count; ++e)
                        {
                            var obj = sharedEdges[e].gameObject;
                            if (!addedToBlock.Contains(obj.GetInstanceID()))
                            {
                                Mesh edgeMesh = obj.GetComponent<MeshFilter>().sharedMesh;
                                Bounds edgeBounds;
                                if (edgeMesh == null)
                                {
                                    var mirroredSharedEdge = GetSharedEdge(sharedEdges[e].neighbourRegionID, sharedEdges[e].selfRegionID);
                                    var mirrorEdgeMesh = mirroredSharedEdge.gameObject.GetComponent<MeshFilter>().sharedMesh;
                                    edgeBounds = mirrorEdgeMesh.bounds;
                                }
                                else
                                {
                                    edgeBounds = edgeMesh.bounds;
                                }
                                var edgeRect = Utils.BoundsToRect(edgeBounds);
                                var blockRect = Utils.BoundsToRect(blockBounds);
                                if (edgeRect.Overlaps(blockRect))
                                {
                                    addedToBlock.Add(obj.GetInstanceID());
                                    var blockEdge = new BlockEdge();
                                    blockEdge.territoryID = t.regionID;
                                    blockEdge.neighbourTerritoryID = sharedEdges[e].neighbourRegionID;
                                    blockEdge.edgeMesh = edgeMesh;
                                    block.edges.Add(blockEdge);
                                }
                            }
                        }
                    }

                    CombineBlock(block, generateAssets, folder, lod, maskTexturePath);
                    ++blockIndex;
                }
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        SharedEdgeWithNeighbourTerritroy GetSharedEdge(int territoryID, int neighbourTerritoryID)
        {
            foreach (var t in mTerritories)
            {
                foreach (var edge in t.sharedEdges)
                {
                    if (edge.selfRegionID == territoryID && edge.neighbourRegionID == neighbourTerritoryID)
                    {
                        return edge;
                    }
                }
            }
            return null;
        }

        void CombineBlock(Block block, bool generateAssets, string folder, int lod, string maskTexturePath)
        {
            var combinedMesh = new Mesh();
            CalculateBlockMeshBoundsAndCenter(block, out Vector3 center, out Bounds bounds);
            block.bounds = bounds;

            List<Vector3> positions = new List<Vector3>();
            List<Vector2> uvs = new List<Vector2>();
            List<int> indices = new List<int>();
            List<Vector3> combinedPositions = new List<Vector3>();
            List<Vector4> combinedUVs = new List<Vector4>();
            List<int> combinedIndices = new List<int>();
            //combine regions
            foreach (var region in block.regions)
            {
                int offset = combinedPositions.Count;
                int regionIndex = GetTerritoryIndex(region.territoryID);
                region.regionMesh.GetVertices(positions);
                region.regionMesh.GetUVs(0, uvs);
                if (uvs.Count == 0)
                {
                    for (int i = 0; i < positions.Count; ++i)
                    {
                        uvs.Add(Vector2.zero);
                    }
                }
                region.regionMesh.GetIndices(indices, 0);
                for (int i = 0; i < positions.Count; ++i)
                {
                    combinedPositions.Add(positions[i] - center);
                }
                for (int i = 0; i < uvs.Count; ++i)
                {
                    combinedUVs.Add(new Vector4(uvs[i].x, uvs[i].y, regionIndex, 1.0f));
                }
                for (int i = 0; i < indices.Count; ++i)
                {
                    combinedIndices.Add(indices[i] + offset);
                }
            }

            //combine edges
            foreach (var edge in block.edges)
            {
                if (edge.edgeMesh == null)
                {
                    continue;
                }
                int edgeIndex = GetEdgeIndex(edge.territoryID, edge.neighbourTerritoryID);
                int offset = combinedPositions.Count;
                edge.edgeMesh.GetVertices(positions);
                edge.edgeMesh.GetUVs(0, uvs);
                edge.edgeMesh.GetIndices(indices, 0);
                for (int i = 0; i < positions.Count; ++i)
                {
                    combinedPositions.Add(positions[i] - center);
                }
                for (int i = 0; i < uvs.Count; ++i)
                {
                    combinedUVs.Add(new Vector4(uvs[i].x, uvs[i].y * 0.5f, edgeIndex + mTerritories.Count, 0));
                }
                for (int i = 0; i < indices.Count; ++i)
                {
                    combinedIndices.Add(indices[i] + offset);
                }
            }

            combinedMesh.SetVertices(combinedPositions);
            combinedMesh.SetUVs(0, combinedUVs);
            combinedMesh.SetIndices(combinedIndices, MeshTopology.Triangles, 0);
            combinedMesh.RecalculateBounds();

            block.prefabPath = $"{folder}/combined_region_prefab_{block.index}_lod{lod}.prefab";
            if (generateAssets && !string.IsNullOrEmpty(folder) && combinedPositions.Count > 0)
            {
                string meshPath = $"{folder}/combined_region_lod{lod}_mesh_{block.index}.asset";
                AssetDatabase.CreateAsset(combinedMesh, meshPath);

                var obj = GameObject.CreatePrimitive(PrimitiveType.Cube);
                var filter = obj.GetComponent<MeshFilter>();
                filter.sharedMesh = combinedMesh;
                var renderer = obj.GetComponent<MeshRenderer>();
                renderer.sharedMaterial = mInput.settings.regionMaterial;
                if (renderer.sharedMaterial != null)
                {
                    var maskTexture = MapModuleResourceMgr.LoadTexture(maskTexturePath);
                    renderer.sharedMaterial.SetTexture("_Mask", maskTexture);
                    renderer.sharedMaterial.SetVector("_MaskTextureSize", new Vector4(maskTexture.width, maskTexture.height, 0, 0));
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "需要设置LOD1的Region Material参数, shader需要有_Mask和_MaskTextureSize", "OK");
                }

                PrefabUtility.SaveAsPrefabAssetAndConnect(obj, block.prefabPath, InteractionMode.AutomatedAction);

                Utils.DestroyObject(obj);
            }
        }

        Texture2D CreateMaskTexture(string folder, int lod, out string texturePath)
        {
            int totalPixelCount = mEdgeAssetsInfo.Count + mTerritories.Count;
            Vector2Int textureSize = Utils.CalculateSmallestPOTTextureSize(totalPixelCount);
            Texture2D texture = new Texture2D(textureSize.x, textureSize.y, TextureFormat.RGB24, false);
            texture.filterMode = FilterMode.Point;
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                int x = i % textureSize.x;
                int y = i / textureSize.x;
                texture.SetPixel(x, y, mTerritories[i].color);
            }

            for (int i = 0; i < mEdgeAssetsInfo.Count; ++i)
            {
                int pixelOffset = i + mTerritories.Count;
                int x = pixelOffset % textureSize.x;
                int y = pixelOffset / textureSize.x;
                texture.SetPixel(x, y, new Color(0, 0, 0, 0));
            }

            texture.Apply();

            texturePath = $"{folder}/combined_region_lod{lod}_mask.tga";
            byte[] bytes = texture.EncodeToTGA();
            System.IO.File.WriteAllBytes(texturePath, bytes);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            return texture;
        }

        void CalculateBlockMeshBoundsAndCenter(Block block, out Vector3 center, out Bounds bounds)
        {
            List<Vector3> positions = new List<Vector3>();
            float minX = float.MaxValue, minY = float.MaxValue, minZ = float.MaxValue;
            float maxX = float.MinValue, maxY = float.MinValue, maxZ = float.MinValue;
            //combine regions
            foreach (var region in block.regions)
            {
                var mesh = region.regionMesh;
                region.regionMesh.GetVertices(positions);
                for (int i = 0; i < positions.Count; ++i)
                {
                    var pos = positions[i];
                    if (pos.x < minX)
                    {
                        minX = pos.x;
                    }
                    if (pos.y < minY)
                    {
                        minY = pos.y;
                    }
                    if (pos.z < minZ)
                    {
                        minZ = pos.z;
                    }
                    if (pos.x > maxX)
                    {
                        maxX = pos.x;
                    }
                    if (pos.y > maxY)
                    {
                        maxY = pos.y;
                    }
                    if (pos.z > maxZ)
                    {
                        maxZ = pos.z;
                    }
                }
            }

            //combine edges
            foreach (var edge in block.edges)
            {
                if (edge.edgeMesh != null)
                {
                    edge.edgeMesh.GetVertices(positions);
                    for (int i = 0; i < positions.Count; ++i)
                    {
                        var pos = positions[i];
                        if (pos.x < minX)
                        {
                            minX = pos.x;
                        }
                        if (pos.y < minY)
                        {
                            minY = pos.y;
                        }
                        if (pos.z < minZ)
                        {
                            minZ = pos.z;
                        }
                        if (pos.x > maxX)
                        {
                            maxX = pos.x;
                        }
                        if (pos.y > maxY)
                        {
                            maxY = pos.y;
                        }
                        if (pos.z > maxZ)
                        {
                            maxZ = pos.z;
                        }
                    }
                }
            }

            center = new Vector3((minX + maxX) * 0.5f, 0, (minZ + maxZ) * 0.5f);
            bounds = new Bounds(center, new Vector3(maxX - minX, 0, maxZ - minZ));
        }

        int GetTerritoryIndex(int territoryID)
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                if (mTerritories[i].regionID == territoryID)
                {
                    return i;
                }
            }
            Debug.Assert(false, "Can't be here!");
            return -1;
        }

        int GetEdgeIndex(int territoryID, int neighbourTerritoryID)
        {
            for (int i = 0; i < mEdgeAssetsInfo.Count; ++i)
            {
                if (mEdgeAssetsInfo[i].territoryID == territoryID &&
                    mEdgeAssetsInfo[i].neighbourTerritoyID == neighbourTerritoryID)
                {
                    return i;
                }
            }
            Debug.Assert(false, "Can't be here!");
            return -1;
        }
    }
}

#endif