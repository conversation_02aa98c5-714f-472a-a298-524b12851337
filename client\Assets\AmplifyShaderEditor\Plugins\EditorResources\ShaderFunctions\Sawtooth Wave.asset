%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Sawtooth Wave
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17500\n508;100;994;700;746.5329;443.6693;1;True;False\nNode;AmplifyShaderEditor.FunctionInput;1;-591,-88;Inherit;False;In;1;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;4;-368,-16;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;3;-528,32;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FloorOpNode;2;-240,-16;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;5;-112,-80;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleNode;7;32,-80;Inherit;False;2;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;176,-80;Inherit;False;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;4;0;1;0\nWireConnection;4;1;3;0\nWireConnection;2;0;4;0\nWireConnection;5;0;1;0\nWireConnection;5;1;2;0\nWireConnection;7;0;5;0\nWireConnection;0;0;7;0\nASEEND*/\n//CHKSM=CD22F0B45C0FC6CDCBADEDB7C0D7C1285F12B773"
  m_functionName: 
  m_description: Creates a sawtooth wave from a given input
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
