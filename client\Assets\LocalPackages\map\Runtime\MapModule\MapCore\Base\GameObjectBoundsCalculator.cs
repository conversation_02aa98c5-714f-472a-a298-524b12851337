﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    //计算一个game object的bounding box
    public class GameObjectBoundsCalculator
    {
        public static Bounds CalculateBounds(GameObject obj, bool resetTransform = true)
        {
            if (System.Object.ReferenceEquals(obj, null))
            {
                return new Bounds();
            }

            var bounds = new Bounds();
            var pos = obj.transform.position;
            var rot = obj.transform.rotation;
            var scale = obj.transform.localScale;
            if (resetTransform)
            {
                obj.transform.position = Vector3.zero;
                obj.transform.rotation = Quaternion.identity;
                obj.transform.localScale = Vector3.one;
            }

            var renderers = obj.GetComponentsInChildren<Renderer>();
            if (renderers != null)
            {
                for (int i = 0; i < renderers.Length; ++i)
                {
                    if (i == 0)
                    {
                        bounds = renderers[i].bounds;
                    }
                    else
                    {
                        bounds.Encapsulate(renderers[i].bounds);
                    }
                }
            }

            var meshColliders = obj.GetComponentsInChildren<MeshCollider>();
            if (meshColliders != null)
            {
                for (int i = 0; i < meshColliders.Length; ++i)
                {
                    if (meshColliders[i].sharedMesh != null)
                    {
                        if (i == 0)
                        {
                            bounds = Utils.TransformBounds(meshColliders[i].sharedMesh.bounds, meshColliders[i].transform);
                        }
                        else
                        {
                            var worldBounds = Utils.TransformBounds(meshColliders[i].sharedMesh.bounds, meshColliders[i].transform);
                            bounds.Encapsulate(worldBounds);
                        }
                    }
                }
            }

            if (resetTransform)
            {
                obj.transform.position = pos;
                obj.transform.rotation = rot;
                obj.transform.localScale = scale;
            }

            if (bounds.extents == Vector3.zero)
            {
                bounds = new Bounds(Vector3.zero, Vector3.one * 10000);
            }

            return bounds;
        }

        public static Rect CalculateRect(GameObject obj, bool resetTransform = true)
        {
            var bounds = CalculateBounds(obj, resetTransform);
            var min = bounds.min;
            var size = bounds.size;
            return new Rect(new Vector2(min.x, min.z), new Vector2(size.x, size.z));
        }

        public static Rect CalculateRect(GameObject prefab, Quaternion rot, Vector3 scale)
        {
            var originalRotation = prefab.transform.rotation;
            var originalScale = prefab.transform.localScale;
            var originalPosition = prefab.transform.position;
            prefab.transform.rotation = rot;
            prefab.transform.localScale = scale;
            prefab.transform.position = Vector3.zero;
            var childBounds = CalculateRect(prefab, false);
            prefab.transform.rotation = originalRotation;
            prefab.transform.localScale = originalScale;
            prefab.transform.position = originalPosition;
            return childBounds;
        }
    }
}