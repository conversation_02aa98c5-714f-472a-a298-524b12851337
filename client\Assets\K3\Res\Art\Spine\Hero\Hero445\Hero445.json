{"skeleton": {"hash": "Pdy74HDKEVs", "spine": "4.2.33", "x": -333.05, "y": -27.44, "width": 598, "height": 1844.48, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 291.28, "y": 1201.38}, {"name": "ALL2", "parent": "ALL", "x": -359.51, "y": -21.09, "icon": "arrows"}, {"name": "body", "parent": "ALL2", "length": 94.14, "rotation": 97.91, "x": -1.73, "y": 8.63}, {"name": "body2", "parent": "body", "length": 245.73, "rotation": 64.74, "x": 94.14, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 81.2, "rotation": 87.17, "x": 245.73, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 174.09, "rotation": 11.79, "x": 81.2}, {"name": "eye_L", "parent": "head", "x": 56.14, "y": 0.69}, {"name": "eye_R", "parent": "head", "x": 56.02, "y": 68.43}, {"name": "eyebrow_L", "parent": "head", "length": 19.9, "rotation": 72.4, "x": 77.74, "y": -24.04}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 26.08, "rotation": 32.11, "x": 19.9}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 15.49, "rotation": 4.4, "x": 26.08}, {"name": "eyebrow_R", "parent": "head", "length": 5.93, "rotation": -79.73, "x": 81.35, "y": 93.08}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 17.75, "rotation": -29.8, "x": 5.93}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 11.37, "rotation": -7.45, "x": 17.75}, {"name": "hair_FR", "parent": "head", "x": 128.6, "y": 85.03}, {"name": "hair_F", "parent": "head", "x": 132.25, "y": 39.25}, {"name": "hair_FL", "parent": "head", "x": 128.75, "y": -23.89}, {"name": "hair_B", "parent": "head", "x": 110.1, "y": -75.47}, {"name": "hair_FL2", "parent": "hair_FL", "length": 41.86, "rotation": -164.12, "x": -12.49, "y": -5.22}, {"name": "hair_FL3", "parent": "hair_FL2", "length": 42.85, "rotation": -14.92, "x": 41.86, "color": "abe323ff"}, {"name": "hair_FL4", "parent": "hair_FL3", "length": 38.69, "rotation": -10.97, "x": 42.85, "color": "abe323ff"}, {"name": "hair_FL5", "parent": "hair_FL4", "length": 28.84, "rotation": -11.64, "x": 38.69, "color": "abe323ff"}, {"name": "hair_FL6", "parent": "hair_FL5", "length": 24.2, "rotation": -11.11, "x": 28.84, "color": "abe323ff"}, {"name": "hair_B2", "parent": "hair_B", "length": 37.3, "rotation": -162.01, "x": -13.9, "y": -4.52}, {"name": "hair_B3", "parent": "hair_B2", "length": 37.95, "rotation": -8.64, "x": 37.3, "color": "abe323ff"}, {"name": "hair_B4", "parent": "hair_B3", "length": 36.23, "rotation": -12.16, "x": 37.95, "color": "abe323ff"}, {"name": "hair_B5", "parent": "hair_B4", "length": 30.24, "rotation": -8.77, "x": 36.23, "color": "abe323ff"}, {"name": "hair_B6", "parent": "hair_B5", "length": 28.83, "rotation": -11.28, "x": 30.24, "color": "abe323ff"}, {"name": "hair_F2", "parent": "hair_F", "length": 24.48, "rotation": 176.12, "x": -8.84, "y": 1.55}, {"name": "hair_F3", "parent": "hair_F2", "length": 23.52, "rotation": 27.7, "x": 24.48, "color": "abe323ff"}, {"name": "hair_F4", "parent": "hair_F3", "length": 21.18, "rotation": 32.54, "x": 23.52, "color": "abe323ff"}, {"name": "hair_FR2", "parent": "hair_FR", "length": 42.24, "rotation": 156.48, "x": -12.55, "y": 5.65}, {"name": "hair_FR3", "parent": "hair_FR2", "length": 39.27, "rotation": 14.57, "x": 42.24, "color": "abe323ff"}, {"name": "hair_FR4", "parent": "hair_FR3", "length": 38.89, "rotation": 12.43, "x": 39.27, "color": "abe323ff"}, {"name": "hair_FR5", "parent": "hair_FR4", "length": 31.47, "rotation": 13.84, "x": 38.89, "color": "abe323ff"}, {"name": "hair_FR6", "parent": "hair_FR5", "length": 26.86, "rotation": 14.24, "x": 31.47, "color": "abe323ff"}, {"name": "sh_L", "parent": "body2", "x": 257.32, "y": -113.15, "inherit": "noScale"}, {"name": "arm_L", "parent": "sh_L", "length": 269, "rotation": -83.43, "x": -24.47, "y": -4.33, "inherit": "noRotationOrReflection"}, {"name": "arm_L2", "parent": "arm_L", "length": 251.22, "rotation": 11.46, "x": 269}, {"name": "arm_L3", "parent": "arm_L2", "length": 72.99, "rotation": -14.85, "x": 251.22}, {"name": "arm_L4", "parent": "arm_L3", "length": 55.26, "rotation": -38.03, "x": 72.99}, {"name": "sh_R", "parent": "body2", "x": 161.15, "y": 117.64, "inherit": "noScale"}, {"name": "arm_R", "parent": "sh_R", "length": 260.47, "rotation": 160.26, "x": -22.26, "y": -10.27}, {"name": "arm_R2", "parent": "arm_R", "length": 241.91, "rotation": -148.13, "x": 260.47, "inherit": "noScale"}, {"name": "arm_R3", "parent": "arm_R2", "length": 71.82, "rotation": -47.15, "x": 241.91, "inherit": "noScale"}, {"name": "arm_R4", "parent": "arm_R3", "length": 64.14, "rotation": -5.56, "x": 71.82}, {"name": "tun", "parent": "ALL2", "length": 217.39, "rotation": -73.03, "x": 1.86, "y": -14.39}, {"name": "leg_L", "parent": "tun", "x": 48.35, "y": 91.1}, {"name": "leg_R", "parent": "tun", "x": 49.78, "y": -94.79}, {"name": "leg_L2", "parent": "leg_L", "length": 484.77, "rotation": -20.45, "x": 111.24, "y": 28.58}, {"name": "leg_L3", "parent": "leg_L2", "length": 389.28, "rotation": 3.03, "x": 484.77, "inherit": "noScale"}, {"name": "leg_L4", "parent": "leg_L3", "length": 203.07, "rotation": -107.33, "x": 389.28, "inherit": "onlyTranslation"}, {"name": "leg_R2", "parent": "leg_R", "length": 400.15, "rotation": -17.64, "x": 122.14, "y": -5.89}, {"name": "leg_R3", "parent": "leg_R2", "length": 389.01, "rotation": 11.93, "x": 400.15, "inherit": "noScale"}, {"name": "foot_R", "parent": "root", "length": 50.8, "rotation": 86.05, "x": -44.71, "y": 4.31}, {"name": "foot_R2", "parent": "foot_R", "length": 135.97, "rotation": 3.93, "x": 50.8}, {"name": "RU_L", "parent": "body2", "length": 40, "rotation": 12.05, "x": 122.78, "y": -23.73}, {"name": "RU_R", "parent": "body2", "length": 40, "rotation": 12.05, "x": 63.33, "y": 114.83}, {"name": "RU_R2", "parent": "RU_R", "length": 40, "rotation": 9.04, "x": -20.05, "y": 52.29}, {"name": "RU_R3", "parent": "RU_R2", "length": 40, "x": -6.92, "y": 31.14}, {"name": "RU_L2", "parent": "RU_L", "length": 40, "rotation": 4.79, "x": -8.7, "y": 5.88}, {"name": "RU_L3", "parent": "RU_L2", "length": 40, "x": -4.57, "y": 3.22}, {"name": "tunround", "parent": "ALL2", "x": 424.74, "y": -110.19, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 424.74, "y": -186.89, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "rotation": -64.74, "x": 340.28, "y": -315.78, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "rotation": -64.74, "x": 272.87, "y": -347.58, "icon": "warning"}, {"name": "headround3", "parent": "head", "x": 422.01, "y": -2.08, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -92.39, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 340.21, "y": -94.46, "icon": "warning"}, {"name": "sh_R2", "parent": "sh_R", "length": 305.6, "rotation": 162.84, "x": -22.1, "y": -9.94}, {"name": "sh_R3", "parent": "sh_R2", "length": 287.46, "rotation": -153.12, "x": 305.6, "color": "abe323ff"}, {"name": "arm_R5", "parent": "sh_R", "rotation": -64.74, "x": -30.75, "y": 128.75, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "sh_R", "rotation": -64.74, "x": -267.42, "y": 77.72, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L5", "parent": "leg_L", "length": 875.44, "rotation": -19.06, "x": 110.31, "y": 27.98}, {"name": "leg_L6", "parent": "root", "x": 62.11, "y": 175.06, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "leg_L5", "rotation": 92.08, "x": 485.87, "y": -11.02, "color": "ff3f00ff", "icon": "ik"}, {"name": "foot_R3", "parent": "root", "x": -41.18, "y": 190.97, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R4", "parent": "leg_R", "length": 783.23, "rotation": -11.77, "x": 121.74, "y": -5.76}, {"name": "leg_R1", "parent": "leg_R4", "rotation": 84.79, "x": 397.68, "y": -41.04, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "arm_Ra", "bone": "root", "attachment": "arm_Ra"}, {"name": "arm_Rb", "bone": "root", "attachment": "arm_Rb"}, {"name": "arm_L", "bone": "root", "attachment": "arm_L"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "arm_L2", "bone": "root", "attachment": "arm_L"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "nose", "bone": "root", "attachment": "nose"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "glass", "bone": "root", "attachment": "glass"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}, {"name": "hat", "bone": "root", "attachment": "hat"}], "ik": [{"name": "arm_R", "order": 1, "bones": ["sh_R2", "sh_R3"], "target": "arm_R5", "bendPositive": false}, {"name": "arm_R1", "order": 3, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 4, "bones": ["arm_R2"], "target": "arm_R5", "compress": true, "stretch": true}, {"name": "foot_R", "order": 5, "bones": ["foot_R2"], "target": "foot_R3", "compress": true, "stretch": true}, {"name": "leg_L", "order": 7, "bones": ["leg_L5"], "target": "leg_L6", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 8, "bones": ["leg_L2"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 9, "bones": ["leg_L3"], "target": "leg_L6", "compress": true, "stretch": true}, {"name": "leg_R", "order": 11, "bones": ["leg_R4"], "target": "foot_R3", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 12, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 13, "bones": ["leg_R3"], "target": "foot_R3", "compress": true, "stretch": true}], "transform": [{"name": "arm_R3", "order": 2, "bones": ["arm_R1"], "target": "sh_R3", "rotation": -74.46, "x": 45.58, "y": -10.37, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 14, "bones": ["bodyround2"], "target": "bodyround", "y": -74.54, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "order": 15, "bones": ["sh_L"], "target": "bodyround", "rotation": 64.74, "x": -218.65, "y": 11.43, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "bones": ["sh_R"], "target": "bodyround", "rotation": 64.74, "x": -468.41, "y": 22.93, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 16, "bones": ["headround2"], "target": "headround", "x": -81.8, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 17, "bones": ["eyebrow_L"], "target": "headround", "rotation": 72.4, "x": -344.27, "y": 70.43, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 18, "bones": ["eyebrow_R"], "target": "headround", "rotation": -79.73, "x": -340.66, "y": 187.54, "mixRotate": 0, "mixX": 0.027, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 19, "bones": ["hair_F"], "target": "headround", "x": -289.77, "y": 133.72, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 20, "bones": ["hair_FL"], "target": "headround", "x": -293.26, "y": 70.57, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround", "order": 21, "bones": ["tunround2"], "target": "tunround", "y": -76.7, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 6, "bones": ["leg_L"], "target": "tunround", "rotation": -73.03, "x": -321.64, "y": 76.14, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 10, "bones": ["leg_R"], "target": "tunround", "rotation": -73.03, "x": -499.02, "y": 20.52, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "hair_B3", "order": 28, "bone": "hair_B3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B4", "order": 29, "bone": "hair_B4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B5", "order": 30, "bone": "hair_B5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B6", "order": 31, "bone": "hair_B6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F3", "order": 22, "bone": "hair_F3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F4", "order": 23, "bone": "hair_F4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL3", "order": 32, "bone": "hair_FL3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL4", "order": 33, "bone": "hair_FL4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL5", "order": 34, "bone": "hair_FL5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FL6", "order": 35, "bone": "hair_FL6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR3", "order": 24, "bone": "hair_FR3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR4", "order": 25, "bone": "hair_FR4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR5", "order": 26, "bone": "hair_FR5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_FR6", "order": 27, "bone": "hair_FR6", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.10689, 0.00182, 0.19108, 0.00258, 0.29061, 0.00399, 0.37328, 0.01315, 0.43829, 0.02827, 0.48458, 0.04916, 0.51348, 0.07399, 0.52503, 0.10713, 0.51996, 0.13904, 0.51266, 0.17215, 0.51272, 0.20282, 0.51278, 0.2335, 0.54272, 0.23818, 0.5625, 0.29132, 0.53381, 0.29683, 0.55281, 0.36184, 0.56502, 0.38556, 0.57723, 0.40928, 0.59986, 0.43752, 0.62945, 0.45891, 0.66156, 0.48054, 0.69279, 0.50321, 0.72177, 0.53024, 0.74906, 0.56175, 0.79966, 0.62884, 0.8423, 0.6939, 0.89166, 0.75756, 0.90664, 0.77687, 0.94891, 0.77807, 0.97209, 0.79893, 0.98068, 0.82053, 0.93848, 0.8218, 0.93759, 0.8369, 0.9367, 0.852, 0.95105, 0.8703, 0.97552, 0.89436, 0.99999, 0.91843, 0.98185, 0.93975, 0.94154, 0.96956, 0.87372, 0.9897, 0.8304, 0.99826, 0.74524, 0.99231, 0.66008, 0.98635, 0.60401, 0.96998, 0.62373, 0.95034, 0.66551, 0.93596, 0.71966, 0.93262, 0.72027, 0.9193, 0.72089, 0.90597, 0.69333, 0.88237, 0.69627, 0.86642, 0.71121, 0.85139, 0.72555, 0.83868, 0.69309, 0.84165, 0.67195, 0.824, 0.65081, 0.80634, 0.68309, 0.79629, 0.65736, 0.7753, 0.60401, 0.73178, 0.4959, 0.65895, 0.41703, 0.60519, 0.36009, 0.57021, 0.30316, 0.53523, 0.26493, 0.50815, 0.25324, 0.48149, 0.2451, 0.45509, 0.23872, 0.42999, 0.21527, 0.40273, 0.19182, 0.37547, 0.14893, 0.30611, 0.12733, 0.30306, 0.10738, 0.24671, 0.10962, 0.21182, 0.10378, 0.17144, 0.09775, 0.1435, 0.06297, 0.16621, 0.05419, 0.13201, 0.00807, 0.0746, 0.01186, 0.03942, 0.06283, 0.01205, 0.81126, 0.80547, 0.8239, 0.82415, 0.83064, 0.83617, 0.82811, 0.84748, 0.79355, 0.78701, 0.77718, 0.76699, 0.15276, 0.01666, 0.25201, 0.02017, 0.32142, 0.03523, 0.36661, 0.05547, 0.38437, 0.08418, 0.39244, 0.11251, 0.39567, 0.14405, 0.39244, 0.17323, 0.403, 0.23237, 0.39405, 0.20084, 0.22003, 0.23614, 0.21973, 0.20602, 0.21489, 0.1726, 0.22457, 0.14577, 0.15355, 0.12977, 0.24503, 0.11883, 0.22566, 0.08917, 0.17723, 0.06658, 0.09007, 0.05011, 0.23789, 0.29972, 0.28064, 0.37162, 0.29412, 0.39955, 0.31146, 0.42819, 0.32879, 0.45178, 0.35961, 0.4765, 0.37436, 0.50046, 0.41982, 0.29564, 0.43666, 0.3636, 0.44822, 0.39281, 0.46363, 0.42033, 0.4694, 0.44504, 0.47903, 0.47144, 0.53104, 0.48829, 0.58413, 0.51369, 0.41147, 0.53155, 0.11124, 0.11301, 0.08876, 0.08629], "triangles": [78, 79, 104, 75, 76, 74, 77, 78, 104, 76, 77, 122, 37, 46, 47, 38, 46, 37, 46, 42, 44, 43, 44, 42, 39, 46, 38, 41, 46, 39, 46, 44, 45, 41, 42, 46, 40, 41, 39, 82, 81, 31, 32, 82, 31, 52, 81, 82, 53, 54, 52, 83, 52, 82, 32, 83, 82, 33, 83, 32, 51, 83, 50, 83, 51, 52, 83, 49, 50, 48, 49, 83, 34, 48, 83, 36, 48, 35, 33, 34, 83, 35, 48, 34, 36, 37, 48, 47, 48, 37, 117, 18, 19, 117, 110, 116, 118, 117, 19, 118, 19, 20, 111, 110, 117, 111, 117, 118, 111, 63, 64, 111, 64, 110, 119, 118, 20, 119, 20, 21, 119, 21, 22, 120, 111, 118, 120, 118, 119, 62, 63, 111, 62, 111, 120, 61, 62, 120, 60, 120, 119, 61, 120, 60, 59, 60, 24, 119, 23, 60, 22, 23, 119, 24, 60, 23, 59, 24, 25, 58, 59, 25, 85, 58, 25, 26, 85, 25, 57, 58, 85, 85, 26, 27, 84, 85, 27, 56, 57, 85, 56, 85, 84, 80, 84, 27, 56, 84, 80, 27, 29, 80, 29, 27, 28, 31, 29, 30, 31, 80, 29, 54, 55, 56, 54, 56, 80, 81, 80, 31, 52, 54, 80, 81, 52, 80, 90, 6, 7, 91, 90, 7, 91, 101, 90, 8, 91, 7, 92, 91, 8, 101, 91, 92, 99, 100, 101, 99, 101, 92, 99, 73, 74, 9, 92, 8, 98, 73, 99, 93, 99, 92, 93, 92, 9, 98, 99, 93, 95, 93, 9, 95, 9, 10, 95, 97, 98, 95, 98, 93, 97, 72, 73, 97, 73, 98, 94, 95, 10, 97, 95, 94, 94, 10, 11, 96, 97, 94, 72, 97, 96, 71, 72, 96, 112, 94, 11, 112, 11, 12, 14, 112, 12, 13, 14, 12, 105, 96, 94, 105, 94, 112, 71, 96, 105, 70, 71, 105, 69, 70, 105, 113, 112, 14, 113, 14, 15, 105, 112, 113, 106, 105, 113, 69, 105, 106, 68, 69, 106, 114, 113, 15, 114, 15, 16, 106, 113, 114, 107, 106, 114, 67, 68, 106, 107, 67, 106, 114, 16, 17, 115, 114, 17, 108, 107, 114, 108, 114, 115, 66, 67, 107, 66, 107, 108, 115, 17, 18, 116, 115, 18, 108, 115, 116, 109, 108, 116, 65, 66, 108, 65, 108, 109, 117, 116, 18, 110, 109, 116, 64, 65, 109, 64, 109, 110, 99, 74, 100, 90, 5, 6, 100, 121, 101, 90, 89, 5, 121, 102, 101, 76, 121, 100, 102, 103, 89, 104, 86, 103, 87, 1, 2, 86, 1, 87, 88, 2, 3, 88, 3, 4, 87, 2, 88, 86, 104, 79, 89, 88, 4, 89, 4, 5, 103, 86, 87, 103, 87, 88, 103, 88, 89, 86, 0, 1, 86, 79, 0, 122, 77, 104, 122, 104, 103, 122, 103, 102, 121, 122, 102, 76, 122, 121, 90, 102, 89, 101, 102, 90, 74, 76, 100], "vertices": [3, 37, 4.86, 47.75, 0.4816, 4, 262.19, -65.41, 0.50752, 65, -259.75, 36.2, 0.01088, 2, 37, 11.51, 32.45, 0.69143, 4, 268.84, -80.7, 0.30857, 3, 37, 19.06, 14.22, 0.76198, 38, -46.77, 7.19, 0.05429, 4, 276.38, -98.93, 0.18374, 3, 37, 20.42, -3.24, 0.81888, 38, -38.71, 22.74, 0.11788, 4, 277.74, -116.39, 0.06324, 2, 37, 16.63, -19.26, 0.75725, 38, -27.05, 34.36, 0.24275, 2, 37, 7.71, -33.6, 0.605, 38, -11.91, 41.84, 0.395, 2, 37, -5.1, -45.97, 0.35478, 38, 5.5, 45.6, 0.64522, 2, 37, -24.47, -57.64, 0.1853, 38, 28.11, 45.3, 0.8147, 2, 37, -44.5, -65.98, 0.07177, 38, 49.53, 41.82, 0.92823, 2, 37, -65.45, -74.26, 0.02612, 38, 71.69, 37.81, 0.97388, 2, 37, -84.28, -83.16, 0.00286, 38, 92.39, 35.44, 0.99714, 1, 38, 113.08, 33.07, 1, 1, 38, 116.92, 38.6, 1, 1, 38, 153.21, 38.36, 1, 1, 38, 156.28, 32.29, 1, 1, 38, 200.56, 30.97, 1, 2, 38, 216.83, 31.53, 0.99716, 39, -44.87, 41.27, 0.00284, 2, 38, 233.11, 32.09, 0.9543, 39, -28.8, 38.58, 0.0457, 2, 38, 252.67, 34.35, 0.71011, 39, -9.18, 36.91, 0.28989, 2, 38, 267.77, 38.51, 0.36707, 39, 6.44, 37.99, 0.63293, 2, 38, 283.09, 43.15, 0.12865, 39, 22.38, 39.49, 0.87135, 2, 38, 299.09, 47.53, 0.02349, 39, 38.92, 40.6, 0.97651, 2, 38, 317.98, 51.13, 0.0001, 39, 58.16, 40.38, 0.9999, 1, 39, 80.18, 38.9, 1, 1, 39, 126.59, 34.33, 1, 1, 39, 171.21, 28.69, 1, 1, 39, 215.34, 24.61, 1, 2, 39, 228.73, 23.37, 0.98631, 40, -27.73, 16.82, 0.01369, 2, 39, 232.09, 31.07, 0.95335, 40, -26.46, 25.13, 0.04665, 2, 39, 246.98, 31.06, 0.80518, 40, -12.06, 28.93, 0.19482, 2, 39, 261.45, 28.13, 0.61935, 40, 2.68, 29.82, 0.38065, 2, 39, 259.68, 19.92, 0.48498, 40, 3.07, 21.43, 0.51502, 2, 39, 269.38, 16.58, 0.11905, 40, 13.3, 20.68, 0.88095, 2, 39, 279.07, 13.24, 0.00951, 40, 23.53, 19.94, 0.99049, 1, 40, 36.09, 22.08, 1, 2, 40, 52.68, 26.01, 0.97823, 41, -32.03, 7.97, 0.02177, 2, 40, 69.26, 29.95, 0.79871, 41, -21.39, 21.28, 0.20129, 2, 40, 83.52, 25.56, 0.48232, 41, -7.46, 26.61, 0.51768, 2, 40, 103.28, 16.47, 0.08304, 41, 13.71, 31.63, 0.91696, 2, 40, 116.19, 2.3, 0.00177, 41, 32.61, 28.43, 0.99823, 1, 41, 42.28, 24.71, 1, 1, 41, 48.6, 8.56, 1, 1, 41, 54.92, -7.59, 1, 1, 41, 52.14, -23.05, 1, 2, 40, 86.76, -45.64, 0.00268, 41, 38.97, -27.47, 0.99732, 2, 40, 77.47, -36.84, 0.03435, 41, 26.22, -26.26, 0.96565, 2, 40, 75.8, -26.01, 0.17527, 41, 18.24, -18.76, 0.82473, 2, 40, 66.78, -25.39, 0.50299, 41, 10.74, -23.83, 0.49701, 2, 40, 57.75, -24.76, 0.78529, 41, 3.25, -28.9, 0.21471, 2, 40, 41.44, -29.32, 0.97422, 41, -6.79, -42.53, 0.02578, 3, 39, 273.65, -35.06, 0.00837, 40, 30.66, -28.14, 0.98812, 41, -16, -48.25, 0.00352, 2, 39, 264.87, -29.09, 0.07015, 40, 20.64, -24.62, 0.92985, 2, 39, 257.54, -23.72, 0.27037, 40, 12.18, -21.31, 0.72963, 2, 39, 257.47, -30.45, 0.39615, 40, 13.84, -27.84, 0.60385, 2, 39, 244.77, -30.72, 0.60608, 40, 1.64, -31.35, 0.39392, 2, 39, 232.08, -30.99, 0.78867, 40, -10.56, -34.86, 0.21133, 2, 39, 227.57, -22.8, 0.8958, 40, -17.02, -28.1, 0.1042, 2, 39, 212.44, -23.24, 0.99368, 40, -31.53, -32.4, 0.00632, 1, 39, 181.07, -24.14, 1, 1, 39, 127.42, -29.19, 1, 1, 39, 87.88, -32.75, 1, 1, 39, 61.8, -36.11, 1, 2, 38, 311.86, -31.6, 0.00883, 39, 35.73, -39.48, 0.99117, 2, 38, 292.73, -37.02, 0.11155, 39, 15.9, -40.99, 0.88845, 2, 38, 274.48, -37.25, 0.40726, 39, -2.03, -37.59, 0.59274, 2, 38, 256.49, -36.79, 0.79368, 39, -19.57, -33.58, 0.20632, 2, 38, 239.41, -36.1, 0.97686, 39, -36.17, -29.5, 0.02314, 1, 38, 220.49, -38.6, 1, 1, 38, 201.57, -41.09, 1, 1, 38, 153.81, -44.14, 1, 1, 38, 151.27, -48.15, 1, 1, 38, 112.81, -47.7, 1, 3, 37, -123.86, -13.58, 0.0849, 38, 89.32, -44.55, 0.84367, 4, 133.46, -126.73, 0.07143, 4, 37, -99.55, -0.84, 0.30124, 38, 61.95, -42.56, 0.51608, 4, 157.77, -113.99, 0.17941, 65, -260.37, -78.96, 0.00328, 4, 37, -82.91, 8.34, 0.40347, 38, 42.97, -41.58, 0.14778, 4, 174.41, -104.82, 0.43794, 65, -261.57, -60, 0.01081, 4, 37, -99.79, 7.98, 0.24943, 4, 157.53, -105.17, 0.52882, 57, 16.98, -86.9, 0.19456, 65, -268.45, -75.42, 0.02719, 4, 37, -79.53, 19.47, 0.30104, 4, 177.79, -93.69, 0.4742, 57, 39.19, -79.9, 0.19381, 65, -270.19, -52.19, 0.03095, 4, 37, -48.17, 44.36, 0.31293, 4, 209.15, -68.8, 0.45921, 57, 75.05, -62.11, 0.19303, 65, -279.32, -13.21, 0.03483, 4, 37, -26.25, 53.87, 0.34193, 4, 231.07, -59.28, 0.57972, 57, 98.48, -57.38, 0.04851, 65, -278.57, 10.67, 0.02984, 3, 37, -5.14, 52.67, 0.41005, 4, 252.19, -60.48, 0.56911, 65, -268.48, 29.26, 0.02084, 1, 39, 241.35, -0.6, 1, 2, 39, 254.18, -2.15, 0.24663, 40, 3.41, -1.32, 0.75337, 1, 40, 11.63, -0.44, 1, 2, 39, 269.5, -6.25, 0.00322, 40, 19.27, -1.36, 0.99678, 1, 39, 228.34, -0.05, 1, 1, 39, 214.42, 1.07, 1, 3, 37, -0.37, 35.23, 0.68122, 4, 256.96, -77.92, 0.30402, 65, -250.67, 26.13, 0.01476, 3, 37, 5.86, 16.44, 0.77697, 4, 263.18, -96.71, 0.20832, 65, -231.02, 23.75, 0.01472, 3, 37, 2.48, -0.35, 0.92114, 4, 259.8, -113.5, 0.06479, 65, -217.28, 13.52, 0.01407, 3, 37, -6.13, -14.31, 0.79439, 38, -10.32, 18.15, 0.19215, 65, -208.33, -0.22, 0.01346, 3, 37, -22.27, -25.8, 0.58193, 38, 9.45, 19.41, 0.40429, 65, -204.81, -19.72, 0.01378, 3, 37, -38.98, -35.46, 0.30765, 38, 28.74, 18.8, 0.67851, 65, -203.22, -38.96, 0.01383, 3, 37, -58.08, -45.17, 0.17779, 38, 50.09, 16.98, 0.80873, 65, -202.58, -60.37, 0.01348, 3, 37, -76.27, -53.05, 0.07618, 38, 69.7, 14.08, 0.91133, 65, -203.22, -80.18, 0.01249, 2, 38, 109.83, 11.56, 0.98844, 65, -201.13, -120.34, 0.01156, 2, 38, 88.36, 12.25, 0.98811, 65, -202.9, -98.93, 0.01189, 2, 38, 108.23, -24.72, 0.98825, 65, -237.35, -122.9, 0.01175, 3, 37, -110.99, -31.62, 0.02821, 38, 87.9, -22.44, 0.9591, 65, -237.41, -102.44, 0.01269, 3, 37, -90.88, -21.07, 0.11283, 38, 65.25, -20.8, 0.87445, 65, -238.37, -79.75, 0.01271, 4, 37, -73.59, -15.03, 0.13169, 38, 47.37, -16.81, 0.81136, 4, 183.74, -128.18, 0.04191, 65, -236.45, -61.54, 0.01503, 4, 37, -69.76, 2.32, 0.51269, 38, 34.97, -29.53, 0.20058, 4, 187.56, -110.83, 0.26912, 65, -250.52, -50.67, 0.01761, 3, 37, -55.31, -10.89, 0.3009, 38, 29.66, -10.69, 0.68337, 65, -232.4, -43.24, 0.01573, 3, 37, -38.74, 1.17, 0.73837, 38, 9.22, -12.2, 0.24425, 65, -236.24, -23.11, 0.01737, 3, 37, -28.96, 16.39, 0.86925, 4, 228.37, -96.76, 0.11216, 65, -245.83, -7.77, 0.01859, 3, 37, -26.2, 36.77, 0.65896, 4, 231.12, -76.38, 0.31831, 65, -263.08, 3.42, 0.02273, 2, 38, 151.52, -26.15, 0.9878, 65, -233.82, -166.07, 0.0122, 2, 38, 200.99, -23.32, 0.99114, 65, -225.35, -214.89, 0.00886, 2, 38, 220.13, -22.84, 0.99337, 65, -222.68, -233.85, 0.00663, 3, 38, 239.85, -21.65, 0.98606, 39, -32.87, -15.43, 0.0099, 65, -219.25, -253.3, 0.00404, 2, 38, 256.15, -20.08, 0.87421, 39, -16.58, -17.12, 0.12579, 2, 38, 273.52, -15.93, 0.36597, 39, 1.27, -16.51, 0.63403, 2, 38, 290.02, -14.89, 0.04096, 39, 17.64, -18.77, 0.95904, 2, 38, 152.89, 9.96, 0.98926, 65, -197.79, -163.3, 0.01074, 2, 38, 199.11, 7.99, 0.99059, 65, -194.46, -209.44, 0.00941, 2, 38, 219.08, 8, 0.99314, 65, -192.17, -229.27, 0.00686, 3, 38, 237.99, 8.89, 0.98408, 39, -28.63, 14.87, 0.01293, 65, -189.12, -247.96, 0.00299, 2, 38, 254.79, 8.11, 0.90699, 39, -12.32, 10.77, 0.09301, 2, 38, 272.82, 7.95, 0.18739, 39, 5.32, 7.03, 0.81261, 2, 38, 285.36, 16.87, 0.07045, 39, 19.38, 13.28, 0.92955, 2, 38, 303.7, 25.34, 0.00682, 39, 39.04, 17.94, 0.99318, 1, 39, 39.99, -18.32, 1, 4, 37, -63.04, 14.75, 0.58394, 4, 194.28, -98.4, 0.34506, 57, 54.33, -87.95, 0.04889, 65, -258.89, -39.29, 0.02211, 4, 37, -48.53, 26.52, 0.59111, 4, 208.79, -86.63, 0.33664, 57, 70.98, -79.47, 0.04883, 65, -263.34, -21.15, 0.02342], "hull": 80, "edges": [0, 158, 4, 6, 6, 8, 10, 12, 12, 14, 22, 24, 24, 26, 26, 28, 28, 30, 48, 50, 54, 56, 56, 58, 58, 60, 72, 74, 74, 76, 76, 78, 78, 80, 84, 86, 88, 90, 90, 92, 110, 112, 138, 140, 140, 142, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 146, 148, 0, 2, 2, 4, 8, 10, 14, 16, 16, 18, 136, 138, 142, 144, 144, 146, 18, 20, 20, 22, 34, 36, 36, 38, 38, 40, 40, 42, 130, 132, 126, 128, 128, 130, 124, 126, 46, 48, 116, 118, 118, 120, 104, 106, 98, 100, 106, 108, 108, 110, 60, 62, 66, 68, 62, 64, 64, 66, 100, 102, 102, 104, 96, 98, 86, 88, 68, 70, 70, 72, 92, 94, 94, 96, 80, 82, 82, 84, 112, 114, 114, 116, 50, 52, 52, 54, 120, 122, 122, 124, 42, 44, 44, 46, 132, 134, 134, 136, 30, 32, 32, 34, 188, 190, 190, 186, 186, 184, 184, 182, 182, 180, 180, 178, 178, 176, 176, 174, 174, 172, 192, 194, 194, 196, 196, 198, 198, 200, 202, 204, 204, 206, 206, 208, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 222, 240, 210, 192, 224, 188, 244, 242], "width": 198, "height": 679}}, "arm_L2": {"arm_L": {"type": "mesh", "uvs": [0.10689, 0.00182, 0.19108, 0.00258, 0.29061, 0.00399, 0.37328, 0.01315, 0.43829, 0.02827, 0.48458, 0.04916, 0.51348, 0.07399, 0.52503, 0.10713, 0.51996, 0.13904, 0.51266, 0.17215, 0.51272, 0.20282, 0.51278, 0.2335, 0.54272, 0.23818, 0.5625, 0.29132, 0.53381, 0.29683, 0.55281, 0.36184, 0.43666, 0.3636, 0.28064, 0.37162, 0.19182, 0.37547, 0.14893, 0.30611, 0.12733, 0.30306, 0.10738, 0.24671, 0.10962, 0.21182, 0.10378, 0.17144, 0.09775, 0.1435, 0.06297, 0.16621, 0.05419, 0.13201, 0.00807, 0.0746, 0.01186, 0.03942, 0.06283, 0.01205, 0.15276, 0.01666, 0.25201, 0.02017, 0.32142, 0.03523, 0.36661, 0.05547, 0.38437, 0.08418, 0.39244, 0.11251, 0.39567, 0.14405, 0.39244, 0.17323, 0.403, 0.23237, 0.39405, 0.20084, 0.22003, 0.23614, 0.21973, 0.20602, 0.21489, 0.1726, 0.22457, 0.14577, 0.15355, 0.12977, 0.24503, 0.11883, 0.22566, 0.08917, 0.17723, 0.06658, 0.09007, 0.05011, 0.23789, 0.29972, 0.41982, 0.29564, 0.11124, 0.11301, 0.08876, 0.08629], "triangles": [28, 29, 48, 27, 28, 48, 26, 27, 52, 25, 26, 24, 34, 6, 7, 35, 34, 7, 35, 45, 34, 8, 35, 7, 36, 35, 8, 45, 35, 36, 43, 44, 45, 43, 45, 36, 43, 23, 24, 43, 24, 44, 9, 36, 8, 42, 23, 43, 37, 43, 36, 37, 36, 9, 42, 43, 37, 39, 37, 9, 39, 9, 10, 39, 41, 42, 39, 42, 37, 41, 22, 23, 41, 23, 42, 38, 39, 10, 41, 39, 38, 38, 10, 11, 40, 41, 38, 22, 41, 40, 21, 22, 40, 50, 38, 11, 50, 11, 12, 14, 50, 12, 13, 14, 12, 49, 40, 38, 49, 38, 50, 21, 40, 49, 20, 21, 49, 19, 20, 49, 16, 50, 14, 16, 14, 15, 49, 50, 16, 17, 49, 16, 19, 49, 17, 18, 19, 17, 30, 0, 1, 31, 1, 2, 30, 1, 31, 32, 2, 3, 32, 3, 4, 31, 2, 32, 30, 48, 29, 30, 29, 0, 33, 32, 4, 33, 4, 5, 47, 30, 31, 47, 31, 32, 47, 32, 33, 48, 30, 47, 34, 33, 5, 34, 5, 6, 46, 47, 33, 52, 27, 48, 52, 48, 47, 34, 46, 33, 52, 47, 46, 45, 46, 34, 51, 52, 46, 51, 46, 45, 44, 51, 45, 26, 52, 51, 26, 51, 44, 24, 26, 44], "vertices": [3, 37, 4.86, 47.75, 0.4816, 4, 262.19, -65.41, 0.50752, 65, -259.75, 36.2, 0.01088, 2, 37, 11.51, 32.45, 0.69143, 4, 268.84, -80.7, 0.30857, 3, 37, 19.06, 14.22, 0.76198, 38, -46.77, 7.19, 0.05429, 4, 276.38, -98.93, 0.18374, 3, 37, 20.42, -3.24, 0.81888, 38, -38.71, 22.74, 0.11788, 4, 277.74, -116.39, 0.06324, 2, 37, 16.63, -19.26, 0.75725, 38, -27.05, 34.36, 0.24275, 2, 37, 7.71, -33.6, 0.605, 38, -11.91, 41.84, 0.395, 2, 37, -5.1, -45.97, 0.35478, 38, 5.5, 45.6, 0.64522, 2, 37, -24.47, -57.64, 0.1853, 38, 28.11, 45.3, 0.8147, 2, 37, -44.5, -65.98, 0.07177, 38, 49.53, 41.82, 0.92823, 2, 37, -65.45, -74.26, 0.02612, 38, 71.69, 37.81, 0.97388, 2, 37, -84.28, -83.16, 0.00286, 38, 92.39, 35.44, 0.99714, 1, 38, 113.08, 33.07, 1, 1, 38, 116.92, 38.6, 1, 1, 38, 153.21, 38.36, 1, 1, 38, 156.28, 32.29, 1, 1, 38, 200.56, 30.97, 1, 2, 38, 199.11, 7.99, 0.99059, 65, -194.46, -209.44, 0.00941, 2, 38, 200.99, -23.32, 0.99114, 65, -225.35, -214.89, 0.00886, 1, 38, 201.57, -41.09, 1, 1, 38, 153.81, -44.14, 1, 1, 38, 151.27, -48.15, 1, 1, 38, 112.81, -47.7, 1, 3, 37, -123.86, -13.58, 0.0849, 38, 89.32, -44.55, 0.84367, 4, 133.46, -126.73, 0.07143, 4, 37, -99.55, -0.84, 0.30124, 38, 61.95, -42.56, 0.51608, 4, 157.77, -113.99, 0.17941, 65, -260.37, -78.96, 0.00328, 4, 37, -82.91, 8.34, 0.40347, 38, 42.97, -41.58, 0.14778, 4, 174.41, -104.82, 0.43794, 65, -261.57, -60, 0.01081, 4, 37, -99.79, 7.98, 0.24943, 4, 157.53, -105.17, 0.52882, 57, 16.98, -86.9, 0.19456, 65, -268.45, -75.42, 0.02719, 4, 37, -79.53, 19.47, 0.30104, 4, 177.79, -93.69, 0.4742, 57, 39.19, -79.9, 0.19381, 65, -270.19, -52.19, 0.03095, 4, 37, -48.17, 44.36, 0.31293, 4, 209.15, -68.8, 0.45921, 57, 75.05, -62.11, 0.19303, 65, -279.32, -13.21, 0.03483, 4, 37, -26.25, 53.87, 0.34193, 4, 231.07, -59.28, 0.57972, 57, 98.48, -57.38, 0.04851, 65, -278.57, 10.67, 0.02984, 3, 37, -5.14, 52.67, 0.41005, 4, 252.19, -60.48, 0.56911, 65, -268.48, 29.26, 0.02084, 3, 37, -0.37, 35.23, 0.68122, 4, 256.96, -77.92, 0.30402, 65, -250.67, 26.13, 0.01476, 3, 37, 5.86, 16.44, 0.77697, 4, 263.18, -96.71, 0.20832, 65, -231.02, 23.75, 0.01472, 3, 37, 2.48, -0.35, 0.92114, 4, 259.8, -113.5, 0.06479, 65, -217.28, 13.52, 0.01407, 3, 37, -6.13, -14.31, 0.79439, 38, -10.32, 18.15, 0.19215, 65, -208.33, -0.22, 0.01346, 3, 37, -22.27, -25.8, 0.58193, 38, 9.45, 19.41, 0.40429, 65, -204.81, -19.72, 0.01378, 3, 37, -38.98, -35.46, 0.30765, 38, 28.74, 18.8, 0.67851, 65, -203.22, -38.96, 0.01383, 3, 37, -58.08, -45.17, 0.17779, 38, 50.09, 16.98, 0.80873, 65, -202.58, -60.37, 0.01348, 3, 37, -76.27, -53.05, 0.07618, 38, 69.7, 14.08, 0.91133, 65, -203.22, -80.18, 0.01249, 2, 38, 109.83, 11.56, 0.98844, 65, -201.13, -120.34, 0.01156, 2, 38, 88.36, 12.25, 0.98811, 65, -202.9, -98.93, 0.01189, 2, 38, 108.23, -24.72, 0.98825, 65, -237.35, -122.9, 0.01175, 3, 37, -110.99, -31.62, 0.02821, 38, 87.9, -22.44, 0.9591, 65, -237.41, -102.44, 0.01269, 3, 37, -90.88, -21.07, 0.11283, 38, 65.25, -20.8, 0.87445, 65, -238.37, -79.75, 0.01271, 4, 37, -73.59, -15.03, 0.13169, 38, 47.37, -16.81, 0.81136, 4, 183.74, -128.18, 0.04191, 65, -236.45, -61.54, 0.01503, 4, 37, -69.76, 2.32, 0.51269, 38, 34.97, -29.53, 0.20058, 4, 187.56, -110.83, 0.26912, 65, -250.52, -50.67, 0.01761, 3, 37, -55.31, -10.89, 0.3009, 38, 29.66, -10.69, 0.68337, 65, -232.4, -43.24, 0.01573, 3, 37, -38.74, 1.17, 0.73837, 38, 9.22, -12.2, 0.24425, 65, -236.24, -23.11, 0.01737, 3, 37, -28.96, 16.39, 0.86925, 4, 228.37, -96.76, 0.11216, 65, -245.83, -7.77, 0.01859, 3, 37, -26.2, 36.77, 0.65896, 4, 231.12, -76.38, 0.31831, 65, -263.08, 3.42, 0.02273, 2, 38, 151.52, -26.15, 0.9878, 65, -233.82, -166.07, 0.0122, 2, 38, 152.89, 9.96, 0.98926, 65, -197.79, -163.3, 0.01074, 4, 37, -63.04, 14.75, 0.58394, 4, 194.28, -98.4, 0.34506, 57, 54.33, -87.95, 0.04889, 65, -258.89, -39.29, 0.02211, 4, 37, -48.53, 26.52, 0.59111, 4, 208.79, -86.63, 0.33664, 57, 70.98, -79.47, 0.04883, 65, -263.34, -21.15, 0.02342], "hull": 30, "edges": [0, 58, 4, 6, 6, 8, 10, 12, 12, 14, 22, 24, 24, 26, 26, 28, 28, 30, 38, 40, 40, 42, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 46, 48, 0, 2, 2, 4, 8, 10, 14, 16, 16, 18, 36, 38, 42, 44, 44, 46, 18, 20, 20, 22, 76, 78, 78, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 80, 82, 82, 84, 84, 86, 86, 88, 90, 92, 92, 94, 94, 96, 98, 34, 100, 32, 98, 80, 100, 76, 104, 102, 36, 34, 34, 32, 32, 30], "width": 198, "height": 679}}, "arm_Ra": {"arm_Ra": {"type": "mesh", "uvs": [0.20141, 0.94634, 0.14457, 0.98028, 0.10559, 0.99485, 0.06541, 0.9972, 0.03199, 0.98701, 0.01242, 0.96601, 0.00159, 0.93806, 0.05461, 0.78195, 0.08898, 0.71657, 0.15427, 0.63229, 0.34622, 0.41001, 0.46065, 0.28943, 0.50911, 0.23623, 0.54032, 0.18297, 0.57875, 0.11232, 0.62361, 0.05467, 0.66597, 0.0204, 0.7139, 0.00031, 0.78097, 0, 0.88123, 0.00463, 1, 0.01515, 0.96866, 0.10347, 0.92558, 0.22487, 0.88251, 0.34628, 0.81979, 0.37092, 0.76673, 0.39825, 0.71611, 0.43651, 0.66454, 0.48415, 0.54113, 0.61426, 0.34939, 0.81249, 0.27669, 0.88498, 0.08362, 0.90307, 0.20351, 0.68052, 0.28061, 0.76668, 0.17237, 0.79425, 0.38101, 0.46703, 0.5115, 0.34031, 0.56639, 0.28177, 0.60397, 0.2075, 0.64404, 0.12404, 0.69569, 0.06106, 0.76439, 0.05156, 0.84931, 0.06513, 0.46612, 0.56152, 0.59367, 0.41751, 0.64817, 0.35673, 0.68991, 0.28803, 0.71658, 0.22064, 0.76296, 0.17044, 0.83717, 0.1678], "triangles": [46, 47, 24, 25, 46, 24, 47, 48, 24, 24, 49, 23, 48, 41, 49, 22, 49, 21, 49, 42, 21, 42, 19, 21, 21, 19, 20, 23, 49, 22, 49, 41, 42, 24, 48, 49, 4, 5, 31, 5, 6, 31, 6, 7, 31, 7, 8, 34, 32, 8, 9, 2, 3, 31, 1, 2, 31, 31, 3, 4, 1, 31, 0, 31, 34, 0, 0, 34, 30, 31, 7, 34, 30, 33, 29, 30, 34, 33, 29, 43, 28, 29, 33, 43, 34, 8, 32, 34, 32, 33, 43, 33, 35, 33, 32, 35, 32, 9, 35, 9, 10, 35, 28, 44, 27, 28, 43, 44, 43, 36, 44, 43, 35, 36, 26, 27, 45, 35, 11, 36, 35, 10, 11, 27, 44, 45, 36, 37, 44, 44, 37, 45, 45, 46, 25, 45, 37, 46, 11, 12, 36, 36, 12, 37, 37, 38, 46, 46, 38, 47, 12, 13, 37, 37, 13, 38, 38, 39, 47, 13, 14, 38, 38, 14, 39, 14, 15, 39, 26, 45, 25, 42, 18, 19, 48, 40, 41, 40, 15, 16, 40, 39, 15, 41, 18, 42, 40, 17, 41, 40, 16, 17, 41, 17, 18, 39, 40, 48, 47, 39, 48], "vertices": [1, 43, 245.5, 32.39, 1, 2, 43, 263.02, 26.92, 0.98141, 44, -16.38, -21.5, 0.01859, 2, 43, 273.49, 21.62, 0.84353, 44, -22.47, -11.47, 0.15647, 2, 43, 282.03, 13.91, 0.61734, 44, -25.64, -0.41, 0.38266, 2, 43, 286.98, 5.35, 0.42916, 44, -25.31, 9.48, 0.57084, 2, 43, 287.21, -2.34, 0.31356, 44, -21.45, 16.12, 0.68644, 2, 43, 284.44, -9.49, 0.22366, 44, -15.32, 20.73, 0.77634, 1, 44, 26.24, 14.83, 1, 1, 44, 44.44, 8.97, 1, 2, 43, 199.3, -32.88, 0.25039, 44, 69.25, -4.42, 0.74961, 2, 43, 121.03, -33.52, 0.99981, 44, 135.97, -45.26, 0.00019, 2, 42, -83.52, 45.47, 3e-05, 43, 76.49, -31.77, 0.99997, 2, 42, -65.53, 38.64, 0.0112, 43, 57.24, -31.42, 0.9888, 2, 42, -49.63, 36.27, 0.06691, 43, 41.48, -34.56, 0.93309, 2, 42, -28.9, 33.89, 0.27915, 43, 21.17, -39.33, 0.72085, 2, 42, -10.34, 28.47, 0.63504, 43, 1.86, -40.49, 0.36496, 2, 42, 2.61, 21.18, 0.90313, 43, -12.78, -38, 0.09687, 2, 42, 13.02, 10.93, 0.94268, 4, 174.17, 128.57, 0.05732, 2, 42, 21.27, -6.38, 0.83896, 4, 182.42, 111.25, 0.16104, 2, 42, 32.45, -32.81, 0.40782, 4, 193.61, 84.83, 0.59218, 2, 42, 44.56, -64.66, 0.04478, 4, 205.71, 52.98, 0.95522, 2, 42, 20.69, -66.01, 0.04888, 4, 181.84, 51.62, 0.95112, 2, 42, -12.13, -67.87, 0.00111, 4, 149.02, 49.76, 0.99889, 2, 42, -44.95, -69.73, 0.00951, 4, 116.21, 47.9, 0.99049, 2, 42, -58.19, -56.15, 0.14477, 4, 102.96, 61.49, 0.85523, 2, 43, 33.9, 49.44, 0.32727, 4, 90.28, 72.28, 0.67273, 2, 43, 50.93, 45.99, 0.57845, 4, 75.42, 81.28, 0.42155, 2, 43, 69.81, 44.02, 0.80383, 4, 58.31, 89.52, 0.19617, 1, 43, 117.86, 42.15, 1, 1, 43, 191.82, 38.56, 1, 1, 43, 219.39, 36.72, 1, 2, 43, 261.65, 0.89, 0.57194, 44, -1.47, -0.13, 0.42806, 3, 43, 197.9, -14.37, 0.6684, 44, 60.65, -20.88, 0.32746, 65, -618.72, -131.37, 0.00414, 2, 43, 197.6, 16.52, 0.99554, 65, -596.66, -153, 0.00446, 2, 43, 224.38, -0.48, 0.99314, 44, 30.86, -18.67, 0.00686, 3, 43, 124.11, -16.36, 0.9864, 44, 124.29, -58.19, 0.0001, 65, -567.95, -77.78, 0.01349, 2, 43, 75.23, -12.46, 0.98564, 65, -530.63, -45.98, 0.01436, 3, 42, -68.88, 18.94, 0.00354, 43, 53.74, -11.75, 0.98099, 65, -514.93, -31.28, 0.01547, 3, 42, -47.43, 17.18, 0.05507, 43, 32.96, -17.33, 0.93083, 65, -504.18, -12.64, 0.0141, 4, 42, -23.6, 15.75, 0.35501, 43, 10.04, -24.04, 0.63136, 4, 137.56, 133.39, 0.00068, 65, -492.72, 8.31, 0.01295, 4, 42, -3, 9.14, 0.80893, 43, -11.58, -24.77, 0.08502, 4, 158.16, 126.77, 0.09432, 65, -477.95, 24.12, 0.01174, 3, 42, 7.54, -7.62, 0.71117, 4, 168.7, 110.02, 0.27482, 65, -458.3, 26.5, 0.01401, 3, 42, 14.83, -31.03, 0.37362, 4, 175.98, 86.6, 0.61159, 65, -434.02, 23.09, 0.01478, 2, 43, 123.67, 17.62, 0.98705, 65, -543.61, -101.5, 0.01295, 2, 43, 72.32, 17.86, 0.98646, 65, -507.13, -65.35, 0.01354, 4, 42, -75.91, -10.24, 0.06575, 43, 50.51, 18.09, 0.86236, 4, 85.24, 107.4, 0.05921, 65, -491.54, -50.1, 0.01268, 4, 42, -55.23, -13.68, 0.24732, 43, 29.87, 14.34, 0.53476, 4, 105.93, 103.96, 0.20489, 65, -479.6, -32.85, 0.01303, 4, 42, -36.67, -13.36, 0.35831, 43, 12.52, 7.77, 0.2352, 4, 124.48, 104.28, 0.39247, 65, -471.98, -15.94, 0.01403, 4, 42, -19.62, -19.98, 0.36771, 43, -5.77, 8.24, 0.01932, 4, 141.53, 97.66, 0.59833, 65, -458.71, -3.34, 0.01464, 3, 42, -9.96, -38.89, 0.18025, 4, 151.19, 78.75, 0.80574, 65, -437.49, -2.68, 0.01401], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 30, 32, 32, 34, 38, 40, 46, 48, 48, 50, 50, 52, 56, 58, 58, 60, 52, 54, 54, 56, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 10, 12, 8, 10, 64, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 40, 42, 66, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 42, 44, 44, 46, 98, 44], "width": 286, "height": 251}}, "arm_Rb": {"arm_Rb": {"type": "mesh", "uvs": [0.74126, 0, 0.85702, 0.0176, 0.91952, 0.03426, 0.99427, 0.07797, 0.99664, 0.09599, 0.94566, 0.09622, 0.90553, 0.08442, 0.90562, 0.10796, 0.9601, 0.10266, 0.9852, 0.11559, 0.9855, 0.13458, 0.87884, 0.17486, 0.82517, 0.19924, 0.76185, 0.23677, 0.6768, 0.26007, 0.60077, 0.26462, 0.53255, 0.2646, 0.50344, 0.29381, 0.47981, 0.32759, 0.45094, 0.33977, 0.41715, 0.45101, 0.39992, 0.57605, 0.41227, 0.71826, 0.40687, 0.78802, 0.38031, 0.83352, 0.34736, 0.86869, 0.30686, 0.89468, 0.3098, 0.96132, 0.2307, 0.98513, 0.1726, 0.99614, 0.11608, 0.99846, 0.07169, 0.99058, 0.04378, 0.9762, 0.02662, 0.95572, 0.00497, 0.87382, 0.00882, 0.76772, 0.04141, 0.66863, 0.08395, 0.59672, 0.26504, 0.29105, 0.22501, 0.26487, 0.28202, 0.20461, 0.32557, 0.16082, 0.36578, 0.16658, 0.40829, 0.13603, 0.45602, 0.10396, 0.54656, 0.04788, 0.61254, 0.01435, 0.29499, 0.25835, 0.44856, 0.31028, 0.39158, 0.24474, 0.44109, 0.2201, 0.48784, 0.20028, 0.34822, 0.18653, 0.36311, 0.32025, 0.37177, 0.28432, 0.15037, 0.90518], "triangles": [23, 35, 22, 24, 35, 23, 55, 24, 25, 25, 26, 55, 55, 26, 27, 28, 55, 27, 28, 29, 55, 29, 30, 55, 40, 41, 52, 40, 52, 49, 47, 40, 49, 39, 40, 47, 54, 47, 49, 38, 39, 47, 38, 47, 54, 49, 50, 17, 48, 54, 49, 17, 48, 49, 53, 38, 54, 53, 54, 48, 18, 48, 17, 19, 48, 18, 53, 48, 19, 20, 53, 19, 53, 20, 38, 20, 37, 38, 21, 37, 20, 37, 21, 22, 36, 37, 22, 35, 36, 22, 24, 55, 35, 35, 55, 34, 33, 34, 55, 32, 33, 55, 31, 32, 55, 31, 55, 30, 52, 41, 42, 51, 44, 45, 43, 44, 51, 42, 43, 51, 50, 42, 51, 52, 42, 50, 12, 45, 46, 13, 45, 12, 51, 45, 13, 49, 52, 50, 14, 51, 13, 15, 16, 51, 50, 51, 16, 14, 15, 51, 17, 50, 16, 6, 1, 2, 6, 2, 3, 5, 6, 3, 4, 5, 3, 7, 0, 6, 6, 0, 1, 10, 7, 8, 10, 11, 7, 9, 10, 8, 7, 12, 0, 11, 12, 7, 46, 0, 12], "vertices": [2, 46, 21.65, 41.48, 0.81834, 45, 97.39, 39.18, 0.18166, 2, 46, 40.39, 26.41, 0.98806, 45, 114.57, 22.37, 0.01194, 1, 46, 49.49, 16.02, 1, 1, 46, 57.02, -3.86, 1, 1, 46, 54.91, -9.7, 1, 1, 46, 45.53, -5.57, 1, 1, 46, 39.84, 1.44, 1, 1, 46, 36.53, -5.95, 1, 1, 46, 47.27, -8.78, 1, 1, 46, 50.05, -14.91, 1, 1, 46, 47.42, -20.89, 1, 2, 46, 22.18, -24.73, 0.93141, 45, 91.5, -26.76, 0.06859, 2, 46, 8.9, -27.95, 0.63842, 45, 77.97, -28.68, 0.36158, 2, 46, -8, -34.5, 0.19372, 45, 60.51, -33.56, 0.80628, 2, 46, -26.88, -34.79, 0.02035, 45, 41.69, -32.02, 0.97965, 2, 45, 27.65, -25.79, 0.98014, 44, 241.83, -37.81, 0.01986, 2, 45, 15.76, -18.97, 0.7758, 44, 238.73, -24.45, 0.2242, 2, 45, 5.69, -24.78, 0.29477, 44, 227.63, -21.03, 0.70523, 2, 45, -4.21, -32.5, 0.05608, 44, 215.25, -19.03, 0.94392, 2, 45, -11.33, -33.26, 0.01154, 44, 209.85, -14.33, 0.98846, 1, 44, 171.08, -16.38, 1, 2, 44, 128.43, -22.75, 0.99324, 43, 139.33, -48.64, 0.00676, 2, 44, 81.38, -36.25, 0.45497, 43, 172.17, -12.29, 0.54503, 1, 43, 189.9, 3.91, 1, 1, 43, 204.75, 11.2, 1, 1, 43, 217.98, 15.07, 1, 1, 43, 230.06, 15.64, 1, 1, 43, 245.85, 32.27, 1, 2, 44, -16.22, -21.49, 0.00997, 43, 262.89, 26.82, 0.99003, 2, 44, -22.55, -10.97, 0.13302, 43, 273.82, 21.24, 0.86698, 2, 44, -25.89, -0.08, 0.34265, 43, 282.42, 13.77, 0.65735, 2, 44, -25.28, 9.22, 0.52553, 43, 286.82, 5.54, 0.47447, 2, 44, -21.73, 15.8, 0.66387, 43, 287.28, -1.92, 0.33613, 2, 44, -15.65, 20.76, 0.79754, 43, 284.74, -9.34, 0.20246, 1, 44, 10.78, 31.38, 1, 1, 44, 46.48, 38.89, 1, 1, 44, 81.13, 40.23, 1, 1, 44, 107.14, 37.5, 1, 1, 44, 217.71, 25.86, 1, 2, 45, -37.94, 11.66, 0.00687, 44, 224.65, 35.74, 0.99313, 2, 45, -17.69, 23.96, 0.27988, 44, 247.42, 29.27, 0.72012, 2, 45, -2.61, 32.69, 0.60681, 44, 264.06, 24.15, 0.39319, 2, 45, 3.42, 26.95, 0.74581, 44, 263.96, 15.83, 0.25419, 3, 46, -58.58, 26.27, 4e-05, 45, 16.05, 31.82, 0.96739, 44, 276.12, 9.89, 0.03257, 3, 46, -45.31, 32.4, 0.0131, 45, 29.86, 36.63, 0.98677, 44, 289.03, 3.04, 0.00013, 2, 46, -20.79, 42.52, 0.20924, 45, 55.24, 44.33, 0.79076, 2, 46, -3.97, 47.59, 0.44718, 45, 72.48, 47.76, 0.55282, 2, 45, -24.61, 6.62, 0.02722, 44, 230.02, 22.54, 0.97278, 2, 45, -6.7, -24.22, 0.0556, 44, 219.62, -11.57, 0.9444, 2, 45, -5.44, 1.04, 0.12252, 44, 238.97, 4.69, 0.87748, 1, 45, 7.41, 3.45, 1, 1, 45, 18.95, 4.7, 1, 2, 45, -3.06, 22.75, 0.59404, 44, 256.48, 17.72, 0.40596, 1, 44, 212.39, 4.39, 1, 1, 44, 224.82, 5.49, 1, 1, 44, 6.89, 0.47, 1], "hull": 47, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 36, 38, 40, 42, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 82, 84, 90, 92, 46, 48, 48, 50, 42, 44, 44, 46, 38, 40, 88, 90, 84, 86, 86, 88, 78, 80, 80, 82, 32, 34, 34, 36, 28, 30, 30, 32, 94, 108, 108, 96, 0, 2, 22, 24, 24, 26, 92, 0], "width": 201, "height": 344}}, "body": {"body": {"type": "mesh", "uvs": [0.53073, 0.01179, 0.73638, 0.00328, 0.77319, 0.00176, 0.81001, 0.00023, 0.84609, 0.01187, 0.88913, 0.02791, 0.92175, 0.04434, 0.95025, 0.06098, 0.96149, 0.07605, 0.95911, 0.09238, 0.94828, 0.10131, 0.8999, 0.11025, 0.86272, 0.11314, 0.81837, 0.12066, 0.79793, 0.11981, 0.80673, 0.13035, 0.87384, 0.1362, 0.87814, 0.13768, 0.93965, 0.14224, 0.97155, 0.14773, 0.98994, 0.15492, 0.99512, 0.1654, 0.99209, 0.17484, 0.97393, 0.19147, 0.94655, 0.20749, 0.9158, 0.22236, 0.86903, 0.23867, 0.82798, 0.2498, 0.77253, 0.26294, 0.70636, 0.2792, 0.6748, 0.2937, 0.6963, 0.30495, 0.73798, 0.31337, 0.80985, 0.32355, 0.86611, 0.3354, 0.89633, 0.33726, 0.90169, 0.34123, 0.94497, 0.34879, 0.99193, 0.35769, 1, 0.36392, 0.95339, 0.39461, 0.87957, 0.4432, 0.80022, 0.45376, 0.72136, 0.46018, 0.68126, 0.46121, 0.67762, 0.47578, 0.67062, 0.49211, 0.65653, 0.50847, 0.61232, 0.55409, 0.57224, 0.60085, 0.55806, 0.62813, 0.55296, 0.64275, 0.54773, 0.65773, 0.5407, 0.67358, 0.54154, 0.6843, 0.55049, 0.69415, 0.57463, 0.7082, 0.6051, 0.72735, 0.61636, 0.74744, 0.61665, 0.75938, 0.60465, 0.78785, 0.59615, 0.8363, 0.59932, 0.86084, 0.60003, 0.87227, 0.60832, 0.88384, 0.61945, 0.89307, 0.65378, 0.90582, 0.66202, 0.9111, 0.66131, 0.92117, 0.64444, 0.93836, 0.64591, 0.94769, 0.64695, 0.95838, 0.6507, 0.96809, 0.65678, 0.98326, 0.6539, 0.98889, 0.64522, 0.99259, 0.6285, 0.99525, 0.60057, 0.99786, 0.56578, 0.99954, 0.4862, 0.9994, 0.4472, 0.99623, 0.43985, 0.99367, 0.43283, 0.99011, 0.42949, 0.98611, 0.42954, 0.97927, 0.45054, 0.96651, 0.46463, 0.95857, 0.47227, 0.94998, 0.47686, 0.94004, 0.48319, 0.9278, 0.48706, 0.90992, 0.48614, 0.89685, 0.48387, 0.88451, 0.48231, 0.87573, 0.47286, 0.8662, 0.43542, 0.84127, 0.3247, 0.77471, 0.29232, 0.75317, 0.28056, 0.74009, 0.27963, 0.72072, 0.29198, 0.70453, 0.29227, 0.69167, 0.28264, 0.68075, 0.27093, 0.66879, 0.27132, 0.6577, 0.26965, 0.64331, 0.25868, 0.62772, 0.22498, 0.6007, 0.17957, 0.54321, 0.1636, 0.50138, 0.16936, 0.47577, 0.18992, 0.45233, 0.21077, 0.43598, 0.23705, 0.41975, 0.2276, 0.39793, 0.20665, 0.39341, 0.22428, 0.3672, 0.23516, 0.36478, 0.24078, 0.34901, 0.25893, 0.33578, 0.26774, 0.32344, 0.25701, 0.31326, 0.2164, 0.29792, 0.18034, 0.28061, 0.1704, 0.26576, 0.15971, 0.25037, 0.08989, 0.24566, 0.03999, 0.23498, 0.0082, 0.22118, 0, 0.20579, 0.00849, 0.19015, 0.05432, 0.17376, 0.12541, 0.16178, 0.21046, 0.15514, 0.33465, 0.15304, 0.3781, 0.14957, 0.42679, 0.14335, 0.48323, 0.13397, 0.52596, 0.13325, 0.56773, 0.13113, 0.60357, 0.12272, 0.58816, 0.1235, 0.55375, 0.12172, 0.52043, 0.12122, 0.484, 0.11211, 0.46168, 0.10609, 0.43667, 0.09634, 0.41887, 0.08429, 0.4088, 0.06976, 0.409, 0.05572, 0.41924, 0.04218, 0.43852, 0.02731, 0.46355, 0.01457, 0.62509, 0.11659, 0.60266, 0.07902, 0.79389, 0.1073, 0.81632, 0.06859, 0.60988, 0.09111, 0.61748, 0.10385, 0.79979, 0.0933, 0.80569, 0.08316, 0.56733, 0.10769, 0.58401, 0.1156, 0.53762, 0.09435, 0.51779, 0.08298, 0.49781, 0.07019, 0.49253, 0.05691, 0.49313, 0.04407, 0.50694, 0.02878, 0.57807, 0.06612, 0.5564, 0.05474, 0.53454, 0.04327, 0.82169, 0.0533, 0.80528, 0.03651, 0.77843, 0.0208, 0.86515, 0.10105, 0.88156, 0.09003, 0.88827, 0.07776, 0.89051, 0.06223, 0.87709, 0.04526, 0.85173, 0.03149, 0.81071, 0.01572, 0.04932, 0.20519, 0.59688, 0.20897, 0.07075, 0.18186, 0.12285, 0.1929, 0.12322, 0.21705, 0.066, 0.22759, 0.22699, 0.16471, 0.28085, 0.18394, 0.41086, 0.18724, 0.25993, 0.21932, 0.39842, 0.21831, 0.47463, 0.23507, 0.58432, 0.24251, 0.70286, 0.23755, 0.7697, 0.21856, 0.77162, 0.19005, 0.69972, 0.16771, 0.58099, 0.16488, 0.48506, 0.17295, 0.19172, 0.2394, 0.23156, 0.24755, 0.31808, 0.23549, 0.39996, 0.23604, 0.49402, 0.24651, 0.59717, 0.2514, 0.70435, 0.24781, 0.79078, 0.23415, 0.82786, 0.21702, 0.83933, 0.20227, 0.83524, 0.1885, 0.81321, 0.16622, 0.70838, 0.15398, 0.59993, 0.15597, 0.51149, 0.16134, 0.4156, 0.15594, 0.64161, 0.14553, 0.77582, 0.14181, 0.81436, 0.15241, 0.57781, 0.14444, 0.51866, 0.14005, 0.34007, 0.19058, 0.32902, 0.2143, 0.65959, 0.1868, 0.69642, 0.20332, 0.68342, 0.22141, 0.6336, 0.23058, 0.49823, 0.21564, 0.50472, 0.19519, 0.56754, 0.18418, 0.5475, 0.22914, 0.46102, 0.14891, 0.49599, 0.15212, 0.54828, 0.14891, 0.34801, 0.16734, 0.45026, 0.17108, 0.33557, 0.26427, 0.34764, 0.28313, 0.3608, 0.30464, 0.38165, 0.32243, 0.38823, 0.33836, 0.40497, 0.35856, 0.43568, 0.37555, 0.24371, 0.2691, 0.34873, 0.37585, 0.32599, 0.35912, 0.30734, 0.33841, 0.30405, 0.32407, 0.29308, 0.3084, 0.27004, 0.29114, 0.54822, 0.36736, 0.53264, 0.35286, 0.51948, 0.33321, 0.49644, 0.31781, 0.48876, 0.30241, 0.48876, 0.28515, 0.49096, 0.26736, 0.59627, 0.27659, 0.5853, 0.28986, 0.59078, 0.30128, 0.61711, 0.31509, 0.65015, 0.33285, 0.67964, 0.35094, 0.87659, 0.3611, 0.78346, 0.33781, 0.71267, 0.32201, 0.48005, 0.40674, 0.53415, 0.43207, 0.61158, 0.45209, 0.35568, 0.40518, 0.36224, 0.42236, 0.47311, 0.44106, 0.58159, 0.45856, 0.60935, 0.39757, 0.65585, 0.41368, 0.64716, 0.4335, 0.66767, 0.45065, 0.97497, 0.35818, 0.9221, 0.35826, 0.80043, 0.36959, 0.35035, 0.43232, 0.31528, 0.45373, 0.30678, 0.47918, 0.3084, 0.53478, 0.32377, 0.58786, 0.46877, 0.58738, 0.50195, 0.536, 0.53407, 0.49404, 0.53268, 0.46783, 0.331, 0.62082, 0.33355, 0.63687, 0.33355, 0.65353, 0.33738, 0.66989, 0.34885, 0.68471, 0.36288, 0.69983, 0.47381, 0.621, 0.46998, 0.63643, 0.46615, 0.65186, 0.46105, 0.67038, 0.45723, 0.68427, 0.4585, 0.69939, 0.35996, 0.74211, 0.52036, 0.8381, 0.53397, 0.74138, 0.36752, 0.71672, 0.48029, 0.71536, 0.49663, 0.96603, 0.59632, 0.96672, 0.49521, 0.97465, 0.59276, 0.97534, 0.55502, 0.95776, 0.55715, 0.94742, 0.56143, 0.93552, 0.56428, 0.92553, 0.48408, 0.98482, 0.57511, 0.98559, 0.85817, 0.15886, 0.86448, 0.17778, 0.87849, 0.18749, 0.85704, 0.19246, 0.88802, 0.1477, 0.9242, 0.15739, 0.93407, 0.17279, 0.93298, 0.18699, 0.66547, 0.11787, 0.66322, 0.10445, 0.66097, 0.08994, 0.65839, 0.0763, 0.73735, 0.07244, 0.7389, 0.07742, 0.73515, 0.0903, 0.72766, 0.11769, 0.73195, 0.10532, 0.60917, 0.13291, 0.69633, 0.13179, 0.74059, 0.14279, 0.53621, 0.14136, 0.75304, 0.37768, 0.69462, 0.39414, 0.83893, 0.14163, 0.8594, 0.13768, 0.73333, 0.40001, 0.73306, 0.43123, 0.84595, 0.39272], "triangles": [309, 85, 307, 310, 307, 308, 310, 308, 72, 309, 307, 310, 84, 85, 309, 310, 72, 73, 315, 84, 309, 316, 309, 310, 316, 310, 73, 315, 309, 316, 83, 84, 315, 74, 316, 73, 82, 83, 315, 74, 76, 316, 81, 82, 315, 75, 76, 74, 80, 81, 315, 77, 316, 76, 78, 79, 315, 316, 78, 315, 80, 315, 79, 77, 78, 316, 91, 64, 65, 90, 91, 65, 67, 314, 66, 314, 90, 65, 314, 65, 66, 68, 314, 67, 89, 90, 314, 313, 89, 314, 69, 314, 68, 313, 314, 69, 88, 89, 313, 312, 88, 313, 312, 313, 69, 312, 69, 70, 87, 88, 312, 311, 87, 312, 307, 86, 87, 311, 307, 87, 85, 86, 307, 311, 70, 71, 70, 311, 312, 308, 311, 71, 307, 311, 308, 308, 71, 72, 53, 299, 52, 102, 103, 293, 299, 294, 293, 300, 299, 53, 300, 53, 54, 300, 294, 299, 102, 293, 294, 101, 102, 294, 301, 300, 54, 301, 54, 55, 295, 294, 300, 295, 300, 301, 101, 294, 295, 100, 101, 295, 306, 301, 55, 306, 55, 56, 305, 295, 301, 305, 301, 306, 100, 295, 305, 99, 100, 305, 302, 98, 99, 57, 304, 306, 57, 306, 56, 305, 302, 99, 304, 57, 58, 97, 98, 302, 96, 97, 302, 59, 60, 304, 59, 304, 58, 302, 304, 303, 306, 302, 305, 61, 303, 60, 306, 304, 302, 304, 60, 303, 303, 96, 302, 95, 96, 303, 303, 61, 62, 94, 95, 303, 94, 303, 62, 94, 62, 63, 93, 94, 63, 92, 93, 63, 92, 63, 64, 91, 92, 64, 281, 113, 271, 112, 113, 281, 282, 112, 281, 111, 112, 282, 283, 111, 282, 110, 111, 283, 45, 288, 289, 109, 110, 283, 282, 281, 272, 47, 288, 46, 288, 284, 283, 109, 283, 284, 288, 283, 289, 289, 283, 282, 287, 288, 47, 287, 284, 288, 108, 109, 284, 48, 287, 47, 286, 284, 287, 286, 287, 48, 285, 284, 286, 108, 284, 285, 107, 108, 285, 49, 286, 48, 290, 285, 286, 107, 285, 290, 296, 286, 49, 290, 286, 296, 106, 107, 290, 50, 296, 49, 297, 290, 296, 297, 296, 50, 291, 290, 297, 106, 290, 291, 51, 297, 50, 105, 106, 291, 298, 291, 297, 298, 297, 51, 292, 291, 298, 105, 291, 292, 104, 105, 292, 52, 298, 51, 299, 293, 292, 104, 292, 293, 103, 104, 293, 298, 299, 292, 299, 298, 52, 118, 119, 247, 118, 247, 246, 117, 118, 246, 245, 114, 116, 115, 116, 114, 117, 246, 245, 245, 116, 117, 114, 245, 270, 271, 113, 114, 271, 114, 270, 342, 338, 344, 278, 37, 38, 279, 36, 37, 279, 37, 278, 264, 36, 279, 344, 280, 264, 39, 40, 279, 264, 279, 40, 344, 264, 40, 278, 38, 39, 39, 279, 278, 41, 344, 40, 41, 343, 344, 42, 343, 41, 338, 280, 344, 266, 31, 32, 261, 31, 266, 262, 261, 266, 33, 266, 32, 265, 33, 34, 265, 266, 33, 263, 262, 266, 263, 266, 265, 36, 264, 34, 36, 34, 35, 265, 34, 264, 280, 265, 264, 263, 265, 280, 338, 263, 280, 343, 342, 344, 281, 271, 272, 271, 270, 267, 272, 271, 267, 246, 247, 241, 245, 246, 242, 245, 242, 243, 241, 240, 254, 246, 241, 242, 247, 240, 241, 253, 254, 261, 253, 261, 262, 241, 254, 253, 252, 253, 262, 252, 262, 263, 242, 241, 253, 242, 253, 252, 251, 252, 263, 243, 242, 252, 243, 252, 251, 339, 263, 338, 251, 263, 339, 274, 251, 339, 342, 339, 338, 267, 243, 251, 267, 251, 274, 270, 243, 267, 275, 274, 339, 275, 339, 342, 343, 275, 342, 268, 267, 274, 268, 274, 275, 276, 268, 275, 276, 275, 343, 268, 272, 267, 277, 276, 343, 269, 268, 276, 269, 276, 277, 43, 277, 343, 273, 268, 269, 272, 268, 273, 42, 43, 343, 44, 277, 43, 269, 277, 44, 44, 273, 269, 45, 273, 44, 45, 289, 273, 46, 288, 45, 270, 245, 243, 289, 272, 273, 282, 272, 289, 124, 202, 244, 119, 248, 247, 237, 204, 257, 244, 202, 237, 258, 257, 206, 258, 206, 29, 123, 124, 244, 238, 237, 257, 244, 237, 238, 256, 238, 257, 256, 257, 258, 259, 256, 258, 250, 244, 238, 123, 244, 250, 30, 258, 29, 259, 258, 30, 122, 123, 250, 260, 259, 30, 256, 239, 238, 255, 256, 259, 255, 259, 260, 255, 239, 256, 250, 238, 239, 249, 250, 239, 122, 250, 249, 121, 122, 249, 261, 260, 30, 261, 30, 31, 254, 255, 260, 254, 260, 261, 240, 239, 255, 240, 255, 254, 248, 120, 121, 248, 249, 239, 248, 239, 240, 248, 121, 249, 247, 248, 240, 119, 120, 248, 161, 144, 163, 143, 144, 161, 142, 143, 161, 146, 164, 163, 145, 146, 163, 144, 145, 163, 147, 148, 165, 147, 165, 164, 146, 147, 164, 149, 150, 166, 148, 149, 166, 148, 166, 165, 167, 151, 168, 150, 151, 167, 166, 150, 167, 168, 152, 0, 151, 152, 168, 167, 168, 171, 168, 0, 171, 320, 211, 319, 24, 210, 320, 212, 219, 317, 140, 141, 162, 336, 335, 332, 139, 140, 334, 221, 137, 138, 337, 138, 139, 221, 138, 337, 337, 139, 334, 218, 15, 340, 15, 218, 336, 220, 337, 334, 334, 325, 335, 217, 335, 336, 217, 334, 335, 220, 334, 217, 234, 337, 220, 232, 136, 137, 232, 137, 221, 233, 232, 221, 234, 233, 221, 234, 221, 337, 219, 218, 340, 213, 217, 336, 216, 135, 136, 216, 136, 232, 134, 135, 216, 216, 232, 233, 214, 220, 217, 234, 220, 214, 214, 217, 213, 215, 233, 234, 215, 234, 214, 216, 233, 215, 188, 133, 134, 132, 133, 188, 199, 215, 214, 219, 336, 218, 219, 213, 336, 212, 213, 219, 235, 134, 216, 188, 134, 235, 198, 214, 213, 198, 213, 212, 199, 214, 198, 236, 216, 215, 235, 216, 236, 200, 236, 215, 200, 215, 199, 184, 131, 132, 189, 188, 235, 230, 200, 199, 224, 199, 198, 230, 199, 224, 190, 235, 236, 222, 189, 235, 190, 236, 200, 197, 198, 212, 197, 212, 211, 224, 198, 197, 130, 131, 184, 190, 222, 235, 185, 184, 132, 185, 132, 188, 185, 188, 189, 229, 200, 230, 190, 200, 229, 210, 211, 320, 197, 211, 210, 225, 224, 197, 182, 130, 184, 182, 184, 185, 129, 130, 182, 183, 230, 224, 183, 224, 225, 229, 230, 183, 223, 189, 222, 229, 192, 190, 228, 229, 183, 210, 196, 197, 196, 225, 197, 191, 186, 185, 189, 191, 185, 182, 185, 186, 192, 222, 190, 228, 192, 229, 223, 222, 192, 210, 209, 196, 223, 191, 189, 128, 129, 182, 187, 128, 182, 226, 183, 225, 226, 225, 196, 25, 210, 24, 209, 210, 25, 186, 187, 182, 231, 228, 183, 227, 231, 183, 226, 227, 183, 208, 196, 209, 127, 128, 187, 193, 192, 228, 193, 228, 231, 203, 191, 223, 203, 223, 192, 204, 192, 193, 203, 192, 204, 195, 226, 196, 195, 196, 208, 227, 226, 195, 26, 209, 25, 208, 209, 26, 201, 186, 191, 202, 201, 191, 126, 187, 186, 194, 231, 227, 193, 231, 194, 194, 227, 195, 201, 126, 186, 127, 187, 126, 205, 193, 194, 204, 193, 205, 203, 202, 191, 207, 195, 208, 206, 194, 195, 27, 208, 26, 207, 208, 27, 125, 126, 201, 125, 201, 202, 207, 206, 195, 205, 194, 206, 28, 207, 27, 237, 203, 204, 202, 203, 237, 124, 125, 202, 257, 204, 205, 257, 205, 206, 29, 207, 28, 206, 207, 29, 332, 15, 336, 325, 334, 140, 340, 15, 341, 341, 15, 16, 219, 340, 317, 211, 212, 318, 335, 325, 332, 12, 155, 175, 176, 160, 177, 162, 142, 161, 162, 161, 158, 161, 163, 157, 331, 327, 330, 331, 330, 160, 157, 154, 327, 159, 331, 160, 159, 160, 176, 158, 157, 327, 326, 327, 331, 158, 327, 326, 333, 326, 331, 333, 331, 159, 155, 333, 159, 155, 159, 175, 158, 161, 157, 153, 158, 326, 162, 158, 153, 332, 326, 333, 332, 333, 155, 325, 153, 326, 332, 325, 326, 13, 14, 155, 332, 155, 14, 12, 13, 155, 140, 162, 153, 15, 332, 14, 153, 325, 140, 141, 142, 162, 175, 159, 176, 341, 321, 340, 212, 317, 318, 321, 17, 18, 322, 321, 18, 322, 18, 19, 322, 19, 20, 340, 321, 317, 341, 16, 17, 17, 321, 341, 322, 20, 21, 323, 322, 21, 22, 323, 21, 317, 323, 318, 322, 317, 321, 323, 317, 322, 324, 318, 323, 324, 323, 22, 319, 318, 324, 23, 324, 22, 24, 324, 23, 319, 324, 24, 320, 319, 24, 211, 318, 319, 12, 175, 11, 175, 176, 10, 11, 175, 10, 176, 177, 9, 10, 176, 9, 9, 177, 8, 8, 178, 7, 8, 177, 178, 177, 156, 178, 178, 179, 6, 178, 6, 7, 180, 4, 5, 179, 180, 5, 179, 5, 6, 2, 3, 181, 181, 3, 4, 181, 4, 180, 174, 181, 180, 173, 174, 180, 165, 166, 170, 1, 2, 174, 171, 0, 1, 171, 1, 174, 170, 171, 174, 170, 174, 173, 170, 328, 169, 170, 173, 329, 173, 172, 329, 165, 170, 169, 156, 329, 172, 329, 328, 170, 330, 329, 156, 328, 329, 330, 154, 169, 328, 164, 169, 154, 160, 330, 156, 327, 328, 330, 154, 328, 327, 157, 163, 154, 166, 171, 170, 171, 166, 167, 164, 165, 169, 163, 164, 154, 174, 2, 181, 172, 173, 179, 173, 180, 179, 156, 172, 178, 172, 179, 178, 160, 156, 177], "vertices": [3, 6, 133.98, 55.44, 0.30938, 27, -113.51, -177.75, 0, 15, 5.37, -29.59, 0.69062, 1, 6, 135.11, -31.97, 1, 3, 6, 135.31, -47.62, 0.49815, 18, 25.21, 27.85, 0.23903, 24, -47.2, -18.72, 0.26282, 3, 6, 135.51, -63.27, 0.18505, 18, 25.41, 12.21, 0.451, 24, -42.56, -3.77, 0.36395, 3, 6, 113.29, -75.94, 0.01289, 18, 3.19, -0.46, 0.98627, 69, -226.93, 18.53, 0.00085, 2, 24, 15.18, 4.07, 0.9955, 69, -257.19, 5.91, 0.0045, 3, 24, 46.72, 3.36, 0.07888, 25, 8.8, 4.74, 0.91446, 69, -287.41, -3.16, 0.00666, 3, 25, 39.91, 7.03, 0.42655, 26, 0.44, 7.29, 0.56447, 69, -317.73, -10.48, 0.00898, 3, 26, 26.87, 9.18, 0.93757, 27, -10.65, 7.64, 0.05253, 69, -344.22, -11.07, 0.0099, 2, 27, 17.63, 7.94, 0.9907, 69, -371.99, -5.68, 0.0093, 3, 27, 36.37, 4.02, 0.05183, 28, 5.23, 5.14, 0.94077, 69, -389.56, 1.93, 0.0074, 5, 6, -60.93, -69.71, 0.001, 26, 85.48, -23.8, 2e-05, 27, 52.3, -16.02, 0.07933, 28, 24.76, -11.39, 0.85057, 5, 35.8, -80.69, 0.06908, 6, 6, -60.98, -54.65, 0.00341, 26, 86.27, -38.84, 0.00352, 27, 55.37, -30.76, 0.08026, 28, 30.66, -25.25, 0.53879, 5, 32.67, -65.96, 0.37039, 4, 301.1, -48.5, 0.00364, 3, 28, 47.76, -40.16, 0.17535, 5, 18.75, -48.04, 0.66387, 4, 281.4, -37.25, 0.16079, 2, 5, 19.79, -39.41, 0.66652, 4, 279.06, -28.88, 0.33348, 2, 37, 6.81, 73.15, 0.08444, 4, 264.14, -40, 0.91556, 2, 37, 9.66, 43.4, 0.53887, 4, 266.98, -69.75, 0.46113, 2, 37, 8.1, 40.67, 0.58348, 4, 265.43, -72.48, 0.41652, 2, 37, 11.96, 14, 0.93736, 4, 269.29, -99.15, 0.06264, 1, 37, 9.07, -2.14, 1, 1, 37, 1.11, -14.42, 1, 2, 37, -14.38, -24.13, 0.95261, 4, 242.94, -137.28, 0.04739, 2, 37, -29.69, -29.95, 0.88676, 4, 227.63, -143.1, 0.11324, 2, 37, -58.98, -35.35, 0.72531, 4, 198.34, -148.51, 0.27469, 3, 37, -88.95, -36.81, 0.53226, 4, 168.38, -149.96, 0.46467, 3, 153.06, -217.64, 0.00306, 3, 37, -117.73, -36.14, 0.35889, 4, 139.59, -149.29, 0.60277, 3, 129.33, -201.34, 0.03834, 3, 37, -151.63, -30.46, 0.18255, 4, 105.7, -143.61, 0.70882, 3, 104.06, -178.04, 0.10863, 3, 37, -176.39, -23.13, 0.07455, 4, 80.94, -136.28, 0.735, 3, 87.34, -158.36, 0.19045, 3, 4, 50.45, -124.97, 0.66027, 3, 68.01, -132.21, 0.32072, 48, -117.76, 53.46, 0.01901, 4, 4, 13.17, -111.91, 0.39143, 3, 43.94, -100.88, 0.51198, 47, -50.58, 109.83, 0.0018, 48, -98.93, 18.73, 0.09478, 4, 4, -15.18, -110.66, 0.1523, 3, 20.9, -84.33, 0.55613, 47, -30.43, 89.86, 0.03202, 48, -78.78, -1.24, 0.25955, 4, 4, -28.94, -127.11, 0.04944, 3, 0.38, -90.57, 0.35368, 47, -9.18, 92.79, 0.06592, 48, -57.53, 1.69, 0.53097, 5, 4, -34.68, -149.13, 0.01154, 3, -16.47, -105.87, 0.13846, 47, 9.87, 105.24, 0.02804, 48, -38.48, 14.14, 0.82138, 50, -135.23, -65.85, 0.00058, 4, 4, -37.76, -183.88, 0.00037, 3, -38.06, -133.27, 0.01061, 48, -12.85, 37.8, 0.90552, 50, -119.48, -34.72, 0.0835, 2, 48, 13.66, 54.36, 0.69323, 50, -100.43, -9.95, 0.30677, 2, 48, 20.43, 65.53, 0.6027, 50, -97.99, 2.89, 0.3973, 2, 48, 27.66, 65.67, 0.56145, 50, -91.27, 5.55, 0.43855, 2, 48, 45.46, 79.2, 0.3896, 50, -79.31, 24.44, 0.6104, 3, 47, 114.29, 184.62, 0, 48, 65.94, 93.52, 0.27566, 50, -65.14, 45.02, 0.72434, 2, 48, 77.25, 93.61, 0.25272, 50, -54.57, 49.05, 0.74728, 2, 48, 122.35, 59.43, 0.15491, 50, -0.37, 32.78, 0.84509, 1, 50, 85.47, 7.02, 1, 2, 47, 249.9, 59.26, 0.41957, 50, 105.73, -25.05, 0.58043, 2, 47, 250.89, 24.41, 0.95987, 50, 118.84, -57.36, 0.04013, 1, 47, 247.7, 7.82, 1, 2, 47, 271.37, -1, 0.69914, 53, 64.58, 125.13, 0.30086, 2, 47, 297.55, -12.06, 0.51305, 53, 92.88, 122.53, 0.48695, 2, 47, 322.92, -25.97, 0.35051, 53, 121.27, 116.95, 0.64949, 3, 27, 822.66, -100.59, 0, 47, 393.03, -66.73, 0.06754, 53, 200.44, 99.35, 0.93246, 4, 27, 904.29, -113.66, 0, 47, 465.55, -106.42, 0.00263, 53, 281.57, 83.5, 0.99684, 54, -98.87, 106.23, 0.00054, 2, 53, 328.86, 78.11, 0.95903, 54, -53.66, 91.17, 0.04097, 2, 53, 354.2, 76.26, 0.86381, 54, -29.22, 84.12, 0.13619, 2, 53, 380.15, 74.37, 0.66822, 54, -4.19, 76.9, 0.33178, 2, 53, 407.61, 71.74, 0.36004, 54, 22.18, 68.65, 0.63996, 2, 53, 426.17, 72.31, 0.16828, 54, 40.47, 65.37, 0.83172, 2, 53, 443.18, 76.26, 0.06087, 54, 57.95, 65.71, 0.93913, 2, 53, 467.37, 86.66, 0.0077, 54, 83.81, 70.87, 0.9923, 1, 54, 118.85, 76.91, 1, 2, 53, 535.09, 104.93, 0, 54, 153.94, 74.74, 1, 2, 53, 555.75, 105.29, 0, 54, 174.25, 70.82, 1, 2, 53, 605.09, 100.84, 0, 54, 221.66, 56.25, 1, 2, 53, 689, 98.25, 0, 54, 303.34, 36.35, 1, 1, 54, 345.31, 29.35, 1, 2, 54, 364.78, 25.77, 0.97678, 56, 164.74, -20.54, 0.02322, 2, 54, 385.13, 25.26, 0.63227, 56, 144.72, -24.02, 0.36773, 2, 54, 401.74, 26.71, 0.19026, 56, 128.74, -28.69, 0.80974, 2, 54, 426.23, 36.5, 0.00536, 56, 106.67, -43.07, 0.99464, 1, 56, 97.54, -46.53, 1, 1, 56, 80.1, -46.24, 1, 2, 56, 50.34, -39.18, 0.97474, 55, 103.72, -35.64, 0.02526, 2, 56, 34.19, -39.8, 0.90313, 55, 87.65, -37.36, 0.09687, 2, 56, 15.69, -40.24, 0.70744, 55, 69.21, -39.07, 0.29256, 2, 56, -1.11, -41.81, 0.42701, 55, 52.56, -41.79, 0.57299, 2, 56, -27.38, -44.37, 0.09618, 55, 26.52, -46.14, 0.90382, 2, 56, -37.13, -43.16, 0.04997, 55, 16.72, -45.61, 0.95003, 2, 56, -43.53, -39.53, 0.02928, 55, 10.08, -42.42, 0.97072, 2, 56, -48.14, -32.53, 0.01554, 55, 5.01, -35.75, 0.98446, 2, 56, -52.66, -20.83, 0.00252, 55, -0.3, -24.39, 0.99748, 1, 55, -4.21, -10.04, 1, 3, 27, 1595.11, -118.06, 0, 53, 971.83, 55.47, 0, 55, -6.26, 23.24, 1, 4, 27, 1590.38, -134.63, 0, 53, 966.53, 39.07, 0, 56, -49.85, 43.44, 0.00546, 55, -1.9, 39.92, 0.99454, 4, 27, 1586.09, -137.91, 0, 53, 962.14, 35.93, 0, 56, -45.42, 46.52, 0.0117, 55, 2.3, 43.3, 0.9883, 4, 27, 1580.07, -141.13, 0, 53, 956.01, 32.92, 0, 56, -39.25, 49.46, 0.02438, 55, 8.25, 46.65, 0.97562, 4, 27, 1573.22, -142.85, 0, 53, 949.11, 31.44, 0, 56, -32.34, 50.86, 0.0455, 55, 15.06, 48.53, 0.9545, 4, 27, 1561.39, -143.37, 0, 53, 937.26, 31.33, 0, 56, -20.49, 50.84, 0.10672, 55, 26.88, 49.32, 0.89328, 4, 27, 1538.93, -135.59, 0, 53, 915.09, 39.87, 0, 56, 1.59, 42.05, 0.42002, 55, 49.51, 42.06, 0.57998, 4, 27, 1524.93, -130.32, 0, 53, 901.27, 45.61, 0, 56, 15.33, 36.15, 0.74988, 55, 63.62, 37.12, 0.25012, 4, 27, 1509.92, -127.81, 0, 53, 886.36, 48.64, 0, 56, 30.21, 32.96, 0.96237, 55, 78.69, 34.95, 0.03763, 3, 27, 1492.65, -126.67, 0, 53, 869.13, 50.36, 0, 56, 47.41, 31.04, 1, 3, 27, 1471.36, -125, 0, 53, 847.92, 52.77, 0, 56, 68.6, 28.39, 1, 4, 27, 1440.36, -124.79, 0, 53, 816.95, 54.03, 0, 54, 419.52, -33.39, 0.01159, 56, 99.56, 26.78, 0.98841, 3, 27, 1417.78, -126.22, 0, 54, 397.22, -29.35, 0.21809, 56, 122.18, 27.17, 0.78191, 3, 27, 1396.49, -128.14, 0, 54, 376.07, -26.1, 0.749, 56, 143.54, 28.13, 0.251, 3, 27, 1381.34, -129.49, 0, 54, 361.01, -23.77, 0.97421, 56, 158.74, 28.79, 0.02579, 2, 27, 1365.04, -134.2, 0, 54, 344.04, -24.43, 1, 2, 27, 1322.66, -151.85, 0, 54, 298.6, -31.37, 1, 2, 27, 1209.68, -203.47, 0, 54, 176.36, -54.34, 1, 2, 27, 1173.06, -218.73, 0, 54, 137.09, -60.35, 1, 2, 27, 1150.66, -224.7, 0, 54, 113.89, -60.76, 1, 2, 27, 1117.19, -226.62, 0, 54, 80.89, -54.58, 1, 2, 27, 1088.95, -222.74, 0, 54, 54.37, -44.03, 1, 3, 27, 1066.71, -223.63, 0, 53, 440.13, -31.97, 0.03461, 54, 32.54, -39.56, 0.96539, 3, 27, 1048.01, -228.53, 0, 53, 421.28, -36.23, 0.22135, 54, 13.19, -39.82, 0.77865, 3, 27, 1027.56, -234.38, 0, 53, 400.63, -41.38, 0.56537, 54, -8.1, -40.58, 0.43463, 3, 27, 1008.37, -235.1, 0, 53, 381.43, -41.44, 0.84889, 54, -26.93, -36.67, 0.15111, 2, 53, 356.54, -42.43, 0.9965, 54, -51.52, -32.48, 0.0035, 1, 53, 329.61, -47.33, 1, 2, 27, 910.71, -259.02, 0, 53, 283.01, -62, 1, 2, 27, 812.16, -282.58, 0, 53, 183.72, -82.18, 1, 1, 53, 111.39, -89.71, 1, 2, 49, 159.43, -109.89, 0.0299, 53, 67.04, -87.81, 0.9701, 3, 47, 172.91, -184.6, 0.00811, 49, 123.13, -89.81, 0.10403, 53, 26.37, -79.67, 0.88787, 3, 47, 148.38, -167.99, 0.02681, 49, 98.61, -73.19, 0.21642, 53, -2.04, -71.27, 0.75677, 3, 47, 124.72, -149.26, 0.15546, 49, 74.95, -54.46, 0.36891, 53, -30.26, -60.58, 0.47563, 3, 47, 87.44, -142.02, 0.06677, 49, 37.67, -47.23, 0.76565, 53, -67.98, -64.98, 0.16758, 2, 49, 27.63, -53.34, 0.81979, 53, -75.69, -73.85, 0.18021, 3, 3, -79.14, 120.15, 0.01161, 49, -13.62, -33.03, 0.9541, 53, -121.16, -66.99, 0.03429, 4, 3, -75.62, 115.05, 0.02066, 47, 33.48, -122.24, 0.00207, 49, -16.3, -27.44, 0.95441, 53, -125.4, -62.48, 0.02286, 4, 3, -48.9, 108.97, 0.12043, 47, 8.06, -112.02, 0.05871, 49, -41.72, -17.23, 0.80657, 53, -152.73, -60.44, 0.01429, 4, 4, -155.41, 15.85, 6e-05, 3, -27.28, 98.29, 0.28183, 47, -11.62, -98.07, 0.13219, 49, -61.39, -3.27, 0.58593, 4, 4, -134.51, 21.63, 0.00351, 3, -6.63, 91.69, 0.51226, 47, -30.97, -88.3, 0.12667, 49, -80.75, 6.5, 0.35756, 5, 27, 413.05, -268.41, 0, 4, -120.49, 33.22, 0.02074, 3, 11.45, 93.72, 0.69741, 47, -49.14, -87.46, 0.06969, 49, -98.92, 7.34, 0.21216, 5, 27, 387.3, -286.63, 0, 4, -103.73, 59.94, 0.098, 3, 40.09, 106.92, 0.7892, 47, -79.51, -95.98, 0.01191, 49, -129.28, -1.19, 0.10089, 5, 27, 358.07, -303.09, 0, 4, -83.09, 86.38, 0.24372, 3, 71.84, 117.76, 0.71242, 47, -112.57, -101.69, 0.00018, 49, -162.34, -6.9, 0.04368, 4, 27, 332.58, -308.43, 0, 4, -61.62, 101.12, 0.42633, 3, 97.88, 118.35, 0.55845, 49, -188.15, -3.38, 0.01522, 5, 27, 306.16, -314.13, 0, 4, -39.42, 116.54, 0.55855, 3, 124.89, 119.12, 0.2362, 65, -553, -158.94, 0.00656, 58, -100.13, 23.12, 0.19869, 5, 27, 299.37, -343.72, 0, 4, -44.54, 146.47, 0.6773, 3, 136.98, 146.98, 0.1093, 65, -582.25, -150.8, 0.01676, 59, -77.67, 13.54, 0.19665, 6, 27, 281.85, -365.46, 0, 4, -36.74, 173.27, 0.74129, 3, 158.18, 165.14, 0.04904, 65, -603.16, -132.3, 0.02429, 60, -53.82, 4.6, 0.09757, 59, -60.75, 35.73, 0.08781, 5, 27, 258.6, -379.85, 0, 4, -20.81, 195.51, 0.77453, 3, 183.67, 175.05, 0.00235, 65, -616.48, -108.42, 0.02891, 60, -30.96, 19.62, 0.19422, 4, 27, 232.15, -384.51, 0, 4, 1.81, 209.99, 0.77435, 65, -619.92, -81.78, 0.03206, 60, -4.65, 24.98, 0.19359, 4, 27, 204.94, -382.19, 0, 4, 27.82, 218.32, 0.77493, 65, -616.36, -54.7, 0.03134, 60, 22.62, 23.4, 0.19373, 5, 27, 175.71, -364.31, 0, 4, 61.67, 213.06, 0.78625, 65, -597.16, -26.33, 0.02932, 60, 52.31, 6.32, 0.09707, 59, 45.38, 37.45, 0.08736, 4, 27, 153.64, -335.51, 0, 4, 93.13, 194.97, 0.77952, 65, -567.37, -5.6, 0.02561, 59, 68.23, 9.25, 0.19488, 6, 27, 140.53, -300.43, 0, 4, 118.73, 167.64, 0.74324, 42, -42.42, 50, 0.04827, 65, -531.73, 5.89, 0.02283, 59, 82.28, -25.46, 0.09772, 58, 65.21, 40.08, 0.08795, 5, 27, 134.52, -248.62, 0, 4, 144.21, 122.13, 0.6225, 42, -16.94, 4.49, 0.16468, 65, -479.7, 9.52, 0.01602, 58, 80.63, -9.75, 0.1968, 5, 27, 127.67, -230.71, 0, 4, 157.42, 108.23, 0.64665, 42, -3.73, -9.4, 0.2211, 65, -461.5, 15.54, 0.01392, 58, 90.64, -26.09, 0.11833, 5, 27, 115.99, -210.82, 0, 4, 175.85, 94.37, 0.66249, 42, 14.7, -23.27, 0.30949, 65, -441.09, 26.3, 0.00818, 58, 105.78, -43.5, 0.01984, 3, 27, 98.69, -187.94, 0, 4, 200.63, 79.91, 0.63232, 42, 39.48, -37.73, 0.36768, 3, 27, 96.63, -170.11, 0, 4, 209.4, 64.25, 0.8595, 42, 48.24, -53.39, 0.1405, 2, 4, 220.19, 49.99, 0.95714, 42, 59.04, -67.65, 0.04286, 4, 6, -60.47, 55.19, 0.00272, 5, 10.74, 41.67, 0.30087, 4, 239.76, 42.62, 0.69639, 34, 102.36, 52.37, 2e-05, 5, 6, -60.78, 61.78, 0.00838, 5, 9.08, 48.06, 0.40026, 4, 235.79, 47.88, 0.53284, 36, 46.97, 18.29, 0.0551, 68, -482.8, 156.24, 0.00342, 7, 6, -55.49, 75.54, 0.00907, 27, 76.14, -159.4, 0, 5, 11.45, 62.61, 0.24527, 4, 232.43, 62.24, 0.15959, 35, 63.35, 17.72, 0.01112, 36, 35.26, 9.33, 0.55009, 68, -477.51, 170, 0.02485, 4, 5, 11.61, 76.59, 0.04251, 4, 227.24, 75.23, 0.06682, 36, 25.54, -0.73, 0.86326, 68, -474.49, 183.66, 0.02742, 4, 27, 61.87, -190.03, 0, 35, 36.05, -2.21, 0.23161, 36, 3.9, -3.27, 0.74381, 68, -457.38, 197.14, 0.02458, 3, 27, 51.15, -199.64, 0, 35, 22.02, -5.44, 0.97804, 68, -444.94, 204.41, 0.02196, 4, 27, 34.51, -210.42, 0, 34, 42.71, -6.09, 0.37263, 35, 2.25, -6.83, 0.60911, 68, -426.48, 211.62, 0.01826, 3, 27, 14.02, -218.83, 0, 34, 20.74, -8.89, 0.98425, 68, -404.71, 215.74, 0.01575, 4, 27, -10.92, -224.19, 0, 33, 36.28, -8.44, 0.63928, 34, -4.73, -7.6, 0.3474, 68, -379.21, 215.99, 0.01332, 4, 27, -35.19, -225.22, 0, 32, 55.94, -5.07, 0.01021, 33, 11.99, -8.36, 0.97828, 68, -355.22, 212.12, 0.01151, 4, 27, -58.82, -222.01, 0, 32, 32.17, -6.82, 0.97789, 33, -11.46, -4.07, 0.01315, 68, -332.73, 204.24, 0.00896, 3, 27, -84.9, -215.12, 0.02929, 32, 5.22, -5.47, 0.96524, 68, -308.56, 192.25, 0.00547, 2, 27, -107.41, -205.65, 0, 15, 5, -1.04, 1, 3, 6, -51.38, 44.63, 0.01133, 5, 21.78, 33.19, 0.62129, 4, 253.2, 38.99, 0.36738, 3, 6, 14.33, 43.79, 0.67781, 5, 86.28, 45.79, 0.31562, 15, -114.28, -41.24, 0.00657, 4, 6, -46.52, -27.74, 0.05936, 5, 41.33, -36.66, 0.90568, 4, 297.92, -18.11, 0.02847, 69, -386.73, 66.73, 0.0065, 3, 6, 18.22, -47.46, 0.81525, 5, 108.73, -42.73, 0.16015, 69, -321.99, 47.01, 0.0246, 3, 6, -6.83, 44.06, 0.36372, 5, 65.51, 41.73, 0.63593, 15, -135.43, -40.97, 0.00035, 3, 6, -29.11, 44.35, 0.12742, 5, 43.65, 37.46, 0.83132, 4, 271.79, 51.28, 0.04126, 4, 6, -22.96, -33.95, 0.16406, 24, 99.12, -80.6, 0.00186, 5, 65.66, -37.93, 0.81908, 69, -363.17, 60.51, 0.015, 3, 6, -6, -39.13, 0.42653, 5, 83.32, -39.53, 0.55328, 69, -346.22, 55.33, 0.02019, 5, 6, -32.4, 66.14, 0.07355, 5, 35.97, 58.12, 0.45885, 4, 256.81, 67.45, 0.05028, 36, 20.5, 29.43, 0.40745, 69, -372.61, 160.6, 0.00986, 7, 6, -47.01, 61.36, 0.02536, 5, 22.64, 50.47, 0.50465, 4, 247.41, 55.29, 0.2248, 33, 115.63, 64.97, 0.00067, 34, 88.56, 47.02, 0.00893, 36, 35.45, 25.85, 0.23119, 69, -387.22, 155.83, 0.0044, 6, 6, -7.66, 74.84, 0.12873, 5, 58.42, 71.7, 0.18238, 34, 48.45, 35.95, 0.09072, 35, 17.88, 32.62, 0.37571, 36, -5.14, 34.96, 0.20422, 69, -347.87, 169.3, 0.01824, 7, 6, 13.09, 79.98, 0.20535, 5, 77.67, 80.97, 0.10405, 15, -115.51, -5.05, 0.00206, 34, 27.44, 32.07, 0.39811, 35, -3.45, 33.88, 0.23815, 36, -25.51, 41.43, 0.02826, 69, -327.12, 174.45, 0.02402, 8, 6, 36.26, 84.8, 0.20398, 5, 99.37, 90.42, 0.03296, 15, -92.34, -0.23, 0.01065, 33, 37.03, 28.85, 0.35188, 34, 4.02, 28.66, 0.3434, 35, -27.01, 36.17, 0.02444, 36, -47.78, 49.44, 0.00588, 69, -303.95, 179.27, 0.02681, 6, 6, 59.32, 83.41, 0.25513, 5, 122.23, 93.77, 0.0098, 15, -69.29, -1.62, 0.04735, 32, 49.12, 29.31, 0.10099, 33, 14.04, 26.64, 0.55708, 69, -280.9, 177.87, 0.02964, 6, 6, 81.22, 79.7, 0.24922, 5, 144.43, 94.61, 0.00059, 15, -47.38, -5.33, 0.13758, 32, 27.56, 23.97, 0.41178, 33, -8.18, 26.89, 0.17007, 69, -258.99, 174.17, 0.03076, 2, 6, 106.46, 69.86, 0.26492, 15, -22.14, -15.17, 0.73508, 3, 6, 37.99, 50.48, 0.85815, 5, 108.08, 57.18, 0.10269, 15, -90.61, -34.55, 0.03916, 3, 6, 58.86, 56.39, 0.84466, 5, 127.3, 67.22, 0.0263, 15, -69.75, -28.64, 0.12904, 3, 6, 79.89, 62.34, 0.65833, 5, 146.67, 77.35, 0.00237, 15, -48.71, -22.69, 0.3393, 3, 6, 44.01, -53.8, 0.95377, 5, 135.27, -43.67, 0.01935, 69, -296.2, 40.67, 0.02689, 2, 6, 73.79, -51.53, 0.97085, 69, -266.42, 42.93, 0.02915, 2, 6, 102.4, -44.65, 0.96992, 69, -237.81, 49.81, 0.03008, 5, 6, -40.47, -58.92, 0.0435, 27, 34.42, -30.7, 0.10761, 28, 10.1, -29.29, 0.39918, 5, 53.62, -65.94, 0.44498, 69, -380.68, 35.54, 0.00472, 6, 6, -22.71, -68.68, 0.07613, 26, 47.35, -26.71, 0.13995, 27, 15.06, -24.7, 0.39049, 28, -10.06, -27.2, 0.15681, 5, 73, -71.87, 0.22852, 69, -362.92, 25.78, 0.0081, 7, 6, -2.15, -74.77, 0.11519, 24, 91.93, -35.35, 0.01047, 26, 26.52, -21.64, 0.59723, 27, -6.3, -22.87, 0.16869, 28, -31.36, -29.57, 0.01281, 5, 94.37, -73.63, 0.08406, 69, -342.36, 19.7, 0.01155, 5, 6, 24.25, -79.88, 0.14085, 25, 34.1, -17.41, 0.32477, 26, -0.1, -17.83, 0.5001, 5, 121.26, -73.24, 0.01967, 69, -315.96, 14.59, 0.01462, 6, 6, 54.14, -78.89, 0.13768, 24, 39.66, -14.04, 0.36901, 25, 4.44, -13.53, 0.46196, 26, -29.91, -20.28, 0.01262, 5, 150.32, -66.17, 0.00033, 69, -286.07, 15.57, 0.0184, 3, 6, 79.35, -72.11, 0.21528, 24, 13.59, -12.71, 0.7635, 69, -260.87, 22.35, 0.02122, 4, 6, 108.97, -59.38, 0.27607, 18, -1.12, 16.09, 0.43308, 24, -18.52, -15.67, 0.26615, 69, -231.24, 35.08, 0.02471, 4, 27, 230.17, -363.91, 0, 4, 11.56, 191.74, 0.71306, 65, -599.25, -80.74, 0.04925, 60, -2.11, 4.45, 0.23769, 3, 4, 103.54, -18.55, 0.69375, 65, -369.83, -87.28, 0.075, 62, -4.17, 0.73, 0.23125, 4, 27, 189.42, -356.79, 0, 4, 51.91, 200.84, 0.76698, 65, -590.27, -40.37, 0.04127, 60, 38.81, -1.57, 0.19175, 4, 27, 207.5, -334.11, 0, 4, 43.95, 172.95, 0.75866, 65, -568.45, -59.47, 0.05168, 60, 21.35, -24.73, 0.18966, 4, 27, 249.26, -332.04, 0, 4, 6.21, 154.97, 0.75856, 65, -568.29, -101.28, 0.0518, 60, -20.34, -27.93, 0.18964, 5, 27, 268.57, -355.16, 0, 4, -20.52, 168.88, 0.73888, 3, 169.35, 152.59, 0.02671, 65, -592.27, -119.51, 0.04301, 60, -40.27, -5.34, 0.1914, 4, 27, 156.75, -292.76, 0, 4, 106.7, 154.31, 0.7711, 65, -524.81, -10.67, 0.03612, 59, 66.27, -33.57, 0.19278, 4, 27, 188.97, -268.69, 0, 4, 86.23, 119.7, 0.761, 65, -502.24, -43.96, 0.04875, 59, 34.71, -58.49, 0.19025, 3, 4, 104.29, 67.99, 0.756, 65, -447.77, -49.68, 0.055, 61, 17.05, 86.55, 0.189, 4, 27, 250.56, -274.64, 0, 4, 27.09, 101.49, 0.76018, 65, -511.01, -105.2, 0.04978, 59, -27.02, -54.21, 0.19004, 3, 4, 53.43, 49.76, 0.756, 65, -452.98, -103.46, 0.055, 61, -36.91, 83.84, 0.189, 4, 4, 40.82, 8.5, 0.6912, 3, 132.96, -15.22, 0.0648, 65, -421.05, -132.47, 0.055, 61, -60.94, 48, 0.189, 4, 4, 48.79, -38.56, 0.64543, 3, 113.89, -58.97, 0.11057, 65, -375.09, -145.34, 0.055, 61, -66.95, 0.66, 0.189, 4, 4, 77.74, -79.82, 0.65255, 3, 115.55, -109.35, 0.10345, 65, -325.42, -136.76, 0.055, 61, -51.19, -47.22, 0.189, 5, 37, -137.9, 22.03, 0.03782, 4, 119.42, -91.12, 0.71194, 3, 144.26, -141.61, 0.00624, 65, -297.41, -103.89, 0.055, 61, -14.57, -70.12, 0.189, 4, 37, -92.92, 42.37, 0.12949, 4, 164.41, -70.79, 0.62651, 65, -296.61, -54.53, 0.055, 61, 34.37, -63.69, 0.189, 4, 37, -70.8, 86.11, 0.02932, 4, 186.52, -27.04, 0.72668, 65, -326.74, -15.86, 0.055, 61, 68.22, -28.23, 0.189, 3, 4, 169.72, 20.04, 0.756, 65, -376.48, -10.97, 0.055, 61, 65.78, 21.7, 0.189, 3, 4, 139.94, 50.43, 0.756, 65, -416.68, -24.94, 0.055, 61, 46.08, 59.42, 0.189, 5, 27, 286.58, -301.6, 0, 4, -16.53, 112.51, 0.68618, 3, 141.85, 103.22, 0.08281, 65, -539.59, -139.95, 0.03877, 59, -63.75, -28.23, 0.19225, 5, 27, 299.91, -284.28, 0, 4, -22.17, 91.39, 0.59797, 3, 125.58, 88.63, 0.18046, 65, -522.9, -154.07, 0.02697, 58, -88.5, -5.07, 0.19461, 5, 27, 277.4, -249.02, 0, 4, 12.18, 67.52, 0.71973, 3, 141.27, 49.85, 0.04827, 65, -486.64, -133.19, 0.04, 58, -59.89, -35.6, 0.192, 4, 4, 25.96, 36.08, 0.70855, 3, 135.61, 16, 0.05945, 65, -452.33, -134.14, 0.04, 57, -82.21, 78.7, 0.192, 4, 4, 26.38, -7.3, 0.61506, 3, 112.23, -20.54, 0.15294, 65, -412.92, -152.27, 0.04, 57, -90.85, 36.19, 0.192, 4, 4, 37.16, -50, 0.54606, 3, 97.9, -62.18, 0.22864, 65, -369.71, -160.74, 0.03161, 57, -89.22, -7.82, 0.19368, 4, 4, 61.94, -87.97, 0.58577, 3, 97.87, -107.52, 0.19029, 65, -324.79, -154.53, 0.02992, 57, -72.91, -50.12, 0.19402, 5, 37, -158.54, 2.52, 0.03471, 4, 98.78, -110.63, 0.65987, 3, 116.31, -146.64, 0.08399, 65, -288.58, -130.88, 0.02678, 57, -41.61, -79.98, 0.19464, 5, 37, -125.09, 1.13, 0.1611, 4, 132.23, -112.02, 0.61, 3, 143.55, -166.11, 0.00595, 65, -273.04, -101.22, 0.0287, 57, -9.19, -88.32, 0.19426, 4, 37, -99.94, 7.68, 0.24943, 4, 157.38, -105.47, 0.52882, 65, -268.24, -75.68, 0.02719, 57, 16.77, -87.17, 0.19456, 4, 37, -79.13, 19.4, 0.30104, 4, 178.2, -93.76, 0.4742, 65, -269.95, -51.86, 0.03095, 57, 39.57, -80.06, 0.19381, 4, 37, -48.18, 44.2, 0.31293, 4, 209.15, -68.95, 0.45921, 65, -279.18, -13.28, 0.03483, 57, 75.02, -62.25, 0.19303, 4, 37, -47.77, 92.96, 0.05743, 4, 209.55, -20.19, 0.71471, 65, -323.11, 7.89, 0.03482, 57, 85.6, -14.65, 0.19304, 3, 4, 187.06, 19.44, 0.77508, 65, -368.55, 4.46, 0.03114, 57, 71.87, 28.8, 0.19377, 3, 4, 162.84, 48.99, 0.77196, 65, -405.6, -4.83, 0.03505, 57, 54.36, 62.75, 0.19299, 4, 27, 137.97, -214.51, 0, 4, 154.15, 89.31, 0.79967, 65, -445.78, 4.51, 0.0248, 58, 83.5, -43.91, 0.17554, 2, 4, 210.86, 11.36, 0.97559, 65, -351.08, 22.54, 0.02441, 3, 37, -16.65, 76.4, 0.12231, 4, 240.67, -36.75, 0.85027, 65, -294.85, 28.97, 0.02742, 4, 37, -26.35, 53.97, 0.34193, 4, 230.97, -59.18, 0.57972, 65, -278.7, 10.63, 0.02984, 57, 98.4, -57.26, 0.04851, 2, 4, 201.15, 36.34, 0.98149, 65, -377.82, 24.41, 0.01851, 4, 27, 108.52, -172.63, 0, 4, 197.44, 61.99, 0.88462, 42, 36.29, -55.64, 0.10471, 65, -402.6, 32.01, 0.01067, 6, 4, 86.41, 92.35, 0.77193, 65, -477.43, -55.46, 0.05037, 61, 7, 115.05, 0.07597, 57, -11.34, 121.11, 0.01575, 59, 25.04, -84.08, 0.06989, 58, 17.89, -26.8, 0.01608, 7, 27, 240.55, -246.12, 0, 4, 47.31, 79.02, 0.77195, 65, -482.06, -96.51, 0.05036, 61, -34.29, 113.62, 0.07597, 57, -52.37, 116.24, 0.01575, 59, -16.24, -82.45, 0.06989, 58, -23.14, -31.68, 0.01608, 3, 4, 149.45, -25.94, 0.744, 65, -343.55, -48.92, 0.07, 62, 37.63, -19.65, 0.186, 3, 4, 130.18, -52.09, 0.744, 65, -328.12, -77.51, 0.07, 62, 11.61, -39.1, 0.186, 3, 4, 99.54, -60.53, 0.744, 65, -333.57, -108.82, 0.07, 62, -20.16, -38.29, 0.186, 4, 4, 76.27, -48.43, 0.71184, 3, 131.49, -82.26, 0.03216, 65, -354.44, -124.7, 0.07, 62, -38.93, -19.97, 0.186, 3, 4, 75.46, 13.91, 0.744, 65, -411.16, -98.83, 0.07, 62, -21.64, 39.93, 0.186, 3, 4, 108.63, 26.55, 0.744, 65, -408.44, -63.44, 0.07, 62, 13.77, 42.41, 0.186, 3, 4, 137.1, 10.88, 0.744, 65, -382.12, -44.38, 0.07, 62, 36.48, 19.17, 0.186, 3, 4, 63.13, -14.74, 0.744, 65, -390.52, -122.2, 0.07, 62, -41.74, 16.08, 0.186, 5, 27, 124.95, -196.06, 0, 4, 173.27, 77.29, 0.79805, 42, 12.12, -40.34, 0.10293, 65, -426.75, 16.67, 0.02068, 58, 99.69, -59.66, 0.07835, 3, 4, 174.51, 61.67, 0.84131, 65, -412.1, 11.12, 0.02173, 58, 97.63, -75.19, 0.13696, 3, 4, 188.87, 44.23, 0.93155, 65, -390.19, 16.67, 0.01942, 58, 108.04, -95.25, 0.04903, 5, 27, 158.99, -241.9, 0, 4, 124.21, 106.5, 0.78097, 65, -474.1, -15.23, 0.03584, 59, 65.4, -84.47, 0.09642, 58, 57.81, -20.85, 0.08677, 4, 4, 136.65, 65, 0.7776, 65, -431.26, -21.69, 0.04, 61, 47.15, 74.32, 0.096, 57, 32.08, 83.88, 0.0864, 4, 27, 326.84, -239.41, 0, 4, -29.76, 39.62, 0.30274, 3, 90.91, 49.45, 0.66348, 65, -479.31, -183.02, 0.03378, 6, 27, 359.21, -232.87, 0, 4, -57.12, 21.13, 0.06493, 3, 57.89, 48.93, 0.89864, 47, -87.95, -35.91, 0.00106, 65, -474.26, -215.65, 0.02852, 63, -482.9, 169.43, 0.00685, 6, 27, 396.15, -225.65, 0, 4, -88.43, 0.25, 0.00427, 3, 20.25, 48.59, 0.90338, 47, -50.73, -41.51, 0.05916, 65, -468.74, -252.88, 0.02018, 63, -477.39, 132.2, 0.013, 4, 3, -11.45, 44.18, 0.60337, 47, -18.72, -42.14, 0.36516, 65, -460.01, -283.68, 0.01359, 63, -468.65, 101.4, 0.01788, 5, 3, -39.15, 45.24, 0.25954, 47, 8.46, -47.55, 0.70081, 49, -41.32, 47.24, 0.00869, 65, -457.25, -311.26, 0.00777, 63, -465.89, 73.82, 0.0232, 5, 3, -74.74, 43.1, 0.03093, 47, 43.95, -51.05, 0.7991, 49, -5.83, 43.75, 0.13561, 65, -450.24, -346.22, 0.00025, 63, -458.88, 38.86, 0.0341, 3, 47, 75.84, -47.32, 0.94339, 49, 26.06, 47.47, 0.02269, 63, -446.01, 9.45, 0.03392, 5, 27, 336.95, -277.48, 0, 4, -53.74, 70.87, 0.31553, 3, 87.92, 88.72, 0.65879, 49, -173.65, 24.32, 4e-05, 65, -517.8, -191.38, 0.02564, 3, 47, 65.7, -82.32, 0.43048, 49, 15.92, 12.47, 0.53792, 63, -482.44, 8.93, 0.0316, 5, 3, -71.16, 76.01, 0.1224, 47, 35.22, -82.98, 0.35854, 49, -14.55, 11.81, 0.49099, 65, -483.33, -347.2, 0.0005, 63, -491.97, 37.89, 0.02757, 5, 3, -34.57, 78.82, 0.36115, 47, -1.35, -79.99, 0.26839, 49, -51.13, 14.8, 0.34357, 65, -491.14, -311.34, 0.00613, 63, -499.79, 73.74, 0.02076, 6, 4, -129, 7.41, 0.00204, 3, -9.8, 76.77, 0.56439, 47, -25.49, -74.07, 0.17839, 49, -75.27, 20.73, 0.22742, 65, -492.52, -286.52, 0.01134, 63, -501.17, 98.56, 0.01642, 7, 27, 403.96, -253.7, 0, 4, -106.44, 23.14, 0.02117, 3, 17.7, 77.59, 0.78359, 47, -52.77, -70.55, 0.06815, 49, -102.55, 24.24, 0.09827, 65, -497.12, -259.4, 0.01681, 63, -505.76, 125.68, 0.01202, 7, 27, 374.56, -264.71, 0, 4, -83.54, 44.61, 0.09928, 3, 48.62, 83.04, 0.84866, 47, -84.16, -71.06, 0.00943, 49, -133.94, 23.73, 0.01511, 65, -506.77, -229.53, 0.0206, 63, -515.41, 155.55, 0.00692, 3, 47, 76.04, 1.92, 0.75579, 48, 27.69, -89.18, 0.21015, 63, -398.86, 23.63, 0.03406, 5, 3, -72.33, -11.24, 0.00043, 47, 50.13, 3, 0.75214, 48, 1.78, -88.1, 0.21211, 65, -396.74, -336.36, 0.00199, 63, -405.38, 48.72, 0.03333, 6, 4, -104.8, -80.98, 3e-05, 3, -37.88, -10.46, 0.01861, 47, 15.99, 7.65, 0.77711, 48, -32.36, -83.45, 0.16664, 65, -402.26, -302.34, 0.00934, 63, -410.9, 82.74, 0.02827, 6, 4, -84.8, -60.88, 5e-05, 3, -10.15, -4.57, 0.53238, 47, -12.33, 6.2, 0.36286, 48, -60.68, -84.9, 0.06591, 65, -411.91, -275.69, 0.01517, 63, -420.55, 109.4, 0.02362, 5, 4, -62.07, -46.59, 0.00196, 3, 16.7, -5.05, 0.95855, 47, -38.77, 10.9, 2e-05, 65, -415.13, -249.03, 0.02107, 63, -423.77, 136.06, 0.0184, 4, 4, -35.05, -33.84, 0.01465, 3, 46.29, -9.16, 0.9491, 65, -415.13, -219.15, 0.02787, 63, -423.77, 165.93, 0.00839, 3, 4, -6.8, -21.53, 0.15351, 3, 76.67, -14.3, 0.81339, 65, -414.21, -188.35, 0.03309, 5, 4, -2.43, -68.26, 0.25352, 3, 54.77, -55.81, 0.67567, 47, -68.37, 67.03, 0.00082, 48, -116.72, -24.07, 0.04485, 65, -370.08, -204.33, 0.02513, 6, 4, -25.17, -73.91, 0.10855, 3, 32.64, -48.1, 0.75118, 47, -47.73, 55.93, 0.01593, 48, -96.08, -35.17, 0.09715, 65, -374.68, -227.31, 0.02125, 63, -383.32, 157.77, 0.00594, 6, 4, -42.07, -84.42, 0.05361, 3, 12.75, -47.66, 0.66336, 47, -28.16, 52.36, 0.08393, 48, -76.51, -38.74, 0.17061, 65, -372.38, -247.08, 0.0176, 63, -381.02, 138, 0.01089, 6, 4, -58.98, -104.59, 0.02071, 3, -12.44, -55.29, 0.37592, 47, -2.08, 55.93, 0.23323, 48, -50.43, -35.17, 0.34267, 65, -361.35, -270.98, 0.01271, 63, -369.99, 114.1, 0.01476, 6, 4, -80.88, -130.23, 0.00229, 3, -44.81, -64.77, 0.09718, 47, 31.38, 60.19, 0.31584, 48, -16.97, -30.9, 0.56011, 65, -347.51, -301.73, 0.00862, 63, -356.15, 83.35, 0.01597, 4, 3, -77.51, -72.71, 0.00505, 47, 64.92, 62.88, 0.37177, 48, 16.57, -28.22, 0.60192, 63, -343.79, 52.05, 0.02125, 4, 47, 105.83, 136.68, 0.01205, 48, 57.48, 45.58, 0.39453, 50, -56.31, -2.86, 0.5857, 63, -261.27, 34.47, 0.00771, 3, 48, 7.54, 20.02, 0.87956, 50, -94.16, -44.26, 0.11031, 63, -300.29, 74.77, 0.01013, 6, 4, -52.72, -145.91, 0.00789, 3, -29.81, -93.31, 0.11585, 47, 21.06, 90.73, 0.05438, 48, -27.29, -0.37, 0.80804, 65, -321.31, -282.95, 0.00613, 63, -329.95, 102.13, 0.00771, 2, 47, 132.9, -45.3, 0.96565, 63, -427.42, -44.55, 0.03435, 2, 47, 181.46, -36.42, 0.9739, 63, -404.75, -88.4, 0.0261, 2, 47, 224.07, -15.5, 0.98299, 63, -372.31, -123.04, 0.01701, 4, 47, 115.12, -94.35, 0.40341, 49, 65.34, 0.44, 0.50918, 53, -56.05, -11.17, 0.05941, 63, -479.53, -41.85, 0.028, 4, 47, 144.36, -100.41, 0.40261, 49, 94.59, -5.61, 0.45145, 53, -26.35, -8.08, 0.12281, 63, -476.78, -71.59, 0.02313, 3, 47, 188.87, -65.42, 0.94619, 53, 5.47, 38.75, 0.0365, 63, -430.33, -103.95, 0.01731, 2, 47, 231.12, -30.79, 0.98745, 63, -384.88, -134.25, 0.01255, 3, 47, 133.54, 11.15, 0.80934, 48, 85.19, -79.95, 0.1589, 63, -373.24, -28.68, 0.03176, 3, 47, 165.89, 21.65, 0.82617, 48, 117.54, -69.45, 0.14793, 63, -353.76, -56.55, 0.0259, 3, 47, 197.65, 8.15, 0.87743, 48, 149.3, -82.95, 0.10079, 63, -357.4, -90.87, 0.02178, 2, 47, 228.55, 7.7, 0.98411, 63, -348.81, -120.56, 0.01589, 2, 48, 64.68, 86.48, 0.28805, 50, -63.85, 37.98, 0.71195, 4, 47, 106.7, 156.35, 0.00091, 48, 58.35, 65.25, 0.35214, 50, -62.36, 15.88, 0.6432, 63, -242.2, 39.37, 0.00375, 4, 47, 110.57, 101.86, 0.12363, 48, 62.22, 10.77, 0.37907, 50, -39.7, -33.82, 0.48255, 63, -293.18, 19.77, 0.01474, 4, 47, 159.4, -110.2, 0.02432, 49, 109.63, -15.41, 0.28815, 53, -9.05, -12.86, 0.66034, 63, -481.76, -88.83, 0.02719, 3, 49, 140.77, -40.28, 0.11844, 53, 28.17, -27.12, 0.85577, 63, -496.46, -125.88, 0.02579, 2, 53, 72.26, -30.17, 0.97481, 63, -500.02, -169.93, 0.02519, 2, 53, 168.5, -28.37, 0.97302, 63, -499.34, -266.18, 0.02698, 2, 53, 260.29, -20.87, 0.97953, 63, -492.9, -358.06, 0.02047, 4, 27, 882.99, -158.03, 0, 47, 430.6, -141.08, 0.00264, 53, 258.76, 39.87, 0.97817, 63, -432.15, -357.23, 0.01919, 4, 27, 793.51, -148.22, 0, 47, 349.59, -101.83, 0.06571, 53, 169.66, 52.74, 0.90942, 63, -418.24, -268.29, 0.02487, 3, 47, 284.05, -67.75, 0.28759, 53, 96.88, 65.36, 0.68943, 63, -404.78, -195.66, 0.02298, 3, 47, 240.49, -55.07, 0.46186, 53, 51.53, 64.24, 0.51574, 63, -405.37, -150.3, 0.0224, 2, 53, 317.31, -17.17, 0.98005, 63, -489.87, -415.11, 0.01995, 2, 53, 345.08, -15.78, 0.9793, 63, -488.8, -442.9, 0.0207, 4, 27, 999.98, -209.38, 0, 53, 373.92, -15.45, 0.96834, 54, -28.9, -9.69, 0.01089, 63, -488.8, -471.74, 0.02077, 4, 27, 1028.19, -206.48, 0, 53, 402.22, -13.52, 0.46471, 54, -0.78, -13.65, 0.51486, 63, -487.2, -500.06, 0.02044, 4, 27, 1053.59, -200.51, 0, 53, 427.8, -8.41, 0.0077, 54, 25.35, -13.95, 0.97269, 63, -482.39, -525.7, 0.0196, 3, 27, 1079.47, -193.44, 0, 54, 52.21, -13.31, 0.98209, 63, -476.52, -551.88, 0.01791, 4, 27, 941.03, -153.26, 0, 53, 316.93, 42.66, 0.96959, 54, -72.69, 58.96, 0.01206, 63, -430.04, -415.43, 0.01835, 3, 53, 343.66, 41.37, 0.92363, 54, -46.77, 52.16, 0.05637, 63, -431.64, -442.14, 0.02, 3, 53, 370.39, 40.08, 0.79545, 54, -20.85, 45.37, 0.18396, 63, -433.24, -468.85, 0.02059, 3, 53, 402.46, 38.31, 0.40366, 54, 10.2, 37, 0.57454, 63, -435.38, -500.91, 0.0218, 3, 53, 426.52, 36.99, 0.10727, 54, 33.5, 30.73, 0.87152, 63, -436.98, -524.95, 0.0212, 3, 53, 452.69, 37.83, 0.01633, 54, 59.31, 26.14, 0.96391, 63, -436.45, -551.13, 0.01976, 3, 27, 1152.64, -191.3, 0, 54, 123.85, -28.82, 0.98863, 63, -477.74, -625.08, 0.01137, 2, 53, 692.48, 66.54, 0, 54, 300.18, 4.6, 1, 3, 53, 524.99, 70.29, 0, 54, 136.87, 42.93, 0.98701, 63, -404.83, -623.8, 0.01299, 3, 27, 1108.59, -190.15, 0, 54, 81.3, -17.11, 0.9844, 63, -474.57, -581.12, 0.0156, 3, 53, 480.22, 47.28, 0.00093, 54, 88.24, 29.68, 0.98312, 63, -427.32, -578.76, 0.01595, 4, 27, 1537.21, -116.34, 0, 53, 914.03, 59.17, 0, 56, 2.43, 22.74, 0.54916, 55, 51.67, 22.85, 0.45084, 2, 56, 1.25, -19.03, 0.52124, 55, 53.35, -18.9, 0.47876, 4, 27, 1552.14, -116.25, 0, 53, 928.95, 58.74, 0, 56, -12.49, 23.33, 0.17394, 55, 36.74, 22.42, 0.82606, 2, 56, -13.67, -17.54, 0.15887, 55, 38.37, -18.44, 0.84113, 1, 56, 16.75, -1.72, 1, 2, 56, 34.66, -2.61, 0.99527, 55, 85.56, -0.23, 0.00473, 2, 56, 55.24, -4.39, 0.99889, 55, 106.22, -0.6, 0.00111, 1, 56, 72.55, -5.58, 1, 4, 27, 1569.94, -120.1, 0, 53, 946.61, 54.29, 0, 56, -30.09, 27.99, 0.0433, 55, 18.86, 25.86, 0.9567, 1, 55, 20.16, -12.28, 1, 3, 37, -28.62, 32.6, 0.61872, 4, 228.7, -80.55, 0.35617, 65, -260.34, -0.55, 0.02511, 3, 37, -57.12, 16.23, 0.54323, 4, 200.2, -96.92, 0.43231, 65, -257.7, -33.3, 0.02446, 3, 37, -69.82, 3.75, 0.52938, 4, 187.51, -109.4, 0.45966, 65, -251.83, -50.11, 0.01096, 3, 37, -81.42, 8.22, 0.42678, 4, 175.9, -104.94, 0.55674, 65, -260.82, -58.7, 0.01648, 3, 37, -5.82, 29.53, 0.72778, 4, 251.5, -83.62, 0.25611, 65, -247.84, 18.77, 0.01611, 3, 37, -14.52, 8.66, 0.88404, 4, 242.81, -104.49, 0.09851, 65, -232.68, 2, 0.01744, 3, 37, -36.85, -6.45, 0.81512, 4, 220.47, -119.6, 0.16616, 65, -228.54, -24.65, 0.01872, 3, 37, -59.28, -16.52, 0.68463, 4, 198.04, -129.67, 0.30531, 65, -229, -49.23, 0.01006, 3, 5, 20.41, 16.18, 0.63702, 4, 258.42, 22.75, 0.34733, 65, -341.09, 70.41, 0.01565, 4, 6, -33.11, 25.57, 0.09515, 5, 43.57, 18.27, 0.86219, 4, 279.03, 33.51, 0.02756, 65, -342.03, 93.64, 0.0151, 4, 6, -8.15, 22.59, 0.28642, 5, 68.61, 20.45, 0.6985, 15, -136.75, -62.44, 1e-05, 65, -342.97, 118.76, 0.01507, 4, 6, 15.34, 19.99, 0.78934, 5, 92.14, 22.7, 0.19225, 15, -113.26, -65.04, 0.00234, 65, -344.05, 142.37, 0.01607, 3, 6, 16.78, -13.73, 0.9528, 5, 100.43, -10.02, 0.03276, 65, -310.97, 149.04, 0.01444, 3, 6, 8.16, -13.03, 0.79826, 5, 91.85, -11.09, 0.18756, 65, -310.32, 140.42, 0.01419, 3, 6, -13.61, -8.01, 0.07288, 5, 69.51, -10.62, 0.91265, 65, -311.89, 118.13, 0.01448, 2, 5, 22.01, -9.83, 0.98467, 65, -315.03, 70.72, 0.01533, 2, 5, 43.48, -10.56, 0.98534, 65, -313.23, 92.13, 0.01466, 2, 4, 224.82, 32.97, 0.98145, 65, -364.67, 44.38, 0.01855, 2, 4, 242.14, 0.77, 0.97836, 65, -328.16, 46.3, 0.02164, 3, 37, -24.48, 89.03, 0.08155, 4, 232.85, -24.13, 0.89265, 65, -309.61, 27.28, 0.0258, 4, 27, 110.44, -165.18, 0, 4, 198.54, 54.38, 0.9198, 42, 37.39, -63.26, 0.06772, 65, -395.25, 29.75, 0.01248, 4, 47, 118.17, 78.79, 0.32804, 48, 69.82, -12.31, 0.2976, 50, -24.52, -52.79, 0.35504, 63, -313.04, 5.77, 0.01932, 4, 47, 138.29, 47.05, 0.63211, 48, 89.94, -44.04, 0.24632, 50, 5.42, -75.49, 0.09678, 63, -337.52, -22.74, 0.02479, 3, 37, -5.09, 52.62, 0.41005, 4, 252.23, -60.54, 0.56911, 65, -268.41, 29.28, 0.02084, 3, 37, 4.76, 47.78, 0.4816, 4, 262.09, -65.37, 0.50752, 65, -259.83, 36.13, 0.01088, 4, 47, 152.73, 59.6, 0.46332, 48, 104.38, -31.49, 0.14342, 50, 14.57, -58.68, 0.37187, 63, -321.3, -32.89, 0.02138, 4, 47, 204.4, 43.72, 0.74877, 48, 156.05, -47.38, 0.02031, 50, 68.53, -55.51, 0.21385, 63, -321.41, -86.94, 0.01707, 4, 47, 154.43, 108.42, 0.05443, 48, 106.08, 17.32, 0.25309, 50, -0.9, -12.35, 0.68031, 63, -274.11, -20.27, 0.01217], "hull": 153, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 60, 62, 68, 70, 70, 72, 84, 86, 86, 88, 92, 94, 114, 116, 116, 118, 118, 120, 120, 122, 130, 132, 132, 134, 134, 136, 136, 138, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 210, 212, 212, 214, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 232, 234, 234, 236, 240, 242, 242, 244, 244, 246, 260, 262, 266, 268, 268, 270, 278, 280, 290, 292, 292, 294, 294, 296, 296, 298, 302, 304, 300, 302, 298, 300, 288, 290, 280, 282, 282, 284, 274, 276, 276, 278, 270, 272, 272, 274, 264, 266, 254, 256, 252, 254, 250, 252, 256, 258, 258, 260, 18, 20, 314, 308, 306, 316, 316, 314, 30, 32, 44, 46, 50, 52, 52, 54, 54, 56, 56, 58, 66, 68, 62, 64, 64, 66, 72, 74, 74, 76, 76, 78, 246, 248, 248, 250, 236, 238, 238, 240, 230, 232, 206, 208, 208, 210, 202, 204, 204, 206, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 82, 84, 88, 90, 90, 92, 214, 216, 94, 96, 96, 98, 98, 100, 58, 60, 0, 304, 308, 338, 340, 338, 336, 342, 342, 340, 0, 2, 2, 4, 4, 6, 368, 370, 370, 372, 372, 374, 376, 378, 384, 386, 386, 388, 390, 392, 392, 394, 394, 396, 396, 398, 398, 400, 400, 380, 382, 402, 376, 264, 250, 404, 404, 406, 406, 408, 408, 410, 410, 412, 412, 414, 414, 416, 416, 418, 418, 420, 420, 422, 422, 424, 424, 426, 426, 428, 428, 430, 432, 268, 262, 264, 378, 444, 444, 380, 382, 446, 446, 384, 388, 390, 448, 450, 452, 454, 456, 458, 450, 452, 448, 460, 460, 458, 454, 462, 462, 456, 432, 466, 466, 468, 468, 440, 430, 472, 430, 432, 474, 476, 476, 478, 478, 480, 480, 482, 482, 484, 484, 486, 490, 492, 492, 494, 494, 496, 496, 498, 498, 500, 500, 488, 502, 504, 504, 506, 506, 508, 508, 510, 510, 512, 512, 514, 516, 518, 518, 520, 520, 522, 522, 524, 524, 526, 528, 530, 530, 532, 486, 534, 534, 536, 536, 538, 538, 88, 490, 540, 228, 542, 542, 544, 544, 546, 502, 548, 550, 552, 552, 554, 528, 558, 558, 556, 560, 528, 562, 564, 564, 566, 566, 568, 568, 570, 572, 574, 574, 576, 576, 578, 580, 582, 582, 584, 584, 586, 586, 588, 588, 590, 592, 594, 594, 596, 596, 598, 598, 600, 600, 602, 580, 570, 592, 572, 604, 606, 606, 608, 590, 610, 610, 604, 602, 612, 612, 608, 184, 186, 186, 188, 122, 124, 124, 126, 126, 128, 128, 130, 178, 180, 180, 182, 182, 184, 142, 144, 144, 146, 138, 140, 140, 142, 174, 176, 176, 178, 172, 174, 168, 170, 170, 172, 614, 616, 618, 620, 630, 632, 650, 652, 652, 654, 308, 656, 654, 656, 312, 658, 658, 656, 658, 660, 660, 662, 662, 666, 666, 664, 434, 672, 672, 436, 440, 674, 674, 442, 668, 674, 670, 672, 676, 678, 678, 550, 560, 676, 32, 34, 34, 36, 402, 252, 284, 286, 286, 288, 78, 80, 80, 82], "width": 419, "height": 1731}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.03398, 0.48042, 0.10336, 0.89752, 0.64383, 0.93343, 0.96336, 0.53014, 0.8885, 0.08542, 0.32065, 0.07161], "triangles": [1, 0, 5, 2, 5, 4, 2, 4, 3, 1, 5, 2], "vertices": [2, 6, 59.26, 33.17, 0.96, 68, -362.75, 127.63, 0.04, 2, 6, 42.56, 31.66, 0.96, 68, -379.46, 126.12, 0.04, 2, 6, 36.21, 0.38, 0.96016, 68, -385.8, 94.84, 0.03984, 2, 6, 48.81, -20.69, 0.96354, 68, -373.2, 73.77, 0.03646, 2, 6, 66.63, -19.03, 0.9634, 68, -355.38, 75.43, 0.0366, 2, 6, 72.38, 13.98, 0.96, 68, -349.63, 108.44, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 59, "height": 39}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.05278, 0.28988, 0.14092, 0.7989, 0.36617, 0.93894, 0.92685, 0.66424, 0.87053, 0.19023, 0.29517, 0.06365], "triangles": [1, 0, 5, 3, 2, 5, 3, 5, 4, 1, 5, 2], "vertices": [2, 6, 66.1, 88.46, 0.98101, 68, -355.92, 182.93, 0.01899, 2, 6, 45.38, 87.8, 0.98001, 68, -376.63, 182.27, 0.01999, 2, 6, 38.3, 78.88, 0.97445, 68, -383.71, 173.35, 0.02555, 2, 6, 45.32, 52.8, 0.96668, 68, -376.7, 147.27, 0.03332, 2, 6, 64.43, 52.3, 0.96663, 68, -357.58, 146.76, 0.03337, 2, 6, 73.37, 76.52, 0.97394, 68, -348.64, 170.98, 0.02606], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 44, "height": 40}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.52504, 0.49417], "triangles": [1, 4, 0, 4, 3, 0, 1, 2, 4, 4, 2, 3], "vertices": [2, 7, -12.72, -8.55, 0.96134, 68, -378.6, 86.6, 0.03866, 2, 7, -9.29, 13.19, 0.96, 68, -375.17, 108.34, 0.04, 2, 7, 12.44, 9.76, 0.96, 68, -353.44, 104.91, 0.04, 2, 7, 9.01, -11.97, 0.96145, 68, -356.86, 83.18, 0.03855, 2, 7, -0.1, 0.04, 0.96049, 68, -365.98, 95.19, 0.03951], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 22}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.44069, 0.14932, 0.5017, 0.02625, 0.62777, 0.04176, 0.75384, 0.05728, 0.87692, 0.09242, 1, 0.12756, 0.95137, 0.31148, 0.83155, 0.45869, 0.83287, 0.69196, 0.72146, 0.89253, 0.5704, 0.97641, 0.41061, 0.96954, 0.25307, 0.93689, 0.11931, 0.87885, 0.03484, 0.86758, 0.02455, 0.75949, 0.07547, 0.61243, 0.13742, 0.43449, 0.2601, 0.25072, 0.07999, 0.79714, 0.11033, 0.65815, 0.1779, 0.48385, 0.2689, 0.36471, 0.40679, 0.28308, 0.56123, 0.27647, 0.6743, 0.31838, 0.75014, 0.40443, 0.7267, 0.55004, 0.66465, 0.69786, 0.54606, 0.81258, 0.41093, 0.84127, 0.28269, 0.83906, 0.16962, 0.81479], "triangles": [24, 1, 2, 0, 1, 24, 23, 18, 0, 23, 0, 24, 6, 4, 5, 25, 2, 3, 24, 2, 25, 22, 18, 23, 26, 25, 3, 26, 3, 4, 7, 26, 4, 17, 18, 22, 6, 7, 4, 21, 17, 22, 27, 25, 26, 27, 26, 7, 16, 17, 21, 20, 16, 21, 27, 7, 8, 27, 28, 24, 27, 24, 25, 28, 27, 8, 15, 16, 20, 19, 15, 20, 29, 23, 24, 29, 24, 28, 32, 20, 21, 19, 20, 32, 30, 31, 22, 23, 30, 22, 21, 22, 31, 32, 21, 31, 29, 30, 23, 14, 15, 19, 13, 19, 32, 14, 19, 13, 9, 28, 8, 29, 28, 9, 12, 32, 31, 13, 32, 12, 11, 31, 30, 12, 31, 11, 10, 29, 9, 11, 30, 29, 10, 11, 29], "vertices": [2, 6, 69.13, 2.59, 0.96045, 68, -352.88, 97.05, 0.03955, 2, 6, 72.85, -1.46, 0.96083, 68, -349.16, 93, 0.03917, 2, 6, 71.22, -8.35, 0.96198, 68, -350.79, 86.12, 0.03802, 2, 6, 69.58, -15.24, 0.96342, 68, -352.43, 79.23, 0.03658, 2, 6, 67.29, -21.85, 0.96482, 68, -354.72, 72.61, 0.03518, 2, 6, 65.01, -28.47, 0.9662, 68, -357.01, 65.99, 0.0338, 2, 6, 59.07, -24.78, 0.96548, 68, -362.94, 69.68, 0.03452, 2, 6, 55.03, -17.35, 0.96385, 68, -366.99, 77.11, 0.03615, 2, 6, 46.95, -16.15, 0.96313, 68, -375.06, 78.31, 0.03687, 2, 6, 40.99, -8.89, 0.96128, 68, -381.02, 85.57, 0.03872, 2, 6, 39.41, -0.08, 0.96024, 68, -382.61, 94.38, 0.03976, 2, 6, 41.04, 8.72, 0.96, 68, -380.98, 103.18, 0.04, 2, 6, 43.54, 17.26, 0.96003, 68, -378.47, 111.72, 0.03997, 2, 6, 46.71, 24.34, 0.96032, 68, -375.3, 118.8, 0.03968, 2, 6, 47.84, 28.95, 0.9605, 68, -374.18, 123.41, 0.0395, 2, 6, 51.66, 28.93, 0.96049, 68, -370.35, 123.39, 0.03951, 2, 6, 56.3, 25.31, 0.96031, 68, -365.71, 119.78, 0.03969, 2, 6, 61.92, 20.92, 0.96008, 68, -360.1, 115.38, 0.03993, 2, 6, 67.2, 13.13, 0.96, 68, -354.81, 107.59, 0.04, 2, 6, 49.88, 26.07, 0.96038, 68, -372.13, 120.53, 0.03962, 2, 6, 54.42, 23.63, 0.96026, 68, -367.59, 118.1, 0.03974, 2, 6, 59.86, 18.95, 0.96002, 68, -362.16, 113.41, 0.03998, 2, 6, 63.18, 13.26, 0.96, 68, -358.83, 107.73, 0.04, 2, 6, 64.8, 5.19, 0.96019, 68, -357.21, 99.65, 0.03981, 2, 6, 63.68, -3.39, 0.96111, 68, -358.33, 91.07, 0.03889, 2, 6, 61.25, -9.42, 0.96235, 68, -360.76, 85.05, 0.03765, 2, 6, 57.61, -13.14, 0.96308, 68, -364.4, 81.32, 0.03692, 2, 6, 52.78, -11.05, 0.9625, 68, -369.23, 83.41, 0.0375, 2, 6, 48.21, -6.81, 0.96142, 68, -373.8, 87.65, 0.03858, 2, 6, 45.28, 0.37, 0.96043, 68, -376.73, 94.84, 0.03957, 2, 6, 45.47, 8, 0.96, 68, -376.54, 102.47, 0.04, 2, 6, 46.66, 15.09, 0.96, 68, -375.35, 109.55, 0.04, 2, 6, 48.49, 21.21, 0.96019, 68, -373.53, 115.67, 0.03981], "hull": 19, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 28, 30, 34, 36, 30, 32, 32, 34, 26, 28, 2, 0, 0, 36, 22, 24, 24, 26, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 38, 2, 4, 4, 6, 6, 8, 8, 10], "width": 56, "height": 35}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.56794, 0.50199], "triangles": [1, 4, 0, 4, 3, 0, 1, 2, 4, 4, 2, 3], "vertices": [2, 8, -11.89, -7.56, 0.96944, 68, -377.89, 155.33, 0.03056, 2, 8, -8.93, 11.21, 0.97452, 68, -374.93, 174.1, 0.02548, 2, 8, 11.81, 7.94, 0.97305, 68, -354.18, 170.83, 0.02695, 2, 8, 8.86, -10.83, 0.96916, 68, -357.14, 152.06, 0.03084, 2, 8, -0.12, -0.06, 0.9702, 68, -366.12, 162.83, 0.0298], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 21}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.18622, 0.19442, 0.25212, 0.00236, 0.38587, 0.18773, 0.52943, 0.18371, 0.66899, 0.20664, 0.79693, 0.30068, 0.89972, 0.4474, 0.97924, 0.57336, 0.97892, 0.65063, 0.88159, 0.72671, 0.79394, 0.84505, 0.67735, 0.93136, 0.52487, 0.97488, 0.39023, 0.97632, 0.29582, 0.86047, 0.26563, 0.6903, 0.27009, 0.55951, 0.14554, 0.54026, 0.01223, 0.37382, 0.12283, 0.29333, 0.34734, 0.4604, 0.40154, 0.35559, 0.50643, 0.28904, 0.64077, 0.2957, 0.75154, 0.35392, 0.84464, 0.46539, 0.90474, 0.57187, 0.8305, 0.65006, 0.74212, 0.76985, 0.64431, 0.83308, 0.50643, 0.83806, 0.41451, 0.75321, 0.35323, 0.61014], "triangles": [13, 30, 12, 13, 31, 30, 13, 14, 31, 12, 29, 11, 12, 30, 29, 11, 28, 10, 11, 29, 28, 14, 15, 31, 10, 28, 9, 30, 31, 23, 30, 23, 29, 23, 31, 22, 29, 23, 28, 28, 27, 9, 28, 24, 27, 28, 23, 24, 15, 32, 31, 31, 32, 22, 22, 32, 21, 9, 26, 8, 9, 27, 26, 15, 16, 32, 8, 26, 7, 27, 25, 26, 27, 24, 25, 16, 20, 32, 32, 20, 21, 25, 6, 26, 7, 26, 6, 20, 16, 19, 18, 19, 17, 16, 17, 19, 24, 5, 25, 25, 5, 6, 19, 0, 20, 21, 0, 2, 21, 20, 0, 21, 2, 22, 5, 24, 4, 24, 23, 4, 22, 3, 23, 23, 3, 4, 22, 2, 3, 0, 1, 2], "vertices": [2, 6, 69.57, 91.34, 0.98254, 68, -352.44, 185.8, 0.01746, 2, 6, 75.53, 87.2, 0.98029, 68, -346.48, 181.66, 0.01971, 2, 6, 68.3, 81.84, 0.97791, 68, -353.71, 176.3, 0.02209, 2, 6, 67.37, 75.01, 0.97458, 68, -354.65, 169.47, 0.02542, 2, 6, 65.55, 68.51, 0.97154, 68, -356.46, 162.98, 0.02846, 2, 6, 61.44, 62.95, 0.96926, 68, -360.57, 157.41, 0.03074, 2, 6, 55.74, 58.85, 0.96777, 68, -366.27, 153.31, 0.03223, 2, 6, 50.92, 55.75, 0.96665, 68, -371.09, 150.21, 0.03335, 2, 6, 48.33, 56.17, 0.96667, 68, -373.69, 150.63, 0.03333, 2, 6, 46.5, 61.19, 0.96808, 68, -375.51, 155.65, 0.03192, 2, 6, 43.18, 65.97, 0.96953, 68, -378.83, 160.43, 0.03047, 2, 6, 41.15, 71.95, 0.97167, 68, -380.86, 166.42, 0.02833, 2, 6, 40.83, 79.41, 0.97517, 68, -381.18, 173.88, 0.02483, 2, 6, 41.79, 85.81, 0.97927, 68, -380.23, 180.27, 0.02073, 2, 6, 46.38, 89.67, 0.98269, 68, -375.63, 184.13, 0.01731, 2, 6, 52.32, 90.2, 0.98356, 68, -369.69, 184.66, 0.01644, 2, 6, 56.68, 89.3, 0.98271, 68, -365.33, 183.76, 0.01729, 2, 6, 58.26, 95.1, 0.98475, 68, -363.75, 189.56, 0.01525, 2, 6, 64.85, 100.54, 0.98705, 68, -357.17, 195, 0.01295, 2, 6, 66.72, 94.87, 0.98429, 68, -355.29, 189.33, 0.01571, 2, 6, 59.43, 85.11, 0.97987, 68, -362.58, 179.57, 0.02013, 2, 6, 62.55, 81.98, 0.97831, 68, -359.46, 176.45, 0.02169, 2, 6, 64, 76.66, 0.97558, 68, -358.01, 171.12, 0.02442, 2, 6, 62.77, 70.32, 0.97224, 68, -359.24, 164.79, 0.02776, 2, 6, 59.99, 65.38, 0.97042, 68, -362.02, 159.84, 0.02958, 2, 6, 55.55, 61.56, 0.96929, 68, -366.46, 156.02, 0.03071, 2, 6, 51.52, 59.27, 0.96876, 68, -370.49, 153.73, 0.03124, 2, 6, 49.45, 63.2, 0.96955, 68, -372.56, 157.67, 0.03045, 2, 6, 46.09, 68.03, 0.9708, 68, -375.92, 162.49, 0.0292, 2, 6, 44.7, 73, 0.97252, 68, -377.31, 167.46, 0.02748, 2, 6, 45.56, 79.57, 0.97589, 68, -376.45, 174.03, 0.02411, 2, 6, 49.1, 83.47, 0.9784, 68, -372.91, 177.94, 0.0216, 2, 6, 54.36, 85.62, 0.98005, 68, -367.65, 180.09, 0.01995], "hull": 20, "edges": [2, 4, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 10, 12, 12, 14, 4, 6, 6, 8, 36, 38, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 40, 2, 0, 0, 38], "width": 48, "height": 34}}, "glass": {"glass": {"type": "mesh", "uvs": [0.99422, 0.02095, 0.99397, 0.13079, 0.90213, 0.16896, 0.84156, 0.21696, 0.82226, 0.63427, 0.76337, 0.73335, 0.65725, 0.78353, 0.53182, 0.7874, 0.47268, 0.67218, 0.40968, 0.48872, 0.3714, 0.47941, 0.33874, 0.51289, 0.32357, 0.77613, 0.28665, 0.89612, 0.17678, 0.99169, 0.08719, 0.98257, 0.04308, 0.871, 0.01089, 0.61508, 0.00757, 0.4213, 0.07004, 0.33481, 0.15619, 0.28697, 0.25169, 0.28369, 0.33023, 0.39952, 0.40605, 0.37232, 0.44994, 0.21949, 0.53869, 0.1208, 0.6489, 0.05924, 0.82956, 0.03296, 0.90929, 0.01976, 0.45522, 0.45083, 0.45159, 0.35295, 0.47427, 0.28621, 0.55322, 0.18832, 0.65938, 0.14383, 0.79277, 0.13048, 0.7964, 0.57541, 0.80316, 0.31092, 0.75466, 0.66884, 0.65666, 0.72445, 0.5514, 0.73113, 0.50331, 0.65104, 0.49605, 0.49532, 0.03134, 0.48865, 0.08488, 0.39966, 0.17018, 0.35294, 0.25275, 0.35739, 0.3072, 0.44193, 0.30447, 0.73113, 0.27362, 0.84459, 0.17925, 0.92467, 0.10575, 0.92245, 0.06401, 0.83791, 0.03588, 0.61545], "triangles": [34, 26, 27, 1, 28, 0, 33, 26, 34, 2, 27, 28, 2, 28, 1, 32, 25, 26, 32, 26, 33, 3, 27, 2, 34, 27, 3, 31, 24, 25, 31, 25, 32, 36, 34, 3, 44, 20, 21, 30, 24, 31, 21, 22, 45, 44, 21, 45, 24, 30, 23, 43, 19, 20, 43, 20, 44, 42, 18, 19, 46, 45, 22, 41, 29, 30, 23, 30, 29, 10, 22, 23, 43, 42, 19, 9, 23, 29, 10, 23, 9, 41, 31, 32, 41, 30, 31, 11, 22, 10, 46, 22, 11, 36, 33, 34, 36, 37, 33, 17, 18, 42, 52, 42, 43, 17, 42, 52, 4, 36, 3, 35, 36, 4, 41, 39, 40, 35, 37, 36, 8, 29, 41, 8, 41, 40, 9, 29, 8, 38, 32, 33, 38, 33, 37, 38, 41, 32, 47, 45, 46, 47, 46, 11, 38, 39, 41, 4, 5, 37, 4, 37, 35, 12, 47, 11, 5, 6, 38, 5, 38, 37, 39, 38, 6, 7, 40, 39, 7, 39, 6, 8, 40, 7, 44, 51, 52, 47, 44, 45, 48, 47, 12, 16, 52, 51, 17, 52, 16, 13, 48, 12, 43, 44, 52, 49, 50, 51, 51, 44, 49, 47, 48, 44, 48, 49, 44, 15, 51, 50, 16, 51, 15, 14, 50, 49, 15, 50, 14, 13, 14, 49, 13, 49, 48], "vertices": [2, 6, 64.32, -46.71, 0.98454, 68, -357.69, 47.75, 0.01546, 2, 6, 57.6, -45.61, 0.98321, 68, -364.41, 48.85, 0.01679, 2, 6, 57.44, -31.45, 0.9643, 68, -364.57, 63.01, 0.0357, 2, 6, 55.93, -21.9, 0.95449, 68, -366.08, 72.57, 0.04551, 2, 6, 30.83, -14.97, 0.95432, 68, -391.18, 79.49, 0.04568, 2, 6, 26.16, -5.17, 0.95485, 68, -395.85, 89.29, 0.04515, 2, 6, 25.6, 11.25, 0.95681, 68, -396.42, 105.71, 0.04319, 2, 6, 28.33, 30.12, 0.95909, 68, -393.69, 124.58, 0.04091, 2, 6, 36.78, 37.88, 0.9601, 68, -385.23, 132.35, 0.0399, 2, 6, 49.51, 45.57, 0.96115, 68, -372.5, 140.04, 0.03885, 2, 6, 50.99, 51.23, 0.96191, 68, -371.03, 145.69, 0.03809, 2, 6, 49.71, 56.46, 0.96277, 68, -372.3, 150.92, 0.03723, 2, 6, 33.95, 61.28, 0.96351, 68, -388.07, 155.74, 0.03649, 2, 6, 27.47, 67.98, 0.96479, 68, -394.54, 162.44, 0.03521, 2, 6, 24.22, 85.4, 0.96816, 68, -397.79, 179.86, 0.03184, 2, 6, 26.9, 98.76, 0.97081, 68, -395.11, 193.22, 0.02919, 2, 6, 34.77, 104.31, 0.97201, 68, -387.24, 198.77, 0.02799, 2, 6, 51.21, 106.67, 0.97267, 68, -370.8, 201.13, 0.02733, 2, 6, 63.16, 105.3, 0.97239, 68, -358.86, 199.76, 0.02761, 2, 6, 66.97, 95.08, 0.97031, 68, -355.04, 189.55, 0.02969, 2, 6, 67.87, 81.69, 0.96765, 68, -354.15, 176.15, 0.03235, 2, 6, 65.81, 67.31, 0.96483, 68, -356.21, 161.78, 0.03517, 2, 6, 56.85, 56.64, 0.96284, 68, -365.16, 151.1, 0.03716, 2, 6, 56.73, 44.99, 0.96114, 68, -365.29, 139.46, 0.03886, 2, 6, 65.05, 36.93, 0.9602, 68, -356.97, 131.39, 0.0398, 2, 6, 68.99, 22.65, 0.95847, 68, -353.02, 117.11, 0.04153, 2, 6, 70.15, 5.51, 0.95641, 68, -351.86, 99.97, 0.04359, 2, 6, 67.49, -21.87, 0.95448, 68, -354.53, 72.59, 0.04552, 2, 6, 66.41, -33.97, 0.96746, 68, -355.61, 60.49, 0.03254, 2, 6, 50.75, 38.37, 0.9603, 68, -371.26, 132.83, 0.0397, 2, 6, 56.83, 37.97, 0.9603, 68, -365.18, 132.43, 0.0397, 2, 6, 60.38, 33.92, 0.95983, 68, -361.63, 128.38, 0.04017, 2, 6, 64.51, 21.12, 0.9583, 68, -357.5, 115.58, 0.0417, 2, 6, 64.72, 4.75, 0.95634, 68, -357.29, 99.21, 0.04366, 2, 6, 62.38, -15.41, 0.95434, 68, -359.63, 79.06, 0.04566, 2, 6, 35.05, -11.66, 0.95424, 68, -386.96, 82.81, 0.04576, 2, 6, 51.09, -15.22, 0.95432, 68, -370.93, 79.24, 0.04568, 2, 6, 30.31, -4.49, 0.95498, 68, -391.7, 89.98, 0.04502, 2, 6, 29.23, 10.77, 0.95679, 68, -392.78, 105.23, 0.04321, 2, 6, 31.31, 26.63, 0.95871, 68, -390.7, 121.1, 0.04129, 2, 6, 37.35, 33.08, 0.95953, 68, -384.66, 127.55, 0.04047, 2, 6, 47.06, 32.67, 0.95958, 68, -374.95, 127.13, 0.04042, 2, 6, 58.47, 102.38, 0.97183, 68, -363.54, 196.84, 0.02817, 2, 6, 62.65, 93.48, 0.97006, 68, -359.36, 187.94, 0.02994, 2, 6, 63.49, 80.22, 0.96742, 68, -358.52, 174.69, 0.03258, 2, 6, 61.27, 67.87, 0.96498, 68, -360.74, 162.33, 0.03502, 2, 6, 54.8, 60.51, 0.96353, 68, -367.21, 154.97, 0.03647, 2, 6, 37.15, 63.71, 0.96402, 68, -384.86, 158.17, 0.03598, 2, 6, 30.94, 69.44, 0.96509, 68, -391.08, 163.9, 0.03491, 2, 6, 28.26, 84.38, 0.96801, 68, -393.75, 178.84, 0.03199, 2, 6, 30.14, 95.39, 0.9702, 68, -391.87, 189.86, 0.0298, 2, 6, 36.31, 100.84, 0.97134, 68, -385.71, 195.31, 0.02866, 2, 6, 50.6, 102.92, 0.97192, 68, -371.42, 197.38, 0.02808], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 28, 30, 30, 32, 34, 36, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52, 54, 56, 46, 48, 18, 20, 20, 22, 8, 10, 6, 8, 22, 24, 24, 26, 26, 28, 32, 34, 36, 38, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 52, 54, 68, 72, 72, 70, 70, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 58, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 84], "width": 152, "height": 62}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.7469, 0.01843, 0.80311, 0.0998, 0.84721, 0.17948, 0.88375, 0.25396, 0.91578, 0.35119, 0.94901, 0.42622, 0.9735, 0.49963, 0.99063, 0.58294, 1, 0.67748, 0.99814, 0.74421, 0.9888, 0.8179, 0.94312, 0.86888, 0.90548, 0.93661, 0.81589, 0.96514, 0.75976, 0.96994, 0.70013, 0.95452, 0.5918, 0.89443, 0.57398, 0.85274, 0.59997, 0.82927, 0.62218, 0.78536, 0.63451, 0.71834, 0.64632, 0.64303, 0.64448, 0.5826, 0.6234, 0.62215, 0.58488, 0.65658, 0.55116, 0.66287, 0.586, 0.63161, 0.61175, 0.58509, 0.62878, 0.52936, 0.63792, 0.45749, 0.6365, 0.38846, 0.6126, 0.35518, 0.55623, 0.32147, 0.49002, 0.27971, 0.43234, 0.22302, 0.4524, 0.27322, 0.48644, 0.32125, 0.52908, 0.34678, 0.44802, 0.35944, 0.37657, 0.39766, 0.31537, 0.37428, 0.25571, 0.32159, 0.21068, 0.24939, 0.22466, 0.31467, 0.25336, 0.37844, 0.20676, 0.34175, 0.17633, 0.27708, 0.16132, 0.19155, 0.14366, 0.26267, 0.13239, 0.34307, 0.12755, 0.42687, 0.12987, 0.51881, 0.13835, 0.61225, 0.15526, 0.70051, 0.18469, 0.78975, 0.21903, 0.85801, 0.2625, 0.90546, 0.25267, 0.99999, 0.17365, 0.95827, 0.11623, 0.89389, 0.06163, 0.83284, 0.02951, 0.77102, 0.00967, 0.66051, 0.00748, 0.56863, 0.01206, 0.47902, 0.00554, 0.40302, 0.02925, 0.31211, 0.07073, 0.20873, 0.12331, 0.10386, 0.23051, 0.0488, 0.38824, 0.01437, 0.50729, 0.00335, 0.59148, 0.00313, 0.28664, 0.14149, 0.39254, 0.12236, 0.31557, 0.22562, 0.5409, 0.08891, 0.57057, 0.15939, 0.63387, 0.32065, 0.60519, 0.23703, 0.71508, 0.90263, 0.73506, 0.841, 0.76298, 0.75314, 0.78244, 0.66733, 0.79259, 0.56925, 0.79343, 0.47518, 0.78244, 0.37608, 0.76213, 0.2872, 0.73337, 0.19832, 0.69615, 0.12578, 0.65047, 0.06346, 0.85756, 0.8863, 0.8871, 0.80833, 0.89914, 0.7198, 0.90023, 0.62607, 0.89257, 0.53928, 0.35051, 0.28742, 0.39157, 0.33702], "triangles": [57, 58, 56, 58, 55, 56, 58, 59, 55, 59, 54, 55, 59, 60, 54, 54, 60, 53, 60, 61, 53, 61, 62, 53, 62, 52, 53, 62, 63, 52, 63, 51, 52, 63, 64, 51, 64, 50, 51, 64, 65, 50, 50, 65, 49, 49, 65, 66, 49, 66, 48, 66, 67, 48, 47, 68, 69, 47, 69, 73, 42, 47, 73, 67, 68, 47, 42, 46, 47, 48, 67, 47, 39, 97, 38, 39, 40, 97, 38, 36, 37, 36, 38, 35, 38, 97, 35, 97, 96, 35, 40, 96, 97, 40, 41, 96, 96, 41, 75, 96, 34, 35, 96, 75, 34, 75, 41, 42, 43, 44, 45, 45, 46, 43, 46, 42, 43, 42, 73, 75, 75, 74, 34, 75, 73, 74, 73, 69, 70, 74, 73, 70, 13, 91, 12, 13, 81, 91, 12, 91, 11, 81, 82, 91, 91, 92, 11, 91, 82, 92, 11, 92, 10, 9, 10, 93, 10, 92, 93, 92, 82, 93, 82, 83, 93, 9, 93, 8, 93, 94, 8, 93, 83, 94, 94, 7, 8, 83, 84, 94, 84, 95, 94, 94, 95, 7, 95, 6, 7, 84, 85, 95, 95, 5, 6, 95, 85, 5, 85, 4, 5, 85, 86, 4, 86, 3, 4, 86, 87, 3, 87, 2, 3, 88, 89, 1, 89, 0, 1, 89, 90, 0, 90, 72, 0, 77, 90, 89, 88, 1, 2, 87, 88, 2, 81, 13, 14, 14, 15, 80, 14, 80, 81, 15, 16, 80, 16, 18, 80, 18, 19, 80, 80, 19, 81, 16, 17, 18, 81, 19, 82, 19, 20, 82, 20, 21, 82, 82, 21, 83, 84, 83, 22, 24, 25, 26, 24, 26, 23, 83, 21, 22, 26, 27, 23, 23, 27, 22, 27, 28, 22, 22, 28, 84, 84, 28, 85, 85, 28, 29, 29, 86, 85, 29, 30, 86, 31, 78, 30, 30, 87, 86, 30, 78, 87, 31, 32, 78, 78, 88, 87, 32, 79, 78, 32, 33, 79, 79, 33, 77, 77, 33, 34, 79, 77, 89, 78, 79, 88, 79, 89, 88, 34, 76, 77, 34, 74, 76, 74, 71, 76, 74, 70, 71, 76, 72, 90, 76, 71, 72, 77, 76, 90], "vertices": [2, 17, -1.86, -46.45, 0.02485, 18, 16.79, 5.13, 0.97515, 2, 18, -2.03, -6.13, 0.99843, 69, -232.15, 12.86, 0.00157, 1, 18, -20.04, -14.45, 1, 4, 18, -36.69, -21.07, 0.52303, 24, 26.79, 8.7, 0.45587, 25, -11.7, 7.02, 0.01731, 69, -266.81, -2.08, 0.00378, 3, 18, -57.82, -25.85, 0.23429, 25, 9.92, 8.3, 0.75865, 69, -287.94, -6.86, 0.00706, 3, 25, 27.28, 11.31, 0.96288, 26, -12.81, 8.81, 0.02577, 69, -304.57, -12.65, 0.01135, 3, 25, 43.63, 12.35, 0.22767, 26, 2.95, 13.27, 0.75927, 69, -320.53, -16.33, 0.01306, 3, 26, 20.56, 15.68, 0.96252, 27, -17.89, 13.11, 0.02304, 69, -338.24, -17.87, 0.01444, 3, 26, 40.26, 15.92, 0.3125, 27, 1.56, 16.35, 0.67217, 69, -357.93, -17.14, 0.01533, 4, 26, 53.95, 13.98, 0.00926, 27, 15.38, 16.51, 0.97348, 28, -17.8, 13.29, 0.00179, 69, -371.5, -14.53, 0.01547, 3, 27, 30.72, 14.88, 0.69773, 28, -2.44, 14.69, 0.28674, 69, -386.21, -9.85, 0.01553, 2, 28, 10.55, 6.14, 0.98659, 69, -394.86, 3.08, 0.01341, 4, 22, 40.98, 65.04, 9e-05, 23, -0.61, 66.16, 0.00215, 28, 26.42, 0.37, 0.98656, 69, -407.24, 14.55, 0.01121, 4, 22, 51.66, 44.49, 0.04916, 23, 13.83, 48.05, 0.30553, 28, 37.54, -19.95, 0.63814, 69, -409.59, 37.6, 0.00717, 3, 22, 55.71, 31.01, 0.04373, 23, 20.4, 35.61, 0.53637, 28, 41.87, -33.33, 0.4199, 3, 22, 55.87, 15.77, 0.01309, 23, 23.49, 20.68, 0.78282, 28, 42.35, -48.57, 0.20409, 1, 23, 23.03, -9.12, 1, 1, 23, 16.93, -16.68, 1, 2, 22, 36.07, -14.35, 0.02802, 23, 9.86, -12.69, 0.97198, 2, 22, 25.99, -10.93, 0.58727, 23, -0.69, -11.28, 0.41273, 2, 21, 48.02, -13.12, 0.02232, 22, 11.78, -10.97, 0.97768, 2, 21, 32.37, -10.45, 0.76923, 22, -4.08, -11.51, 0.23077, 3, 20, 60.24, -14.72, 0.00637, 21, 19.88, -11.14, 0.99211, 22, -16.18, -14.71, 0.00152, 1, 21, 28.16, -16.26, 1, 2, 20, 72.76, -32.03, 1e-05, 21, 35.46, -25.76, 0.99999, 2, 20, 72.59, -40.56, 2e-05, 21, 36.91, -34.16, 0.99998, 2, 20, 67.71, -30.86, 1e-05, 21, 30.29, -25.57, 0.99999, 2, 20, 59.34, -22.86, 0.00393, 21, 20.54, -19.31, 0.99607, 2, 20, 48.71, -16.68, 0.17114, 21, 8.93, -15.26, 0.82886, 2, 20, 34.45, -11.87, 0.85619, 21, -5.99, -13.25, 0.14381, 5, 19, 58.97, -14.65, 0.03825, 20, 20.31, -9.75, 0.96108, 29, 62.86, 76.38, 0.00043, 30, 69.49, 49.79, 0.00013, 31, 65.53, 17.26, 0.0001, 7, 17, -65.49, -2.44, 0, 19, 50.21, -17.18, 0.274, 20, 12.49, -14.45, 0.71474, 29, 55.47, 71.04, 0.00453, 30, 60.46, 48.5, 0.00151, 31, 57.23, 21.02, 0.00121, 68, -358.75, 68.14, 0.004, 8, 17, -56.4, 10.4, 0.00664, 19, 37.96, -27.04, 0.74378, 20, 3.19, -27.13, 0.19039, 16, -59.9, -52.74, 0.00158, 29, 47.27, 57.62, 0.0309, 30, 46.96, 40.43, 0.01105, 31, 41.51, 21.47, 0.00935, 68, -349.66, 80.97, 0.0063, 8, 17, -45.29, 25.4, 0.07748, 19, 23.16, -38.43, 0.62261, 20, -8.18, -41.94, 0.02203, 16, -48.78, -37.74, 0.02299, 29, 37.19, 41.9, 0.1372, 30, 30.73, 31.19, 0.05767, 31, 22.86, 22.42, 0.05248, 68, -338.55, 95.98, 0.00754, 7, 17, -31.45, 37.82, 0.14138, 19, 6.45, -46.58, 0.05117, 16, -34.94, -25.32, 0.06479, 29, 24.23, 28.58, 0.17924, 30, 13.06, 25.42, 0.26997, 31, 4.85, 27.05, 0.2866, 68, -324.71, 108.4, 0.00686, 6, 17, -42.49, 34.48, 0.0214, 16, -45.99, -28.66, 0.00602, 29, 35.02, 32.65, 0.06058, 30, 24.51, 24.01, 0.16635, 31, 13.75, 19.71, 0.7379, 68, -335.75, 105.06, 0.00775, 7, 17, -53.64, 27.63, 0.00115, 19, 30.59, -42.85, 0.00343, 16, -57.14, -35.52, 0.0001, 29, 45.68, 40.25, 0.00418, 30, 37.48, 25.78, 0.00824, 31, 25.64, 14.23, 0.97443, 68, -346.9, 98.2, 0.00846, 4, 16, -64.02, -45.22, 1e-05, 30, 47.69, 31.88, 1e-05, 31, 37.53, 13.88, 0.99235, 68, -353.78, 88.5, 0.00763, 5, 17, -59.95, 38.35, 9e-05, 19, 33.72, -54.89, 0.00065, 29, 52.7, 29.98, 0.00021, 31, 20.21, 3.03, 0.99021, 68, -353.21, 108.92, 0.00884, 6, 17, -64.99, 57.22, 0, 19, 33.4, -74.42, 0, 29, 59, 11.49, 0, 30, 35.91, -5.87, 0.12814, 31, 7.29, -11.62, 0.86423, 68, -358.25, 127.8, 0.00763, 5, 30, 23.56, -16.12, 0.80585, 31, -8.64, -13.61, 0.18796, 15, -57.68, -37.34, 0.00036, 32, 24.22, 57.43, 0.00204, 68, -351.09, 142.16, 0.00378, 5, 29, 40.64, -17.21, 0.17227, 30, 6.31, -22.75, 0.76526, 15, -44.58, -24.3, 0.0299, 32, 17.41, 40.25, 0.03128, 68, -337.99, 155.19, 0.00129, 6, 16, -31.71, 30.27, 0.00185, 29, 24.76, -27.1, 0.40087, 30, -12.35, -24.13, 0.25825, 15, -28.06, -15.51, 0.26789, 32, 5.78, 25.6, 0.06657, 68, -321.47, 163.99, 0.00458, 6, 16, -45.6, 28.92, 0, 29, 38.53, -24.82, 0.50181, 30, 0.91, -28.51, 0.08974, 15, -41.96, -16.86, 0.15117, 32, 17.98, 32.38, 0.24636, 68, -335.37, 162.64, 0.01092, 6, 16, -59.76, 23.89, 0, 29, 52.31, -18.84, 0.52954, 30, 15.89, -29.62, 0.07534, 15, -56.11, -21.89, 0.136, 32, 28.95, 42.64, 0.24652, 68, -349.52, 157.6, 0.0126, 6, 16, -50.44, 34.21, 0, 29, 43.72, -29.77, 0.52203, 30, 3.2, -35.3, 0.08354, 15, -46.8, -11.56, 0.1435, 32, 24.53, 29.46, 0.24147, 68, -340.21, 167.93, 0.00945, 5, 29, 29.71, -36.17, 0.47747, 30, -12.18, -34.45, 0.15967, 15, -32.39, -6.13, 0.26137, 32, 13.49, 18.73, 0.09908, 68, -325.8, 173.36, 0.00241, 4, 16, -17.96, 40.59, 0.032, 15, -14.32, -5.19, 0.89808, 32, -2.7, 10.64, 0.0496, 68, -307.73, 174.31, 0.02032, 4, 30, -19.1, -39.71, 0.11085, 15, -28.17, 1.47, 0.30582, 32, 12.66, 10.07, 0.56531, 68, -321.58, 180.96, 0.01802, 4, 15, -44.17, 6.84, 0.08928, 32, 29.47, 11.53, 0.80769, 33, -9.46, 14.37, 0.07958, 68, -337.58, 186.34, 0.02345, 3, 32, 46.57, 14.72, 0.19136, 33, 7.89, 13.16, 0.78547, 68, -354.53, 190.23, 0.02317, 3, 33, 26.92, 13.74, 0.90558, 34, -9.1, 16.07, 0.07071, 68, -373.42, 192.63, 0.02371, 3, 33, 46.26, 15.86, 0.09098, 34, 10.24, 13.98, 0.88475, 68, -392.85, 193.54, 0.02427, 3, 34, 28.99, 14.18, 0.82847, 35, -6.22, 16.14, 0.14756, 68, -411.56, 192.21, 0.02397, 4, 34, 48.62, 17.39, 0.03701, 35, 13.6, 14.56, 0.88788, 36, -13.74, 18.51, 0.05074, 68, -430.95, 187.82, 0.02437, 3, 35, 30.07, 16.01, 0.41838, 36, 2.58, 15.86, 0.55647, 68, -446.25, 181.54, 0.02515, 3, 35, 43.69, 21.4, 0.05005, 36, 17.11, 17.74, 0.92154, 68, -457.64, 172.33, 0.02841, 2, 36, 30.39, 3.16, 0.9731, 68, -476.59, 177.81, 0.0269, 2, 36, 10.99, -6.25, 0.97651, 68, -464.98, 195.98, 0.02349, 3, 35, 25.36, -10.33, 0.89286, 36, -8.46, -8.51, 0.08758, 68, -449.58, 208.08, 0.01955, 3, 34, 50.71, -14.57, 0.11934, 35, 7.98, -16.97, 0.86402, 68, -434.97, 219.6, 0.01664, 3, 34, 36.48, -19.66, 0.72299, 35, -7.05, -18.51, 0.2638, 68, -421.08, 225.54, 0.01321, 3, 33, 56.25, -16.31, 0.03278, 34, 13.08, -19.58, 0.95478, 68, -397.71, 226.88, 0.01244, 3, 33, 37.24, -16.86, 0.63011, 34, -5.61, -16.03, 0.35826, 68, -378.84, 224.45, 0.01163, 3, 33, 18.69, -15.71, 0.98265, 34, -23.48, -10.91, 0.00712, 68, -360.7, 220.43, 0.01023, 4, 15, -51.49, 40.1, 0.06514, 32, 49.46, -16.04, 0.24111, 33, 2.95, -17.34, 0.68503, 68, -344.9, 219.6, 0.00872, 4, 15, -33.83, 31.32, 0.25227, 32, 29.76, -15.04, 0.60479, 33, -15.86, -11.42, 0.13501, 68, -327.24, 210.81, 0.00794, 3, 15, -14.3, 17.74, 0.61627, 32, 6.44, -10.38, 0.37771, 68, -307.71, 197.24, 0.00602, 2, 16, 1.45, 47.15, 0.00154, 15, 5.09, 1.38, 0.99846, 3, 16, 8.54, 18.91, 0.56326, 15, 12.18, -26.87, 0.43612, 68, -281.23, 152.62, 0.00063, 3, 17, 12.93, 41.99, 0.31344, 16, 9.44, -21.16, 0.685, 68, -280.33, 112.56, 0.00156, 2, 17, 10.55, 12.23, 0.88071, 16, 7.05, -50.91, 0.11929, 2, 17, 7.32, -8.57, 0.92351, 18, 25.97, 43.02, 0.07649, 4, 16, -12.6, 8.03, 0.1429, 29, 4.19, -6.21, 0.54406, 15, -8.96, -37.74, 0.30192, 68, -302.37, 141.75, 0.01113, 5, 17, -9.32, 44.41, 0.33495, 16, -12.81, -18.74, 0.50962, 29, 2.59, 20.51, 0.11617, 31, -12.89, 41.83, 0.03462, 68, -302.58, 114.98, 0.00463, 5, 29, 22.18, -0.55, 0.56892, 30, -2.29, 0.58, 0.42204, 15, -27.29, -42.18, 0.00272, 32, -5.58, 49.74, 0.00129, 68, -320.7, 137.31, 0.00503, 4, 17, -8.25, 6.69, 0.92935, 16, -11.75, -56.45, 0.05796, 30, 4.41, 63.27, 0.00708, 31, 17.92, 63.61, 0.00561, 5, 17, -23.82, 1.63, 0.22793, 19, 9.02, -9.69, 0.68123, 16, -27.31, -61.51, 0.04057, 30, 20.7, 61.61, 0.02757, 31, 30.75, 53.46, 0.0227, 5, 19, 45.96, -9.35, 0.2821, 20, 6.37, -7.98, 0.71361, 29, 48.82, 76.97, 0.0027, 30, 57.33, 56.84, 0.00089, 31, 59.07, 29.74, 0.0007, 7, 17, -41.04, -4.41, 0.00748, 19, 27.24, -8.59, 0.86023, 20, -11.92, -12.06, 0.04889, 16, -44.54, -67.56, 0.00328, 29, 30.94, 71.36, 0.05032, 30, 38.89, 60.18, 0.01631, 31, 45.33, 42.47, 0.01349, 5, 22, 44.57, 17.06, 0.06134, 23, 12.15, 19.77, 0.72631, 27, 51.38, -52.67, 0.00536, 28, 31.03, -47.52, 0.20579, 68, -474.68, 60.48, 0.0012, 7, 21, 72.95, 12.48, 0.00078, 22, 31.03, 19.13, 0.31746, 23, -1.54, 19.19, 0.43929, 26, 66.83, -53.56, 0.00251, 27, 38.41, -48.27, 0.03498, 28, 17.45, -45.74, 0.20284, 68, -462.86, 53.56, 0.00215, 8, 21, 54.63, 19.12, 0.07733, 22, 11.75, 21.95, 0.5551, 23, -20.99, 18.24, 0.06327, 25, 76.92, -54.09, 1e-05, 26, 49.49, -44.67, 0.03934, 27, 19.92, -42.13, 0.12862, 28, -1.88, -43.33, 0.13348, 68, -445.98, 43.83, 0.00286, 9, 20, 83.46, 16.23, 0.00022, 21, 36.78, 23.66, 0.38486, 22, -6.64, 22.79, 0.25035, 23, -39.21, 15.53, 0.0017, 25, 61.58, -43.9, 0.01406, 26, 32.35, -37.94, 0.16621, 27, 1.95, -38.08, 0.14386, 28, -20.3, -42.88, 0.03508, 68, -429.19, 36.26, 0.00367, 8, 20, 63.9, 22.23, 0.05313, 21, 16.44, 25.83, 0.49265, 22, -27.01, 20.81, 0.02583, 25, 43.11, -35.11, 0.12487, 26, 12.44, -33.24, 0.26227, 27, -18.45, -36.48, 0.03564, 28, -40.61, -45.3, 0.00099, 68, -409.53, 30.59, 0.00462, 7, 20, 44.76, 25.8, 0.28885, 21, -3.03, 25.69, 0.22898, 24, 57.38, -32.18, 0.01288, 25, 24.68, -28.8, 0.35441, 26, -6.9, -30.94, 0.10909, 27, -37.91, -37.16, 0.00064, 68, -390.33, 27.35, 0.00515, 6, 20, 24.08, 26.63, 0.47422, 21, -23.49, 22.57, 0.01549, 24, 37.85, -25.34, 0.1927, 25, 4.35, -24.97, 0.30625, 26, -27.59, -31.48, 0.00559, 68, -369.64, 26.87, 0.00575, 6, 19, 53.15, 22.66, 0.06672, 20, 5.08, 24.8, 0.44919, 18, -38.76, 10.03, 0.0427, 24, 19.15, -21.52, 0.37034, 25, -14.72, -24.01, 0.06497, 68, -350.67, 29.02, 0.00608, 6, 19, 33.43, 23.86, 0.33864, 20, -14.29, 20.89, 0.18814, 18, -19.46, 14.27, 0.27364, 24, -0.51, -19.59, 0.19244, 25, -34.44, -25.06, 0.00109, 68, -331.38, 33.26, 0.00606, 5, 17, -21.84, -30.46, 0.0103, 19, 15.9, 21.72, 0.24449, 20, -30.68, 14.31, 0.00901, 18, -3.18, 21.12, 0.71796, 68, -315.1, 40.11, 0.01823, 4, 17, -7.31, -21.19, 0.25607, 19, -0.61, 16.78, 0.13354, 18, 11.34, 30.4, 0.59315, 68, -300.58, 49.38, 0.01724, 6, 21, 81.76, 43.27, 0.00038, 22, 33.45, 51.07, 0.05446, 23, -5.31, 51, 0.10309, 27, 46.37, -17.25, 0.02539, 28, 19.19, -13.76, 0.81448, 68, -476.89, 24.77, 0.0022, 7, 21, 65.49, 50.36, 0.00724, 22, 16.09, 54.73, 0.05654, 23, -23.06, 51.25, 0.02155, 26, 64.17, -15.05, 0.00305, 27, 29.91, -10.61, 0.38335, 28, 1.75, -10.47, 0.52388, 68, -462.09, 14.96, 0.0044, 7, 21, 47.11, 53.03, 0.02817, 22, -2.45, 53.64, 0.04367, 23, -41.04, 46.61, 0.00212, 26, 46.28, -10.09, 0.07124, 27, 11.46, -8.44, 0.81349, 28, -16.76, -11.95, 0.03528, 68, -444.46, 9.13, 0.00603, 8, 20, 80.13, 46.71, 0.00087, 21, 27.71, 52.95, 0.05223, 22, -21.44, 49.65, 0.01784, 25, 62.72, -13.26, 0.00818, 26, 27.01, -7.74, 0.8251, 27, -7.93, -9.06, 0.08766, 28, -35.66, -16.35, 0.00074, 68, -425.34, 5.84, 0.00737, 7, 20, 62.1, 47.92, 0.01267, 21, 9.78, 50.71, 0.05554, 22, -38.55, 43.84, 0.00296, 25, 45.07, -9.43, 0.12968, 26, 8.95, -7.72, 0.78814, 27, -25.79, -11.79, 0.00261, 68, -407.29, 4.93, 0.0084, 5, 17, -41.43, 60.11, 0.00116, 16, -44.93, -3.04, 0.00031, 30, 13.2, 1, 0.98168, 31, -8.17, 6.4, 0.01045, 68, -334.69, 130.68, 0.0064, 7, 17, -53.17, 51.56, 0.00055, 19, 23.58, -65.74, 0.00146, 16, -56.67, -11.58, 9e-05, 29, 46.83, 16.34, 0.00248, 30, 27.39, 4.07, 0.0097, 31, 5.45, 1.36, 0.97803, 68, -346.43, 122.14, 0.00769], "hull": 73, "edges": [0, 144, 0, 2, 8, 10, 28, 30, 30, 32, 32, 34, 34, 36, 48, 50, 50, 52, 52, 54, 98, 100, 104, 106, 106, 108, 108, 110, 110, 112, 114, 116, 116, 118, 120, 122, 122, 124, 130, 132, 136, 138, 138, 140, 140, 142, 142, 144, 88, 90, 96, 98, 94, 96, 90, 92, 92, 94, 100, 102, 102, 104, 112, 114, 118, 120, 124, 126, 126, 128, 128, 130, 132, 134, 134, 136, 6, 8, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 36, 38, 44, 46, 46, 48, 42, 44, 54, 56, 56, 58, 38, 40, 40, 42, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 74, 76, 76, 78], "width": 250, "height": 207}}, "hat": {"hat": {"type": "mesh", "uvs": [0.44045, 0.02479, 0.67575, 0.14584, 0.85363, 0.30198, 0.95267, 0.43109, 0.9957, 0.54384, 0.99579, 0.61622, 0.96238, 0.69511, 0.88979, 0.77173, 0.87507, 0.88066, 0.84164, 0.81238, 0.79538, 0.75415, 0.72201, 0.68756, 0.64228, 0.73932, 0.53934, 0.788, 0.42448, 0.82995, 0.31109, 0.87845, 0.18523, 0.94129, 0.10395, 0.98857, 0.06006, 0.98993, 0.0527, 0.95261, 0.12273, 0.80863, 0.12213, 0.77843, 0.04231, 0.71608, 0.00428, 0.62253, 0, 0.54299, 0.02231, 0.40794, 0.08237, 0.26458, 0.18072, 0.12382, 0.29021, 0.00369, 0.2735, 0.45144, 0.38126, 0.41145, 0.50827, 0.40849, 0.64793, 0.4544, 0.76314, 0.53629, 0.85366, 0.64737, 0.1816, 0.53192, 0.11436, 0.68595, 0.18927, 0.72182, 0.28345, 0.66643, 0.39291, 0.63144, 0.51001, 0.62124, 0.64068, 0.64164], "triangles": [18, 19, 17, 16, 17, 20, 17, 19, 20, 16, 37, 15, 16, 20, 37, 8, 9, 7, 37, 38, 15, 15, 39, 14, 15, 38, 39, 14, 40, 13, 14, 39, 40, 7, 9, 34, 20, 21, 37, 13, 41, 12, 13, 40, 41, 22, 36, 21, 21, 36, 37, 9, 10, 34, 7, 34, 6, 34, 10, 33, 12, 41, 11, 38, 37, 35, 22, 23, 36, 6, 34, 5, 10, 11, 33, 11, 41, 33, 37, 36, 35, 35, 36, 24, 35, 29, 38, 38, 29, 39, 34, 4, 5, 34, 3, 4, 34, 33, 3, 41, 32, 33, 41, 40, 32, 29, 30, 39, 39, 31, 40, 39, 30, 31, 36, 23, 24, 40, 31, 32, 24, 25, 35, 33, 2, 3, 33, 32, 2, 25, 26, 35, 35, 26, 29, 32, 1, 2, 32, 31, 1, 26, 27, 29, 30, 29, 28, 29, 27, 28, 30, 0, 31, 30, 28, 0, 31, 0, 1], "vertices": [2, 6, 231.37, 1.96, 0.95879, 68, -190.64, 96.42, 0.04121, 2, 6, 202.9, -57.4, 0.96681, 68, -219.12, 37.07, 0.03319, 2, 6, 171.41, -100.69, 0.98175, 68, -250.6, -6.23, 0.01825, 2, 6, 147.39, -123.78, 0.99308, 68, -274.63, -29.31, 0.00692, 3, 6, 128.22, -132.43, 0.99518, 68, -293.8, -37.97, 0.00282, 69, -212, -37.97, 0.002, 3, 6, 117.06, -130.69, 0.99455, 68, -304.95, -36.23, 0.00121, 69, -223.15, -36.23, 0.00424, 2, 6, 106.3, -119.93, 0.99355, 69, -233.92, -25.47, 0.00645, 2, 6, 97.52, -98.86, 0.99039, 69, -242.69, -4.39, 0.00961, 1, 6, 81.35, -92.31, 1, 2, 6, 93.26, -85.12, 0.99785, 68, -328.75, 9.34, 0.00215, 2, 6, 104.17, -74.29, 0.98804, 68, -317.85, 20.17, 0.01196, 2, 6, 117.49, -56.48, 0.97526, 68, -304.52, 37.98, 0.02474, 2, 6, 112.84, -34.12, 0.96225, 68, -309.17, 60.34, 0.03775, 2, 6, 109.64, -5.69, 0.95256, 68, -312.38, 88.78, 0.04744, 2, 6, 107.96, 25.74, 0.9476, 68, -314.05, 120.2, 0.0524, 2, 6, 105.22, 56.94, 0.94775, 68, -316.79, 151.4, 0.05225, 2, 6, 100.79, 91.78, 0.95561, 68, -321.22, 186.25, 0.04439, 2, 6, 96.9, 114.45, 0.96497, 68, -325.12, 208.91, 0.03503, 2, 6, 98.52, 126.1, 0.97338, 68, -323.49, 220.56, 0.02662, 2, 6, 104.58, 127.14, 0.98044, 68, -317.44, 221.6, 0.01956, 2, 6, 123.84, 105.11, 0.98558, 68, -298.17, 199.57, 0.01442, 2, 6, 128.52, 104.53, 0.98533, 68, -293.49, 198.99, 0.01467, 2, 6, 141.46, 124.15, 0.99015, 68, -280.55, 218.61, 0.00985, 2, 6, 157.46, 131.94, 0.98681, 68, -264.55, 226.4, 0.01319, 2, 6, 169.9, 131.14, 0.98359, 68, -252.12, 225.61, 0.01641, 2, 6, 189.78, 121.96, 0.97815, 68, -232.24, 216.42, 0.02185, 2, 6, 209.36, 102.57, 0.96933, 68, -212.65, 197.04, 0.03067, 2, 6, 226.95, 73.12, 0.96208, 68, -195.06, 167.58, 0.03792, 2, 6, 240.89, 41.22, 0.95734, 68, -181.12, 135.68, 0.04266, 2, 6, 172.59, 56.52, 0.96777, 68, -249.42, 150.98, 0.03223, 2, 6, 174.26, 27.02, 0.96487, 68, -247.76, 121.48, 0.03513, 2, 6, 169.41, -6.68, 0.9659, 68, -252.6, 87.78, 0.0341, 2, 6, 156.51, -42.54, 0.97089, 68, -265.5, 51.93, 0.02911, 2, 6, 139.08, -71.05, 0.97994, 68, -282.93, 23.42, 0.02006, 2, 6, 118.19, -92.31, 0.99255, 68, -303.82, 2.15, 0.00745, 2, 6, 164.02, 82.8, 0.97473, 68, -257.99, 177.26, 0.02527, 2, 6, 143.09, 104.34, 0.98523, 68, -278.92, 198.8, 0.01477, 2, 6, 134.44, 85.38, 0.97456, 68, -287.57, 179.85, 0.02544, 2, 6, 139.05, 59.1, 0.96618, 68, -282.96, 153.57, 0.03382, 2, 6, 139.87, 29.28, 0.96199, 68, -282.14, 123.74, 0.03801, 2, 6, 136.56, -1.97, 0.96276, 68, -285.46, 92.49, 0.03724, 2, 6, 127.96, -36.07, 0.96784, 68, -294.05, 58.4, 0.03216], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 20, 22, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 22, 24, 16, 18, 18, 20, 10, 12, 12, 14, 8, 10, 56, 0, 52, 54, 54, 56, 44, 46, 46, 48, 34, 36, 36, 38, 32, 34, 24, 26, 26, 28, 28, 30, 30, 32, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 58, 70, 70, 72, 40, 74, 74, 76, 76, 78, 78, 80, 80, 82], "width": 268, "height": 156}}, "head": {"head": {"type": "mesh", "uvs": [0.13106, 0.09419, 0.22185, 0.08178, 0.35603, 0.06345, 0.5533, 0.0365, 0.70815, 0.01534, 0.80426, 0.00221, 0.88512, 0.11603, 0.94678, 0.23943, 0.97725, 0.35191, 0.99367, 0.47404, 0.98733, 0.58836, 0.92085, 0.71714, 0.83844, 0.80548, 0.66411, 0.91739, 0.53542, 0.98199, 0.45986, 0.99426, 0.38735, 0.98639, 0.29105, 0.91172, 0.15438, 0.80321, 0.09449, 0.71702, 0.0657, 0.62903, 0.04819, 0.49841, 0.00981, 0.41451, 0.00281, 0.35454, 0.01429, 0.28015, 0.03325, 0.20939, 0.0755, 0.10178, 0.29004, 0.43317, 0.38738, 0.41851, 0.46057, 0.46811, 0.46294, 0.60006, 0.47269, 0.62006, 0.4699, 0.64837, 0.45082, 0.665, 0.40718, 0.66819, 0.37269, 0.681, 0.30199, 0.68137, 0.46237, 0.56432, 0.46147, 0.51825, 0.31924, 0.481, 0.4415, 0.36181, 0.68886, 0.26666, 0.74855, 0.2654, 0.82158, 0.27364, 0.81844, 0.25905, 0.75326, 0.23749, 0.68023, 0.23305, 0.41322, 0.32058, 0.61791, 0.24518, 0.64433, 0.27902, 0.54409, 0.32095, 0.51322, 0.28125, 0.46303, 0.29978, 0.49397, 0.34267, 0.59482, 0.30024, 0.56649, 0.26122, 0.4093, 0.35025, 0.23883, 0.39049, 0.06026, 0.35568, 0.04918, 0.31987, 0.09967, 0.32335, 0.2376, 0.3517, 0.25915, 0.37358, 0.14849, 0.37403, 0.10037, 0.36635, 0.19428, 0.37928, 0.19881, 0.33715, 0.14832, 0.32845, 0.28187, 0.47074, 0.26152, 0.45066, 0.23158, 0.43276, 0.19445, 0.42067, 0.15194, 0.4197, 0.11601, 0.42647, 0.08668, 0.44872, 0.08548, 0.48064, 0.09829, 0.50912, 0.13278, 0.53335, 0.18427, 0.53626, 0.22379, 0.5261, 0.2568, 0.50482, 0.28068, 0.48789, 0.29238, 0.46897, 0.27009, 0.44018, 0.23556, 0.41948, 0.19601, 0.40733, 0.15144, 0.40328, 0.11022, 0.40913, 0.07345, 0.43163, 0.05953, 0.46177, 0.49545, 0.46746, 0.49656, 0.44316, 0.5116, 0.41392, 0.53722, 0.38872, 0.57232, 0.36802, 0.62245, 0.35498, 0.68039, 0.35228, 0.72997, 0.35992, 0.76228, 0.38017, 0.75336, 0.41932, 0.72607, 0.44901, 0.68206, 0.47016, 0.63248, 0.47376, 0.56508, 0.47376, 0.52163, 0.47016, 0.46871, 0.43033, 0.49266, 0.39388, 0.52497, 0.36554, 0.57121, 0.34619, 0.62413, 0.33629, 0.67983, 0.33449, 0.73665, 0.33899, 0.79514, 0.35654, 0.48316, 0.47477, 0.55001, 0.48512, 0.62689, 0.49547, 0.69763, 0.49412, 0.75445, 0.47612, 0.79846, 0.43248, 0.29241, 0.49324, 0.2445, 0.53013, 0.18991, 0.55263, 0.11136, 0.55983, 0.07181, 0.52834, 0.58919, 0.73818, 0.55782, 0.74831, 0.5119, 0.74523, 0.46525, 0.7337, 0.38639, 0.74211, 0.33762, 0.73351, 0.32113, 0.75113, 0.36041, 0.73373, 0.40741, 0.72709, 0.43286, 0.72492, 0.30489, 0.76999, 0.2852, 0.773, 0.26967, 0.7479, 0.25293, 0.7728, 0.28509, 0.79949, 0.28817, 0.78935, 0.30447, 0.79095, 0.28207, 0.78047, 0.30987, 0.7828, 0.33334, 0.7823, 0.36094, 0.77398, 0.40251, 0.77906, 0.44139, 0.76356, 0.48679, 0.76528, 0.53515, 0.76198, 0.56813, 0.75378, 0.59696, 0.73953, 0.33898, 0.83247, 0.36821, 0.84743, 0.42298, 0.84867, 0.47739, 0.83756, 0.51481, 0.81484, 0.53835, 0.78411, 0.56807, 0.76089, 0.59411, 0.74927, 0.6166, 0.76365, 0.62925, 0.7205, 0.56412, 0.70158, 0.35262, 0.80481, 0.40756, 0.80785, 0.47148, 0.7936, 0.39683, 0.8628, 0.48299, 0.85137, 0.40481, 0.92759, 0.50218, 0.91499, 0.3207, 0.81212, 0.59669, 0.87647, 0.7435, 0.78301, 0.85689, 0.52718, 0.82162, 0.67076, 0.33993, 0.89202, 0.23776, 0.80781, 0.16686, 0.7076, 0.14289, 0.62003, 0.23567, 0.59829, 0.313, 0.55347, 0.66574, 0.56465, 0.63477, 0.65961, 0.2143, 0.20401, 0.3817, 0.18242, 0.60772, 0.12844, 0.10608, 0.20716, 0.75696, 0.11523, 0.87038, 0.39019], "triangles": [22, 23, 58, 23, 59, 58, 23, 24, 59, 87, 58, 64, 87, 64, 63, 64, 67, 63, 63, 67, 66, 58, 60, 64, 64, 60, 67, 58, 59, 60, 66, 67, 182, 60, 185, 67, 60, 59, 24, 57, 62, 27, 85, 65, 57, 86, 63, 65, 65, 61, 57, 57, 61, 62, 65, 66, 61, 65, 63, 66, 47, 62, 61, 182, 47, 61, 182, 61, 66, 28, 56, 40, 28, 62, 56, 106, 40, 53, 56, 62, 47, 107, 53, 50, 56, 47, 40, 40, 52, 53, 40, 47, 52, 53, 52, 51, 47, 183, 52, 52, 183, 51, 108, 50, 54, 53, 51, 50, 109, 54, 49, 110, 49, 41, 50, 55, 54, 50, 51, 55, 54, 48, 49, 54, 55, 48, 51, 183, 55, 49, 48, 46, 48, 55, 184, 48, 184, 46, 111, 42, 43, 111, 41, 42, 41, 49, 46, 42, 44, 43, 43, 44, 7, 42, 41, 45, 42, 45, 44, 45, 41, 46, 44, 45, 6, 45, 46, 186, 16, 167, 15, 15, 168, 14, 15, 167, 168, 16, 17, 167, 167, 17, 174, 14, 170, 13, 14, 168, 170, 174, 165, 167, 167, 166, 168, 167, 165, 166, 13, 171, 12, 13, 170, 171, 168, 166, 170, 18, 175, 17, 17, 175, 174, 174, 175, 151, 138, 151, 175, 140, 169, 138, 138, 139, 140, 138, 169, 151, 174, 152, 165, 174, 151, 152, 166, 155, 170, 155, 156, 170, 170, 156, 159, 159, 156, 157, 170, 159, 171, 159, 157, 158, 166, 153, 154, 166, 165, 153, 165, 152, 153, 166, 154, 155, 152, 163, 153, 153, 163, 154, 151, 162, 152, 152, 162, 163, 163, 164, 154, 154, 164, 155, 151, 169, 162, 155, 164, 156, 156, 147, 148, 147, 156, 164, 169, 140, 143, 169, 143, 162, 143, 140, 142, 163, 162, 145, 162, 144, 145, 163, 146, 164, 163, 145, 146, 175, 137, 138, 137, 175, 176, 11, 12, 173, 162, 143, 144, 175, 18, 176, 18, 19, 176, 137, 141, 138, 138, 141, 139, 164, 146, 147, 140, 139, 142, 139, 141, 142, 156, 148, 157, 159, 160, 171, 12, 171, 173, 171, 160, 173, 141, 135, 142, 135, 134, 142, 142, 134, 143, 131, 144, 129, 129, 144, 130, 134, 130, 143, 130, 144, 143, 141, 137, 135, 146, 132, 133, 132, 146, 128, 144, 128, 145, 146, 145, 128, 144, 131, 128, 134, 135, 136, 135, 137, 136, 137, 176, 136, 134, 136, 130, 147, 126, 148, 146, 127, 147, 147, 127, 126, 158, 150, 159, 159, 150, 160, 146, 133, 127, 157, 148, 149, 148, 126, 125, 157, 149, 158, 149, 148, 125, 150, 158, 149, 130, 136, 129, 149, 125, 124, 150, 149, 124, 124, 125, 161, 136, 36, 129, 136, 176, 36, 125, 126, 161, 126, 127, 161, 128, 131, 132, 150, 124, 160, 124, 161, 160, 132, 131, 35, 133, 33, 127, 161, 33, 32, 161, 127, 33, 131, 129, 35, 129, 36, 35, 133, 132, 34, 132, 35, 34, 133, 34, 33, 160, 181, 173, 160, 161, 181, 11, 173, 10, 19, 177, 176, 19, 20, 177, 176, 178, 36, 176, 177, 178, 181, 161, 31, 35, 36, 179, 34, 35, 179, 181, 180, 173, 173, 180, 172, 117, 172, 180, 173, 172, 10, 117, 180, 116, 179, 36, 178, 31, 33, 34, 30, 34, 179, 32, 33, 31, 38, 37, 179, 38, 179, 39, 38, 39, 29, 37, 30, 179, 161, 32, 31, 181, 31, 180, 114, 115, 180, 114, 180, 37, 30, 31, 34, 20, 122, 177, 20, 123, 122, 20, 21, 123, 31, 37, 180, 177, 121, 178, 177, 122, 121, 31, 30, 37, 178, 120, 179, 178, 121, 120, 10, 172, 9, 180, 115, 116, 114, 38, 113, 114, 37, 38, 121, 77, 78, 121, 122, 77, 122, 123, 77, 77, 123, 76, 39, 28, 29, 120, 119, 179, 179, 119, 39, 121, 79, 120, 121, 78, 79, 79, 78, 71, 77, 76, 72, 78, 72, 71, 120, 80, 119, 80, 81, 119, 120, 79, 80, 72, 78, 77, 75, 73, 72, 123, 21, 76, 117, 118, 172, 172, 187, 9, 172, 118, 187, 79, 70, 80, 79, 71, 70, 38, 29, 113, 21, 75, 76, 72, 76, 75, 73, 75, 74, 81, 80, 68, 80, 70, 69, 21, 89, 75, 21, 22, 89, 115, 101, 116, 115, 102, 101, 114, 103, 115, 115, 103, 102, 116, 100, 117, 116, 101, 100, 119, 81, 82, 81, 68, 82, 119, 82, 39, 68, 80, 69, 114, 113, 104, 113, 90, 104, 114, 104, 103, 28, 39, 27, 75, 89, 74, 100, 99, 117, 117, 99, 118, 113, 29, 90, 187, 8, 9, 95, 101, 102, 102, 103, 94, 102, 94, 95, 103, 92, 93, 103, 93, 94, 82, 68, 83, 95, 96, 101, 101, 96, 100, 90, 91, 104, 91, 92, 104, 103, 104, 92, 68, 69, 83, 83, 27, 82, 39, 82, 27, 90, 29, 91, 29, 28, 105, 91, 29, 105, 89, 88, 74, 89, 22, 88, 69, 70, 83, 99, 96, 97, 99, 100, 96, 74, 88, 73, 91, 105, 92, 27, 83, 84, 83, 70, 84, 84, 57, 27, 27, 62, 28, 70, 71, 84, 99, 98, 118, 98, 112, 118, 118, 112, 187, 22, 58, 88, 88, 87, 73, 87, 88, 58, 105, 106, 92, 106, 105, 40, 73, 87, 72, 71, 85, 84, 71, 72, 85, 87, 86, 72, 72, 86, 85, 84, 85, 57, 99, 97, 98, 105, 28, 40, 92, 106, 93, 87, 63, 86, 86, 65, 85, 106, 107, 93, 106, 53, 107, 8, 187, 43, 93, 107, 94, 112, 98, 111, 107, 108, 94, 94, 108, 95, 107, 50, 108, 98, 97, 111, 97, 96, 111, 187, 112, 43, 112, 111, 43, 95, 109, 96, 95, 108, 109, 109, 110, 96, 96, 110, 111, 43, 7, 8, 47, 182, 183, 108, 54, 109, 110, 41, 111, 110, 109, 49, 182, 1, 183, 67, 185, 182, 185, 60, 24, 185, 24, 25, 7, 44, 6, 55, 183, 184, 45, 186, 6, 46, 184, 186, 25, 26, 185, 185, 0, 182, 185, 26, 0, 1, 2, 183, 182, 0, 1, 183, 3, 184, 183, 2, 3, 184, 4, 186, 184, 3, 4, 186, 5, 6, 186, 4, 5], "vertices": [2, 6, 124.26, 69.23, 0.97829, 68, -297.75, 163.69, 0.02171, 2, 6, 124.42, 55.69, 0.97335, 68, -297.6, 150.16, 0.02665, 2, 6, 124.64, 35.69, 0.97051, 68, -297.37, 130.15, 0.02949, 2, 6, 124.97, 6.28, 0.97027, 68, -297.04, 100.74, 0.02973, 2, 6, 125.23, -16.8, 0.97517, 68, -296.78, 77.66, 0.02483, 2, 6, 125.39, -31.13, 0.98369, 68, -296.62, 63.33, 0.01631, 2, 6, 103.08, -39.65, 0.98389, 68, -318.94, 54.81, 0.01611, 2, 6, 79.48, -45.11, 0.98493, 68, -342.53, 49.36, 0.01507, 2, 6, 58.56, -46.34, 0.98532, 68, -363.45, 48.12, 0.01468, 2, 6, 36.23, -45.26, 0.98649, 68, -385.78, 49.2, 0.01351, 2, 6, 15.82, -41.1, 0.98599, 68, -406.19, 53.36, 0.01401, 2, 6, -5.81, -27.8, 0.97938, 68, -427.82, 66.66, 0.02062, 2, 6, -19.8, -13.33, 0.97552, 68, -441.82, 81.13, 0.02448, 2, 6, -35.93, 15.15, 0.96998, 68, -457.94, 109.62, 0.03002, 2, 6, -44.6, 35.67, 0.96613, 68, -466.61, 130.13, 0.03387, 2, 6, -45.08, 46.99, 0.96485, 68, -467.09, 141.45, 0.03515, 2, 6, -42, 57.3, 0.96783, 68, -464.01, 151.76, 0.03217, 2, 6, -26.37, 69.16, 0.97231, 68, -448.39, 163.63, 0.02769, 2, 6, -3.74, 85.93, 0.97839, 68, -425.75, 180.4, 0.02161, 2, 6, 13.13, 92.19, 0.98242, 68, -408.88, 186.65, 0.01758, 2, 6, 29.61, 93.88, 0.98469, 68, -392.41, 188.34, 0.01531, 2, 6, 53.49, 92.72, 0.98547, 68, -368.52, 187.18, 0.01453, 2, 6, 69.45, 95.91, 0.98368, 68, -352.56, 190.38, 0.01632, 5, 14, -23.8, -7.72, 0.00143, 13, -6.85, -4.57, 0.00216, 12, -2.29, -0.56, 0.97131, 68, -341.62, 189.69, 0.00175, 69, -259.82, 189.69, 0.02335, 2, 6, 93.5, 91.45, 0.98385, 68, -328.51, 185.92, 0.01615, 2, 6, 105.79, 86.7, 0.98336, 68, -316.22, 181.16, 0.01664, 2, 6, 124.17, 77.51, 0.98222, 68, -297.84, 171.97, 0.01778, 2, 6, 59.68, 55.75, 0.96511, 68, -362.33, 150.21, 0.03489, 2, 6, 60.09, 41.2, 0.96074, 68, -361.92, 135.66, 0.03926, 2, 6, 49.5, 31.98, 0.96057, 68, -372.51, 126.44, 0.03943, 2, 6, 25.72, 35.37, 0.96013, 68, -396.29, 129.84, 0.03987, 2, 6, 21.9, 34.52, 0.96024, 68, -400.11, 128.99, 0.03976, 2, 6, 16.88, 35.73, 0.96044, 68, -405.14, 130.19, 0.03956, 2, 6, 14.32, 38.97, 0.96054, 68, -407.69, 133.44, 0.03946, 2, 6, 14.75, 45.4, 0.96058, 68, -407.26, 139.86, 0.03942, 2, 6, 13.24, 50.77, 0.96249, 68, -408.78, 145.23, 0.03751, 2, 6, 14.79, 61.05, 0.96589, 68, -407.23, 155.51, 0.03411, 2, 6, 32.16, 34.44, 0.96006, 68, -389.85, 128.91, 0.03994, 2, 6, 40.46, 33.27, 0.96027, 68, -381.55, 127.73, 0.03973, 2, 6, 50.42, 52.86, 0.96365, 68, -371.6, 147.33, 0.03635, 4, 9, 50.53, 25.16, 0.00535, 10, 39.31, 5.03, 0.00082, 11, 13.58, 4, 0.99305, 68, -352.97, 126.2, 0.00078, 5, 9, 17.18, 2.57, 0.8075, 10, -0.94, 3.63, 0.18903, 11, -26.66, 5.69, 0.0019, 68, -341.52, 87.58, 0.001, 69, -259.72, 87.58, 0.00057, 4, 9, 8.54, 1.03, 0.99309, 10, -9.08, 6.91, 0.00505, 11, -34.53, 9.58, 0.00084, 69, -260.86, 78.88, 0.00103, 4, 9, -2.3, 0.9, 0.98765, 10, -18.33, 12.56, 0.00094, 11, -43.32, 15.93, 0.00224, 69, -264.01, 68.51, 0.00917, 2, 9, -1.44, -1.66, 0.99081, 69, -261.32, 68.55, 0.00919, 2, 9, 8.62, -4.1, 0.99811, 69, -255.95, 77.4, 0.00189, 4, 9, 19.36, -3.29, 0.71919, 10, -2.21, -2.49, 0.27929, 68, -335.28, 87.88, 0.001, 69, -253.48, 87.88, 0.00052, 3, 10, 40.14, -3.51, 0.01441, 11, 13.74, -4.58, 0.985, 68, -344.91, 129.13, 0.00058, 3, 9, 28.08, 0.27, 0.00294, 10, 7.07, -4.12, 0.99522, 68, -336.04, 97.28, 0.00185, 4, 9, 23.32, 5.78, 0.11984, 10, 5.96, 3.08, 0.87391, 11, -19.83, 4.61, 0.0043, 68, -342.73, 94.4, 0.00195, 4, 9, 36.74, 15.54, 0.01503, 10, 22.52, 4.21, 0.78106, 11, -3.23, 4.47, 0.20256, 68, -347.97, 110.14, 0.00135, 3, 10, 23.8, -4.22, 0.81248, 11, -2.6, -4.04, 0.18633, 68, -340.13, 113.5, 0.00119, 3, 10, 31.91, -4.07, 0.06814, 11, 5.5, -4.5, 0.93098, 68, -342.31, 121.31, 0.00088, 4, 9, 43.43, 20.55, 0.00933, 10, 30.85, 4.9, 0.0493, 11, 5.13, 4.52, 0.94031, 68, -350.73, 118.04, 0.00106, 4, 9, 29.93, 10.69, 0.03288, 10, 14.18, 3.72, 0.94469, 11, -11.59, 4.63, 0.02078, 68, -345.41, 102.19, 0.00165, 3, 10, 15.17, -4.45, 0.99628, 11, -11.22, -3.6, 0.00219, 68, -337.74, 105.2, 0.00152, 4, 9, 55.52, 23.79, 0.00031, 10, 42.82, 1.21, 0.00014, 11, 16.78, -0.07, 0.99897, 68, -350.15, 130.55, 0.00058, 4, 14, 11.21, -3.21, 0.97629, 13, 28.45, -4.64, 0.0134, 12, 28.32, -18.17, 0.00375, 68, -353.49, 156.44, 0.00656, 4, 14, -15.71, -5.31, 0.00583, 13, 1.49, -3.22, 0.57691, 12, 5.62, -3.54, 0.40747, 69, -261.34, 181.38, 0.00979, 3, 13, -1.31, 2.88, 0.41569, 12, 6.23, 3.15, 0.57116, 69, -254.65, 181.98, 0.01314, 2, 13, 6.1, 3.62, 0.99497, 69, -256.43, 174.74, 0.00503, 2, 14, 8.86, 3.45, 0.99336, 68, -346.48, 155.52, 0.00664, 2, 14, 13.1, 0.64, 0.99177, 68, -350.91, 153.01, 0.00823, 4, 14, -2.34, -4.47, 0.22521, 13, 14.85, -4.13, 0.75151, 12, 16.77, -10.96, 0.0222, 68, -348.46, 169.09, 0.00108, 4, 14, -9.5, -5.33, 0.0319, 13, 7.64, -4.05, 0.88113, 12, 10.55, -7.31, 0.0825, 69, -264.17, 175.86, 0.00447, 4, 14, 4.35, -3.3, 0.88348, 13, 21.64, -3.83, 0.10576, 12, 22.81, -14.08, 0.00719, 68, -350.45, 162.59, 0.00356, 3, 14, 2.62, 4.2, 0.8088, 13, 20.89, 3.83, 0.18717, 68, -342.98, 160.74, 0.00403, 3, 14, -4.93, 3.41, 0.04611, 13, 13.3, 4.02, 0.95269, 68, -340.26, 167.82, 0.0012, 2, 6, 53.12, 58, 0.96839, 68, -368.9, 152.46, 0.03161, 2, 6, 57.19, 60.39, 0.96919, 68, -364.82, 154.85, 0.03081, 2, 6, 61.09, 64.23, 0.97023, 68, -360.92, 158.69, 0.02977, 2, 6, 64.12, 69.27, 0.97159, 68, -357.9, 163.74, 0.02841, 2, 6, 65.26, 75.42, 0.97452, 68, -356.75, 169.88, 0.02548, 2, 6, 64.87, 80.83, 0.97745, 68, -357.14, 175.29, 0.02255, 2, 6, 61.54, 85.72, 0.98123, 68, -360.47, 180.18, 0.01877, 2, 6, 55.83, 86.8, 0.98171, 68, -366.18, 181.26, 0.01829, 2, 6, 50.42, 85.74, 0.98092, 68, -371.59, 180.21, 0.01908, 2, 6, 45.27, 81.42, 0.9784, 68, -376.74, 175.89, 0.0216, 2, 6, 43.57, 74.03, 0.97369, 68, -378.44, 168.49, 0.02631, 2, 6, 44.49, 68, 0.97039, 68, -377.52, 162.47, 0.02961, 2, 6, 47.56, 62.61, 0.96923, 68, -374.45, 157.07, 0.03077, 2, 6, 50.06, 58.66, 0.96837, 68, -371.95, 153.12, 0.03163, 2, 6, 53.19, 56.42, 0.96801, 68, -368.82, 150.89, 0.03199, 2, 6, 58.88, 58.84, 0.96885, 68, -363.13, 153.31, 0.03115, 2, 6, 63.39, 63.27, 0.97005, 68, -358.62, 157.74, 0.02995, 2, 6, 66.48, 68.67, 0.97157, 68, -355.53, 163.13, 0.02843, 2, 6, 68.23, 75.03, 0.97478, 68, -353.78, 169.49, 0.02522, 2, 6, 68.12, 81.18, 0.97887, 68, -353.89, 175.64, 0.02113, 2, 6, 64.92, 87.15, 0.98247, 68, -357.1, 181.62, 0.01753, 2, 6, 59.82, 90.03, 0.98469, 68, -362.2, 184.49, 0.01531, 2, 6, 48.82, 26.89, 0.96057, 68, -373.2, 121.36, 0.03943, 2, 6, 53.16, 26.04, 0.96045, 68, -368.85, 120.51, 0.03955, 2, 6, 58.07, 23.03, 0.96023, 68, -363.94, 117.49, 0.03977, 2, 6, 62.02, 18.6, 0.95957, 68, -360, 113.06, 0.04043, 2, 6, 64.94, 12.91, 0.95933, 68, -357.08, 107.38, 0.04067, 2, 6, 66.13, 5.26, 0.9592, 68, -355.88, 99.73, 0.0408, 2, 6, 65.29, -3.23, 0.96008, 68, -356.72, 91.24, 0.03992, 2, 6, 62.78, -10.21, 0.96224, 68, -359.23, 84.26, 0.03776, 2, 6, 58.4, -14.33, 0.96418, 68, -363.61, 80.14, 0.03582, 2, 6, 51.57, -11.92, 0.96363, 68, -370.44, 82.54, 0.03637, 2, 6, 46.85, -7.12, 0.96199, 68, -375.16, 87.35, 0.03801, 2, 6, 44.06, -0.13, 0.96027, 68, -377.95, 94.34, 0.03973, 2, 6, 44.55, 7.17, 0.95977, 68, -377.46, 101.64, 0.04023, 2, 6, 46.09, 16.96, 0.95952, 68, -375.92, 111.42, 0.04048, 2, 6, 47.73, 23.17, 0.96046, 68, -374.28, 117.63, 0.03954, 2, 6, 56.1, 29.72, 0.96075, 68, -365.91, 124.19, 0.03925, 2, 6, 62.11, 25.21, 0.96114, 68, -359.91, 119.68, 0.03886, 2, 6, 66.46, 19.72, 0.96096, 68, -355.55, 114.18, 0.03904, 2, 6, 68.88, 12.46, 0.96006, 68, -353.13, 106.92, 0.03994, 2, 6, 69.45, 4.49, 0.95988, 68, -352.56, 98.96, 0.04012, 2, 6, 68.5, -3.65, 0.96072, 68, -353.51, 90.82, 0.03928, 2, 6, 66.39, -11.77, 0.96309, 68, -355.62, 82.69, 0.03691, 2, 6, 61.9, -19.77, 0.96497, 68, -360.11, 74.7, 0.03503, 2, 6, 47.78, 28.88, 0.96056, 68, -374.23, 123.35, 0.03944, 2, 6, 44.39, 19.47, 0.96026, 68, -377.62, 113.93, 0.03974, 2, 6, 40.77, 8.6, 0.95902, 68, -381.24, 103.07, 0.04098, 2, 6, 39.4, -1.71, 0.95968, 68, -382.62, 92.75, 0.04032, 2, 6, 41.33, -10.47, 0.96155, 68, -380.68, 83.99, 0.03845, 2, 6, 48.17, -18.1, 0.96503, 68, -373.84, 76.37, 0.03497, 2, 6, 48.83, 57.11, 0.96796, 68, -373.18, 151.57, 0.03204, 2, 6, 43.29, 65.11, 0.96962, 68, -378.72, 159.57, 0.03038, 2, 6, 40.5, 73.67, 0.97334, 68, -381.52, 168.14, 0.02666, 2, 6, 41, 85.28, 0.9806, 68, -381.01, 179.75, 0.0194, 2, 6, 47.57, 90.13, 0.98458, 68, -374.44, 184.6, 0.01542, 2, 6, -2, 20.95, 0.96249, 68, -424.01, 115.42, 0.03751, 2, 6, -3.1, 25.79, 0.96171, 68, -425.11, 120.26, 0.03829, 2, 6, -1.5, 32.38, 0.96073, 68, -423.51, 126.84, 0.03927, 2, 6, 1.64, 38.82, 0.96018, 68, -420.37, 133.29, 0.03982, 2, 6, 1.94, 50.51, 0.95994, 68, -420.08, 144.98, 0.04006, 2, 6, 4.6, 57.35, 0.96174, 68, -417.42, 151.81, 0.03826, 2, 6, 1.81, 60.24, 0.96303, 68, -420.21, 154.71, 0.03697, 2, 6, 4.04, 54.05, 0.96058, 68, -417.98, 148.51, 0.03942, 2, 6, 4.15, 47.04, 0.95962, 68, -417.86, 141.5, 0.04038, 2, 6, 3.96, 43.28, 0.96016, 68, -418.05, 137.74, 0.03984, 2, 6, -1.21, 63.14, 0.96474, 68, -423.22, 157.6, 0.03526, 2, 6, -1.3, 66.08, 0.96676, 68, -423.31, 160.54, 0.03324, 2, 6, 3.57, 67.63, 0.96786, 68, -418.45, 162.09, 0.03214, 2, 6, -0.53, 70.76, 0.96931, 68, -422.54, 165.23, 0.03069, 2, 6, -6.06, 66.85, 0.96697, 68, -428.07, 161.31, 0.03303, 2, 6, -4.31, 66.11, 0.96667, 68, -426.32, 160.58, 0.03333, 2, 6, -4.97, 63.79, 0.96565, 68, -426.98, 158.26, 0.03435, 2, 6, -2.57, 66.75, 0.96699, 68, -424.58, 161.21, 0.03301, 2, 6, -3.63, 62.78, 0.96524, 68, -425.64, 157.24, 0.03476, 2, 6, -4.07, 59.35, 0.96326, 68, -426.09, 153.82, 0.03674, 2, 6, -3.21, 55.11, 0.96151, 68, -425.22, 149.58, 0.03849, 2, 6, -5.08, 49.22, 0.9597, 68, -427.09, 143.68, 0.0403, 2, 6, -3.18, 43.13, 0.96002, 68, -425.19, 137.6, 0.03998, 2, 6, -4.53, 36.59, 0.96051, 68, -426.54, 131.05, 0.03949, 2, 6, -5.04, 29.47, 0.96137, 68, -427.05, 123.94, 0.03863, 2, 6, -4.32, 24.45, 0.9621, 68, -426.34, 118.92, 0.0379, 2, 6, -2.42, 19.86, 0.96264, 68, -424.43, 114.33, 0.03736, 2, 6, -13.22, 59.96, 0.96374, 68, -435.24, 154.42, 0.03626, 2, 6, -16.58, 56.14, 0.96265, 68, -438.59, 150.6, 0.03735, 2, 6, -18.06, 48.22, 0.96093, 68, -440.07, 142.68, 0.03907, 2, 6, -17.31, 40, 0.96066, 68, -439.32, 134.47, 0.03934, 2, 6, -14.08, 33.93, 0.9608, 68, -436.09, 128.39, 0.0392, 2, 6, -9.09, 29.64, 0.96139, 68, -431.11, 124.1, 0.03861, 2, 6, -5.6, 24.66, 0.96226, 68, -427.61, 119.13, 0.03774, 2, 6, -4.11, 20.55, 0.96287, 68, -426.12, 115.02, 0.03713, 2, 6, -7.21, 17.7, 0.96362, 68, -429.22, 112.16, 0.03638, 2, 6, 0.26, 14.64, 0.96255, 68, -421.75, 109.1, 0.03745, 2, 6, 5.15, 23.56, 0.96131, 68, -416.86, 118.02, 0.03869, 2, 6, -8.56, 57.19, 0.96152, 68, -430.58, 151.66, 0.03848, 2, 6, -10.37, 49.3, 0.95847, 68, -432.38, 143.76, 0.04153, 2, 6, -9.27, 39.62, 0.95906, 68, -431.28, 134.08, 0.04094, 2, 6, -20, 52.42, 0.96335, 68, -442.01, 146.88, 0.03665, 2, 6, -19.92, 39.58, 0.96233, 68, -441.93, 134.05, 0.03767, 2, 6, -31.83, 53.09, 0.96199, 68, -453.84, 147.56, 0.03801, 2, 6, -31.79, 38.6, 0.96039, 68, -453.81, 133.06, 0.03961, 2, 6, -9.15, 62.04, 0.96461, 68, -431.16, 156.5, 0.03539, 2, 6, -27.03, 23.78, 0.96413, 68, -449.05, 118.25, 0.03587, 2, 6, -13.59, -0.18, 0.96586, 68, -435.6, 94.28, 0.03414, 2, 6, 29.81, -23.9, 0.96674, 68, -392.21, 70.57, 0.03326, 2, 6, 4.8, -14.71, 0.96698, 68, -417.21, 79.76, 0.03302, 2, 6, -23.95, 61.51, 0.96644, 68, -445.96, 155.97, 0.03356, 2, 6, -6.47, 73.96, 0.971, 68, -428.49, 168.42, 0.029, 2, 6, 13.16, 81.41, 0.97354, 68, -408.85, 175.88, 0.02646, 2, 6, 29.46, 82.41, 0.97452, 68, -392.56, 176.88, 0.02548, 2, 6, 31.24, 68.32, 0.96826, 68, -390.77, 162.79, 0.03174, 2, 6, 37.53, 55.82, 0.96403, 68, -384.48, 150.29, 0.03597, 2, 6, 27.45, 4.92, 0.95981, 68, -394.57, 99.38, 0.04019, 2, 6, 11.08, 12.11, 0.96062, 68, -410.93, 106.57, 0.03938, 2, 6, 102.61, 60.25, 0.9679, 68, -319.4, 154.72, 0.0321, 2, 6, 102.66, 35.33, 0.96301, 68, -319.35, 129.8, 0.03699, 2, 6, 107.2, 0.98, 0.96235, 68, -314.82, 95.45, 0.03765, 2, 6, 104.53, 76.06, 0.97619, 68, -317.49, 170.52, 0.02381, 2, 6, 106.15, -21.06, 0.97095, 68, -315.86, 73.4, 0.02905, 2, 6, 54.13, -29.74, 0.97097, 68, -367.89, 64.72, 0.02903], "hull": 27, "edges": [12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 56, 58, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 74, 60, 58, 76, 76, 74, 54, 78, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 96, 98, 82, 94, 104, 104, 102, 80, 106, 106, 100, 98, 108, 108, 100, 96, 110, 110, 102, 94, 112, 112, 80, 44, 46, 46, 48, 116, 46, 118, 120, 122, 124, 124, 114, 116, 128, 128, 126, 114, 130, 130, 126, 122, 132, 120, 134, 134, 132, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 136, 32, 34, 34, 36, 20, 22, 10, 12, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 180, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 238, 240, 240, 242, 242, 244, 244, 246, 248, 250, 250, 252, 252, 254, 258, 260, 256, 262, 262, 258, 264, 256, 264, 266, 266, 254, 260, 268, 268, 270, 272, 274, 274, 276, 278, 280, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 316, 318, 320, 320, 322, 324, 326, 326, 328, 330, 332, 334, 336, 280, 338, 338, 302, 340, 342, 344, 346, 348, 350, 350, 352, 352, 354, 72, 358, 358, 78, 360, 362, 346, 342, 48, 50, 50, 52, 0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10], "width": 147, "height": 182}}, "head2": {"head": {"type": "mesh", "uvs": [0.16686, 0.7076, 0.23567, 0.59829, 0.313, 0.55347, 0.46237, 0.56432, 0.66574, 0.56465, 0.82162, 0.67076, 0.83844, 0.80548, 0.66411, 0.91739, 0.53542, 0.98199, 0.45986, 0.99426, 0.38735, 0.98639, 0.29105, 0.91172, 0.15438, 0.80321, 0.46294, 0.60006, 0.47269, 0.62006, 0.4699, 0.64837, 0.45082, 0.665, 0.40718, 0.66819, 0.37269, 0.681, 0.30199, 0.68137, 0.58919, 0.73818, 0.55782, 0.74831, 0.5119, 0.74523, 0.46525, 0.7337, 0.38639, 0.74211, 0.33762, 0.73351, 0.32113, 0.75113, 0.36041, 0.73373, 0.40741, 0.72709, 0.43286, 0.72492, 0.30489, 0.76999, 0.2852, 0.773, 0.26967, 0.7479, 0.25293, 0.7728, 0.28509, 0.79949, 0.28817, 0.78935, 0.30447, 0.79095, 0.28207, 0.78047, 0.30987, 0.7828, 0.33334, 0.7823, 0.36094, 0.77398, 0.40251, 0.77906, 0.44139, 0.76356, 0.48679, 0.76528, 0.53515, 0.76198, 0.56813, 0.75378, 0.59696, 0.73953, 0.33898, 0.83247, 0.36821, 0.84743, 0.42298, 0.84867, 0.47739, 0.83756, 0.51481, 0.81484, 0.53835, 0.78411, 0.56807, 0.76089, 0.59411, 0.74927, 0.6166, 0.76365, 0.62925, 0.7205, 0.56412, 0.70158, 0.35262, 0.80481, 0.40756, 0.80785, 0.47148, 0.7936, 0.39683, 0.8628, 0.48299, 0.85137, 0.40481, 0.92759, 0.50218, 0.91499, 0.3207, 0.81212, 0.59669, 0.87647, 0.7435, 0.78301, 0.33993, 0.89202, 0.23776, 0.80781, 0.63477, 0.65961], "triangles": [14, 13, 3, 4, 14, 3, 13, 14, 17, 70, 14, 4, 57, 15, 14, 2, 13, 17, 13, 2, 3, 15, 16, 14, 14, 16, 17, 2, 19, 1, 70, 4, 5, 17, 18, 2, 18, 19, 2, 70, 57, 14, 0, 1, 19, 56, 57, 70, 56, 70, 5, 29, 17, 16, 28, 18, 17, 29, 28, 17, 25, 19, 18, 27, 25, 18, 57, 23, 16, 57, 16, 15, 29, 16, 23, 28, 27, 18, 20, 57, 56, 46, 20, 56, 24, 27, 28, 22, 23, 57, 21, 22, 57, 32, 0, 19, 32, 19, 25, 20, 21, 57, 46, 45, 20, 45, 21, 20, 26, 32, 25, 46, 54, 45, 45, 44, 21, 53, 45, 54, 44, 22, 21, 53, 44, 45, 42, 29, 23, 55, 46, 56, 54, 46, 55, 43, 23, 22, 42, 23, 43, 43, 22, 44, 30, 32, 26, 33, 0, 32, 31, 33, 32, 30, 31, 32, 40, 27, 24, 42, 41, 24, 40, 24, 41, 28, 42, 24, 42, 28, 29, 37, 33, 31, 26, 40, 39, 30, 26, 39, 25, 40, 26, 27, 40, 25, 38, 30, 39, 31, 30, 38, 37, 31, 38, 67, 56, 5, 55, 56, 67, 52, 44, 53, 35, 37, 38, 36, 35, 38, 60, 42, 43, 34, 37, 35, 33, 37, 34, 69, 12, 0, 58, 39, 40, 67, 5, 6, 33, 69, 0, 69, 33, 34, 59, 41, 42, 59, 42, 60, 58, 40, 41, 59, 58, 41, 39, 36, 38, 65, 39, 58, 65, 36, 39, 43, 52, 60, 52, 43, 44, 51, 60, 52, 47, 65, 58, 50, 60, 51, 59, 60, 50, 48, 58, 59, 47, 58, 48, 49, 59, 50, 48, 59, 49, 62, 50, 51, 61, 48, 49, 62, 61, 49, 62, 49, 50, 55, 53, 54, 66, 55, 67, 55, 52, 53, 66, 52, 55, 51, 52, 66, 62, 51, 66, 68, 47, 48, 68, 48, 61, 34, 65, 47, 34, 35, 36, 36, 65, 34, 34, 47, 69, 68, 69, 47, 11, 69, 68, 12, 69, 11, 64, 62, 66, 7, 66, 67, 7, 67, 6, 63, 61, 62, 63, 62, 64, 68, 61, 63, 8, 64, 66, 8, 66, 7, 63, 11, 68, 10, 11, 63, 9, 63, 64, 9, 64, 8, 10, 63, 9], "vertices": [2, 6, 13.16, 81.41, 0.97354, 68, -408.85, 175.88, 0.02646, 2, 6, 31.24, 68.32, 0.96826, 68, -390.77, 162.79, 0.03174, 2, 6, 37.53, 55.82, 0.96403, 68, -384.48, 150.29, 0.03597, 2, 6, 32.16, 34.44, 0.96006, 68, -389.85, 128.91, 0.03994, 2, 6, 27.45, 4.92, 0.95981, 68, -394.57, 99.38, 0.04019, 2, 6, 4.8, -14.71, 0.96698, 68, -417.21, 79.76, 0.03302, 2, 6, -19.8, -13.33, 0.97552, 68, -441.82, 81.13, 0.02448, 2, 6, -35.93, 15.15, 0.96998, 68, -457.94, 109.62, 0.03002, 2, 6, -44.6, 35.67, 0.96613, 68, -466.61, 130.13, 0.03387, 2, 6, -45.08, 46.99, 0.96485, 68, -467.09, 141.45, 0.03515, 2, 6, -42, 57.3, 0.96783, 68, -464.01, 151.76, 0.03217, 2, 6, -26.37, 69.16, 0.97231, 68, -448.39, 163.63, 0.02769, 2, 6, -3.74, 85.93, 0.97839, 68, -425.75, 180.4, 0.02161, 2, 6, 25.72, 35.37, 0.96013, 68, -396.29, 129.84, 0.03987, 2, 6, 21.9, 34.52, 0.96024, 68, -400.11, 128.99, 0.03976, 2, 6, 16.88, 35.73, 0.96044, 68, -405.14, 130.19, 0.03956, 2, 6, 14.32, 38.97, 0.96054, 68, -407.69, 133.44, 0.03946, 2, 6, 14.75, 45.4, 0.96058, 68, -407.26, 139.86, 0.03942, 2, 6, 13.24, 50.77, 0.96249, 68, -408.78, 145.23, 0.03751, 2, 6, 14.79, 61.05, 0.96589, 68, -407.23, 155.51, 0.03411, 2, 6, -2, 20.95, 0.96249, 68, -424.01, 115.42, 0.03751, 2, 6, -3.1, 25.79, 0.96171, 68, -425.11, 120.26, 0.03829, 2, 6, -1.5, 32.38, 0.96073, 68, -423.51, 126.84, 0.03927, 2, 6, 1.64, 38.82, 0.96018, 68, -420.37, 133.29, 0.03982, 2, 6, 1.94, 50.51, 0.95994, 68, -420.08, 144.98, 0.04006, 2, 6, 4.6, 57.35, 0.96174, 68, -417.42, 151.81, 0.03826, 2, 6, 1.81, 60.24, 0.96303, 68, -420.21, 154.71, 0.03697, 2, 6, 4.04, 54.05, 0.96058, 68, -417.98, 148.51, 0.03942, 2, 6, 4.15, 47.04, 0.95962, 68, -417.86, 141.5, 0.04038, 2, 6, 3.96, 43.28, 0.96016, 68, -418.05, 137.74, 0.03984, 2, 6, -1.21, 63.14, 0.96474, 68, -423.22, 157.6, 0.03526, 2, 6, -1.3, 66.08, 0.96676, 68, -423.31, 160.54, 0.03324, 2, 6, 3.57, 67.63, 0.96786, 68, -418.45, 162.09, 0.03214, 2, 6, -0.53, 70.76, 0.96931, 68, -422.54, 165.23, 0.03069, 2, 6, -6.06, 66.85, 0.96697, 68, -428.07, 161.31, 0.03303, 2, 6, -4.31, 66.11, 0.96667, 68, -426.32, 160.58, 0.03333, 2, 6, -4.97, 63.79, 0.96565, 68, -426.98, 158.26, 0.03435, 2, 6, -2.57, 66.75, 0.96699, 68, -424.58, 161.21, 0.03301, 2, 6, -3.63, 62.78, 0.96524, 68, -425.64, 157.24, 0.03476, 2, 6, -4.07, 59.35, 0.96326, 68, -426.09, 153.82, 0.03674, 2, 6, -3.21, 55.11, 0.96151, 68, -425.22, 149.58, 0.03849, 2, 6, -5.08, 49.22, 0.9597, 68, -427.09, 143.68, 0.0403, 2, 6, -3.18, 43.13, 0.96002, 68, -425.19, 137.6, 0.03998, 2, 6, -4.53, 36.59, 0.96051, 68, -426.54, 131.05, 0.03949, 2, 6, -5.04, 29.47, 0.96137, 68, -427.05, 123.94, 0.03863, 2, 6, -4.32, 24.45, 0.9621, 68, -426.34, 118.92, 0.0379, 2, 6, -2.42, 19.86, 0.96264, 68, -424.43, 114.33, 0.03736, 2, 6, -13.22, 59.96, 0.96374, 68, -435.24, 154.42, 0.03626, 2, 6, -16.58, 56.14, 0.96265, 68, -438.59, 150.6, 0.03735, 2, 6, -18.06, 48.22, 0.96093, 68, -440.07, 142.68, 0.03907, 2, 6, -17.31, 40, 0.96066, 68, -439.32, 134.47, 0.03934, 2, 6, -14.08, 33.93, 0.9608, 68, -436.09, 128.39, 0.0392, 2, 6, -9.09, 29.64, 0.96139, 68, -431.11, 124.1, 0.03861, 2, 6, -5.6, 24.66, 0.96226, 68, -427.61, 119.13, 0.03774, 2, 6, -4.11, 20.55, 0.96287, 68, -426.12, 115.02, 0.03713, 2, 6, -7.21, 17.7, 0.96362, 68, -429.22, 112.16, 0.03638, 2, 6, 0.26, 14.64, 0.96255, 68, -421.75, 109.1, 0.03745, 2, 6, 5.15, 23.56, 0.96131, 68, -416.86, 118.02, 0.03869, 2, 6, -8.56, 57.19, 0.96152, 68, -430.58, 151.66, 0.03848, 2, 6, -10.37, 49.3, 0.95847, 68, -432.38, 143.76, 0.04153, 2, 6, -9.27, 39.62, 0.95906, 68, -431.28, 134.08, 0.04094, 2, 6, -20, 52.42, 0.96335, 68, -442.01, 146.88, 0.03665, 2, 6, -19.92, 39.58, 0.96233, 68, -441.93, 134.05, 0.03767, 2, 6, -31.83, 53.09, 0.96199, 68, -453.84, 147.56, 0.03801, 2, 6, -31.79, 38.6, 0.96039, 68, -453.81, 133.06, 0.03961, 2, 6, -9.15, 62.04, 0.96461, 68, -431.16, 156.5, 0.03539, 2, 6, -27.03, 23.78, 0.96413, 68, -449.05, 118.25, 0.03587, 2, 6, -13.59, -0.18, 0.96586, 68, -435.6, 94.28, 0.03414, 2, 6, -23.95, 61.51, 0.96644, 68, -445.96, 155.97, 0.03356, 2, 6, -6.47, 73.96, 0.971, 68, -428.49, 168.42, 0.029, 2, 6, 11.08, 12.11, 0.96062, 68, -410.93, 106.57, 0.03938], "hull": 13, "edges": [12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 6, 26, 20, 22, 22, 24, 40, 42, 42, 44, 44, 46, 50, 52, 48, 54, 54, 50, 56, 48, 56, 58, 58, 46, 52, 60, 60, 62, 64, 66, 66, 68, 70, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 116, 118, 118, 120, 122, 124, 126, 128, 72, 130, 130, 94, 132, 134, 136, 138, 138, 0, 38, 4, 8, 140, 10, 134, 0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12], "width": 147, "height": 182}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.67736, 0.00072, 0.755, 0.00084, 0.79915, 0.0089, 0.87462, 0.02959, 0.93263, 0.05462, 0.97458, 0.08191, 0.9989, 0.10839, 0.99762, 0.13566, 0.98088, 0.16955, 0.96207, 0.20751, 0.9382, 0.24616, 0.83687, 0.36049, 0.77935, 0.42539, 0.76182, 0.44926, 0.76269, 0.46617, 0.7755, 0.48896, 0.77981, 0.50233, 0.80828, 0.5259, 0.83251, 0.54563, 0.85941, 0.5694, 0.8721, 0.58845, 0.87293, 0.61771, 0.85855, 0.64162, 0.77762, 0.69934, 0.64304, 0.77618, 0.6237, 0.79574, 0.60635, 0.81652, 0.60231, 0.83403, 0.66009, 0.84963, 0.69419, 0.86646, 0.69436, 0.88422, 0.65122, 0.91319, 0.62684, 0.94241, 0.61581, 0.97081, 0.61667, 0.97983, 0.56615, 0.9802, 0.55679, 0.93713, 0.51966, 0.98707, 0.51371, 0.99133, 0.47324, 0.99488, 0.34482, 0.99917, 0.04345, 0.99915, 0.00712, 0.99594, 0.00655, 0.98015, 0.0219, 0.96846, 0.08374, 0.95791, 0.19223, 0.9463, 0.25714, 0.91884, 0.2905, 0.89642, 0.3236, 0.86219, 0.34704, 0.83864, 0.37065, 0.82201, 0.39058, 0.80492, 0.40671, 0.78176, 0.41619, 0.76402, 0.42237, 0.71875, 0.41756, 0.61493, 0.43659, 0.56939, 0.44893, 0.54958, 0.44718, 0.53901, 0.42816, 0.52035, 0.39724, 0.49984, 0.3684, 0.48026, 0.36188, 0.46323, 0.36078, 0.43496, 0.30889, 0.36698, 0.20469, 0.23331, 0.19567, 0.20333, 0.19563, 0.1692, 0.20863, 0.14366, 0.23929, 0.11985, 0.28982, 0.09622, 0.34446, 0.07548, 0.39546, 0.06318, 0.42515, 0.0306, 0.49655, 0.01784, 0.61059, 0.00531, 0.55201, 0.04733, 0.55183, 0.07065, 0.49782, 0.0904, 0.44196, 0.11889, 0.40844, 0.19865, 0.42018, 0.2474, 0.45107, 0.3766, 0.65426, 0.37706, 0.72187, 0.25015, 0.75525, 0.20438, 0.7882, 0.16215, 0.79765, 0.12091, 0.78461, 0.08094, 0.74364, 0.04924, 0.69336, 0.02719, 0.40937, 0.1561, 0.45241, 0.43254, 0.44236, 0.45981, 0.44035, 0.48162, 0.47049, 0.50244, 0.50264, 0.52326, 0.51269, 0.54656, 0.66321, 0.54479, 0.64129, 0.52376, 0.63124, 0.50294, 0.62522, 0.48063, 0.61517, 0.45733, 0.61517, 0.43304, 0.48919, 0.61885, 0.53822, 0.74343, 0.72218, 0.62048, 0.71998, 0.58144, 0.50094, 0.57783], "triangles": [41, 45, 40, 45, 46, 40, 36, 39, 40, 41, 43, 44, 41, 44, 45, 41, 42, 43, 38, 39, 37, 36, 37, 39, 40, 47, 36, 46, 47, 40, 35, 33, 34, 35, 36, 33, 33, 36, 32, 32, 36, 31, 47, 48, 36, 36, 48, 31, 31, 48, 49, 30, 49, 28, 49, 30, 31, 50, 51, 27, 30, 28, 29, 50, 28, 49, 28, 50, 27, 27, 51, 26, 26, 51, 52, 26, 52, 25, 52, 53, 25, 25, 53, 24, 24, 53, 54, 24, 54, 106, 24, 106, 23, 54, 55, 106, 107, 23, 106, 107, 106, 105, 55, 105, 106, 105, 108, 107, 105, 109, 108, 108, 109, 99, 109, 98, 99, 55, 56, 105, 23, 107, 22, 22, 107, 21, 107, 20, 21, 107, 108, 20, 105, 56, 109, 56, 57, 109, 108, 19, 20, 108, 18, 19, 108, 99, 18, 109, 57, 98, 98, 57, 58, 58, 59, 98, 59, 97, 98, 98, 100, 99, 98, 97, 100, 99, 17, 18, 99, 100, 17, 59, 60, 97, 100, 16, 17, 97, 101, 100, 100, 101, 16, 60, 96, 97, 97, 96, 101, 60, 61, 96, 96, 102, 101, 101, 15, 16, 101, 102, 15, 61, 95, 96, 96, 95, 102, 61, 62, 95, 102, 14, 15, 103, 95, 94, 95, 103, 102, 103, 93, 104, 103, 94, 93, 94, 95, 63, 102, 103, 14, 95, 62, 63, 103, 13, 14, 63, 64, 94, 94, 64, 93, 103, 104, 13, 13, 104, 12, 64, 83, 93, 64, 65, 83, 12, 104, 84, 84, 104, 83, 104, 93, 83, 12, 84, 11, 11, 84, 85, 65, 82, 83, 85, 84, 82, 84, 83, 82, 65, 66, 82, 11, 85, 10, 85, 86, 10, 86, 85, 81, 66, 81, 82, 85, 82, 81, 10, 86, 9, 66, 67, 81, 86, 87, 9, 9, 87, 8, 81, 92, 86, 86, 92, 87, 87, 92, 80, 87, 80, 79, 88, 87, 78, 78, 87, 79, 67, 68, 81, 81, 68, 92, 8, 87, 7, 87, 88, 7, 88, 78, 89, 7, 88, 6, 88, 5, 6, 88, 89, 5, 80, 72, 79, 72, 73, 79, 79, 73, 78, 89, 4, 5, 78, 90, 89, 89, 90, 4, 78, 77, 90, 78, 73, 77, 73, 74, 77, 90, 3, 4, 77, 91, 90, 90, 2, 3, 90, 91, 2, 74, 75, 77, 77, 76, 91, 77, 75, 76, 76, 0, 91, 91, 1, 2, 91, 0, 1, 68, 69, 92, 69, 70, 92, 92, 70, 80, 70, 71, 80, 71, 72, 80], "vertices": [4, 50, -62.69, 15.83, 0.6432, 48, 58.03, 65.32, 0.35214, 47, 106.38, 156.42, 0.00091, 63, -242.22, 39.7, 0.00375, 2, 50, -63.88, 37.62, 0.71195, 48, 64.53, 86.15, 0.28805, 2, 50, -55.47, 50.56, 0.74728, 48, 76.93, 95.34, 0.25272, 2, 50, -33.25, 73.16, 0.78, 48, 105.65, 108.74, 0.22, 2, 50, -5.77, 91.16, 0.86286, 48, 137.68, 116.01, 0.13714, 2, 50, 24.53, 104.82, 0.97429, 48, 170.85, 118.22, 0.02571, 1, 50, 54.22, 113.47, 1, 1, 50, 85.24, 115, 1, 1, 50, 124.06, 112.65, 1, 1, 50, 167.54, 110, 1, 1, 50, 211.89, 105.99, 1, 1, 50, 343.6, 85.48, 1, 2, 50, 418.37, 73.84, 0.95947, 51, -62.41, 77.25, 0.04053, 2, 50, 445.81, 70.58, 0.8188, 51, -35.18, 72.54, 0.1812, 2, 50, 465.01, 71.99, 0.62601, 51, -15.93, 72.93, 0.37399, 2, 50, 490.71, 77.16, 0.33084, 51, 10.01, 76.74, 0.66916, 2, 50, 505.83, 79.3, 0.18614, 51, 25.23, 78.07, 0.81386, 2, 50, 532.15, 88.91, 0.04284, 51, 52.01, 86.29, 0.95716, 2, 50, 554.16, 97.08, 0.00764, 51, 74.43, 93.27, 0.99236, 2, 50, 580.72, 106.27, 1e-05, 51, 101.44, 101.05, 0.99999, 1, 51, 123.11, 104.79, 1, 1, 51, 156.43, 105.29, 1, 1, 51, 183.7, 101.46, 1, 1, 51, 249.63, 79.25, 1, 1, 51, 337.45, 42.13, 1, 2, 51, 359.76, 36.87, 0.98948, 52, -38.95, 26.72, 0.01052, 2, 51, 383.48, 32.19, 0.72896, 52, -14.89, 29.12, 0.27104, 2, 51, 403.43, 31.21, 0.20217, 52, 4.49, 33.97, 0.79783, 2, 51, 421.07, 47.59, 0.01862, 52, 16.61, 54.76, 0.98138, 1, 52, 32.05, 69.62, 1, 1, 52, 51.35, 75.69, 1, 1, 52, 86.46, 73.94, 1, 1, 52, 120.27, 77.32, 1, 1, 52, 152.07, 84, 1, 1, 52, 161.81, 87.29, 1, 1, 52, 166.44, 73.86, 1, 1, 52, 120.39, 56.74, 1, 1, 52, 177.8, 63.72, 1, 1, 52, 182.93, 63.57, 1, 1, 52, 190.18, 53.92, 1, 1, 52, 205.6, 20.93, 1, 1, 52, 230.79, -59.93, 1, 1, 52, 230.34, -70.76, 1, 1, 52, 213.22, -76.27, 1, 1, 52, 199.23, -76.12, 1, 1, 52, 182.58, -63.11, 1, 1, 52, 160.88, -37.94, 1, 1, 52, 125.59, -29.85, 1, 1, 52, 98.42, -28.5, 1, 1, 52, 58.43, -31.24, 1, 2, 51, 409.26, -40.48, 0.04424, 52, 30.86, -32.94, 0.95576, 2, 51, 390.26, -34, 0.28659, 52, 10.8, -32.25, 0.71341, 2, 51, 370.75, -28.55, 0.75599, 52, -9.45, -32.7, 0.24401, 2, 51, 344.33, -24.23, 0.99384, 52, -35.98, -36.23, 0.00616, 1, 51, 324.11, -21.73, 1, 1, 51, 272.53, -20.4, 1, 1, 51, 154.28, -22.69, 1, 1, 51, 102.37, -17.76, 1, 1, 51, 79.79, -14.47, 1, 1, 51, 67.75, -15.06, 1, 1, 51, 46.54, -20.57, 1, 2, 50, 509.54, -28.18, 0.10281, 51, 23.25, -29.45, 0.89719, 2, 50, 487.77, -37.62, 0.52079, 51, 1.01, -37.73, 0.47921, 2, 50, 468.52, -40.63, 0.84113, 51, -18.37, -39.72, 0.15887, 1, 50, 436.4, -42.9, 1, 1, 50, 360, -62.16, 1, 1, 50, 209.81, -100.64, 1, 1, 50, 175.88, -105.24, 1, 1, 50, 137.08, -107.62, 1, 1, 50, 107.82, -105.74, 1, 4, 50, 80.23, -98.79, 0.93546, 48, 151.89, -92.02, 0.00283, 47, 200.24, -0.92, 0.0567, 63, -365.32, -95.99, 0.005, 4, 50, 52.5, -86.25, 0.82026, 48, 130.28, -70.58, 0.01577, 47, 178.63, 20.52, 0.15397, 63, -351.12, -69.07, 0.01, 4, 50, 27.98, -72.36, 0.59272, 48, 112.16, -49, 0.07457, 47, 160.51, 42.1, 0.31771, 63, -335.77, -45.44, 0.015, 4, 50, 13.13, -58.91, 0.37187, 48, 102.95, -31.2, 0.14355, 47, 151.3, 59.9, 0.46319, 63, -321.44, -31.44, 0.02138, 4, 50, -24.42, -52.84, 0.35403, 48, 69.89, -12.39, 0.2976, 47, 118.24, 78.71, 0.32905, 63, -313.09, 5.67, 0.01932, 4, 50, -40.14, -33.7, 0.48518, 48, 61.85, 11.04, 0.37907, 47, 110.2, 102.14, 0.12101, 63, -293.03, 20.2, 0.01474, 4, 50, -56.33, -2.58, 0.58569, 48, 57.56, 45.85, 0.39453, 47, 105.91, 136.95, 0.01206, 63, -260.98, 34.47, 0.00771, 4, 50, -7.56, -16.1, 0.57166, 48, 98.53, 16.14, 0.36826, 47, 146.88, 107.24, 0.04397, 63, -277.45, -13.39, 0.01611, 4, 50, 18.95, -14.53, 0.75949, 48, 123.91, 8.34, 0.20779, 47, 172.26, 99.44, 0.0155, 63, -277.5, -39.94, 0.01723, 3, 50, 42.33, -28.31, 0.89611, 48, 141, -12.74, 0.08708, 63, -292.67, -62.45, 0.01681, 2, 50, 75.67, -42.01, 0.98412, 63, -308.37, -94.89, 0.01588, 2, 50, 166.93, -45.89, 0.98099, 63, -317.79, -185.74, 0.01901, 2, 50, 222.14, -39.22, 0.97921, 63, -314.49, -241.26, 0.02079, 2, 50, 368.51, -21.61, 0.98144, 63, -305.81, -388.42, 0.01856, 2, 50, 365.56, 35.41, 0.98477, 63, -248.71, -388.95, 0.01523, 2, 50, 220.12, 45.59, 0.97888, 63, -229.71, -244.4, 0.02112, 2, 50, 167.52, 51.78, 0.97954, 63, -220.33, -192.27, 0.02046, 2, 50, 118.94, 58.1, 0.98059, 63, -211.08, -144.17, 0.01941, 2, 50, 71.9, 57.89, 0.98291, 63, -208.42, -97.2, 0.01709, 3, 50, 26.68, 51.47, 0.9158, 48, 154.22, 67.48, 0.06742, 63, -212.08, -51.67, 0.01678, 3, 50, -8.66, 37.78, 0.77383, 48, 116.32, 67.01, 0.21105, 63, -223.6, -15.56, 0.01512, 3, 50, -32.88, 22.15, 0.69393, 48, 88.18, 60.83, 0.29337, 63, -237.73, 9.55, 0.0127, 2, 50, 118.54, -48.58, 0.98265, 63, -317.53, -137.28, 0.01735, 2, 50, 432.08, -17.36, 0.98311, 63, -305.43, -452.14, 0.01689, 3, 50, 463.25, -18.29, 0.9291, 51, -22.45, -17.13, 0.05524, 63, -308.26, -483.2, 0.01567, 3, 50, 488.09, -17.35, 0.41193, 51, 2.4, -17.5, 0.57327, 63, -308.82, -508.04, 0.0148, 3, 50, 511.24, -7.45, 0.00022, 51, 26.04, -8.84, 0.98412, 63, -300.35, -531.76, 0.01566, 2, 51, 49.69, 0.38, 0.98362, 63, -291.32, -555.47, 0.01638, 2, 51, 76.2, 3.42, 0.98374, 63, -288.49, -582.01, 0.01626, 3, 50, 556.1, 49.53, 0.00365, 51, 73.85, 45.69, 0.98017, 63, -246.2, -580, 0.01618, 3, 50, 532.56, 41.93, 0.02596, 51, 49.94, 39.34, 0.9571, 63, -252.36, -556.04, 0.01695, 3, 50, 509.06, 37.67, 0.11225, 51, 26.25, 36.33, 0.8706, 63, -255.18, -532.32, 0.01715, 3, 50, 483.8, 34.43, 0.4594, 51, 0.85, 34.44, 0.52366, 63, -256.88, -506.91, 0.01694, 3, 50, 457.48, 30, 0.82942, 51, -25.66, 31.4, 0.15403, 63, -259.7, -480.38, 0.01654, 3, 50, 429.87, 28.32, 0.95472, 51, -53.33, 31.18, 0.02903, 63, -259.7, -452.71, 0.01625, 2, 51, 158.6, -2.53, 0.98875, 63, -295.1, -664.35, 0.01125, 1, 51, 300.38, 12.38, 1, 2, 51, 159.93, 62.95, 0.98993, 63, -229.63, -666.2, 0.01007, 2, 51, 115.46, 61.98, 0.98693, 63, -230.25, -621.73, 0.01307, 2, 51, 111.84, 0.4, 0.9853, 63, -291.8, -617.62, 0.0147], "hull": 77, "edges": [0, 152, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 24, 26, 26, 28, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 104, 106, 106, 108, 116, 118, 130, 132, 132, 134, 134, 136, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 14, 16, 16, 18, 18, 20, 136, 138, 128, 130, 124, 126, 126, 128, 122, 124, 118, 120, 120, 122, 112, 114, 114, 116, 36, 38, 32, 34, 34, 36, 28, 30, 30, 32, 20, 22, 22, 24, 154, 156, 156, 158, 158, 160, 162, 164, 164, 166, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 160, 184, 184, 162, 166, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 168, 210, 212, 212, 214, 214, 216, 216, 198, 196, 218, 218, 210, 96, 98, 98, 100, 100, 102, 102, 104, 48, 50, 50, 52, 108, 110, 110, 112], "width": 281, "height": 1139}}, "nose": {"nose": {"type": "mesh", "uvs": [0.56107, 0.02477, 0.89485, 0.20389, 0.8992, 0.3802, 0.90355, 0.5565, 0.90671, 0.68461, 0.94952, 0.75182, 0.94551, 0.85454, 0.85005, 0.92102, 0.64941, 0.93221, 0.48989, 0.97848, 0.16638, 0.97948, 0.06387, 0.90389, 0.04422, 0.7979, 0.06285, 0.68884, 0.17941, 0.4819, 0.18957, 0.26946, 0.11248, 0.07662, 0.50844, 0.24029, 0.50267, 0.46961, 0.57592, 0.65804, 0.56588, 0.80413, 0.31079, 0.86603, 0.29457, 0.76364, 0.29853, 0.64639, 0.32641, 0.44895, 0.34195, 0.23211, 0.76259, 0.81501, 0.7657, 0.65387], "triangles": [10, 21, 9, 10, 11, 21, 9, 20, 8, 9, 21, 20, 8, 26, 7, 8, 20, 26, 7, 26, 6, 11, 12, 21, 12, 22, 21, 21, 22, 20, 6, 26, 5, 20, 19, 26, 26, 4, 5, 26, 27, 4, 26, 19, 27, 20, 22, 19, 12, 13, 22, 22, 23, 19, 22, 13, 23, 13, 14, 23, 27, 3, 4, 27, 19, 18, 19, 23, 18, 27, 18, 3, 23, 24, 18, 23, 14, 24, 18, 2, 3, 14, 15, 24, 2, 18, 17, 18, 24, 17, 24, 25, 17, 24, 15, 25, 17, 1, 2, 15, 16, 25, 1, 17, 0, 17, 25, 0, 25, 16, 0], "vertices": [2, 6, 60.02, 41.2, 0.96074, 68, -361.99, 135.66, 0.03926, 2, 6, 49.51, 32.04, 0.96057, 68, -372.5, 126.51, 0.03943, 2, 6, 40.78, 33.28, 0.96027, 68, -381.23, 127.74, 0.03973, 2, 6, 32.05, 34.51, 0.96006, 68, -389.96, 128.98, 0.03994, 2, 6, 25.71, 35.41, 0.96013, 68, -396.3, 129.88, 0.03987, 2, 6, 22.18, 34.58, 0.96024, 68, -399.84, 129.05, 0.03976, 2, 6, 17.12, 35.51, 0.96044, 68, -404.89, 129.97, 0.03956, 2, 6, 14.31, 39.04, 0.96054, 68, -407.7, 133.51, 0.03946, 2, 6, 14.76, 45.47, 0.96058, 68, -407.25, 139.94, 0.03942, 2, 6, 13.27, 50.88, 0.96249, 68, -408.74, 145.34, 0.03751, 2, 6, 14.83, 61.11, 0.96589, 68, -407.18, 155.57, 0.03411, 2, 6, 19.08, 63.76, 0.9627, 68, -402.93, 158.22, 0.0373, 2, 6, 24.41, 63.56, 0.95993, 68, -397.6, 158.02, 0.04007, 2, 6, 29.7, 62.12, 0.95963, 68, -392.31, 156.58, 0.04037, 2, 6, 39.34, 56.82, 0.96052, 68, -382.67, 151.29, 0.03948, 2, 6, 49.79, 54.85, 0.96251, 68, -372.23, 149.31, 0.03749, 2, 6, 59.69, 55.78, 0.96511, 68, -362.32, 150.25, 0.03489, 2, 6, 49.64, 44.54, 0.96015, 68, -372.37, 139.01, 0.03985, 2, 6, 38.34, 46.51, 0.95806, 68, -383.67, 140.97, 0.04194, 2, 6, 28.67, 45.66, 0.95608, 68, -393.34, 140.12, 0.04392, 2, 6, 21.5, 47.12, 0.95622, 68, -400.51, 141.58, 0.04378, 2, 6, 19.72, 55.66, 0.95742, 68, -402.3, 150.12, 0.04258, 2, 6, 24.86, 55.38, 0.95541, 68, -397.16, 149.84, 0.04459, 2, 6, 30.63, 54.34, 0.95564, 68, -391.39, 148.8, 0.04436, 2, 6, 40.24, 51.92, 0.95814, 68, -381.77, 146.38, 0.04186, 2, 6, 50.87, 49.74, 0.96036, 68, -371.14, 144.2, 0.03964, 2, 6, 19.99, 40.98, 0.95845, 68, -402.03, 135.45, 0.04155, 2, 6, 27.93, 39.63, 0.9584, 68, -394.08, 134.09, 0.0416], "hull": 17, "edges": [0, 32, 8, 10, 10, 12, 18, 20, 28, 30, 30, 32, 0, 2, 26, 28, 20, 22, 12, 14, 14, 16, 16, 18, 6, 8, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 22, 24, 24, 26, 44, 46, 46, 48, 48, 50, 52, 54, 2, 4, 4, 6], "width": 32, "height": 50}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": 15.41, "curve": [0.444, 15.41, 0.889, -12.15, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -12.16, "curve": [1.778, -12.17, 2.222, 15.4, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 15.41, "curve": [3.111, 15.42, 3.556, -12.15, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -12.16, "curve": [4.444, -12.17, 4.889, 15.4, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 15.41, "curve": [5.667, 15.42, 6, -12.16, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": -12.16, "curve": [6.778, -12.16, 7.222, 1.62, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 1.62, "curve": [8.111, 1.63, 8.556, -12.15, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -12.16, "curve": [9.444, -12.17, 9.889, 15.4, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 15.41, "curve": [10.778, 15.42, 11.222, -12.15, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": -12.16, "curve": [12.111, -12.17, 12.556, 15.41, 12.111, 0, 12.556, 0]}, {"time": 13, "x": 15.41}]}, "ALL2": {"translate": [{"y": -24.64, "curve": [0.444, 0, 0.889, 0, 0.444, -24.64, 0.889, 26.45]}, {"time": 1.3333, "y": 26.47, "curve": [1.778, 0, 2.222, 0, 1.778, 26.48, 2.222, -24.63]}, {"time": 2.6667, "y": -24.64, "curve": [3.111, 0, 3.556, 0, 3.111, -24.66, 3.556, 26.45]}, {"time": 4, "y": 26.47, "curve": [4.444, 0, 4.889, 0, 4.444, 26.48, 4.889, -24.62]}, {"time": 5.3333, "y": -24.64, "curve": [5.667, 0, 6, 0, 5.667, -24.66, 6, 26.46]}, {"time": 6.3333, "y": 26.47, "curve": [6.778, 0, 7.222, 0, 6.778, 26.47, 7.222, 0.92]}, {"time": 7.6667, "y": 0.91, "curve": [8.111, 0, 8.556, 0, 8.111, 0.91, 8.556, 26.45]}, {"time": 9, "y": 26.47, "curve": [9.444, 0, 9.889, 0, 9.444, 26.48, 9.889, -24.63]}, {"time": 10.3333, "y": -24.64, "curve": [10.778, 0, 11.222, 0, 10.778, -24.66, 11.222, 26.45]}, {"time": 11.6667, "y": 26.47, "curve": [12.111, 0, 12.556, 0, 12.111, 26.48, 12.556, -24.64]}, {"time": 13, "y": -24.64}]}, "body": {"rotate": [{"value": 5.13, "curve": [0.444, 5.13, 0.889, -4.42]}, {"time": 1.3333, "value": -4.42, "curve": [1.778, -4.42, 2.222, 5.12]}, {"time": 2.6667, "value": 5.13, "curve": [3.111, 5.13, 3.556, -4.42]}, {"time": 4, "value": -4.42, "curve": [4.444, -4.42, 4.889, 5.12]}, {"time": 5.3333, "value": 5.13, "curve": [5.667, 5.13, 6, -4.42]}, {"time": 6.3333, "value": -4.42, "curve": [6.778, -4.42, 7.222, 0.35]}, {"time": 7.6667, "value": 0.35, "curve": [8.111, 0.35, 8.556, -4.42]}, {"time": 9, "value": -4.42, "curve": [9.444, -4.42, 9.889, 5.12]}, {"time": 10.3333, "value": 5.13, "curve": [10.778, 5.13, 11.222, -4.42]}, {"time": 11.6667, "value": -4.42, "curve": [12.111, -4.42, 12.556, 5.13]}, {"time": 13, "value": 5.13}], "translate": [{"x": 1.56, "y": -10.29, "curve": [0.057, 1.65, 0.112, 1.72, 0.057, -10.95, 0.112, -11.45]}, {"time": 0.1667, "x": 1.72, "y": -11.45, "curve": [0.611, 1.72, 1.056, -1.72, 0.611, -11.45, 1.056, 13.16]}, {"time": 1.5, "x": -1.72, "y": 13.17, "curve": [1.944, -1.72, 2.389, 1.72, 1.944, 13.17, 2.389, -11.44]}, {"time": 2.8333, "x": 1.72, "y": -11.45, "curve": [3.278, 1.72, 3.722, -1.72, 3.278, -11.45, 3.722, 13.16]}, {"time": 4.1667, "x": -1.72, "y": 13.17, "curve": [4.611, -1.72, 5.056, 1.72, 4.611, 13.17, 5.056, -11.44]}, {"time": 5.5, "x": 1.72, "y": -11.45, "curve": [5.833, 1.72, 6.167, -1.72, 5.833, -11.45, 6.167, 13.16]}, {"time": 6.5, "x": -1.72, "y": 13.17, "curve": [6.944, -1.72, 7.389, 0, 6.944, 13.17, 7.389, 0.86]}, {"time": 7.8333, "y": 0.86, "curve": [8.278, 0, 8.722, -1.72, 8.278, 0.86, 8.722, 13.16]}, {"time": 9.1667, "x": -1.72, "y": 13.17, "curve": [9.611, -1.72, 10.056, 1.72, 9.611, 13.17, 10.056, -11.44]}, {"time": 10.5, "x": 1.72, "y": -11.45, "curve": [10.944, 1.72, 11.389, -1.72, 10.944, -11.45, 11.389, 13.16]}, {"time": 11.8333, "x": -1.72, "y": 13.17, "curve": [12.223, -1.72, 12.613, 0.9, 12.223, 13.17, 12.613, -5.62]}, {"time": 13, "x": 1.56, "y": -10.29}], "scale": [{"y": 1.032, "curve": [0.057, 1, 0.112, 1, 0.057, 1.033, 0.112, 1.035]}, {"time": 0.1667, "y": 1.035, "curve": [0.611, 1, 1.056, 1, 0.611, 1.035, 1.056, 0.972]}, {"time": 1.5, "y": 0.972, "curve": [1.944, 1, 2.389, 1, 1.944, 0.972, 2.389, 1.035]}, {"time": 2.8333, "y": 1.035, "curve": [3.278, 1, 3.722, 1, 3.278, 1.035, 3.722, 0.972]}, {"time": 4.1667, "y": 0.972, "curve": [4.611, 1, 5.056, 1, 4.611, 0.972, 5.056, 1.035]}, {"time": 5.5, "y": 1.035, "curve": [5.833, 1, 6.167, 1, 5.833, 1.035, 6.167, 0.972]}, {"time": 6.5, "y": 0.972, "curve": [6.944, 1, 7.389, 1, 6.944, 0.972, 7.389, 1.004]}, {"time": 7.8333, "y": 1.004, "curve": [8.278, 1, 8.722, 1, 8.278, 1.004, 8.722, 0.972]}, {"time": 9.1667, "y": 0.972, "curve": [9.611, 1, 10.056, 1, 9.611, 0.972, 10.056, 1.035]}, {"time": 10.5, "y": 1.035, "curve": [10.944, 1, 11.389, 1, 10.944, 1.035, 11.389, 0.972]}, {"time": 11.8333, "y": 0.972, "curve": [12.223, 1, 12.613, 1, 12.223, 0.972, 12.613, 1.02]}, {"time": 13, "y": 1.032}]}, "body2": {"rotate": [{"value": -1.96, "curve": [0.057, -2.07, 0.112, -2.15]}, {"time": 0.1667, "value": -2.15, "curve": [0.611, -2.15, 1.056, 1.99]}, {"time": 1.5, "value": 1.99, "curve": [1.944, 1.99, 2.389, -2.15]}, {"time": 2.8333, "value": -2.15, "curve": [3.278, -2.15, 3.722, 1.99]}, {"time": 4.1667, "value": 1.99, "curve": [4.611, 1.99, 5.056, -2.15]}, {"time": 5.5, "value": -2.15, "curve": [5.833, -2.15, 6.167, 1.99]}, {"time": 6.5, "value": 1.99, "curve": [6.944, 1.99, 7.389, -0.08]}, {"time": 7.8333, "value": -0.08, "curve": [8.278, -0.08, 8.722, 1.99]}, {"time": 9.1667, "value": 1.99, "curve": [9.611, 1.99, 10.056, -2.15]}, {"time": 10.5, "value": -2.15, "curve": [10.944, -2.15, 11.389, 1.99]}, {"time": 11.8333, "value": 1.99, "curve": [12.223, 1.99, 12.613, -1.17]}, {"time": 13, "value": -1.96}], "translate": [{"x": -10.61, "curve": [0.114, -12.33, 0.224, -13.6, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -13.6, "curve": [0.778, -13.6, 1.222, 5.07, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 5.07, "curve": [2.111, 5.08, 2.556, -13.6, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -13.6, "curve": [3.444, -13.61, 3.889, 5.07, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 5.07, "curve": [4.778, 5.08, 5.222, -13.6, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -13.6, "curve": [6, -13.61, 6.333, 5.07, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 5.07, "curve": [7.111, 5.07, 7.556, -4.26, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -4.26, "curve": [8.444, -4.27, 8.889, 5.07, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 5.07, "curve": [9.778, 5.08, 10.222, -13.6, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -13.6, "curve": [11.111, -13.61, 11.556, 5.07, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 5.07, "curve": [12.335, 5.08, 12.67, -5.38, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -10.61}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.114, 1.001, 0.224, 1, 0.114, 1.001, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.016, 0.778, 1, 1.222, 1.016]}, {"time": 1.6667, "x": 1.016, "y": 1.016, "curve": [2.111, 1.016, 2.556, 1, 2.111, 1.016, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.016, 3.444, 1, 3.889, 1.016]}, {"time": 4.3333, "x": 1.016, "y": 1.016, "curve": [4.778, 1.016, 5.222, 1, 4.778, 1.016, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.016, 6, 1, 6.333, 1.016]}, {"time": 6.6667, "x": 1.016, "y": 1.016, "curve": [7.111, 1.016, 7.556, 1.008, 7.111, 1.016, 7.556, 1.008]}, {"time": 8, "x": 1.008, "y": 1.008, "curve": [8.444, 1.008, 8.889, 1.016, 8.444, 1.008, 8.889, 1.016]}, {"time": 9.3333, "x": 1.016, "y": 1.016, "curve": [9.778, 1.016, 10.222, 1, 9.778, 1.016, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.016, 11.111, 1, 11.556, 1.016]}, {"time": 12, "x": 1.016, "y": 1.016, "curve": [12.335, 1.016, 12.67, 1.007, 12.335, 1.016, 12.67, 1.007]}, {"time": 13, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": 0.96, "curve": [0.168, 1.79, 0.334, 2.47]}, {"time": 0.5, "value": 2.47, "curve": [0.944, 2.47, 1.389, -2.27]}, {"time": 1.8333, "value": -2.28, "curve": [2.278, -2.28, 2.722, 2.46]}, {"time": 3.1667, "value": 2.47, "curve": [3.611, 2.47, 4.056, -2.27]}, {"time": 4.5, "value": -2.28, "curve": [4.944, -2.28, 5.389, 2.46]}, {"time": 5.8333, "value": 2.47, "curve": [6.167, 2.47, 6.5, -8.18]}, {"time": 6.8333, "value": -8.18, "curve": [7.278, -8.18, 7.722, -5.52]}, {"time": 8.1667, "value": -5.52, "curve": [8.611, -5.52, 9.056, -8.18]}, {"time": 9.5, "value": -8.18, "curve": [9.944, -8.19, 10.389, 2.46]}, {"time": 10.8333, "value": 2.47, "curve": [11.278, 2.47, 11.722, -2.27]}, {"time": 12.1667, "value": -2.28, "curve": [12.445, -2.28, 12.724, -0.43]}, {"time": 13, "value": 0.96}]}, "head": {"rotate": [{"value": 0.09, "curve": [0.225, 1.27, 0.446, 2.47]}, {"time": 0.6667, "value": 2.47, "curve": [1.111, 2.47, 1.556, -2.27]}, {"time": 2, "value": -2.28, "curve": [2.444, -2.28, 2.889, 2.46]}, {"time": 3.3333, "value": 2.47, "curve": [3.778, 2.47, 4.222, -2.27]}, {"time": 4.6667, "value": -2.28, "curve": [5.111, -2.28, 5.556, 2.46]}, {"time": 6, "value": 2.47, "curve": [6.333, 2.47, 6.667, -8.18]}, {"time": 7, "value": -8.18, "curve": [7.444, -8.18, 7.889, -5.52]}, {"time": 8.3333, "value": -5.52, "curve": [8.778, -5.52, 9.222, -8.18]}, {"time": 9.6667, "value": -8.18, "curve": [10.111, -8.19, 10.556, 2.46]}, {"time": 11, "value": 2.47, "curve": [11.444, 2.47, 11.889, -2.27]}, {"time": 12.3333, "value": -2.28, "curve": [12.557, -2.28, 12.781, -1.1]}, {"time": 13, "value": 0.09}]}, "eyebrow_L": {"translate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 2.48, 6.444, 0, 6.556, 0]}, {"time": 6.6667, "x": 2.48, "curve": "stepped"}, {"time": 9.6667, "x": 2.48, "curve": [9.889, 2.48, 10.111, 0, 9.889, 0, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_L2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -3.93]}, {"time": 6.6667, "value": -3.93, "curve": "stepped"}, {"time": 9.6667, "value": -3.93, "curve": [9.889, -3.93, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_L3": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -6.33]}, {"time": 6.6667, "value": -6.33, "curve": "stepped"}, {"time": 9.6667, "value": -6.33, "curve": [9.889, -6.33, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R": {"translate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 2.48, 6.444, 0, 6.556, 0]}, {"time": 6.6667, "x": 2.48, "curve": "stepped"}, {"time": 9.6667, "x": 2.48, "curve": [9.889, 2.48, 10.111, 0, 9.889, 0, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 5.49]}, {"time": 6.6667, "value": 5.49, "curve": "stepped"}, {"time": 9.6667, "value": 5.49, "curve": [9.889, 5.49, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R3": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 10.47]}, {"time": 6.6667, "value": 10.47, "curve": "stepped"}, {"time": 9.6667, "value": 10.47, "curve": [9.889, 10.47, 10.111, 0]}, {"time": 10.3333}]}, "hair_FL2": {"rotate": [{"value": 2.2, "curve": [0.279, -0.75, 0.556, -4.67]}, {"time": 0.8333, "value": -4.67, "curve": [1.278, -4.67, 1.722, 5.4]}, {"time": 2.1667, "value": 5.4, "curve": [2.611, 5.4, 3.056, -4.66]}, {"time": 3.5, "value": -4.67, "curve": [3.944, -4.67, 4.389, 5.4]}, {"time": 4.8333, "value": 5.4, "curve": [5.278, 5.4, 5.722, -4.66]}, {"time": 6.1667, "value": -4.67, "curve": [6.5, -4.67, 6.833, 5.4]}, {"time": 7.1667, "value": 5.4, "curve": [7.611, 5.4, 8.056, 0.37]}, {"time": 8.5, "value": 0.37, "curve": [8.944, 0.37, 9.389, 5.4]}, {"time": 9.8333, "value": 5.4, "curve": [10.278, 5.4, 10.722, -4.66]}, {"time": 11.1667, "value": -4.67, "curve": [11.611, -4.67, 12.056, 5.4]}, {"time": 12.5, "value": 5.4, "curve": [12.667, 5.4, 12.835, 3.98]}, {"time": 13, "value": 2.2}]}, "hair_B2": {"rotate": [{"value": 3.79, "curve": [0.336, 0.95, 0.668, -4.67]}, {"time": 1, "value": -4.67, "curve": [1.444, -4.67, 1.889, 5.4]}, {"time": 2.3333, "value": 5.4, "curve": [2.778, 5.4, 3.222, -4.66]}, {"time": 3.6667, "value": -4.67, "curve": [4.111, -4.67, 4.556, 5.4]}, {"time": 5, "value": 5.4, "curve": [5.444, 5.4, 5.889, -4.66]}, {"time": 6.3333, "value": -4.67, "curve": [6.667, -4.67, 7, 5.4]}, {"time": 7.3333, "value": 5.4, "curve": [7.778, 5.4, 8.222, 0.37]}, {"time": 8.6667, "value": 0.37, "curve": [9.111, 0.37, 9.556, 5.4]}, {"time": 10, "value": 5.4, "curve": [10.444, 5.4, 10.889, -4.66]}, {"time": 11.3333, "value": -4.67, "curve": [11.778, -4.67, 12.222, 5.4]}, {"time": 12.6667, "value": 5.4, "curve": [12.779, 5.4, 12.892, 4.76]}, {"time": 13, "value": 3.79}]}, "hair_F2": {"rotate": [{"value": 0.37, "curve": [0.225, -2.13, 0.446, -4.67]}, {"time": 0.6667, "value": -4.67, "curve": [1.111, -4.67, 1.556, 5.4]}, {"time": 2, "value": 5.4, "curve": [2.444, 5.4, 2.889, -4.66]}, {"time": 3.3333, "value": -4.67, "curve": [3.778, -4.67, 4.222, 5.4]}, {"time": 4.6667, "value": 5.4, "curve": [5.111, 5.4, 5.556, -4.66]}, {"time": 6, "value": -4.67, "curve": [6.333, -4.67, 6.667, 5.4]}, {"time": 7, "value": 5.4, "curve": [7.444, 5.4, 7.889, 0.37]}, {"time": 8.3333, "value": 0.37, "curve": [8.778, 0.37, 9.222, 5.4]}, {"time": 9.6667, "value": 5.4, "curve": [10.111, 5.4, 10.556, -4.66]}, {"time": 11, "value": -4.67, "curve": [11.444, -4.67, 11.889, 5.4]}, {"time": 12.3333, "value": 5.4, "curve": [12.557, 5.4, 12.781, 2.9]}, {"time": 13, "value": 0.37}]}, "hair_FR2": {"rotate": [{"value": -1.47, "curve": [0.279, 1.48, 0.556, 5.4]}, {"time": 0.8333, "value": 5.4, "curve": [1.278, 5.4, 1.722, -4.66]}, {"time": 2.1667, "value": -4.67, "curve": [2.611, -4.67, 3.056, 5.4]}, {"time": 3.5, "value": 5.4, "curve": [3.944, 5.4, 4.389, -4.66]}, {"time": 4.8333, "value": -4.67, "curve": [5.278, -4.67, 5.722, 5.4]}, {"time": 6.1667, "value": 5.4, "curve": [6.5, 5.4, 6.833, -4.67]}, {"time": 7.1667, "value": -4.67, "curve": [7.611, -4.67, 8.056, 0.37]}, {"time": 8.5, "value": 0.37, "curve": [8.944, 0.37, 9.389, -4.66]}, {"time": 9.8333, "value": -4.67, "curve": [10.278, -4.67, 10.722, 5.4]}, {"time": 11.1667, "value": 5.4, "curve": [11.611, 5.4, 12.056, -4.66]}, {"time": 12.5, "value": -4.67, "curve": [12.667, -4.67, 12.835, -3.25]}, {"time": 13, "value": -1.47}]}, "sh_L": {"translate": [{"x": -4.09, "curve": [0.114, -5.45, 0.224, -6.46, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -6.46, "curve": [0.778, -6.46, 1.222, 8.37, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 8.37, "curve": [2.111, 8.37, 2.556, -6.46, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -6.46, "curve": [3.444, -6.47, 3.889, 8.37, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 8.37, "curve": [4.778, 8.37, 5.222, -6.46, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -6.46, "curve": [6, -6.47, 6.333, 8.37, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 8.37, "curve": [7.111, 8.37, 7.556, 0.95, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 0.95, "curve": [8.444, 0.95, 8.889, 8.37, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 8.37, "curve": [9.778, 8.37, 10.222, -6.46, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -6.46, "curve": [11.111, -6.47, 11.556, 8.37, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 8.37, "curve": [12.335, 8.37, 12.67, 0.06, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -4.09}]}, "arm_L": {"rotate": [{"value": -2.42, "curve": [0.168, -3.05, 0.334, -3.56]}, {"time": 0.5, "value": -3.56, "curve": [0.944, -3.56, 1.389, 0.02]}, {"time": 1.8333, "value": 0.02, "curve": [2.278, 0.02, 2.722, -3.56]}, {"time": 3.1667, "value": -3.56, "curve": [3.611, -3.56, 4.056, 0.02]}, {"time": 4.5, "value": 0.02, "curve": [4.944, 0.02, 5.389, -3.56]}, {"time": 5.8333, "value": -3.56, "curve": [6.167, -3.56, 6.5, 0.02]}, {"time": 6.8333, "value": 0.02, "curve": [7.278, 0.02, 7.722, -1.77]}, {"time": 8.1667, "value": -1.77, "curve": [8.611, -1.77, 9.056, 0.02]}, {"time": 9.5, "value": 0.02, "curve": [9.944, 0.02, 10.389, -3.56]}, {"time": 10.8333, "value": -3.56, "curve": [11.278, -3.56, 11.722, 0.02]}, {"time": 12.1667, "value": 0.02, "curve": [12.445, 0.02, 12.724, -1.37]}, {"time": 13, "value": -2.42}]}, "arm_L2": {"rotate": [{"value": -1.77, "curve": [0.225, -2.66, 0.446, -3.56]}, {"time": 0.6667, "value": -3.56, "curve": [1.111, -3.56, 1.556, 0.02]}, {"time": 2, "value": 0.02, "curve": [2.444, 0.02, 2.889, -3.56]}, {"time": 3.3333, "value": -3.56, "curve": [3.778, -3.56, 4.222, 0.02]}, {"time": 4.6667, "value": 0.02, "curve": [5.111, 0.02, 5.556, -3.56]}, {"time": 6, "value": -3.56, "curve": [6.333, -3.56, 6.667, 0.02]}, {"time": 7, "value": 0.02, "curve": [7.444, 0.02, 7.889, -1.77]}, {"time": 8.3333, "value": -1.77, "curve": [8.778, -1.77, 9.222, 0.02]}, {"time": 9.6667, "value": 0.02, "curve": [10.111, 0.02, 10.556, -3.56]}, {"time": 11, "value": -3.56, "curve": [11.444, -3.56, 11.889, 0.02]}, {"time": 12.3333, "value": 0.02, "curve": [12.557, 0.02, 12.781, -0.87]}, {"time": 13, "value": -1.77}]}, "arm_L3": {"rotate": [{"value": 1.45, "curve": [0.279, -0.81, 0.556, -3.81]}, {"time": 0.8333, "value": -3.81, "curve": [1.278, -3.81, 1.722, 3.89]}, {"time": 2.1667, "value": 3.9, "curve": [2.611, 3.9, 3.056, -3.81]}, {"time": 3.5, "value": -3.81, "curve": [3.944, -3.81, 4.389, 3.89]}, {"time": 4.8333, "value": 3.9, "curve": [5.278, 3.9, 5.722, -3.81]}, {"time": 6.1667, "value": -3.81, "curve": [6.5, -3.81, 6.833, 3.9]}, {"time": 7.1667, "value": 3.9, "curve": [7.611, 3.9, 8.056, 0.04]}, {"time": 8.5, "value": 0.04, "curve": [8.944, 0.04, 9.389, 3.89]}, {"time": 9.8333, "value": 3.9, "curve": [10.278, 3.9, 10.722, -3.81]}, {"time": 11.1667, "value": -3.81, "curve": [11.611, -3.81, 12.056, 3.89]}, {"time": 12.5, "value": 3.9, "curve": [12.667, 3.9, 12.835, 2.81]}, {"time": 13, "value": 1.45}]}, "arm_L4": {"rotate": [{"value": 2.66, "curve": [0.336, 0.49, 0.668, -3.81]}, {"time": 1, "value": -3.81, "curve": [1.444, -3.81, 1.889, 3.89]}, {"time": 2.3333, "value": 3.9, "curve": [2.778, 3.9, 3.222, -3.81]}, {"time": 3.6667, "value": -3.81, "curve": [4.111, -3.81, 4.556, 3.89]}, {"time": 5, "value": 3.9, "curve": [5.444, 3.9, 5.889, -3.81]}, {"time": 6.3333, "value": -3.81, "curve": [6.667, -3.81, 7, 3.9]}, {"time": 7.3333, "value": 3.9, "curve": [7.778, 3.9, 8.222, 0.04]}, {"time": 8.6667, "value": 0.04, "curve": [9.111, 0.04, 9.556, 3.89]}, {"time": 10, "value": 3.9, "curve": [10.444, 3.9, 10.889, -3.81]}, {"time": 11.3333, "value": -3.81, "curve": [11.778, -3.81, 12.222, 3.89]}, {"time": 12.6667, "value": 3.9, "curve": [12.779, 3.9, 12.892, 3.4]}, {"time": 13, "value": 2.66}]}, "sh_R": {"translate": [{"x": -5.94, "curve": [0.114, -7.91, 0.224, -9.38, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -9.38, "curve": [0.778, -9.38, 1.222, 12.1, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 12.11, "curve": [2.111, 12.11, 2.556, -9.37, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -9.38, "curve": [3.444, -9.38, 3.889, 12.1, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 12.11, "curve": [4.778, 12.11, 5.222, -9.37, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -9.38, "curve": [6, -9.38, 6.333, 12.1, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 12.11, "curve": [7.111, 12.11, 7.556, 1.37, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 1.37, "curve": [8.444, 1.36, 8.889, 12.1, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 12.11, "curve": [9.778, 12.11, 10.222, -9.37, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -9.38, "curve": [11.111, -9.38, 11.556, 12.1, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 12.11, "curve": [12.335, 12.11, 12.67, 0.08, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -5.94}]}, "arm_R": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -11.29]}, {"time": 7.6667, "value": -11.3, "curve": [8.111, -11.3, 8.556, 0]}, {"time": 9}]}, "arm_R2": {"rotate": [{"value": 0.05, "curve": "stepped"}, {"time": 5.3333, "value": 0.05, "curve": [5.667, 0.05, 6, 0.04]}, {"time": 6.3333, "value": 0.05, "curve": [6.778, 0.05, 7.222, -5.14]}, {"time": 7.6667, "value": -5.14, "curve": [8.111, -5.14, 8.556, 0.05]}, {"time": 9, "value": 0.05}]}, "arm_R3": {"rotate": [{"value": -0.05, "curve": [0.225, -2.98, 0.446, -5.95]}, {"time": 0.6667, "value": -5.95, "curve": [1.111, -5.95, 1.556, 5.84]}, {"time": 2, "value": 5.84, "curve": [2.444, 5.85, 2.889, -5.95]}, {"time": 3.3333, "value": -5.95, "curve": [3.778, -5.95, 4.222, 5.84]}, {"time": 4.6667, "value": 5.84, "curve": [5.111, 5.85, 5.556, -5.94]}, {"time": 6, "value": -5.95, "curve": [6.333, -5.96, 6.667, 26.93]}, {"time": 7, "value": 26.93, "curve": [7.444, 26.93, 7.889, 18.71]}, {"time": 8.3333, "value": 18.71, "curve": [8.778, 18.71, 9.222, 26.92]}, {"time": 9.6667, "value": 26.93, "curve": [10.111, 26.94, 10.556, -5.95]}, {"time": 11, "value": -5.95, "curve": [11.444, -5.95, 11.889, 5.84]}, {"time": 12.3333, "value": 5.84, "curve": [12.557, 5.84, 12.781, 2.92]}, {"time": 13, "value": -0.05}]}, "arm_R4": {"rotate": [{"value": 2.09, "curve": [0.279, -1.36, 0.556, -5.95]}, {"time": 0.8333, "value": -5.95, "curve": [1.278, -5.95, 1.722, 5.84]}, {"time": 2.1667, "value": 5.84, "curve": [2.611, 5.85, 3.056, -5.95]}, {"time": 3.5, "value": -5.95, "curve": [3.944, -5.95, 4.389, 5.84]}, {"time": 4.8333, "value": 5.84, "curve": [5.278, 5.85, 5.722, -5.94]}, {"time": 6.1667, "value": -5.95, "curve": [6.5, -5.96, 6.833, 26.93]}, {"time": 7.1667, "value": 26.93, "curve": [7.611, 26.93, 8.056, 18.71]}, {"time": 8.5, "value": 18.71, "curve": [8.944, 18.71, 9.389, 26.92]}, {"time": 9.8333, "value": 26.93, "curve": [10.278, 26.94, 10.722, -5.95]}, {"time": 11.1667, "value": -5.95, "curve": [11.611, -5.95, 12.056, 5.84]}, {"time": 12.5, "value": 5.84, "curve": [12.667, 5.84, 12.835, 4.18]}, {"time": 13, "value": 2.09}]}, "tun": {"rotate": [{"value": 5.13, "curve": [0.444, 5.13, 0.889, -4.42]}, {"time": 1.3333, "value": -4.42, "curve": [1.778, -4.42, 2.222, 5.12]}, {"time": 2.6667, "value": 5.13, "curve": [3.111, 5.13, 3.556, -4.42]}, {"time": 4, "value": -4.42, "curve": [4.444, -4.42, 4.889, 5.12]}, {"time": 5.3333, "value": 5.13, "curve": [5.667, 5.13, 6, -4.42]}, {"time": 6.3333, "value": -4.42, "curve": [6.778, -4.42, 7.222, 0.35]}, {"time": 7.6667, "value": 0.35, "curve": [8.111, 0.35, 8.556, -4.42]}, {"time": 9, "value": -4.42, "curve": [9.444, -4.42, 9.889, 5.12]}, {"time": 10.3333, "value": 5.13, "curve": [10.778, 5.13, 11.222, -4.42]}, {"time": 11.6667, "value": -4.42, "curve": [12.111, -4.42, 12.556, 5.13]}, {"time": 13, "value": 5.13}]}, "leg_L2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.5]}, {"time": 7.6667, "value": -0.5, "curve": [8.111, -0.5, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0]}, {"time": 7.6667, "curve": [8.111, 0, 8.556, 0]}, {"time": 9}]}, "leg_R2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.55]}, {"time": 7.6667, "value": -0.55, "curve": [8.111, -0.55, 8.556, 0]}, {"time": 9}]}, "leg_R3": {"rotate": [{"value": 0.01, "curve": "stepped"}, {"time": 6.3333, "value": 0.01, "curve": [6.778, 0.01, 7.222, -0.01]}, {"time": 7.6667, "value": -0.01, "curve": [8.111, -0.01, 8.556, 0.01]}, {"time": 9, "value": 0.01}]}, "foot_R2": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, 0]}, {"time": 7.6667, "curve": [8.111, 0, 8.556, 0]}, {"time": 9}]}, "RU_L": {"translate": [{"x": -16.09, "y": -3.43, "curve": [0.168, -28.69, 0.334, -39.02, 0.168, -6.12, 0.334, -8.33]}, {"time": 0.5, "x": -39.02, "y": -8.33, "curve": [0.944, -39.02, 1.389, 33.06, 0.944, -8.33, 1.389, 7.06]}, {"time": 1.8333, "x": 33.08, "y": 7.06, "curve": [2.278, 33.1, 2.722, -39, 2.278, 7.06, 2.722, -8.33]}, {"time": 3.1667, "x": -39.02, "y": -8.33, "curve": [3.611, -39.04, 4.056, 33.06, 3.611, -8.33, 4.056, 7.06]}, {"time": 4.5, "x": 33.08, "y": 7.06, "curve": [4.944, 33.1, 5.389, -39, 4.944, 7.06, 5.389, -8.32]}, {"time": 5.8333, "x": -39.02, "y": -8.33, "curve": [6.167, -39.04, 6.5, 33.07, 6.167, -8.33, 6.5, 7.06]}, {"time": 6.8333, "x": 33.08, "y": 7.06, "curve": [7.278, 33.1, 7.722, -39, 7.278, 7.06, 7.722, -8.33]}, {"time": 8.1667, "x": -39.02, "y": -8.33, "curve": [8.611, -39.04, 9.056, 33.06, 8.611, -8.33, 9.056, 7.06]}, {"time": 9.5, "x": 33.08, "y": 7.06, "curve": [9.944, 33.1, 10.389, -39, 9.944, 7.06, 10.389, -8.33]}, {"time": 10.8333, "x": -39.02, "y": -8.33, "curve": [11.278, -39.04, 11.722, 33.06, 11.278, -8.33, 11.722, 7.06]}, {"time": 12.1667, "x": 33.08, "y": 7.06, "curve": [12.445, 33.09, 12.724, 5.06, 12.445, 7.06, 12.724, 1.08]}, {"time": 13, "x": -16.09, "y": -3.43}], "scale": [{"x": 1.049, "y": 0.971, "curve": [0.167, 1.01, 0.333, 0.932, 0.167, 1.007, 0.333, 1.078]}, {"time": 0.5, "x": 0.932, "y": 1.078, "curve": [0.722, 0.932, 0.944, 1.071, 0.722, 1.078, 0.944, 0.951]}, {"time": 1.1667, "x": 1.071, "y": 0.951, "curve": [1.389, 1.071, 1.611, 0.932, 1.389, 0.951, 1.611, 1.078]}, {"time": 1.8333, "x": 0.932, "y": 1.078, "curve": [2.056, 0.932, 2.278, 1.071, 2.056, 1.078, 2.278, 0.951]}, {"time": 2.5, "x": 1.071, "y": 0.951, "curve": [2.722, 1.071, 2.944, 0.932, 2.722, 0.951, 2.944, 1.078]}, {"time": 3.1667, "x": 0.932, "y": 1.078, "curve": [3.389, 0.932, 3.611, 1.071, 3.389, 1.078, 3.611, 0.951]}, {"time": 3.8333, "x": 1.071, "y": 0.951, "curve": [4.056, 1.071, 4.278, 0.932, 4.056, 0.951, 4.278, 1.078]}, {"time": 4.5, "x": 0.932, "y": 1.078, "curve": [4.722, 0.932, 4.944, 1.071, 4.722, 1.078, 4.944, 0.951]}, {"time": 5.1667, "x": 1.071, "y": 0.951, "curve": [5.389, 1.071, 5.611, 0.932, 5.389, 0.951, 5.611, 1.078]}, {"time": 5.8333, "x": 0.932, "y": 1.078, "curve": [6, 0.932, 6.167, 1.071, 6, 1.078, 6.167, 0.951]}, {"time": 6.3333, "x": 1.071, "y": 0.951, "curve": [6.5, 1.071, 6.667, 0.932, 6.5, 0.951, 6.667, 1.078]}, {"time": 6.8333, "x": 0.932, "y": 1.078, "curve": [7.056, 0.932, 7.278, 1.071, 7.056, 1.078, 7.278, 0.951]}, {"time": 7.5, "x": 1.071, "y": 0.951, "curve": [7.722, 1.071, 7.944, 0.932, 7.722, 0.951, 7.944, 1.078]}, {"time": 8.1667, "x": 0.932, "y": 1.078, "curve": [8.389, 0.932, 8.611, 1.071, 8.389, 1.078, 8.611, 0.951]}, {"time": 8.8333, "x": 1.071, "y": 0.951, "curve": [9.056, 1.071, 9.278, 0.932, 9.056, 0.951, 9.278, 1.078]}, {"time": 9.5, "x": 0.932, "y": 1.078, "curve": [9.722, 0.932, 9.944, 1.071, 9.722, 1.078, 9.944, 0.951]}, {"time": 10.1667, "x": 1.071, "y": 0.951, "curve": [10.389, 1.071, 10.611, 0.932, 10.389, 0.951, 10.611, 1.078]}, {"time": 10.8333, "x": 0.932, "y": 1.078, "curve": [11.056, 0.932, 11.278, 1.071, 11.056, 1.078, 11.278, 0.951]}, {"time": 11.5, "x": 1.071, "y": 0.951, "curve": [11.722, 1.071, 11.944, 0.932, 11.722, 0.951, 11.944, 1.078]}, {"time": 12.1667, "x": 0.932, "y": 1.078, "curve": [12.389, 0.932, 12.611, 1.071, 12.389, 1.078, 12.611, 0.951]}, {"time": 12.8333, "x": 1.071, "y": 0.951, "curve": [12.889, 1.071, 12.944, 1.062, 12.889, 0.951, 12.944, 0.959]}, {"time": 13, "x": 1.049, "y": 0.971}]}, "RU_R": {"translate": [{"x": -16.09, "y": -3.43, "curve": [0.168, -28.69, 0.334, -39.02, 0.168, -6.12, 0.334, -8.33]}, {"time": 0.5, "x": -39.02, "y": -8.33, "curve": [0.944, -39.02, 1.389, 33.06, 0.944, -8.33, 1.389, 7.06]}, {"time": 1.8333, "x": 33.08, "y": 7.06, "curve": [2.278, 33.1, 2.722, -39, 2.278, 7.06, 2.722, -8.33]}, {"time": 3.1667, "x": -39.02, "y": -8.33, "curve": [3.611, -39.04, 4.056, 33.06, 3.611, -8.33, 4.056, 7.06]}, {"time": 4.5, "x": 33.08, "y": 7.06, "curve": [4.944, 33.1, 5.389, -39, 4.944, 7.06, 5.389, -8.32]}, {"time": 5.8333, "x": -39.02, "y": -8.33, "curve": [6.167, -39.04, 6.5, 33.07, 6.167, -8.33, 6.5, 7.06]}, {"time": 6.8333, "x": 33.08, "y": 7.06, "curve": [7.278, 33.1, 7.722, -39, 7.278, 7.06, 7.722, -8.33]}, {"time": 8.1667, "x": -39.02, "y": -8.33, "curve": [8.611, -39.04, 9.056, 33.06, 8.611, -8.33, 9.056, 7.06]}, {"time": 9.5, "x": 33.08, "y": 7.06, "curve": [9.944, 33.1, 10.389, -39, 9.944, 7.06, 10.389, -8.33]}, {"time": 10.8333, "x": -39.02, "y": -8.33, "curve": [11.278, -39.04, 11.722, 33.06, 11.278, -8.33, 11.722, 7.06]}, {"time": 12.1667, "x": 33.08, "y": 7.06, "curve": [12.445, 33.09, 12.724, 5.06, 12.445, 7.06, 12.724, 1.08]}, {"time": 13, "x": -16.09, "y": -3.43}], "scale": [{"x": 1.049, "y": 0.971, "curve": [0.167, 1.01, 0.333, 0.932, 0.167, 1.007, 0.333, 1.078]}, {"time": 0.5, "x": 0.932, "y": 1.078, "curve": [0.722, 0.932, 0.944, 1.071, 0.722, 1.078, 0.944, 0.951]}, {"time": 1.1667, "x": 1.071, "y": 0.951, "curve": [1.389, 1.071, 1.611, 0.932, 1.389, 0.951, 1.611, 1.078]}, {"time": 1.8333, "x": 0.932, "y": 1.078, "curve": [2.056, 0.932, 2.278, 1.071, 2.056, 1.078, 2.278, 0.951]}, {"time": 2.5, "x": 1.071, "y": 0.951, "curve": [2.722, 1.071, 2.944, 0.932, 2.722, 0.951, 2.944, 1.078]}, {"time": 3.1667, "x": 0.932, "y": 1.078, "curve": [3.389, 0.932, 3.611, 1.071, 3.389, 1.078, 3.611, 0.951]}, {"time": 3.8333, "x": 1.071, "y": 0.951, "curve": [4.056, 1.071, 4.278, 0.932, 4.056, 0.951, 4.278, 1.078]}, {"time": 4.5, "x": 0.932, "y": 1.078, "curve": [4.722, 0.932, 4.944, 1.071, 4.722, 1.078, 4.944, 0.951]}, {"time": 5.1667, "x": 1.071, "y": 0.951, "curve": [5.389, 1.071, 5.611, 0.932, 5.389, 0.951, 5.611, 1.078]}, {"time": 5.8333, "x": 0.932, "y": 1.078, "curve": [6, 0.932, 6.167, 1.071, 6, 1.078, 6.167, 0.951]}, {"time": 6.3333, "x": 1.071, "y": 0.951, "curve": [6.5, 1.071, 6.667, 0.932, 6.5, 0.951, 6.667, 1.078]}, {"time": 6.8333, "x": 0.932, "y": 1.078, "curve": [7.056, 0.932, 7.278, 1.071, 7.056, 1.078, 7.278, 0.951]}, {"time": 7.5, "x": 1.071, "y": 0.951, "curve": [7.722, 1.071, 7.944, 0.932, 7.722, 0.951, 7.944, 1.078]}, {"time": 8.1667, "x": 0.932, "y": 1.078, "curve": [8.389, 0.932, 8.611, 1.071, 8.389, 1.078, 8.611, 0.951]}, {"time": 8.8333, "x": 1.071, "y": 0.951, "curve": [9.056, 1.071, 9.278, 0.932, 9.056, 0.951, 9.278, 1.078]}, {"time": 9.5, "x": 0.932, "y": 1.078, "curve": [9.722, 0.932, 9.944, 1.071, 9.722, 1.078, 9.944, 0.951]}, {"time": 10.1667, "x": 1.071, "y": 0.951, "curve": [10.389, 1.071, 10.611, 0.932, 10.389, 0.951, 10.611, 1.078]}, {"time": 10.8333, "x": 0.932, "y": 1.078, "curve": [11.056, 0.932, 11.278, 1.071, 11.056, 1.078, 11.278, 0.951]}, {"time": 11.5, "x": 1.071, "y": 0.951, "curve": [11.722, 1.071, 11.944, 0.932, 11.722, 0.951, 11.944, 1.078]}, {"time": 12.1667, "x": 0.932, "y": 1.078, "curve": [12.389, 0.932, 12.611, 1.071, 12.389, 1.078, 12.611, 0.951]}, {"time": 12.8333, "x": 1.071, "y": 0.951, "curve": [12.889, 1.071, 12.944, 1.062, 12.889, 0.951, 12.944, 0.959]}, {"time": 13, "x": 1.049, "y": 0.971}]}, "RU_R2": {"translate": [{"x": -0.2, "y": -0.03, "curve": [0.225, -18.44, 0.446, -36.93, 0.225, -2.93, 0.446, -5.88]}, {"time": 0.6667, "x": -36.93, "y": -5.88, "curve": [1.111, -36.93, 1.556, 36.51, 1.111, -5.88, 1.556, 5.81]}, {"time": 2, "x": 36.52, "y": 5.81, "curve": [2.444, 36.54, 2.889, -36.92, 2.444, 5.81, 2.889, -5.87]}, {"time": 3.3333, "x": -36.93, "y": -5.88, "curve": [3.778, -36.95, 4.222, 36.51, 3.778, -5.88, 4.222, 5.81]}, {"time": 4.6667, "x": 36.52, "y": 5.81, "curve": [5.111, 36.54, 5.556, -36.91, 5.111, 5.81, 5.556, -5.87]}, {"time": 6, "x": -36.93, "y": -5.88, "curve": [6.333, -36.95, 6.667, 36.51, 6.333, -5.88, 6.667, 5.81]}, {"time": 7, "x": 36.52, "y": 5.81, "curve": [7.444, 36.54, 7.889, -36.92, 7.444, 5.81, 7.889, -5.87]}, {"time": 8.3333, "x": -36.93, "y": -5.88, "curve": [8.778, -36.95, 9.222, 36.51, 8.778, -5.88, 9.222, 5.81]}, {"time": 9.6667, "x": 36.52, "y": 5.81, "curve": [10.111, 36.54, 10.556, -36.92, 10.111, 5.81, 10.556, -5.87]}, {"time": 11, "x": -36.93, "y": -5.88, "curve": [11.444, -36.95, 11.889, 36.51, 11.444, -5.88, 11.889, 5.81]}, {"time": 12.3333, "x": 36.52, "y": 5.81, "curve": [12.557, 36.53, 12.781, 18.29, 12.557, 5.81, 12.781, 2.91]}, {"time": 13, "x": -0.2, "y": -0.03}], "scale": [{"x": 1.071, "y": 0.951, "curve": [0.222, 1.071, 0.444, 0.932, 0.222, 0.951, 0.444, 1.078]}, {"time": 0.6667, "x": 0.932, "y": 1.078, "curve": [0.889, 0.932, 1.111, 1.071, 0.889, 1.078, 1.111, 0.951]}, {"time": 1.3333, "x": 1.071, "y": 0.951, "curve": [1.556, 1.071, 1.778, 0.932, 1.556, 0.951, 1.778, 1.078]}, {"time": 2, "x": 0.932, "y": 1.078, "curve": [2.222, 0.932, 2.444, 1.071, 2.222, 1.078, 2.444, 0.951]}, {"time": 2.6667, "x": 1.071, "y": 0.951, "curve": [2.889, 1.071, 3.111, 0.932, 2.889, 0.951, 3.111, 1.078]}, {"time": 3.3333, "x": 0.932, "y": 1.078, "curve": [3.556, 0.932, 3.778, 1.071, 3.556, 1.078, 3.778, 0.951]}, {"time": 4, "x": 1.071, "y": 0.951, "curve": [4.222, 1.071, 4.444, 0.932, 4.222, 0.951, 4.444, 1.078]}, {"time": 4.6667, "x": 0.932, "y": 1.078, "curve": [4.889, 0.932, 5.111, 1.071, 4.889, 1.078, 5.111, 0.951]}, {"time": 5.3333, "x": 1.071, "y": 0.951, "curve": [5.556, 1.071, 5.778, 0.932, 5.556, 0.951, 5.778, 1.078]}, {"time": 6, "x": 0.932, "y": 1.078, "curve": [6.167, 0.932, 6.333, 1.071, 6.167, 1.078, 6.333, 0.951]}, {"time": 6.5, "x": 1.071, "y": 0.951, "curve": [6.667, 1.071, 6.833, 0.932, 6.667, 0.951, 6.833, 1.078]}, {"time": 7, "x": 0.932, "y": 1.078, "curve": [7.222, 0.932, 7.444, 1.071, 7.222, 1.078, 7.444, 0.951]}, {"time": 7.6667, "x": 1.071, "y": 0.951, "curve": [7.889, 1.071, 8.111, 0.932, 7.889, 0.951, 8.111, 1.078]}, {"time": 8.3333, "x": 0.932, "y": 1.078, "curve": [8.556, 0.932, 8.778, 1.071, 8.556, 1.078, 8.778, 0.951]}, {"time": 9, "x": 1.071, "y": 0.951, "curve": [9.222, 1.071, 9.444, 0.932, 9.222, 0.951, 9.444, 1.078]}, {"time": 9.6667, "x": 0.932, "y": 1.078, "curve": [9.889, 0.932, 10.111, 1.071, 9.889, 1.078, 10.111, 0.951]}, {"time": 10.3333, "x": 1.071, "y": 0.951, "curve": [10.556, 1.071, 10.778, 0.932, 10.556, 0.951, 10.778, 1.078]}, {"time": 11, "x": 0.932, "y": 1.078, "curve": [11.222, 0.932, 11.444, 1.071, 11.222, 1.078, 11.444, 0.951]}, {"time": 11.6667, "x": 1.071, "y": 0.951, "curve": [11.889, 1.071, 12.111, 0.932, 11.889, 0.951, 12.111, 1.078]}, {"time": 12.3333, "x": 0.932, "y": 1.078, "curve": [12.556, 0.932, 12.778, 1.071, 12.556, 1.078, 12.778, 0.951]}, {"time": 13, "x": 1.071, "y": 0.951}]}, "RU_R3": {"translate": [{"x": 16.53, "curve": [0.279, -2.5, 0.556, -27.82, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -27.82, "curve": [1.278, -27.82, 1.722, 37.19, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 37.2, "curve": [2.611, 37.22, 3.056, -27.8, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -27.82, "curve": [3.944, -27.83, 4.389, 37.19, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 37.2, "curve": [5.278, 37.22, 5.722, -27.79, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -27.82, "curve": [6.5, -27.83, 6.833, 37.19, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 37.2, "curve": [7.611, 37.22, 8.056, -27.8, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -27.82, "curve": [8.944, -27.83, 9.389, 37.19, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 37.2, "curve": [10.278, 37.22, 10.722, -27.8, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -27.82, "curve": [11.611, -27.83, 12.056, 37.19, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 37.2, "curve": [12.667, 37.21, 12.835, 28.03, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 16.53}], "scale": [{"x": 1.049, "y": 0.971, "curve": [0.056, 1.062, 0.111, 1.071, 0.056, 0.96, 0.111, 0.951]}, {"time": 0.1667, "x": 1.071, "y": 0.951, "curve": [0.389, 1.071, 0.611, 0.932, 0.389, 0.951, 0.611, 1.078]}, {"time": 0.8333, "x": 0.932, "y": 1.078, "curve": [1.056, 0.932, 1.278, 1.071, 1.056, 1.078, 1.278, 0.951]}, {"time": 1.5, "x": 1.071, "y": 0.951, "curve": [1.722, 1.071, 1.944, 0.932, 1.722, 0.951, 1.944, 1.078]}, {"time": 2.1667, "x": 0.932, "y": 1.078, "curve": [2.389, 0.932, 2.611, 1.071, 2.389, 1.078, 2.611, 0.951]}, {"time": 2.8333, "x": 1.071, "y": 0.951, "curve": [3.056, 1.071, 3.278, 0.932, 3.056, 0.951, 3.278, 1.078]}, {"time": 3.5, "x": 0.932, "y": 1.078, "curve": [3.722, 0.932, 3.944, 1.071, 3.722, 1.078, 3.944, 0.951]}, {"time": 4.1667, "x": 1.071, "y": 0.951, "curve": [4.389, 1.071, 4.611, 0.932, 4.389, 0.951, 4.611, 1.078]}, {"time": 4.8333, "x": 0.932, "y": 1.078, "curve": [5.056, 0.932, 5.278, 1.071, 5.056, 1.078, 5.278, 0.951]}, {"time": 5.5, "x": 1.071, "y": 0.951, "curve": [5.722, 1.071, 5.944, 0.932, 5.722, 0.951, 5.944, 1.078]}, {"time": 6.1667, "x": 0.932, "y": 1.078, "curve": [6.333, 0.932, 6.5, 1.071, 6.333, 1.078, 6.5, 0.951]}, {"time": 6.6667, "x": 1.071, "y": 0.951, "curve": [6.833, 1.071, 7, 0.932, 6.833, 0.951, 7, 1.078]}, {"time": 7.1667, "x": 0.932, "y": 1.078, "curve": [7.389, 0.932, 7.611, 1.071, 7.389, 1.078, 7.611, 0.951]}, {"time": 7.8333, "x": 1.071, "y": 0.951, "curve": [8.056, 1.071, 8.278, 0.932, 8.056, 0.951, 8.278, 1.078]}, {"time": 8.5, "x": 0.932, "y": 1.078, "curve": [8.722, 0.932, 8.944, 1.071, 8.722, 1.078, 8.944, 0.951]}, {"time": 9.1667, "x": 1.071, "y": 0.951, "curve": [9.389, 1.071, 9.611, 0.932, 9.389, 0.951, 9.611, 1.078]}, {"time": 9.8333, "x": 0.932, "y": 1.078, "curve": [10.056, 0.932, 10.278, 1.071, 10.056, 1.078, 10.278, 0.951]}, {"time": 10.5, "x": 1.071, "y": 0.951, "curve": [10.722, 1.071, 10.944, 0.932, 10.722, 0.951, 10.944, 1.078]}, {"time": 11.1667, "x": 0.932, "y": 1.078, "curve": [11.389, 0.932, 11.611, 1.071, 11.389, 1.078, 11.611, 0.951]}, {"time": 11.8333, "x": 1.071, "y": 0.951, "curve": [12.056, 1.071, 12.278, 0.932, 12.056, 0.951, 12.278, 1.078]}, {"time": 12.5, "x": 0.932, "y": 1.078, "curve": [12.667, 0.932, 12.833, 1.01, 12.667, 1.078, 12.833, 1.007]}, {"time": 13, "x": 1.049, "y": 0.971}]}, "RU_L2": {"translate": [{"x": -0.2, "y": -0.02, "curve": [0.225, -18.61, 0.446, -37.27, 0.225, -1.56, 0.446, -3.13]}, {"time": 0.6667, "x": -37.27, "y": -3.13, "curve": [1.111, -37.27, 1.556, 36.84, 1.111, -3.13, 1.556, 3.09]}, {"time": 2, "x": 36.85, "y": 3.09, "curve": [2.444, 36.87, 2.889, -37.25, 2.444, 3.09, 2.889, -3.12]}, {"time": 3.3333, "x": -37.27, "y": -3.13, "curve": [3.778, -37.29, 4.222, 36.84, 3.778, -3.13, 4.222, 3.09]}, {"time": 4.6667, "x": 36.85, "y": 3.09, "curve": [5.111, 36.87, 5.556, -37.24, 5.111, 3.09, 5.556, -3.12]}, {"time": 6, "x": -37.27, "y": -3.13, "curve": [6.333, -37.29, 6.667, 36.84, 6.333, -3.13, 6.667, 3.09]}, {"time": 7, "x": 36.85, "y": 3.09, "curve": [7.444, 36.87, 7.889, -37.25, 7.444, 3.09, 7.889, -3.12]}, {"time": 8.3333, "x": -37.27, "y": -3.13, "curve": [8.778, -37.29, 9.222, 36.84, 8.778, -3.13, 9.222, 3.09]}, {"time": 9.6667, "x": 36.85, "y": 3.09, "curve": [10.111, 36.87, 10.556, -37.25, 10.111, 3.09, 10.556, -3.12]}, {"time": 11, "x": -37.27, "y": -3.13, "curve": [11.444, -37.29, 11.889, 36.84, 11.444, -3.13, 11.889, 3.09]}, {"time": 12.3333, "x": 36.85, "y": 3.09, "curve": [12.557, 36.86, 12.781, 18.46, 12.557, 3.09, 12.781, 1.55]}, {"time": 13, "x": -0.2, "y": -0.02}], "scale": [{"x": 1.071, "y": 0.951, "curve": [0.222, 1.071, 0.444, 0.932, 0.222, 0.951, 0.444, 1.078]}, {"time": 0.6667, "x": 0.932, "y": 1.078, "curve": [0.889, 0.932, 1.111, 1.071, 0.889, 1.078, 1.111, 0.951]}, {"time": 1.3333, "x": 1.071, "y": 0.951, "curve": [1.556, 1.071, 1.778, 0.932, 1.556, 0.951, 1.778, 1.078]}, {"time": 2, "x": 0.932, "y": 1.078, "curve": [2.222, 0.932, 2.444, 1.071, 2.222, 1.078, 2.444, 0.951]}, {"time": 2.6667, "x": 1.071, "y": 0.951, "curve": [2.889, 1.071, 3.111, 0.932, 2.889, 0.951, 3.111, 1.078]}, {"time": 3.3333, "x": 0.932, "y": 1.078, "curve": [3.556, 0.932, 3.778, 1.071, 3.556, 1.078, 3.778, 0.951]}, {"time": 4, "x": 1.071, "y": 0.951, "curve": [4.222, 1.071, 4.444, 0.932, 4.222, 0.951, 4.444, 1.078]}, {"time": 4.6667, "x": 0.932, "y": 1.078, "curve": [4.889, 0.932, 5.111, 1.071, 4.889, 1.078, 5.111, 0.951]}, {"time": 5.3333, "x": 1.071, "y": 0.951, "curve": [5.556, 1.071, 5.778, 0.932, 5.556, 0.951, 5.778, 1.078]}, {"time": 6, "x": 0.932, "y": 1.078, "curve": [6.167, 0.932, 6.333, 1.071, 6.167, 1.078, 6.333, 0.951]}, {"time": 6.5, "x": 1.071, "y": 0.951, "curve": [6.667, 1.071, 6.833, 0.932, 6.667, 0.951, 6.833, 1.078]}, {"time": 7, "x": 0.932, "y": 1.078, "curve": [7.222, 0.932, 7.444, 1.071, 7.222, 1.078, 7.444, 0.951]}, {"time": 7.6667, "x": 1.071, "y": 0.951, "curve": [7.889, 1.071, 8.111, 0.932, 7.889, 0.951, 8.111, 1.078]}, {"time": 8.3333, "x": 0.932, "y": 1.078, "curve": [8.556, 0.932, 8.778, 1.071, 8.556, 1.078, 8.778, 0.951]}, {"time": 9, "x": 1.071, "y": 0.951, "curve": [9.222, 1.071, 9.444, 0.932, 9.222, 0.951, 9.444, 1.078]}, {"time": 9.6667, "x": 0.932, "y": 1.078, "curve": [9.889, 0.932, 10.111, 1.071, 9.889, 1.078, 10.111, 0.951]}, {"time": 10.3333, "x": 1.071, "y": 0.951, "curve": [10.556, 1.071, 10.778, 0.932, 10.556, 0.951, 10.778, 1.078]}, {"time": 11, "x": 0.932, "y": 1.078, "curve": [11.222, 0.932, 11.444, 1.071, 11.222, 1.078, 11.444, 0.951]}, {"time": 11.6667, "x": 1.071, "y": 0.951, "curve": [11.889, 1.071, 12.111, 0.932, 11.889, 0.951, 12.111, 1.078]}, {"time": 12.3333, "x": 0.932, "y": 1.078, "curve": [12.556, 0.932, 12.778, 1.071, 12.556, 1.078, 12.778, 0.951]}, {"time": 13, "x": 1.071, "y": 0.951}]}, "RU_L3": {"translate": [{"x": 16.53, "curve": [0.279, -2.5, 0.556, -27.82, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -27.82, "curve": [1.278, -27.82, 1.722, 37.19, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 37.2, "curve": [2.611, 37.22, 3.056, -27.8, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -27.82, "curve": [3.944, -27.83, 4.389, 37.19, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 37.2, "curve": [5.278, 37.22, 5.722, -27.79, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -27.82, "curve": [6.5, -27.83, 6.833, 37.19, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 37.2, "curve": [7.611, 37.22, 8.056, -27.8, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -27.82, "curve": [8.944, -27.83, 9.389, 37.19, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 37.2, "curve": [10.278, 37.22, 10.722, -27.8, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -27.82, "curve": [11.611, -27.83, 12.056, 37.19, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 37.2, "curve": [12.667, 37.21, 12.835, 28.03, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 16.53}], "scale": [{"x": 1.049, "y": 0.971, "curve": [0.056, 1.062, 0.111, 1.071, 0.056, 0.96, 0.111, 0.951]}, {"time": 0.1667, "x": 1.071, "y": 0.951, "curve": [0.389, 1.071, 0.611, 0.932, 0.389, 0.951, 0.611, 1.078]}, {"time": 0.8333, "x": 0.932, "y": 1.078, "curve": [1.056, 0.932, 1.278, 1.071, 1.056, 1.078, 1.278, 0.951]}, {"time": 1.5, "x": 1.071, "y": 0.951, "curve": [1.722, 1.071, 1.944, 0.932, 1.722, 0.951, 1.944, 1.078]}, {"time": 2.1667, "x": 0.932, "y": 1.078, "curve": [2.389, 0.932, 2.611, 1.071, 2.389, 1.078, 2.611, 0.951]}, {"time": 2.8333, "x": 1.071, "y": 0.951, "curve": [3.056, 1.071, 3.278, 0.932, 3.056, 0.951, 3.278, 1.078]}, {"time": 3.5, "x": 0.932, "y": 1.078, "curve": [3.722, 0.932, 3.944, 1.071, 3.722, 1.078, 3.944, 0.951]}, {"time": 4.1667, "x": 1.071, "y": 0.951, "curve": [4.389, 1.071, 4.611, 0.932, 4.389, 0.951, 4.611, 1.078]}, {"time": 4.8333, "x": 0.932, "y": 1.078, "curve": [5.056, 0.932, 5.278, 1.071, 5.056, 1.078, 5.278, 0.951]}, {"time": 5.5, "x": 1.071, "y": 0.951, "curve": [5.722, 1.071, 5.944, 0.932, 5.722, 0.951, 5.944, 1.078]}, {"time": 6.1667, "x": 0.932, "y": 1.078, "curve": [6.333, 0.932, 6.5, 1.071, 6.333, 1.078, 6.5, 0.951]}, {"time": 6.6667, "x": 1.071, "y": 0.951, "curve": [6.833, 1.071, 7, 0.932, 6.833, 0.951, 7, 1.078]}, {"time": 7.1667, "x": 0.932, "y": 1.078, "curve": [7.389, 0.932, 7.611, 1.071, 7.389, 1.078, 7.611, 0.951]}, {"time": 7.8333, "x": 1.071, "y": 0.951, "curve": [8.056, 1.071, 8.278, 0.932, 8.056, 0.951, 8.278, 1.078]}, {"time": 8.5, "x": 0.932, "y": 1.078, "curve": [8.722, 0.932, 8.944, 1.071, 8.722, 1.078, 8.944, 0.951]}, {"time": 9.1667, "x": 1.071, "y": 0.951, "curve": [9.389, 1.071, 9.611, 0.932, 9.389, 0.951, 9.611, 1.078]}, {"time": 9.8333, "x": 0.932, "y": 1.078, "curve": [10.056, 0.932, 10.278, 1.071, 10.056, 1.078, 10.278, 0.951]}, {"time": 10.5, "x": 1.071, "y": 0.951, "curve": [10.722, 1.071, 10.944, 0.932, 10.722, 0.951, 10.944, 1.078]}, {"time": 11.1667, "x": 0.932, "y": 1.078, "curve": [11.389, 0.932, 11.611, 1.071, 11.389, 1.078, 11.611, 0.951]}, {"time": 11.8333, "x": 1.071, "y": 0.951, "curve": [12.056, 1.071, 12.278, 0.932, 12.056, 0.951, 12.278, 1.078]}, {"time": 12.5, "x": 0.932, "y": 1.078, "curve": [12.667, 0.932, 12.833, 1.01, 12.667, 1.078, 12.833, 1.007]}, {"time": 13, "x": 1.049, "y": 0.971}]}, "tunround": {"translate": [{"x": 220.77, "curve": [0.057, 233.71, 0.112, 243.58, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": 243.58, "curve": [0.611, 243.58, 1.056, -241.62, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": -241.74, "curve": [1.944, -241.87, 2.389, 243.46, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": 243.58, "curve": [3.278, 243.71, 3.722, -241.62, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": -241.74, "curve": [4.611, -241.87, 5.056, 243.42, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": 243.58, "curve": [5.833, 243.71, 6.167, -241.7, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": -241.74, "curve": [6.944, -241.8, 7.389, 0.86, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 0.92, "curve": [8.278, 0.98, 8.722, -241.62, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -241.74, "curve": [9.611, -241.87, 10.056, 243.46, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 243.58, "curve": [10.944, 243.71, 11.389, -241.62, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": -241.74, "curve": [12.223, -241.85, 12.613, 128.74, 12.223, 0, 12.613, 0]}, {"time": 13, "x": 220.77}]}, "bodyround": {"translate": [{"x": 46.09, "y": -76.83, "curve": [0.168, 82.37, 0.334, 112.13, 0.168, -153.41, 0.334, -216.24]}, {"time": 0.5, "x": 112.13, "y": -216.24, "curve": [0.944, 112.13, 1.389, -95.45, 0.944, -216.24, 1.389, 221.97]}, {"time": 1.8333, "x": -95.51, "y": 222.08, "curve": [2.278, -95.56, 2.722, 112.08, 2.278, 222.19, 2.722, -216.13]}, {"time": 3.1667, "x": 112.13, "y": -216.24, "curve": [3.611, 112.18, 4.056, -95.45, 3.611, -216.35, 4.056, 221.97]}, {"time": 4.5, "x": -95.51, "y": 222.08, "curve": [4.944, -95.56, 5.389, 112.06, 4.944, 222.19, 5.389, -216.09]}, {"time": 5.8333, "x": 112.13, "y": -216.24, "curve": [6.167, 112.18, 6.5, -95.49, 6.167, -216.35, 6.5, 222.04]}, {"time": 6.8333, "x": -95.51, "y": 222.08, "curve": [7.278, -95.53, 7.722, 8.29, 7.278, 222.13, 7.722, 2.97]}, {"time": 8.1667, "x": 8.31, "y": 2.92, "curve": [8.611, 8.34, 9.056, -95.45, 8.611, 2.86, 9.056, 221.97]}, {"time": 9.5, "x": -95.51, "y": 222.08, "curve": [9.944, -95.56, 10.389, 112.08, 9.944, 222.19, 10.389, -216.13]}, {"time": 10.8333, "x": 112.13, "y": -216.24, "curve": [11.278, 112.18, 11.722, -95.45, 11.278, -216.35, 11.722, 221.97]}, {"time": 12.1667, "x": -95.51, "y": 222.08, "curve": [12.445, -95.54, 12.724, -14.81, 12.445, 222.15, 12.724, 51.72]}, {"time": 13, "x": 46.09, "y": -76.83}]}, "headround3": {"translate": [{"x": 55.61, "curve": [0.279, -35.6, 0.556, -156.93, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -156.93, "curve": [1.278, -156.93, 1.722, 154.58, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 154.66, "curve": [2.611, 154.74, 3.056, -156.85, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -156.93, "curve": [3.944, -157.01, 4.389, 154.58, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 154.66, "curve": [5.278, 154.74, 5.722, -156.82, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -156.93, "curve": [6.5, -157.01, 6.833, 154.63, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 154.66, "curve": [7.611, 154.7, 8.056, -1.1, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -1.14, "curve": [8.944, -1.17, 9.389, 154.58, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 154.66, "curve": [10.278, 154.74, 10.722, -156.85, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -156.93, "curve": [11.611, -157.01, 12.056, 154.58, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 154.66, "curve": [12.667, 154.69, 12.835, 110.72, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 55.61}]}, "headround": {"translate": [{"y": 186.52, "curve": [0.336, 0, 0.668, 0, 0.336, 43.52, 0.668, -238.82]}, {"time": 1, "y": -238.82, "curve": [1.444, 0, 1.889, 0, 1.444, -238.82, 1.889, 267.35]}, {"time": 2.3333, "y": 267.48, "curve": [2.778, 0, 3.222, 0, 2.778, 267.6, 3.222, -238.69]}, {"time": 3.6667, "y": -238.82, "curve": [4.111, 0, 4.556, 0, 4.111, -238.95, 4.556, 267.35]}, {"time": 5, "y": 267.48, "curve": [5.444, 0, 5.889, 0, 5.444, 267.6, 5.889, -238.65]}, {"time": 6.3333, "y": -238.82, "curve": [6.667, 0, 7, 0, 6.667, -238.95, 7, 267.43]}, {"time": 7.3333, "y": 267.48, "curve": [7.778, 0, 8.222, 0, 7.778, 267.54, 8.222, 14.39]}, {"time": 8.6667, "y": 14.33, "curve": [9.111, 0, 9.556, 0, 9.111, 14.26, 9.556, 267.35]}, {"time": 10, "y": 267.48, "curve": [10.444, 0, 10.889, 0, 10.444, 267.6, 10.889, -238.69]}, {"time": 11.3333, "y": -238.82, "curve": [11.778, 0, 12.222, 0, 11.778, -238.95, 12.222, 267.35]}, {"time": 12.6667, "y": 267.48, "curve": [12.779, 0, 12.892, 0, 12.779, 267.51, 12.892, 235.04]}, {"time": 13, "y": 186.52}]}, "sh_R2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -14.85]}, {"time": 7.6667, "value": -14.85, "curve": [8.111, -14.85, 8.556, 0]}, {"time": 9}]}, "sh_R3": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -4.25]}, {"time": 7.6667, "value": -4.25, "curve": [8.111, -4.25, 8.556, 0]}, {"time": 9}]}, "arm_R5": {"translate": [{"x": -3.22, "y": -3.53, "curve": [0.168, -10.13, 0.334, -15.8, 0.168, -4.24, 0.334, -4.82]}, {"time": 0.5, "x": -15.8, "y": -4.82, "curve": [0.944, -15.8, 1.389, 23.76, 0.944, -4.82, 1.389, -0.75]}, {"time": 1.8333, "x": 23.77, "y": -0.75, "curve": [2.278, 23.78, 2.722, -15.79, 2.278, -0.75, 2.722, -4.82]}, {"time": 3.1667, "x": -15.8, "y": -4.82, "curve": [3.611, -15.81, 4.056, 23.76, 3.611, -4.82, 4.056, -0.75]}, {"time": 4.5, "x": 23.77, "y": -0.75, "curve": [4.944, 23.78, 5.389, -15.77, 4.944, -0.75, 5.389, -4.83]}, {"time": 5.8333, "x": -15.8, "y": -4.82, "curve": [6.167, -15.83, 6.5, 82.08, 6.167, -4.81, 6.5, -42.27]}, {"time": 6.8333, "x": 82.09, "y": -42.27, "curve": [7.278, 82.1, 7.722, 57.62, 7.278, -42.28, 7.722, -32.91]}, {"time": 8.1667, "x": 57.62, "y": -32.91, "curve": [8.611, 57.61, 9.056, 82.06, 8.611, -32.91, 9.056, -42.27]}, {"time": 9.5, "x": 82.09, "y": -42.27, "curve": [9.944, 82.11, 10.389, -15.79, 9.944, -42.28, 10.389, -4.82]}, {"time": 10.8333, "x": -15.8, "y": -4.82, "curve": [11.278, -15.81, 11.722, 23.76, 11.278, -4.82, 11.722, -0.75]}, {"time": 12.1667, "x": 23.77, "y": -0.75, "curve": [12.445, 23.78, 12.724, 8.39, 12.445, -0.75, 12.724, -2.33]}, {"time": 13, "x": -3.22, "y": -3.53}]}, "leg_L5": {"rotate": [{"value": 0.01, "curve": "stepped"}, {"time": 5.3333, "value": 0.01, "curve": [5.667, 0.01, 6, 0.01]}, {"time": 6.3333, "value": 0.01, "curve": [6.778, 0.01, 7.222, -0.49]}, {"time": 7.6667, "value": -0.49, "curve": [8.111, -0.49, 8.556, 0.01]}, {"time": 9, "value": 0.01}]}, "foot_R3": {"translate": [{"x": -6.18, "y": 2.06, "curve": [0.444, -6.18, 0.889, 6.18, 0.444, 2.06, 0.889, -5.49]}, {"time": 1.3333, "x": 6.18, "y": -5.5, "curve": [1.778, 6.19, 2.222, -6.18, 1.778, -5.5, 2.222, 2.06]}, {"time": 2.6667, "x": -6.18, "y": 2.06, "curve": [3.111, -6.19, 3.556, 6.18, 3.111, 2.06, 3.556, -5.49]}, {"time": 4, "x": 6.18, "y": -5.5, "curve": [4.444, 6.19, 4.889, -6.18, 4.444, -5.5, 4.889, 2.06]}, {"time": 5.3333, "x": -6.18, "y": 2.06, "curve": [5.667, -6.19, 6, 6.18, 5.667, 2.06, 6, -5.5]}, {"time": 6.3333, "x": 6.18, "y": -5.5, "curve": [6.778, 6.19, 7.222, 0, 6.778, -5.5, 7.222, -1.72]}, {"time": 7.6667, "y": -1.72, "curve": [8.111, 0, 8.556, 6.18, 8.111, -1.72, 8.556, -5.49]}, {"time": 9, "x": 6.18, "y": -5.5, "curve": [9.444, 6.19, 9.889, -6.18, 9.444, -5.5, 9.889, 2.06]}, {"time": 10.3333, "x": -6.18, "y": 2.06, "curve": [10.778, -6.19, 11.222, 6.18, 10.778, 2.06, 11.222, -5.49]}, {"time": 11.6667, "x": 6.18, "y": -5.5, "curve": [12.111, 6.19, 12.556, -6.18, 12.111, -5.5, 12.556, 2.06]}, {"time": 13, "x": -6.18, "y": 2.06}]}, "leg_R4": {"rotate": [{"value": 0.02, "curve": "stepped"}, {"time": 5.3333, "value": 0.02, "curve": [5.667, 0.02, 6, 0.02]}, {"time": 6.3333, "value": 0.02, "curve": [6.778, 0.02, 7.222, -0.55]}, {"time": 7.6667, "value": -0.55, "curve": [8.111, -0.55, 8.556, 0.02]}, {"time": 9, "value": 0.02}]}, "leg_R1": {"translate": [{"y": -15.8, "curve": [0.444, 0, 0.889, 0, 0.444, -15.8, 0.889, 15.91]}, {"time": 1.3333, "y": 15.92, "curve": [1.778, 0, 2.222, 0, 1.778, 15.93, 2.222, -15.8]}, {"time": 2.6667, "y": -15.8, "curve": [3.111, 0, 3.556, 0, 3.111, -15.81, 3.556, 15.91]}, {"time": 4, "y": 15.92, "curve": [4.444, 0, 4.889, 0, 4.444, 15.93, 4.889, -15.79]}, {"time": 5.3333, "y": -15.8, "curve": [5.667, 0, 6, 0, 5.667, -15.81, 6, 15.92]}, {"time": 6.3333, "y": 15.92, "curve": [6.778, 0, 7.222, 0, 6.778, 15.93, 7.222, 0.06]}, {"time": 7.6667, "y": 0.06, "curve": [8.111, 0, 8.556, 0, 8.111, 0.05, 8.556, 15.91]}, {"time": 9, "y": 15.92, "curve": [9.444, 0, 9.889, 0, 9.444, 15.93, 9.889, -15.8]}, {"time": 10.3333, "y": -15.8, "curve": [10.778, 0, 11.222, 0, 10.778, -15.81, 11.222, 15.91]}, {"time": 11.6667, "y": 15.92, "curve": [12.111, 0, 12.556, 0, 12.111, 15.93, 12.556, -15.8]}, {"time": 13, "y": -15.8}]}, "eye_L": {"translate": [{"time": 2.3333, "curve": [2.367, 0, 2.4, -1.64, 2.367, 0, 2.4, 0]}, {"time": 2.4333, "x": -1.64, "curve": "stepped"}, {"time": 3.7333, "x": -1.64, "curve": [3.767, -1.64, 3.8, -0.8, 3.767, 0, 3.8, 1.59]}, {"time": 3.8333, "x": -0.8, "y": 1.59, "curve": "stepped"}, {"time": 4.2333, "x": -0.8, "y": 1.59, "curve": [4.267, -0.8, 4.3, 0, 4.267, 1.59, 4.3, 0]}, {"time": 4.3333, "curve": "stepped"}, {"time": 6.5, "curve": [6.533, 0, 6.567, 2.12, 6.533, 0, 6.567, -2.92]}, {"time": 6.6, "x": 2.12, "y": -2.92, "curve": "stepped"}, {"time": 7.3333, "x": 2.12, "y": -2.92, "curve": [7.367, 2.12, 7.4, 2.84, 7.367, -2.92, 7.4, -1.45]}, {"time": 7.4333, "x": 2.84, "y": -1.45, "curve": "stepped"}, {"time": 8.3333, "x": 2.84, "y": -1.45, "curve": [8.367, 2.84, 8.4, 1.43, 8.367, -1.45, 8.4, -0.48]}, {"time": 8.4333, "x": 1.43, "y": -0.48, "curve": "stepped"}, {"time": 8.8333, "x": 1.43, "y": -0.48, "curve": [8.867, 1.43, 8.9, 2.12, 8.867, -0.48, 8.9, -2.92]}, {"time": 8.9333, "x": 2.12, "y": -2.92, "curve": "stepped"}, {"time": 10, "x": 2.12, "y": -2.92, "curve": [10.033, 2.12, 10.067, 0, 10.033, -2.92, 10.067, 0]}, {"time": 10.1}]}, "eye_R": {"translate": [{"time": 2.3333, "curve": [2.367, 0, 2.4, -1.64, 2.367, 0, 2.4, 0]}, {"time": 2.4333, "x": -1.64, "curve": "stepped"}, {"time": 3.7333, "x": -1.64, "curve": [3.767, -1.64, 3.8, -0.8, 3.767, 0, 3.8, 1.59]}, {"time": 3.8333, "x": -0.8, "y": 1.59, "curve": "stepped"}, {"time": 4.2333, "x": -0.8, "y": 1.59, "curve": [4.267, -0.8, 4.3, 0, 4.267, 1.59, 4.3, 0]}, {"time": 4.3333, "curve": "stepped"}, {"time": 6.5, "curve": [6.533, 0, 6.567, 2.12, 6.533, 0, 6.567, -2.92]}, {"time": 6.6, "x": 2.12, "y": -2.92, "curve": "stepped"}, {"time": 7.3333, "x": 2.12, "y": -2.92, "curve": [7.367, 2.12, 7.4, 2.84, 7.367, -2.92, 7.4, -1.45]}, {"time": 7.4333, "x": 2.84, "y": -1.45, "curve": "stepped"}, {"time": 8.3333, "x": 2.84, "y": -1.45, "curve": [8.367, 2.84, 8.4, 1.43, 8.367, -1.45, 8.4, -0.48]}, {"time": 8.4333, "x": 1.43, "y": -0.48, "curve": "stepped"}, {"time": 8.8333, "x": 1.43, "y": -0.48, "curve": [8.867, 1.43, 8.9, 2.12, 8.867, -0.48, 8.9, -2.92]}, {"time": 8.9333, "x": 2.12, "y": -2.92, "curve": "stepped"}, {"time": 10, "x": 2.12, "y": -2.92, "curve": [10.033, 2.12, 10.067, 0, 10.033, -2.92, 10.067, 0]}, {"time": 10.1}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-19.28296, 1.42595, -19.28271, 1.42673, -19.1012, 2.35359, -19.10132, 2.35422, -15.58215, 2.65349, -15.58154, 2.65411, -11.67407, 1.46216, -11.67419, 1.46307, -7.78674, 1.48181, -7.78845, 1.48282, -5.01465, 0.70541, -5.01562, 0.70557, -4.5603, 0.5336, -4.56152, 0.53397, -4.34106, 0.38943, -4.34106, 0.3891, -1.88513, 0.10199, -1.88501, 0.10193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.79834, -0.03004, -3.7981, -0.02982, -10.61768, -0.537, -10.61768, -0.53577, -16.63892, -0.30692, -16.63965, -0.3056, 0, 0, 0, 0, -5.84546, -0.18359, -5.8429, -0.18326, -12.53528, -0.16908, -12.53125, -0.16818, -17.47961, -0.48282, -17.47778, -0.48166, -19.84253, 0.21478, -19.84094, 0.21564, -18.2948, 1.17033, -18.29419, 1.1713, -13.25342, 0.24599, -13.25317, 0.24661, -5.6217, -0.40976, -5.62134, -0.40936, -3.19946, 0.01854, -3.19922, 0.01862, -1.1969, -0.09715, -1.19702, -0.09723], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-19.28296, 1.42595, -19.28271, 1.42673, -19.1012, 2.35359, -19.10132, 2.35422, -15.58215, 2.65349, -15.58154, 2.65411, -11.67407, 1.46216, -11.67419, 1.46307, -7.78674, 1.48181, -7.78845, 1.48282, -5.01465, 0.70541, -5.01562, 0.70557, -4.5603, 0.5336, -4.56152, 0.53397, -4.34106, 0.38943, -4.34106, 0.3891, -1.88513, 0.10199, -1.88501, 0.10193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.79834, -0.03004, -3.7981, -0.02982, -10.61768, -0.537, -10.61768, -0.53577, -16.63892, -0.30692, -16.63965, -0.3056, 0, 0, 0, 0, -5.84546, -0.18359, -5.8429, -0.18326, -12.53528, -0.16908, -12.53125, -0.16818, -17.47961, -0.48282, -17.47778, -0.48166, -19.84253, 0.21478, -19.84094, 0.21564, -18.2948, 1.17033, -18.29419, 1.1713, -13.25342, 0.24599, -13.25317, 0.24661, -5.6217, -0.40976, -5.62134, -0.40936, -3.19946, 0.01854, -3.19922, 0.01862, -1.1969, -0.09715, -1.19702, -0.09723], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-19.28296, 1.42595, -19.28271, 1.42673, -19.1012, 2.35359, -19.10132, 2.35422, -15.58215, 2.65349, -15.58154, 2.65411, -11.67407, 1.46216, -11.67419, 1.46307, -7.78674, 1.48181, -7.78845, 1.48282, -5.01465, 0.70541, -5.01562, 0.70557, -4.5603, 0.5336, -4.56152, 0.53397, -4.34106, 0.38943, -4.34106, 0.3891, -1.88513, 0.10199, -1.88501, 0.10193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.79834, -0.03004, -3.7981, -0.02982, -10.61768, -0.537, -10.61768, -0.53577, -16.63892, -0.30692, -16.63965, -0.3056, 0, 0, 0, 0, -5.84546, -0.18359, -5.8429, -0.18326, -12.53528, -0.16908, -12.53125, -0.16818, -17.47961, -0.48282, -17.47778, -0.48166, -19.84253, 0.21478, -19.84094, 0.21564, -18.2948, 1.17033, -18.29419, 1.1713, -13.25342, 0.24599, -13.25317, 0.24661, -5.6217, -0.40976, -5.62134, -0.40936, -3.19946, 0.01854, -3.19922, 0.01862, -1.1969, -0.09715, -1.19702, -0.09723], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-13.62048, 0.2653, -13.61731, 0.26648, -17.13086, -0.55737, -17.12903, -0.55527, -16.47986, 0.23102, -16.48022, 0.23279, -18.19604, 1.06152, -18.19531, 1.06433, -16.30994, 0.91203, -16.30774, 0.91357, -9.20532, 0.04759, -9.20386, 0.04898, -2.7561, 0.24165, -2.75537, 0.24216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58142, -0.10909, -2.58032, -0.1088, -4.64429, -0.17604, -4.64221, -0.1756, -6.00464, 0.49704, -6.00269, 0.49744, -6.69824, 0.47934, -6.69592, 0.47983, -9.47937, 0.38971, -9.47644, 0.39041, -11.20654, 0.5918, -11.20312, 0.5929, -10.08582, 0.54526, -10.08081, 0.54611, -16.86951, -1.34116, -16.87109, -1.34015, -19.4325, -0.77528, -19.43066, -0.7738, -17.96472, -0.71649, -17.96338, -0.71506, -12.07397, -0.84212, -12.07422, -0.84082, -5.27246, -0.51056, -5.27393, -0.5094, -0.70801, 0.16367, -0.70813, 0.16382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.84363, -0.03818, -1.84253, -0.03793, -4.93921, 0.07008, -4.93726, 0.07068], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-13.62048, 0.2653, -13.61731, 0.26648, -17.13086, -0.55737, -17.12903, -0.55527, -16.47986, 0.23102, -16.48022, 0.23279, -18.19604, 1.06152, -18.19531, 1.06433, -16.30994, 0.91203, -16.30774, 0.91357, -9.20532, 0.04759, -9.20386, 0.04898, -2.7561, 0.24165, -2.75537, 0.24216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58142, -0.10909, -2.58032, -0.1088, -4.64429, -0.17604, -4.64221, -0.1756, -6.00464, 0.49704, -6.00269, 0.49744, -6.69824, 0.47934, -6.69592, 0.47983, -9.47937, 0.38971, -9.47644, 0.39041, -11.20654, 0.5918, -11.20312, 0.5929, -10.08582, 0.54526, -10.08081, 0.54611, -16.86951, -1.34116, -16.87109, -1.34015, -19.4325, -0.77528, -19.43066, -0.7738, -17.96472, -0.71649, -17.96338, -0.71506, -12.07397, -0.84212, -12.07422, -0.84082, -5.27246, -0.51056, -5.27393, -0.5094, -0.70801, 0.16367, -0.70813, 0.16382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.84363, -0.03818, -1.84253, -0.03793, -4.93921, 0.07008, -4.93726, 0.07068], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-13.62048, 0.2653, -13.61731, 0.26648, -17.13086, -0.55737, -17.12903, -0.55527, -16.47986, 0.23102, -16.48022, 0.23279, -18.19604, 1.06152, -18.19531, 1.06433, -16.30994, 0.91203, -16.30774, 0.91357, -9.20532, 0.04759, -9.20386, 0.04898, -2.7561, 0.24165, -2.75537, 0.24216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58142, -0.10909, -2.58032, -0.1088, -4.64429, -0.17604, -4.64221, -0.1756, -6.00464, 0.49704, -6.00269, 0.49744, -6.69824, 0.47934, -6.69592, 0.47983, -9.47937, 0.38971, -9.47644, 0.39041, -11.20654, 0.5918, -11.20312, 0.5929, -10.08582, 0.54526, -10.08081, 0.54611, -16.86951, -1.34116, -16.87109, -1.34015, -19.4325, -0.77528, -19.43066, -0.7738, -17.96472, -0.71649, -17.96338, -0.71506, -12.07397, -0.84212, -12.07422, -0.84082, -5.27246, -0.51056, -5.27393, -0.5094, -0.70801, 0.16367, -0.70813, 0.16382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.84363, -0.03818, -1.84253, -0.03793, -4.93921, 0.07008, -4.93726, 0.07068], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 356, "vertices": [-1.39026, 0.1626, -1.38843, 0.16296, -5.72571, 0.75792, -5.72375, 0.75858, -11.3739, 0.25272, -11.37231, 0.25385, -16.97925, 0.17497, -16.97864, 0.17609, -18.47559, 0.48941, -18.47412, 0.49048, -16.49011, 0.6078, -16.48572, 0.60895, -10.63269, -0.51813, -10.63098, -0.51736, -5.31519, -0.25931, -5.31055, -0.25815, -1.94214, -0.03717, -1.94031, -0.03665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.35254, 0.13589, -2.35229, 0.13638, -7.68921, -0.21518, -7.68848, -0.21475, -13.09729, -0.00046, -13.09583, 0.00073, -17.4397, -0.18913, -17.43799, -0.18811, -19.04126, 0.41623, -19.04004, 0.41769, -17.34241, 0.6356, -17.34253, 0.63699, -12.05542, 0.57437, -12.05493, 0.57513, -5.255, 0.33615, -5.25439, 0.33682, -1.30139, -0.40515, -1.30115, -0.40497], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "offset": 356, "vertices": [-1.39026, 0.1626, -1.38843, 0.16296, -5.72571, 0.75792, -5.72375, 0.75858, -11.3739, 0.25272, -11.37231, 0.25385, -16.97925, 0.17497, -16.97864, 0.17609, -18.47559, 0.48941, -18.47412, 0.49048, -16.49011, 0.6078, -16.48572, 0.60895, -10.63269, -0.51813, -10.63098, -0.51736, -5.31519, -0.25931, -5.31055, -0.25815, -1.94214, -0.03717, -1.94031, -0.03665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.35254, 0.13589, -2.35229, 0.13638, -7.68921, -0.21518, -7.68848, -0.21475, -13.09729, -0.00046, -13.09583, 0.00073, -17.4397, -0.18913, -17.43799, -0.18811, -19.04126, 0.41623, -19.04004, 0.41769, -17.34241, 0.6356, -17.34253, 0.63699, -12.05542, 0.57437, -12.05493, 0.57513, -5.255, 0.33615, -5.25439, 0.33682, -1.30139, -0.40515, -1.30115, -0.40497], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 356, "vertices": [-1.39026, 0.1626, -1.38843, 0.16296, -5.72571, 0.75792, -5.72375, 0.75858, -11.3739, 0.25272, -11.37231, 0.25385, -16.97925, 0.17497, -16.97864, 0.17609, -18.47559, 0.48941, -18.47412, 0.49048, -16.49011, 0.6078, -16.48572, 0.60895, -10.63269, -0.51813, -10.63098, -0.51736, -5.31519, -0.25931, -5.31055, -0.25815, -1.94214, -0.03717, -1.94031, -0.03665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.35254, 0.13589, -2.35229, 0.13638, -7.68921, -0.21518, -7.68848, -0.21475, -13.09729, -0.00046, -13.09583, 0.00073, -17.4397, -0.18913, -17.43799, -0.18811, -19.04126, 0.41623, -19.04004, 0.41769, -17.34241, 0.6356, -17.34253, 0.63699, -12.05542, 0.57437, -12.05493, 0.57513, -5.255, 0.33615, -5.25439, 0.33682, -1.30139, -0.40515, -1.30115, -0.40497], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1]}, {"time": 6.6667, "offset": 80, "vertices": [0.73291, -1.00525, 0.72974, -1.00543, 0.35681, -0.61056, 0.35449, -0.61069, -0.16821, -0.15101, -0.16943, -0.15106, -1.08728, 0.00275, -1.08765, 0.00272, -1.31665, -0.01244, -1.31714, -0.01248, -1.83765, -0.04693, -1.83838, -0.04698, -1.19812, -0.06218, -1.19849, -0.06223, -1.83765, -0.04693, -1.83838, -0.04698, -1.83765, -0.04693, -1.83838, -0.04698, -1.83765, -0.04693, -1.83838, -0.04698, -0.24561, 0.2613, -0.24585, 0.26118, 0.11609, 0.6553, 0.11548, 0.65506, 0.78577, 0.69963, 0.78564, 0.69942, 0.78577, 0.69963, 0.78564, 0.69942, 0.78577, 0.69963, 0.78564, 0.69942, 1.37622, 0.81342, 1.37476, 0.81319, 0.94031, 0.41451, 0.93872, 0.41437, 0.78577, 0.69963, 0.78564, 0.69942, 0.34985, 0.30071, 0.34985, 0.30061, 0.09204, 0.0061, 0.09204, 0.00609, -0.56738, -0.03757, -0.56763, -0.03758, 0, 0, 0, 0, -0.64661, 0.0319, -0.64697, 0.03188, 0.19031, -0.0799, 0.19019, -0.07991, 0.42725, -0.1116, 0.42627, -0.11163, 0.87292, -0.50169, 0.87085, -0.50179, 1.24902, -0.89638, 1.24609, -0.89653, 1.69177, 0.05436, 1.68823, 0.05424, 2.2179, 0.03159, 2.21338, 0.03143, 2.96973, 0.08135, 2.96411, 0.08116, 2.2179, 0.03159, 2.21338, 0.03143, 1.69177, 0.05436, 1.68823, 0.05424, 1.12793, -0.06524, 1.125, -0.06534, 1.24011, -0.40267, 1.23706, -0.40279, 1.61621, -0.79736, 1.6123, -0.79753, 1.24902, -0.89638, 1.24609, -0.89653, 1.24902, -0.89638, 1.24609, -0.89653, 1.24902, -0.89638, 1.24609, -0.89653, 1.80261, -0.02778, 1.79834, -0.02794, 2.39331, -0.06222, 2.38745, -0.06244, 1.80261, -0.02778, 1.79834, -0.02794, 1.40076, 0.09272, 1.39941, 0.09271, 1.40076, 0.09272, 1.39941, 0.09271, 1.40076, 0.29569, 1.40015, 0.29567, 1.40076, 0.29569, 1.40015, 0.29567, 0.93384, 0.0042, 0.93188], "curve": "stepped"}, {"time": 9.6667, "offset": 80, "vertices": [0.73291, -1.00525, 0.72974, -1.00543, 0.35681, -0.61056, 0.35449, -0.61069, -0.16821, -0.15101, -0.16943, -0.15106, -1.08728, 0.00275, -1.08765, 0.00272, -1.31665, -0.01244, -1.31714, -0.01248, -1.83765, -0.04693, -1.83838, -0.04698, -1.19812, -0.06218, -1.19849, -0.06223, -1.83765, -0.04693, -1.83838, -0.04698, -1.83765, -0.04693, -1.83838, -0.04698, -1.83765, -0.04693, -1.83838, -0.04698, -0.24561, 0.2613, -0.24585, 0.26118, 0.11609, 0.6553, 0.11548, 0.65506, 0.78577, 0.69963, 0.78564, 0.69942, 0.78577, 0.69963, 0.78564, 0.69942, 0.78577, 0.69963, 0.78564, 0.69942, 1.37622, 0.81342, 1.37476, 0.81319, 0.94031, 0.41451, 0.93872, 0.41437, 0.78577, 0.69963, 0.78564, 0.69942, 0.34985, 0.30071, 0.34985, 0.30061, 0.09204, 0.0061, 0.09204, 0.00609, -0.56738, -0.03757, -0.56763, -0.03758, 0, 0, 0, 0, -0.64661, 0.0319, -0.64697, 0.03188, 0.19031, -0.0799, 0.19019, -0.07991, 0.42725, -0.1116, 0.42627, -0.11163, 0.87292, -0.50169, 0.87085, -0.50179, 1.24902, -0.89638, 1.24609, -0.89653, 1.69177, 0.05436, 1.68823, 0.05424, 2.2179, 0.03159, 2.21338, 0.03143, 2.96973, 0.08135, 2.96411, 0.08116, 2.2179, 0.03159, 2.21338, 0.03143, 1.69177, 0.05436, 1.68823, 0.05424, 1.12793, -0.06524, 1.125, -0.06534, 1.24011, -0.40267, 1.23706, -0.40279, 1.61621, -0.79736, 1.6123, -0.79753, 1.24902, -0.89638, 1.24609, -0.89653, 1.24902, -0.89638, 1.24609, -0.89653, 1.24902, -0.89638, 1.24609, -0.89653, 1.80261, -0.02778, 1.79834, -0.02794, 2.39331, -0.06222, 2.38745, -0.06244, 1.80261, -0.02778, 1.79834, -0.02794, 1.40076, 0.09272, 1.39941, 0.09271, 1.40076, 0.09272, 1.39941, 0.09271, 1.40076, 0.29569, 1.40015, 0.29567, 1.40076, 0.29569, 1.40015, 0.29567, 0.93384, 0.0042, 0.93188], "curve": [9.889, 0, 10.111, 1]}, {"time": 10.3333}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": 15.41, "curve": [0.025, 15.27, 0.059, -15.08, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -15.08, "curve": [0.544, -15.08, 0.856, 15.41, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 15.41}]}, "ALL2": {"translate": [{"y": -24.64, "curve": [0.078, 0, 0.156, 0, 0.025, -24.4, 0.059, 30.55]}, {"time": 0.2333, "y": 30.55, "curve": [0.544, 0, 0.856, 0, 0.544, 30.55, 0.856, -24.64]}, {"time": 1.1667, "y": -24.64}]}, "body": {"rotate": [{"value": 5.13, "curve": [0.025, 5.08, 0.059, -6.39]}, {"time": 0.2333, "value": -6.39, "curve": [0.544, -6.39, 0.856, 5.13]}, {"time": 1.1667, "value": 5.13}], "translate": [{"x": 1.56, "y": -10.29, "curve": [0.025, 1.54, 0.059, -1.72, 0.025, -10.17, 0.059, 17.07]}, {"time": 0.2333, "x": -1.72, "y": 17.07, "curve": [0.544, -1.72, 0.856, 1.56, 0.544, 17.07, 0.856, -10.29]}, {"time": 1.1667, "x": 1.56, "y": -10.29}], "scale": [{"y": 1.032, "curve": [0.078, 1, 0.156, 1, 0.025, 1.032, 0.059, 0.972]}, {"time": 0.2333, "y": 0.972, "curve": [0.544, 1, 0.856, 1, 0.544, 0.972, 0.856, 1.032]}, {"time": 1.1667, "y": 1.032}]}, "body2": {"rotate": [{"value": -1.96, "curve": [0.029, -1.92, 0.067, 5.32]}, {"time": 0.2667, "value": 5.32, "curve": [0.567, 5.32, 0.867, -2.34]}, {"time": 1.1667, "value": -1.96}], "translate": [{"x": -10.61, "curve": [0.029, -10.51, 0.067, 9.95, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 9.95, "curve": [0.567, 9.95, 0.867, -11.69, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -10.61}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.029, 1.003, 0.067, 1.023, 0.029, 1.003, 0.067, 1.023]}, {"time": 0.2667, "x": 1.023, "y": 1.023, "curve": [0.567, 1.023, 0.867, 1.002, 0.567, 1.023, 0.867, 1.002]}, {"time": 1.1667, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": 0.96, "curve": [0.033, 0.88, 0.076, -13.43]}, {"time": 0.3, "value": -13.43, "curve": [0.589, -13.43, 0.878, 0.96]}, {"time": 1.1667, "value": 0.96}]}, "head": {"rotate": [{"value": 0.09, "curve": [0.033, 0.02, 0.076, -13.43]}, {"time": 0.3, "value": -13.43, "curve": [0.589, -13.43, 0.878, 0.09]}, {"time": 1.1667, "value": 0.09}]}, "hair_FL2": {"rotate": [{"value": 2.2, "curve": [0.122, -0.75, 0.245, -4.67]}, {"time": 0.3667, "value": -4.67, "curve": [0.561, -4.67, 0.739, 5.4]}, {"time": 0.9333, "value": 5.4, "curve": [1.007, 5.4, 1.094, 3.98]}, {"time": 1.1667, "value": 2.2}]}, "hair_B2": {"rotate": [{"value": 3.79, "curve": [0.147, 0.95, 0.288, -4.67]}, {"time": 0.4333, "value": -4.67, "curve": [0.628, -4.67, 0.839, 5.4]}, {"time": 1.0333, "value": 5.4, "curve": [1.083, 5.4, 1.119, 4.76]}, {"time": 1.1667, "value": 3.79}]}, "hair_F2": {"rotate": [{"value": 0.37, "curve": [0.099, -2.13, 0.203, -4.67]}, {"time": 0.3, "value": -4.67, "curve": [0.495, -4.67, 0.672, 5.4]}, {"time": 0.8667, "value": 5.4, "curve": [0.965, 5.4, 1.071, 2.9]}, {"time": 1.1667, "value": 0.37}]}, "hair_FR2": {"rotate": [{"value": -1.47, "curve": [0.122, 1.48, 0.245, 5.4]}, {"time": 0.3667, "value": 5.4, "curve": [0.561, 5.4, 0.739, -4.66]}, {"time": 0.9333, "value": -4.67, "curve": [1.007, -4.67, 1.094, -3.25]}, {"time": 1.1667, "value": -1.47}]}, "sh_L": {"translate": [{"x": -4.09, "curve": [0.029, -4, 0.067, 14.38, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 14.38, "curve": [0.567, 14.38, 0.867, -4.09, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.09}]}, "arm_L": {"rotate": [{"value": -2.42, "curve": [0.074, -3.05, 0.161, -3.56]}, {"time": 0.2333, "value": -3.56, "curve": [0.428, -3.56, 0.605, 0.02]}, {"time": 0.8, "value": 0.02, "curve": [0.922, 0.02, 1.046, -1.37]}, {"time": 1.1667, "value": -2.42}]}, "arm_L2": {"rotate": [{"value": -1.77, "curve": [0.099, -2.66, 0.203, -3.56]}, {"time": 0.3, "value": -3.56, "curve": [0.495, -3.56, 0.672, 0.02]}, {"time": 0.8667, "value": 0.02, "curve": [0.965, 0.02, 1.071, -0.87]}, {"time": 1.1667, "value": -1.77}]}, "arm_L3": {"rotate": [{"value": 1.45, "curve": [0.122, -0.81, 0.245, -3.81]}, {"time": 0.3667, "value": -3.81, "curve": [0.561, -3.81, 0.739, 3.89]}, {"time": 0.9333, "value": 3.9, "curve": [1.007, 3.9, 1.094, 2.81]}, {"time": 1.1667, "value": 1.45}]}, "arm_L4": {"rotate": [{"value": 2.66, "curve": [0.147, 0.49, 0.288, -3.81]}, {"time": 0.4333, "value": -3.81, "curve": [0.628, -3.81, 0.839, 3.89]}, {"time": 1.0333, "value": 3.9, "curve": [1.083, 3.9, 1.119, 3.4]}, {"time": 1.1667, "value": 2.66}]}, "sh_R": {"translate": [{"x": -5.94, "curve": [0.029, -5.78, 0.067, 24.33, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 24.33, "curve": [0.567, 24.33, 0.867, -5.94, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -5.94}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{"value": 0.05}]}, "arm_R3": {"rotate": [{"value": -0.05, "curve": [0.029, -0.02, 0.067, 16.97]}, {"time": 0.2667, "value": 16.97, "curve": [0.567, 16.97, 0.867, -0.38]}, {"time": 1.1667, "value": -0.05}]}, "arm_R4": {"rotate": [{"value": 2.09, "curve": [0.029, 2.13, 0.067, 12.09]}, {"time": 0.2667, "value": 12.09, "curve": [0.567, 12.09, 0.867, 1.77]}, {"time": 1.1667, "value": 2.09}]}, "tun": {"rotate": [{"value": 5.13, "curve": [0.025, 5.08, 0.059, -6.39]}, {"time": 0.2333, "value": -6.39, "curve": [0.544, -6.39, 0.856, 5.13]}, {"time": 1.1667, "value": 5.13}]}, "leg_L3": {"rotate": [{}]}, "leg_R3": {"rotate": [{"value": 0.01}]}, "RU_L": {"translate": [{"x": -16.09, "y": -3.43, "curve": [0.074, -28.69, 0.161, -39.02, 0.074, -6.12, 0.161, -8.33]}, {"time": 0.2333, "x": -39.02, "y": -8.33, "curve": [0.428, -39.02, 0.605, 33.06, 0.428, -8.33, 0.605, 7.06]}, {"time": 0.8, "x": 33.08, "y": 7.06, "curve": [0.922, 33.09, 1.046, 5.06, 0.922, 7.06, 1.046, 1.08]}, {"time": 1.1667, "x": -16.08, "y": -3.43}], "scale": [{"x": 1.049, "y": 0.971, "curve": [0.073, 1.01, 0.16, 0.932, 0.073, 1.007, 0.16, 1.078]}, {"time": 0.2333, "x": 0.932, "y": 1.078, "curve": [0.331, 0.932, 0.403, 1.071, 0.331, 1.078, 0.403, 0.951]}, {"time": 0.5, "x": 1.071, "y": 0.951, "curve": [0.597, 1.071, 0.703, 0.932, 0.597, 0.951, 0.703, 1.078]}, {"time": 0.8, "x": 0.932, "y": 1.078, "curve": [0.897, 0.932, 1.003, 1.071, 0.897, 1.078, 1.003, 0.951]}, {"time": 1.1, "x": 1.071, "y": 0.951, "curve": [1.125, 1.071, 1.143, 1.062, 1.125, 0.951, 1.143, 0.959]}, {"time": 1.1667, "x": 1.049, "y": 0.971}]}, "RU_R": {"translate": [{"x": -16.09, "y": -3.43, "curve": [0.074, -28.69, 0.161, -39.02, 0.074, -6.12, 0.161, -8.33]}, {"time": 0.2333, "x": -39.02, "y": -8.33, "curve": [0.428, -39.02, 0.605, 33.06, 0.428, -8.33, 0.605, 7.06]}, {"time": 0.8, "x": 33.08, "y": 7.06, "curve": [0.922, 33.09, 1.046, 5.06, 0.922, 7.06, 1.046, 1.08]}, {"time": 1.1667, "x": -16.08, "y": -3.43}], "scale": [{"x": 1.049, "y": 0.971, "curve": [0.073, 1.01, 0.16, 0.932, 0.073, 1.007, 0.16, 1.078]}, {"time": 0.2333, "x": 0.932, "y": 1.078, "curve": [0.331, 0.932, 0.403, 1.071, 0.331, 1.078, 0.403, 0.951]}, {"time": 0.5, "x": 1.071, "y": 0.951, "curve": [0.597, 1.071, 0.703, 0.932, 0.597, 0.951, 0.703, 1.078]}, {"time": 0.8, "x": 0.932, "y": 1.078, "curve": [0.897, 0.932, 1.003, 1.071, 0.897, 1.078, 1.003, 0.951]}, {"time": 1.1, "x": 1.071, "y": 0.951, "curve": [1.125, 1.071, 1.143, 1.062, 1.125, 0.951, 1.143, 0.959]}, {"time": 1.1667, "x": 1.049, "y": 0.971}]}, "RU_R2": {"translate": [{"x": -0.2, "y": -0.03, "curve": [0.099, -18.44, 0.203, -36.93, 0.099, -2.93, 0.203, -5.88]}, {"time": 0.3, "x": -36.93, "y": -5.88, "curve": [0.495, -36.93, 0.672, 36.51, 0.495, -5.88, 0.672, 5.81]}, {"time": 0.8667, "x": 36.52, "y": 5.81, "curve": [0.965, 36.53, 1.071, 18.3, 0.965, 5.81, 1.071, 2.91]}, {"time": 1.1667, "x": -0.19, "y": -0.03}], "scale": [{"x": 1.071, "y": 0.951, "curve": [0.097, 1.071, 0.203, 0.932, 0.097, 0.951, 0.203, 1.078]}, {"time": 0.3, "x": 0.932, "y": 1.078, "curve": [0.397, 0.932, 0.503, 1.071, 0.397, 1.078, 0.503, 0.951]}, {"time": 0.6, "x": 1.071, "y": 0.951, "curve": [0.697, 1.071, 0.769, 0.932, 0.697, 0.951, 0.769, 1.078]}, {"time": 0.8667, "x": 0.932, "y": 1.078, "curve": [0.964, 0.932, 1.069, 1.071, 0.964, 1.078, 1.069, 0.951]}, {"time": 1.1667, "x": 1.071, "y": 0.951}]}, "RU_R3": {"translate": [{"x": 16.53, "curve": [0.122, -2.5, 0.245, -27.82, 0.122, 0, 0.245, 0]}, {"time": 0.3667, "x": -27.82, "curve": [0.561, -27.82, 0.739, 37.19, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 37.2, "curve": [1.007, 37.21, 1.094, 28.04, 1.007, 0, 1.094, 0]}, {"time": 1.1667, "x": 16.54}], "scale": [{"x": 1.049, "y": 0.971, "curve": [0.024, 1.062, 0.042, 1.071, 0.024, 0.96, 0.042, 0.951]}, {"time": 0.0667, "x": 1.071, "y": 0.951, "curve": [0.164, 1.071, 0.269, 0.932, 0.164, 0.951, 0.269, 1.078]}, {"time": 0.3667, "x": 0.932, "y": 1.078, "curve": [0.464, 0.932, 0.569, 1.071, 0.464, 1.078, 0.569, 0.951]}, {"time": 0.6667, "x": 1.071, "y": 0.951, "curve": [0.764, 1.071, 0.836, 0.932, 0.764, 0.951, 0.836, 1.078]}, {"time": 0.9333, "x": 0.932, "y": 1.078, "curve": [1.007, 0.932, 1.094, 1.01, 1.007, 1.078, 1.094, 1.007]}, {"time": 1.1667, "x": 1.049, "y": 0.971}]}, "RU_L2": {"translate": [{"x": -0.2, "y": -0.02, "curve": [0.099, -18.61, 0.203, -37.27, 0.099, -1.56, 0.203, -3.13]}, {"time": 0.3, "x": -37.27, "y": -3.13, "curve": [0.495, -37.27, 0.672, 36.84, 0.495, -3.13, 0.672, 3.09]}, {"time": 0.8667, "x": 36.85, "y": 3.09, "curve": [0.965, 36.86, 1.071, 18.46, 0.965, 3.09, 1.071, 1.55]}, {"time": 1.1667, "x": -0.19, "y": -0.02}], "scale": [{"x": 1.071, "y": 0.951, "curve": [0.097, 1.071, 0.203, 0.932, 0.097, 0.951, 0.203, 1.078]}, {"time": 0.3, "x": 0.932, "y": 1.078, "curve": [0.397, 0.932, 0.503, 1.071, 0.397, 1.078, 0.503, 0.951]}, {"time": 0.6, "x": 1.071, "y": 0.951, "curve": [0.697, 1.071, 0.769, 0.932, 0.697, 0.951, 0.769, 1.078]}, {"time": 0.8667, "x": 0.932, "y": 1.078, "curve": [0.964, 0.932, 1.069, 1.071, 0.964, 1.078, 1.069, 0.951]}, {"time": 1.1667, "x": 1.071, "y": 0.951}]}, "RU_L3": {"translate": [{"x": 16.53, "curve": [0.122, -2.5, 0.245, -27.82, 0.122, 0, 0.245, 0]}, {"time": 0.3667, "x": -27.82, "curve": [0.561, -27.82, 0.739, 37.19, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 37.2, "curve": [1.007, 37.21, 1.094, 28.04, 1.007, 0, 1.094, 0]}, {"time": 1.1667, "x": 16.54}], "scale": [{"x": 1.049, "y": 0.971, "curve": [0.024, 1.062, 0.042, 1.071, 0.024, 0.96, 0.042, 0.951]}, {"time": 0.0667, "x": 1.071, "y": 0.951, "curve": [0.164, 1.071, 0.269, 0.932, 0.164, 0.951, 0.269, 1.078]}, {"time": 0.3667, "x": 0.932, "y": 1.078, "curve": [0.464, 0.932, 0.569, 1.071, 0.464, 1.078, 0.569, 0.951]}, {"time": 0.6667, "x": 1.071, "y": 0.951, "curve": [0.764, 1.071, 0.836, 0.932, 0.764, 0.951, 0.836, 1.078]}, {"time": 0.9333, "x": 0.932, "y": 1.078, "curve": [1.007, 0.932, 1.094, 1.01, 1.007, 1.078, 1.094, 1.007]}, {"time": 1.1667, "x": 1.049, "y": 0.971}]}, "tunround": {"translate": [{"x": 220.77, "curve": [0.025, 218.21, 0.059, -355.47, 0.025, 0, 0.059, 0]}, {"time": 0.2333, "x": -355.47, "curve": [0.544, -355.47, 0.856, 220.77, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 220.77}]}, "bodyround": {"translate": [{"x": 46.09, "y": -76.83, "curve": [0.033, 45.23, 0.076, -103.74, 0.033, -74.47, 0.076, 336.14]}, {"time": 0.3, "x": -103.74, "y": 336.14, "curve": [0.589, -103.74, 0.878, 46.09, 0.589, 336.14, 0.878, -76.83]}, {"time": 1.1667, "x": 46.09, "y": -76.83}]}, "headround3": {"translate": [{"x": 55.61, "curve": [0.036, 56.41, 0.084, 182.68, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 182.68, "curve": [0.611, 182.68, 0.889, 55.61, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 55.61}]}, "headround": {"translate": [{"y": 186.52, "curve": [0.111, 0, 0.222, 0, 0.036, 187.44, 0.084, 331.24]}, {"time": 0.3333, "y": 331.24, "curve": [0.611, 0, 0.889, 0, 0.611, 331.24, 0.889, 186.52]}, {"time": 1.1667, "y": 186.52}]}, "sh_R2": {"rotate": [{}]}, "sh_R3": {"rotate": [{}]}, "arm_R5": {"translate": [{"x": -3.22, "y": -3.53, "curve": [0.033, -3.22, 0.076, -3.67, 0.033, -3.81, 0.076, -53.28]}, {"time": 0.3, "x": -3.67, "y": -53.28, "curve": [0.589, -3.67, 0.878, -3.22, 0.589, -53.28, 0.878, -3.53]}, {"time": 1.1667, "x": -3.22, "y": -3.53}]}, "leg_L5": {"rotate": [{"value": 0.01}]}, "foot_R3": {"translate": [{"x": -6.18, "y": 2.06, "curve": [0.025, -6.14, 0.059, 4.56, 0.025, 2.03, 0.059, -3.92]}, {"time": 0.2333, "x": 4.56, "y": -3.92, "curve": [0.544, 4.56, 0.856, -6.18, 0.544, -3.92, 0.856, 2.06]}, {"time": 1.1667, "x": -6.18, "y": 2.06}]}, "leg_R4": {"rotate": [{"value": 0.02}]}, "leg_R1": {"translate": [{"y": -15.8, "curve": [0.025, 0.04, 0.059, 8.22, 0.025, -15.67, 0.059, 15.08]}, {"time": 0.2333, "x": 8.22, "y": 15.08, "curve": [0.544, 8.22, 0.856, 0, 0.544, 15.08, 0.856, -15.8]}, {"time": 1.1667, "y": -15.8}]}, "eyebrow_L": {"translate": [{"curve": [0.025, 0.01, 0.059, -0.85, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -0.85, "curve": [0.544, -0.85, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.025, 0.01, 0.059, -0.85, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -0.85, "curve": [0.544, -0.85, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.025, -0.03, 0.059, -7.65]}, {"time": 0.2333, "value": -7.65, "curve": [0.544, -7.65, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.025, 1, 0.059, 0.952, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.952, "curve": [0.544, 0.952, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.025, -0.06, 0.059, -13.75]}, {"time": 0.2333, "value": -13.75, "curve": [0.544, -13.75, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.025, 0.05, 0.059, 10.92]}, {"time": 0.2333, "value": 10.92, "curve": [0.544, 10.92, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.025, 1, 0.059, 0.952, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.952, "curve": [0.544, 0.952, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.025, 0.08, 0.059, 18.43]}, {"time": 0.2333, "value": 18.43, "curve": [0.544, 18.43, 0.856, 0]}, {"time": 1.1667}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.025, 0, 0.059, 1]}, {"time": 0.2333, "vertices": [-4.16511, 0.308, -4.16506, 0.30817, -4.12585, 0.50838, -4.12588, 0.50851, -3.36574, 0.57315, -3.36561, 0.57329, -2.5216, 0.31583, -2.52162, 0.31602, -1.68193, 0.32007, -1.6823, 0.32029, -1.08316, 0.15237, -1.08337, 0.1524, -0.98502, 0.11526, -0.98529, 0.11534, -0.93767, 0.08412, -0.93767, 0.08405, -0.40719, 0.02203, -0.40716, 0.02202, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.82044, -0.00649, -0.82039, -0.00644, -2.29342, -0.11599, -2.29342, -0.11573, -3.594, -0.06629, -3.59416, -0.06601, 0, 0, 0, 0, -1.26262, -0.03966, -1.26206, -0.03958, -2.70762, -0.03652, -2.70675, -0.03633, -3.77559, -0.10429, -3.7752, -0.10404, -4.28598, 0.04639, -4.28564, 0.04658, -3.95167, 0.25279, -3.95154, 0.253, -2.86273, 0.05313, -2.86268, 0.05327, -1.21429, -0.08851, -1.21421, -0.08842, -0.69108, 0.004, -0.69103, 0.00402, -0.25853, -0.02098, -0.25856, -0.021], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.025, 0, 0.059, 1]}, {"time": 0.2333, "vertices": [-2.94202, 0.05731, -2.94133, 0.05756, -3.70026, -0.12039, -3.69987, -0.11994, -3.55964, 0.0499, -3.55972, 0.05028, -3.93034, 0.22929, -3.93018, 0.2299, -3.52294, 0.197, -3.52247, 0.19733, -1.98835, 0.01028, -1.98803, 0.01058, -0.59532, 0.0522, -0.59516, 0.05231, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.55759, -0.02356, -0.55735, -0.0235, -1.00316, -0.03802, -1.00272, -0.03793, -1.297, 0.10736, -1.29658, 0.10745, -1.44682, 0.10354, -1.44632, 0.10364, -2.04754, 0.08418, -2.04691, 0.08433, -2.42061, 0.12783, -2.41987, 0.12807, -2.17853, 0.11778, -2.17745, 0.11796, -3.64381, -0.28969, -3.64415, -0.28947, -4.19741, -0.16746, -4.19702, -0.16714, -3.88037, -0.15476, -3.88008, -0.15445, -2.60798, -0.1819, -2.60803, -0.18162, -1.13885, -0.11028, -1.13917, -0.11003, -0.15293, 0.03535, -0.15296, 0.03538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.39822, -0.00825, -0.39799, -0.00819, -1.06687, 0.01514, -1.06645, 0.01527], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.025, 0, 0.059, 1]}, {"time": 0.2333, "offset": 356, "vertices": [-0.3003, 0.03512, -0.2999, 0.0352, -1.23675, 0.16371, -1.23633, 0.16385, -2.45676, 0.05459, -2.45642, 0.05483, -3.66751, 0.03779, -3.66738, 0.03803, -3.99072, 0.10571, -3.99041, 0.10594, -3.56186, 0.13129, -3.56091, 0.13153, -2.29666, -0.11192, -2.29629, -0.11175, -1.14808, -0.05601, -1.14708, -0.05576, -0.4195, -0.00803, -0.41911, -0.00792, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.50815, 0.02935, -0.5081, 0.02946, -1.66087, -0.04648, -1.66071, -0.04639, -2.82901, -0.0001, -2.82869, 0.00016, -3.76697, -0.04085, -3.7666, -0.04063, -4.11291, 0.08991, -4.11264, 0.09022, -3.74596, 0.13729, -3.74598, 0.13759, -2.60397, 0.12406, -2.60386, 0.12423, -1.13508, 0.07261, -1.13495, 0.07275, -0.2811, -0.08751, -0.28105, -0.08747], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.025, 0, 0.059, 1]}, {"time": 0.2333, "offset": 80, "vertices": [0.73291, -1.00525, 0.72974, -1.00543, 0.35681, -0.61056, 0.35449, -0.61069, -0.16821, -0.15101, -0.16943, -0.15106, -1.08728, 0.00275, -1.08765, 0.00272, -1.31665, -0.01244, -1.31714, -0.01248, -1.83765, -0.04693, -1.83838, -0.04698, -1.19812, -0.06218, -1.19849, -0.06223, -1.83765, -0.04693, -1.83838, -0.04698, -1.83765, -0.04693, -1.83838, -0.04698, -1.83765, -0.04693, -1.83838, -0.04698, -0.24561, 0.2613, -0.24585, 0.26118, 0.11609, 0.6553, 0.11548, 0.65506, 0.78577, 0.69963, 0.78564, 0.69942, 0.78577, 0.69963, 0.78564, 0.69942, 0.78577, 0.69963, 0.78564, 0.69942, 1.37622, 0.81342, 1.37476, 0.81319, 0.94031, 0.41451, 0.93872, 0.41437, 0.78577, 0.69963, 0.78564, 0.69942, 0.34985, 0.30071, 0.34985, 0.30061, 0.09204, 0.0061, 0.09204, 0.00609, -0.56738, -0.03757, -0.56763, -0.03758, 0, 0, 0, 0, -0.64661, 0.0319, -0.64697, 0.03188, 0.19031, -0.0799, 0.19019, -0.07991, 0.42725, -0.1116, 0.42627, -0.11163, 0.87292, -0.50169, 0.87085, -0.50179, 1.24902, -0.89638, 1.24609, -0.89653, 1.69177, 0.05436, 1.68823, 0.05424, 2.2179, 0.03159, 2.21338, 0.03143, 2.96973, 0.08135, 2.96411, 0.08116, 2.2179, 0.03159, 2.21338, 0.03143, 1.69177, 0.05436, 1.68823, 0.05424, 1.12793, -0.06524, 1.125, -0.06534, 1.24011, -0.40267, 1.23706, -0.40279, 1.61621, -0.79736, 1.6123, -0.79753, 1.24902, -0.89638, 1.24609, -0.89653, 1.24902, -0.89638, 1.24609, -0.89653, 1.24902, -0.89638, 1.24609, -0.89653, 1.80261, -0.02778, 1.79834, -0.02794, 2.39331, -0.06222, 2.38745, -0.06244, 1.80261, -0.02778, 1.79834, -0.02794, 1.40076, 0.09272, 1.39941, 0.09271, 1.40076, 0.09272, 1.39941, 0.09271, 1.40076, 0.29569, 1.40015, 0.29567, 1.40076, 0.29569, 1.40015, 0.29567, 0.93384, 0.0042, 0.93188], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}}}}}}