﻿ 



 
 


#if UNITY_EDITOR

/*
 * created by wzw at 2019.12.16
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //地图对象的模型层
    public class RuinLayerView : ModelLayerView
    {
        public RuinLayerView(MapLayerData layerData, bool asyncLoading)
        : base(layerData, asyncLoading)
        {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new RuinView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }

        public void ChangePrefab(RuinSetting ruinType)
        {
            foreach (var view in mViews)
            {
                var ruinView = view.Value as RuinView;
                var ruinData = Map.currentMap.FindObject(ruinView.objectDataID) as RuinData;
                if (ruinData.objectType == ruinType.name)
                {
                    ruinView.ChangePrefab(ruinType);
                }
            }
        }

        //计算transform改变后的entity id
        public List<ObjectTransformInfo> GetTransformChangedObjectIDs()
        {
            List<ObjectTransformInfo> changedObjects = new List<ObjectTransformInfo>();
#if UNITY_EDITOR
            foreach (var view in mViews)
            {
                var behaviour = view.Value.model.gameObject.GetComponentInChildren<RuinObjectBehaviour>();
                if (behaviour != null)
                {
                    bool isTransformChanged = behaviour.IsTransformChanged();
                    if (isTransformChanged)
                    {
                        ObjectTransformInfo info = new ObjectTransformInfo();
                        var transform = view.Value.transform;
                        info.objectID = view.Key;
                        info.position = transform.position;
                        info.rotation = transform.rotation;

                        changedObjects.Add(info);
                        behaviour.Reset();
                    }
                }
            }
#endif
            return changedObjects;
        }
    }
}

#endif