﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class CityTerritoryLayerData : QuadTreeObjectLayerData
    {
        public class SubLayerData
        {
            public void Init(int horizontalTileCount, int verticalTileCount, float tileWidth, float tileHeight, CityTerritoryData[] territories, CityTerritoryEdgeData[] edges, short[,] grids, bool isActive, List<CityTerritoryDataBlock> blocks, Texture2D maskTexture)
            {
                this.horizontalTileCount = horizontalTileCount;
                this.verticalTileCount = verticalTileCount;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.territories = territories;
                this.edges = edges;
                this.grids = grids;
                this.isActive = isActive;
                this.blocks = blocks;
                this.maskTexture = maskTexture;
                territoriesByID = new Dictionary<int, CityTerritoryData>();
                foreach (var t in territories)
                {
                    territoriesByID.Add(t.territoryID, t);
                }
            }

            public void OnDestroy()
            {
                Utils.DestroyObject(maskTexture);
                maskTexture = null;
            }

            public void SetTerritoryColor(int territoryIndex, Color color)
            {
                int x = territoryIndex % maskTexture.width;
                int y = territoryIndex / maskTexture.width;
                maskTexture.SetPixel(x, y, color);
                maskTexture.Apply();
            }

            public void SetEdgeMaskPxiel(int edgeIndex, float value)
            {
                int x = edgeIndex % maskTexture.width;
                int y = edgeIndex / maskTexture.width;
                var color = maskTexture.GetPixel(x, y);
                color.r = value;
                maskTexture.SetPixel(x, y, color);
                maskTexture.Apply();
            }

            public void SetEdgeAlpha(int edgeIndex, float alpha)
            {
                int x = edgeIndex % maskTexture.width;
                int y = edgeIndex / maskTexture.width;
                var color = maskTexture.GetPixel(x, y);
                color.a = alpha;
                maskTexture.SetPixel(x, y, color);
                maskTexture.Apply();
            }

            public CityTerritoryEdgeData GetEdge(int territoryID, int neighbourTerritoryID)
            {
                for (int i = 0; i < edges.Length; ++i)
                {
                    if (edges[i].territoryID == territoryID &&
                        edges[i].neighbourTerritoryID == neighbourTerritoryID)
                    {
                        return edges[i];
                    }
                }
                return null;
            }

            public CityTerritoryData GetTerritory(int territoryID)
            {
                territoriesByID.TryGetValue(territoryID, out CityTerritoryData t);
                return t;
            }

            public int horizontalTileCount;
            public int verticalTileCount;
            public float tileWidth;
            public float tileHeight;
            public CityTerritoryData[] territories;
            public Dictionary<int, CityTerritoryData> territoriesByID;
            public CityTerritoryEdgeData[] edges;
            public List<CityTerritoryDataBlock> blocks;
            public short[,] grids;
            public bool isActive;
            public Texture2D maskTexture;

            public Vector2Int FromWorldPositionToCoordinate(Vector3 pos)
            {
                return new Vector2Int(Mathf.FloorToInt(pos.x / tileWidth), Mathf.FloorToInt(pos.z / tileHeight));
            }
        }

        public CityTerritoryLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, List<SubLayerData> subLayers) : base(header, config, map, null, null)
        {
            mSubLayers = subLayers;
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            for (int i = 0; i < mSubLayers.Count; ++i)
            {
                mSubLayers[i].OnDestroy();
            }
        }

        public int GetTerritoryID(int x, int y)
        {
            return GetTerritoryID(0, x, y);
        }

        public int GetTerritoryID(int subLayerIdx, int x, int y)
        {
            var subLayer = GetSubLayer(subLayerIdx);
            if (subLayer != null)
            {
                if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                {
                    short idx = subLayer.grids[y, x];
                    if (idx >= 0)
                    {
                        return subLayer.territories[idx].territoryID;
                    }
                }
            }
            return 0;
        }

        public int GetTerritoryID(Vector3 pos)
        {
            return GetTerritoryID(0, pos);
        }

        public int GetTerritoryID(int subLayerIdx, Vector3 pos)
        {
            var subLayer = GetSubLayer(subLayerIdx);
            if (subLayer != null)
            {
                var coord = subLayer.FromWorldPositionToCoordinate(pos);
                if (coord.x >= 0 && coord.x < mCols && coord.y >= 0 && coord.y < mRows)
                {
                    short idx = subLayer.grids[coord.y, coord.x];
                    if (idx >= 0)
                    {
                        return subLayer.territories[idx].territoryID;
                    }
                }
            }
            return 0;
        }

        CityTerritoryData GetTerritory(int territoryID)
        {
            if (territoryID == 0)
            {
                return null;
            }

            for (int i = 0; i < mSubLayers.Count; ++i)
            {
                var subLayer = mSubLayers[i];
                var t = subLayer.GetTerritory(territoryID);
                if (t != null)
                {
                    return t;
                }
            }
            return null;
        }

        public void Hide(CityTerritoryDataBase data)
        {
            data.isHidden = true;
            SetObjectActive(data, false, currentLOD);
            data.SetAlpha(0);
        }

        public void Show(CityTerritoryDataBase data)
        {
            data.isHidden = false;
            SetObjectActive(data, true, currentLOD);
            data.SetAlpha(1);
        }

        //显示区域
        public bool ShowTerritory(int territoryID)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                Show(t);
                return true;
            }
            return false;
        }

        //隐藏区域
        public bool HideTerritory(int territoryID)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                Hide(t);
                return true;
            }
            return false;
        }

        //隐藏区域边界线
        public bool HideTerritoryEdge(int territoryID)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                foreach (var edge in t.edges)
                {
                    var neighbour = GetTerritory(edge.neighbourTerritoryID);
                    if (neighbour == null || neighbour.isHidden)
                    {
                        Hide(edge);
                        if (neighbour != null)
                        {
                            var neighbourEdge = GetEdge(neighbour.territoryID, territoryID);
                            Hide(neighbourEdge);
                        }
                    }
                }
                return true;
            }
            return false;
        }

        //显示区域边界线
        public bool ShowTerritoryEdge(int territoryID)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                foreach (var edge in t.edges)
                {
                    Show(edge);
                    var neighbour = GetTerritory(edge.neighbourTerritoryID);
                    if (neighbour != null)
                    {
                        var neighbourEdge = GetEdge(neighbour.territoryID, territoryID);
                        Show(neighbourEdge);
                    }                    
                }
                return true;
            }
            return false;
        }

        public Vector3 GetCityPosition(int territoryID)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                return t.cityPosition;
            }
            
            Debug.LogError($"territory not found with {territoryID}");
            return Vector3.zero;
        }

        public bool SetTerritoryColor(int territoryID, Color color)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                t.SetColor(color);
                return true;
            }
            return false;
        }

        public bool SetTerritoryAlpha(int territoryID, float alpha)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                t.SetAlpha(alpha);
                return true;
            }
            return false;
        }

        public Color GetTerritoryColor(int territoryID)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                return t.color;
            }
            return Color.white;
        }

        public SubLayerData GetSubLayer(int idx)
        {
            if (idx >= 0 && idx < mSubLayers.Count)
            {
                return mSubLayers[idx];
            }
            return null;
        }

        public void RefreshSubLayerDataVisibility(int subLayerIdx)
        { 
            var subLayer = GetSubLayer(subLayerIdx);
            foreach (var p in mObjects)
            {
                var obj = p.Value as CityTerritoryDataBase;
                if (obj.subLayer == subLayer)
                {
                    ForceUpdateObjectVisibility(obj, currentLOD);
                }
            }
        }

        public CityTerritoryEdgeData GetEdge(int territoryID, int neighbourTerritoryID)
        {
            var territory = GetTerritory(territoryID);
            if (territory != null)
            {
                return territory.GetNeighbourEdge(neighbourTerritoryID);
            }
            return null;
        }

        public CityTerritoryDataBlock GetOwnerBlock(int dataID)
        {
            var obj = GetObjectData(dataID) as CityTerritoryDataBase;
            Debug.Assert(obj != null);
            return obj.ownerBlock;
        }

        public IEnumerable<CityTerritoryEdgeData> GetEdges(int territoryID)
        {
            var t = GetTerritory(territoryID);
            if (t != null)
            {
                return t.edges;
            }
            return null;
        }

        public IEnumerable<CityTerritoryEdgeData> GetAllEdges(int layer)
        {
            if (layer >= 0 && layer < mSubLayers.Count)
            {
                return mSubLayers[layer].edges;
            }
            return null;
        }

        public IEnumerable<CityTerritoryData> GetAllTerritories(int layer)
        {
            if (layer >= 0 && layer < mSubLayers.Count)
            {
                return mSubLayers[layer].territories;
            }
            return null;
        }

        List<SubLayerData> mSubLayers = new List<SubLayerData>();
    }
}
