﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class BakeMaterialManager
    {
        //设置烘培前的mtl使用哪个烘培后的shader
        public void SetMaterialShader(Material mtl, Shader shader)
        {
            var assetPath = AssetDatabase.GetAssetPath(mtl);
            var assetGuid = AssetDatabase.AssetPathToGUID(assetPath);

            mProcessingMaterials[assetGuid] = shader;
        }

        public Shader GetTargetShader(Material mtl)
        {
            var assetPath = AssetDatabase.GetAssetPath(mtl);
            var assetGuid = AssetDatabase.AssetPathToGUID(assetPath);

            Shader shader;
            mProcessingMaterials.TryGetValue(assetGuid, out shader);
            return shader;
        }

        public Dictionary<string, Shader> processingMaterials { get { return mProcessingMaterials; } }

        //bake时待处理的材质,相同的材质bake后使用相同的shader
        Dictionary<string, Shader> mProcessingMaterials = new Dictionary<string, Shader>();
    }
}

#endif