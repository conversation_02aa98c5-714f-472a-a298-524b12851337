﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplineEditorUI : UnityEditor.Editor
    {
        void HandleEditMeshFunction(Event e, Vector3 worldPos)
        {
            if (mSelectedSplineObjectID > 0)
            {
                if (e.button == 0 && (e.type == EventType.MouseDown || e.type == EventType.MouseDrag))
                {
                    if (e.type == EventType.MouseDown)
                    {
                        PickVertex(worldPos);
                    }

                    MoveVertex(worldPos);
                    Repaint();
                    SceneView.RepaintAll();
                }
            }
            HandleUtility.AddDefaultControl(0);
        }

        public void DrawEditMeshFunctionSceneGUI()
        {
        }

        public void DrawEditMeshFunctionInspectorGUI()
        {
        }

        void PickVertex(Vector3 worldPos)
        {
            mSelectedVertexIndex = -1;
            mMover.Reset();
            float pickRadius2 = 0.5f * 0.5f;
            var spline = mEditor.splineObjectManager.FindSplineObject(mSelectedSplineObjectID);
            var meshVertices = spline.meshVertices;
            for (int i = 0; i < meshVertices.Count; ++i)
            {
                var d = worldPos - meshVertices[i];
                if (d.sqrMagnitude <= pickRadius2)
                {
                    mSelectedVertexIndex = i;
                    break;
                }
            }
        }

        void MoveVertex(Vector3 worldPos)
        {
            if (mSelectedVertexIndex >= 0)
            {
                mMover.Update(worldPos);

                var delta = mMover.GetDelta();
                var spline = mEditor.splineObjectManager.FindSplineObject(mSelectedSplineObjectID);
                spline.MoveVertex(mSelectedVertexIndex, delta);
            }
        }

        int mSelectedVertexIndex = -1;
        MouseMover mMover;
    }
}

#endif