﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveComplexGridModelLayer(BinaryWriter writer, EditorComplexGridModelLayer layer, int version, MapDataSectionType section)
        {
            //convert to grid model layer 2!
            BeginSection(section, writer);

            writer.Write(version);

            //--------------------version 1 start--------------------------
            bool useLayer = layer != null;
            if (useLayer)
            {
                bool exportData = layer.layerData.exportData;
                if (!exportData)
                {
                    useLayer = false;
                }
            }
            writer.Write(useLayer);
            if (!useLayer)
            {
                return;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            int cols = layer.horizontalTileCount;
            int rows = layer.verticalTileCount;

            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);
            Utils.WriteRect(writer, layer.layerData.realLayerBounds);

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    bool tileIsNotEmpty = layer.CheckIfPrefabIsNeededForThisTile(j, i);
                    writer.Write(tileIsNotEmpty);
                }
            }

            //save map layer lod config
            SaveComplextGridModelLayerLODConfigV1(writer, layer.layerData);
            //--------------------version 1 end--------------------------
            writer.Write(layer.layerData.enableObjectMaterialChange);
        }

        void SaveComplextGridModelLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
                writer.Write(c.useRenderTexture);
            }
        }
    }
}

#endif