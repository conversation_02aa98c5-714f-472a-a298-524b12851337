﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RuinData : ModelData
    {
        public RuinData(int id, int layerID, Map map, int flag, Vector3 position, Quaternion rotation, Vector3 scale, ModelTemplate modelTemplate, RuinType type, int level, string objectType, Color color, PropertyDatas properties)
            : base(id, map, flag, position, rotation, scale, modelTemplate, true)
        {
            mType = type;
            mLayerID = layerID;
            mLevel = level;
            mObjectType = objectType;
            mColor = color;
            mProperties = properties;
        }

        public RuinType type { get { return mType; } }
        public int level { get { return mLevel; } }
        public string objectType { get { return mObjectType; } }
        public Color color { set { mColor = value; } get { return mColor; } }
        public PropertyDatas properties { get { return mProperties; } }
        public int layerID { get { return mLayerID; } }

        public float radius
        {
            get
            {
                var layer = map.GetMapLayerByID(mLayerID) as RuinLayer;
                var ruinSetting = layer.GetRuinSetting(mObjectType);
                return ruinSetting.colliderRadius;
            }
        }

        RuinType mType;
        int mLevel;
        int mLayerID;
        string mObjectType;
        Color mColor;
        PropertyDatas mProperties;
    }
}


#endif