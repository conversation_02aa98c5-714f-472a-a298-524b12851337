﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveVaryingTileSizeTerrainPrefabManager(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.VaryingTileSizeTerrainPrefabManager, writer);

            writer.Write(VersionSetting.VaryingTileSizeTerrainPrefabManagerStructVersion);

            //-------------------version 1 start------------------------------
            var map = Map.currentMap;
            var prefabManager = map.data.varyingTileSizeTerrainPrefabManager;

            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                writer.Write(group.groupID);
                int nPrefabs = group.prefabCount;
                writer.Write(nPrefabs);
                for (int k = 0; k < nPrefabs; ++k)
                {
                    var prefabPath = group.GetPrefabPath(k);
                    var size = group.GetPrefabSize(k);
                    Utils.WriteString(writer, prefabPath);
                    Utils.WriteVector2Int(writer, size);
                }
            }
            //-------------------version 1 end------------------------------
        }
    }
}

#endif