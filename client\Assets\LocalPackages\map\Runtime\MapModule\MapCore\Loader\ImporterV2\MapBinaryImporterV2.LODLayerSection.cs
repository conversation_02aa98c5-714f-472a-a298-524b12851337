﻿ 



 
 


using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadLODLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.LODLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            //load layer info
            float layerWidth = reader.ReadSingle();
            float layerHeight = reader.ReadSingle();

            var config = LoadLODLayerLODConfigV1(reader);
            //---------------------version 1 end------------------------
            //-------------------version 2 start-----------------------
            if (version >= 2)
            {
                LoadLODLayerLODConfigV2(reader, config);
            }
            //-------------------version 2 end-----------------------

            var layer = new config.LODLayerData(layerID, name, offset, config, layerWidth, layerHeight);
            return layer;
        }

        config.MapLayerLODConfig LoadLODLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }

        void LoadLODLayerLODConfigV2(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].useRenderTexture = reader.ReadBoolean();
            }
        }
    }
}
