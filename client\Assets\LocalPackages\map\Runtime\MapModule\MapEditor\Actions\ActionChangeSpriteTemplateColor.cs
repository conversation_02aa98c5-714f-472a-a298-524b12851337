﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.11
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map {
    //改变sprite template颜色的命令
    [Black]
    public class ActionChangeSpriteTemplateColor : EditorAction {
        public ActionChangeSpriteTemplateColor(int spriteTemplateID, Color32 newColor) {
            var map = Map.currentMap;

            mSpriteTemplateID = spriteTemplateID;
            var spriteTemplate = map.FindObject(mSpriteTemplateID) as SpriteTemplate;
            mOldColor = spriteTemplate.color;
            mNewColor = newColor;

            int n = map.GetMapLayerCount();
            for (int i = 0; i < n; ++i) {
                var layerData = map.GetMapLayerByIndex(i);
                if (layerData is SpriteTileLayer) {
                    AddCoordinatesInTiles(layerData as SpriteTileLayer);
                }
            }

            mDescription = string.Format("change {0} color", spriteTemplate.name);
        }

        public override bool Do() {
            var map = Map.currentMap;
            //改变引用了该sprite template的所有tile的颜色
            foreach (var p in mSpriteTemplateTiles) {
                var layerData = map.FindObject(p.Key) as SpriteTileLayerData;
                
                var layer = Map.currentMap.GetMapLayerByID(layerData.id) as SpriteTileLayer;
                layer.ChangeMaterialColor(mOldColor, mNewColor);
            }

            var spriteTemplate = map.FindObject(mSpriteTemplateID) as SpriteTemplate;
            spriteTemplate.color = mNewColor;

            return true;
        }

        public override bool Undo() {
            Debug.Assert(false, "todo");
            return true;
        }

        void AddCoordinatesInTiles(SpriteTileLayer layer) {
            Vector2Int firstCoord = Vector2Int.zero;
            bool found = false;
            var layerData = layer.layerData;
            int rows = layerData.verticalTileCount;
            int cols = layerData.horizontalTileCount;
            for (int i = 0; i < rows; ++i) {
                for (int j = 0; j < cols; ++j) {
                    var tile = layerData.GetTile(j, i) as SpriteTileData;
                    if (tile != null && tile.spriteTemplate != null && tile.spriteTemplate.id == mSpriteTemplateID) {
                        //只收集每层一个使用了该sprite template的tile
                        firstCoord = new Vector2Int(j, i);
                        found = true;
                        break;
                    }
                }
            }

            if (found) {
                mSpriteTemplateTiles[layerData.id] = firstCoord;
            }
        }

        public override string description {
            get {
                return mDescription;
            }
        }

        int mSpriteTemplateID;
        Color32 mOldColor;
        Color32 mNewColor;
        string mDescription;
        Dictionary<int, Vector2Int> mSpriteTemplateTiles = new Dictionary<int, Vector2Int>();
    }
}

#endif