﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

namespace TFW.Map
{
    public static partial class MapManager
    {
        //public class MapSetting
        //{
        //    public int id;
        //    public string mapName;
        //    //同时只能存在一个main map,main map一般就是世界地图
        //    public bool isMainMap = false;
        //    public string dataFileFolder;
        //    public Vector3 origin;
        //    public string defaultCameraSetting;
        //    public MapCameraSetting cameraSetting;
        //    public bool loadAtStartup;
        //    public bool clampBorder;
        //    public bool updateNearClipPlane;
        //    public bool updateFarClipPlane;
        //    public bool isPlaceholderMap;
        //    public CameraClipPlaneSetting nearClipPlaneSetting;
        //    public CameraClipPlaneSetting farClipPlaneSetting;
        //}

        //public class MapSettings
        //{
        //    public List<MapSetting> maps = new List<MapSetting>();
        //    public string activeMapName = "";
        //}
        /// <summary>
        /// 所有地图配置信息
        /// </summary>
        [System.Serializable]
        public class MapSettings
        {
            /// <summary>
            /// 地图配置列表
            /// </summary>
            public MapSetting[] maps = new MapSetting[0];
            public string activeMapName = "";
        }

        /// <summary>
        /// 地图设置信息
        /// </summary>
        [System.Serializable]
        public class MapSetting
        {

            /// <summary>
            /// 地图Id
            /// </summary>
            public int id;
            /// <summary>
            /// 同时只能存在一个main map,main map一般就是世界地图
            /// </summary>
            public bool isMainMap = false;
            public MapCameraSetting cameraSetting;
            public bool clampBorder;
            //public bool updateNearClipPlane;
            //public bool updateFarClipPlane;
            /// <summary>
            /// 是否为占位地图？？
            /// </summary>
            //public bool isPlaceholderMap;
            //public CameraClipPlaneSetting nearClipPlaneSetting;
            //public CameraClipPlaneSetting farClipPlaneSetting;


            /// <summary>
            /// 地图名字
            /// </summary>
            public string name;
            /// <summary>
            /// 地图配置目录
            /// </summary>
            public string file_folder;
            /// <summary>
            /// 原点坐标
            /// </summary>
            public Vector3 origin;
            /// <summary>
            /// 默认相机配置
            /// </summary>
            public string default_camera_setting;
            /// <summary>
            /// 是否在启动加载
            /// </summary>
            public bool load_at_startup;
            //public bool clampBorder;
            //public bool updateClipPlane;
            /// <summary>
            /// 地图大小
            /// </summary>
            public Vector2 map_size;
            /// <summary>
            /// 地图中心点
            /// </summary>
            public Vector2 map_center_pos;

            /// <summary>
            /// 方向光旋转
            /// </summary>
            public Vector3 directional_light_rot;
            /// <summary>
            /// 方向光颜色
            /// </summary>
            public Color directional_light_color;
            /// <summary>
            /// 方向光强度
            /// </summary>
            public float directional_light_intensity;

            /// <summary>
            /// 环境光照类型
            /// </summary>
            public int environment_light_source_type;
            /// <summary>
            /// 环境光颜色
            /// </summary>
            public Color environment_light_color;
            /// <summary>
            /// 环境光强度
            /// </summary>
            public float environment_light_intensity;
        }


        /// <summary>
        /// 初始化地图配置
        /// </summary>
        /// 
        private static void InitMapSettings()
        {
                LoadSetting(MapModule.mapListDir + "/" + MapCoreDef.MAP_LIST_FILE_PATH);
        }

        private static async UniTask InitMapSettingsAsync()
        {
            await LoadSettingAsync(MapModule.mapListDir + "/" + MapCoreDef.MAP_LIST_FILE_PATH);
        }



        /// <summary>
        /// 读取地图列表信息
        /// </summary>
        /// <param name="filePath"></param>
        static void LoadSetting(string filePath)
        {
            //UnityEngine.Debug.LogError(" filePath " + filePath);
            var bytes = MapModuleResourceMgr.LoadTextBytes(filePath, true);
            if (bytes != null)
            {
                //string configData = System.Text.Encoding.UTF8.GetString(bytes);
                //var configDict = JSONParser.Deserialize(configData) as Dictionary<string, object>;
                //var maps = configDict["maps"] as List<object>;

                //mMapSettings = new MapSettings();
                //mMapSettings.maps = new List<MapSetting>(maps.Count);

                //object activeMapName;
                //bool found = configDict.TryGetValue("active_map", out activeMapName);
                //if (found)
                //{
                //    mMapSettings.activeMapName = activeMapName as string;
                //}

                //for (int i = 0; i < maps.Count; ++i)
                //{
                //    var mapSetting = maps[i] as Dictionary<string, object>;
                //    int id = JsonHelper.ReadInt(mapSetting, "id", i + 1);
                //    string name = JsonHelper.ReadString(mapSetting, "name", "");
                //    Debug.Assert(!string.IsNullOrEmpty(name));
                //    string mapDataFolder = JsonHelper.ReadString(mapSetting, "file_folder", "");
                //    Debug.Assert(!string.IsNullOrEmpty(mapDataFolder));
                //    bool loadAtStartup = JsonHelper.ReadBoolean(mapSetting, "load_at_startup", true);
                //    bool isPlaceholderMap = JsonHelper.ReadBoolean(mapSetting, "is_placeholder_map", false);
                //    string defaultCameraSetting = JsonHelper.ReadString(mapSetting, "default_camera_setting", "");
                //    string nearClipPlaneSettingPath = JsonHelper.ReadString(mapSetting, MapCoreDef.MAP_CAMERA_NEAR_CLIP_PLANE_SETTING_PATH, "");
                //    string farClipPlaneSettingPath = JsonHelper.ReadString(mapSetting, MapCoreDef.MAP_CAMERA_FAR_CLIP_PLANE_SETTING_PATH, "");
                //    Debug.Assert(!string.IsNullOrEmpty(defaultCameraSetting));
                //    Vector3 origin = JsonHelper.ReadVector3(mapSetting, "origin", Vector3.zero);
                //    bool clampBorder = JsonHelper.ReadBoolean(mapSetting, MapCoreDef.MAP_CAMERA_CLAMP_BORDER_NAME, MapModule.cameraClampBorder);
                //    bool updateFarClipPlane = JsonHelper.ReadBoolean(mapSetting, MapCoreDef.MAP_ENABLE_UPDATE_CLIP_PLANE, MapModule.enableUpdateFarClipPlane);
                //    bool updateNearClipPlane = JsonHelper.ReadBoolean(mapSetting, MapCoreDef.MAP_ENABLE_UPDATE_NEAR_CLIP_PLANE, MapModule.enableUpdateNearClipPlane);
                //    bool isMainMap = JsonHelper.ReadBoolean(mapSetting, MapCoreDef.MAP_SETTING_IS_MAIN_MAP, false);
                //    var setting = new MapSetting()
                //    {
                //        id = id,
                //        mapName = name,
                //        isPlaceholderMap = isPlaceholderMap,
                //        isMainMap = isMainMap,
                //        dataFileFolder = mapDataFolder,
                //        origin = origin,
                //        loadAtStartup = loadAtStartup,
                //        clampBorder = clampBorder,
                //        defaultCameraSetting = defaultCameraSetting,
                //        updateFarClipPlane = updateFarClipPlane,
                //        updateNearClipPlane = updateNearClipPlane,
                //        nearClipPlaneSetting = string.IsNullOrEmpty(nearClipPlaneSettingPath) ? null : MapModuleResourceMgr.LoadResource<CameraClipPlaneSetting>(nearClipPlaneSettingPath),
                //        farClipPlaneSetting = string.IsNullOrEmpty(farClipPlaneSettingPath) ? null : MapModuleResourceMgr.LoadResource<CameraClipPlaneSetting>(farClipPlaneSettingPath),
                //    };
                //    if (GetSetting(name) != null)
                //    {
                //        Debug.Assert(false, "map setting already exists!");
                //    }
                //    mMapSettings.maps.Add(setting);
                //}

                //json 获取配置数据
                string configData = System.Text.Encoding.UTF8.GetString(bytes);
                mMapSettings = JsonUtility.FromJson<MapSettings>(configData);
            }
            else
            {
                Debug.Assert(false, "Can't find map list file");
            }
        }
        static async UniTask LoadSettingAsync(string filePath)
        {
            var configData = await MapModuleResourceMgr.LoadTextStringAsync(filePath);
            {
                if (!string.IsNullOrEmpty(configData))
                {
                    //json 获取配置数据
                    mMapSettings = JsonUtility.FromJson<MapSettings>(configData);
                    //mMapSettings.activeMapName = activeMapName as string
                }
                else
                {
                    Debug.Assert(false, "Can't find map list file");
                    UnityEngine.Debug.Log($"[{nameof(MapManager)}] MapLog cfg LoadSetting Can't find map list file");
                }
            }
        }

        /// <summary>
        /// 获取地图配置信息
        /// </summary>
        /// <param name="mapName">地图名字</param>
        /// <param name="isNotLoad">是否数据不存在时，那么加载相应数据</param>
        /// <returns></returns>
        public static MapSetting GetSetting(string mapName, bool isNotLoad = false)
        {
            if (mMapSettings.maps.Length == 0
                && isNotLoad)
            {
                InitMapSettings();
            }

            for (int i = 0; i < mMapSettings.maps.Length; ++i)
            {
                if (mMapSettings.maps[i].name == mapName)
                {
                    return mMapSettings.maps[i];
                }
            }
            return null;
        }

        public static MapSetting GetSetting(int id)
        {
            for (int i = 0; i < mMapSettings.maps.Length; ++i)
            {
                if (mMapSettings.maps[i].id == id)
                {
                    return mMapSettings.maps[i];
                }
            }
            return null;
        }
        public static async UniTask<MapSetting> GetSettingAsync(string mapName, bool isNotLoad = false)
        {
            if (mMapSettings.maps.Length == 0 || isNotLoad)
            {
                await InitMapSettingsAsync();
                {
                    for (int i = 0; i < mMapSettings.maps.Length; ++i)
                    {
                        if (string.Equals(mMapSettings.maps[i].name, mapName))
                        {
                           return mMapSettings.maps[i];
                        }
                    }
                }
            }
            else
            {
                for (int i = 0; i < mMapSettings.maps.Length; ++i)
                {
                    if (string.Equals(mMapSettings.maps[i].name, mapName))
                    {
                       return mMapSettings.maps[i];
                    }
                }
            }

            UnityEngine.Debug.LogError($"GetMapSetting NUll {mapName} ");
            return mMapSettings.maps[0];

            
            return null; 
        }

        public static void SetMapActive(string mapName, bool active)
        {
            var map = FindMap(mapName);
            if (map != null)
            {
                map.SetActive(active);
            }
        }

//        public static void SetCurrentMap(string mapName, Vector3 cameraPos)
//        {
//#if !UNITY_WEBGL
//            SetCurrentMap(mapName);
//            MapCameraMgr.MoveCameraToTargetInstantly(cameraPos, null);
//#else
//            SetCurrentMapAsync(mapName, (result) =>
//            {
//                MapCameraMgr.MoveCameraToTargetInstantly(cameraPos, null);
//            });
//#endif
//        }

        //public static void SetCurrentMap(string mapName)
        //{
        //    var map = FindMap(mapName);
        //    if (map != null)
        //    {
        //        Map.currentMap = map;
        //        map.SetActive(true);

        //        var mapSetting = GetSetting(mapName);
        //        ApplyMapSetting(mapSetting);

        //        if (mapSetting.isMainMap)
        //        {
        //            //check if other main map exists!
        //            foreach (var p in mMaps)
        //            {
        //                var s = GetSetting(p.Key);
        //                if (s.isMainMap)
        //                {
        //                    Debug.LogError($"{p.Key} is also main map and loaded! can only load one main map at the same time!");
        //                }
        //            }

        //            mMainMap = map;
        //        }
        //    }
        //}

        //public static async UniTask SetCurrentMapAsync(string mapName, System.Action<bool> action)
        //{
        //    var map = FindMap(mapName);
        //    if (map != null)
        //    {
        //        Map.currentMap = map;
        //        map.SetActive(true);

        //        var mapSetting = await GetSettingAsync(mapName, false);
        //        if(mapSetting!=null)
        //        { 
        //            ApplyMapSetting(mapSetting);

        //            if (mapSetting.isMainMap)
        //            {
        //                //check if other main map exists!
        //                foreach (var p in mMaps)
        //                {
        //                    var s = GetSetting(p.Key);
        //                    if (s.isMainMap)
        //                    {
        //                        Debug.LogError($"{p.Key} is also main map and loaded! can only load one main map at the same time!");
        //                    }
        //                }

        //                mMainMap = map;
        //            }
                   
        //        } 
        //    }
        //}

        public static List<string> GetCameraSettingFilePaths()
        {
            List<string> paths = new List<string>();
            foreach (var setting in mMapSettings.maps)
            {
                if (!string.IsNullOrEmpty(setting.default_camera_setting) 
                    && !paths.Contains(setting.default_camera_setting))
                {
                    paths.Add(setting.default_camera_setting);
                }
            }
            return paths;
        }

        /// <summary>
        /// 应用地图相关设置
        /// </summary>
        /// <param name="mapSetting"></param>
        static void ApplyMapSetting(MapSetting mapSetting)
        {
            MapCameraMgr.SetActiveCameraSetting(mapSetting.default_camera_setting);
            MapCameraMgr.clampBorder = mapSetting.clampBorder;
            //MapCameraMgr.enableUpdateFarClipPlane = mapSetting.updateFarClipPlane;
            //MapCameraMgr.enableUpdateNearClipPlane = mapSetting.updateNearClipPlane;
            //MapCameraMgr.nearClipPlaneSetting = mapSetting.nearClipPlaneSetting;
            //MapCameraMgr.farClipPlaneSetting = mapSetting.farClipPlaneSetting;

            //应用地图光照数据
            ApplyMapLight(mapSetting);
        }

        /// <summary>
        /// 应用大地图的光照信息
        /// </summary>
        /// <param name="mapSetting"></param>
        static void ApplyMapLight(MapSetting mapSetting)
        {
            if (mapSetting == null)
                return;

            var obj = GameObject.FindGameObjectWithTag("light");
            if(obj != null)
            {
                var light = obj.GetComponent<Light>();
                if(light != null)
                {
                    light.color = mapSetting.directional_light_color;
                    light.intensity = mapSetting.directional_light_intensity;
                    light.transform.rotation = Quaternion.Euler(mapSetting.directional_light_rot);
                }
            }
        }

        public static Map mainMap { get { return mMainMap; } }

        static Dictionary<string, Map> mMaps = new Dictionary<string, Map>();
        static MapSettings mMapSettings = new MapSettings();
        static Map mMainMap;
    }
}
