﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //相机水平追赶并跟随目标点
    public class CameraFollowTarget : CameraAction
    {
        public CameraFollowTarget(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mCurrentPos = currentCameraPos;
        }

        public void StartFollow(float x, float z, float catchDuration, float fixedCatchSpeed = 0)
        {
            mCurrentPos = MapCameraMgr.updatedCameraPosition;
            StartFollow(x, 0, z, catchDuration, mCurrentPos.y, fixedCatchSpeed);
        }

        public void StartFollow(float x, float y, float z, float catchDuration, float lookAtHeight, float fixedCatchSpeed = 0)
        {
            Cancel();

            mIsFollowing = false;
            //current camera pos may not equal to cameraTransform.position!
            mCurrentPos = MapCameraMgr.updatedCameraPosition;
            mLookAtHeight = lookAtHeight;
            mEndPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(x, z, mLookAtHeight, y);
            if (fixedCatchSpeed != 0)
            {
                if (catchDuration > 0)
                {
                    float distance = (mEndPos - mCurrentPos).magnitude;
                    mCatchSpeed = distance / catchDuration;
                }
                else
                {
                    mCatchSpeed = 0;
                }
            }
            else
            {
                mCatchSpeed = fixedCatchSpeed;
            }
            enabled = true;
        }

        public void UpdateTarget(float x, float z)
        {
            UpdateTarget(x, 0, z, mLookAtHeight);
        }

        public void UpdateTarget(float x, float y, float z, float lookAtHeight)
        {
            if (enabled)
            {
                if (lookAtHeight != 0)
                {
                    mLookAtHeight = lookAtHeight;
                }
                mEndPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(x, z, mLookAtHeight, y);
            }
        }

        public void Cancel()
        {
            mIsFollowing = false;
            enabled = false;
            mCatchSpeed = 0;
        }

        public override void OnFinishImpl()
        {
            Cancel();
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            if (mIsFollowing || mCatchSpeed == 0)
            {
                return mEndPos;
            }
            else
            {
                float maxDistance = Time.deltaTime * mCatchSpeed;
                mCurrentPos = Vector3.MoveTowards(mCurrentPos, mEndPos, maxDistance);
                if (mCurrentPos == mEndPos)
                {
                    mIsFollowing = true;
                }

                return mCurrentPos;
            }
        }

        //相机的起始位置
        Vector3 mCurrentPos;
        //相机最终的目标位置
        Vector3 mEndPos;
        float mCatchSpeed;
        float mLookAtHeight;
        bool mIsFollowing = false;
    }
}