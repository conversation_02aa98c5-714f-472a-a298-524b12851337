﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public class RiverData : ModelData
    {
        public RiverData(int id, Map map, int flag, Vector3 position, Quaternion rotation, Vector3 scale, ModelTemplate modelTemplate, bool calculateBounds, int hideLOD) : base(id, map, flag, position, rotation, scale, modelTemplate, calculateBounds)
        {
            mHideLOD = hideLOD;
        }

        public int hideLOD { get { return mHideLOD; } }

        int mHideLOD;
    }
}
