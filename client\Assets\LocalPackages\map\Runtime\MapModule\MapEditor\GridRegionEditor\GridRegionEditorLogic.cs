﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    class GridRegionEditorLogic : MonoBehaviour
    {
        public int brushSize = 1;
        public GridRegionOperation operation = GridRegionOperation.Create;
        public int selectedIndex = -1;

        public GridRegionEditor editor
        {
            get
            {
                return (Map.currentMap.data as EditorMapData).gridRegionEditor;
            }
        }
    }
}


#endif