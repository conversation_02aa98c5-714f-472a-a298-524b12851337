﻿ 



 
 


//created by wzw 2019.8.12

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class ConvexHullBuilder
    {
        public static List<Vector3> CreateGameObject2DConvexHullInXZPlane(List<GameObject> objs, bool worldSpace)
        {
            List<Vector3> vertices = new List<Vector3>();
            for (int i = 0; i < objs.Count; ++i)
            {
                var v = CalculateVertices(objs[i], worldSpace);
                vertices.AddRange(v);
            }

            return Create2DConvexHullInXZPlane(vertices);
        }

        public static List<Vector3> CreateGameObject2DConvexHullInXZPlane(GameObject obj, bool worldSpace)
        {
            return CreateGameObject2DConvexHullInXZPlane(new List<GameObject>() { obj }, worldSpace);
        }

        public static List<Vector3> CalculateVertices(GameObject obj, bool worldSpace)
        {
            //convex hull包含所有mesh的顶点
            var meshFilters = obj.GetComponentsInChildren<MeshFilter>();
            List<Vector3> vertices = new List<Vector3>();
            for (int i = 0; i < meshFilters.Length; ++i)
            {
                var worldSpaceVertices = ConvertToWorldSpacePosition(meshFilters[i], worldSpace);
                if (worldSpaceVertices != null)
                {
                    vertices.AddRange(worldSpaceVertices);
                }
            }

            //convex hull包含所有sprite renderer的顶点
            var spriteRenderers = obj.GetComponentsInChildren<SpriteRenderer>();
            for (int i = 0; i < spriteRenderers.Length; ++i)
            {
                if (worldSpace)
                {
                    var boundsVertices = GetBoundsVertices(spriteRenderers[i].bounds);
                    vertices.AddRange(boundsVertices);
                }
                else
                {
                    var boundsVertices = GetBoundsVertices(spriteRenderers[i].sprite.bounds);
                    vertices.AddRange(boundsVertices);
                }
            }

            return vertices;
        }

        //create a 2d convex hull from mesh in xz plane, vertices are in clock-wise winding order
        public static List<Vector3> Create2DConvexHullInXZPlane(List<Vector3> vertices)
        {
            vertices = RemoveDuplicatedVertices(vertices);

            mUpperHull.Clear();
            mLowerHull.Clear();

            SortPoints(vertices);
            if (vertices.Count > 2)
            {
                mUpperHull.Add(vertices[0]);
                mUpperHull.Add(vertices[1]);
                //creat upper hull
                for (int i = 2; i < vertices.Count; ++i)
                {
                    for (int j = mUpperHull.Count; j >= 2; --j)
                    {
                        bool isConvexTurn = IsConvexTurn(mUpperHull[j - 2], mUpperHull[j - 1], vertices[i]);
                        if (!isConvexTurn)
                        {
                            mUpperHull.RemoveAt(mUpperHull.Count - 1);
                        }
                        else
                        {
                            mUpperHull.Add(vertices[i]);
                            break;
                        }
                    }
                    if (mUpperHull.Count == 1)
                    {
                        mUpperHull.Add(vertices[i]);
                    }
                }

                //create lower hull, right most point is in upper hull
                int n = mUpperHull.Count;
                mLowerHull.Add(mUpperHull[n - 1]);
                mLowerHull.Add(mUpperHull[n - 2]);

                for (int i = vertices.Count - 2; i >= 0; --i)
                {
                    for (int j = mLowerHull.Count; j >= 2; --j)
                    {
                        if (vertices[i] != mLowerHull[j - 1])
                        {
                            bool isConvexTurn = IsConvexTurn(mLowerHull[j - 2], mLowerHull[j - 1], vertices[i]);
                            if (!isConvexTurn)
                            {
                                mLowerHull.RemoveAt(mLowerHull.Count - 1);
                            }
                            else
                            {
                                mLowerHull.Add(vertices[i]);
                                break;
                            }
                        }
                    }
                    if (mLowerHull.Count == 1)
                    {
                        mLowerHull.Add(vertices[i]);
                    }
                }
                return CreateConvexHullPolygon();
            }
            else
            {
                return vertices;
            }
        }

        static List<Vector3> ConvertToWorldSpacePosition(MeshFilter meshFilter, bool worldSpace)
        {
            var mesh = meshFilter.sharedMesh;
            if (mesh != null)
            {
                mMeshVertices.Clear();
                mesh.GetVertices(mMeshVertices);

                List<Vector3> result = new List<Vector3>();

                if (worldSpace)
                {
                    for (int i = 0; i < mMeshVertices.Count; ++i)
                    {
                        var worldSpacePos = meshFilter.transform.TransformPoint(mMeshVertices[i]);

                        result.Add(new Vector3(worldSpacePos.x, 0, worldSpacePos.z));
                    }
                }
                else
                {
                    result.AddRange(mMeshVertices);
                }

                return result;
            }
            return null;
        }

        static List<Vector3> GetBoundsVertices(Bounds bounds)
        {
            var min = bounds.min;
            var max = bounds.max;
            List<Vector3> outlineVertices = new List<Vector3> {
                        new Vector3(min.x, 0, min.z),
                        new Vector3(max.x, 0, min.z),
                        new Vector3(max.x, 0, max.z),
                        new Vector3(min.x, 0, max.z),
            };
            return outlineVertices;
        }

        static void SortPoints(List<Vector3> vertices)
        {
            System.Comparison<Vector3> func = (Vector3 a, Vector3 b) =>
            {
                if (!Mathf.Approximately(a.x, b.x))
                {
                    return a.x < b.x ? -1 : 1;
                }
                return a.z < b.z ? -1 : 1;
            };

            vertices.Sort(func);
        }

        static List<Vector3> CreateConvexHullPolygon()
        {
            List<Vector3> vertices = new List<Vector3>();
            int upperSize = mUpperHull.Count;
            int lowerSize = mLowerHull.Count;
            Debug.Assert(lowerSize >= 2);
            Debug.Assert(upperSize >= 2);
            for (int i = 0; i < upperSize; ++i)
            {
                vertices.Add(mUpperHull[i]);
            }
            for (int j = 1; j < lowerSize - 1; ++j)
            {
                vertices.Add(mLowerHull[j]);
            }
            return vertices;
        }

        static bool IsConvexTurn(Vector3 start, Vector3 end, Vector3 pos)
        {
            var d1 = end - start;
            var d2 = pos - end;
            var c = Vector3.Cross(d1.normalized, d2.normalized);
            if (Utils.Approximately(c.y, 0, 0.01f))
            {
                return false;
            }
            return Utils.LT(c.y, 0);
        }

        //删除重复的顶点
        static List<Vector3> RemoveDuplicatedVertices(List<Vector3> vertices)
        {
            List<Vector3> verts = new List<Vector3>();
            for (int i = 0; i < vertices.Count; ++i)
            {
                bool found = false;
                for (int j = 0; j < verts.Count; ++j)
                {
                    if ((vertices[i] - verts[j]).magnitude < 0.1f)
                    {
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    verts.Add(vertices[i]);
                }
            }

            return verts;
        }

        static List<Vector3> mUpperHull = new List<Vector3>();
        static List<Vector3> mLowerHull = new List<Vector3>();

        //temp variable
        static List<Vector3> mMeshVertices = new List<Vector3>();
    }
}