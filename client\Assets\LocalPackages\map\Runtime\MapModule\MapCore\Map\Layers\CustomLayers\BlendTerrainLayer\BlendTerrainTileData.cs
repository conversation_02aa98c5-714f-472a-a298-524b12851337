﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum TileUVRotation
    {
        None,
        Rotate90,
        Rotate180,
        Rotate270,
    }

    //拼接地表层上每个tile使用的数据
    public class BlendTerrainTileData : ModelData
    {
        //modelTemplateID: 地表tile使用的模型的配置id
        //index: 一个tile可以设置成15种拼接类型, index的范围是[1,15]
        //type: tile使用的prefab图集的类型,例如沙地图集,草地图集,雪地图集等
        //heights: 是否使用了高度图,null表示平地
        public BlendTerrainTileData(int id, Map map, Vector3 position, ModelTemplate modelTemplate, int index, int type, int subTypeIndex, float[] heights, bool hasHeightData) : base(id, map, 0, position, Quaternion.identity, Vector3.one, modelTemplate, false)
        {
            mIndex = index;
            mType = type;
            mSubTypeIndex = subTypeIndex;
            mHeights = heights;
            mHasHeightData = hasHeightData;
            if (heights != null)
            {
                mResolution = Mathf.FloorToInt(Mathf.Sqrt(mHeights.Length)) - 1;
            }
        }

        //直接设置这个tile的图集信息
        public void SetTile(int tileIndex, int tileType, int subTypeIndex)
        {
            mType = tileType;
            mIndex = tileIndex;
            mSubTypeIndex = subTypeIndex;
        }

        public int GetPushTileResult(int tileIndex, int tileType, bool changeRandomTile, List<int> excludedTileIndices, out int subTypeIndex)
        {
            subTypeIndex = 0;
            if (mType != tileType)
            {
                subTypeIndex = CalculateSubTypeIndex(tileType, tileIndex, map);
                return tileIndex;
            }
            else
            {
                //同类型的tile,拼起来
                int combinedIndex = mIndex;
                if (combinedIndex < 15)
                {
                    combinedIndex |= tileIndex;
                }

                if (changeRandomTile)
                {
                    var prefabManager = map.data.terrainPrefabManager;
                    if (prefabManager.onlyUse15Prefab)
                    {
                        subTypeIndex = CalculateSubTypeIndex(tileType, combinedIndex, map);
                    }
                    else
                    {
                        if (combinedIndex >= 15)
                        {
                            List<int> indices = prefabManager.GetPrefabValidFullTileIndicesByID(tileType);

                            List<int> validIndices = Utils.Substract(indices, excludedTileIndices);
                            if (validIndices.Count > 0)
                            {
                                combinedIndex = validIndices[Random.Range(0, validIndices.Count)];
                            }
                            else
                            {
                                combinedIndex = indices[Random.Range(0, indices.Count)];
                            }
                        }
                        else
                        {
                            subTypeIndex = CalculateSubTypeIndex(tileType, combinedIndex, map);
                        }
                    }
                }

                return combinedIndex;
            }
        }

        //和四周的tile拼接
        public void PushTile(int tileIndex, int tileType, bool changeRandomTile)
        {
            if (mType != tileType)
            {
                //不同类型的tile,直接替换成新的tile
                mType = tileType;
                mIndex = tileIndex;
                mSubTypeIndex = 0;
            }
            else
            {
                //同类型的tile,拼起来
                if (mIndex < 15)
                {
                    mIndex |= tileIndex;
                }

                if (changeRandomTile)
                {
                    var prefabManager = map.data.terrainPrefabManager;
                    if (prefabManager.onlyUse15Prefab)
                    {
                        mSubTypeIndex = CalculateSubTypeIndex(tileType, mIndex, map);
                    }
                    else
                    {
                        if (mIndex >= 15)
                        {
                            List<int> indices = prefabManager.GetPrefabValidFullTileIndicesByID(tileType);
                            mIndex = indices[Random.Range(0, indices.Count)];
                        }
                        else
                        {
                            mSubTypeIndex = CalculateSubTypeIndex(tileType, mIndex, map);
                        }
                    }
                }
            }
        }

        //反向拼接
        public void PopTile(int tileIndex)
        {
            if (mIndex > 15)
            {
                mIndex = 15;
            }
            mIndex &= ~tileIndex;
        }

        public float GetHeight(int x, int y)
        {
            if (mHeights == null)
            {
                return 0;
            }
            int idx = y * (resolution + 1) + x;
            return mHeights[idx];
        }

        public float GetHeightAtPos(float localPosX, float localPosZ, float gridSize)
        {
            if (mHeights == null)
            {
                return 0;
            }

            int x = (int)(localPosX / gridSize);
            int y = (int)(localPosZ / gridSize);
            localPosX = localPosX - x * gridSize;
            localPosZ = localPosZ - y * gridSize;
            float height0 = mHeights[y * (mResolution + 1) + x];
            float height2 = mHeights[(y + 1) * (mResolution + 1) + x + 1];
            if (localPosX > localPosZ)
            {
                float height3 = mHeights[y * (mResolution + 1) + x + 1];
                //lower triangle
                Vector3 bc = Utils.CalculateBaryCentricCoord(0, 0, gridSize, gridSize, gridSize, 0, localPosX, localPosZ);
                return bc.x * height0 + bc.y * height2 + bc.z * height3;
            }
            else
            {
                float height1 = mHeights[(y + 1) * (mResolution + 1) + x];
                //upper triangle
                Vector3 bc = Utils.CalculateBaryCentricCoord(0, 0, 0, gridSize, gridSize, gridSize, localPosX, localPosZ);
                return bc.x * height0 + bc.y * height1 + bc.z * height2;
            }
        }

        public void SetResolution(int resolution)
        {
#if UNITY_EDITOR
            if (resolution != mResolution)
            {
                if (resolution == 0)
                {
                    mHeights = null;
                    mResolution = 0;
                }
                else
                {
                    mHeights = new float[(resolution + 1) * (resolution + 1)];
                    mResolution = resolution;
                }
                mIsMeshDirty = true;
            }
#endif
        }

        public void SetHeight(int minX, int minY, int maxX, int maxY, int resolution, List<float> heights, float height, bool dontChangeEdgeVertexHeight)
        {
#if UNITY_EDITOR
            if (resolution != this.resolution)
            {
                if (resolution == 0)
                {
                    mHeights = null;
                    mResolution = 0;
                }
                else
                {
                    mHeights = new float[(resolution + 1) * (resolution + 1)];
                    mResolution = resolution;
                }
            }
            if (mHeights != null)
            {
                if (dontChangeEdgeVertexHeight)
                {
                    minY = Mathf.Max(1, minY);
                    minX = Mathf.Max(1, minX);
                    maxY = Mathf.Min(resolution - 1, maxY);
                    maxX = Mathf.Min(resolution - 1, maxX);
                }

                if (heights != null)
                {
                    int width = maxX - minX + 1;
                    for (int i = minY; i <= maxY; ++i)
                    {
                        for (int j = minX; j <= maxX; ++j)
                        {
                            int srcIdx = i * (resolution + 1) + j;
                            int dstIdx = (i - minY) * width + j - minX;
                            mHeights[srcIdx] = heights[dstIdx];
                        }
                    }
                }
                else
                {
                    for (int i = minY; i <= maxY; ++i)
                    {
                        for (int j = minX; j <= maxX; ++j)
                        {
                            int idx = i * (resolution + 1) + j;
                            mHeights[idx] = height;
                        }
                    }
                }
            }
            mIsMeshDirty = true;
#endif
        }

        public static int CalculateSubTypeIndex(int tileType, int tileIndex, Map map)
        {
            int subTypeIndex = 0;
            var prefabManager = map.data.terrainPrefabManager;
            List<int> indices = prefabManager.GetPrefabValidSubTypeIndicesByID(tileType, tileIndex);
            if (indices != null && indices.Count > 0)
            {
                subTypeIndex = indices[Random.Range(0, indices.Count)];
            }
            return subTypeIndex;
        }

        public int index { get { return mIndex; } }
        public int type { get { return mType; } }
        public int subTypeIndex { get { return mSubTypeIndex; } }
        public float[] heights
        {
            get { return mHeights; }
        }
        public int resolution
        {
            get
            {
                return mResolution;
            }
        }
        //运行时可能会把heights设置为null,但是本来tile又有height数据,所以用这个来标记
        public bool hasHeightData { get { return mHasHeightData; } }

#if UNITY_EDITOR
        public Vector2[] uvs
        {
            get { return mUVs; }
            set { mUVs = value; }
        }
        public bool meshDirty { set { mIsMeshDirty = value; } get { return mIsMeshDirty; } }
#endif

        int mType = -1;
        int mIndex = 0;
        int mSubTypeIndex;
        float[] mHeights;
#if UNITY_EDITOR
        //only used in editor
        Vector2[] mUVs;
#endif
        int mResolution;
        bool mHasHeightData = false;
#if UNITY_EDITOR
        bool mIsMeshDirty = true;
#endif
    }
}
