﻿ 



 
 



/*
 * created by wzw at 2019.12.12
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //这一层基于四叉树来管理地图的对象
    public partial class ComplexGridModelLayer : MapLayerBase
    {
        public ComplexGridModelLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.ComplexGridModelLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, sourceLayer.zTileCount, sourceLayer.xTileCount, sourceLayer.tileWidth, sourceLayer.tileHeight, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            mLocalViewCenter = sourceLayer.localViewCenter;
            mLocalViewSize = sourceLayer.localViewSize;

            float layerWidth = sourceLayer.xTileCount * sourceLayer.tileWidth;
            float layerHeight = sourceLayer.zTileCount * sourceLayer.tileHeight;
            if (mLocalViewCenter == Vector3.zero)
            {
                mLocalViewCenter = new Vector3(layerWidth * 0.5f, 0, layerHeight * 0.5f);
            }

            if (mLocalViewSize == Vector2.zero)
            {
                mLocalViewSize = new Vector2(layerWidth, layerHeight);
            }

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            List<ObjectTagSetting> objectTags = new List<ObjectTagSetting>();
            objectTags.AddRange(sourceLayer.objectTags);
            mLayerData = new ComplexGridObjectLayerData(this, header, config, map, sourceLayer.exportData, objectTags, sourceLayer.objectPlacementSetting, sourceLayer.realLayerBounds, sourceLayer.enableObjectMaterialChange, sourceLayer.enableCullIntersectedObjects);
            mLayerView = new ComplexGridObjectLayerView(mLayerData, true, mLocalViewCenter);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);

            mLayerData.isLoading = true;

            int nLODs = 1;
            if (sourceLayer.objectsInLODs != null)
            {
                nLODs = sourceLayer.objectsInLODs.Count;
                for (int i = 0; i < nLODs; ++i)
                {
                    var objList = sourceLayer.objectsInLODs[i];
                    int nObjects = objList.Length;
                    for (int k = 0; k < nObjects; ++k)
                    {
                        var model = objList[k] as config.ComplexGridModelData;
                        var modelTemplate = map.FindObject(model.modelTemplateID) as ModelTemplate;
                        if (modelTemplate == null)
                        {
                            Debug.Assert(false, $"model template{model.modelTemplateID} is not found!");
                        }
                        else
                        {
                            var modelData = new ComplexGridModelData(model.id, map, 0, model.position, model.rotation, model.scale, modelTemplate, model.occupiedGridCount, (byte)i, model.objectTag, model.useRenderTextureModel);
                            mLayerData.AddObjectData(modelData, i, false);
                        }
                    }
                }
            }

            mLayerData.isLoading = false;

#if UNITY_EDITOR
            //set grid dirty to false
            for (int lod = 0; lod < nLODs; ++lod)
            {
                for (int i = 0; i < verticalTileCount; ++i)
                {
                    for (int j = 0; j < horizontalTileCount; ++j)
                    {
                        mLayerData.SetGridDirty(lod, j, i, false);
                    }
                }
            }
#endif

            map.AddMapLayer(this);
        }

        public bool AddObject(int lod, ComplexGridModelData objectData)
        {
#if UNITY_EDITOR
            Debug.Assert(lod == objectData.lod);
#endif
            if (objectData == null)
            {
                return false;
            }

            int objectID = objectData.GetEntityID();
            if (mLayerData.GetObjectData(objectID) == null)
            {
                //注意使用的是lod0的模型
                ModelTemplate modelTemplate = map.GetOrCreateModelTemplate(objectID, objectData.GetAssetPath(0), false, false);
                bool success = mLayerData.AddObjectData(objectData, lod, true);
                if (success)
                {
                    //注意使用的是lod0的模型
                    mLayerView.OnAddObject(objectData, 0);
                }
            }
            return false;
        }

        public int AddObject(int lod, string prefabPath, Vector3 position, Quaternion rot, Vector3 scale, int occupiedGridCount, string objectTag, int objectID = 0, bool useRenderTextureModel = false)
        {
            if (objectID == 0)
            {
                objectID = map.nextCustomObjectID;
            }
            ModelTemplate modelTemplate = map.GetOrCreateModelTemplate(objectID, prefabPath, false, false);
            var data = new ComplexGridModelData(objectID, map, 0, position, rot, scale, modelTemplate, (ushort)occupiedGridCount, (byte)lod, objectTag, useRenderTextureModel);
            bool success = mLayerData.AddObjectData(data, lod, true);
            return data.id;
        }

        public bool RemoveObject(int objectDataID)
        {
            if (mLayerData.Contains(objectDataID))
            {
                mLayerView.OnRemoveObject(objectDataID);
                mLayerData.RemoveObjectData(objectDataID);
                return true;
            }
            return false;
        }

        public void RemoveAllObjects()
        {
            List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                RemoveObject(objects[i].GetEntityID());
            }
        }

        public void GetAllObjects(List<IComplexGridModelData> objects)
        {
            mLayerData.GetAllObjects(objects);
        }

        public void ShowObjectsOfLOD(int lod)
        {
            mLayerData.ShowObjectsOfLOD(lod);
        }

        public IComplexGridModelData FindObjectAtPosition(int lod, Vector3 pos)
        {
            return mLayerData.FindObjectAtPosition(pos, lod);
        }

        public IComplexGridModelData FindObjectAtExactSamePosition(int lod, Vector3 pos)
        {
            return mLayerData.FindObjectAtExactSamePosition(pos, lod);
        }

        public IComplexGridModelData FindObjectOfPrefabPath(int lod, string prefabPath)
        {
            return mLayerData.FindObjectOfPrefabPath(lod, prefabPath);
        }

        public void ChangeObjectModel(int objectID, bool useRenderTextureModel)
        {
            mLayerData.ChangeObjectModel(objectID, useRenderTextureModel);
        }

        public void SetLocalViewport(Vector3 newLocalViewCenter, Vector2 newLocalViewportSize)
        {
            mLocalViewCenter = newLocalViewCenter;
            mLocalViewSize = newLocalViewportSize;
            var newViewport = new Rect(newLocalViewCenter.x - newLocalViewportSize.x * 0.5f, newLocalViewCenter.z - newLocalViewportSize.y * 0.5f, newLocalViewportSize.x, newLocalViewportSize.y);
            DoUpdateViewport(newViewport, 0);
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            return false;
        }

        bool DoUpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            if (lodChanged)
            {
                mLayerView.SetZoom(newCameraZoom, lodChanged);
            }
            return lodChanged;
        }

        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        //如果物体要缩放,就要返回非空的scale config
        public virtual KeepScaleConfig GetScaleConfig()
        {
            return null;
        }

        //计算一个物体占据的格子数量
        public int CalculateOccupiedGridCount(float minX, float minZ, float maxX, float maxZ)
        {
            return mLayerData.CalculateOccupiedGridCount(minX, minZ, maxX, maxZ);
        }

        //当lod count改变时触发
        public void OnLODCountChanged(int oldLODCount, int newLODCount)
        {
            mLayerData.OnLODCountChanged(oldLODCount, newLODCount);
        }

        //检查某个tile是否需要创建prefab,规则是只要这个tile的任意lod中有至少一个prefab,就需要创建这个tile的prefab lod链
        public bool CheckIfPrefabIsNeededForThisTile(int x, int y)
        {
            int nLODs = lodCount;
            for (int i = 0; i < nLODs; ++i)
            {
                var objects = layerData.GetObjectsInGrid(i, x, y);
                if (objects.Count > 0)
                {
                    return true;
                }
            }
            return false;
        }

        public void MoveLocalViewport(Vector3 offset)
        {
            mLayerView.MoveLocalViewport(offset);
        }

        public void ScaleLocalViewport(float deltaScale)
        {
            mLayerView.ScaleLocalViewport(deltaScale);
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public bool AddObjectTag(string tag, bool visible)
        {
            return mLayerData.AddObjectTag(tag, visible);
        }

        public void RemoveObjectTag(string tag)
        {
            mLayerData.RemoveObjectTag(tag);
        }

        public string[] GetObjectTags()
        {
            return mLayerData.GetObjectTagNames();
        }

        public bool IsTagVisible(string name)
        {
            return mLayerData.IsTagVisible(name);
        }

        public void SetObjectsOfTagVisible(string tag, bool visible)
        {
            mLayerData.SetObjectsOfTagVisible(tag, visible);
        }

        public void GetObjectsInRangeWithTag(int lod, string tag, Vector3 minPos, Vector3 maxPos, List<IComplexGridModelData> objects)
        {
            objects.Clear();
            List<IComplexGridModelData> allObjects = new List<IComplexGridModelData>();
            GetObjectsInRange(lod, minPos, maxPos, allObjects);
            for (int i = 0; i < allObjects.Count; ++i)
            {
                if (allObjects[i].objectTag == tag)
                {
                    objects.Add(allObjects[i]);
                }
            }
        }

        public void GetObjectsInRange(int lod, Vector3 minPos, Vector3 maxPos, List<IComplexGridModelData> objects)
        {
            objects.Clear();
            Rect range = new Rect(minPos.x, minPos.z, maxPos.x - minPos.x, maxPos.z - minPos.z);
            var minCoord = mLayerData.FromWorldPositionToCoordinate(minPos);
            var maxCoord = mLayerData.FromWorldPositionToCoordinate(maxPos);
            HashSet<int> visitedObjects = new HashSet<int>();
            for (int i = minCoord.y; i <= maxCoord.y; ++i)
            {
                for(int j = minCoord.x; j <= maxCoord.x; ++j)
                {
                    var objectsInGrid = mLayerData.GetObjectsInGrid(lod, j, i);
                    if (objectsInGrid != null)
                    {
                        int n = objectsInGrid.Count;
                        for (int k = 0; k < n; ++k)
                        {
                            int id = objectsInGrid[k].GetEntityID();
                            if (!visitedObjects.Contains(id) &&
                                objectsInGrid[k].IsObjActive() &&
                                range.Contains(Utils.ToVector2(objectsInGrid[k].GetPosition())))
                            {
                                visitedObjects.Add(id);
                                objects.Add(objectsInGrid[k]);
                            }
                        }
                    }
                }
            }
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }

        //x方向上格子的数量
        public override int horizontalTileCount { get { return mLayerData.horizontalTileCount; } }
        //z方向上格子的数量
        public override int verticalTileCount { get { return mLayerData.verticalTileCount; } }
        //格子的宽
        public override float tileWidth { get { return mLayerData.tileWidth; } }
        //格子的高
        public override float tileHeight { get { return mLayerData.tileHeight; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override GridType gridType { get { return GridType.Rectangle; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public override int lodCount => mLayerData.lodCount;
        public Vector3 localViewCenter { get { return mLocalViewCenter; } }
        public Vector2 localViewSize { get { return mLocalViewSize; } }
        public ComplexGridObjectLayerData layerData { get { return mLayerData; } }
        public ComplexGridObjectLayerView layerView { get { return mLayerView; } }

        protected ComplexGridObjectLayerData mLayerData;
        protected ComplexGridObjectLayerView mLayerView;
        //这层layer的viewport
        public Vector3 mLocalViewCenter = new Vector3(-10000, 0, -10000);
        public Vector2 mLocalViewSize;
    }
}
