﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class TextureAtlasEqualRects : TextureAtlas
    {
        public TextureAtlasEqualRects(int tileTextureWidth, int tileTextureHeight, int atlasTextureWidth, int atlasTextureHeight, int borderWidth, int padding, Color color)
        {
            InitEmptyBins(tileTextureWidth, tileTextureHeight, atlasTextureWidth, atlasTextureHeight, borderWidth, padding);

            mAtlasTexture = new Texture2D(atlasTextureWidth, atlasTextureHeight, TextureFormat.RGBA32, true);

            Color32[] pixels = new Color32[atlasTextureWidth * atlasTextureHeight];
            for (int i = 0; i < atlasTextureHeight; ++i)
            {
                for (int j = 0; j < atlasTextureWidth; ++j)
                {
                    pixels[i] = color;
                }
            }
            mAtlasTexture.SetPixels32(pixels);
            mAtlasTexture.Apply();
        }

        public void OnDestroy()
        {
            Object.DestroyImmediate(mAtlasTexture);
        }

        void InitEmptyBins(int tileTextureWidth, int tileTextureHeight, int atlasTextureWidth, int atlasTextureHeight, int borderWidth, int padding)
        {
            Debug.Assert(tileTextureWidth > 0 && tileTextureHeight > 0);
            mBinWidth = tileTextureWidth + borderWidth * 2 + padding * 2;
            mBinHeight = tileTextureHeight + borderWidth * 2 + padding * 2;
            mHorizontalBinCount = Mathf.FloorToInt(atlasTextureWidth / mBinWidth);
            mVerticalBinCount = Mathf.FloorToInt(atlasTextureHeight / mBinHeight);
            int n = mHorizontalBinCount * mVerticalBinCount;
            mEmptyBins = new List<PackRect>(n);
            for (int i = 0; i < mVerticalBinCount; ++i)
            {
                for (int j = 0; j < mHorizontalBinCount; ++j)
                {
                    mEmptyBins.Add(new PackRect(j * mBinWidth, i * mBinHeight, (j + 1) * mBinWidth, (i + 1) * mBinHeight));
                }
            }
        }

        public int FindEmptyBin(int width, int height, int border, int padding, int atlasWidth, int atlasHeight, out PackRect emptyBin)
        {
            emptyBin = new PackRect();
            //find first fit
            int fullWidth = width + border * 2 + padding * 2;
            int fullHeight = height + border * 2 + padding * 2;
            if (fullWidth > atlasWidth || fullHeight > atlasHeight)
            {
                return -2;
            }
            for (int i = 0; i < mEmptyBins.Count; ++i)
            {
                if (mEmptyBins[i].width >= fullWidth && mEmptyBins[i].height >= fullHeight)
                {
                    emptyBin = mEmptyBins[i];
                    mEmptyBins.RemoveAt(i);
                    return i;
                }
            }
            return -1;
        }

        public void AddImage(int textureInstanceID, string imageName, Texture2D image, PackRect emptyBin, TexturePackerSetting setting, bool treatAlphaAsZero)
        {
            //copy image data to texture
            CopyImageData(image, emptyBin, setting, treatAlphaAsZero);

            int imageWidth = image.width;
            int imageHeight = image.height;
            TexturePackEntry entry = new TexturePackEntry();

            entry.offsetX = emptyBin.minX;
            entry.offsetY = emptyBin.minY;
            entry.width = imageWidth + setting.borderSize * 2 + setting.padding * 2;
            entry.height = imageHeight + setting.borderSize * 2 + setting.padding * 2;
            entry.name = imageName;
            float uvX = (entry.offsetX + setting.borderSize + setting.padding) / (float)setting.atlasTextureMaxWidth;
            float uvY = (entry.offsetY + setting.borderSize + setting.padding) / (float)setting.atlasTextureMaxHeight;
            float uvWidth = imageWidth / (float)setting.atlasTextureMaxWidth;
            float uvHeight = imageHeight / (float)setting.atlasTextureMaxHeight;
            entry.uv = new Rect(uvX, uvY, uvWidth, uvHeight);

            mEntries.Add(entry);
            int index = mEntries.Count - 1;
            mNameToEntryIndex[imageName] = index;
            mIDToEntryIndex[textureInstanceID] = index;
        }

        public int GetEntryCount()
        {
            return mEntries.Count;
        }

        public TexturePackEntry GetEntry(int index)
        {
            if (index >= 0 && index < mEntries.Count)
            {
                return mEntries[index];
            }
            return null;
        }

        public int GetTexturePackEntryIndex(int textureInstanceID)
        {
            int index;
            bool found = mIDToEntryIndex.TryGetValue(textureInstanceID, out index);
            if (found)
            {
                return index;
            }
            return -1;
        }

        public Texture2D GetTexture()
        {
            return mAtlasTexture;
        }

        List<TexturePackEntry> GetEntries()
        {
            return mEntries;
        }

        public bool GetEntryUVs(int textureID, Vector2[] uvs)
        {
            var index = GetTexturePackEntryIndex(textureID);
            if (index == -1)
            {
                return false;
            }
            var entry = GetEntry(index);
            if (entry.rotated)
            {
                Debug.Assert(false, "todo");
            }
            else
            {
                uvs[0] = new Vector2(entry.uv.xMin, entry.uv.yMin);
                uvs[1] = new Vector2(entry.uv.xMin, entry.uv.yMax);
                uvs[2] = new Vector2(entry.uv.xMax, entry.uv.yMax);
                uvs[3] = new Vector2(entry.uv.xMax, entry.uv.yMin);
            }

            return true;
        }

        public void OptimizeTextureRegion(TexturePackerSetting setting)
        {
            //贴图中有数据的区域,可以将atlas贴图裁剪到这个范围并且不影响显示效果
            PackRect validBounds = new PackRect();
            foreach (var e in mEntries)
            {
                validBounds.Add(new PackRect(e.offsetX, e.offsetY, e.width, e.height));
            }

            int width = GetNearestPowerOf2Up(validBounds.width);
            int height = GetNearestPowerOf2Up(validBounds.height);

            if (width == setting.atlasTextureMaxWidth && height == setting.atlasTextureMaxHeight)
            {
                return;
            }

            foreach (var e in mEntries)
            {
                float uvX = (e.offsetX + setting.borderSize + setting.padding) / (float)width;
                float uvY = (e.offsetY + setting.borderSize + setting.padding) / (float)height;
                var imageWidth = e.width - (setting.borderSize * 2 + setting.padding * 2);
                var imageHeight = e.height - (setting.borderSize * 2 + setting.padding * 2);
                float uvWidth = imageWidth / (float)width;
                float uvHeight = imageHeight / (float)height;
                e.uv = new Rect(uvX, uvY, uvWidth, uvHeight);
            }

            Texture2D optimizedTexture = new Texture2D(width, height, TextureFormat.RGBA32, true);
            var pixels = mAtlasTexture.GetPixels(0, 0, width, height);
            optimizedTexture.SetPixels(pixels);
            optimizedTexture.Apply();
            Object.DestroyImmediate(mAtlasTexture);
            mAtlasTexture = optimizedTexture;
        }

        void CopyImageData(Texture2D image, PackRect bin, TexturePackerSetting setting, bool treatAlphaAsZero)
        {
            var format = mAtlasTexture.format;
            if (format == TextureFormat.RGBA32)
            {
                int dataOffsetX = bin.x() + setting.borderSize + setting.padding;
                int dataOffsetY = bin.y() + setting.borderSize + setting.padding;

                int w = image.width;
                int h = image.height;

                var pixels = image.GetPixels();
                Debug.Log(image.format.ToString());
                if (setting.rgbTextureAlphaIsOne == false)
                {
                    if (!Utils.IsAlphaFormat(image.format))
                    {
                        int n = pixels.Length;
                        for (int i = 0; i < n; ++i)
                        {
                            pixels[i] = new Color(pixels[i].r, pixels[i].g, pixels[i].b, 0);
                        }
                    }
                    else
                    {
                        if (treatAlphaAsZero)
                        {
                            //check if all alpha is white
                            bool allWhite = true;
                            for (int i = 0; i < pixels.Length; ++i)
                            {
                                if (!Mathf.Approximately(pixels[i].a, 1.0f))
                                {
                                    allWhite = false;
                                    break;
                                }
                            }
                            if (allWhite)
                            {
                                int n = pixels.Length;
                                for (int i = 0; i < n; ++i)
                                {
                                    pixels[i] = new Color(pixels[i].r, pixels[i].g, pixels[i].b, 0);
                                }
                            }
                        }
                    }
                }

                //copy line 1 by 1
                for (int i = 0; i < h; ++i)
                {
                    for (int j = 0; j < w; ++j)
                    {
                        mAtlasTexture.SetPixel(dataOffsetX + j, dataOffsetY + i, pixels[i*w+j] /*realImage.GetPixel(j, i)*/);
                    }
                }
                if (setting.borderSize > 0)
                {
                    //calculate border colors
                    //top border
                    int dstX = bin.x() + setting.padding + setting.borderSize;
                    int dstY = bin.y() + setting.padding;

                    for (int i = 0; i < setting.borderSize; ++i)
                    {
                        for (int j = 0; j < w; ++j)
                        {
                            mAtlasTexture.SetPixel(dstX + j, dstY + i, pixels[j]/*realImage.GetPixel(j, 0)*/);
                        }
                    }

                    //bottom border
                    dstY += (h + setting.borderSize);
                    for (int i = 0; i < setting.borderSize; ++i)
                    {
                        for (int j = 0; j < w; ++j)
                        {
                            mAtlasTexture.SetPixel(dstX + j, dstY + i, pixels[(h-1)*w+j] /*realImage.GetPixel(j, h - 1)*/);
                        }
                    }

                    //left border
                    dstX = bin.x() + setting.padding;
                    dstY = bin.y() + setting.padding + setting.borderSize;
                    for (int i = 0; i < h; ++i)
                    {
                        for (int j = 0; j < setting.borderSize; ++j)
                        {
                            mAtlasTexture.SetPixel(dstX + j, i + dstY, pixels[i*w] /*realImage.GetPixel(0, i)*/);
                        }
                    }

                    //right border
                    for (int i = 0; i < h; ++i)
                    {
                        for (int j = 0; j < setting.borderSize; ++j)
                        {
                            mAtlasTexture.SetPixel(dstX + w + setting.borderSize + j, dstY + i, pixels[i*w+w-1] /*realImage.GetPixel(w - 1, i)*/);
                        }
                    }

                    //set 4 corners
                    {
                        //top left corner
                        int dstMinX = bin.x() + setting.padding;
                        int dstMaxX = dstMinX + setting.borderSize - 1;
                        int dstMinY = bin.y() + setting.padding;
                        int dstMaxY = dstMinY + setting.borderSize - 1;
                        for (int y = dstMinY; y <= dstMaxY; ++y)
                        {
                            for (int x = dstMinX; x <= dstMaxX; ++x)
                            {
                                mAtlasTexture.SetPixel(x, y, pixels[0]/* realImage.GetPixel(0, 0)*/);
                            }
                        }

                        //top right corner
                        dstMinX = bin.x() + setting.padding + setting.borderSize + w;
                        dstMaxX = dstMinX + setting.borderSize - 1;
                        dstMinY = bin.y() + setting.padding;
                        dstMaxY = dstMinY + setting.borderSize - 1;
                        for (int y = dstMinY; y <= dstMaxY; ++y)
                        {
                            for (int x = dstMinX; x <= dstMaxX; ++x)
                            {
                                mAtlasTexture.SetPixel(x, y, pixels[w-1]/* realImage.GetPixel(w - 1, 0)*/);
                            }
                        }

                        //bottom right corner
                        dstMinX = bin.x() + setting.padding + setting.borderSize + w;
                        dstMaxX = dstMinX + setting.borderSize - 1;
                        dstMinY = bin.y() + setting.padding + setting.borderSize + h;
                        dstMaxY = dstMinY + setting.borderSize - 1;
                        for (int y = dstMinY; y <= dstMaxY; ++y)
                        {
                            for (int x = dstMinX; x <= dstMaxX; ++x)
                            {
                                mAtlasTexture.SetPixel(x, y, pixels[(h-1)*w+w-1] /*realImage.GetPixel(w - 1, h - 1)*/);
                            }
                        }

                        //bottom left corner
                        dstMinX = bin.x() + setting.padding;
                        dstMaxX = dstMinX + setting.borderSize - 1;
                        dstMinY = bin.y() + setting.padding + setting.borderSize + h;
                        dstMaxY = dstMinY + setting.borderSize - 1;
                        for (int y = dstMinY; y <= dstMaxY; ++y)
                        {
                            for (int x = dstMinX; x <= dstMaxX; ++x)
                            {
                                mAtlasTexture.SetPixel(x, y, pixels[(h-1)*w]/* realImage.GetPixel(0, h - 1)*/);
                            }
                        }
                    }
                }

                mAtlasTexture.Apply();
            }
            else
            {
                Debug.Assert(false, "todo");
            }
        }

        int GetNearestPowerOf2Up(int val)
        {
            Debug.Assert(val <= 2048);
            if (val >= 1024)
            {
                return 2048;
            }
            if (val >= 512)
            {
                return 1024;
            }
            if (val >= 256)
            {
                return 512;
            }
            if (val >= 128)
            {
                return 256;
            }
            if (val >= 64)
            {
                return 128;
            }
            if (val >= 32)
            {
                return 64;
            }
            if (val >= 16)
            {
                return 32;
            }
            if (val >= 8)
            {
                return 16;
            }
            if (val >= 4)
            {
                return 8;
            }
            if (val >= 2)
            {
                return 4;
            }
            if (val >= 1)
            {
                return 2;
            }
            return 1;
        }

        int mBinHeight;
        int mBinWidth;
        int mHorizontalBinCount;
        int mVerticalBinCount;
        List<PackRect> mEmptyBins;
        List<TexturePackEntry> mEntries = new List<TexturePackEntry>();
        Dictionary<string, int> mNameToEntryIndex = new Dictionary<string, int>();
        Dictionary<int, int> mIDToEntryIndex = new Dictionary<int, int>();
        Texture2D mAtlasTexture;
    };
}


#endif