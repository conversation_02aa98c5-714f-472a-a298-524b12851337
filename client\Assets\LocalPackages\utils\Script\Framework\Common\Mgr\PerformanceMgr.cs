﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using UnityEngine;

namespace TFW
{
    /// <summary>
    /// <para>性能管理器。</para>
    /// <para>这个管理器主要用于提供程序的性能相关信息，包括帧率，实时帧时间等等。</para>
    /// </summary>
    [DefaultExecutionOrder(-10000)]
    public class PerformanceMgr : MonoBehaviour
    {
        // 目前该性能管理器获取实时时间的方式基于Environment.TickCount，
        // 因此时间精度受硬件平台影响，根据文档精度范围大约在1-10ms。

        #region 单例
        /// <summary>
        /// 单例。外部不可访问。
        /// </summary>
        private static PerformanceMgr s_Instance;
        #endregion

        #region 字段
        public const long UTC_1970_TICK = 621355968000000000;

        /// <summary>
        /// 目标帧率
        /// </summary>
        public int targetFrameRate = 60;

        /// <summary>
        /// 帧时间预算比例
        /// </summary>
        public float frameTimeBudgetRatio = 0.5f;

        /// <summary>
        /// FPS统计检测间隔（单位：毫秒）
        /// </summary>
        public float checkInterval = 1000f;

        /// <summary>
        /// 目标帧时间（= 1/目标帧率。单位：毫秒）
        /// </summary>
        public static float targetFrameTime => (1f / s_Instance.targetFrameRate * 1000f);

        /// <summary>
        /// 帧时间预算（= 目标帧时间 * 帧时间预算比例。单位：毫秒）
        /// </summary>
        public static float frameTimeBudget => targetFrameTime * s_Instance.frameTimeBudgetRatio;

        /// <summary>
        /// 当前帧率。每个统计周期更新一次。
        /// </summary>
        public static float frameRate { get; private set; } = 60;

        /// <summary>
        /// 实时帧时间（= 实时Tick时间 - 当前帧Tick时间。单位：毫秒）
        /// </summary>
        public static float realtimeFrameTime => realtimeTickTime - s_TickTime;

        /// <summary>
        /// 实时帧时间预算（= 帧时间预算 - 实时帧时间。单位：毫秒）
        /// 注：值有可能小于0，负值意味着当前帧的实时耗时已经超出预算。
        /// </summary>
        public static float realtimeFrameTimeBudget => frameTimeBudget - realtimeFrameTime;

        /// <summary>
        /// 实时Tick时间
        /// </summary>
        public static long realtimeTickTime => (DateTime.Now.Ticks - UTC_1970_TICK) / 10000;  // 系统当前时间（毫秒），用于替代Environment.TickCount

        #endregion

        #region 内部字段

        /// <summary>
        /// 上一次更新时的帧号
        /// </summary>
        private static int s_LastFrameNum;
        /// <summary>
        /// 当前统计周期内经过的时间
        /// </summary>
        private static float s_IntervalTime;
        /// <summary>
        /// 当前统计周期内经过的帧数
        /// </summary>
        private static int s_IntervalFrameCount;
        /// <summary>
        /// 当前帧Tick时间（单位：毫秒）
        /// </summary>
        private static long s_TickTime;
        /// <summary>
        /// 上一帧Tick时间（单位：毫秒）
        /// </summary>
        private static long s_LastTickTime;
        /// <summary>
        /// 帧Tick时间Delta(= 当前帧Tick时间 - 上一帧Tick时间)
        /// </summary>
        private static long s_DeltaTickTime;

        #endregion

        #region MonoBehaviour生命周期

        private void Start()
        {
            s_Instance = gameObject.GetComponent<PerformanceMgr>();

            D.Info?.Log("[PerformanceMgr] 性能管理器 PerformanceMgr 组件.");
        }

        private void FixedUpdate()
        {
            var frameNum = Time.frameCount;

            if (s_LastFrameNum != frameNum)
            {
                s_LastFrameNum = frameNum;
                OnFrameStart();
            }
        }

        #endregion

        #region 内部方法

        /// <summary>
        /// 帧开始时执行逻辑
        /// </summary>
        private static void OnFrameStart()
        {
            var tick = realtimeTickTime;
            if (s_TickTime == 0) { s_TickTime = tick; }

            s_LastTickTime = s_TickTime;
            s_TickTime = tick;
            s_DeltaTickTime = s_TickTime - s_LastTickTime;
            s_IntervalTime += s_DeltaTickTime;
            s_IntervalFrameCount++;

            if (s_IntervalTime >= s_Instance.checkInterval)
            {
                UpdateStats();
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private static void UpdateStats()
        {
            frameRate = s_IntervalFrameCount / s_Instance.checkInterval;
            s_IntervalFrameCount = 0;
        }

        #endregion
    }
}