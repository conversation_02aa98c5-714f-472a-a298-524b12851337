﻿//混合扭曲流动效果
Shader "K1/Particle/FX_AddBlend_DistortionDissovle_Flow" 
{

    Properties 
    {
        [Header(Base Effect Control)]
        _TintColor ("TintColor", Color) = (0.5,0.5,0.5,0.5)
        _MainTex ("MainTex", 2D) = "white" {}
        _MainTex_Uspeed ("MainTex Uspeed", Float ) = 0
        _MainTex_Vspeed ("MainTex Vspeed", Float ) = 0
        _RotateSpeed("RotateTex",float) = 0

        [Header(Mask Control)]
        _MaskTex ("MaskTex", 2D) = "white" {}
        _MaskTex_Uspeed("Mask Uspeed",float) = 0
        _MaskTex_Vspeed("Mask Vspeed",float) = 0
        
        [Space(10)]
        [Header(Linear Feather Control)]
        //[KeywordEnum(OFF, ON)]FEATHER_MASK("Feather Control Functio Trigger", float) = 0
        _disslove ("disslove", Range(-0,1)) =0
        [Enum(Off,0,On,1)]_DissloveTex_Flip("DissloveTex_Flip", float) = 0
        _dissloveTex ("disslove Tex", 2D) = "black" {}
        _dissloveTex_mask ("disslove Tex_mask", 2D) = "white" {}
        _feather_weight  ("Feather Weight ", Float ) = 999999
        _dis_U_speed ("dis_U_speed", Float ) = 0
        _dis_V_speed ("dis_V_speed", Float ) = 0

        [Space(20)][Header(Distortion RG Channel Tex Control)]
        _DistortionTex ("Distortion Tex", 2D) = "black" {}
        _Distortion_Amount1 ("Distortion_Amount1", range(0,1) ) = 0
        _R_Uspeed ("R Channel Uspeed", Float ) = 0
        _R_Vspeed ("R Channel Vspeed", Float ) = 0

        //_noise2_tex ("noise2_tex", 2D) = "black" {}
        // _Distortion_Amount2 ("G Channel DisAmount", Float ) = 0
        _G_Uspeed ("G Channel Uspeed", Float ) = 0
        _G_Vspeed ("G Channel Vspeed", Float ) = 0

        [Header(Blend Model)]
        [KeywordEnum(OFF, ON)] ALPHA_FOR_R("ALPHA_FOR_R(If addtive use R channel)", float) = 0
        [Enum(UnityEngine.Rendering.BlendMode)] _SrcBlend ("SrcBlend Mode", Float) = 5
        [Enum(UnityEngine.Rendering.BlendMode)] _DstBlend ("DstBlend Mode", Float) = 10
        [Enum(Off,0,On,1)] _ZWrite ("ZWrite", Float) = 0   //特效用不到
        [Enum(UnityEngine.Rendering.CompareFunction)] _ZTest ("ZTest", Float) = 2
        [Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", float) = 2
        
        // // begin property block of UI mask support
        // [HideInInspector] _StencilComp("Stencil Comparison", Float) = 8
        // [HideInInspector] _Stencil("Stencil ID", Float) = 0
        // [HideInInspector] _StencilOp("Stencil Operation", Float) = 0
        // [HideInInspector] _StencilWriteMask("Stencil Write Mask", Float) = 255
        // [HideInInspector] _StencilReadMask("Stencil Read Mask", Float) = 255
        // [HideInInspector] _ColorMask("Color Mask", Float) = 15
        // // end property block of UI mask support
    }

    SubShader
    {
        Tags { "Queue"="Transparent+500" "IgnoreProjector"="True" "RenderType"="Transparent" "PreviewType"="Plane" }

        // Stencil
        // {
            //     Ref[_Stencil]
            //     Comp[_StencilComp]
            //     Pass[_StencilOp]
            //     ReadMask[_StencilReadMask]
            //     WriteMask[_StencilWriteMask]
        // }

        Blend [_SrcBlend] [_DstBlend]
        ZWrite[_ZWrite]
        ZTest[_ZTest]
        Cull[_Cull]
        Lighting Off

        Pass 
        {

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            #include "UnityCG.cginc"

            #pragma multi_compile _ ALPHA_FOR_R_OFF ALPHA_FOR_R_ON 
            //#pragma multi_compile _ FEATHER_MASK

            sampler2D _MainTex;
            float4 _MainTex_ST;
            sampler2D _MaskTex;
            float4 _MaskTex_ST;
            float4 _TintColor;
            float _RotateSpeed;
            float _MainTex_Uspeed;
            float _MainTex_Vspeed;
            float _MaskTex_Uspeed;
            float _MaskTex_Vspeed;

            
            sampler2D _dissloveTex;
            sampler2D _dissloveTex_mask;
            float4 _dissloveTex_ST;
            float4 _dissloveTex_mask_ST;
            float _feather_weight;
            float _dis_U_speed;
            float _dis_V_speed;
            float _DissloveTex_Flip;
            float _disslove;

            sampler2D _DistortionTex;
            float4 _DistortionTex_ST;
            float _Distortion_Amount1;
            float _R_Uspeed;
            float _R_Vspeed;

            // sampler2D _noise2_tex;
            // float4 _noise2_tex_ST;
            float _Distortion_Amount2;
            float _G_Uspeed;
            float _G_Vspeed;

            struct appdata_t 
            {
                float4 vertex : POSITION;
                fixed4 color : COLOR;
                float2 texcoord : TEXCOORD0;
                float2 texcoord2 : TEXCOORD1;
            };

            struct v2f 
            {
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
                float4 texcoord : TEXCOORD0;
                float4 texcoord2 : TEXCOORD1;
                float4 texcoord3 : TEXCOORD2;
                float2 uv2 : TEXCOORD3;
            };

            
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.color = v.color * _TintColor;
                o.texcoord.xy = TRANSFORM_TEX(v.texcoord, _MainTex) + float2(_Time.y * _MainTex_Uspeed, _Time.y * _MainTex_Vspeed);
                o.texcoord.zw = TRANSFORM_TEX(v.texcoord, _MaskTex) + float2(_Time.y * _MaskTex_Uspeed, _Time.y * _MaskTex_Vspeed);

                o.texcoord2.xy = TRANSFORM_TEX(v.texcoord, _dissloveTex) + float2(_Time.y * _dis_U_speed, _Time.y * _dis_V_speed);
                o.texcoord2.zw = TRANSFORM_TEX(v.texcoord, _dissloveTex_mask);

                o.texcoord3.xy = TRANSFORM_TEX(v.texcoord, _DistortionTex) + float2(_Time.y * _R_Uspeed, _Time.y * _R_Vspeed);
                o.texcoord3.zw = TRANSFORM_TEX(v.texcoord, _DistortionTex) + float2(_Time.y * _G_Uspeed, _Time.y * _G_Vspeed);

                o.uv2 = v.texcoord2;
                return o;
            }

            float4 frag (v2f i) : SV_Target
            {
                float2 uvMain = i.texcoord.xy - float2(0.5,0.5);
                uvMain = float2(uvMain.x*cos(_RotateSpeed/(180/3.1416)) - uvMain.y*sin(_RotateSpeed/(180/3.1416)),uvMain.x*sin(_RotateSpeed /(180/3.1416)) + uvMain.y*cos(_RotateSpeed/(180/3.1416)) );
                uvMain += float2(0.5,0.5);

                float4 col = i.color;

                float2 noiseTex1 = tex2D(_DistortionTex, i.texcoord3.xy).r;
                float2 noiseTex2 = tex2D(_DistortionTex, i.texcoord3.zw).g;
                float2 distort = lerp(uvMain,noiseTex1.r + noiseTex2.g,_Distortion_Amount1);

                float4 texCol = tex2D(_MainTex,distort) * 2.0;

                col.rgb *= texCol.rgb;

                //for addtive model
                #ifdef ALPHA_FOR_R_ON
                    col.a *= texCol.r;
                #else
                    col.a *= texCol.a;
                #endif
                
                col.a *= tex2D(_MaskTex, i.texcoord.zw).r;
                //linear feather mask function
                //#ifdef FEATHER_MASK
                float4 dissloveTex = tex2D(_dissloveTex, i.texcoord2.xy);
                float4 dissloveTex_mask = tex2D(_dissloveTex_mask, i.texcoord2.zw);
                col.a *= saturate( (i.uv2.x - (abs(dissloveTex.r - _DissloveTex_Flip)+_disslove) * dissloveTex_mask.r) * _feather_weight );
                //#endif
                return col;
            }
            ENDCG
        }
    }

}
