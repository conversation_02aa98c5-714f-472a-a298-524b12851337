﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using UnityEditor.SceneManagement;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionSetSplitFogLayerTiles : EditorAction
    {
        public ActionSetSplitFogLayerTiles(int layerID, int prefabGroupIndex, Vector2Int[] pickedTiles, int[] oldTileDatas, int[] newTileDatas)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as SplitFogLayer;
            mPickedTiles = (Vector2Int[])pickedTiles.Clone();
            mOldTileDatas = oldTileDatas;
            mNewTileDatas = newTileDatas;
            mLayerID = layerID;
            mPrefabGroupIndex = prefabGroupIndex;
            mDescription = string.Format("set {0} layer's tiles", layer.name);
        }

        public override bool Do()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as SplitFogLayer;
            if (layer != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorSplitFogPrefabManager;
                if (mPrefabGroupIndex >= 0)
                {
                    var group = prefabManager.GetGroupByIndex(mPrefabGroupIndex);
                    if (group != null && group.isValid)
                    {
                        for (int i = 0; i < 4; ++i)
                        {
                            layer.SetTile(mPickedTiles[i].x, mPickedTiles[i].y, mNewTileDatas[i]);
                        }

                        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
                        return true;
                    }
                }
            }
            return false;
        }

        public override bool Undo()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as SplitFogLayer;
            if (layer != null)
            {
                for (int i = 0; i < 4; ++i)
                {
                    if (mOldTileDatas[i] != 0)
                    {
                        layer.SetTile(mPickedTiles[i].x, mPickedTiles[i].y, mOldTileDatas[i]);
                    }
                }
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
                return true;
            }
            return false;
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        static int[] mTileIndex = new int[] { 1, 2, 4, 8 };
        int[] mOldTileDatas;
        int[] mNewTileDatas;
        Vector2Int[] mPickedTiles;
        int mLayerID;
        int mPrefabGroupIndex;
        string mDescription;
    }
}

#endif