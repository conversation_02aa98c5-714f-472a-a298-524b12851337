﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理地图上对象的模型的基类
    public abstract class MapObjectLayerView : MapLayerView
    {
        public MapObjectLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            layerData.map.messageQueue.RegisterGameMessageHandler(GMMapObjectTransformChange.staticType, this, OnObjectTransformChange);
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            foreach (var view in mViews)
            {
                view.Value.OnDestroy();
            }
            mViews = null;

            layerData.map.messageQueue.RemoveGameMessageHandler(GMMapObjectTransformChange.staticType, this);
        }

        public void OnObjectActiveStateChange(IMapObjectData data, int lod)
        {
            if (data.IsObjActive())
            {
                ShowObject(data, lod);
            }
            else
            {
                HideObject(data, lod);
            }
        }

        public void OnObjectScaleChange(IMapObjectData data)
        {
            var view = GetObjectView(data.GetEntityID());
            if (view != null)
            {
                view.SetScale(data.GetScale());
            }
        }

        //当地图物体的位置改变时调用
        void OnObjectTransformChange(GameMessage msg)
        {
            var m = msg as GMMapObjectTransformChange;
            if (m.layerID == mLayerData.id)
            {
                var view = GetObjectView(m.data.GetEntityID());
                if (view != null)
                {
                    view.SetPosition(m.data.GetPosition());
                    view.SetRotation(m.data.GetRotation());
                    view.SetScale(m.data.GetScale());
                }
            }
        }

        //显示地图对象的模型
        protected virtual void ShowObject(IMapObjectData obj, int lod)
        {
            if (mVisibleViews.ContainsKey(obj.GetEntityID()))
            {
                return;
            }

            var view = GetObjectView(obj.GetEntityID());

            bool createdView = false;
            if (view == null)
            {
                createdView = true;
                view = CreateObjectView(obj);
                mViews[obj.GetEntityID()] = view;
                if (layerData.map.eventListener != null)
                {
                    layerData.map.eventListener.OnAddMapObject(obj);
                }
            }

            bool active = obj.IsObjActive();

            if (active)
            {
                AddVisibleView(view);
            }

            if (!createdView)
            {
                view.SetActive(active);
            }

            //if (view.transform != null)
            //{
            //    view.transform.SetParent(root.transform, false);
            //}
        }

        //隐藏地图对象的模型
        protected virtual void HideObject(IMapObjectData obj, int lod)
        {
            var data = obj as IMapObjectData;
            var view = GetObjectView(data.GetEntityID());
            if (view != null)
            {
#if UNITY_EDITOR
                OnViewUnload(view);
#endif
                view.SetActive(false);
                RemoveVisibleView(view);
            }
        }

        void AddVisibleView(MapObjectView view)
        {
            mVisibleViews.Add(view.objectDataID, view);
        }

        void RemoveVisibleView(MapObjectView view)
        {
            bool suc = mVisibleViews.Remove(view.objectDataID);
        }

        //创建地图对象时,给该对象增加一个视图
        public void AddObjectView(IMapObjectData data)
        {
            ShowObject(data, layerData.currentLOD);
        }

        //删除地图对象的视图
        public bool RemoveObjectView(int id)
        {
            var view = GetObjectView(id);
            if (view != null)
            {
#if UNITY_EDITOR
                OnViewUnload(view);
#endif
                RemoveVisibleView(view);
                RemoveObjectInternal(view);
                mViews.Remove(id);
                return true;
            }
            return false;
        }

        //返回地图对象的视图
        public MapObjectView GetObjectView(int id)
        {
            MapObjectView view = null;
            mViews.TryGetValue(id, out view);
            return view;
        }

        public override void SetZoom(float zoom, bool lodChanged)
        {
            foreach (var p in mVisibleViews)
            {
                var view = p.Value;
                view.SetZoom(zoom, lodChanged);

                //通知event listener
                var mapEventListener = layerData.map.eventListener;
                if (mapEventListener != null)
                {
                    mapEventListener.OnMapObjectLODChange(view);
                }
            }
        }

        void OnViewUnload(MapObjectView view)
        {
            layerData.map.OnMapGameObjectUnloaded(layerData, view.objectDataID, view.model.gameObject);
        }

        //重新加载view,因为可能他们使用的model template的prefab变了
        public override void ReloadVisibleViews()
        {
            var map = mLayerData.map;
            foreach (var p in mVisibleViews)
            {
                var objectData = map.FindObject(p.Key) as IMapObjectData;
                if (objectData != null)
                {
                    p.Value.Refresh(objectData, mLayerData.currentLOD);
                }
            }
        }

        //创建地图对象的视图
        protected abstract MapObjectView CreateObjectView(IMapObjectData data);
        protected virtual void RemoveObjectInternal(MapObjectView view)
        {
            view.OnDestroy();
        }

        public Dictionary<int, MapObjectView> allViews { get { return mViews; } }

        //该层中所有地图对象的视图
        protected Dictionary<int, MapObjectView> mViews = new Dictionary<int, MapObjectView>();
        //在视口内的view
        protected Dictionary<int, MapObjectView> mVisibleViews = new Dictionary<int, MapObjectView>();
    };
}