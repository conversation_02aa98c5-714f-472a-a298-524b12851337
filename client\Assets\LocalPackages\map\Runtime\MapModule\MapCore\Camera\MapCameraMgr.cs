﻿ 



 
 



using System.Collections.Generic;
using System;
using UnityEngine;
using System.Collections;
using Cysharp.Threading.Tasks;
using static TFW.Map.MapManager;

namespace TFW.Map
{
    //相机管理私有接口
    public static partial class MapCameraMgr
    {
        public class Camera2DSetting
        {
            public float minOrthoSize = 20;
            public float maxOrthoSize = 2000;
        }

        class SortActions : IComparer<CameraAction>
        {
            public int Compare(CameraAction x, CameraAction y)
            {
                if (x.updateOrder != y.updateOrder)
                {
                    return x.updateOrder - y.updateOrder;
                }

                return x.callOrder - y.callOrder;
            }
        }

        class CameraCurveMovementSetting
        {
            //设置相机在下降时往forward方向移动的距离,这样相机就可以根据坐标设置x旋转角
            public AnimationCurve forwardOffsetCurveBelowSplitHeight;
            public AnimationCurve forwardOffsetCurveAboveSplitHeight;
            //超过这个距离相机移动使用forwardOffsetCurveAboveSplitHeight,低于这个距离则使用forwardOffsetCurveBelowSplitHeight
            public float splitHeight;
            public float xRotationAtMaxHeight;
            public float maxHeight;
        }

        //public static void Init(GameObject cameraRoot)
        //{
        //    if (MapCamera == null)
        //    {
        //        // map camera
        //        MapCameraRoot = cameraRoot;
        //        mMapCameras = new MapCameras(MapCameraRoot, false);
        //        UnityEngine.Debug.Assert(MapCamera != null);

        //        mBorderClamp = MapCameraRoot.GetComponent<MapCameraBorderClampByCurve>();
        //        mClampBorder = MapModule.cameraClampBorder;

        //        mEnableUpdateFarClipPlane = MapModule.enableUpdateFarClipPlane;
        //        mEnableUpdateNearClipPlane = MapModule.enableUpdateNearClipPlane;

        //        if (MapModule.is2DGame)
        //        {
        //            var camera = MapCameraMgr.MapCamera.firstCamera;
        //            camera.orthographicSize = camera.pixelHeight * 0.5f;
        //        }

        //        var cameraSettings = MapManager.GetCameraSettingFilePaths();
        //        mCameraHeightSettings = new Dictionary<string, MapCameraSetting>(cameraSettings.Count);
        //        for (int i = 0; i < cameraSettings.Count; ++i)
        //        {
        //            var cameraSetting = new MapCameraSetting();
        //            cameraSetting.Load($"{MapModule.configResDirectory}{cameraSettings[i]}.bytes");
        //            mCameraHeightSettings.Add(cameraSettings[i], cameraSetting);
        //            if (i == 0)
        //            {
        //                SetActiveCameraSetting(cameraSetting);
        //            }
        //        }

        //        //跟随目标
        //        var followTarget = new CameraFollowTarget(CameraActionType.FollowTarget);
        //        followTarget.finishedCallback = FinishCameraFollowTarget;
        //        AddGlobalAction(followTarget);

        //        //拖拽
        //        var move = new CameraDrag(CameraActionType.Drag, 1.2f, 0.8f);
        //        AddGlobalAction(move);

        //        var dragRotate = new CameraDragRotate(CameraActionType.DragRotate, 0);
        //        AddGlobalAction(dragRotate);
        //        dragRotate.on = false;

        //        //二指缩放+平移
        //        if (MapModule.is2DGame)
        //        {
        //            var zoom = new CameraZoom2D(CameraActionType.Zoom);
        //            AddGlobalAction(zoom);
        //        }
        //        else
        //        {
        //            mZoom = new CameraZoomAndPan(CameraActionType.Zoom);
        //            AddGlobalAction(mZoom);
        //        }
        //        CameraMoveByDir moveDir = new CameraMoveByDir(CameraActionType.DirMovement);
        //        AddGlobalAction(moveDir);

        //        //鼠标缩放
        //        if (MapModule.is2DGame)
        //        {
        //            var scroll = new CameraScroll2D(CameraActionType.Scroll);
        //            AddGlobalAction(scroll);
        //        }
        //        else
        //        {
        //            var scroll = new CameraScroll(CameraActionType.Scroll);
        //            AddGlobalAction(scroll);
        //        }

        //        //弧线移动
        //        if (!MapModule.is2DGame)
        //        {
        //            //maya鼠标缩放
        //            var mayaZoom = new CameraMayaZoom(CameraActionType.MayaZoom);
        //            AddGlobalAction(mayaZoom);
        //            var curveZoom = new CameraCurveZoom(CameraActionType.CurveZoom);
        //            //AddGlobalAction(curveZoom);
        //        }

        //        if (MapModule.is2DGame)
        //        {
        //            var moveToTarget = new CameraMoveToTarget2D(CameraActionType.MoveToTarget);
        //            moveToTarget.finishedCallback = OnCameraReachToTarget;
        //            moveToTarget.interrupedCallback = InterruptCameraMoveToTarget;
        //            AddGlobalAction(moveToTarget);
        //        }
        //        else
        //        {
        //            //移动并缩放到目标
        //            var moveToTarget = new CameraMoveToTarget(CameraActionType.MoveToTarget);
        //            moveToTarget.finishedCallback = OnCameraReachToTarget;
        //            moveToTarget.interrupedCallback = InterruptCameraMoveToTarget;
        //            AddGlobalAction(moveToTarget);
        //        }

        //        //直线移动到目标,没有先平移再缩放的过程
        //        var moveStraightToTarget = new CameraMoveStraightToTarget(CameraActionType.MoveToTargetStraight);
        //        moveStraightToTarget.finishedCallback = OnCameraReachToTarget;
        //        moveStraightToTarget.interrupedCallback = InterruptCameraMoveToTarget;
        //        AddGlobalAction(moveStraightToTarget);

        //        //立即移动到目标
        //        var moveToTargetInstantly = new CameraMoveToTargetInstantly(CameraActionType.MoveToTargetInstantly);
        //        AddGlobalAction(moveToTargetInstantly);

        //        //缩放并移动到目标
        //        var moveToTargetRev = new CameraMoveToTargetRev(CameraActionType.MoveToTargetRev);
        //        moveToTargetRev.finishedCallback = OnCameraReachToTargetRev;
        //        moveToTargetRev.interrupedCallback = InterruptCameraMoveToTargetRev;
        //        AddGlobalAction(moveToTargetRev);

        //        if (MapModule.useNewCameraHeightAutoUpdateAlgorithm)
        //        {
        //            var cameraAutoUpdateHeight = new CameraAutoUpdateHeight(CameraActionType.AutoUpdateHeight);
        //            AddGlobalAction(cameraAutoUpdateHeight);
        //        }
        //        else
        //        {
        //            //更改相机高度
        //            var cameraHeightChange = new CameraHeightChange(CameraActionType.ChangeCameraHeight);
        //            AddGlobalAction(cameraHeightChange);
        //        }

        //        //震动
        //        var shake = new CameraShake(CameraActionType.Shake);
        //        AddLocalAction(shake);

        //        // var keyboardMovement = new CameraKeyboardMovement(CameraActionType.KeyboardMovement);
        //        // AddGlobalAction(keyboardMovement);

        //        if (MapModule.enableCameraCollision)
        //        {
        //            //碰撞解析
        //            var collisionResolve = new CameraCollisionResolve(CameraActionType.CollisionResolve, 10, 40);
        //            AddGlobalAction(collisionResolve);

        //            //var collisionCheck = new CameraCollisionCheck(CameraActionType.CollisionCheck);
        //            //AddGlobalAction(collisionCheck);
        //        }

        //        mCurrentMinimumHeight = mActiveCameraSetting.GetCameraHeightSetting("min").cameraHeight;
        //        mCurrentCameraHeight = cameraRoot.transform.position.y;
        //        mLastCameraHeight = mCurrentCameraHeight;
        //        mUpdatedCameraYRotation = cameraRoot.transform.rotation.eulerAngles.y;
        //        mUpdatedCameraXRotation = cameraRoot.transform.rotation.eulerAngles.x;

        //        mStatistics = new CameraStatistics(5);

        //        mSortedGlobalActions.AddRange(mGlobalActions);

        //        bounceRange = 0;

        //        //time = 0,相机在最低点,time = 1,相机在最高点
        //        mCurveMovementSetting.forwardOffsetCurveBelowSplitHeight = AnimationCurve.Linear(0, 1, 1, 1);
        //    }
        //}

        #if UNITY_EDITOR
        [RuntimeInitializeOnLoadMethod]
        static void RunOnStart()
        {
            Uninit();
        }
        #endif
        
        public static async UniTask InitAsync(GameObject cameraRoot)
        { 
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera");
            if (MapCamera == null)
            {
                // map camera
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera step01");
                MapCameraRoot = cameraRoot;
                mMapCameras = new MapCameras(MapCameraRoot, false);
               // UnityEngine.Debug.Assert(MapCamera != null);

                mBorderClamp = MapCameraRoot.GetComponent<MapCameraBorderClampByCurve>();
                mClampBorder = MapModule.cameraClampBorder;

                mEnableUpdateFarClipPlane = MapModule.enableUpdateFarClipPlane;
                mEnableUpdateNearClipPlane = MapModule.enableUpdateNearClipPlane;
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera step02");
                if (MapModule.is2DGame)
                {
                    //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera step03");
                    var camera = MapCameraMgr.MapCamera.firstCamera;
                    camera.orthographicSize = camera.pixelHeight * 0.5f;
                }
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera step04");
                var cameraSettings = MapManager.GetCameraSettingFilePaths();// MapModule.cameraSettings;
                mCameraHeightSettings = new Dictionary<string, MapCameraSetting>(cameraSettings.Count);

                //UnityEngine.Debug.LogError($"SetCameraSetting  List:  {Newtonsoft.Json.JsonConvert.SerializeObject(cameraSettings)}");

                for (int i = 0; i < cameraSettings.Count; ++i)
                { 
                    var cameraSetting = new MapCameraSetting();
                    //cameraSetting.Load($"{MapModule.configResDirectory}{cameraSettings[i]}.bytes");
                    await cameraSetting.LoadAsync($"{MapModule.configResDirectory}{cameraSettings[i]}.bytes");
                    
                    mCameraHeightSettings.Add(cameraSettings[i], cameraSetting);

                    //UnityEngine.Debug.LogError($"SetCameraSetting  {cameraSettings[i]}：{$"{MapModule.configResDirectory}{cameraSettings[i]}.bytes"}  Dic:  {Newtonsoft.Json.JsonConvert.SerializeObject(cameraSetting)}");
                    if (i == 0)
                    {
                       await SetActiveCameraSetting(cameraSetting);
                    }
                }

                //UnityEngine.Debug.LogError($"SetCameraSetting {mCameraHeightSettings.Count} End:  {Newtonsoft.Json.JsonConvert.SerializeObject(mCameraHeightSettings)}");

                //跟随目标
                var followTarget = new CameraFollowTarget(CameraActionType.FollowTarget);
                followTarget.finishedCallback = FinishCameraFollowTarget;
                AddGlobalAction(followTarget);

                //拖拽
                var move = new CameraDrag(CameraActionType.Drag, 1.2f, 0.8f);
                AddGlobalAction(move);

                var dragRotate = new CameraDragRotate(CameraActionType.DragRotate, 0);
                AddGlobalAction(dragRotate);
                dragRotate.on = false;

                //二指缩放+平移
                if (MapModule.is2DGame)
                {
                    var zoom = new CameraZoom2D(CameraActionType.Zoom);
                    AddGlobalAction(zoom);
                }
                else
                {
                    mZoom = new CameraZoomAndPan(CameraActionType.Zoom);
                    AddGlobalAction(mZoom);
                }
                CameraMoveByDir moveDir = new CameraMoveByDir(CameraActionType.DirMovement);
                AddGlobalAction(moveDir);

                //鼠标缩放
                if (MapModule.is2DGame)
                {
                    var scroll = new CameraScroll2D(CameraActionType.Scroll);
                    AddGlobalAction(scroll);
                }
                else
                {
                    var scroll = new CameraScroll(CameraActionType.Scroll);
                    AddGlobalAction(scroll);
                }

                //弧线移动
                if (!MapModule.is2DGame)
                {
                    //maya鼠标缩放
                    var mayaZoom = new CameraMayaZoom(CameraActionType.MayaZoom);
                    AddGlobalAction(mayaZoom);
                    var curveZoom = new CameraCurveZoom(CameraActionType.CurveZoom);
                    //AddGlobalAction(curveZoom);
                }

                if (MapModule.is2DGame)
                {
                    var moveToTarget = new CameraMoveToTarget2D(CameraActionType.MoveToTarget);
                    moveToTarget.finishedCallback = OnCameraReachToTarget;
                    moveToTarget.interrupedCallback = InterruptCameraMoveToTarget;
                    AddGlobalAction(moveToTarget);
                }
                else
                {
                    //移动并缩放到目标
                    var moveToTarget = new CameraMoveToTarget(CameraActionType.MoveToTarget);
                    moveToTarget.finishedCallback = OnCameraReachToTarget;
                    moveToTarget.interrupedCallback = InterruptCameraMoveToTarget;
                    AddGlobalAction(moveToTarget);
                }

                //直线移动到目标,没有先平移再缩放的过程
                var moveStraightToTarget = new CameraMoveStraightToTarget(CameraActionType.MoveToTargetStraight);
                moveStraightToTarget.finishedCallback = OnCameraReachToTarget;
                moveStraightToTarget.interrupedCallback = InterruptCameraMoveToTarget;
                AddGlobalAction(moveStraightToTarget);

                //立即移动到目标
                var moveToTargetInstantly = new CameraMoveToTargetInstantly(CameraActionType.MoveToTargetInstantly);
                AddGlobalAction(moveToTargetInstantly);

                //缩放并移动到目标
                var moveToTargetRev = new CameraMoveToTargetRev(CameraActionType.MoveToTargetRev);
                moveToTargetRev.finishedCallback = OnCameraReachToTargetRev;
                moveToTargetRev.interrupedCallback = InterruptCameraMoveToTargetRev;
                AddGlobalAction(moveToTargetRev);

                if (MapModule.useNewCameraHeightAutoUpdateAlgorithm)
                {
                    var cameraAutoUpdateHeight = new CameraAutoUpdateHeight(CameraActionType.AutoUpdateHeight);
                    AddGlobalAction(cameraAutoUpdateHeight);
                }
                else
                {
                    //更改相机高度
                    var cameraHeightChange = new CameraHeightChange(CameraActionType.ChangeCameraHeight);
                    AddGlobalAction(cameraHeightChange);
                }

                //震动
                var shake = new CameraShake(CameraActionType.Shake);
                AddLocalAction(shake);

                // var keyboardMovement = new CameraKeyboardMovement(CameraActionType.KeyboardMovement);
                // AddGlobalAction(keyboardMovement);

                if (MapModule.enableCameraCollision)
                {
                    //碰撞解析
                    var collisionResolve = new CameraCollisionResolve(CameraActionType.CollisionResolve, 10, 40);
                    AddGlobalAction(collisionResolve);

                    //var collisionCheck = new CameraCollisionCheck(CameraActionType.CollisionCheck);
                    //AddGlobalAction(collisionCheck);
                }

                mCurrentMinimumHeight = mActiveCameraSetting.GetCameraHeightSetting("min").cameraHeight;
                mCurrentCameraHeight = cameraRoot.transform.position.y;
                mLastCameraHeight = mCurrentCameraHeight;
                mUpdatedCameraYRotation = cameraRoot.transform.rotation.eulerAngles.y;
                mUpdatedCameraXRotation = cameraRoot.transform.rotation.eulerAngles.x;

                mStatistics = new CameraStatistics(5);

                mSortedGlobalActions.AddRange(mGlobalActions);

                bounceRange = 0;

                //time = 0,相机在最低点,time = 1,相机在最高点
                mCurveMovementSetting.forwardOffsetCurveBelowSplitHeight = AnimationCurve.Linear(0, 1, 1, 1); 
            }
        }

        public static void Uninit()
        {
            mMapCameras = null;
            MapCameraRoot = null;
            IsTwoTouchZooming = false;

            for (int i = 0; i < mGlobalActions.Count; ++i)
            {
                mGlobalActions[i].OnDestroy();
            }
            mGlobalActions.Clear();

            for (int i = 0; i < mLocalActions.Count; ++i)
            {
                mLocalActions[i].OnDestroy();
            }
            mLocalActions.Clear();
        }

        //注册当相机位置改变时的回调
        public static void SetCameraPositionChangingListener(Action<Vector3, float, float> listener)
        {
            mOnCameraPositionChanging = listener;
        }

        public static void UnsetCameraPositionChangingListener()
        {
            mOnCameraPositionChanging = null;
        }

        static void SetActionCallOrder(CameraAction action)
        {
            action.callOrder = ++mCallOrder;
        }

        internal static void SetAutoUpdateTargetHeight(float height, CameraAutoUpdateState state)
        {
            var action = GetGlobalAction(typeof(CameraAutoUpdateHeight)) as CameraAutoUpdateHeight;
            if (action != null)
            {
                action.SetMinimumHeight(height, state);
            }
        }

        internal static void ResetAutoUpdateTargetHeight()
        {
            var action = GetGlobalAction(typeof(CameraAutoUpdateHeight)) as CameraAutoUpdateHeight;
            if (action != null)
            {
                action.ResetHeight();
            }
        }

        public static void StartCurveZoom(float zDistance, float targetHeight, float targetAngle, float duration, System.Action onReachTarget)
        {
            var zoom = GetGlobalAction(typeof(CameraCurveZoom)) as CameraCurveZoom;
            if (zoom != null)
            {
                zoom.StartZooming(zDistance, targetHeight, targetAngle, duration, onReachTarget);
            }
        }

        //启用使相机高度改变的功能
        internal static void EnableChangeCameraHeight(Vector3 targetCenter, float startDistance, Vector2 cameraHeightRange, Vector2 xzDistanceAndHeightRatioRange, Vector2 cameraRisingSpeedRange)
        {
            var action = GetGlobalAction(typeof(CameraHeightChange)) as CameraHeightChange;
            action.StartMoving(targetCenter, startDistance, cameraHeightRange, xzDistanceAndHeightRatioRange, cameraRisingSpeedRange);
        }

        internal static void SetScrollHeightLimit(bool hasLimit)
        {
            var action = GetGlobalAction(typeof(CameraScroll)) as CameraScroll;
            if (action != null)
            {
                action.hasHeightLimit = hasLimit;
            }

            var action1 = GetGlobalAction(typeof(CameraZoomAndPan)) as CameraZoomAndPan;
            if (action1 != null)
            {
                action1.hasHeightLimit = hasLimit;
            }
        }

        internal static void DisableChangeCameraHeight()
        {
            var action = GetGlobalAction(typeof(CameraHeightChange)) as CameraHeightChange;
            if (action != null)
            {
                action.StopMoving();
            }
        }

        internal static void SetCameraHeightChangeTargetHeight(float height)
        {
            var action = GetGlobalAction(typeof(CameraHeightChange)) as CameraHeightChange;
            if (action != null)
            {
                action.SetTargetHeight(height);
            }
        }

        static void UpdateLocalActions()
        {
            var localCameraPos = MapCamera.transform.localPosition;
            CameraMoveAxis takenAxis = CameraMoveAxis.None;
            bool isMoved = false;
            for (int i = 0; i < mLocalActions.Count; ++i)
            {
                var action = mLocalActions[i];
                Debug.Assert(action.moveAxis != CameraMoveAxis.None);

                bool enabled = action.enabled;
                action.Update(localCameraPos, null);

                if (action.enabled)
                {
                    //计算相机的目标位置
                    localCameraPos = action.GetTargetPosition();
                    //占领轴
                    takenAxis |= action.ownAxis;
                    //让其他在被占领轴移动的action失效
                    DisableLocalActions(i + 1, takenAxis);

                    isMoved = true;
                }

                if (enabled && action.isFinished)
                {
                    action.OnFinish();
                }
                if (action.blockOtherActions)
                {
                    action.blockOtherActions = false;
                    break;
                }
            }

            if (isMoved)
            {
                MapCamera.transform.localPosition = localCameraPos;
            }
        }

        //更新相机参数
        public static void Update()
        {
            if (!mActive)
            {
                return;
            }

            //local actions only move children of MapCamera
            UpdateLocalActions();

            mLastEnabledAction = null;
            //update global actions
            var cameraOldPos = MapCameraRoot.transform.position;
            mUpdatedCameraPosition = cameraOldPos;
            var rotation = MapCameraRoot.transform.rotation.eulerAngles;
            mUpdatedCameraYRotation = Utils.ClampAngleTo180(rotation.y);
            mUpdatedCameraXRotation = rotation.x;
            mUpdatedOrthographicSize = MapCamera.firstCamera.orthographicSize;
            mCameraRotateCenterDirty = false;
            float oldOrthographicSize = mUpdatedOrthographicSize;
            bool isMoved = false;
            bool changeViewCenter = false;
            //已经被占用的轴
            CameraMoveAxis takenAxis = CameraMoveAxis.None;

            List<CameraAction> actions = null;
            if (mUseCallOrder)
            {
                mSortedGlobalActions.Sort(mSortActions);
                actions = mSortedGlobalActions;
            }
            else
            {
                actions = mGlobalActions;
            }

            //直接让优先级更高的相机移动方案覆盖优先级低的相机移动
            for (int i = 0; i < actions.Count; ++i)
            {
                var action = actions[i];
                Debug.Assert(action.moveAxis != CameraMoveAxis.None);

                bool enabled = action.enabled;
                action.Update(mUpdatedCameraPosition, mUpdateCallback);

                if (action.enabled)
                {
                    if (mLastEnabledAction != null)
                    {
                        //这个action打断了上一个action
                        mLastEnabledAction.OnInterrupted(action.actionType);
                    }
                    mLastEnabledAction = action;
                    isMoved = true;
                    //计算相机的目标位置
                    mUpdatedCameraPosition = action.GetTargetPosition();
                    //相机移动是否会影响关注点,某些效果只是有相机移动,但不会影响地图的刷新
                    changeViewCenter |= action.affectViewCenter;

                    //占领轴
                    takenAxis |= action.ownAxis;
                    //让其他在被占领轴移动的action失效
                    DisableActions(i, takenAxis);
                }

                action.PostUpdate();

                if (enabled && action.isFinished)
                {
                    action.OnFinish();
                }
                if (action.blockOtherActions)
                {
                    action.blockOtherActions = false;
                    break;
                }
            }

            float newRotation = mHorizontalRotationUpdater.Update(mUpdatedCameraPosition, out bool rotationUpdated, out Vector3 newCameraPos);
            if (rotationUpdated )
            {
                mUpdatedCameraYRotation = newRotation;
                mUpdatedCameraPosition = newCameraPos;
                mForceUpdate = true;
            }

            if (!Mathf.Approximately(mUpdatedOrthographicSize, oldOrthographicSize) || isMoved || mForceUpdate || rotationUpdated)
            {
                MoveCamera(mUpdatedCameraPosition, changeViewCenter, mForceUpdate);
                MapCamera.orthographicSize = mUpdatedOrthographicSize;
                mForceUpdate = false;
            }
            
            if (mEnableUpdateFarClipPlane)
            {
                if (mFarClipSetting != null)
                {
                    MapCameraClipPlaneUpdate.UpdateFarClipPlane(mFarClipSetting);
                }
                else
                {
                    float farClipOffset = Map.currentMap.data.farClipOffset;
                    MapCameraClipPlaneUpdate.UpdateFarClipPlane(farClipOffset);
                }
            }
            if (mEnableUpdateNearClipPlane)
            {
                if (mNearClipSetting != null)
                {
                    MapCameraClipPlaneUpdate.UpdateNearClipPlane(mNearClipSetting);
                }
                else
                {
                    var borderHeight = Map.currentMap.data.borderHeight;
                    float offset = cameraSetting.GetCameraHeightSetting("world_min").cameraHeight + borderHeight;
                    MapCameraClipPlaneUpdate.UpdateNearClipPlane(offset);
                }
            }
            
            mStatistics.Add(MapCameraRoot.transform.position);
        }

        static void DisableActions(int startIndex, CameraMoveAxis ownedAxis)
        {
            if (mUseCallOrder)
            {
                DisableSortedGlobalActions(startIndex, ownedAxis);
            }
            else
            {
                DisableGlobalActions(startIndex + 1, ownedAxis);
            }
        }

        static void DisableGlobalActions(int startIndex, CameraMoveAxis ownedAxis)
        {
            for (int i = startIndex; i < mGlobalActions.Count; ++i)
            {
                if (!mGlobalActions[i].ignoreAxis && (mGlobalActions[i].moveAxis & ownedAxis) != 0)
                {
                    bool enabled = mGlobalActions[i].enabled;
                    mGlobalActions[i].isFinished = true;
                    if (enabled)
                    {
                        mGlobalActions[i].OnFinish();
                    }
                }
            }
        }

        //禁止使用了ownedAxis轴的action
        static void DisableSortedGlobalActions(int startIndex, CameraMoveAxis ownedAxis)
        {
            //只禁止updateOrder更高的action
            int updateOrder = mSortedGlobalActions[startIndex].updateOrder;
            for (int i = startIndex + 1; i < mSortedGlobalActions.Count; ++i)
            {
                Debug.Assert(mSortedGlobalActions[i].updateOrder >= updateOrder);

                if (mSortedGlobalActions[i].updateOrder != updateOrder &&
                    !mSortedGlobalActions[i].ignoreAxis && 
                    (mSortedGlobalActions[i].moveAxis & ownedAxis) != 0)
                {
                    bool enabled = mSortedGlobalActions[i].enabled;
                    mSortedGlobalActions[i].isFinished = true;
                    if (enabled)
                    {
                        mSortedGlobalActions[i].OnFinish();
                    }
                }
            }
        }

        static void DisableLocalActions(int startIndex, CameraMoveAxis ownedAxis)
        {
            for (int i = startIndex; i < mLocalActions.Count; ++i)
            {
                if (!mLocalActions[i].ignoreAxis && (mLocalActions[i].moveAxis & ownedAxis) != 0)
                {
                    mLocalActions[i].isFinished = true;
                }
            }
        }

        //clampBorder:是否将使用绑到相机上的曲线来控制border clamp
        internal static Vector3 ClampPosition(Vector3 cameraTargetPos, bool clampBorder, out bool clamped)
        {
            if (mBorderClamp == null)
            {
                mBorderClamp = MapCameraRoot.GetComponent<MapCameraBorderClampByCurve>();
                //在MapCamera绑的脚本上配置相机可移动范围!
                Debug.Assert(mBorderClamp != null, "Border Clamp not found!");
            }
            return mBorderClamp.ClampPosition(cameraTargetPos, clampBorder, out clamped);
        }

        //移动相机并调整视野
        //clampBorder:是否将使用绑到相机上的曲线来控制border clamp
        static void MoveCamera(Vector3 cameraTargetPos, bool changeViewCenter, bool forceUpdate)
        {
            var map = Map.currentMap;
            var lastCameraPos = MapCameraRoot.transform.position;

            if (mEnableCameraXAngleChange)
            {
                cameraTargetPos = CalculateCameraPoseFromHeight(cameraTargetPos, forceUpdate);
            }
            else
            {
                mCameraRotateCenterDirty = true;
            }

            UpdateCameraRotation(cameraTargetPos.y);

            cameraTargetPos = ClampPosition(cameraTargetPos, mClampBorder, out bool clamped);
            if (clamped)
            {
                mCameraRotateCenterDirty = true;
            }

            if (map.data.isCircleMap && MapModule.cameraCheckCircleBorderCollision)
            {
                var borderHeight = map.data.borderHeight;
                cameraTargetPos = MapCameraBorderCollisionCheck.CheckCollision(lastCameraPos, cameraTargetPos, mCameraColliderRadius, MapModule.cameraBorderColliderRadius, borderHeight);
            }

            if (mEnableClampTopDownView)
            {
                cameraTargetPos = ClampTopDownViewCameraToRange.Clamp(cameraTargetPos, MapCamera.fieldOfView, MapCamera.firstCamera.aspect, 0, 0, Map.currentMap.mapWidth, Map.currentMap.mapHeight);
            }

            UpdateHeightChange(cameraTargetPos.y);

            if (changeViewCenter || forceUpdate)
            {
                if (!MapModule.is2DGame)
                {
                    cameraSetting.SetCameraFOV(cameraTargetPos.y, 0);
                }
                Vector3 lookAtPosition;
                if (mCameraRotateCenterDirty || forceUpdate)
                {
                    lookAtPosition = map.GetViewCenterFromCamera(MapCamera.transform.forward, cameraTargetPos);
                    mCameraRotateCenter = lookAtPosition;
                }
                else
                {
                    lookAtPosition = mCameraRotateCenter;
                }

                var zoom = map.CalculateCameraZoom(cameraTargetPos.y);
                if (mOnCameraPositionChanging != null)
                {
                    mOnCameraPositionChanging(lookAtPosition, cameraTargetPos.y, zoom);
                }

                mCurrentCameraHeight = cameraTargetPos.y;
                mLastCameraHeight = lastCameraPos.y;
            }
        }

        static void UpdateCameraRotation(float cameraHeight)
        {
            var cameraRoot = MapCameraRoot.transform;

            if (cameraHeight > mActiveCameraSetting.maxHorizontalRotationHeight)
            {
                //相机左右旋转自动恢复
                mHorizontalRotationUpdater.Start(mUpdatedCameraYRotation, MapObjectUtils.NeedRotation ? 180f : 0, mActiveCameraSetting.horizontalRotationRestoreSpeed);
            }
 
            Quaternion rotation = Quaternion.Euler(mUpdatedCameraXRotation, mUpdatedCameraYRotation, 0);
            
            cameraRoot.rotation = rotation;
        }

        //如果启用了x角度可变功能,需要根据相机高度初始化相机角度和位置
        static void CalculateInitCameraPose()
        {
            var cameraRoot = MapCameraMgr.MapCameraRoot.transform;
            cameraRoot.transform.position = CalculateCameraPoseFromHeight(cameraRoot.transform.position, true);
            cameraRoot.transform.rotation = Quaternion.Euler(mUpdatedCameraXRotation, mUpdatedCameraYRotation, 0);
            mForceUpdate = true;
        }

        //根据相机高度计算x angle
        static Vector3 CalculateCameraPoseFromHeight(Vector3 newCameraPos, bool forceUpdate)
        {
            if (Utils.IsCurveEmpty(mCurveMovementSetting.forwardOffsetCurveBelowSplitHeight) && Utils.IsCurveEmpty(mCurveMovementSetting.forwardOffsetCurveAboveSplitHeight))
            {
                //no curve movement
                return newCameraPos;
            }

            bool rotateCenterNotInited = mCameraRotateCenter.x < -5000;
            float newCameraHeight = newCameraPos.y;
            if (!Mathf.Approximately(newCameraHeight, mLastCameraHeight) || rotateCenterNotInited || forceUpdate)
            {
                if (newCameraHeight >= mCurveMovementSetting.splitHeight && newCameraHeight < mCurveMovementSetting.maxHeight)
                {
                    if (Utils.IsCurveEmpty(mCurveMovementSetting.forwardOffsetCurveAboveSplitHeight))
                    {
                        mUpdatedCameraXRotation = cameraSetting.rotationXAngle;
                        return newCameraPos;
                    }
                    else
                    {
                        return CalculateCameraPoseFromHeightAboveSplitHeight(newCameraHeight, rotateCenterNotInited);
                    }
                }
                else if (mCurveMovementSetting.maxHeight != 0 && newCameraHeight >= mCurveMovementSetting.maxHeight)
                {
                    return CalculateCameraPoseFromHeightAboveMaxHeight(newCameraHeight, rotateCenterNotInited);
                }
                else
                {
                    if (!Utils.IsCurveEmpty(mCurveMovementSetting.forwardOffsetCurveBelowSplitHeight))
                    {
                        if (newCameraHeight >= mCurveMovementSetting.splitHeight)
                        {
                            mUpdatedCameraXRotation = cameraSetting.rotationXAngle;
                            return newCameraPos;
                        }
                        else
                        {
                            return CalculateCameraPoseFromHeightBelowSplitHeight(newCameraHeight, rotateCenterNotInited);
                        }
                    }
                }
            }
            return newCameraPos;
        }

        static void UpdateHeightChange(float newCameraHeight)
        {
            if (mZoomAlgorithmSwitchHeight > 0 && mZoom != null)
            {
                if ((MapCameraMgr.lastCameraHeight > mZoomAlgorithmSwitchHeight && newCameraHeight < mZoomAlgorithmSwitchHeight) ||
                    (MapCameraMgr.lastCameraHeight < mZoomAlgorithmSwitchHeight && newCameraHeight > mZoomAlgorithmSwitchHeight))
                {
                    mZoom.OnCameraHeightPassZoomAlgorithmChangeHeight();
                }
            }
        }

        static Vector3 CalculateCameraPoseFromHeightBelowSplitHeight(float newCameraHeight, bool rotateCenterNotInited)
        {
            var map = Map.currentMap;

            float maxCameraHeightWhenXAngleIsMaximum = mCurveMovementSetting.splitHeight;
            if (maxCameraHeightWhenXAngleIsMaximum == 0)
            {
                maxCameraHeightWhenXAngleIsMaximum = cameraSetting.cameraMaxHeight;
            }

            float minCameraHeight = currentMinimumHeight;
            newCameraHeight = Mathf.Clamp(newCameraHeight, currentMinimumHeight, maxCameraHeightWhenXAngleIsMaximum);
            float t = Mathf.Clamp01((newCameraHeight - minCameraHeight) / (maxCameraHeightWhenXAngleIsMaximum - minCameraHeight));
            float zOffset = mCurveMovementSetting.forwardOffsetCurveBelowSplitHeight.Evaluate(t);
            float xRot = cameraSetting.rotationXAngle;
            float yRot = mUpdatedCameraYRotation;

            if (rotateCenterNotInited)
            {
                //rotate center在旋转过程中不能改变
                mCameraRotateCenter = Map.currentMap.viewCenter;
            }

            Vector3 cameraPosWhenReachMaxHeight = map.CalculateCameraPositionFromLookAtPositionWithRotation(mCameraRotateCenter.x, mCameraRotateCenter.z, maxCameraHeightWhenXAngleIsMaximum, xRot, yRot);
            Vector3 cameraPosWhenReachMinHeight = map.CalculateCameraPositionFromLookAtPositionWithRotation(mCameraRotateCenter.x, mCameraRotateCenter.z, minCameraHeight, xRot, yRot);
            Vector3 f = Quaternion.Euler(0, mUpdatedCameraYRotation, 0) * Vector3.forward;
            var d = cameraPosWhenReachMinHeight - cameraPosWhenReachMaxHeight;
            float maxOffset = (Vector3.Project(d, f)).magnitude;
            zOffset = Mathf.Clamp01(zOffset) * maxOffset;
            Vector3 currentCameraPosIfNoXAngleChange = map.CalculateCameraPositionFromLookAtPositionWithRotation(mCameraRotateCenter.x, mCameraRotateCenter.z, newCameraHeight, xRot, yRot);

            var d1 = currentCameraPosIfNoXAngleChange - cameraPosWhenReachMaxHeight;
            Vector3 proj = Vector3.Project(d1, f);
            float delta = Mathf.Max(0, proj.magnitude - zOffset);
            var cameraPos = currentCameraPosIfNoXAngleChange - f * delta;
            Vector3 viewCenterToCamera = (mCameraRotateCenter - cameraPos).normalized;
            float xAngle = Vector3.Angle(f, viewCenterToCamera);
            mUpdatedCameraXRotation = xAngle;

            //Debug.LogError($"camera height: {newCameraHeight}, zOffset: {zOffset}, f: {f}, maxOffset: {maxOffset}, Angle: {xAngle}");

            return cameraPos;
        }

        static Vector3 CalculateCameraPoseFromHeightAboveSplitHeight(float newCameraHeight, bool rotateCenterNotInited)
        {
            var map = Map.currentMap;

            float t = Mathf.Clamp01((newCameraHeight - mCurveMovementSetting.splitHeight) / (mCurveMovementSetting.maxHeight - mCurveMovementSetting.splitHeight));
            float zOffset = mCurveMovementSetting.forwardOffsetCurveAboveSplitHeight.Evaluate(t);
            float xRot = cameraSetting.rotationXAngle;
            float yRot = mUpdatedCameraYRotation;

            if (rotateCenterNotInited)
            {
                //rotate center在旋转过程中不能改变
                mCameraRotateCenter = Map.currentMap.viewCenter;
            }

            Vector3 cameraPosWhenReachSplitHeight = map.CalculateCameraPositionFromLookAtPositionWithRotation(mCameraRotateCenter.x, mCameraRotateCenter.z, mCurveMovementSetting.splitHeight, xRot, yRot);
            Vector3 cameraPosWhenReachCurveMovementMaxHeight = map.CalculateCameraPositionFromLookAtPositionWithRotation(mCameraRotateCenter.x, mCameraRotateCenter.z, mCurveMovementSetting.maxHeight, mCurveMovementSetting.xRotationAtMaxHeight, yRot);

            Vector3 f = Quaternion.Euler(0, mUpdatedCameraYRotation, 0) * Vector3.forward;
            var d = cameraPosWhenReachSplitHeight - cameraPosWhenReachCurveMovementMaxHeight;
            float maxOffset = (Vector3.Project(d, f)).magnitude;
            zOffset = Mathf.Clamp01(zOffset) * maxOffset;

            var lerpPos = Vector3.Lerp(cameraPosWhenReachSplitHeight, cameraPosWhenReachCurveMovementMaxHeight, t);
            var d1 = lerpPos - cameraPosWhenReachCurveMovementMaxHeight;
            Vector3 proj = Vector3.Project(d1, f);
            float delta = Mathf.Max(0, proj.magnitude - zOffset);
            var cameraPos = lerpPos - f * delta;

            Vector3 viewCenterToCamera = (mCameraRotateCenter - cameraPos).normalized;
            float xAngle = Vector3.Angle(f, viewCenterToCamera);
            mUpdatedCameraXRotation = xAngle;

            //Debug.LogError($"camera height: {newCameraHeight}, zOffset: {zOffset}, f: {f}, maxOffset: {maxOffset}, Angle: {xAngle}");

            return cameraPos;
        }

        static Vector3 CalculateCameraPoseFromHeightAboveMaxHeight(float newCameraHeight, bool rotateCenterNotInited)
        {
            var map = Map.currentMap;

            float yRot = mUpdatedCameraYRotation;

            if (rotateCenterNotInited)
            {
                //rotate center在旋转过程中不能改变
                mCameraRotateCenter = Map.currentMap.viewCenter;
            }

            Vector3 cameraPos = map.CalculateCameraPositionFromLookAtPositionWithRotation(mCameraRotateCenter.x, mCameraRotateCenter.z, newCameraHeight, mCurveMovementSetting.xRotationAtMaxHeight, yRot);
            mUpdatedCameraXRotation = mCurveMovementSetting.xRotationAtMaxHeight;

            //Debug.LogError($"camera height: {newCameraHeight}, zOffset: {zOffset}, f: {f}, maxOffset: {maxOffset}, Angle: {xAngle}");

            return cameraPos;
        }

        static async UniTask SetActiveCameraSetting(MapCameraSetting setting)
        {
            Debug.Assert(setting != null);
            mActiveCameraSetting = setting;

            if (!string.IsNullOrEmpty(setting.xBorderClampConfigPath))
            {
                mBorderClamp.xClampConfig = await MapModuleResourceMgr.LoadResourceAsync<BorderClampConfig>(setting.xBorderClampConfigPath, null);
                {
                    //mBorderClamp.xClampConfig = clampConfig;
                    //MapModuleResourceMgr.UnloadAsset(setting.xBorderClampConfigPath);
                }
            }
            else
            {
                mBorderClamp.xClampConfig = null;
            }

            if (!string.IsNullOrEmpty(setting.zBorderClampConfigPath))
            {
                mBorderClamp.zClampConfig = await MapModuleResourceMgr.LoadResourceAsync<BorderClampConfig>(setting.zBorderClampConfigPath, null);
                {
                    //mBorderClamp.zClampConfig = clampConfig;
                    //MapModuleResourceMgr.UnloadAsset(setting.zBorderClampConfigPath);
                }
               
            }
            else
            {
                mBorderClamp.zClampConfig = null;
            }

            for (int i = 0; i < mGlobalActions.Count; ++i)
            {
                mGlobalActions[i].OnCameraSettingChange(setting);
            }

            mUpdatedCameraXRotation = setting.rotationXAngle;
            mUpdatedCameraYRotation = setting.rotationYAngle;
            MapCameraRoot.transform.rotation = Quaternion.Euler(mUpdatedCameraXRotation, mUpdatedCameraYRotation, 0);
            //UnityEngine.Debug.LogError($" MapCameraMgr.MapCameraRoot.transform.localRotation:{MapCameraMgr.MapCameraRoot.transform.localEulerAngles}");
        }

        //相机移动到终点时回调
        static void OnCameraReachToTarget()
        {
            EnableCameraCollisionCheck(true);

            if (CameraStopMoveToTargetCallback != null)
            {
                CameraStopMoveToTargetCallback();
            }
        }

        //相机在移动过程中被中断时调用
        static void InterruptCameraMoveToTarget(CameraActionType type)
        {
            EnableCameraCollisionCheck(true);

            if (CameraStopMoveToTargetCallback != null)
            {
                CameraStopMoveToTargetCallback();
            }
        }

        //相机移动到终点时回调
        static void OnCameraReachToTargetRev()
        {
            if (CameraStopMoveToTargetRevCallback != null)
            {
                CameraStopMoveToTargetRevCallback();
            }
        }

        //相机在移动过程中被中断时调用
        static void InterruptCameraMoveToTargetRev(CameraActionType type)
        {
            if (CameraStopMoveToTargetRevCallback != null)
            {
                CameraStopMoveToTargetRevCallback();
            }
        }

        static void FinishCameraFollowTarget()
        {
            if (CameraStopFollowTargetCallback != null)
            {
                CameraStopFollowTargetCallback();
            }
        }

        //设置相机拖动的范围, width或height为0时取消范围限制
        public static void SetDragRange(float x, float y, float width, float height)
        {
            var action = GetGlobalAction(typeof(CameraDrag)) as CameraDrag;
            if (action != null)
            {
                action.SetRange(new Rect(x, y, width, height));
            }
        }

        public static void SetBorderClampConfig(BorderClampConfig xClampConfig, BorderClampConfig zClampConfig)
        {
            if (xClampConfig == null)
            {
                xClampConfig = MapModuleResourceMgr.LoadResource<BorderClampConfig>(cameraSetting.xBorderClampConfigPath);
            }
            if (zClampConfig == null)
            {
                zClampConfig = MapModuleResourceMgr.LoadResource<BorderClampConfig>(cameraSetting.zBorderClampConfigPath);
            }
            mBorderClamp.xClampConfig = xClampConfig;
            mBorderClamp.zClampConfig = zClampConfig;
        }

        internal static void InvokeStartMovingCallback()
        {
            if (CameraStartMoveToTargetCallback != null)
            {
                CameraStartMoveToTargetCallback();
            }
        }

        internal static void InvokeStartMovingRevCallback()
        {
            if (CameraStartMoveToTargetRevCallback != null)
            {
                CameraStartMoveToTargetRevCallback();
            }
        }

        public static void SaveMoveToTargetState(bool keepOriginalDuration)
        {
            var action = GetGlobalAction(CameraActionType.MoveToTarget) as CameraMoveToTarget;
            if (action != null)
            {
                action.SaveState(keepOriginalDuration);
            }
        }

        public static void RestoreMoveToTargetState(bool angleChanged)
        {
            var action = GetGlobalAction(CameraActionType.MoveToTarget) as CameraMoveToTarget;
            if (action != null)
            {
                action.RestoreState(angleChanged);
            }
        }

        internal static void UpdateRotateCenter()
        {
            mCameraRotateCenterDirty = true;
        }

        internal static void Reset()
        {
            mCameraRotateCenter = new Vector3(-10000, 0, 0);
            mCameraRotateCenterDirty = false;
        }

        internal static int GetActionUpdateOrder(CameraActionType type)
        {
            Debug.Assert(mTypeToUpdateOrder.ContainsKey(type), $"Uknown Update Order for {type}");
            return mTypeToUpdateOrder[type];
        }

        internal static float updatedCameraYRotation { get { return mUpdatedCameraYRotation; }  
            set {
                mHorizontalRotationUpdater.Cancel();
                mUpdatedCameraYRotation = value;
            } 
        }
        internal static float updatedCameraXRotation { get { return mUpdatedCameraXRotation; } set { mUpdatedCameraXRotation = value; } }
        internal static float updatedOrthographicSize { get { return mUpdatedOrthographicSize; } set { mUpdatedOrthographicSize = value; } }
        internal static KeepScaleConfig buildingScaleConfig { get { return mActiveCameraSetting.buildingScaleConfig; } }
        internal static CameraClipPlaneSetting nearClipPlaneSetting { get { return mNearClipSetting; } set { mNearClipSetting = value; } }
        internal static CameraClipPlaneSetting farClipPlaneSetting { get { return mFarClipSetting; } set { mFarClipSetting = value; } }
        public static CameraZoomCurveSetting cameraZoomCurveSetting { get { return mCameraZoomCurveSetting; } set { mCameraZoomCurveSetting = value; } }
        internal static float minOrthoSize { get { return m2DSetting.minOrthoSize; } }
        internal static float maxOrthoSize { get { return m2DSetting.maxOrthoSize; } }
        public static float minDelta { get; private set; } = 0.001f;  // 相机移动到目标位置时，最小的判断距离。小于该距离认为当前值就等于目标值。

        //相机的行为列表
        //updateOrder值越低越靠前,updateOrder值相同时先调用的action排前面,例如MoveToTarget和MoveToTargetRev优先级相同,先调用哪个就先更新哪个,后面的调用会覆盖前面的action
        static List<CameraAction> mSortedGlobalActions = new List<CameraAction>();
        //updateOrder相同的callOrder越小越靠前更新
        static int mCallOrder;
        static List<CameraAction> mGlobalActions = new List<CameraAction>();
        static List<CameraAction> mLocalActions = new List<CameraAction>();
        static CameraAction mLastEnabledAction;
        static Action<Vector3, float, float> mOnCameraPositionChanging;
        //上一次更新时相机的高度
        static float mLastCameraHeight;
        //相机更新后的高度
        static float mCurrentCameraHeight;
        //是否限制相机的移动范围
        static bool mClampBorder;
        //当前相机能到达的最低高度
        static float mCurrentMinimumHeight;
        static bool mIsMovingToTarget = false;
        //相机的碰撞半径
        static float mCameraColliderRadius = 1.0f;
        static bool mEnableUpdateFarClipPlane = true;
        static bool mEnableUpdateNearClipPlane = false;
        static CameraClipPlaneSetting mNearClipSetting;
        static CameraClipPlaneSetting mFarClipSetting;
        static CameraZoomCurveSetting mCameraZoomCurveSetting;
        static Dictionary<string, MapCameraSetting> mCameraHeightSettings;
        static MapCameraSetting mActiveCameraSetting;
        static CameraStatistics mStatistics;
        static CameraZoomAndPan mZoom;
        //中间计算的最新相机位置
        static Vector3 mUpdatedCameraPosition;
        static float mUpdatedCameraYRotation = 0;
        static float mUpdatedCameraXRotation = 0;
        //是否允许相机高度改变时修改相机角度
        static bool mEnableCameraXAngleChange = false;
        static bool mCameraRotateCenterDirty = true;
        static Vector3 mCameraRotateCenter = new Vector3(-10000, 0, 0);
        static CameraCurveMovementSetting mCurveMovementSetting = new CameraCurveMovementSetting();
        //中间计算的最新相机orthographic size
        static float mUpdatedOrthographicSize;
        static MapCameraBorderClampByCurve mBorderClamp;
        static MapCameras mMapCameras;

        static Camera2DSetting m2DSetting = new Camera2DSetting();
        static bool mActive = true;
        static bool mForceUpdate = false;
        static bool mEnableClampTopDownView = false;
        //如果为true,则相同update order的action会按call order排序,否则直接使用global action的更新顺序
        static bool mUseCallOrder = false;
        //如果mZoomAlgorithmSwitchHeight != 0, 相机超过mZoomAlgorithmSwitchHeight就切换缩放算法
        static float mZoomAlgorithmSwitchHeight = 0;
        static bool mIsDragging = false;
        static SortActions mSortActions = new SortActions();
        static CameraHorizontalRotationAutoUpdater mHorizontalRotationUpdater = new CameraHorizontalRotationAutoUpdater();

        static System.Action<CameraAction, int> mUpdateCallback;
        /// <summary>
        /// 当前是否处于多点触控标记
        /// </summary>
        private static bool isTwoTouchZooming;
        /// <summary>
        /// 当前是否处于多点触控标记
        /// </summary>
        public static bool IsTwoTouchZooming
        {
            get
            {
                return isTwoTouchZooming;
            }
            set
            {
                isTwoTouchZooming = value;
                //Debug.LogFormat("isTwoTouchZooming={0}", isTwoTouchZooming);
            }
        }
    }
}
