﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    class GroundTileMakerAddLODDialog : EditorWindow
    {
        class TextureItem
        {
            public string propertyName = "";
            public int textureResolution = 512;
            public bool initChannelData = true;
            public bool normalizeColor = true;
        }

        public void Show(GroundTileMaker maker)
        {
            mMaker = maker;
        }

        void OnGUI()
        {
            mCopyTextureDataFromLastLOD = EditorGUILayout.ToggleLeft("Copy Texture Data From Last LOD", mCopyTextureDataFromLastLOD);
            var newMtl = EditorGUILayout.ObjectField("Material", mMaterial, typeof(Material), false, null) as Material;
            if (newMtl != null)
            {
                if (newMtl != mMaterial)
                {
                    mMaterial = newMtl;
                    mTexturePropertyNames = Utils.GetTexturePropertyNames(mMaterial);
                    mSelectedPropertyIndex = 0;
                }
                DrawTextureSelectionGUI();
            }
            else
            {
                EditorGUILayout.LabelField("Please select a material first!");
            }


            DrawTextures();

            if (GUILayout.Button("Create"))
            {
                string errorMsg = CheckValidation();
                if (string.IsNullOrEmpty(errorMsg))
                {
                    int n = mTextures != null ? mTextures.Count : 0;
                    List<GroundTileMaker.MaskTextureSetting> settings = new List<GroundTileMaker.MaskTextureSetting>();
                    for (int i = 0; i < n; ++i)
                    {
                        var setting = new GroundTileMaker.MaskTextureSetting() { shaderPropertyName = mTextures[i].propertyName, resolution = mTextures[i].textureResolution, initChannelData = mTextures[i].initChannelData, normalizeColor = mTextures[i].normalizeColor };
                        settings.Add(setting);
                    }

                    string materialPath = AssetDatabase.GetAssetPath(mMaterial);
                    var lodMaterialSetting = new GroundTileMaker.GroundLODMaterialSetting(materialPath, settings);

                    mMaker.AddLOD(materialPath, settings, mCopyTextureDataFromLastLOD);

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", errorMsg, "OK");
                }
            }
        }

        void DrawTextureSelectionGUI()
        {
            if (mTexturePropertyNames.Length > 0)
            {
                EditorGUILayout.BeginHorizontal();
                mSelectedPropertyIndex = EditorGUILayout.Popup("Texture Property", mSelectedPropertyIndex, mTexturePropertyNames);
                if (GUILayout.Button("Add"))
                {
                    AddTextureItem(mTexturePropertyNames[mSelectedPropertyIndex]);
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        void AddTextureItem(string propertyName)
        {
            for (int i = 0; i < mTextures.Count; ++i)
            {
                if (mTextures[i].propertyName == propertyName)
                {
                    EditorUtility.DisplayDialog("Error", "Texture is already added!", "OK");
                    return;
                }
            }
            var item = new TextureItem();
            item.initChannelData = true;
            item.normalizeColor = true;
            item.propertyName = propertyName;
            item.textureResolution = 1024;
            mTextures.Add(item);
        }

        void RemoveTextureItem(int index)
        {
            mTextures.RemoveAt(index);
        }

        void DrawTextures()
        {
            if (mTextures != null)
            {
                for (int i = 0; i < mTextures.Count; ++i)
                {
                    if (mTextures[i] != null)
                    {
                        EditorGUILayout.BeginVertical("GroupBox");
                        mTextures[i].textureResolution = EditorGUILayout.IntField("Texture Size", mTextures[i].textureResolution);
                        EditorGUILayout.TextField("Texture Property Name", mTextures[i].propertyName);
                        mTextures[i].initChannelData = EditorGUILayout.ToggleLeft("Init Channel Data", mTextures[i].initChannelData);
                        mTextures[i].normalizeColor = EditorGUILayout.ToggleLeft("Normalize Color", mTextures[i].normalizeColor);
                        if (GUILayout.Button("Remove"))
                        {
                            RemoveTextureItem(i);
                            break;
                        }
                        EditorGUILayout.EndVertical();
                    }
                }
            }
        }

        string CheckValidation()
        {
            if (mMaterial == null)
            {
                return "invalid material";
            }

            if (mTextures == null || mTextures.Count == 0)
            {
                return "invalid texture";
            }

            for (int i = 0; i < mTextures.Count; ++i)
            {
                if (mTextures[i] == null)
                {
                    return $"invalid texture {i}";
                }
                if (string.IsNullOrEmpty(mTextures[i].propertyName))
                {
                    return $"invalid texture {i} property name";
                }

                var texture = mMaterial.GetTexture(mTextures[i].propertyName);
                if (texture == null)
                {
                    return $"invalid texture {i} property name {mTextures[i].propertyName}";
                }

                if (!Mathf.IsPowerOfTwo(mTextures[i].textureResolution))
                {
                    return $"{mTextures[i].propertyName} texture size must be power of 2!";
                }
            }

            return "";
        }

        Material mMaterial;
        List<TextureItem> mTextures = new List<TextureItem>();
        GroundTileMaker mMaker;
        string[] mTexturePropertyNames;
        int mSelectedPropertyIndex = -1;
        bool mCopyTextureDataFromLastLOD = true;
    }
}
#endif