fileFormatVersion: 2
guid: 4e39d7a07179f904fa6b89061b953d3f
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Enzo_Armor_MAT
    second: {fileID: 2100000, guid: c892d776d18dc414c8680034a150ffe2, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Enzo_Bottom
    second: {fileID: 2100000, guid: 6fd3070e2c194f14c8554d57d966e791, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Enzo_Details_MAT
    second: {fileID: 2100000, guid: 9c9c1dd8441471b469a7387af618204a, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Enzo_Glass
    second: {fileID: 2100000, guid: 3127193e126a0734997650095e31babd, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Enzo_Main_MAT
    second: {fileID: 2100000, guid: 20ad2f56615707f419dbc0434f91ce96, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Enzo_Rims
    second: {fileID: 2100000, guid: 38f1cc1f575e74545b0eb352768e05ec, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Enzo_TireTread
    second: {fileID: 2100000, guid: 90602b4fa55663e4fb0c912d0d83b3a6, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Grate
    second: {fileID: 2100000, guid: 96166ef75013ee64b956fe585c6e1aaa, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Weapons_Homing_MissileLauncher_MAT
    second: {fileID: 2100000, guid: 024635f9d9dc277408aa52b595520698, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Weapons_Minigun_MAT
    second: {fileID: 2100000, guid: 6dc9d122e934d254cafa6d3604576342, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Weapons_missile_Launcher_MAT
    second: {fileID: 2100000, guid: af1e289006b0cad46a064d1002c42ebc, type: 2}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Car_EnzoBody
    second: {fileID: 2800000, guid: 36e75e007b851424b80a94bb93fcb096, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Car_EnzoDetails
    second: {fileID: 2800000, guid: 6df0089f118055d41a5eab75482dd586, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: EnzoTires
    second: {fileID: 2800000, guid: 395a2dceb1a8d6d48a4482e7fbead9cd, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Enzo_Armor
    second: {fileID: 2800000, guid: a717ee76a2846534ca1597852c9c299d, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Enzo_Armor_NM
    second: {fileID: 2800000, guid: 2ff4a061ab2cea349ad6600811f96c5c, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Enzo_Armor_Spec
    second: {fileID: 2800000, guid: d530951cacfa83742a5ae3b209576551, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Grate
    second: {fileID: 2800000, guid: 3bb590c1cb931b349b7d42576a22e178, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: HomingMissileLauncher
    second: {fileID: 2800000, guid: fde6a968620a7eb4e905e66f6b95dc28, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: HomingMissileLauncher_Spec
    second: {fileID: 2800000, guid: 8ad3269964150304db99e991f8c0f32e, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: MiniGun
    second: {fileID: 2800000, guid: 3df1d1f0ec6667344ab8e0042a41719f, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: MiniGunS
    second: {fileID: 2800000, guid: b0a803a7caf36c949ad88b7d9c5d46ac, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: MissileLauncher
    second: {fileID: 2800000, guid: 19f1f180eab55b34b80d9a013d962171, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: MissileLauncherS
    second: {fileID: 2800000, guid: 79283a15cb3df4e48a71d0f2f34642d6, type: 3}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 1
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 0
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 0.01
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
