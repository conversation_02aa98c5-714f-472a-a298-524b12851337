﻿ 



 
 


#if UNITY_EDITOR

namespace TFW.Map
{
    public partial class DecorationBorderLayer : EditorComplexGridModelLayer
    {
        public DecorationBorderLayer(Map map) : base(map) { }

        public override PrefabManager GetPrefabManager()
        {
            var editorMapData = map.data as EditorMapData;
            return editorMapData.complexGridModelLayerPrefabManager;
        }
    }
}


#endif