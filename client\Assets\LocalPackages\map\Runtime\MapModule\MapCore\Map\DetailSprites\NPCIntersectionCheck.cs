﻿ 



 
 

﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class DetailSprites
    {
        List<DynamicObject> mObjects = new List<DynamicObject>();

        class DynamicObject
        {
            public long id;
            public float x;
            public float z;
            public float radius;

            public bool IntersectWithCircle(float x, float z, float radius)
            {
                var dx = this.x - x;
                var dz = this.z - z;
                double dis = dx * dx + dz * dz;
                double r2 = this.radius + radius;
                return dis <= r2 * r2;
            }
        }

        public void OnShowNPC(long npcID, float x, float z, float radius)
        {
            if (mSubGridObjects == null)
            {
                return;
            }
            Debug.Assert(GetNPCIndex(npcID) == -1);
            var obj = new DynamicObject();
            obj.id = npcID;
            obj.x = x;
            obj.z = z;
            obj.radius = radius;
            mObjects.Add(obj);

            //hide overlapped objects
            for (int i = mSprites.Count - 1; i >= 0; --i)
            {
                var sprite = mSprites[i];
                var objPos = sprite.obj.transform.position;
                var spriteGroup = mSpriteGroups[sprite.groupIndex];
                if (obj.IntersectWithCircle(objPos.x, objPos.z, spriteGroup.spriteRadius[sprite.type]))
                {
                    ReleaseSprite(sprite);
                }
            }
        }

        public void OnHideNPC(long npcID)
        {
            if (mSubGridObjects == null)
            {
                return;
            }
            var idx = GetNPCIndex(npcID);
            if (idx >= 0)
            {
                mObjects.RemoveAt(idx);
            }
            //if (idx < 0)
            //{
            //    Debug.Assert(false);
            //}
        }

        int GetNPCIndex(long npcID)
        {
            for (int i = 0; i < mObjects.Count; ++i)
            {
                if (mObjects[i].id == npcID)
                {
                    return i;
                }
            }
            return -1;
        }

        bool IsIntersectedWithAnyDynamicObject(float spriteX, float spriteZ, float radius)
        {
            for (int i = 0; i < mObjects.Count; ++i)
            {
                if (mObjects[i].IntersectWithCircle(spriteX, spriteZ, radius))
                {
                    return true;
                }
            }
            return false;
        }
    }
}
