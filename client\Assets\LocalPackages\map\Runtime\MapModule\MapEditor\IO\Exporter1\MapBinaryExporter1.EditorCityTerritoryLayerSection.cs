﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        bool SaveEditorTerritoryLayer(BinaryWriter writer, EditorTerritoryLayer layer)
        {
            BeginSection(MapDataSectionType.CityTerritoryLayer, writer);
            //版本号
            writer.Write(VersionSetting.CityTerritoryLayerStructVersion);

            //-----------------version 1 start------------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return false;
            }

            var subLayer0 = layer.subLayers[0];
            int horizontalTileCount = subLayer0.horizontalTileCount;
            int verticalTileCount = subLayer0.verticalTileCount;
            float tileWidth = subLayer0.tileWidth;
            float tileHeight = subLayer0.tileHeight;
            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);
            writer.Write(verticalTileCount);
            writer.Write(horizontalTileCount);
            writer.Write(tileWidth);
            writer.Write(tileHeight);

            var territories = subLayer0.territories;
            writer.Write(territories.Count);
            for (int i = 0; i < territories.Count; ++i)
            {
                writer.Write(territories[i].id);
                //save buildings
                var buildings = territories[i].buildings;
                writer.Write(buildings.Count);
                for (int b = 0; b < buildings.Count; ++b)
                {
                    Utils.WriteVector3(writer, buildings[b].gameObject.transform.position);
                }

                //asset path
                string assetPrefixPath = layer.GetAssetPathLOD0(SLGMakerEditor.instance.exportFolder, 0, territories[i].id);
                Utils.WriteString(writer, assetPrefixPath);
                //mesh position
                var center = subLayer0.GetTerritoryCenter(territories[i].id);
                Utils.WriteVector3(writer, center);
                //mesh bounds
                var worldBounds = subLayer0.GetTerritoryWorldBounds(territories[i].id);
                Utils.WriteRect(writer, worldBounds);
            }

            var grids = subLayer0.grids;
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    short territoryIndex = subLayer0.GetTerritoryIndex(grids[i, j]);
                    writer.Write(territoryIndex);
                }
            }
           
            SaveMapLayerLODConfig(writer, layer.lodConfig);
            //-----------------version 1 end------------------------------
            //-----------------version 2 start------------------------------
            for (int i = 0; i < territories.Count; ++i)
            {
                Utils.WriteColor(writer, territories[i].GetColor());
            }
            //-----------------version 2 end------------------------------
            //-----------------version 3 start------------------------------
            int subLayerCount = layer.subLayers.Count - 1;
            writer.Write(subLayerCount);
            for (int s = 1; s <= subLayerCount; ++s)
            {
                var subLayer = layer.subLayers[s];
                horizontalTileCount = subLayer.horizontalTileCount;
                verticalTileCount = subLayer.verticalTileCount;
                tileWidth = subLayer.tileWidth;
                tileHeight = subLayer.tileHeight;
                writer.Write(verticalTileCount);
                writer.Write(horizontalTileCount);
                writer.Write(tileWidth);
                writer.Write(tileHeight);

                territories = subLayer.territories;
                writer.Write(territories.Count);
                for (int i = 0; i < territories.Count; ++i)
                {
                    writer.Write(territories[i].id);
                    //save buildings
                    var buildings = territories[i].buildings;
                    writer.Write(buildings.Count);
                    for (int b = 0; b < buildings.Count; ++b)
                    {
                        Utils.WriteVector3(writer, buildings[b].gameObject.transform.position);
                    }

                    //asset path
                    string assetPrefixPath = layer.GetAssetPathLOD0(SLGMakerEditor.instance.exportFolder, s, territories[i].id);
                    Utils.WriteString(writer, assetPrefixPath);
                    //mesh position
                    var center = subLayer0.GetTerritoryCenter(territories[i].id);
                    Utils.WriteVector3(writer, center);
                    //mesh bounds
                    var worldBounds = subLayer0.GetTerritoryWorldBounds(territories[i].id);
                    Utils.WriteRect(writer, worldBounds);

                    Utils.WriteColor(writer, territories[i].GetColor());
                }

                grids = subLayer.grids;
                for (int i = 0; i < verticalTileCount; ++i)
                {
                    for (int j = 0; j < horizontalTileCount; ++j)
                    {
                        short territoryIndex = subLayer.GetTerritoryIndex(grids[i, j]);
                        writer.Write(territoryIndex);
                    }
                }
            }
            //-----------------version 3 end------------------------------

            return true;
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerLODConfig config)
        {
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
            }
        }
    }
}

#endif