﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    [CustomEditor(typeof(AnimatorWrapper))]
    public class EditorAnimatorController : UnityEditor.Editor
    {
        void OnEnable()
        {
            var wrapper = target as AnimatorWrapper;
            mAnimator = wrapper.animator as CustomAnimatorBase;
            if (mAnimator != null)
            {
                var statemachine = mAnimator.statemachine;
                if (statemachine != null)
                {
                    var states = statemachine.states;
                    mStateNames = new string[states.Length];
                    mInterruptionStateNames = new string[states.Length];
                    for (int i = 0; i < states.Length; ++i)
                    {
                        mStateNames[i] = states[i].animStateInfo.stateName;
                        mInterruptionStateNames[i] = states[i].animStateInfo.stateName;
                    }
                }
            }
        }

        public override void OnInspectorGUI()
        {
            if (mStateNames != null && mAnimator != null)
            {
                mStateIndex = EditorGUILayout.Popup("State", mStateIndex, mStateNames);
                mInterruptionStateIndex = EditorGUILayout.Popup("Interrupt State", mInterruptionStateIndex, mInterruptionStateNames);
                mBlendingDuration = EditorGUILayout.FloatField("Blending Duration(seconds)", mBlendingDuration);
                mOffset = EditorGUILayout.FloatField("Next Animation Start Offset(seconds)", mOffset);
                mGlobalAnimationBlendingState = (GlobalAnimationBlendingState)EditorGUILayout.EnumPopup("Global Animation Blending State", mGlobalAnimationBlendingState);
                AnimatorWrapper.SetGlobalAnimationBlendingState(mGlobalAnimationBlendingState);

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Play"))
                {
                    if (mStateIndex >= 0)
                    {
                        mAnimator.PlayInFixedTime(mStateNames[mStateIndex], 0, mOffset);
                    }
                }

                if (GUILayout.Button("Play With Blending"))
                {
                    if (mStateIndex >= 0)
                    {
                        if (mAnimator.IsInTransition(0))
                        {
                            mAnimator.CrossFadeInFixedTime(mInterruptionStateNames[mInterruptionStateIndex], mBlendingDuration, 0, mOffset);
                        }
                        else
                        {
                            mAnimator.CrossFadeInFixedTime(mStateNames[mStateIndex], mBlendingDuration, 0, mOffset);
                        }
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.FloatField("Current Normalized Time", mAnimator.GetCurrentNormalizedTime());

                mShowParameters = EditorGUILayout.Foldout(mShowParameters, "Parameters");
                if (mShowParameters)
                {
                    var parameters = mAnimator.parameters;
                    for (int i = 0; i < parameters.Length; ++i)
                    {
                        DrawParameter(parameters[i]);
                    }
                }

                Repaint();
            }
            else
            {
                base.OnInspectorGUI();
            }
        }

        void DrawParameter(AnimationParameterInfo parameter)
        {
            switch (parameter.type)
            {
                case AnimatorControllerParameterType.Bool:
                    {
                        bool newVal = EditorGUILayout.Toggle(parameter.name, parameter.defaultBool);
                        if (newVal != parameter.defaultBool)
                        {
                            mAnimator.SetBool(parameter.name, newVal);
                        }
                        break;
                    }
                case AnimatorControllerParameterType.Trigger:
                    {
                        if (GUILayout.Button($"Trigger {parameter.name}")) 
                        { 
                            mAnimator.SetTrigger(parameter.name);
                        }
                        break;
                    }
                case AnimatorControllerParameterType.Float:
                    {
                        float newVal = EditorGUILayout.FloatField(parameter.name, parameter.defaultFloat);
                        if (!Mathf.Approximately(newVal, parameter.defaultFloat))
                        {
                            mAnimator.SetFloat(parameter.name, newVal);
                        }
                        break;
                    }
                case AnimatorControllerParameterType.Int:
                    {
                        int newVal = EditorGUILayout.IntField(parameter.name, parameter.defaultInt);
                        if (newVal != parameter.defaultInt)
                        {
                            mAnimator.SetInteger(parameter.name, newVal);
                        }
                        break;
                    }
            }
        }

        CustomAnimatorBase mAnimator;
        string[] mStateNames;
        string[] mInterruptionStateNames;
        int mStateIndex;
        int mInterruptionStateIndex;
        float mBlendingDuration = 0.3f;
        float mOffset;
        bool mShowParameters = true;
        GlobalAnimationBlendingState mGlobalAnimationBlendingState;
    }
}
#endif