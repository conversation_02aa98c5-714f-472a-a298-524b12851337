%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: FetchHDColorPyramid
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=15502\n460;92;961;705;422.5;427.5;1;True;False\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;4;192.5,-174.5;Float;False;3;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.Vector4Node;5;-38.5,-232.5;Float;False;Constant;_Vector0;Vector
    0;0;0;Create;True;0;0;False;0;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CustomExpressionNode;1;-33.5,-23.5;Float;False;LOAD_TEXTURE2D_LOD(\r
    \ _ColorPyramidTexture,UV,LOD);4;False;2;True;UV;FLOAT2;0,0;In;;True;LOD;FLOAT;0;In;;FetchColorPyramid;True;False;0;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;2;-195.5,35.5;Float;False;LOD;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;3;-227.5,-119.5;Float;False;UV;2;0;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;0;432,-54;Float;False;True;Output;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nWireConnection;4;0;5;0\nWireConnection;4;1;5;0\nWireConnection;4;2;1;0\nWireConnection;1;0;3;0\nWireConnection;1;1;2;0\nWireConnection;0;0;4;0\nASEEND*/\n//CHKSM=FCEB018C27BAF233C7FC33A2B4BB473BEB28E231"
  m_functionName: 
  m_description: 'Fetches the _ColorPyramidTexture texture of the HDRP pipeline.

    Using this over LW or default pipelines will result in incorrect behaviors.'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
