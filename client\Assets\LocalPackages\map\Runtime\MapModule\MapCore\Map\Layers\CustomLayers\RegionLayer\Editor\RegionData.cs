﻿ 



 
 

//created by wzw at 2020/2/17

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RegionData : PolygonObjectData
    {
        //regionNumber匹配的可以组合
        public RegionData(int id, Map map, OutlineData[] outlines, float displayRadius, bool isExtendable, Vector3[] meshVertices, int[] meshIndices, Color[] vertexColors, Color innerColor, Color outerColor, RegionType type, int regionNumber, Material mtl) : base(id, map, outlines, displayRadius, isExtendable, true)
        {
            mMeshVertices = meshVertices;
            mMeshIndices = meshIndices;
            mMeshColors = vertexColors;
            mInnerColor = innerColor;
            mOuterColor = outerColor;
            mType = type;
            mNumber = regionNumber;
            mMaterial = mtl;
        }

        public override void OnDestroy()
        {
        }

        public void SetMesh(Vector3[] vertices, int[] indices, Color[] vertexColors)
        {
            mMeshVertices = vertices;
            mMeshIndices = indices;
            mMeshColors = vertexColors;
        }

        public Vector3[] meshVertices { get { return mMeshVertices; } }
        public Color[] vertexColors { get { return mMeshColors; } }
        public int[] meshIndices { get { return mMeshIndices; } }
        public RegionType type { get { return mType; } set { mType = value; } }
        public int number { get { return mNumber; } set { mNumber = value; } }
        public Material material { set { mMaterial = value; } get { return mMaterial; } }
        public Color innerColor { get { return mInnerColor; } set { mInnerColor = value; } }
        public Color outerColor { get { return mOuterColor; } set { mOuterColor = value; } }

        Vector3[] mMeshVertices;
        int[] mMeshIndices;
        Color[] mMeshColors;
        RegionType mType;
        int mNumber;
        Material mMaterial;
        Color mInnerColor;
        Color mOuterColor;
    }
}
#endif