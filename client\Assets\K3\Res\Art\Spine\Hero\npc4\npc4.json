{"skeleton": {"hash": "Uqb1TDAI6Dk", "spine": "4.2.33", "x": -261.85, "y": -11.58, "width": 483.12, "height": 1822.11, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 100, "x": 282.29, "y": 1202.73}, {"name": "bone2", "parent": "bone", "x": -237.69, "y": -24.46, "icon": "arrows"}, {"name": "body", "parent": "bone2", "length": 102.22, "rotation": 95.71, "x": -1.09, "y": 9.45}, {"name": "body2", "parent": "body", "length": 254.77, "rotation": 105.24, "x": 102.22, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 96.75, "rotation": 78.89, "x": 254.77, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 162, "rotation": -2.22, "x": 96.75}, {"name": "tun", "parent": "bone2", "length": 122.27, "rotation": -83.09, "x": 1.11, "y": -11.12}, {"name": "leg_R2", "parent": "tun", "x": 39.69, "y": -93.63}, {"name": "leg_R3", "parent": "leg_R2", "length": 454, "rotation": 16.75, "x": 75.84, "y": -13.2}, {"name": "leg_R4", "parent": "leg_R3", "length": 438.2, "rotation": -41.23, "x": 454.03}, {"name": "foot_R", "parent": "root", "length": 98.45, "rotation": 154.4, "x": 101.5, "y": -5.34}, {"name": "foot_R2", "parent": "foot_R", "length": 169.98, "rotation": 93.15, "x": 98.45, "inherit": "noRotationOrReflection"}, {"name": "leg_L2", "parent": "tun", "x": 32.88, "y": 72.74}, {"name": "leg_L3", "parent": "leg_L2", "length": 480.3, "rotation": -18.11, "x": 72.46, "y": 9.22}, {"name": "leg_L4", "parent": "leg_L3", "length": 328.45, "rotation": -18.04, "x": 480.26}, {"name": "sh_R", "parent": "body2", "x": 257.05, "y": 132.28, "inherit": "noScale"}, {"name": "arm_R", "parent": "sh_R", "length": 255.58, "rotation": 151, "x": -21.31, "y": 5.8}, {"name": "arm_R2", "parent": "arm_R", "length": 211, "rotation": 90.11, "x": 255.58, "inherit": "noScale"}, {"name": "hand_R", "parent": "arm_R2", "length": 67.63, "rotation": 17.65, "x": 211, "inherit": "onlyTranslation"}, {"name": "hand_R2", "parent": "hand_R", "length": 42.7, "rotation": -48.61, "x": 67.63}, {"name": "sh_L", "parent": "body2", "x": 181.56, "y": -115.29, "inherit": "noScale"}, {"name": "arm_La", "parent": "sh_L", "length": 239.98, "rotation": 174.67, "x": -16.79, "y": 9.9}, {"name": "arm_Lb", "parent": "arm_La", "x": 246.54, "y": 13.5, "inherit": "noScale"}, {"name": "arm_Lc", "parent": "arm_Lb", "length": 48.36, "rotation": -154.55, "x": 5.83, "y": 1.43}, {"name": "body3", "parent": "body", "length": 95.85, "rotation": -67.77, "x": 40.48, "y": -12.19}, {"name": "arm_L", "parent": "body3", "rotation": -27.94, "x": 80.93, "y": 2.7, "color": "ff3f00ff", "icon": "ik"}, {"name": "hand_L", "parent": "arm_L", "length": 23.41, "rotation": -166.1, "x": 8.9, "y": 1.31, "inherit": "noScaleOrReflection"}, {"name": "hand_L2", "parent": "hand_L", "length": 53.53, "rotation": 33.4, "x": 23.41}, {"name": "eyebrow_R3", "parent": "head", "length": 22.5, "rotation": -67.93, "x": 90.41, "y": 32.78}, {"name": "eyebrow_R2", "parent": "eyebrow_R3", "length": 24.58, "rotation": -40.47, "x": 22.5}, {"name": "eyebrow_R", "parent": "eyebrow_R2", "length": 15.45, "rotation": -2.75, "x": 24.58}, {"name": "eyebrow_L3", "parent": "head", "length": 6.46, "rotation": 60.71, "x": 88.85, "y": -75.1}, {"name": "eyebrow_L2", "parent": "eyebrow_L3", "length": 13.91, "rotation": 52.85, "x": 6.46}, {"name": "eyebrow_L", "parent": "eyebrow_L2", "length": 11.46, "rotation": -4.53, "x": 13.91}, {"name": "eye_R", "parent": "head", "x": 65.81, "y": 4.76}, {"name": "eye_L", "parent": "head", "x": 59.93, "y": -54.79}, {"name": "earring", "parent": "head", "length": 25.89, "rotation": -173.64, "x": 9.57, "y": 52.29, "color": "abe323ff"}, {"name": "hair", "parent": "head", "x": 137.8, "y": -35.81}, {"name": "hair_LF", "parent": "hair", "length": 40.23, "rotation": -151.48, "x": 21.95, "y": -31.25}, {"name": "hair_LF2", "parent": "hair_LF", "length": 52.9, "rotation": -1.03, "x": 40.23, "color": "abe323ff"}, {"name": "hair_LF3", "parent": "hair_LF2", "length": 47.94, "rotation": -13.59, "x": 52.9, "color": "abe323ff"}, {"name": "hair_LF4", "parent": "hair_LF3", "length": 46.18, "rotation": -48.94, "x": 47.94, "color": "abe323ff"}, {"name": "hair_LF5", "parent": "hair_LF4", "length": 34.2, "rotation": 42.74, "x": 46.18, "color": "abe323ff"}, {"name": "hair_LF6", "parent": "hair_LF5", "length": 27.33, "rotation": 57.76, "x": 34.2, "color": "abe323ff"}, {"name": "hair_RF", "parent": "hair", "length": 45.18, "rotation": 144.46, "x": 22.49, "y": 43.43, "color": "abe323ff"}, {"name": "hair_RF2", "parent": "hair_RF", "length": 50.5, "rotation": 3.49, "x": 45.18, "color": "abe323ff"}, {"name": "hair_RF3", "parent": "hair_RF2", "length": 40.1, "rotation": -16.82, "x": 50.5, "color": "abe323ff"}, {"name": "hair_RF4", "parent": "hair_RF3", "length": 44.43, "rotation": -7.6, "x": 40.1, "color": "abe323ff"}, {"name": "hair_RF5", "parent": "hair_RF4", "length": 42.4, "rotation": 22.52, "x": 44.43, "color": "abe323ff"}, {"name": "hair_RF6", "parent": "hair_RF5", "length": 33.2, "rotation": 29.64, "x": 42.4, "color": "abe323ff"}, {"name": "hair_RR", "parent": "head", "length": 26.13, "rotation": -176.92, "x": 73.9, "y": 49.95}, {"name": "hair_RR2", "parent": "hair_RR", "length": 21.69, "rotation": 20.11, "x": 26.13, "color": "abe323ff"}, {"name": "hair_RR3", "parent": "hair_RR2", "length": 16.74, "rotation": 41.14, "x": 21.69, "color": "abe323ff"}, {"name": "hair_RR4", "parent": "hair_RR3", "length": 16.3, "rotation": 30.24, "x": 16.74, "color": "abe323ff"}, {"name": "hair_R", "parent": "sh_R", "length": 85.32, "rotation": 17.79, "x": 21.65, "y": -5.72, "color": "abe323ff"}, {"name": "hair_L", "parent": "sh_L", "length": 57.08, "rotation": -38.44, "x": 18.42, "y": -2.63, "color": "abe323ff"}, {"name": "bone57", "parent": "leg_R2", "length": 486.79, "rotation": 22.76, "x": 75.82, "y": -13.21}, {"name": "bone58", "parent": "bone57", "length": 451.44, "rotation": -54.95, "x": 486.79, "color": "abe323ff"}, {"name": "leg_R", "parent": "foot_R2", "rotation": -93.15, "x": 169.03, "y": -0.11, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "root", "x": 135.79, "y": 623.77, "color": "ff3f00ff", "icon": "ik"}, {"name": "foot_L", "parent": "root", "length": 94.57, "rotation": 109.98, "x": -135.56, "y": 80.53}, {"name": "foot_L2", "parent": "foot_L", "length": 155.48, "rotation": -40.3, "x": 94.57}, {"name": "bone59", "parent": "leg_L2", "length": 497.88, "rotation": -10.51, "x": 71.38, "y": 9.06}, {"name": "bone60", "parent": "bone59", "length": 344.88, "rotation": -36.81, "x": 497.88, "color": "abe323ff"}, {"name": "leg_L", "parent": "foot_L2", "rotation": -69.68, "x": 154.98, "y": -0.24, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "root", "x": 46.55, "y": 601.27, "color": "ff3f00ff", "icon": "ik"}, {"name": "RU_R", "parent": "body2", "length": 30, "x": 112.78, "y": 28.49}, {"name": "RU_R2", "parent": "RU_R", "length": 30, "x": -3.17, "y": 6.41}, {"name": "RU_R3", "parent": "RU_R2", "length": 30, "x": -7.13, "y": 7.43}, {"name": "RU_L", "parent": "body2", "length": 30, "x": 95.67, "y": -88.57}, {"name": "RU_L2", "parent": "RU_L", "length": 30, "x": -13.14, "y": -28.63}, {"name": "RU_L3", "parent": "RU_L2", "length": 30, "x": -9.13, "y": -20.66}, {"name": "headround3", "parent": "head", "x": 359.54, "y": -4.11, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -46.39, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 319.51, "y": -50.5, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "x": 87.89, "y": -318.56, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "x": 41.68, "y": -318.56, "icon": "warning"}, {"name": "tunround", "parent": "bone2", "x": 330.99, "y": -99.06, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "bone2", "x": 330.99, "y": -151.24, "icon": "warning"}, {"name": "sh_R2", "parent": "sh_R", "length": 277.8, "rotation": 147.49, "x": -21.3, "y": 5.81}, {"name": "sh_R3", "parent": "sh_R2", "length": 229.78, "rotation": 98.85, "x": 277.8, "color": "abe323ff"}, {"name": "arm_R3", "parent": "body", "rotation": -95.71, "x": 0.93, "y": 61.32, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "bone2", "x": -267.23, "y": 54.07, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "arm_La", "bone": "root", "attachment": "arm_La"}, {"name": "arm_Lb", "bone": "root", "attachment": "arm_Lb"}, {"name": "arm_Lc", "bone": "root", "attachment": "arm_Lc"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "hand_L", "bone": "root", "attachment": "hand_L"}, {"name": "arm_Rb", "bone": "root", "attachment": "arm_Rb"}, {"name": "glass2", "bone": "root", "attachment": "glass2"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "earring", "bone": "earring", "attachment": "earring"}, {"name": "hair_R", "bone": "root", "attachment": "hair_R"}, {"name": "glass", "bone": "root", "attachment": "glass"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}], "ik": [{"name": "arm_L", "order": 10, "bones": ["arm_Lc"], "target": "arm_L", "compress": true, "stretch": true}, {"name": "arm_R", "order": 11, "bones": ["sh_R2", "sh_R3"], "target": "arm_R3"}, {"name": "arm_R1", "order": 13, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 14, "bones": ["arm_R2"], "target": "arm_R3", "compress": true, "stretch": true}, {"name": "leg_L", "order": 1, "bones": ["bone59", "bone60"], "target": "leg_L", "bendPositive": false}, {"name": "leg_L1", "order": 3, "bones": ["leg_L3"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 4, "bones": ["leg_L4"], "target": "leg_L", "compress": true, "stretch": true}, {"name": "leg_R", "order": 6, "bones": ["bone57", "bone58"], "target": "leg_R", "bendPositive": false}, {"name": "leg_R1", "order": 8, "bones": ["leg_R3"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 9, "bones": ["leg_R4"], "target": "leg_R", "compress": true, "stretch": true}], "transform": [{"name": "arm_R3", "order": 12, "bones": ["arm_R1"], "target": "sh_R3", "rotation": 8.25, "x": 19.72, "y": 19.85, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 15, "bones": ["bodyround2"], "target": "bodyround", "x": -46.21, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "order": 16, "bones": ["sh_R"], "target": "bodyround", "x": 169.16, "y": 450.84, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "order": 17, "bones": ["sh_L"], "target": "bodyround", "x": 93.67, "y": 203.27, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 18, "bones": ["headround2"], "target": "headround", "x": -40.03, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 19, "bones": ["eyebrow_R3"], "target": "headround", "rotation": -67.93, "x": -269.14, "y": 83.28, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 20, "bones": ["eyebrow_L3"], "target": "headround", "rotation": 60.71, "x": -270.7, "y": -24.59, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 22, "bones": ["earring"], "target": "headround", "rotation": -173.64, "x": -349.98, "y": 102.8, "mixRotate": 0, "mixX": 0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 23, "bones": ["hair"], "target": "headround", "x": -221.74, "y": 14.69, "mixRotate": 0, "mixX": 0.0285, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround5", "order": 39, "bones": ["hair_RR"], "target": "headround", "rotation": -176.92, "x": -285.64, "y": 100.45, "mixRotate": 0, "mixX": 0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_L3", "order": 2, "bones": ["leg_L1"], "target": "bone60", "rotation": 130.62, "x": 22.91, "y": -64.84, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "leg_R3", "order": 7, "bones": ["leg_R1"], "target": "bone58", "rotation": 114.81, "x": 16.71, "y": -55.25, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "tunround", "order": 24, "bones": ["tunround2"], "target": "tunround", "y": -52.19, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 5, "bones": ["leg_R2"], "target": "tunround", "rotation": -83.09, "x": -418.05, "y": 37.26, "mixRotate": 0, "mixX": 0.015, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "bones": ["leg_L2"], "target": "tunround", "rotation": -83.09, "x": -253.71, "y": 64.05, "mixRotate": 0, "mixX": -0.01, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "earring", "order": 21, "bone": "earring", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L", "order": 40, "bone": "hair_L", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LF2", "order": 25, "bone": "hair_LF2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LF3", "order": 26, "bone": "hair_LF3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LF4", "order": 27, "bone": "hair_LF4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LF5", "order": 28, "bone": "hair_LF5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LF6", "order": 29, "bone": "hair_LF6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R", "order": 41, "bone": "hair_R", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RF", "order": 30, "bone": "hair_RF", "rotate": 1, "inertia": 0.25, "strength": 35, "damping": 0.88, "mass": 0.4}, {"name": "hair_RF2", "order": 31, "bone": "hair_RF2", "rotate": 1, "inertia": 0.25, "strength": 35, "damping": 0.88, "mass": 0.4}, {"name": "hair_RF3", "order": 32, "bone": "hair_RF3", "rotate": 1, "inertia": 0.25, "strength": 35, "damping": 0.88, "mass": 0.4}, {"name": "hair_RF4", "order": 33, "bone": "hair_RF4", "rotate": 1, "inertia": 0.25, "strength": 35, "damping": 0.88, "mass": 0.4}, {"name": "hair_RF5", "order": 34, "bone": "hair_RF5", "rotate": 1, "inertia": 0.25, "strength": 35, "damping": 0.88, "mass": 0.4}, {"name": "hair_RF6", "order": 35, "bone": "hair_RF6", "rotate": 1, "inertia": 0.25, "strength": 35, "damping": 0.88, "mass": 0.4}, {"name": "hair_RR2", "order": 36, "bone": "hair_RR2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR3", "order": 37, "bone": "hair_RR3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR4", "order": 38, "bone": "hair_RR4", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_La": {"arm_La": {"type": "mesh", "uvs": [0.53725, 0.01796, 0.61969, 0.07072, 0.65223, 0.1351, 0.68557, 0.23109, 0.83613, 0.66461, 0.88877, 0.69657, 0.93371, 0.74571, 0.99323, 0.85432, 0.98134, 0.92001, 0.9147, 0.96802, 0.78151, 0.9963, 0.64352, 0.99831, 0.51963, 0.97543, 0.41268, 0.92891, 0.35772, 0.85377, 0.32197, 0.7852, 0.28623, 0.71662, 0.18346, 0.50208, 0.05, 0.29095, 0.00019, 0.17109, 0.01564, 0.09295, 0.09191, 0.04017, 0.23383, 0.00607, 0.40532, 0.00188, 0.80884, 0.8595, 0.59983, 0.8805, 0.28921, 0.31312, 0.50651, 0.1893, 0.3088, 0.06262, 0.22463, 0.13547, 0.53015, 0.1248, 0.54496, 0.06066, 0.52094, 0.29169, 0.22348, 0.22366], "triangles": [9, 24, 8, 10, 25, 24, 12, 13, 25, 8, 24, 7, 7, 24, 6, 25, 14, 15, 25, 26, 24, 4, 5, 24, 4, 24, 32, 25, 16, 17, 27, 2, 3, 32, 3, 4, 32, 27, 3, 24, 26, 32, 25, 17, 26, 5, 6, 24, 16, 25, 15, 13, 14, 25, 10, 11, 25, 11, 12, 25, 10, 24, 9, 30, 1, 2, 30, 28, 31, 30, 31, 1, 31, 23, 0, 23, 31, 28, 21, 22, 28, 28, 22, 23, 1, 31, 0, 29, 28, 30, 17, 18, 26, 26, 33, 32, 29, 21, 28, 33, 19, 29, 19, 20, 29, 20, 21, 29, 18, 19, 33, 33, 29, 27, 18, 33, 26, 33, 27, 32, 27, 29, 30, 27, 30, 2], "vertices": [1, 21, 16.34, -19.86, 1, 2, 21, -2.88, -25.98, 0.80679, 22, -17.18, 34.44, 0.19321, 2, 21, -23.96, -24.73, 0.43102, 22, 3.92, 35.14, 0.56898, 2, 21, -54.86, -20.91, 0.18, 22, 35.04, 34.21, 0.82, 1, 22, 175.57, 30, 1, 1, 22, 186.88, 35.14, 1, 1, 22, 203.44, 38.31, 1, 1, 22, 239.15, 40.11, 1, 1, 22, 259.65, 34.93, 1, 1, 22, 273.31, 23.55, 1, 1, 22, 279.2, 4.53, 1, 1, 22, 276.68, -13.66, 1, 1, 22, 266.61, -28.62, 1, 1, 22, 249.45, -40.07, 1, 1, 22, 224.44, -43.12, 1, 1, 22, 201.94, -44.02, 1, 1, 22, 179.43, -44.92, 1, 2, 4, 60.33, -48.9, 0.1, 22, 109.24, -46.53, 0.9, 2, 4, 130.39, -49.58, 0.88286, 22, 39.42, -52.36, 0.11714, 2, 4, 169.25, -53.3, 0.83517, 21, -12.31, 61.98, 0.16483, 2, 4, 192.91, -61.88, 0.75597, 21, 11.35, 53.4, 0.24403, 2, 4, 206.59, -76.12, 0.56585, 21, 25.03, 39.16, 0.43415, 2, 4, 212.19, -97.21, 0.37295, 21, 30.63, 18.08, 0.62705, 2, 4, 207.49, -119.57, 0.16406, 21, 25.93, -4.28, 0.83594, 1, 22, 236.57, 15.67, 1, 1, 22, 238.43, -12.88, 1, 4, 4, 115.16, -78.41, 0.54029, 21, -66.4, 36.88, 0.14519, 22, 51.91, -22.25, 0.29743, 76, 27.27, 240.15, 0.01709, 3, 4, 145.91, -116.74, 0.83935, 21, -35.65, -1.46, 0.14993, 76, 58.02, 201.82, 0.01072, 3, 4, 192.06, -102.06, 0.48053, 21, 10.49, 13.23, 0.51266, 76, 104.17, 216.5, 0.00681, 3, 4, 172.43, -85.11, 0.74313, 21, -9.13, 30.17, 0.24865, 76, 84.55, 233.45, 0.00822, 3, 4, 165.06, -125.22, 0.44428, 21, -16.5, -9.93, 0.55072, 76, 77.17, 193.34, 0.005, 2, 21, 2.85, -17.24, 0.99686, 76, 96.52, 186.03, 0.00314, 4, 4, 113.69, -109.95, 0.24799, 21, -67.87, 5.33, 0.0628, 22, 50.43, 9.3, 0.66539, 76, 25.8, 208.6, 0.02382, 3, 4, 145.16, -77.52, 0.82519, 21, -36.4, 37.76, 0.16378, 76, 57.27, 241.03, 0.01103], "hull": 24, "edges": [0, 46, 0, 2, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 26, 28, 2, 4, 4, 6, 32, 34, 34, 36, 28, 30, 30, 32, 50, 52, 48, 64, 6, 8], "width": 133, "height": 321}}, "arm_Lb": {"arm_Lb": {"type": "mesh", "uvs": [0.64506, 0.01077, 0.83405, 0.07966, 0.95382, 0.26908, 1, 0.57096, 0.91987, 0.79235, 0.74154, 0.96721, 0.54436, 0.99085, 0.27083, 0.93691, 0.08861, 0.78927, 0.00901, 0.5795, 0.01861, 0.27925, 0.14366, 0.09482, 0.35175, 0.00864, 0.63404, 0.14784, 0.7533, 0.27619, 0.81486, 0.46222, 0.79562, 0.65381, 0.70906, 0.79272, 0.55325, 0.84295, 0.38782, 0.81132, 0.25124, 0.72947, 0.18199, 0.56167, 0.17814, 0.3403, 0.29548, 0.18404, 0.47246, 0.1208, 0.4865, 0.51811], "triangles": [22, 23, 25, 22, 11, 23, 25, 24, 13, 13, 24, 0, 23, 24, 25, 23, 12, 24, 7, 19, 6, 6, 18, 5, 6, 19, 18, 18, 17, 5, 5, 17, 4, 8, 20, 7, 7, 20, 19, 17, 18, 25, 25, 18, 19, 19, 20, 25, 17, 16, 4, 17, 25, 16, 4, 16, 3, 8, 21, 20, 8, 9, 21, 20, 21, 25, 16, 15, 3, 16, 25, 15, 9, 22, 21, 9, 10, 22, 15, 2, 3, 21, 22, 25, 25, 14, 15, 15, 14, 2, 10, 11, 22, 14, 1, 2, 23, 11, 12, 13, 0, 1, 24, 12, 0, 25, 13, 14, 14, 13, 1], "vertices": [1, 23, -39.49, 18.68, 1, 1, 23, -30.45, 33.98, 1, 1, 23, -11.66, 41.4, 1, 1, 23, 16.1, 40.68, 1, 1, 23, 34.73, 30.27, 1, 1, 23, 47.71, 12.07, 1, 1, 23, 46.84, -5.39, 1, 1, 23, 37.87, -28.26, 1, 1, 23, 21.88, -41.74, 1, 1, 23, 1.87, -45.36, 1, 1, 23, -24.9, -39.83, 1, 1, 23, -39.54, -26.1, 1, 1, 23, -44.12, -6.71, 1, 2, 23, -27.37, 15.58, 0.86937, 26, 36.21, -4.31, 0.13063, 2, 23, -14.06, 23.91, 0.92471, 26, 46.7, -15.99, 0.07529, 2, 23, 3.55, 26.33, 0.9828, 26, 52.12, -32.91, 0.0172, 1, 23, 20.43, 21.67, 1, 1, 23, 31.58, 11.99, 1, 1, 23, 33.72, -2.3, 1, 1, 23, 28.38, -16.15, 1, 2, 23, 18.98, -26.71, 0.96928, 26, 2.52, -57.23, 0.03072, 2, 23, 2.89, -30.09, 0.90574, 26, -3.57, -41.96, 0.09426, 2, 23, -17.02, -26.95, 0.83203, 26, -3.91, -21.82, 0.16797, 2, 23, -29.25, -14.34, 0.78333, 26, 6.41, -7.6, 0.21667, 2, 23, -32.24, 1.99, 0.80954, 26, 21.99, -1.85, 0.19046, 1, 23, 3.59, -3.01, 1], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 18, 20, 22, 24, 20, 22, 14, 16, 4, 6, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 26], "width": 88, "height": 91}}, "arm_Lc": {"arm_Lc": {"type": "mesh", "uvs": [0.45012, 0.02563, 0.62851, 0.15982, 0.79841, 0.2538, 0.92299, 0.37317, 0.98793, 0.54292, 0.99106, 0.7482, 0.91013, 0.92016, 0.75483, 0.99159, 0.56603, 0.99186, 0.42796, 0.94384, 0.28945, 0.81569, 0.13162, 0.54627, 0.01831, 0.35284, 0.00214, 0.22769, 0.05008, 0.10209, 0.17707, 0.01146, 0.31608, 1e-05, 0.58153, 0.77315, 0.20604, 0.25753, 0.78827, 0.63907, 0.33925, 0.16273], "triangles": [20, 16, 0, 20, 18, 15, 20, 15, 16, 14, 15, 18, 13, 14, 18, 12, 13, 18, 19, 18, 20, 19, 1, 2, 20, 1, 19, 18, 17, 11, 10, 11, 17, 11, 12, 18, 20, 0, 1, 8, 9, 17, 19, 17, 18, 17, 19, 6, 19, 4, 5, 6, 19, 5, 7, 17, 6, 7, 8, 17, 9, 10, 17, 19, 3, 4, 19, 2, 3], "vertices": [2, 24, 54.59, -18.78, 0.7065, 27, -6.25, -13.53, 0.2935, 1, 24, 37.54, -22.87, 1, 2, 23, -27.76, 16.95, 0.21073, 24, 23.67, -28.45, 0.78927, 2, 23, -16.07, 24.27, 0.5683, 24, 9.96, -30.03, 0.4317, 2, 23, -0.86, 26.49, 0.89736, 24, -4.72, -25.5, 0.10264, 2, 23, 16.57, 23.68, 0.96614, 24, -19.26, -15.48, 0.03386, 1, 23, 30.11, 15.24, 1, 1, 23, 34.18, 2.86, 1, 2, 23, 31.8, -10.9, 0.91124, 24, -18.15, 22.3, 0.08876, 2, 23, 25.98, -20.26, 0.77483, 24, -8.87, 28.24, 0.22517, 2, 23, 13.36, -28.46, 0.33176, 24, 6.05, 30.23, 0.66824, 2, 24, 31.7, 26.35, 0.89354, 27, 27.38, 24.27, 0.10646, 2, 24, 50.12, 23.56, 0.62614, 27, 31.53, 6.11, 0.37386, 2, 24, 59.6, 18.31, 0.29458, 27, 30.1, -4.63, 0.70542, 2, 24, 66.35, 9.16, 0.01538, 27, 24.06, -14.26, 0.98462, 2, 24, 67.27, -3.01, 0.09033, 27, 13.07, -19.57, 0.90967, 2, 24, 62.12, -11.97, 0.34046, 27, 2.85, -18.05, 0.65954, 2, 23, 13.47, -6.54, 0.78802, 24, -3.47, 10.48, 0.21198, 2, 24, 48.77, 7.49, 0.31895, 27, 16.07, 1.49, 0.68105, 2, 23, 4.74, 10.52, 0.79957, 24, -2.92, -8.67, 0.20043, 2, 24, 49.72, -5.27, 0.41341, 27, 4.54, -4.06, 0.58659], "hull": 17, "edges": [4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 30, 32, 6, 8, 8, 10, 4, 2, 2, 0, 0, 32, 26, 28, 28, 30, 20, 22, 22, 24, 36, 34, 38, 40], "width": 74, "height": 86}}, "arm_Rb": {"arm_Rb": {"type": "mesh", "uvs": [0.78243, 1e-05, 0.86694, 0.04671, 0.95349, 0.09621, 0.99569, 0.40723, 0.99585, 0.62575, 0.89523, 0.88277, 0.84595, 0.9949, 0.78753, 0.9881, 0.73691, 0.88083, 0.67147, 0.96665, 0.60777, 0.99288, 0.54775, 0.96703, 0.50479, 0.921, 0.46178, 0.91717, 0.4098, 0.91161, 0.0018, 0.87426, 0.0784, 0.17468, 0.24718, 0.31156, 0.43116, 0.38965, 0.48245, 0.40231, 0.52726, 0.3999, 0.57519, 0.33935, 0.7074, 0.11966], "triangles": [6, 7, 5, 7, 8, 5, 5, 3, 4, 3, 5, 1, 3, 1, 2, 1, 5, 8, 1, 22, 0, 9, 10, 21, 10, 20, 21, 11, 12, 20, 10, 11, 20, 9, 21, 8, 12, 19, 20, 21, 22, 8, 1, 8, 22, 13, 19, 12, 19, 13, 18, 13, 14, 18, 15, 17, 14, 14, 17, 18, 15, 16, 17], "vertices": [2, 19, 77.05, 35.42, 0.57047, 20, -20.35, 30.48, 0.42953, 2, 19, 94, 25.71, 0.2234, 20, -1.85, 36.79, 0.7766, 2, 19, 111.32, 15.63, 0.00247, 20, 17.16, 43.12, 0.99753, 1, 20, 39.42, 24.55, 1, 1, 20, 49.34, 8.08, 1, 2, 19, 77.79, -46.34, 0.10136, 20, 41.48, -23.01, 0.89864, 2, 19, 64.18, -52.36, 0.2154, 20, 37.01, -37.2, 0.7846, 2, 19, 51.78, -47.79, 0.33042, 20, 25.38, -43.48, 0.66958, 2, 19, 43.74, -35.32, 0.62881, 20, 10.71, -41.27, 0.37119, 2, 19, 27.36, -38.04, 0.90342, 20, 1.92, -55.36, 0.09658, 3, 18, 240.69, -23.93, 0.0134, 19, 12.94, -35.87, 0.959, 20, -9.24, -64.75, 0.0276, 3, 18, 226.98, -24.92, 0.12794, 19, 0.7, -29.59, 0.86748, 20, -22.04, -69.77, 0.00459, 3, 18, 216.59, -23.27, 0.40954, 19, -7.32, -22.79, 0.59029, 20, -32.45, -71.29, 0.00016, 2, 18, 207.06, -25.24, 0.75843, 19, -16.48, -19.52, 0.24157, 2, 18, 195.53, -27.54, 0.96082, 19, -27.53, -15.49, 0.03918, 1, 18, 105.15, -46.1, 1, 1, 18, 107.44, 17.81, 1, 1, 18, 147.35, 15.1, 1, 2, 18, 189.38, 18.24, 0.91776, 19, -9, 26.82, 0.08224, 2, 18, 200.91, 19.89, 0.61842, 19, 1.71, 22.24, 0.38158, 2, 18, 210.7, 22.49, 0.20111, 19, 11.42, 19.37, 0.79889, 2, 18, 219.97, 30.22, 0.01508, 19, 23.36, 21.16, 0.98492, 2, 19, 57.7, 30.53, 0.96679, 20, -29.47, 12.73, 0.03321], "hull": 23, "edges": [0, 44, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 28, 30, 42, 44, 30, 32, 20, 22, 22, 24, 0, 2, 2, 4, 38, 40, 40, 42, 34, 36, 36, 38, 24, 26, 26, 28, 32, 34], "width": 226, "height": 88}}, "body": {"body": {"type": "mesh", "uvs": [0.58409, 0.00159, 0.62309, 0.00701, 0.63833, 0.01096, 0.65777, 0.01077, 0.68167, 0.01432, 0.71949, 0.02463, 0.74215, 0.03663, 0.7589, 0.05224, 0.77187, 0.06773, 0.77068, 0.08403, 0.75747, 0.09484, 0.71931, 0.10637, 0.69507, 0.11357, 0.68568, 0.12283, 0.68755, 0.13189, 0.70186, 0.14098, 0.72383, 0.14481, 0.74731, 0.14537, 0.76478, 0.14193, 0.76432, 0.13721, 0.75069, 0.13105, 0.76905, 0.13313, 0.77986, 0.13837, 0.78182, 0.14232, 0.77469, 0.14724, 0.76204, 0.15253, 0.74217, 0.15567, 0.76115, 0.15584, 0.77438, 0.15337, 0.78308, 0.15054, 0.7913, 0.1463, 0.7962, 0.14992, 0.79293, 0.15293, 0.78715, 0.15634, 0.7756, 0.1586, 0.76467, 0.16003, 0.7759, 0.16742, 0.77086, 0.17974, 0.76455, 0.19156, 0.82115, 0.19762, 0.87398, 0.21404, 0.89322, 0.22492, 0.89494, 0.23693, 0.87245, 0.25208, 0.82025, 0.26439, 0.80801, 0.27146, 0.8059, 0.28184, 0.80784, 0.29473, 0.80916, 0.30926, 0.81968, 0.33164, 0.86397, 0.34417, 0.90878, 0.35852, 0.95028, 0.37648, 0.98075, 0.39829, 0.99792, 0.42005, 1, 0.44074, 0.99134, 0.46336, 0.95883, 0.49943, 0.94676, 0.52327, 0.9213, 0.52295, 0.91657, 0.52082, 0.86073, 0.51765, 0.80063, 0.51326, 0.73964, 0.50561, 0.76416, 0.51872, 0.77831, 0.53282, 0.82365, 0.56955, 0.85342, 0.59065, 0.88729, 0.6198, 0.90289, 0.6344, 0.91571, 0.65038, 0.90455, 0.66396, 0.87343, 0.67612, 0.82965, 0.68793, 0.80772, 0.69739, 0.78446, 0.70597, 0.75169, 0.73321, 0.69898, 0.77234, 0.64058, 0.82358, 0.62246, 0.84065, 0.61111, 0.85717, 0.61161, 0.87138, 0.61532, 0.88307, 0.61861, 0.89343, 0.61161, 0.90035, 0.6225, 0.92035, 0.62727, 0.94049, 0.63903, 0.95776, 0.64972, 0.96646, 0.66933, 0.97362, 0.69629, 0.97897, 0.75385, 0.9887, 0.76441, 0.99645, 0.72882, 0.99958, 0.60286, 0.99937, 0.55031, 0.99635, 0.50524, 0.99045, 0.4802, 0.98398, 0.47952, 0.97048, 0.48069, 0.95453, 0.46641, 0.93786, 0.44282, 0.92862, 0.39272, 0.96946, 0.36225, 0.9694, 0.40418, 0.92813, 0.40951, 0.89968, 0.42673, 0.8891, 0.4686, 0.87894, 0.48581, 0.87688, 0.496, 0.87044, 0.50321, 0.85921, 0.50827, 0.83841, 0.50969, 0.82169, 0.51113, 0.74531, 0.51832, 0.72296, 0.55412, 0.69639, 0.60831, 0.67217, 0.64041, 0.65743, 0.65752, 0.64852, 0.65802, 0.64233, 0.64954, 0.63238, 0.62263, 0.61842, 0.58559, 0.60407, 0.48953, 0.5696, 0.45882, 0.55733, 0.39758, 0.53285, 0.34569, 0.53434, 0.30071, 0.49392, 0.25673, 0.4569, 0.25406, 0.4307, 0.26681, 0.40779, 0.2907, 0.39238, 0.33539, 0.37318, 0.36958, 0.36039, 0.39788, 0.34815, 0.39502, 0.32652, 0.39429, 0.31798, 0.36177, 0.30386, 0.33997, 0.29204, 0.30206, 0.27891, 0.26931, 0.26661, 0.25182, 0.25845, 0.23859, 0.26808, 0.21344, 0.28141, 0.21443, 0.29303, 0.19757, 0.29787, 0.20561, 0.30529, 0.21986, 0.30635, 0.22905, 0.30982, 0.32139, 0.32217, 0.31195, 0.33108, 0.29895, 0.34335, 0.28594, 0.35563, 0.14143, 0.36203, 0.10168, 0.35337, 0.0915, 0.34835, 0.05096, 0.3453, 0.02019, 0.34082, 0.00243, 0.33294, 0.00182, 0.32082, 0.01082, 0.30396, 0.02209, 0.29114, 0.0402, 0.27054, 0.08138, 0.23678, 0.08993, 0.22375, 0.09919, 0.21086, 0.10845, 0.19811, 0.11103, 0.18619, 0.11853, 0.17689, 0.13636, 0.16558, 0.12203, 0.15867, 0.10928, 0.15187, 0.10947, 0.14423, 0.11289, 0.13826, 0.1229, 0.13361, 0.1298, 0.13403, 0.12519, 0.13934, 0.12577, 0.14436, 0.13499, 0.1489, 0.14749, 0.15118, 0.15133, 0.14617, 0.16671, 0.14209, 0.1498, 0.13913, 0.14326, 0.13127, 0.14501, 0.12427, 0.15108, 0.11855, 0.16219, 0.11446, 0.17797, 0.11072, 0.19258, 0.10909, 0.20373, 0.1109, 0.18922, 0.1126, 0.17806, 0.11569, 0.17713, 0.121, 0.19481, 0.12557, 0.22272, 0.1252, 0.24273, 0.12038, 0.24366, 0.10852, 0.22877, 0.09864, 0.21857, 0.08869, 0.22432, 0.07926, 0.24774, 0.07163, 0.2907, 0.06104, 0.326, 0.04751, 0.3592, 0.03146, 0.39006, 0.01831, 0.42455, 0.00932, 0.48268, 0.00201, 0.54014, 7e-05, 0.70373, 0.18135, 0.1696, 0.15721, 0.24939, 0.15333, 0.32626, 0.1506, 0.38628, 0.13447, 0.41044, 0.11982, 0.42142, 0.10543, 0.42289, 0.08911, 0.42435, 0.07237, 0.62842, 0.08184, 0.60792, 0.09856, 0.59035, 0.1143, 0.57278, 0.12946, 0.56176, 0.14108, 0.56119, 0.15203, 0.64806, 0.14545, 0.68644, 0.16666, 0.66841, 0.15583, 0.35543, 0.17074, 0.20586, 0.16258, 0.24325, 0.17792, 0.27477, 0.2014, 0.27215, 0.24128, 0.41358, 0.27656, 0.50366, 0.2781, 0.57918, 0.27304, 0.64207, 0.26356, 0.71091, 0.26311, 0.76739, 0.26767, 0.66967, 0.24832, 0.67035, 0.23079, 0.64857, 0.21542, 0.60772, 0.20349, 0.55122, 0.19428, 0.47353, 0.18749, 0.3951, 0.1878, 0.32963, 0.20004, 0.30625, 0.22331, 0.30928, 0.2472, 0.34385, 0.26458, 0.28482, 0.22473, 0.30106, 0.17383, 0.50588, 0.16835, 0.55045, 0.1683, 0.47857, 0.15192, 0.47223, 0.13792, 0.47381, 0.12416, 0.47844, 0.10942, 0.484, 0.09271, 0.48955, 0.07551, 0.53913, 0.1536, 0.54042, 0.14087, 0.54507, 0.12735, 0.55248, 0.11286, 0.56451, 0.09664, 0.57839, 0.08042, 0.42903, 0.16407, 0.61828, 0.16439, 0.60952, 0.19132, 0.6497, 0.18058, 0.57045, 0.28566, 0.45771, 0.29417, 0.49451, 0.32369, 0.48198, 0.31038, 0.75991, 0.31142, 0.75756, 0.29687, 0.76069, 0.28191, 0.67849, 0.2817, 0.68005, 0.29916, 0.68005, 0.31558, 0.58219, 0.3029, 0.5908, 0.31953, 0.49717, 0.34715, 0.60286, 0.34466, 0.69367, 0.34092, 0.77274, 0.3351, 0.17177, 0.17443, 0.16105, 0.19067, 0.15411, 0.21138, 0.07025, 0.28975, 0.06137, 0.30215, 0.06604, 0.31567, 0.09409, 0.32842, 0.15013, 0.28923, 0.22159, 0.22081, 0.2359, 0.20123, 0.14477, 0.29973, 0.15183, 0.30734, 0.17261, 0.31246, 0.13764, 0.33509, 0.46703, 0.23829, 0.85555, 0.22675, 0.4126, 0.20309, 0.48605, 0.20153, 0.4337, 0.22171, 0.48761, 0.21942, 0.44956, 0.25531, 0.50321, 0.2536, 0.40237, 0.23316, 0.40885, 0.24819, 0.53037, 0.22852, 0.53621, 0.24337, 0.36142, 0.21609, 0.35015, 0.23437, 0.36456, 0.25084, 0.40755, 0.26311, 0.47185, 0.26627, 0.53406, 0.26367, 0.58482, 0.25381, 0.59784, 0.22147, 0.5573, 0.20756, 0.60539, 0.24019, 0.75644, 0.19382, 0.70944, 0.19935, 0.71836, 0.2456, 0.76517, 0.25483, 0.82479, 0.25655, 0.80289, 0.21054, 0.78235, 0.22335, 0.79287, 0.23753, 0.86727, 0.2447, 0.83906, 0.20893, 0.82806, 0.24533, 0.69519, 0.23254, 0.69118, 0.21389, 0.5661, 0.97407, 0.59801, 0.9818, 0.55745, 0.96195, 0.64282, 0.98641, 0.5523, 0.94637, 0.85018, 0.619, 0.87125, 0.63397, 0.8804, 0.65096, 0.86475, 0.66421, 0.82855, 0.6759, 0.77865, 0.68915, 0.72582, 0.72499, 0.59516, 0.81974, 0.61183, 0.72529, 0.65141, 0.69318, 0.70188, 0.67505, 0.73423, 0.6652, 0.75037, 0.65373, 0.75307, 0.64049, 0.73948, 0.62778, 0.63462, 0.58247, 0.75797, 0.56686, 0.68902, 0.52145, 0.52654, 0.54123, 0.4479, 0.50728, 0.3766, 0.52348, 0.64496, 0.49587, 0.69365, 0.57247, 0.54835, 0.49726, 0.60344, 0.52633, 0.47753, 0.36324, 0.44226, 0.38119, 0.40994, 0.39939, 0.38153, 0.42176, 0.38349, 0.44973, 0.40406, 0.4784, 0.59384, 0.3641, 0.71567, 0.35892, 0.57339, 0.38554, 0.55298, 0.40789, 0.54629, 0.42845, 0.56282, 0.45028, 0.60142, 0.47127, 0.75031, 0.38025, 0.77099, 0.40388, 0.78624, 0.42985, 0.78742, 0.45944, 0.78788, 0.48316, 0.80294, 0.35142, 0.83814, 0.3701, 0.86771, 0.39104, 0.89305, 0.41496, 0.89305, 0.44187, 0.88038, 0.46953, 0.8663, 0.4927, 0.71294, 0.481, 0.68824, 0.45825, 0.66295, 0.4347, 0.20479, 0.14207, 0.24368, 0.13937, 0.28764, 0.13354, 0.31384, 0.12546, 0.3316, 0.11536, 0.34005, 0.10336, 0.34259, 0.08675, 0.35358, 0.07048, 0.37218, 0.05836, 0.43298, 0.056, 0.47525, 0.03737, 0.52428, 0.02345, 0.60205, 0.02278, 0.65616, 0.02772, 0.69504, 0.0331, 0.39969, 0.04446, 0.43604, 0.02875, 0.47408, 0.01663, 0.51551, 0.01169, 0.56031, 0.01034, 0.61248, 0.01394, 0.65289, 0.01907, 0.68175, 0.02337, 0.69748, 0.04502, 0.63801, 0.06402, 0.64877, 0.04421, 0.7012, 0.05821, 0.70466, 0.07248, 0.69139, 0.0857, 0.66403, 0.09869, 0.6433, 0.11124, 0.63252, 0.12269, 0.63418, 0.13481, 0.18908, 0.25194, 0.1164, 0.24662], "triangles": [58, 59, 60, 60, 61, 387, 387, 386, 57, 57, 386, 56, 384, 53, 54, 384, 383, 53, 386, 385, 56, 386, 379, 385, 56, 385, 55, 379, 378, 385, 385, 54, 55, 378, 384, 385, 385, 384, 54, 377, 383, 384, 383, 52, 53, 380, 379, 386, 387, 380, 386, 57, 60, 387, 57, 58, 60, 154, 297, 153, 153, 297, 152, 152, 297, 151, 154, 155, 297, 155, 290, 297, 155, 156, 290, 156, 157, 290, 150, 151, 296, 157, 158, 290, 151, 297, 296, 297, 290, 296, 158, 289, 290, 296, 148, 150, 296, 146, 148, 146, 147, 148, 150, 148, 149, 290, 295, 296, 290, 289, 295, 296, 295, 146, 295, 145, 146, 158, 159, 289, 159, 160, 289, 160, 288, 289, 295, 288, 294, 295, 289, 288, 295, 294, 145, 160, 161, 288, 288, 287, 294, 288, 161, 287, 294, 291, 145, 294, 287, 291, 145, 291, 144, 291, 143, 144, 161, 162, 287, 291, 287, 425, 287, 162, 425, 291, 424, 143, 291, 425, 424, 143, 424, 142, 162, 163, 425, 142, 424, 141, 141, 424, 230, 230, 424, 292, 163, 164, 425, 425, 164, 286, 424, 425, 292, 425, 286, 292, 286, 164, 165, 292, 286, 293, 165, 166, 286, 286, 285, 293, 286, 166, 285, 293, 285, 228, 166, 167, 285, 167, 168, 285, 285, 284, 228, 285, 168, 284, 94, 336, 93, 336, 90, 93, 93, 91, 92, 93, 90, 91, 95, 334, 94, 94, 334, 336, 95, 333, 334, 95, 96, 333, 96, 97, 333, 336, 89, 90, 336, 334, 89, 334, 88, 89, 334, 333, 88, 97, 98, 333, 98, 335, 333, 88, 335, 87, 88, 333, 335, 98, 99, 335, 102, 104, 101, 102, 103, 104, 99, 337, 335, 335, 337, 87, 337, 86, 87, 99, 100, 337, 86, 337, 85, 337, 100, 85, 85, 101, 84, 85, 100, 101, 84, 106, 107, 107, 108, 84, 83, 84, 108, 82, 108, 109, 104, 105, 101, 101, 105, 84, 106, 84, 105, 83, 108, 82, 109, 110, 81, 110, 80, 81, 81, 82, 109, 110, 111, 80, 80, 111, 79, 345, 79, 111, 345, 111, 112, 79, 345, 78, 78, 345, 77, 112, 113, 345, 344, 76, 77, 345, 113, 346, 344, 345, 346, 77, 345, 344, 113, 114, 346, 76, 344, 75, 114, 115, 346, 346, 347, 344, 346, 115, 347, 75, 344, 343, 343, 347, 348, 344, 347, 343, 75, 343, 74, 74, 343, 73, 115, 116, 347, 347, 116, 348, 348, 349, 343, 343, 342, 73, 343, 349, 342, 73, 342, 72, 342, 341, 72, 72, 341, 71, 349, 350, 342, 342, 350, 341, 116, 117, 348, 348, 117, 349, 117, 118, 349, 349, 118, 350, 341, 340, 71, 341, 350, 340, 71, 340, 70, 389, 378, 379, 374, 390, 389, 388, 379, 380, 350, 118, 351, 350, 351, 340, 351, 118, 119, 351, 339, 340, 340, 69, 70, 340, 339, 69, 119, 352, 351, 119, 120, 352, 351, 338, 339, 351, 352, 338, 339, 68, 69, 339, 338, 68, 120, 121, 352, 121, 353, 352, 353, 360, 352, 352, 354, 338, 352, 360, 354, 338, 67, 68, 338, 354, 67, 67, 354, 66, 121, 122, 353, 122, 123, 353, 360, 353, 356, 353, 123, 356, 356, 362, 360, 362, 355, 360, 360, 355, 354, 123, 124, 356, 354, 65, 66, 65, 355, 64, 65, 354, 355, 124, 125, 356, 125, 357, 356, 356, 361, 362, 356, 357, 361, 126, 358, 125, 126, 127, 358, 125, 358, 357, 362, 359, 355, 362, 361, 359, 358, 127, 357, 355, 63, 64, 355, 359, 63, 127, 368, 357, 357, 368, 361, 359, 388, 63, 63, 388, 380, 361, 375, 359, 361, 368, 375, 359, 375, 388, 127, 128, 368, 375, 389, 388, 128, 367, 368, 368, 374, 375, 368, 367, 374, 375, 374, 389, 128, 129, 367, 367, 373, 374, 374, 373, 390, 129, 366, 367, 367, 366, 373, 129, 130, 366, 366, 365, 373, 373, 365, 372, 130, 131, 366, 366, 131, 365, 63, 380, 62, 62, 380, 387, 388, 389, 379, 61, 62, 387, 373, 372, 390, 376, 382, 383, 376, 381, 382, 382, 51, 52, 382, 50, 51, 382, 381, 50, 381, 49, 50, 383, 382, 52, 365, 364, 372, 131, 132, 365, 365, 132, 364, 364, 363, 371, 132, 133, 364, 364, 133, 363, 133, 134, 363, 390, 372, 377, 371, 377, 372, 377, 371, 376, 376, 369, 370, 369, 376, 371, 369, 281, 370, 372, 364, 371, 371, 363, 369, 376, 370, 381, 363, 280, 369, 369, 280, 281, 377, 376, 383, 390, 377, 378, 378, 377, 384, 389, 390, 378, 281, 282, 370, 363, 134, 280, 370, 283, 381, 370, 282, 283, 381, 283, 49, 134, 135, 280, 135, 270, 280, 280, 270, 281, 270, 279, 281, 281, 279, 282, 279, 277, 282, 282, 277, 283, 277, 272, 283, 283, 272, 49, 272, 48, 49, 135, 136, 270, 136, 271, 270, 270, 271, 279, 271, 278, 279, 279, 278, 277, 136, 269, 271, 136, 137, 269, 277, 276, 272, 277, 278, 276, 276, 273, 272, 272, 273, 48, 271, 268, 278, 268, 271, 269, 273, 47, 48, 278, 268, 276, 268, 275, 276, 276, 275, 273, 273, 274, 47, 274, 46, 47, 273, 275, 274, 37, 208, 36, 35, 26, 27, 26, 35, 224, 224, 36, 208, 35, 36, 224, 224, 225, 26, 265, 223, 225, 35, 27, 34, 27, 28, 34, 34, 28, 33, 33, 28, 32, 225, 16, 26, 225, 15, 16, 225, 223, 15, 26, 17, 25, 26, 16, 17, 28, 29, 32, 32, 29, 31, 25, 17, 24, 29, 30, 31, 17, 18, 24, 24, 18, 23, 223, 14, 15, 18, 22, 23, 18, 19, 22, 19, 21, 22, 19, 20, 21, 268, 269, 232, 269, 137, 231, 137, 138, 231, 269, 231, 232, 138, 247, 231, 138, 139, 247, 268, 233, 275, 268, 232, 233, 275, 235, 274, 46, 274, 45, 274, 235, 236, 45, 274, 236, 233, 234, 275, 275, 234, 235, 139, 140, 247, 231, 314, 232, 232, 315, 233, 232, 314, 315, 247, 313, 231, 231, 313, 314, 233, 316, 234, 233, 315, 316, 45, 236, 44, 235, 323, 236, 236, 323, 44, 247, 140, 246, 313, 304, 314, 314, 305, 315, 314, 304, 305, 140, 141, 246, 247, 312, 313, 247, 246, 312, 44, 324, 43, 44, 323, 324, 315, 309, 316, 315, 305, 309, 234, 237, 235, 234, 316, 237, 313, 307, 304, 313, 312, 307, 235, 322, 323, 235, 237, 322, 141, 230, 246, 43, 330, 328, 43, 324, 330, 324, 323, 330, 305, 304, 298, 323, 327, 330, 323, 322, 327, 316, 319, 237, 316, 309, 319, 304, 307, 298, 305, 298, 309, 43, 328, 42, 246, 311, 312, 312, 306, 307, 312, 311, 306, 237, 331, 322, 237, 238, 331, 237, 319, 238, 307, 306, 298, 311, 246, 245, 230, 245, 246, 245, 230, 248, 322, 331, 327, 330, 327, 299, 330, 299, 328, 325, 299, 326, 328, 299, 42, 298, 308, 309, 309, 308, 319, 230, 292, 248, 308, 317, 319, 319, 317, 238, 306, 302, 298, 298, 303, 308, 298, 302, 303, 325, 329, 299, 331, 326, 327, 299, 327, 326, 299, 41, 42, 311, 310, 306, 311, 245, 310, 306, 310, 302, 326, 331, 332, 317, 239, 238, 331, 238, 332, 238, 239, 332, 308, 318, 317, 308, 303, 318, 299, 40, 41, 299, 329, 40, 245, 248, 229, 326, 332, 325, 325, 332, 321, 325, 321, 320, 248, 292, 229, 245, 244, 310, 245, 229, 244, 310, 300, 302, 303, 302, 301, 317, 240, 239, 317, 318, 240, 292, 293, 229, 302, 300, 301, 303, 301, 318, 310, 244, 300, 239, 240, 332, 332, 240, 321, 325, 39, 329, 325, 320, 39, 301, 241, 318, 318, 241, 240, 240, 266, 321, 240, 241, 266, 244, 243, 300, 300, 242, 301, 300, 243, 242, 301, 242, 241, 293, 228, 229, 244, 229, 249, 229, 228, 249, 244, 226, 243, 244, 249, 226, 266, 267, 321, 321, 267, 320, 320, 38, 39, 242, 250, 241, 250, 251, 241, 241, 251, 266, 267, 208, 320, 320, 208, 38, 38, 208, 37, 266, 265, 267, 266, 251, 265, 243, 264, 242, 243, 226, 264, 242, 264, 250, 267, 224, 208, 267, 265, 224, 249, 211, 226, 226, 211, 264, 264, 252, 250, 250, 258, 251, 250, 252, 258, 251, 222, 265, 251, 258, 222, 265, 225, 224, 265, 222, 223, 221, 223, 222, 211, 212, 264, 252, 264, 212, 222, 258, 259, 258, 252, 259, 39, 40, 329, 284, 227, 228, 249, 228, 210, 168, 169, 284, 169, 209, 284, 284, 209, 227, 228, 227, 210, 249, 210, 211, 169, 170, 209, 227, 209, 210, 170, 179, 209, 170, 178, 179, 170, 171, 178, 179, 180, 209, 180, 181, 209, 209, 391, 210, 209, 181, 391, 391, 392, 210, 210, 393, 211, 210, 392, 393, 171, 177, 178, 171, 172, 177, 172, 176, 177, 172, 173, 176, 391, 181, 193, 193, 181, 183, 391, 194, 392, 391, 193, 194, 392, 194, 393, 173, 174, 176, 176, 174, 175, 181, 182, 183, 193, 184, 192, 184, 193, 183, 194, 195, 393, 184, 185, 192, 185, 186, 192, 192, 186, 191, 186, 187, 191, 191, 187, 190, 190, 188, 189, 190, 187, 188, 223, 221, 423, 252, 212, 253, 222, 259, 221, 252, 253, 259, 393, 394, 211, 211, 394, 212, 223, 423, 14, 221, 220, 423, 220, 221, 260, 221, 259, 260, 259, 253, 260, 212, 213, 253, 253, 254, 260, 253, 213, 254, 220, 422, 423, 14, 422, 13, 14, 423, 422, 394, 395, 212, 212, 395, 213, 393, 195, 394, 220, 219, 422, 219, 220, 261, 220, 260, 261, 260, 254, 261, 394, 195, 395, 254, 255, 261, 254, 213, 255, 422, 421, 13, 13, 421, 12, 422, 219, 421, 195, 196, 395, 395, 396, 213, 213, 214, 255, 213, 396, 214, 395, 196, 396, 261, 262, 219, 219, 218, 421, 261, 255, 262, 255, 256, 262, 255, 214, 256, 196, 197, 396, 214, 396, 215, 396, 397, 215, 396, 197, 397, 197, 198, 397, 199, 397, 198, 219, 262, 218, 11, 12, 420, 12, 421, 420, 421, 218, 420, 420, 419, 11, 11, 419, 10, 214, 215, 256, 218, 217, 420, 420, 217, 419, 397, 199, 200, 262, 263, 218, 218, 263, 217, 262, 256, 263, 10, 419, 9, 256, 257, 263, 256, 215, 257, 215, 397, 216, 215, 216, 257, 216, 397, 398, 397, 200, 398, 398, 200, 201, 419, 418, 9, 419, 217, 418, 9, 418, 8, 217, 415, 418, 217, 263, 415, 263, 257, 415, 416, 415, 402, 401, 402, 415, 216, 400, 257, 400, 401, 257, 415, 257, 401, 415, 417, 418, 418, 417, 8, 398, 399, 216, 216, 399, 400, 398, 201, 399, 417, 7, 8, 417, 416, 414, 417, 415, 416, 416, 402, 403, 201, 202, 399, 399, 406, 400, 399, 202, 406, 417, 414, 7, 400, 406, 401, 414, 6, 7, 202, 203, 406, 416, 405, 414, 414, 405, 6, 406, 407, 401, 406, 203, 407, 416, 404, 405, 416, 403, 404, 407, 408, 401, 401, 408, 402, 405, 5, 6, 404, 413, 405, 405, 413, 5, 203, 204, 407, 204, 205, 407, 407, 205, 408, 404, 403, 412, 412, 403, 411, 411, 2, 412, 404, 412, 413, 408, 409, 402, 402, 410, 403, 402, 409, 410, 412, 4, 413, 5, 413, 4, 403, 410, 411, 412, 3, 4, 412, 2, 3, 408, 206, 409, 408, 205, 206, 410, 0, 411, 411, 1, 2, 411, 0, 1, 409, 207, 410, 409, 206, 207, 410, 207, 0], "vertices": [2, 6, 180.86, 6.95, 0.99826, 74, -178.69, 57.45, 0.00174, 2, 6, 175.6, -13.7, 0.9966, 74, -183.95, 36.81, 0.0034, 2, 6, 170.29, -22.54, 0.99418, 74, -189.25, 27.97, 0.00582, 2, 6, 172.8, -31.61, 0.997, 74, -186.74, 18.89, 0.003, 2, 6, 169.16, -44.36, 0.99723, 74, -190.39, 6.14, 0.00277, 2, 6, 155.1, -66.5, 0.99668, 74, -204.45, -16, 0.00332, 4, 6, 136.34, -82.22, 0.99248, 5, 229.81, -87.43, 0.00228, 21, 240.32, -65.07, 0.00042, 74, -223.2, -31.72, 0.00481, 4, 6, 110.52, -96.67, 0.9722, 5, 203.45, -100.87, 0.01843, 21, 210.73, -65.41, 0.00327, 74, -249.03, -46.17, 0.0061, 4, 6, 84.49, -109.29, 0.92588, 5, 176.95, -112.47, 0.05652, 21, 181.84, -64.04, 0.00997, 74, -275.05, -58.78, 0.00762, 4, 6, 55.44, -115.57, 0.85017, 5, 147.68, -117.63, 0.11981, 21, 153.33, -55.67, 0.02194, 74, -304.1, -65.07, 0.00808, 4, 6, 34.78, -113.9, 0.7819, 5, 127.1, -115.16, 0.17674, 21, 135.98, -44.32, 0.03416, 74, -324.76, -63.4, 0.00721, 5, 6, 10.07, -100.77, 0.62555, 5, 102.91, -101.09, 0.30235, 4, 302.12, -136.26, 0.00026, 21, 120.55, -20.98, 0.06817, 74, -349.47, -50.27, 0.00368, 5, 6, -5.39, -92.38, 0.45033, 5, 87.79, -92.1, 0.42295, 4, 292.55, -121.5, 0.00535, 21, 110.99, -6.22, 0.12013, 74, -364.93, -41.88, 0.00124, 5, 6, -22.87, -91.85, 0.24402, 5, 70.35, -90.9, 0.5132, 4, 277.45, -112.68, 0.02391, 21, 95.89, 2.61, 0.21874, 74, -382.41, -41.35, 0.00013, 4, 6, -38.74, -96.54, 0.10349, 5, 54.31, -94.97, 0.47297, 4, 261.28, -109.21, 0.05249, 21, 79.72, 6.08, 0.37106, 5, 6, -53.27, -107.1, 0.01256, 5, 39.38, -104.96, 0.27911, 4, 243.46, -111.53, 0.06546, 21, 61.9, 3.75, 0.61908, 56, 30.09, 32.03, 0.02379, 4, 5, 34.58, -116.74, 0.12917, 4, 233.93, -119.96, 0.03632, 21, 52.37, -4.67, 0.73403, 56, 27.87, 19.51, 0.10048, 4, 5, 35.78, -128.09, 0.05876, 4, 229.97, -130.66, 0.00995, 21, 48.41, -15.37, 0.73768, 56, 31.42, 8.67, 0.19361, 4, 5, 43.56, -135.18, 0.03435, 4, 233.79, -140.46, 0.00092, 21, 52.23, -25.18, 0.68624, 56, 40.5, 3.36, 0.27849, 3, 5, 51.95, -133.3, 0.02805, 21, 60.59, -27.22, 0.62458, 56, 48.32, 6.95, 0.34737, 3, 5, 61.7, -124.66, 0.03727, 21, 73.16, -23.81, 0.51165, 56, 56.05, 17.44, 0.45108, 3, 5, 59.69, -134.11, 0.02334, 21, 67.17, -31.38, 0.58579, 56, 56.06, 7.78, 0.39087, 4, 5, 51.33, -141.09, 0.01383, 4, 238.14, -149.21, 2e-05, 21, 56.57, -33.93, 0.68464, 56, 49.34, -0.79, 0.30151, 4, 5, 44.44, -143.41, 0.0137, 4, 230.93, -148.23, 0.00045, 21, 49.37, -32.95, 0.73915, 56, 43.09, -4.5, 0.2467, 4, 5, 34.99, -141.75, 0.02888, 4, 223.2, -142.55, 0.00291, 21, 41.64, -27.26, 0.7784, 56, 33.5, -4.86, 0.1898, 4, 5, 24.33, -137.6, 0.0306, 4, 215.49, -134.1, 0.01571, 21, 33.93, -18.82, 0.86352, 56, 22.22, -3.03, 0.09017, 2, 4, 212.51, -123.32, 0.09577, 21, 30.94, -8.04, 0.90423, 3, 4, 209.78, -132.1, 0.07187, 21, 28.22, -16.81, 0.90833, 56, 16.5, -5.02, 0.01979, 2, 21, 30.88, -24.18, 0.89956, 56, 23.16, -9.13, 0.10044, 3, 4, 216.32, -144.88, 0.00359, 21, 34.76, -29.6, 0.8652, 56, 29.56, -10.96, 0.13121, 3, 4, 222.72, -150.75, 0, 21, 41.16, -35.46, 0.82479, 56, 38.23, -11.58, 0.17521, 3, 4, 215.74, -151.31, 0, 21, 34.18, -36.02, 0.88235, 56, 33.11, -16.36, 0.11765, 2, 21, 29.3, -33.05, 0.91754, 56, 27.43, -17.06, 0.08246, 2, 21, 24.03, -28.72, 0.95734, 56, 20.62, -16.94, 0.04266, 3, 4, 203.09, -137.53, 0.10404, 21, 21.53, -22.24, 0.88727, 56, 14.63, -13.43, 0.00869, 2, 4, 201.97, -131.74, 0.13377, 21, 20.41, -16.45, 0.86623, 1, 21, 5.98, -18.16, 1, 3, 4, 166.51, -125.18, 0.47307, 21, -15.05, -9.9, 0.52253, 76, 78.62, 193.38, 0.00441, 5, 4, 146.53, -116.57, 0.70626, 21, -35.03, -1.29, 0.08327, 71, 64, 0.63, 0.09747, 70, 50.86, -28.01, 0.08773, 76, 58.64, 201.98, 0.02527, 3, 4, 128.67, -140.1, 0.78524, 71, 46.14, -22.9, 0.19631, 76, 40.78, 178.45, 0.01845, 3, 4, 93.06, -156.91, 0.77903, 72, 19.66, -19.05, 0.19476, 76, 5.17, 161.65, 0.02621, 4, 4, 71.49, -160.68, 0.7772, 13, -249.76, 78.34, 0, 72, -1.92, -22.82, 0.1943, 76, -16.4, 157.88, 0.0285, 4, 4, 50.13, -155.72, 0.77864, 13, -227.91, 76.53, 0.0004, 72, -23.27, -17.86, 0.19476, 76, -37.76, 162.84, 0.0262, 5, 4, 26.35, -137.96, 0.7709, 3, 151.04, -131.7, 0.01321, 13, -201.81, 62.4, 0.00277, 71, -56.18, -20.76, 0.19672, 76, -61.54, 180.6, 0.0164, 3, 4, 11.34, -107.69, 0.67243, 3, 131.23, -104.33, 0.12757, 70, -84.33, -19.12, 0.2, 3, 4, 0.47, -98.58, 0.61479, 3, 119, -97.15, 0.25521, 70, -95.21, -10.02, 0.13, 3, 4, -17.53, -92.63, 0.48023, 3, 100.27, -94.25, 0.46977, 70, -113.2, -4.06, 0.05, 3, 4, -40.44, -87.36, 0.28523, 3, 76.8, -92.85, 0.69416, 13, -128.4, 21.99, 0.02061, 3, 4, -66.18, -81.01, 0.10448, 3, 50.37, -90.84, 0.81355, 13, -102.01, 19.44, 0.08197, 5, 4, -106.87, -75.2, 0.00637, 21, -288.43, 40.08, 0.0026, 3, 9.27, -91.85, 0.65569, 7, -28.02, 92.32, 0.01813, 13, -60.9, 19.58, 0.3172, 4, 21, -316.1, 25.41, 7e-05, 3, -15.59, -110.91, 0.30068, 13, -35.65, 38.11, 0.676, 14, -111.73, -6.14, 0.02325, 3, 3, -43.77, -129.89, 0.07266, 13, -7.07, 56.49, 0.73075, 14, -90.29, 20.22, 0.19658, 2, 13, 27.86, 72.49, 0.48989, 14, -62.05, 46.28, 0.51011, 3, 13, 69.1, 82.35, 0.16427, 9, 62.5, 254.71, 0.0008, 14, -25.92, 68.46, 0.83493, 3, 13, 109.48, 85.82, 0.04756, 9, 102.18, 246.4, 0.0022, 14, 11.39, 84.31, 0.95024, 1, 14, 48.2, 92.62, 1, 1, 14, 89.46, 96.51, 1, 1, 14, 157.02, 93.84, 1, 1, 14, 200.78, 96.54, 1, 2, 9, 259.11, 137.14, 0.03588, 14, 202.6, 84.34, 0.96412, 2, 9, 254.64, 136.6, 0.03895, 14, 199.25, 81.34, 0.96105, 3, 9, 238.49, 114.16, 0.44765, 14, 198.82, 53.7, 0.54127, 78, -221.73, -212.24, 0.01108, 3, 9, 219.49, 90.73, 0.74831, 14, 196.62, 23.61, 0.23951, 78, -250.82, -204.24, 0.01218, 1, 9, 194.86, 69.3, 1, 1, 9, 221.51, 70.57, 1, 1, 9, 247.8, 66.53, 1, 1, 9, 317.94, 59.76, 1, 1, 9, 358.96, 57.51, 1, 1, 9, 414.21, 51.2, 1, 2, 9, 441.63, 47.43, 0.91753, 10, -40.58, 27.5, 0.08247, 2, 9, 470.79, 41.42, 0.54072, 10, -14.69, 42.21, 0.45928, 2, 9, 491.3, 26.54, 0.18765, 10, 10.54, 44.53, 0.81235, 2, 9, 505.56, 3.85, 0.01349, 10, 36.22, 36.86, 0.98651, 1, 10, 63.13, 23.15, 1, 1, 10, 82.79, 18.24, 1, 1, 10, 101.1, 12.23, 1, 1, 10, 153.22, 12.1, 1, 1, 10, 228.93, 9.31, 1, 1, 10, 326.51, 10.55, 1, 1, 10, 358.83, 11.59, 1, 1, 10, 389.19, 15.44, 1, 2, 10, 413.81, 23.49, 0.88291, 12, 183.53, -30.7, 0.11709, 2, 10, 433.59, 31.64, 0.40512, 12, 162.14, -31.32, 0.59488, 2, 10, 451.11, 38.85, 0.09051, 12, 143.21, -31.88, 0.90949, 2, 10, 464.16, 39.43, 0.01791, 12, 130.79, -27.8, 0.98209, 1, 12, 94.09, -31.06, 1, 2, 12, 57.31, -31.35, 0.97635, 11, 98.53, -65.33, 0.02365, 2, 12, 25.56, -35.3, 0.67779, 11, 79.79, -39.39, 0.32221, 2, 12, 9.43, -39.6, 0.28794, 11, 68.27, -27.32, 0.71206, 2, 12, -4.12, -48.36, 0.0439, 11, 54.07, -19.65, 0.9561, 2, 12, -14.56, -60.85, 0.00337, 11, 38.1, -16.5, 0.99663, 1, 11, 5.3, -12.53, 1, 1, 11, -5.41, -2.01, 1, 1, 11, 7.66, 10.58, 1, 2, 12, -49.22, -13.65, 0.0023, 11, 62.8, 36.58, 0.9977, 2, 12, -42.32, 11.44, 0.14167, 11, 88.12, 42.61, 0.85833, 2, 12, -30.39, 32.63, 0.41759, 11, 112.44, 42.33, 0.58241, 2, 12, -17.94, 44.08, 0.57666, 11, 128.47, 36.93, 0.42334, 2, 12, 6.65, 43.06, 0.8163, 11, 139.4, 14.88, 0.1837, 2, 12, 35.64, 40.9, 0.98836, 11, 151.45, -11.58, 0.01164, 1, 12, 66.36, 46.13, 1, 1, 12, 83.81, 56.6, 1, 1, 12, 10.81, 84.9, 1, 1, 12, 11.73, 99.62, 1, 1, 12, 85.73, 75.23, 1, 1, 12, 137.39, 69.8, 1, 2, 10, 471.61, -52.07, 0.02011, 12, 156.19, 60.42, 0.97989, 2, 10, 447.84, -38.34, 0.20396, 12, 173.57, 39.17, 0.79605, 2, 10, 441.76, -31.53, 0.36692, 12, 176.85, 30.64, 0.63308, 2, 10, 429.07, -30.37, 0.70943, 12, 188.3, 25.08, 0.29057, 2, 10, 408.5, -33.22, 0.97709, 12, 208.56, 20.46, 0.02291, 1, 10, 371.61, -42.33, 1, 1, 10, 342.36, -50.88, 1, 1, 10, 209.39, -92.24, 1, 1, 10, 169.5, -101.22, 1, 2, 9, 477.38, -152.54, 0.0094, 10, 118.1, -99.32, 0.9906, 2, 9, 447.45, -110.79, 0.11544, 10, 68.08, -87.65, 0.88456, 2, 9, 429.08, -85.78, 0.3153, 10, 37.77, -80.95, 0.6847, 2, 9, 417.53, -71.67, 0.53474, 10, 19.79, -77.96, 0.46526, 2, 9, 407.3, -66.92, 0.69227, 10, 8.97, -81.13, 0.30773, 2, 9, 389.03, -63.4, 0.87184, 10, -7.09, -90.52, 0.12816, 2, 9, 360.5, -65.12, 0.97787, 10, -27.42, -110.62, 0.02213, 2, 9, 329.35, -71.04, 0.99941, 10, -46.94, -135.6, 0.00059, 1, 9, 253.13, -88.4, 1, 1, 9, 226.67, -93.03, 1, 1, 9, 173.91, -102.28, 1, 1, 9, 166.31, -126.37, 1, 1, 9, 90.09, -116.73, 1, 2, 8, 126.18, -112.02, 0.01452, 9, 19.73, -109.14, 0.98548, 2, 8, 78.6, -107.56, 0.11916, 9, -24.54, -91.16, 0.88084, 2, 8, 37.88, -96.4, 0.32883, 9, -60.33, -68.73, 0.67117, 2, 8, 11.39, -81.54, 0.52926, 9, -81.41, -46.87, 0.47074, 5, 4, -118.34, 170.86, 0.00061, 3, -42.76, 148.92, 0.02967, 8, -20.75, -55.86, 0.72042, 9, -104.78, -13.02, 0.24909, 17, 325.58, 142.97, 0.00022, 6, 4, -100.18, 148.77, 0.01198, 3, -21.2, 130.13, 0.21651, 7, -2.22, -130.26, 0.00029, 8, -41.91, -36.62, 0.70091, 9, -119.5, 11.5, 0.06688, 17, 298.99, 153.5, 0.00343, 6, 4, -82.26, 129.69, 0.04375, 3, -0.37, 114.28, 0.44685, 7, -22.71, -113.97, 0.03034, 8, -62.4, -20.34, 0.46551, 9, -134.43, 33, 0.00205, 17, 274.07, 161.5, 0.0115, 4, 4, -43.85, 120.66, 0.18201, 3, 39, 111.74, 0.66051, 7, -62.02, -110.6, 0.00949, 8, -101.71, -16.97, 0.14799, 4, 4, -28.74, 116.91, 0.29913, 3, 54.53, 110.54, 0.67376, 7, -77.52, -109.07, 0.00223, 8, -117.21, -15.44, 0.02488, 2, 4, 0.24, 125.32, 0.51666, 3, 81.72, 123.64, 0.48334, 2, 4, 23.81, 129.84, 0.66743, 3, 104.21, 131.99, 0.33257, 4, 16, -205.33, 8.97, 0.03829, 4, 51.72, 141.25, 0.7321, 3, 129.85, 147.87, 0.16612, 17, 162.49, 86.43, 0.06349, 4, 16, -179.54, 18.38, 0.05223, 4, 77.52, 150.66, 0.69588, 3, 153.73, 161.41, 0.05548, 17, 144.49, 65.7, 0.1964, 4, 16, -162.95, 22.63, 0.09327, 4, 94.1, 154.91, 0.44176, 8, -233.26, -70.83, 0.01097, 17, 132.04, 53.94, 0.454, 3, 4, 78.85, 165.71, 0.21761, 8, -216.6, -79.3, 0.00384, 17, 150.62, 51.9, 0.77855, 4, 4, 58.6, 183.83, 0.01401, 8, -193.94, -94.31, 0.00053, 17, 177.12, 45.86, 0.97906, 18, 46, 78.38, 0.00641, 3, 4, 38.03, 188.94, 0.00162, 17, 197.58, 51.36, 0.69064, 18, 51.47, 57.9, 0.30774, 4, 4, 31.66, 199.14, 0.00034, 8, -165.07, -105.55, 1e-05, 17, 208.1, 45.53, 0.59785, 18, 45.62, 47.4, 0.4018, 3, 4, 17.59, 198.94, 0, 17, 220.3, 52.52, 0.23382, 18, 52.59, 35.18, 0.76618, 2, 17, 220.55, 59.69, 0.12579, 18, 59.75, 34.92, 0.87421, 1, 18, 65.56, 29.83, 1, 1, 18, 114.31, 18.49, 1, 1, 18, 113.7, 1.64, 1, 1, 18, 112.87, -21.59, 1, 1, 18, 112.03, -44.81, 1, 1, 18, 46.82, -72.66, 1, 1, 18, 24.39, -61.87, 1, 1, 18, 17.45, -54.14, 1, 1, 18, -2.93, -53.36, 1, 2, 17, 304.55, -19.24, 0.24611, 18, -19.33, -48.94, 0.75389, 2, 17, 292.66, -31.01, 0.47801, 18, -31.07, -37.02, 0.52199, 2, 17, 271.26, -36.55, 0.72666, 18, -36.57, -15.61, 0.27334, 1, 17, 240.38, -39.62, 1, 1, 17, 216.38, -39.88, 1, 1, 17, 177.81, -40.3, 1, 1, 17, 113.29, -35.58, 1, 1, 17, 89.24, -37.21, 1, 1, 17, 65.34, -38.44, 1, 2, 16, -38.58, 60.67, 0.01263, 17, 41.7, -39.62, 0.98737, 2, 16, -17.95, 53.76, 0.11769, 17, 20.31, -43.57, 0.88231, 2, 16, -2.55, 45.8, 0.32497, 17, 2.98, -44.08, 0.67503, 2, 16, 15.08, 32.05, 0.79694, 17, -19.11, -40.6, 0.20306, 2, 16, 29.06, 35.44, 0.96328, 17, -29.69, -50.33, 0.03672, 3, 16, 42.65, 38.13, 0.87523, 17, -40.27, -59.27, 0.00227, 55, 33.39, 35.34, 0.1225, 2, 16, 56.05, 34.38, 0.74138, 55, 45, 27.67, 0.25862, 3, 16, 66.13, 29.92, 0.62973, 4, 323.18, 162.2, 0, 55, 53.24, 20.35, 0.37027, 4, 16, 73.03, 23.02, 0.55218, 4, 330.08, 155.3, 0, 17, -74.17, -60.79, 1e-05, 55, 57.7, 11.67, 0.44782, 4, 16, 71.42, 19.99, 0.58615, 4, 328.47, 152.27, 0, 17, -74.23, -57.36, 1e-05, 55, 55.24, 9.28, 0.41384, 4, 16, 62.67, 24.69, 0.68382, 4, 319.72, 156.97, 0, 17, -64.3, -57.23, 1e-05, 55, 48.35, 16.43, 0.31617, 3, 16, 53.76, 26.83, 0.78943, 17, -55.47, -54.78, 1e-05, 55, 40.52, 21.18, 0.21056, 4, 16, 44.6, 24.7, 0.90721, 4, 301.65, 156.98, 0, 17, -48.49, -48.47, 0.0025, 55, 31.14, 21.95, 0.09028, 4, 16, 39.01, 19.95, 0.94828, 4, 296.06, 152.23, 0.00019, 17, -45.9, -41.61, 0.00581, 55, 24.37, 19.14, 0.04572, 3, 16, 47.31, 15.76, 0.93927, 4, 304.36, 148.04, 0.00073, 55, 31, 12.61, 0.06, 4, 5, -12.53, 148.8, 0.02201, 16, 52.54, 6.62, 0.93862, 4, 309.59, 138.9, 0.00188, 55, 33.18, 2.32, 0.03748, 4, 5, -8.81, 157.88, 0.03491, 16, 59.9, 13.1, 0.90872, 4, 316.95, 145.38, 0.0003, 55, 42.17, 6.24, 0.05607, 3, 5, 4.64, 163.74, 0.05148, 16, 74.55, 12.39, 0.81084, 55, 55.9, 1.08, 0.13768, 3, 5, 17.34, 165.37, 0.05297, 16, 86.65, 8.21, 0.73101, 55, 66.15, -6.59, 0.21601, 3, 5, 28.13, 164.5, 0.05763, 16, 95.94, 2.64, 0.6577, 55, 73.29, -14.73, 0.28467, 3, 5, 36.48, 160.66, 0.06562, 16, 101.71, -4.51, 0.58606, 55, 76.61, -23.3, 0.34832, 3, 5, 44.64, 154.48, 0.0742, 16, 106.28, -13.67, 0.50477, 55, 78.16, -33.42, 0.42103, 4, 5, 48.92, 148.12, 0.08045, 16, 107.3, -21.27, 0.44652, 4, 364.35, 111.01, 0, 55, 76.8, -40.97, 0.47303, 4, 5, 46.72, 142.18, 0.09178, 16, 102.69, -25.61, 0.45483, 4, 359.75, 106.67, 0, 55, 71.09, -43.7, 0.45339, 4, 5, 42.33, 148.48, 0.08217, 16, 101.55, -18.02, 0.4906, 4, 358.6, 114.26, 0, 55, 72.32, -36.12, 0.42723, 3, 5, 35.76, 152.69, 0.07887, 16, 97.54, -11.33, 0.555, 55, 70.54, -28.52, 0.36613, 4, 5, 26.17, 151.27, 0.09079, 16, 88.31, -8.35, 0.62885, 4, 345.36, 123.93, 0.00032, 55, 62.67, -22.86, 0.28004, 4, 5, 19.64, 141.27, 0.16381, 16, 78.02, -14.41, 0.67992, 4, 335.07, 117.87, 0.00322, 55, 51.02, -25.5, 0.15305, 5, 6, -78.73, 125.19, 0.02845, 5, 22.91, 128.14, 0.3204, 16, 75.12, -27.63, 0.58532, 4, 332.17, 104.65, 0.01127, 55, 44.22, -37.19, 0.05455, 4, 6, -67.95, 117.79, 0.09074, 5, 33.4, 120.33, 0.45395, 16, 81.05, -39.28, 0.44334, 4, 338.1, 93, 0.01196, 5, 6, -46.81, 122.34, 0.20956, 5, 54.7, 124.06, 0.48908, 16, 101.79, -45.39, 0.29671, 4, 358.84, 86.89, 0.00197, 74, -406.36, 172.84, 0.00268, 5, 6, -30.95, 133.5, 0.28349, 5, 70.98, 134.6, 0.44519, 16, 121.07, -43.18, 0.26382, 74, -390.49, 184.01, 0.00658, 75, -350.46, 184.01, 0.00092, 5, 6, -14.43, 142.49, 0.34172, 5, 87.83, 142.94, 0.42097, 16, 139.87, -43.18, 0.22432, 74, -373.98, 192.99, 0.01024, 75, -333.94, 192.99, 0.00275, 5, 6, 2.94, 143.74, 0.38625, 5, 105.24, 143.52, 0.39787, 16, 155.72, -50.38, 0.19927, 74, -356.61, 194.25, 0.01251, 75, -316.57, 194.25, 0.00409, 5, 6, 19.08, 135.92, 0.4441, 5, 121.07, 135.08, 0.36642, 16, 166.16, -64.97, 0.17317, 74, -340.46, 186.42, 0.01195, 75, -300.43, 186.42, 0.00436, 5, 6, 42.67, 120.14, 0.61055, 5, 144.03, 118.4, 0.27931, 16, 179.33, -90.11, 0.09706, 74, -316.88, 170.64, 0.00933, 75, -276.84, 170.64, 0.00375, 5, 6, 70.6, 109.2, 0.80221, 5, 171.51, 106.39, 0.1487, 16, 198.63, -113.08, 0.03921, 74, -288.94, 159.7, 0.00606, 75, -248.91, 159.7, 0.00382, 5, 6, 102.78, 100.31, 0.91266, 5, 203.33, 96.26, 0.05122, 16, 222.64, -136.28, 0.0275, 74, -256.76, 150.81, 0.00409, 75, -216.73, 150.81, 0.00452, 5, 6, 129.56, 91.3, 0.9682, 5, 229.73, 86.22, 0.01397, 16, 241.85, -156.99, 0.0112, 74, -229.99, 141.8, 0.00143, 75, -189.96, 141.8, 0.00521, 4, 6, 149.35, 78.84, 0.98703, 5, 249.04, 73, 0.00307, 16, 253.28, -177.4, 0.00484, 75, -170.16, 129.34, 0.00506, 3, 6, 168.79, 54.53, 0.99542, 16, 258.73, -208.05, 0.00084, 75, -150.72, 105.03, 0.00375, 2, 6, 178.66, 28.29, 0.99852, 75, -140.85, 78.79, 0.00148, 4, 4, 172.22, -93.07, 0.54813, 21, -9.34, 22.22, 0.22938, 70, 76.55, -4.5, 0.19438, 76, 84.33, 225.49, 0.02812, 3, 16, 25.57, 12.52, 0.95877, 4, 282.62, 144.8, 0.00071, 17, -37.75, -28.6, 0.04053, 2, 16, 22.26, -26.61, 0.79581, 4, 279.31, 105.67, 0.20419, 3, 5, -12.87, 70.04, 0.19014, 16, 17.27, -63.8, 0.35162, 4, 274.32, 68.48, 0.45824, 3, 5, 21.58, 47.2, 0.72796, 16, 38.01, -99.56, 0.08778, 4, 295.06, 32.72, 0.18426, 4, 6, -48.24, 39.04, 0.08439, 5, 50.06, 40.88, 0.90344, 4, 317.76, 14.41, 0.0096, 74, -407.78, 89.55, 0.00257, 3, 6, -21.5, 39.92, 0.22266, 5, 76.81, 40.72, 0.76906, 74, -381.04, 90.42, 0.00828, 3, 6, 7.62, 46.09, 0.5798, 5, 106.15, 45.76, 0.40313, 74, -351.92, 96.59, 0.01706, 3, 6, 37.48, 52.43, 0.82279, 5, 136.22, 50.94, 0.16211, 74, -322.07, 102.93, 0.0151, 3, 6, 43.44, -47.66, 0.89349, 5, 138.31, -49.3, 0.09815, 74, -316.1, 2.85, 0.00836, 3, 6, 11.51, -45.03, 0.62821, 5, 106.5, -45.44, 0.36342, 74, -348.04, 5.48, 0.00837, 4, 6, -18.38, -43.37, 0.24929, 5, 76.7, -42.63, 0.73844, 4, 304.58, -72.24, 0.00672, 74, -377.92, 7.13, 0.00555, 5, 6, -47.23, -41.47, 0.0744, 5, 47.95, -39.61, 0.81801, 4, 280.15, -56.78, 0.0954, 21, 98.59, 58.51, 0.00989, 74, -406.77, 9.04, 0.0023, 3, 5, 26.13, -38.46, 0.63648, 4, 261.11, -46.06, 0.3546, 21, 79.55, 69.22, 0.00892, 2, 5, 6.5, -42.04, 0.23435, 4, 241.93, -40.55, 0.76565, 5, 6, -67.2, -83.64, 0.00757, 5, 26.36, -80.98, 0.33841, 4, 242.44, -84.27, 0.19221, 21, 60.88, 31.02, 0.45012, 76, 154.55, 234.29, 0.0117, 3, 4, 200.27, -92.03, 0.42304, 21, 18.71, 23.26, 0.55263, 76, 112.38, 226.53, 0.02433, 4, 5, 9.7, -94.29, 0.128, 4, 221.6, -88.8, 0.2509, 21, 40.04, 26.49, 0.60169, 76, 133.71, 229.76, 0.01941, 3, 16, -21.86, -67.78, 0.28079, 4, 235.19, 64.5, 0.69154, 76, 147.3, 383.06, 0.02767, 3, 16, 11.52, -1.84, 0.88091, 4, 268.57, 130.44, 0.09749, 76, 180.68, 449, 0.0216, 4, 16, -20.22, -11.95, 0.54467, 4, 236.83, 120.33, 0.20305, 17, -9.56, 15, 0.22294, 76, 148.94, 438.89, 0.02933, 4, 16, -65.53, -15.42, 0.2894, 4, 191.52, 116.86, 0.56809, 17, 28.39, 40, 0.10578, 76, 103.63, 435.42, 0.03673, 4, 16, -135.34, 4.91, 0.08116, 4, 121.71, 137.19, 0.63722, 17, 99.3, 56.06, 0.25839, 76, 33.82, 455.75, 0.02323, 4, 4, 41.68, 88.05, 0.61195, 3, 128.75, 93.73, 0.16073, 67, -71.1, 59.56, 0.19317, 76, -46.21, 406.61, 0.03414, 4, 4, 27.49, 46.73, 0.62012, 3, 121.6, 50.63, 0.14994, 67, -85.29, 18.23, 0.19252, 76, -60.4, 365.28, 0.03742, 3, 4, 26.8, 9.03, 0.768, 67, -85.98, -19.47, 0.192, 76, -61.09, 327.59, 0.04, 3, 4, 35.47, -24.88, 0.768, 67, -77.31, -53.37, 0.192, 76, -52.42, 293.68, 0.04, 4, 4, 27.51, -57.24, 0.68742, 3, 138.82, -51.9, 0.08058, 70, -68.17, 31.33, 0.192, 76, -60.38, 261.32, 0.04, 4, 4, 12.3, -81.44, 0.59155, 3, 127.83, -78.28, 0.18704, 70, -83.37, 7.13, 0.19465, 76, -75.59, 237.12, 0.02675, 4, 4, 58.77, -45.07, 0.7776, 67, -54.01, -73.57, 0.096, 70, -36.9, 43.5, 0.0864, 76, -29.12, 273.49, 0.04, 4, 4, 89.52, -53.79, 0.7776, 67, -23.26, -82.29, 0.096, 70, -6.15, 34.78, 0.0864, 76, 1.63, 264.77, 0.04, 4, 4, 119.31, -50.98, 0.7776, 67, 6.53, -79.47, 0.096, 70, 23.64, 37.59, 0.0864, 76, 31.42, 267.58, 0.04, 4, 4, 145.49, -37.62, 0.7776, 67, 32.71, -66.11, 0.096, 70, 49.82, 50.95, 0.0864, 76, 57.6, 280.94, 0.04, 3, 4, 168.89, -15.65, 0.768, 67, 56.11, -44.15, 0.192, 76, 81, 302.91, 0.04, 4, 16, -66.34, -114.9, 0.06073, 4, 190.71, 17.38, 0.70727, 67, 77.93, -11.12, 0.192, 76, 102.82, 335.93, 0.04, 4, 16, -56.91, -78.13, 0.10978, 4, 200.14, 54.15, 0.65822, 67, 87.36, 25.65, 0.192, 76, 112.25, 372.71, 0.04, 4, 16, -70.11, -41.69, 0.18125, 4, 186.94, 90.59, 0.58675, 67, 74.16, 62.09, 0.192, 76, 99.05, 409.14, 0.04, 4, 16, -108.07, -19.62, 0.13368, 4, 148.98, 112.66, 0.63705, 67, 36.2, 84.16, 0.19268, 76, 61.09, 431.22, 0.0366, 4, 16, -150.47, -9.59, 0.06666, 4, 106.58, 122.69, 0.70768, 67, -6.2, 94.19, 0.19359, 76, 18.69, 441.24, 0.03206, 4, 4, 71.61, 114.87, 0.69811, 3, 153.83, 125.14, 0.0771, 67, -41.17, 86.38, 0.1938, 76, -16.28, 433.43, 0.03099, 4, 16, -107.84, -8.93, 0.15991, 4, 149.21, 123.35, 0.683, 17, 68.54, 54.84, 0.12428, 76, 61.32, 441.91, 0.0328, 3, 16, -20.39, -40.91, 0.36021, 4, 236.67, 91.37, 0.6113, 76, 148.78, 409.93, 0.02849, 2, 4, 220.26, -6.9, 0.97267, 76, 132.37, 311.66, 0.02733, 2, 4, 214.67, -27.74, 0.97572, 76, 126.78, 290.82, 0.02428, 3, 5, -1.02, -2.76, 0.13021, 4, 252.62, -2.02, 0.84953, 76, 164.73, 316.54, 0.02026, 3, 5, 23.44, 5.17, 0.98018, 74, -433, 52.84, 0.00125, 76, 190.17, 312.79, 0.01857, 3, 5, 48.2, 9.26, 0.97361, 74, -408.41, 57.87, 0.00542, 76, 214.17, 305.46, 0.02097, 4, 6, -22.2, 11.39, 0.04479, 5, 75, 12.24, 0.92687, 74, -381.75, 61.89, 0.00909, 76, 239.52, 296.23, 0.01924, 4, 6, 8.06, 15.79, 0.72073, 5, 105.41, 15.47, 0.24586, 74, -351.49, 66.3, 0.01529, 76, 268.2, 285.63, 0.01812, 4, 6, 39.19, 20.41, 0.93922, 5, 136.69, 18.88, 0.02699, 74, -320.36, 70.91, 0.01565, 76, 297.74, 274.8, 0.01814, 3, 5, 1.64, -32.11, 0.12462, 4, 241.98, -29.5, 0.85934, 76, 154.09, 289.06, 0.01603, 4, 5, 24.53, -28.25, 0.65725, 4, 264.2, -36.2, 0.32664, 74, -430.62, 19.49, 0.00101, 76, 176.31, 282.36, 0.0151, 5, 6, -46.58, -27.53, 0.04773, 5, 49.13, -25.71, 0.87131, 4, 287.38, -44.85, 0.0597, 74, -406.13, 22.97, 0.00362, 76, 199.49, 273.71, 0.01764, 5, 6, -20.04, -24.93, 0.13825, 5, 75.76, -24.13, 0.8372, 4, 311.94, -55.25, 0.00106, 74, -379.59, 25.58, 0.00599, 76, 224.05, 263.31, 0.01751, 4, 6, 10.07, -23.78, 0.65372, 5, 105.89, -24.15, 0.31774, 74, -349.48, 26.73, 0.01159, 76, 251.04, 249.92, 0.01695, 4, 6, 40.38, -23.5, 0.94223, 5, 136.19, -25.04, 0.02815, 74, -319.16, 27, 0.01197, 76, 277.8, 235.67, 0.01765, 3, 16, -19.48, -105.34, 0.14637, 4, 237.57, 26.94, 0.82841, 76, 149.68, 345.49, 0.02522, 3, 4, 212.92, -61.28, 0.73414, 21, 31.35, 54, 0.24336, 76, 125.03, 257.27, 0.0225, 3, 4, 166.67, -44.29, 0.7721, 70, 70.99, 44.28, 0.19303, 76, 78.78, 274.27, 0.03487, 4, 4, 180.45, -68.2, 0.69893, 21, -1.11, 47.09, 0.07613, 70, 84.78, 20.37, 0.19377, 76, 92.56, 250.36, 0.03116, 5, 4, 5.71, 19.15, 0.56072, 3, 104.68, 19.84, 0.30295, 67, -107.07, -9.34, 0.09596, 76, -82.18, 337.71, 0.03999, 78, -362.23, 210.69, 0.00037, 5, 4, 5.08, 75.88, 0.48722, 3, 94.67, 75.67, 0.43184, 67, -107.7, 47.38, 0.04837, 76, -82.81, 394.44, 0.0293, 78, -416.79, 195.17, 0.00327, 5, 4, -51.52, 72.84, 0.11351, 3, 39.36, 63.31, 0.8252, 7, -61.36, -62.17, 0.02765, 76, -139.41, 391.4, 0.02107, 78, -398.98, 141.36, 0.01258, 5, 4, -26.53, 72.31, 0.25007, 3, 64.09, 66.93, 0.7143, 7, -86.17, -65.27, 0.00218, 76, -114.42, 390.87, 0.02539, 78, -405.05, 165.61, 0.00806, 5, 4, -63.71, -56.97, 0.07698, 3, 48.82, -66.73, 0.89321, 7, -68.09, 68.04, 0.00102, 76, -151.6, 261.58, 0.01854, 78, -270.53, 163.72, 0.01025, 4, 4, -37.82, -62.85, 0.23752, 3, 75.33, -68.24, 0.73261, 76, -125.71, 255.71, 0.02367, 78, -271.67, 190.24, 0.00619, 5, 4, -11.89, -71.48, 0.4418, 3, 102.32, -72.46, 0.43384, 70, -107.57, 17.09, 0.09729, 76, -99.78, 247.08, 0.02464, 78, -270.15, 217.53, 0.00244, 6, 4, -1.07, -33.19, 0.50382, 3, 106.66, -32.91, 0.36205, 67, -113.85, -61.69, 0.04557, 70, -96.74, 55.37, 0.04797, 76, -88.96, 285.36, 0.03998, 78, -309.94, 217.91, 0.00061, 4, 4, -31.98, -25.56, 0.11449, 3, 74.91, -30.5, 0.84055, 76, -119.87, 293, 0.03979, 78, -309.18, 186.08, 0.00517, 4, 4, -60.86, -17.69, 0.0063, 3, 45.13, -27.52, 0.94791, 76, -148.75, 300.87, 0.03484, 78, -309.18, 156.14, 0.01095, 4, 4, -26.11, 21.93, 0.06877, 3, 72.84, 17.31, 0.88517, 76, -114, 340.49, 0.03975, 78, -356.54, 179.26, 0.00631, 5, 4, -56.46, 25.88, 0.01118, 3, 42.26, 16.18, 0.93933, 7, -63.28, -14.99, 0.00209, 76, -144.35, 344.44, 0.03519, 78, -352.38, 148.94, 0.01222, 7, 4, -93.13, 82.84, 0.02418, 3, -3.34, 66.28, 0.57786, 7, -18.74, -66.04, 0.22441, 8, -58.43, 27.59, 0.13278, 17, 260.87, 207.74, 0.00677, 76, -181.02, 401.4, 0.01355, 78, -397.7, 98.59, 0.02046, 6, 4, -102.19, 32.29, 0.00024, 3, -3.9, 14.93, 0.65225, 7, -17.1, -14.72, 0.29997, 17, 244.29, 256.35, 0.00031, 76, -190.08, 350.85, 0.02586, 78, -346.54, 103.13, 0.02136, 5, 21, -288.72, 103.37, 7e-05, 3, -1.49, -29.49, 0.68861, 7, -18.58, 29.74, 0.26559, 76, -195.05, 306.65, 0.02543, 78, -302.59, 109.95, 0.0203, 7, 4, -106.98, -51.63, 0.00288, 21, -288.54, 63.66, 0.00194, 3, 5.26, -68.62, 0.77016, 7, -24.5, 69.01, 0.07918, 13, -57.38, -3.73, 0.11802, 76, -194.87, 266.93, 0.01141, 78, -264.32, 120.56, 0.01642, 3, 16, -4.98, 19.75, 0.53389, 17, -7.52, -20.12, 0.44043, 76, 164.18, 470.59, 0.02568, 2, 17, 22.48, -18.11, 0.97619, 76, 136.97, 483.38, 0.02381, 2, 17, 59.94, -12.4, 0.97747, 76, 101.43, 496.55, 0.02253, 2, 17, 208.37, -17.85, 0.99563, 76, -25.75, 573.26, 0.00437, 2, 17, 231.35, -16.64, 0.84993, 18, -16.6, 24.26, 0.15007, 2, 17, 254.75, -8.59, 0.5463, 18, -8.58, 0.85, 0.4537, 2, 17, 274.1, 10.13, 0.16872, 18, 10.09, -18.54, 0.83128, 5, 4, 52.89, 217.15, 0.00059, 8, -183.47, -126.45, 2e-05, 17, 198.26, 19.48, 0.89686, 18, 19.59, 57.29, 0.09936, 76, -35, 535.71, 0.00318, 5, 16, -92.9, 18.71, 0.14285, 4, 164.15, 150.99, 0.29229, 8, -303.13, -77.09, 0.00206, 17, 68.87, 23.41, 0.53359, 76, 76.26, 469.55, 0.02922, 5, 16, -60.28, 2.65, 0.20964, 4, 196.77, 134.93, 0.40184, 8, -337.74, -65.92, 0.00027, 17, 32.56, 21.65, 0.35501, 76, 108.88, 453.49, 0.03324, 2, 17, 217.47, 21.52, 0.65683, 18, 21.59, 38.07, 0.34317, 2, 17, 230.13, 28.13, 0.43358, 18, 28.18, 25.4, 0.56642, 2, 17, 236.8, 40.12, 0.0616, 18, 40.16, 18.71, 0.9384, 1, 18, 33.44, -25.38, 1, 3, 4, 102.18, 44.75, 0.68625, 69, -0.3, 2.42, 0.22875, 76, 14.29, 363.31, 0.085, 4, 4, 73.05, -142.21, 0.71451, 13, -248.63, 59.84, 6e-05, 72, -0.35, -4.35, 0.23819, 76, -14.84, 176.35, 0.04724, 4, 16, -86.03, -78.98, 0.05955, 4, 171.02, 53.3, 0.69245, 68, 61.41, 18.4, 0.188, 76, 83.13, 371.86, 0.06, 4, 16, -92.63, -114.02, 0.02308, 4, 164.42, 18.26, 0.72892, 68, 54.81, -16.64, 0.188, 76, 76.53, 336.82, 0.06, 3, 4, 135.58, 52.37, 0.74, 69, 33.1, 10.04, 0.185, 76, 47.69, 370.93, 0.075, 3, 4, 132.76, 26.1, 0.74, 69, 30.28, -16.23, 0.185, 76, 44.87, 344.66, 0.075, 3, 4, 74.47, 61.07, 0.74, 69, -28.01, 18.73, 0.185, 76, -13.42, 379.62, 0.075, 3, 4, 70.65, 35.2, 0.74, 69, -31.83, -7.14, 0.185, 76, -17.24, 353.75, 0.075, 3, 4, 119.42, 72.49, 0.74, 69, 16.95, 30.16, 0.185, 76, 31.53, 391.05, 0.075, 3, 4, 92.17, 76.67, 0.74, 69, -10.31, 34.34, 0.185, 76, 4.28, 395.23, 0.075, 3, 4, 111.31, 10.49, 0.74, 69, 8.84, -31.84, 0.185, 76, 23.42, 329.05, 0.075, 3, 4, 84.44, 14.88, 0.74, 69, -18.04, -27.45, 0.185, 76, -3.45, 333.44, 0.075, 4, 16, -102.39, -48.84, 0.10221, 4, 154.66, 83.44, 0.64979, 68, 45.05, 48.54, 0.188, 76, 66.77, 401.99, 0.06, 4, 16, -133.11, -34.82, 0.06911, 4, 123.94, 97.46, 0.68289, 68, 14.33, 62.56, 0.188, 76, 36.05, 416.02, 0.06, 5, 16, -163.91, -33.66, 0.01025, 4, 93.14, 98.62, 0.73962, 3, 177.75, 112.67, 0.00213, 68, -16.47, 63.72, 0.188, 76, 5.25, 417.18, 0.06, 4, 4, 66.09, 84.42, 0.69878, 3, 153.42, 94.2, 0.05322, 68, -43.52, 49.52, 0.188, 76, -21.8, 402.98, 0.06, 4, 4, 52.36, 55.91, 0.70838, 3, 144.61, 63.8, 0.04362, 68, -57.25, 21.01, 0.188, 76, -35.53, 374.46, 0.06, 3, 4, 49.02, 25.61, 0.752, 68, -60.59, -9.29, 0.188, 76, -38.87, 344.17, 0.06, 3, 4, 59.9, -2.82, 0.752, 68, -49.71, -37.72, 0.188, 76, -27.99, 315.74, 0.06, 3, 4, 115.12, -24.39, 0.752, 68, 5.51, -59.29, 0.188, 76, 27.23, 294.17, 0.06, 3, 4, 144.76, -12.13, 0.752, 68, 35.15, -47.03, 0.188, 76, 56.87, 306.43, 0.06, 3, 4, 81.23, -18.95, 0.752, 68, -28.37, -53.85, 0.188, 76, -6.66, 299.61, 0.06, 4, 4, 143.58, -111.7, 0.75548, 21, -37.98, 3.58, 0.01686, 71, 61.05, 5.5, 0.19309, 76, 55.69, 206.86, 0.03457, 3, 4, 139.84, -87.11, 0.76745, 71, 57.31, 30.09, 0.19186, 76, 51.95, 231.45, 0.04069, 3, 4, 57.34, -69.11, 0.764, 71, -25.19, 48.09, 0.191, 76, -30.54, 249.45, 0.045, 4, 4, 35.16, -86.55, 0.70769, 3, 151.22, -79.54, 0.05701, 71, -47.37, 30.65, 0.19117, 76, -52.73, 232.01, 0.04413, 4, 4, 24.56, -113.57, 0.71179, 3, 145.24, -107.93, 0.06089, 71, -57.98, 3.64, 0.19317, 76, -63.33, 204.99, 0.03414, 3, 4, 108.26, -125.38, 0.76244, 72, 34.86, 12.47, 0.19061, 76, 20.37, 193.17, 0.04695, 3, 4, 88.35, -109.65, 0.76, 72, 14.95, 28.2, 0.19, 76, 0.46, 208.9, 0.05, 3, 4, 62.07, -107.78, 0.76, 72, -11.33, 30.08, 0.19, 76, -25.82, 210.78, 0.05, 4, 4, 39.99, -139.08, 0.76765, 13, -215.47, 61.53, 0.0016, 72, -33.41, -1.22, 0.19231, 76, -47.9, 179.48, 0.03844, 3, 4, 106.5, -143.05, 0.76754, 72, 33.1, -5.19, 0.19189, 76, 18.61, 175.51, 0.04057, 4, 4, 43.87, -120.47, 0.75973, 3, 165.43, -111.54, 0.0029, 72, -29.53, 17.39, 0.19066, 76, -44.02, 198.09, 0.04672, 3, 4, 83.28, -64.55, 0.764, 71, 0.74, 52.65, 0.191, 76, -4.61, 254.01, 0.045, 3, 4, 116.58, -71.61, 0.764, 71, 34.05, 45.59, 0.191, 76, 28.69, 246.94, 0.045, 2, 12, -2.19, 1.58, 0.44481, 11, 98.78, 2.68, 0.55519, 1, 11, 78.76, 8.72, 1, 1, 12, 20.1, 4.54, 1, 1, 11, 55.57, 6.91, 1, 1, 12, 48.6, 5.47, 1, 2, 9, 405.66, 35.34, 0.98, 78, -226.84, -396.99, 0.02, 3, 9, 434.76, 33.72, 0.94374, 10, -36.72, 12.66, 0.03626, 78, -216.64, -424.28, 0.02, 3, 9, 464.91, 25.34, 0.50154, 10, -8.52, 26.23, 0.47846, 78, -212.21, -455.26, 0.02, 3, 9, 483.99, 8.71, 0.0958, 10, 16.79, 26.3, 0.8842, 78, -219.79, -479.41, 0.02, 2, 10, 42.39, 16.03, 0.98, 78, -237.31, -500.72, 0.02, 2, 10, 72.71, 0.29, 0.98, 78, -261.46, -524.87, 0.02, 2, 10, 142.72, -4.36, 0.98, 78, -287.03, -590.22, 0.02, 1, 10, 326.47, -12.52, 1, 2, 10, 159.89, -56.8, 0.98, 78, -342.2, -590.76, 0.02, 3, 9, 490.91, -107.06, 0.02376, 10, 98.3, -56.2, 0.95624, 78, -323.04, -532.22, 0.02, 3, 9, 470.45, -71.42, 0.10842, 10, 59.43, -42.89, 0.87158, 78, -298.62, -499.18, 0.02, 3, 9, 460.29, -49.87, 0.19642, 10, 37.58, -33.38, 0.78358, 78, -282.96, -481.22, 0.02, 3, 9, 444.26, -34.32, 0.44315, 10, 15.28, -32.25, 0.53685, 78, -275.15, -460.3, 0.02, 3, 9, 422.69, -23.44, 0.82534, 10, -8.12, -38.28, 0.15466, 78, -273.84, -436.17, 0.02, 3, 9, 398.83, -20.17, 0.93864, 10, -28.22, -51.55, 0.04136, 78, -280.42, -413, 0.02, 2, 9, 302.8, -33.5, 0.98, 78, -331.17, -330.4, 0.02, 2, 9, 300.69, 32.61, 0.98, 78, -271.47, -301.94, 0.02, 2, 9, 211.48, 35.27, 0.98, 78, -304.84, -219.16, 0.02, 2, 9, 212.95, -51.24, 0.98, 78, -383.48, -255.22, 0.02, 2, 9, 140.98, -61.26, 0.98, 78, -421.54, -193.33, 0.02, 1, 9, 154.18, -104.72, 1, 2, 9, 160.21, 34.45, 0.98, 78, -326.17, -172.53, 0.02, 2, 9, 297.56, -0.01, 0.973, 78, -302.6, -312.16, 0.027, 2, 9, 143.77, -9.39, 0.973, 78, -372.92, -175.06, 0.027, 2, 9, 203, -6.24, 0.973, 78, -346.26, -228.05, 0.027, 7, 4, -118.94, 99.72, 0.00723, 3, -31.58, 78.66, 0.26178, 7, 9.24, -79.01, 0.24383, 8, -30.45, 14.62, 0.45416, 17, 291.62, 205.48, 0.00236, 76, -206.83, 418.28, 0.00738, 78, -407.2, 69.25, 0.02325, 4, 8, -0.03, -6.26, 0.79562, 9, -70.65, 28.5, 0.18216, 76, -233.9, 443.35, 0.00127, 78, -424.27, 36.54, 0.02095, 3, 8, 31.03, -25.79, 0.46744, 9, -46.53, 0.85, 0.51129, 78, -439.92, 3.35, 0.02127, 3, 8, 69.85, -44.35, 0.12315, 9, -14.71, -28.11, 0.85712, 78, -453.67, -37.42, 0.01973, 3, 8, 120.59, -49.55, 0.00382, 9, 32.38, -47.7, 0.97663, 78, -452.72, -88.42, 0.01955, 2, 9, 84.24, -59.56, 0.98145, 78, -442.76, -140.68, 0.01855, 6, 3, -38.73, 22.8, 0.04205, 7, 17.56, -23.31, 0.91002, 9, -69.75, 108.21, 0.00046, 17, 279.75, 260.53, 1e-05, 76, -223.12, 364.37, 0.01899, 78, -350.9, 67.7, 0.02848, 4, 3, -35.21, -36.81, 0.15596, 7, 15.29, 36.36, 0.79831, 76, -229.52, 305, 0.01856, 78, -291.94, 77.13, 0.02718, 5, 7, 55.18, -37.85, 0.84483, 8, 15.49, 55.78, 0.05648, 9, -37.91, 83.45, 0.05751, 76, -258.24, 384.2, 0.00964, 78, -360.81, 28.6, 0.03155, 4, 7, 94.43, -52.56, 0.50511, 8, 54.74, 41.07, 0.07375, 9, -4.56, 58.05, 0.39147, 78, -370.68, -12.14, 0.02966, 4, 7, 131.25, -60.28, 0.20187, 8, 91.56, 33.35, 0.01129, 9, 28.47, 40.04, 0.761, 78, -373.92, -49.62, 0.02584, 2, 9, 68.12, 31.4, 0.97795, 78, -365.92, -89.41, 0.02205, 2, 9, 110.68, 33.15, 0.97869, 78, -347.24, -127.68, 0.02131, 5, 7, 55.9, 48.32, 0.81812, 13, 23.02, -24.42, 0.10892, 14, -36.54, -47.34, 0.03291, 76, -271.43, 299.04, 0.0083, 78, -275.17, 38.26, 0.03175, 3, 7, 99.88, 53.07, 0.88092, 14, 3.79, -29.16, 0.08865, 78, -265.17, -4.84, 0.03043, 4, 7, 147.77, 54.7, 0.47439, 9, 77.42, 145.39, 0.11631, 14, 48.8, -12.73, 0.38507, 78, -257.78, -52.17, 0.02423, 4, 7, 201.39, 48.77, 0.06946, 9, 127.06, 124.26, 0.36133, 14, 101.6, -1.69, 0.54662, 78, -257.21, -106.12, 0.0226, 4, 7, 244.34, 43.79, 0.00127, 9, 166.76, 107.11, 0.66433, 14, 143.98, 6.92, 0.31879, 78, -256.99, -149.36, 0.01561, 7, 21, -321.09, 57.38, 8e-05, 3, -25.8, -80.21, 0.40883, 7, 6.79, 79.94, 0.07881, 13, -26.09, 7.2, 0.47209, 14, -93.04, -32.54, 0.01155, 76, -227.42, 260.65, 0.00399, 78, -249.7, 90.81, 0.02466, 4, 7, 42.67, 92.75, 0.11467, 13, 9.79, 20.01, 0.71701, 14, -62.92, -9.22, 0.13866, 78, -232.67, 56.74, 0.02967, 3, 13, 49.39, 29.62, 0.45337, 14, -28.27, 12.22, 0.51484, 78, -218.36, 18.58, 0.03178, 4, 13, 94.16, 36.55, 0.13592, 9, 73.3, 203.63, 0.00572, 14, 12.13, 32.72, 0.83039, 78, -206.09, -25.02, 0.02797, 3, 9, 118.24, 183.94, 0.06936, 14, 60.26, 42.24, 0.90616, 78, -206.09, -74.08, 0.02449, 3, 9, 161.96, 158.08, 0.19323, 14, 110.91, 46.01, 0.78338, 78, -212.22, -124.51, 0.0234, 3, 9, 197.92, 134.89, 0.32226, 14, 153.68, 47.52, 0.6601, 78, -219.04, -166.75, 0.01764, 3, 7, 236.06, 8.25, 0.04776, 9, 148.59, 75.47, 0.93758, 78, -293.26, -145.42, 0.01466, 4, 7, 193.44, 1.38, 0.30633, 9, 105.79, 81.17, 0.64432, 14, 108.78, -49.21, 0.02767, 78, -305.22, -103.94, 0.02168, 4, 7, 149.35, -5.6, 0.51809, 9, 61.56, 87.18, 0.36254, 14, 69.04, -69.55, 0.09533, 78, -317.46, -61.01, 0.02404, 3, 5, -8.93, 130.73, 0.11115, 16, 47.74, -11.17, 0.86677, 4, 304.79, 121.11, 0.02208, 3, 5, -0.48, 113.21, 0.25717, 16, 47.53, -30.62, 0.68781, 4, 304.58, 101.66, 0.05502, 4, 6, -86.28, 91.11, 0.03096, 5, 14.06, 94.38, 0.44701, 16, 52.2, -53.95, 0.44391, 4, 309.25, 78.33, 0.07812, 4, 6, -69.02, 82.17, 0.08668, 5, 30.95, 84.77, 0.59969, 16, 63.08, -70.06, 0.26799, 4, 320.13, 62.22, 0.04565, 4, 6, -49.12, 78.05, 0.16968, 5, 50.68, 79.89, 0.69685, 16, 78.59, -83.19, 0.12134, 4, 335.64, 49.09, 0.01214, 3, 6, -26.9, 79.11, 0.28838, 5, 72.92, 80.09, 0.66295, 16, 98.61, -92.88, 0.04867, 3, 6, 2.84, 84.9, 0.51598, 5, 102.87, 84.72, 0.48349, 16, 127.5, -102.02, 0.00053, 2, 6, 32.94, 86.56, 0.70981, 5, 133.01, 85.22, 0.29019, 3, 6, 56.51, 82.89, 0.83203, 5, 156.42, 80.65, 0.16456, 74, -303.03, 133.4, 0.00341, 3, 6, 67.49, 55.25, 0.92225, 5, 166.32, 52.6, 0.06493, 74, -292.06, 105.75, 0.01282, 3, 6, 105.25, 43.17, 0.98691, 5, 203.58, 39.07, 0.00776, 74, -254.3, 93.67, 0.00533, 2, 6, 135.4, 25.93, 0.97581, 74, -224.14, 76.43, 0.02419, 2, 6, 145.27, -10.42, 0.9723, 74, -214.27, 40.08, 0.0277, 2, 6, 142.55, -37.98, 0.97101, 74, -216.99, 12.53, 0.02899, 4, 6, 137.34, -58.55, 0.97507, 5, 231.72, -63.82, 0.00029, 21, 252.52, -44.75, 7e-05, 74, -222.21, -8.05, 0.02457, 3, 6, 84.24, 75.77, 0.92821, 5, 183.86, 72.46, 0.06509, 74, -275.3, 126.28, 0.0067, 3, 6, 116.17, 65.26, 0.97196, 5, 215.35, 60.72, 0.01277, 74, -243.38, 115.76, 0.01527, 3, 6, 141.91, 52.43, 0.98296, 5, 240.58, 46.91, 0.00089, 74, -217.63, 102.94, 0.01615, 3, 6, 155.29, 35, 0.98631, 16, 237.53, -218.74, 0.0004, 74, -204.25, 85.5, 0.01329, 2, 6, 162.68, 14.47, 0.9852, 74, -196.86, 64.97, 0.0148, 2, 6, 162.12, -11.61, 0.98067, 74, -197.43, 38.89, 0.01933, 2, 6, 157.53, -32.8, 0.97993, 74, -202.01, 17.7, 0.02007, 2, 6, 153.12, -48.2, 0.98075, 74, -206.43, 2.3, 0.01925, 4, 6, 116.48, -64.71, 0.97344, 5, 210.64, -69.16, 0.00434, 21, 231.25, -40.19, 0.00089, 74, -243.06, -14.21, 0.02133, 3, 6, 76.14, -44.69, 0.97474, 5, 171.1, -47.59, 0.0174, 74, -283.41, 5.82, 0.00786, 3, 6, 112.48, -41.43, 0.97342, 5, 207.54, -45.74, 0.00078, 74, -247.06, 9.08, 0.0258, 4, 6, 93.49, -72, 0.95945, 5, 187.39, -75.56, 0.02072, 21, 207.58, -35.6, 0.00398, 74, -266.05, -21.5, 0.01586, 4, 6, 68.56, -79.63, 0.91062, 5, 162.18, -82.22, 0.0674, 21, 182.04, -30.38, 0.01213, 74, -290.98, -29.12, 0.00986, 4, 6, 43.63, -78.94, 0.83047, 5, 137.29, -80.56, 0.15273, 21, 160.47, -17.85, 0.01024, 74, -315.92, -28.43, 0.00656, 4, 6, 17.53, -71.51, 0.67154, 5, 111.5, -72.13, 0.31478, 21, 141.1, 1.16, 0.00592, 74, -342.01, -21, 0.00776, 6, 6, -7.04, -67.02, 0.41969, 5, 87.12, -66.7, 0.53111, 4, 303.23, -98.44, 0.00518, 21, 121.67, 16.85, 0.03679, 74, -366.58, -16.52, 0.00686, 76, 215.34, 220.12, 0.00037, 6, 6, -28.55, -66.75, 0.21659, 5, 65.64, -65.6, 0.6331, 4, 284.47, -87.92, 0.03091, 21, 102.9, 27.37, 0.11129, 74, -388.09, -16.25, 0.00525, 76, 196.58, 230.64, 0.00285, 6, 6, -49.87, -72.63, 0.08461, 5, 44.11, -70.65, 0.52853, 4, 262.93, -82.88, 0.11039, 21, 81.37, 32.4, 0.26845, 74, -409.42, -22.13, 0.0016, 76, 175.04, 235.67, 0.00641, 6, 16, -143.52, 48.81, 0.07899, 4, 113.53, 181.09, 0.1619, 8, -248.69, -99.55, 0.00115, 17, 127.74, 21.62, 0.69824, 18, 21.86, 127.8, 0.04467, 76, 25.64, 499.65, 0.01505, 2, 17, 126.69, -14.85, 0.98515, 76, 44.24, 531.04, 0.01485], "hull": 208, "edges": [8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 40, 42, 82, 84, 84, 86, 90, 92, 92, 94, 94, 96, 96, 98, 106, 108, 108, 110, 128, 130, 130, 132, 132, 134, 140, 142, 150, 152, 152, 154, 160, 162, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 222, 224, 224, 226, 226, 228, 242, 244, 250, 252, 256, 258, 266, 268, 268, 270, 270, 272, 272, 274, 296, 298, 304, 306, 308, 310, 314, 316, 316, 318, 318, 320, 324, 326, 412, 414, 410, 412, 414, 0, 0, 2, 6, 8, 2, 4, 4, 6, 406, 408, 408, 410, 402, 404, 404, 406, 398, 400, 400, 402, 336, 338, 332, 334, 334, 336, 326, 328, 310, 312, 312, 314, 306, 308, 294, 296, 290, 292, 292, 294, 288, 290, 286, 288, 282, 284, 284, 286, 278, 280, 280, 282, 274, 276, 276, 278, 262, 264, 264, 266, 258, 260, 260, 262, 252, 254, 254, 256, 244, 246, 238, 240, 240, 242, 234, 236, 236, 238, 228, 230, 230, 232, 232, 234, 216, 218, 218, 220, 220, 222, 154, 156, 158, 160, 156, 158, 162, 164, 164, 166, 166, 168, 146, 148, 148, 150, 142, 144, 144, 146, 138, 140, 134, 136, 136, 138, 124, 126, 126, 128, 122, 124, 114, 116, 116, 118, 118, 120, 120, 122, 110, 112, 102, 104, 104, 106, 98, 100, 100, 102, 86, 88, 88, 90, 12, 14, 14, 16, 24, 26, 26, 28, 28, 30, 30, 32, 38, 40, 36, 38, 32, 34, 34, 36, 42, 44, 44, 46, 60, 62, 50, 52, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 66, 66, 68, 68, 70, 418, 420, 418, 338, 420, 422, 422, 424, 424, 426, 426, 428, 428, 430, 430, 432, 434, 436, 436, 438, 438, 440, 440, 442, 442, 444, 446, 450, 450, 448, 70, 72, 72, 74, 74, 76, 454, 456, 456, 458, 462, 464, 464, 466, 466, 468, 470, 472, 468, 474, 474, 476, 476, 478, 478, 480, 480, 482, 482, 484, 484, 486, 486, 488, 488, 490, 490, 492, 462, 494, 494, 492, 496, 458, 480, 532, 532, 534, 472, 88, 470, 474, 320, 322, 322, 324, 298, 300, 592, 300, 300, 302, 302, 304, 302, 594, 328, 330, 330, 332, 416, 76, 534, 416, 600, 602, 604, 606, 608, 610, 612, 614, 616, 618, 618, 610, 616, 606, 604, 612, 614, 608, 600, 620, 620, 622, 622, 624, 624, 626, 626, 628, 628, 630, 630, 632, 634, 636, 636, 602, 632, 638, 638, 634, 640, 642, 644, 646, 646, 648, 652, 654, 650, 652, 650, 658, 654, 660, 660, 656, 648, 86, 78, 80, 80, 82, 658, 80, 656, 84, 644, 662, 662, 664, 664, 642, 664, 480, 76, 78, 640, 78, 188, 190, 190, 192, 192, 194, 174, 176, 176, 178, 194, 196, 196, 198, 198, 200, 172, 174, 168, 170, 170, 172, 676, 678, 678, 680, 680, 682, 682, 684, 684, 686, 686, 688, 688, 690, 690, 692, 692, 694, 694, 696, 696, 698, 698, 700, 700, 702, 702, 704, 704, 706, 676, 708, 708, 710, 706, 712, 246, 248, 248, 250, 712, 714, 710, 718, 720, 724, 724, 722, 112, 114, 396, 398, 396, 394, 394, 392, 392, 390, 390, 388, 388, 386, 386, 384, 384, 382, 382, 380, 380, 378, 378, 376, 376, 374, 372, 374, 370, 372, 368, 370, 368, 366, 366, 364, 364, 362, 362, 360, 360, 358, 358, 356, 356, 354, 354, 352, 352, 350, 346, 348, 350, 348, 342, 344, 344, 346, 338, 340, 340, 342, 432, 800, 800, 802, 802, 804, 804, 806, 806, 808, 434, 830, 830, 832, 832, 808, 640, 534, 582, 848, 848, 584, 572, 850, 850, 574], "width": 484, "height": 1823}}, "earring": {"earring": {"type": "mesh", "uvs": [0.9005, 0.095, 0.96103, 0.83457, 0.74911, 0.96873, 0.39792, 0.96824, 0.17523, 0.87138, 0.04939, 0.71265, 0.0491, 0.38542, 0.22574, 0.16025, 0.5302, 0.02969, 0.8, 0.03175], "triangles": [1, 4, 5, 5, 6, 1, 0, 7, 8, 0, 8, 9, 6, 7, 0, 0, 1, 6, 3, 4, 1, 2, 3, 1], "vertices": [-1.64, 3.75, 20.97, 7.73, 25.61, 4.03, 26.44, -2.94, 24, -7.73, 19.42, -10.82, 9.36, -12.06, 2, -9.4, -2.76, -3.84, -3.35, 1.52], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 20, "height": 31}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.11456, 0.26848, 0.06388, 0.71045, 0.5137, 0.93997, 0.80302, 0.85207, 0.94662, 0.27092, 0.52003, 0.0707], "triangles": [3, 5, 4, 2, 1, 0, 0, 5, 2, 3, 2, 5], "vertices": [2, 6, 63.29, -42.02, 0.972, 74, -296.26, 8.49, 0.028, 2, 6, 49.09, -43.45, 0.972, 74, -310.45, 7.05, 0.028, 2, 6, 45.78, -61.34, 0.97343, 74, -313.76, -10.84, 0.02657, 2, 6, 50.99, -71.11, 0.97755, 74, -308.56, -20.6, 0.02245, 2, 6, 70.31, -71.99, 0.97868, 74, -289.24, -21.49, 0.02132, 2, 6, 72.9, -55.16, 0.97309, 74, -286.64, -4.65, 0.02691], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 37, "height": 32}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.11534, 0.08934, 0.65629, 0.05233, 0.95381, 0.65481, 0.8336, 0.94474, 0.37529, 0.90978, 0.05072, 0.50882], "triangles": [3, 4, 2, 4, 5, 1, 4, 1, 2, 1, 5, 0], "vertices": [2, 6, 74.2, 25.56, 0.966, 74, -285.34, 76.06, 0.034, 2, 6, 82.05, -1.49, 0.96623, 74, -277.49, 49.02, 0.03377, 2, 6, 63.34, -21.82, 0.96673, 74, -296.2, 28.68, 0.03327, 2, 6, 51.18, -18.28, 0.9667, 74, -308.36, 32.23, 0.0333, 2, 6, 46.98, 5.22, 0.9663, 74, -312.57, 55.72, 0.0337, 2, 6, 57.91, 25.16, 0.966, 74, -301.63, 75.66, 0.034], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 52, "height": 38}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 36, -6.55, -10.04, 0.97509, 74, -304.75, -13.83, 0.02491, 2, 36, -10.47, 6.51, 0.9723, 74, -308.67, 2.71, 0.0277, 2, 36, 6.07, 10.42, 0.97236, 74, -292.13, 6.63, 0.02764, 2, 36, 9.99, -6.12, 0.97519, 74, -288.21, -9.91, 0.0248], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 17}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.8251, 0.16618, 0.93461, 0.27272, 0.98332, 0.46216, 0.84633, 0.53517, 0.82023, 0.7107, 0.73463, 0.87232, 0.5623, 0.96407, 0.3381, 0.96141, 0.21027, 0.85021, 0.11581, 0.7419, 0.02847, 0.64766, 0.02512, 0.55986, 0.10863, 0.41831, 0.18871, 0.2975, 0.31549, 0.19602, 0.4695, 0.17327, 0.64071, 0.18174, 0.73125, 0.04384, 0.0874, 0.6195, 0.15413, 0.4883, 0.25534, 0.3672, 0.37762, 0.31269, 0.50014, 0.31136, 0.61027, 0.32833, 0.73676, 0.50849, 0.71999, 0.62017, 0.65654, 0.73365, 0.3883, 0.78608, 0.27168, 0.74472, 0.16748, 0.69448, 0.54052, 0.78643, 0.69689, 0.40952], "triangles": [16, 17, 0, 22, 15, 16, 21, 14, 15, 21, 15, 22, 23, 22, 16, 20, 13, 14, 20, 14, 21, 31, 16, 0, 31, 0, 1, 23, 16, 31, 3, 24, 31, 20, 19, 12, 20, 12, 13, 31, 1, 3, 2, 3, 1, 11, 12, 19, 18, 11, 19, 25, 31, 24, 10, 11, 18, 28, 29, 19, 18, 19, 29, 3, 25, 24, 4, 25, 3, 25, 26, 23, 25, 23, 31, 9, 18, 29, 10, 18, 9, 28, 20, 21, 28, 19, 20, 27, 21, 22, 28, 21, 27, 22, 30, 27, 23, 30, 22, 23, 26, 30, 8, 29, 28, 9, 29, 8, 4, 26, 25, 5, 26, 4, 7, 28, 27, 8, 28, 7, 6, 30, 26, 6, 26, 5, 27, 30, 6, 7, 27, 6], "vertices": [2, 6, 71.19, -70.6, 0.97897, 74, -288.35, -20.1, 0.02103, 2, 6, 69.28, -75, 0.98024, 74, -290.27, -24.49, 0.01976, 2, 6, 64.69, -77.83, 0.98048, 74, -294.85, -27.33, 0.01952, 2, 6, 61.67, -73.62, 0.97934, 74, -297.87, -23.12, 0.02066, 2, 6, 56.85, -73.83, 0.97908, 74, -302.69, -23.32, 0.02092, 2, 6, 51.91, -71.92, 0.97844, 74, -307.63, -21.41, 0.02156, 2, 6, 48.11, -66.62, 0.9769, 74, -311.43, -16.12, 0.0231, 2, 6, 46.37, -58.97, 0.97468, 74, -313.17, -8.46, 0.02532, 2, 6, 48.26, -53.92, 0.97324, 74, -311.28, -3.42, 0.02676, 2, 6, 50.35, -50.03, 0.97211, 74, -309.2, 0.47, 0.02789, 2, 6, 52.12, -46.47, 0.972, 74, -307.43, 4.03, 0.028, 2, 6, 54.4, -45.81, 0.972, 74, -305.15, 4.7, 0.028, 2, 6, 58.79, -47.77, 0.972, 74, -300.75, 2.73, 0.028, 2, 6, 62.61, -49.75, 0.97272, 74, -296.93, 0.76, 0.02728, 2, 6, 66.3, -53.43, 0.97399, 74, -293.24, -2.93, 0.02601, 2, 6, 68.14, -58.54, 0.9755, 74, -291.4, -8.03, 0.0245, 2, 6, 69.3, -64.42, 0.9772, 74, -290.25, -13.92, 0.0228, 2, 6, 73.65, -66.65, 0.97784, 74, -285.89, -16.14, 0.02216, 2, 6, 53.33, -48.3, 0.972, 74, -306.21, 2.2, 0.028, 2, 6, 57.32, -49.76, 0.97232, 74, -302.23, 0.75, 0.02768, 2, 6, 61.32, -52.45, 0.97339, 74, -298.23, -1.95, 0.02661, 2, 6, 63.73, -56.27, 0.97465, 74, -295.81, -5.77, 0.02535, 2, 6, 64.76, -60.44, 0.9759, 74, -294.79, -9.94, 0.0241, 2, 6, 65.2, -64.3, 0.97701, 74, -294.34, -13.79, 0.02299, 2, 6, 61.49, -69.72, 0.97824, 74, -298.06, -19.22, 0.02176, 2, 6, 58.42, -69.85, 0.97803, 74, -301.12, -19.35, 0.02197, 2, 6, 54.93, -68.39, 0.97744, 74, -304.62, -17.89, 0.02256, 2, 6, 51.38, -59.58, 0.97489, 74, -308.16, -9.08, 0.02511, 2, 6, 51.53, -55.36, 0.97366, 74, -308.01, -4.85, 0.02634, 2, 6, 52.01, -51.49, 0.97254, 74, -307.53, -0.99, 0.02746, 2, 6, 52.6, -64.77, 0.97637, 74, -306.94, -14.27, 0.02363, 2, 6, 63.77, -67.75, 0.97786, 74, -295.78, -17.25, 0.02214], "hull": 18, "edges": [2, 4, 8, 10, 26, 28, 32, 34, 28, 30, 30, 32, 2, 0, 0, 34, 22, 24, 24, 26, 20, 22, 18, 20, 14, 16, 16, 18, 10, 12, 12, 14, 4, 6, 6, 8, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52, 54, 56, 56, 58, 58, 36, 52, 60, 60, 54, 46, 62, 62, 48], "width": 35, "height": 27}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 35, -6.86, -10.71, 0.966, 74, -299.17, 45.04, 0.034, 2, 35, -11, 6.8, 0.966, 74, -303.32, 62.55, 0.034, 2, 35, 6.51, 10.95, 0.966, 74, -285.8, 66.7, 0.034, 2, 35, 10.66, -6.56, 0.966, 74, -281.65, 49.19, 0.034], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.52222, 0.14047, 0.69374, 0.17522, 0.83317, 0.27674, 0.92618, 0.44939, 0.97784, 0.67387, 0.98359, 0.88889, 0.92488, 0.96759, 0.77226, 0.96933, 0.61121, 0.96748, 0.43645, 0.92112, 0.28619, 0.85836, 0.14636, 0.67728, 0.12978, 0.47306, 0.00998, 0.26445, 0.08526, 0.1502, 0.27351, 0.09454, 0.40264, 0.01833, 0.9076, 0.83714, 0.9076, 0.67269, 0.85829, 0.50986, 0.76321, 0.36658, 0.62587, 0.29656, 0.47562, 0.28517, 0.34297, 0.33727, 0.23263, 0.44799, 0.30541, 0.60268, 0.40519, 0.72479, 0.55074, 0.80621, 0.68104, 0.83877, 0.80312, 0.84366], "triangles": [22, 16, 0, 23, 15, 16, 21, 0, 1, 22, 0, 21, 22, 23, 16, 20, 1, 2, 21, 1, 20, 24, 14, 15, 24, 15, 23, 12, 13, 14, 19, 20, 2, 24, 12, 14, 3, 19, 2, 25, 24, 23, 18, 19, 3, 18, 3, 4, 11, 12, 24, 11, 24, 25, 22, 25, 23, 26, 25, 22, 21, 26, 22, 27, 21, 20, 28, 27, 20, 19, 28, 20, 27, 26, 21, 17, 18, 4, 18, 28, 19, 18, 29, 28, 17, 29, 18, 10, 11, 25, 10, 25, 26, 17, 4, 5, 9, 26, 27, 10, 26, 9, 8, 27, 28, 9, 27, 8, 6, 17, 5, 7, 28, 29, 8, 28, 7, 6, 7, 29, 6, 29, 17], "vertices": [2, 6, 76.09, 9.09, 0.96602, 74, -283.46, 59.6, 0.03398, 2, 6, 76.74, 1.67, 0.96616, 74, -282.81, 52.17, 0.03384, 2, 6, 75.06, -4.89, 0.9664, 74, -284.49, 45.61, 0.0336, 2, 6, 70.77, -10.02, 0.96663, 74, -288.77, 40.49, 0.03337, 2, 6, 64.51, -13.78, 0.96683, 74, -295.03, 36.72, 0.03317, 2, 6, 58.08, -15.56, 0.96694, 74, -301.46, 34.94, 0.03306, 2, 6, 55.13, -13.66, 0.96689, 74, -304.42, 36.84, 0.03311, 2, 6, 53.56, -7.29, 0.96665, 74, -305.98, 43.21, 0.03335, 2, 6, 52.02, -0.54, 0.96638, 74, -307.52, 49.96, 0.03362, 2, 6, 51.69, 7.1, 0.96608, 74, -307.86, 57.61, 0.03392, 2, 6, 52.09, 13.84, 0.966, 74, -307.45, 64.34, 0.034, 2, 6, 56.17, 20.99, 0.966, 74, -303.38, 71.49, 0.034, 2, 6, 62.16, 23.14, 0.966, 74, -297.38, 73.64, 0.034, 2, 6, 67.27, 29.64, 0.966, 74, -292.27, 80.14, 0.034, 2, 6, 71.46, 27.31, 0.966, 74, -288.08, 77.81, 0.034, 2, 6, 75.01, 19.83, 0.966, 74, -284.54, 70.33, 0.034, 2, 6, 78.59, 14.97, 0.966, 74, -280.96, 65.47, 0.034, 2, 6, 58.89, -12.01, 0.9668, 74, -300.65, 38.49, 0.0332, 2, 6, 63.85, -10.83, 0.96672, 74, -295.69, 39.67, 0.03328, 2, 6, 68.27, -7.61, 0.96656, 74, -291.27, 42.9, 0.03344, 2, 6, 71.65, -2.6, 0.96634, 74, -287.89, 47.9, 0.03366, 2, 6, 72.4, 3.64, 0.96613, 74, -287.14, 54.14, 0.03387, 2, 6, 71.26, 10.01, 0.966, 74, -288.28, 60.51, 0.034, 2, 6, 68.37, 15.19, 0.966, 74, -291.17, 65.69, 0.034, 2, 6, 63.94, 19.01, 0.966, 74, -295.6, 69.52, 0.034, 2, 6, 59.99, 14.86, 0.966, 74, -299.55, 65.37, 0.034, 2, 6, 57.3, 9.82, 0.96602, 74, -302.24, 60.32, 0.03398, 2, 6, 56.29, 3.14, 0.96622, 74, -303.26, 53.65, 0.03378, 2, 6, 56.6, -2.54, 0.96644, 74, -302.95, 47.96, 0.03356, 2, 6, 57.66, -7.68, 0.96664, 74, -301.88, 42.82, 0.03336], "hull": 17, "edges": [4, 6, 10, 12, 20, 22, 26, 28, 28, 30, 30, 32, 0, 32, 0, 2, 2, 4, 6, 8, 8, 10, 22, 24, 24, 26, 16, 18, 18, 20, 12, 14, 14, 16, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 34], "width": 43, "height": 31}}, "glass": {"glass": {"type": "mesh", "uvs": [0, 0.10244, 0.23888, 0.00255, 0.31053, 0.00614, 0.42098, 0.0843, 0.53272, 0.21863, 0.60321, 0.35515, 0.63956, 0.38159, 0.68012, 0.39782, 0.76468, 0.36406, 0.85675, 0.3996, 0.93942, 0.48052, 1, 0.54645, 0.94183, 0.77217, 0.87457, 0.97371, 0.79682, 0.98319, 0.7097, 0.93711, 0.65692, 0.82741, 0.63745, 0.67232, 0.64975, 0.50889, 0.60853, 0.4928, 0.52288, 0.74764, 0.43423, 0.75089, 0.33972, 0.72773, 0.25656, 0.62937, 0.22039, 0.50409, 0.21373, 0.18839, 0.02445, 0.20517, 0.68749, 0.53115, 0.91517, 0.74285, 0.74375, 0.63597, 0.82545, 0.70384, 0.56981, 0.46189, 0.26356, 0.20929, 0.32016, 0.33709, 0.40252, 0.42672, 0.48569, 0.4732], "triangles": [13, 14, 30, 14, 15, 30, 13, 28, 12, 13, 30, 28, 15, 29, 30, 15, 16, 29, 16, 17, 29, 12, 28, 11, 22, 34, 21, 21, 35, 20, 21, 34, 35, 20, 31, 19, 20, 35, 31, 28, 10, 11, 28, 30, 10, 22, 23, 34, 30, 9, 10, 30, 29, 9, 17, 27, 29, 17, 18, 27, 29, 8, 9, 29, 27, 8, 23, 33, 34, 23, 24, 33, 18, 7, 27, 27, 7, 8, 19, 6, 18, 18, 6, 7, 24, 32, 33, 24, 25, 32, 31, 5, 19, 19, 5, 6, 31, 35, 4, 35, 34, 4, 31, 4, 5, 34, 3, 4, 34, 33, 3, 32, 2, 33, 33, 2, 3, 25, 1, 32, 32, 1, 2, 26, 0, 25, 25, 0, 1], "vertices": [2, 6, 65.82, 68.79, 0.9918, 74, -293.72, 119.29, 0.0082, 2, 6, 82.1, 32.14, 0.96002, 74, -277.44, 82.64, 0.03998, 2, 6, 84.57, 20.58, 0.96074, 74, -274.98, 71.08, 0.03926, 2, 6, 83.14, 1.51, 0.96241, 74, -276.4, 52.01, 0.03759, 2, 6, 77.72, -18.72, 0.96424, 74, -281.83, 31.78, 0.03576, 2, 6, 70.57, -32.37, 0.96554, 74, -288.97, 18.13, 0.03446, 2, 6, 70.05, -38.65, 0.96645, 74, -289.5, 11.85, 0.03355, 2, 6, 70.42, -45.44, 0.96764, 74, -289.12, 5.06, 0.03236, 2, 6, 76.07, -58.45, 0.97039, 74, -283.47, -7.94, 0.02961, 2, 6, 77.01, -73.83, 0.97384, 74, -282.53, -23.33, 0.02616, 2, 6, 74.33, -88.49, 0.97727, 74, -285.21, -37.98, 0.02273, 2, 6, 71.89, -99.34, 0.97982, 74, -287.66, -48.84, 0.02018, 2, 6, 53.42, -93.85, 0.97915, 74, -306.12, -43.35, 0.02085, 2, 6, 36.35, -86.49, 0.97757, 74, -323.19, -35.98, 0.02243, 2, 6, 32.71, -74.17, 0.97473, 74, -326.83, -23.66, 0.02527, 2, 6, 32.71, -59.39, 0.97138, 74, -326.83, -8.89, 0.02862, 2, 6, 38.61, -49.05, 0.96905, 74, -320.94, 1.45, 0.03095, 2, 6, 49.03, -43.28, 0.96767, 74, -310.51, 7.23, 0.03233, 2, 6, 61.27, -42.46, 0.96725, 74, -298.27, 8.04, 0.03275, 2, 6, 60.86, -35.57, 0.96617, 74, -298.68, 14.93, 0.03383, 2, 6, 39.25, -26.17, 0.9654, 74, -320.29, 24.34, 0.0346, 2, 6, 35.65, -11.99, 0.96415, 74, -323.9, 38.51, 0.03585, 2, 6, 33.72, 3.58, 0.96278, 74, -325.83, 54.08, 0.03722, 2, 6, 37.64, 18.61, 0.96147, 74, -321.91, 69.11, 0.03853, 2, 6, 45.28, 26.56, 0.96074, 74, -314.26, 77.06, 0.03926, 2, 6, 67.76, 33.01, 0.96008, 74, -291.78, 83.51, 0.03992, 2, 6, 59.35, 63.11, 0.98765, 74, -300.19, 113.62, 0.01235, 2, 6, 61.1, -48.9, 0.96872, 74, -298.44, 1.6, 0.03128, 2, 6, 54.52, -89.07, 0.97804, 74, -305.03, -38.57, 0.02196, 2, 6, 55.69, -59.72, 0.97132, 74, -303.85, -9.22, 0.02868, 2, 6, 53.91, -74, 0.97462, 74, -305.63, -23.5, 0.02538, 2, 6, 61.61, -28.83, 0.96539, 74, -297.93, 21.68, 0.03461, 2, 6, 68.15, 24.65, 0.96067, 74, -291.39, 75.16, 0.03933, 2, 6, 61.1, 13.38, 0.96175, 74, -298.44, 63.89, 0.03825, 2, 6, 57.78, -1.37, 0.96307, 74, -301.76, 49.14, 0.03693, 2, 6, 57.6, -15.51, 0.9643, 74, -301.95, 34.99, 0.0357], "hull": 27, "edges": [0, 2, 2, 4, 28, 30, 42, 44, 44, 46, 46, 48, 48, 50, 52, 0, 50, 52, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 34, 36, 36, 38, 30, 32, 32, 34, 38, 40, 40, 42, 18, 20, 20, 22, 54, 58, 60, 58, 56, 60, 66, 64, 68, 66, 62, 70, 70, 68, 22, 24, 24, 26, 26, 28], "width": 165, "height": 74}}, "glass2": {"glass2": {"type": "mesh", "uvs": [0.9711, 0.18033, 0.97187, 0.99789, 0.02786, 0.74255, 0.02658, 0.08611], "triangles": [2, 3, 0, 2, 0, 1], "vertices": [2, 6, 72.9, -92.95, 0.97731, 74, -286.65, -42.45, 0.02269, 2, 6, 64.15, -95.05, 0.97816, 74, -295.39, -44.55, 0.02184, 2, 6, 59.48, -63.17, 0.98495, 74, -300.06, -12.67, 0.01505, 2, 6, 66.5, -61.46, 0.98588, 74, -293.04, -10.96, 0.01412], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 6, 0], "width": 34, "height": 11}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.64065, 0.00182, 0.69373, 0.01385, 0.73263, 0.0566, 0.75987, 0.10574, 0.79219, 0.06534, 0.83386, 0.05792, 0.86801, 0.0777, 0.9005, 0.12485, 0.9178, 0.18756, 0.92714, 0.24834, 0.92928, 0.30378, 0.94705, 0.35432, 0.96317, 0.41343, 0.98035, 0.48476, 0.99754, 0.55609, 1, 0.62506, 0.99526, 0.70084, 0.97004, 0.75044, 0.92171, 0.78684, 0.87346, 0.82453, 0.82382, 0.83438, 0.82257, 0.88182, 0.83974, 0.92529, 0.86952, 0.96067, 0.9037, 1, 0.84155, 0.9832, 0.7978, 0.94015, 0.76834, 0.89315, 0.76346, 0.82431, 0.77618, 0.77592, 0.80962, 0.72087, 0.84746, 0.66846, 0.87988, 0.61743, 0.8937, 0.56182, 0.89329, 0.50166, 0.87291, 0.43942, 0.85685, 0.36956, 0.85024, 0.30805, 0.84763, 0.24073, 0.83951, 0.17802, 0.8207, 0.15062, 0.78648, 0.1529, 0.75436, 0.19768, 0.73103, 0.19469, 0.72674, 0.1402, 0.69877, 0.10177, 0.66915, 0.10159, 0.63355, 0.1474, 0.59333, 0.21935, 0.5579, 0.27865, 0.51698, 0.33399, 0.46236, 0.38008, 0.40323, 0.42677, 0.35233, 0.46038, 0.29254, 0.48859, 0.2386, 0.48832, 0.19164, 0.48978, 0.12944, 0.5101, 0.08439, 0.54516, 0.05048, 0.592, 0.02988, 0.63841, 0.00708, 0.70394, 0.01069, 0.63756, 0.02349, 0.58149, 0.05245, 0.53224, 0.07697, 0.4828, 0.1026, 0.43731, 0.15269, 0.38495, 0.20169, 0.33852, 0.25389, 0.305, 0.30775, 0.27346, 0.35052, 0.2346, 0.39688, 0.17306, 0.43547, 0.121, 0.48395, 0.07025, 0.53037, 0.03513, 0.58229, 0.00857], "triangles": [25, 22, 23, 26, 22, 25, 25, 23, 24, 20, 29, 30, 20, 28, 29, 20, 27, 28, 21, 27, 20, 26, 27, 21, 26, 21, 22, 17, 32, 16, 31, 32, 17, 18, 31, 17, 30, 31, 18, 19, 30, 18, 30, 19, 20, 33, 34, 13, 33, 13, 14, 33, 14, 15, 32, 33, 15, 16, 32, 15, 37, 9, 10, 36, 37, 10, 36, 10, 11, 35, 36, 11, 35, 11, 12, 34, 35, 12, 34, 12, 13, 59, 63, 64, 60, 62, 63, 59, 60, 63, 61, 62, 60, 57, 66, 56, 65, 66, 57, 58, 65, 57, 64, 65, 58, 59, 64, 58, 55, 68, 69, 54, 55, 69, 56, 67, 68, 55, 56, 68, 66, 67, 56, 52, 71, 51, 70, 71, 52, 53, 70, 52, 69, 70, 53, 53, 54, 69, 49, 73, 48, 72, 73, 49, 50, 72, 49, 51, 71, 72, 50, 51, 72, 37, 38, 9, 39, 40, 7, 40, 5, 6, 48, 73, 74, 48, 75, 47, 47, 75, 76, 46, 47, 76, 46, 0, 1, 45, 46, 1, 2, 45, 1, 46, 76, 0, 45, 2, 3, 44, 45, 3, 41, 3, 4, 44, 3, 41, 42, 43, 44, 41, 42, 44, 40, 41, 4, 74, 75, 48, 40, 4, 5, 40, 6, 7, 39, 7, 8, 38, 39, 8, 38, 8, 9], "vertices": [2, 38, 38.61, 41.73, 0.99598, 75, -143.1, 56.42, 0.00402, 4, 38, 39.37, 25.16, 0.96419, 45, -24.36, 5.05, 0.03398, 49, -199.64, 32.62, 0, 75, -142.34, 39.85, 0.00183, 1, 38, 31.47, 11.01, 1, 1, 38, 21.16, -0.02, 1, 2, 38, 33.51, -7.3, 0.998, 75, -148.2, 7.4, 0.002, 2, 38, 38.31, -19.31, 0.99569, 75, -143.4, -4.62, 0.00431, 2, 38, 35.8, -30.67, 0.99392, 75, -145.91, -15.98, 0.00608, 4, 38, 26.35, -43.16, 0.87779, 49, -227.01, 96.56, 0, 39, 1.82, 12.57, 0.11329, 75, -155.36, -28.47, 0.00892, 4, 38, 11.96, -52.03, 0.63765, 49, -220.02, 111.96, 0, 39, 18.71, 13.49, 0.35226, 75, -169.76, -37.34, 0.01009, 5, 38, -2.52, -58.41, 0.41589, 49, -211.58, 125.34, 0, 39, 34.47, 12.18, 0.48742, 40, -5.98, 12.07, 0.08761, 75, -184.23, -43.72, 0.00908, 5, 38, -16.18, -62.32, 0.21919, 49, -202.43, 136.21, 0, 39, 48.34, 9.09, 0.06325, 40, 7.94, 9.23, 0.70957, 75, -197.89, -47.63, 0.00799, 4, 38, -27.51, -70.61, 0.02245, 49, -197.67, 149.42, 0, 40, 21.82, 11.36, 0.96505, 75, -209.23, -55.92, 0.0125, 3, 49, -191.04, 163.89, 0, 40, 37.71, 12.45, 0.98348, 75, -222.81, -64.22, 0.01652, 4, 49, -182.53, 180.89, 0, 40, 56.7, 13.1, 0.33502, 41, 0.62, 13.63, 0.64312, 75, -239.36, -73.57, 0.02186, 3, 49, -174.02, 197.89, 0, 41, 18.93, 18.72, 0.97567, 75, -255.92, -82.91, 0.02433, 4, 49, -162.6, 211.37, 0, 41, 36.6, 19.3, 0.92246, 42, -22, 4.13, 0.05181, 75, -272.92, -87.71, 0.02572, 5, 49, -148.37, 224.64, 0, 41, 55.98, 17.65, 0.40999, 42, -8.03, 17.66, 0.5633, 44, 8.99, 79, 0, 75, -292.13, -90.77, 0.02671, 5, 49, -134.07, 228.72, 0, 41, 68.6, 9.78, 0.05218, 42, 6.2, 22.01, 0.92195, 44, 10.67, 64.23, 0, 75, -306.27, -86.16, 0.02587, 5, 49, -116.85, 225.51, 0, 42, 23.48, 19.12, 0.94601, 43, -3.7, 29.44, 0.03095, 44, 4.68, 47.76, 0, 75, -318.76, -73.88, 0.02304, 5, 49, -99.42, 222.55, 0, 42, 40.96, 16.48, 0.66065, 43, 7.35, 15.65, 0.31903, 44, -1.09, 31.05, 0, 75, -331.56, -61.68, 0.02033, 4, 49, -86.51, 214.06, 0, 42, 54.03, 8.24, 0.00584, 43, 11.36, 0.73, 0.97723, 75, -337.53, -47.44, 0.01694, 4, 49, -77.99, 222.73, 0, 43, 23.48, 1.54, 0.98117, 44, -4.41, 9.88, 0.00118, 75, -349.44, -49.86, 0.01765, 4, 49, -74.32, 234.48, 0, 43, 34.04, 7.88, 0.05781, 44, 6.58, 4.33, 0.92256, 75, -359.05, -57.56, 0.01963, 4, 49, -74.89, 247.34, 0, 43, 42.16, 17.86, 0, 44, 19.36, 2.8, 0.97761, 75, -365.76, -68.54, 0.02239, 3, 49, -75.77, 261.85, 0, 44, 33.82, 1.29, 0.97446, 75, -373.14, -81.07, 0.02554, 2, 44, 16.12, -7.03, 0.97921, 75, -373.35, -61.52, 0.02079, 3, 43, 39.09, -4.56, 0.47568, 44, -1.25, -6.57, 0.50741, 75, -365.72, -45.9, 0.01691, 3, 43, 28, -14.74, 0.98578, 44, -15.77, -2.63, 0.00026, 75, -356.1, -34.33, 0.01395, 3, 42, 66.16, -5.99, 0.0129, 43, 10.61, -17.96, 0.97457, 75, -339.3, -28.81, 0.01254, 3, 42, 55.01, -12.66, 0.30089, 43, -2.1, -15.29, 0.68657, 75, -326.34, -29.75, 0.01254, 4, 41, 60.54, -39.39, 0.00245, 42, 37.98, -16.37, 0.95056, 43, -17.13, -6.46, 0.03321, 75, -310.26, -36.5, 0.01378, 3, 41, 47.24, -27.64, 0.12507, 42, 20.38, -18.68, 0.8593, 75, -294.53, -44.71, 0.01564, 3, 41, 34.27, -17.55, 0.67392, 42, 4.26, -21.83, 0.30899, 75, -279.52, -51.38, 0.01709, 4, 40, 69.32, -17.52, 0.00077, 41, 20.08, -13.17, 0.96591, 42, -8.37, -29.65, 0.01638, 75, -264.69, -52.23, 0.01695, 3, 40, 54.36, -13.87, 0.36087, 41, 4.68, -13.14, 0.62532, 75, -249.73, -48.55, 0.0138, 3, 40, 37.38, -16.04, 0.96776, 41, -11.31, -19.24, 0.02629, 75, -235.67, -38.79, 0.00594, 2, 38, -37.69, -44.56, 0.13193, 40, 18.83, -16.45, 0.86807, 3, 38, -22.84, -38.96, 0.35762, 39, 43.04, -14.61, 0.18751, 40, 3.07, -14.56, 0.45487, 3, 38, -6.26, -34.21, 0.5964, 39, 26.2, -10.87, 0.39815, 40, -13.84, -11.12, 0.00545, 2, 38, 8.79, -28.08, 0.83911, 39, 10.05, -9.07, 0.16089, 1, 38, 14.29, -20.84, 1, 1, 38, 11.3, -10.76, 1, 1, 38, -2.13, -3.8, 1, 1, 38, -3.04, 3.34, 1, 2, 38, 10.23, 7.84, 0.9919, 74, -211.51, 22.53, 0.0081, 2, 38, 17.83, 18.46, 0.99082, 74, -203.92, 33.15, 0.00918, 2, 38, 15.77, 27.32, 0.99006, 74, -205.97, 42.01, 0.00994, 4, 38, 1.84, 35.25, 0.74986, 45, 12.04, 18.65, 0.24019, 46, -31.94, 20.63, 0.00074, 74, -219.9, 49.94, 0.00921, 4, 38, -18.92, 43.02, 0.43055, 45, 33.46, 24.4, 0.42545, 46, -10.22, 25.07, 0.13571, 74, -240.67, 57.71, 0.0083, 4, 38, -36.2, 50.11, 0.16647, 45, 51.64, 28.68, 0.19139, 46, 8.19, 28.24, 0.63468, 74, -257.95, 64.8, 0.00747, 4, 45, 70.42, 31.09, 0.01682, 46, 27.08, 29.5, 0.97255, 47, -30.96, 21.46, 0.00412, 74, -274.63, 73.76, 0.00651, 4, 46, 47.31, 26.12, 0.74108, 47, -10.62, 24.08, 0.25372, 49, -83.85, 53.34, 0, 74, -289.98, 87.35, 0.0052, 5, 46, 68.62, 21.88, 0.12484, 47, 11.01, 26.19, 0.86919, 48, -32.29, 22.11, 0.00223, 49, -62.41, 49.81, 0, 74, -305.79, 102.26, 0.00375, 5, 46, 85.79, 17.03, 0.00051, 47, 28.85, 26.51, 0.86633, 48, -14.66, 24.79, 0.13077, 49, -45.09, 45.53, 0, 74, -317.77, 115.48, 0.0024, 5, 47, 48.45, 24.34, 0.36486, 48, 5.06, 25.24, 0.62928, 49, -26.71, 38.39, 0, 74, -329.03, 131.68, 0.00071, 75, -288.99, 131.68, 0.00516, 4, 47, 63.07, 16.56, 0.04736, 48, 20.58, 19.45, 0.94123, 49, -14.58, 27.1, 0.00133, 75, -292.75, 147.81, 0.01009, 3, 48, 34.24, 14.83, 0.8846, 49, -3.74, 17.6, 0.1018, 75, -296.43, 161.75, 0.0136, 4, 48, 53.96, 13.12, 0.02985, 49, 13.82, 8.47, 0.95308, 50, -20.65, 21.5, 0.00011, 75, -305.9, 179.13, 0.01696, 3, 48, 70.04, 16.77, 0, 49, 30.07, 5.69, 0.98181, 75, -317.82, 190.52, 0.01819, 3, 49, 45.86, 7.43, 0.08513, 50, 6.68, 4.75, 0.89595, 75, -331.88, 197.89, 0.01891, 3, 49, 58.56, 11.88, 2e-05, 50, 19.92, 2.33, 0.98089, 75, -344.9, 201.3, 0.01909, 2, 50, 38.03, 0.75, 0.98061, 75, -362.84, 204.25, 0.01939, 3, 48, 99.44, 31.16, 0, 50, 21.5, -3.35, 0.98042, 75, -346.05, 207.09, 0.01958, 2, 50, 6.63, -3.96, 0.98032, 75, -331.18, 206.57, 0.01968, 2, 49, 35.04, -3.4, 0.98061, 75, -316.86, 200.83, 0.01939, 2, 49, 20.92, -7.59, 0.98096, 75, -302.81, 196.42, 0.01904, 3, 48, 55.26, -7.21, 0.04779, 49, 7.24, -10.81, 0.93343, 75, -289.66, 191.45, 0.01878, 3, 48, 36.2, -14.49, 0.96465, 49, -13.15, -10.23, 0.01744, 75, -273.07, 179.57, 0.01792, 4, 47, 55.21, -22.65, 0.0748, 48, 17.98, -20.45, 0.90837, 49, -32.27, -8.76, 0, 75, -258.04, 167.68, 0.01683, 5, 46, 79.36, -32.51, 0.00192, 47, 37.04, -22.77, 0.59769, 48, -0.02, -22.97, 0.38512, 49, -49.86, -4.2, 0, 75, -246, 154.06, 0.01527, 4, 46, 61.92, -26.64, 0.11814, 47, 18.64, -22.2, 0.84908, 48, -18.33, -24.84, 0.01913, 75, -234.33, 139.83, 0.01365, 3, 46, 45.59, -24.51, 0.57065, 47, 2.39, -24.88, 0.41675, 75, -221.62, 129.35, 0.01261, 5, 38, -21.3, 104.44, 0.02635, 45, 71.09, -24.19, 0.02266, 46, 24.39, -25.72, 0.91101, 47, -17.55, -32.18, 0.02832, 75, -203.01, 119.13, 0.01166, 4, 38, -5.6, 95.99, 0.27318, 45, 53.4, -26.44, 0.20966, 46, 6.6, -26.89, 0.50627, 75, -187.31, 110.68, 0.01088, 4, 38, 10.47, 84.5, 0.5503, 45, 33.65, -26.43, 0.36771, 46, -13.12, -25.68, 0.07226, 75, -171.24, 99.19, 0.00973, 4, 38, 22.51, 72.7, 0.78972, 45, 17, -23.83, 0.20083, 46, -29.58, -22.08, 0.00119, 75, -159.2, 87.39, 0.00826, 3, 38, 32.8, 58.76, 0.99059, 45, 0.52, -18.47, 0.00306, 75, -148.91, 73.45, 0.00635], "hull": 77, "edges": [12, 14, 42, 44, 64, 66, 76, 78, 94, 96, 110, 112, 112, 114, 122, 124, 124, 126, 140, 142, 84, 86, 82, 84, 78, 80, 80, 82, 74, 76, 66, 68, 68, 70, 70, 72, 72, 74, 86, 88, 92, 94, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 114, 116, 116, 118, 118, 120, 120, 122, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 2, 0, 0, 152, 2, 4, 10, 12, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 62, 64, 58, 60, 60, 62, 54, 56, 56, 58, 50, 52, 52, 54, 48, 50, 44, 46, 46, 48, 38, 40, 40, 42, 36, 38, 32, 34, 34, 36], "width": 307, "height": 256}}, "hair_R": {"hair_R": {"type": "mesh", "uvs": [0.26674, 0.32649, 0.45794, 0.23806, 0.66962, 0.13763, 0.99514, 0.03361, 0.82094, 0.16111, 0.64674, 0.28861, 0.55608, 0.36366, 0.45219, 0.46499, 0.38193, 0.55957, 0.34139, 0.65803, 0.33049, 0.74031, 0.34827, 0.80868, 0.40642, 0.8579, 0.55415, 0.89292, 0.72599, 0.90234, 0.91112, 0.91054, 0.73974, 0.9929, 0.58476, 0.99245, 0.38957, 0.96745, 0.19438, 0.94246, 0.11552, 0.88592, 0.03666, 0.82937, 0.03034, 0.73831, 0.03256, 0.65881, 0.02631, 0.5468, 0.02198, 0.42824], "triangles": [4, 2, 3, 5, 2, 4, 5, 1, 2, 7, 8, 25, 25, 0, 7, 7, 0, 6, 6, 1, 5, 0, 1, 6, 8, 24, 25, 9, 24, 8, 23, 24, 9, 17, 14, 16, 16, 14, 15, 18, 13, 17, 17, 13, 14, 18, 12, 13, 18, 19, 12, 19, 20, 12, 20, 11, 12, 20, 21, 11, 21, 10, 11, 21, 22, 10, 22, 23, 10, 10, 23, 9], "vertices": [2, 6, 90.17, 54.62, 0.98134, 74, -269.38, 105.12, 0.01866, 2, 6, 103.13, 47.08, 0.97566, 74, -256.41, 97.58, 0.02434, 2, 6, 117.79, 38.8, 0.96997, 74, -241.76, 89.31, 0.03003, 2, 6, 134.29, 24.65, 0.967, 74, -225.26, 75.15, 0.033, 2, 6, 116.86, 30.19, 0.96795, 74, -242.68, 80.69, 0.03205, 2, 6, 99.43, 35.73, 0.97045, 74, -260.11, 86.23, 0.02955, 2, 6, 89.32, 38.36, 0.97149, 74, -270.22, 88.86, 0.02851, 2, 6, 75.9, 40.95, 0.97189, 74, -283.64, 91.45, 0.02811, 3, 51, 10.62, 7.43, 0.29553, 6, 63.7, 41.96, 0.67981, 74, -295.84, 92.46, 0.02465, 4, 51, 22.92, 7.43, 0.43934, 52, -0.45, 8.08, 0.15889, 6, 51.42, 41.29, 0.38139, 74, -308.13, 91.8, 0.02038, 4, 51, 32.99, 8.65, 0.01082, 52, 9.42, 5.77, 0.83928, 6, 41.43, 39.53, 0.13522, 74, -318.11, 90.04, 0.01468, 3, 52, 17.87, 5.27, 0.78844, 53, 0.59, 6.49, 0.19829, 74, -326.07, 87.16, 0.01327, 3, 52, 24.37, 7.33, 0.02355, 53, 6.84, 3.76, 0.96233, 74, -331.24, 82.71, 0.01412, 3, 53, 15.75, 5.43, 0.42084, 54, 1.88, 5.19, 0.56388, 74, -333.59, 73.96, 0.01528, 2, 54, 11.23, 5.46, 0.98334, 74, -332.58, 64.66, 0.01666, 2, 54, 21.26, 5.98, 0.98584, 74, -331.26, 54.7, 0.01416, 2, 54, 13.66, -5.44, 0.9846, 74, -343.25, 61.37, 0.0154, 3, 53, 24.74, -3.04, 0.00302, 54, 5.38, -6.66, 0.9821, 74, -345.12, 69.53, 0.01488, 3, 53, 14.61, -7.29, 0.91661, 54, -5.51, -5.22, 0.06998, 74, -344.56, 80.49, 0.01341, 4, 52, 32.65, -5.73, 0.03796, 53, 4.48, -11.53, 0.95561, 74, -344, 91.46, 0.00479, 75, -303.97, 91.46, 0.00163, 4, 52, 25.07, -8.74, 0.50637, 53, -3.2, -8.8, 0.48555, 74, -338.22, 97.2, 0.0025, 75, -298.18, 97.2, 0.00558, 4, 52, 17.49, -11.74, 0.96081, 53, -10.89, -6.08, 0.02959, 74, -332.43, 102.95, 0.00061, 75, -292.4, 102.95, 0.00899, 5, 51, 35.63, -7.34, 0.02258, 52, 6.4, -10.16, 0.90472, 6, 37.94, 55.36, 0.06406, 74, -321.61, 105.87, 0.00128, 75, -281.58, 105.87, 0.00736, 4, 51, 25.98, -8.96, 0.46773, 52, -3.22, -8.36, 0.22161, 6, 47.48, 57.5, 0.30837, 74, -312.07, 108, 0.00229, 3, 51, 12.48, -11.74, 0.34451, 6, 60.81, 61, 0.6474, 74, -298.74, 111.51, 0.00809, 3, 51, -1.82, -14.57, 0.00201, 6, 74.94, 64.59, 0.98575, 74, -284.6, 115.1, 0.01224], "hull": 26, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 28, 30, 32, 34, 6, 8, 8, 10, 22, 24, 24, 26, 26, 28, 30, 32, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 18, 20, 20, 22, 38, 40, 40, 42, 50, 0, 0, 2, 2, 4, 4, 6], "width": 54, "height": 123}}, "hand_L": {"hand_L": {"type": "mesh", "uvs": [0.53752, 0.02978, 0.75861, 0.01057, 0.84764, 0.03098, 0.92937, 0.10674, 0.98447, 0.23749, 0.98719, 0.44395, 0.9532, 0.53998, 0.88323, 0.63486, 0.82838, 0.69907, 0.77272, 0.77927, 0.6653, 0.99138, 0.2648, 0.97141, 0.1985, 0.86786, 0.08127, 0.83879, 0.00325, 0.73836, 0.0306, 0.4879, 0.0992, 0.40159, 0.05698, 0.20403, 0.25935, 0.17433, 0.40421, 0.03577, 0.61522, 0.33005, 0.52556, 0.45766, 0.72967, 0.29002], "triangles": [16, 17, 18, 20, 21, 19, 18, 19, 21, 8, 21, 20, 9, 21, 8, 12, 13, 15, 14, 15, 13, 21, 12, 16, 21, 16, 18, 12, 15, 16, 11, 12, 21, 10, 11, 21, 9, 10, 21, 22, 0, 1, 22, 1, 2, 8, 22, 7, 8, 20, 22, 20, 0, 22, 20, 19, 0, 7, 22, 6, 6, 22, 5, 22, 4, 5, 22, 2, 3, 22, 3, 4], "vertices": [1, 27, 27.04, -24.77, 1, 2, 27, 5.01, -20.84, 0.76655, 24, 65.51, -10.98, 0.23345, 2, 27, -3.34, -17.15, 0.58496, 24, 59.03, -17.4, 0.41504, 2, 27, -9.95, -9.51, 0.35574, 24, 49.49, -20.76, 0.64426, 2, 27, -12.93, 1.6, 0.26745, 24, 38.06, -19.47, 0.73255, 2, 27, -9.38, 17.1, 0.89143, 24, 24.94, -10.5, 0.10857, 1, 27, -4.27, 23.45, 1, 2, 27, 4.34, 28.85, 0.98041, 28, -0.04, 34.58, 0.01959, 2, 27, 10.91, 32.31, 0.67785, 28, 7.35, 33.86, 0.32215, 2, 27, 17.85, 36.96, 0.36049, 28, 15.7, 33.92, 0.63951, 2, 27, 32.3, 50.21, 0.07337, 28, 35.06, 37.02, 0.92663, 1, 28, 61.36, 6.25, 1, 1, 28, 60.04, -4.08, 1, 1, 28, 66.42, -14.3, 1, 1, 28, 66.08, -25.34, 1, 1, 28, 50.04, -36.38, 1, 1, 28, 40.45, -35.8, 1, 1, 28, 32.17, -49.25, 1, 2, 27, 56.99, -20.71, 0.13333, 28, 16.62, -35.78, 0.86667, 2, 27, 40.22, -27.56, 0.67835, 28, -1.14, -32.26, 0.32165, 3, 27, 24.98, -0.44, 0.4361, 28, 1.06, -1.23, 0.43819, 24, 53.83, 15.07, 0.12571, 1, 28, 14.43, -1.22, 1, 1, 27, 13.02, -0.65, 1], "hull": 20, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 36, 38, 28, 30, 30, 32, 12, 14, 14, 16, 2, 0, 0, 38, 34, 36], "width": 101, "height": 77}}, "head": {"head": {"type": "mesh", "uvs": [0.52009, 0.00615, 0.67051, 0.00492, 0.75693, 0.01198, 0.89894, 0.05868, 0.954, 0.09902, 0.98637, 0.16347, 0.99365, 0.23908, 0.99153, 0.33474, 0.98897, 0.3816, 0.97843, 0.43352, 0.94863, 0.51065, 0.9414, 0.61455, 0.92023, 0.70482, 0.8123, 0.86441, 0.69974, 0.97043, 0.65133, 0.9931, 0.56073, 0.9881, 0.49559, 0.96639, 0.36713, 0.89103, 0.2594, 0.81587, 0.21934, 0.76588, 0.18156, 0.64127, 0.13268, 0.63496, 0.05873, 0.56829, 0.02286, 0.4744, 0.00254, 0.38713, 0.02275, 0.30278, 0.06381, 0.26902, 0.1202, 0.27244, 0.17639, 0.32812, 0.19986, 0.22197, 0.34999, 0.05825, 0.42414, 0.02395, 0.95449, 0.30476, 0.82612, 0.31985, 0.80199, 0.34584, 0.82294, 0.37299, 0.95208, 0.35508, 0.88769, 0.36826, 0.89244, 0.31425, 0.8591, 0.31786, 0.85387, 0.37155, 0.92435, 0.30902, 0.92123, 0.36049, 0.97394, 0.3153, 0.97252, 0.36183, 0.80543, 0.06441, 0.84394, 0.06749, 0.36449, 0.21551, 0.41158, 0.18774, 0.46493, 0.17268, 0.52232, 0.17502, 0.71431, 0.28025, 0.61832, 0.2241, 0.57109, 0.19708, 0.66865, 0.25323, 0.36846, 0.22558, 0.4192, 0.21703, 0.46367, 0.21361, 0.51378, 0.22501, 0.68916, 0.32869, 0.60278, 0.28514, 0.55421, 0.25222, 0.6444, 0.31176, 0.71283, 0.30889, 0.47635, 0.09247, 0.74143, 0.15579, 0.85277, 0.18842, 0.93937, 0.20868, 0.61099, 0.11303, 0.35243, 0.11978, 0.17311, 0.48759, 0.17238, 0.40853, 0.17533, 0.56262, 0.40593, 0.36266, 0.44522, 0.33806, 0.4854, 0.33111, 0.52889, 0.33511, 0.56272, 0.35389, 0.58336, 0.38265, 0.59171, 0.41182, 0.59333, 0.4373, 0.41877, 0.39195, 0.44549, 0.41587, 0.47866, 0.43124, 0.51333, 0.43972, 0.54917, 0.44256, 0.57729, 0.44193, 0.3956, 0.32277, 0.4449, 0.31215, 0.48943, 0.30743, 0.53873, 0.31529, 0.57808, 0.33614, 0.60099, 0.36524, 0.61526, 0.3971, 0.62218, 0.43249, 0.37805, 0.36738, 0.39889, 0.41715, 0.45945, 0.44684, 0.52798, 0.45814, 0.59569, 0.45505, 0.78719, 0.50041, 0.79933, 0.47906, 0.81916, 0.45882, 0.84708, 0.44962, 0.88148, 0.45146, 0.9094, 0.46324, 0.92599, 0.48385, 0.92639, 0.50078, 0.80014, 0.51818, 0.82402, 0.53069, 0.85558, 0.53437, 0.88835, 0.5329, 0.91021, 0.51965, 0.7777, 0.49094, 0.79227, 0.46665, 0.8121, 0.44604, 0.84568, 0.43279, 0.8821, 0.43169, 0.91245, 0.43905, 0.93996, 0.45745, 0.7797, 0.5145, 0.80802, 0.54284, 0.85294, 0.55756, 0.9019, 0.55094, 0.93144, 0.52591, 0.74274, 0.44949, 0.70332, 0.44071, 0.77391, 0.58732, 0.74995, 0.5045, 0.78914, 0.64027, 0.7709, 0.68131, 0.72512, 0.676, 0.67348, 0.65962, 0.61079, 0.6487, 0.58878, 0.6208, 0.59879, 0.58016, 0.63833, 0.55821, 0.69754, 0.49559, 0.68129, 0.56429, 0.67069, 0.59799, 0.70967, 0.62533, 0.73972, 0.63198, 0.77462, 0.61564, 0.71534, 0.59115, 0.75432, 0.59928, 0.78599, 0.61057, 0.77399, 0.40252, 0.70233, 0.38461, 0.30899, 0.28612, 0.28653, 0.37647, 0.28196, 0.4674, 0.28576, 0.60925, 0.33648, 0.76956, 0.4878, 0.9093, 0.56474, 0.94491, 0.6582, 0.95693, 0.7061, 0.9314, 0.78779, 0.84848, 0.89272, 0.69545, 0.909, 0.61506, 0.45027, 0.53399, 0.43588, 0.63391, 0.60861, 0.50577, 0.48765, 0.73901, 0.50483, 0.74382, 0.53573, 0.73061, 0.58323, 0.70698, 0.62774, 0.69574, 0.6621, 0.69694, 0.68853, 0.71537, 0.72333, 0.71136, 0.74492, 0.7302, 0.76034, 0.75825, 0.76342, 0.7891, 0.48131, 0.75381, 0.50255, 0.76327, 0.51994, 0.79609, 0.54859, 0.82118, 0.58888, 0.83838, 0.64389, 0.84609, 0.69068, 0.8548, 0.72494, 0.84691, 0.74621, 0.82913, 0.75705, 0.80981, 0.47653, 0.72344, 0.45435, 0.73551, 0.46494, 0.75935, 0.47401, 0.74498, 0.50429, 0.7534, 0.53598, 0.756, 0.56341, 0.75795, 0.60101, 0.75344, 0.63282, 0.75269, 0.66421, 0.75795, 0.68611, 0.7681, 0.70222, 0.78313, 0.59308, 0.7685, 0.6278, 0.7752, 0.65839, 0.78043, 0.68441, 0.78275, 0.73113, 0.79515, 0.76005, 0.80041, 0.77834, 0.79259, 0.77297, 0.81288, 0.61513, 0.79906, 0.65684, 0.80615, 0.69067, 0.81024, 0.57822, 0.78952, 0.71255, 0.80919, 0.63908, 0.86782, 0.57123, 0.86513, 0.68836, 0.88291, 0.09087, 0.37889], "triangles": [8, 45, 7, 7, 44, 6, 30, 31, 70, 65, 70, 31, 65, 31, 32, 65, 0, 69, 53, 69, 66, 69, 0, 1, 67, 52, 66, 66, 1, 2, 67, 46, 47, 67, 47, 3, 47, 46, 2, 67, 3, 4, 3, 47, 2, 68, 5, 6, 14, 157, 13, 14, 156, 157, 15, 156, 14, 156, 15, 155, 156, 212, 157, 16, 17, 155, 156, 155, 210, 18, 154, 17, 155, 154, 211, 29, 30, 149, 25, 26, 213, 24, 71, 23, 24, 25, 213, 213, 71, 24, 213, 28, 29, 72, 71, 213, 72, 29, 150, 22, 73, 21, 21, 73, 152, 73, 71, 151, 20, 152, 153, 19, 153, 18, 153, 154, 18, 19, 20, 153, 154, 153, 187, 187, 153, 186, 153, 162, 186, 154, 187, 177, 154, 177, 178, 176, 187, 175, 187, 176, 177, 176, 175, 189, 175, 188, 189, 188, 164, 189, 189, 164, 165, 165, 164, 185, 191, 177, 190, 177, 191, 208, 167, 192, 191, 191, 190, 166, 191, 166, 167, 208, 197, 205, 197, 192, 198, 197, 191, 192, 193, 192, 168, 192, 167, 168, 167, 134, 168, 210, 180, 181, 180, 205, 206, 205, 198, 206, 198, 193, 199, 198, 192, 193, 193, 168, 169, 157, 212, 182, 212, 210, 181, 180, 206, 207, 206, 198, 199, 182, 212, 181, 181, 180, 207, 195, 194, 170, 132, 170, 169, 182, 209, 183, 209, 196, 201, 195, 171, 172, 171, 170, 132, 182, 183, 158, 209, 201, 183, 183, 201, 184, 201, 196, 173, 157, 158, 13, 183, 204, 158, 183, 184, 204, 184, 202, 204, 184, 201, 202, 202, 201, 174, 196, 172, 173, 172, 171, 131, 203, 202, 174, 174, 201, 173, 159, 158, 203, 159, 203, 173, 173, 131, 159, 159, 160, 12, 10, 107, 120, 10, 108, 107, 107, 106, 120, 105, 118, 119, 118, 38, 119, 117, 41, 118, 125, 108, 10, 113, 107, 108, 113, 105, 106, 105, 104, 118, 123, 124, 160, 124, 112, 113, 123, 112, 124, 111, 105, 112, 111, 104, 105, 116, 117, 104, 160, 146, 123, 123, 111, 112, 123, 110, 111, 111, 103, 104, 169, 133, 132, 132, 142, 131, 131, 142, 130, 160, 130, 146, 122, 110, 123, 110, 102, 103, 143, 146, 130, 123, 146, 128, 142, 143, 130, 146, 143, 128, 123, 128, 122, 122, 109, 110, 121, 109, 122, 101, 102, 109, 114, 115, 102, 115, 126, 147, 145, 142, 144, 145, 144, 128, 128, 129, 121, 129, 114, 121, 121, 114, 101, 114, 126, 115, 94, 60, 148, 168, 134, 133, 133, 141, 132, 147, 126, 148, 133, 140, 141, 142, 141, 144, 114, 129, 126, 134, 135, 140, 140, 139, 144, 151, 72, 150, 150, 149, 96, 96, 149, 88, 88, 57, 89, 88, 89, 75, 76, 89, 90, 89, 58, 90, 91, 90, 62, 162, 152, 161, 161, 151, 97, 161, 98, 99, 161, 97, 98, 98, 84, 85, 97, 83, 98, 98, 83, 84, 84, 76, 77, 84, 83, 76, 97, 82, 83, 82, 96, 74, 82, 97, 96, 82, 75, 83, 83, 75, 76, 82, 74, 75, 74, 88, 75, 75, 89, 76, 78, 77, 91, 91, 77, 90, 77, 76, 90, 163, 161, 99, 163, 99, 100, 163, 100, 95, 100, 86, 87, 100, 99, 86, 99, 98, 85, 99, 85, 86, 87, 81, 100, 100, 81, 95, 87, 86, 80, 81, 87, 80, 95, 148, 127, 85, 80, 86, 80, 85, 79, 81, 80, 95, 80, 94, 95, 95, 94, 148, 79, 85, 78, 78, 85, 77, 85, 84, 77, 80, 79, 94, 79, 93, 94, 60, 94, 93, 79, 78, 93, 78, 92, 93, 93, 92, 63, 92, 78, 91, 140, 136, 137, 162, 161, 135, 135, 161, 136, 136, 161, 163, 136, 163, 137, 137, 163, 138, 163, 95, 138, 150, 96, 97, 96, 88, 74, 92, 91, 61, 161, 152, 151, 151, 150, 97, 167, 166, 135, 135, 166, 185, 135, 185, 162, 136, 140, 135, 139, 137, 138, 138, 95, 127, 140, 137, 139, 129, 139, 138, 138, 127, 126, 126, 127, 148, 167, 135, 134, 134, 140, 133, 141, 140, 144, 144, 139, 129, 144, 129, 128, 129, 138, 126, 121, 101, 109, 101, 114, 102, 132, 141, 142, 142, 145, 143, 143, 145, 128, 128, 121, 122, 110, 109, 102, 102, 115, 103, 116, 115, 147, 147, 148, 35, 111, 110, 103, 115, 116, 103, 103, 116, 104, 116, 147, 36, 171, 132, 131, 131, 130, 159, 104, 117, 118, 117, 116, 36, 124, 113, 125, 113, 112, 105, 159, 130, 160, 160, 124, 11, 124, 125, 11, 113, 108, 125, 113, 106, 107, 106, 119, 120, 106, 105, 119, 12, 160, 11, 11, 125, 10, 10, 120, 9, 120, 119, 9, 12, 13, 159, 9, 119, 8, 203, 174, 173, 173, 172, 131, 204, 203, 158, 158, 159, 13, 204, 202, 203, 157, 182, 158, 182, 207, 209, 207, 196, 209, 196, 195, 172, 182, 181, 207, 206, 200, 207, 207, 200, 196, 200, 195, 196, 171, 195, 170, 200, 199, 195, 206, 199, 200, 199, 194, 195, 199, 193, 194, 170, 194, 169, 194, 193, 169, 169, 168, 133, 156, 210, 212, 210, 179, 180, 179, 205, 180, 205, 197, 198, 211, 179, 210, 178, 208, 179, 179, 208, 205, 208, 191, 197, 155, 211, 210, 154, 178, 211, 211, 178, 179, 178, 177, 208, 177, 176, 190, 190, 176, 189, 189, 165, 190, 165, 166, 190, 166, 165, 185, 187, 188, 175, 187, 186, 188, 188, 185, 164, 188, 186, 185, 186, 162, 185, 153, 152, 162, 20, 21, 152, 73, 151, 152, 23, 71, 73, 71, 72, 151, 72, 213, 29, 150, 29, 149, 213, 27, 28, 26, 27, 213, 149, 56, 88, 149, 30, 48, 22, 23, 73, 17, 154, 155, 15, 16, 155, 5, 68, 4, 33, 68, 6, 68, 67, 4, 68, 39, 67, 67, 66, 46, 34, 52, 67, 66, 2, 46, 69, 1, 66, 69, 51, 65, 65, 32, 0, 30, 70, 48, 65, 49, 70, 8, 119, 43, 37, 45, 8, 45, 37, 7, 37, 44, 7, 37, 33, 44, 8, 43, 37, 119, 38, 43, 38, 39, 43, 38, 40, 39, 43, 42, 37, 43, 39, 42, 37, 42, 33, 40, 67, 39, 44, 33, 6, 42, 39, 68, 42, 68, 33, 148, 64, 35, 117, 36, 41, 118, 41, 38, 147, 35, 36, 41, 36, 34, 36, 35, 34, 41, 40, 38, 41, 34, 40, 35, 64, 34, 34, 67, 40, 60, 93, 63, 148, 60, 64, 92, 61, 63, 60, 63, 64, 64, 52, 34, 64, 63, 52, 63, 61, 55, 52, 63, 55, 52, 55, 66, 91, 62, 61, 90, 59, 62, 61, 53, 55, 61, 62, 53, 55, 53, 66, 62, 54, 53, 62, 59, 54, 59, 51, 54, 59, 58, 51, 54, 69, 53, 54, 51, 69, 88, 56, 57, 89, 57, 58, 90, 58, 59, 149, 48, 56, 56, 48, 57, 48, 49, 57, 57, 49, 58, 48, 70, 49, 58, 50, 51, 58, 49, 50, 50, 49, 65, 51, 50, 65], "vertices": [2, 6, 133.98, 17.18, 0.96905, 74, -225.56, 67.68, 0.03095, 2, 6, 140.14, -7.8, 0.97017, 74, -219.41, 42.71, 0.02983, 2, 6, 142.25, -22.48, 0.97241, 74, -217.29, 28.02, 0.02759, 2, 6, 139.3, -48.14, 0.97698, 74, -220.24, 2.37, 0.02302, 2, 6, 134.1, -59.05, 0.97842, 74, -225.45, -8.54, 0.02158, 2, 6, 123.58, -67.23, 0.97914, 74, -235.96, -16.72, 0.02086, 2, 6, 110.04, -71.71, 0.98148, 74, -249.51, -21.21, 0.01852, 2, 6, 92.45, -75.51, 0.98305, 74, -267.09, -25, 0.01695, 2, 6, 83.78, -77.11, 0.98374, 74, -275.76, -26.61, 0.01626, 2, 6, 73.87, -77.61, 0.98368, 74, -285.68, -27.11, 0.01632, 2, 6, 58.58, -75.99, 0.98196, 74, -300.96, -25.49, 0.01804, 2, 6, 39.29, -79.29, 0.98226, 74, -320.26, -28.79, 0.01774, 2, 6, 21.94, -79.68, 0.98208, 74, -337.6, -29.18, 0.01792, 2, 6, -11.51, -68.64, 0.97788, 74, -371.05, -18.14, 0.02212, 2, 6, -35.34, -54.51, 0.97321, 74, -394.88, -4, 0.02679, 2, 6, -41.4, -47.43, 0.97074, 74, -400.94, 3.07, 0.02926, 2, 6, -44.05, -32.14, 0.96771, 74, -403.6, 18.36, 0.03229, 2, 6, -42.65, -20.36, 0.96688, 74, -402.19, 30.14, 0.03312, 2, 6, -33.93, 4.28, 0.96582, 74, -393.47, 54.78, 0.03418, 2, 6, -24.42, 25.46, 0.97288, 74, -383.97, 75.97, 0.02712, 2, 6, -16.86, 34.3, 0.97846, 74, -376.4, 84.8, 0.02154, 2, 6, 4.45, 45.98, 0.98602, 74, -355.1, 96.49, 0.01398, 3, 6, 3.68, 54.39, 0.9862, 74, -355.87, 104.89, 0.01055, 75, -315.84, 104.89, 0.00325, 3, 6, 12.96, 69.58, 0.98386, 74, -346.59, 120.09, 0.00486, 75, -306.55, 120.09, 0.01129, 3, 6, 28.72, 79.62, 0.98182, 74, -330.82, 130.12, 0.00255, 75, -290.79, 130.12, 0.01562, 3, 6, 43.88, 86.78, 0.98073, 74, -315.66, 137.29, 0.00105, 75, -275.63, 137.29, 0.01822, 3, 6, 60.11, 87.08, 0.98135, 74, -299.43, 137.58, 0.00215, 75, -259.4, 137.58, 0.01649, 3, 6, 67.91, 81.71, 0.98298, 74, -291.64, 132.21, 0.00518, 75, -251.61, 132.21, 0.01184, 3, 6, 69.5, 72.18, 0.98439, 74, -290.04, 122.68, 0.01011, 75, -250.01, 122.68, 0.0055, 2, 6, 61.53, 60.41, 0.9907, 74, -298.01, 110.92, 0.0093, 2, 6, 81.87, 61.11, 0.98708, 74, -277.67, 111.61, 0.01292, 2, 6, 117.74, 43.22, 0.97233, 74, -241.8, 93.73, 0.02767, 2, 6, 126.94, 32.37, 0.97033, 74, -232.6, 82.88, 0.02967, 7, 29, 95.71, -32.27, 0, 30, 76.64, 22.97, 0.00582, 31, 50.89, 25.44, 0.01182, 34, -13.98, -5.79, 0.0066, 33, -0.49, -4.67, 0.73275, 32, 9.88, -3.21, 0.23365, 75, -223.03, -17.54, 0.00936, 6, 30, 59.46, 9.02, 0.00408, 31, 34.4, 10.68, 0.0696, 34, 8.15, -5.15, 0.82056, 33, 21.62, -5.78, 0.10103, 74, -270.89, 3.16, 0.00324, 75, -230.85, 3.16, 0.0015, 7, 29, 68.76, -35.93, 9e-05, 30, 58.52, 2.69, 0.00013, 31, 33.77, 4.32, 0.04536, 34, 12.74, -0.7, 0.94669, 33, 26.55, -1.7, 0.00362, 74, -276.59, 6.05, 0.00394, 75, -236.56, 6.05, 0.00017, 6, 29, 71.53, -41.52, 0.00105, 31, 39.61, 2.14, 0.00431, 34, 9.68, 4.74, 0.96544, 32, 17.75, 21.46, 0.02489, 74, -280.73, 1.39, 0.00295, 75, -240.7, 1.39, 0.00137, 4, 34, -12.63, 3.59, 0.0038, 33, 1.6, 4.57, 0.35232, 32, 3.78, 4.04, 0.63459, 75, -232.33, -19.32, 0.00929, 6, 29, 82.6, -42.33, 8e-05, 34, -1.43, 4.96, 0.44649, 33, 12.88, 5.05, 0.45386, 32, 10.21, 13.31, 0.09322, 74, -277.32, -9.18, 0.00127, 75, -237.28, -9.18, 0.00507, 6, 30, 68.55, 15.88, 0.00466, 31, 43.16, 17.97, 0.01828, 34, -3.24, -5.07, 0.17759, 33, 10.27, -4.8, 0.79294, 74, -267.25, -7.63, 0.00126, 75, -227.22, -7.63, 0.00526, 6, 30, 64.06, 12.3, 0.00453, 31, 38.84, 14.18, 0.03192, 34, 2.5, -4.96, 0.59855, 33, 16, -5.14, 0.35936, 74, -269.22, -2.24, 0.00226, 75, -229.19, -2.24, 0.00338, 6, 29, 76.79, -42.06, 0.0004, 34, 4.39, 5, 0.92442, 33, 18.68, 4.63, 0.02507, 32, 14.04, 17.69, 0.04479, 74, -279.25, -3.69, 0.00218, 75, -239.22, -3.69, 0.00314, 8, 29, 90.5, -32.27, 0, 30, 72.67, 19.58, 0.00522, 31, 47.1, 21.87, 0.01361, 34, -8.77, -5.5, 0.03512, 33, 4.73, -4.79, 0.93697, 32, 13.13, 0.87, 0.00177, 74, -265.03, -12.71, 0.0003, 75, -225, -12.71, 0.00702, 5, 34, -7.28, 4.07, 0.04082, 33, 6.97, 4.63, 0.75128, 32, 6.97, 8.36, 0.20055, 74, -274.57, -14.42, 0.00036, 75, -234.54, -14.42, 0.00699, 7, 29, 98.7, -34.73, 0, 30, 80.51, 23.04, 0.00292, 31, 54.76, 25.69, 0.00528, 34, -17.09, -3.49, 6e-05, 33, -3.41, -2.13, 0.32272, 32, 6.09, -4, 0.65523, 75, -224.19, -21.23, 0.01379, 4, 34, -15.98, 5.2, 0.00893, 33, -1.61, 6.44, 0.08588, 32, 0.35, 2.6, 0.89154, 75, -232.76, -23.01, 0.01365, 2, 6, 134.57, -32.82, 0.97151, 74, -224.97, 17.68, 0.02849, 2, 6, 135.53, -39.37, 0.97347, 74, -224.02, 11.14, 0.02653, 6, 29, -1.45, -0.34, 0.98476, 30, -18, -15.8, 0.00067, 31, -41.78, -17.83, 0.00077, 34, 84.74, -32.52, 0.00027, 32, 95.49, 52.75, 1e-05, 75, -229.96, 84.5, 0.01352, 2, 29, 7.3, 3.6, 0.98837, 75, -223.03, 77.87, 0.01163, 4, 29, 16.75, 5.01, 0.95312, 30, -7.63, 0.07, 0.03754, 33, 77.48, -43.98, 0.00098, 75, -218.17, 69.64, 0.00836, 5, 29, 26.38, 3.08, 0.20148, 30, 0.95, 4.86, 0.78663, 31, -23.84, 3.72, 8e-05, 33, 67.91, -41.8, 0.00564, 75, -216.34, 59.99, 0.00616, 5, 30, 39.28, 5.3, 0.01295, 31, 14.43, 6, 0.8755, 34, 26.43, -14.46, 0.06775, 33, 39.11, -16.5, 0.03822, 75, -228.02, 23.48, 0.00558, 5, 30, 19.77, 5.64, 0.88244, 31, -5.08, 5.41, 0.09577, 34, 41.71, -26.6, 2e-05, 33, 53.39, -29.8, 0.01704, 75, -221.53, 41.89, 0.00472, 5, 29, 33.99, -2.29, 0.00014, 30, 10.23, 5.72, 0.97715, 31, -14.61, 5.02, 0.00651, 33, 60.43, -36.24, 0.01127, 75, -218.45, 50.92, 0.00493, 5, 30, 29.97, 5.51, 0.12128, 31, 5.12, 5.76, 0.84326, 34, 33.69, -20.29, 0.00449, 33, 45.89, -22.88, 0.02602, 75, -224.88, 32.25, 0.00494, 6, 29, -1.07, -2.31, 0.97756, 30, -16.43, -17.06, 0.00497, 31, -40.15, -19.01, 0.00329, 34, 84.25, -30.57, 0.00108, 32, 93.71, 53.69, 6e-05, 75, -231.65, 83.4, 0.01304, 6, 29, 7.75, -2.04, 0.97351, 30, -9.9, -11.13, 0.01294, 31, -33.9, -12.77, 0.00207, 34, 75.46, -31.31, 0.00046, 32, 88.41, 46.63, 3e-05, 75, -228.09, 75.33, 0.01099, 6, 29, 15.36, -2.57, 0.9183, 30, -3.77, -6.58, 0.07125, 31, -28, -7.94, 0.0017, 34, 67.83, -31.19, 0.00024, 32, 83.25, 41.01, 1e-05, 75, -225.71, 68.08, 0.00849, 6, 29, 23.51, -5.99, 0.22689, 30, 4.65, -3.9, 0.76313, 31, -19.72, -4.85, 0.0028, 34, 59.52, -28.2, 0.00017, 32, 75.5, 36.78, 1e-05, 75, -225.82, 59.25, 0.00699, 6, 29, 50.18, -29.81, 0.0061, 30, 40.41, -4.71, 0.00189, 31, 16.04, -3.95, 0.93553, 34, 31.61, -5.82, 0.04932, 32, 40.23, 30.83, 0.00137, 75, -237.87, 25.57, 0.0058, 6, 29, 36.83, -19.47, 0.02553, 30, 23.54, -5.51, 0.56678, 31, -0.78, -5.56, 0.40011, 34, 45.5, -15.44, 0.00194, 32, 56.64, 34.8, 0.00034, 75, -233.31, 41.83, 0.00531, 6, 29, 29.56, -12.09, 0.06191, 30, 13.22, -4.62, 0.91365, 31, -11.13, -5.16, 0.01793, 34, 53.15, -22.43, 0.00057, 32, 66.94, 35.87, 8e-05, 75, -229.2, 51.34, 0.00585, 6, 29, 43.1, -25.5, 0.01472, 30, 32.23, -6.03, 0.05609, 31, 7.93, -5.66, 0.91363, 34, 38.91, -9.75, 0.00931, 32, 48.01, 33.66, 0.00089, 75, -236.54, 33.75, 0.00536, 4, 31, 17.27, 1.41, 0.92, 34, 27.22, -9.13, 0.06734, 33, 40.32, -11.24, 0.0071, 75, -233.32, 22.49, 0.00556, 2, 6, 116.46, 20.72, 0.96614, 74, -243.08, 71.22, 0.03386, 2, 6, 115.33, -26.14, 0.96983, 74, -244.21, 24.37, 0.03017, 2, 6, 113.75, -46.08, 0.97367, 74, -245.79, 4.43, 0.02633, 2, 6, 113.46, -61.36, 0.97665, 74, -246.08, -10.86, 0.02335, 2, 6, 118.01, -2.58, 0.96755, 74, -241.53, 47.92, 0.03245, 2, 6, 106.58, 40.15, 0.97069, 74, -252.96, 90.65, 0.02931, 2, 6, 32.23, 54.05, 0.98998, 74, -327.31, 104.55, 0.01002, 2, 6, 46.66, 57.6, 0.9911, 74, -312.88, 108.1, 0.0089, 2, 6, 18.59, 50.43, 0.98837, 74, -340.95, 100.93, 0.01163, 2, 6, 64.26, 20.72, 0.96607, 74, -295.28, 71.23, 0.03393, 2, 6, 70.31, 15.25, 0.96676, 74, -289.23, 65.75, 0.03324, 2, 6, 73.16, 8.87, 0.96686, 74, -286.38, 59.37, 0.03314, 2, 6, 74.15, 1.46, 0.96665, 74, -285.4, 51.96, 0.03335, 2, 6, 72.05, -4.98, 0.96641, 74, -287.5, 45.52, 0.03359, 2, 6, 67.6, -9.67, 0.96665, 74, -291.95, 40.84, 0.03335, 2, 6, 62.59, -12.32, 0.96696, 74, -296.95, 38.18, 0.03304, 2, 6, 57.99, -13.69, 0.96711, 74, -301.55, 36.81, 0.03289, 2, 6, 59.41, 17.32, 0.96576, 74, -300.13, 67.82, 0.03424, 2, 6, 56.09, 11.83, 0.96635, 74, -303.46, 62.34, 0.03365, 2, 6, 54.58, 5.65, 0.96657, 74, -304.96, 56.15, 0.03343, 2, 6, 54.4, -0.49, 0.96676, 74, -305.15, 50.01, 0.03324, 2, 6, 55.29, -6.57, 0.96693, 74, -304.26, 43.93, 0.03307, 2, 6, 56.51, -11.23, 0.96705, 74, -303.03, 39.28, 0.03295, 2, 6, 71.15, 24.17, 0.96739, 74, -288.39, 74.67, 0.03261, 2, 6, 75.04, 16.43, 0.96721, 74, -284.51, 66.93, 0.03279, 2, 6, 77.66, 9.22, 0.96725, 74, -281.89, 59.73, 0.03275, 2, 6, 78.16, 0.68, 0.96694, 74, -281.38, 51.18, 0.03306, 2, 6, 75.9, -6.77, 0.96648, 74, -283.65, 43.73, 0.03352, 2, 6, 71.48, -11.85, 0.96684, 74, -288.07, 38.66, 0.03316, 2, 6, 66.21, -15.6, 0.96719, 74, -293.33, 34.9, 0.03281, 2, 6, 60.01, -18.29, 0.96764, 74, -299.53, 32.22, 0.03236, 2, 6, 62.3, 25.16, 0.96698, 74, -297.24, 75.66, 0.03302, 2, 6, 54.01, 19.53, 0.96584, 74, -305.53, 70.04, 0.03416, 2, 6, 50.97, 8.17, 0.96646, 74, -308.57, 58.67, 0.03354, 2, 6, 51.6, -3.73, 0.96688, 74, -307.94, 46.78, 0.03312, 2, 6, 54.84, -14.86, 0.96717, 74, -304.71, 35.65, 0.03283, 2, 6, 54.09, -48.69, 0.97299, 74, -305.45, 1.82, 0.02701, 2, 6, 58.47, -49.78, 0.97324, 74, -301.07, 0.72, 0.02676, 2, 6, 62.96, -52.2, 0.97376, 74, -296.59, -1.7, 0.02624, 2, 6, 65.74, -56.45, 0.97456, 74, -293.8, -5.95, 0.02544, 2, 6, 66.76, -62.25, 0.97569, 74, -292.78, -11.75, 0.02431, 2, 6, 65.71, -67.41, 0.9767, 74, -293.84, -16.91, 0.0233, 2, 6, 62.59, -71.06, 0.97846, 74, -296.95, -20.56, 0.02154, 2, 6, 59.51, -71.86, 0.9789, 74, -300.03, -21.36, 0.0211, 2, 6, 51.35, -51.61, 0.97354, 74, -308.19, -1.11, 0.02646, 2, 6, 50, -56.13, 0.9744, 74, -309.54, -5.62, 0.0256, 2, 6, 50.57, -61.54, 0.97546, 74, -308.97, -11.03, 0.02454, 2, 6, 52.13, -66.93, 0.97654, 74, -307.41, -16.42, 0.02346, 2, 6, 55.42, -69.99, 0.97735, 74, -304.12, -19.49, 0.02265, 2, 6, 55.45, -46.7, 0.9726, 74, -304.1, 3.81, 0.0274, 2, 6, 60.47, -48.07, 0.97293, 74, -299.08, 2.43, 0.02707, 2, 6, 65.02, -50.47, 0.97341, 74, -294.53, 0.03, 0.02659, 2, 6, 68.77, -55.49, 0.97439, 74, -290.78, -4.99, 0.02561, 2, 6, 70.4, -61.5, 0.97558, 74, -289.14, -11, 0.02442, 2, 6, 70.25, -66.87, 0.97662, 74, -289.29, -16.37, 0.02338, 2, 6, 67.97, -72.25, 0.97943, 74, -291.57, -21.74, 0.02057, 2, 6, 51.22, -48.05, 0.97285, 74, -308.33, 2.45, 0.02715, 2, 6, 47.15, -53.99, 0.97394, 74, -312.39, -3.49, 0.02606, 2, 6, 46.23, -62.1, 0.97549, 74, -313.32, -11.6, 0.02451, 2, 6, 49.37, -69.96, 0.97713, 74, -310.17, -19.46, 0.02287, 2, 6, 55.11, -73.79, 0.98025, 74, -304.43, -23.29, 0.01975, 2, 6, 61.65, -39.08, 0.9659, 74, -297.89, 11.42, 0.0341, 2, 6, 61.7, -32.14, 0.96434, 74, -297.84, 18.36, 0.03566, 2, 6, 37.67, -50.24, 0.96543, 74, -321.88, 0.26, 0.03457, 2, 6, 51.87, -42.67, 0.96481, 74, -307.67, 7.84, 0.03519, 2, 6, 28.58, -55.07, 0.97051, 74, -330.96, -4.57, 0.02949, 2, 6, 20.36, -53.82, 0.96988, 74, -339.19, -3.31, 0.03012, 2, 6, 19.52, -45.97, 0.96755, 74, -340.02, 4.54, 0.03244, 2, 6, 20.48, -36.67, 0.96537, 74, -339.06, 13.84, 0.03463, 2, 6, 20.01, -25.76, 0.96431, 74, -339.54, 24.74, 0.03569, 2, 6, 24.24, -20.89, 0.96403, 74, -335.3, 29.61, 0.03597, 2, 6, 32.07, -20.79, 0.96418, 74, -327.47, 29.71, 0.03582, 2, 6, 37.65, -26.42, 0.96463, 74, -321.9, 24.08, 0.03537, 2, 6, 51.44, -33.56, 0.96246, 74, -308.11, 16.94, 0.03754, 2, 6, 38.23, -33.83, 0.96114, 74, -321.31, 16.67, 0.03886, 2, 6, 31.65, -33.53, 0.96102, 74, -327.9, 16.97, 0.03898, 2, 6, 28.18, -41.2, 0.96244, 74, -331.36, 9.3, 0.03756, 2, 6, 28.15, -46.49, 0.96399, 74, -331.39, 4.01, 0.03601, 2, 6, 32.52, -51.59, 0.96564, 74, -327.03, -1.09, 0.03436, 2, 6, 34.66, -40.66, 0.96053, 74, -324.89, 9.84, 0.03947, 2, 6, 34.71, -47.5, 0.96253, 74, -324.84, 3, 0.03747, 2, 6, 33.89, -53.26, 0.97019, 74, -325.65, -2.76, 0.02981, 2, 6, 71.48, -42.25, 0.96661, 74, -288.07, 8.26, 0.03339, 2, 6, 71.93, -29.55, 0.96384, 74, -287.61, 20.96, 0.03616, 2, 6, 74.44, 40.17, 0.9694, 74, -285.1, 90.67, 0.0306, 2, 6, 57.03, 39.99, 0.96827, 74, -302.52, 90.5, 0.03173, 2, 6, 40.21, 36.81, 0.96708, 74, -319.33, 87.31, 0.03292, 2, 6, 14.41, 30.03, 0.96577, 74, -345.13, 80.53, 0.03423, 2, 6, -12.91, 14.65, 0.96521, 74, -372.46, 65.15, 0.03479, 2, 6, -32.51, -16.59, 0.96354, 74, -392.06, 33.91, 0.03646, 2, 6, -35.99, -30.94, 0.96361, 74, -395.54, 19.57, 0.03639, 2, 6, -34.51, -47.01, 0.96667, 74, -394.05, 3.49, 0.03333, 2, 6, -27.95, -53.87, 0.96886, 74, -387.49, -3.37, 0.03114, 2, 6, -9.56, -63.87, 0.97374, 74, -369.1, -13.37, 0.02626, 2, 6, 22.57, -74.7, 0.97752, 74, -336.97, -24.2, 0.02248, 2, 6, 37.92, -73.92, 0.97733, 74, -321.62, -23.42, 0.02267, 2, 6, 34.67, 5.92, 0.96342, 74, -324.88, 56.42, 0.03658, 2, 6, 15.82, 3.98, 0.96279, 74, -343.72, 54.49, 0.03721, 2, 6, 46.07, -19.21, 0.96771, 74, -313.47, 31.3, 0.03229, 2, 6, -1.37, -9.19, 0.9651, 74, -360.91, 41.32, 0.0349, 2, 6, -1.57, -12.25, 0.96508, 74, -361.11, 38.25, 0.03492, 2, 6, 2.07, -16.82, 0.96508, 74, -357.48, 33.68, 0.03492, 2, 6, 8.26, -23.7, 0.96506, 74, -351.28, 26.8, 0.03494, 2, 6, 12.07, -30.62, 0.965, 74, -347.47, 19.88, 0.035, 2, 6, 13.21, -36.39, 0.96523, 74, -346.34, 14.11, 0.03477, 2, 6, 10.88, -41.59, 0.9662, 74, -348.67, 8.92, 0.0338, 2, 6, 12.98, -47.2, 0.96792, 74, -346.56, 3.3, 0.03208, 2, 6, 10.39, -51.61, 0.96949, 74, -349.16, -1.11, 0.03051, 2, 6, 5.86, -55.39, 0.97032, 74, -353.68, -4.89, 0.02968, 2, 6, 0.34, -57.24, 0.9708, 74, -359.2, -6.74, 0.0292, 2, 6, -4.32, -8.77, 0.9651, 74, -363.87, 41.73, 0.0349, 2, 6, -5.22, -12.72, 0.96509, 74, -364.76, 37.79, 0.03491, 2, 6, -10.54, -17.03, 0.9651, 74, -370.08, 33.47, 0.0349, 2, 6, -14, -22.89, 0.96508, 74, -373.54, 27.62, 0.03492, 2, 6, -15.55, -30.34, 0.96505, 74, -375.1, 20.17, 0.03495, 2, 6, -14.8, -39.82, 0.96533, 74, -374.34, 10.68, 0.03467, 2, 6, -14.54, -47.99, 0.96774, 74, -374.09, 2.52, 0.03226, 2, 6, -11.75, -53.34, 0.96924, 74, -371.29, -2.84, 0.03076, 2, 6, -7.66, -56.11, 0.97029, 74, -367.2, -5.61, 0.02971, 2, 6, -3.7, -57.08, 0.97069, 74, -363.24, -6.58, 0.02931, 2, 6, 1.04, -6.66, 0.96513, 74, -358.5, 43.84, 0.03487, 2, 6, -2.04, -3.49, 0.96513, 74, -361.58, 47.01, 0.03487, 2, 6, -5.98, -6.29, 0.96513, 74, -365.53, 44.22, 0.03487, 2, 6, -3, -7.17, 0.96511, 74, -362.54, 43.33, 0.03489, 2, 6, -3.34, -12.58, 0.96508, 74, -362.89, 37.93, 0.03492, 2, 6, -2.57, -17.96, 0.96505, 74, -362.11, 32.54, 0.03495, 2, 6, -1.85, -22.61, 0.96502, 74, -361.39, 27.89, 0.03498, 2, 6, 0.46, -28.67, 0.96501, 74, -359.08, 21.83, 0.03499, 2, 6, 1.85, -33.93, 0.96498, 74, -357.69, 16.57, 0.03502, 2, 6, 2.13, -39.39, 0.96547, 74, -357.42, 11.12, 0.03453, 2, 6, 1.14, -43.47, 0.96668, 74, -358.41, 7.03, 0.03332, 2, 6, -0.98, -46.8, 0.96765, 74, -360.52, 3.7, 0.03235, 2, 6, -2.6, -28.01, 0.96499, 74, -362.15, 22.5, 0.03501, 2, 6, -2.46, -34.07, 0.96496, 74, -362.01, 16.43, 0.03504, 2, 6, -2.21, -39.39, 0.96544, 74, -361.76, 11.11, 0.03456, 2, 6, -1.61, -43.82, 0.96676, 74, -361.16, 6.68, 0.03324, 2, 6, -2.04, -52.13, 0.96916, 74, -361.58, -1.63, 0.03084, 2, 6, -1.86, -57.17, 0.97072, 74, -361.4, -6.67, 0.02928, 2, 6, 0.29, -59.88, 0.97165, 74, -359.25, -9.37, 0.02835, 2, 6, -3.63, -59.86, 0.97159, 74, -363.18, -9.36, 0.02841, 2, 6, -7.33, -33, 0.96384, 74, -366.87, 17.5, 0.03616, 2, 6, -6.98, -40.25, 0.96446, 74, -366.52, 10.26, 0.03554, 2, 6, -6.39, -46.05, 0.96626, 74, -365.94, 4.45, 0.03374, 2, 6, -7.04, -26.44, 0.964, 74, -366.58, 24.06, 0.036, 2, 6, -5.34, -49.65, 0.96752, 74, -364.88, 0.85, 0.03248, 2, 6, -18.96, -39.96, 0.96626, 74, -378.5, 10.54, 0.03374, 2, 6, -21.14, -28.56, 0.96416, 74, -380.69, 21.94, 0.03584, 2, 6, -19.78, -48.82, 0.96817, 74, -379.32, 1.68, 0.03183, 3, 6, 48.87, 72.44, 0.98398, 74, -310.67, 122.95, 0.00764, 75, -270.64, 122.95, 0.00838], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 26, 28, 32, 34, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 38, 40, 40, 42, 34, 36, 36, 38, 28, 30, 30, 32, 22, 24, 20, 22, 16, 18, 18, 20, 14, 16, 68, 80, 80, 78, 72, 82, 82, 76, 66, 84, 84, 78, 74, 86, 86, 76, 66, 88, 74, 90, 68, 70, 70, 72, 24, 26, 62, 64, 10, 12, 12, 14, 6, 8, 8, 10, 4, 6, 96, 98, 98, 100, 100, 102, 102, 108, 108, 106, 104, 110, 110, 106, 112, 114, 114, 116, 116, 118, 118, 124, 124, 122, 120, 126, 126, 122, 96, 112, 104, 128, 128, 120, 60, 62, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 148, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 162, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 192, 194, 194, 196, 196, 198, 198, 200, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 202, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 216, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 242, 244, 244, 246, 246, 248, 248, 250, 252, 258, 258, 256, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 254, 276, 276, 278, 280, 282, 282, 284, 256, 286, 284, 286, 278, 280, 288, 290, 292, 260, 144, 142, 142, 146, 146, 42, 144, 58, 302, 304, 304, 306, 306, 308, 310, 312, 314, 316, 316, 318, 318, 320, 322, 324, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 346, 346, 348, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 376, 378, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 388, 390, 390, 392, 382, 394, 394, 396, 396, 398, 398, 400, 400, 392, 392, 402, 402, 404, 410, 412, 412, 414, 410, 416, 418, 414, 420, 422, 420, 424, 312, 314, 310, 308], "width": 171, "height": 188}}, "head2": {"head": {"type": "mesh", "uvs": [0.33648, 0.76956, 0.43588, 0.63391, 0.58878, 0.6208, 0.61079, 0.6487, 0.67348, 0.65962, 0.72512, 0.676, 0.7709, 0.68131, 0.78914, 0.64027, 0.89272, 0.69545, 0.8123, 0.86441, 0.69974, 0.97043, 0.65133, 0.9931, 0.56073, 0.9881, 0.49559, 0.96639, 0.36713, 0.89103, 0.4878, 0.9093, 0.56474, 0.94491, 0.6582, 0.95693, 0.7061, 0.9314, 0.78779, 0.84848, 0.48765, 0.73901, 0.50483, 0.74382, 0.53573, 0.73061, 0.58323, 0.70698, 0.62774, 0.69574, 0.6621, 0.69694, 0.68853, 0.71537, 0.72333, 0.71136, 0.74492, 0.7302, 0.76034, 0.75825, 0.76342, 0.7891, 0.48131, 0.75381, 0.50255, 0.76327, 0.51994, 0.79609, 0.54859, 0.82118, 0.58888, 0.83838, 0.64389, 0.84609, 0.69068, 0.8548, 0.72494, 0.84691, 0.74621, 0.82913, 0.75705, 0.80981, 0.47653, 0.72344, 0.45435, 0.73551, 0.46494, 0.75935, 0.47401, 0.74498, 0.50429, 0.7534, 0.53598, 0.756, 0.56341, 0.75795, 0.60101, 0.75344, 0.63282, 0.75269, 0.66421, 0.75795, 0.68611, 0.7681, 0.70222, 0.78313, 0.59308, 0.7685, 0.6278, 0.7752, 0.65839, 0.78043, 0.68441, 0.78275, 0.73113, 0.79515, 0.76005, 0.80041, 0.77834, 0.79259, 0.77297, 0.81288, 0.61513, 0.79906, 0.65684, 0.80615, 0.69067, 0.81024, 0.57822, 0.78952, 0.71255, 0.80919, 0.63908, 0.86782, 0.57123, 0.86513, 0.68836, 0.88291], "triangles": [6, 7, 8, 24, 3, 4, 25, 24, 4, 25, 4, 5, 2, 41, 1, 23, 2, 3, 23, 3, 24, 5, 26, 25, 27, 5, 6, 27, 26, 5, 2, 22, 41, 28, 27, 6, 23, 22, 2, 42, 1, 41, 21, 20, 41, 22, 21, 41, 44, 42, 41, 44, 41, 20, 49, 24, 25, 50, 49, 25, 48, 23, 24, 45, 20, 21, 44, 20, 45, 49, 48, 24, 47, 22, 23, 31, 44, 45, 47, 46, 22, 21, 22, 46, 45, 21, 46, 26, 50, 25, 23, 48, 47, 29, 28, 6, 29, 6, 8, 43, 42, 44, 43, 44, 31, 32, 31, 45, 51, 50, 26, 53, 47, 48, 0, 1, 42, 54, 48, 49, 53, 48, 54, 55, 49, 50, 55, 50, 51, 54, 49, 55, 56, 55, 51, 51, 27, 28, 27, 51, 26, 52, 28, 29, 52, 51, 28, 56, 51, 52, 59, 30, 29, 57, 52, 29, 64, 47, 53, 8, 59, 29, 30, 57, 29, 46, 32, 45, 33, 32, 46, 61, 53, 54, 64, 53, 61, 58, 57, 30, 62, 54, 55, 62, 55, 56, 61, 54, 62, 65, 52, 57, 40, 57, 58, 63, 56, 52, 63, 52, 65, 62, 56, 63, 59, 58, 30, 60, 58, 59, 40, 58, 60, 33, 47, 64, 47, 33, 46, 34, 33, 64, 39, 57, 40, 65, 57, 39, 35, 64, 61, 34, 64, 35, 36, 61, 62, 35, 61, 36, 38, 65, 39, 19, 59, 8, 60, 59, 19, 38, 37, 63, 38, 63, 65, 36, 62, 63, 37, 36, 63, 9, 19, 8, 67, 34, 35, 66, 35, 36, 66, 36, 37, 67, 35, 66, 68, 66, 37, 0, 15, 14, 43, 0, 42, 43, 32, 33, 32, 43, 31, 15, 33, 34, 15, 34, 67, 15, 43, 33, 15, 0, 43, 38, 68, 37, 18, 68, 38, 39, 40, 60, 39, 60, 19, 38, 39, 19, 18, 38, 19, 16, 15, 67, 17, 66, 68, 17, 68, 18, 16, 67, 66, 17, 16, 66, 13, 15, 16, 14, 15, 13, 10, 17, 18, 18, 19, 9, 10, 18, 9, 12, 13, 16, 11, 12, 16, 17, 11, 16, 11, 17, 10], "vertices": [2, 6, -12.91, 14.65, 0.96521, 74, -372.46, 65.15, 0.03479, 2, 6, 15.82, 3.98, 0.96279, 74, -343.72, 54.49, 0.03721, 2, 6, 24.24, -20.89, 0.96403, 74, -335.3, 29.61, 0.03597, 2, 6, 20.01, -25.76, 0.96431, 74, -339.54, 24.74, 0.03569, 2, 6, 20.48, -36.67, 0.96537, 74, -339.06, 13.84, 0.03463, 2, 6, 19.52, -45.97, 0.96755, 74, -340.02, 4.54, 0.03244, 2, 6, 20.36, -53.82, 0.96988, 74, -339.19, -3.31, 0.03012, 2, 6, 28.58, -55.07, 0.97051, 74, -330.96, -4.57, 0.02949, 2, 6, 22.57, -74.7, 0.97752, 74, -336.97, -24.2, 0.02248, 2, 6, -11.51, -68.64, 0.97788, 74, -371.05, -18.14, 0.02212, 2, 6, -35.34, -54.51, 0.97321, 74, -394.88, -4, 0.02679, 2, 6, -41.4, -47.43, 0.97074, 74, -400.94, 3.07, 0.02926, 2, 6, -44.05, -32.14, 0.96771, 74, -403.6, 18.36, 0.03229, 2, 6, -42.65, -20.36, 0.96688, 74, -402.19, 30.14, 0.03312, 2, 6, -33.93, 4.28, 0.96582, 74, -393.47, 54.78, 0.03418, 2, 6, -32.51, -16.59, 0.96354, 74, -392.06, 33.91, 0.03646, 2, 6, -35.99, -30.94, 0.96361, 74, -395.54, 19.57, 0.03639, 2, 6, -34.51, -47.01, 0.96667, 74, -394.05, 3.49, 0.03333, 2, 6, -27.95, -53.87, 0.96886, 74, -387.49, -3.37, 0.03114, 2, 6, -9.56, -63.87, 0.97374, 74, -369.1, -13.37, 0.02626, 2, 6, -1.37, -9.19, 0.9651, 74, -360.91, 41.32, 0.0349, 2, 6, -1.57, -12.25, 0.96508, 74, -361.11, 38.25, 0.03492, 2, 6, 2.07, -16.82, 0.96508, 74, -357.48, 33.68, 0.03492, 2, 6, 8.26, -23.7, 0.96506, 74, -351.28, 26.8, 0.03494, 2, 6, 12.07, -30.62, 0.965, 74, -347.47, 19.88, 0.035, 2, 6, 13.21, -36.39, 0.96523, 74, -346.34, 14.11, 0.03477, 2, 6, 10.88, -41.59, 0.9662, 74, -348.67, 8.92, 0.0338, 2, 6, 12.98, -47.2, 0.96792, 74, -346.56, 3.3, 0.03208, 2, 6, 10.39, -51.61, 0.96949, 74, -349.16, -1.11, 0.03051, 2, 6, 5.86, -55.39, 0.97032, 74, -353.68, -4.89, 0.02968, 2, 6, 0.34, -57.24, 0.9708, 74, -359.2, -6.74, 0.0292, 2, 6, -4.32, -8.77, 0.9651, 74, -363.87, 41.73, 0.0349, 2, 6, -5.22, -12.72, 0.96509, 74, -364.76, 37.79, 0.03491, 2, 6, -10.54, -17.03, 0.9651, 74, -370.08, 33.47, 0.0349, 2, 6, -14, -22.89, 0.96508, 74, -373.54, 27.62, 0.03492, 2, 6, -15.55, -30.34, 0.96505, 74, -375.1, 20.17, 0.03495, 2, 6, -14.8, -39.82, 0.96533, 74, -374.34, 10.68, 0.03467, 2, 6, -14.54, -47.99, 0.96774, 74, -374.09, 2.52, 0.03226, 2, 6, -11.75, -53.34, 0.96924, 74, -371.29, -2.84, 0.03076, 2, 6, -7.66, -56.11, 0.97029, 74, -367.2, -5.61, 0.02971, 2, 6, -3.7, -57.08, 0.97069, 74, -363.24, -6.58, 0.02931, 2, 6, 1.04, -6.66, 0.96513, 74, -358.5, 43.84, 0.03487, 2, 6, -2.04, -3.49, 0.96513, 74, -361.58, 47.01, 0.03487, 2, 6, -5.98, -6.29, 0.96513, 74, -365.53, 44.22, 0.03487, 2, 6, -3, -7.17, 0.96511, 74, -362.54, 43.33, 0.03489, 2, 6, -3.34, -12.58, 0.96508, 74, -362.89, 37.93, 0.03492, 2, 6, -2.57, -17.96, 0.96505, 74, -362.11, 32.54, 0.03495, 2, 6, -1.85, -22.61, 0.96502, 74, -361.39, 27.89, 0.03498, 2, 6, 0.46, -28.67, 0.96501, 74, -359.08, 21.83, 0.03499, 2, 6, 1.85, -33.93, 0.96498, 74, -357.69, 16.57, 0.03502, 2, 6, 2.13, -39.39, 0.96547, 74, -357.42, 11.12, 0.03453, 2, 6, 1.14, -43.47, 0.96668, 74, -358.41, 7.03, 0.03332, 2, 6, -0.98, -46.8, 0.96765, 74, -360.52, 3.7, 0.03235, 2, 6, -2.6, -28.01, 0.96499, 74, -362.15, 22.5, 0.03501, 2, 6, -2.46, -34.07, 0.96496, 74, -362.01, 16.43, 0.03504, 2, 6, -2.21, -39.39, 0.96544, 74, -361.76, 11.11, 0.03456, 2, 6, -1.61, -43.82, 0.96676, 74, -361.16, 6.68, 0.03324, 2, 6, -2.04, -52.13, 0.96916, 74, -361.58, -1.63, 0.03084, 2, 6, -1.86, -57.17, 0.97072, 74, -361.4, -6.67, 0.02928, 2, 6, 0.29, -59.88, 0.97165, 74, -359.25, -9.37, 0.02835, 2, 6, -3.63, -59.86, 0.97159, 74, -363.18, -9.36, 0.02841, 2, 6, -7.33, -33, 0.96384, 74, -366.87, 17.5, 0.03616, 2, 6, -6.98, -40.25, 0.96446, 74, -366.52, 10.26, 0.03554, 2, 6, -6.39, -46.05, 0.96626, 74, -365.94, 4.45, 0.03374, 2, 6, -7.04, -26.44, 0.964, 74, -366.58, 24.06, 0.036, 2, 6, -5.34, -49.65, 0.96752, 74, -364.88, 0.85, 0.03248, 2, 6, -18.96, -39.96, 0.96626, 74, -378.5, 10.54, 0.03374, 2, 6, -21.14, -28.56, 0.96416, 74, -380.69, 21.94, 0.03584, 2, 6, -19.78, -48.82, 0.96817, 74, -379.32, 1.68, 0.03183], "hull": 15, "edges": [18, 20, 24, 26, 26, 28, 20, 22, 22, 24, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 0, 30, 32, 34, 36, 38, 38, 16, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 94, 106, 106, 108, 108, 110, 110, 112, 112, 104, 104, 114, 114, 116, 122, 124, 124, 126, 122, 128, 130, 126, 132, 134, 132, 136, 34, 36, 32, 30, 0, 28, 0, 2, 2, 4, 14, 16, 16, 18], "width": 171, "height": 188}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.89314, 0.01908, 0.92722, 0.03851, 0.95843, 0.06439, 0.98111, 0.0904, 0.99407, 0.11572, 0.99897, 0.14556, 0.99705, 0.173, 0.98446, 0.20582, 0.96132, 0.24995, 0.93703, 0.29029, 0.91339, 0.32802, 0.84053, 0.39396, 0.78806, 0.45272, 0.75057, 0.49193, 0.73037, 0.51107, 0.71563, 0.53203, 0.69888, 0.54875, 0.67387, 0.56243, 0.64407, 0.5753, 0.6171, 0.59352, 0.55294, 0.64464, 0.47238, 0.69738, 0.39182, 0.75011, 0.3617, 0.77167, 0.34115, 0.79218, 0.32556, 0.80844, 0.31689, 0.82467, 0.30013, 0.84694, 0.27696, 0.88004, 0.25929, 0.90528, 0.24641, 0.92368, 0.24459, 0.94496, 0.24829, 0.9978, 0.20822, 0.99848, 0.09589, 0.95961, 0.06134, 0.93738, 0.04628, 0.91678, 0.06998, 0.89648, 0.09653, 0.8773, 0.11429, 0.85638, 0.11558, 0.83707, 0.01949, 0.88105, 0, 0.87264, 0.02121, 0.86207, 0.07943, 0.83112, 0.09891, 0.81873, 0.12533, 0.77844, 0.20841, 0.76162, 0.2279, 0.76099, 0.24278, 0.75457, 0.26, 0.74295, 0.2779, 0.72534, 0.32094, 0.67024, 0.36937, 0.58392, 0.42031, 0.54488, 0.47133, 0.52468, 0.49987, 0.51338, 0.52453, 0.5039, 0.53198, 0.49097, 0.53249, 0.47302, 0.53191, 0.43646, 0.54144, 0.36701, 0.56506, 0.29345, 0.57878, 0.26452, 0.58956, 0.22881, 0.60033, 0.1931, 0.6083, 0.1667, 0.61363, 0.13354, 0.61008, 0.10426, 0.60469, 0.07788, 0.60058, 0.05225, 0.72918, 0.02612, 0.85778, 0, 0.16161, 0.93613, 0.1552, 0.92179, 0.16073, 0.90598, 0.17611, 0.88993, 0.19462, 0.86725, 0.17752, 0.95097, 0.60037, 0.47852, 0.59644, 0.49617, 0.58606, 0.51542, 0.56612, 0.53188, 0.52502, 0.55106, 0.44978, 0.58678, 0.36176, 0.71203, 0.53407, 0.6086, 0.59112, 0.56937, 0.62765, 0.55394, 0.65493, 0.53862, 0.67114, 0.52149, 0.68173, 0.50495, 0.69222, 0.48545, 0.62504, 0.37996, 0.76146, 0.39193, 0.66098, 0.30314, 0.80904, 0.3177, 0.68901, 0.23541, 0.85273, 0.24561, 0.87228, 0.20488, 0.83089, 0.28165, 0.67384, 0.27165, 0.70469, 0.19872, 0.71387, 0.16933, 0.7242, 0.13236, 0.72076, 0.09756, 0.70699, 0.06817, 0.8057, 0.03499, 0.84817, 0.05917, 0.87802, 0.0895, 0.88835, 0.126, 0.88605, 0.16676], "triangles": [34, 78, 33, 32, 33, 31, 33, 78, 31, 34, 73, 78, 34, 35, 73, 78, 73, 31, 31, 73, 30, 35, 74, 73, 35, 36, 74, 73, 74, 30, 74, 75, 30, 30, 75, 29, 36, 37, 74, 74, 37, 75, 37, 38, 75, 75, 76, 29, 75, 38, 76, 29, 76, 28, 76, 38, 77, 76, 77, 28, 77, 38, 39, 41, 42, 43, 28, 77, 27, 39, 40, 77, 77, 40, 27, 40, 41, 43, 27, 40, 26, 46, 26, 45, 26, 46, 47, 44, 40, 43, 25, 26, 47, 44, 45, 40, 26, 40, 45, 25, 47, 48, 25, 48, 24, 24, 48, 49, 24, 49, 23, 49, 50, 23, 23, 50, 22, 50, 51, 22, 51, 85, 22, 22, 85, 21, 51, 52, 85, 20, 85, 86, 84, 52, 53, 85, 20, 21, 86, 85, 84, 52, 84, 85, 20, 86, 19, 84, 83, 86, 86, 87, 19, 86, 83, 87, 19, 87, 18, 53, 54, 84, 84, 54, 83, 87, 88, 18, 18, 88, 17, 83, 82, 87, 87, 82, 88, 88, 89, 17, 17, 89, 16, 88, 82, 89, 54, 55, 83, 55, 56, 83, 83, 56, 82, 16, 89, 15, 82, 81, 89, 89, 90, 15, 89, 81, 90, 56, 57, 82, 82, 57, 81, 15, 90, 14, 90, 91, 14, 91, 90, 80, 81, 57, 80, 90, 81, 80, 80, 57, 58, 13, 14, 92, 14, 91, 92, 91, 80, 92, 80, 79, 92, 80, 58, 79, 13, 92, 12, 58, 59, 79, 94, 12, 92, 94, 92, 93, 93, 92, 79, 93, 79, 60, 79, 59, 60, 12, 94, 11, 60, 61, 93, 10, 11, 96, 11, 94, 96, 96, 94, 95, 94, 93, 95, 93, 61, 95, 61, 62, 95, 96, 100, 10, 10, 100, 9, 95, 101, 96, 96, 101, 100, 95, 62, 101, 101, 62, 63, 100, 98, 9, 9, 98, 8, 101, 97, 100, 100, 97, 98, 101, 63, 97, 63, 64, 97, 98, 99, 8, 8, 99, 7, 97, 102, 98, 98, 102, 99, 102, 97, 65, 97, 64, 65, 99, 111, 7, 7, 111, 6, 102, 103, 99, 99, 103, 111, 65, 66, 102, 102, 66, 103, 6, 111, 5, 103, 104, 111, 111, 110, 5, 111, 104, 110, 110, 4, 5, 104, 105, 110, 105, 109, 110, 110, 3, 4, 110, 109, 3, 105, 108, 109, 109, 2, 3, 109, 108, 2, 108, 1, 2, 106, 107, 108, 106, 71, 107, 108, 0, 1, 108, 107, 0, 107, 72, 0, 107, 71, 72, 66, 67, 103, 103, 67, 104, 67, 68, 104, 68, 105, 104, 68, 69, 105, 69, 106, 105, 105, 106, 108, 69, 70, 106, 106, 70, 71], "vertices": [2, 13, -5.33, 42.93, 0.85073, 14, -84.42, 7.86, 0.14927, 2, 13, 17.68, 55.69, 0.58552, 14, -66.5, 27.14, 0.41448, 2, 13, 47.57, 66.3, 0.26996, 14, -41.4, 46.52, 0.73004, 2, 13, 77.14, 73.07, 0.07673, 14, -15.4, 62.14, 0.92327, 2, 13, 105.41, 75.55, 0.01067, 14, 10.7, 73.29, 0.98933, 1, 14, 42.38, 81.82, 1, 1, 14, 72.08, 86.81, 1, 1, 14, 108.51, 88.2, 1, 1, 14, 158.04, 87.31, 1, 1, 14, 203.58, 85.11, 1, 1, 14, 246.26, 82.63, 1, 1, 14, 323.63, 64.29, 1, 1, 14, 391.48, 53.49, 1, 2, 14, 436.97, 45.17, 0.99872, 15, -55.15, 29.54, 0.00128, 2, 14, 459.34, 40.27, 0.90962, 15, -32.36, 31.81, 0.09038, 2, 14, 483.19, 38.18, 0.53944, 15, -9.04, 37.21, 0.46056, 2, 14, 502.66, 34.3, 0.20327, 15, 10.67, 39.55, 0.79673, 2, 14, 519.59, 26.1, 0.033, 15, 29.31, 36.99, 0.967, 1, 15, 48.21, 32.11, 1, 1, 15, 71.62, 31.2, 1, 1, 15, 134.75, 33.23, 1, 1, 15, 203.05, 29.63, 1, 1, 15, 271.35, 26.03, 1, 2, 15, 298.65, 25.68, 0.97687, 62, 180.44, -30.23, 0.02313, 2, 15, 322.83, 28.54, 0.57456, 62, 156.11, -29.3, 0.42544, 2, 15, 341.84, 31.09, 0.12183, 62, 136.93, -28.88, 0.87817, 2, 15, 359.3, 36.36, 0.00693, 62, 118.87, -31.38, 0.99307, 1, 62, 93.32, -32.74, 1, 2, 62, 55.62, -35.51, 0.9882, 61, 114.03, -63.06, 0.0118, 2, 62, 26.88, -37.61, 0.7966, 61, 90.74, -46.07, 0.2034, 2, 62, 5.93, -39.15, 0.37182, 61, 73.77, -33.7, 0.62818, 2, 62, -16.25, -46.48, 0.0365, 61, 52.11, -24.94, 0.9635, 1, 61, -2.94, -6.71, 1, 1, 61, 2.56, 10.61, 1, 2, 62, -54.71, 11.11, 0.03828, 61, 60.03, 43.86, 0.96172, 2, 62, -37.28, 34.25, 0.27065, 61, 88.3, 50.23, 0.72935, 2, 62, -18.45, 48.5, 0.51527, 61, 111.87, 48.92, 0.48473, 2, 62, 6.16, 46.16, 0.79684, 61, 129.12, 31.22, 0.20316, 2, 62, 30.06, 42.19, 0.98912, 61, 144.79, 12.73, 0.01088, 1, 62, 54.38, 42.61, 1, 1, 62, 74.45, 49.42, 1, 2, 15, 479.05, -51, 3e-05, 62, 14.09, 73.48, 0.99997, 2, 15, 475.31, -63.22, 3e-05, 62, 19.68, 84.97, 0.99997, 2, 15, 460.5, -60.49, 2e-05, 62, 33.89, 79.99, 0.99998, 2, 15, 418, -54.06, 1e-05, 62, 74.88, 67.04, 0.99999, 1, 62, 90.69, 63.49, 1, 2, 15, 357.41, -64.14, 0.03997, 62, 136.3, 67.61, 0.96003, 2, 15, 322.94, -40.3, 0.42565, 62, 166.67, 38.72, 0.57435, 2, 15, 318.02, -32.94, 0.61221, 62, 170.38, 30.68, 0.38779, 2, 15, 308.59, -30.49, 0.84723, 62, 179.33, 26.81, 0.15277, 2, 15, 293.65, -29.91, 0.98316, 62, 193.99, 23.92, 0.01684, 1, 15, 272.83, -32.27, 1, 1, 15, 210.57, -44.77, 1, 1, 15, 117.23, -71.87, 1, 2, 14, 522.99, -90.31, 0.01418, 15, 68.59, -72.64, 0.98582, 2, 14, 496.76, -71.94, 0.10866, 15, 37.97, -63.29, 0.89134, 2, 14, 482.09, -61.66, 0.26364, 15, 20.84, -58.07, 0.73636, 2, 14, 469.73, -52.72, 0.49881, 15, 6.31, -53.39, 0.50119, 2, 14, 455.15, -52.17, 0.7556, 15, -7.72, -57.38, 0.2444, 2, 14, 435.79, -55.76, 0.93297, 15, -25.01, -66.79, 0.06703, 2, 14, 396.5, -63.8, 0.9999, 15, -59.88, -86.6, 0.0001, 1, 14, 320.92, -74.35, 1, 2, 7, 308.45, -68.11, 0.00505, 14, 239.69, -79.52, 0.99495, 2, 7, 277.69, -58.12, 0.02566, 14, 207.35, -79.58, 0.97434, 2, 7, 239.39, -48.55, 0.10774, 14, 167.97, -82.39, 0.89226, 2, 7, 201.09, -38.99, 0.29987, 14, 128.59, -85.21, 0.70013, 2, 7, 172.77, -31.92, 0.55402, 14, 99.48, -87.29, 0.44598, 2, 7, 136.95, -25.15, 0.92991, 14, 63.33, -91.98, 0.07009, 1, 7, 104.87, -22.88, 1, 1, 7, 75.85, -21.81, 1, 2, 7, 47.71, -20.28, 0.99607, 13, 14.83, -93.02, 0.00393, 4, 7, 26.27, 41, 0.35719, 13, -6.61, -31.73, 0.60486, 14, -62.43, -63.5, 0.01117, 78, -286.01, 66.79, 0.02678, 2, 13, -28.04, 29.55, 0.98355, 14, -101.84, -11.91, 0.01645, 1, 61, 74.07, 7.08, 1, 2, 62, -6.47, 0.32, 0.24191, 61, 89.84, 4.43, 0.75809, 2, 62, 10.66, 3.99, 0.98319, 61, 105.29, -3.85, 0.01681, 1, 62, 29.59, 3.57, 1, 1, 62, 55.83, 4.34, 1, 1, 61, 56.3, 5.87, 1, 3, 14, 435.75, -24.42, 0.94582, 15, -34.76, -37.01, 0.02979, 78, -344.36, -429.5, 0.02439, 3, 14, 455.08, -22.42, 0.853, 15, -17, -29.11, 0.12394, 78, -346.14, -448.85, 0.02306, 3, 14, 476.71, -22.93, 0.49142, 15, 3.73, -22.9, 0.4874, 78, -350.84, -469.97, 0.02117, 3, 14, 496.18, -28.28, 0.14155, 15, 23.9, -21.96, 0.83925, 78, -359.87, -488.03, 0.0192, 3, 14, 520.43, -42.47, 0.02921, 15, 51.34, -27.94, 0.95436, 78, -378.49, -509.06, 0.01643, 2, 15, 102.18, -38.55, 0.985, 78, -412.58, -548.26, 0.015, 1, 15, 241.54, -6.25, 1, 2, 15, 104.42, 6.46, 0.985, 78, -374.39, -572.19, 0.015, 2, 15, 54.25, 8, 0.98388, 78, -348.55, -529.16, 0.01612, 2, 15, 31.39, 14.17, 0.98174, 78, -332, -512.22, 0.01826, 3, 14, 495.62, 12.61, 0.13868, 15, 10.7, 16.75, 0.84112, 78, -319.64, -495.42, 0.0202, 3, 14, 475.76, 16.17, 0.6812, 15, -9.29, 13.98, 0.29662, 78, -312.3, -476.63, 0.02218, 3, 14, 457.03, 17.36, 0.95723, 15, -27.46, 9.31, 0.01872, 78, -307.5, -458.49, 0.02405, 2, 14, 435.13, 17.87, 0.97377, 78, -302.75, -437.1, 0.02623, 2, 14, 327.51, -34.45, 0.97341, 78, -333.18, -321.38, 0.02659, 2, 14, 328.4, 28.73, 0.97276, 78, -271.38, -334.5, 0.02724, 3, 7, 324.23, -26.25, 0.00143, 14, 241.68, -34.83, 0.97187, 78, -316.9, -237.1, 0.0267, 2, 14, 244.33, 34.07, 0.9735, 78, -249.83, -253.07, 0.0265, 3, 7, 252, -4.7, 0.04737, 14, 166.33, -36.79, 0.92623, 78, -304.21, -162.8, 0.0264, 2, 14, 162.91, 38.14, 0.97387, 78, -230.04, -173.99, 0.02613, 2, 14, 117.36, 38.15, 0.97401, 78, -221.18, -129.31, 0.02599, 2, 14, 203.62, 36.1, 0.97358, 78, -239.93, -213.53, 0.02642, 3, 7, 290.63, -16.31, 0.01054, 14, 206.66, -35.82, 0.96303, 78, -311.07, -202.55, 0.02643, 3, 7, 212.9, 7.2, 0.14419, 14, 125.47, -37.64, 0.82937, 78, -297.1, -122.55, 0.02644, 3, 7, 181.39, 15.21, 0.2931, 14, 93.03, -39.81, 0.68018, 78, -292.94, -90.31, 0.02672, 3, 7, 141.69, 24.73, 0.53284, 14, 52.34, -43.09, 0.44001, 78, -288.26, -49.75, 0.02715, 4, 7, 103.61, 27.78, 0.6789, 13, 70.73, -44.96, 0.02936, 14, 15.19, -52.03, 0.26438, 78, -289.82, -11.58, 0.02736, 4, 7, 70.85, 25.47, 0.73623, 13, 37.97, -47.27, 0.12915, 14, -15.22, -64.41, 0.10738, 78, -296.06, 20.66, 0.02724, 4, 7, 40.1, 74.24, 0.00181, 13, 7.22, 1.51, 0.92442, 14, -59.61, -27.61, 0.04646, 78, -251.34, 57.06, 0.02731, 4, 7, 68.75, 90.15, 0.01611, 13, 35.87, 17.41, 0.47157, 14, -37.33, -3.59, 0.48609, 78, -232.1, 30.54, 0.02623, 3, 13, 70.53, 26.83, 0.06421, 14, -7.31, 16.14, 0.91023, 78, -218.58, -2.74, 0.02556, 2, 14, 31.06, 28.5, 0.9747, 78, -213.9, -42.78, 0.0253, 2, 14, 75.13, 36.15, 0.97458, 78, -214.94, -87.5, 0.02542], "hull": 73, "edges": [2, 4, 12, 14, 18, 20, 38, 40, 44, 46, 60, 62, 62, 64, 66, 68, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 100, 102, 102, 104, 104, 106, 106, 108, 122, 124, 124, 126, 132, 134, 2, 0, 0, 144, 4, 6, 6, 8, 8, 10, 10, 12, 138, 140, 134, 136, 136, 138, 130, 132, 126, 128, 128, 130, 20, 22, 118, 120, 120, 122, 114, 116, 116, 118, 112, 114, 108, 110, 110, 112, 28, 30, 26, 28, 30, 32, 32, 34, 34, 36, 36, 38, 22, 24, 24, 26, 14, 16, 16, 18, 92, 94, 94, 96, 96, 98, 98, 100, 46, 48, 48, 50, 50, 52, 52, 54, 68, 70, 70, 72, 64, 66, 58, 60, 72, 74, 74, 76, 76, 78, 78, 80, 54, 56, 56, 58, 40, 42, 42, 44, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 158, 186, 184, 188, 186, 190, 188, 192, 192, 200, 200, 196, 190, 202, 202, 194, 140, 142, 142, 144], "width": 453, "height": 1097}}}}], "animations": {"idle": {"bones": {"bone": {"translate": [{"x": 12.47, "curve": [0.444, 12.47, 0.889, -19.59, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -19.59, "curve": [1.778, -19.59, 2.222, 12.47, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 12.47, "curve": [3.111, 12.47, 3.556, -19.59, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -19.59, "curve": [4.444, -19.59, 4.889, 12.47, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 12.47, "curve": [5.667, 12.47, 6, -19.59, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": -19.59, "curve": [6.778, -19.59, 7.222, -7.57, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -7.57, "curve": [8.111, -7.57, 8.556, -19.59, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -19.59, "curve": [9.444, -19.59, 9.889, 12.47, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 12.47, "curve": [10.778, 12.47, 11.222, -19.59, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": -19.59, "curve": [12.111, -19.59, 12.556, 12.47, 12.111, 0, 12.556, 0]}, {"time": 13, "x": 12.47}]}, "bone2": {"translate": [{"y": -16.92, "curve": [0.444, 0, 0.889, 0, 0.444, -16.92, 0.889, 15.17]}, {"time": 1.3333, "y": 15.17, "curve": [1.778, 0, 2.222, 0, 1.778, 15.17, 2.222, -16.92]}, {"time": 2.6667, "y": -16.92, "curve": [3.111, 0, 3.556, 0, 3.111, -16.92, 3.556, 15.17]}, {"time": 4, "y": 15.17, "curve": [4.444, 0, 4.889, 0, 4.444, 15.17, 4.889, -16.92]}, {"time": 5.3333, "y": -16.92, "curve": [5.667, 0, 6, 0, 5.667, -16.92, 6, 15.17]}, {"time": 6.3333, "y": 15.17, "curve": [6.778, 0, 7.222, 0, 6.778, 15.17, 7.222, 3.14]}, {"time": 7.6667, "y": 3.14, "curve": [8.111, 0, 8.556, 0, 8.111, 3.14, 8.556, 15.17]}, {"time": 9, "y": 15.17, "curve": [9.444, 0, 9.889, 0, 9.444, 15.17, 9.889, -16.92]}, {"time": 10.3333, "y": -16.92, "curve": [10.778, 0, 11.222, 0, 10.778, -16.92, 11.222, 15.17]}, {"time": 11.6667, "y": 15.17, "curve": [12.111, 0, 12.556, 0, 12.111, 15.17, 12.556, -16.92]}, {"time": 13, "y": -16.92}]}, "body": {"rotate": [{"value": 2.67, "curve": [0.444, 2.67, 0.889, -2.92]}, {"time": 1.3333, "value": -2.92, "curve": [1.778, -2.92, 2.222, 2.67]}, {"time": 2.6667, "value": 2.67, "curve": [3.111, 2.67, 3.556, -2.92]}, {"time": 4, "value": -2.92, "curve": [4.444, -2.92, 4.889, 2.67]}, {"time": 5.3333, "value": 2.67, "curve": [5.667, 2.67, 6, -2.92]}, {"time": 6.3333, "value": -2.92, "curve": [6.778, -2.92, 7.222, -0.82]}, {"time": 7.6667, "value": -0.82, "curve": [8.111, -0.82, 8.556, -2.92]}, {"time": 9, "value": -2.92, "curve": [9.444, -2.92, 9.889, 2.67]}, {"time": 10.3333, "value": 2.67, "curve": [10.778, 2.67, 11.222, -2.92]}, {"time": 11.6667, "value": -2.92, "curve": [12.111, -2.92, 12.556, 2.67]}, {"time": 13, "value": 2.67}], "translate": [{"y": -8.35, "curve": [0.057, 0, 0.112, 0, 0.057, -8.78, 0.112, -9.11]}, {"time": 0.1667, "y": -9.11, "curve": [0.611, 0, 1.056, 0, 0.611, -9.11, 1.056, 7.19]}, {"time": 1.5, "y": 7.19, "curve": [1.944, 0, 2.389, 0, 1.944, 7.19, 2.389, -9.11]}, {"time": 2.8333, "y": -9.11, "curve": [3.278, 0, 3.722, 0, 3.278, -9.11, 3.722, 7.19]}, {"time": 4.1667, "y": 7.19, "curve": [4.611, 0, 5.056, 0, 4.611, 7.19, 5.056, -9.11]}, {"time": 5.5, "y": -9.11, "curve": [5.833, 0, 6.167, 0, 5.833, -9.11, 6.167, 7.19]}, {"time": 6.5, "y": 7.19, "curve": [6.944, 0, 7.389, 0, 6.944, 7.19, 7.389, 1.08]}, {"time": 7.8333, "y": 1.08, "curve": [8.278, 0, 8.722, 0, 8.278, 1.08, 8.722, 7.19]}, {"time": 9.1667, "y": 7.19, "curve": [9.611, 0, 10.056, 0, 9.611, 7.19, 10.056, -9.11]}, {"time": 10.5, "y": -9.11, "curve": [10.944, 0, 11.389, 0, 10.944, -9.11, 11.389, 7.19]}, {"time": 11.8333, "y": 7.19, "curve": [12.223, 0, 12.613, 0, 12.223, 7.19, 12.613, -5.26]}, {"time": 13, "y": -8.35}], "scale": [{"y": 1.026, "curve": [0.057, 1, 0.112, 1, 0.057, 1.028, 0.112, 1.029]}, {"time": 0.1667, "y": 1.029, "curve": [0.611, 1, 1.056, 1, 0.611, 1.029, 1.056, 0.967]}, {"time": 1.5, "y": 0.967, "curve": [1.944, 1, 2.389, 1, 1.944, 0.967, 2.389, 1.029]}, {"time": 2.8333, "y": 1.029, "curve": [3.278, 1, 3.722, 1, 3.278, 1.029, 3.722, 0.967]}, {"time": 4.1667, "y": 0.967, "curve": [4.611, 1, 5.056, 1, 4.611, 0.967, 5.056, 1.029]}, {"time": 5.5, "y": 1.029, "curve": [5.833, 1, 6.167, 1, 5.833, 1.029, 6.167, 0.967]}, {"time": 6.5, "y": 0.967, "curve": [6.944, 1, 7.389, 1, 6.944, 0.967, 7.389, 0.99]}, {"time": 7.8333, "y": 0.99, "curve": [8.278, 1, 8.722, 1, 8.278, 0.99, 8.722, 0.967]}, {"time": 9.1667, "y": 0.967, "curve": [9.611, 1, 10.056, 1, 9.611, 0.967, 10.056, 1.029]}, {"time": 10.5, "y": 1.029, "curve": [10.944, 1, 11.389, 1, 10.944, 1.029, 11.389, 0.967]}, {"time": 11.8333, "y": 0.967, "curve": [12.223, 1, 12.613, 1, 12.223, 0.967, 12.613, 1.014]}, {"time": 13, "y": 1.026}]}, "body2": {"rotate": [{"value": -2.3, "curve": [0.444, -2.3, 0.889, 1.92]}, {"time": 1.3333, "value": 1.92, "curve": [1.778, 1.92, 2.222, -2.3]}, {"time": 2.6667, "value": -2.3, "curve": [3.111, -2.3, 3.556, 1.92]}, {"time": 4, "value": 1.92, "curve": [4.444, 1.92, 4.889, -2.3]}, {"time": 5.3333, "value": -2.3, "curve": [5.667, -2.3, 6, 1.92]}, {"time": 6.3333, "value": 1.92, "curve": [6.778, 1.92, 7.222, 0.34]}, {"time": 7.6667, "value": 0.34, "curve": [8.111, 0.34, 8.556, 1.92]}, {"time": 9, "value": 1.92, "curve": [9.444, 1.92, 9.889, -2.3]}, {"time": 10.3333, "value": -2.3, "curve": [10.778, -2.3, 11.222, 1.92]}, {"time": 11.6667, "value": 1.92, "curve": [12.111, 1.92, 12.556, -2.3]}, {"time": 13, "value": -2.3}], "translate": [{"x": -6.99, "curve": [0.114, -8.67, 0.224, -9.92, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -9.92, "curve": [0.778, -9.92, 1.222, 8.39, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 8.39, "curve": [2.111, 8.39, 2.556, -9.92, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -9.92, "curve": [3.444, -9.92, 3.889, 8.39, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 8.39, "curve": [4.778, 8.39, 5.222, -9.92, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -9.92, "curve": [6, -9.92, 6.333, 8.39, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 8.39, "curve": [7.111, 8.39, 7.556, 1.52, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 1.52, "curve": [8.444, 1.52, 8.889, 8.39, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 8.39, "curve": [9.778, 8.39, 10.222, -9.92, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -9.92, "curve": [11.111, -9.92, 11.556, 8.39, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 8.39, "curve": [12.335, 8.39, 12.67, -1.87, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -6.99}], "scale": [{"x": 1.005, "y": 1.005, "curve": [0.114, 1.002, 0.224, 1, 0.114, 1.002, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.029, 0.778, 1, 1.222, 1.029]}, {"time": 1.6667, "x": 1.029, "y": 1.029, "curve": [2.111, 1.029, 2.556, 1, 2.111, 1.029, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.029, 3.444, 1, 3.889, 1.029]}, {"time": 4.3333, "x": 1.029, "y": 1.029, "curve": [4.778, 1.029, 5.222, 1, 4.778, 1.029, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.029, 6, 1, 6.333, 1.029]}, {"time": 6.6667, "x": 1.029, "y": 1.029, "curve": [7.111, 1.029, 7.556, 1.018, 7.111, 1.029, 7.556, 1.018]}, {"time": 8, "x": 1.018, "y": 1.018, "curve": [8.444, 1.018, 8.889, 1.029, 8.444, 1.018, 8.889, 1.029]}, {"time": 9.3333, "x": 1.029, "y": 1.029, "curve": [9.778, 1.029, 10.222, 1, 9.778, 1.029, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.029, 11.111, 1, 11.556, 1.029]}, {"time": 12, "x": 1.029, "y": 1.029, "curve": [12.335, 1.029, 12.67, 1.013, 12.335, 1.029, 12.67, 1.013]}, {"time": 13, "x": 1.005, "y": 1.005}]}, "neck": {"rotate": [{"value": -0.69, "curve": [0.168, -1.35, 0.334, -1.9]}, {"time": 0.5, "value": -1.9, "curve": [0.944, -1.9, 1.389, 1.88]}, {"time": 1.8333, "value": 1.88, "curve": [2.278, 1.88, 2.722, -1.9]}, {"time": 3.1667, "value": -1.9, "curve": [3.611, -1.9, 4.056, 1.88]}, {"time": 4.5, "value": 1.88, "curve": [4.944, 1.88, 5.389, -1.9]}, {"time": 5.8333, "value": -1.9, "curve": [6.167, -1.9, 6.5, 9.04]}, {"time": 6.8333, "value": 9.04, "curve": [7.278, 9.04, 7.722, 4.94]}, {"time": 8.1667, "value": 4.94, "curve": [8.611, 4.94, 9.056, 9.04]}, {"time": 9.5, "value": 9.04, "curve": [9.944, 9.04, 10.389, -1.9]}, {"time": 10.8333, "value": -1.9, "curve": [11.278, -1.9, 11.722, 1.88]}, {"time": 12.1667, "value": 1.88, "curve": [12.445, 1.88, 12.724, 0.41]}, {"time": 13, "value": -0.69}]}, "head": {"rotate": [{"value": -0.01, "curve": [0.225, -0.94, 0.446, -1.9]}, {"time": 0.6667, "value": -1.9, "curve": [1.111, -1.9, 1.556, 1.88]}, {"time": 2, "value": 1.88, "curve": [2.444, 1.88, 2.889, -1.9]}, {"time": 3.3333, "value": -1.9, "curve": [3.778, -1.9, 4.222, 1.88]}, {"time": 4.6667, "value": 1.88, "curve": [5.111, 1.88, 5.556, -1.9]}, {"time": 6, "value": -1.9, "curve": [6.333, -1.9, 6.667, 9.04]}, {"time": 7, "value": 9.04, "curve": [7.444, 9.04, 7.889, 4.94]}, {"time": 8.3333, "value": 4.94, "curve": [8.778, 4.94, 9.222, 9.04]}, {"time": 9.6667, "value": 9.04, "curve": [10.111, 9.04, 10.556, -1.9]}, {"time": 11, "value": -1.9, "curve": [11.444, -1.9, 11.889, 1.88]}, {"time": 12.3333, "value": 1.88, "curve": [12.557, 1.88, 12.781, 0.94]}, {"time": 13, "value": -0.01}]}, "tun": {"rotate": [{"value": 1.04, "curve": [0.444, 1.04, 0.889, -4.48]}, {"time": 1.3333, "value": -4.48, "curve": [1.778, -4.48, 2.222, 1.04]}, {"time": 2.6667, "value": 1.04, "curve": [3.111, 1.04, 3.556, -4.48]}, {"time": 4, "value": -4.48, "curve": [4.444, -4.48, 4.889, 1.04]}, {"time": 5.3333, "value": 1.04, "curve": [5.667, 1.04, 6, -4.48]}, {"time": 6.3333, "value": -4.48, "curve": [6.778, -4.48, 7.222, -2.41]}, {"time": 7.6667, "value": -2.41, "curve": [8.111, -2.41, 8.556, -4.48]}, {"time": 9, "value": -4.48, "curve": [9.444, -4.48, 9.889, 1.04]}, {"time": 10.3333, "value": 1.04, "curve": [10.778, 1.04, 11.222, -4.48]}, {"time": 11.6667, "value": -4.48, "curve": [12.111, -4.48, 12.556, 1.04]}, {"time": 13, "value": 1.04}]}, "leg_R3": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, 2.1]}, {"time": 7.6667, "value": 2.1, "curve": [8.111, 2.1, 8.556, 0]}, {"time": 9}]}, "leg_R4": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 1.22]}, {"time": 7.6667, "value": 1.22, "curve": [8.111, 1.22, 8.556, 0]}, {"time": 9}]}, "foot_R2": {"rotate": [{"value": -2.36, "curve": [0.444, -2.36, 0.889, 4.68]}, {"time": 1.3333, "value": 4.68, "curve": [1.778, 4.68, 2.222, -2.36]}, {"time": 2.6667, "value": -2.36, "curve": [3.111, -2.36, 3.556, 4.68]}, {"time": 4, "value": 4.68, "curve": [4.444, 4.68, 4.889, -2.36]}, {"time": 5.3333, "value": -2.36, "curve": [5.667, -2.36, 6, 4.68]}, {"time": 6.3333, "value": 4.68, "curve": [6.778, 4.68, 7.222, 2.04]}, {"time": 7.6667, "value": 2.04, "curve": [8.111, 2.04, 8.556, 4.68]}, {"time": 9, "value": 4.68, "curve": [9.444, 4.68, 9.889, -2.36]}, {"time": 10.3333, "value": -2.36, "curve": [10.778, -2.36, 11.222, 4.68]}, {"time": 11.6667, "value": 4.68, "curve": [12.111, 4.68, 12.556, -2.36]}, {"time": 13, "value": -2.36}]}, "leg_L3": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, 4.23]}, {"time": 7.6667, "value": 4.23, "curve": [8.111, 4.23, 8.556, 0]}, {"time": 9}]}, "leg_L4": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -1.83]}, {"time": 7.6667, "value": -1.83, "curve": [8.111, -1.83, 8.556, 0]}, {"time": 9}]}, "sh_R": {"translate": [{"x": -6.81, "curve": [0.114, -8.65, 0.224, -10.02, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -10.02, "curve": [0.778, -10.02, 1.222, 10.03, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 10.03, "curve": [2.111, 10.03, 2.556, -10.02, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -10.02, "curve": [3.444, -10.02, 3.889, 10.03, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 10.03, "curve": [4.778, 10.03, 5.222, -10.02, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -10.02, "curve": [6, -10.02, 6.333, 10.03, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 10.03, "curve": [7.111, 10.03, 7.556, 2.51, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 2.51, "curve": [8.444, 2.51, 8.889, 10.03, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 10.03, "curve": [9.778, 10.03, 10.222, -10.02, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -10.02, "curve": [11.111, -10.02, 11.556, 10.03, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 10.03, "curve": [12.335, 10.03, 12.67, -1.2, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -6.81}]}, "arm_R": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.47]}, {"time": 7.6667, "value": -0.47, "curve": [8.111, -0.47, 8.556, 0]}, {"time": 9}]}, "arm_R2": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.96]}, {"time": 7.6667, "value": -0.96, "curve": [8.111, -0.96, 8.556, 0]}, {"time": 9}]}, "sh_L": {"translate": [{"x": -1.81, "curve": [0.114, -2.29, 0.224, -2.65, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -2.65, "curve": [0.778, -2.65, 1.222, 2.56, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 2.56, "curve": [2.111, 2.56, 2.556, -2.65, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -2.65, "curve": [3.444, -2.65, 3.889, 2.56, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 2.56, "curve": [4.778, 2.56, 5.222, -2.65, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -2.65, "curve": [6, -2.65, 6.333, 2.56, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 2.56, "curve": [7.111, 2.56, 7.556, 0.61, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 0.61, "curve": [8.444, 0.61, 8.889, 2.56, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 2.56, "curve": [9.778, 2.56, 10.222, -2.65, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -2.65, "curve": [11.111, -2.65, 11.556, 2.56, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 2.56, "curve": [12.335, 2.56, 12.67, -0.35, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -1.81}]}, "arm_La": {"rotate": [{"value": 0.9, "curve": [0.168, 0.41, 0.334, 0]}, {"time": 0.5, "curve": [0.944, 0, 1.389, 2.83]}, {"time": 1.8333, "value": 2.83, "curve": [2.278, 2.83, 2.722, 0]}, {"time": 3.1667, "curve": [3.611, 0, 4.056, 2.83]}, {"time": 4.5, "value": 2.83, "curve": [4.944, 2.83, 5.389, 0]}, {"time": 5.8333, "curve": [6.167, 0, 6.5, 2.83]}, {"time": 6.8333, "value": 2.83, "curve": [7.278, 2.83, 7.722, 0]}, {"time": 8.1667, "curve": [8.611, 0, 9.056, 2.83]}, {"time": 9.5, "value": 2.83, "curve": [9.944, 2.83, 10.389, 0]}, {"time": 10.8333, "curve": [11.278, 0, 11.722, 2.83]}, {"time": 12.1667, "value": 2.83, "curve": [12.445, 2.83, 12.724, 1.73]}, {"time": 13, "value": 0.9}], "scale": [{"x": 0.983, "curve": [0.168, 0.972, 0.334, 0.963, 0.168, 1, 0.334, 1]}, {"time": 0.5, "x": 0.963, "curve": [0.944, 0.963, 1.389, 1.028, 0.944, 1, 1.389, 1]}, {"time": 1.8333, "x": 1.028, "curve": [2.278, 1.028, 2.722, 0.963, 2.278, 1, 2.722, 1]}, {"time": 3.1667, "x": 0.963, "curve": [3.611, 0.963, 4.056, 1.028, 3.611, 1, 4.056, 1]}, {"time": 4.5, "x": 1.028, "curve": [4.944, 1.028, 5.389, 0.963, 4.944, 1, 5.389, 1]}, {"time": 5.8333, "x": 0.963, "curve": [6.167, 0.963, 6.5, 1.028, 6.278, 1, 6.389, 1]}, {"time": 6.8333, "x": 1.028, "curve": [7.278, 1.028, 7.722, 0.963, 7.278, 1, 7.722, 1]}, {"time": 8.1667, "x": 0.963, "curve": [8.611, 0.963, 9.056, 1.028, 8.611, 1, 9.056, 1]}, {"time": 9.5, "x": 1.028, "curve": [9.944, 1.028, 10.389, 0.963, 9.944, 1, 10.389, 1]}, {"time": 10.8333, "x": 0.963, "curve": [11.278, 0.963, 11.722, 1.028, 11.278, 1, 11.722, 1]}, {"time": 12.1667, "x": 1.028, "curve": [12.445, 1.028, 12.724, 1.003, 12.445, 1, 12.724, 1]}, {"time": 13, "x": 0.983}]}, "arm_Lc": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 6]}, {"time": 7.6667, "value": 6, "curve": [8.111, 6, 8.556, 0]}, {"time": 9}]}, "eyebrow_R3": {"translate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 1.35, 6.278, 0, 6.389, 0]}, {"time": 6.5, "x": 1.35, "curve": "stepped"}, {"time": 9.6667, "x": 1.35, "curve": [9.778, 1.35, 9.889, 0, 9.778, 0, 9.889, 0]}, {"time": 10}]}, "eyebrow_R2": {"rotate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 2.69]}, {"time": 6.5, "value": 2.69, "curve": "stepped"}, {"time": 9.6667, "value": 2.69, "curve": [9.778, 2.69, 9.889, 0]}, {"time": 10}], "scale": [{"time": 6.1667, "curve": [6.278, 1, 6.389, 0.972, 6.278, 1, 6.389, 1]}, {"time": 6.5, "x": 0.972, "curve": "stepped"}, {"time": 9.6667, "x": 0.972, "curve": [9.778, 0.972, 9.889, 1, 9.778, 1, 9.889, 1]}, {"time": 10}]}, "eyebrow_R": {"rotate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 2.69]}, {"time": 6.5, "value": 2.69, "curve": "stepped"}, {"time": 9.6667, "value": 2.69, "curve": [9.778, 2.69, 9.889, 0]}, {"time": 10}]}, "eyebrow_L3": {"translate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 1.35, 6.278, 0, 6.389, 0]}, {"time": 6.5, "x": 1.35, "curve": "stepped"}, {"time": 9.6667, "x": 1.35, "curve": [9.778, 1.35, 9.889, 0, 9.778, 0, 9.889, 0]}, {"time": 10}]}, "eyebrow_L2": {"rotate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, -11.52]}, {"time": 6.5, "value": -11.52, "curve": "stepped"}, {"time": 9.6667, "value": -11.52, "curve": [9.778, -11.52, 9.889, 0]}, {"time": 10}], "scale": [{"time": 6.1667, "curve": [6.278, 1, 6.389, 0.941, 6.278, 1, 6.389, 1]}, {"time": 6.5, "x": 0.941, "curve": "stepped"}, {"time": 9.6667, "x": 0.941, "curve": [9.778, 0.941, 9.889, 1, 9.778, 1, 9.889, 1]}, {"time": 10}]}, "eyebrow_L": {"rotate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, -2.34]}, {"time": 6.5, "value": -2.34, "curve": "stepped"}, {"time": 9.6667, "value": -2.34, "curve": [9.778, -2.34, 9.889, 0]}, {"time": 10}]}, "eye_R": {"translate": [{"time": 1.8333, "curve": [1.867, 0, 1.9, -1.44, 1.867, 0, 1.9, -1.31]}, {"time": 1.9333, "x": -1.44, "y": -1.31, "curve": "stepped"}, {"time": 3.1667, "x": -1.44, "y": -1.31, "curve": [3.2, -1.44, 3.233, -1.14, 3.2, -1.31, 3.233, 0.78]}, {"time": 3.2667, "x": -1.14, "y": 0.78, "curve": "stepped"}, {"time": 3.9, "x": -1.14, "y": 0.78, "curve": [3.933, -1.14, 3.967, 0, 3.933, 0.78, 3.967, 0]}, {"time": 4, "curve": "stepped"}, {"time": 6.3333, "curve": [6.367, 0, 6.4, -2.27, 6.367, 0, 6.4, 3.37]}, {"time": 6.4333, "x": -2.27, "y": 3.37, "curve": "stepped"}, {"time": 7.2, "x": -2.27, "y": 3.37, "curve": [7.244, -2.27, 7.289, 0.31, 7.244, 3.37, 7.289, 3.37]}, {"time": 7.3333, "x": 0.31, "y": 3.37, "curve": "stepped"}, {"time": 7.6, "x": 0.31, "y": 3.37, "curve": [7.633, 0.31, 7.667, -1.04, 7.633, 3.37, 7.667, 2.01]}, {"time": 7.7, "x": -1.04, "y": 2.01, "curve": "stepped"}, {"time": 8.5667, "x": -1.04, "y": 2.01, "curve": [8.6, -1.04, 8.633, -2.27, 8.6, 2.01, 8.633, 3.37]}, {"time": 8.6667, "x": -2.27, "y": 3.37, "curve": "stepped"}, {"time": 9.8333, "x": -2.27, "y": 3.37, "curve": [9.867, -2.27, 9.9, 0, 9.867, 3.37, 9.9, 0]}, {"time": 9.9333}]}, "eye_L": {"translate": [{"time": 1.8333, "curve": [1.867, 0, 1.9, -0.98, 1.867, 0, 1.9, -0.8]}, {"time": 1.9333, "x": -0.98, "y": -0.8, "curve": "stepped"}, {"time": 3.1667, "x": -0.98, "y": -0.8, "curve": [3.2, -0.98, 3.233, -1.14, 3.2, -0.8, 3.233, 0.78]}, {"time": 3.2667, "x": -1.14, "y": 0.78, "curve": "stepped"}, {"time": 3.9, "x": -1.14, "y": 0.78, "curve": [3.933, -1.14, 3.967, 0, 3.933, 0.78, 3.967, 0]}, {"time": 4, "curve": "stepped"}, {"time": 6.3333, "curve": [6.367, 0, 6.4, -1.72, 6.367, 0, 6.4, 1.36]}, {"time": 6.4333, "x": -1.72, "y": 1.36, "curve": "stepped"}, {"time": 7.2, "x": -1.72, "y": 1.36, "curve": [7.244, -1.72, 7.289, 0.1, 7.244, 1.36, 7.289, 1.36]}, {"time": 7.3333, "x": 0.1, "y": 1.36, "curve": "stepped"}, {"time": 7.6, "x": 0.1, "y": 1.36, "curve": [7.633, 0.1, 7.667, -1.24, 7.633, 1.36, 7.667, 0]}, {"time": 7.7, "x": -1.24, "curve": "stepped"}, {"time": 8.5667, "x": -1.24, "curve": [8.6, -1.24, 8.633, -1.72, 8.6, 0, 8.633, 1.36]}, {"time": 8.6667, "x": -1.72, "y": 1.36, "curve": "stepped"}, {"time": 9.8333, "x": -1.72, "y": 1.36, "curve": [9.867, -1.72, 9.9, 0, 9.867, 1.36, 9.9, 0]}, {"time": 9.9333}]}, "hair_LF": {"rotate": [{"value": 1.8, "curve": [0.279, -0.96, 0.556, -4.65]}, {"time": 0.8333, "value": -4.65, "curve": [1.278, -4.65, 1.722, 4.81]}, {"time": 2.1667, "value": 4.81, "curve": [2.611, 4.81, 3.056, -4.65]}, {"time": 3.5, "value": -4.65, "curve": [3.944, -4.65, 4.389, 4.81]}, {"time": 4.8333, "value": 4.81, "curve": [5.278, 4.81, 5.722, -4.65]}, {"time": 6.1667, "value": -4.65, "curve": [6.5, -4.65, 6.833, 4.81]}, {"time": 7.1667, "value": 4.81, "curve": [7.611, 4.81, 8.056, 1.27]}, {"time": 8.5, "value": 1.27, "curve": [8.944, 1.27, 9.389, 4.81]}, {"time": 9.8333, "value": 4.81, "curve": [10.278, 4.81, 10.722, -4.65]}, {"time": 11.1667, "value": -4.65, "curve": [11.611, -4.65, 12.056, 4.81]}, {"time": 12.5, "value": 4.81, "curve": [12.667, 4.81, 12.835, 3.48]}, {"time": 13, "value": 1.8}]}, "bone57": {"rotate": [{"value": -0.22, "curve": "stepped"}, {"time": 6.3333, "value": -0.22, "curve": [6.778, -0.22, 7.222, 1.64]}, {"time": 7.6667, "value": 1.64, "curve": [8.111, 1.64, 8.556, -0.22]}, {"time": 9, "value": -0.22}]}, "bone58": {"rotate": [{"value": 0.69, "curve": "stepped"}, {"time": 6.3333, "value": 0.69, "curve": [6.778, 0.69, 7.222, 2.87]}, {"time": 7.6667, "value": 2.87, "curve": [8.111, 2.87, 8.556, 0.69]}, {"time": 9, "value": 0.69}]}, "foot_L": {"rotate": [{"value": 4.94, "curve": [0.444, 4.94, 0.889, -4.71]}, {"time": 1.3333, "value": -4.71, "curve": [1.778, -4.71, 2.222, 4.94]}, {"time": 2.6667, "value": 4.94, "curve": [3.111, 4.94, 3.556, -4.71]}, {"time": 4, "value": -4.71, "curve": [4.444, -4.71, 4.889, 4.94]}, {"time": 5.3333, "value": 4.94, "curve": [5.667, 4.94, 6, -4.71]}, {"time": 6.3333, "value": -4.71, "curve": [6.778, -4.71, 7.222, -1.09]}, {"time": 7.6667, "value": -1.09, "curve": [8.111, -1.09, 8.556, -4.71]}, {"time": 9, "value": -4.71, "curve": [9.444, -4.71, 9.889, 4.94]}, {"time": 10.3333, "value": 4.94, "curve": [10.778, 4.94, 11.222, -4.71]}, {"time": 11.6667, "value": -4.71, "curve": [12.111, -4.71, 12.556, 4.94]}, {"time": 13, "value": 4.94}]}, "foot_L2": {"rotate": [{"value": -3.12, "curve": [0.444, -3.12, 0.889, 2.18]}, {"time": 1.3333, "value": 2.18, "curve": [1.778, 2.18, 2.222, -3.12]}, {"time": 2.6667, "value": -3.12, "curve": [3.111, -3.12, 3.556, 2.18]}, {"time": 4, "value": 2.18, "curve": [4.444, 2.18, 4.889, -3.12]}, {"time": 5.3333, "value": -3.12, "curve": [5.667, -3.12, 6, 2.18]}, {"time": 6.3333, "value": 2.18, "curve": [6.778, 2.18, 7.222, 0.19]}, {"time": 7.6667, "value": 0.19, "curve": [8.111, 0.19, 8.556, 2.18]}, {"time": 9, "value": 2.18, "curve": [9.444, 2.18, 9.889, -3.12]}, {"time": 10.3333, "value": -3.12, "curve": [10.778, -3.12, 11.222, 2.18]}, {"time": 11.6667, "value": 2.18, "curve": [12.111, 2.18, 12.556, -3.12]}, {"time": 13, "value": -3.12}]}, "bone59": {"rotate": [{"value": 0.28, "curve": "stepped"}, {"time": 6.3333, "value": 0.28, "curve": [6.778, 0.28, 7.222, 4.57]}, {"time": 7.6667, "value": 4.57, "curve": [8.111, 4.57, 8.556, 0.28]}, {"time": 9, "value": 0.28}]}, "bone60": {"rotate": [{"value": -0.49, "curve": "stepped"}, {"time": 6.3333, "value": -0.49, "curve": [6.778, -0.49, 7.222, -2.67]}, {"time": 7.6667, "value": -2.67, "curve": [8.111, -2.67, 8.556, -0.49]}, {"time": 9, "value": -0.49}]}, "RU_R": {"translate": [{"x": -14.21, "curve": [0.168, -28.56, 0.334, -40.33, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -40.33, "curve": [0.944, -40.33, 1.389, 41.82, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 41.82, "curve": [2.278, 41.82, 2.722, -40.33, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -40.33, "curve": [3.611, -40.33, 4.056, 41.82, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 41.82, "curve": [4.944, 41.82, 5.389, -40.33, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -40.33, "curve": [6.167, -40.33, 6.5, 41.82, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 41.82, "curve": [7.278, 41.82, 7.722, -40.33, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -40.33, "curve": [8.611, -40.33, 9.056, 41.82, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 41.82, "curve": [9.944, 41.82, 10.389, -40.33, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -40.33, "curve": [11.278, -40.33, 11.722, 41.82, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 41.82, "curve": [12.445, 41.82, 12.724, 9.88, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -14.21}], "scale": [{"x": 1.047, "y": 0.947, "curve": [0.167, 1.003, 0.333, 0.917, 0.167, 0.994, 0.333, 1.085]}, {"time": 0.5, "x": 0.917, "y": 1.085, "curve": [0.722, 0.917, 0.944, 1.072, 0.722, 1.085, 0.944, 0.921]}, {"time": 1.1667, "x": 1.072, "y": 0.921, "curve": [1.389, 1.072, 1.611, 0.917, 1.389, 0.921, 1.611, 1.085]}, {"time": 1.8333, "x": 0.917, "y": 1.085, "curve": [2.056, 0.917, 2.278, 1.072, 2.056, 1.085, 2.278, 0.921]}, {"time": 2.5, "x": 1.072, "y": 0.921, "curve": [2.722, 1.072, 2.944, 0.917, 2.722, 0.921, 2.944, 1.085]}, {"time": 3.1667, "x": 0.917, "y": 1.085, "curve": [3.389, 0.917, 3.611, 1.072, 3.389, 1.085, 3.611, 0.921]}, {"time": 3.8333, "x": 1.072, "y": 0.921, "curve": [4.056, 1.072, 4.278, 0.917, 4.056, 0.921, 4.278, 1.085]}, {"time": 4.5, "x": 0.917, "y": 1.085, "curve": [4.722, 0.917, 4.944, 1.072, 4.722, 1.085, 4.944, 0.921]}, {"time": 5.1667, "x": 1.072, "y": 0.921, "curve": [5.389, 1.072, 5.611, 0.917, 5.389, 0.921, 5.611, 1.085]}, {"time": 5.8333, "x": 0.917, "y": 1.085, "curve": [6, 0.917, 6.167, 1.072, 6, 1.085, 6.167, 0.921]}, {"time": 6.3333, "x": 1.072, "y": 0.921, "curve": [6.5, 1.072, 6.667, 0.917, 6.5, 0.921, 6.667, 1.085]}, {"time": 6.8333, "x": 0.917, "y": 1.085, "curve": [7.056, 0.917, 7.278, 1.072, 7.056, 1.085, 7.278, 0.921]}, {"time": 7.5, "x": 1.072, "y": 0.921, "curve": [7.722, 1.072, 7.944, 0.917, 7.722, 0.921, 7.944, 1.085]}, {"time": 8.1667, "x": 0.917, "y": 1.085, "curve": [8.389, 0.917, 8.611, 1.072, 8.389, 1.085, 8.611, 0.921]}, {"time": 8.8333, "x": 1.072, "y": 0.921, "curve": [9.056, 1.072, 9.278, 0.917, 9.056, 0.921, 9.278, 1.085]}, {"time": 9.5, "x": 0.917, "y": 1.085, "curve": [9.722, 0.917, 9.944, 1.072, 9.722, 1.085, 9.944, 0.921]}, {"time": 10.1667, "x": 1.072, "y": 0.921, "curve": [10.389, 1.072, 10.611, 0.917, 10.389, 0.921, 10.611, 1.085]}, {"time": 10.8333, "x": 0.917, "y": 1.085, "curve": [11.056, 0.917, 11.278, 1.072, 11.056, 1.085, 11.278, 0.921]}, {"time": 11.5, "x": 1.072, "y": 0.921, "curve": [11.722, 1.072, 11.944, 0.917, 11.722, 0.921, 11.944, 1.085]}, {"time": 12.1667, "x": 0.917, "y": 1.085, "curve": [12.389, 0.917, 12.611, 1.072, 12.389, 1.085, 12.611, 0.921]}, {"time": 12.8333, "x": 1.072, "y": 0.921, "curve": [12.889, 1.072, 12.944, 1.061, 12.889, 0.921, 12.944, 0.932]}, {"time": 13, "x": 1.047, "y": 0.947}]}, "RU_R2": {"translate": [{"x": -1.14, "curve": [0.225, -17.76, 0.446, -34.61, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -34.61, "curve": [1.111, -34.61, 1.556, 32.32, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 32.32, "curve": [2.444, 32.32, 2.889, -34.61, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -34.61, "curve": [3.778, -34.61, 4.222, 32.32, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 32.32, "curve": [5.111, 32.32, 5.556, -34.61, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -34.61, "curve": [6.333, -34.61, 6.667, 32.32, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 32.32, "curve": [7.444, 32.32, 7.889, -34.61, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -34.61, "curve": [8.778, -34.61, 9.222, 32.32, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 32.32, "curve": [10.111, 32.32, 10.556, -34.61, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -34.61, "curve": [11.444, -34.61, 11.889, 32.32, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 32.32, "curve": [12.557, 32.32, 12.781, 15.7, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -1.14}], "scale": [{"x": 1.072, "y": 0.921, "curve": [0.222, 1.072, 0.444, 0.917, 0.222, 0.921, 0.444, 1.085]}, {"time": 0.6667, "x": 0.917, "y": 1.085, "curve": [0.889, 0.917, 1.111, 1.072, 0.889, 1.085, 1.111, 0.921]}, {"time": 1.3333, "x": 1.072, "y": 0.921, "curve": [1.556, 1.072, 1.778, 0.917, 1.556, 0.921, 1.778, 1.085]}, {"time": 2, "x": 0.917, "y": 1.085, "curve": [2.222, 0.917, 2.444, 1.072, 2.222, 1.085, 2.444, 0.921]}, {"time": 2.6667, "x": 1.072, "y": 0.921, "curve": [2.889, 1.072, 3.111, 0.917, 2.889, 0.921, 3.111, 1.085]}, {"time": 3.3333, "x": 0.917, "y": 1.085, "curve": [3.556, 0.917, 3.778, 1.072, 3.556, 1.085, 3.778, 0.921]}, {"time": 4, "x": 1.072, "y": 0.921, "curve": [4.222, 1.072, 4.444, 0.917, 4.222, 0.921, 4.444, 1.085]}, {"time": 4.6667, "x": 0.917, "y": 1.085, "curve": [4.889, 0.917, 5.111, 1.072, 4.889, 1.085, 5.111, 0.921]}, {"time": 5.3333, "x": 1.072, "y": 0.921, "curve": [5.556, 1.072, 5.778, 0.917, 5.556, 0.921, 5.778, 1.085]}, {"time": 6, "x": 0.917, "y": 1.085, "curve": [6.167, 0.917, 6.333, 1.072, 6.167, 1.085, 6.333, 0.921]}, {"time": 6.5, "x": 1.072, "y": 0.921, "curve": [6.667, 1.072, 6.833, 0.917, 6.667, 0.921, 6.833, 1.085]}, {"time": 7, "x": 0.917, "y": 1.085, "curve": [7.222, 0.917, 7.444, 1.072, 7.222, 1.085, 7.444, 0.921]}, {"time": 7.6667, "x": 1.072, "y": 0.921, "curve": [7.889, 1.072, 8.111, 0.917, 7.889, 0.921, 8.111, 1.085]}, {"time": 8.3333, "x": 0.917, "y": 1.085, "curve": [8.556, 0.917, 8.778, 1.072, 8.556, 1.085, 8.778, 0.921]}, {"time": 9, "x": 1.072, "y": 0.921, "curve": [9.222, 1.072, 9.444, 0.917, 9.222, 0.921, 9.444, 1.085]}, {"time": 9.6667, "x": 0.917, "y": 1.085, "curve": [9.889, 0.917, 10.111, 1.072, 9.889, 1.085, 10.111, 0.921]}, {"time": 10.3333, "x": 1.072, "y": 0.921, "curve": [10.556, 1.072, 10.778, 0.917, 10.556, 0.921, 10.778, 1.085]}, {"time": 11, "x": 0.917, "y": 1.085, "curve": [11.222, 0.917, 11.444, 1.072, 11.222, 1.085, 11.444, 0.921]}, {"time": 11.6667, "x": 1.072, "y": 0.921, "curve": [11.889, 1.072, 12.111, 0.917, 11.889, 0.921, 12.111, 1.085]}, {"time": 12.3333, "x": 0.917, "y": 1.085, "curve": [12.556, 0.917, 12.778, 1.072, 12.556, 1.085, 12.778, 0.921]}, {"time": 13, "x": 1.072, "y": 0.921}]}, "RU_R3": {"translate": [{"x": 13.14, "curve": [0.279, -5.13, 0.556, -29.42, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -29.42, "curve": [1.278, -29.42, 1.722, 32.98, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 32.98, "curve": [2.611, 32.98, 3.056, -29.42, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -29.42, "curve": [3.944, -29.42, 4.389, 32.98, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 32.98, "curve": [5.278, 32.98, 5.722, -29.42, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -29.42, "curve": [6.5, -29.42, 6.833, 32.98, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 32.98, "curve": [7.611, 32.98, 8.056, -29.42, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -29.42, "curve": [8.944, -29.42, 9.389, 32.98, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 32.98, "curve": [10.278, 32.98, 10.722, -29.42, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -29.42, "curve": [11.611, -29.42, 12.056, 32.98, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 32.98, "curve": [12.667, 32.98, 12.835, 24.17, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 13.14}], "scale": [{"x": 1.047, "y": 0.947, "curve": [0.056, 1.061, 0.111, 1.072, 0.056, 0.932, 0.111, 0.921]}, {"time": 0.1667, "x": 1.072, "y": 0.921, "curve": [0.389, 1.072, 0.611, 0.917, 0.389, 0.921, 0.611, 1.085]}, {"time": 0.8333, "x": 0.917, "y": 1.085, "curve": [1.056, 0.917, 1.278, 1.072, 1.056, 1.085, 1.278, 0.921]}, {"time": 1.5, "x": 1.072, "y": 0.921, "curve": [1.722, 1.072, 1.944, 0.917, 1.722, 0.921, 1.944, 1.085]}, {"time": 2.1667, "x": 0.917, "y": 1.085, "curve": [2.389, 0.917, 2.611, 1.072, 2.389, 1.085, 2.611, 0.921]}, {"time": 2.8333, "x": 1.072, "y": 0.921, "curve": [3.056, 1.072, 3.278, 0.917, 3.056, 0.921, 3.278, 1.085]}, {"time": 3.5, "x": 0.917, "y": 1.085, "curve": [3.722, 0.917, 3.944, 1.072, 3.722, 1.085, 3.944, 0.921]}, {"time": 4.1667, "x": 1.072, "y": 0.921, "curve": [4.389, 1.072, 4.611, 0.917, 4.389, 0.921, 4.611, 1.085]}, {"time": 4.8333, "x": 0.917, "y": 1.085, "curve": [5.056, 0.917, 5.278, 1.072, 5.056, 1.085, 5.278, 0.921]}, {"time": 5.5, "x": 1.072, "y": 0.921, "curve": [5.722, 1.072, 5.944, 0.917, 5.722, 0.921, 5.944, 1.085]}, {"time": 6.1667, "x": 0.917, "y": 1.085, "curve": [6.333, 0.917, 6.5, 1.072, 6.333, 1.085, 6.5, 0.921]}, {"time": 6.6667, "x": 1.072, "y": 0.921, "curve": [6.833, 1.072, 7, 0.917, 6.833, 0.921, 7, 1.085]}, {"time": 7.1667, "x": 0.917, "y": 1.085, "curve": [7.389, 0.917, 7.611, 1.072, 7.389, 1.085, 7.611, 0.921]}, {"time": 7.8333, "x": 1.072, "y": 0.921, "curve": [8.056, 1.072, 8.278, 0.917, 8.056, 0.921, 8.278, 1.085]}, {"time": 8.5, "x": 0.917, "y": 1.085, "curve": [8.722, 0.917, 8.944, 1.072, 8.722, 1.085, 8.944, 0.921]}, {"time": 9.1667, "x": 1.072, "y": 0.921, "curve": [9.389, 1.072, 9.611, 0.917, 9.389, 0.921, 9.611, 1.085]}, {"time": 9.8333, "x": 0.917, "y": 1.085, "curve": [10.056, 0.917, 10.278, 1.072, 10.056, 1.085, 10.278, 0.921]}, {"time": 10.5, "x": 1.072, "y": 0.921, "curve": [10.722, 1.072, 10.944, 0.917, 10.722, 0.921, 10.944, 1.085]}, {"time": 11.1667, "x": 0.917, "y": 1.085, "curve": [11.389, 0.917, 11.611, 1.072, 11.389, 1.085, 11.611, 0.921]}, {"time": 11.8333, "x": 1.072, "y": 0.921, "curve": [12.056, 1.072, 12.278, 0.917, 12.056, 0.921, 12.278, 1.085]}, {"time": 12.5, "x": 0.917, "y": 1.085, "curve": [12.667, 0.917, 12.833, 1.003, 12.667, 1.085, 12.833, 0.994]}, {"time": 13, "x": 1.047, "y": 0.947}]}, "RU_L": {"translate": [{"x": -10.52, "curve": [0.168, -23.93, 0.334, -34.93, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -34.93, "curve": [0.944, -34.93, 1.389, 41.82, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 41.82, "curve": [2.278, 41.82, 2.722, -34.93, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -34.93, "curve": [3.611, -34.93, 4.056, 41.82, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 41.82, "curve": [4.944, 41.82, 5.389, -34.93, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -34.93, "curve": [6.167, -34.93, 6.5, 41.82, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 41.82, "curve": [7.278, 41.82, 7.722, -34.93, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -34.93, "curve": [8.611, -34.93, 9.056, 41.82, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 41.82, "curve": [9.944, 41.82, 10.389, -34.93, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -34.93, "curve": [11.278, -34.93, 11.722, 41.82, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 41.82, "curve": [12.445, 41.82, 12.724, 11.98, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -10.52}], "scale": [{"x": 1.047, "y": 0.947, "curve": [0.167, 1.003, 0.333, 0.917, 0.167, 0.994, 0.333, 1.085]}, {"time": 0.5, "x": 0.917, "y": 1.085, "curve": [0.722, 0.917, 0.944, 1.072, 0.722, 1.085, 0.944, 0.921]}, {"time": 1.1667, "x": 1.072, "y": 0.921, "curve": [1.389, 1.072, 1.611, 0.917, 1.389, 0.921, 1.611, 1.085]}, {"time": 1.8333, "x": 0.917, "y": 1.085, "curve": [2.056, 0.917, 2.278, 1.072, 2.056, 1.085, 2.278, 0.921]}, {"time": 2.5, "x": 1.072, "y": 0.921, "curve": [2.722, 1.072, 2.944, 0.917, 2.722, 0.921, 2.944, 1.085]}, {"time": 3.1667, "x": 0.917, "y": 1.085, "curve": [3.389, 0.917, 3.611, 1.072, 3.389, 1.085, 3.611, 0.921]}, {"time": 3.8333, "x": 1.072, "y": 0.921, "curve": [4.056, 1.072, 4.278, 0.917, 4.056, 0.921, 4.278, 1.085]}, {"time": 4.5, "x": 0.917, "y": 1.085, "curve": [4.722, 0.917, 4.944, 1.072, 4.722, 1.085, 4.944, 0.921]}, {"time": 5.1667, "x": 1.072, "y": 0.921, "curve": [5.389, 1.072, 5.611, 0.917, 5.389, 0.921, 5.611, 1.085]}, {"time": 5.8333, "x": 0.917, "y": 1.085, "curve": [6, 0.917, 6.167, 1.072, 6, 1.085, 6.167, 0.921]}, {"time": 6.3333, "x": 1.072, "y": 0.921, "curve": [6.5, 1.072, 6.667, 0.917, 6.5, 0.921, 6.667, 1.085]}, {"time": 6.8333, "x": 0.917, "y": 1.085, "curve": [7.056, 0.917, 7.278, 1.072, 7.056, 1.085, 7.278, 0.921]}, {"time": 7.5, "x": 1.072, "y": 0.921, "curve": [7.722, 1.072, 7.944, 0.917, 7.722, 0.921, 7.944, 1.085]}, {"time": 8.1667, "x": 0.917, "y": 1.085, "curve": [8.389, 0.917, 8.611, 1.072, 8.389, 1.085, 8.611, 0.921]}, {"time": 8.8333, "x": 1.072, "y": 0.921, "curve": [9.056, 1.072, 9.278, 0.917, 9.056, 0.921, 9.278, 1.085]}, {"time": 9.5, "x": 0.917, "y": 1.085, "curve": [9.722, 0.917, 9.944, 1.072, 9.722, 1.085, 9.944, 0.921]}, {"time": 10.1667, "x": 1.072, "y": 0.921, "curve": [10.389, 1.072, 10.611, 0.917, 10.389, 0.921, 10.611, 1.085]}, {"time": 10.8333, "x": 0.917, "y": 1.085, "curve": [11.056, 0.917, 11.278, 1.072, 11.056, 1.085, 11.278, 0.921]}, {"time": 11.5, "x": 1.072, "y": 0.921, "curve": [11.722, 1.072, 11.944, 0.917, 11.722, 0.921, 11.944, 1.085]}, {"time": 12.1667, "x": 0.917, "y": 1.085, "curve": [12.389, 0.917, 12.611, 1.072, 12.389, 1.085, 12.611, 0.921]}, {"time": 12.8333, "x": 1.072, "y": 0.921, "curve": [12.889, 1.072, 12.944, 1.061, 12.889, 0.921, 12.944, 0.932]}, {"time": 13, "x": 1.047, "y": 0.947}]}, "RU_L2": {"translate": [{"x": 0.28, "curve": [0.225, -13.51, 0.446, -27.49, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -27.49, "curve": [1.111, -27.49, 1.556, 28.05, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 28.05, "curve": [2.444, 28.05, 2.889, -27.49, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -27.49, "curve": [3.778, -27.49, 4.222, 28.05, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 28.05, "curve": [5.111, 28.05, 5.556, -27.49, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -27.49, "curve": [6.333, -27.49, 6.667, 28.05, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 28.05, "curve": [7.444, 28.05, 7.889, -27.49, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -27.49, "curve": [8.778, -27.49, 9.222, 28.05, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 28.05, "curve": [10.111, 28.05, 10.556, -27.49, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -27.49, "curve": [11.444, -27.49, 11.889, 28.05, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 28.05, "curve": [12.557, 28.05, 12.781, 14.26, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 0.28}], "scale": [{"x": 1.072, "y": 0.921, "curve": [0.222, 1.072, 0.444, 0.917, 0.222, 0.921, 0.444, 1.085]}, {"time": 0.6667, "x": 0.917, "y": 1.085, "curve": [0.889, 0.917, 1.111, 1.072, 0.889, 1.085, 1.111, 0.921]}, {"time": 1.3333, "x": 1.072, "y": 0.921, "curve": [1.556, 1.072, 1.778, 0.917, 1.556, 0.921, 1.778, 1.085]}, {"time": 2, "x": 0.917, "y": 1.085, "curve": [2.222, 0.917, 2.444, 1.072, 2.222, 1.085, 2.444, 0.921]}, {"time": 2.6667, "x": 1.072, "y": 0.921, "curve": [2.889, 1.072, 3.111, 0.917, 2.889, 0.921, 3.111, 1.085]}, {"time": 3.3333, "x": 0.917, "y": 1.085, "curve": [3.556, 0.917, 3.778, 1.072, 3.556, 1.085, 3.778, 0.921]}, {"time": 4, "x": 1.072, "y": 0.921, "curve": [4.222, 1.072, 4.444, 0.917, 4.222, 0.921, 4.444, 1.085]}, {"time": 4.6667, "x": 0.917, "y": 1.085, "curve": [4.889, 0.917, 5.111, 1.072, 4.889, 1.085, 5.111, 0.921]}, {"time": 5.3333, "x": 1.072, "y": 0.921, "curve": [5.556, 1.072, 5.778, 0.917, 5.556, 0.921, 5.778, 1.085]}, {"time": 6, "x": 0.917, "y": 1.085, "curve": [6.167, 0.917, 6.333, 1.072, 6.167, 1.085, 6.333, 0.921]}, {"time": 6.5, "x": 1.072, "y": 0.921, "curve": [6.667, 1.072, 6.833, 0.917, 6.667, 0.921, 6.833, 1.085]}, {"time": 7, "x": 0.917, "y": 1.085, "curve": [7.222, 0.917, 7.444, 1.072, 7.222, 1.085, 7.444, 0.921]}, {"time": 7.6667, "x": 1.072, "y": 0.921, "curve": [7.889, 1.072, 8.111, 0.917, 7.889, 0.921, 8.111, 1.085]}, {"time": 8.3333, "x": 0.917, "y": 1.085, "curve": [8.556, 0.917, 8.778, 1.072, 8.556, 1.085, 8.778, 0.921]}, {"time": 9, "x": 1.072, "y": 0.921, "curve": [9.222, 1.072, 9.444, 0.917, 9.222, 0.921, 9.444, 1.085]}, {"time": 9.6667, "x": 0.917, "y": 1.085, "curve": [9.889, 0.917, 10.111, 1.072, 9.889, 1.085, 10.111, 0.921]}, {"time": 10.3333, "x": 1.072, "y": 0.921, "curve": [10.556, 1.072, 10.778, 0.917, 10.556, 0.921, 10.778, 1.085]}, {"time": 11, "x": 0.917, "y": 1.085, "curve": [11.222, 0.917, 11.444, 1.072, 11.222, 1.085, 11.444, 0.921]}, {"time": 11.6667, "x": 1.072, "y": 0.921, "curve": [11.889, 1.072, 12.111, 0.917, 11.889, 0.921, 12.111, 1.085]}, {"time": 12.3333, "x": 0.917, "y": 1.085, "curve": [12.556, 0.917, 12.778, 1.072, 12.556, 1.085, 12.778, 0.921]}, {"time": 13, "x": 1.072, "y": 0.921}]}, "RU_L3": {"translate": [{"x": 8.15, "curve": [0.279, -5.43, 0.556, -23.48, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -23.48, "curve": [1.278, -23.48, 1.722, 22.9, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 22.9, "curve": [2.611, 22.9, 3.056, -23.48, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -23.48, "curve": [3.944, -23.48, 4.389, 22.9, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 22.9, "curve": [5.278, 22.9, 5.722, -23.48, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -23.48, "curve": [6.5, -23.48, 6.833, 22.9, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 22.9, "curve": [7.611, 22.9, 8.056, -23.48, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -23.48, "curve": [8.944, -23.48, 9.389, 22.9, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 22.9, "curve": [10.278, 22.9, 10.722, -23.48, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -23.48, "curve": [11.611, -23.48, 12.056, 22.9, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 22.9, "curve": [12.667, 22.9, 12.835, 16.36, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 8.15}], "scale": [{"x": 1.047, "y": 0.947, "curve": [0.056, 1.061, 0.111, 1.072, 0.056, 0.932, 0.111, 0.921]}, {"time": 0.1667, "x": 1.072, "y": 0.921, "curve": [0.389, 1.072, 0.611, 0.917, 0.389, 0.921, 0.611, 1.085]}, {"time": 0.8333, "x": 0.917, "y": 1.085, "curve": [1.056, 0.917, 1.278, 1.072, 1.056, 1.085, 1.278, 0.921]}, {"time": 1.5, "x": 1.072, "y": 0.921, "curve": [1.722, 1.072, 1.944, 0.917, 1.722, 0.921, 1.944, 1.085]}, {"time": 2.1667, "x": 0.917, "y": 1.085, "curve": [2.389, 0.917, 2.611, 1.072, 2.389, 1.085, 2.611, 0.921]}, {"time": 2.8333, "x": 1.072, "y": 0.921, "curve": [3.056, 1.072, 3.278, 0.917, 3.056, 0.921, 3.278, 1.085]}, {"time": 3.5, "x": 0.917, "y": 1.085, "curve": [3.722, 0.917, 3.944, 1.072, 3.722, 1.085, 3.944, 0.921]}, {"time": 4.1667, "x": 1.072, "y": 0.921, "curve": [4.389, 1.072, 4.611, 0.917, 4.389, 0.921, 4.611, 1.085]}, {"time": 4.8333, "x": 0.917, "y": 1.085, "curve": [5.056, 0.917, 5.278, 1.072, 5.056, 1.085, 5.278, 0.921]}, {"time": 5.5, "x": 1.072, "y": 0.921, "curve": [5.722, 1.072, 5.944, 0.917, 5.722, 0.921, 5.944, 1.085]}, {"time": 6.1667, "x": 0.917, "y": 1.085, "curve": [6.333, 0.917, 6.5, 1.072, 6.333, 1.085, 6.5, 0.921]}, {"time": 6.6667, "x": 1.072, "y": 0.921, "curve": [6.833, 1.072, 7, 0.917, 6.833, 0.921, 7, 1.085]}, {"time": 7.1667, "x": 0.917, "y": 1.085, "curve": [7.389, 0.917, 7.611, 1.072, 7.389, 1.085, 7.611, 0.921]}, {"time": 7.8333, "x": 1.072, "y": 0.921, "curve": [8.056, 1.072, 8.278, 0.917, 8.056, 0.921, 8.278, 1.085]}, {"time": 8.5, "x": 0.917, "y": 1.085, "curve": [8.722, 0.917, 8.944, 1.072, 8.722, 1.085, 8.944, 0.921]}, {"time": 9.1667, "x": 1.072, "y": 0.921, "curve": [9.389, 1.072, 9.611, 0.917, 9.389, 0.921, 9.611, 1.085]}, {"time": 9.8333, "x": 0.917, "y": 1.085, "curve": [10.056, 0.917, 10.278, 1.072, 10.056, 1.085, 10.278, 0.921]}, {"time": 10.5, "x": 1.072, "y": 0.921, "curve": [10.722, 1.072, 10.944, 0.917, 10.722, 0.921, 10.944, 1.085]}, {"time": 11.1667, "x": 0.917, "y": 1.085, "curve": [11.389, 0.917, 11.611, 1.072, 11.389, 1.085, 11.611, 0.921]}, {"time": 11.8333, "x": 1.072, "y": 0.921, "curve": [12.056, 1.072, 12.278, 0.917, 12.056, 0.921, 12.278, 1.085]}, {"time": 12.5, "x": 0.917, "y": 1.085, "curve": [12.667, 0.917, 12.833, 1.003, 12.667, 1.085, 12.833, 0.994]}, {"time": 13, "x": 1.047, "y": 0.947}]}, "headround3": {"translate": [{"x": 12.26, "curve": [0.279, -77.93, 0.556, -197.89, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -197.89, "curve": [1.278, -197.89, 1.722, 110.25, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 110.25, "curve": [2.611, 110.25, 3.056, -197.89, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -197.89, "curve": [3.944, -197.89, 4.389, 110.25, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 110.25, "curve": [5.278, 110.25, 5.722, -197.89, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -197.89, "curve": [6.5, -197.89, 6.833, -17.58, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": -17.58, "curve": [7.611, -17.58, 8.056, -58.43, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -58.43, "curve": [8.944, -58.43, 9.389, -17.58, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": -17.58, "curve": [10.278, -17.58, 10.722, -197.89, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -197.89, "curve": [11.611, -197.89, 12.056, 110.25, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 110.25, "curve": [12.667, 110.25, 12.835, 66.77, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 12.26}]}, "headround": {"translate": [{"y": 137.3, "curve": [0.336, 0, 0.668, 0, 0.336, 10.79, 0.668, -238.92]}, {"time": 1, "y": -238.92, "curve": [1.444, 0, 1.889, 0, 1.444, -238.92, 1.889, 208.96]}, {"time": 2.3333, "y": 208.96, "curve": [2.778, 0, 3.222, 0, 2.778, 208.96, 3.222, -238.92]}, {"time": 3.6667, "y": -238.92, "curve": [4.111, 0, 4.556, 0, 4.111, -238.92, 4.556, 208.96]}, {"time": 5, "y": 208.96, "curve": [5.444, 0, 5.889, 0, 5.444, 208.96, 5.889, -238.92]}, {"time": 6.3333, "y": -238.92, "curve": [6.667, 0, 7, 0, 6.667, -238.92, 7, 288.56]}, {"time": 7.3333, "y": 288.56, "curve": [7.778, 0, 8.222, 0, 7.778, 288.56, 8.222, 207.38]}, {"time": 8.6667, "y": 207.38, "curve": [9.111, 0, 9.556, 0, 9.111, 207.38, 9.556, 288.56]}, {"time": 10, "y": 288.56, "curve": [10.444, 0, 10.889, 0, 10.444, 288.56, 10.889, -238.92]}, {"time": 11.3333, "y": -238.92, "curve": [11.778, 0, 12.222, 0, 11.778, -238.92, 12.222, 208.96]}, {"time": 12.6667, "y": 208.96, "curve": [12.779, 0, 12.892, 0, 12.779, 208.96, 12.892, 180.22]}, {"time": 13, "y": 137.3}]}, "bodyround": {"translate": [{"y": -80.45, "curve": [0.168, 0, 0.334, 0, 0.168, -146.98, 0.334, -201.56]}, {"time": 0.5, "y": -201.56, "curve": [0.944, 0, 1.389, 0, 0.944, -201.56, 1.389, 179.31]}, {"time": 1.8333, "y": 179.31, "curve": [2.278, 0, 2.722, 0, 2.278, 179.31, 2.722, -201.56]}, {"time": 3.1667, "y": -201.56, "curve": [3.611, 0, 4.056, 0, 3.611, -201.56, 4.056, 179.31]}, {"time": 4.5, "y": 179.31, "curve": [4.944, 0, 5.389, 0, 4.944, 179.31, 5.389, -201.56]}, {"time": 5.8333, "y": -201.56, "curve": [6.167, 0, 6.5, 0, 6.167, -201.56, 6.5, 179.31]}, {"time": 6.8333, "y": 179.31, "curve": [7.278, 0, 7.722, 0, 7.278, 179.31, 7.722, 36.48]}, {"time": 8.1667, "y": 36.48, "curve": [8.611, 0, 9.056, 0, 8.611, 36.48, 9.056, 179.31]}, {"time": 9.5, "y": 179.31, "curve": [9.944, 0, 10.389, 0, 9.944, 179.31, 10.389, -201.56]}, {"time": 10.8333, "y": -201.56, "curve": [11.278, 0, 11.722, 0, 11.278, -201.56, 11.722, 179.31]}, {"time": 12.1667, "y": 179.31, "curve": [12.445, 0, 12.724, 0, 12.445, 179.31, 12.724, 31.24]}, {"time": 13, "y": -80.45}]}, "tunround": {"translate": [{"x": 172.47, "curve": [0.057, 182.99, 0.112, 191.01, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": 191.01, "curve": [0.611, 191.01, 1.056, -203.61, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": -203.61, "curve": [1.944, -203.61, 2.389, 191.01, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": 191.01, "curve": [3.278, 191.01, 3.722, -203.61, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": -203.61, "curve": [4.611, -203.61, 5.056, 191.01, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": 191.01, "curve": [5.833, 191.01, 6.167, -203.61, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": -203.61, "curve": [6.944, -203.61, 7.389, -55.62, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -55.62, "curve": [8.278, -55.62, 8.722, -203.61, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -203.61, "curve": [9.611, -203.61, 10.056, 191.01, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 191.01, "curve": [10.944, 191.01, 11.389, -203.61, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": -203.61, "curve": [12.223, -203.61, 12.613, 97.66, 12.223, 0, 12.613, 0]}, {"time": 13, "x": 172.47}]}, "sh_R2": {"rotate": [{"value": -0.15, "curve": "stepped"}, {"time": 6.3333, "value": -0.15, "curve": [6.778, -0.15, 7.222, -1.05]}, {"time": 7.6667, "value": -1.05, "curve": [8.111, -1.05, 8.556, -0.15]}, {"time": 9, "value": -0.15}]}, "sh_R3": {"rotate": [{"value": 0.31, "curve": "stepped"}, {"time": 6.3333, "value": 0.31, "curve": [6.778, 0.31, 7.222, -0.57]}, {"time": 7.6667, "value": -0.57, "curve": [8.111, -0.57, 8.556, 0.31]}, {"time": 9, "value": 0.31}]}, "body3": {"rotate": [{"value": -4.99, "curve": [0.114, -6.07, 0.224, -6.87]}, {"time": 0.3333, "value": -6.87, "curve": [0.778, -6.87, 1.222, 4.84]}, {"time": 1.6667, "value": 4.84, "curve": [2.111, 4.84, 2.556, -6.87]}, {"time": 3, "value": -6.87, "curve": [3.444, -6.87, 3.889, 4.84]}, {"time": 4.3333, "value": 4.84, "curve": [4.778, 4.84, 5.222, -6.87]}, {"time": 5.6667, "value": -6.87, "curve": [6, -6.87, 6.333, 4.84]}, {"time": 6.6667, "value": 4.84, "curve": [7.111, 4.84, 7.556, -6.87]}, {"time": 8, "value": -6.87, "curve": [8.444, -6.87, 8.889, 4.84]}, {"time": 9.3333, "value": 4.84, "curve": [9.778, 4.84, 10.222, -6.87]}, {"time": 10.6667, "value": -6.87, "curve": [11.111, -6.87, 11.556, 4.84]}, {"time": 12, "value": 4.84, "curve": [12.335, 4.84, 12.67, -1.72]}, {"time": 13, "value": -4.99}]}, "hair_RF": {"rotate": [{"value": -0.9, "curve": [0.279, 0.08, 0.556, 1.38]}, {"time": 0.8333, "value": 1.38, "curve": [1.278, 1.38, 1.722, -1.97]}, {"time": 2.1667, "value": -1.97, "curve": [2.611, -1.97, 3.056, 1.38]}, {"time": 3.5, "value": 1.38, "curve": [3.944, 1.38, 4.389, -1.97]}, {"time": 4.8333, "value": -1.97, "curve": [5.278, -1.97, 5.722, 1.38]}, {"time": 6.1667, "value": 1.38, "curve": [6.5, 1.38, 6.833, -1.97]}, {"time": 7.1667, "value": -1.97, "curve": [7.611, -1.97, 8.056, 1.38]}, {"time": 8.5, "value": 1.38, "curve": [8.944, 1.38, 9.389, -1.97]}, {"time": 9.8333, "value": -1.97, "curve": [10.278, -1.97, 10.722, 1.38]}, {"time": 11.1667, "value": 1.38, "curve": [11.611, 1.38, 12.056, -1.97]}, {"time": 12.5, "value": -1.97, "curve": [12.667, -1.97, 12.835, -1.49]}, {"time": 13, "value": -0.9}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.6667, "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "vertices": [-9.703, -1.19946, -9.70459, -1.1976, -7.24976, -1.18744, -7.25049, -1.18582, -5.41077, -0.82343, -5.41064, -0.8223, -4.15674, -0.75699, -4.15662, -0.75592, -2.2616, 0.10315, -2.26196, 0.10339, -0.57751, 0.01575, -0.57751, 0.01584, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.77124, -0.03735, -3.77051, -0.03693, -8.00586, -0.89294, -8.00562, -0.89227, -12.2323, -1.02493, -12.22925, -1.02353, -13.27576, -0.95721, -13.2749, -0.95596, -11.83276, -1.15576, -11.83496, -1.15405, -11.80273, -1.53342, -11.80432, -1.53168, 0, 0, 0, 0, -5.29932, -1.07025, -5.29724, -1.06952, -9.97119, -1.46954, -9.96985, -1.46838, -12.80298, -1.40472, -12.802, -1.40408, -13.37646, -1.07257, -13.37415, -1.07141, -12.71411, -0.58847, -12.71326, -0.58777, -5.19348, -0.46249, -5.19214, -0.46194, -2.17358, -0.31198, -2.17078, -0.3114, -0.66553, -0.05292, -0.66455, -0.0527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.8623, -0.26251, -9.86108, -0.26157], "curve": [1.889, 0, 1.944, 1]}, {"time": 2, "curve": "stepped"}, {"time": 6.1667, "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "vertices": [-9.703, -1.19946, -9.70459, -1.1976, -7.24976, -1.18744, -7.25049, -1.18582, -5.41077, -0.82343, -5.41064, -0.8223, -4.15674, -0.75699, -4.15662, -0.75592, -2.2616, 0.10315, -2.26196, 0.10339, -0.57751, 0.01575, -0.57751, 0.01584, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.77124, -0.03735, -3.77051, -0.03693, -8.00586, -0.89294, -8.00562, -0.89227, -12.2323, -1.02493, -12.22925, -1.02353, -13.27576, -0.95721, -13.2749, -0.95596, -11.83276, -1.15576, -11.83496, -1.15405, -11.80273, -1.53342, -11.80432, -1.53168, 0, 0, 0, 0, -5.29932, -1.07025, -5.29724, -1.06952, -9.97119, -1.46954, -9.96985, -1.46838, -12.80298, -1.40472, -12.802, -1.40408, -13.37646, -1.07257, -13.37415, -1.07141, -12.71411, -0.58847, -12.71326, -0.58777, -5.19348, -0.46249, -5.19214, -0.46194, -2.17358, -0.31198, -2.17078, -0.3114, -0.66553, -0.05292, -0.66455, -0.0527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.8623, -0.26251, -9.86108, -0.26157], "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "vertices": [-9.703, -1.19946, -9.70459, -1.1976, -7.24976, -1.18744, -7.25049, -1.18582, -5.41077, -0.82343, -5.41064, -0.8223, -4.15674, -0.75699, -4.15662, -0.75592, -2.2616, 0.10315, -2.26196, 0.10339, -0.57751, 0.01575, -0.57751, 0.01584, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.77124, -0.03735, -3.77051, -0.03693, -8.00586, -0.89294, -8.00562, -0.89227, -12.2323, -1.02493, -12.22925, -1.02353, -13.27576, -0.95721, -13.2749, -0.95596, -11.83276, -1.15576, -11.83496, -1.15405, -11.80273, -1.53342, -11.80432, -1.53168, 0, 0, 0, 0, -5.29932, -1.07025, -5.29724, -1.06952, -9.97119, -1.46954, -9.96985, -1.46838, -12.80298, -1.40472, -12.802, -1.40408, -13.37646, -1.07257, -13.37415, -1.07141, -12.71411, -0.58847, -12.71326, -0.58777, -5.19348, -0.46249, -5.19214, -0.46194, -2.17358, -0.31198, -2.17078, -0.3114, -0.66553, -0.05292, -0.66455, -0.0527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.8623, -0.26251, -9.86108, -0.26157], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.6667, "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "vertices": [-12.00732, 1.65247, -12.00769, 1.65369, -15.90662, 2.17102, -15.90881, 2.17166, -15.13672, 2.4476, -15.13843, 2.44821, -10.25244, 2.35233, -10.25415, 2.35364, -2.96741, 1.23193, -2.97021, 1.23236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.14246, -0.02448, -0.14258, -0.02448, -0.87988, -0.00427, -0.88062, -0.0043, -1.08691, -0.03986, -1.08777, -0.03983, -1.05664, -0.37955, -1.05664, -0.37903, -4.27258, 0.72833, -4.27393, 0.72903, -9.08008, -1.07382, -9.08423, -1.07336, 0, 0, 0, 0, -5.6687, 0.07718, -5.66956, 0.07751, -11.26147, 0.16794, -11.26233, 0.16827, -15.88049, 0.77335, -15.88232, 0.77356, -16.37793, 0.84949, -16.38013, 0.84976, -14.10852, 0.67828, -14.10986, 0.67871, -9.27356, 0.32507, -9.27393, 0.32578, -1.89221, 0.33585, -1.89392, 0.33588, -0.4762, 0.21176, -0.47681, 0.21191], "curve": [1.889, 0, 1.944, 1]}, {"time": 2, "curve": "stepped"}, {"time": 6.1667, "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "vertices": [-12.00732, 1.65247, -12.00769, 1.65369, -15.90662, 2.17102, -15.90881, 2.17166, -15.13672, 2.4476, -15.13843, 2.44821, -10.25244, 2.35233, -10.25415, 2.35364, -2.96741, 1.23193, -2.97021, 1.23236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.14246, -0.02448, -0.14258, -0.02448, -0.87988, -0.00427, -0.88062, -0.0043, -1.08691, -0.03986, -1.08777, -0.03983, -1.05664, -0.37955, -1.05664, -0.37903, -4.27258, 0.72833, -4.27393, 0.72903, -9.08008, -1.07382, -9.08423, -1.07336, 0, 0, 0, 0, -5.6687, 0.07718, -5.66956, 0.07751, -11.26147, 0.16794, -11.26233, 0.16827, -15.88049, 0.77335, -15.88232, 0.77356, -16.37793, 0.84949, -16.38013, 0.84976, -14.10852, 0.67828, -14.10986, 0.67871, -9.27356, 0.32507, -9.27393, 0.32578, -1.89221, 0.33585, -1.89392, 0.33588, -0.4762, 0.21176, -0.47681, 0.21191], "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "vertices": [-12.00732, 1.65247, -12.00769, 1.65369, -15.90662, 2.17102, -15.90881, 2.17166, -15.13672, 2.4476, -15.13843, 2.44821, -10.25244, 2.35233, -10.25415, 2.35364, -2.96741, 1.23193, -2.97021, 1.23236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.14246, -0.02448, -0.14258, -0.02448, -0.87988, -0.00427, -0.88062, -0.0043, -1.08691, -0.03986, -1.08777, -0.03983, -1.05664, -0.37955, -1.05664, -0.37903, -4.27258, 0.72833, -4.27393, 0.72903, -9.08008, -1.07382, -9.08423, -1.07336, 0, 0, 0, 0, -5.6687, 0.07718, -5.66956, 0.07751, -11.26147, 0.16794, -11.26233, 0.16827, -15.88049, 0.77335, -15.88232, 0.77356, -16.37793, 0.84949, -16.38013, 0.84976, -14.10852, 0.67828, -14.10986, 0.67871, -9.27356, 0.32507, -9.27393, 0.32578, -1.89221, 0.33585, -1.89392, 0.33588, -0.4762, 0.21176, -0.47681, 0.21191], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "head": {"head": {"deform": [{"time": 1.6667, "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "offset": 528, "vertices": [-8.72766, 1.00049, -8.72778, 1.00076, -13.98047, 1.61261, -13.979, 1.6138, -16.27258, 1.29477, -16.27185, 1.29626, -14.43445, 1.6105, -14.43359, 1.61172, -9.38452, 1.34161, -9.38525, 1.34235, -2.90564, -0.27179, -2.90503, -0.27158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34253, -0.77338, -5.34204, -0.77319, -10.47937, -1.07849, -10.47974, -1.07831, -13.31494, -1.34924, -13.31506, -1.349, -13.20093, -0.75308, -13.20105, -0.75256, -9.9209, -0.55048, -9.92065, -0.55002, -4.57898, -0.67944, -4.57886, -0.67902, -1.72839, -0.19, -1.72791, -0.18979, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67419, -0.11578, -0.67297, -0.11548], "curve": [1.889, 0, 1.944, 1]}, {"time": 2, "curve": "stepped"}, {"time": 6.1667, "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "offset": 528, "vertices": [-8.72766, 1.00049, -8.72778, 1.00076, -13.98047, 1.61261, -13.979, 1.6138, -16.27258, 1.29477, -16.27185, 1.29626, -14.43445, 1.6105, -14.43359, 1.61172, -9.38452, 1.34161, -9.38525, 1.34235, -2.90564, -0.27179, -2.90503, -0.27158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34253, -0.77338, -5.34204, -0.77319, -10.47937, -1.07849, -10.47974, -1.07831, -13.31494, -1.34924, -13.31506, -1.349, -13.20093, -0.75308, -13.20105, -0.75256, -9.9209, -0.55048, -9.92065, -0.55002, -4.57898, -0.67944, -4.57886, -0.67902, -1.72839, -0.19, -1.72791, -0.18979, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67419, -0.11578, -0.67297, -0.11548], "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "offset": 528, "vertices": [-8.72766, 1.00049, -8.72778, 1.00076, -13.98047, 1.61261, -13.979, 1.6138, -16.27258, 1.29477, -16.27185, 1.29626, -14.43445, 1.6105, -14.43359, 1.61172, -9.38452, 1.34161, -9.38525, 1.34235, -2.90564, -0.27179, -2.90503, -0.27158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34253, -0.77338, -5.34204, -0.77319, -10.47937, -1.07849, -10.47974, -1.07831, -13.31494, -1.34924, -13.31506, -1.349, -13.20093, -0.75308, -13.20105, -0.75256, -9.9209, -0.55048, -9.92065, -0.55002, -4.57898, -0.67944, -4.57886, -0.67902, -1.72839, -0.19, -1.72791, -0.18979, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67419, -0.11578, -0.67297, -0.11548], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "head2": {"head": {"deform": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 1]}, {"time": 6.5, "offset": 80, "vertices": [2.12964, 1.33801, 2.13098, 1.3381, 2.12964, 1.33801, 2.13098, 1.3381, 0.89587, 0.42654, 0.89636, 0.42658, -1.71521, -0.00858, -1.71533, -0.00859, -2.82947, -0.17564, -2.82935, -0.17564, -2.9375, 0.01193, -2.93738, 0.01198, -2.9375, 0.01193, -2.93738, 0.01198, -3.16516, 0.08696, -3.16504, 0.08704, -3.29285, 0.28426, -3.29285, 0.28426, -1.96326, 0.93996, -1.96216, 0.94006, 1.60706, 0.49402, 1.60864, 0.49401, 2.70081, 1.30261, 2.70215, 1.30268, 2.70081, 1.30261, 2.70215, 1.30268, 3.03149, 0.45227, 3.03223, 0.45229, 2.07874, 0.12967, 2.07874, 0.12968, 2.01819, 0.05576, 2.01831, 0.05575, 1.48108, 0.02226, 1.4812, 0.02227, 2.22632, 0.17647, 2.22644, 0.17648, 2.73108, 0.59914, 2.73145, 0.59914, 2.78467, 0.59579, 2.78528, 0.59579, 3.18872, 0.03445, 3.18958, 0.03453, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 1.61121, 0.47134, 1.61169, 0.47137, 0.74512, 0.0464, 0.74512, 0.04643, -1.31934, -0.08281, -1.31946, -0.08284, -2.6106, -0.08501, -2.6106, -0.08495, -3.04846, -0.064, -3.04822, -0.06393, -1.81372, -0.05745, -1.81372, -0.05742, 0.52148, -0.18106, 0.52161, -0.18103, 1.51782, -0.14861, 1.51843, -0.14858, 1.48767, -0.15047, 1.48828, -0.15044, 1.18774, -0.02328, 1.18811, -0.02325, 0.80542, 0.24109, 0.80579, 0.2411, 1.98682, -0.05083, 1.98743, -0.05085, 2.64844, -0.42595, 2.64929, -0.42586, 2.62134, -0.00095, 2.62219, -0.00084, 2.62134, -0.00095, 2.62219, -0.00084, 1.48206, 0.09242, 1.48206, 0.09241, 1.26709, 0.07903, 1.26709, 0.07903, 1.48206, 0.09242, 1.48206, 0.09241, 1.97327, 0.87712, 1.97327, 0.87715, 1.72363, -0.32346, 1.72412, -0.32349, 0.71753, 0.04478, 0.71753, 0.04479, 0.71753, 0.04478, 0.71753, 0.04479, 0.71753, 0.04478, 0.71753, 0.04479], "curve": "stepped"}, {"time": 9.6667, "offset": 80, "vertices": [2.12964, 1.33801, 2.13098, 1.3381, 2.12964, 1.33801, 2.13098, 1.3381, 0.89587, 0.42654, 0.89636, 0.42658, -1.71521, -0.00858, -1.71533, -0.00859, -2.82947, -0.17564, -2.82935, -0.17564, -2.9375, 0.01193, -2.93738, 0.01198, -2.9375, 0.01193, -2.93738, 0.01198, -3.16516, 0.08696, -3.16504, 0.08704, -3.29285, 0.28426, -3.29285, 0.28426, -1.96326, 0.93996, -1.96216, 0.94006, 1.60706, 0.49402, 1.60864, 0.49401, 2.70081, 1.30261, 2.70215, 1.30268, 2.70081, 1.30261, 2.70215, 1.30268, 3.03149, 0.45227, 3.03223, 0.45229, 2.07874, 0.12967, 2.07874, 0.12968, 2.01819, 0.05576, 2.01831, 0.05575, 1.48108, 0.02226, 1.4812, 0.02227, 2.22632, 0.17647, 2.22644, 0.17648, 2.73108, 0.59914, 2.73145, 0.59914, 2.78467, 0.59579, 2.78528, 0.59579, 3.18872, 0.03445, 3.18958, 0.03453, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 1.61121, 0.47134, 1.61169, 0.47137, 0.74512, 0.0464, 0.74512, 0.04643, -1.31934, -0.08281, -1.31946, -0.08284, -2.6106, -0.08501, -2.6106, -0.08495, -3.04846, -0.064, -3.04822, -0.06393, -1.81372, -0.05745, -1.81372, -0.05742, 0.52148, -0.18106, 0.52161, -0.18103, 1.51782, -0.14861, 1.51843, -0.14858, 1.48767, -0.15047, 1.48828, -0.15044, 1.18774, -0.02328, 1.18811, -0.02325, 0.80542, 0.24109, 0.80579, 0.2411, 1.98682, -0.05083, 1.98743, -0.05085, 2.64844, -0.42595, 2.64929, -0.42586, 2.62134, -0.00095, 2.62219, -0.00084, 2.62134, -0.00095, 2.62219, -0.00084, 1.48206, 0.09242, 1.48206, 0.09241, 1.26709, 0.07903, 1.26709, 0.07903, 1.48206, 0.09242, 1.48206, 0.09241, 1.97327, 0.87712, 1.97327, 0.87715, 1.72363, -0.32346, 1.72412, -0.32349, 0.71753, 0.04478, 0.71753, 0.04479, 0.71753, 0.04478, 0.71753, 0.04479, 0.71753, 0.04478, 0.71753, 0.04479], "curve": [9.778, 0, 9.889, 1]}, {"time": 10}]}}}}}, "touch": {"bones": {"bone": {"translate": [{"x": 12.47, "curve": [0.028, 12.47, 0.068, -22.56, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -22.56, "curve": [0.544, -22.56, 0.856, 12.47, 0.731, 0, 0.856, 0]}, {"time": 1.1667, "x": 12.47}]}, "bone2": {"translate": [{"y": -16.92, "curve": [0.078, 0, 0.156, 0, 0.028, -16.92, 0.068, 20.11]}, {"time": 0.2333, "y": 20.11, "curve": [0.731, 0, 0.856, 0, 0.544, 20.11, 0.856, -16.92]}, {"time": 1.1667, "y": -16.92}]}, "body": {"rotate": [{"value": 2.67, "curve": [0.028, 2.67, 0.068, -4.54]}, {"time": 0.2333, "value": -4.54, "curve": [0.544, -4.54, 0.856, 2.67]}, {"time": 1.1667, "value": 2.67}], "translate": [{"y": -8.35, "curve": [0.078, 0, 0.156, 0, 0.028, -8.35, 0.068, 13.24]}, {"time": 0.2333, "y": 13.24, "curve": [0.731, 0, 0.856, 0, 0.544, 13.24, 0.856, -8.35]}, {"time": 1.1667, "y": -8.35}], "scale": [{"y": 1.026, "curve": [0.078, 1, 0.156, 1, 0.028, 1.026, 0.068, 0.967]}, {"time": 0.2333, "y": 0.967, "curve": [0.731, 1, 0.856, 1, 0.544, 0.967, 0.856, 1.026]}, {"time": 1.1667, "y": 1.026}]}, "body2": {"rotate": [{"value": -2.3, "curve": [0.028, -2.3, 0.068, -1.66]}, {"time": 0.2333, "value": -1.66, "curve": [0.544, -1.66, 0.856, -2.3]}, {"time": 1.1667, "value": -2.3}], "translate": [{"x": -6.99, "curve": [0.028, -6.99, 0.068, 16.84, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 16.84, "curve": [0.544, 16.84, 0.856, -6.99, 0.731, 0, 0.856, 0]}, {"time": 1.1667, "x": -6.99}], "scale": [{"x": 1.005, "y": 1.005, "curve": [0.028, 1.005, 0.068, 1.029, 0.028, 1.005, 0.068, 1.029]}, {"time": 0.2333, "x": 1.029, "y": 1.029, "curve": [0.544, 1.029, 0.856, 1.005, 0.544, 1.029, 0.856, 1.005]}, {"time": 1.1667, "x": 1.005, "y": 1.005}]}, "neck": {"rotate": [{"value": -0.69, "curve": [0.032, -0.69, 0.077, -4.45]}, {"time": 0.2667, "value": -4.45, "curve": [0.567, -4.45, 0.867, -0.69]}, {"time": 1.1667, "value": -0.69}]}, "head": {"rotate": [{"value": -0.01, "curve": [0.032, -0.01, 0.077, -3.76]}, {"time": 0.2667, "value": -3.76, "curve": [0.567, -3.76, 0.867, -0.01]}, {"time": 1.1667, "value": -0.01}]}, "tun": {"rotate": [{"value": 1.04, "curve": [0.028, 1.04, 0.068, -6.1]}, {"time": 0.2333, "value": -6.1, "curve": [0.544, -6.1, 0.856, 1.04]}, {"time": 1.1667, "value": 1.04}]}, "leg_R4": {"rotate": [{}]}, "foot_R2": {"rotate": [{"value": -2.36, "curve": [0.028, -2.36, 0.068, 4.68]}, {"time": 0.2333, "value": 4.68, "curve": [0.544, 4.68, 0.856, -2.36]}, {"time": 1.1667, "value": -2.36}]}, "leg_L4": {"rotate": [{}]}, "sh_R": {"translate": [{"x": -6.81, "curve": [0.028, -6.81, 0.068, 14.85, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 14.85, "curve": [0.544, 14.85, 0.856, -6.81, 0.731, 0, 0.856, 0]}, {"time": 1.1667, "x": -6.81}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{}]}, "sh_L": {"translate": [{"x": -1.81, "curve": [0.028, -1.81, 0.068, 7.38, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 7.38, "curve": [0.544, 7.38, 0.856, -1.81, 0.731, 0, 0.856, 0]}, {"time": 1.1667, "x": -1.81}]}, "arm_La": {"rotate": [{"value": 0.9, "curve": [0.032, 0.9, 0.077, 2.7]}, {"time": 0.2667, "value": 2.7, "curve": [0.567, 2.7, 0.867, 0.9]}, {"time": 1.1667, "value": 0.9}], "scale": [{"x": 0.983, "curve": [0.032, 0.983, 0.077, 1.052, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 1.052, "curve": [0.567, 1.052, 0.867, 0.983, 0.567, 1, 0.867, 1]}, {"time": 1.1667, "x": 0.983}]}, "arm_Lc": {"rotate": [{}]}, "eyebrow_R3": {"translate": [{"curve": [0.035, 0, 0.086, 1.35, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 1.35, "curve": [0.567, 1.35, 0.867, 0, 1.407, 0, 0.867, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.035, 0, 0.086, 2.69]}, {"time": 0.2667, "value": 2.69, "curve": [0.567, 2.69, 0.867, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.035, 1, 0.086, 0.972, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.972, "curve": [0.567, 0.972, 0.867, 1, 1.407, 1, 0.867, 1]}, {"time": 1.1667}]}, "eyebrow_R": {"rotate": [{"curve": [0.035, 0, 0.086, 2.69]}, {"time": 0.2667, "value": 2.69, "curve": [0.567, 2.69, 0.867, 0]}, {"time": 1.1667}]}, "eyebrow_L3": {"translate": [{"curve": [0.035, 0, 0.086, 1.35, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 1.35, "curve": [0.567, 1.35, 0.867, 0, 1.407, 0, 0.867, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.035, 0, 0.086, -11.52]}, {"time": 0.2667, "value": -11.52, "curve": [0.567, -11.52, 0.867, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.035, 1, 0.086, 0.941, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.941, "curve": [0.567, 0.941, 0.867, 1, 1.407, 1, 0.867, 1]}, {"time": 1.1667}]}, "eyebrow_L": {"rotate": [{"curve": [0.035, 0, 0.086, -2.34]}, {"time": 0.2667, "value": -2.34, "curve": [0.567, -2.34, 0.867, 0]}, {"time": 1.1667}]}, "hair_LF": {"rotate": [{"value": 1.8}]}, "bone57": {"rotate": [{"value": -0.22}]}, "bone58": {"rotate": [{"value": 0.69}]}, "foot_L": {"rotate": [{"value": 4.94, "curve": [0.028, 4.94, 0.068, -4.71]}, {"time": 0.2333, "value": -4.71, "curve": [0.544, -4.71, 0.856, 4.94]}, {"time": 1.1667, "value": 4.94}]}, "foot_L2": {"rotate": [{"value": -3.12, "curve": [0.028, -3.12, 0.068, 2.18]}, {"time": 0.2333, "value": 2.18, "curve": [0.544, 2.18, 0.856, -3.12]}, {"time": 1.1667, "value": -3.12}]}, "bone59": {"rotate": [{"value": 0.28}]}, "bone60": {"rotate": [{"value": -0.49}]}, "RU_R": {"translate": [{"x": -14.21, "curve": [0.074, -28.56, 0.16, -40.33, 0.074, 0, 0.16, 0]}, {"time": 0.2333, "x": -40.33, "curve": [0.429, -40.33, 0.604, 41.82, 0.429, 0, 0.604, 0]}, {"time": 0.8, "x": 41.82, "curve": [0.923, 41.82, 1.045, 9.88, 0.923, 0, 1.045, 0]}, {"time": 1.1667, "x": -14.21}], "scale": [{"x": 1.047, "y": 0.947, "curve": [0.073, 1.003, 0.16, 0.917, 0.073, 0.994, 0.16, 1.085]}, {"time": 0.2333, "x": 0.917, "y": 1.085, "curve": [0.331, 0.917, 0.402, 1.072, 0.331, 1.085, 0.402, 0.921]}, {"time": 0.5, "x": 1.072, "y": 0.921, "curve": [0.598, 1.072, 0.702, 0.917, 0.598, 0.921, 0.702, 1.085]}, {"time": 0.8, "x": 0.917, "y": 1.085, "curve": [0.898, 0.917, 1.002, 1.072, 0.898, 1.085, 1.002, 0.921]}, {"time": 1.1, "x": 1.072, "y": 0.921, "curve": [1.125, 1.072, 1.143, 1.062, 1.125, 0.921, 1.143, 0.932]}, {"time": 1.1667, "x": 1.047, "y": 0.947}]}, "RU_R2": {"translate": [{"x": -1.14, "curve": [0.099, -17.76, 0.203, -34.61, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -34.61, "curve": [0.496, -34.61, 0.671, 32.32, 0.496, 0, 0.671, 0]}, {"time": 0.8667, "x": 32.32, "curve": [0.965, 32.32, 1.07, 15.7, 0.965, 0, 1.07, 0]}, {"time": 1.1667, "x": -1.14}], "scale": [{"x": 1.072, "y": 0.921, "curve": [0.098, 1.072, 0.202, 0.917, 0.098, 0.921, 0.202, 1.085]}, {"time": 0.3, "x": 0.917, "y": 1.085, "curve": [0.398, 0.917, 0.502, 1.072, 0.398, 1.085, 0.502, 0.921]}, {"time": 0.6, "x": 1.072, "y": 0.921, "curve": [0.698, 1.072, 0.769, 0.917, 0.698, 0.921, 0.769, 1.085]}, {"time": 0.8667, "x": 0.917, "y": 1.085, "curve": [0.965, 0.917, 1.069, 1.072, 0.965, 1.085, 1.069, 0.921]}, {"time": 1.1667, "x": 1.072, "y": 0.921}]}, "RU_R3": {"translate": [{"x": 13.14, "curve": [0.123, -5.13, 0.245, -29.42, 0.123, 0, 0.245, 0]}, {"time": 0.3667, "x": -29.42, "curve": [0.562, -29.42, 0.771, 32.98, 0.562, 0, 0.771, 0]}, {"time": 0.9667, "x": 32.98, "curve": [1.04, 32.98, 1.094, 24.17, 1.04, 0, 1.094, 0]}, {"time": 1.1667, "x": 13.14}], "scale": [{"x": 1.047, "y": 0.947, "curve": [0.024, 1.061, 0.042, 1.072, 0.024, 0.932, 0.042, 0.921]}, {"time": 0.0667, "x": 1.072, "y": 0.921, "curve": [0.165, 1.072, 0.269, 0.917, 0.165, 0.921, 0.269, 1.085]}, {"time": 0.3667, "x": 0.917, "y": 1.085, "curve": [0.465, 0.917, 0.569, 1.072, 0.465, 1.085, 0.569, 0.921]}, {"time": 0.6667, "x": 1.072, "y": 0.921, "curve": [0.765, 1.072, 0.869, 0.917, 0.765, 0.921, 0.869, 1.085]}, {"time": 0.9667, "x": 0.917, "y": 1.085, "curve": [1.04, 0.917, 1.094, 1.003, 1.04, 1.085, 1.094, 0.993]}, {"time": 1.1667, "x": 1.047, "y": 0.947}]}, "RU_L": {"translate": [{"x": -10.52, "curve": [0.074, -23.93, 0.16, -34.93, 0.074, 0, 0.16, 0]}, {"time": 0.2333, "x": -34.93, "curve": [0.429, -34.93, 0.604, 41.82, 0.429, 0, 0.604, 0]}, {"time": 0.8, "x": 41.82, "curve": [0.923, 41.82, 1.045, 11.98, 0.923, 0, 1.045, 0]}, {"time": 1.1667, "x": -10.52}], "scale": [{"x": 1.047, "y": 0.947, "curve": [0.073, 1.003, 0.16, 0.917, 0.073, 0.994, 0.16, 1.085]}, {"time": 0.2333, "x": 0.917, "y": 1.085, "curve": [0.331, 0.917, 0.402, 1.072, 0.331, 1.085, 0.402, 0.921]}, {"time": 0.5, "x": 1.072, "y": 0.921, "curve": [0.598, 1.072, 0.702, 0.917, 0.598, 0.921, 0.702, 1.085]}, {"time": 0.8, "x": 0.917, "y": 1.085, "curve": [0.898, 0.917, 1.002, 1.072, 0.898, 1.085, 1.002, 0.921]}, {"time": 1.1, "x": 1.072, "y": 0.921, "curve": [1.125, 1.072, 1.143, 1.062, 1.125, 0.921, 1.143, 0.932]}, {"time": 1.1667, "x": 1.047, "y": 0.947}]}, "RU_L2": {"translate": [{"x": 0.28, "curve": [0.099, -13.51, 0.203, -27.49, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -27.49, "curve": [0.496, -27.49, 0.671, 28.05, 0.496, 0, 0.671, 0]}, {"time": 0.8667, "x": 28.05, "curve": [0.965, 28.05, 1.07, 14.26, 0.965, 0, 1.07, 0]}, {"time": 1.1667, "x": 0.28}], "scale": [{"x": 1.072, "y": 0.921, "curve": [0.098, 1.072, 0.202, 0.917, 0.098, 0.921, 0.202, 1.085]}, {"time": 0.3, "x": 0.917, "y": 1.085, "curve": [0.398, 0.917, 0.502, 1.072, 0.398, 1.085, 0.502, 0.921]}, {"time": 0.6, "x": 1.072, "y": 0.921, "curve": [0.698, 1.072, 0.769, 0.917, 0.698, 0.921, 0.769, 1.085]}, {"time": 0.8667, "x": 0.917, "y": 1.085, "curve": [0.965, 0.917, 1.069, 1.072, 0.965, 1.085, 1.069, 0.921]}, {"time": 1.1667, "x": 1.072, "y": 0.921}]}, "RU_L3": {"translate": [{"x": 8.15, "curve": [0.123, -5.43, 0.245, -23.48, 0.123, 0, 0.245, 0]}, {"time": 0.3667, "x": -23.48, "curve": [0.562, -23.48, 0.771, 22.9, 0.562, 0, 0.771, 0]}, {"time": 0.9667, "x": 22.9, "curve": [1.04, 22.9, 1.094, 16.36, 1.04, 0, 1.094, 0]}, {"time": 1.1667, "x": 8.15}], "scale": [{"x": 1.047, "y": 0.947, "curve": [0.024, 1.061, 0.042, 1.072, 0.024, 0.932, 0.042, 0.921]}, {"time": 0.0667, "x": 1.072, "y": 0.921, "curve": [0.165, 1.072, 0.269, 0.917, 0.165, 0.921, 0.269, 1.085]}, {"time": 0.3667, "x": 0.917, "y": 1.085, "curve": [0.465, 0.917, 0.569, 1.072, 0.465, 1.085, 0.569, 0.921]}, {"time": 0.6667, "x": 1.072, "y": 0.921, "curve": [0.765, 1.072, 0.869, 0.917, 0.765, 0.921, 0.869, 1.085]}, {"time": 0.9667, "x": 0.917, "y": 1.085, "curve": [1.04, 0.917, 1.094, 1.003, 1.04, 1.085, 1.094, 0.993]}, {"time": 1.1667, "x": 1.047, "y": 0.947}]}, "headround3": {"translate": [{"x": 12.26, "curve": [0.04, 12.26, 0.097, 208.19, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 208.19, "curve": [0.611, 208.19, 0.889, 12.26, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 12.26}]}, "headround": {"translate": [{"y": 137.3, "curve": [0.111, 0, 0.222, 0, 0.04, 137.3, 0.097, 294.38]}, {"time": 0.3333, "y": 294.38, "curve": [0.611, 0, 0.889, 0, 0.611, 294.38, 0.889, 137.3]}, {"time": 1.1667, "y": 137.3}]}, "bodyround": {"translate": [{"y": -80.45, "curve": [0.078, 0, 0.156, 0, 0.028, -80.45, 0.068, 367.68]}, {"time": 0.2333, "y": 367.68, "curve": [0.544, 0, 0.856, 0, 0.544, 367.68, 0.856, -80.45]}, {"time": 1.1667, "y": -80.45}]}, "tunround": {"translate": [{"x": 172.47, "curve": [0.028, 172.47, 0.068, -344.13, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -344.13, "curve": [0.544, -344.13, 0.856, 172.47, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 172.47}]}, "sh_R2": {"rotate": [{"value": -0.15}]}, "sh_R3": {"rotate": [{"value": 0.31}]}, "hand_R": {"rotate": [{"curve": [0.028, 0, 0.068, -4.38]}, {"time": 0.2333, "value": -4.38, "curve": [0.544, -4.38, 0.856, 0]}, {"time": 1.1667}]}, "hand_R2": {"rotate": [{"curve": [0.028, 0, 0.068, -4.38]}, {"time": 0.2333, "value": -4.38, "curve": [0.544, -4.38, 0.856, 0]}, {"time": 1.1667}]}, "body3": {"rotate": [{"value": -4.99, "curve": [0.028, -4.99, 0.068, 6.96]}, {"time": 0.2333, "value": 6.96, "curve": [0.544, 6.96, 0.856, -4.99]}, {"time": 1.1667, "value": -4.99}]}, "hair_RF": {"rotate": [{"value": -0.9, "curve": [0.041, -0.9, 0.103, 5.59]}, {"time": 0.3333, "value": 5.59, "curve": [0.611, 5.59, 0.889, -0.9]}, {"time": 1.1667, "value": -0.9}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.033, 0, 0.083, 0.97]}, {"time": 0.2667, "vertices": [-2.09585, -0.25908, -2.09619, -0.25868, -1.56595, -0.25649, -1.5661, -0.25614, -1.16872, -0.17786, -1.1687, -0.17762, -0.89785, -0.16351, -0.89783, -0.16328, -0.4885, 0.02228, -0.48858, 0.02233, -0.12474, 0.0034, -0.12474, 0.00342, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.81459, -0.00807, -0.81443, -0.00798, -1.72926, -0.19288, -1.72921, -0.19273, -2.64217, -0.22139, -2.64151, -0.22108, -2.86756, -0.20676, -2.86738, -0.20649, -2.55587, -0.24964, -2.55635, -0.24928, -2.54939, -0.33122, -2.54973, -0.33084, 0, 0, 0, 0, -1.14465, -0.23117, -1.1442, -0.23102, -2.15377, -0.31742, -2.15348, -0.31717, -2.76544, -0.30342, -2.76523, -0.30328, -2.88931, -0.23167, -2.88881, -0.23142, -2.74624, -0.12711, -2.74606, -0.12696, -1.12179, -0.0999, -1.1215, -0.09978, -0.46949, -0.06739, -0.46889, -0.06726, -0.14375, -0.01143, -0.14354, -0.01138, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.13026, -0.0567, -2.12999, -0.0565], "curve": [0.566, 0.06, 0.867, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.033, 0, 0.083, 0.97]}, {"time": 0.2667, "vertices": [-4.23183, 0.58239, -4.23196, 0.58282, -5.60608, 0.76515, -5.60686, 0.76537, -5.33474, 0.86263, -5.33534, 0.86284, -3.61334, 0.82905, -3.61394, 0.82951, -1.04582, 0.43418, -1.04681, 0.43433, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.05021, -0.00863, -0.05025, -0.00863, -0.3101, -0.00151, -0.31036, -0.00152, -0.38307, -0.01405, -0.38337, -0.01404, -0.3724, -0.13377, -0.3724, -0.13358, -1.50582, 0.25669, -1.50629, 0.25694, -3.20016, -0.37845, -3.20162, -0.37829, 0, 0, 0, 0, -1.99786, 0.0272, -1.99816, 0.02732, -3.96896, 0.05919, -3.96926, 0.05931, -5.59688, 0.27256, -5.59752, 0.27263, -5.77219, 0.29939, -5.77297, 0.29949, -4.97237, 0.23905, -4.97284, 0.2392, -3.26835, 0.11457, -3.26848, 0.11482, -0.66689, 0.11836, -0.66749, 0.11838, -0.16783, 0.07463, -0.16804, 0.07469], "curve": [0.566, 0.06, 0.867, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.033, 0, 0.083, 0.97]}, {"time": 0.2667, "offset": 528, "vertices": [-2.86971, 0.26454, -2.86998, 0.26437, -4.34528, 0.38486, -4.34563, 0.38481, -5.26733, 0.52539, -5.2686, 0.52505, -4.78634, 0.60801, -4.78758, 0.60759, -3.0201, 0.59618, -3.02127, 0.59588, -1.08246, 0.15264, -1.08291, 0.15239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.39798, -0.20237, -1.39785, -0.20232, -2.74214, -0.28221, -2.74224, -0.28216, -3.48412, -0.35306, -3.48416, -0.35299, -3.45429, -0.19706, -3.45432, -0.19692, -2.596, -0.14404, -2.59594, -0.14392, -1.19818, -0.17779, -1.19815, -0.17768, -0.45227, -0.04972, -0.45214, -0.04966, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.17642, -0.0303, -0.1761, -0.03022], "curve": [0.566, 0.06, 0.867, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.033, 0, 0.083, 0.97]}, {"time": 0.2667, "offset": 80, "vertices": [2.12964, 1.33801, 2.13098, 1.3381, 2.12964, 1.33801, 2.13098, 1.3381, 0.89587, 0.42654, 0.89636, 0.42658, -1.71521, -0.00858, -1.71533, -0.00859, -2.82947, -0.17564, -2.82935, -0.17564, -2.9375, 0.01193, -2.93738, 0.01198, -2.9375, 0.01193, -2.93738, 0.01198, -3.16516, 0.08696, -3.16504, 0.08704, -3.29285, 0.28426, -3.29285, 0.28426, -1.96326, 0.93996, -1.96216, 0.94006, 1.60706, 0.49402, 1.60864, 0.49401, 2.70081, 1.30261, 2.70215, 1.30268, 2.70081, 1.30261, 2.70215, 1.30268, 3.03149, 0.45227, 3.03223, 0.45229, 2.07874, 0.12967, 2.07874, 0.12968, 2.01819, 0.05576, 2.01831, 0.05575, 1.48108, 0.02226, 1.4812, 0.02227, 2.22632, 0.17647, 2.22644, 0.17648, 2.73108, 0.59914, 2.73145, 0.59914, 2.78467, 0.59579, 2.78528, 0.59579, 3.18872, 0.03445, 3.18958, 0.03453, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 2.48328, 1.36017, 2.48462, 1.36027, 1.61121, 0.47134, 1.61169, 0.47137, 0.74512, 0.0464, 0.74512, 0.04643, -1.31934, -0.08281, -1.31946, -0.08284, -2.6106, -0.08501, -2.6106, -0.08495, -3.04846, -0.064, -3.04822, -0.06393, -1.81372, -0.05745, -1.81372, -0.05742, 0.52148, -0.18106, 0.52161, -0.18103, 1.51782, -0.14861, 1.51843, -0.14858, 1.48767, -0.15047, 1.48828, -0.15044, 1.18774, -0.02328, 1.18811, -0.02325, 0.80542, 0.24109, 0.80579, 0.2411, 1.98682, -0.05083, 1.98743, -0.05085, 2.64844, -0.42595, 2.64929, -0.42586, 2.62134, -0.00095, 2.62219, -0.00084, 2.62134, -0.00095, 2.62219, -0.00084, 1.48206, 0.09242, 1.48206, 0.09241, 1.26709, 0.07903, 1.26709, 0.07903, 1.48206, 0.09242, 1.48206, 0.09241, 1.97327, 0.87712, 1.97327, 0.87715, 1.72363, -0.32346, 1.72412, -0.32349, 0.71753, 0.04478, 0.71753, 0.04479, 0.71753, 0.04478, 0.71753, 0.04479, 0.71753, 0.04478, 0.71753, 0.04479], "curve": [0.566, 0.06, 0.867, 1]}, {"time": 1.1667}]}}}}}}}