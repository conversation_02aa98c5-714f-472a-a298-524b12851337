﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class PathViewerWindow : EditorWindow
    {
        void OnGUI()
        {
            GUILayout.BeginVertical();
            mStartCoordText = EditorGUILayout.TextField("Start Coordinate", mStartCoordText);
            mEndCoordText = EditorGUILayout.TextField("End Coordinate", mEndCoordText);
            mUnitPathText = EditorGUILayout.TextField("Unit Path Coordinates", mUnitPathText);
            mTargetPathText = EditorGUILayout.TextField("Target Path Coordinates", mTargetPathText);
            mColor = EditorGUILayout.ColorField("Color", mColor);
            mWidth = EditorGUILayout.FloatField("Width", mWidth);
            GUILayout.EndHorizontal();

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Create"))
            {
                bool suc1, suc2, suc3, suc4;
                var start = DeserializeVector2(mStartCoordText, out suc1);
                var end = DeserializeVector2(mEndCoordText, out suc2);
                var unitPath = DeserializeVector2Array(mUnitPathText, out suc3);
                var targetPath = DeserializeVector2Array(mTargetPathText, out suc4);

                if (suc1 && suc2 && suc3 && suc4)
                {
                    PathViewer.Create(unitPath, targetPath, start, end, mWidth, mColor);

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        Vector2 DeserializeVector2(string text, out bool suc)
        {
            suc = false;
            var obj = JSONParser.Deserialize(text) as Dictionary<string, object>;
            if (obj != null)
            {
                object xObj;
                obj.TryGetValue("X", out xObj);
                object yObj;
                obj.TryGetValue("Z", out yObj);
                if (xObj != null && yObj != null)
                {
                    float x = System.Convert.ToSingle(xObj);
                    float y = System.Convert.ToSingle(yObj);
                    suc = true;
                    return new Vector2(x / 1000.0f, y / 1000.0f);
                }
            }
            return Vector2.zero;
        }

        List<Vector2> DeserializeVector2Array(string text, out bool suc)
        {
            suc = false;
            List<Vector2> ret = null;
            var obj = JSONParser.Deserialize(text) as List<object>;
            if (obj != null)
            {
                ret = new List<Vector2>();
                for (int i = 0; i < obj.Count; ++i)
                {
                    var vec2 = obj[i] as Dictionary<string, object>;
                    if (vec2 != null)
                    {
                        object xObj;
                        vec2.TryGetValue("X", out xObj);
                        object yObj;
                        vec2.TryGetValue("Z", out yObj);
                        if (xObj != null && yObj != null)
                        {
                            float x = System.Convert.ToSingle(xObj);
                            float y = System.Convert.ToSingle(yObj);
                            ret.Add(new Vector2(x / 1000.0f, y / 1000.0f));
                        }
                    }
                }

                suc = ret.Count == obj.Count;
            }
            return ret;
        }

        string mUnitPathText = "[{\"X\":304778, \"Z\":4124723}, {\"X\":354778, \"Z\":3124723}, {\"X\":404778, \"Z\":4124723}]";
        string mTargetPathText = "[{\"X\":304778, \"Z\":4124723}, {\"X\":324778, \"Z\":3724723}, {\"X\":404778, \"Z\":4124723}]";
        string mStartCoordText = "{\"X\":304778, \"Z\":4124723}";
        string mEndCoordText = "{\"X\":404778, \"Z\":4124723}";
        Color mColor = Color.green;
        float mWidth = 0.1f;
    }
}

#endif