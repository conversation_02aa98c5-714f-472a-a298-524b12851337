%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3627001157082658190
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2071543277304020494}
  - component: {fileID: 240151828560125681}
  m_Layer: 0
  m_Name: ObstacleOutline 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2071543277304020494
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3627001157082658190}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &240151828560125681
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3627001157082658190}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -58.830105, y: 0, z: -4.8784714}
    - {x: -36.131386, y: 0, z: -29.643307}
    - {x: -32.352554, y: 0, z: -55.943863}
    - {x: -23.804522, y: 0, z: -63.67005}
    - {x: -14.68861, y: 0, z: -60.68235}
    - {x: -10.873577, y: 0, z: -51.462482}
    - {x: -24.512033, y: 0, z: -15.798641}
    - {x: -46.861294, y: 0, z: 15.857943}
    - {x: -55.48535, y: 0, z: 19.390638}
    - {x: -61.73763, y: 0, z: 15.2739525}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -22.681, y: 0, z: -65.5645}
    - {x: -17.336, y: 0, z: -64.2962}
    - {x: -16.8865, y: 0, z: -64.1088}
    - {x: -15.3055, y: 0, z: -63.1243}
    - {x: -15.2071, y: 0, z: -63.0575}
    - {x: -14.7174, y: 0, z: -62.6956}
    - {x: -14.6281, y: 0, z: -62.6243}
    - {x: -12.8242, y: 0, z: -61.066}
    - {x: -12.4719, y: 0, z: -60.6211}
    - {x: -10.9861, y: 0, z: -57.775}
    - {x: -10.9827, y: 0, z: -57.7684}
    - {x: -10.0696, y: 0, z: -55.9983}
    - {x: -10.0165, y: 0, z: -55.8836}
    - {x: -9.6791, y: 0, z: -55.0675}
    - {x: -9.5955, y: 0, z: -54.7941}
    - {x: -8.9886, y: 0, z: -51.8233}
    - {x: -9.0442, y: 0, z: -51.0107}
    - {x: -20.67, y: 0, z: -17.9526}
    - {x: -20.788, y: 0, z: -17.6967}
    - {x: -30.4625, y: 0, z: -1.0278}
    - {x: -30.559, y: 0, z: -0.8818}
    - {x: -41.653, y: 0, z: 13.9408}
    - {x: -41.8828, y: 0, z: 14.1853}
    - {x: -47.725, y: 0, z: 19.1542}
    - {x: -48.1982, y: 0, z: 19.4271}
    - {x: -53.3864, y: 0, z: 21.272}
    - {x: -53.6631, y: 0, z: 21.3417}
    - {x: -56.5717, y: 0, z: 21.786}
    - {x: -57.7009, y: 0, z: 21.5212}
    - {x: -61.6577, y: 0, z: 18.7428}
    - {x: -62.0107, y: 0, z: 18.3958}
    - {x: -63.8784, y: 0, z: 15.8305}
    - {x: -64.1732, y: 0, z: 14.9041}
    - {x: -64.106, y: 0, z: 9.8244}
    - {x: -64.1038, y: 0, z: 9.7605}
    - {x: -63.872, y: 0, z: 5.6123}
    - {x: -63.857, y: 0, z: 5.4688}
    - {x: -63.5898, y: 0, z: 3.7241}
    - {x: -63.5699, y: 0, z: 3.6192}
    - {x: -63.2584, y: 0, z: 2.2469}
    - {x: -63.2503, y: 0, z: 2.2131}
    - {x: -61.0194, y: 0, z: -6.6579}
    - {x: -60.8894, y: 0, z: -6.9962}
    - {x: -34.378, y: 0, z: -56.9765}
    - {x: -34.1692, y: 0, z: -57.2757}
    - {x: -30.5499, y: 0, z: -61.3147}
    - {x: -30.4596, y: 0, z: -61.4071}
    - {x: -30.1827, y: 0, z: -61.6672}
    - {x: -30.1774, y: 0, z: -61.6722}
    - {x: -28.7242, y: 0, z: -63.0239}
    - {x: -28.4974, y: 0, z: -63.1979}
    - {x: -26.756, y: 0, z: -64.2868}
    - {x: -26.552, y: 0, z: -64.3935}
    - {x: -24.2867, y: 0, z: -65.3658}
    - {x: -23.9569, y: 0, z: -65.4645}
    - {x: -23.2967, y: 0, z: -65.582}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
