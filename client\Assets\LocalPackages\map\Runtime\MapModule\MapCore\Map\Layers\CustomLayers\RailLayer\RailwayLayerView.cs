﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public class RailwayLayerView : ModelLayerView
    {
        public RailwayLayerView(RailwayLayerData layerData, bool asyncLoading)
        : base(layerData, asyncLoading)
        {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new RailObjectView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }
    }
}
