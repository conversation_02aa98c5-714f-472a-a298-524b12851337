﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/8/20
using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace TFW.Map
{
    public static class TSVReader
    {
        enum UserType
        {
            All,
            Server,
            Client,
        }
        enum DataType
        {
            Int,
            Float,
            String,
            Array,
            Map,
            Bool,
            Long,
        }
        class ColumnInfo
        {
            public ColumnInfo(UserType u, DataType d, string n)
            {
                userType = u;
                dataType = d;
                name = n;
            }

            //server or client or all
            public UserType userType;
            public DataType dataType;
            public string name;
        }

        public static bool Load(string filePath)
        {
            Clear();

            if (!File.Exists(filePath))
            {
                return false;
            }
            var lines = File.ReadLines(filePath, Encoding.UTF8);

            int lineIdx = 0;
            foreach (var line in lines)
            {
                if (lineIdx == 0)
                {
                    ParseColumn(line);
                }
                else
                {
                    ParseRow(line);
                }

                ++lineIdx;
            }

            return true;
        }

        static void ParseColumn(string line)
        {
            var tokens = line.Split(mSeparator);
            for (int i = 0; i < tokens.Length; ++i)
            {
                ParseColumnEntry(tokens[i]);
            }
        }

        static void ParseColumnEntry(string token)
        {
            int userTypeLength = 2;
            int dataTypeLength = 4;
            int offset = 0;
            var userType = token.Substring(offset, userTypeLength);
            offset += userTypeLength;
            var dataType = token.Substring(offset, dataTypeLength);
            offset += dataTypeLength;
            var name = token.Substring(offset);

            mColumns.Add(new ColumnInfo(ConvertToUserType(userType), ConvertToDataType(dataType), name));
        }

        static UserType ConvertToUserType(string u)
        {
            for (int i = 0; i < mUserTypeTable.Length; ++i)
            {
                if (mUserTypeTable[i] == u)
                {
                    return (UserType)i;
                }
            }
            Debug.Assert(false);
            return UserType.All;
        }

        static DataType ConvertToDataType(string d)
        {
            for (int i = 0; i < mDataTypeTable.Length; ++i)
            {
                if (mDataTypeTable[i] == d)
                {
                    return (DataType)i;
                }
            }
            Debug.Assert(false);
            return DataType.String;
        }

        static void ParseRow(string line)
        {
            List<object> row = new List<object>();
            var tokens = line.Split(mSeparator);
            for (int i = 0; i < tokens.Length; ++i)
            {
                switch (mColumns[i].dataType)
                {
                    case DataType.Array:
                        if (!string.IsNullOrEmpty(tokens[i])) { 
                            var arr = JSONParser.Deserialize(tokens[i]);
                            row.Add(arr);
                        }
                        else
                        {
                            row.Add(null);
                        }
                        break;
                    case DataType.Long:
                    case DataType.Int:
                        long.TryParse(tokens[i], out long val);
                        row.Add(val);
                        break;
                    case DataType.Map:
                        if (!string.IsNullOrEmpty(tokens[i]))
                        {
                            var map = JSONParser.Deserialize(tokens[i]);
                            row.Add(map);
                        }
                        else
                        {
                            row.Add(null);
                        }
                        break;
                    case DataType.String:
                        row.Add(tokens[i]);
                        break;
                    case DataType.Float:
                        double fv;
                        bool suc = double.TryParse(tokens[i], out fv);
                        if (suc)
                        {
                            row.Add(fv);
                        }
                        else
                        {
                            var lv = long.Parse(tokens[i]);
                            row.Add(lv);
                        }
                        break;
                    case DataType.Bool:
                        bool bv;
                        suc = bool.TryParse(tokens[i], out bv);
                        if (suc)
                        {
                            row.Add(bv);
                        }
                        break;
                    default:
                        Debug.Assert(false, "unknown tsv data type");
                        break;
                }
            }
            mRows.Add(row);
        }

        public static List<List<object>> rows { get { return mRows; } }

        public static int GetColumnIndex(string columnName)
        {
            for (int i = 0; i < mColumns.Count; ++i)
            {
                if (columnName == mColumns[i].name)
                {
                    return i;
                }
            }
            return -1;
        }

        public static long GetInt(int row, int column, out bool suc)
        {
            suc = false;
            long val = 0;
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Int)
                {
                    suc = true;
                    val = System.Convert.ToInt64(mRows[row][column]);
                }
            }
            return val;
        }

        public static long GetLong(int row, int column, out bool suc)
        {
            suc = false;
            long val = 0;
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Long)
                {
                    suc = true;
                    val = System.Convert.ToInt64(mRows[row][column]);
                }
            }
            return val;
        }

        public static double GetFloat(int row, int column, out bool suc)
        {
            suc = false;
            double val = 0;
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Float)
                {
                    suc = true;
                    val = System.Convert.ToDouble(mRows[row][column]);
                }
            }
            return val;
        }

        public static long GetInt(int row, string columnName, out bool suc)
        {
            suc = false;
            long val = 0;
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Int)
                {
                    suc = true;
                    val = System.Convert.ToInt64(mRows[row][column]);
                }
            }
            return val;
        }

        public static long GetLong(int row, string columnName, out bool suc)
        {
            suc = false;
            long val = 0;
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Long)
                {
                    suc = true;
                    val = System.Convert.ToInt64(mRows[row][column]);
                }
            }
            return val;
        }

        public static double GetFloat(int row, string columnName, out bool suc)
        {
            suc = false;
            double val = 0;
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Float)
                {
                    suc = true;
                    val = System.Convert.ToDouble(mRows[row][column]);
                }
            }
            return val;
        }

        public static bool GetBool(int row, string columnName, out bool suc)
        {
            suc = false;
            bool val = false;
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Bool)
                {
                    suc = true;
                    val = System.Convert.ToBoolean(mRows[row][column]);
                }
            }
            return val;
        }

        public static string GetString(int row, string columnName, out bool suc)
        {
            suc = false;
            string val = "";
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.String)
                {
                    suc = true;
                    val = System.Convert.ToString(mRows[row][column]);
                }
            }
            return val;
        }

        public static List<string> GetStringList(int row, string columnName, out bool suc)
        {
            suc = false;
            List<string> val = null;
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Array)
                {
                    var arr = mRows[row][column] as List<object>;
                    val = new List<string>();
                    suc = true;
                    for (int i = 0; i < arr.Count; ++i)
                    {
                        var s = arr[i] as string;
                        if (s == null)
                        {
                            suc = false;
                        }
                        val.Add(s);
                    }
                }
            }
            return val;
        }

        public static Vector2Int GetCoord(int row, string columnName, out bool suc)
        {
            suc = false;
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Map)
                {
                    var map = mRows[row][column] as Dictionary<string, object>;
                    Vector2Int coord = new Vector2Int();
                    suc = true;
                    coord.x = System.Convert.ToInt32(map["x"]);
                    coord.y = System.Convert.ToInt32(map["z"]);
                    return coord;
                }
            }
            return Vector2Int.zero;
        }

        public static Dictionary<string, object> GetMap(int row, string columnName, out bool suc)
        {
            suc = false;
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Map)
                {
                    var map = mRows[row][column] as Dictionary<string, object>;
                    return map;
                }
            }
            return null;
        }

        public static List<object> GetArray(int row, string columnName, out bool suc)
        {
            suc = false;
            var column = GetColumnIndex(columnName);
            if (row >= 0 && row < mRows.Count && column >= 0 && column < mColumns.Count)
            {
                if (mColumns[column].dataType == DataType.Array)
                {
                    var array = mRows[row][column] as List<object>;
                    return array;
                }
            }
            return null;
        }

        static void Clear()
        {
            mColumns.Clear();
            mRows.Clear();
        }

        static List<ColumnInfo> mColumns = new List<ColumnInfo>();
        static List<List<object>> mRows = new List<List<object>>();

        static char mSeparator = '\t';
        static string[] mUserTypeTable = new string[]
            {
                "A_",
                "S_",
                "C_",
            };
        static string[] mDataTypeTable = new string[]
            {
                "INT_",
                "FLT_",
                "STR_",
                "ARR_",
                "MAP_",
                "BOL_",
                "LNG_",
            };
    }
}

#endif