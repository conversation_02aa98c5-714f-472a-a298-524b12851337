﻿ 



 
 



/*
 * created by wzw at 2019.12.9
 */

using UnityEngine;

namespace TFW.Map
{
    //地图对象的模型层
    public class RiverLayerView : MapObjectLayerView
    {
        public RiverLayerView(MapLayerData layerData, bool asyncLoading)
        : base(layerData, asyncLoading)
        {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new RiverView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }
    }
}
