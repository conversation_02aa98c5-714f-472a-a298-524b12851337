﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SimpleBlendTerrainLayerData : MapLayerData
    {
        void DestroyLODTiles()
        {
            if (mOtherLODTiles != null)
            {
                for (int i = 0; i < mOtherLODTiles.Count; ++i)
                {
                    if (mOtherLODTiles[i] != null)
                    {
                        int r = mOtherLODTiles[i].GetLength(0);
                        int c = mOtherLODTiles[i].GetLength(1);

                        for (int k = 0; k < r; ++k)
                        {
                            for (int j = 0; j < c; ++j)
                            {
                                map.DestroyObject(mOtherLODTiles[i][k, j]);
                            }
                        }
                    }
                }
                mOtherLODTiles = null;
            }
        }

        public override void RefreshObjectsInViewport()
        {
            Debug.Assert(false, "Can't be here!");
        }

        public void SetCallbacks(
            System.Action<int, int> onShowLOD0,
            System.Action<int, int> onHideLOD0,
            System.Action<IMapObjectData, int, int, int> onShowLOD1,
            System.Action<IMapObjectData, int, int, int> onHideLOD1,
            System.Action onFinishUpdateViewport)
        {
            mOnShowLOD0 = onShowLOD0;
            mOnHideLOD0 = onHideLOD0;
            mOnShowLOD1 = onShowLOD1;
            mOnHideLOD1 = onHideLOD1;
            mOnFinishUpdateViewport = onFinishUpdateViewport;
        }

        bool UpdateViewRectLOD0(Rect2D oldViewRect, Rect2D newViewRect, int currentLOD)
        {
#if false
            Debug.Assert(currentLOD == 0, "Only support 1 lod now");
            var map = map;
            if (oldViewRect != newViewRect)
            {
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (!newViewRect.Contains(j, i))
                        {
                            //物体不在新的视野中,隐藏它
                            if (j >= 0 && j < mCols && i >= 0 && i < mRows)
                            {
                                if (GetTileType(j, i) > 0)
                                {
                                    mOnHideLOD0(j, i);
                                }
                            }
                        }
                    }
                }

                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (!oldViewRect.Contains(j, i))
                        {
                            //物体不在旧的视野中,显示它
                            if (j >= 0 && j < mCols && i >= 0 && i < mRows)
                            {
                                if (GetTileType(j, i) > 0)
                                {
                                    mOnShowLOD0(j, i);
                                }
                            }
                        }
                    }
                }

                mOnFinishUpdateViewport();

                return true;
            }

            return false;
#else
            mOldRect.Set(oldViewRect.minX, oldViewRect.minY, oldViewRect.maxX, oldViewRect.maxY);
            mNewRect.Set(newViewRect.minX, newViewRect.minY, newViewRect.maxX, newViewRect.maxY);
            mRectDiffCalculator.Calculate(mOldRect, mNewRect, mVisibleRects, mInvisibleRects);

            for (int i = 0; i < mInvisibleRects.Count; ++i)
            {
                var xMin = mInvisibleRects[i].xMin;
                var yMin = mInvisibleRects[i].yMin;
                var xMax = mInvisibleRects[i].xMax;
                var yMax = mInvisibleRects[i].yMax;
                for (int y = yMin; y <= yMax; ++y)
                {
                    for (int x = xMin; x <= xMax; ++x)
                    {
                        if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                        {
                            if (GetTileType(x, y) > 0)
                            {
                                mOnHideLOD0(x, y);
                            }
                        }
                    }
                }
            }

            for (int i = 0; i < mVisibleRects.Count; ++i)
            {
                var xMin = mVisibleRects[i].xMin;
                var yMin = mVisibleRects[i].yMin;
                var xMax = mVisibleRects[i].xMax;
                var yMax = mVisibleRects[i].yMax;
                for (int y = yMin; y <= yMax; ++y)
                {
                    for (int x = xMin; x <= xMax; ++x)
                    {
                        if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                        {
                            if (GetTileType(x, y) > 0)
                            {
                                mOnShowLOD0(x, y);
                            }
                        }
                    }
                }
            }

            mOnFinishUpdateViewport();
            return true;
#endif
        }

        public void UpdateViewport(Rect newViewport)
        {
            if (mLastViewport != newViewport)
            {
                if (mCurrentLOD < mDefaultLODCount || mLODSettings == null)
                {
                    if (map.isEditorMode)
                    {
                        if (mLastViewport.width == 0)
                        {
                            mLastViewport = newViewport;
                        }
                    }
                    var oldViewRect = GetViewRect(mLastViewport);
                    var newViewRect = GetViewRect(newViewport);
                    UpdateViewRectLOD0(oldViewRect, newViewRect, mCurrentLOD);
                }
                else
                {
                    //使用合并后的tile
                    int localLOD = mCurrentLOD - mDefaultLODCount;
                    var oldViewRect = GetLODTileViewRect(mLastViewport, mLODSettings[localLOD].realBlockSize);
                    var newViewRect = GetLODTileViewRect(newViewport, mLODSettings[localLOD].realBlockSize);

                    UpdateLODTileViewRect(oldViewRect, newViewRect, mCurrentLOD);
                }
            }

            mLastViewport = newViewport;
        }

        IMapObjectData GetLODTile(int lod, int x, int y)
        {
#if UNITY_EDITOR
            Debug.Assert(lod > 0);
#endif
            return mOtherLODTiles[lod - mDefaultLODCount][y, x];
        }

        Vector2Int GetLODDimension(int lod)
        {
            if (lod < mDefaultLODCount || mLODSettings == null)
            {
                return new Vector2Int(mCols, mRows);
            }
            return new Vector2Int(mOtherLODTiles[lod - mDefaultLODCount].GetLength(1), mOtherLODTiles[lod - mDefaultLODCount].GetLength(0));
        }

        public void OnLODChanged(int oldLOD, int newLOD, Rect newViewport)
        {
            int n = lodConfig.lodConfigs.Length;
            if (oldLOD < n && newLOD < n)
            {
                Rect2D oldViewRect = GetTerrainViewRect(oldLOD, mLastViewport);
                Rect2D newViewRect = GetTerrainViewRect(newLOD, newViewport);

                Vector2Int dimensions = GetLODDimension(oldLOD);
                if (oldLOD == 0)
                {
                    for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                    {
                        for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                        {
                            if (i >= 0 && i < dimensions.y && j >= 0 && j < dimensions.x)
                            {
                                if (GetTileType(j, i) > 0)
                                {
                                    mOnHideLOD0(j, i);
                                }
                            }
                        }
                    }
                }
                else
                {
                    for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                    {
                        for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                        {
                            if (i >= 0 && i < dimensions.y && j >= 0 && j < dimensions.x)
                            {
                                var objectData = GetLODTile(oldLOD, j, i);
                                mOnHideLOD1(objectData, j, i, oldLOD);
                            }
                        }
                    }
                }

                dimensions = GetLODDimension(newLOD);
                if (newLOD == 0)
                {
                    for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                    {
                        for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                        {
                            if (i >= 0 && i < dimensions.y && j >= 0 && j < dimensions.x)
                            {
                                if (GetTileType(j, i) > 0)
                                {
                                    mOnShowLOD0(j, i);
                                }
                            }
                        }
                    }
                }
                else
                {
                    for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                    {
                        for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                        {
                            if (i >= 0 && i < dimensions.y && j >= 0 && j < dimensions.x)
                            {
                                var objectData = GetLODTile(newLOD, j, i);
                                mOnShowLOD1(objectData, j, i, newLOD);
                            }
                        }
                    }
                }
            }

            mLastViewport = newViewport;

            mOnFinishUpdateViewport();
        }

        protected bool UpdateLODTileViewRect(Rect2D oldViewRect, Rect2D newViewRect, int currentLOD)
        {
            var newLODTiles = mOtherLODTiles[currentLOD - mDefaultLODCount];
            int rows = newLODTiles.GetLength(0);
            int cols = newLODTiles.GetLength(1);

            if (oldViewRect != newViewRect)
            {
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (!newViewRect.Contains(j, i))
                        {
                            //物体不在新的视野中,隐藏它
                            if (j >= 0 && i >= 0 && i < rows && j < cols)
                            {
                                mOnHideLOD1(newLODTiles[i, j], j, i, currentLOD);
                            }
                        }
                    }
                }

                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (!oldViewRect.Contains(j, i))
                        {
                            //物体不在旧的视野中,显示它
                            if (j >= 0 && i >= 0 && i < rows && j < cols)
                            {
                                mOnShowLOD1(newLODTiles[i, j], j, i, currentLOD);
                            }
                        }
                    }
                }

                return true;
            }

            return false;
        }

        //return: if extra lod is changed
        public bool UpdateLOD(float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }
            return lodChanged;
        }

        int CalculateExtraLODFromCameraHeight()
        {
            float cameraHeight = MapCameraMgr.currentCameraHeight;
            for (int i = mLODSettings.Length - 1; i >= 0; --i)
            {
                if (cameraHeight >= mLODSettings[i].cameraHeight)
                {
                    return i;
                }
            }
            return -1;
        }

        Vector2Int WorldPosToCoordFloor(Vector3 pos, int blockSize)
        {
            float tw = tileWidth * blockSize;
            float th = tileHeight * blockSize;
            float dx = pos.x - layerOffset.x;
            float dz = pos.z - layerOffset.z;
            return new Vector2Int(Mathf.FloorToInt(dx / tw), Mathf.FloorToInt(dz / th));
        }

        Vector2Int WorldPosToCoordCeil(Vector3 pos, int blockSize)
        {
            float tw = tileWidth * blockSize;
            float th = tileHeight * blockSize;
            float dx = pos.x - layerOffset.x;
            float dz = pos.z - layerOffset.z;
            return new Vector2Int(Mathf.CeilToInt(dx / tw), Mathf.CeilToInt(dz / th));
        }

        public Rect2D GetLODTileViewRect(Rect viewport, int blockSize)
        {
            var startCoord = WorldPosToCoordFloor(new Vector3(viewport.xMin, 0, viewport.yMin), blockSize);
            var endCoord = WorldPosToCoordCeil(new Vector3(viewport.xMax, 0, viewport.yMax), blockSize);

            return new Rect2D(startCoord.x, startCoord.y, endCoord.x, endCoord.y);
        }

        public Rect2D GetTerrainViewRect(int lod, Rect viewport)
        {
            if (lod < mDefaultLODCount)
            {
                return GetViewRect(viewport);
            }
            else
            {
                lod = lod - mDefaultLODCount;
                return GetLODTileViewRect(viewport, mLODSettings[lod].realBlockSize);
            }
        }

        public bool IsOneTileLOD(int lod)
        {
            if (lod < mDefaultLODCount)
            {
                return false;
            }
            else
            {
                lod = lod - mDefaultLODCount;
                return lod == mLODSettings.Length - 1 && mLODSettings[lod].realBlockSize == horizontalTileCount;
            }
        }

        public Rect2D GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new Rect2D(startCoord.x, startCoord.y, endCoord.x, endCoord.y);
        }

        public void UpdateTerrainGeneratedLODHeight(int lod, float newHeight)
        {
            float lodHeight = map.data.lodManager.GetCameraHeight(lod);
            for (int i = 0; i < mLODSettings.Length; ++i)
            {
                if (Mathf.Approximately(lodHeight, mLODSettings[i].cameraHeight))
                {
                    mLODSettings[i].cameraHeight = newHeight;
                    break;
                }
            }
        }

        public TerrainRenderTextureLODSetting[] lodSettings { get { return mLODSettings; } }
        public List<ModelData[,]> otherLODTiles { get { return mOtherLODTiles; } }
        public int defaultLODCount { get { return mDefaultLODCount; } }

        List<ModelData[,]> mOtherLODTiles;
        TerrainRenderTextureLODSetting[] mLODSettings;
        int mDefaultLODCount;

        System.Action<int, int> mOnShowLOD0;
        System.Action<int, int> mOnHideLOD0;
        System.Action<IMapObjectData, int, int, int> mOnShowLOD1;
        System.Action<IMapObjectData, int, int, int> mOnHideLOD1;
        System.Action mOnFinishUpdateViewport;

        RectDifference mRectDiffCalculator = new RectDifference();
        RectI mOldRect = new RectI();
        RectI mNewRect = new RectI();
        List<RectInt> mVisibleRects = new List<RectInt>(9);
        List<RectInt> mInvisibleRects = new List<RectInt>(9);
    }
}