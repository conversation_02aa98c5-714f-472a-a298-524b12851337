﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class TileModelTemplateLODConnectionManager
    {
        public int GetConnectedPrefabIndexInNextLOD(int curLODPrefabIndex, int curLOD, int nextLOD)
        {
            var key = Utils.MakeInt32Key(curLOD, curLODPrefabIndex);
            List<LODIndex> sharedPrefabKeys;
            mSharedPrefabs.TryGetValue(key, out sharedPrefabKeys);
            if (sharedPrefabKeys != null)
            {
                for (int i = 0; i < sharedPrefabKeys.Count; ++i)
                {
                    if (sharedPrefabKeys[i].lod == nextLOD)
                    {
                        return sharedPrefabKeys[i].index;
                    }
                }
            }
            return -1;
        }

        public void CalculateLODConnections(ModelTemplate template)
        {
            List<KeyValuePair<LODIndex, LODIndex>> sharedPrefabs = new List<KeyValuePair<LODIndex, LODIndex>>();
            var childrenPrefabInfo = template.childrenPrefabInfo;
            int lodCount = childrenPrefabInfo.Count;
            for (int lod = 0; lod < lodCount; ++lod)
            {
                var lodInfo = childrenPrefabInfo[lod];
                for (int i = 0; i < lodInfo.Count; ++i)
                {
                    int nextLOD = lod + 1;
                    if (nextLOD < lodCount)
                    {
                        var nextLodInfo = childrenPrefabInfo[nextLOD];
                        for (int j = 0; j < nextLodInfo.Count; ++j)
                        {
                            if (IsPrefabConnected(lod, nextLOD, i, j, lodInfo[i], nextLodInfo[j]))
                            {
                                sharedPrefabs.Add(new KeyValuePair<LODIndex, LODIndex>(new LODIndex(lod, i), new LODIndex(nextLOD, j)));
                                break;
                            }
                        }
                    }
                }
            }

            for (int i = 0; i < sharedPrefabs.Count; ++i)
            {
                var key = Utils.MakeInt32Key(sharedPrefabs[i].Key.lod, sharedPrefabs[i].Key.index);
                
                List<LODIndex> sharedPrefabKeys;
                mSharedPrefabs.TryGetValue(key, out sharedPrefabKeys);
                if (sharedPrefabKeys == null)
                {
                    sharedPrefabKeys = new List<LODIndex>();
                    mSharedPrefabs[key] = sharedPrefabKeys;
                }
                for (int k = 0; k < sharedPrefabKeys.Count; ++k)
                {
                    if (sharedPrefabKeys[k].lod == sharedPrefabs[i].Value.lod &&
                        sharedPrefabKeys[k].index == sharedPrefabs[i].Value.index)
                    {
                        Debug.Assert(false);
                    }
                }
                sharedPrefabKeys.Add(sharedPrefabs[i].Value);
            }
            //调换key和value
            for (int i = 0; i < sharedPrefabs.Count; ++i)
            {
                var key = Utils.MakeInt32Key(sharedPrefabs[i].Value.lod, sharedPrefabs[i].Value.index);

                List<LODIndex> sharedPrefabKeys;
                mSharedPrefabs.TryGetValue(key, out sharedPrefabKeys);
                if (sharedPrefabKeys == null)
                {
                    sharedPrefabKeys = new List<LODIndex>();
                    mSharedPrefabs[key] = sharedPrefabKeys;
                }

                for (int k = 0; k < sharedPrefabKeys.Count; ++k)
                {
                    if (sharedPrefabKeys[k].lod == sharedPrefabs[i].Key.lod &&
                        sharedPrefabKeys[k].index == sharedPrefabs[i].Key.index)
                    {
                        Debug.Assert(false);
                    }
                }
                sharedPrefabKeys.Add(sharedPrefabs[i].Key);
            }
        }

        bool IsPrefabConnected(int lod, int nextLOD, int i, int j, ChildPrefabTransform a, ChildPrefabTransform b)
        {
            if (a.position == b.position)
            {
                return true;
            }
            return false;
        }

        Dictionary<int, List<LODIndex>> mSharedPrefabs = new Dictionary<int, List<LODIndex>>();
    }
}
