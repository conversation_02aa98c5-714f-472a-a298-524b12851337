﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        bool SaveBlendTerrainLayer(BinaryWriter writer, BlendTerrainLayer layer)
        {
            BeginSection(MapDataSectionType.Blend<PERSON><PERSON><PERSON><PERSON>ay<PERSON>, writer);
            //版本号
            writer.Write(VersionSetting.BlendTerrainLayerStructVersion);

            //-----------------version 1 start------------------------------
            bool useLayer = layer != null && layer.tileWidth >= MapCoreDef.BlendTerrainLayerTileSizeThreshold;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return false;
            }

            bool useTileHeight = false;
            //combined tiles
            int horizontalTileCount = layer.layerData.horizontalTileCount;
            int verticalTileCount = layer.layerData.verticalTileCount;
            float tileWidth = layer.layerData.tileWidth;
            float tileHeight = layer.layerData.tileHeight;
            var terrainLayerData = layer.layerData as BlendTerrainLayerData;
            BlendTerrainTileData[] tiles = terrainLayerData.tiles;

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            writer.Write(verticalTileCount);
            writer.Write(horizontalTileCount);
            writer.Write(tileWidth);
            writer.Write(tileHeight);

            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    int idx = i * horizontalTileCount + j;
                    var tile = tiles[idx];
                    bool hasTile = tile != null;
                    writer.Write(hasTile);
                    if (hasTile)
                    {
                        SaveBlendTerrainTileDataV1(writer, tile);
                    }
                }
            }

            //save map layer lod config
            SaveBlendTerrainLayerLayerLODConfigV1(writer, layer.layerData);
            //-----------------version 1 end------------------------------
            //-----------------version 2 start------------------------------
            SaveBlendTerrainLayerLayerLODConfigV2(writer, layer.layerData);
            //-----------------version 2 end------------------------------
            //-----------------version 3 start------------------------------
            writer.Write(terrainLayerData.useGeneratedLOD);
            //-----------------version 3 end------------------------------
            //-----------------version 4 start------------------------------
            //save blend terrain tile sub type index
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    int idx = i * horizontalTileCount + j;
                    var tile = tiles[idx];
                    int subTypeIndex = 0;
                    if (tile != null)
                    {
                        subTypeIndex = tile.subTypeIndex;
                    }
                    writer.Write(subTypeIndex);
                }
            }
            //-----------------version 4 end------------------------------
            //-----------------version 5 start------------------------------
            SaveBlendTerrainLayerLayerLODConfigV5(writer, layer.layerData);
            //-----------------version 5 end------------------------------
            //-----------------version 6 start------------------------------
            SaveTerrainHeightsV6(writer, horizontalTileCount, verticalTileCount, tiles, out useTileHeight);
            //-----------------version 6 end------------------------------
            //-----------------version 7 start------------------------------
            writer.Write(false/*terrainLayerData.useCombinedTiles*/);
            //-----------------version 7 end------------------------------
            //-----------------version 8 start------------------------------
            SaveBlendTerrainLayerLayerLODConfigV8(writer, layer.layerData);
            //-----------------version 8 end------------------------------
            //-----------------version 9 start------------------------------
            writer.Write(useTileHeight);
            List<string> prefabs = layer.GetUsedTilePrefabs();
            Utils.WriteStringArray(writer, prefabs.ToArray());
            //-----------------version 9 end------------------------------
            //-----------------version 10 start------------------------------
            writer.Write(terrainLayerData.getGroundHeightInGame);
            //-----------------version 10 end------------------------------
            //-----------------version 11 start------------------------------
            Utils.WriteVector3(writer, layer.layerView.root.transform.position);
            //-----------------version 11 end------------------------------

            return true;
        }

        void SaveBlendTerrainLayerLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
            }
        }

        void SaveBlendTerrainLayerLayerLODConfigV2(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.useRenderTexture);
            }
        }

        void SaveBlendTerrainLayerLayerLODConfigV5(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write((int)c.flag);
            }
        }

        void SaveBlendTerrainLayerLayerLODConfigV8(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write((int)c.terrainLODTileCount);
            }
        }

        void SaveBlendTerrainTileDataV1(BinaryWriter writer, BlendTerrainTileData tileData)
        {
            mIDExport.Export(writer, tileData.id);
            mIDExport.Export(writer, tileData.GetModelTemplateID());
            writer.Write(tileData.type);
            writer.Write(tileData.index);
        }

        void SaveTerrainHeightsV6(BinaryWriter writer, int horizontalTileCount, int verticalTileCount, BlendTerrainTileData[] tiles, out bool useHeight)
        {
            useHeight = false;
            //save heights
            int v = verticalTileCount;
            int h = horizontalTileCount;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    int idx = i * h + j;
                    var tile = tiles[idx];
                    bool hasHeight = false;
                    if (tile != null && tile.heights != null)
                    {
                        hasHeight = true;
                        useHeight = true;
                    }
                    writer.Write(hasHeight);
                    if (hasHeight)
                    {
                        Utils.WriteFloatArray(writer, tile.heights);
                    }
                }
            }
        }
    }
}

#endif