﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/8/20

using UnityEngine;

namespace TFW.Map
{
    //调试navmesh
    [Black]
    public class NavMeshDebugger : MonoBehaviour
    {
        public enum PickVertexMode
        {
            CreateDebugObject,
        }

        public void Create(Vector3[] vertices, int[] indices)
        {
            var bounds = Utils.CreateBounds(vertices);
            var size = bounds.size;
            mTriangleQuadTree = new TriangleQuadTree();
            mTriangleQuadTree.Create("navmesh debugger", Vector2.zero, new Vector2(size.x, size.z), vertices, indices, 0, indices != null ? indices.Length - 1 : 0, 5, true, false, false);
        }

        public void PickVertex(Vector3 pos, float radius, PickVertexMode mode)
        {
            VertexInfo info;
            if (mTriangleQuadTree.GetHitVertex(pos.x, pos.z, radius, out info))
            {
                if (mode == PickVertexMode.CreateDebugObject)
                {
                    var obj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                    obj.name = "vertex_" + info.index;
                    obj.transform.position = info.position;
                    obj.transform.localScale = Vector3.one * radius;
                    obj.transform.parent = gameObject.transform;
                    Font ArialFont = (Font)Resources.GetBuiltinResource(typeof(Font), "Arial.ttf");
                    var textObj = new GameObject();
                    var renderer = textObj.AddComponent<MeshRenderer>();
                    renderer.sharedMaterial = ArialFont.material;
                    var textMesh = textObj.AddComponent<TextMesh>();
                    textMesh.text = info.index.ToString();
                    textMesh.fontSize = 32;
                    textObj.transform.parent = obj.transform;
                    textObj.transform.position = obj.transform.position;
                    textObj.transform.rotation = Quaternion.Euler(70, 0, 0);
                }
            }
        }

        void Update()
        {
            transform.position = Vector3.zero;
            transform.rotation = Quaternion.identity;
            transform.localScale = Vector3.one;
        }

        TriangleQuadTree mTriangleQuadTree;
        public float pickRadius = 5.0f;
    }
}


#endif