﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //不受unity mesh顶点数限制的mesh显示
    public class BigMeshViewer
    {
        public void OnDestroy()
        {
            if (mMeshObject != null)
            {
                GameObject.DestroyImmediate(mMaterial);
                mMaterial = null;
                GameObject.DestroyImmediate(mMeshObject);
                mMeshObject = null;
            }
        }

        public void Create(GameObject parentObject, string name, Vector3[] vertices, int[] indices, bool hideGameObject, Color color, float meshHeight = 0.6f)
        {
            OnDestroy();
            if (vertices == null)
            {
                return;
            }

            int submeshIndexCount = 30000;
            Mesh mesh = new Mesh();
            mesh.vertices = vertices;
            mesh.indexFormat = UnityEngine.Rendering.IndexFormat.UInt32;
            int k = 0;
            int s = 0;
            List<int[]> allSubmeshIndices = new List<int[]>();
            while (true)
            {
                int left = indices.Length - k;
                int indexCount = Mathf.Min(left, submeshIndexCount);
                int[] submeshIndices = new int[indexCount];
                for (int h = 0; h < indexCount; ++h)
                {
                    submeshIndices[h] = indices[h + k];
                }
                allSubmeshIndices.Add(submeshIndices);
                k += indexCount;

                if (left <= submeshIndexCount)
                {
                    break;
                }
                ++s;
            }

            mesh.subMeshCount = allSubmeshIndices.Count;
            for (int i = 0; i < allSubmeshIndices.Count; ++i)
            {
                mesh.SetIndices(allSubmeshIndices[i], MeshTopology.Triangles, i);
            }

            mMeshObject = new GameObject(name);
            mMeshObject.transform.position = new Vector3(0, meshHeight, 0);
            if (hideGameObject)
            {
                Utils.HideGameObject(mMeshObject);
            }
            if (parentObject != null)
            {
                mMeshObject.transform.SetParent(parentObject.transform);
            }

            MeshRenderer renderer = mMeshObject.AddComponent<MeshRenderer>();
            MeshFilter filter = mMeshObject.AddComponent<MeshFilter>();
            var shader = Shader.Find("SLGMaker/ColorTransparent");
            mMaterial = new Material(shader);
            mMaterial.color = color;
            Material[] materials = new Material[allSubmeshIndices.Count];
            for (int i = 0; i < materials.Length; ++i)
            {
                materials[i] = mMaterial;
            }
            renderer.sharedMaterials = materials;
            filter.sharedMesh = mesh;
        }

        public Mesh mesh
        {
            get
            {
                if (mMeshObject != null)
                {
                    return mMeshObject.GetComponent<MeshFilter>().sharedMesh;
                }
                return null;
            }
        }
        public bool active
        {
            get
            {
                if (mMeshObject != null)
                {
                    return mMeshObject.activeSelf;
                }
                return false;
            }
            set
            {
                if (mMeshObject != null)
                {
                    mMeshObject.SetActive(value);
                }
            }
        }

        public GameObject gameObject { get { return mMeshObject; } }

        GameObject mMeshObject;
        Material mMaterial;
    }
}