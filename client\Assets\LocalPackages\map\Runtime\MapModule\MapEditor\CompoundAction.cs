﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;

namespace TFW.Map {
    [Black]
    public class CompoundAction : EditorAction {
        public CompoundAction(string description)
        {
            mDescription = description;
        }

        public override void OnDestroy() {
            foreach (var act in mActions) {
                act.OnDestroy();
            }
        }

        public void Add(EditorAction act, bool execute = false) {
            if (mActions.Contains(act) == false) {
                mActions.Add(act);
                if (execute) {
                    act.Do();
                }
            }
        }

        public bool IsEmpty() {
            return mActions.Count == 0;
        }

        public override bool Do() {
            bool suc = true;
            for (int i = 0; i < mActions.Count; ++i) {
                suc &= mActions[i].Do();
            }
            return suc;
        }

        public override bool Undo() {
            bool suc = true;
            for (int i = mActions.Count - 1; i >= 0; --i) {
                suc &= mActions[i].Undo();
            }
            return suc;
        }

        public override bool IsUndoable() {
            bool undoable = false;
            for (int i = 0; i < mActions.Count; ++i) {
                undoable |= mActions[i].IsUndoable();
            }
            return undoable;
        }

        public override string description { get { return mDescription; } }

        List<EditorAction> mActions = new List<EditorAction>();

        string mDescription;
    };
}

#endif