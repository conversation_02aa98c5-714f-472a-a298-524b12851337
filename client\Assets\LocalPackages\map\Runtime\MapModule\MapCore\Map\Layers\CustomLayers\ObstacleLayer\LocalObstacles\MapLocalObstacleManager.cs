﻿ 



 
 



//create by wzw on 2019.5.8

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //分块管理地图的障碍物数据
    public class MapLocalObstacleManager
    {
        public MapLocalObstacleManager(Map map, float mapWidth, float mapHeight, float regionWidth, float regionHeight, int[] tiles, Dictionary<int, KeyValuePair<Vector3[], int[]>> prefabObstacles, string obstacleMaterialPath, bool circleMap)
        {
            mMap = map;
            obstacleMaterialPath = MapModule.defaultObstacleMaterial;
            mObstacleViewer = new MapLocalObstacleViewer(obstacleMaterialPath);

            mIsCircleMap = circleMap;
            mMapWidth = mapWidth;
            mMapHeight = mapHeight;
            mRegionWidth = regionWidth;
            mRegionHeight = regionHeight;
            mHorizontalRegionCount = Mathf.CeilToInt(mapWidth / regionWidth);
            mVerticalRegionCount = Mathf.CeilToInt(mapHeight / regionHeight);
            mTiles = tiles;

            if (mTiles != null && prefabObstacles.Count > 0)
            {
                //根据tiles和prefabObstacles的数据创建障碍物
                mPrefabTileDatas = new Dictionary<int, MapLocalObstacleTileData>();
                for (int i = 0; i < mTiles.Length; ++i)
                {
                    if (mTiles[i] > 0)
                    {
                        if (mPrefabTileDatas.ContainsKey(mTiles[i]) == false)
                        {
                            KeyValuePair<Vector3[], int[]> obstacle;
                            bool found = prefabObstacles.TryGetValue(mTiles[i], out obstacle);
                            Debug.Assert(found);
                            var tileData = new MapLocalObstacleTileData(obstacle.Key, obstacle.Value, PrefabOutlineType.ObjectPlacementObstacle, regionWidth);
                            mPrefabTileDatas[mTiles[i]] = tileData;
                        }
                    }
                }
                CreateQuadTrees();
            }
        }

        public void OnDestroy()
        {
            mQuadTrees = null;
            if (mPrefabTileDatas != null)
            {
                foreach (var p in mPrefabTileDatas)
                {
                    p.Value.OnDestroy();
                }
                mPrefabTileDatas = null;
            }

            mObstacleViewer.OnDestroy();
            mObstacleViewer = null;
        }

#if UNITY_EDITOR
        //在编辑器中创建数据,在游戏中加载
        //创建障碍物数据,对每个prefab创建一个障碍物数据,注意创建障碍物时必须在lod0上,考虑在编辑器中禁止lod的改变
        public void CreateObstacles(EditorGridModelLayer layer, PrefabOutlineType outlineType)
        {
            //create a obstacles mesh first
            var mapWidth = mMap.mapWidth;
            var mapHeight = mMap.mapHeight;
            var map = Map.currentMap as EditorMap;
            var meshies = Utils.CreateNavMesh(LayerTypeMask.kGridModelLayer, 1, 1, outlineType, false, NavigationCreateMode.CreateLandObstacles, false, true, false, map.removeSameHoles);
            //create a quad tree from the mesh
            TriangleQuadTree wholeTree = new TriangleQuadTree();
            Vector3[] vertices = null;
            int[] indices = null;
            if (meshies != null && meshies[0] != null)
            {
                //temp code, merge all submeshies
                vertices = meshies[0].vertices;
                indices = meshies[0].indices;

                wholeTree.Create("local obstacles", Vector2.zero, new Vector2(mapWidth, mapHeight), vertices, indices, 0, indices != null ? indices.Length - 1 : 0, 4, false, false, false);
            }

            //foreach region create the quad tree
            mTiles = new int[mHorizontalRegionCount * mVerticalRegionCount];
            mPrefabTileDatas = new Dictionary<int, MapLocalObstacleTileData>();

            for (int y = 0; y < mVerticalRegionCount; ++y)
            {
                for (int x = 0; x < mHorizontalRegionCount; ++x)
                {
                    var objData = layer.GetObjectData(x, y);
                    if (objData != null)
                    {
                        var templateID = objData.GetModelTemplateID();
                        //record which prefab is used in this region
                        mTiles[y * mHorizontalRegionCount + x] = templateID;

                        if (mPrefabTileDatas.ContainsKey(templateID) == false)
                        {
                            //create a tile data for the prefab
                            //find triangles which are intersected with this region
                            float startX = x * regionWidth;
                            float startZ = y * regionHeight;
                            var intersectedTriangles = wholeTree.GetIntersectedTriangles(startX, startZ, startX + regionWidth, startZ + regionHeight);
                            var intersectedTriangleIndices = new int[intersectedTriangles.Count * 3];
                            for (int k = 0; k < intersectedTriangles.Count; ++k)
                            {
                                intersectedTriangleIndices[k * 3] = intersectedTriangles[k].x;
                                intersectedTriangleIndices[k * 3 + 1] = intersectedTriangles[k].y;
                                intersectedTriangleIndices[k * 3 + 2] = intersectedTriangles[k].z;
                            }
                            var tileData = new MapLocalObstacleTileData(startX, startZ, intersectedTriangleIndices, wholeTree.vertices, outlineType, regionWidth);
                            mPrefabTileDatas[templateID] = tileData;
                        }

                    }
                }
            }

            CreateQuadTrees();
        }
#endif

        //create obstacle quad tree for every prefab
        void CreateQuadTrees()
        {
            mQuadTrees = new Dictionary<int, TriangleQuadTree>();

            foreach (var p in mPrefabTileDatas)
            {
                var tree = new TriangleQuadTree();
                var tile = p.Value;
                tree.Create("local obstacles", Vector2.zero, new Vector2(mRegionWidth, mRegionHeight), tile.vertices, tile.triangles, 0, tile.triangles != null ? tile.triangles.Length - 1 : 0, 4, false, false, false);
                mQuadTrees[p.Key] = tree;
            }
        }

        //check if a circle is intersected with any obstacle
        public bool IsIntersectedWithObstacles(float centerX, float centerZ, float radius, bool multithread)
        {
            if (mTiles == null)
            {
                return false;
            }

            if (mIsCircleMap)
            {
                float dx = centerX - mMapWidth * 0.5f;
                float dz = centerZ - mMapHeight * 0.5f;
                float validRadius = mMapWidth * 0.5f - radius;
                if (dx * dx + dz * dz > validRadius * validRadius)
                {
                    return true;
                }
            }
            else
            {
                if (centerX > mMapWidth - radius || centerX < radius || centerZ > mMapHeight - radius || centerZ < radius)
                {
                    //out of map bounds
                    return true;
                }
            }

            List<Vector2Int> intersectedRegions = null;
            if (multithread)
            {
                intersectedRegions = new List<Vector2Int>();
            }
            else
            {
                intersectedRegions = mIntersectedRegions;
                mIntersectedRegions.Clear();
            }
            GetIntersectedRegions(centerX, centerZ, radius, intersectedRegions);

            for (int i = 0; i < intersectedRegions.Count; ++i)
            {
                float localX = centerX - intersectedRegions[i].x * mRegionWidth;
                float localZ = centerZ - intersectedRegions[i].y * mRegionHeight;

                bool intersected = IsIntersectedWithRegionObstacles(localX, localZ, radius, intersectedRegions[i]);
                if (intersected)
                {
                    return true;
                }
            }

            return false;
        }

        //check if a convex polygon intersected with map obstacles
        public bool IsIntersectedWithObstacles(List<Vector3> convexPolygons, Rect polygonBounds)
        {
            if (mTiles == null)
            {
                return false;
            }

            mIntersectedRegions.Clear();

            var minX = polygonBounds.xMin;
            var maxX = polygonBounds.xMax;
            var minZ = polygonBounds.yMin;
            var maxZ = polygonBounds.yMax;
            GetIntersectedRegions(minX, minZ, maxX, maxZ, mIntersectedRegions);

            for (int i = 0; i < mIntersectedRegions.Count; ++i)
            {
                mLocalConvexPolygonVertices.Clear();
                Rect localPolygonBounds;
                CreateLocalPolygonVertices(convexPolygons, mIntersectedRegions[i].x * mRegionWidth, mIntersectedRegions[i].y * mRegionHeight, mLocalConvexPolygonVertices, out localPolygonBounds);

                bool intersected = IsIntersectedWithRegionObstacles(mLocalConvexPolygonVertices, localPolygonBounds, mIntersectedRegions[i]);
                if (intersected)
                {
                    return true;
                }
            }

            return false;
        }

        void CreateLocalPolygonVertices(List<Vector3> polygonWorldVertices, float regionStartX, float regionStartZ, List<Vector3> polygonLocalVertices, out Rect polygonLocalBounds)
        {
            var offset = new Vector3(regionStartX, 0, regionStartZ);
            float minX = float.MaxValue;
            float minZ = float.MaxValue;
            float maxX = float.MinValue;
            float maxZ = float.MinValue;
            for (int i = 0; i < polygonWorldVertices.Count; ++i)
            {
                var local = polygonWorldVertices[i] - offset;
                polygonLocalVertices.Add(local);

                if (local.x < minX)
                {
                    minX = local.x;
                }
                if (local.z < minZ)
                {
                    minZ = local.z;
                }
                if (local.x > maxX)
                {
                    maxX = local.x;
                }
                if (local.z > maxZ)
                {
                    maxZ = local.z;
                }
            }

            polygonLocalBounds = new Rect(minX, minZ, maxX - minX, maxZ - minZ);
        }

        //calculate region index from position
        Vector2Int GetRegionIndex(float posX, float posZ)
        {
            int x = Mathf.FloorToInt(posX / mRegionWidth);
            int y = Mathf.FloorToInt(posZ / mRegionHeight);

            return new Vector2Int(x, y);
        }

        void GetIntersectedRegions(float centerX, float centerZ, float radius, List<Vector2Int> intersectedRegions)
        {
            Vector2Int curRegionIndex = GetRegionIndex(centerX, centerZ);
            intersectedRegions.Add(curRegionIndex);

            bool isInside = IsInsideRegion(curRegionIndex, centerX, centerZ, radius);
            if (!isInside)
            {
                var minX = centerX - radius;
                var minZ = centerZ - radius;
                var maxX = centerX + radius;
                var maxZ = centerZ + radius;

                var startRegion = GetRegionIndex(minX, minZ);
                var endRegion = GetRegionIndex(maxX, maxZ);

                for (int y = startRegion.y; y <= endRegion.y; ++y)
                {
                    for (int x = startRegion.x; x <= endRegion.x; ++x)
                    {
                        if (y != curRegionIndex.y || x != curRegionIndex.x)
                        {
                            intersectedRegions.Add(new Vector2Int(x, y));
                        }
                    }
                }
            }
        }

        void GetIntersectedRegions(float minX, float minZ, float maxX, float maxZ, List<Vector2Int> intersectedRegions)
        {
            Vector2Int curRegionIndex = GetRegionIndex((maxX - minX) * 0.5f, (maxZ - minZ) * 0.5f);
            intersectedRegions.Add(curRegionIndex);

            bool isInside = IsInsideRegion(curRegionIndex, minX, minZ, maxX, maxZ);
            if (!isInside)
            {
                var startRegion = GetRegionIndex(minX, minZ);
                var endRegion = GetRegionIndex(maxX, maxZ);

                for (int y = startRegion.y; y <= endRegion.y; ++y)
                {
                    for (int x = startRegion.x; x <= endRegion.x; ++x)
                    {
                        if (y != curRegionIndex.y || x != curRegionIndex.x)
                        {
                            intersectedRegions.Add(new Vector2Int(x, y));
                        }
                    }
                }
            }
        }

        //是否在去掉radius大小的内部矩形内
        bool IsInsideRegion(Vector2 regionIndex, float centerX, float centerZ, float radius)
        {
            var regionCenter = new Vector2((regionIndex.x + 0.5f) * mRegionWidth, (regionIndex.y + 0.5f) * mRegionHeight);
            float insideWidth = mRegionWidth * 0.5f - radius;
            float insideHeight = mRegionHeight * 0.5f - radius;
            var minX = regionCenter.x - insideWidth;
            var minZ = regionCenter.y - insideHeight;
            var maxX = regionCenter.x + insideWidth;
            var maxZ = regionCenter.y + insideHeight;

            if (centerX >= minX && centerX <= maxX &&
                centerZ >= minZ && centerZ <= maxZ)
            {
                return true;
            }

            return false;
        }

        bool IsInsideRegion(Vector2 regionIndex, float objectMinX, float objectMinZ, float objectMaxX, float objectMaxZ)
        {
            var regionCenter = new Vector2((regionIndex.x + 0.5f) * mRegionWidth, (regionIndex.y + 0.5f) * mRegionHeight);
            Rect objectRect = new Rect(objectMinX, objectMinZ, objectMaxX - objectMinX, objectMaxZ - objectMinZ);
            Rect regionRect = new Rect(regionCenter.x - mRegionWidth * 0.5f, regionCenter.y - mRegionHeight * 0.5f, mRegionWidth, mRegionHeight);
            return Utils.FullContains(regionRect, objectRect);
        }

        bool IsIntersectedWithRegionObstacles(float centerX, float centerZ, float radius, Vector2Int regionIndex)
        {
            int idx = regionIndex.y * mHorizontalRegionCount + regionIndex.x;
            if (idx < 0 || idx >= mTiles.Length)
            {
                Debug.Log($"invalid idx {idx}, {regionIndex}, {mHorizontalRegionCount}, {mTiles.Length}");
                return false;
            }
            var tileID = mTiles[idx];
            if (tileID == 0)
            {
                return false;
            }
            TriangleQuadTree quadTree;
            mQuadTrees.TryGetValue(tileID, out quadTree);
            if (quadTree != null)
            {
                return quadTree.IsTriangleIntersectedWithCircle(centerX, centerZ, radius);
            }
            return false;
        }

        bool IsIntersectedWithRegionObstacles(List<Vector3> convexPolygonVertices, Rect polygonBounds, Vector2Int regionIndex)
        {
            int idx = regionIndex.y * mHorizontalRegionCount + regionIndex.x;
            if (idx >= mTiles.Length || idx < 0)
            {
                return false;
            }
            var tileID = mTiles[idx];
            if (tileID == 0)
            {
                return false;
            }
            TriangleQuadTree quadTree;
            mQuadTrees.TryGetValue(tileID, out quadTree);
            if (quadTree != null)
            {
                return quadTree.IsTriangleIntersectedWithConvexPolygon(convexPolygonVertices, polygonBounds);
            }
            return false;
        }

        //获取模型对应的障碍物
        public GameObject RequireObstacleView(int modelTemplateID)
        {
            if (mPrefabTileDatas != null)
            {
                MapLocalObstacleTileData tileData;
                mPrefabTileDatas.TryGetValue(modelTemplateID, out tileData);
                if (tileData != null)
                {
                    var view = mObstacleViewer.Require(modelTemplateID, tileData);
                    return view;
                }
            }
            return null;
        }

        public void ReleaseObstacleView(int modelTemplateID, GameObject gameObject)
        {
            if (gameObject != null)
            {
                gameObject.SetActive(false);
                gameObject.transform.SetParent(null);
                mObstacleViewer.Release(modelTemplateID, gameObject);
            }
        }

        public int horizontalRegionCount { get { return mHorizontalRegionCount; } }
        public int verticalRegionCount { get { return mVerticalRegionCount; } }
        public float regionWidth { get { return mRegionWidth; } }
        public float regionHeight { get { return mRegionHeight; } }
        public int[] tiles { get { return mTiles; } }
        public Dictionary<int, MapLocalObstacleTileData> prefabTileDatas { get { return mPrefabTileDatas; } }
        public string obstacleMaterialPath { get { return mObstacleViewer.obstacleMaterialPath; } }

        int[] mTiles;
        Dictionary<int, MapLocalObstacleTileData> mPrefabTileDatas;
        //每个前景块prefab的四叉树,用于快速判断npc和障碍物是否相交
        Dictionary<int, TriangleQuadTree> mQuadTrees;
        MapLocalObstacleViewer mObstacleViewer;
        float mRegionWidth;
        float mRegionHeight;
        float mMapWidth;
        float mMapHeight;
        int mHorizontalRegionCount;
        int mVerticalRegionCount;
        bool mIsCircleMap;
        Map mMap;
        //中间计算变量,避免gc
        List<Vector2Int> mIntersectedRegions = new List<Vector2Int>();
        //中间计算变量,避免gc
        List<Vector3> mLocalConvexPolygonVertices = new List<Vector3>();
    }
}
