﻿#if UNITY_EDITOR

using UnityEditor;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public static class DecorationPrefabCreator
    {
        public static void Create(bool ignoreInactive, string filePath)
        {
            var objects = Selection.gameObjects;
            List<GameObject> prefabs = new List<GameObject>();
            for (int i = 0; i < objects.Length; ++i)
            {
                if (!objects[i].activeSelf && ignoreInactive)
                {
                    Debug.Log($"ignore inactive object {objects[i].name}");
                    continue;
                }

                var prefab = PrefabUtility.GetCorrespondingObjectFromSource(objects[i]);
                string childPrefabAssetPath = "";
                if (prefab != null)
                {
                    childPrefabAssetPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefab);
                }
                if (!string.IsNullOrEmpty(childPrefabAssetPath))
                {
                    if (objects[i].transform.parent == null)
                    {
                        var obj = (GameObject)PrefabUtility.InstantiatePrefab(prefab);
                        obj.transform.localPosition = objects[i].transform.localPosition;
                        obj.transform.localRotation = objects[i].transform.localRotation;
                        obj.transform.localScale = objects[i].transform.localScale;
                        obj.tag = objects[i].tag;
                        obj.layer = objects[i].layer;
                        prefabs.Add(obj);
                    }
                }
                else
                {
                    Debug.LogError($"ignore none prefab object {objects[i].name}");
                }
            }

            var root = new GameObject();
            for (int i = 0; i < prefabs.Count; ++i)
            {
                prefabs[i].transform.SetParent(root.transform, true);
            }
            PrefabUtility.SaveAsPrefabAsset(root, filePath);
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            GameObject.DestroyImmediate(root);
        }
    }
}


#endif