﻿// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

Shader "TFW/Particles Additive Clip" {
Properties {
    _TintColor ("Tint Color", Color) = (0.5,0.5,0.5,0.5)
    _MainTex ("Particle Texture", 2D) = "white" {}
    _InvFade ("Soft Particles Factor", Range(0.01,3.0)) = 1.0
}

Category {
    Tags { "Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent" "PreviewType"="Plane" }
    Blend SrcAlpha One
    ColorMask RGB
    Cull Off Lighting Off ZWrite Off

    SubShader {
        Pass {

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            #pragma multi_compile_particles
            #pragma multi_compile_fog

            #include "UnityCG.cginc"
			// UnityUI.cginc中包含了UnityGet2DClipping的实现
			#include "UnityUI.cginc"

            sampler2D _MainTex;
            fixed4 _TintColor;

            struct appdata_t {
                float4 vertex : POSITION;
                fixed4 color : COLOR;
                float2 texcoord : TEXCOORD0;
                //UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
				// 将texcoord扩展为float4，会使shader代码编译错误，这时只要将错误的位置改为texcoord.xy即可
                float4 texcoord : TEXCOORD0;
                UNITY_FOG_COORDS(1)
                #ifdef SOFTPARTICLES_ON
                float4 projPos : TEXCOORD2;
                #endif

				
                //UNITY_VERTEX_OUTPUT_STEREO
            };

            float4 _MainTex_ST;

            v2f vert (appdata_t v)
            {
                v2f o;
                //UNITY_SETUP_INSTANCE_ID(v);
                //UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                o.vertex = UnityObjectToClipPos(v.vertex);
                #ifdef SOFTPARTICLES_ON
                o.projPos = ComputeScreenPos (o.vertex);
                COMPUTE_EYEDEPTH(o.projPos.z);
                #endif
                o.color = v.color;
                UNITY_TRANSFER_FOG(o,o.vertex);

				//o.texcoord = TRANSFORM_TEX(v.texcoord,_MainTex);
				// 原有uv坐标保存到texcoord.xy中，world space中的xy坐标保存到o.texcoord.zw中
				o.texcoord.xy = TRANSFORM_TEX(v.texcoord.xy,_MainTex).xy;
				o.texcoord.zw = mul(unity_ObjectToWorld, v.vertex).xy;

                return o;
            }

            UNITY_DECLARE_DEPTH_TEXTURE(_CameraDepthTexture);
            float _InvFade;

			// C#代码需要传入的裁剪区域变量, 这里我们增加一个变量（_UseClipRect）用来标记是否需要裁剪
			float4 _ClipRect;
			float _UseClipRect;

            fixed4 frag (v2f i) : SV_Target
            {
                #ifdef SOFTPARTICLES_ON
                float sceneZ = LinearEyeDepth (SAMPLE_DEPTH_TEXTURE_PROJ(_CameraDepthTexture, UNITY_PROJ_COORD(i.projPos)));
                float partZ = i.projPos.z;
                float fade = saturate (_InvFade * (sceneZ-partZ));
                i.color.a *= fade;
                #endif

                fixed4 col = 2.0f * i.color * _TintColor * tex2D(_MainTex, i.texcoord);
                col.a = saturate(col.a); // alpha should not have double-brightness applied to it, but we can't fix that legacy behavior without breaking everyone's effects, so instead clamp the output to get sensible HDR behavior (case 967476)

                UNITY_APPLY_FOG_COLOR(i.fogCoord, col, fixed4(0,0,0,0)); // fog towards black due to our blend mode

				// fragment shader非常简单，我们只需要在最后，对fragment进行裁剪即可，
				// UnityGet2DClipping这个函数实现了判断2D空间中的一点是否在一个矩形区域中
				// lerp函数用来标记是否需要进行裁剪，当_UseClipRect值为1时表示裁剪
				float c = UnityGet2DClipping(i.texcoord.zw, _ClipRect);
				col.a = lerp(col.a, c * col.a, _UseClipRect);
                return col;
            }
            ENDCG
        }
    }
}
}
