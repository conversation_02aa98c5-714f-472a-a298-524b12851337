%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Reconstruct World Position From Depth
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity
    Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18713\n-2016;79;1188;819;1091.131;592.3694;1;True;False\nNode;AmplifyShaderEditor.ComponentMaskNode;44;-1216,-480;Inherit;False;True;True;True;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;49;-285.0508,-414.7967;Inherit;False;FLOAT4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;1;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CameraToWorldMatrix;48;-349.0508,-494.7968;Inherit;False;0;1;FLOAT4x4;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;50;-125.0509,-462.7967;Inherit;False;2;2;0;FLOAT4x4;0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1;False;1;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;73;-794.1306,-248.3694;Inherit;False;float3
    result = In@$#if !defined(ASE_SRP_VERSION) || ASE_SRP_VERSION <= 70301 || ASE_SRP_VERSION
    >= 80301 $result *= float3(1,1,-1)@$#endif$return result@;3;False;1;True;In;FLOAT3;0,0,0;In;;Inherit;False;InvertDepthDirHD;True;False;0;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;72;-816,-432;Inherit;False;float3
    result = In@$#if !defined(ASE_SRP_VERSION) || ASE_SRP_VERSION <= 70301$result
    *= float3(1,1,-1)@$#endif$return result@;3;False;1;True;In;FLOAT3;0,0,0;In;;Inherit;False;InvertDepthDir;True;False;0;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;37;-2368,-144;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScreenPosInputsNode;70;-2896,-304;Inherit;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.DynamicAppendNode;42;-1552,-384;Inherit;False;FLOAT4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;1;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;64;-2272,-384;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleDivideOpNode;46;-976,-432;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CameraProjectionNode;41;-1664,-480;Inherit;False;unity_CameraInvProjection;0;1;FLOAT4x4;0\nNode;AmplifyShaderEditor.ScreenDepthNode;69;-2576,-208;Inherit;False;1;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;40;-1776,-384;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT;2;False;2;FLOAT;-1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StaticSwitch;38;-2208,-208;Float;False;Property;_Keyword0;Keyword
    0;3;0;Fetch;True;0;0;0;False;0;False;0;0;0;False;UNITY_REVERSED_Z;Toggle;2;Key0;Key1;Fetch;False;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;43;-1376,-416;Inherit;False;2;2;0;FLOAT4x4;0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1;False;1;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DynamicAppendNode;39;-1936,-384;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ComponentMaskNode;45;-1216,-368;Inherit;False;False;False;False;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;67;-2576,-384;Inherit;False;Non
    Stereo Screen Pos;-1;;3;1731ee083b93c104880efc701e11b49b;0;1;23;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;74;-545.1306,-416.3694;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;68,-460;Inherit;False;True;-1;XYZW;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nWireConnection;44;0;43;0\nWireConnection;49;0;74;0\nWireConnection;50;0;48;0\nWireConnection;50;1;49;0\nWireConnection;73;0;46;0\nWireConnection;72;0;46;0\nWireConnection;37;0;69;0\nWireConnection;42;0;40;0\nWireConnection;64;0;67;0\nWireConnection;46;0;44;0\nWireConnection;46;1;45;0\nWireConnection;69;0;70;0\nWireConnection;40;0;39;0\nWireConnection;38;1;69;0\nWireConnection;38;0;37;0\nWireConnection;43;0;41;0\nWireConnection;43;1;42;0\nWireConnection;39;0;64;0\nWireConnection;39;1;64;1\nWireConnection;39;2;38;0\nWireConnection;45;0;43;0\nWireConnection;67;23;70;0\nWireConnection;74;0;72;0\nWireConnection;74;3;72;0\nWireConnection;74;1;72;0\nWireConnection;74;2;73;0\nWireConnection;0;0;50;0\nASEEND*/\n//CHKSM=F5D5E2CF41914D43F8623BAEFD01D1AE6F20E602"
  m_functionName: 
  m_description: Reconstructs world position from the depth of the scene. If depth
    is unconnected a default screen depth will be calculated. For best results, zwrite
    should be OFF and ztest should be ALWAYS.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
