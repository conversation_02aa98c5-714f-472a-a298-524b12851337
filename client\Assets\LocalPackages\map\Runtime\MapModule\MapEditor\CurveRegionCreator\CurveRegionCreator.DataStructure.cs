﻿#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class CurveRegionCreator
    {
        class Edge
        {
            public Edge(int id, Vector3 start, Vector3 end, int selfRegionID, int neighbourRegionID)
            {
                this.id = id;
                this.start = start;
                this.end = end;
                this.selfRegionID = selfRegionID;
                this.neighbourRegionID = neighbourRegionID;
            }

            public bool isHorizontal { get { return Mathf.Approximately(start.z, end.z); } }

            public Vector3 start;
            public Vector3 end;
            public bool removed = false;
            public int selfRegionID = 0;
            public int neighbourRegionID = 0;
            public int id;
            //与该edge的起点相连的edge
            public Edge connectedToStart;
            //与该edge的终点相连的edge
            public Edge connectedToEnd;
        }

        //将outline根据相邻的区域分段
        class SharedEdgeWithNeighbourTerritroy
        {
            public SharedEdgeWithNeighbourTerritroy(List<Vector3> controlPoints, int selfRegionID, int neighbourRegionID)
            {
                this.controlPointPositions = controlPoints;
                this.selfRegionID = selfRegionID;
                this.neighbourRegionID = neighbourRegionID;
            }

            public void OnDestroy()
            {
                Utils.DestroyObject(gameObject);
            }

            public void RemoveControlPoint(Vector3 pos)
            {
                for (int i = 0; i < controlPointPositions.Count; ++i)
                {
                    if (controlPointPositions[i] == pos)
                    {
                        controlPointPositions.RemoveAt(i);
                        break;
                    }
                }
            }

            public void ConvertToControlPoints(List<ControlPoint> controlPointsReference)
            {
                controlPoints = new List<ControlPoint>();
                for (int i = 0; i < controlPointPositions.Count; ++i)
                {
                    var controlPoint = GetControlPoint(controlPointsReference, controlPointPositions[i]);
                    Debug.Assert(controlPoint != null);
                    controlPoints.Add(controlPoint);
                }
            }

            ControlPoint GetControlPoint(List<ControlPoint> controlPointsReference, Vector3 pos)
            {
                for (int i = 0; i < controlPointsReference.Count; ++i)
                {
                    if (controlPointsReference[i].position == pos)
                    {
                        return controlPointsReference[i];
                    }
                }
                return null;
            }

            public bool IsEndPoint(Vector3 pos)
            {
                return controlPointPositions[controlPointPositions.Count - 1] == pos;
            }

            public int selfRegionID;
            public int neighbourRegionID;
            //control point
            List<Vector3> controlPointPositions;
            public List<ControlPoint> controlPoints;
            //曲线点
            public List<Vector3> evaluatedVertices;
            public GameObject gameObject;
            public string prefabPath;
            public string meshPath;
            public int fixedEdgePointCount = 0;
        }

        public class EdgeAssetInfo
        {
            public EdgeAssetInfo(int territoryID, int neighbourTerritoyID, string prefabPath, Material material)
            {
                this.territoryID = territoryID;
                this.neighbourTerritoyID = neighbourTerritoyID;
                this.prefabPath = prefabPath;
                this.material = material;
            }

            public int territoryID;
            public int neighbourTerritoyID;
            public string prefabPath;
            public Material material;
        }

        class Territory
        {
            public enum ObjectType
            {
                Region,
                InnerOutline,
                CurveOutline,

                Count,
            }

            public Territory(List<SharedEdgeWithNeighbourTerritroy> sharedEdges, List<Edge> edges, List<Vector3> outline, int regionID, Color color)
            {
                Debug.Assert(sharedEdges.Count > 0);
                mOutlineSharedEdges = sharedEdges;
                mRegionID = regionID;
                mEdges = edges;
                mOutline = outline;
                mColor = color;
            }

            public void OnDestroy()
            {
                for (int i = 0; i < mControlPoints.Count; ++i)
                {
                    mControlPoints[i].OnDestroy();
                }

                foreach (var edge in mOutlineSharedEdges)
                {
                    edge.OnDestroy();
                }

                HideLineAndMesh();
            }

            public void CreateSharedEdgeControlPoints()
            {
                //将shared edge的坐标转换成control point的引用
                for (int i = 0; i < mOutlineSharedEdges.Count; ++i)
                {
                    mOutlineSharedEdges[i].ConvertToControlPoints(controlPoints);
                }
            }

            public void HideLineAndMesh()
            {
                HideLine();
                HideMesh();
            }

            public void HideLine()
            {
                Utils.DestroyObject(mObjects[(int)ObjectType.InnerOutline]);
                mObjects[(int)ObjectType.InnerOutline] = null;
                Utils.DestroyObject(mObjects[(int)ObjectType.CurveOutline]);
                mObjects[(int)ObjectType.CurveOutline] = null;   
            }

            public void HideMesh()
            {
                if (mObjects[(int)ObjectType.Region] != null)
                {
                    mObjects[(int)ObjectType.Region].SetActive(false);
                }
                for (int i = 0; i < mOutlineSharedEdges.Count; ++i)
                {
                    if (mOutlineSharedEdges[i].gameObject != null)
                    {
                        mOutlineSharedEdges[i].gameObject.SetActive(false);
                    }
                }
            }

            public void HideRegionMesh()
            {
                if (mObjects[(int)ObjectType.Region] != null)
                {
                    mObjects[(int)ObjectType.Region].SetActive(false);
                }
            }

            public void ShowMesh()
            {
                if (mObjects[(int)ObjectType.Region])
                {
                    mObjects[(int)ObjectType.Region].SetActive(true);
                }
                for (int i = 0; i < mOutlineSharedEdges.Count; ++i)
                {
                    if (mOutlineSharedEdges[i].gameObject != null)
                    {
                        mOutlineSharedEdges[i].gameObject.SetActive(true);
                    }
                }
            }

            public void RemoveVertices(List<Vector3> vertices)
            {
                for (int i = 0; i < vertices.Count; ++i)
                {
                    mOutline.Remove(vertices[i]);

                    for (int k = 0; k < mOutlineSharedEdges.Count; ++k)
                    {
                        mOutlineSharedEdges[k].RemoveControlPoint(vertices[i]);
                    }
                }
            }

            public void HideTangent()
            {
                for (int i = 0; i < mControlPoints.Count; ++i)
                {
                    mControlPoints[i].ShowTangent(false);
                }
            }

            public void ShowTangent(int index)
            {
                if (index >= 0 && index < mControlPoints.Count)
                {
                    mControlPoints[index].ShowTangent(true);
                }
            }

            public void SetGameObject(ObjectType type, GameObject obj)
            {
                int idx = (int)type;
                if (mObjects[idx] != null)
                {
                    Utils.DestroyObject(mObjects[idx]);
                }
                mObjects[idx] = obj;
            }

            public GameObject GetGameObject(ObjectType type)
            {
                int idx = (int)type;
                return mObjects[idx];
            }

            public SharedEdgeWithNeighbourTerritroy GetSharedEdge(int territoryID, int neighbourTerritoryID)
            {
                foreach (var edge in mOutlineSharedEdges)
                {
                    if (edge.selfRegionID == territoryID && edge.neighbourRegionID == neighbourTerritoryID)
                    {
                        return edge;
                    }
                }
                return null;
            }

            public List<Edge> edges { get { return mEdges; } }
            public List<Vector3> outline { get { return mOutline; } set { mOutline = value; } }
            public List<Vector3> innerOutline { get { return mInnerOutline; } set { mInnerOutline = value; } }
            public List<ControlPoint> controlPoints { get { return mControlPoints; } set { mControlPoints = value; } }
            public List<SharedEdgeWithNeighbourTerritroy> sharedEdges { get { return mOutlineSharedEdges; } set { mOutlineSharedEdges = value; } }
            public int regionID { get { return mRegionID; } }
            public Color color { get { return mColor; } }
            public string prefabPath { get { return mPrefabPath; } set { mPrefabPath = value; } }
            
            List<Edge> mEdges;
            List<SharedEdgeWithNeighbourTerritroy> mOutlineSharedEdges = new List<SharedEdgeWithNeighbourTerritroy>();
            List<Vector3> mOutline;
            List<Vector3> mInnerOutline;
            List<ControlPoint> mControlPoints = new List<ControlPoint>();
            int mRegionID;
            Color mColor;
            GameObject[] mObjects = new GameObject[(int)ObjectType.Count];
            string mPrefabPath;
        }

        class ControlPoint
        {
            public ControlPoint(Vector3 pos, Vector3 tangent0, Vector3 tangent1, float vertexDisplayRadius, string name, Transform root)
            {
                mPoint = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                mPoint.transform.position = pos;
                mPoint.transform.localScale = Vector3.one * vertexDisplayRadius * 2;
                mPoint.name = name;
                mPoint.transform.SetParent(root, true);
                mPoint.SetActive(false);

                mTangents[0] = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                mTangents[0].transform.position = tangent0;
                mTangents[0].transform.localScale = Vector3.one * vertexDisplayRadius * 2;
                mTangents[0].SetActive(false);
                mTangents[0].transform.SetParent(root, true);

                mTangents[1] = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                mTangents[1].transform.position = tangent1;
                mTangents[1].transform.localScale = Vector3.one * vertexDisplayRadius * 2;
                mTangents[1].SetActive(false);
                mTangents[1].transform.SetParent(root, true);

                Utils.HideGameObject(mPoint);
                Utils.HideGameObject(mTangents[0]);
                Utils.HideGameObject(mTangents[1]);
            }

            public void OnDestroy()
            {
                Utils.DestroyObject(mPoint);
                for (int i = 0; i < 2; ++i)
                {
                    Utils.DestroyObject(mTangents[i]);
                }
            }

            public void ShowTangent(bool show)
            {
                for (int i = 0; i < mTangents.Length; ++i)
                {
                    mTangents[i].SetActive(show);
                }
            }

            public void MoveControlPoint(Vector3 delta)
            {
                mPoint.transform.position = mPoint.transform.position + delta;
            }

            public void MoveTangent(int tangentIndex, Vector3 delta, TangentMoveType type)
            {
                if (type == TangentMoveType.Free)
                {
                    mTangents[tangentIndex].transform.position = mTangents[tangentIndex].transform.position + delta;
                }
                else if (type == TangentMoveType.Rotate)
                {
                    int other = (tangentIndex + 1) % 2;
                    var oldDir = mTangents[tangentIndex].transform.position - position;
                    float len = oldDir.magnitude;
                    mTangents[tangentIndex].transform.position = mTangents[tangentIndex].transform.position + delta;
                    var newDir = (mTangents[tangentIndex].transform.position - position).normalized;
                    oldDir.Normalize();
                    mTangents[tangentIndex].transform.position = newDir * len + position;
                    float angle = Utils.Angle(oldDir, newDir);

                    float rightLen = (mTangents[other].transform.position - position).magnitude;
                    var oldRightDir = (mTangents[other].transform.position - position).normalized;
                    var newRightDir = Utils.RotateY(oldRightDir, angle);
                    mTangents[other].transform.position = newRightDir * rightLen + position;
                }
                else if (type == TangentMoveType.RotateAndScale)
                {
                    int other = (tangentIndex + 1) % 2;
                    var oldDir = mTangents[tangentIndex].transform.position - position;
                    float len = oldDir.magnitude;
                    mTangents[tangentIndex].transform.position = mTangents[tangentIndex].transform.position + delta;
                    float newLen = (mTangents[tangentIndex].transform.position - position).magnitude;
                    float deltaLen = newLen - len;
                    var newDir = (mTangents[tangentIndex].transform.position - position).normalized;
                    oldDir.Normalize();
                    mTangents[tangentIndex].transform.position = newDir * newLen + position;
                    float angle = Utils.Angle(oldDir, newDir);

                    float rightLen = (mTangents[other].transform.position - position).magnitude + deltaLen;
                    var oldRightDir = (mTangents[other].transform.position - position).normalized;
                    var newRightDir = Utils.RotateY(oldRightDir, angle);
                    mTangents[other].transform.position = newRightDir * rightLen + position;
                }
            }

            public Vector3 position { get { return mPoint.transform.position; } }
            public Vector3 tangent0 { get { return mTangents[0].transform.position; } }
            public Vector3 tangent1 { get { return mTangents[1].transform.position; } }

            GameObject mPoint;
            //0 is left, 1 is right
            GameObject[] mTangents = new GameObject[2];
        }

        class EvaluatePoint
        {
            public EvaluatePoint(Vector3 pos)
            {
                this.pos = pos;
            }

            public Vector3 pos;
        }

        class VertexWrapper
        {
            public bool removed = false;
            public int index;
            public Vector3 position;
            public bool moved = false;
        }

        public class RegionInput {
            public RegionInput(int regionID, List<Vector2Int> coordinates)
            {
                Debug.Assert(coordinates.Count > 0);
                mRegionID = regionID;
                mCoordinates = coordinates;
            }

            public int regionID { get { return mRegionID; } }
            public List<Vector2Int> coordinates { get { return mCoordinates; } }

            List<Vector2Int> mCoordinates;
            int mRegionID;
        }

        public class SettingInput
        {
            public SettingInput(float pointDeltaDistance, float segmentLengthRatio, float minTangentLength, float maxTangentLength, int maxPointCountInOneSegment, bool moreRectangular, float lineWidth, float vertexDisplayRadius, float textureAspectRatio, float segmentLengthRatioRandomRange, float tangentRandomRotationRange, float gridErrorThreshold, Material edgeMaterial, Material regionMaterial, bool useVertexColorForRegionMesh, bool combineMesh, bool mergeEdge, float edgeHeight, bool shareEdge)
            {
                mPointDeltaDistance = pointDeltaDistance;
                mSegmentLengthRatio = segmentLengthRatio;
                mMinTangentLength = minTangentLength;
                mMaxTangentLength = maxTangentLength;
                mMaxPointCountInOneSegment = maxPointCountInOneSegment;
                mMoreRectangular = moreRectangular;
                mLineWidth = lineWidth;
                mVertexDisplayRadius = vertexDisplayRadius;
                mTextureAspectRatio = textureAspectRatio;
                mSegmentLengthRatioRandomRange = segmentLengthRatioRandomRange;
                mTangentRandomRotationRange = tangentRandomRotationRange;
                mUseVertexColorForRegionMesh = useVertexColorForRegionMesh;
                mRegionMaterial = regionMaterial;
                mEdgeMaterial = edgeMaterial;
                mGridErrorThreshold = gridErrorThreshold;
                mCombineMesh = combineMesh;
                mMergeEdge = mergeEdge;
                mEdgeHeight = edgeHeight;
                mShareEdge = shareEdge;
            }

            public float pointDeltaDistance { get { return mPointDeltaDistance; } }
            public float segmentLengthRatio { get { return mSegmentLengthRatio; } }
            public float minTangentLength { get { return mMinTangentLength; } }
            public float maxTangentLength { get { return mMaxTangentLength; } }
            public int maxPointCountInOneSegment { get { return mMaxPointCountInOneSegment; } }
            public bool moreRectangular { get { return mMoreRectangular; } }
            public float lineWidth { get { return mLineWidth; } }
            public float vertexDisplayRadius { get { return mVertexDisplayRadius; } }
            public float textureAspectRatio { get { return mTextureAspectRatio; } }
            public float segmentLengthRatioRandomRange { get { return mSegmentLengthRatioRandomRange; } }
            public float tangentRandomRotationRange { get { return mTangentRandomRotationRange; } }
            public float gridErrorThreshold { get { return mGridErrorThreshold; } }
            public bool useVertexColorForRegionMesh { get { return mUseVertexColorForRegionMesh; } }
            public Material edgeMaterial { get { return mEdgeMaterial; } }
            public Material regionMaterial { get { return mRegionMaterial; } }
            public bool combineMesh { get { return mCombineMesh; } }
            public bool mergeEdge { get { return mMergeEdge; } }
            public float edgeHeight { get { return mEdgeHeight; } }
            public bool shareEdge { get { return mShareEdge; } }

            //生成曲线点时两点之间的最小距离
            float mPointDeltaDistance;
            //tangent的长度占线段长度的比例
            float mSegmentLengthRatio;
            //最小tangent长度
            float mMinTangentLength;
            //最大tangent长度
            float mMaxTangentLength;
            //一段中最多插值多少个曲线点
            int mMaxPointCountInOneSegment;
            //是否生成更方正的边缘线
            bool mMoreRectangular;
            //线段宽度
            float mLineWidth;
            //曲线顶点显示半径
            float mVertexDisplayRadius;
            //图片的宽高比,用于生成无拉伸的uv
            float mTextureAspectRatio;
            float mSegmentLengthRatioRandomRange;
            float mTangentRandomRotationRange;
            //删除顶点时的距离误差格子数
            float mGridErrorThreshold;
            Material mEdgeMaterial;
            Material mRegionMaterial;
            bool mUseVertexColorForRegionMesh;
            //是否将mesh按组合并
            bool mCombineMesh;
            //是否将两个区域共享的边合并为一条
            bool mMergeEdge;
            //是否根据相邻区域不同而分裂一个区域的边
            bool mShareEdge;
            //edge顶点的高度
            float mEdgeHeight;
        }

        //生成需要的所有输入参数
        public class Input
        {
            public Input(List<RegionInput> regions, float gridSize, int horizontalGridCount, int verticalGridCount,
                System.Func<int, int, int> GetRegionIDFunc, 
                System.Func<int, int, Vector3> FromCoordinateToPositionFunc,
                System.Func<Vector3, Vector2Int> FromPositionToCoordinateFunc,
                System.Func<int, Color> GetRegionColorFunc,
                System.Func<int, Vector3> getTerritoryCenterFunc,
                SettingInput settings)
            {
                mGridSize = gridSize;
                mHorizontalGridCount = horizontalGridCount;
                mVerticalGridCount = verticalGridCount;
                mRegions = regions;
                mGetRegionIDFunc = GetRegionIDFunc;
                mFromCoordinateToPositionFunc = FromCoordinateToPositionFunc;
                mFromPositionToCoordinateFunc = FromPositionToCoordinateFunc;
                mGetRegionColorFunc = GetRegionColorFunc;
                mGetTerritoryCenterFunc = getTerritoryCenterFunc;
                mSettings = settings;
            }

            public float gridSize { get { return mGridSize; } }
            public int horizontalGridCount { get { return mHorizontalGridCount; } }
            public int verticalGridCount { get { return mVerticalGridCount; } }
            public List<RegionInput> regions { get { return mRegions; } }
            public System.Func<int, int, int> getRegionIDFunc { get { return mGetRegionIDFunc; } }
            public System.Func<int, int, Vector3> fromCoordinateToPositionFunc { get { return mFromCoordinateToPositionFunc; } }
            public System.Func<Vector3, Vector2Int> fromPositionToCoordinate { get { return mFromPositionToCoordinateFunc; } }
            public System.Func<int, Color> getRegionColorFunc { get { return mGetRegionColorFunc; } }
            public System.Func<int, Vector3> getTerritoryCenterFunc { get { return mGetTerritoryCenterFunc; } }
            public SettingInput settings { get { return mSettings; } }

            float mGridSize;
            int mHorizontalGridCount;
            int mVerticalGridCount;
            List<RegionInput> mRegions;
            System.Func<int, int, int> mGetRegionIDFunc;
            System.Func<int, int, Vector3> mFromCoordinateToPositionFunc;
            System.Func<int, Color> mGetRegionColorFunc;
            System.Func<Vector3, Vector2Int> mFromPositionToCoordinateFunc;
            System.Func<int, Vector3> mGetTerritoryCenterFunc;
            SettingInput mSettings;
        }
    }
}

#endif