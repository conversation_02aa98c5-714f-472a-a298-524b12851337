﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionRemovePolygonRiverSplitter : EditorAction
    {
        public ActionRemovePolygonRiverSplitter(int layerID, int dataID, int splitterIndex)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mSplitterIndex = splitterIndex;
            var river = Map.currentMap.FindObject(dataID) as PolygonRiverData;
            var splitter = river.GetSplitter(splitterIndex);
            mStartPos = splitter.startVertexPosition;
            mEndPos = splitter.endVertexPosition;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            var index = layer.GetSplitterIndex(mDataID, mStartPos, mEndPos);
            Debug.Assert(index == mSplitterIndex);
            layer.DeleteSplitter(mDataID, mSplitterIndex);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.InsertSplitter(mDataID, mSplitterIndex, mStartPos, mEndPos);
            return true;
        }

        int mLayerID;
        int mDataID;
        int mSplitterIndex;
        Vector3 mStartPos;
        Vector3 mEndPos;
    }
}


#endif