﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map {
    //四叉树
    public class QuadTreeNode<ObjectType>
    {
        public QuadTreeNode(Rect bounds, int depth)
        {
            mDepth = depth;
            mBounds = bounds;
        }

        public bool IsFullInside(Rect objectBounds)
        {
            return Utils.FullContains(mBounds, objectBounds);
        }

        public bool IsIntersected(Rect worldBounds)
        {
            var min = worldBounds.min;
            var max = worldBounds.max;
            var regionMin = mBounds.min;
            var regionMax = mBounds.max;
            if (min.x > regionMax.x || min.y > regionMax.y || regionMin.x > max.x || regionMin.y > max.y)
            {
                return false;
            }
            return true;
        }

        public int depth { get { return mDepth; } }
        public Rect bounds { get { return mBounds; } }

        public QuadTreeNode<ObjectType>[] children = new QuadTreeNode<ObjectType>[4];
        public List<ObjectType> objects = new List<ObjectType>();
        Rect mBounds;
        int mDepth = 0;
    };

    public class QuadTree<ObjectType> where ObjectType : ModelData {
        public QuadTree(float mapMinX, float mapMinZ, float mapMaxX, float mapMaxZ, int maxDepth)
        {
            mMapMinX = mapMinX;
            mMapMinZ = mapMinZ;
            mMapMaxX = mapMaxX;
            mMapMaxZ = mapMaxZ;
            mMaxDepth = maxDepth;

            Debug.Assert(mRoot == null);
            mRoot = new QuadTreeNode<ObjectType>(new Rect(mMapMinX, mMapMinZ, mMapMaxX - mMapMinX, mMapMaxZ - mMapMinZ), 0);
        }

        public void AddObject(ObjectType model)
        {
            AddRecursively(mRoot, model.GetBounds(), model);
        }

        void AddRecursively(QuadTreeNode<ObjectType> node, Rect objectBounds, ObjectType obj)
        {
            if (node.depth == mMaxDepth)
            {
                AddToNode(node, obj);
            }
            else
            {
                Rect regionBounds;
                int regionIdx = GetRegionIndex(node.bounds, objectBounds, out regionBounds);
                if (regionIdx == -1)
                {
                    //物体不完全包含在任何子区域中,直接将物体加入到node中
                    AddToNode(node, obj);
                }
                else
                {
                    if (node.children[regionIdx] == null)
                    {
                        //分割node
                        node.children[regionIdx] = new QuadTreeNode<ObjectType>(regionBounds, node.depth + 1);
                    }
                    AddRecursively(node.children[regionIdx], objectBounds, obj);
                }
            }
        }

        void AddToNode(QuadTreeNode<ObjectType> node, ObjectType obj)
        {
            node.objects.Add(obj);
            int id = obj.GetEntityID();
            Debug.Assert(mObjectIDToNode.ContainsKey(id) == false);
            mObjectIDToNode.Add(id, node);
        }

        int GetRegionIndex(Rect nodeBounds, Rect objectWorldBounds, out Rect regionBounds)
        {
            var min = nodeBounds.min;
            var max = nodeBounds.max;
            var size = max - min;
            float width = size.x * 0.5f;
            float height = size.y * 0.5f;

            Rect r0 = new Rect(min.x, min.y, width, height);
            if (Utils.FullContains(r0, objectWorldBounds))
            {
                regionBounds = r0;
                return 0;
            }

            Rect r1 = new Rect(min.x + width, min.y, width, height);
            if (Utils.FullContains(r1, objectWorldBounds))
            {
                regionBounds = r1;
                return 1;
            }

            Rect r2 = new Rect(min.x, min.y + height, width, height);
            if (Utils.FullContains(r2, objectWorldBounds))
            {
                regionBounds = r2;
                return 2;
            }

            Rect r3 = new Rect(min.x + width, min.y + height, width, height);
            if (Utils.FullContains(r3, objectWorldBounds))
            {
                regionBounds = r3;
                return 3;
            }

            regionBounds = new Rect();
            return -1;
        }

        public void RemoveObject(ObjectType model) {
            QuadTreeNode<ObjectType> node;
            var modelID = model.GetEntityID();
            bool found = mObjectIDToNode.TryGetValue(modelID, out node);
            Debug.Assert(found);
            mObjectIDToNode.Remove(modelID);
            int n = node.objects.Count;
            for (int i = 0; i < n; ++i) {
                if (node.objects[i].GetEntityID() == modelID) {
                    node.objects[i] = node.objects[n - 1];
                    node.objects[n - 1] = default(ObjectType);
                    node.objects.RemoveAt(n - 1);
                    return;
                }
            }
            Debug.Assert(false, "Can't be here");
        }

        public void GetIntersection(Rect worldBounds, CheckQuadTreeObjectVisibility func, List<ObjectType> intersections, int step) {
            //check if bounds is outside of root bounds
            var max = worldBounds.max;
            var min = worldBounds.min;
            if (min.x > mMapMaxX || mMapMinX > max.x ||
                min.y > mMapMaxZ || mMapMinZ > max.y) {
                GetIntersectionWithObjects(mRoot, worldBounds, func, intersections, step);
            }
            else {
                GetIntersection(mRoot, worldBounds, func, intersections, step);
            }
        }

        public void GetAllObjects(List<ObjectType> objects) {
            GetAllObjects(mRoot, objects);
        }

        public QuadTreeNode<ObjectType> GetRootNode() {
            return mRoot;
        }

        void GetAllObjects(QuadTreeNode<ObjectType> node, List<ObjectType> objects) {
            foreach (var obj in node.objects) {
                objects.Add(obj);
            }
            for (int i = 0; i < 4; ++i) {
                if (node.children[i] != null) {
                    GetAllObjects(node.children[i], objects);
                }
            }
        }

        void GetIntersection(QuadTreeNode<ObjectType> node, Rect bounds, CheckQuadTreeObjectVisibility func, List<ObjectType> intersections, int step) {
            if (node.IsIntersected(bounds)) {
                for (int i = 0; i < 4; ++i) {
                    var childNode = node.children[i];
                    if (childNode != null) {
                        GetIntersection(childNode, bounds, func, intersections, step);
                    }
                }

                GetIntersectionWithObjects(node, bounds, func, intersections, step);
            }
        }

        void GetIntersectionWithObjects(QuadTreeNode<ObjectType> node, Rect bounds, CheckQuadTreeObjectVisibility func, List<ObjectType> intersections, int step) {
            foreach (var obj in node.objects) {
                //if (obj.visibilityValue <= 1)
                //{
                    if (func.Check(obj.GetBounds(), bounds))
                    {
                        intersections.Add(obj);
                        obj.visibilityValue += step;
                    }
                //}
            }
        }
        
        float mMapMinX = 0;
        float mMapMinZ = 0;
        float mMapMaxX = 0;
        float mMapMaxZ = 0;
        int mMaxDepth;
        QuadTreeNode<ObjectType> mRoot;
        Dictionary<int, QuadTreeNode<ObjectType>> mObjectIDToNode = new Dictionary<int, QuadTreeNode<ObjectType>>();
    };
}