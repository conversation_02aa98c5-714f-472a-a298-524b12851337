﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理一个主城的lod实现
    public class CityLODManager : BuildingLODManager
    {
        public enum CityLODState
        {
            //完全展开状态
            Expanded,
            //图标状态
            Iconic,
            //缩放状态
            Scaling,
        }

        protected override void OnEnableImpl()
        {
            mAllCities.Add(gameObject.GetInstanceID(), this);

            mIsInViewport = false;
        }

        protected override void OnDisableImpl()
        {
            //当主城隐藏时,从视野列表内删除
            mCitiesInViewport.Remove(gameObject.GetInstanceID());
            mAllCities.Remove(gameObject.GetInstanceID());

            mIsInViewport = false;
        }

        protected override void OnDestroyImpl()
        {
            //当主城销毁时,从视野列表内删除
            mCitiesInViewport.Remove(gameObject.GetInstanceID());
            mAllCities.Remove(gameObject.GetInstanceID());

            mIsInViewport = false;
        }

        //当相机高度低于主城缩放的最低点时调用
        //upScale:相机的高度是上升还是下降
        protected override void OnDownScaleChange(float cameraHeight, bool cameraRising)
        {
            if (cameraRising)
            {
                SetDecorationsActive(false);
            }
            else
            {
                SetDecorationsActive(true);
            }

            InvokeDownScaleEvent(cameraRising);

        }

        protected override void OnUpScaleChange(float cameraHeight, bool cameraRising)
        {
            InvokeUpScaleEvent(cameraRising);
        }

        //设置主城内的其他物体
        //wall: 主城的城墙
        //item: 主城的装饰物
        //area: 主城地基
        //slot: 主城地盘
        public void SetCityGameObjects(GameObject wall, GameObject item, GameObject area, GameObject slot)
        {
            mCityWall = wall.GetComponent<CityWall>();
            mItemRoot = item;
            if (mItemRoot != null)
            {
                mItemRoot.SetActive(false);
            }
            mAreaRoot = area;
            mSlotRoot = slot;
            if (mCityWall != null)
            {
                mAllObjects.Add(mCityWall);
            }
            CreateCollidableList();
        }

        /// <summary>
        /// 获取城市当前状态
        /// </summary>
        /// <returns></returns>
        public CityLODState GetState()
        {
            float currentHeight = MapCameraMgr.currentCameraHeight;
            if (currentHeight <= mCityCollapseCameraHeight)
            {
                return CityLODState.Expanded;
            }
            else if (currentHeight >= mCityOpenCameraHeight)
            {
                return CityLODState.Iconic;
            }
            return CityLODState.Scaling;
        }

        //更新所有主城,手动控制update时机
        public static void UpdateAllCities(bool forceUpdate, Rect viewport)
        {
            UpdateCityInViewportCall();
            //相机高度在低于开城高度才更新
            SetEnterCity(false);
            SetLeaveCity(false);
            foreach (var p in mAllCities)
            {
                p.Value.UpdateCity(forceUpdate, viewport);
            }
            if (IsEnterCity())
            {
                DoEnterCity();
            }
            else if (IsLeaveCity())
            {
                DoLeaveCity();   
            }
        }

        protected override void EnterCity()
        {
            SetEnterCity(true);
        }

        protected override void LeaveCity()
        {
            SetLeaveCity(true);
            mLeaveCityRadius = mCityRadius;
            mLeaveCityPosition = transform.position;
        }

        static void DoEnterCity()
        {
            if (!MapModule.useNewCameraHeightAutoUpdateAlgorithm)
            {
                MapCameraMgr.currentMinimumHeight = MapCameraMgr.cameraSetting.cameraMinHeight;
                MapCameraMgr.DisableChangeCameraHeight();
            }
            MapCameraMgr.SetScrollHeightLimit(false);
        }

        static void DoLeaveCity()
        {
            MapCameraMgr.SetScrollHeightLimit(true);
            if (!MapModule.useNewCameraHeightAutoUpdateAlgorithm)
            {
                float cameraHeight = MapCameraMgr.currentCameraHeight;
                float worldMapMinHeight = MapCameraMgr.cameraSetting.GetOutofCityCameraHeight();
                if (cameraHeight >= worldMapMinHeight)
                {
                    MapCameraMgr.currentMinimumHeight = worldMapMinHeight;
                }
                float minCameraHeight = MapCameraMgr.cameraSetting.cameraMinHeight;
                if (cameraHeight <= worldMapMinHeight)
                {
                    MapCameraMgr.EnableChangeCameraHeight(mLeaveCityPosition, mLeaveCityRadius, new Vector2(minCameraHeight, worldMapMinHeight), mXZDistanceAndHeightRatioRange, mCameraRisingSpeedRange);   
                }
                else
                {
                    MapCameraMgr.DisableChangeCameraHeight();
                }
            }
        }

        public void RemoveWall(IBuildingElement wall)
        {
            mAllObjects.Remove(wall);
            mCityWall = null;
        }

        public void ShowWall(bool visible)
        {
            if (mCityWall != null)
            {
                mCityWall.SetVisible(visible);
            }
        }

        //更新视野内可见的其他玩家主城的列表
        protected override void UpdateCitiesInViewport(bool isInViewport)
        {
            if (mIsPlayerCity == false)
            {
                //玩家的主城不参与计算
                int id = gameObject.GetInstanceID();
                if (isInViewport)
                {
                    if (mCitiesInViewport.ContainsKey(id) == false)
                    {
                        mCitiesInViewport[id] = this;
                    }
                }
                else
                {
                    if (mCitiesInViewport.ContainsKey(id))
                    {
                        mCitiesInViewport.Remove(id);
                    }
                }
            }
        }

        protected override void UpdateCityEnterLeaveViewport(bool isInViewport)
        {
            if (mIsInViewport != isInViewport)
            {
                mIsInViewport = isInViewport;
                if (mIsPlayerCity)
                {
                    if (PlayerCityEnterLeaveViewport != null)
                    {
                        PlayerCityEnterLeaveViewport(mIsInViewport);
                    }
                }
                else
                {
                    DoUpdateCity();
                }
            }

        }

        //返回相机高度所在的区间
        int GetCameraHeightSection()
        {
            if (mCities.Count == 0)
            {
                return 0;
            }

            var cameraHeight = MapCameraMgr.currentCameraHeight;
            var building0 = mCities[0];
            if (cameraHeight >= building0.upperHeight)
            {
                return 2;
            }
            else if (cameraHeight <= building0.lowerHeight)
            {
                return 0;
            }
            return 1;
        }

        public bool onlyShowMainCity
        {
            set
            {
                if (mOnlyShowMainCity != value)
                {
                    mOnlyShowMainCity = value;
                    //强制更新lod
                    mDirty = true;

                    bool wallIsVisible = true;
                    bool domainIsVisible = false;
                    if (!mOnlyShowMainCity)
                    {
                        //判断是否该显示城墙等其他物体
                        int section = GetCameraHeightSection();
                        if (section == 0)
                        {
                            //相机最低时显示装饰物
                            domainIsVisible = true;
                        }
                        else if (section == 2)
                        {
                            //相机最高时隐藏城墙
                            wallIsVisible = false;
                        }
                    }
                    else
                    {
                        wallIsVisible = false;
                    }

                    //隐藏城墙
                    ShowWall(wallIsVisible);

                    //隐藏装饰物
                    SetDecorationsActive(domainIsVisible);
                }
            }
            get
            {
                return mOnlyShowMainCity;
            }
        }

        public void SetDecorationsActive(bool isActive)
        {
            if (mItemRoot != null)
            {
                mItemRoot.SetActive(isActive);
            }
            if (mAreaRoot != null)
            {
                mAreaRoot.SetActive(isActive);
            }
            if (mSlotRoot != null)
            {
                mSlotRoot.SetActive(isActive);
            }
        }

        // 判断视野内有多个主城的情况, 由上层逻辑驱动, 每帧调用一次
        static void UpdateCityInViewportCall()
        {
            if (citiesInViewport.Count >= 2)
            {
                //如果视野内有>=2个其他玩家的主城时,只显示最近的主城全貌,其他主城只显示主建筑
                var nearestCity = GetNearestCityToViewCenter(citiesInViewport);
                nearestCity.onlyShowMainCity = false;

                //其他非最近的主城,只显示主建筑
                foreach (var p in citiesInViewport)
                {
                    var city = p.Value;
                    if (city != nearestCity)
                    {
                        city.onlyShowMainCity = true;
                    }
                }
            }
            else
            {
                //视野内只有1个主城,显示其全貌
                foreach (var p in citiesInViewport)
                {
                    var city = p.Value;
                    city.onlyShowMainCity = false;
                }
            }
        }
        //计算离相机视野中心点最近的主城
        static CityLODManager GetNearestCityToViewCenter(Dictionary<int, CityLODManager> citiesInViewport)
        {
            float minDistance = float.MaxValue;
            var viewCenter = Map.currentMap.viewCenter;
            CityLODManager nearestCity = null;
            foreach (var p in citiesInViewport)
            {
                var city = p.Value;
                var d = city.position - viewCenter;
                float distance = d.sqrMagnitude;
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestCity = p.Value;
                }
            }

            return nearestCity;
        }

        static void SetEnterCity(bool enter)
        {
            mEnterCity = enter;
        }

        static void SetLeaveCity(bool leave)
        {
            mLeaveCity = leave;
        }

        static bool IsEnterCity()
        {
            return mEnterCity;
        }

        static bool IsLeaveCity()
        {
            return mLeaveCity;
        }
        protected override void OnAddCity(IBuildingElement city) { }

        public static Dictionary<int, CityLODManager> citiesInViewport { get { return mCitiesInViewport; } }

        public static event System.Action<bool> PlayerCityEnterLeaveViewport;

        //主城的城墙
        CityWall mCityWall;
        //主城的装饰物
        GameObject mItemRoot;
        //主城地基
        GameObject mAreaRoot;
        //主城地盘
        GameObject mSlotRoot;
        bool mIsInViewport = false;

        //视野内的所有主城
        static Dictionary<int, CityLODManager> mCitiesInViewport = new Dictionary<int, CityLODManager>();
        static Dictionary<int, CityLODManager> mAllCities = new Dictionary<int, CityLODManager>();
        //在update期间相机是否进入过任何主城
        static bool mEnterCity = false;
        //在update期间相机是否离开过任何主城
        static bool mLeaveCity = false;
        static float mLeaveCityRadius;
        static Vector3 mLeaveCityPosition;
    }
}