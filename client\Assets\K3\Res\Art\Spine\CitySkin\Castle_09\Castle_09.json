{"skeleton": {"hash": "bJvQNbhaDW8", "spine": "4.2.33", "x": -418.7, "y": -224.04, "width": 841, "height": 799.14, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "tree_Ra", "parent": "root", "x": -296.42, "y": 34.81}, {"name": "tree_Ra2", "parent": "tree_Ra", "length": 36.36, "rotation": 92.97, "x": -0.47, "y": 9.9}, {"name": "tree_Ra3", "parent": "tree_Ra2", "length": 35.37, "rotation": -2.21, "x": 36.36}, {"name": "tree_Ra4", "parent": "tree_Ra3", "length": 42.44, "rotation": -1.4, "x": 35.37}, {"name": "tree_Rb", "parent": "root", "x": -333.44, "y": 59.15}, {"name": "tree_Rb2", "parent": "tree_Rb", "length": 31.33, "rotation": 89.3, "y": 6.88}, {"name": "tree_Rb3", "parent": "tree_Rb2", "length": 35.93, "rotation": -1.13, "x": 31.33}, {"name": "BR", "parent": "root", "length": 49.18, "rotation": 91.94, "x": -324.05, "y": 16.54}, {"name": "BR2", "parent": "root", "length": 43.32, "rotation": 89.45, "x": -354.04, "y": 36.95}, {"name": "BL", "parent": "root", "length": 47.1, "rotation": 87.97, "x": 286.78, "y": 38.2}, {"name": "tree_L", "parent": "root", "x": 250.12, "y": 66.11}, {"name": "tree_L2", "parent": "tree_L", "length": 41.25, "rotation": 88.26, "y": 6.25}, {"name": "tree_L3", "parent": "tree_L2", "length": 45.83, "rotation": -6.1, "x": 41.25}, {"name": "tree", "parent": "root", "x": 15.53, "y": -80.24}, {"name": "tree2", "parent": "tree", "length": 32.91, "rotation": 88.28, "x": 0.66, "y": 5.59}, {"name": "tree3", "parent": "tree2", "length": 40.78, "rotation": -8.5, "x": 32.91}, {"name": "bone", "parent": "root", "x": 186.66, "y": 236.34}, {"name": "bone2", "parent": "bone", "length": 42.55, "rotation": 91.31, "x": -0.73, "y": 3.16}, {"name": "bone3", "parent": "root", "length": 120.47, "rotation": -151.83, "x": 89.3, "y": -112.81}, {"name": "car", "parent": "bone3", "length": 52.45, "rotation": -116.72, "x": -13.88, "y": -3.15}], "slots": [{"name": "Castle", "bone": "root", "attachment": "Castle"}, {"name": "tree_L", "bone": "tree_L", "attachment": "tree_L"}, {"name": "Castle2", "bone": "root", "attachment": "Castle"}, {"name": "shadow", "bone": "root", "attachment": "shadow"}, {"name": "tree_Rb", "bone": "tree_Rb", "attachment": "tree_Rb"}, {"name": "tree_Ra", "bone": "tree_Ra", "attachment": "tree_Ra"}, {"name": "tree", "bone": "tree", "attachment": "tree"}, {"name": "BL", "bone": "BL", "attachment": "BL"}, {"name": "BR2", "bone": "BR2", "attachment": "BR2"}, {"name": "BR", "bone": "BR", "attachment": "BR"}, {"name": "grass", "bone": "root", "attachment": "grass"}, {"name": "motel_R", "bone": "root", "color": "ffffff95", "attachment": "motel_R", "blend": "additive"}, {"name": "M", "bone": "root", "attachment": "M", "blend": "additive"}, {"name": "motel_W", "bone": "root", "color": "ffffff95", "attachment": "motel_W", "blend": "additive"}, {"name": "car2", "bone": "car", "attachment": "car2"}, {"name": "car1", "bone": "car", "attachment": "car1"}, {"name": "O1", "bone": "bone3", "attachment": "O1"}, {"name": "O2", "bone": "bone3", "attachment": "O2"}, {"name": "car", "bone": "car", "attachment": "car"}], "skins": [{"name": "default", "attachments": {"BL": {"BL": {"type": "mesh", "uvs": [1, 0.11681, 0.89568, 0.86312, 0.66217, 0.99447, 0.26583, 0.98297, 0.05224, 0.89867, 0.05216, 0.12368, 0.25506, 0.0072, 0.75597, 0.01415, 0.23536, 0.19404, 0.70937, 0.23245], "triangles": [8, 5, 6, 9, 6, 7, 9, 7, 0, 8, 6, 9, 1, 9, 0, 4, 5, 8, 4, 8, 9, 4, 9, 1, 3, 4, 1, 2, 3, 1], "vertices": [45.02, -17.95, 3.12, -15.46, -4.55, -6.85, -4.44, 8.22, -0.01, 16.5, 43.37, 18.04, 50.16, 10.56, 50.44, -8.47, 39.67, 10.94, 38.16, -7.14], "hull": 8, "edges": [0, 14, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 0, 2], "width": 38, "height": 56}}, "BR": {"BR": {"type": "mesh", "uvs": [0.89937, 0.01853, 0.97761, 0.10617, 0.97494, 0.83055, 0.85086, 0.91528, 0.58663, 0.97757, 0.27381, 0.98384, 0.06797, 0.87974, 0.01824, 0.23105, 0.22748, 0.04142, 0.59375, 0, 0.15572, 0.34995, 0.54271, 0.348, 0.83958, 0.25665], "triangles": [12, 9, 0, 12, 0, 1, 11, 8, 9, 11, 9, 12, 10, 7, 8, 11, 10, 8, 2, 12, 1, 11, 12, 2, 6, 7, 10, 3, 11, 2, 4, 11, 3, 11, 6, 10, 4, 6, 11, 4, 5, 6], "vertices": [51.91, -16.69, 46.54, -19.96, 3.11, -18.37, -1.79, -12.74, -5.13, -0.99, -5.04, 12.78, 1.51, 21.62, 40.48, 22.49, 51.54, 12.9, 53.48, -3.29, 33.15, 16.68, 32.69, -0.34, 37.72, -13.58], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 14, 16, 20, 22, 22, 24], "width": 44, "height": 60}}, "BR2": {"BR2": {"type": "mesh", "uvs": [0.96134, 0.14628, 0.96355, 0.9053, 0.70374, 0.99304, 0.23818, 0.97696, 0.04568, 0.87487, 0.06035, 0.13959, 0.2658, 0.01695, 0.78867, 0.01817, 0.22555, 0.24704, 0.76071, 0.24047], "triangles": [9, 6, 7, 9, 7, 0, 8, 5, 6, 8, 6, 9, 4, 5, 8, 9, 0, 1, 9, 4, 8, 9, 2, 4, 4, 2, 3, 9, 1, 2], "vertices": [38.69, -14.7, -0.78, -15.15, -5.42, -6.62, -4.73, 8.75, 0.51, 15.15, 38.75, 15.04, 45.19, 8.32, 45.3, -8.93, 33.22, 9.53, 33.73, -8.12], "hull": 8, "edges": [0, 14, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 2, 16, 18], "width": 33, "height": 52}}, "car": {"car": {"type": "mesh", "uvs": [0.98175, 0.22882, 0.99528, 0.33858, 0.99056, 0.42335, 0.90079, 0.49796, 0.81519, 0.47532, 0.77705, 0.57265, 0.41203, 0.8672, 0.36444, 0.77996, 0.31487, 0.84032, 0.27366, 0.99379, 0.23706, 0.99389, 0.09781, 0.9069, 0.00371, 0.75307, 0.00102, 0.70564, 0.02177, 0.54846, 0.2365, 0.35902, 0.23673, 0.30347, 0.33146, 0.17707, 0.54744, 0.01211, 0.73292, 0.00619], "triangles": [0, 3, 4, 4, 19, 0, 0, 1, 3, 2, 3, 1, 4, 18, 19, 12, 13, 11, 5, 7, 17, 15, 16, 17, 7, 15, 17, 8, 14, 15, 14, 8, 13, 5, 18, 4, 5, 17, 18, 8, 15, 7, 6, 7, 5, 8, 11, 13, 8, 10, 11, 9, 10, 8], "vertices": [33.49, -109.67, 16.94, -112.56, 4.26, -111.08, -6.37, -88.81, -2.44, -67.93, -16.8, -58.22, -58.69, 32.3, -45.31, 43.62, -54.06, 56, -76.81, 66.67, -76.6, 75.64, -62.69, 109.41, -39.04, 131.87, -31.91, 132.35, -8.47, 126.67, 18.6, 73.36, 26.93, 73.09, 45.3, 49.4, 68.69, -4.12, 68.42, -49.57], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0], "width": 245, "height": 150}}, "car1": {"car1": {"type": "mesh", "uvs": [0.99999, 0.30817, 0.86141, 0.90085, 0.74751, 0.99999, 0.0053, 0.5932, 0.16179, 0.19767, 0.76368, 0.02159], "triangles": [0, 4, 5, 1, 4, 0, 3, 4, 1, 2, 3, 1], "vertices": [-42.89, 27.85, -62.33, 32.92, -65.5, 36.76, -51.46, 60.9, -38.54, 55.41, -33.24, 35.4], "hull": 6, "edges": [0, 2, 4, 6, 6, 8, 8, 10, 2, 4, 10, 0], "width": 33, "height": 33}}, "car2": {"car2": {"type": "mesh", "uvs": [0.56855, 0, 1, 0.08159, 0.87291, 1, 0.75933, 1, 1e-05, 0.49511, 0.56569, 0], "triangles": [3, 0, 1, 5, 0, 3, 4, 5, 3, 2, 3, 1], "vertices": [15.55, -80.3, 13.42, -93.62, -6.68, -89.17, -6.59, -85.65, 5.11, -62.4, 15.55, -80.21], "hull": 6, "edges": [0, 10, 0, 2, 4, 6, 6, 8, 8, 10, 2, 4], "width": 31, "height": 22}}, "Castle": {"Castle": {"type": "mesh", "uvs": [0.50926, 0, 0.51104, 0, 0.54477, 0.01847, 0.54461, 0.11059, 0.7611, 0.25992, 0.75486, 0.30342, 0.83057, 0.63222, 0.9987, 0.74276, 1, 0.74865, 0.53786, 0.99893, 0.46822, 0.99867, 0, 0.68638, 0, 0.68309, 0.12927, 0.61892, 0.1403, 0.11714, 0.13937, 0.04761, 0.21333, 0.01096, 0.23225, 0.024, 0.23513, 0.16499, 0.41361, 0.04429], "triangles": [2, 0, 1, 17, 14, 15, 5, 3, 4, 17, 15, 16, 14, 17, 18, 13, 14, 18, 11, 12, 13, 18, 19, 3, 3, 13, 18, 10, 6, 9, 3, 0, 2, 3, 19, 0, 11, 13, 10, 6, 10, 13, 5, 13, 3, 6, 13, 5, 8, 9, 6, 8, 6, 7], "vertices": [9.59, 575.1, 11.09, 575.1, 39.46, 560.33, 39.32, 486.63, 221.39, 367.17, 216.14, 332.37, 279.81, 69.33, 421.21, -19.1, 422.3, -23.81, 33.64, -224.04, -24.92, -223.83, -418.7, 26, -418.7, 28.63, -309.98, 79.97, -300.71, 481.39, -301.48, 537.02, -239.28, 566.33, -223.37, 555.9, -220.95, 443.12, -70.85, 539.67], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 18, 20, 20, 22, 16, 18], "width": 841, "height": 800}}, "Castle2": {"Castle": {"type": "mesh", "uvs": [0.72779, 0.73172, 0.71898, 0.48699, 0.75481, 0.47239, 0.80259, 0.50774, 0.80703, 0.58902, 0.80677, 0.64987, 0.81638, 0.65505, 0.81586, 0.69516], "triangles": [3, 4, 1, 7, 5, 6, 1, 4, 0, 3, 1, 2, 5, 0, 4, 0, 5, 7], "vertices": [193.38, -10.27, 185.97, 185.52, 216.1, 197.19, 256.29, 168.91, 260.02, 103.88, 259.8, 55.21, 267.88, 51.06, 267.44, 18.98], "hull": 8, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 0, 0, 2, 4, 6, 2, 4], "width": 841, "height": 800}}, "grass": {"grass": {"type": "mesh", "uvs": [0.6368, 0.07439, 0.85986, 0.30417, 0.82727, 0.51372, 1, 0.68029, 0.99761, 0.72463, 0.81654, 0.65056, 0.67262, 0.65969, 0.68783, 0.74436, 0.5645, 0.79073, 0.4746, 0.8653, 0.30193, 0.99416, 0.21958, 0.95429, 0.1637, 0.98253, 0.00519, 0.67583, 0.06935, 0.3731, 0.17547, 0.14264, 0.43337, 0.01676, 0.67549, 0.46731, 0.56683, 0.31463, 0.41361, 0.33107, 0.26874, 0.45557, 0.1977, 0.65053, 0.28406, 0.81495, 0.37043, 0.69516, 0.51251, 0.67402], "triangles": [23, 20, 19, 12, 13, 21, 4, 5, 3, 5, 2, 3, 13, 14, 21, 24, 19, 18, 6, 17, 5, 17, 24, 18, 5, 17, 2, 21, 14, 20, 2, 17, 1, 1, 17, 0, 14, 15, 20, 20, 15, 19, 19, 16, 18, 19, 15, 16, 17, 18, 0, 18, 16, 0, 8, 6, 7, 11, 22, 10, 9, 22, 23, 9, 10, 22, 11, 21, 22, 9, 24, 8, 9, 23, 24, 22, 21, 23, 8, 24, 6, 21, 20, 23, 24, 23, 19, 6, 24, 17, 12, 21, 11], "vertices": [2, 17, 20.4, 40.97, 0.02021, 18, 37.32, -21.99, 0.97979, 2, 17, 39.59, 29.25, 0.01086, 18, 25.17, -40.9, 0.98914, 2, 17, 36.78, 18.57, 0.20801, 18, 14.55, -37.86, 0.79199, 1, 18, 5.71, -52.51, 1, 1, 18, 3.46, -52.26, 1, 2, 17, 35.86, 11.59, 0.30119, 18, 7.59, -36.77, 0.69881, 2, 17, 23.48, 11.12, 0.54491, 18, 7.41, -24.39, 0.45509, 2, 17, 24.79, 6.8, 0.56912, 18, 3.06, -25.6, 0.43088, 2, 17, 14.19, 4.44, 0.79922, 18, 0.94, -14.94, 0.20078, 1, 17, 6.46, 0.64, 1, 1, 17, -8.39, -5.94, 1, 2, 17, -15.48, -3.9, 0.70287, 18, -6.72, 14.9, 0.29713, 2, 17, -20.28, -5.34, 0.59586, 18, -8.05, 19.74, 0.40414, 2, 17, -33.91, 10.3, 0.20929, 18, 7.9, 33.01, 0.79071, 2, 17, -28.4, 25.74, 0.09254, 18, 23.2, 27.14, 0.90746, 1, 18, 34.75, 17.75, 1, 2, 17, 2.91, 43.91, 0.00413, 18, 40.66, -4.57, 0.99587, 2, 17, 23.73, 20.93, 0.38502, 18, 17.21, -24.86, 0.61498, 2, 17, 14.39, 28.72, 0.31975, 18, 25.21, -15.7, 0.68025, 2, 17, 1.21, 27.88, 0.35446, 18, 24.67, -2.5, 0.64554, 2, 17, -11.25, 21.53, 0.38104, 18, 18.61, 10.1, 0.61896, 2, 17, -17.36, 11.59, 0.48959, 18, 8.81, 16.43, 0.51041, 2, 17, -9.93, 3.2, 0.73789, 18, 0.25, 9.2, 0.26211, 2, 17, -2.5, 9.31, 0.70991, 18, 6.19, 1.63, 0.29009, 2, 17, 9.72, 10.39, 0.73992, 18, 6.99, -10.61, 0.26008], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 24, 26, 26, 28, 28, 30, 30, 32, 20, 22, 22, 24, 18, 20, 14, 16, 16, 18, 8, 10, 10, 12], "width": 86, "height": 51}}, "M": {"M": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-206.7, 450.1, -296.7, 450.1, -296.7, 557.1, -206.7, 557.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 90, "height": 107}}, "motel_R": {"motel_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [188.8, 118.18, 76.8, 118.18, 76.8, 213.18, 188.8, 213.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 95}}, "motel_W": {"motel_W": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39.3, 438.1, -68.7, 438.1, -68.7, 536.1, 39.3, 536.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 108, "height": 98}}, "O1": {"O1": {"type": "mesh", "uvs": [0.7253, 0.02437, 0.94659, 0.21896, 0.94656, 0.62271, 0.72043, 0.91953, 0.46382, 1, 0.42307, 1, 0.13817, 0.90767, 0, 0.65058, 0, 0.51164, 0.18539, 0.12494, 0.33279, 0.02361], "triangles": [1, 9, 0, 6, 7, 5, 3, 7, 8, 9, 10, 0, 2, 9, 1, 2, 8, 9, 8, 2, 3, 7, 4, 5, 3, 4, 7], "vertices": [44.96, 17.96, 41.42, 28.68, 49.05, 42.92, 62.03, 49.44, 71.92, 47.79, 73.25, 47.08, 80.79, 38.85, 80.45, 27.37, 77.82, 22.47, 64.47, 12.07, 57.75, 11.07], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 37, "height": 40}}, "O2": {"O2": {"type": "mesh", "uvs": [0.72197, 0.02775, 0.96993, 0.17515, 0.97057, 0.57287, 0.91023, 0.74396, 0.63608, 1, 0.56962, 1, 0.13497, 0.91686, 0.01459, 0.60081, 0.25679, 0.10679, 0.4245, 0.02807], "triangles": [0, 2, 9, 2, 0, 1, 7, 5, 6, 2, 8, 9, 2, 7, 8, 2, 3, 7, 3, 5, 7, 4, 5, 3], "vertices": [-85.96, 23.41, -90.74, 31.82, -84.19, 44.1, -79.6, 48.44, -67.4, 52.07, -65.46, 51.04, -54.19, 41.7, -55.91, 30.07, -71.12, 18.6, -77.3, 18.79], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 33, "height": 35}}, "shadow": {"shadow": {"type": "mesh", "uvs": [0.92472, 1e-05, 1, 0.09568, 1, 0.14141, 0.33923, 1, 0.14371, 0.95155, 0.00595, 0.71079, 0, 0.64428, 0.24957, 0.33891], "triangles": [4, 5, 6, 7, 4, 6, 4, 7, 3, 3, 0, 2, 3, 7, 0, 0, 1, 2], "vertices": [1, 19, -98.64, 21.16, 1, 1, 19, -112.49, 42.03, 1, 1, 19, -109.81, 47.03, 1, 1, 0, 14.7, -208.9, 1, 1, 0, -42.59, -202.89, 1, 1, 0, -82.95, -173.03, 1, 1, 0, -84.7, -164.79, 1, 1, 19, 95.58, -35.19, 1], "hull": 8, "edges": [0, 14, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 2, 4, 0, 2], "width": 293, "height": 124}}, "tree": {"tree": {"type": "mesh", "uvs": [0.85084, 0.06198, 0.99591, 0.25092, 0.97161, 0.44983, 0.78542, 0.60491, 0.56895, 0.59492, 0.48503, 0.5307, 0.47605, 0.66982, 0.46706, 0.80893, 0.46292, 0.98913, 0.35493, 0.98555, 0.35277, 0.81862, 0.35795, 0.68719, 0.23759, 0.57541, 0.03005, 0.59616, 0.18076, 0.49279, 0.29519, 0.37773, 0.02066, 0.42128, 0.08224, 0.26784, 0.16219, 0.14657, 0.36678, 0.09321, 0.55055, 0.02465, 0.74016, 0.00894, 0.55212, 0.36752, 0.77753, 0.3871, 0.36845, 0.2109, 0.67735, 0.19132], "triangles": [4, 23, 3, 3, 23, 2, 5, 22, 4, 4, 22, 23, 5, 15, 22, 2, 23, 1, 16, 17, 15, 22, 25, 23, 23, 25, 1, 15, 17, 24, 15, 24, 22, 24, 17, 18, 22, 24, 25, 25, 0, 1, 18, 19, 24, 24, 20, 25, 24, 19, 20, 25, 21, 0, 25, 20, 21, 7, 8, 10, 10, 11, 7, 7, 11, 6, 6, 11, 5, 5, 11, 12, 13, 14, 12, 5, 12, 15, 12, 14, 15, 8, 9, 10], "vertices": [2, 15, 66.64, -27.14, 6e-05, 16, 37.37, -21.85, 0.99994, 2, 15, 51.82, -37.31, 0.01841, 16, 24.21, -34.1, 0.98159, 2, 15, 35.86, -36.16, 0.07793, 16, 8.27, -35.32, 0.92207, 2, 15, 23.09, -24.06, 0.21635, 16, -6.16, -25.25, 0.78365, 2, 15, 23.45, -9.54, 0.42471, 16, -7.94, -10.83, 0.57529, 2, 15, 28.42, -3.76, 0.78907, 16, -3.89, -4.39, 0.21093, 1, 15, 17.28, -3.5, 1, 1, 15, 6.14, -3.23, 1, 2, 14, 3.79, -2.79, 0.99696, 15, -8.28, -3.38, 0.00304, 1, 14, -3.45, -2.5, 1, 1, 15, 5.13, 4.4, 1, 1, 15, 15.65, 4.37, 1, 2, 15, 24.35, 12.7, 0.94623, 16, -10.35, 11.29, 0.05377, 1, 15, 22.27, 26.55, 1, 2, 15, 30.84, 16.7, 0.86959, 16, -4.52, 16.21, 0.13041, 2, 15, 40.27, 9.32, 0.11465, 16, 5.9, 10.3, 0.88535, 1, 16, -0.79, 27.79, 1, 1, 16, 12.02, 25.9, 1, 1, 16, 22.52, 22.35, 1, 1, 16, 29.15, 9.62, 1, 1, 16, 36.73, -1.52, 1, 1, 16, 40.23, -13.8, 1, 2, 15, 41.6, -7.86, 0.02052, 16, 9.76, -6.49, 0.97948, 2, 15, 40.49, -23.01, 0.07118, 16, 10.9, -21.63, 0.92882, 1, 16, 19.91, 7.84, 1, 2, 15, 55.94, -15.83, 0.00072, 16, 25.12, -12.25, 0.99928], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 30, 32, 36, 38, 38, 40, 40, 42, 24, 26, 8, 10, 10, 12, 12, 14, 26, 28, 28, 30, 32, 34, 34, 36], "width": 67, "height": 80}}, "tree_L": {"tree_L": {"type": "mesh", "uvs": [0.7389, 0.07196, 0.73982, 0.16757, 0.66918, 0.29169, 0.89729, 0.32067, 0.99754, 0.4882, 0.85331, 0.68697, 0.6622, 0.8296, 0.48893, 0.79198, 0.49034, 0.88482, 0.49116, 0.99125, 0.44665, 0.9909, 0.44303, 0.88836, 0.44301, 0.79125, 0.31968, 0.69028, 0.14074, 0.69699, 0.21849, 0.54494, 0.08766, 0.50518, 0.14779, 0.32346, 1e-05, 0.34458, 0.16525, 0.19778, 0.3138, 0.10566, 0.53561, 0.02009, 0.67034, 0.00941, 0.53071, 0.60629, 0.67854, 0.45007, 0.40751, 0.40113, 0.47527, 0.2148], "triangles": [25, 15, 17, 15, 16, 17, 24, 3, 4, 25, 2, 24, 24, 2, 3, 17, 19, 25, 25, 19, 26, 25, 26, 2, 26, 19, 20, 17, 18, 19, 2, 26, 1, 0, 1, 22, 22, 1, 21, 26, 21, 1, 26, 20, 21, 9, 11, 8, 8, 12, 7, 8, 11, 12, 5, 6, 23, 6, 7, 23, 7, 12, 23, 12, 13, 23, 14, 15, 13, 13, 15, 23, 23, 24, 5, 5, 24, 4, 15, 25, 23, 23, 25, 24, 9, 10, 11], "vertices": [1, 13, 41.37, -17.48, 1, 2, 12, 71.36, -22.13, 0.00061, 13, 32.29, -18.81, 0.99939, 2, 12, 59.27, -16.28, 0.09774, 13, 19.64, -14.27, 0.90226, 2, 12, 57.09, -36.43, 0.43409, 13, 19.62, -34.54, 0.56591, 2, 12, 41.28, -45.73, 0.57982, 13, 4.89, -45.47, 0.42018, 2, 12, 21.83, -33.63, 0.82869, 13, -15.74, -35.5, 0.17131, 2, 12, 7.63, -17.23, 0.99382, 13, -31.6, -20.71, 0.00618, 1, 12, 10.78, -1.88, 1, 1, 12, 1.88, -2.28, 1, 1, 11, 2.4, -2.16, 1, 1, 11, -1.51, -2.13, 1, 1, 12, 1.41, 1.88, 1, 1, 12, 10.73, 2.16, 1, 2, 12, 20.09, 13.3, 0.99637, 13, -22.46, 10.98, 0.00363, 2, 12, 18.97, 29.02, 0.99987, 13, -25.25, 26.49, 0.00013, 2, 12, 33.76, 22.63, 0.74065, 13, -9.85, 21.7, 0.25935, 2, 12, 37.23, 34.25, 0.50168, 13, -7.64, 33.63, 0.49832, 2, 12, 54.83, 29.49, 0.1747, 13, 10.36, 30.76, 0.8253, 2, 12, 52.41, 42.43, 0.10622, 13, 6.58, 43.37, 0.89378, 2, 12, 66.93, 28.32, 0.07441, 13, 22.53, 30.89, 0.92559, 2, 12, 76.17, 15.52, 0.00556, 13, 33.07, 19.14, 0.99444, 1, 13, 43.87, 0.92, 1, 1, 13, 46.5, -10.68, 1, 2, 12, 28.71, -5.02, 0.99291, 13, -11.94, -6.32, 0.00709, 2, 12, 44.09, -17.57, 0.44396, 13, 4.69, -17.16, 0.55604, 2, 12, 48.07, 6.42, 0.15492, 13, 6.09, 7.1, 0.84508, 1, 13, 24.63, 3.64, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 42, 44, 34, 36, 28, 30], "width": 88, "height": 96}}, "tree_Ra": {"tree_Ra": {"type": "mesh", "uvs": [0.52021, 0.0054, 0.63065, 0.08161, 0.78721, 0.13775, 0.91299, 0.17915, 0.90829, 0.30261, 0.89609, 0.42164, 0.75768, 0.54282, 0.60688, 0.64089, 0.5194, 0.69636, 0.49471, 0.75042, 0.49707, 0.86169, 0.50623, 0.9919, 0.39761, 0.99387, 0.3945, 0.86169, 0.36237, 0.72841, 0.20659, 0.66564, 0.02262, 0.60019, 0.01014, 0.48402, 0.02994, 0.35503, 0.04384, 0.26453, 0.14325, 0.15302, 0.19376, 0.05436, 0.33105, 0.61503, 0.40266, 0.4842, 0.58591, 0.38228, 0.60066, 0.2418, 0.43215, 0.12061, 0.34368, 0.24424, 0.33104, 0.37508], "triangles": [26, 21, 0, 26, 0, 1, 20, 21, 26, 25, 26, 1, 25, 1, 2, 27, 20, 26, 27, 26, 25, 19, 20, 27, 4, 2, 3, 25, 2, 4, 28, 19, 27, 24, 27, 25, 24, 25, 4, 18, 19, 28, 28, 27, 24, 5, 24, 4, 17, 18, 28, 23, 28, 24, 17, 28, 23, 6, 24, 5, 23, 24, 6, 22, 16, 17, 23, 22, 17, 7, 23, 6, 22, 23, 7, 15, 16, 22, 8, 22, 7, 14, 22, 8, 15, 22, 14, 9, 14, 8, 13, 14, 9, 13, 9, 10, 12, 13, 10, 12, 10, 11], "vertices": [1, 4, 44.12, -8.28, 1, 2, 3, 69.24, -18.61, 0.02463, 4, 34.31, -17.78, 0.97537, 2, 3, 61.76, -31.82, 0.15666, 4, 27.16, -31.17, 0.84334, 2, 3, 56.24, -42.44, 0.247, 4, 21.9, -41.92, 0.753, 2, 3, 40.19, -41.83, 0.43098, 4, 5.85, -41.7, 0.56902, 3, 2, 59.51, -41.51, 0.00059, 3, 24.73, -40.58, 0.67565, 4, -9.64, -40.83, 0.32376, 3, 2, 44.39, -28.94, 0.05852, 3, 9.14, -28.61, 0.86263, 4, -25.52, -29.24, 0.07885, 3, 2, 32.32, -15.48, 0.50398, 3, -3.44, -15.62, 0.49511, 4, -38.41, -16.57, 0.0009, 2, 2, 25.5, -7.68, 0.96038, 3, -10.55, -8.09, 0.03962, 1, 2, 18.59, -5.22, 1, 1, 2, 4.14, -4.67, 1, 1, 1, 4.76, -2.65, 1, 2, 1, -4.48, -2.91, 0.99962, 2, -12.58, 4.66, 0.00038, 1, 2, 4.59, 4.04, 1, 2, 2, 22.03, 5.87, 0.98964, 3, -14.54, 5.31, 0.01036, 2, 2, 30.87, 18.67, 0.47651, 3, -6.2, 18.44, 0.52349, 3, 2, 40.18, 33.84, 0.13735, 3, 2.52, 33.96, 0.85302, 4, -33.67, 33.15, 0.00963, 3, 2, 55.32, 34.12, 0.04161, 3, 17.63, 34.82, 0.87188, 4, -18.58, 34.38, 0.08651, 3, 2, 71.97, 31.57, 0.00099, 3, 34.38, 32.92, 0.6215, 4, -1.8, 32.88, 0.37751, 2, 3, 46.12, 31.58, 0.36211, 4, 9.98, 31.83, 0.63789, 2, 3, 60.51, 22.94, 0.0731, 4, 24.57, 23.54, 0.9269, 2, 3, 73.27, 18.47, 0.00287, 4, 37.44, 19.39, 0.99713, 2, 2, 36.89, 7.76, 0.44512, 3, 0.24, 7.77, 0.55488, 1, 3, 17.16, 1.46, 1, 2, 3, 30.2, -14.29, 0.73954, 4, -4.81, -14.41, 0.26046, 2, 3, 48.45, -15.79, 0.13784, 4, 13.46, -15.46, 0.86216, 1, 4, 29.06, -0.97, 1, 2, 3, 48.42, 6.06, 0.0415, 4, 12.9, 6.38, 0.9585, 2, 3, 31.43, 7.36, 0.80044, 4, -4.12, 7.26, 0.19956], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 10, 12, 12, 14, 14, 16, 34, 36, 36, 38, 6, 8], "width": 85, "height": 130}}, "tree_Rb": {"tree_Rb": {"type": "mesh", "uvs": [0.66626, 0.06285, 0.81054, 0.13959, 0.95175, 0.2779, 0.98577, 0.37046, 0.96963, 0.48054, 0.88964, 0.55712, 0.69645, 0.58813, 0.62208, 0.66528, 0.55128, 0.57497, 0.5663, 0.76867, 0.57522, 0.98778, 0.46013, 0.98861, 0.45565, 0.78731, 0.47599, 0.60873, 0.18165, 0.60838, 0.00865, 0.45256, 0.11368, 0.2992, 0.19552, 0.16147, 0.34199, 0.09933, 0.50658, 0.0161, 0.31237, 0.45283, 0.51927, 0.45283, 0.75649, 0.45283, 0.3641, 0.30064, 0.67266, 0.29352, 0.40155, 0.14417, 0.63877, 0.14702], "triangles": [22, 6, 21, 4, 22, 3, 21, 24, 22, 22, 2, 3, 22, 24, 2, 15, 16, 20, 20, 23, 21, 20, 16, 23, 21, 23, 24, 24, 25, 26, 25, 24, 23, 16, 17, 23, 23, 17, 25, 25, 17, 18, 24, 1, 2, 24, 26, 1, 25, 19, 26, 26, 0, 1, 26, 19, 0, 25, 18, 19, 12, 9, 10, 12, 13, 9, 8, 9, 13, 7, 8, 6, 14, 20, 13, 13, 21, 8, 13, 20, 21, 14, 15, 20, 6, 22, 5, 6, 8, 21, 5, 22, 4, 11, 12, 10], "vertices": [2, 6, 63.23, -8.94, 0.002, 7, 32.07, -8.31, 0.998, 2, 6, 57.28, -18.11, 0.04417, 7, 26.3, -17.59, 0.95583, 2, 6, 46.46, -27.14, 0.21223, 7, 15.67, -26.83, 0.78777, 2, 6, 39.18, -29.37, 0.32235, 7, 8.43, -29.21, 0.67765, 2, 6, 30.47, -28.46, 0.45505, 7, -0.3, -28.47, 0.54495, 2, 6, 24.36, -23.49, 0.56416, 7, -6.51, -23.63, 0.43584, 2, 6, 21.76, -11.35, 0.87671, 7, -9.34, -11.54, 0.12329, 1, 6, 15.61, -6.74, 1, 2, 6, 22.69, -2.19, 0.99954, 7, -8.59, -2.36, 0.00046, 1, 6, 7.4, -3.33, 1, 1, 5, 3.98, -3.08, 1, 1, 5, -3.27, -3.14, 1, 1, 6, 5.84, 3.62, 1, 2, 6, 19.96, 2.52, 0.99733, 7, -11.41, 2.29, 0.00267, 2, 6, 19.77, 21.06, 0.71014, 7, -11.98, 20.83, 0.28986, 2, 6, 31.94, 32.11, 0.47948, 7, -0.02, 32.11, 0.52052, 2, 6, 44.14, 25.64, 0.24256, 7, 12.3, 25.89, 0.75744, 2, 6, 55.08, 20.61, 0.05721, 7, 23.34, 21.08, 0.94279, 2, 6, 60.1, 11.45, 0.00547, 7, 28.54, 12.01, 0.99453, 1, 7, 35.44, 1.86, 1, 2, 6, 32.15, 12.97, 0.49107, 7, 0.57, 12.99, 0.50893, 2, 6, 32.31, -0.06, 0.01477, 7, 0.99, -0.04, 0.98523, 2, 6, 32.49, -15, 0.48115, 7, 1.46, -14.98, 0.51885, 2, 6, 44.22, 9.86, 0.06523, 7, 12.69, 10.11, 0.93477, 2, 6, 45.01, -9.57, 0.06567, 7, 13.87, -9.3, 0.93433, 2, 6, 56.6, 7.65, 0.00261, 7, 25.12, 8.15, 0.99739, 2, 6, 56.56, -7.29, 0.005, 7, 25.37, -6.8, 0.995], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 12, 14, 14, 16, 16, 18, 30, 32, 32, 34], "width": 63, "height": 79}}}}], "animations": {"idle": {"slots": {"M": {"rgba": [{"color": "ffffffde", "curve": [0.167, 1, 0.333, 1, 0.167, 1, 0.333, 1, 0.167, 1, 0.333, 1, 0.167, 0.65, 0.333, 0.2]}, {"time": 0.5, "color": "ffffff32", "curve": [0.722, 1, 0.944, 1, 0.722, 1, 0.944, 1, 0.722, 1, 0.944, 1, 0.722, 0.2, 0.944, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [1.389, 1, 1.611, 1, 1.389, 1, 1.611, 1, 1.389, 1, 1.611, 1, 1.389, 1, 1.611, 0.2]}, {"time": 1.8333, "color": "ffffff32", "curve": [2.056, 1, 2.278, 1, 2.056, 1, 2.278, 1, 2.056, 1, 2.278, 1, 2.056, 0.2, 2.278, 1]}, {"time": 2.5, "color": "ffffffff", "curve": [2.723, 1, 2.944, 1, 2.723, 1, 2.944, 1, 2.723, 1, 2.944, 1, 2.723, 1, 2.944, 0.2]}, {"time": 3.1667, "color": "ffffff32", "curve": [3.389, 1, 3.611, 1, 3.389, 1, 3.611, 1, 3.389, 1, 3.611, 1, 3.389, 0.2, 3.611, 1]}, {"time": 3.8333, "color": "ffffffff", "curve": [4.056, 1, 4.277, 1, 4.056, 1, 4.277, 1, 4.056, 1, 4.277, 1, 4.056, 1, 4.277, 0.2]}, {"time": 4.5, "color": "ffffff32", "curve": [4.722, 1, 4.944, 1, 4.722, 1, 4.944, 1, 4.722, 1, 4.944, 1, 4.722, 0.2, 4.944, 1]}, {"time": 5.1667, "color": "ffffffff", "curve": [5.39, 1, 5.611, 1, 5.39, 1, 5.611, 1, 5.39, 1, 5.611, 1, 5.39, 1, 5.611, 0.2]}, {"time": 5.8333, "color": "ffffff32", "curve": [6.056, 1, 6.278, 1, 6.056, 1, 6.278, 1, 6.056, 1, 6.278, 1, 6.056, 0.2, 6.278, 1]}, {"time": 6.5, "color": "ffffffff", "curve": [6.722, 1, 6.944, 1, 6.722, 1, 6.944, 1, 6.722, 1, 6.944, 1, 6.722, 1, 6.944, 0.2]}, {"time": 7.1667, "color": "ffffff32", "curve": [7.389, 1, 7.611, 1, 7.389, 1, 7.611, 1, 7.389, 1, 7.611, 1, 7.389, 0.2, 7.611, 1]}, {"time": 7.8333, "color": "ffffffff", "curve": [8.056, 1, 8.277, 1, 8.056, 1, 8.277, 1, 8.056, 1, 8.277, 1, 8.056, 1, 8.277, 0.2]}, {"time": 8.5, "color": "ffffff32", "curve": [8.722, 1, 8.944, 1, 8.722, 1, 8.944, 1, 8.722, 1, 8.944, 1, 8.722, 0.2, 8.944, 1]}, {"time": 9.1667, "color": "ffffffff", "curve": [9.39, 1, 9.61, 1, 9.39, 1, 9.61, 1, 9.39, 1, 9.61, 1, 9.39, 1, 9.61, 0.2]}, {"time": 9.8333, "color": "ffffff32", "curve": [10.056, 1, 10.278, 1, 10.056, 1, 10.278, 1, 10.056, 1, 10.278, 1, 10.056, 0.2, 10.278, 1]}, {"time": 10.5, "color": "ffffffff", "curve": [10.556, 1, 10.611, 1, 10.556, 1, 10.611, 1, 10.556, 1, 10.611, 1, 10.556, 1, 10.611, 0.95]}, {"time": 10.6667, "color": "ffffffde"}]}, "motel_R": {"rgba": [{"color": "ffffff32", "curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 0.2, 0.444, 0.59]}, {"time": 0.6667, "color": "ffffff96", "curve": [0.89, 1, 1.11, 1, 0.89, 1, 1.11, 1, 0.89, 1, 1.11, 1, 0.89, 0.59, 1.11, 0.2]}, {"time": 1.3333, "color": "ffffff32", "curve": [1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 0.2, 1.778, 0.59]}, {"time": 2, "color": "ffffff96", "curve": [2.223, 1, 2.444, 1, 2.223, 1, 2.444, 1, 2.223, 1, 2.444, 1, 2.223, 0.59, 2.444, 0.2]}, {"time": 2.6667, "color": "ffffff32", "curve": [2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 0.2, 3.111, 0.59]}, {"time": 3.3333, "color": "ffffff96", "curve": [3.556, 1, 3.777, 1, 3.556, 1, 3.777, 1, 3.556, 1, 3.777, 1, 3.556, 0.59, 3.777, 0.2]}, {"time": 4, "color": "ffffff32", "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 1, 4.222, 1, 4.444, 1, 4.222, 0.2, 4.444, 0.59]}, {"time": 4.6667, "color": "ffffff96", "curve": [4.89, 1, 5.111, 1, 4.89, 1, 5.111, 1, 4.89, 1, 5.111, 1, 4.89, 0.59, 5.111, 0.2]}, {"time": 5.3333, "color": "ffffff32", "curve": [5.556, 1, 5.778, 1, 5.556, 1, 5.778, 1, 5.556, 1, 5.778, 1, 5.556, 0.2, 5.778, 0.59]}, {"time": 6, "color": "ffffff96", "curve": [6.223, 1, 6.444, 1, 6.223, 1, 6.444, 1, 6.223, 1, 6.444, 1, 6.223, 0.59, 6.444, 0.2]}, {"time": 6.6667, "color": "ffffff32", "curve": [6.889, 1, 7.111, 1, 6.889, 1, 7.111, 1, 6.889, 1, 7.111, 1, 6.889, 0.2, 7.111, 0.59]}, {"time": 7.3333, "color": "ffffff96", "curve": [7.556, 1, 7.777, 1, 7.556, 1, 7.777, 1, 7.556, 1, 7.777, 1, 7.556, 0.59, 7.777, 0.2]}, {"time": 8, "color": "ffffff32", "curve": [8.222, 1, 8.444, 1, 8.222, 1, 8.444, 1, 8.222, 1, 8.444, 1, 8.222, 0.2, 8.444, 0.59]}, {"time": 8.6667, "color": "ffffff96", "curve": [8.89, 1, 9.11, 1, 8.89, 1, 9.11, 1, 8.89, 1, 9.11, 1, 8.89, 0.59, 9.11, 0.2]}, {"time": 9.3333, "color": "ffffff32", "curve": [9.556, 1, 9.778, 1, 9.556, 1, 9.778, 1, 9.556, 1, 9.778, 1, 9.556, 0.2, 9.778, 0.59]}, {"time": 10, "color": "ffffff96", "curve": [10.223, 1, 10.444, 1, 10.223, 1, 10.444, 1, 10.223, 1, 10.444, 1, 10.223, 0.59, 10.444, 0.2]}, {"time": 10.6667, "color": "ffffff32"}]}, "motel_W": {"rgba": [{"color": "ffffff64", "curve": [0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 0.49, 0.222, 0.59]}, {"time": 0.3333, "color": "ffffff96", "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 1, 0.556, 1, 0.778, 1, 0.556, 0.59, 0.778, 0.2]}, {"time": 1, "color": "ffffff32", "curve": [1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 0.2, 1.444, 0.59]}, {"time": 1.6667, "color": "ffffff96", "curve": [1.89, 1, 2.11, 1, 1.89, 1, 2.11, 1, 1.89, 1, 2.11, 1, 1.89, 0.59, 2.11, 0.2]}, {"time": 2.3333, "color": "ffffff32", "curve": [2.556, 1, 2.778, 1, 2.556, 1, 2.778, 1, 2.556, 1, 2.778, 1, 2.556, 0.2, 2.778, 0.59]}, {"time": 3, "color": "ffffff96", "curve": [3.223, 1, 3.444, 1, 3.223, 1, 3.444, 1, 3.223, 1, 3.444, 1, 3.223, 0.59, 3.444, 0.2]}, {"time": 3.6667, "color": "ffffff32", "curve": [3.889, 1, 4.111, 1, 3.889, 1, 4.111, 1, 3.889, 1, 4.111, 1, 3.889, 0.2, 4.111, 0.59]}, {"time": 4.3333, "color": "ffffff96", "curve": [4.556, 1, 4.777, 1, 4.556, 1, 4.777, 1, 4.556, 1, 4.777, 1, 4.556, 0.59, 4.777, 0.2]}, {"time": 5, "color": "ffffff32", "curve": [5.222, 1, 5.444, 1, 5.222, 1, 5.444, 1, 5.222, 1, 5.444, 1, 5.222, 0.2, 5.444, 0.59]}, {"time": 5.6667, "color": "ffffff96", "curve": [5.89, 1, 6.111, 1, 5.89, 1, 6.111, 1, 5.89, 1, 6.111, 1, 5.89, 0.59, 6.111, 0.2]}, {"time": 6.3333, "color": "ffffff32", "curve": [6.556, 1, 6.778, 1, 6.556, 1, 6.778, 1, 6.556, 1, 6.778, 1, 6.556, 0.2, 6.778, 0.59]}, {"time": 7, "color": "ffffff96", "curve": [7.223, 1, 7.444, 1, 7.223, 1, 7.444, 1, 7.223, 1, 7.444, 1, 7.223, 0.59, 7.444, 0.2]}, {"time": 7.6667, "color": "ffffff32", "curve": [7.889, 1, 8.111, 1, 7.889, 1, 8.111, 1, 7.889, 1, 8.111, 1, 7.889, 0.2, 8.111, 0.59]}, {"time": 8.3333, "color": "ffffff96", "curve": [8.556, 1, 8.777, 1, 8.556, 1, 8.777, 1, 8.556, 1, 8.777, 1, 8.556, 0.59, 8.777, 0.2]}, {"time": 9, "color": "ffffff32", "curve": [9.222, 1, 9.444, 1, 9.222, 1, 9.444, 1, 9.222, 1, 9.444, 1, 9.222, 0.2, 9.444, 0.59]}, {"time": 9.6667, "color": "ffffff96", "curve": [9.89, 1, 10.11, 1, 9.89, 1, 10.11, 1, 9.89, 1, 10.11, 1, 9.89, 0.59, 10.11, 0.2]}, {"time": 10.3333, "color": "ffffff32", "curve": [10.444, 1, 10.556, 1, 10.444, 1, 10.556, 1, 10.444, 1, 10.556, 1, 10.444, 0.2, 10.556, 0.29]}, {"time": 10.6667, "color": "ffffff64"}]}}, "bones": {"tree_Ra": {"translate": [{"time": 9.3333, "curve": [9.778, 0, 10.222, 0, 9.778, 0, 10.222, 0]}, {"time": 10.6667}]}, "tree_Ra2": {"rotate": [{"value": -1.07, "curve": [0.279, 0.7, 0.556, 3.06]}, {"time": 0.8333, "value": 3.06, "curve": [1.278, 3.06, 1.722, -3]}, {"time": 2.1667, "value": -3, "curve": [2.611, -3, 3.056, 3.06]}, {"time": 3.5, "value": 3.06, "curve": [3.944, 3.06, 4.389, -3]}, {"time": 4.8333, "value": -3, "curve": [5.278, -3, 5.722, 3.06]}, {"time": 6.1667, "value": 3.06, "curve": [6.611, 3.06, 7.056, -3]}, {"time": 7.5, "value": -3, "curve": [7.944, -3, 8.389, 3.06]}, {"time": 8.8333, "value": 3.06, "curve": [9.278, 3.06, 9.722, -3]}, {"time": 10.1667, "value": -3, "curve": [10.334, -3, 10.501, -2.15]}, {"time": 10.6667, "value": -1.07}], "translate": [{"curve": [0.279, 0, 0.556, 0, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "curve": "stepped"}, {"time": 10.1667, "curve": [10.334, 0, 10.501, 0, 10.334, 0, 10.501, 0]}, {"time": 10.6667}]}, "tree_Ra3": {"rotate": [{"value": -2.72, "curve": [0.39, -1.54, 0.779, 3.06]}, {"time": 1.1667, "value": 3.06, "curve": [1.611, 3.06, 2.056, -3]}, {"time": 2.5, "value": -3, "curve": [2.944, -3, 3.389, 3.06]}, {"time": 3.8333, "value": 3.06, "curve": [4.278, 3.06, 4.722, -3]}, {"time": 5.1667, "value": -3, "curve": [5.611, -3, 6.056, 3.06]}, {"time": 6.5, "value": 3.06, "curve": [6.944, 3.06, 7.389, -3]}, {"time": 7.8333, "value": -3, "curve": [8.278, -3, 8.722, 3.06]}, {"time": 9.1667, "value": 3.06, "curve": [9.611, 3.06, 10.056, -3]}, {"time": 10.5, "value": -3, "curve": [10.556, -3, 10.613, -2.89]}, {"time": 10.6667, "value": -2.72}]}, "tree_Ra4": {"rotate": [{"value": -2.72, "curve": [0.057, -2.88, 0.112, -3]}, {"time": 0.1667, "value": -3, "curve": [0.611, -3, 1.056, 3.06]}, {"time": 1.5, "value": 3.06, "curve": [1.944, 3.06, 2.389, -3]}, {"time": 2.8333, "value": -3, "curve": [3.278, -3, 3.722, 3.06]}, {"time": 4.1667, "value": 3.06, "curve": [4.611, 3.06, 5.056, -3]}, {"time": 5.5, "value": -3, "curve": [5.944, -3, 6.389, 3.06]}, {"time": 6.8333, "value": 3.06, "curve": [7.278, 3.06, 7.722, -3]}, {"time": 8.1667, "value": -3, "curve": [8.611, -3, 9.056, 3.06]}, {"time": 9.5, "value": 3.06, "curve": [9.89, 3.06, 10.279, -1.57]}, {"time": 10.6667, "value": -2.72}], "translate": [{"time": 0.1667, "curve": [0.611, 0, 1.056, 0, 0.611, 0, 1.056, 0]}, {"time": 1.5}]}, "tree_Rb": {"translate": [{"time": 9.3333, "curve": [9.778, 0, 10.222, 0, 9.778, 0, 10.222, 0]}, {"time": 10.6667}]}, "tree_Rb2": {"rotate": [{"value": -3.49, "curve": [0.114, -4.42, 0.224, -5.12]}, {"time": 0.3333, "value": -5.12, "curve": [0.778, -5.12, 1.222, 5.05]}, {"time": 1.6667, "value": 5.05, "curve": [2.111, 5.05, 2.556, -5.12]}, {"time": 3, "value": -5.12, "curve": [3.444, -5.12, 3.889, 5.05]}, {"time": 4.3333, "value": 5.05, "curve": [4.778, 5.05, 5.222, -5.12]}, {"time": 5.6667, "value": -5.12, "curve": [6.111, -5.12, 6.556, 5.05]}, {"time": 7, "value": 5.05, "curve": [7.444, 5.05, 7.889, -5.12]}, {"time": 8.3333, "value": -5.12, "curve": [8.778, -5.12, 9.222, 5.05]}, {"time": 9.6667, "value": 5.05, "curve": [10.001, 5.05, 10.336, -0.65]}, {"time": 10.6667, "value": -3.49}], "translate": [{"time": 0.3333, "curve": [0.778, 0, 1.222, 0, 0.778, 0, 1.222, 0]}, {"time": 1.6667}]}, "tree_Rb3": {"rotate": [{"value": -0.04, "curve": [0.225, -2.56, 0.446, -5.12]}, {"time": 0.6667, "value": -5.12, "curve": [1.111, -5.12, 1.556, 5.05]}, {"time": 2, "value": 5.05, "curve": [2.444, 5.05, 2.889, -5.12]}, {"time": 3.3333, "value": -5.12, "curve": [3.778, -5.12, 4.222, 5.05]}, {"time": 4.6667, "value": 5.05, "curve": [5.111, 5.05, 5.556, -5.12]}, {"time": 6, "value": -5.12, "curve": [6.444, -5.12, 6.889, 5.05]}, {"time": 7.3333, "value": 5.05, "curve": [7.778, 5.05, 8.222, -5.12]}, {"time": 8.6667, "value": -5.12, "curve": [9.111, -5.12, 9.556, 5.05]}, {"time": 10, "value": 5.05, "curve": [10.224, 5.05, 10.447, 2.52]}, {"time": 10.6667, "value": -0.04}], "translate": [{"time": 0.6667, "curve": [1.111, 0, 1.556, 0, 1.111, 0, 1.556, 0]}, {"time": 2}]}, "BR": {"rotate": [{"time": 3.3333, "curve": [3.389, 0, 3.444, 3.73]}, {"time": 3.5, "value": 3.73, "curve": [3.556, 3.73, 3.611, -4.9]}, {"time": 3.6667, "value": -4.9, "curve": [3.722, -4.9, 3.778, 2.52]}, {"time": 3.8333, "value": 2.52, "curve": [3.889, 2.52, 3.944, 0]}, {"time": 4, "curve": "stepped"}, {"time": 4.3333, "curve": [4.389, 0, 4.444, 3.73]}, {"time": 4.5, "value": 3.73, "curve": [4.556, 3.73, 4.611, -4.9]}, {"time": 4.6667, "value": -4.9, "curve": [4.722, -4.9, 4.778, 2.52]}, {"time": 4.8333, "value": 2.52, "curve": [4.889, 2.52, 4.944, 0]}, {"time": 5}]}, "BR2": {"rotate": [{"time": 3.5, "curve": [3.556, 0, 3.611, 3.73]}, {"time": 3.6667, "value": 3.73, "curve": [3.722, 3.73, 3.778, -4.9]}, {"time": 3.8333, "value": -4.9, "curve": [3.889, -4.9, 3.944, 2.52]}, {"time": 4, "value": 2.52, "curve": [4.056, 2.52, 4.111, 0]}, {"time": 4.1667, "curve": "stepped"}, {"time": 4.5, "curve": [4.556, 0, 4.611, 3.73]}, {"time": 4.6667, "value": 3.73, "curve": [4.722, 3.73, 4.778, -4.9]}, {"time": 4.8333, "value": -4.9, "curve": [4.889, -4.9, 4.944, 2.52]}, {"time": 5, "value": 2.52, "curve": [5.056, 2.52, 5.111, 0]}, {"time": 5.1667}]}, "BL": {"rotate": [{"time": 3.6667, "curve": [3.722, 0, 3.778, 4.39]}, {"time": 3.8333, "value": 4.39, "curve": [3.889, 4.39, 3.944, -3.9]}, {"time": 4, "value": -3.9, "curve": [4.056, -3.91, 4.111, 2.66]}, {"time": 4.1667, "value": 2.66, "curve": [4.222, 2.66, 4.278, 0]}, {"time": 4.3333, "curve": "stepped"}, {"time": 4.6667, "curve": [4.722, 0, 4.778, 4.39]}, {"time": 4.8333, "value": 4.39, "curve": [4.889, 4.39, 4.944, -3.9]}, {"time": 5, "value": -3.9, "curve": [5.056, -3.91, 5.111, 2.66]}, {"time": 5.1667, "value": 2.66, "curve": [5.222, 2.66, 5.278, 0]}, {"time": 5.3333}], "translate": [{"time": 3.6667, "curve": [3.722, 0, 3.778, 0, 3.722, 0, 3.778, 0]}, {"time": 3.8333, "curve": [3.889, 0, 3.944, 0, 3.889, 0, 3.944, 0]}, {"time": 4, "curve": [4.056, 0, 4.111, 0, 4.056, 0, 4.111, 0]}, {"time": 4.1667, "curve": "stepped"}, {"time": 4.6667, "curve": [4.722, 0, 4.778, 0, 4.722, 0, 4.778, 0]}, {"time": 4.8333, "curve": [4.889, 0, 4.944, 0, 4.889, 0, 4.944, 0]}, {"time": 5, "curve": [5.056, 0, 5.111, 0, 5.056, 0, 5.111, 0]}, {"time": 5.1667}]}, "tree_L": {"translate": [{"time": 9.3333, "curve": [9.778, 0, 10.222, 0, 9.778, 0, 10.222, 0]}, {"time": 10.6667}]}, "tree_L2": {"rotate": [{"value": -0.2, "curve": [0.225, 1.45, 0.446, 3.11]}, {"time": 0.6667, "value": 3.11, "curve": [1.111, 3.11, 1.556, -3.51]}, {"time": 2, "value": -3.51, "curve": [2.444, -3.51, 2.889, 3.11]}, {"time": 3.3333, "value": 3.11, "curve": [3.778, 3.11, 4.222, -3.51]}, {"time": 4.6667, "value": -3.51, "curve": [5.111, -3.51, 5.556, 3.11]}, {"time": 6, "value": 3.11, "curve": [6.444, 3.11, 6.889, -3.51]}, {"time": 7.3333, "value": -3.51, "curve": [7.778, -3.51, 8.222, 3.11]}, {"time": 8.6667, "value": 3.11, "curve": [9.111, 3.11, 9.556, -3.51]}, {"time": 10, "value": -3.51, "curve": [10.224, -3.51, 10.447, -1.86]}, {"time": 10.6667, "value": -0.2}], "translate": [{"curve": [0.225, 0, 0.446, 0, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 10, "curve": [10.224, 0, 10.447, 0, 10.224, 0, 10.447, 0]}, {"time": 10.6667}]}, "tree_L3": {"rotate": [{"value": -2.45, "curve": [0.336, -0.58, 0.668, 3.11]}, {"time": 1, "value": 3.11, "curve": [1.444, 3.11, 1.889, -3.51]}, {"time": 2.3333, "value": -3.51, "curve": [2.778, -3.51, 3.222, 3.11]}, {"time": 3.6667, "value": 3.11, "curve": [4.111, 3.11, 4.556, -3.51]}, {"time": 5, "value": -3.51, "curve": [5.444, -3.51, 5.889, 3.11]}, {"time": 6.3333, "value": 3.11, "curve": [6.778, 3.11, 7.222, -3.51]}, {"time": 7.6667, "value": -3.51, "curve": [8.111, -3.51, 8.556, 3.11]}, {"time": 9, "value": 3.11, "curve": [9.444, 3.11, 9.889, -3.51]}, {"time": 10.3333, "value": -3.51, "curve": [10.446, -3.51, 10.559, -3.08]}, {"time": 10.6667, "value": -2.45}]}, "tree": {"translate": [{"time": 9.3333, "curve": [9.778, 0, 10.222, 0, 9.778, 0, 10.222, 0]}, {"time": 10.6667}]}, "tree2": {"rotate": [{"value": 2.41, "curve": [0.444, 2.41, 0.889, -2.2]}, {"time": 1.3333, "value": -2.2, "curve": [1.778, -2.2, 2.222, 2.41]}, {"time": 2.6667, "value": 2.41, "curve": [3.111, 2.41, 3.556, -2.2]}, {"time": 4, "value": -2.2, "curve": [4.444, -2.2, 4.889, 2.41]}, {"time": 5.3333, "value": 2.41, "curve": [5.778, 2.41, 6.222, -2.2]}, {"time": 6.6667, "value": -2.2, "curve": [7.111, -2.2, 7.556, 2.41]}, {"time": 8, "value": 2.41, "curve": [8.444, 2.41, 8.889, -2.2]}, {"time": 9.3333, "value": -2.2, "curve": [9.778, -2.2, 10.222, 2.41]}, {"time": 10.6667, "value": 2.41}], "translate": [{"time": 9.3333, "curve": [9.778, 0, 10.222, 0, 9.778, 0, 10.222, 0]}, {"time": 10.6667}]}, "tree3": {"rotate": [{"value": 1.67, "curve": [0.114, 2.09, 0.224, 2.41]}, {"time": 0.3333, "value": 2.41, "curve": [0.778, 2.41, 1.222, -2.2]}, {"time": 1.6667, "value": -2.2, "curve": [2.111, -2.2, 2.556, 2.41]}, {"time": 3, "value": 2.41, "curve": [3.444, 2.41, 3.889, -2.2]}, {"time": 4.3333, "value": -2.2, "curve": [4.778, -2.2, 5.222, 2.41]}, {"time": 5.6667, "value": 2.41, "curve": [6.111, 2.41, 6.556, -2.2]}, {"time": 7, "value": -2.2, "curve": [7.444, -2.2, 7.889, 2.41]}, {"time": 8.3333, "value": 2.41, "curve": [8.778, 2.41, 9.222, -2.2]}, {"time": 9.6667, "value": -2.2, "curve": [10.001, -2.2, 10.336, 0.38]}, {"time": 10.6667, "value": 1.67}]}, "bone": {"translate": [{"time": 9.3333, "curve": [9.778, 0, 10.222, 0, 9.778, 0, 10.222, 0]}, {"time": 10.6667}]}, "bone2": {"rotate": [{"value": -9.54, "curve": [0.444, -9.54, 0.889, 9.63]}, {"time": 1.3333, "value": 9.63, "curve": [1.778, 9.63, 2.222, -9.54]}, {"time": 2.6667, "value": -9.54, "curve": [3.111, -9.54, 3.556, 9.63]}, {"time": 4, "value": 9.63, "curve": [4.444, 9.63, 4.889, -9.54]}, {"time": 5.3333, "value": -9.54, "curve": [5.778, -9.54, 6.222, 9.63]}, {"time": 6.6667, "value": 9.63, "curve": [7.111, 9.63, 7.556, -9.54]}, {"time": 8, "value": -9.54, "curve": [8.444, -9.54, 8.889, 9.63]}, {"time": 9.3333, "value": 9.63, "curve": [9.778, 9.63, 10.222, -9.54]}, {"time": 10.6667, "value": -9.54}], "translate": [{"curve": [0.444, 0, 0.889, 0, 0.444, 0, 0.889, 0]}, {"time": 1.3333}]}, "bone3": {"rotate": [{"curve": [1.056, 0, 2.639, 0]}, {"time": 3.1667, "curve": "stepped"}, {"time": 3.4333, "curve": [3.511, 0, 3.589, 0]}, {"time": 3.6667, "curve": [3.889, 0, 4.111, 0]}, {"time": 4.3333, "curve": [4.422, 0, 4.511, 0]}, {"time": 4.6, "curve": [4.689, 0, 4.778, 0]}, {"time": 4.8667, "curve": [5.244, 0, 5.622, 0]}, {"time": 6, "curve": "stepped"}, {"time": 6.6667, "curve": [8, 0, 9.333, 0]}, {"time": 10.6667}], "translate": [{"curve": [1.056, 0, 2.639, 0.03, 1.056, 0, 2.639, 0.01]}, {"time": 3.1667, "curve": [3.256, 0, 3.344, 18.67, 3.256, 0, 3.344, 10]}, {"time": 3.4333, "x": 18.67, "y": 10, "curve": [3.511, 18.67, 3.589, 17.67, 3.511, 10, 3.589, 9.46]}, {"time": 3.6667, "x": 17.67, "y": 9.46, "curve": [3.889, 17.67, 4.111, 17.65, 3.889, 9.46, 4.111, 9.45]}, {"time": 4.3333, "x": 17.67, "y": 9.46, "curve": [4.422, 17.68, 4.511, -13.58, 4.422, 9.47, 4.511, -7.27]}, {"time": 4.6, "x": -13.58, "y": -7.27, "curve": [4.689, -13.58, 4.778, -12.28, 4.689, -7.27, 4.778, -6.58]}, {"time": 4.8667, "x": -12.28, "y": -6.58, "curve": [4.979, -12.28, 5.988, -12.28, 5.071, -6.58, 5.988, -6.58]}, {"time": 6, "x": -12.28, "y": -6.58, "curve": [6.222, -12.29, 6.444, 0, 6.222, -6.58, 6.444, 0]}, {"time": 6.6667, "curve": [8, 0, 9.333, 0, 8, 0, 9.333, 0]}, {"time": 10.6667}]}, "car": {"rotate": [{"time": 3.1667, "curve": [3.256, 0, 3.344, -0.89]}, {"time": 3.4333, "value": -0.89, "curve": [3.511, -0.89, 3.589, 0]}, {"time": 3.6667, "curve": [3.914, 0, 4.086, 0]}, {"time": 4.3333, "curve": [4.422, 0, 4.511, 1.42]}, {"time": 4.6, "value": 1.42, "curve": [4.689, 1.42, 4.778, 0]}, {"time": 4.8667, "curve": [6.8, 0, 8.733, 0]}, {"time": 10.6667}], "translate": [{"x": 0.28, "y": 0.56, "curve": [0.222, 0.28, 0.444, -0.43, 0.222, 0.56, 0.444, -0.85]}, {"time": 0.6667, "x": -0.43, "y": -0.85, "curve": [0.889, -0.43, 1.111, 0.28, 0.889, -0.85, 1.111, 0.56]}, {"time": 1.3333, "x": 0.28, "y": 0.56, "curve": [1.556, 0.28, 1.778, -0.43, 1.556, 0.56, 1.778, -0.85]}, {"time": 2, "x": -0.43, "y": -0.85, "curve": [2.222, -0.43, 2.444, 0.28, 2.222, -0.85, 2.444, 0.56]}, {"time": 2.6667, "x": 0.28, "y": 0.56, "curve": [2.889, 0.28, 3.111, -0.43, 2.889, 0.56, 3.111, -0.85]}, {"time": 3.3333, "x": -0.43, "y": -0.85, "curve": [3.556, -0.43, 3.778, 0.28, 3.556, -0.85, 3.778, 0.56]}, {"time": 4, "x": 0.28, "y": 0.56, "curve": [4.222, 0.28, 4.444, -0.43, 4.222, 0.56, 4.444, -0.85]}, {"time": 4.6667, "x": -0.43, "y": -0.85, "curve": [4.889, -0.43, 5.111, 0.28, 4.889, -0.85, 5.111, 0.56]}, {"time": 5.3333, "x": 0.28, "y": 0.56, "curve": [5.556, 0.28, 5.778, -0.43, 5.556, 0.56, 5.778, -0.85]}, {"time": 6, "x": -0.43, "y": -0.85, "curve": [6.222, -0.43, 6.444, 0.28, 6.222, -0.85, 6.444, 0.56]}, {"time": 6.6667, "x": 0.28, "y": 0.56, "curve": [6.889, 0.28, 7.111, -0.43, 6.889, 0.56, 7.111, -0.85]}, {"time": 7.3333, "x": -0.43, "y": -0.85, "curve": [7.556, -0.43, 7.778, 0.28, 7.556, -0.85, 7.778, 0.56]}, {"time": 8, "x": 0.28, "y": 0.56, "curve": [8.222, 0.28, 8.444, -0.43, 8.222, 0.56, 8.444, -0.85]}, {"time": 8.6667, "x": -0.43, "y": -0.85, "curve": [8.889, -0.43, 9.111, 0.28, 8.889, -0.85, 9.111, 0.56]}, {"time": 9.3333, "x": 0.28, "y": 0.56, "curve": [9.556, 0.28, 9.778, -0.43, 9.556, 0.56, 9.778, -0.85]}, {"time": 10, "x": -0.43, "y": -0.85, "curve": [10.222, -0.43, 10.444, 0.28, 10.222, -0.85, 10.444, 0.56]}, {"time": 10.6667, "x": 0.28, "y": 0.56}]}}}}}