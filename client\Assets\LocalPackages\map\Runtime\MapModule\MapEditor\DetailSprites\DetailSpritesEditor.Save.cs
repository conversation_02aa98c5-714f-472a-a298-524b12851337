﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class DetailSpriteSetting
    {
        public static void Save(string dataPath)
        {
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.DetailSpritesEditorDataVersion);

            SaveData(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        static void SaveData(BinaryWriter writer)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var detailSpritesSetting = editorMapData.detailSpritesSetting;
            writer.Write(editorMapData.mapWidth);
            writer.Write(editorMapData.mapHeight);
            writer.Write(detailSpritesSetting.alpha0Height);
            writer.Write(detailSpritesSetting.alpha1Height);
            writer.Write(detailSpritesSetting.crossfading);
            writer.Write(detailSpritesSetting.updateScale);
            writer.Write(detailSpritesSetting.horizontalGridCount);
            writer.Write(detailSpritesSetting.verticalGridCount);

            int nGroups = detailSpritesSetting.spriteGroups.Count;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = detailSpritesSetting.spriteGroups[i];
                int nSprites = 0;
                if (group.detailSpritesGUIDs != null)
                {
                    nSprites = group.detailSpritesGUIDs.Length;
                }
                writer.Write(nSprites);
                for (int k = 0; k < nSprites; ++k)
                {
                    Utils.WriteString(writer, group.detailSpritesGUIDs[k]);
                }
                Utils.WriteString(writer, group.name);
                Utils.WriteColor32(writer, group.color);
            }
        }
    }
}

#endif