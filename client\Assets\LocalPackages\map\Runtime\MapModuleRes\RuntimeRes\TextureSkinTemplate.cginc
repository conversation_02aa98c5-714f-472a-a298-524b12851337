﻿
#ifdef USE_SLERP_BLENDING
#include "TextureSkinSlerpBlendingTemplate.cginc"
#else
#include "TextureSkinLinearBlendingTemplate.cginc"
#endif

#ifdef ENABLE_SKIN_ANIM_BLENDING
#define CustomSkin2Bone		CustomSkin2BoneWithBlendingImpl
#define CustomSkin			CustomSkinWithBlendingImpl
#define RigidTransform		RigidTransformWithBlendingImpl
#else
#define CustomSkin2Bone		CustomSkin2BoneImpl
#define CustomSkin			CustomSkinImpl
#define RigidTransform		RigidTransformImpl
#endif

//add these code to shader!

//#pragma multi_compile_instancing
//#pragma multi_compile __ ENABLE_SKIN_ANIM_BLENDING
//#pragma multi_compile __ USE_SLERP_BLENDING