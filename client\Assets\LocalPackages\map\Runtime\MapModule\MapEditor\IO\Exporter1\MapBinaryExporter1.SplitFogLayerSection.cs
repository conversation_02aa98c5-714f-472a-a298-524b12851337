﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveSplitFogLayer(BinaryWriter writer, SplitFogLayer layer)
        {
            BeginSection(MapDataSectionType.SplitFogLayer, writer);
            //版本号
            writer.Write(VersionSetting.SplitFogLayerStructVersion);

            //-----------------version 1 start------------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            var map = Map.currentMap;

            int cols = layer.horizontalTileCount;
            int rows = layer.verticalTileCount;

            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);
            writer.Write(layer.layerData.fogHeight);
            Utils.WriteIntArray(writer, layer.layerData.tileTypes);

            SaveSplitFogLayerLODConfigV1(writer, layer.layerData);

            //save prefab paths
            var prefabManager = Map.currentMap.data.splitFogPrefabManager;
            int n = prefabManager.groupCount;
            string[] prefabPaths = new string[16];
            if (n > 0)
            {
                var group = prefabManager.GetOrCreateGroup(0);
                for (int i = 0; i < 16; ++i)
                {
                    prefabPaths[i] = group.GetPrefabPath(i, 0);
                }
            }
            else
            {
                for (int i = 0; i < 16; ++i)
                {
                    prefabPaths[i] = "";
                }
            }
            Utils.WriteString(writer, layer.layerData.fogLOD1PrefabPath);
            Utils.WriteString(writer, layer.layerData.selectionMaterialPath);
            Utils.WriteString(writer, layer.layerData.fogMaskTexPropertyName);
            Utils.WriteStringArray(writer, prefabPaths);
            //----------------------------version 1 end---------------------------------
        }

        void SaveSplitFogLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
                writer.Write((int)c.flag);
            }
        }
    }
}

#endif