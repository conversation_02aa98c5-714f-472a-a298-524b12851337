%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Random Range
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17803\n388;132;1046;800;1433.178;491.644;1.608637;True;False\nNode;AmplifyShaderEditor.SinOpNode;6;-544,176;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;8;-640,272;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;False;0;43758.55;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;4;-704,176;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1;-928,176;Inherit;False;Seed;2;0;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.Vector2Node;5;-976,256;Inherit;False;Constant;_Vector0;Vector
    0;0;0;Create;True;0;0;False;0;12.9898,78.233;0,0;0;3;FLOAT2;0;FLOAT;1;FLOAT;2\nNode;AmplifyShaderEditor.FunctionInput;3;-256,80;Inherit;False;Max;1;2;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;10;-64,0;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;2;-256,0;Inherit;False;Min;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;7;-400,176;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FractNode;9;-256,176;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;128,0;Inherit;False;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;6;0;4;0\nWireConnection;4;0;1;0\nWireConnection;4;1;5;0\nWireConnection;10;0;2;0\nWireConnection;10;1;3;0\nWireConnection;10;2;9;0\nWireConnection;7;0;6;0\nWireConnection;7;1;8;0\nWireConnection;9;0;7;0\nWireConnection;0;0;10;0\nASEEND*/\n//CHKSM=66CE582F1B0E5425FE558D38A8279477232CEB3A"
  m_functionName: 
  m_description: Returns a pseudo-random number value based on input Seed that is
    between the minimum and maximum values defined by inputs Min and Max respectively
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
