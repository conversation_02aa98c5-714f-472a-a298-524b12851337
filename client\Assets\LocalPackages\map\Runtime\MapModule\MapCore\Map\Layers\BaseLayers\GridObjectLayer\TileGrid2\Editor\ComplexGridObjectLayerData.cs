﻿ 



 
 

#define DEBUG_COMPLEX_GRID

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ObjectTagSetting
    {
        public ObjectTagSetting(string name, bool visible)
        {
            this.name = name;
            this.visible = visible;
        }

        public string name;
        public bool visible = true;
    }

    public class DecorationObjectPlacementSetting
    {
        public bool useMapLargeTile = true;
        public bool showObjectBounds = false;
        public float translationStep = 10.0f;
        public float scaleStep = 10.0f;
        public bool isGroup = true;
        public bool considerObstacle = false;
        public bool addLOD = true;
        public bool alignByGrid = true;
        public float alignGridSize = 180;
        public bool onlySelectCurrentTagObjects = false;
    }

    //用格子管理物体,一个物体会在他占用的所有格子中,只适用于地图静态物体
    public class ComplexGridObjectLayerData : MapLayerData
    {
        public ComplexGridObjectLayerData(ComplexGridModelLayer layer, MapLayerDataHeader header, MapLayerLODConfig lodConfig, Map map, bool exportData, List<ObjectTagSetting> objectTagNames, DecorationObjectPlacementSetting placementSetting, Rect realLayerBounds, bool enableObjectMaterialChange, bool enableCullIntersectedObjects) : base(header, lodConfig, map)
        {
            mExportData = exportData;
            mLastViewport = new Rect(-10000, -10000, 0, 0);
            mObjectTags = objectTagNames;
            mPlacementSetting = placementSetting;
            mLayer = layer;
            mEnableObjectMaterialChange = enableObjectMaterialChange;
            mEnableCullIntersectedObjects = enableCullIntersectedObjects;

            mGrids = new ComplexGrid[header.rows * header.cols];
            for (int i = 0; i < mGrids.Length; ++i)
            {
                mGrids[i] = new ComplexGrid(lodConfig.lodConfigs.Length);
            }

            mOldRect = new RectI();
            mNewRect = new RectI();

            mRealLayerBounds = realLayerBounds;

#if UNITY_EDITOR
            mGridViewer = new GridViewer("Complex Grid Object Layer Grids", 0, 0, header.cols, header.rows, header.tileWidth * header.cols, header.tileHeight * header.rows, header.tileWidth, header.tileHeight, new Color(0, 1, 0, 0.2f), new Color(1, 0, 0, 0.2f), true, 0.3f, Map.currentMap.root, false);
            mGridViewer.SetVisible(false);
#endif
        }

        public override void OnDestroy()
        {
            foreach (var obj in mAllLODObjects)
            {
                Map.currentMap.DestroyObject(obj.Value.GetEntityID());
            }
            mAllLODObjects = null;
            mGrids = null;

#if UNITY_EDITOR
            mGridViewer.OnDestroy();
#endif
        }

        public override bool SetZoom(float zoom)
        {
            bool lodChanged = false;
            if (Map.currentMap.enableLOD)
            {
                lodChanged = base.SetZoom(zoom);
            }

            return lodChanged;
        }

        //设置对象的可见性
        public virtual void SetObjectActive(IComplexGridModelData objectData, bool active)
        {
            SetObjectActiveFromAction(objectData, active);
        }

        public bool SetObjectActiveFromAction(IComplexGridModelData objectData, bool active)
        {
            bool isTagVisible = IsTagVisible(objectData.objectTag);
            bool change = SetObjectActiveImpl(objectData, active && isTagVisible);
            if (change)
            {
                mOnActiveStateChangeCallback(objectData, objectData.lod);
            }
            return change;
        }

        protected bool SetObjectActiveImpl(IComplexGridModelData objectData, bool newActiveState)
        {
            bool changed = objectData.SetObjActive(newActiveState);
            if (changed)
            {
                if (newActiveState)
                {
                    mVisibleObjects.Add(objectData.GetEntityID(), objectData);
                }
                else
                {
                    mVisibleObjects.Remove(objectData.GetEntityID());
                }
            }
            return changed;
        }

        public bool AddObjectData(ComplexGridModelData data, int lod, bool checkPositionDuplicate)
        {
            bool ableToAdd = IsAbleToAdd(data);
            if (ableToAdd)
            {
                if (!mAllLODObjects.ContainsKey(data.GetEntityID()))
                {
                    bool added = AddToGrid(data, lod, checkPositionDuplicate);

                    if (added)
                    {
                        mAllLODObjects.Add(data.GetEntityID(), data);
                        var mapData = Map.currentMap.data;
                        mapData.GetOrCreateModelTemplate(data.GetEntityID(), data.GetAssetPath(0), false, false, data.map);

                        bool isActive = data.IsObjActive();
                        if (isActive)
                        {
                            if (mVisibleObjects.ContainsKey(data.GetEntityID()) == false)
                            {
                                mVisibleObjects.Add(data.GetEntityID(), data);
                            }
                        }
                        return true;
                    }
                }
                else
                {
                    Debug.Assert(false, "Object " + data.GetEntityID() + " is already added!");
                }
            }
            else
            {
                Debug.Assert(false, "Can't add object " + data.GetEntityID() + "!");
            }
            return false;
        }

        public bool RemoveObjectData(int objectDataID)
        {
            var data = GetObjectData(objectDataID);
            if (data != null)
            {
                Debug.Assert(data != null, objectDataID + " is not found!");

                RemoveFromGrid(data, data.lod);
                mAllLODObjects.Remove(objectDataID);
                mVisibleObjects.Remove(objectDataID);
                SetObjectActive(data, false);
                Map.currentMap.DestroyObject(data.GetEntityID());

                return true;
            }
            return false;
        }

        //get一个地图对象的数据
        public IComplexGridModelData GetObjectData(int objectID)
        {
            IComplexGridModelData val;
            mAllLODObjects.TryGetValue(objectID, out val);
            return val;
        }

        public override bool Contains(int objectID)
        {
            return mAllLODObjects.ContainsKey(objectID);
        }

        public void GetAllObjects(List<IComplexGridModelData> objects)
        {
            if (mAllLODObjects.Count > 0)
            {
                foreach (var obj in mAllLODObjects)
                {
                    objects.Add(obj.Value);
                }
            }
        }

        public void GetObjectsOfLOD(List<IComplexGridModelData> objects, int lod)
        {
            objects.Clear();
            HashSet<int> objs = new HashSet<int>();
            for (int i = 0; i < mGrids.Length; ++i)
            {
                var gridObjects = mGrids[i].GetLODObjects(lod);
                for (int k = 0; k < gridObjects.Count; ++k)
                {
                    if (!objs.Contains(gridObjects[k].GetEntityID()))
                    {
                        objects.Add(gridObjects[k]);
                        objs.Add(gridObjects[k].GetEntityID());
                    }
                }
            }
        }

        public void SetObjectScaleChangeCallback(System.Action<IComplexGridModelData> onObjectScaleChangeCallback)
        {
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;
        }

        public void SetObjectActiveStateChangeCallback(System.Action<IComplexGridModelData, int> onObjectActiveStateChangeCallback)
        {
            mOnActiveStateChangeCallback = onObjectActiveStateChangeCallback;
        }

        public override void RefreshObjectsInViewport()
        {
            UpdateViewport(mLastViewport, 0);
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }

            if (mLastViewport != newViewport)
            {
                UpdateObjectActiveState(lodChanged, mLastViewport, newViewport);

                mLastViewport = newViewport;
            }

            return lodChanged;
        }

        //使用差集来判断矩形的可见性,非常快速
        void UpdateObjectActiveState(bool lodChanged, Rect oldViewport, Rect newViewport)
        {
            var oldMin = FromWorldPositionToCoordinate(new Vector3(oldViewport.xMin, 0, oldViewport.yMin));
            var oldMax = FromWorldPositionToCoordinate(new Vector3(oldViewport.xMax, 0, oldViewport.yMax));
            var newMin = FromWorldPositionToCoordinate(new Vector3(newViewport.xMin, 0, newViewport.yMin));
            var newMax = FromWorldPositionToCoordinate(new Vector3(newViewport.xMax, 0, newViewport.yMax));

            if (lodChanged)
            {
                //改变lod
                for (int y = oldMin.y; y <= oldMax.y; ++y)
                {
                    for (int x = oldMin.x; x <= oldMax.x; ++x)
                    {
                        if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                        {
                            int idx = y * mCols + x;
                            SetGridActive(idx, false, lastLOD);
                        }
                    }
                }

                for (int y = newMin.y; y <= newMax.y; ++y)
                {
                    for (int x = newMin.x; x <= newMax.x; ++x)
                    {
                        if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                        {
                            int idx = y * mCols + x;
                            SetGridActive(idx, true, currentLOD);
                        }
                    }
                }
            }
            else
            {
                mOldRect.Set(oldMin.x, oldMin.y, oldMax.x, oldMax.y);
                mNewRect.Set(newMin.x, newMin.y, newMax.x, newMax.y);
                mRectDiffCalculator.Calculate(mOldRect, mNewRect, mVisibleRects, mInvisibleRects);
                for (int i = 0; i < mVisibleRects.Count; ++i)
                {
                    var xMin = mVisibleRects[i].xMin;
                    var yMin = mVisibleRects[i].yMin;
                    var xMax = mVisibleRects[i].xMax;
                    var yMax = mVisibleRects[i].yMax;
                    for (int y = yMin; y <= yMax; ++y)
                    {
                        for (int x = xMin; x <= xMax; ++x)
                        {
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
                                SetGridActive(idx, true, currentLOD);
                            }
                        }
                    }
                }

                for (int i = 0; i < mInvisibleRects.Count; ++i)
                {
                    var xMin = mInvisibleRects[i].xMin;
                    var yMin = mInvisibleRects[i].yMin;
                    var xMax = mInvisibleRects[i].xMax;
                    var yMax = mInvisibleRects[i].yMax;
                    for (int y = yMin; y <= yMax; ++y)
                    {
                        for (int x = xMin; x <= xMax; ++x)
                        {
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
                                SetGridActive(idx, false, currentLOD);
                            }
                        }
                    }
                }
            }
        }

        void SetGridActive(int gridIndex, bool active, int lod)
        {
            bool isObjectHidden = lodConfig.lodConfigs[lod].hideObject;
            if (isObjectHidden)
            {
                active = false;
            }
            var objects = mGrids[gridIndex].GetLODObjects(lod);
            for (int i = 0; i < objects.Count; ++i)
            {
                int oldActiveGridCount = objects[i].activeGridCount;
                if (active)
                {
                    objects[i].ShowGrid();
                }
                else
                {
                    objects[i].HideGrid();
                }
                int newActiveGridCount = objects[i].activeGridCount;
                if (oldActiveGridCount == 0 && newActiveGridCount > 0)
                {
                    SetObjectActive(objects[i], true);
                }
                else if (oldActiveGridCount > 0 && newActiveGridCount == 0)
                {
                    SetObjectActive(objects[i], false);
                }
            }
        }

        //加入某lod上的物体到格子里
        bool AddToGrid(IComplexGridModelData objectData, int lod, bool checkPositionDuplicate)
        {
#if false
            //temp code
            var bounds = objectData.GetBounds();
            var obj = new GameObject(objectData.GetModelTemplate().GetLODPrefabPath(0));
            var dp = obj.AddComponent<DrawBounds>();
            dp.bounds = Utils.RectToBounds(objectData.GetBounds());
#endif
            SetDirty();
            bool added = false;
            RectInt objCoordBounds = GetCoordinateBounds(objectData.GetBounds(), Vector2.zero);
            var min = objCoordBounds.min;
            var max = objCoordBounds.max;

#if false
            if (checkPositionDuplicate)
            {
                for (int i = min.y; i <= max.y; ++i)
                {
                    for (int j = min.x; j <= max.x; ++j)
                    {
                        if (i >= 0 && i < mRows &&
                            j >= 0 && j < mCols)
                        {
                            bool canAdd = mGrids[i * mCols + j].CanAddObject(objectData, lod);
                            if (!canAdd)
                            {
                                return false;
                            }
                        }
                    }
                }
            }
#endif

            for (int i = min.y; i <= max.y; ++i)
            {
                for (int j = min.x; j <= max.x; ++j)
                {
                    if (i >= 0 && i < mRows &&
                        j >= 0 && j < mCols)
                    {
                        mGrids[i * mCols + j].AddObject(objectData, lod);
                        added = true;
                    }
                }
            }

            if (!added)
            {
                return false;
            }

            int width = max.x - min.x + 1;
            int height = max.y - min.y + 1;
            objectData.occupiedGridCount = (ushort)(width * height);
            objectData.activeGridCount = 0;

            var mapData = Map.currentMap.data;
            bool isVisible = false;

            if (!isLoading)
            {
                bool isObjectHidden = lodConfig.lodConfigs[currentLOD].hideObject;
                if (isObjectHidden)
                {
                    isVisible = false;
                }
                else
                {
                    RectInt viewportBounds = GetCoordinateBounds(mLastViewport, Vector2.zero);
                    if (Utils.RectIntOverlap(viewportBounds, objCoordBounds))
                    {
                        isVisible = true;
                    }

                    //计算初始时占据多少个可见的格子
                    int count = GetIntersectionGridCount(viewportBounds, objCoordBounds);
                    for (int i = 0; i < count; ++i)
                    {
                        objectData.ShowGrid();
                    }
                }
            }

            SetObjectActive(objectData, isVisible);

            return true;
        }

        int GetIntersectionGridCount(RectInt a, RectInt b)
        {
            var amin = a.min;
            var amax = a.max;
            var bmin = b.min;
            var bmax = b.max;

            int minX = Mathf.Max(amin.x, bmin.x);
            int minY = Mathf.Max(amin.y, bmin.y);
            int maxX = Mathf.Min(amax.x, bmax.x);
            int maxY = Mathf.Min(amax.y, bmax.y);
            int dx = maxX - minX + 1;
            int dy = maxY - minY + 1;
            if (dx <= 0 || dy <= 0)
            {
                return 0;
            }
            int n = dx * dy;
            return n > 0 ? n : 0;
        }

        void RemoveFromGrid(IComplexGridModelData objectData, int lod)
        {
            SetDirty();

            RectInt objCoordBounds = GetCoordinateBounds(objectData.GetBounds(), Vector2.zero);
            var min = objCoordBounds.min;
            var max = objCoordBounds.max;
            for (int i = min.y; i <= max.y; ++i)
            {
                for (int j = min.x; j <= max.x; ++j)
                {
                    if (i >= 0 && i < mRows &&
                        j >= 0 && j < mCols)
                    {
                        mGrids[i * mCols + j].RemoveObject(objectData, lod);
                    }
                }
            }
        }

        bool IsAbleToAdd(ComplexGridModelData objectData)
        {
            var pos = objectData.GetPosition();
            return IsValidPosition(pos);
        }

        public bool IsValidPosition(Vector3 pos)
        {
            float layerWidth = GetLayerWidthInMeter();
            float layerHeight = GetLayerHeightInMeter();
            return pos.x >= 0 && pos.x < layerWidth &&
                pos.z >= 0 && pos.z < layerHeight;
        }

        public void ChangeObjectModel(int objectID, bool useRenderTextureModel)
        {
            var obj = GetObjectData(objectID) as ComplexGridModelData;
            if (obj != null)
            {
                SetObjectActive(obj, false);
                obj.useRenderTextureModel = useRenderTextureModel;
                SetObjectActive(obj, true);
            }
        }

        public IComplexGridModelData FindObjectOfPrefabPath(int lod, string prefabPath)
        {
            for (int i = 0; i < mGrids.Length; ++i)
            {
                var grid = mGrids[i];
                var objects = grid.GetLODObjects(lod);
                foreach (var obj in objects)
                {
                    if (obj.GetAssetPath(lod) == prefabPath)
                    {
                        return obj;
                    }
                }
            }
            return null;
        }

        public IComplexGridModelData FindObjectAtExactSamePosition(Vector3 pos, int lod)
        {
            var p2 = Utils.ToVector2(pos);
            var coord = GetGridCoordinate(p2, Vector2.zero);
            if (coord.x >= 0 && coord.x < mCols && coord.y >= 0 && coord.y < mRows)
            {
                var grid = mGrids[coord.y * mCols + coord.x];
                var objects = grid.GetLODObjects(lod);
                for (int i = 0; i < objects.Count; ++i)
                {
                    if (Utils.Approximately(objects[i].GetPosition(), pos))
                    {
                        return objects[i];
                    }
                }
            }
            return null;
        }

        public IComplexGridModelData FindObjectAtPosition(Vector3 pos, int lod)
        {
            var p2 = Utils.ToVector2(pos);
            var coord = GetGridCoordinate(p2, Vector2.zero);
            if (coord.x >= 0 && coord.x < mCols && coord.y >= 0 && coord.y < mRows)
            {
                var grid = mGrids[coord.y * mCols + coord.x];
                var objects = grid.GetLODObjects(lod);
                for (int i = 0; i < objects.Count; ++i)
                {
                    if (objects[i].GetBounds().Contains(p2))
                    {
                        return objects[i];
                    }
                }
            }
            return null;
        }

        public int CalculateOccupiedGridCount(float minX, float minZ, float maxX, float maxZ)
        {
            var coordBounds = GetCoordinateBounds(new Rect(minX, minZ, maxX - minX, maxZ - minZ), Vector2.zero);
            var min = coordBounds.min;
            var max = coordBounds.max;
            return (max.x - min.x + 1) * (max.y - min.y + 1);
        }

        public RectInt GetCoordinateBounds(Rect r, Vector2 lowerLeftCornerPos)
        {
            var min = GetGridCoordinate(r.min, lowerLeftCornerPos);
            var max = GetGridCoordinate(r.max, lowerLeftCornerPos);
            var width = (max.x - min.x);
            var height = (max.y - min.y);
            return new RectInt(min.x, min.y, width, height);
        }

        Vector2Int GetGridCoordinate(Vector2 pos, Vector2 lowerLeftCornerPos)
        {
            int x = (int)((pos.x - lowerLeftCornerPos.x) / mTileWidth);
            int y = (int)((pos.y - lowerLeftCornerPos.y) / mTileHeight);
            return new Vector2Int(x, y);
        }

        public void OnLODCountChanged(int oldLODCount, int newLODCount)
        {
            if (oldLODCount == newLODCount)
            {
                return;
            }
            List<IComplexGridModelData> removingObjects = new List<IComplexGridModelData>();
            int curLOD = currentLOD;
            if (curLOD >= newLODCount)
            {
                curLOD = newLODCount - 1;
            }
            if (newLODCount < oldLODCount)
            {
                int n = oldLODCount - newLODCount;
                for (int i = newLODCount; i < oldLODCount; ++i)
                {
                    removingObjects.Clear();
                    GetObjectsOfLOD(removingObjects, i);
                    //删除取消的lod的物体
                    for (int k = 0; k < removingObjects.Count; ++k)
                    {
                        RemoveObjectData(removingObjects[k].GetEntityID());
                    }
                }
            }

            //设置当前的lod
            SetCurrentLOD(curLOD);
            //设置格子的lod数
            for (int i = 0; i < mGrids.Length; ++i)
            {
                mGrids[i].SetLODCount(newLODCount);
            }
        }

        public void SetCurrentLOD(int lod)
        {
            var zoom = lodConfig.lodConfigs[lod].changeZoom;
            SetZoom(zoom);
        }

        public void ShowObjectsOfLOD(int lod)
        {
            if (mCurrentLOD >= 0)
            {
                DoShowObjectsOfLOD(mCurrentLOD, false);
            }
            DoShowObjectsOfLOD(lod, true);
        }

        void DoShowObjectsOfLOD(int lod, bool visible)
        {
            RectInt viewportBounds = GetCoordinateBounds(mLastViewport, Vector2.zero);
            for (int g = 0; g < mGrids.Length; ++g)
            {
                var gridObjects = mGrids[g].GetLODObjects(lod);
                for (int k = 0; k < gridObjects.Count; ++k)
                {
                    RectInt objCoordBounds = GetCoordinateBounds(gridObjects[k].GetBounds(), Vector2.zero);
                    if (Utils.RectIntOverlap(viewportBounds, objCoordBounds))
                    {
                        SetObjectActive(gridObjects[k], visible);
                    }
                }
            }
        }

        public void UpdateTransformChangedObjects(List<ObjectTransformInfo> objectInfos)
        {
            for (int i = 0; i < objectInfos.Count; ++i)
            {
                var obj = GetObjectData(objectInfos[i].objectID);
                RemoveFromGrid(obj, obj.lod);

                obj.SetPosition(objectInfos[i].position);
                obj.SetRotation(objectInfos[i].rotation);
                obj.SetScale(objectInfos[i].scale);
                obj.CalculateBounds();

                AddToGrid(obj, obj.lod, false);
            }
        }

        public List<IComplexGridModelData> GetObjectsInGrid(int lod, int x, int y)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                return mGrids[y * mCols + x].GetLODObjects(lod);
            }
            return null;
        }

        public bool IsGridDirty(int lod, int x, int y)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                return mGrids[y * mCols + x].IsLODDirty(lod);
            }
            return false;
        }

        public void SetGridDirty(int lod, int x, int y, bool dirty)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                mGrids[y * mCols + x].SetLODDirty(lod, dirty);
            }
        }

        public ObjectTagSetting FindObjectTag(string name)
        {
            for (int i = 0; i < mObjectTags.Count; ++i)
            {
                if (mObjectTags[i].name == name)
                {
                    return mObjectTags[i];
                }
            }
            return null;
        }

        public bool AddObjectTag(string tag, bool visible)
        {
            if (string.IsNullOrEmpty(tag))
            {
                return false;
            }

            if (FindObjectTag(tag) != null)
            {
                return false;
            }
            mObjectTags.Add(new ObjectTagSetting(tag, visible));
            return true;
        }

        public bool RemoveObjectTag(string tag)
        {
            if (string.IsNullOrEmpty(tag))
            {
                return false;
            }
            for (int i = 0; i < mObjectTags.Count; ++i)
            {
                if (mObjectTags[i].name == tag)
                {
                    mObjectTags.RemoveAt(i);
                    return true;
                }
            }
            return false;
        }

        public string[] GetObjectTagNames()
        {
            string[] tags = new string[mObjectTags.Count];

            for (int i = 0; i < mObjectTags.Count; ++i)
            {
                tags[i] = mObjectTags[i].name;
            }

            return tags;
        }

        public void UpdateObjectVisibleStateByTag()
        {
            var min = FromWorldPositionToCoordinate(new Vector3(mLastViewport.xMin, 0, mLastViewport.yMin));
            var max = FromWorldPositionToCoordinate(new Vector3(mLastViewport.xMax, 0, mLastViewport.yMax));

            for (int y = min.y; y <= max.y; ++y)
            {
                for (int x = min.x; x <= max.x; ++x)
                {
                    if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                    {
                        int idx = y * mCols + x;
                        var objects = mGrids[idx].GetLODObjects(currentLOD);
                        for (int i = 0; i < objects.Count; ++i)
                        {
                            SetObjectActive(objects[i], true);
                        }
                    }
                }
            }
        }

        public void SetObjectsOfTagVisible(string tag, bool visible)
        {
            var tagSetting = FindObjectTag(tag);
            if (tagSetting == null || tagSetting.visible == visible)
            {
                return;
            }

            tagSetting.visible = visible;

            UpdateObjectVisibleStateByTag();
        }

        public bool IsTagVisible(string tag)
        {
            var tagSetting = FindObjectTag(tag);
            if (tagSetting == null)
            {
                return true;
            }

            return tagSetting.visible;
        }

        void SetDirty()
        {
#if UNITY_EDITOR
            EditorConfig.dirtyFlag |= DirtyMask.NavMesh;
            EditorConfig.dirtyFlag |= DirtyMask.NPCRegionConfig;
            EditorConfig.dirtyFlag |= DirtyMask.NPCSpawnPoints;
            EditorConfig.dirtyFlag |= DirtyMask.ComplexGridModelLayer;
#endif
        }

        public override bool isGameLayer { get { return true; } }
        public int objectCount { get { return mAllLODObjects.Count; } }
        public Dictionary<int, IComplexGridModelData> objects { get { return mAllLODObjects; } }
        public bool isLoading { set; get; }
        public bool showGridViewer { get { return mGridViewer.visible; } set { mGridViewer.SetVisible(value); } }
        public bool exportData { get { return mExportData; } set { mExportData = value; } }
        public ComplexGrid[] grids { get { return mGrids; } }
        public List<ObjectTagSetting> objectTags { get { return mObjectTags; } }
        public DecorationObjectPlacementSetting objectPlacementSetting { get { return mPlacementSetting; } }
        public Rect realLayerBounds { get { return mRealLayerBounds; } set { mRealLayerBounds = value; } }
        public ComplexGridModelLayer layer { get { return mLayer; } }
        public bool enableObjectMaterialChange { get { return mEnableObjectMaterialChange; } set { mEnableObjectMaterialChange = value; } }
        public bool enableCullIntersectedObjects { get { return mEnableCullIntersectedObjects; } set { mEnableCullIntersectedObjects = value; } }

        //手动填写一个layer的大小,因为decoration layer的实际大小是根据摆放的模型确定.如果不填写则使用layer创建时的size
        public Rect mRealLayerBounds;

        ComplexGrid[] mGrids;
        float mLastZoom;
        Rect mLastViewport;
        ComplexGridModelLayer mLayer;
        bool mExportData = true;
        bool mEnableObjectMaterialChange;
        bool mEnableCullIntersectedObjects;

        System.Action<IComplexGridModelData, int> mOnActiveStateChangeCallback;
        protected System.Action<IComplexGridModelData> mOnObjectScaleChangeCallback;

        Dictionary<int, IComplexGridModelData> mVisibleObjects = new Dictionary<int, IComplexGridModelData>();
        //所有lod下的物体数据
        Dictionary<int, IComplexGridModelData> mAllLODObjects = new Dictionary<int, IComplexGridModelData>(1000);

        RectDifference mRectDiffCalculator = new RectDifference();
        RectI mOldRect;
        RectI mNewRect;
        List<RectInt> mVisibleRects = new List<RectInt>(9);
        List<RectInt> mInvisibleRects = new List<RectInt>(9);
        GridViewer mGridViewer = null;
        List<ObjectTagSetting> mObjectTags = new List<ObjectTagSetting>();

        DecorationObjectPlacementSetting mPlacementSetting;
    }
}
