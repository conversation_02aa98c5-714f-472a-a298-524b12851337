﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class BlendTerrainLayerSettingWindow : EditorWindow
    {
        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Layer Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Layer Height", mLayerHeight);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            mTileWidth = EditorGUILayout.FloatField("Tile Width", mTileWidth);
            mTileHeight = EditorGUILayout.FloatField("Tile Height", mTileHeight);
            GUILayout.EndHorizontal();

            if (GUILayout.Button("Create"))
            {
                var map = Map.currentMap;
                bool valid = CheckParameter();
                if (valid)
                {
                    var layer = Map.currentMap.CreateBlendTerrainLayer(MapCoreDef.MAP_LAYER_NODE_GROUND, mLayerWidth, mLayerHeight, mTileWidth, mTileHeight, 0);
                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0 ||
                mTileWidth <= 0 || mTileHeight <= 0)
            {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public float mTileWidth = MapModule.defaultGroundTileSize;
        public float mTileHeight = MapModule.defaultGroundTileSize;
    }
}

#endif