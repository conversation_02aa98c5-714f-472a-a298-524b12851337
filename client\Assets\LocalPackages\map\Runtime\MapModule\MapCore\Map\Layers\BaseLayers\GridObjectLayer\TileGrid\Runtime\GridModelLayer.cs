﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using TFW.Map.config;
using UnityEngine;

namespace TFW.Map
{
    //基于格子来管理地图对象,每个格子有许多子对象,格子使用的是稀疏的数据结构,根据格子的索引来管理
    public class GridModelLayer : MapLayerBase
    {
        public GridModelLayer(Map map) :base(map){ }

        //清理地图层
        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var sourceLayer = layerData as config.GridModelLayerData;
            int rows = sourceLayer.zTileCount;
            int cols = sourceLayer.xTileCount;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth, sourceLayer.tileHeight, sourceLayer.gridType, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            var groupManager = Utils.CreateLODGroupManager(layerData, map);

            LoadAsync(async, sourceLayer, header, config); 
        }

        private async UniTask LoadAsync(bool async, GridModelLayerData sourceLayer, MapLayerDataHeader header, MapLayerLODConfig config)
        {
            var path= map.name == "MobaMap" ? $"{MapModule.configResDirectory}keep_decoration_size_moba.asset" : $"{MapModule.configResDirectory}keep_decoration_size.asset";
            var keepScaleConfig0 = await MapModuleResourceMgr.LoadResourceAsync<KeepScaleConfig>(path, string.Empty);
            {
                mLayerData = new TileGridObjectLayerData(header, config, map, new KeepScaleConfig[] { keepScaleConfig0 }, async);
                mLayerView = new TileGridObjectLayerView(mLayerData, true);
                mLayerView.active = sourceLayer.active;
                mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
                mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);
                mLayerData.isLoading = true;
                map.AddMapLayer(this);
                MapModuleResourceMgr.UnloadAsset(path);
            }
        }



        //返回x,y格子中的地图对象数据
        public OptimizedTileData GetObjectData(int x, int y)
        {
            return mLayerData.GetBigTileData(x, y);
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            return lodChanged;
        }

        //内部使用,加载完地图后刷新该层中可见的地图对象
        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        //从世界坐标转换到格子坐标
        public Vector2Int FromWorldPositionToCoordinate(Vector3 position)
        {
            return mLayerData.FromWorldPositionToCoordinate(position);
        }
        //从屏幕坐标转换到格子坐标
        public Vector2Int FromScreenToCoordinate(Vector3 screenPos, UnityEngine.Camera camera)
        {
            return mLayerData.FromScreenToCoordinate(screenPos, camera);
        }
        //从格子坐标转换到世界坐标
        public Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPosition(x, y);
        }
        //从格子坐标转换到格子的中心点的世界坐标
        public Vector3 FromCoordinateToWorldPositionCenter(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPositionCenter(x, y);
        }
        public Vector3 FromCoordinateToWorldPositionCenter(int x, int y, int width, int height)
        {
            return mLayerData.FromCoordinateToWorldPositionCenter(x, y, width, height);
        }
        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void UpdateObjectScale()
        {
            mLayerData.UpdateObjectScaleAtHeight();
        }

        public void OnShowNPC(long npcID, Vector3 center, float radius)
        {
            mLayerData.OnShowNPC(npcID, center, radius);
        }

        public void OnHideNPC(long npcID)
        {
            mLayerData.OnHideNPC(npcID);
        }

        public void ShowObstacles(bool visible)
        {
            if (visible)
            {
                mLayerData.GetTilesInViewport(mBigTilesInViewport);
                mLayerView.ShowObstacles(mBigTilesInViewport);
                mBigTilesInViewport.Clear();
            }
            else
            {
                mLayerView.HideObstacles();
            }
        }

        public virtual bool AddObject(IMapObjectData objectData)
        {
            throw new System.NotImplementedException();
        }
        public virtual bool RemoveObject(int objectID)
        {
            throw new System.NotImplementedException();
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public bool asyncLoading
        {
            set
            {
                mLayerView.asyncLoading = value;
            }
            get
            {
                return mLayerView.asyncLoading;
            }
        }
        public int objectCount { get { return mLayerData.objectCount; } }
        public override int horizontalTileCount { get { return mLayerData.horizontalTileCount; } }
        public override int verticalTileCount { get { return mLayerData.verticalTileCount; } }
        public override float tileWidth { get { return mLayerData.tileWidth; } }
        public override float tileHeight { get { return mLayerData.tileHeight; } }
        public override GridType gridType { get { return mLayerData.gridType; } }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }

        public override bool active { get { return layerView.active; } set { mLayerView.active = value; } }
        public override int lodCount => mLayerData.lodCount;
        public TileGridObjectLayerData layerData { get { return mLayerData; } }
        public TileGridObjectLayerView layerView { get { return mLayerView; } }

        //该层数据的管理
        TileGridObjectLayerData mLayerData;
        //该层视图的管理
        TileGridObjectLayerView mLayerView;

        //temp variable
        List<ObstacleDisplayInfo> mBigTilesInViewport = new List<ObstacleDisplayInfo>(100);
    }
}
