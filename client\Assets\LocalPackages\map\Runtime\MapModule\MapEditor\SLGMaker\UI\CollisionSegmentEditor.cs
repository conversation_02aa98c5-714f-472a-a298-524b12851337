﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(CollisionSegment))]
    class CollisionSegmentEditor : UnityEditor.Editor
    {
        void OnEnable()
        {
            mSegment = target as CollisionSegment;
            mSegment.Init();

            CreateDisplayOutlineVertices();
        }

        public override void OnInspectorGUI()
        {
            EditorGUI.BeginChangeCheck();

            base.OnInspectorGUI();

            //var newType = (PrefabOutlineType)EditorGUILayout.EnumPopup("Outline Type", mSegment.currentOutlineType);
            //if (mSegment.currentOutlineType != newType)
            //{
            //    ChangeOutlineType(newType);
            //}

            //if (GUILayout.Button("Copy To Other Outline"))
            //{
            //    CopyToOtherOutline(mSegment.currentOutlineType, 0);
            //}

            //EditorGUILayout.BeginHorizontal();
            //if (GUILayout.Button("Expand Outline"))
            //{
            //    ExpandPrefabOutline();
            //}

            //EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Clamp To Border"))
            {
                mSegment.ClampToBorder();
                CreateDisplayOutlineVertices();
            }

            if (Map.currentMap != null)
            {
                var terrainLayer = Map.currentMap.GetMapLayer<BlendTerrainLayer>();
                if (terrainLayer != null)
                {
                    if (GUILayout.Button(new GUIContent("Apply Segment Change To Prefab", "将修改过的Collision Segment数据应用到Prefab上")))
                    {
                        terrainLayer.ApplySegmentChangeToPrefab();
                    }
                }
            }

            EditorGUILayout.BeginVertical("GroupBox");
            bool edit = GUILayout.Toggle(mEditingOutline, "Edit Outline");
            if (edit != mEditingOutline)
            {
                SetEditMode(edit);
            }
            mVertexRadius = EditorGUILayout.FloatField("Vertex Radius", mVertexRadius);
            if (mSelectedVertex >= 0)
            {
                var pos = mSegment.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle)[mSelectedVertex];
                EditorGUILayout.LabelField($"Selected Point Position: {pos}");
            }
            EditorGUILayout.LabelField("Add Vertex: Ctrl + Mouse Left Button");
            EditorGUILayout.LabelField("Remove Vertex: Ctrl + Shift + Mouse Left Button");
            EditorGUILayout.EndVertical();

            bool changed = EditorGUI.EndChangeCheck();
            if (changed)
            {
                SceneView.RepaintAll();
            }
        }

        //void ChangeOutlineType(PrefabOutlineType type)
        //{
        //    mSegment.currentOutlineType = type;
        //    mMovingVertex = false;
        //    CreateDisplayOutlineVertices();
        //    SetEditMode(false);
        //}

        //void CopyToOtherOutline(PrefabOutlineType currentType, float radius)
        //{
        //    if (currentType == PrefabOutlineType.NavMeshObstacle)
        //    {
        //        mSegment.CopyFromTo(PrefabOutlineType.NavMeshObstacle, PrefabOutlineType.ObjectPlacementObstacle, radius);
        //    }
        //    else
        //    {
        //        mSegment.CopyFromTo(PrefabOutlineType.ObjectPlacementObstacle, PrefabOutlineType.NavMeshObstacle, radius);
        //    }
        //}

        void OnSceneGUI()
        {
            DrawObstacleEditor();
            EditOutline();
        }

        void DrawObstacleEditor()
        {
            if (mWorldSpaceOutlineVertices == null)
            {
                return;
            }

            for (int i = 0; i < mWorldSpaceOutlineVertices.Length; ++i)
            {
                mWorldSpaceOutlineVertices[i] = mSegment.TransformToWorldPosition(mLocalSpaceOutlineVertices[i]);
            }
            int nLines = Mathf.Max(0, mWorldSpaceOutlineVertices.Length - 1);
            for (int i = 0; i < nLines; ++i)
            {
                mWorldSpaceLines[i * 2] = mWorldSpaceOutlineVertices[i];
                mWorldSpaceLines[i * 2 + 1] = mWorldSpaceOutlineVertices[i + 1];
            }

            if (mEditingOutline)
            {
                Handles.color = Color.green;
            }
            else
            {
                Handles.color = Color.white;
            }

            Handles.DrawLines(mWorldSpaceLines);

            for (int i = 0; i < mWorldSpaceOutlineVertices.Length; ++i)
            {
                Handles.SphereHandleCap(0, mWorldSpaceOutlineVertices[i], Quaternion.identity, mVertexRadius, EventType.Repaint);
            }
        }

        void EditOutline()
        {
            if (mEditingOutline)
            {
                var currentEvent = Event.current;
                var camera = SceneView.GetAllSceneCameras()[0];
                var worldPos = EditorUtils.FromEditorScreenToWorldPosition(currentEvent.mousePosition, camera, mSegment.gameObject.transform.position.y);

                bool leftClick = currentEvent.type == EventType.MouseDown && currentEvent.button == 0;
                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
                {
                    if (currentEvent.alt == false)
                    {
                        mLeftButtonDown = true;
                    }
                    var localPos = mSegment.TransformToLocalPosition(worldPos);
                    if (currentEvent.control == false)
                    {
                        mSelectedVertex = PickVertex(localPos, mVertexRadius);
                    }
                    else
                    {
                        if (currentEvent.shift)
                        {
                            mSelectedVertex = PickVertex(localPos, mVertexRadius);
                            RemoveVertex(mSelectedVertex);
                            CreateDisplayOutlineVertices();
                        }
                        else
                        {
                            AddVertex(localPos);
                            CreateDisplayOutlineVertices();
                        }
                    }
                }

                if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
                {
                    mLeftButtonDown = false;
                    mMovingVertex = false;
                    EditorUtility.SetDirty(target);
                }

                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 1)
                {
                    //pick vertex
                    var localPos = mSegment.TransformToLocalPosition(worldPos);
                    int vertexIndex = PickVertex(localPos, mVertexRadius);
                    if (vertexIndex >= 0)
                    {
                        var vertices = mSegment.GetOutlineVertices(mSegment.currentOutlineType);
                        Debug.Log($"vertex {vertexIndex}: {vertices[vertexIndex].ToString()}");
                        SceneView.RepaintAll();
                    }
                }

                if (mLeftButtonDown && !leftClick)
                {
                    if (mSelectedVertex >= 0)
                    {
                        if (mMovingVertex == false)
                        {
                            mMovingVertex = true;
                        }

                        Transform root = Utils.GetRootTransform(mSegment.gameObject.transform);
                        var localPosInRoot = mSegment.TransformToLocalPosition(worldPos);

                        if (mSegment.hasRange)
                        {
                            localPosInRoot.x = Mathf.Clamp(localPosInRoot.x, mSegment.minX, mSegment.maxX);
                            localPosInRoot.z = Mathf.Clamp(localPosInRoot.z, mSegment.minZ, mSegment.maxZ);
                            worldPos = mSegment.TransformToWorldPosition(localPosInRoot);
                        }

                        var localPos = mSegment.TransformToLocalPosition(worldPos);

                        MoveVertex(mSelectedVertex, localPos);
                        SceneView.RepaintAll();
                    }
                }

                HandleUtility.AddDefaultControl(0);
            }
        }

        void CreateDisplayOutlineVertices()
        {
            var outlineVertices = mSegment.GetOutlineVertices(mSegment.currentOutlineType);
            int n = outlineVertices.Count;
            if (n > 0)
            {
                mLocalSpaceOutlineVertices = new Vector3[n];
                for (int i = 0; i < n; ++i)
                {
                    mLocalSpaceOutlineVertices[i] = outlineVertices[i];
                }
                mWorldSpaceOutlineVertices = new Vector3[mLocalSpaceOutlineVertices.Length];
                mWorldSpaceLines = new Vector3[mWorldSpaceOutlineVertices.Length * 2];

                SceneView.RepaintAll();
            }
            mSelectedVertex = -1;
        }

        void SetEditMode(bool edit)
        {
            mEditingOutline = edit;
            mSelectedVertex = -1;
            mLeftButtonDown = false;
            SceneView.RepaintAll();
        }

        public int PickVertex(Vector3 localPos, float radius)
        {
            //convert to local space radius
            radius = radius / mSegment.gameObject.transform.localScale.x;
            float r2 = radius * radius;
            var outline = mSegment.GetOutlineVertices(mSegment.currentOutlineType);
            for (int i = 0; i < outline.Count; ++i)
            {
                var d = outline[i] - localPos;
                if (d.sqrMagnitude <= r2)
                {
                    return i;
                }
            }
            return -1;
        }

        public void MoveVertex(int index, Vector3 localPos)
        {
            var outline = mSegment.GetOutlineVertices(mSegment.currentOutlineType);
            if (index >= 0 && index < outline.Count)
            {
                outline[index] = localPos;
                mLocalSpaceOutlineVertices[index] = localPos;
                mSegment.dirty = true;
            }
        }

        public void AddVertex(Vector3 localPos)
        {
            if (mSegment.hasRange)
            {
                localPos = mSegment.ClampPosition(localPos);
            }
            var outline = mSegment.GetOutlineVertices(mSegment.currentOutlineType);
            var idx = Utils.FindNearestEdgeDistance(localPos, outline);
            InsertVertex(idx, localPos);
        }

        void InsertVertex(int index, Vector3 localPos)
        {
            var outline = mSegment.GetOutlineVertices(mSegment.currentOutlineType);
            outline.Insert(index, localPos);
            mSegment.Reverse(mSegment.currentOutlineType);
            CreateLoopVertices();
            mSegment.dirty = true;
        }

        public void RemoveVertex(int index)
        {
            var outline = mSegment.GetOutlineVertices(mSegment.currentOutlineType);
            if (index >= 0 && index < outline.Count)
            {
                outline.RemoveAt(index);
                CreateLoopVertices();
                mSegment.dirty = true;
            }
        }

        void CreateLoopVertices()
        {
            var outline = mSegment.GetOutlineVertices(mSegment.currentOutlineType);
            mLocalSpaceOutlineVertices = new Vector3[outline.Count];
            mWorldSpaceOutlineVertices = new Vector3[outline.Count];
            mWorldSpaceLines = new Vector3[mWorldSpaceOutlineVertices.Length * 2];
            for (int i = 0; i < outline.Count; ++i)
            {
                mLocalSpaceOutlineVertices[i] = outline[i];
            }
        }

        //void ExpandPrefabOutline()
        //{
        //    var inputDialog = EditorUtils.CreateInputDialog("Expand Prefab Outline");

        //    var items = new List<InputDialog.Item> {
        //                new InputDialog.StringItem("Radius", "", "1"),
        //            };
        //    inputDialog.Show(items, OnExpandOutline);
        //}

        //bool OnExpandOutline(List<InputDialog.Item> param)
        //{
        //    var radiusStr = (param[0] as InputDialog.StringItem).text;
        //    if (!string.IsNullOrEmpty(radiusStr))
        //    {
        //        float radius;
        //        bool suc = Utils.ParseFloat(radiusStr, out radius);

        //        if (suc)
        //        {
        //            mSegment.Expand(mSegment.currentOutlineType, radius);
        //            CreateDisplayOutlineVertices();
        //            SceneView.RepaintAll();
        //            return true;
        //        }
        //    }
        //    return false;
        //}

        CollisionSegment mSegment;
        Vector3[] mWorldSpaceOutlineVertices;
        Vector3[] mWorldSpaceLines;
        Vector3[] mLocalSpaceOutlineVertices;
        bool mEditingOutline = false;
        bool mLeftButtonDown = false;
        bool mMovingVertex = false;
        float mVertexRadius = 5f;
        int mSelectedVertex = -1;
    }
}
#endif