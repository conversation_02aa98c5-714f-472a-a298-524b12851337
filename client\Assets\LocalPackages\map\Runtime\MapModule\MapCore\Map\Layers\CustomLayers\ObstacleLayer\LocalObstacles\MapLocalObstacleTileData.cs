﻿ 



 
 



//create by wzw on 2019.5.8

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class MapLocalObstacleTileData
    {
        public MapLocalObstacleTileData(Vector3[] vertices, int[] indices, PrefabOutlineType outlineType, float tileSize)
        {
            mVertices = vertices;
            mTriangleIndices = indices;
            mOutlineType = outlineType;
            mTileSize = tileSize;

            CalculateUVs();
        }

        //convert triangle vertices into local space
        public MapLocalObstacleTileData(float tileX, float tileZ, int[] triangleIndices, Vector3[] vertices, PrefabOutlineType outlineType, float tileSize)
        {
            mTileSize = tileSize;
            mOutlineType = outlineType;
            Vector3[] trianglesVertices = new Vector3[triangleIndices.Length];
            for (int i = 0; i < triangleIndices.Length; ++i)
            {
                trianglesVertices[i] = vertices[triangleIndices[i]];
            }

            CreateMeshData(tileX, tileZ, trianglesVertices);

            CalculateUVs();
        }

        public void OnDestroy()
        {
        }

        void CreateMeshData(float tileX, float tileZ, Vector3[] triangleVertices)
        {
            mTriangleIndices = new int[triangleVertices.Length];

            List<Vector3> vertexList = new List<Vector3>();
            for (int i = 0; i < triangleVertices.Length; ++i)
            {
                int idx = -1;
                for (int j = 0; j < vertexList.Count; ++j)
                {
                    if (vertexList[j] == triangleVertices[i])
                    {
                        idx = j;
                        break;
                    }
                }

                if (idx == -1)
                {
                    idx = vertexList.Count;
                    vertexList.Add(triangleVertices[i]);
                }

                mTriangleIndices[i] = idx;
            }

#if UNITY_EDITOR

            //clip every triangle to make sure they are inside mTileSize X mTileSize area
            float minX = tileX;
            float maxX = minX + mTileSize;
            float minZ = tileZ;
            float maxZ = minZ + mTileSize;
            List<Vector3> tileBounds = new List<Vector3>()
            {
                new Vector3(minX, 0, minZ),
                new Vector3(maxX, 0, minZ),
                new Vector3(maxX, 0, maxZ),
                new Vector3(0, 0, maxZ),
            };
            int nTriangles = mTriangleIndices.Length / 3;
            var tv = new List<Vector3>();
            List<Vector3> clippedVertices = new List<Vector3>();
            List<int> clippedTriangleIndices = new List<int>();
            for (int t = 0; t < nTriangles; ++t)
            {
                tv.Clear();
                tv.Add(vertexList[mTriangleIndices[t * 3 + 2]]);
                tv.Add(vertexList[mTriangleIndices[t * 3 + 1]]);
                tv.Add(vertexList[mTriangleIndices[t * 3]]);

                bool invalidTriangle = true;
                for (int i = 0; i < 3; ++i)
                {
                    if (tv[i].x > minX && tv[i].x < maxX && tv[i].z > minZ && tv[i].z < maxZ)
                    {
                        invalidTriangle = false;
                        break;
                    }
                }

                if (!invalidTriangle)
                {
                    var vertices = PolygonAlgorithm.GetPolygonIntersections(tv, tileBounds);
                    if (vertices.Count > 0)
                    {
                        Vector3[] pv;
                        int[] pi;
                        Triangulator.TriangulatePolygon(vertices, out pv, out pi);

                        for (int v = 0; v < pi.Length; ++v)
                        {
                            var pos = pv[pi[v]];

                            ////temp code
                            //if (pos.x < minX || pos.x > maxX || pos.z < minZ || pos.z > maxZ)
                            //{
                            //    D.Log("out range vertex: " + pos);
                            //    int a = 1;
                            //}

                            int idx = clippedVertices.IndexOf(pos);
                            if (idx == -1)
                            {
                                clippedVertices.Add(pos);
                                idx = clippedVertices.Count - 1;
                            }
                            clippedTriangleIndices.Add(idx);
                        }
                    }
                }
            }
            mTriangleIndices = clippedTriangleIndices.ToArray();
            mVertices = clippedVertices.ToArray();
#else
            mVertices = vertexList.ToArray();
#endif
            //convert vertices to local space
            for (int i = 0; i < mVertices.Length; ++i)
            {
                mVertices[i] = new Vector3(mVertices[i].x - tileX, 0, mVertices[i].z - tileZ);
                if (mVertices[i].x > mTileSize || mVertices[i].z > mTileSize || mVertices[i].x < 0 || mVertices[i].z < 0)
                {
                    Debug.LogError(string.Format("{0} outline vertex is out of range!", mOutlineType.ToString()));
                }
            }
        }

        //todo,可移到编辑器中计算然后导出
        void CalculateUVs()
        {
            float maxX = float.MinValue;
            float maxZ = float.MinValue;
            float minX = float.MaxValue;
            float minZ = float.MaxValue;

            for (int i = 0; i < mVertices.Length; ++i)
            {
                var pos = mVertices[i];
                if (pos.x > maxX)
                {
                    maxX = pos.x;
                }
                if (pos.z > maxZ)
                {
                    maxZ = pos.z;
                }
                if (pos.x < minX)
                {
                    minX = pos.x;
                }
                if (pos.z < minZ)
                {
                    minZ = pos.z;
                }
            }
            
            mUVs = new Vector2[mVertices.Length];
            for (int i = 0; i < mVertices.Length; ++i)
            {
                float u = (mVertices[i].x - minX);
                float v = (mVertices[i].z - minZ);
                mUVs[i] = new Vector2(u, v);
            }
        }

        public int[] triangles { get { return mTriangleIndices; } }
        public Vector3[] vertices { get { return mVertices; } }
        public Vector2[] uvs { get { return mUVs; } }

        PrefabOutlineType mOutlineType;
        int[] mTriangleIndices;
        Vector3[] mVertices;
        Vector2[] mUVs;
        float mTileSize;
    }
}
