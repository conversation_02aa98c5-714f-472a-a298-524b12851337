package com.common.android;
import android.app.Activity;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.content.pm.PackageManager;
import android.content.pm.PackageInfo;
import android.os.Vibrator;
import android.app.Service;
import android.content.Intent;
import android.os.Bundle;

public class AndroidUtils
{
	
	//获取系统版本名字
	public static String getVersionName()
	{
	  try {
		 String str = android.os.Build.VERSION.RELEASE;
		  return  str;
	  } catch (Exception e) {
		 e.printStackTrace();
	  }
	  return "";
	}
   
	//获取当前程序的VersionCode
	public static String getVersionCode(Context context){  

			PackageManager packageManager=context.getPackageManager();  

			PackageInfo packageInfo;  

			String versionCode="";  

			try {  

				 packageInfo=packageManager.getPackageInfo(context.getPackageName(),0);  

				 versionCode=packageInfo.versionCode+"";  

			} catch (PackageManager.NameNotFoundException e) {  

				e.printStackTrace();  

			}  

			return versionCode;  

	}  
   
	//跳转到谷歌商店
	public static void JumpGoogleAppDetail(Activity context, String appPkg, String marketPkg)
	{
	  try {
		 //Uri uri = Uri.parse("https://play.google.com/store/search?q=kingdom%20guard&c=apps" + appPkg);
		 Uri uri = Uri.parse("https://play.google.com/store/search?q=KingdomGuard&c=apps");
		 //Uri uri = Uri.parse("market://search?q=kingdom%20guard&c=apps");

		 Intent intent = new Intent(Intent.ACTION_VIEW, uri);
		 //intent.setPackage(marketPkg);
		 intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		 intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
		 context.startActivity(intent);
		 context.finish();
		 System.exit(0);
	  } catch (Exception e) {
		 e.printStackTrace();
	  }
	}
	
	//跳转到谷歌商店
    public static void JumpGoogleAppDetail2(Activity context, String uriString, String marketPkg)
    {
      try {
         //Uri uri = Uri.parse("https://play.google.com/store/search?q=kingdom%20guard&c=apps" + appPkg);
         Uri uri = Uri.parse(uriString);
         //Uri uri = Uri.parse("market://search?q=kingdom%20guard&c=apps");

         Intent intent = new Intent(Intent.ACTION_VIEW, uri);
         //intent.setPackage(marketPkg);
         intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
         intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
         context.startActivity(intent);
         context.finish();
         System.exit(0);
      } catch (Exception e) {
         e.printStackTrace();
      }
    }
	
	//振动
	public static void UnityCallShake(Activity context,long[] time)
	{
		Vibrator vibrator=(Vibrator)context.getApplication().getSystemService(Service.VIBRATOR_SERVICE);
		vibrator.vibrate(time,-1);
	}
		
		
		
	//游戏重启
	public static void RestartApplicationSelf(Activity context)
	{  
		 new Thread(){
			public void run(){
				Intent launch = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
				launch.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
				context.startActivity(launch);  
				android.os.Process.killProcess(android.os.Process.myPid());
			}
		 }.start();
		 context.finish();  
    }  
	
	
	//分享链接
	public static void ShareText(Context context, String title, String text)
	{
		 Intent intent = new Intent(Intent.ACTION_SEND);
		 intent.setType("text/plain");
		 intent.putExtra(Intent.EXTRA_TEXT, text);
		 context.startActivity(Intent.createChooser(intent, title));
	}
}