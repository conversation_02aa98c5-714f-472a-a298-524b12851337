﻿using Common;
using Config;
using DG.Tweening;
using Game.Data;
using Game.Utils;
using Logic;
using System.Collections.Generic;
using TFW;
using TFW.UI;
using UnityEngine;

namespace K3
{
    public class K3ResText : MonoBehaviour
    {
        public enum ResEnum
        {
            Coin,
            Diamond,
            Alliance_Sliver,
            Wood,
            Star,
            Energy,
            Solider,
            HeroStar,
            //转盘券
            Item_SpinTicket,
            HeroExp,
        }

        public K3ResText.ResEnum resID = 0;

        public GameObject btnAdd;

        public TFWImage mIcon;

        private TFWText textLabel;

        public TFWSlider starSlider;

        public GameObject flyPrefab;

        public Transform flyTarget, flyParent;

        public void Init()
        {
            textLabel = GetComponent<TFWText>();
            RefreshRes();

            if (btnAdd && btnAdd.activeSelf)
            {
                UI.UIBase.AddRemoveListener(EventTriggerType.Click, btnAdd, (x, y) => { AddRes(); });
            }

            if (K3PlayerMgr.I.K3ResFlyDic != null)
            {
                if (!K3PlayerMgr.I.K3ResFlyDic.ContainsKey((int)resID))
                {
                    K3PlayerMgr.I.K3ResFlyDic.Add((int)resID, new List<K3ResText>());
                }

                if (!K3PlayerMgr.I.K3ResFlyDic[(int)resID].Contains(this))
                {
                    K3PlayerMgr.I.K3ResFlyDic[(int)resID].Add(this);
                }
            }

            EventMgr.RegisterEvent(TEventType.RefreshAssetAck, (objs) =>
            {
                RefreshRes();
            }, this);

            EventMgr.RegisterEvent(TEventType.RefreshHeroData, (objs) =>
            {
                RefreshRes();
            }, this);

            EventMgr.RegisterEvent(TEventType.ExChangeHeroEnd, (objs) => { RefreshRes(); }, this);


            switch (resID)
            {
                case ResEnum.Coin:
                    UITools.SetVMIcon(mIcon, ConfigID.VM_Gold);
                    break;
                case ResEnum.Diamond:
                    UITools.SetVMIcon(mIcon, ConfigID.VM_Diamond);
                    break;
                case ResEnum.Alliance_Sliver:
                    UITools.SetVMIcon(mIcon, ConfigID.VM_UnionStoreCoin);
                    break;
                case ResEnum.Wood:
                    UITools.SetItemIcon(mIcon, ConfigID.Item_Wood);
                    break;
                case ResEnum.Star:
                    UITools.SetVMIcon(mIcon, ConfigID.VM_Star);
                    break;
                case ResEnum.Energy:
                    UITools.SetRecoverIcon(mIcon, ConfigID.Recover_Energy);
                    break;
                case ResEnum.Solider:
                    UITools.SetVMIcon(mIcon, ConfigID.vm_soldier);
                    break;
                case ResEnum.Item_SpinTicket:
                    UITools.SetItemIcon(mIcon, ConfigID.Item_SpinTicket);
                    break;
                default:
                    break;
            }
        }

        public void ResFlyAni(Vector3 startGa, int flyNum)
        { 
            if (resID == K3ResText.ResEnum.Coin)
            {
                if (flyTarget && flyPrefab)
                {
                    UITools.D7Fly(Config.ConfigID.VM_Gold, flyPrefab, flyParent, startGa,
                        flyTarget.position, flyNum, () =>
                        {
                            var animation = flyTarget.GetComponent<Animation>();
                            if (animation != null)
                                animation.Play();
                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();
                        }, () =>
                        {
                            DOTween.Kill(flyTarget.transform);
                            flyTarget.transform.localScale = Vector3.one;
                        });
                }
            }

            if (resID == K3ResText.ResEnum.Wood)
            {
                if (flyTarget && flyPrefab)
                {
                    UITools.D7Fly(Config.ConfigID.Item_Wood, flyPrefab, flyParent, startGa,
                        flyTarget.position, flyNum, () =>
                        {
                            var animation = flyTarget.GetComponent<Animation>();
                            if (animation != null)
                                animation.Play();
                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();
                        }, () =>
                        {
                            DOTween.Kill(flyTarget.transform);
                            flyTarget.transform.localScale = Vector3.one;
                        });
                }
            }

            if (resID == K3ResText.ResEnum.Item_SpinTicket)
            {
                if (flyTarget && flyPrefab)
                {
                    UITools.D7Fly(Config.ConfigID.Item_SpinTicket, flyPrefab, flyParent, startGa,
                        flyTarget.position, flyNum, () =>
                        {
                            var animation = flyTarget.GetComponent<Animation>();
                            if (animation != null)
                                animation.Play();
                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();
                        }, () =>
                        {
                            DOTween.Kill(flyTarget.transform);
                            flyTarget.transform.localScale = Vector3.one;
                        });
                }
            }

            if (resID == K3ResText.ResEnum.Diamond)
            {
                if (flyTarget && flyPrefab)
                {
                    UITools.D7Fly(Config.ConfigID.VM_Diamond, flyPrefab, flyParent, startGa,
                        flyTarget.position, flyNum, () =>
                        {
                            var animation = flyTarget.GetComponent<Animation>();
                            if (animation != null)
                                animation.Play();
                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();
                        }, () =>
                        {
                            DOTween.Kill(flyTarget.transform);
                            flyTarget.transform.localScale = Vector3.one;
                        });
                }
            }

            if (resID == K3ResText.ResEnum.Energy)
            {
                if (flyTarget && flyPrefab)
                {
                    UITools.D7Fly(Config.ConfigID.Recover_Energy, flyPrefab, flyParent, startGa,
                        flyTarget.position, flyNum, () =>
                        {
                            var animation = flyTarget.GetComponent<Animation>();
                            if (animation != null)
                                animation.Play();
                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();
                        }, () =>
                        {
                            DOTween.Kill(flyTarget.transform);
                            flyTarget.transform.localScale = Vector3.one;
                        });
                }
            }

            if (resID == K3ResText.ResEnum.Star)
            {
                if (flyTarget && flyPrefab)
                {
                    UITools.D7Fly(Config.ConfigID.VM_Star, flyPrefab, flyParent, startGa,
                        flyTarget.position, flyNum, () =>
                        {
                            var animation = flyTarget.GetComponent<Animation>();
                            if (animation != null)
                                animation.Play();
                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();
                        }, () =>
                        {

                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();

                            NTimer.CountDown(1f, () =>
                            {
                                if (flyTarget != null)
                                {
                                    DOTween.Kill(flyTarget.transform);
                                    flyTarget.transform.localScale = Vector3.one;
                                }
                            });
                        });
                }
            }


            if (resID == K3ResText.ResEnum.HeroStar)
            {
                if (flyTarget && flyPrefab)
                {
                    UITools.D7Fly(460, flyPrefab, flyParent, startGa,
                        flyTarget.position, flyNum, () =>
                        {
                            var animation = flyTarget.GetComponent<Animation>();
                            if (animation != null)
                                animation.Play();
                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();
                        }, () =>
                        {
                            //flyTarget.GetComponentInChildren<ParticleSystem>()?.Play();


                            if (flyTarget != null)
                            {
                                DOTween.Kill(flyTarget.transform);
                                flyTarget.transform.localScale = Vector3.one;
                            }

                        });
                }
            }
        }

        private void OnEnable()
        {
            Init();

            if (!Game.Data.GameData.I.MainData.ResFlyTarget.ContainsKey((int)resID))
            {
                Game.Data.GameData.I.MainData.ResFlyTarget.Add((int)resID, new List<Transform>());
            }

            Game.Data.GameData.I.MainData.ResFlyTarget[(int)resID].Add(flyParent);
        }

        private void OnDisable()
        {
            curValue = -1;

            if (Game.Data.GameData.I.MainData.ResFlyTarget.TryGetValue((int)resID, out var resList))
            {
                resList.Remove(flyParent);
            }

            EventMgr.UnregisterEvent(this);

            if (K3PlayerMgr.I.K3ResFlyDic != null && K3PlayerMgr.I.K3ResFlyDic.ContainsKey((int)resID))
            {
                K3PlayerMgr.I.K3ResFlyDic[(int)resID].Remove(this);
            }
        }

        private long curValue = -1;

        private void RefreshRes()
        {

            if (textLabel != null)
            {
                long curResValue = 0;

                switch (resID)
                {
                    case K3ResText.ResEnum.Coin:
                        curResValue = K3PlayerMgr.I.PlayerData?.gold ?? 0;
                        break;

                    case K3ResText.ResEnum.Diamond:
                        curResValue = K3PlayerMgr.I.PlayerData?.diamond ?? 0;
                        break;

                    case K3ResText.ResEnum.Energy:
                        curResValue = K3PlayerMgr.I.PlayerData?.energy ?? 0;
                        break;

                    case K3ResText.ResEnum.Star:
                        curResValue = K3PlayerMgr.I.PlayerData?.star ?? 0;
                        break;
                    case ResEnum.Solider:
                        curResValue = LogicCreateSoldier.I.Total;
                        break;

                    case ResEnum.HeroStar:
                        curResValue = GetHasExp();
                        break;

                    case ResEnum.Item_SpinTicket:
                        curResValue = PlayerAssetsMgr.I.GetItemCountByID(ConfigID.Item_SpinTicket);
                        break;

                    default:
                        break;
                }

                if (curResValue <= 0)
                    curResValue = 0;


                if (curValue >= 0 && curValue < curResValue)
                {
                    RefreshNum(curResValue);
                }
                else if (curValue != curResValue)
                {
                    curValue = curResValue;

                    if (resID == K3ResText.ResEnum.Diamond || resID == ResEnum.Energy)// || resID == ResEnum.HeroStar)
                    {
                        textLabel.text = UIStringUtils.FormatIntegerByLanguage(curResValue);
                    }
                    else if (resID == K3ResText.ResEnum.Star)
                    {
                        int totalNeedStar = 1;// GetTotalNeedStar();

                        if (curResValue < totalNeedStar)
                        {
                            textLabel.text = $"<color=#ff0000>{curResValue}</color>/{totalNeedStar}";
                        }
                        else
                        {
                            textLabel.text = $"{curResValue}/{totalNeedStar}";
                        }

                        if (starSlider)
                        {
                            starSlider.minValue = 0;
                            starSlider.maxValue = totalNeedStar;
                            DOTween.Kill(starSlider);
                            starSlider.DOValue(curValue, 0.38f);
                        }
                    }
                    else
                    {
                        textLabel.text = UIStringUtils.FormatIntUnitByLanguage(curResValue);
                    }
                }
            }
        }

        private long GetHasExp()
        {
            long curExp = PlayerAssetsMgr.I.GetVMCount(Config.ConfigID.vm_heroExp);
            //var costHeros = HeroGameData.I.GetExpHeros();

            //for (int i = 0; i < costHeros.Count; i++)
            //{
            //    var heroData = costHeros[i];

            //    if (HeroGameData.I.mCultivateMap.TryGetValue(heroData.ModelId, out var CurHeroCount))
            //    {
            //        if (CurHeroCount <= 1)
            //        {
            //            continue;
            //        }

            //        var iiExp = HeroUtils.GetHeroExp(heroData);
            //        curExp = curExp + (iiExp * (CurHeroCount - 1));
            //    }
            //}

            return curExp;
        }

        //private int GetTotalNeedStar()
        //{
        //    int totalNeedStar = 0;

        //    int displayCfgID = LPlayer.I.GetNowCityBuildingID();

        //    if (displayCfgID > 0 && Cfg.C.CD2CityBuilding.I(displayCfgID).OpenState)
        //    {
        //        foreach (var ii in Cfg.C.CD2CityBuilding.I(displayCfgID).ProgressCost)
        //        {
        //            if (ii.Id == ConfigID.VM_Star)
        //            {
        //                totalNeedStar += ii.Val;
        //            }
        //        }
        //    }

        //    if (totalNeedStar > 0)
        //    {
        //    }
        //    else
        //    {
        //        //��ʾ�Ƽ�
        //        if (GameData.I.SkillData.MTechs.TryGetValue(1, out var data) && data.MConfig != null)
        //        {
        //            foreach (var ii in data.MConfig.UpgradeCost)
        //            {
        //                if (ii.Id == ConfigID.VM_Star)
        //                {
        //                    totalNeedStar += ii.Val;
        //                }
        //            }
        //        }
        //    }

        //    return totalNeedStar;
        //}

        private Tweener RefreshNumTween;

        private void RefreshNum(long targetValue)
        {
            if (RefreshNumTween != null)
            {
                RefreshNumTween.Kill();
            }

            int totalNeedStar = 1;// GetTotalNeedStar();
            RefreshNumTween = DOTween.To(() => curValue, (v) =>
            {
                curValue = v;
                if (resID == K3ResText.ResEnum.Diamond || resID == ResEnum.Energy)// || resID == ResEnum.HeroStar)
                {
                    textLabel.text = UIStringUtils.FormatIntegerByLanguage(curValue);
                }
                else if (resID == K3ResText.ResEnum.Star)
                {
                    if (curValue < totalNeedStar)
                    {
                        textLabel.text = $"<color=#ff0000>{curValue}</color>/{totalNeedStar}";
                    }
                    else
                    {
                        textLabel.text = $"{curValue}/{totalNeedStar}";
                    }

                    if (starSlider)
                    {
                        starSlider.minValue = 0;
                        starSlider.maxValue = totalNeedStar;
                        DOTween.Kill(starSlider);
                        starSlider.DOValue(curValue, 0.38f);
                    }
                }
                else
                {
                    textLabel.text = UIStringUtils.FormatIntUnitByLanguage(curValue);
                }
            }, targetValue, 0.36f);
        }

        public void AddRes()
        {
            UITools.AddRes(resID);
        }

        private bool waitingAniToRefreshText;

        public void RefreshRes(int count, bool isImmediately = false)
        {
            if (count > 0 && !isImmediately)
            {
                NTimer.CountDown(1.2f, () =>
                {
                    RefreshRes();//�����ɺ�ͬ���� �¼�ͬ��Ϊ����ͬ��~
                });
            }
            else
            {
                RefreshRes();
            }
        }
    }
}