fileFormatVersion: 2
guid: 2211c81436fb9d345b68039643e0fcf9
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: 2_0
      rect:
        serializedVersion: 2
        x: 9
        y: 802
        width: 714
        height: 641
      alignment: 9
      pivot: {x: 0.59639484, y: 0.104063325}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b20faff6311ed28479a565933d17e54d
      internalID: 2009274380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2_1
      rect:
        serializedVersion: 2
        x: 0
        y: 1467
        width: 704
        height: 572
      alignment: 9
      pivot: {x: 0.6833076, y: 0.15406576}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b460bfef0f5b9154d9b6471dbaa6e8e2
      internalID: -645632131
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2_2
      rect:
        serializedVersion: 2
        x: 0
        y: 149
        width: 699
        height: 606
      alignment: 9
      pivot: {x: 0.64279234, y: 0.15408953}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aec7c4b67fd86754fad3540f28905900
      internalID: -1439984732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2_3
      rect:
        serializedVersion: 2
        x: 708
        y: 164
        width: 679
        height: 611
      alignment: 9
      pivot: {x: 0.7027273, y: 0.07758248}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9853f1da4ec11ed4490b9c0d66345022
      internalID: 2004173139
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2_4
      rect:
        serializedVersion: 2
        x: 872
        y: 1904
        width: 119
        height: 124
      alignment: 9
      pivot: {x: 0.44577822, y: 0.20513128}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d553523fd837d364db6a398fb2887e27
      internalID: -1003634578
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2_5
      rect:
        serializedVersion: 2
        x: 1141
        y: 1899
        width: 138
        height: 134
      alignment: 9
      pivot: {x: 0.5779286, y: 0.25923818}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7ed14ea264a421547807e841a041c62b
      internalID: 1736728524
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2_6
      rect:
        serializedVersion: 2
        x: 836
        y: 1607
        width: 110
        height: 120
      alignment: 9
      pivot: {x: 0.4804471, y: 0.24907227}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: db4f806eb4a142f4396ce7fb76f4ad20
      internalID: -230342270
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: c9f24f23dc47b1941b60e13c8fda3e63
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      2_0: 2009274380
      2_1: -645632131
      2_2: -1439984732
      2_3: 2004173139
      2_4: -1003634578
      2_5: 1736728524
      2_6: -230342270
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
