// Amplify Shader Editor - Visual Shader Editing Tool
// Copyright (c) Amplify Creations, Lda <<EMAIL>>

namespace AmplifyShaderEditor
{
	[System.Serializable]
	[NodeAttributes( "Object To World Matrix", "Matrix Transform", "Current model matrix" )]
	public sealed class ObjectToWorldMatrixNode : ConstantShaderVariable
	{
		protected override void CommonInit( int uniqueId )
		{
			base.CommonInit( uniqueId );
			ChangeOutputProperties( 0, "Out", WirePortDataType.FLOAT4x4 );
            m_value = "unity_ObjectToWorld";
			m_HDValue = "GetObjectToWorldMatrix()";
			m_LWValue = "GetObjectToWorldMatrix()"; 
		}
    }
}
