﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class SliderUpdateText : MonoBehaviour
{
    public Slider targetSlider;
    public Text mText;

    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        mText.text = $"{Mathf.Clamp( (int)( targetSlider.value*100),0,100)}%";
    }
}
