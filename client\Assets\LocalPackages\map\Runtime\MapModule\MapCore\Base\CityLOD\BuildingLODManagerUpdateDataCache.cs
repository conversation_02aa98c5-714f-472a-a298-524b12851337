﻿ 



 
 

﻿using UnityEngine;

//cache一些update的值
namespace TFW.Map
{
    public static class BuildingLODManagerUpdateDataCache
    {
        public static void Init(float collapseHeight, float iconicHeight)
        {
            mCityIconicCameraHeight = iconicHeight;
            mCityCollapseCameraHeight = collapseHeight;
        }

        public static void Update()
        {
            float cameraHeight = MapCameraMgr.currentCameraHeight;
            float lastCameraHeight = MapCameraMgr.lastCameraHeight;
            mIsValidUpperHeight = (Utils.LE(cameraHeight, mCityIconicCameraHeight) || Utils.LE(lastCameraHeight, mCityIconicCameraHeight));
            mIsValidLowerHeight = (Utils.GE(cameraHeight, mCityCollapseCameraHeight) || Utils.GE(lastCameraHeight, mCityCollapseCameraHeight));
            float clampedCameraHeight = Mathf.Clamp(cameraHeight, mCityCollapseCameraHeight, mCityIconicCameraHeight);
            mFOVAtCurrentCameraHeight = Map.currentMap.GetCameraFOVAtHeight(clampedCameraHeight);
            mViewportWidthAtCurrentCameraHeight = Utils.CalculateViewportWidth(mFOVAtCurrentCameraHeight, Map.currentMap);
        }

        public static float fovAtCurrentCameraHeight { get { return mFOVAtCurrentCameraHeight; } }
        public static float viewportWidthAtCurrentCameraHeight { get { return mViewportWidthAtCurrentCameraHeight; } }
        public static bool isValidUpperHeight { get { return mIsValidUpperHeight; } }
        public static bool isValidLowerHeight { get { return mIsValidLowerHeight; } }
        public static float collapseCameraHeight { get { return mCityCollapseCameraHeight; } }
        public static float iconicCameraHeight { get { return mCityIconicCameraHeight; } }

        static float mFOVAtCurrentCameraHeight;
        static float mViewportWidthAtCurrentCameraHeight;
        static float mCityCollapseCameraHeight;
        static float mCityIconicCameraHeight;
        static bool mIsValidUpperHeight;
        static bool mIsValidLowerHeight;
    }
}
