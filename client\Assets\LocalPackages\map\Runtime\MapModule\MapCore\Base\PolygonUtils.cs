﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map {
    public class PolygonUtils {
        public static bool SegmentSegmentIntersection(Vector3 sa, Vector3 ea, Vector3 sb, Vector3 eb) {
            var d1 = ea - sa;
            var d2 = eb - sb;
            var k = sb - sa;
            float delta = d1.z * d2.x - d1.x * d2.z;
            if (Mathf.Approximately(delta, 0)) {
                return false;
            }

            float t1 = (k.z * d2.x - k.x * d2.z) / delta;
            float t2 = (d1.x * k.z - d1.z * k.x) / delta;
            float epslon = 0.00001f;
            return t1 > epslon && t1 < 1 - epslon && t2 > epslon && t2 < 1.0f - epslon;
        }

        public static bool IsSimplePolygon(List<Vector3> outline) {
            //暴力算法太慢,等有空研究下快速算法,先屏蔽,对功能没有影响
#if false
            int nSegments = outline.Count;
            for (int i = 0; i < nSegments; ++i) {
                for (int j = i + 1; j < nSegments; ++j) {
                    if (SegmentSegmentIntersection(outline[i], outline[(i + 1) % nSegments], outline[j], outline[(j + 1) % nSegments])) {
                        return false;
                    }
                }
            }
#endif
            return true;
        }

        public static bool IsConvexPolygon(List<Vector3> polygon)
        {
            return true;
        }
    }
}