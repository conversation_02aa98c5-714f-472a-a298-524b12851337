﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/8/15

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //地图上的碰撞数据,用于生成navmesh, npc刷新点等, 地图障碍物等
    public class MapCollisionData : PolygonObjectData
    {
        //isObstacle:是否在生成地图障碍物数据时考虑这个obstacle
        public MapCollisionData(int id, Map map, OutlineData[] outlines, float displayRadius, bool isExtendable, CollisionAttribute attribute, int type, bool checkWindingOrder) : base(id, map, outlines, displayRadius, isExtendable, checkWindingOrder)
        {
            mAttribute = attribute;
            mType = type;
        }

        public override void OnDestroy()
        {
        }

        public void AddAttribute(CollisionAttribute attr)
        {
            mAttribute |= attr;
        }

        public void RemoveAttribute(CollisionAttribute attr)
        {
            mAttribute &= ~attr;
        }

        public bool HasAttribute(CollisionAttribute attr)
        {
            return mAttribute.HasFlag(attr);
        }

        public bool FindSnapPosition(PrefabOutlineType type, Vector3 pos, float radius, out Vector3 snapPos)
        {
            snapPos = Vector3.zero;
            var outlines = mOutlines[(int)type].outline;
            int n = outlines.Count;
            float r2 = radius * radius;
            for (int i = 0; i < n; ++i)
            {
                if ((outlines[i] - pos).sqrMagnitude <= r2)
                {
                    snapPos = outlines[i];
                    return true;
                }
            }
            return false;
        }

        public CollisionAttribute attribute { get { return mAttribute; } }
        public int type { get { return mType; } set { mType = value; } }

        CollisionAttribute mAttribute = 0;
        int mType;
    }
}


#endif