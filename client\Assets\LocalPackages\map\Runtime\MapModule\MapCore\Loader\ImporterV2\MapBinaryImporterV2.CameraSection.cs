﻿ 



 
 

﻿
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadCamera(BinaryReader reader)
        {
            reader.BaseStream.Position = GetSectionDataStartPosition(MapDataSectionType.Camera);
            int version = reader.ReadInt32();

            //--------------------version 1 start--------------------------
            var cameraInfo = new config.Camera();
            cameraInfo.position = Utils.ReadVector3(reader);
            cameraInfo.rotation = Utils.ReadQuaternion(reader);
            cameraInfo.orthographic = reader.ReadBoolean();
            cameraInfo.orthongonalSize = reader.ReadSingle();
            cameraInfo.verticalFov = reader.ReadSingle();

            mEditorData.map.camera = cameraInfo;
            //--------------------version 1 end--------------------------
        }
    }
}
