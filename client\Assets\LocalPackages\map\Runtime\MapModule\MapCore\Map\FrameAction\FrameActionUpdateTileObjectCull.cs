﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //管理tile object的视野剔除
    public class FrameActionUpdateTileObjectCull : FrameAction
    {
        public static FrameActionUpdateTileObjectCull Require(TileGridObjectLayerData layerData)
        {
            var act = mPool.Require();
            act.Init(layerData);
            return act;
        }

        void Init(TileGridObjectLayerData layerData)
        {
            InitAction();
            mLayerData = layerData;
            mKey = MakeKeyHelper(layerData.id, type);
        }

        protected override void DoImpl()
        {
            mLayerData.UpdateCulling();
        }

        public override bool isEnabled
        {
            get
            {
                return true;
            }
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mPool.Release(this);
        }

        public override long key { get { return mKey; } }
        public override FrameActionType type => FrameActionType.UpdateTileObjectCull;
        public override string name => "Update Tile Object Cull";
        public override string debugInfo => "Update Tile Object Cull";
        public override bool keepAlive => true;

        TileGridObjectLayerData mLayerData;        
        long mKey;

        static ObjectPool<FrameActionUpdateTileObjectCull> mPool = new ObjectPool<FrameActionUpdateTileObjectCull>(1, () => new FrameActionUpdateTileObjectCull());
    }
}
