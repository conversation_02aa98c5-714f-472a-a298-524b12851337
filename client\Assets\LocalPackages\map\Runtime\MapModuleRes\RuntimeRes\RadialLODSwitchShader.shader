﻿// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Map/RadialBlur"
{
	Properties
	{
		_Tex("Base (RGB)", 2D) = "white" {}
		_Strength("Strength", Range(0, 1)) = 0
	}

	SubShader
	{
		Pass
		{
			Tags { "LightMode" = "ForwardBase" }
			ZTest Always Cull Off ZWrite Off

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma fragmentoption ARB_precision_hint_fastest
			#include "UnityCG.cginc"

			sampler2D _Tex;
			float _Strength;

			v2f_img vert(appdata_img v)
			{
				v2f_img o;
				o.pos = UnityObjectToClipPos(v.vertex);
				o.uv = v.texcoord;
				return o;
			}

			fixed4 frag (v2f_img i) : COLOR
			{
				fixed4 main = 0;
				half2 center = float2(0.5, 0.5);
				i.uv = i.uv - center;
				for(int j = 1; j < 11; j++)
				{
					float scale = 1 - (_Strength * j / 10.0);
					main.rgb += tex2D(_Tex, i.uv * scale + center);
				}
				main.rgb /= 10;
				return main;
			}
			ENDCG
		}
	}

	Fallback off
}
