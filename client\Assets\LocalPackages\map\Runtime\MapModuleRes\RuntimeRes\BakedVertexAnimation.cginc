﻿
#ifdef ENABLE_SKIN_ANIM_BLENDING
#define GetVertexPosition		GetVertexPositionNormal
#else
#define GetVertexPosition		GetVertexPositionBlending
#endif

#define TEXTURE_SKIN_APP_DATA \
			UNITY_VERTEX_INPUT_INSTANCE_ID

sampler2D_half _AnimationData;

//use text2Dlod to sample texture in vertex shader
float4 GetVertexPositionNormal(float vertexIndex) {
    float4 animTextureSize = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimTextureSize);
    float4 animParams = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams);
    float2 uvOffset = float2(1.0 / animTextureSize.x * 0.5, 1.0 / animTextureSize.y * 0.5);
    //_AnimationParams.x is vertex count
    float u = (float)vertexIndex / animParams.x;
    //_AnimationParams.y is v
    float4 pos = tex2Dlod(_AnimationData, float4(u + uvOffset.x, animParams.y + uvOffset.y, 0, 0));
    return pos;
}

float4 GetVertexPositionBlending(float vertexIndex) {
    float4 animTextureSize = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimTextureSize);
    float4 animParams = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams);
    float blend = UNITY_ACCESS_INSTANCED_PROP(Props, _BlendParams);
    float2 uvOffset = float2(1.0 / animTextureSize.x * 0.5, 1.0 / animTextureSize.y * 0.5);
    //_AnimationParams.x is vertex count
    float u = (float)vertexIndex / animParams.x;
    //_AnimationParams.y is current animation v
    float4 curPos = tex2Dlod(_AnimationData, float4(u + uvOffset.x, animParams.y + uvOffset.y, 0, 0));
    //_AnimationParams.z is next animation v
    float4 nextPos = tex2Dlod(_AnimationData, float4(u + uvOffset.x, animParams.z + uvOffset.y, 0, 0));
    return curPos * (1 - blend) + nextPos * blend;
}