﻿ 



 
 

﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class DetailSprites
    {
        Rect mLastUpdateViewport;

        void UpdateViewport(Rect newViewport)
        {
            if (MapCameraMgr.currentCameraHeight <= mAlpha0Height)
            {
                if (mLastUpdateViewport != newViewport)
                {
                    int oldMinX, oldMinY, oldMaxX, oldMaxY;
                    int newMinX, newMinY, newMaxX, newMaxY;
                    GetGridRange(mLastUpdateViewport, out oldMinX, out oldMinY, out oldMaxX, out oldMaxY);
                    GetGridRange(newViewport, out newMinX, out newMinY, out newMaxX, out newMaxY);

                    int maxR = mRows * mHorizontalGridCount;
                    int maxC = mCols * mHorizontalGridCount;

                    for (int i = oldMinY; i <= oldMaxY; ++i)
                    {
                        for (int j = oldMinX; j <= oldMaxX; ++j)
                        {
                            if (!(i >= newMinY && i <= newMaxY &&
                                j >= newMinX && j <= newMaxX))
                            {
                                if (i >= 0 && j >= 0 && i < maxR && j < maxC)
                                {
                                    int gx = j / mHorizontalGridCount;
                                    int sx = j % mHorizontalGridCount;
                                    int gy = i / mHorizontalGridCount;
                                    int sy = i % mHorizontalGridCount;
                                    RemoveObjects(gx, gy, sx, sy);
                                }
                            }
                        }
                    }

                    bool generated = false;
                    for (int i = newMinY; i <= newMaxY; ++i)
                    {
                        for (int j = newMinX; j <= newMaxX; ++j)
                        {
                            if (!(i >= oldMinY && i <= oldMaxY &&
                                j >= oldMinX && j <= oldMaxX))
                            {
                                if (i >= 0 && j >= 0 && i < maxR && j < maxC)
                                {
                                    int gx = j / mHorizontalGridCount;
                                    int sx = j % mHorizontalGridCount;
                                    int gy = i / mHorizontalGridCount;
                                    int sy = i % mHorizontalGridCount;
                                    GenerateObjects(gx, gy, sx, sy, true);
                                    generated = true;
                                }
                            }
                        }
                    }

                    if (generated)
                    {
                        UpdateFading();
                        UpdateScale();
                    }

                    mLastUpdateViewport = newViewport;
                }
            }
            else
            {
                mLastUpdateViewport = new Rect(-10000, -10000, 0, 0);
                ReleaseAllSprites();
            }
        }

        void GetGridRange(Rect viewport, out int minX, out int minY, out int maxX, out int maxY)
        {
            minX = Mathf.FloorToInt(viewport.xMin / mGridSize);
            minY = Mathf.FloorToInt(viewport.yMin / mGridSize);
            maxX = Mathf.FloorToInt(viewport.xMax / mGridSize);
            maxY = Mathf.FloorToInt(viewport.yMax / mGridSize);
        }
    }
}
