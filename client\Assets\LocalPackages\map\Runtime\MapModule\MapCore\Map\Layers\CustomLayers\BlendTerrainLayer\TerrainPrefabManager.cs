﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class TerrainPrefab
    {
        public string[] prefabPaths;
        public int fixedSubGroupPrefabIndex = -1;

        public TerrainPrefab(string firstPrefabPath)
        {
            prefabPaths = new string[1];
            prefabPaths[0] = firstPrefabPath;
        }

        public void SetSubGroupPrefabPath(int index, string path)
        {
            if (index >= prefabPaths.Length)
            {
                int newCount = index + 1;
                string[] newPaths = new string[newCount];
                for (int i = 0; i < prefabPaths.Length; ++i)
                {
                    newPaths[i] = prefabPaths[i];
                }
                prefabPaths = newPaths;
            }

            prefabPaths[index] = path;
        }

        public List<int> GetValidPrefabIndices()
        {
            UnityEngine.Debug.Assert(!string.IsNullOrEmpty(prefabPaths[0]));

            List<int> indices = new List<int>();
            if (fixedSubGroupPrefabIndex == -1)
            {
                for (int i = 0; i < prefabPaths.Length; ++i)
                {
                    if (!string.IsNullOrEmpty(prefabPaths[i]))
                    {
                        indices.Add(i);
                    }
                }
            }
            else
            {
                indices.Add(fixedSubGroupPrefabIndex);
            }
            return indices;
        }
    }

    public class TerrainPrefabGroup
    {
        public TerrainPrefabGroup(int groupID, bool onlyUse15Prefab)
        {
            mGroupID = groupID;
            fixedPrefabIndex = -1;
            mOnlyUse15Prefab = onlyUse15Prefab;
        }

        //now only set first prefab
        public void SetPrefabPath(int index, int subTypeIndex, string path)
        {
            if (index >= 0 && index < mPrefabPaths.Length)
            {
                if (mPrefabPaths[index] == null)
                {
                    mPrefabPaths[index] = new TerrainPrefab(path);
                }
                mPrefabPaths[index].SetSubGroupPrefabPath(subTypeIndex, path);
            }
        }

        public string GetPrefabPath(int index, int subTypeIndex)
        {
            if (subTypeIndex < 0)
            {
                Debug.Assert(false, "Invalid index");
            }
            if (mPrefabPaths[index] == null)
            {
                return null;
            }
            subTypeIndex = Mathf.Clamp(subTypeIndex, 0, mPrefabPaths[index].prefabPaths.Length - 1);
            return mPrefabPaths[index].prefabPaths[subTypeIndex];
        }

        public void SetSubGroupPrefabCount(int index, int count)
        {
            if (mPrefabPaths[index] != null)
            {
                var newPaths = new string[count];
                int n = Mathf.Min(count, mPrefabPaths[index].prefabPaths.Length);
                for (int i = 0; i < n; ++i)
                {
                    newPaths[i] = mPrefabPaths[index].prefabPaths[i];
                }
                mPrefabPaths[index].prefabPaths = newPaths;
            }
        }

        public List<int> GetPrefabValidFullTileIndices()
        {
            List<int> indices = new List<int>();

            if (fixedPrefabIndex < 0)
            {
                for (int i = 15; i < 32; ++i)
                {
                    if (mPrefabPaths[i] != null && !string.IsNullOrEmpty(mPrefabPaths[i].prefabPaths[0]))
                    {
                        indices.Add(i);
                    }
                }
            }
            else
            {
                if (fixedPrefabIndex >= 0 && fixedPrefabIndex < mPrefabPaths.Length &&
                    mPrefabPaths[fixedPrefabIndex] != null &&
                    !string.IsNullOrEmpty(mPrefabPaths[fixedPrefabIndex].prefabPaths[0]))
                {
                    indices.Add(fixedPrefabIndex);
                }
            }

            if (indices.Count == 0)
            {
                indices.Add(15);
            }
            return indices;
        }

        public List<int> GetPrefabValidSubTypeIndices(int tileIndex)
        {
            if (mPrefabPaths[tileIndex] == null)
            {
                return null;
            }

            if (mOnlyUse15Prefab)
            {
                return mPrefabPaths[tileIndex].GetValidPrefabIndices();
            }

            List<int> indices = new List<int>();

            if (tileIndex >= 0 && tileIndex < 15)
            {
                return mPrefabPaths[tileIndex].GetValidPrefabIndices();
            }

            if (indices.Count == 0)
            {
                indices.Add(0);
            }
            return indices;
        }

        TerrainPrefab[] mPrefabPaths = new TerrainPrefab[1024];
        int mGroupID;
        bool mOnlyUse15Prefab;

        public TerrainPrefab[] terrainPrefabs { get { return mPrefabPaths; } }
        public int prefabCount { get { return mPrefabPaths.Length; } }
        public int fixedPrefabIndex { get; set; }
        public int groupID { get { return mGroupID; } }
    }

    public class TerrainPrefabManager
    {
        public TerrainPrefabManager(bool useOnly15Prefab)
        {
            mUseOnly15Prefab = useOnly15Prefab;
        }

        public void SetGroupPrefabByID(int groupID, int index, int subTypeIndex, string prefabPath)
        {
            var group = GetOrCreateGroup(groupID);
            group.SetPrefabPath(index, subTypeIndex, prefabPath);
        }

        public void SetPrefabFixedIndexByID(int groupID, int fixedIndex)
        {
            var group = GetOrCreateGroup(groupID);
            group.fixedPrefabIndex = fixedIndex;
        }

        public void SetPrefabSubGroupPrefabFixedIndexByID(int groupID, int prefabIndex, int fixedIndex)
        {
            var group = GetOrCreateGroup(groupID);
            group.terrainPrefabs[prefabIndex].fixedSubGroupPrefabIndex = fixedIndex;
        }

        public string GetPrefabByID(int groupID, int index, int subTypeIndex)
        {
            var group = GetOrCreateGroup(groupID);
            return group.GetPrefabPath(index, subTypeIndex);
        }

        //获取15-31的所有有效地表prefab的索引
        public List<int> GetPrefabValidFullTileIndicesByID(int groupID)
        {
            var group = GetOrCreateGroup(groupID);
            return group.GetPrefabValidFullTileIndices();
        }

        //获取1到14号随机边角
        public List<int> GetPrefabValidSubTypeIndicesByID(int groupID, int tileIndex)
        {
            var group = GetOrCreateGroup(groupID);
            return group.GetPrefabValidSubTypeIndices(tileIndex);
        }

        public TerrainPrefabGroup GetGroupByIndex(int groupIndex)
        {
            return mGroups[groupIndex];
        }

        public TerrainPrefabGroup GetOrCreateGroup(int groupID)
        {
            Debug.Assert(groupID >= 0);
            for (int i = 0; i < mGroups.Count; ++i)
            {
                if (mGroups[i].groupID == groupID)
                {
                    return mGroups[i];
                }
            }

            var group = new TerrainPrefabGroup(groupID, mUseOnly15Prefab);
            mGroups.Add(group);
            return group;
        }

        public void SetSubGroupPrefabCountByID(int groupID, int prefabIndex, int count)
        {
            var group = GetOrCreateGroup(groupID);
            group.SetSubGroupPrefabCount(prefabIndex, count);
        }

        public int groupCount { get { return mGroups.Count; } }
        public bool onlyUse15Prefab { get { return mUseOnly15Prefab; } }

        List<TerrainPrefabGroup> mGroups = new List<TerrainPrefabGroup>();
        //只使用1-15的prefab,包括随机prefab
        bool mUseOnly15Prefab;
    }
}
