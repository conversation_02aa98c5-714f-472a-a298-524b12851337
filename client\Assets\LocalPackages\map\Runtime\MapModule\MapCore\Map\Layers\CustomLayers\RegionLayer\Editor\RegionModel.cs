﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RegionModel : PolygonObjectModel
    {
        public RegionModel(RegionData data) : base(data, MapCoreDef.REGION_MODEL_NAME)
        {
            if (data.meshVertices != null)
            {
                mMeshModel = new RegionMeshModel(data.meshVertices, data.meshIndices, data.vertexColors, data.material);
                mMeshModel.SetParent(mGameObject.transform);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            DestroyMesh();
        }

        void DestroyMesh()
        {
            mMeshModel?.OnDestroy();
            mMeshModel = null;

            mBorderModel?.OnDestroy();
            mBorderModel = null;
        }

        public override void Update(PolygonObjectData data)
        {
            var colliderData = data as RegionData;
            base.Update(data);
            mMeshModel?.Update(colliderData.meshVertices);
        }

        public void UpdateMesh(RegionData data)
        {
            DestroyMesh();
            if (data.meshVertices != null)
            {
                mMeshModel = new RegionMeshModel(data.meshVertices, data.meshIndices, data.vertexColors, data.material);
                mMeshModel.SetParent(mGameObject.transform);
            }
        }

        public void SetMaterial(Material mtl)
        {
            mMeshModel?.SetMaterial(mtl);
        }

        public Mesh GetMesh()
        {
            if (mMeshModel == null)
            {
                return null;
            }
            return mMeshModel.GetMesh();
        }

        public Mesh GetBorderLineMesh()
        {
            if (mBorderModel == null)
            {
                return null;
            }
            return mBorderModel.GetMesh();
        }

        public void SetBorderLineMesh(Mesh mesh, Material mtl)
        {
            if (mBorderModel == null)
            {
                mBorderModel = new RegionBorderModel();
            }

            mBorderModel.UpdateMesh(mesh, mtl);
        }

        public void SetMeshVisible(bool visible)
        {
            mMeshModel?.SetVisible(visible);
        }

        public void SetBorderLineMeshVisible(bool visible)
        {
            mBorderModel?.SetVisible(visible);
        }

        RegionMeshModel mMeshModel;
        RegionBorderModel mBorderModel;
    }
}

#endif