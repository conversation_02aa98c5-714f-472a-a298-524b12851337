﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class ComplexGridModelLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath, System.Type layerType)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            PrepareLoading();
            LoadSetting(reader);
            LoadPrefabManager(reader, layerType);
            var layerData = LoadComplexGridModelLayerData(reader, AllocateID(), version);

            reader.Close();

            var layer = System.Activator.CreateInstance(layerType, Map.currentMap) as ComplexGridModelLayer;
            layer.Load(layerData, null, false);
        }

        static void LoadSetting(BinaryReader reader)
        {
            long pathMapperDataOffset = reader.ReadInt64();
            long curPos = reader.BaseStream.Position;
            reader.BaseStream.Position = pathMapperDataOffset;
            LoadPathMapper(reader);
            reader.BaseStream.Position = curPos;
        }

        static config.ComplexGridModelLayerData LoadComplexGridModelLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            bool exportData = reader.ReadBoolean();
            Vector3 localViewCenter = Utils.ReadVector3(reader);
            Vector2 localViewSize = Utils.ReadVector2(reader);
            Rect realLayerBounds = new Rect(layerOffset.x, layerOffset.z, tileWidth * cols, tileHeight * rows);
            if (version.minorVersion >= 6)
            {
                realLayerBounds = Utils.ReadRect(reader);
            }
            bool enableObjectMaterialChange = true;
            if (version.minorVersion >= 8)
            {
                enableObjectMaterialChange = reader.ReadBoolean();
            }
            bool enableCullIntersectedObjects = false;
            if (version.minorVersion >= 9)
            {
                enableCullIntersectedObjects = reader.ReadBoolean();
            }

            DecorationObjectPlacementSetting placementSetting = new DecorationObjectPlacementSetting();
            if (version.minorVersion >= 5)
            {
                placementSetting = LoadObjectPlacementSetting(reader);
            }

            int nLODs = reader.ReadInt32();
            List<config.GridMapObjectData[]> objectsInLODs = new List<config.GridMapObjectData[]>();
            for (int i = 0; i < nLODs; ++i)
            {
                int objectCount = reader.ReadInt32();
                config.GridMapObjectData[] objects = new config.GridMapObjectData[objectCount];
                for (int k = 0; k < objectCount; ++k)
                {
                    objects[k] = LoadComplexGridModelData(reader, version);
                }
                objectsInLODs.Add(objects);
            }

            var lodConfig = LoadMapLayerLODConfig(reader, version);

            ObjectTagSetting[] objectTags = new ObjectTagSetting[0];
            if (version.minorVersion >= 4)
            {
                int n = reader.ReadInt32();
                objectTags = new ObjectTagSetting[n];
                for (int i = 0; i < n; ++i)
                {
                    objectTags[i] = new ObjectTagSetting(Utils.ReadString(reader), reader.ReadBoolean());
                }
            }

            var layer = new config.ComplexGridModelLayerData(layerID, layerName, layerOffset, lodConfig, rows, cols, tileWidth, tileHeight, GridType.Rectangle, objectsInLODs, exportData, localViewCenter, localViewSize, objectTags, placementSetting, realLayerBounds, enableObjectMaterialChange, enableCullIntersectedObjects);
            layer.active = active;
            return layer;
        }

        static DecorationObjectPlacementSetting LoadObjectPlacementSetting(BinaryReader reader)
        {
            DecorationObjectPlacementSetting setting = new DecorationObjectPlacementSetting();
            setting.useMapLargeTile = reader.ReadBoolean();
            setting.showObjectBounds = reader.ReadBoolean();
            setting.translationStep = reader.ReadSingle();
            setting.scaleStep = reader.ReadSingle();
            setting.isGroup = reader.ReadBoolean();
            setting.considerObstacle = reader.ReadBoolean();
            setting.addLOD = reader.ReadBoolean();
            setting.alignByGrid = reader.ReadBoolean();
            setting.alignGridSize = reader.ReadSingle();
            setting.onlySelectCurrentTagObjects = reader.ReadBoolean();
            return setting;
        }

        static config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader, Version version)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                MapLayerLODConfigFlag flag = MapLayerLODConfigFlag.None;
                if (version.minorVersion >= 2)
                {
                    flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                }
                string name = "";
                if (version.minorVersion >= 3)
                {
                    name = Utils.ReadString(reader);
                }
                else
                {
                    name = Map.currentMap.data.lodManager.ConvertZoomToName(zoom);
                }
                config.lodConfigs[i] = new config.MapLayerLODConfigItem(name, zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, 0);
            }
            return config;
        }

        static void LoadPrefabManager(BinaryReader reader, System.Type layerType)
        {
            var prefabManagerData = LoadPrefabManagerData(reader);

            PrefabManager prefabManager;
            //if (layerType == typeof(DecorationBorderLayer))
            //{
            //    prefabManager = (Map.currentMap.data as EditorMapData).decorationBorderLayerPrefabManager;
            //}
            //else
            //{
                prefabManager = (Map.currentMap.data as EditorMapData).complexGridModelLayerPrefabManager;
            //}
            
            EditorMap.CreatePrefabManager(prefabManager, prefabManagerData, false, true, false);
        }

        static config.PrefabManager LoadPrefabManagerData(BinaryReader reader)
        {
            var prefabManager = new config.PrefabManager();
            int nGroups = reader.ReadInt32();
            prefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                prefabManager.groups[i] = LoadPrefabGroup(reader);
            }
            return prefabManager;
        }

        static config.PrefabGroup LoadPrefabGroup(BinaryReader reader)
        {
            config.PrefabGroup group = new config.PrefabGroup();
            group.name = Utils.ReadString(reader);
            group.color = Utils.ReadColor32(reader);

            int nPrefabs = reader.ReadInt32();
            group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                group.prefabPaths[i] = new config.PrefabSubGroup();
                string prefabPath = Utils.ReadString(reader);
                prefabPath = mLoadPathMapper.Unmap(prefabPath);
                group.prefabPaths[i].prefabPath = prefabPath;
                //load subgroup prefabs

                int subGroupPrefabCount = reader.ReadInt32();
                group.prefabPaths[i].subGroupPrefabPaths = new string[subGroupPrefabCount];
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    string subgroupPrefabPath = Utils.ReadString(reader);
                    group.prefabPaths[i].subGroupPrefabPaths[k] = mLoadPathMapper.Unmap(subgroupPrefabPath);
                }
            }
            return group;
        }

        static config.ComplexGridModelData LoadComplexGridModelData(BinaryReader reader, Version version)
        {
            var modelData = new config.ComplexGridModelData();
            modelData.SetID(AllocateID());
            modelData.position = Utils.ReadVector3(reader);
            modelData.rotation = Utils.ReadQuaternion(reader);
            modelData.scale = Utils.ReadVector3(reader);
            var prefabPathIndex = reader.ReadInt16();
            string prefabPath = mLoadPrefabPathStringTable[prefabPathIndex];
            var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(modelData.id, prefabPath, false, false);
            modelData.modelTemplateID = modelTemplate.id;
            if (version.minorVersion >= 5)
            {
                modelData.occupiedGridCount = reader.ReadUInt16();
            }
            else
            {
                modelData.occupiedGridCount = reader.ReadByte();
            }
            if (version.minorVersion >= 4)
            {
                modelData.objectTag = Utils.ReadString(reader);
            }
            if (version.minorVersion >= 7)
            {
                modelData.useRenderTextureModel = reader.ReadBoolean();
            }
            return modelData;
        }

        static void LoadPathMapper(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var path = Utils.ReadString(reader);
                var guid = Utils.ReadString(reader);

                mLoadPathMapper.pathToGuid[path] = guid;
            }

            int pathCount = reader.ReadInt32();
            mLoadPrefabPathStringTable = new List<string>(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                mLoadPrefabPathStringTable.Add(mLoadPathMapper.Unmap(Utils.ReadString(reader)));
            }
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }

        static void PrepareLoading()
        {
            mLoadPathMapper = new PathMapper();
            mLoadPrefabPathStringTable = new List<string>();
        }

        static PathMapper mLoadPathMapper;
        static List<string> mLoadPrefabPathStringTable;
    }
}

#endif