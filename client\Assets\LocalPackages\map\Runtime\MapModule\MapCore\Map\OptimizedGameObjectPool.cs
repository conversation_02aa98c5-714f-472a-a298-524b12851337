﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;
using System;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    class Pool
    {
        public GameObject template;
        public List<GameObject> unusedObjects = new List<GameObject>();
        public List<GameObject> unusedObjectsWithHeight = new List<GameObject>();
    }
    //地图上对象使用的对象池
    public class OptimizedGameObjectPool
    {
        public OptimizedGameObjectPool(Map map, Action<int, int, System.Action<GameObject>> createModel, bool setParent, bool hideObject, bool alwaysUseLOD0Model)
        {
            mMap = map;
            mCreateFunc = createModel;
            mSetParent = setParent;
            mHideObject = hideObject;
            mAlwaysUseLOD0Model = alwaysUseLOD0Model;
        }

        public void OnDestroy()
        {
            Clear();
        }

        long MakeKeyHelper(int modelTemplateID, int lod)
        {
            long id64 = (long)modelTemplateID;
            long type64 = (long)lod;
            id64 <<= 32;
            return type64 | id64;
        }

        //path是prefab的路径
        public GameObject Require(int modelTemplateID, int lod, bool hasHeight)
        {
            GameObject model = null;

            Pool pool;
            var key = new Vector2Int(modelTemplateID, lod);
            bool found = mObjects.TryGetValue(key, out pool);
            if (found)
            {
                if (hasHeight)
                {
                    int n = pool.unusedObjects.Count;
                    if (n > 0)
                    {
                        model = pool.unusedObjects[n - 1];
                        pool.unusedObjects.RemoveAt(n - 1);
                    }
                }
                else
                {
                    int n = pool.unusedObjectsWithHeight.Count;
                    if (n > 0)
                    {
                        model = pool.unusedObjectsWithHeight[n - 1];
                        pool.unusedObjectsWithHeight.RemoveAt(n - 1);
                    }
                }
            }
            else
            {
               pool = new Pool();
               mCreateFunc(modelTemplateID, mAlwaysUseLOD0Model ? 0 : lod, (obj) => 
               {
                   pool.template = obj;
#if UNITY_EDITOR
                Utils.HideGameObject(pool.template);
#endif
                   pool.template.SetActive(false);
               });

                mObjects[key] = pool;
            }
            if (System.Object.ReferenceEquals(model, null))
            {
                model = GameObject.Instantiate<GameObject>(pool.template);
#if UNITY_EDITOR
                if (mHideObject)
                {
                    Utils.HideGameObject(model);
                }
                else
                {
                    model.gameObject.hideFlags &= ~HideFlags.HideInHierarchy;
                }
#endif
            }
            if (model != null)
            {
                model.SetActive(true);
            }

            return model;
        }

        public void Release(int modelTemplateID, int lod, GameObject model, bool hasHeight)
        {
            if (model != null)
            {
                Pool pool;
                var key = new Vector2Int(modelTemplateID, lod);
                bool found = mObjects.TryGetValue(key, out pool);
#if DEBUG
                Debug.Assert(found);
#endif
                if (hasHeight)
                {
                    pool.unusedObjects.Add(model);
                }
                else
                {
                    pool.unusedObjectsWithHeight.Add(model);
                }
                model.SetActive(false);

#if UNITY_EDITOR
                if (mSetParent)
                {
                    var parentObj = mMap.view.poolObjectRoot;
                    if (parentObj != null)
                    {
                        model.transform.SetParent(parentObj.transform, false);
                    }
                    else
                    {
                        model.transform.SetParent(null, false);
                    }
                }
#endif
            }
        }

        public void Clear()
        {
            foreach (var p in mObjects)
            {
                var pool = p.Value;
                foreach (var obj in pool.unusedObjects)
                {
                    Utils.DestroyObject(obj);
                }
                foreach (var obj in pool.unusedObjectsWithHeight)
                {
                    Utils.DestroyObject(obj);
                }
                Utils.DestroyObject(pool.template);
            }
            mObjects.Clear();
        }

        public void Clear(int modelTemplateID, int lod)
        {
            var key = new Vector2Int(modelTemplateID, lod);
            Pool pool;
            bool found = mObjects.TryGetValue(key, out pool);
            if (found)
            {
                for (int i = 0; i < pool.unusedObjects.Count; ++i)
                {
                    Utils.DestroyObject(pool.unusedObjects[i]);
                }
                for (int i = 0; i < pool.unusedObjectsWithHeight.Count; ++i)
                {
                    Utils.DestroyObject(pool.unusedObjectsWithHeight[i]);
                }
                mObjects.Remove(key);
            }
        }

        bool mSetParent;
        bool mHideObject = true;
        bool mAlwaysUseLOD0Model;
        Map mMap;
        Action<int, int, Action<GameObject>> mCreateFunc;
        Dictionary<Vector2Int, Pool> mObjects = new Dictionary<Vector2Int, Pool>();
    }
}
