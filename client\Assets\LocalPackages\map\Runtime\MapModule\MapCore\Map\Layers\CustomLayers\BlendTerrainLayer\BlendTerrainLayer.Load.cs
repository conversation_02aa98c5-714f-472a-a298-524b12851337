﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class BlendTerrainLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var editorMapData = Map.currentMap.data as EditorMapData;
            PrepareLoading();
            LoadSetting(reader);
            LoadPrefabManager(reader, version);
            var layerData = LoadLayerData(reader, AllocateID(), version);

            reader.Close();

            var layer = new BlendTerrainLayer(Map.currentMap);
            layer.Load(layerData, null, false);

#if UNITY_EDITOR
            layer.LoadRenderTextures(layerData.texturePropertyName);
#endif
        }

        static void LoadSetting(BinaryReader reader)
        {
            long pathMapperDataOffset = reader.ReadInt64();
            long curPos = reader.BaseStream.Position;
            reader.BaseStream.Position = pathMapperDataOffset;
            LoadPathMapper(reader);
            reader.BaseStream.Position = curPos;
        }

        static config.BlendTerrainLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            bool useGeneratedLOD = reader.ReadBoolean();
            bool useDecorationObject = false;
            if (version.minorVersion >= 4)
            {
                useDecorationObject = reader.ReadBoolean();
            }
            bool useCombinedTiles = false;
            string materialGuid = "";
            if (version.minorVersion >= 7)
            {
                useCombinedTiles = reader.ReadBoolean();
                /*combinedHorizontalTileCount = */reader.ReadInt32();
                /*combinedVerticalTileCount = */reader.ReadInt32();
                materialGuid = Utils.ReadString(reader);
            }

            string texturePropertyName = "_MainTex";
            if (version.minorVersion >= 9)
            {
                texturePropertyName = Utils.ReadString(reader);
            }

            string groundTileAtlasSettingGuid = "";
            if (version.minorVersion >= 8)
            {
                groundTileAtlasSettingGuid = Utils.ReadString(reader);
            }

            bool generateMeshCollider = false;
            bool getGroundHeightInGame = false;
            bool optimizeMesh = true;
            if (version.minorVersion >= 12)
            {
                generateMeshCollider = reader.ReadBoolean();
                getGroundHeightInGame = reader.ReadBoolean();
                optimizeMesh = reader.ReadBoolean();
            }
            Vector3 layerPosition = Vector3.zero;
            if (version.minorVersion >= 13)
            {
                layerPosition = Utils.ReadVector3(reader);
            }

            var objects = new config.BlendTerrainTileData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    bool hasTile = reader.ReadBoolean();
                    if (hasTile)
                    {
                        var idx = i * cols + j;
                        objects[idx] = LoadBlendTerrainTileData(reader, layerID, version);
                    }
                }
            }

            var config = LoadMapLayerLODConfig(reader, version);
            var layer = new config.BlendTerrainLayerData(layerID, layerName, layerOffset, config, rows, cols, tileWidth, tileHeight, objects, useGeneratedLOD, useDecorationObject, texturePropertyName, groundTileAtlasSettingGuid, false, null, generateMeshCollider, getGroundHeightInGame, optimizeMesh, layerPosition);
            layer.active = active;
            return layer;
        }

        static config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader, Version version)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                MapLayerLODConfigFlag flag = MapLayerLODConfigFlag.None;
                if (version.minorVersion >= 2)
                {
                    flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                }
                string name = "";
                if (version.minorVersion >= 3)
                {
                    name = Utils.ReadString(reader);
                }
                else
                {
                    name = Map.currentMap.data.lodManager.ConvertZoomToName(zoom);
                }

                int terrainTileCount = 0;
                if (version.minorVersion >= 10)
                {
                    terrainTileCount = reader.ReadInt32();
                }

                config.lodConfigs[i] = new config.MapLayerLODConfigItem(name, zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, terrainTileCount);
            }
            return config;
        }

        static void LoadPrefabManager(BinaryReader reader, Version version)
        {
            var prefabManagerData = LoadPrefabManagerData(reader, version);
            var prefabManager = (Map.currentMap.data as EditorMapData).editorTerrainPrefabManager;
            EditorMap.CreatePrefabManager(prefabManager, prefabManagerData, true, false, false);

            var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
            int nGroups = prefabManager.groupCount;
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                int nPrefabs = group.count;
                for (int k = 0; k < nPrefabs; ++k)
                {
                    terrainPrefabManager.SetGroupPrefabByID(group.groupID, k, 0, group.GetPrefabPath(k));
                    var subGroupPrefabPaths = group.GetSubGroupPrefabPaths(k);
                    if (subGroupPrefabPaths != null)
                    {
                        int n = subGroupPrefabPaths.Length;
                        for (int x = 1; x < n; ++x)
                        {
                            terrainPrefabManager.SetGroupPrefabByID(group.groupID, k, x, subGroupPrefabPaths[x]);
                        }
                    }
                }
            }
        }

        static config.PrefabManager LoadPrefabManagerData(BinaryReader reader, Version version)
        {
            int nextGroupID = -1;
            if (version.minorVersion >= 6)
            {
                nextGroupID = reader.ReadInt32();
            }
            var prefabManager = new config.PrefabManager();
            prefabManager.nextGroupID = nextGroupID;
            int nGroups = reader.ReadInt32();
            prefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                prefabManager.groups[i] = LoadPrefabGroup(reader, version, i);
            }
            return prefabManager;
        }

        static config.PrefabGroup LoadPrefabGroup(BinaryReader reader, Version version, int groupIndex)
        {
            config.PrefabGroup group = new config.PrefabGroup();

            if (version.minorVersion >= 6)
            {
                group.id = reader.ReadInt32();
            }
            group.name = Utils.ReadString(reader);
            group.color = Utils.ReadColor32(reader);
            if (version.minorVersion >= 11)
            {
                group.addPrefabSet = reader.ReadBoolean();
            }

            int nPrefabs = reader.ReadInt32();
            group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                group.prefabPaths[i] = new config.PrefabSubGroup();
                string prefabPath = Utils.ReadString(reader);
                prefabPath = mLoadPathMapper.Unmap(prefabPath);
                group.prefabPaths[i].prefabPath = prefabPath;
                //load subgroup prefabs

                int subGroupPrefabCount = reader.ReadInt32();
                group.prefabPaths[i].subGroupPrefabPaths = new string[subGroupPrefabCount];
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    string subgroupPrefabPath = Utils.ReadString(reader);
                    group.prefabPaths[i].subGroupPrefabPaths[k] = mLoadPathMapper.Unmap(subgroupPrefabPath);
                }

                //load decoration prefab info
                if (version.minorVersion >= 4)
                {
                    int decorationPrefabCount = reader.ReadInt32();
                    group.prefabPaths[i].decorationPrefabGuids = new string[decorationPrefabCount];
                    for (int k = 0; k < decorationPrefabCount; ++k)
                    {
                        group.prefabPaths[i].decorationPrefabGuids[k] = Utils.ReadString(reader);
                    }
                }
            }
            return group;
        }

        static config.BlendTerrainTileData LoadBlendTerrainTileData(BinaryReader reader, int layerID, Version version)
        {
            var pathIndex = reader.ReadInt16();
            var type = reader.ReadInt32();
            var index = reader.ReadInt32();
            int subTypeIndex = reader.ReadInt32();
            float[] heights = null;
            if (version.minorVersion >= 5)
            {
                heights = Utils.ReadFloatArray(reader);
            }
            string prefabPath = "";
            if (pathIndex >= 0 && pathIndex < mLoadPrefabPathStringTable.Count)
            {
                prefabPath = mLoadPrefabPathStringTable[pathIndex];
            }
            
            var id = AllocateID();
            var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(id, prefabPath, false, MapModule.preloadGroundLayerTile);
            if (modelTemplate != null)
            {
                var tileData = new config.BlendTerrainTileData(id, layerID, type, index, subTypeIndex, modelTemplate.id, heights);
                return tileData;
            }
            return null;
        }

        static void LoadPathMapper(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var path = Utils.ReadString(reader);
                var guid = Utils.ReadString(reader);

                mLoadPathMapper.pathToGuid[path] = guid;
            }

            int pathCount = reader.ReadInt32();
            mLoadPrefabPathStringTable = new List<string>(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                mLoadPrefabPathStringTable.Add(mLoadPathMapper.Unmap(Utils.ReadString(reader)));
            }
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }

        static void PrepareLoading()
        {
            mLoadPathMapper = new PathMapper();
            mLoadPrefabPathStringTable = new List<string>();
        }

        static PathMapper mLoadPathMapper;
        static List<string> mLoadPrefabPathStringTable;
    }
}
#endif