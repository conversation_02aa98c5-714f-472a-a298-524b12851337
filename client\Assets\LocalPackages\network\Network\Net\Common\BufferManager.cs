﻿#if !UNITY_WEBGL
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace Net.Common
{
    class BufferManager
    {
        static ConcurrentStack<byte[]> s_Buffers = new ConcurrentStack<byte[]>();

        static byte[] Pop()
        {
            byte[] buffer;
            if(!s_Buffers.TryPop(out buffer))
            {
                buffer = new byte[1024 * 4];
            }
            return buffer;
        }
        static void Push(byte[] buffer)
        {
            if (buffer == null || buffer.Length != 1024 * 4) return;
            s_Buffers.Push(buffer);
        }
        public static void Alloc(SocketAsyncEventArgs e)
        {
            var buffer = Pop();
            e.SetBuffer(buffer, 0, buffer.Length);
        }
        public static void Free(SocketAsyncEventArgs e)
        {
            var buffer = e.Buffer;
            Push(buffer);
            e.<PERSON>Buffer(null, 0, 0);
        }
    }
}
#endif