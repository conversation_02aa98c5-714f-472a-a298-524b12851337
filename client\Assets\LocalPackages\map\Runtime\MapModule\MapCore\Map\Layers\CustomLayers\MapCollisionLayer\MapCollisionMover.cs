﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class MapCollisionMover
    {
        public MapCollisionMover(int layerID, int dataID, Vector3 pos)
        {
            Debug.Assert(mAction == null);
            mAction = new ActionMoveMapCollision(layerID, dataID, pos);
        }

        public void Stop(Vector3 pos)
        {
            mAction.SetEndPosition(pos);
            if (mAction.moved)
            {
                ActionManager.instance.PushAction(mAction, true, false);
            }
            mAction = null;
        }

        ActionMoveMapCollision mAction;
    }
}


#endif