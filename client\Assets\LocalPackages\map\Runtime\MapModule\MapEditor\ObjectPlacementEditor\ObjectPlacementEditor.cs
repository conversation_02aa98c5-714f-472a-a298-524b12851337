﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        enum Mode
        {
            Select,
            Single,
            FillRectangle,
            FillCircle,
            FillPolygon,
            FillLine,
            CustomBrush,
            ReplacePrefab,
        }

        class ObjectPlacementInfo
        {
            public string prefabPath;
            public Vector3 position;
        }

        void InitPrefabManager()
        {
            mPrefabManager = new PrefabManager(false, false, false, false, false, null, null);
            mPrefabManager.changeSelectedPrefabGroupEvent += OnPrefabGroupSelectionChange;
            mPrefabManager.changePrefabGroupGridSizeEvent += OnPrefabGroupGridSizeChange;
        }

        void OnEnable()
        {
            Undo.undoRedoPerformed -= UndoRedoPerformed;
            Undo.undoRedoPerformed += UndoRedoPerformed;
            InitPrefabManager();

            if (mWorldIndicator != null)
            {
                mWorldIndicator = new PrefabIndicator(false);
            }
        }

        void OnDisable()
        {
            mPrefabManager.changeSelectedPrefabGroupEvent -= OnPrefabGroupSelectionChange;
            mPrefabManager.changePrefabGroupGridSizeEvent -= OnPrefabGroupGridSizeChange;
        }

        void OnFocus()
        {
            SceneView.duringSceneGui -= this.OnSceneGUI;
            SceneView.duringSceneGui += this.OnSceneGUI;
        }

        void OnDestroy()
        {
            SceneView.duringSceneGui -= this.OnSceneGUI;
            Undo.undoRedoPerformed -= UndoRedoPerformed;
            DestroyIndicator();
            GameObject.DestroyImmediate(mToolObjectRoot);
            mBrushIndicator.OnDestroy();
            if (mWorldIndicator != null)
            {
                mWorldIndicator.OnDestroy();
                mWorldIndicator = null;
            }
        }

        void OnLostFocus()
        {
            HideIndicator();
            mBrushIndicator.HideBrush();
        }

        void UndoRedoPerformed()
        {
            mLastPlacementInfo.prefabPath = "";
            mLastPlacementInfo.position = Vector3.zero;
        }

        void OnSceneGUI(SceneView sceneView)
        {
            if (!mEnable)
            {
                HideIndicator();
                mBrushIndicator.HideBrush();
                return;
            }

            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
            if (stage == null)
            {
                return;
            }

            UnityEditor.SceneManagement.PrefabStage.prefabSaving -= OnSavingPrefab;
            UnityEditor.SceneManagement.PrefabStage.prefabSaving += OnSavingPrefab;
            UnityEditor.SceneManagement.PrefabStage.prefabSaved -= OnSavedPrefab;
            UnityEditor.SceneManagement.PrefabStage.prefabSaved += OnSavedPrefab;
            UnityEditor.SceneManagement.PrefabStage.prefabStageOpened -= OnPrefabStageOpened;
            UnityEditor.SceneManagement.PrefabStage.prefabStageOpened += OnPrefabStageOpened;
            UnityEditor.SceneManagement.PrefabStage.prefabStageClosing -= OnPrefabStageClosing;
            UnityEditor.SceneManagement.PrefabStage.prefabStageClosing += OnPrefabStageClosing;

            if (mToolObjectRoot == null)
            {
                mToolObjectRoot = new GameObject("__ObjectPlacementEditorToolRoot");
                mToolObjectRoot.transform.parent = stage.prefabContentsRoot.transform;
            }

            var currentEvent = Event.current;
            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, sceneView.camera);
            var worldPos = Utils.FromScreenToWorldPosition(screenPos, sceneView.camera, 0);

            if (mPrefabRotationSetting.Update())
            {
                Repaint();
            }

            Handles.BeginGUI();
            EditorGUILayout.BeginHorizontal();
            var newMode = (Mode)EditorGUILayout.EnumPopup("", mMode, GUILayout.MaxWidth(120));
            if (newMode != mMode)
            {
                SetMode(newMode);
            }
            EditorGUILayout.EndHorizontal();

            Handles.EndGUI();

            if (mMode == Mode.Select)
            {
                return;
            }

            if (mMode == Mode.Single)
            {
                var selectedPrefab = mPrefabManager.selectedPrefab;

                worldPos = CalculatePosAlignedToGrid(worldPos);
                UpdateIndicator(worldPos, selectedPrefab, mToolObjectRoot.transform);

                if (currentEvent.button == 0 && currentEvent.type == EventType.MouseDown)
                {
                    var assetPath = stage.prefabAssetPath;
                    if (string.IsNullOrEmpty(assetPath))
                    {
                        EditorUtility.DisplayDialog("Error", "Error!", "OK");
                        return;
                    }

                    if (selectedPrefab != null)
                    {
                        string prefabPath = AssetDatabase.GetAssetPath(selectedPrefab);
                        if (worldPos != mLastPlacementInfo.position ||
                            prefabPath != mLastPlacementInfo.prefabPath)
                        {
                            mLastPlacementInfo.position = worldPos;
                            mLastPlacementInfo.prefabPath = prefabPath;
                            CreateObject(worldPos, selectedPrefab, stage.prefabContentsRoot.transform);
                        }
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Select a prefab first!", "OK");
                    }
                }

                if (mDisplayGridSizeVertices != null && mDisplayGridSize > 0 && mShowDisplayGrid)
                {
                    var color = Handles.color;
                    Handles.color = Color.yellow;
                    Handles.DrawLines(mDisplayGridSizeVertices);
                    Handles.color = color;
                }

                if (mWorldSize > 0)
                {
                    var color = Handles.color;
                    Handles.color = Color.green;
                    Handles.DrawWireCube(Vector3.zero, new Vector3(mWorldSize, 0, mWorldSize));
                    Handles.color = color;
                }

                if (mSnapToGrid)
                {
                    Vector3 offset = Vector3.zero;
                    if (!mAlignToGridCenter)
                    {
                        offset = new Vector3(mGridSize * 0.5f, 0, mGridSize * 0.5f);
                    }
                    Handles.DrawWireCube(worldPos + offset, new Vector3(mGridSize, 0, mGridSize));
                }
            }
            else if (mMode == Mode.CustomBrush)
            {
                DrawCustomBrushSceneGUI(Event.current, worldPos);
            }
            else if (mMode == Mode.FillRectangle)
            {
                DrawRectangleFillToolSceneGUI(Event.current, worldPos);
            }
            else if (mMode == Mode.FillLine)
            {
                DrawLineFillToolSceneGUI(Event.current, worldPos);
            }
            else if (mMode == Mode.FillCircle)
            {
                DrawCircleFillToolSceneGUI(Event.current, worldPos);
            }
            else if (mMode == Mode.FillPolygon)
            {
                DrawPolygonFillToolSceneGUI(Event.current, worldPos);
            }

            SceneView.RepaintAll();
            HandleUtility.AddDefaultControl(0);
        }

        void CreateObject(Vector3 worldPos, GameObject prefab, Transform rootTransform)
        {
            var childPrefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(prefab);
            childPrefabInstance.transform.position = worldPos;
            var originalRotation = childPrefabInstance.transform.rotation.eulerAngles;
            childPrefabInstance.transform.rotation = Quaternion.Euler(originalRotation.x, mPrefabRotationSetting.yRotation, originalRotation.z);
            childPrefabInstance.transform.SetParent(rootTransform, true);
            EditorUtility.SetDirty(rootTransform.gameObject);

            Undo.RegisterCreatedObjectUndo(childPrefabInstance, "Created Prefab");

            if (mPlaceRandomObject)
            {
                RandomSelectObject();
            }
            mPrefabRotationSetting.StepToNextRandomRotation();
        }

        void CreateObjectEx(Vector3 worldPos, Vector3 localScale, Quaternion rotation, string tag, int layer, GameObject prefab, Transform rootTransform)
        {
            var childPrefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(prefab);
            childPrefabInstance.transform.position = worldPos;
            childPrefabInstance.transform.localScale = localScale;
            childPrefabInstance.transform.rotation = rotation;
            childPrefabInstance.tag = tag;
            childPrefabInstance.layer = layer;
            
            childPrefabInstance.transform.SetParent(rootTransform, true);
            EditorUtility.SetDirty(rootTransform.gameObject);

            Undo.RegisterCreatedObjectUndo(childPrefabInstance, "Created Prefab");
        }

        void OnGUI()
        {
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
            if (stage == null)
            {
                return;
            }

            if (mWorldIndicator == null)
            {
                mWorldIndicator = new PrefabIndicator(false);
            }

            mScrollPos = EditorGUILayout.BeginScrollView(mScrollPos);

            EditorGUILayout.BeginHorizontal();
            mEnable = EditorGUILayout.ToggleLeft("Enable", mEnable, GUILayout.MaxWidth(100));
            if (!string.IsNullOrEmpty(mProjectFolder))
            {
                EditorGUIUtility.labelWidth = 90;
                EditorGUILayout.LabelField("Project Folder", mProjectFolder);
                EditorGUIUtility.labelWidth = 0;
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginVertical("GroupBox");
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Save"))
            {
                string folder = mProjectFolder;
                if (string.IsNullOrEmpty(folder))
                {
                    folder = EditorUtility.OpenFolderPanel("Select Save Folder", "", "");
                }
                if (!string.IsNullOrEmpty(folder))
                {
                    Save(folder);
                }
            }

            if (GUILayout.Button("Load"))
            {
                string folder = EditorUtility.OpenFolderPanel("Select Load Folder", "", "");
                if (!string.IsNullOrEmpty(folder))
                {
                    Load(folder);
                }
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Select Duplicated Object"))
            {
                CheckDuplicatedObject(stage.prefabContentsRoot, false);
            }
            if (GUILayout.Button("Delete Duplicated Object"))
            {
                CheckDuplicatedObject(stage.prefabContentsRoot, true);
            }
            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Apply Modification To Other LODs"))
            {
                ApplyModificationToOtherLODs();
            }

            EditorGUILayout.EndVertical();

            if (mEnable)
            {
                if (mMode == Mode.ReplacePrefab)
                {
                    DrawReplacePrefabGUI();
                }
                else if (mMode == Mode.Select)
                {
                    DrawBrushUI();
                }
                else
                {
                    if (mMode == Mode.Single)
                    {
                        mShowBackgroundSetting = EditorGUILayout.Foldout(mShowBackgroundSetting, "Background");
                        if (mShowBackgroundSetting)
                        {
                            EditorGUILayout.BeginVertical("GroupBox");
                            {
                                EditorGUILayout.BeginHorizontal();
                                EditorGUILayout.FloatField("World Size", mWorldSize);
                                if (GUILayout.Button("Change Size"))
                                {
                                    var dlg = EditorUtils.CreateInputDialog("Set World Size");
                                    var items = new List<InputDialog.Item> {
                                    new InputDialog.StringItem("Size", "", mWorldSize.ToString()),
                                };
                                    dlg.Show(items, OnClickChangeWorldSize);
                                }
                                EditorGUILayout.EndHorizontal();
                            }
                            {
                                EditorGUILayout.BeginHorizontal();
                                EditorGUILayout.FloatField("Display Grid Size", mDisplayGridSize);
                                if (GUILayout.Button("Change Size"))
                                {
                                    var dlg = EditorUtils.CreateInputDialog("Set Display Grid Size");
                                    var items = new List<InputDialog.Item> {
                                    new InputDialog.StringItem("Size", "", mDisplayGridSize.ToString()),
                                };
                                    dlg.Show(items, OnClickChangeDisplayGridSize);
                                }
                                EditorGUILayout.EndHorizontal();
                            }

                            {
                                var prefab = EditorGUILayout.ObjectField("Background Prefab", mWorldIndicator.prefab, typeof(GameObject), false) as GameObject;
                                if (prefab != null && PrefabUtility.GetPrefabAssetType(prefab) == PrefabAssetType.Regular)
                                {
                                    mWorldIndicator.SetPrefab(prefab, mToolObjectRoot.transform);
                                    mWorldIndicator.SetActive(true);
                                }
                            }
                            mShowDisplayGrid = EditorGUILayout.Toggle("Show Grid", mShowDisplayGrid);
                            EditorGUILayout.EndVertical();
                        }

                        mShowGridSetting = EditorGUILayout.Foldout(mShowGridSetting, "Grid Setting");
                        if (mShowGridSetting)
                        {
                            EditorGUILayout.BeginVertical("GroupBox");
                            EditorGUILayout.BeginHorizontal();
                            mSnapToGrid = EditorGUILayout.ToggleLeft("Snap To Grid", mSnapToGrid, GUILayout.MaxWidth(120));
                            mGridSize = EditorGUILayout.FloatField("Grid Size", mGridSize);
                            EditorGUILayout.EndHorizontal();
                            mAlignToGridCenter = EditorGUILayout.ToggleLeft("Align To Grid Center", mAlignToGridCenter);
                            EditorGUILayout.EndVertical();
                        }
                    }

                    if (mMode != Mode.CustomBrush)
                    {
                        mShowRotationSetting = EditorGUILayout.Foldout(mShowRotationSetting, "Object Rotation Setting");
                        if (mShowRotationSetting)
                        {
                            EditorGUILayout.BeginVertical("GroupBox");
                            mPrefabRotationSetting.Draw();
                            EditorGUILayout.EndVertical();
                        }
                    }

                    DrawModeSettingGUI();

                    DrawBrushUI();

                    if (mMode != Mode.CustomBrush)
                    {
                        mShowPrefabSetting = EditorGUILayout.Foldout(mShowPrefabSetting, "Prefab Setting");
                        if (mShowPrefabSetting)
                        {
                            bool newPlaceObject = EditorGUILayout.ToggleLeft("Random Object In Group", mPlaceRandomObject);
                            if (newPlaceObject != mPlaceRandomObject)
                            {
                                mPlaceRandomObject = newPlaceObject;
                                if (newPlaceObject)
                                {
                                    RandomSelectObject();
                                }
                            }
                            mPrefabManager.Draw(PrefabGroupDisplayFlag.None | PrefabGroupDisplayFlag.CanRemovePrefab | PrefabGroupDisplayFlag.ShowAddFolderWithoutLODConstrain | PrefabGroupDisplayFlag.ShowGridSize);
                        }
                    }
                }
            }

            EditorGUILayout.EndScrollView();
        }

        void RandomSelectObject()
        {
            var group = mPrefabManager.selectedGroup;
            if (group != null)
            {
                group.SelectRandomIndex();
                Repaint();
            }
        }

        Vector3 CalculatePosAlignedToGrid(Vector3 pos)
        {
            if (mSnapToGrid)
            {
                float halfSize = mWorldSize * 0.5f;
                int gridX = Mathf.FloorToInt((pos.x + halfSize) / mGridSize);
                int gridY = Mathf.FloorToInt((pos.z + halfSize) / mGridSize);
                if (mAlignToGridCenter)
                {
                    return new Vector3(gridX * mGridSize + 0.5f * mGridSize - halfSize, 0, gridY * mGridSize + 0.5f * mGridSize - halfSize);
                }
                return new Vector3(gridX * mGridSize - halfSize, 0, gridY * mGridSize - halfSize);
            }
            return pos;
        }

        void SetMode(Mode mode)
        {
            mMode = mode;
            Repaint();
            if (mode == Mode.FillRectangle || mode == Mode.FillCircle || mode == Mode.FillPolygon || mode == Mode.FillLine)
            {
                HideIndicator();
            }

            if (mode != Mode.CustomBrush)
            {
                mBrushIndicator.HideBrush();
            }
            else
            {
                mBrushIndicator.ShowBrush();
            }
        }

        void DrawModeSettingGUI()
        {
            if (mMode == Mode.Single || mMode == Mode.CustomBrush)
            {
                return;
            }

            EditorGUILayout.BeginVertical("GroupBox");

            if (mMode == Mode.FillRectangle)
            {
                mFillCount = EditorGUILayout.IntField(new GUIContent("Fill Object Count", "填充物体最大个数,如果设置了物体之间的最小间隔距离, 填充的物体个数不一定能达到最大个数"), mFillCount);
                mMinDistance = EditorGUILayout.FloatField(new GUIContent("Minimum Distance", "物体之间最小间隔距离"), mMinDistance);
                EditorGUILayout.BeginHorizontal();
                mFillEdge = EditorGUILayout.ToggleLeft(new GUIContent("Fill Edge", "是否只在矩形的边附近生成物体"), mFillEdge);
                if (mFillEdge)
                {
                    mEdgeSize = EditorGUILayout.FloatField(new GUIContent("Edge Size", "填充边的范围"), mEdgeSize);
                }
                EditorGUILayout.EndHorizontal();
                mRemoveObjectOfSeletedPrefab = EditorGUILayout.ToggleLeft(new GUIContent("Remove Object Of Selected Prefab Type", "按住Ctrl键,勾上后删除矩形范围内物体,只删除Prefab Group中选择的物体"), mRemoveObjectOfSeletedPrefab);
                if (GUILayout.Button(new GUIContent("Regenerate", "重新生成最后一次所选范围内的物体")))
                {
                    RegenerateObjectsInRectangle();
                }
            }
            else if (mMode == Mode.FillLine)
            {
                if (!mUseSameDeltaDistance)
                {
                    mFillCount = EditorGUILayout.IntField(new GUIContent("Fill Object Count", "填充物体最大个数,如果设置了物体之间的最小间隔距离, 填充的物体个数不一定能达到最大个数"), mFillCount);
                }
                mMinDistance = EditorGUILayout.FloatField(new GUIContent("Minimum Distance", "物体之间最小间隔距离"), mMinDistance);
                
                bool useSameDeltaDistance = EditorGUILayout.Toggle(new GUIContent("Same Delta Distance", "所有物体沿直线生成,并且间隔相同"), mUseSameDeltaDistance);
                if (useSameDeltaDistance != mUseSameDeltaDistance)
                {
                    mUseSameDeltaDistance = useSameDeltaDistance;
                    Repaint();
                }
                mEdgeSize = EditorGUILayout.FloatField(new GUIContent("Edge Size", "填充边的范围"), mEdgeSize);
                if (GUILayout.Button(new GUIContent("Regenerate", "重新生成最后一次所选范围内的物体")))
                {
                    RegenerateObjectsInLine();
                }
            }
            else if (mMode == Mode.FillCircle)
            {
                mFillCount = EditorGUILayout.IntField(new GUIContent("Fill Object Count", "填充物体最大个数,如果设置了物体之间的最小间隔距离, 填充的物体个数不一定能达到最大个数"), mFillCount);
                mMinDistance = EditorGUILayout.FloatField(new GUIContent("Minimum Distance", "物体之间最小间隔距离"), mMinDistance);
                mCircleRadius = EditorGUILayout.FloatField(new GUIContent("Radius", "填充半径"), mCircleRadius);
                EditorGUILayout.BeginHorizontal();
                mFillEdge = EditorGUILayout.ToggleLeft(new GUIContent("Fill Edge", "是否只在圆的边附近生成物体"), mFillEdge);
                if (mFillEdge)
                {
                    mEdgeSize = EditorGUILayout.FloatField(new GUIContent("Edge Size", "填充边的范围"), mEdgeSize);
                }
                EditorGUILayout.EndHorizontal();
                mRemoveObjectOfSeletedPrefab = EditorGUILayout.ToggleLeft(new GUIContent("Remove Object Of Selected Prefab Type", "按住Ctrl键,勾上后删除矩形范围内物体,只删除Prefab Group中选择的物体"), mRemoveObjectOfSeletedPrefab);
                if (GUILayout.Button(new GUIContent("Regenerate", "重新生成最后一次所选范围内的物体")))
                {
                    RegenerateObjectsInCircle();
                }
            }
            else if (mMode == Mode.FillPolygon)
            {
                mFillCount = EditorGUILayout.IntField(new GUIContent("Fill Object Count", "填充物体最大个数,如果设置了物体之间的最小间隔距离, 填充的物体个数不一定能达到最大个数"), mFillCount);
                EditorGUILayout.BeginHorizontal();
                mFillEdge = EditorGUILayout.ToggleLeft(new GUIContent("Fill Edge", "是否只在多边形边附近生成物体"), mFillEdge);
                if (mFillEdge)
                {
                    mEdgeSize = EditorGUILayout.FloatField(new GUIContent("Edge Size", "填充边的范围"), mEdgeSize);
                }
                EditorGUILayout.EndHorizontal();
                mMinDistance = EditorGUILayout.FloatField(new GUIContent("Minimum Distance", "物体之间最小间隔距离"), mMinDistance);
                if (GUILayout.Button(new GUIContent("Regenerate", "重新生成最后一次所选范围内的物体")))
                {
                    RegenerateObjectsInPolygon();
                }
            }
            EditorGUILayout.EndHorizontal();
        }

        void OnSavingPrefab(GameObject obj)
        {
            if (mToolObjectRoot != null)
            {
                mToolObjectRoot.transform.SetParent(null);
            }
        }

        void OnSavedPrefab(GameObject obj)
        {
            if (mToolObjectRoot != null)
            {
                mToolObjectRoot.transform.SetParent(obj.transform);
            }
        }

        void OnPrefabGroupGridSizeChange(int groupIndex, float gridSize)
        {
            if (gridSize > 0)
            {
                mSnapToGrid = true;
                mGridSize = gridSize;
            }
            else
            {
                mSnapToGrid = false;
                mGridSize = 0;
            }
        }

        void OnPrefabGroupSelectionChange(int oldGroupIndex, int newGroupIndex)
        {
            var group = mPrefabManager.GetGroupByIndex(newGroupIndex);
            if (group.gridSize > 0) {
                mSnapToGrid = true;
                mGridSize = group.gridSize;
            }
            else
            {
                mSnapToGrid = false;
                mGridSize = 0;
            }
            
            SceneView.RepaintAll();
        }

        bool OnClickChangeDisplayGridSize(List<InputDialog.Item> parameters)
        {
            string gridSizeStr = (parameters[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(gridSizeStr))
            {
                return false;
            }

            bool ok = float.TryParse(gridSizeStr, out float gridSize);
            if (ok && gridSize >= 0)
            {
                SetDisplayGridSize(gridSize);
                return true;
            }

            return false;
        }

        bool OnClickChangeWorldSize(List<InputDialog.Item> parameters)
        {
            string gridSizeStr = (parameters[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(gridSizeStr))
            {
                return false;
            }

            bool ok = float.TryParse(gridSizeStr, out float gridSize);
            if (ok && gridSize >= 0)
            {
                SetWorldSize(gridSize);
                return true;
            }

            return false;
        }

        void SetWorldSize(float size)
        {
            size = Mathf.Max(0, size);
            mWorldSize = size;
            CreateGridVertices();
        }

        void SetDisplayGridSize(float size)
        {
            size = Mathf.Max(0, size);
            if (size == 0)
            {
                mDisplayGridSize = 0;
                mDisplayGridSizeVertices = null;
            }
            else
            {
                if (!Mathf.Approximately(size, mDisplayGridSize))
                {
                    mDisplayGridSize = size;
                    CreateGridVertices();
                }
            }
        }

        void CreateGridVertices()
        {
            int gridCount = Mathf.CeilToInt(mWorldSize / mDisplayGridSize);
            int vertexCount = gridCount + 1;
            List<Vector3> vertices = new List<Vector3>();
            float minX = -mWorldSize * 0.5f;
            float minZ = -mWorldSize * 0.5f;
            float maxX = mWorldSize * 0.5f;
            float maxZ = mWorldSize * 0.5f;

            //vertical line
            for (int i = 0; i < vertexCount; ++i)
            {
                vertices.Add(new Vector3(minX + i * mDisplayGridSize, 0, minZ));
                vertices.Add(new Vector3(minX + i * mDisplayGridSize, 0, maxZ));
            }

            //horizontal line
            for (int i = 0; i < vertexCount; ++i)
            {
                vertices.Add(new Vector3(minX, 0, minZ + i * mDisplayGridSize));
                vertices.Add(new Vector3(maxX, 0, minZ + i * mDisplayGridSize));
            }

            mDisplayGridSizeVertices = vertices.ToArray();
        }

        bool mShowRotationSetting = true;
        bool mShowGridSetting = true;
        bool mShowBackgroundSetting = true;
        bool mShowPrefabSetting = true;
        bool mShowCustomPrefabBrush = true;
        bool mSnapToGrid = true;
        bool mAlignToGridCenter = false;
        bool mEnable = true;
        bool mShowDisplayGrid = true;
        bool mPlaceRandomObject = false;
        float mGridSize = 60.0f;
        float mWorldSize = 0;
        float mDisplayGridSize = 0;
        Vector3[] mDisplayGridSizeVertices;
        PrefabIndicator mWorldIndicator;
        float mEdgeSize = 1.0f;
        bool mFillEdge = false;
        bool mRemoveObjectOfSeletedPrefab = false;
        string mProjectFolder;
        Mode mMode = Mode.Select;
        ObjectPlacementInfo mLastPlacementInfo = new ObjectPlacementInfo();
        PrefabManager mPrefabManager;
        PrefabRotationSetting mPrefabRotationSetting = new PrefabRotationSetting();
        Vector2 mScrollPos;

        GameObject mToolObjectRoot;
    }
}
#endif