%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: UI-Sprite Effect Layer
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=14301\n487;594;979;424;3607.895;518.2078;1.589382;True;False\nNode;AmplifyShaderEditor.CommentaryNode;223;-6464.406,-405.0316;Float;False;1173;245;Comment;5;221;219;218;222;225;UVs;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;133;-3799.791,-542.9508;Float;False;2592.219;432.9557;Comment;14;33;43;228;14;18;199;203;92;93;20;72;248;249;250;Flow;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;141;-4706.676,155.4364;Float;False;4042.436;693;Comment;35;114;122;130;129;30;99;115;116;126;98;125;100;102;101;103;57;6;2;3;8;13;11;4;7;12;17;97;205;200;236;237;239;240;242;246;Rotate;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;86;-4791.083,-1656.615;Float;False;4027.944;807.3318;Comment;31;229;230;80;190;183;198;184;182;75;83;202;224;185;187;179;84;71;65;181;188;96;189;77;82;186;177;234;233;232;235;231;Distortion;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;209;-6158.292,-709.5029;Float;False;714.7412;175.0351;Comment;3;59;40;197;Time;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;208;-6188.004,-1135.091;Float;False;775.5483;285.9527;Comment;3;37;201;207;Main
    FX Texture;1,1,1,1;0;0\nNode;AmplifyShaderEditor.ComponentMaskNode;6;-4144.676,573.4364;Float;False;True;True;False;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;191;128,-800;Float;False;Tint
    Effect;True;0;2;1;In 0;In 1;Object;-1;8;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;COLOR;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;228;-3264,-400;Float;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.DynamicAppendNode;82;-3060.073,-1576.615;Float;False;FLOAT4;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.StepOpNode;115;-2640.676,653.4366;Float;False;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;77;-4745.313,-1609.291;Float;True;Property;_DistortionNormal;Distortion
    Normal;2;2;[NoScaleOffset];[Normal];Create;True;dd2fd2df93418444c8e280f1d34deeb5;9f3c58d3e4da749499d5cf376feb6225;True;bump;Auto;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionInput;192;128,-688;Float;False;Color;5;0;False;1;0;COLOR;1,1,1,1;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;71;-1780.073,-1160.615;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;14;-3008,-480;Float;True;Property;_FlowandMaskSampler;Flow
    and Mask Sampler;11;1;[NoScaleOffset];Create;True;None;db10d612ce611ae47a896290b6c40f26;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.ComponentMaskNode;27;320,-512;Float;False;False;False;False;True;1;0;COLOR;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;30;-2170.379,545.9249;Float;False;Opacity;1;17;False;1;0;FLOAT;1.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;122;-2448.676,733.4366;Float;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;96;-1588.073,-1336.615;Float;True;Property;_DistortionSampler;Distortion
    Sampler;0;1;[NoScaleOffset];Create;True;None;None;True;0;False;black;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;188;-3700.073,-1320.615;Float;False;Distortion
    Mask;9;9;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionInput;181;-4132.074,-968.615;Float;False;Move
    Vector;2;5;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;57;-4432.676,605.4365;Float;False;Position
    Scale;4;14;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;33;-3536,-480;Float;False;Flow
    (RG) Mask (A);9;10;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionInput;101;-2688.676,429.4364;Float;False;Rotation
    Mask;9;13;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.ScaleNode;7;-3712.676,685.4366;Float;False;0.5;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;43;-3776,-480;Float;True;Property;_FlowandMask;Flow
    and Mask;3;1;[NoScaleOffset];Create;True;90cbb44e451af6f44acb32a4c63486ec;db10d612ce611ae47a896290b6c40f26;False;white;Auto;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;193;752,-608;Float;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0.0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitch;186;-2116.073,-1368.615;Float;False;Use
    Distortion Mask;True;0;2;6;In 0;In 1;Object;-1;8;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;203;-2304,-480;Float;False;201;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.ComponentMaskNode;84;-2596.073,-1576.615;Float;True;True;True;False;False;1;0;FLOAT3;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;195;560,-528;Float;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0.0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;39;-320,-608;Float;False;Tint
    Color;5;1;False;1;0;COLOR;1,1,1,1;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;102;-2480.676,429.4364;Float;True;Property;_RotateMaskSampler;Rotate
    Mask Sampler;11;1;[NoScaleOffset];Create;True;None;db10d612ce611ae47a896290b6c40f26;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleAddOpNode;196;752,-720;Float;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;37;-5881.824,-1082.837;Float;False;Tex;9;2;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionSwitch;242;-2326.053,204.1207;Float;False;Vertex
    UVs;False;0;2;3;Fragment;Vertex;Object;-1;8;0;FLOAT2;0.0;False;1;FLOAT2;0.0;False;2;FLOAT2;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RotatorNode;17;-3216,208;Float;False;3;0;FLOAT2;0,0;False;1;FLOAT2;0.5,0.5;False;2;FLOAT;1.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;74;-400,-800;Float;False;Effect;False;0;3;0;Distortion;Flow;Rotate;Object;-1;8;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;224;-4464,-1120;Float;False;222;0;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;236;-4480,368;Float;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleTimeNode;250;-2976,-192;Float;False;1;0;FLOAT;1.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;13;-3376.676,365.4364;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;239;-4432,496;Float;False;UV;2;4;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;246;-2592,272;Float;False;1;0;FLOAT2;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;31;976,-800;Float;False;Blend
    Mode;False;0;3;16;None;Additive;Additive With Alpha Blend;Object;-1;8;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;222;-5536,-352;Float;False;MainUvs;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;65;-3412.073,-1608.615;Float;True;Property;_DistortionNormalSampler;Distortion
    Normal Sampler;3;1;[NoScaleOffset];Create;True;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleAddOpNode;8;-3792.676,365.4364;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;100;-2976.676,301.4365;Float;True;Property;_RotateMask;Rotate
    Mask;5;1;[NoScaleOffset];Create;True;800ff50c9cc42bb4c99ea4b57ccd019e;db10d612ce611ae47a896290b6c40f26;False;white;Auto;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;26;-96,-688;Float;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitch;98;-944.6762,205.4365;Float;False;Use
    Mask;True;0;2;11;In 0;In 1;Object;-1;8;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitch;126;-1984.676,477.4364;Float;False;Mask
    Type;False;0;2;13;Texture;Color;Object;-1;8;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;99;-1168,320;Float;False;3;3;0;COLOR;0,0,0,0;False;1;FLOAT;0,0,0,0;False;2;FLOAT;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitch;204;-368,-240;Float;False;Option;False;0;2;-1;In
    0;In 1;Instance;74;8;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.PannerNode;179;-3860.073,-1032.615;Float;False;3;0;FLOAT2;0,0;False;2;FLOAT2;0,-0.1;False;1;FLOAT;1.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;72;-2004.572,-476.025;Float;True;Property;_FlowSampler;Flow
    Sampler;0;1;[NoScaleOffset];Create;True;None;None;True;0;False;black;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;103;-4656.676,605.4365;Float;False;Constant;_Vector0;Vector
    0;4;0;Create;True;0,0,1,1;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;125;-1715.194,611.3666;Float;False;3;0;FLOAT;1.0;False;1;FLOAT;1.0;False;2;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;130;-2224.676,653.4366;Float;False;Invert
    Middle Point;True;0;2;15;In 0;In 1;Object;-1;8;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;201;-5655.456,-1085.091;Float;False;FxTexVar;-1;True;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SamplerNode;187;-3428.073,-1320.615;Float;True;Property;_DistortionMaskSampler;Distortion
    Mask Sampler;3;1;[NoScaleOffset];Create;True;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;40;-5900.292,-649.4677;Float;False;Time;1;15;False;1;0;FLOAT;0.01;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;185;-4164.074,-1432.615;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;116;-2832.676,653.4366;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;-0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;205;-1840.676,301.4365;Float;False;201;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.GetLocalVarNode;202;-1802.593,-1350.938;Float;False;201;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.ComponentMaskNode;114;-3152.676,653.4366;Float;False;False;True;True;True;1;0;FLOAT2;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;83;-2852.073,-1576.615;Float;False;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1.0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionSwitch;225;-5792,-352;Float;False;Custom
    UVs;True;0;2;2;In 0;In 1;Object;-1;8;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;197;-5686.55,-659.5028;Float;False;TimeVar;-1;True;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;75;-3700.073,-1608.615;Float;False;Distortion
    Normal;9;6;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionSwitch;182;-3700.073,-1464.615;Float;False;Move
    Distortion;True;0;2;8;In 0;In 1;Object;-1;8;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;184;-3924.073,-1432.615;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;92;-1444.572,-476.025;Float;False;Use
    Mask;True;0;2;10;In 0;In 1;Object;-1;8;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;93;-1652.572,-300.025;Float;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0.0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;198;-4471.64,-1349.006;Float;False;197;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;183;-4484.074,-1432.615;Float;False;Move
    Distortion Vector;2;8;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;190;-2324.073,-1304.615;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0.0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleTimeNode;59;-6108.292,-649.4677;Float;False;1;0;FLOAT;1.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;80;-3124.073,-1416.615;Float;False;Distortion
    Amount;1;7;False;1;0;FLOAT;1.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;177;-3600,-1120;Float;False;Move
    Background;True;0;2;7;Simple;Moving Background;Object;-1;8;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;235;-2688,-1056;Float;True;Property;_DistortionBlendMask;Distortion
    Blend Mask;4;1;[NoScaleOffset];Create;True;800ff50c9cc42bb4c99ea4b57ccd019e;db10d612ce611ae47a896290b6c40f26;False;white;Auto;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.NegateNode;12;-3552.676,685.4366;Float;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;230;-1184,-1184;Float;False;3;3;0;COLOR;0,0,0,0;False;1;FLOAT;0.0,0,0,0;False;2;FLOAT;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;200;-3601.361,241.9365;Float;False;197;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;219;-6219.89,-355.0316;Float;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;11;-3568.676,365.4364;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;207;-6138.004,-1079.138;Float;True;Property;_FXTexture;FX
    Texture;0;1;[NoScaleOffset];Create;True;84508b93f15f2b64386ec07486afc7a3;f7e96904e8667e1439548f0f86389447;False;white;Auto;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.ComponentMaskNode;2;-4144.676,653.4366;Float;False;False;False;True;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;129;-1504.676,477.4364;Float;False;Use
    Middle Point Cut;True;1;2;14;In 0;In 1;Object;-1;8;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;231;-1792,-944;Float;False;Opacity;1;16;False;1;0;FLOAT;1.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;4;-3872.676,685.4366;Float;False;2;0;FLOAT2;0,0;False;1;FLOAT;0.0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;234;-1584,-1008;Float;False;Mask
    Type;False;0;2;12;Texture;Color;Object;-1;8;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;221;-6414.406,-355.0316;Float;False;201;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionSwitch;229;-1008,-1328;Float;False;Use
    Mask;True;0;2;9;In 0;In 1;Object;-1;8;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;COLOR;0\nNode;AmplifyShaderEditor.TexturePropertyNode;189;-4745.313,-1321.291;Float;True;Property;_DistortionMask;Distortion
    Mask;1;1;[NoScaleOffset];Create;True;23d106e42b09f48ecb609eb2d6032f0a;9f3c58d3e4da749499d5cf376feb6225;False;white;Auto;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionInput;218;-5968,-256;Float;False;UV;2;3;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;97;-1520.676,205.4365;Float;True;Property;_RotateSampler;Rotate
    Sampler;4;1;[NoScaleOffset];Create;True;None;None;True;0;False;black;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;248;-2788.04,-193.6505;Float;False;Move
    Dir;2;11;False;1;0;FLOAT2;1,1;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;237;-4144,464;Float;False;Custom
    UVs;True;0;2;4;In 0;In 1;Object;-1;8;0;FLOAT2;0.0;False;1;FLOAT2;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RelayNode;240;-1897.553,209.827;Float;False;1;0;FLOAT2;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;233;-2448,-1056;Float;False;Distortion
    Blend Mask;9;12;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.DynamicAppendNode;20;-2496.058,-413.0761;Float;False;FLOAT2;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;18;-2312.529,-412.6489;Float;False;2;0;FLOAT2;0.0;False;1;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;232;-2176,-1056;Float;True;Property;_DistortionBlendMaskSampler;Distortion
    Blend Mask Sampler;11;1;[NoScaleOffset];Create;True;None;db10d612ce611ae47a896290b6c40f26;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionSwitch;249;-2592,-272;Float;False;Separate
    XY Time;True;0;2;5;In 0;In 1;Object;-1;8;0;FLOAT;0.0;False;1;FLOAT2;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;199;-2979.91,-269.36;Float;False;197;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;3;-4096.676,733.4366;Float;False;Constant;_Float0;Float
    0;3;0;Create;True;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1344,-800;Float;False;True;RGBA;0;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;172;16,-240;Float;False;False;UV;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nWireConnection;6;0;57;0\nWireConnection;191;0;74;0\nWireConnection;191;1;26;0\nWireConnection;228;2;33;0\nWireConnection;82;1;65;2\nWireConnection;82;3;65;1\nWireConnection;115;0;116;0\nWireConnection;71;0;186;0\nWireConnection;71;1;177;0\nWireConnection;14;0;33;0\nWireConnection;14;1;228;0\nWireConnection;27;0;192;0\nWireConnection;122;0;115;0\nWireConnection;96;0;202;0\nWireConnection;96;1;71;0\nWireConnection;188;0;189;0\nWireConnection;57;0;103;0\nWireConnection;33;0;43;0\nWireConnection;101;0;100;0\nWireConnection;7;0;4;0\nWireConnection;193;0;192;0\nWireConnection;193;1;195;0\nWireConnection;186;0;84;0\nWireConnection;186;1;190;0\nWireConnection;84;0;83;0\nWireConnection;195;0;191;0\nWireConnection;195;1;27;0\nWireConnection;102;0;101;0\nWireConnection;102;1;237;0\nWireConnection;196;0;191;0\nWireConnection;196;1;192;0\nWireConnection;37;0;207;0\nWireConnection;242;0;17;0\nWireConnection;242;1;246;0\nWireConnection;17;0;13;0\nWireConnection;17;2;200;0\nWireConnection;74;0;229;0\nWireConnection;74;1;92;0\nWireConnection;74;2;98;0\nWireConnection;13;0;11;0\nWireConnection;13;1;12;0\nWireConnection;246;0;17;0\nWireConnection;31;0;191;0\nWireConnection;31;1;196;0\nWireConnection;31;2;193;0\nWireConnection;222;0;225;0\nWireConnection;65;0;75;0\nWireConnection;65;1;182;0\nWireConnection;8;0;237;0\nWireConnection;8;1;6;0\nWireConnection;26;0;74;0\nWireConnection;26;1;39;0\nWireConnection;98;0;97;0\nWireConnection;98;1;99;0\nWireConnection;126;0;102;2\nWireConnection;126;1;30;0\nWireConnection;99;0;97;0\nWireConnection;99;1;129;0\nWireConnection;99;2;97;4\nWireConnection;204;0;71;0\nWireConnection;204;1;18;0\nWireConnection;204;2;240;0\nWireConnection;179;0;224;0\nWireConnection;179;2;181;0\nWireConnection;72;0;203;0\nWireConnection;72;1;18;0\nWireConnection;125;1;126;0\nWireConnection;125;2;130;0\nWireConnection;130;0;115;0\nWireConnection;130;1;122;0\nWireConnection;201;0;37;0\nWireConnection;187;0;188;0\nWireConnection;187;1;182;0\nWireConnection;40;0;59;0\nWireConnection;185;0;183;0\nWireConnection;185;1;198;0\nWireConnection;116;0;114;0\nWireConnection;114;0;13;0\nWireConnection;83;0;82;0\nWireConnection;83;1;80;0\nWireConnection;225;0;219;0\nWireConnection;225;1;218;0\nWireConnection;197;0;40;0\nWireConnection;75;0;77;0\nWireConnection;182;0;224;0\nWireConnection;182;1;184;0\nWireConnection;184;0;185;0\nWireConnection;184;1;224;0\nWireConnection;92;0;72;0\nWireConnection;92;1;93;0\nWireConnection;93;0;72;0\nWireConnection;93;1;14;4\nWireConnection;190;0;84;0\nWireConnection;190;1;187;2\nWireConnection;177;0;224;0\nWireConnection;177;1;179;0\nWireConnection;12;0;7;0\nWireConnection;230;0;96;0\nWireConnection;230;1;96;4\nWireConnection;230;2;234;0\nWireConnection;219;2;221;0\nWireConnection;11;0;8;0\nWireConnection;11;1;2;0\nWireConnection;2;0;57;0\nWireConnection;129;0;126;0\nWireConnection;129;1;125;0\nWireConnection;4;0;2;0\nWireConnection;4;1;3;0\nWireConnection;234;0;232;2\nWireConnection;234;1;231;0\nWireConnection;229;0;96;0\nWireConnection;229;1;230;0\nWireConnection;218;0;219;0\nWireConnection;97;0;205;0\nWireConnection;97;1;240;0\nWireConnection;248;0;250;0\nWireConnection;237;0;236;0\nWireConnection;237;1;239;0\nWireConnection;240;0;242;0\nWireConnection;233;0;235;0\nWireConnection;20;0;14;1\nWireConnection;20;1;14;2\nWireConnection;18;0;20;0\nWireConnection;18;1;249;0\nWireConnection;232;0;233;0\nWireConnection;249;0;199;0\nWireConnection;249;1;248;0\nWireConnection;0;0;31;0\nWireConnection;172;0;204;0\nASEEND*/\n//CHKSM=3897411E214292FD4588680A344DD01F3BE97598"
  m_functionName: 
  m_description: 
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
