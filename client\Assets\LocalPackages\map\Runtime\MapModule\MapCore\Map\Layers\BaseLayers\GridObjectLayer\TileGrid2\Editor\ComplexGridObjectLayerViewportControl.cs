﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    class ComplexGridObjectLayerViewportControl : MonoBehaviour
    {
        public void Init(ComplexGridModelLayer layer)
        {
            mLayer = layer;
        }

        void Start()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                var viewportSize = mLayer.localViewSize;
                transform.localScale = new Vector3(viewportSize.x, 1, viewportSize.y);

                ForceUpdate(true);
            }
        }

        void Update()
        {
            ForceUpdate(false);
        }

        void ForceUpdate(bool force)
        {
            if (Map.currentMap != null)
            {
                var pos = transform.position;
                pos.y = 0;
                transform.position = pos;

                float maxScale = 10000;
                var scale = transform.localScale;
                scale.x = Mathf.Clamp(scale.x, 1, maxScale);
                scale.z = Mathf.Clamp(scale.z, 1, maxScale);
                scale.y = 1.0f;
                transform.localScale = scale;
                var newViewportSize = new Vector2(scale.x, scale.z);
                var offset = pos - mLayer.localViewCenter;
                var scaleOffset = newViewportSize - mLayer.localViewSize;
                if (offset != Vector3.zero || force || scaleOffset != Vector2.zero)
                {
                    mLayer.SetLocalViewport(pos, newViewportSize);
                }
            }
        }

        void OnDrawGizmos()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                Gizmos.color = Color.cyan;
                var scale = transform.localScale;
                Gizmos.DrawWireCube(transform.position, new Vector3(scale.x, 0, scale.z));
            }
        }

        ComplexGridModelLayer mLayer;
    }
}