﻿using Cfg.G;
using Common;
using Config;
using cspb;
using Cysharp.Threading.Tasks;
using Game.Config;
using Logic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TFW;

namespace Game.Data
{

    

    /// <summary>
    /// 联盟属性信息数据
    /// </summary>
    public class AlliancePropGameData : IDisposable
    {

        #region 属性数据信息

        /// <summary>
        ///  联盟属性字典信息数据
        /// </summary>
        private Dictionary<int, double> _propDic;

        /// <summary>
        /// buff属性字典信息数据
        /// </summary>
        private Dictionary<int, List<CBuffProperty>> _buffPropertyDic;

        #endregion

        #region 数据初始化

        /// <summary>
        /// 数据初始化
        /// </summary>
        public async UniTask Init()
        {
             
            //初始化属性信息数据
            await InitBuffPropertyInfo();

            MessageMgr.RegisterMsg<UnionPropNtf>(this, OnUnionPropNtf); 
        }

        public void ReqData()
        {
            MessageMgr.Send(new UnionPropReq());
        }


        /// <summary>
        /// 联盟的buff Ntf
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionPropNtf(UnionPropNtf obj)
        {
            if (obj.propMap != null)
            {
                UpdatePropInfo(obj.propMap);
            }
        }

        /// <summary>
        /// 初始化buff属性信息数据
        /// </summary>
        private async UniTask InitBuffPropertyInfo()
        {
            var dic = await Cfg.C.CBuffProperty.RawDictAsync();
            var itor = dic.GetEnumerator();

            while(itor.MoveNext())
            {
                InitBuffPropertyInfo(itor.Current.Value);
            }
        }

        /// <summary>
        /// 初始化buff属性信息数据
        /// </summary>
        /// <param name="prop"></param>
        private void InitBuffPropertyInfo(CBuffProperty prop)
        {
            if (prop == null 
                || prop.PropertyType == (int)PropTypeEnum.NONE)
                return;

            if (_buffPropertyDic == null)
                _buffPropertyDic = new Dictionary<int, List<CBuffProperty>>();

            List<CBuffProperty> list = null;
            if(!_buffPropertyDic.TryGetValue(prop.PropertyType, out list))
            {
                list = new List<CBuffProperty>();
                _buffPropertyDic[prop.PropertyType] = list;
            }

            list.Add(prop);

        }

        #endregion


        #region 数据刷新

        /// <summary>
        /// 更新属性信息数据
        /// </summary>
        /// <param name="dic"></param>
        public void UpdatePropInfo(Dictionary<int, double> dic)
        {
            _propDic = dic;

            EventMgr.FireEvent(TEventType.AllianceBuffRefresh);
        }

        #endregion

        #region 获取属性数据信息


        /// <summary>
        /// 获取具体某一属性加速信息
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public async UniTask<double> GetPropSpeedUp(int cardId)
        {
            if (_propDic == null || cardId <= 0)
                return 0;

            double val = 0;
            if(_propDic.TryGetValue(cardId, out var speedUp))
            {
                //var prop = Cfg.C.CBuffProperty.I(cardId);
                var prop = await Cfg.C.CBuffProperty.GetConfigAsync(cardId);
                if (prop != null)
                {
                    if (prop.ValueType == 0)
                    {
                        //取值
                        val += speedUp;
                    }
                    else
                    {
                        //百分比
                        val += speedUp;
                    }
                }
            }

            //D.Warning?.Log($"联盟属性：{cardId}：val：{val}  字典有：{GameUtils.SerializeToStr(_propDic)}");
            return val;
        }

        /// <summary>
        /// 获取属性信息数据
        /// </summary>
        /// <param name="propType"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        //public async UniTask<double> GetPropInfo(PropTypeEnum propType, int type)
        //{
        //    if (propType == PropTypeEnum.NONE
        //        || _buffPropertyDic == null
        //        || !LSwitchMgr.I.IsFunctionOpen(SwitchConfig.BuffProperty_Tower))
        //        return 0;

        //    List<CBuffProperty> list = null;
        //    if(_buffPropertyDic.TryGetValue((int)propType, out list))
        //    {
        //        return await GetPropInfo(list, type);
        //    }

        //    return 0;
        //}

        /// <summary>
        /// 获取属性信息数据
        /// </summary>
        /// <param name="list"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        private async UniTask<double> GetPropInfo(List<CBuffProperty> list, int type)
        {
            if (list == null)
                return 0;

            CBuffProperty prop = null;
            double val = 0;
            for (int i = 0; i < list.Count; i++)
            {
                prop = list[i];
                if(prop != null
                    && (prop.PropertyParam == 0 || prop.PropertyParam == type))
                {
                    val += await GetPropSpeedUp(prop.Id);
                }
            }

            return val;
        }

        #endregion

        #region 数据清理

        /// <summary>
        /// 数据清理
        /// </summary>
        public void Dispose()
        {
            if(_propDic != null)
            {
                _propDic.Clear();
                _propDic = null;
            }

            if(_buffPropertyDic != null)
            {
                _buffPropertyDic.Clear();
                _buffPropertyDic = null;
            }
        }


        #endregion

    }
}
