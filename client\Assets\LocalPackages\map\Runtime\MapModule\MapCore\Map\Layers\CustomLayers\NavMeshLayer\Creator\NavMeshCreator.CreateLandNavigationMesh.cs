﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static partial class NavMeshCreator
    {
        static void CreateLandNavigationMesh(PrefabOutlineType type, Vector3 min, Vector3 max, List<IObstacle> obstacles, float agentRadius, float minimumAngle, float maximumArea, bool useDelaunay, float scaleFactor, bool removeSameHoles, out Vector3[] meshVertices, out int[] meshIndices)
        {
            meshVertices = null;
            meshIndices = null;

            List<List<Vector3>> holes;
            List<List<Vector3>> noneHoles;
            PolygonAlgorithm.GetDifferencePolygons(type, min.x, min.z, max.x, max.z, obstacles, agentRadius, scaleFactor, removeSameHoles, out noneHoles, out holes);
            if (noneHoles.Count > 0 || holes.Count > 0)
            {
                Triangulator.TriangulatePolygons(noneHoles, holes, useDelaunay, minimumAngle, maximumArea, null, out meshVertices, out meshIndices);
            }
        }
    }
}


#endif